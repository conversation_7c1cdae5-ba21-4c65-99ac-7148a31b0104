package com.zhy.view.flowlayout;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.LayoutDirection;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.text.TextUtilsCompat;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

//public class FlowLayout extends ViewGroup {
//    private static final String TAG = "FlowLayout";
//    private static final int LEFT = -1;
//    private static final int CENTER = 0;
//    private static final int RIGHT = 1;
//
//    protected List<List<View>> mAllViews = new ArrayList<List<View>>();
//    protected List<Integer> mLineHeight = new ArrayList<Integer>();
//    protected List<Integer> mLineWidth = new ArrayList<Integer>();
//    private int mGravity;
//    private int mMultiLineGravity;
//    private List<View> lineViews = new ArrayList<>();
//
//    public FlowLayout(Context context, AttributeSet attrs, int defStyle) {
//        super(context, attrs, defStyle);
//        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.TagFlowLayout);
//        mGravity = ta.getInt(R.styleable.TagFlowLayout_tag_gravity, LEFT);
//        mMultiLineGravity = ta.getInt(R.styleable.TagFlowLayout_tag_multi_line_gravity, mGravity);
//        int layoutDirection = TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault());
//        if (layoutDirection == LayoutDirection.RTL) {
//            if (mGravity == LEFT) {
//                mGravity = RIGHT;
//                mMultiLineGravity = RIGHT;
//            } else {
//                mGravity = LEFT;
//                mMultiLineGravity = LEFT;
//            }
//        }
//        ta.recycle();
//    }
//
//    public FlowLayout(Context context, AttributeSet attrs) {
//        this(context, attrs, 0);
//    }
//
//    public FlowLayout(Context context) {
//        this(context, null);
//    }
//
//    @Override
//    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
//        int sizeWidth = MeasureSpec.getSize(widthMeasureSpec);
//        int modeWidth = MeasureSpec.getMode(widthMeasureSpec);
//        int sizeHeight = MeasureSpec.getSize(heightMeasureSpec);
//        int modeHeight = MeasureSpec.getMode(heightMeasureSpec);
//
//        // wrap_content
//        int width = 0;
//        int height = 0;
//
//        int lineWidth = 0;
//        int lineHeight = 0;
//
//        int cCount = getChildCount();
//
//        for (int i = 0; i < cCount; i++) {
//            View child = getChildAt(i);
//            if (child.getVisibility() == View.GONE) {
//                if (i == cCount - 1) {
//                    width = Math.max(lineWidth, width);
//                    height += lineHeight;
//                }
//                continue;
//            }
//            measureChild(child, widthMeasureSpec, heightMeasureSpec);
//            MarginLayoutParams lp = (MarginLayoutParams) child
//                    .getLayoutParams();
//
//            int childWidth = child.getMeasuredWidth() + lp.leftMargin
//                    + lp.rightMargin;
//            int childHeight = child.getMeasuredHeight() + lp.topMargin
//                    + lp.bottomMargin;
//
//            if (lineWidth + childWidth > sizeWidth - getPaddingLeft() - getPaddingRight()) {
//                width = Math.max(width, lineWidth);
//                lineWidth = childWidth;
//                height += lineHeight;
//                lineHeight = childHeight;
//            } else {
//                lineWidth += childWidth;
//                lineHeight = Math.max(lineHeight, childHeight);
//            }
//            if (i == cCount - 1) {
//                width = Math.max(lineWidth, width);
//                height += lineHeight;
//            }
//        }
//        setMeasuredDimension(
//                //
//                modeWidth == MeasureSpec.EXACTLY ? sizeWidth : width + getPaddingLeft() + getPaddingRight(),
//                modeHeight == MeasureSpec.EXACTLY ? sizeHeight : height + getPaddingTop() + getPaddingBottom()//
//        );
//
//    }
//
//
//    @Override
//    protected void onLayout(boolean changed, int l, int t, int r, int b) {
//        mAllViews.clear();
//        mLineHeight.clear();
//        mLineWidth.clear();
//        lineViews.clear();
//
//        int width = getWidth();
//
//        int lineWidth = 0;
//        int lineHeight = 0;
//
//        int cCount = getChildCount();
//
//        for (int i = 0; i < cCount; i++) {
//            View child = getChildAt(i);
//            if (child.getVisibility() == View.GONE) continue;
//            MarginLayoutParams lp = (MarginLayoutParams) child
//                    .getLayoutParams();
//
//            int childWidth = child.getMeasuredWidth();
//            int childHeight = child.getMeasuredHeight();
//
//            if (childWidth + lineWidth + lp.leftMargin + lp.rightMargin > width - getPaddingLeft() - getPaddingRight()) {
//                mLineHeight.add(lineHeight);
//                mAllViews.add(lineViews);
//                mLineWidth.add(lineWidth);
//
//                lineWidth = 0;
//                lineHeight = childHeight + lp.topMargin + lp.bottomMargin;
//                lineViews = new ArrayList<View>();
//            }
//            lineWidth += childWidth + lp.leftMargin + lp.rightMargin;
//            lineHeight = Math.max(lineHeight, childHeight + lp.topMargin
//                    + lp.bottomMargin);
//            lineViews.add(child);
//
//        }
//        mLineHeight.add(lineHeight);
//        mLineWidth.add(lineWidth);
//        mAllViews.add(lineViews);
//
//
//        int left = getPaddingLeft();
//        int top = getPaddingTop();
//
//        int lineNum = mAllViews.size();
//        if (lineNum > 1) {
//            mGravity = mMultiLineGravity;
//        }
//
//        for (int i = 0; i < lineNum; i++) {
//            lineViews = mAllViews.get(i);
//            lineHeight = mLineHeight.get(i);
//
//            // set gravity
//            int currentLineWidth = this.mLineWidth.get(i);
//            switch (this.mGravity) {
//                case LEFT:
//                    left = getPaddingLeft();
//                    break;
//                case CENTER:
//                    left = (width - currentLineWidth) / 2 + getPaddingLeft();
//                    break;
//                case RIGHT:
//                    //  适配了rtl，需要补偿一个padding值
//                    left = width - (currentLineWidth + getPaddingLeft()) - getPaddingRight();
//                    //  适配了rtl，需要把lineViews里面的数组倒序排
//                    Collections.reverse(lineViews);
//                    break;
//            }
//
//            for (int j = 0; j < lineViews.size(); j++) {
//                View child = lineViews.get(j);
//                if (child.getVisibility() == View.GONE) {
//                    continue;
//                }
//
//                MarginLayoutParams lp = (MarginLayoutParams) child
//                        .getLayoutParams();
//
//                int lc = left + lp.leftMargin;
//                int tc = top + lp.topMargin;
//                int rc = lc + child.getMeasuredWidth();
//                int bc = tc + child.getMeasuredHeight();
//
//                child.layout(lc, tc, rc, bc);
//
//                left += child.getMeasuredWidth() + lp.leftMargin
//                        + lp.rightMargin;
//            }
//            top += lineHeight;
//        }
//
//    }
//
//    @Override
//    public LayoutParams generateLayoutParams(AttributeSet attrs) {
//        return new MarginLayoutParams(getContext(), attrs);
//    }
//
//    @Override
//    protected LayoutParams generateDefaultLayoutParams() {
//        return new MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
//    }
//
//    @Override
//    protected LayoutParams generateLayoutParams(LayoutParams p) {
//        return new MarginLayoutParams(p);
//    }
//}
public class FlowLayout extends ViewGroup {
    private static final String TAG = "FlowLayout";
    public static final int LEFT = -1;
    public static final int CENTER = 0;
    public static final int RIGHT = 1;

    protected List<List<View>> mAllViews = new ArrayList<List<View>>();
    protected List<Integer> mLineHeight = new ArrayList<Integer>();
    protected List<Integer> mLineWidth = new ArrayList<Integer>();
    private int mGravity;
    private int mMultiLineGravity;
    private List<View> lineViews = new ArrayList<>();

    // 新增属性：单行最大子视图数量，默认为-1表示不限制
    private int mMaxItemsPerLine = -1;

    public FlowLayout(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        try {
            TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.TagFlowLayout);
            mGravity = ta.getInt(R.styleable.TagFlowLayout_tag_gravity, LEFT);
            mMultiLineGravity = ta.getInt(R.styleable.TagFlowLayout_tag_multi_line_gravity, mGravity);
            // 读取新属性，如果属性未定义则使用默认值-1
            if (ta.hasValue(R.styleable.TagFlowLayout_max_items_per_line)) {
                mMaxItemsPerLine = ta.getInt(R.styleable.TagFlowLayout_max_items_per_line, -1);
            } else {
                mMaxItemsPerLine = -1; // 默认不限制
            }

            int layoutDirection = TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault());
            if (layoutDirection == LayoutDirection.RTL) {
                if (mGravity == LEFT) {
                    mGravity = RIGHT;
                    mMultiLineGravity = RIGHT;
                } else {
                    mGravity = LEFT;
                    mMultiLineGravity = LEFT;
                }
            }
            ta.recycle();
        } catch (Exception e) {
            // 防止资源未定义导致崩溃
            mGravity = LEFT;
            mMultiLineGravity = LEFT;
            mMaxItemsPerLine = -1;
        }
    }

    public FlowLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FlowLayout(Context context) {
        this(context, null);
    }

    public void setMaxItemsPerLine(int maxItemsPerLine) {
        this.mMaxItemsPerLine = maxItemsPerLine;
        requestLayout();
    }

    public int getMaxItemsPerLine() {
        return mMaxItemsPerLine;
    }
    public void setConfig(int mGravity, int mMultiLineGravity, int mMaxItemsPerLine) {
        this.mGravity = mGravity;
        this.mMultiLineGravity = mMultiLineGravity;
        this.mMaxItemsPerLine = mMaxItemsPerLine;
        requestLayout();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int sizeWidth = MeasureSpec.getSize(widthMeasureSpec);
        int modeWidth = MeasureSpec.getMode(widthMeasureSpec);
        int sizeHeight = MeasureSpec.getSize(heightMeasureSpec);
        int modeHeight = MeasureSpec.getMode(heightMeasureSpec);

        // wrap_content
        int width = 0;
        int height = 0;

        int lineWidth = 0;
        int lineHeight = 0;

        int cCount = getChildCount();

        // 当前行的子视图计数
        int lineItemCount = 0;

        for (int i = 0; i < cCount; i++) {
            View child = getChildAt(i);
            if (child.getVisibility() == View.GONE) {
                if (i == cCount - 1) {
                    width = Math.max(lineWidth, width);
                    height += lineHeight;
                }
                continue;
            }
            measureChild(child, widthMeasureSpec, heightMeasureSpec);
            MarginLayoutParams lp = (MarginLayoutParams) child
                    .getLayoutParams();

            int childWidth = child.getMeasuredWidth() + lp.leftMargin
                    + lp.rightMargin;
            int childHeight = child.getMeasuredHeight() + lp.topMargin
                    + lp.bottomMargin;

            // 检查是否需要换行：1. 宽度不足 或 2. 达到单行最大子视图数量
            if ((lineWidth + childWidth > sizeWidth - getPaddingLeft() - getPaddingRight()) ||
                    (mMaxItemsPerLine > 0 && lineItemCount >= mMaxItemsPerLine)) {
                width = Math.max(width, lineWidth);
                lineWidth = childWidth;
                height += lineHeight;
                lineHeight = childHeight;
                // 重置当前行的子视图计数
                lineItemCount = 1;
            } else {
                lineWidth += childWidth;
                lineHeight = Math.max(lineHeight, childHeight);
                // 增加当前行的子视图计数
                lineItemCount++;
            }
            if (i == cCount - 1) {
                width = Math.max(lineWidth, width);
                height += lineHeight;
            }
        }
        setMeasuredDimension(
                modeWidth == MeasureSpec.EXACTLY ? sizeWidth : width + getPaddingLeft() + getPaddingRight(),
                modeHeight == MeasureSpec.EXACTLY ? sizeHeight : height + getPaddingTop() + getPaddingBottom()
        );
    }
    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        mAllViews.clear();
        mLineHeight.clear();
        mLineWidth.clear();
        lineViews.clear();

        int width = getWidth();

        int lineWidth = 0;
        int lineHeight = 0;

        // 当前行的子视图计数
        int lineItemCount = 0;

        int cCount = getChildCount();

        for (int i = 0; i < cCount; i++) {
            View child = getChildAt(i);
            if (child.getVisibility() == View.GONE) continue;
            MarginLayoutParams lp = (MarginLayoutParams) child
                    .getLayoutParams();

            int childWidth = child.getMeasuredWidth();
            int childHeight = child.getMeasuredHeight();

            // 检查是否需要换行：1. 宽度不足 或 2. 达到单行最大子视图数量
            if ((childWidth + lineWidth + lp.leftMargin + lp.rightMargin > width - getPaddingLeft() - getPaddingRight()) ||
                    (mMaxItemsPerLine > 0 && lineItemCount >= mMaxItemsPerLine)) {
                mLineHeight.add(lineHeight);
                mAllViews.add(lineViews);
                mLineWidth.add(lineWidth);

                lineWidth = 0;
                lineHeight = childHeight + lp.topMargin + lp.bottomMargin;
                lineViews = new ArrayList<View>();
                // 重置当前行的子视图计数
                lineItemCount = 0;
            }
            lineWidth += childWidth + lp.leftMargin + lp.rightMargin;
            lineHeight = Math.max(lineHeight, childHeight + lp.topMargin
                    + lp.bottomMargin);
            lineViews.add(child);
            // 增加当前行的子视图计数
            lineItemCount++;
        }
        mLineHeight.add(lineHeight);
        mLineWidth.add(lineWidth);
        mAllViews.add(lineViews);

        int left = getPaddingLeft();
        int top = getPaddingTop();

        int lineNum = mAllViews.size();
        if (lineNum > 1) {
            mGravity = mMultiLineGravity;
        }

        for (int i = 0; i < lineNum; i++) {
            lineViews = mAllViews.get(i);
            lineHeight = mLineHeight.get(i);

            // set gravity
            int currentLineWidth = this.mLineWidth.get(i);
            switch (this.mGravity) {
                case LEFT:
                    left = getPaddingLeft();
                    break;
                case CENTER:
                    left = (width - currentLineWidth) / 2 + getPaddingLeft();
                    break;
                case RIGHT:
                    //  适配了rtl，需要补偿一个padding值
                    left = width - (currentLineWidth + getPaddingLeft()) - getPaddingRight();
                    //  适配了rtl，需要把lineViews里面的数组倒序排
                    Collections.reverse(lineViews);
                    break;
            }

            for (int j = 0; j < lineViews.size(); j++) {
                View child = lineViews.get(j);
                if (child.getVisibility() == View.GONE) {
                    continue;
                }

                MarginLayoutParams lp = (MarginLayoutParams) child
                        .getLayoutParams();

                int lc = left + lp.leftMargin;
                int tc = top + lp.topMargin;
                int rc = lc + child.getMeasuredWidth();
                int bc = tc + child.getMeasuredHeight();

                child.layout(lc, tc, rc, bc);

                left += child.getMeasuredWidth() + lp.leftMargin
                        + lp.rightMargin;
            }
            top += lineHeight;
        }
    }

    @Override
    public LayoutParams generateLayoutParams(AttributeSet attrs) {
        return new MarginLayoutParams(getContext(), attrs);
    }

    @Override
    protected LayoutParams generateDefaultLayoutParams() {
        return new MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
    }

    @Override
    protected LayoutParams generateLayoutParams(LayoutParams p) {
        return new MarginLayoutParams(p);
    }
}