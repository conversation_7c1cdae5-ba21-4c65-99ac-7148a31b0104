package com.dinsafer.plugin.widget.view.anit.interfere;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;

import com.dinsafer.plugin.widget.R;
import com.dinsafer.plugin.widget.customview.IOSSwitch;
import com.dinsafer.plugin.widget.databinding.LayoutAntiInterfereBinding;
import com.dinsafer.plugin.widget.model.AntiInterfereInfo;
import com.dinsafer.plugin.widget.model.OneConfInfo;
import com.dinsafer.plugin.widget.model.StringResponseEntry;
import com.dinsafer.plugin.widget.net.DinsafeAPI;
import com.dinsafer.plugin.widget.util.Info;
import com.dinsafer.plugin.widget.util.Local;
import com.dinsafer.plugin.widget.view.base.BaseFragment;
import com.google.gson.Gson;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by LT on 2019-08-20.
 */
public class AntiInterfereFragment extends BaseFragment<LayoutAntiInterfereBinding> {

    public static AntiInterfereFragment newInstance() {
        return new AntiInterfereFragment();
    }

    private String content;

    @Override
    protected int getContentViewLayoutID() {
        return R.layout.layout_anti_interfere;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        mBinding.smartCommonBar.smartCommonBarTitle.setLocalText(getResources().getString(R.string.smart_anti_interfere));
        mBinding.tvDescripe.setLocalText(getResources().getString(R.string.smart_anti_interfere_descripe));
        mBinding.tvTip.setLocalText(getResources().getString(R.string.smart_anti_interfere_tips));
        mBinding.tvContent.setText(Local.s(getResources().getString(R.string.smart_anti_interfere_content)));
        mBinding.btnSave.setLocalText(getResources().getString(R.string.save));
        content = mBinding.tvContent.getText().toString();
        mBinding.switchOpen.setOn(false);
        mBinding.switchOpen.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                if (!mBinding.tvContent.getText().toString().equals("")) {
                    content = mBinding.tvContent.getText().toString();
                }
                showTimeOutLoadinFramgmentWithErrorAlert();
                DinsafeAPI.getApi().modifyAntiInterfere(Info.getInstance().getDeviceId(), isOn, content)
                        .enqueue(new Callback<StringResponseEntry>() {
                            @Override
                            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                                StringResponseEntry stringResponseEntry = response.body();
                                if (stringResponseEntry.getStatus() == 1) {
                                    closeTimeOutLoadinFramgmentWithErrorAlert();
                                } else {
                                    showErrorToast();
                                    mBinding.switchOpen.setOn(!mBinding.switchOpen.isOn());
                                    closeTimeOutLoadinFramgmentWithErrorAlert();
                                }
                            }

                            @Override
                            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                                showErrorToast();
                                mBinding.switchOpen.setOn(!mBinding.switchOpen.isOn());
                                closeTimeOutLoadinFramgmentWithErrorAlert();

                            }
                        });
            }
        });


        mBinding.smartCommonBar.smartCommonBarBack.setOnClickListener(v -> {
            toBack();
        });
        mBinding.smartCommonBar.smartCommonBarRight.setVisibility(View.INVISIBLE);
        mBinding.btnSave.setOnClickListener(v -> {
            toSave();
        });
        mBinding.tvContent.addTextChangedListener(textWatcher);
    }

    @Override
    public void initData() {
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void toBack() {
        removeSelf();
    }

    public void toSave() {
        if (mBinding.tvContent.getText().toString().equals("")) {
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        DinsafeAPI.getApi().modifyAntiInterfere(Info.getInstance().getDeviceId(), mBinding.switchOpen.isOn(), mBinding.tvContent.getText().toString())
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        StringResponseEntry entry = response.body();
                        if (entry == null) {
                            return;
                        }
                        if (entry.getStatus() == 1) {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showTopToast(getString(R.string.success));
                            removeSelf();
                            return;
                        }
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                    }
                });
    }


    private void getData() {
        showTimeOutLoadinFramgment();
        DinsafeAPI.getApi().getOneConfCall(Info.getInstance().getDeviceId(), "ANTINTERER").
                enqueue(new Callback<OneConfInfo>() {
                    @Override
                    public void onResponse(Call<OneConfInfo> call, Response<OneConfInfo> response) {
                        OneConfInfo oneConfInfo = response.body();
                        if (oneConfInfo == null) {
                            closeLoadingFragment();
                            mBinding.tvContent.addTextChangedListener(textWatcher);
                            return;
                        }
                        try {
                            Gson gson = new Gson();
                            AntiInterfereInfo antiInterfereInfo = gson.fromJson(oneConfInfo.getResult().getConf(), AntiInterfereInfo.class);
                            mBinding.switchOpen.setOn(antiInterfereInfo.isEnable());
                            mBinding.tvContent.setText(antiInterfereInfo.getMessage());
                            mBinding.tvContent.addTextChangedListener(textWatcher);
                            closeLoadingFragment();
                        } catch (Exception e) {
                            closeLoadingFragment();
                        }
                    }

                    @Override
                    public void onFailure(Call<OneConfInfo> call, Throwable t) {
                        closeLoadingFragment();
                        mBinding.tvContent.addTextChangedListener(textWatcher);
                    }
                });
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        getData();
    }

    private TextWatcher textWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

        }

        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            if (mBinding.tvContent.getText().toString().equals("")) {
                mBinding.smartCommonBar.smartCommonBarRight.setAlpha(76);
            } else {
                mBinding.smartCommonBar.smartCommonBarRight.setAlpha(255);
            }
        }

        @Override
        public void afterTextChanged(Editable editable) {

        }
    };


}
