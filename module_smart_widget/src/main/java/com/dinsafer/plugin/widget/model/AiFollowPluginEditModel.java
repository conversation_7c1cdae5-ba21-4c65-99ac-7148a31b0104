package com.dinsafer.plugin.widget.model;


import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.plugin.widget.R;
import com.dinsafer.plugin.widget.customview.IOSSwitch;
import com.dinsafer.plugin.widget.customview.rv.BaseBindModel;
import com.dinsafer.plugin.widget.databinding.ItemAiFollowPluginEditBinding;
import com.dinsafer.plugin.widget.util.DDLog;
import com.dinsafer.plugin.widget.util.Util;

/**
 * Created by LT on 2019-08-20.
 */
public class AiFollowPluginEditModel implements BaseBindModel<ItemAiFollowPluginEditBinding> {

    private String TAG = getClass().getSimpleName();
    private boolean isOn;
    private String name;
    private boolean isAdded;
    private String code;
    private String id;
    private boolean isHeader;
    private boolean enable;
    private int offsetSize;

    /**
     * 新的ASK只能插座修改状态需要以下字段
     */
    private boolean isAskPlug;
    private int dtype;
    private String stype;
    private String sendid;

    public AiFollowPluginEditModel(boolean isOn, String name, String code, boolean isAdded, String id) {
        this.isOn = isOn;
        this.name = name;
        this.isAdded = isAdded;
        this.code = code;
        this.id = id;
        aiFollowPluginEditModel = this;
    }

    private AiFollowPluginEditModel aiFollowPluginEditModel;

    public AiFollowPluginEditModel getAiFollowPluginEditModel() {
        return aiFollowPluginEditModel;
    }

    public void setAiFollowPluginEditModel(AiFollowPluginEditModel aiFollowPluginEditModel) {
        this.aiFollowPluginEditModel = aiFollowPluginEditModel;
    }

    public boolean isOn() {
        return isOn;
    }

    public String getName() {
        return name;
    }

    public boolean isAdded() {
        return isAdded;
    }

    public AiFollowPluginEditModel setAdded(boolean added) {
        isAdded = added;
        return this;
    }

    public String getCode() {
        return code;
    }

    public String getId() {
        return id;
    }

    public boolean isEnable() {
        return enable;
    }

    public AiFollowPluginEditModel setEnable(boolean enable) {
        this.enable = enable;
        return this;
    }

    public AiFollowPluginEditModel setHeader(boolean header) {
        isHeader = header;
        return this;
    }

    public String getTAG() {
        return TAG;
    }

    public void setTAG(String TAG) {
        this.TAG = TAG;
    }

    public boolean isAskPlug() {
        return isAskPlug;
    }

    public AiFollowPluginEditModel setAskPlug(boolean askPlug) {
        isAskPlug = askPlug;
        return this;
    }

    public int getDtype() {
        return dtype;
    }

    public AiFollowPluginEditModel setDtype(int dtype) {
        this.dtype = dtype;
        return this;
    }

    public String getStype() {
        return stype;
    }

    public AiFollowPluginEditModel setStype(String stype) {
        this.stype = stype;
        return this;
    }

    public String getSendid() {
        return sendid;
    }

    public AiFollowPluginEditModel setSendid(String sendid) {
        this.sendid = sendid;
        return this;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ai_follow_plugin_edit;
    }

    @Override
    public boolean onDo(View v) {

        DDLog.d(TAG, "onDo: ");
        return false;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAiFollowPluginEditBinding viewBinding) {
        Log.d(TAG, "convert: ");

        if(isHeader) {
            viewBinding.rl.setVisibility(View.GONE);
            viewBinding.header.setLocalText(id);
        } else {
            viewBinding.rl.setVisibility(View.VISIBLE);
            viewBinding.tvPlugin.setLocalText(name);
            viewBinding.tvDelete.setLocalText("Delete");
            if (isAdded) {
                viewBinding.switchOpen.setVisibility(View.VISIBLE);
                viewBinding.icon.setImageResource(R.drawable.btn_plugin_follow_delete);
                viewBinding.switchOpen.setOn(enable);
            }else {
                viewBinding.switchOpen.setVisibility(View.GONE);
                viewBinding.icon.setImageResource(R.drawable.btn_plugin_follow_switch_add);
            }

            viewBinding.iconOn.setImageResource(Util.getResIdJustOn(code, stype));

            viewBinding.content.clearAnimation();
            viewBinding.content.setX(0);
            viewBinding.btnDelete.setVisibility(View.GONE);

            viewBinding.icon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!isAdded) {
                        if (btnAddIconClickListener != null) {
                            btnAddIconClickListener.onClick(v, aiFollowPluginEditModel);
                        }
                        return;
                    }
                    TranslateAnimation anim = new TranslateAnimation(0, -offsetSize, 0, 0);
                    anim.setDuration(500);
                    anim.setFillAfter(true);
                    viewBinding.content.startAnimation(anim);
                    anim.setAnimationListener(new Animation.AnimationListener() {
                        @Override
                        public void onAnimationStart(Animation animation) {

                        }

                        @Override
                        public void onAnimationEnd(Animation animation) {
                            viewBinding.content.clearAnimation();
                            viewBinding.content.setX(-87*3);


                        }

                        @Override
                        public void onAnimationRepeat(Animation animation) {

                        }
                    });
                    viewBinding.btnDelete.setVisibility(View.VISIBLE);

                }
            });

            viewBinding.content.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!isAdded) {
                        return;
                    }
                    if(viewBinding.content.getX() >= 0)
                        return;
                    TranslateAnimation anim = new TranslateAnimation(0, offsetSize, 0, 0);
                    anim.setDuration(500);
                    anim.setFillAfter(true);
                    anim.setAnimationListener(new Animation.AnimationListener() {
                        @Override
                        public void onAnimationStart(Animation animation) {

                        }

                        @Override
                        public void onAnimationEnd(Animation animation) {
                            viewBinding.content.clearAnimation();
                            viewBinding.content.setX(0);
                            viewBinding.btnDelete.setVisibility(View.GONE);
                        }

                        @Override
                        public void onAnimationRepeat(Animation animation) {

                        }
                    });
                    viewBinding.content.startAnimation(anim);

                }
            });

            viewBinding.btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Log.d(TAG, "click delete ");

                    viewBinding.content.setX(0);
                    viewBinding.btnDelete.setVisibility(View.GONE);


                    if (btnAddIconClickListener != null) {
                        btnAddIconClickListener.onDelete(v, aiFollowPluginEditModel);
                    }

                }
            });

            viewBinding.switchOpen.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOpen) {
                    enable = isOpen;
                }
            });
        }


    }


    @Override
    public boolean equals(Object obj) {
        AiFollowPluginEditModel info = (AiFollowPluginEditModel) obj;
        return id.equals(info.id);
    }

    public AiFollowPluginEditModel(String id) {
        this.id = id;
    }

    public interface BtnAddIconClickListener{
        void onClick(View view, AiFollowPluginEditModel id);
        void onDelete(View view, AiFollowPluginEditModel id);
    }

    public BtnAddIconClickListener btnAddIconClickListener;

    public BtnAddIconClickListener getBtnAddIconClickListener() {
        return btnAddIconClickListener;
    }

    public AiFollowPluginEditModel setBtnAddIconClickListener(BtnAddIconClickListener btnAddIconClickListener) {
        this.btnAddIconClickListener = btnAddIconClickListener;
        return this;
    }

    public int getOffsetSize() {
        return offsetSize;
    }

    public AiFollowPluginEditModel setOffsetSize(int offsetSize) {
        this.offsetSize = offsetSize;
        return this;
    }
}
