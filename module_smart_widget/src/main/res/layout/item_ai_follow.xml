<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="model"
            type="Object" />

        <variable
            name="itemClick"
            type="android.view.View.OnClickListener" />

        <variable
            name="childClick"
            type="android.view.View.OnClickListener" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/smart_ai_arm_item_height"
        android:layout_marginBottom="15dp"
        android:background="@drawable/shape_item_ai_follow"
        android:gravity="center_vertical"
        android:onClick="@{itemClick::onClick}"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/smart_left_margin"
            android:layout_marginRight="@dimen/smart_left_margin"
            android:src="@drawable/btn_plugin_follow_arm" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <com.dinsafer.plugin.widget.customview.LocalTextView
                android:id="@+id/tv_arm_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/smart_arm"
                android:textColor="#4A4A4A"
                style="@style/TextFamilyTittleXL" />

            <com.dinsafer.plugin.widget.customview.LocalTextView
                android:id="@+id/tv_item_count"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/smart_ai_item_num"
                android:textColor="#9D9D9D"
                style="@style/TextFamilyCaptionL" />
        </LinearLayout>

        <ImageView
            android:id="@+id/btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/smart_left_margin"
            android:padding="@dimen/smart_left_margin"
            android:src="@drawable/btn_plugin_follow_add" />
    </LinearLayout>
</layout>