
package androidx.databinding;
import com.dinsafer.dinnet.BR;
class DataBinderMapper {
    final static int TARGET_MIN_SDK = 16;
    public DataBinderMapper() {
    }
    public androidx.databinding.ViewDataBinding getDataBinder(androidx.databinding.DataBindingComponent bindingComponent, android.view.View view, int layoutId) {
        switch(layoutId) {
                case com.dinsafer.dinnet.R.layout.item_binding1:
                    return com.dinsafer.dinnet.databinding.ItemBinding1Binding.bind(view, bindingComponent);
        }
        return null;
    }
    androidx.databinding.ViewDataBinding getDataBinder(androidx.databinding.DataBindingComponent bindingComponent, android.view.View[] views, int layoutId) {
        switch(layoutId) {
        }
        return null;
    }
    int getLayoutId(String tag) {
        if (tag == null) {
            return 0;
        }
        final int code = tag.hashCode();
        switch(code) {
            case 723922628: {
                if(tag.equals("layout/item_binding1_0")) {
                    return com.dinsafer.dinnet.R.layout.item_binding1;
                }
                break;
            }
        }
        return 0;
    }
    String convertBrIdToString(int id) {
        if (id < 0 || id >= InnerBrLookup.sKeys.length) {
            return null;
        }
        return InnerBrLookup.sKeys[id];
    }
    private static class InnerBrLookup {
        static String[] sKeys = new String[]{
            "_all"
            ,"childClick"
            ,"itemClick"};
    }
}