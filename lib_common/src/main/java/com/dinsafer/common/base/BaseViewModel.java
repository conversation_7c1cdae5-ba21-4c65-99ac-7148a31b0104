package com.dinsafer.common.base;

import android.app.Application;
import androidx.lifecycle.AndroidViewModel;

import com.dinsafer.common.utils.DDLog;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2019/3/20
 */
public class BaseViewModel extends AndroidViewModel implements IBaseViewModel {
    protected String TAG = getClass().getSimpleName();

    public BaseViewModel(Application application) {
        super(application);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        DDLog.d(TAG, "onCleared: ");
    }
}
