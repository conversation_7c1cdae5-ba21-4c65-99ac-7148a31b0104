package com.dinsafer.common.utils;

import android.content.Context;
import android.text.SpannableString;
import android.text.style.TextAppearanceSpan;

/**
 * Created by lJJ on 2016/9/6.
 */

public class StringStyle {

    /**
     * 为文本设置样式
     *
     * @param context
     * @param text
     * @param style
     * @return
     */
    public static SpannableString format(Context context, String text, int style) {
        SpannableString spannableString = new SpannableString(text);
        spannableString.setSpan(new TextAppearanceSpan(context, style), 0, text.length(), 0);
        return spannableString;
    }
}
