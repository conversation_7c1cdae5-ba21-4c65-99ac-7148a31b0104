package com.dinsafer.common.utils;

import java.lang.reflect.ParameterizedType;

/**
 * Created by Administrator on 2017/8/7 0007.
 */

public class ClassUtil {

    public static <T> T getT(Object o, int i) {
        try {
            return ((Class<T>) ((ParameterizedType) (o.getClass().getGenericSuperclass())).getActualTypeArguments()[i]).newInstance();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
