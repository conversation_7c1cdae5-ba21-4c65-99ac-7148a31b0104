package com.dinsafer.common.utils;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.view.View;

import com.dinsafer.common.utils.DDLog;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Hashtable;

public class GenerateImageUtils {

    private static final String IMAGE_CACHE = "image";
    private static int QR_WIDTH = 300;
    private static int QR_HEIGHT = 300;

    public static Bitmap createQRImage(String resultcode) {
        try {
            // 判断URL合法性
            if (resultcode == null || "".equals(resultcode) || resultcode.length() < 1) {
                return null;
            }
//            Hashtable<EncodeHintType, String> hints = new Hashtable<EncodeHintType, String>();
            //修改存储类型；
            Hashtable hints = new Hashtable();

            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");

            //设置容错率；
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.Q);

            // 图像数据转换，使用了矩阵转换
//			BitMatrix bitMatrix = new MultiFormatWriter().encode(new String(url.getBytes("GBK"),"ISO-8859-1"),BarcodeFormat.QR_CODE, 300, 300);
            BitMatrix bitMatrix = new QRCodeWriter().encode(new String(resultcode.getBytes("UTF-8"), "ISO-8859-1"), BarcodeFormat.QR_CODE, QR_WIDTH, QR_HEIGHT);
            DDLog.i("GenerateImageUtils", "resultcode" + resultcode);
            int[] pixels = new int[QR_WIDTH * QR_HEIGHT];
            // 下面这里按照二维码的算法，逐个生成二维码的图片，
            // 两个for循环是图片横列扫描的结果
            for (int y = 0; y < QR_HEIGHT; y++) {
                for (int x = 0; x < QR_WIDTH; x++) {
                    if (bitMatrix.get(x, y)) {
                        //0xff000000中 ff 表示不透明；
                        pixels[y * QR_WIDTH + x] = 0xff000000;
                    } else {
                        pixels[y * QR_WIDTH + x] = 0xffffffff;
                    }
                }
            }
            // 生成二维码图片的格式，使用ARGB_8888
            Bitmap bitmap = Bitmap.createBitmap(QR_WIDTH, QR_HEIGHT, Bitmap.Config.ARGB_8888);
//            Bitmap bitmap = Bitmap.createBitmap(QR_WIDTH, QR_HEIGHT,Bitmap.Config.ARGB_4444);
            bitmap.setPixels(pixels, 0, QR_WIDTH, 0, 0, QR_WIDTH, QR_HEIGHT);
            return bitmap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    //    获取view的截图；
    public static Bitmap captureView(View view) {
        Bitmap bmp = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bmp);
        view.draw(canvas);
        return bmp;
    }

    public static String saveImage(Activity activity, Bitmap bitmap) {
        OutputStream outStream = null;
        String filePath = null;
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            DDLog.i("GenerateImageUtils", "sdcard_mount");
            byte[] bitmaps = getbitmaptobytes(bitmap);
            if (true) {

                try {
                    //防止出现重复名字,其中月份要用大写的 MM ；否则月份显示错误;
                    String fileName = "ipcQR" + new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date()) + ".png";
                    File dir;
                    dir = new File(Environment
                            .getExternalStorageDirectory().toString() + "/dinsafer/" + IMAGE_CACHE);
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }

                    File cacheFile = new File(dir, fileName);
                    FileOutputStream fstream = new FileOutputStream(cacheFile);
                    outStream = new BufferedOutputStream(fstream);
                    outStream.write(bitmaps);

//                    Toast.makeText(activity, Local.s("图片成功存至matisight/ipc目录下"), Toast.LENGTH_SHORT).show();

                    //刷新图片路径
                    reflash_dir_fileName(activity, dir, fileName);

                    filePath = "file://" + dir.getPath() + "/" + fileName;
                    DDLog.i("GenerateImageUtils", "filePath1 = " + filePath);
//                    Toast.makeText(activity, "重新扫描SD 卡", Toast.LENGTH_SHORT).show();

                } catch (Exception e) {
                    e.printStackTrace();
                    DDLog.i("GenerateImageUtils", "保存本地图片异常！！！");
                } finally {

                    if (outStream != null) {
                        try {
                            outStream.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
            return filePath;
        } else {
            return null;
        }

    }

    public static void reflash_dir_fileName(Activity activity, File dir, String fileName) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            activity.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
//                                                        Uri.parse("file://" + fileName)));
                    Uri.parse("file://" + dir.getPath() + "/" + fileName)));
        } else {
            activity.sendBroadcast(new Intent(Intent.ACTION_MEDIA_MOUNTED,
//                                                        Uri.parse("file://" + Environment.getExternalStorageDirectory())));
                    Uri.parse("file://" + dir.getPath() + "/" + fileName)));
        }
    }

    private static byte[] getbitmaptobytes(Bitmap bitmap) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
        return out.toByteArray();
    }
}
