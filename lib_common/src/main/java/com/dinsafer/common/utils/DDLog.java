package com.dinsafer.common.utils;

import android.util.Log;

import com.dinsafer.common.BuildConfig;

public class DDLog {

    public static boolean isEnableLog = BuildConfig.LOG_DEBUG;

    /**
     * 初始化日志
     * @param isShowLog         打印普通日志
     */
    public static void initLog(boolean isShowLog) {
        DDLog.isEnableLog = isShowLog;
    }

    public static void i(String tag, String msg) {
        if (isEnableLog) {
            Log.i(tag, msg);
        }
    }

    public static void v(String tag, String msg) {
        if (isEnableLog) {
            Log.v(tag, msg);
        }
    }

    public static void d(String tag, String msg) {
        if (isEnableLog) {
            Log.d(tag, msg);
        }
    }

    public static void w(String tag, String msg) {
        if (isEnableLog) {
            Log.w(tag, msg);
        }
    }

    public static void e(String tag, String msg) {
        if (isEnableLog) {
            Log.e(tag, msg);
        }
    }
}
