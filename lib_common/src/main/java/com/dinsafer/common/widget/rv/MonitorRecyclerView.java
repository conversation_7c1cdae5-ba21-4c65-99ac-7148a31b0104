package com.dinsafer.common.widget.rv;


import android.content.Context;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;
import android.util.AttributeSet;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;

/**
 * Created by Administrator on 2017/11/22.
 */

public class MonitorRecyclerView extends RecyclerView {
    public MonitorRecyclerView(Context context) {
        super(context);
    }

    public MonitorRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public MonitorRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    //获取滑动距离
    public int getScollYDistance() {
        int Y = 0;
        if (this.getLayoutManager() instanceof LinearLayoutManager) {
            LinearLayoutManager layoutManager = (LinearLayoutManager) this.getLayoutManager();
            int position = layoutManager.findFirstVisibleItemPosition();
            View firstVisiableChildView = layoutManager.findViewByPosition(position);
            int itemHeight = firstVisiableChildView.getHeight();
            for (int a = 0; a < position; a++) {
                Y += itemHeight;
            }
            return Y - firstVisiableChildView.getTop();
        } else if (this.getLayoutManager() instanceof StaggeredGridLayoutManager) {
            StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) this.getLayoutManager();
            //获取最后一个完全显示的ItemPosition
            int[] lastVisiblePositions = layoutManager.findFirstVisibleItemPositions(new int[layoutManager.getSpanCount()]);
            int position = lastVisiblePositions[0];
            View firstVisiableChildView = layoutManager.findViewByPosition(position);
            int itemHeight = firstVisiableChildView.getHeight();
            for (int a = 0; a < position; a++) {
                if (a == 0) {
                    Y += ((BaseQuickAdapter) getAdapter()).getHeaderLayout().getHeight();
                } else {
                    Y += itemHeight;
                }
            }
            return Y - firstVisiableChildView.getTop();
        }
        return 0;
    }

    //获取显示的item下标
    public int getVisibleItemIndex() {
        int position = 0;
        if (this.getLayoutManager() instanceof LinearLayoutManager) {
            LinearLayoutManager layoutManager = (LinearLayoutManager) this.getLayoutManager();
            position = layoutManager.findFirstVisibleItemPosition();

        } else if (this.getLayoutManager() instanceof StaggeredGridLayoutManager) {
            StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) this.getLayoutManager();
            //获取最后一个完全显示的ItemPosition
            int[] firstVisiblePositions = layoutManager.findFirstVisibleItemPositions(new int[layoutManager.getSpanCount()]);
            position = firstVisiblePositions[0];
        }
        return position;
    }

    //移动到制定的tiem
    public void MoveToPosition(int n) {
        int firstItem = 0;
        int lastItem = 0;
        if (this.getLayoutManager() instanceof LinearLayoutManager) {
            LinearLayoutManager layoutManager = (LinearLayoutManager) this.getLayoutManager();
            firstItem = layoutManager.findFirstVisibleItemPosition();
            lastItem = layoutManager.findLastVisibleItemPosition();

        } else if (this.getLayoutManager() instanceof StaggeredGridLayoutManager) {
            StaggeredGridLayoutManager layoutManager = (StaggeredGridLayoutManager) this.getLayoutManager();
            int[] firstVisiblePositions = layoutManager.findFirstVisibleItemPositions(new int[layoutManager.getSpanCount()]);
            firstItem = firstVisiblePositions[0];
            int[] lastVisiblePositions = layoutManager.findLastVisibleItemPositions(new int[layoutManager.getSpanCount()]);
            lastItem = lastVisiblePositions[0];
        }
        if (n <= firstItem) {
            this.scrollToPosition(n);
        } else if (n <= lastItem) {
            int top = this.getChildAt(n - firstItem).getTop();
            this.scrollBy(0, top);
        } else {
            this.scrollToPosition(n);
        }

    }

}
