# ChangeLog


## novapro-0.9.2 (2019-04-22)


### Features


* feat(apns): 修改apns api服务器域名为http://api-np.dinsafer.com ([3c20bc02]by 廖家杰)

* feat(apns build): 增加打包脚本要用到的注释 ([fc3270d4]by 廖家杰)

* feat(apns fcm): 增加app/google-services文件夹用于存放fcm json配置文件 ([a6c4a9e6]by 廖家杰)

* feat(apns)：apns增加设置服务器域名是否为api的接口 ([80c27281]by 廖家杰)

* feat(ble): 在最终设置网络的时候需要发多一个系统信息，以及漏掉了一些uid，补上 ([0addf1d5]by LT)

* feat(build gradle)：增加小米和google play的多渠道打包 ([362d7933]by 廖家杰)

*  增加一个更多支持的开关 ([f1d68a3d]by LT)

* feat(<PERSON> Hue): 去掉白灯模式 ([4f5b5c5d]by 廖家杰)

* feat(devicesetting、switchbot): 增加more页面，并且整理devicesetting排序 ([1d1ed277]by Rinfon)


### Bug Fixes


* fix(DDSystemUtil): getWIFISSID方法空指针的问题2 ([30caf56f] by miracleshed)

* Revert "fix(DDSystemUtil): 修复getWIFISSID空指针的问题" ([47251b75] by miracleshed)

* fix(DDSystemUtil): 修复getWIFISSID空指针的问题 ([14df9a43] by miracleshed)

* fix(tuya): tuya初始化放到application ([68e19280] by LT)

* fix(switchbot): 开关完善。新主页黑名单页面增加开关 ([91966884] by LT)

* fix(Philips Hue): 第一次进入已添加的hue时自动开始搜索 ([9271fbd1] by 廖家杰)

*  修复涂鸦灯泡再次进来无法同步状态问题 ([41202098] by Rinfon)

* Revert "fix(tuya灯泡): 修复在tuya灯泡控制页面切换后台再回前台点击控制开关状态不对的问题" ([38e5b853] by 廖家杰)

* Revert "fix(tuya灯泡): 修复在tuya灯泡控制页面切换后台再回前台点击控制开关状态不对的问题" ([701c2909] by Rinfon)

* fix(tuya灯泡): 修复在tuya灯泡控制页面切换后台再回前台点击控制开关状态不对的问题 ([9cb75cf2] by 廖家杰)

* fix(Philips Hue): 修复hue离线时进入灯泡列表没有显示hue名字的问题 ([cc4694c9] by 廖家杰)

* fix(Philips Hue): 修复hue离线的时候不能修改名字的问题 ([fe12bd36] by 廖家杰)

* fix(Philips Hue): 问题修改 ([80d1864f] by 廖家杰)

* fix(Philips Hue): bug修改 ([eeba848d] by 廖家杰)

* fix(Philips Hue): 部分手机开关灯泡出现闪退的问题 ([10d72f7c] by 廖家杰)

*  (ble) 等待3分钟弹出提示弹窗后，按返回键出现app无响应情况;继续等待3分钟，没有再次出现提示弹 ([67412518] by LT)

*  (tuya)扫描添加方式配对时，添加超时定时器 ([3f703d77] by LT)

* fix(Philips Hue): 修复灯泡亮度不能设置100%的问题 ([70e79863] by 廖家杰)

* fix(push): 修改push配置 ([389218c6] by 廖家杰)

*  (ble)打开主机蓝牙提示框设置可返回 ([871d5762] by LT)

*  (switchbot)新主页的排序更新排在tuya插座下面 ([80573426] by LT)

* fix(tuya): 列表点击涂鸦灯泡进入设置时闪退；列表编辑涂鸦插座，进入不到改名界面。 ([467d4cbd] by 廖家杰)

* Revert " (switchbot)新主页排序修改，在灯泡之上。(ble)打开主机蓝牙提示框设置可返回" ([fcd5ea04] by LT)

* fix(Philips Hue): 修复点击DeviceSetting页面的Hue Item时崩溃的问题 ([acb786b2] by 廖家杰)

*  (switchbot)新主页排序修改，在灯泡之上。(ble)打开主机蓝牙提示框设置可返回 ([5008fd89] by LT)

*  新配件改名保存之后，列表有更新，再次进入改名界面，名字没有更新 ([20bf5516] by LT)

* fix(fcm): 修改fcm的json配置文件为api的 ([b74fdc8c] by 廖家杰)

*  (switchbot)列表页离线后的开关数不对，新主页黑名单编辑页开关数不对 ([292cc769] by LT)

* fix(user permission): 修复权限判断问题 ([1218324e] by Rinfon)

* fix(apns): 修复移除账号之后点击推送进去会进入被移除的主机里面 ([29d67a72] by Rinfon)

* fix(sim detail): 修改sim卡没有翻译问题 ([65dbfd8a] by Rinfon)

* (advance): 关闭wifi显示 ([bcb61428] by Rinfon)

* (simData): 修复sim卡信号量计算公司 ([3ade8b2c] by Rinfon)

* (scope): 修复guest权限显示问题 ([34839a65] by Rinfon)

*  触发配对添加涂鸦配件，wifi密码输入框的提示文案没有翻译的bug ([762c2ba1] by LT)

* switchbot-新主页的显示顺序以及黑名单页面灯泡没图片的bug ([7c21efdf] by LT)

* (apike):  修改涂鸦key ([c044a12c] by Rinfon Chan)

* (string.xml): 修改翻译key ([30ad6c42] by Rinfon)

*  修改翻译key ([0943cdfe] by Rinfon)

* #bug - fix homepage api url ([548e2878] by LT)


