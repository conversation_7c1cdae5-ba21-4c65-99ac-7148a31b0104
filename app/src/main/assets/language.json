{"Swedish": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "<PERSON><PERSON> på", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Larma av", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "AV", "Sat": "Sat", "Confirm delete?": "<PERSON><PERSON><PERSON><PERSON>fta radering?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Skalskydda", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Smart Plug", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "PÅ", "Every Sunday": "Every Sunday", "Label": "Label"}, "Turkish": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Arm", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Disarm", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OFF", "Sat": "Sat", "Confirm delete?": "Confirm delete?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Home Arm", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Smart Plug", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ON", "Every Sunday": "Every Sunday", "Label": "Label"}, "Italian": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Arm", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Disarm", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OFF", "Sat": "Sat", "Confirm delete?": "Confirm delete?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Home Arm", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Smart Plug", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ON", "Every Sunday": "Every Sunday", "Label": "Label"}, "Spanish": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Armar", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "<PERSON><PERSON><PERSON>", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "<PERSON><PERSON><PERSON>", "Sat": "Sat", "Confirm delete?": "¿Confirma eliminar?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Armado Hogar", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Enchufe Inteligente", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "Encendido", "Every Sunday": "Every Sunday", "Label": "Label"}, "Romanian": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Armare", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "<PERSON><PERSON><PERSON>", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OPRIT", "Sat": "Sat", "Confirm delete?": "Con<PERSON>rma<PERSON> sterger<PERSON>?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Armare pe timp de noapte", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Priza inteligenta", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "PORNIT", "Every Sunday": "Every Sunday", "Label": "Label"}, "Greek": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Arm", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Disarm", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OFF", "Sat": "Sat", "Confirm delete?": "Confirm delete?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Home Arm", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Smart Plug", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ON", "Every Sunday": "Every Sunday", "Label": "Label"}, "default": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Arm", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Disarm", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OFF", "Sat": "Sat", "Confirm delete?": "Confirm delete?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Home Arm", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Smart Plug", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ON", "Every Sunday": "Every Sunday", "Label": "Label"}, "French": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "<PERSON><PERSON>", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "<PERSON><PERSON><PERSON><PERSON>", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OFF", "Sat": "Sat", "Confirm delete?": "Confirmer la suppression ?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "<PERSON>", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Prise Intelligente", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ON", "Every Sunday": "Every Sunday", "Label": "Label"}, "English": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Arm", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Disarm", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OFF", "Sat": "Sat", "Confirm delete?": "Confirm delete?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Home Arm", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Smart Plug", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ON", "Every Sunday": "Every Sunday", "Label": "Label"}, "Arabic": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "تفعيل اذرع الروبورت", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "تعطيل اذرع الروبورت", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "ايقاف", "Sat": "Sat", "Confirm delete?": "تأكيد الحذف ", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "تفعيل اذرع الروبورت المنزلي ", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "القابس الذكي ", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "تشغيل ", "Every Sunday": "Every Sunday", "Label": "Label"}, "German": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "extern scharf", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "unscharf", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "AUS", "Sat": "Sat", "Confirm delete?": "löschen bestätigen?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "intern s<PERSON><PERSON>", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "<PERSON>", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "EIN", "Every Sunday": "Every Sunday", "Label": "Label"}, "hk": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "布防", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "撤防", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "關閉", "Sat": "Sat", "Confirm delete?": "確認刪除？", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "在家布防", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "智慧插座", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "打開", "Every Sunday": "Every Sunday", "Label": "Label"}, "Portuguese": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Ativar", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Desativar", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "OFF", "Sat": "Sat", "Confirm delete?": "Confirm delete?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Ativar-EmCasa", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Smart Plug", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ON", "Every Sunday": "Every Sunday", "Label": "Label"}, "Russian": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "Постановка", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "Снятие", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "ВЫКЛ", "Sat": "Sat", "Confirm delete?": "Подтвердить удаление?", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "Частичная постановка", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "Интеллектуальная розетка", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "ВКЛ", "Every Sunday": "Every Sunday", "Label": "Label"}, "cn": {"Repeat": "Repeat", "Security Status": "Security Status", "Every Saturday": "Every Saturday", "Arm": "布防", "anti-interference": "anti-interference", "Thu": "<PERSON>hu", "Add Task": "Add Task", "Every Friday": "Every Friday", "Every Thursday": "Every Thursday", "Disarm": "撤防", "Never": "Never", "Sun": "Sun", "Everyday": "Everyday", "Every Monday": "Every Monday", "Mon": "Mon", "Plugin": "Plugin", "Wed": "Wed", "Edit Task": "Edit Task", "Every Tuesday": "Every Tuesday", "OFF": "关闭", "Sat": "Sat", "Confirm delete?": "确认删除？", "Tue": "<PERSON><PERSON>", "Caution nearby interfering signal detected": "Caution nearby interfering signal detected", "Automate Tasks": "Automate Tasks", "Delete Task": "Delete Task", "Edit": "Edit", "HomeArm": "在家布防", "Edit push message when anti-interference triggered": "Edit push message when anti-interference triggered", "Select the label color": "Select the label color", "Task": "Task", "Smart Plug": "智能插座", "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.": "Enable anti-interference will trigger alarm when system receive interfering signal. Disable anti-interference then system will ignore interfering signal.", "Every Wednesday": "Every Wednesday", "ON": "开启", "Every Sunday": "Every Sunday", "Label": "Label"}}