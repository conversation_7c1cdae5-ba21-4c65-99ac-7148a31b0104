{"v": "5.9.6", "fr": 60, "ip": 0, "op": 180, "w": 60, "h": 60, "nm": "icon/accesories/fan/on", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Union", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 179, "s": [360]}], "ix": 10}, "p": {"a": 0, "k": [30.01, 30, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.869, 0.522], [0, 0], [0, -4.696], [0, 0], [0, 0], [-0.522, -2.869], [0, 0], [4.696, 0], [0, 0], [0, 0], [2.869, -0.522], [0, 0], [0, 4.696], [0, 0], [0, 0], [0.522, 2.869], [0, 0], [-4.696, 0], [0, 0]], "o": [[-2.343, -1.736], [0, 0], [4.62, -0.84], [0, 0], [0, 0], [1.736, -2.343], [0, 0], [0.84, 4.62], [0, 0], [0, 0], [2.343, 1.736], [0, 0], [-4.62, 0.84], [0, 0], [0, 0], [-1.736, 2.343], [0, 0], [-0.84, -4.62], [0, 0], [0, 0]], "v": [[-18.367, -13.888], [-16.98, -19.841], [-3.723, -22.251], [5.15, -14.846], [5.15, -6.531], [13.895, -18.332], [19.848, -16.944], [22.259, -3.69], [14.854, 5.183], [6.54, 5.183], [18.289, 13.888], [16.902, 19.841], [3.645, 22.251], [-5.228, 14.846], [-5.228, 6.63], [-13.895, 18.326], [-19.848, 16.938], [-22.259, 3.684], [-14.854, -5.189], [-6.626, -5.189]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.936, -0.694], [0, 0], [-1.674, 0], [-0.963, -0.444], [0, 0], [3.392, -0.617]], "o": [[-1.146, 0.208], [0, 0], [1.253, -0.921], [1.129, 0], [0, 0], [0, -3.448], [0, 0]], "v": [[-16.622, -17.874], [-17.176, -15.495], [-4.491, -6.096], [-0.015, -7.562], [3.15, -6.87], [3.15, -14.846], [-3.365, -20.284]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [1.341, 0], [0.992, -0.913], [0, 0], [0, 0], [0, -1.345], [-0.914, -0.991], [0, 0], [0, 0], [-1.341, 0], [-0.991, 0.913], [0, 0], [0, 0], [0, 1.34], [0.914, 0.991], [0, 0]], "o": [[-0.96, -0.79], [-1.454, 0], [0, 0], [0, 0], [-0.795, 0.962], [0, 1.454], [0, 0], [0, 0], [0.96, 0.79], [1.454, 0], [0, 0], [0, 0], [0.789, -0.96], [0, -1.454], [0, 0], [0, 0]], "v": [[3.515, -4.299], [-0.015, -5.562], [-3.782, -4.093], [-4.109, -3.791], [-4.307, -3.552], [-5.578, -0.014], [-4.107, 3.753], [-3.64, 4.261], [-3.593, 4.299], [-0.063, 5.562], [3.704, 4.093], [4.312, 3.532], [4.316, 3.535], [5.578, 0.008], [4.107, -3.759], [3.906, -3.978]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0.936, 0.694], [0, 0], [1.674, 0], [0.963, 0.444], [0, 0], [-3.392, 0.617]], "o": [[1.146, -0.208], [0, 0], [-1.253, 0.921], [-1.129, 0], [0, 0], [0, 3.448], [0, 0]], "v": [[16.544, 17.874], [17.098, 15.495], [4.412, 6.096], [-0.063, 7.562], [-3.228, 6.87], [-3.228, 14.846], [3.287, 20.284]], "c": true}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[-0.694, 0.936], [0, 0], [0, 1.674], [-0.448, 0.966], [0, 0], [-0.617, -3.392], [0, 0]], "o": [[0, 0], [-0.922, -1.253], [0, -1.133], [0, 0], [-3.448, 0], [0, 0], [0.208, 1.146]], "v": [[-15.502, 17.135], [-6.111, 4.463], [-7.578, -0.014], [-6.88, -3.189], [-14.854, -3.189], [-20.291, 3.326], [-17.881, 16.581]], "c": true}, "ix": 2}, "nm": "路径 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0.694, -0.936], [0, 0], [0, -1.674], [0.448, -0.966], [0, 0], [0.617, 3.392]], "o": [[-0.208, -1.146], [0, 0], [0.922, 1.253], [0, 1.133], [0, 0], [3.448, 0], [0, 0]], "v": [[17.881, -16.586], [15.502, -17.141], [6.111, -4.468], [7.578, 0.008], [6.88, 3.183], [14.854, 3.183], [20.291, -3.332]], "c": true}, "ix": 2}, "nm": "路径 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 0.5], "ix": 4}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Union", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}], "markers": []}