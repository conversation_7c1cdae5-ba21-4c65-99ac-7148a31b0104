[{"cmd": "connect"}, {"cmd": "disconnect"}, {"cmd": "get_inverter_info", "index": 0}, {"cmd": "get_inverter_input_info"}, {"cmd": "get_inverter_output_info"}, {"cmd": "set_inverter_open", "on": true, "indexs": [0, 1, 2]}, {"cmd": "get_battery_allinfo"}, {"cmd": "set_battery_alloff"}, {"cmd": "get_battery_info", "index": 1}, {"cmd": "get_battery_accessorystate", "index": 1}, {"cmd": "get_global_loadstate"}, {"cmd": "get_global_exceptions"}, {"cmd": "get_mcu_info"}, {"cmd": "get_cabinet_allinfo"}, {"cmd": "get_cabinet_state", "index": 1}, {"cmd": "get_mppt_state"}, {"cmd": "get_ev_state"}, {"cmd": "set_emergency_charge", "startTime": 1671009940, "endTime": 1671182740, "on": true}, {"cmd": "get_emergency_charge"}, {"cmd": "set_charge_strategies", "strategyType": 2, "smartReserve": 80, "goodPricePercentage": 20, "emergencyReserve": 20, "acceptablePricePercentage": 80}, {"cmd": "get_charge_strategies"}, {"cmd": "get_region_list"}, {"cmd": "update_region", "country_code": "CN", "country_name": "China", "city_name": "Guangzhou", "timezone": "Asia/Shanghai"}, {"cmd": "get_region"}, {"cmd": "set_virtualpowerplant", "on": true}, {"cmd": "get_virtualpowerplant"}, {"cmd": "get_communicate_signal"}, {"cmd": "get_advance_info"}, {"cmd": "reboot_inverter"}, {"cmd": "get_mode"}]