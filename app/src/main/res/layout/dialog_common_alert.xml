<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/alert_dialog_width"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:background="@drawable/alert_dialog_rectangle">
        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="80dp"
            android:layout_height="60dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:src="@drawable/icon_define_setting_warning"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>
        <com.dinsafer.ui.LocalTextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            style="@style/TextFamilyMedium"
            android:gravity="center_horizontal"
            tools:text="Low Battery Protection"
            android:textColor="@color/color_white_01"
            android:textSize="16sp"
            app:layout_constraintTop_toBottomOf="@+id/iv_cover"/>

        <com.dinsafer.ui.LocalTextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            style="@style/TextFamilyBodyL"
            tools:text="#device_Name, #family: The battery output has been turned off. Please charge the battery first."
            android:textColor="@color/color_white_01"
            android:textSize="16sp"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

        <com.dinsafer.ui.LocalCustomButton
            android:id="@+id/lcb_confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="14dp"
            android:paddingBottom="14dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:layout_marginTop="34dp"
            tools:text="check"
            android:textColor="@color/color_white_01"
            app:layout_constraintTop_toBottomOf="@+id/tv_content"
            app:normal_color="@color/ipc_subscription_change_plan_btn_bg"
            app:press_color="@color/ipc_subscription_change_plan_btn_bg"
            app:stroke_color="@color/ipc_subscription_change_plan_btn_bg"/>

        <com.dinsafer.ui.LocalTextView
            android:id="@+id/tv_cancel"
            style="@style/TextFamilyTittleM"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/stroke_bg_brand_text_r8"
            android:paddingTop="14dp"
            android:paddingBottom="14dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:visibility="gone"
            tools:text="Ignore"
            android:textColor="@color/color_brand_text"
            app:layout_constraintTop_toBottomOf="@+id/lcb_confirm" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>