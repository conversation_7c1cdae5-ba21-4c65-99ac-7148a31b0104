<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/device_management_add_layout">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_common">

            <com.dinsafer.ui.LocalTextView
                android:id="@+id/device_settting_hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/common_title_bar"
                android:background="@drawable/img_member_list_warning_bg"
                android:drawableLeft="@drawable/icon_member_setting_warningtips"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="@color/color_black_01"
                android:text="@string/device_settting_hint"
                android:textSize="@dimen/small_text_size"
                android:visibility="visible" />

            <!-- device Management-->
            <RelativeLayout
                android:id="@+id/device_management_label_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_settting_hint">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_label"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_management_label_height"
                    android:alpha="0.5"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_management_label"
                    android:textColor="@color/white"
                    android:textSize="@dimen/device_management_label_text_size" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/device_management_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_management_label_layout"
                android:background="@color/device_management_content_background">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_share_device"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_toLeftOf="@+id/device_management_share_nor"
                    android:alpha="0.8"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_share_device"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <ImageView
                    android:id="@+id/device_management_share_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_share_device"
                    android:layout_alignBottom="@id/device_management_share_device"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@+id/device_management_share_device"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_member_setting"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_share_device"
                    android:layout_toLeftOf="@+id/device_management_member_nor"
                    android:alpha="0.8"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_member_setting"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <com.dinsafer.ui.CircularView
                    android:id="@+id/device_management_member_avatar_1"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignTop="@+id/device_management_member_nor"
                    android:layout_marginTop="14.5dp"
                    android:layout_marginRight="20dp"
                    android:layout_toLeftOf="@+id/device_management_member_number"
                    android:src="@drawable/btn_setting_drop"
                    app:civ_border="true"
                    app:civ_borderColor="@color/colorMemberItemAvatarBorder"
                    app:civ_borderWidth="@dimen/member_item_border" />

                <com.dinsafer.ui.CircularView
                    android:id="@+id/device_management_member_avatar_2"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignTop="@+id/device_management_member_nor"
                    android:layout_marginTop="14.5dp"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@+id/device_management_member_number"
                    android:src="@drawable/btn_setting_drop"
                    app:civ_border="true"
                    app:civ_borderColor="@color/colorMemberItemAvatarBorder"
                    app:civ_borderWidth="@dimen/member_item_border" />

                <com.dinsafer.ui.CircularView
                    android:id="@+id/device_management_member_avatar_3"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignTop="@+id/device_management_member_nor"
                    android:layout_marginTop="14.5dp"
                    android:layout_toLeftOf="@+id/device_management_member_number"
                    android:src="@drawable/btn_setting_drop"
                    app:civ_border="true"
                    app:civ_borderColor="@color/colorMemberItemAvatarBorder"
                    app:civ_borderWidth="@dimen/member_item_border" />

                <TextView
                    android:id="@+id/device_management_member_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_member_setting"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/device_management_member_nor"
                    android:text="1"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize" />


                <ImageView
                    android:id="@+id/device_management_member_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_member_setting"
                    android:layout_alignBottom="@id/device_management_member_setting"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />


                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_member_setting"
                    android:background="@color/colorDeviceSettingLine"></View>
            </RelativeLayout>


            <!-- accessories Management-->
            <RelativeLayout
                android:id="@+id/device_management_accessories_label_layout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/device_management_label_height"
                android:layout_below="@+id/device_management_layout">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_accessories_label"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_management_label_height"
                    android:alpha="0.5"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_management_accessories_label"
                    android:textColor="@color/white"
                    android:textSize="@dimen/device_management_label_text_size" />

            </RelativeLayout>


            <!-- home arm status-->
            <RelativeLayout
                android:id="@+id/device_management_home_arm_label_layout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/device_management_label_height"
                android:layout_below="@+id/device_management_accessories_layout">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_home_arm_label"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_management_label_height"
                    android:alpha="0.5"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_management_home_label"
                    android:textColor="@color/white"
                    android:textSize="@dimen/device_management_label_text_size" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/device_management_home_arm_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_management_home_arm_label_layout"
                android:background="@color/device_management_content_background">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_home_arm_text"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_toLeftOf="@+id/device_management_home_arm_nor"
                    android:alpha="0.8"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_managent_home_arm"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <ImageView
                    android:id="@+id/device_management_home_arm_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_home_arm_text"
                    android:layout_alignBottom="@id/device_management_home_arm_text"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_home_arm_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_home_arm_text"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_plugin_text"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@+id/device_management_home_arm_text"
                    android:layout_toLeftOf="@+id/device_management_plugin_nor"
                    android:alpha="0.8"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_managent_plugin"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <ImageView
                    android:id="@+id/device_management_plugin_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_plugin_text"
                    android:layout_alignBottom="@id/device_management_plugin_text"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_doorbell_cap_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_plugin_text"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_doorbell_cap_text"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@+id/device_management_doorbell_cap_line"
                    android:layout_toLeftOf="@+id/device_management_doorbell_cap_nor"
                    android:alpha="0.8"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_managent_doorbell_cap"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <ImageView
                    android:id="@+id/device_management_doorbell_cap_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_doorbell_cap_text"
                    android:layout_alignBottom="@id/device_management_doorbell_cap_text"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_doorbell_cap_text"
                    android:background="@color/colorDeviceSettingLine"></View>
            </RelativeLayout>

            <!-- 增值服务-->
            <RelativeLayout
                android:id="@+id/device_management_increase_label_layout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/device_management_label_height"
                android:layout_below="@+id/device_management_home_arm_layout"
                android:visibility="gone">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_increase_label"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_management_label_height"
                    android:alpha="0.5"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_management_increase_label"
                    android:textColor="@color/white"
                    android:textSize="@dimen/device_management_label_text_size" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/device_management_increaes_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_management_increase_label_layout"
                android:background="@color/device_management_content_background"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_contacts"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_toLeftOf="@+id/device_management_contacts_nor"
                    android:alpha="0.8"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_managent_contacts"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <ImageView
                    android:id="@+id/device_management_contacts_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_contacts"
                    android:layout_alignBottom="@id/device_management_contacts"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_contacts"
                    android:background="@color/colorDeviceSettingLine"></View>
            </RelativeLayout>

            <!-- advanced setting-->
            <RelativeLayout
                android:id="@+id/device_management_advanced_label_layout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/device_management_label_height"
                android:layout_below="@+id/device_management_increaes_layout">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_advanced_label"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_management_label_height"
                    android:alpha="0.5"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_management_advanced_label"
                    android:textColor="@color/white"
                    android:textSize="@dimen/device_management_label_text_size" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/device_management_advanced_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_management_advanced_label_layout"
                android:layout_marginBottom="@dimen/device_management_margin_bottom"
                android:background="@color/device_management_content_background">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_safe_label"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_toLeftOf="@+id/device_management_safe_nor"
                    android:alpha="0.8"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_management_safe_label"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />


                <ImageView
                    android:id="@+id/device_management_safe_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_safe_label"
                    android:layout_alignBottom="@id/device_management_safe_label"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />


                <View
                    android:id="@+id/device_management_safe_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_safe_label"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <!--ready_to_arm-->
                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_ready_to_arm"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@+id/device_management_safe_label"
                    android:layout_toLeftOf="@+id/device_management_ready_to_arm_nor"
                    android:alpha="0.8"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/ready_to_arm"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />


                <ImageView
                    android:id="@+id/device_management_ready_to_arm_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_ready_to_arm"
                    android:layout_alignBottom="@id/device_management_ready_to_arm"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_ready_to_arm"
                    android:background="@color/colorDeviceSettingLine"></View>

                <!--more-->
                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_set_up"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@+id/device_management_ready_to_arm"
                    android:layout_toLeftOf="@+id/device_management_set_up_nor"
                    android:alpha="0.8"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:text="@string/device_management_advanced_label"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />


                <ImageView
                    android:id="@+id/device_management_set_up_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_set_up"
                    android:layout_alignBottom="@id/device_management_set_up"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_set_up"
                    android:background="@color/colorDeviceSettingLine"></View>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/device_management_accessories_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_management_accessories_label_layout"
                android:layout_toEndOf="@+id/device_management_increase_label_layout"
                android:layout_toRightOf="@+id/device_management_increase_label_layout"
                android:background="@color/device_management_content_background">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_ip_camera"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_toLeftOf="@+id/device_management_camera_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_ipc"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_ip_camera"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_camera_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_ip_camera"
                    android:layout_toLeftOf="@+id/device_management_camera_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/device_management_camera_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_ip_camera"
                    android:layout_alignBottom="@id/device_management_ip_camera"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_ip_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_ip_camera"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_smart_plug"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@+id/device_management_ip_camera"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentLeft="true"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_switch"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_smart_plug"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_smart_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_smart_plug"
                    android:layout_toLeftOf="@+id/device_management_smart_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/device_management_smart_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_smart_plug"
                    android:layout_alignBottom="@id/device_management_smart_plug"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_plug_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_smart_plug"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <!--tuya smart plugin-->

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_tuya_smart_plug"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@+id/device_management_plug_line"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentLeft="true"
                    android:layout_toLeftOf="@+id/device_management_tuya_smart_plugin_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_switch_tuya"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/tuya_smart_plugin"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_tuya_smart_plugin_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_tuya_smart_plug"
                    android:layout_toLeftOf="@+id/device_management_smart_nor"
                    android:background="@drawable/rectangle"
                    android:text=""
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/device_management_tuya_smart_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_tuya_smart_plug"
                    android:layout_alignBottom="@id/device_management_tuya_smart_plug"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_tuya_smart_plugin_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_tuya_smart_plug"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <!--tuya smart plugin-->


                <!--tuya color light-->

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_tuya_light"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@+id/device_management_tuya_smart_plugin_line"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentLeft="true"
                    android:layout_toLeftOf="@+id/device_management_tuya_light_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_bulb_tuya"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/tuya_color_light"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />


                <TextView
                    android:id="@+id/device_management_tuya_light_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_tuya_light"
                    android:layout_toLeftOf="@+id/device_management_smart_nor"
                    android:background="@drawable/rectangle"
                    android:text=""
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/device_management_tuya_light_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_tuya_light"
                    android:layout_alignBottom="@id/device_management_tuya_light"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_tuya_lighgt_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_tuya_light"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <!--tuya color light-->

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_security_accessories"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_tuya_lighgt_line"
                    android:layout_toLeftOf="@+id/device_management_security_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_security"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_security_accessories"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_security_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_security_accessories"
                    android:layout_toLeftOf="@+id/device_management_security_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/device_management_security_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_security_accessories"
                    android:layout_alignBottom="@id/device_management_security_accessories"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_security_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_security_accessories"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_door_sensor"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_security_line"
                    android:layout_toLeftOf="@+id/device_management_security_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_door_sensor"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_management_door_sensor"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_door_sensor_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_door_sensor"
                    android:layout_toLeftOf="@+id/device_management_door_sensor_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/device_management_door_sensor_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_door_sensor"
                    android:layout_alignBottom="@id/device_management_door_sensor"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_door_sensor_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_door_sensor"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>


                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_wireless"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_door_sensor_line"
                    android:layout_toLeftOf="@+id/device_management_wireless_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_siren"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_wireless"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_wireless_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_wireless"
                    android:layout_toLeftOf="@+id/device_management_wireless_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/device_management_wireless_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_wireless"
                    android:layout_alignBottom="@id/device_management_wireless"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View
                    android:id="@+id/device_management_wireless_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_wireless"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_keypad"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_wireless_line"
                    android:layout_toLeftOf="@+id/device_management_keypad_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_keypad"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_keypad"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_keypad_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_keypad"
                    android:layout_toLeftOf="@+id/device_management_keypad_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />


                <ImageView
                    android:id="@+id/device_management_keypad_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_keypad"
                    android:layout_alignBottom="@id/device_management_keypad"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <!-- doorbell -->
                <View
                    android:id="@+id/device_management_doorbell_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_keypad"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_doorbell"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_doorbell_line"
                    android:layout_toLeftOf="@+id/device_management_doorbell_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_visual_doorbell"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_doorbell"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_doorbell_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_doorbell"
                    android:layout_toLeftOf="@+id/device_management_doorbell_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/device_management_doorbell_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_doorbell"
                    android:layout_alignBottom="@id/device_management_doorbell"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <!--heart bit-->

                <View
                    android:id="@+id/device_management_heart_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_doorbell"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_heart"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_heart_line"
                    android:layout_toLeftOf="@+id/device_management_heart_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_keypad"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_heart_bit"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_heart_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_heart"
                    android:layout_toLeftOf="@+id/device_management_heart_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />


                <ImageView
                    android:id="@+id/device_management_heart_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_heart"
                    android:layout_alignBottom="@id/device_management_heart"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />


                <!--other plugin-->

                <View
                    android:id="@+id/device_management_keypad_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@id/device_management_heart"
                    android:layout_marginLeft="@dimen/device_management_padding_left"
                    android:background="@color/colorDeviceSettingLine"></View>

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/device_management_other_plugin"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_managerment_content_height"
                    android:layout_below="@id/device_management_keypad_line"
                    android:layout_toLeftOf="@+id/device_management_other_plugin_number"
                    android:alpha="0.8"
                    android:drawableLeft="@drawable/icon_device_setting_third_party"
                    android:drawablePadding="@dimen/device_management_content_icon_padding_right"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:singleLine="true"
                    android:text="@string/device_managent_other_plugin"
                    android:textColor="@color/colorDeviceText"
                    android:textSize="@dimen/device_management_content_text_size" />

                <TextView
                    android:id="@+id/device_management_other_plugin_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/device_management_other_plugin"
                    android:layout_toLeftOf="@+id/device_management_other_plugin_nor"
                    android:background="@drawable/rectangle"
                    android:text="120"
                    android:textColor="@color/colorMainFragmentListViewType"
                    android:textSize="@dimen/main_fragment_listview_item_typeSize"
                    android:visibility="gone" />


                <ImageView
                    android:id="@+id/device_management_other_plugin_nor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/device_management_other_plugin"
                    android:layout_alignBottom="@id/device_management_other_plugin"
                    android:layout_alignParentRight="true"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:src="@drawable/btn_device_setting_arrow" />

                <View

                    android:id="@+id/device_management_other_plugin_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/colorDeviceSettingLine"></View>
            </RelativeLayout>
        </RelativeLayout>
    </ScrollView>

    <RelativeLayout
        android:id="@+id/device_management_add_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/device_management_add_height"
        android:layout_alignParentBottom="true"
        android:background="@color/colorDeviceAddBtn">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@+id/device_management_add_text"
            android:src="@drawable/icon_dropdown_add" />

        <com.dinsafer.ui.LocalTextView
            android:id="@+id/device_management_add_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:alpha="0.8"
            android:text="@string/device_management_add"
            android:textColor="@color/white"
            android:textSize="@dimen/device_management_add_size" />
    </RelativeLayout>


    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        android:src="@color/transparent" />

    <ImageView
        android:id="@+id/device_management_add_downline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/device_management_add_layout"
        android:scaleType="fitXY"
        android:src="@color/transparent" />

</RelativeLayout>