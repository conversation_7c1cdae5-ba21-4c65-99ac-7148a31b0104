<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_common"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_title_bar_height"
            android:background="@color/colorCommonBar"
            android:clickable="true"
            android:focusableInTouchMode="true">

            <ImageView
                android:id="@+id/common_bar_back"
                android:layout_width="@dimen/common_title_bar_height"
                android:layout_height="@dimen/common_title_bar_height"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:scaleType="center"
                android:src="@drawable/icon_nav_back" />


            <com.dinsafer.ui.LocalTextView
                android:id="@+id/common_bar_title"
                style="@style/TextFamilyTittleM"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/main_fragment_bar_title"
                android:textColor="@color/colorCommonBarTitle" />

            <ImageView
                android:id="@+id/common_bar_left"
                android:layout_width="@dimen/common_title_bar_height"
                android:layout_height="@dimen/common_title_bar_height"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:scaleType="center"
                android:src="@drawable/btn_user_setting_select"
                android:visibility="invisible" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/common_bar_line_color" />
        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@+id/app_setting_password"
                    android:layout_marginTop="@dimen/siren_cell_top"
                    android:background="@color/colorDeviceSettingLine"></View>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/device_management_content_background"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_disarm_remind"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_disarm_switch"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_disarm"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.IOSSwitch
                            android:id="@+id/siren_setting_disarm_switch"
                            android:layout_width="@dimen/ios_switch_width"
                            android:layout_height="@dimen/ios_switch_height"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:layout_marginBottom="13dp"
                            app:offColor="@color/colorIOSSwitchOff"
                            app:strokeWidth="@dimen/ios_switch_stroke"
                            app:thumbTintColor="@color/colorBgWhite"
                            app:tintColor="@color/colorIIOSSwitchOn" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/device_management_padding_left"
                        android:background="@color/colorDeviceSettingLine"></View>


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_homearm_remind"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_homearm_switch"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_homearm"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.IOSSwitch
                            android:id="@+id/siren_setting_homearm_switch"
                            android:layout_width="@dimen/ios_switch_width"
                            android:layout_height="@dimen/ios_switch_height"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:layout_marginBottom="13dp"
                            app:offColor="@color/colorIOSSwitchOff"
                            app:strokeWidth="@dimen/ios_switch_stroke"
                            app:thumbTintColor="@color/colorBgWhite"
                            app:tintColor="@color/colorIIOSSwitchOn" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/device_management_padding_left"
                        android:background="@color/colorDeviceSettingLine" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_arm_remind"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_arm_switch"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_arm"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.IOSSwitch
                            android:id="@+id/siren_setting_arm_switch"
                            android:layout_width="@dimen/ios_switch_width"
                            android:layout_height="@dimen/ios_switch_height"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:layout_marginBottom="13dp"
                            app:offColor="@color/colorIOSSwitchOff"
                            app:strokeWidth="@dimen/ios_switch_stroke"
                            app:thumbTintColor="@color/colorBgWhite"
                            app:tintColor="@color/colorIIOSSwitchOn" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/colorDeviceSettingLine" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/siren_setting_light_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/siren_cell_top"
                    android:background="@color/device_management_content_background"
                    android:orientation="vertical">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_below="@+id/app_setting_password"
                        android:background="@color/colorDeviceSettingLine" />


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_disarm_light_mode"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_disarm_light_current_mode"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_disarm_light_mode"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_disarm_light_current_mode"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/siren_setting_disarm_light_mode"
                            android:layout_toLeftOf="@+id/siren_setting_disarm_light_mode_nor"
                            android:text="@string/flash"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:visibility="visible" />

                        <ImageView
                            android:id="@+id/siren_setting_disarm_light_mode_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/siren_setting_disarm_light_mode"
                            android:layout_alignBottom="@id/siren_setting_disarm_light_mode"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/device_management_padding_left"
                        android:background="@color/colorDeviceSettingLine" />


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_homearm_light_mode"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_homearm_light_current_mode"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_homearm_light_mode"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_homearm_light_current_mode"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/siren_setting_homearm_light_mode"
                            android:layout_toLeftOf="@+id/siren_setting_homearm_light_mode_nor"
                            android:text="@string/twinkle"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:visibility="visible" />

                        <ImageView
                            android:id="@+id/siren_setting_homearm_light_mode_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/siren_setting_homearm_light_mode"
                            android:layout_alignBottom="@id/siren_setting_homearm_light_mode"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/device_management_padding_left"
                        android:background="@color/colorDeviceSettingLine" />


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_arm_light_mode"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_arm_light_current_mode"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_arm_light_mode"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_arm_light_current_mode"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/siren_setting_arm_light_mode"
                            android:layout_toLeftOf="@+id/siren_setting_arm_light_mode_nor"
                            android:text="@string/always"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:visibility="visible" />

                        <ImageView
                            android:id="@+id/siren_setting_arm_light_mode_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/siren_setting_arm_light_mode"
                            android:layout_alignBottom="@id/siren_setting_arm_light_mode"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </RelativeLayout>


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/device_management_padding_left"
                        android:background="@color/colorDeviceSettingLine" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_sos_mode"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_sos_current_mode"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_sos"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_sos_current_mode"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/siren_setting_sos_mode"
                            android:layout_toLeftOf="@+id/siren_setting_sos_mode_nor"
                            android:text="@string/always"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:visibility="visible" />

                        <ImageView
                            android:id="@+id/siren_setting_sos_mode_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/siren_setting_sos_mode"
                            android:layout_alignBottom="@id/siren_setting_sos_mode"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_below="@+id/app_setting_password"
                        android:background="@color/colorDeviceSettingLine" />

                </LinearLayout>


                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@+id/app_setting_password"
                    android:layout_marginTop="@dimen/siren_cell_top"
                    android:background="@color/colorDeviceSettingLine" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/device_management_content_background"
                    android:orientation="vertical">


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_remind_volume"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_remind_volume_current_number"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_remind_volume"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_remind_volume_current_number"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/siren_setting_remind_volume"
                            android:layout_toLeftOf="@+id/siren_setting_remind_volume_nor"
                            android:text="0"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:visibility="visible" />

                        <ImageView
                            android:id="@+id/siren_setting_remind_volume_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/siren_setting_remind_volume"
                            android:layout_alignBottom="@id/siren_setting_remind_volume"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/device_management_padding_left"
                        android:background="@color/colorDeviceSettingLine" />


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_alarm_volume_mode"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_alarm_volume_current_number"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_setting_alarm_volume"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_alarm_volume_current_number"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/siren_setting_alarm_volume_mode"
                            android:layout_toLeftOf="@+id/siren_setting_alarm_volume_nor"
                            android:text="0"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:visibility="visible" />

                        <ImageView
                            android:id="@+id/siren_setting_alarm_volume_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/siren_setting_alarm_volume_mode"
                            android:layout_alignBottom="@id/siren_setting_alarm_volume_mode"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="@dimen/device_management_padding_left"
                        android:background="@color/colorDeviceSettingLine" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_alert_time"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/siren_setting_alert_time_current_number"
                            android:alpha="0.8"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:text="@string/siren_alert_time"
                            android:textColor="@color/colorDeviceText" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/siren_setting_alert_time_current_number"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/siren_setting_alert_time"
                            android:layout_toLeftOf="@+id/siren_setting_alert_time_nor"
                            android:text="0"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:visibility="visible" />

                        <ImageView
                            android:id="@+id/siren_setting_alert_time_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/siren_setting_alert_time"
                            android:layout_alignBottom="@id/siren_setting_alert_time"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_below="@+id/app_setting_password"
                        android:background="@color/colorDeviceSettingLine" />

                </LinearLayout>


            </LinearLayout>


        </ScrollView>

        <com.dinsafer.ui.LocalCustomButton
            android:id="@+id/btn_save"
            style="@style/CommonBottomBtnStyle"
            android:layout_alignParentBottom="true"
            android:text="@string/save" />

    </LinearLayout>
</layout>