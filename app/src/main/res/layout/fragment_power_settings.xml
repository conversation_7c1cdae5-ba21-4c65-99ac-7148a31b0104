<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="click"
            type="com.dinsafer.module.powerstation.settings.PowerSettingsFragment.OnClickHandler" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/device_management_content_background"
        android:clickable="true"
        android:orientation="vertical">

        <include
            android:id="@+id/common_bar"
            layout="@layout/common_title_bar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingTop="@dimen/power_settings_padding_top">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/device_management_content_background"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv"
                        android:layout_width="72dp"
                        android:layout_height="72dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="8dp"
                        android:src="@drawable/power_station" />

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_device_name"
                        style="@style/TextFamilyTittleS"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="@dimen/modify_plugs_id_margin_top"
                        android:clickable="true"
                        android:drawableEnd="@drawable/icon_edit_small"
                        android:drawablePadding="1dp"
                        android:focusable="true"
                        android:onClick="@{()->click.deviceName()}"
                        android:text="#device_name"
                        android:textColor="@color/white" />

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_region"
                        style="@style/TextFamilyCaptionS"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="15dp"
                        android:clickable="true"
                        android:drawableStart="@drawable/icon_location"
                        android:focusable="true"
                        android:paddingHorizontal="8dp"
                        android:paddingTop="4dp"
                        android:paddingBottom="2dp"
                        android:textColor="@color/color_white_02"
                        tools:text="Guangzhou, China" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:baselineAligned="false"
                    android:gravity="top"
                    android:orientation="horizontal"
                    android:padding="15dp">

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_battery_overview"
                        style="@style/TextFamilyCaptionM"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="5dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:drawableTop="@drawable/icon_setting_overview"
                        android:focusable="true"
                        android:gravity="center"
                        android:onClick="@{()->click.batteryOverview()}"
                        android:text="@string/ps_battery_overview"
                        android:textColor="@color/color_white_02" />


                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_accessories"
                        style="@style/TextFamilyCaptionM"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="5dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:drawableTop="@drawable/icon_setting_accessories"
                        android:focusable="true"
                        android:gravity="center"
                        android:onClick="@{()->click.accessories()}"
                        android:text="@string/ps_accessories"
                        android:textColor="@color/color_white_02" />

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_impact_strategies"
                        style="@style/TextFamilyCaptionM"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="5dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:drawableTop="@drawable/icon_setting_impact"
                        android:focusable="true"
                        android:gravity="center"
                        android:onClick="@{()->click.impactStrategies()}"
                        android:text="@string/power_impacts_and_strategies"
                        android:textColor="@color/color_white_02" />


                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_ev_charge"
                        style="@style/TextFamilyCaptionM"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="5dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:drawableTop="@drawable/icon_setting_evcharge"
                        android:focusable="true"
                        android:gravity="center"
                        android:onClick="@{()->click.evCharge()}"
                        android:text="@string/ps_ev_charge"
                        android:textColor="@color/color_white_02" />

                </LinearLayout>

                <include
                    android:id="@+id/view_alert_service"
                    layout="@layout/layout_ipc_cloud_service"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:onClick="@{()->click.powerCare()}"
                    android:visibility="gone"
                    tools:visibility="visible" />


                <LinearLayout
                    style="@style/ps_setting_item_outline_style"
                    android:paddingTop="15dp">

                    <LinearLayout
                        android:id="@+id/ll_temperature_units"
                        style="@style/ps_setting_item_style"
                        android:onClick="@{()->click.temperatureUnits()}">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_temperature_units_name"
                            style="@style/ps_setting_item_text_style"
                            android:text="@string/ps_temperature_units" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_temperature_units"
                            style="@style/ps_setting_item_value_style"
                            android:text="°C" />

                        <ImageView
                            android:id="@+id/iv_temperature_units_icon"
                            style="@style/ps_setting_item_right_icon"
                            android:src="@drawable/btn_device_setting_arrow" />
                    </LinearLayout>

                    <View style="@style/ps_setting_item_line_left_18" />

                    <LinearLayout
                        android:id="@+id/ll_grid_configuration"
                        style="@style/ps_setting_item_style"
                        android:onClick="@{()->click.gridConfiguration()}"
                        android:visibility="gone">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_grid_connect_setting"
                            style="@style/ps_setting_item_text_style"
                            android:text="@string/on_grid_configuration" />

                        <ImageView
                            android:id="@+id/iv_grid_connect_setting"
                            style="@style/ps_setting_item_right_icon"
                            android:src="@drawable/btn_device_setting_arrow" />
                    </LinearLayout>

                    <View
                        android:id="@+id/view_grid_configuration_line"
                        style="@style/ps_setting_item_line_left_18"
                        android:visibility="gone"/>

                    <LinearLayout
                        android:id="@+id/ll_total_load_setting"
                        style="@style/ps_setting_item_style"
                        android:onClick="@{()->click.totalLoadSetting()}"
                        android:visibility="gone" >
                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_total_load_setting"
                            style="@style/ps_setting_item_text_style"
                            android:text="@string/total_load_settings" />

                        <ImageView
                            android:id="@+id/iv_total_load_setting"
                            style="@style/ps_setting_item_right_icon"
                            android:src="@drawable/btn_device_setting_arrow" />
                    </LinearLayout>

                    <View
                        android:id="@+id/view_total_load_setting"
                        style="@style/ps_setting_item_line_left_18"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/ll_energy_setting"
                        style="@style/ps_setting_item_style"
                        android:onClick="@{()->click.energySetting()}"
                        android:visibility="gone">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_energy_setting_name"
                            style="@style/ps_setting_item_text_style"
                            android:text="@string/energy_settings" />

                        <ImageView
                            android:id="@+id/iv_energy_setting_icon"
                            style="@style/ps_setting_item_right_icon"
                            android:src="@drawable/btn_device_setting_arrow" />
                    </LinearLayout>

                    <View
                        android:id="@+id/view_energy_setting_line"
                        style="@style/ps_setting_item_line_left_18"
                        android:visibility="gone"/>

                    <LinearLayout
                        android:id="@+id/ll_user_guide"
                        style="@style/ps_setting_item_style"
                        android:onClick="@{()->click.userGuide()}">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_user_guide_name"
                            style="@style/ps_setting_item_text_style"
                            android:text="@string/ps_user_guide" />

                        <ImageView
                            android:id="@+id/iv_user_guide_icon"
                            style="@style/ps_setting_item_right_icon"
                            android:src="@drawable/btn_device_setting_arrow" />
                    </LinearLayout>

                    <View style="@style/ps_setting_item_line_left_18" />

                    <LinearLayout
                        android:id="@+id/ll_advanced_setting"
                        style="@style/ps_setting_item_style"
                        android:onClick="@{()->click.advancedSetting()}">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_advanced_setting_name"
                            style="@style/ps_setting_item_text_style"
                            android:text="@string/ps_advanced_settings" />

                        <ImageView
                            android:id="@+id/iv_advanced_setting_icon"
                            style="@style/ps_setting_item_right_icon"
                            android:src="@drawable/btn_device_setting_arrow" />
                    </LinearLayout>
                </LinearLayout>

                <View style="@style/ps_setting_item_line" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@color/electricity_bg_color" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>