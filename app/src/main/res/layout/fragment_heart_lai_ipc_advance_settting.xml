<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_common"
        android:clickable="true"
        android:focusable="true">

        <include
            android:id="@+id/common_title_bar"
            layout="@layout/common_title_bar" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/common_title_bar"
            android:background="@color/transparent"
            android:scrollbars="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <com.dinsafer.ui.DividerLinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="15dp"
                    android:background="@color/device_management_content_background"
                    android:orientation="vertical"
                    android:visibility="visible"
                    app:isShowBottomDivider="true"
                    app:isShowTopDivider="true"
                    tools:visibility="visible">

                    <com.dinsafer.ui.DividerLinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/device_management_content_background"
                        android:drawableRight="@drawable/btn_device_setting_arrow"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/ipc_setting_ipc_status"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/ipc_setting_ipc_status_text"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_right"
                            android:singleLine="true"
                            android:text="@string/ipc_status"
                            android:textColor="@color/color_white_01" />

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/ipc_setting_ipc_status_text"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/ios_switch_height"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:background="@drawable/rectangle"
                            android:text="@string/ipc_off_online"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:textSize="@dimen/main_fragment_listview_item_typeSize" />

                    </com.dinsafer.ui.DividerLinearLayout>

                    <com.dinsafer.ui.DividerLinearLayout
                        android:id="@+id/ll_pwd"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/device_management_content_background"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        app:isShowTopDivider="true"
                        app:topDividerMarginLeft="@dimen/device_management_padding_left">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/ipc_setting_ipc_password"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_right"
                            android:singleLine="true"
                            android:text="@string/ipc_password"
                            android:textColor="@color/color_white_01" />

                        <TextView
                            android:id="@+id/ipc_password_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:textSize="@dimen/main_fragment_listview_item_typeSize" />


                        <ImageView
                            android:id="@+id/ipc_password_nor"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@id/ipc_setting_ipc_password"
                            android:layout_alignBottom="@id/ipc_setting_ipc_password"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:src="@drawable/btn_device_setting_arrow" />

                    </com.dinsafer.ui.DividerLinearLayout>


                    <com.dinsafer.ui.DividerLinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/device_management_content_background"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        app:isShowTopDivider="true"
                        app:topDividerMarginLeft="@dimen/device_management_padding_left">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/ipc_setting_ipc_pid"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_toLeftOf="@+id/ipc_setting_ipc_pid_text"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_right"
                            android:singleLine="true"
                            android:text="@string/ipc_setting_ipc_pid"
                            android:textColor="@color/color_white_01" />

                        <com.dinsafer.ui.CopyMenuTextView
                            android:id="@+id/ipc_setting_ipc_pid_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/ipc_setting_ipc_pid"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:padding="5dp"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:textSize="@dimen/main_fragment_listview_item_typeSize" />

                    </com.dinsafer.ui.DividerLinearLayout>

                    <com.dinsafer.ui.DividerLinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        app:isShowTopDivider="true"
                        app:topDividerMarginLeft="@dimen/common_line_margin">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/tv_device_current_network_title"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="1dp"
                            android:layout_weight="1"
                            android:background="@color/device_management_content_background"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:minHeight="50dp"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_left"
                            android:singleLine="true"
                            android:text="@string/dscam_advance_current_network"
                            android:textColor="@color/color_white_01" />

                        <ProgressBar
                            android:id="@+id/pb_loading_ipc_wifi"
                            android:layout_width="@dimen/device_managerment_loading_height"
                            android:layout_height="@dimen/device_managerment_loading_height"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:indeterminateDrawable="@drawable/main_panel_item_loading"
                            android:indeterminateDuration="500"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tv_device_current_network"
                            style="@style/TextFamilyCaptionM"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginRight="15dp"
                            android:alpha="0.5"
                            android:gravity="center|right"
                            android:maxLines="1"
                            android:textColor="@color/white" />
                    </com.dinsafer.ui.DividerLinearLayout>

                    <com.dinsafer.ui.DividerLinearLayout
                        android:id="@+id/ipc_setting_ipc_ip_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/device_management_content_background"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        app:isShowTopDivider="true"
                        app:topDividerMarginLeft="@dimen/device_management_padding_left">

                        <com.dinsafer.ui.LocalTextView
                            android:id="@+id/ipc_setting_ipc_ip"
                            style="@style/TextFamilyBodyL"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/device_managerment_content_height"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:paddingLeft="@dimen/device_management_padding_left"
                            android:paddingRight="@dimen/device_management_padding_right"
                            android:singleLine="true"
                            android:text="@string/ipc_setting_ipc_ip"
                            android:textColor="@color/color_white_01" />

                        <ProgressBar
                            android:id="@+id/pb_loading_ipc_ip"
                            android:layout_width="@dimen/device_managerment_loading_height"
                            android:layout_height="@dimen/device_managerment_loading_height"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:indeterminateDrawable="@drawable/main_panel_item_loading"
                            android:indeterminateDuration="500"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/ipc_setting_ipc_ip_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/ipc_setting_ipc_ip"
                            android:layout_alignParentRight="true"
                            android:layout_marginRight="@dimen/ios_switch_margin_right"
                            android:textColor="@color/colorMainFragmentListViewType"
                            android:textSize="@dimen/main_fragment_listview_item_typeSize" />

                    </com.dinsafer.ui.DividerLinearLayout>

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/ipc_wifi_setting"
                        style="@style/TextFamilyBodyL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/device_management_content_background"
                        android:drawableRight="@drawable/btn_device_setting_arrow"
                        android:drawablePadding="@dimen/device_management_padding_left"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:minHeight="50dp"
                        android:paddingLeft="@dimen/device_management_padding_left"
                        android:paddingRight="20dp"
                        android:singleLine="true"
                        android:text="Wifi Setting"
                        android:textColor="@color/color_white_01"
                        app:isShowTopDivider="true"
                        app:topDividerMarginLeft="@dimen/device_management_padding_left" />


                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/ipc_network_config"
                        style="@style/TextFamilyBodyL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/device_management_content_background"
                        android:drawableRight="@drawable/btn_device_setting_arrow"
                        android:drawablePadding="@dimen/device_management_padding_left"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:minHeight="50dp"
                        android:paddingLeft="@dimen/device_management_padding_left"
                        android:paddingRight="20dp"
                        android:singleLine="true"
                        android:text="@string/modify_plugs_network_2"
                        android:textColor="@color/color_white_01"
                        app:isShowBottomDivider="true"
                        app:isShowTopDivider="true"
                        app:topDividerMarginLeft="@dimen/device_management_padding_left" />

                </com.dinsafer.ui.DividerLinearLayout>

                <com.dinsafer.ui.DividerLinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="15dp"
                    android:background="@color/device_management_content_background"
                    android:orientation="vertical"
                    android:visibility="visible"
                    app:isShowBottomDivider="true"
                    app:isShowTopDivider="true"
                    tools:visibility="visible">

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/sync_timezone_text"
                        style="@style/TextFamilyBodyL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/device_management_content_background"
                        android:drawableRight="@drawable/btn_device_setting_arrow"
                        android:drawablePadding="@dimen/device_management_padding_left"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:minHeight="50dp"
                        android:paddingLeft="@dimen/device_management_padding_left"
                        android:paddingRight="@dimen/device_management_padding_left"
                        android:singleLine="true"
                        android:text="Sync Timezone"
                        android:textColor="@color/color_white_01" />
                </com.dinsafer.ui.DividerLinearLayout>
            </LinearLayout>

        </ScrollView>

    </RelativeLayout>
</layout>