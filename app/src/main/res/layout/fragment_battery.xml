<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/electricity_bg_color"
        android:clickable="true"
        android:alpha="0.5">
        <LinearLayout
            android:id="@+id/ll_balance"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ll_last_for"
            android:orientation="horizontal"
            android:gravity="center_horizontal">
            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_balance_val"
                style="@style/power_station_battery_val_style"
                android:text="-"/>
            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_balance_unit"
                style="@style/power_station_battery_unit_style"
                android:text="@string/power_station_kWh"/>
            <ImageView
                android:id="@+id/iv_balance_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/power_station_battery_arrow_margin_left"
                android:src="@drawable/btn_device_setting_arrow"
                android:visibility="gone"/>
        </LinearLayout>

        <com.dinsafer.ui.LocalTextView
            android:id="@+id/tv_balance_key"
            style="@style/power_station_battery_key_style"
            app:layout_constraintBottom_toTopOf="@+id/ll_balance"
            app:layout_constraintLeft_toLeftOf="@+id/ll_balance"
            app:layout_constraintRight_toRightOf="@+id/ll_balance"
            android:text="@string/power_battery_balance" />


        <LinearLayout
            android:id="@+id/ll_last_for"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/ll_balance"
            android:orientation="horizontal"
            android:gravity="center_horizontal">
            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_last_for_hour"
                style="@style/power_station_battery_val_style"
                android:text="-"/>
            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_last_for_hour_unit"
                style="@style/power_station_battery_unit_style"
                android:text="@string/power_h"/>
            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_last_for_minute"
                style="@style/power_station_battery_val_style"
                android:layout_marginLeft="2dp"
                android:text="-"/>
            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_last_for_minute_unit"
                style="@style/power_station_battery_unit_style"
                android:text="@string/power_min"/>
        </LinearLayout>

        <com.dinsafer.ui.LocalTextView
            android:id="@+id/tv_last_for_key"
            style="@style/power_station_battery_key_style"
            app:layout_constraintBottom_toTopOf="@+id/ll_last_for"
            app:layout_constraintLeft_toLeftOf="@+id/ll_last_for"
            app:layout_constraintRight_toRightOf="@+id/ll_last_for"
            android:text="@string/power_battery_lasts_for" />
        <LinearLayout
            android:id="@+id/ll_mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/tv_balance_key"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:padding="@dimen/power_station_battery_mode_padding"
            android:layout_marginBottom="@dimen/power_station_battery_mode_margin_bottom"
            android:background="@drawable/shape_bg_power_mode"
            android:orientation="horizontal"
            android:gravity="center"
            android:visibility="invisible">
            <ImageView
                android:id="@+id/iv_mode_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:src="@drawable/icon_power_mode_smart_charge" />
            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_mode"
                style="@style/TextFamilyBodyS"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/reserve_mode_icon_text_space"
                android:textColor="@color/color_white_01"
                tools:text="@string/power_smart_charge"/>
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_power_battery_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/ll_mode"
            android:layout_marginBottom="@dimen/power_station_battery_marin_bottom">
            <com.dinsafer.module.powerstation.widget.BatteryChargeView
                android:id="@+id/power_battery_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:battery_charge_progress_text_size="28sp"
                app:battery_charge_unit_text_size="14sp"
                app:battery_charge_show_progress="true"
                app:battery_charge_scale="1.8"
                app:battery_charge_times="2.5"
                app:battery_charge_speed="0.3"/>

            <com.dinsafer.module.powerstation.widget.segmentbar.SegmentSlideBar
                android:id="@+id/ssb"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_0"
                app:layout_constraintTop_toTopOf="@+id/power_battery_view"
                app:layout_constraintBottom_toBottomOf="@+id/power_battery_view"
                app:layout_constraintLeft_toRightOf="@+id/power_battery_view"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginTop="@dimen/power_station_ssb_margin_top"
                android:layout_marginBottom="@dimen/power_station_ssb_margin_bottom"
                android:layout_marginLeft="@dimen/power_station_ssb_margin_left"
                app:ssb_bar_width="@dimen/power_station_ssb_width"
                app:ssb_bar_radius="@dimen/power_station_ssb_radius"
                app:ssb_triangle_size="@dimen/power_station_ssb_triangle_size"
                app:ssb_triangle_color="@color/color_white_02"/>

            <ImageView
                android:id="@+id/iv_updating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="@+id/power_battery_view"
                app:layout_constraintRight_toRightOf="@+id/power_battery_view"
                app:layout_constraintBottom_toBottomOf="@+id/power_battery_view"
                android:layout_marginBottom="@dimen/power_station_battery_updating_margin_bottom"
                android:src="@drawable/icon_ipc_updating"
                android:visibility="gone"/>
        </androidx.constraintlayout.widget.ConstraintLayout>



        <View
            android:id="@+id/view_disable"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0.5"
            android:background="@color/color_brand_dark_03"
            android:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>