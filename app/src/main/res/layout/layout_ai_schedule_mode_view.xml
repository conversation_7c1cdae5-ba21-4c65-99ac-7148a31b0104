<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false">
        <com.dinsafer.module.powerstation.widget.ai_schedule_mode_view.AIRecyclerView
            android:id="@+id/rv_data"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="10dp"
            android:paddingBottom="190dp"
            android:clipChildren="false"
            android:clipToPadding="false" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>