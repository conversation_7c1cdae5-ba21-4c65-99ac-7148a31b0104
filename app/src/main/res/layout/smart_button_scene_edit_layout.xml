<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_common"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical">

    <include
        android:id="@+id/common_bar"
        layout="@layout/common_title_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_brand_dark_02"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/tab_selector_l"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/device_management_content_background"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_selector_tab"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <com.dinsafer.ui.CustomizeSegmentTabLayout
                        android:id="@+id/stl_action_type_tab"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="@dimen/cms_tab_margin_top"
                        android:layout_marginEnd="16dp"
                        android:clipChildren="false"
                        app:tl_bar_color="#48000000"
                        app:tl_bar_stroke_width="0dp"
                        app:tl_iconVisible="false"
                        app:tl_indicator_anim_duration="150"
                        app:tl_indicator_anim_enable="true"
                        app:tl_indicator_color="@color/transparent"
                        app:tl_indicator_corner_radius="@dimen/smart_button_tab_corner_radius"
                        app:tl_textSelectColor="@color/smart_button_command_tab_text"
                        app:tl_textUnselectColor="@color/smart_button_command_tab_text" />

                    <ImageView
                        android:id="@+id/iv_action_type"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="22dp"
                        android:layout_marginBottom="30dp" />
                </LinearLayout>

                <ImageView
                    android:id="@+id/iv_single_top_image"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="20dp"
                    android:src="@drawable/img_rc_custom"
                    android:visibility="gone" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="@dimen/device_management_padding_left"
                    android:background="@color/bgColorPage" />

                <LinearLayout
                    android:id="@+id/ll_command"
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp">

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_command"
                        style="@style/TextFamilyBodyL"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/smart_button_command"
                        android:textColor="@color/smart_button_command" />

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_command_value"
                        style="@style/TextFamilyCaptionM"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:textColor="@color/smart_button_command_value" />

                    <ImageView
                        android:id="@+id/iv_command_nor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="11dp"
                        android:src="@drawable/btn_device_setting_arrow" />
                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/bgColorPage" />

            <LinearLayout
                android:id="@+id/ll_setting_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_customer_setting2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/color_brand_dark_01"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp">

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_customer_setting2"
                        style="@style/TextFamilyBodyL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingTop="16dp"
                        android:paddingBottom="16dp"
                        android:text="@string/smart_button_doorbell_volume"
                        android:textColor="@color/smart_button_command" />

                    <com.dinsafer.ui.VolumeControlerView
                        android:id="@+id/vcv_volume"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="15dp"
                        android:paddingTop="30dp"
                        android:paddingEnd="15dp"
                        android:paddingBottom="30dp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginStart="@dimen/device_management_padding_left"
                    android:background="@color/bgColorPage" />

                <LinearLayout
                    android:id="@+id/ll_customer_setting1"
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    android:background="@color/color_brand_dark_01"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp">

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_customer_setting1"
                        style="@style/TextFamilyBodyL"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/smart_button_doorbell_music"
                        android:textColor="@color/smart_button_command" />

                    <com.dinsafer.ui.LocalTextView
                        android:id="@+id/tv_customer_setting_value1"
                        style="@style/TextFamilyCaptionM"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:textColor="@color/smart_button_command_value" />

                    <ImageView
                        android:id="@+id/iv_customer_setting_nor1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="11dp"
                        android:src="@drawable/btn_device_setting_arrow" />
                </LinearLayout>
            </LinearLayout>

            <Space
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <com.dinsafer.ui.LocalCustomButton
                android:id="@+id/btn_top"
                style="@style/CommonOkBtnStyle"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text="@string/smart_button_test_doorbell"
                android:textColor="@color/colorLogout"
                android:textSize="@dimen/user_zone_logout_text_size"
                app:normal_color="@color/transparent"
                app:press_color="@color/transparent"
                app:stroke_color="@color/colorLogout" />

            <com.dinsafer.ui.LocalCustomButton
                android:id="@+id/btn_bottom"
                style="@style/CommonOkBtnStyle"
                android:layout_marginBottom="@dimen/common_btn_to_bottom_margin"
                android:gravity="center"
                android:text="@string/Done" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>