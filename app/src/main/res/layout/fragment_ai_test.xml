<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/electricity_bg_color">

        <include
            android:id="@+id/common_bar"
            layout="@layout/common_title_bar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:background="@color/color_black_02"
            app:layout_constraintTop_toBottomOf="@+id/common_bar"
            app:layout_constraintBottom_toBottomOf="parent">
            <com.dinsafer.module.powerstation.impacts.aischeduledchart.AIScheduledCombinedChart
                android:id="@+id/view_chart"
                android:layout_width="match_parent"
                android:layout_height="306dp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>