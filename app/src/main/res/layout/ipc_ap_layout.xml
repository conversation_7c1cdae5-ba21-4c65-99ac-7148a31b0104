<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_common"
        android:clickable="true"
        android:focusable="true">

        <RelativeLayout
            android:id="@+id/common_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_title_bar_height"
            android:background="@color/colorCommonBar"
            android:clickable="true"
            android:focusableInTouchMode="true">

            <ImageView
                android:id="@+id/common_bar_back"
                android:layout_width="@dimen/common_title_bar_height"
                android:layout_height="@dimen/common_title_bar_height"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:scaleType="center"
                android:src="@drawable/icon_nav_back" />


            <com.dinsafer.ui.LocalTextView
                android:id="@+id/common_bar_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/main_fragment_bar_title"
                android:textColor="@color/colorCommonBarTitle"
                android:textSize="@dimen/main_fragment_bar_title_size" />

            <ImageView
                android:id="@+id/common_bar_left_icon"
                android:layout_width="@dimen/common_title_bar_height"
                android:layout_height="@dimen/common_title_bar_height"
                android:layout_alignParentRight="true"
                android:scaleType="center"
                android:src="@drawable/btn_define_setting_select"
                android:visibility="invisible" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/common_bar_line_color" />
        </RelativeLayout>


        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/ipc_ap_image"
            android:layout_width="141dp"
            android:layout_height="109dp"
            android:layout_below="@+id/common_bar"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="40dp"
            app:lottie_autoPlay="false"
            app:lottie_fileName="json/animation_succeed.json"
            app:lottie_repeatMode="restart" />

        <com.dinsafer.ui.LocalTextView
            android:id="@+id/ipc_ap_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="@dimen/bind_account_hint_layout_description_margin"
            android:layout_marginRight="@dimen/bind_account_hint_layout_description_margin"
            android:gravity="center|left"
            android:text="@string/ipc_ap_hint"
            android:textColor="@color/white"
            style="@style/TextFamilyBodyL" />


        <com.dinsafer.ui.LocalCustomButton
            android:id="@+id/ipc_ap_next"
            style="@style/CommonOkBtnStyle"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/common_btn_to_bottom_margin"
            android:text="@string/ipc_ap_change_wifi" />

    </RelativeLayout>
</layout>
