<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/loding_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bgColorPage"
        android:clickable="true"
        android:focusable="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginBottom="96dp"
            android:background="@drawable/shape_loading_text_bg"
            android:minWidth="128dp"
            android:minHeight="96dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/loding_image"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/loding_text"
                android:layout_centerHorizontal="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:lottie_autoPlay="false"
                app:lottie_repeatCount="0"
                app:lottie_repeatMode="restart" />

            <com.dinsafer.ui.LocalTextView
                android:id="@+id/loding_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="12dp"
                android:text="Connecting.."
                android:textColor="@color/colorLogout"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>