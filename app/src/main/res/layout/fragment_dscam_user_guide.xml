<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <include
            android:id="@+id/title"
            layout="@layout/common_title_bar" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_common"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:animateLayoutChanges="true"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingBottom="80dp">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_extend_battery_life"
                    style="@style/TextFamilyBodyL"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:background="@color/device_management_content_background"
                    android:drawableRight="@drawable/btn_device_setting_arrow"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:minHeight="50dp"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:lineSpacingMultiplier="1"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:text="@string/dscam_guide_tittle_extend_battery_life"
                    android:textColor="@color/color_white_01"
                    app:isShowTopDivider="true" />

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_improve_accuracy"
                    style="@style/TextFamilyBodyL"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/device_management_content_background"
                    android:drawableRight="@drawable/btn_device_setting_arrow"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:minHeight="50dp"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:lineSpacingMultiplier="1"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:text="@string/dscam_guide_tittle_improve_accuracy"
                    android:textColor="@color/color_white_01"
                    app:isShowTopDivider="true"
                    app:topDividerMarginLeft="@dimen/common_line_margin" />

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_solar_charge"
                    style="@style/TextFamilyBodyL"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/device_management_content_background"
                    android:drawableRight="@drawable/btn_device_setting_arrow"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:lineSpacingMultiplier="1"
                    android:minHeight="50dp"
                    android:paddingLeft="@dimen/device_management_padding_left"
                    android:paddingRight="@dimen/device_management_padding_left"
                    android:text="@string/dscam_guide_tittle_solar_charge"
                    android:textColor="@color/color_white_01"
                    app:isShowTopDivider="true"
                    app:topDividerMarginLeft="@dimen/common_line_margin" />
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</layout>