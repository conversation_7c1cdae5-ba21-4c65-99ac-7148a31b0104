<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:id="@+id/contentLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_common">

        <!-- 导航栏 -->

        <RelativeLayout
            android:id="@+id/common_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_title_bar_height"
            android:background="@color/colorCommonBar"
            android:clickable="true"
            android:focusableInTouchMode="true">

            <ImageView
                android:id="@+id/common_bar_back"
                android:layout_width="@dimen/common_title_bar_height"
                android:layout_height="@dimen/common_title_bar_height"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:scaleType="center"
                android:src="@drawable/icon_nav_back" />

            <com.dinsafer.ui.LocalTextView
                android:id="@+id/common_bar_title"
                style="@style/TextFamilyTittleM"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginRight="@dimen/common_title_bar_height"
                android:layout_toRightOf="@+id/common_bar_back"
                android:gravity="center"
                android:text="@string/main_fragment_bar_title"
                android:textColor="@color/colorCommonBarTitle" />

            <FrameLayout
                android:id="@+id/fl_download_tittle"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/btn_download_tittle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="center"
                    android:src="@drawable/icon_nav_download" />

                <ProgressBar
                    android:id="@+id/pb_download_progress_tittle"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="3dp"
                    android:background="@drawable/pb_circle_shape"
                    android:indeterminate="false"
                    android:max="3"
                    android:progress="2"
                    android:progressDrawable="@drawable/circular_progress_bar" />
            </FrameLayout>

            <View
                android:id="@+id/v_divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/common_bar_line_color" />
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/rl_player"
            android:layout_width="match_parent"
            android:layout_height="220dp"
            android:layout_below="@id/common_bar"
            android:layout_gravity="center">

            <!--视频-->
            <tv.danmaku.ijk.media.player.video.IjkVideoView
                android:id="@+id/video_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="1dp"
                android:layout_marginTop="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/btn_back_land"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentTop="true"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:scaleType="center"
                android:src="@drawable/icon_compatible_ipc_fullscreen_back"
                android:visibility="gone"
                tools:visibility="visible" />

            <FrameLayout
                android:id="@+id/fl_download"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/btn_download"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="center"
                    android:src="@drawable/icon_compatible_ipc_fullscreen_download" />

                <ProgressBar
                    android:id="@+id/pb_download_progress"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="5dp"
                    android:background="@drawable/pb_circle_shape"
                    android:indeterminate="false"
                    android:max="3"
                    android:progress="2"
                    android:progressDrawable="@drawable/circular_progress_bar" />
            </FrameLayout>

            <ImageView
                android:id="@+id/resume_pause"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_centerInParent="true"
                android:src="@drawable/icon_ipc_play"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/glview_fullscreen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:padding="10dp"
                android:src="@drawable/icon_ipc_full_screen" />

            <LinearLayout
                android:id="@+id/fullscreen_control"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="50dp"
                android:layout_marginBottom="35dp"
                android:layout_toLeftOf="@+id/glview_fullscreen"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/seekBar_fullscreen_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="10dp"
                    android:textColor="@color/color_white_02"
                    android:textSize="12dp" />

                <com.dinsafer.ui.MySeekBar
                    android:id="@+id/seekBar_fullscreen"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:progressBackgroundTint="@color/color_white_03"
                    android:progressTint="@color/color_brand_primary"
                    android:thumb="@drawable/seekbar_thum"
                    android:thumbTint="@color/color_brand_primary"
                    android:visibility="visible" />
            </LinearLayout>

            <ProgressBar
                android:id="@+id/img_loading"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_centerInParent="true"
                android:indeterminateDrawable="@drawable/main_panel_item_loading"
                android:indeterminateDuration="500"
                android:minWidth="36dp"
                android:minHeight="36dp" />

            <RelativeLayout
                android:id="@+id/ll_function_btn_land"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/color_brand_black_01"
                android:clickable="true"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/btn_back_land2"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:scaleType="center"
                    android:src="@drawable/icon_compatible_ipc_fullscreen_back" />

                <ImageView
                    android:id="@+id/btn_function_play_land"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="bottom|center_horizontal"
                    android:layout_marginBottom="40dp"
                    android:src="@drawable/icon_ipc_static"
                    android:visibility="visible" />

                <LinearLayout
                    android:id="@+id/ll_btn_land"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_below="@id/btn_function_play_land"
                    android:layout_marginBottom="30dp"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <com.dinsafer.ui.LocalCustomButton
                        android:id="@+id/btn_go_live_land"
                        style="@style/CommonOkBtnStyle"
                        android:layout_width="275dp"
                        android:layout_gravity="bottom|center_horizontal"
                        android:text="@string/ipc_motion_detect_go_live" />

                    <com.dinsafer.ui.LocalCustomButton
                        android:id="@+id/btn_all_message_land"
                        style="@style/CommonOkBtnStyle"
                        android:layout_width="275dp"
                        android:layout_marginTop="30dp"
                        android:text="@string/contact_push_all"
                        android:textColor="@color/color_cancel_button_text"
                        android:visibility="visible"
                        app:normal_color="@color/transparent"
                        app:press_color="@color/transparent" />
                </LinearLayout>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ll_error"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/btn_error_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:src="@drawable/icon_ipc_offline" />

                <com.dinsafer.ui.LocalCustomButton
                    android:id="@+id/btn_error_retry"
                    style="@style/CommonOkBtnStyle"
                    android:layout_width="275dp"
                    android:text="@string/Retry" />
            </LinearLayout>
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/control_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/rl_player"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/seekbar_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="10dp"
                    android:textColor="@color/color_white_02"
                    android:textSize="12dp" />


                <!--<SeekBar-->
                <com.dinsafer.ui.MySeekBar
                    android:id="@+id/seekBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:layout_marginRight="18dp"
                    android:layout_marginBottom="18dp"
                    android:progressBackgroundTint="@color/color_white_03"
                    android:progressTint="@color/color_brand_primary"
                    android:thumb="@drawable/seekbar_thum"
                    android:thumbTint="@color/color_brand_primary" />

            </LinearLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/ll_function_btn_port"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/common_btn_to_bottom_margin"
                android:orientation="vertical"
                android:visibility="visible">

                <com.dinsafer.ui.LocalCustomButton
                    android:id="@+id/btn_go_live_port"
                    style="@style/CommonOkBtnStyle"
                    android:text="@string/ipc_motion_detect_go_live" />

                <com.dinsafer.ui.LocalCustomButton
                    android:id="@+id/btn_all_message_port"
                    style="@style/CommonOkBtnStyle"
                    android:text="@string/contact_push_all"
                    android:textColor="@color/color_cancel_button_text"
                    android:visibility="visible"
                    app:normal_color="@color/transparent"
                    app:press_color="@color/transparent" />
            </LinearLayout>
        </LinearLayout>

        <com.dinsafer.module_base.view.TopToast
            android:id="@+id/common_top_toast_ly"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorLogout_alpha"
            android:visibility="gone">

            <com.dinsafer.ui.LocalTextView
                android:id="@+id/common_top_toast"
                style="@style/TextFamilyTittleM"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center|left"
                android:maxLines="2"
                android:paddingLeft="20dp"
                android:paddingTop="24dp"
                android:paddingRight="20dp"
                android:paddingBottom="24dp"
                android:text="toast"
                android:textColor="@color/white"
                android:visibility="visible" />

        </com.dinsafer.module_base.view.TopToast>
    </RelativeLayout>
</layout>