<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/cl_main_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/main_panel_item_margin_left_right"
        android:layout_marginRight="@dimen/main_panel_item_margin_left_right"
        android:layout_marginBottom="15dp"
        android:visibility="gone">

        <!-- 有主机视图内容-START -->

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/main_toolbar_height"
            android:animateLayoutChanges="true"
            android:background="@drawable/shape_main_device_status_bg"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/rl_toolbar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <com.dinsafer.ui.device.ToolbarTabLayout
                    android:id="@+id/stl_action_type_tab"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/main_toolbar_tab_layout_height"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="42dp"
                    android:layout_marginEnd="28dp"
                    app:ttlBgColor="@color/main_new_toolbar_tab_bg"
                    app:ttlCounterTextColor="@color/main_new_toolbar_tab_text_select"
                    app:ttlCounterTextSize="@dimen/main_toolbar_tab_counter_textsize"
                    app:ttlGroupDivision="@dimen/main_toolbar_group_division"
                    app:ttlLoadingDuration="200"
                    app:ttlMainContainerHeight="@dimen/main_toolbar_tab_main_container_height"
                    app:ttlMainContainerWidth="@dimen/main_toolbar_tab_main_container_width"
                    app:ttlMainIndicatorBgColor="@color/main_new_toolbar_tab_select_bg"
                    app:ttlMainIndicatorCornerRadius="@dimen/main_toolbar_tab_indicator_corner_radius"
                    app:ttlMainIndicatorHeight="@dimen/main_toolbar_tab_indicator_height"
                    app:ttlMainIndicatorWidth="@dimen/main_toolbar_tab_indicator_width"
                    app:ttlOpenTransitionAnim="true"
                    app:ttlSelectedTextColor="@color/main_new_toolbar_tab_text_select"
                    app:ttlSosIcon="@drawable/btn_sos"
                    app:ttlSosText="SOS"
                    app:ttlTextSize="@dimen/main_toolbar_tab_name_textsize"
                    app:ttlUnSelectedTextColor="@color/main_new_toolbar_tab_text_unselect" />

            </LinearLayout>

            <!-- 加载中 -->
            <com.dinsafer.ui.LottieLoadingView
                android:id="@+id/view_loading"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_centerInParent="true"
                android:visibility="gone" />


            <!-- 断开连接模式页面 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_not_connected"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:animateLayoutChanges="true"
                android:visibility="gone">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_not_connect"
                    style="@style/TextFamilyBodyM"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="44dp"
                    android:paddingBottom="17dp"
                    android:text="@string/device_status_not_connect"
                    android:textColor="@color/main_device_status_offline_mode_color"
                    app:layout_constraintEnd_toStartOf="@id/tv_retry"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_retry"
                    style="@style/TextFamilyTittleS"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:drawableStart="@drawable/icon_retry"
                    android:drawablePadding="3dp"
                    android:paddingBottom="17dp"
                    android:text="@string/Retry"
                    android:textColor="@color/main_device_status_retry_color"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toEndOf="@id/tv_not_connect"
                    app:layout_constraintTop_toTopOf="@id/tv_not_connect"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_not_connected"
                    style="@style/TextFamilyTittleS"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_main_device_status_offline_status_bg"
                    android:paddingStart="22dp"
                    android:paddingTop="11dp"
                    android:paddingEnd="22dp"
                    android:paddingBottom="11dp"
                    android:text="@string/device_status_offline_mode"
                    android:textColor="@color/main_device_status_offline_mode_color"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_retry"
                    app:layout_constraintVertical_chainStyle="packed" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 离线模式 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_offline"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:animateLayoutChanges="true"
                android:visibility="gone">


                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_offline"
                    style="@style/TextFamilyTittleS"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:drawableStart="@drawable/icon_info"
                    android:drawablePadding="6dp"
                    android:text="@string/restrict_model_title"
                    android:textColor="@color/main_new_device_status_text_color"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 升级模式页面 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_upgrade"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:animateLayoutChanges="true"
                android:visibility="gone">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_panel_upgrade_hint"
                    style="@style/TextFamilyBodyM"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_horizontal"
                    android:text="@string/firmware_upgrading"
                    android:textColor="@color/main_device_status_offline_mode_color"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <!-- 被删除 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_unavailable"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_main_panel_item_unavailable_bg"
                android:visibility="gone">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_unavailable"
                    style="@style/TextFamilyBodyM"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/Unavailable"
                    android:textColor="@color/main_device_status_offline_mode_color"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </RelativeLayout>

        <!-- 有主机视图内容-END -->

        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="@dimen/panel_plugin_item_delete_width"
            android:layout_height="@dimen/panel_plugin_item_delete_width"
            android:paddingEnd="10dp"
            android:paddingBottom="10dp"
            android:layout_marginStart="-12dp"
            android:layout_marginTop="-12dp"
            android:alpha="0"
            android:scaleX="0"
            android:scaleY="0"
            android:src="@drawable/btn_plugin_follow_delete" />

    </RelativeLayout>
</layout>