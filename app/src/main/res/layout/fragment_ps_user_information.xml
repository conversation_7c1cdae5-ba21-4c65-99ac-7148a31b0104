<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/ll_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/electricity_bg_color"
        android:orientation="vertical"
        android:clickable="true">

        <include
            android:id="@+id/common_bar"
            layout="@layout/common_title_bar" />

        <LinearLayout
            android:id="@+id/ll_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:orientation="horizontal"
            android:paddingStart="@dimen/common_button_margin_left"
            android:paddingEnd="@dimen/common_button_margin_right">

            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_signup"
                style="@style/TextFamilyTittleHuge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/sign_up"
                android:textColor="@color/color_white_01" />

            <com.dinsafer.ui.LocalTextView
                android:id="@+id/tv_page"
                style="@style/TextFamilyTittleXS"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:text="1/4"
                android:textColor="@color/color_white_01" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nsc_parent"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true"
            android:paddingStart="@dimen/common_button_margin_left"
            android:paddingEnd="@dimen/common_button_margin_right">

            <LinearLayout
                android:id="@+id/ll_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_name_key"
                    style="@style/TextFamilyTittleXS"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="30dp"
                    android:text="@string/company_name"
                    android:textColor="@color/color_white_02" />

                <EditText
                    android:id="@+id/et_name_value"
                    style="@style/TextFamilyBodyL"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/alpha_input_rectangle"
                    android:maxLines="1"
                    android:paddingStart="15dp"
                    android:paddingTop="14dp"
                    android:paddingEnd="15dp"
                    android:paddingBottom="14dp"
                    android:singleLine="true"
                    android:textColor="@color/color_white_01"
                    android:textCursorDrawable="@drawable/shape_common_et_cursor" />

                <com.dinsafer.ui.LocalTextView
                    android:id="@+id/tv_eu_vat_number_key"
                    style="@style/TextFamilyTittleXS"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/eu_vat_number"
                    android:textColor="@color/color_white_02" />

                <EditText
                    android:id="@+id/et_eu_vat_number"
                    style="@style/TextFamilyBodyL"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/alpha_input_rectangle"
                    android:hint="Ex. DK88888888"
                    android:maxLines="1"
                    android:paddingStart="15dp"
                    android:paddingTop="14dp"
                    android:paddingEnd="15dp"
                    android:paddingBottom="14dp"
                    android:singleLine="true"
                    android:textColor="@color/color_white_01"
                    android:textColorHint="@color/color_white_03"
                    android:textCursorDrawable="@drawable/shape_common_et_cursor" />

                <com.dinsafer.ui.LocalTextView
                    style="@style/TextFamilyTittleXS"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/Email_Address"
                    android:textColor="@color/color_white_02" />

                <EditText
                    android:id="@+id/et_email_address"
                    style="@style/TextFamilyBodyL"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/alpha_input_rectangle"
                    android:maxLines="1"
                    android:paddingStart="15dp"
                    android:paddingTop="14dp"
                    android:paddingEnd="15dp"
                    android:paddingBottom="14dp"
                    android:singleLine="true"
                    android:inputType="textEmailAddress"
                    android:textColor="@color/color_white_01"
                    android:textCursorDrawable="@drawable/shape_common_et_cursor" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <com.dinsafer.ui.LocalCustomButton
            android:id="@+id/btn_next"
            style="@style/CommonOkBtnStyle"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="50dp"
            android:text="@string/next" />
    </LinearLayout>
</layout>