<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="bmt_exception_key_0001">Low Battery Protection</string>

    <string name="bmt_exception_key_0101">MCU Master Update Failure</string>
    <string name="bmt_exception_key_0102">MCU Slave Update Failure</string>
    <string name="bmt_exception_key_0103">Cabinet Update Failure</string>
    <string name="bmt_exception_key_0104">Inverter Update Failure</string>
    <string name="bmt_exception_key_0105">BMS 106 Update Failure</string>
    <string name="bmt_exception_key_0106">BMS Pack Update Failure</string>
    <string name="bmt_exception_key_0107">EV Update Failure</string>

    <string name="bmt_exception_key_0201">Inverter Shutdown for Unknown Reasons</string>

    <string name="bmt_exception_key_1000">Inverter - Battery Overvoltage</string>
    <string name="bmt_exception_key_1001">Inverter - Battery Undervoltage</string>
    <string name="bmt_exception_key_1002">Inverter - Battery Overtemperature</string>
    <string name="bmt_exception_key_1003">Inverter - Battery Overcurrent</string>
    <string name="bmt_exception_key_1004">Inverter - Battery Hardware overcurrent</string>
    <string name="bmt_exception_key_1005">Inverter - Booster Radiator 1 Overtemperature</string>
    <string name="bmt_exception_key_1006">Inverter - Booster Radiator 2 Overtemperature</string>
    <string name="bmt_exception_key_1007">Inverter - Booster Radiator 3 Overtemperature</string>
    <string name="bmt_exception_key_1008">Inverter - Booster Radiator 1 Fault</string>
    <string name="bmt_exception_key_1009">Inverter - Booster Radiator 2 Fault</string>
    <string name="bmt_exception_key_1010">Inverter - Booster Radiator 3 Fault</string>
    <string name="bmt_exception_key_1100">Inverter Output Overvoltage</string>
    <string name="bmt_exception_key_1101">Inverter Output Undervoltage</string>
    <string name="bmt_exception_key_1102"></string>
    <string name="bmt_exception_key_1103">High DC Component of Inverter Current</string>
    <string name="bmt_exception_key_1104">Inverter Current Overcurrent</string>
    <string name="bmt_exception_key_1105">Inverter Current Hardware Overcurrent</string>
    <string name="bmt_exception_key_1106">Inverter Output Short Circuit</string>
    <string name="bmt_exception_key_1107">105% Overloaded</string>
    <string name="bmt_exception_key_1108">120% Overloaded</string>
    <string name="bmt_exception_key_1109">200% Overloaded</string>
    <string name="bmt_exception_key_1110">Inverter Radiator Overtemperature</string>
    <string name="bmt_exception_key_1111">Inverter Radiator Failure</string>
    <string name="bmt_exception_key_1200">Instantaneous Overvoltage of Power Grid</string>
    <string name="bmt_exception_key_1201">Grid RMS Overvoltage Level 1</string>
    <string name="bmt_exception_key_1202">Grid RMS Overvoltage Level 2</string>
    <string name="bmt_exception_key_1203">Grid RMS Undervoltage Level 1</string>
    <string name="bmt_exception_key_1204">Grid RMS Undervoltage Level 2</string>
    <string name="bmt_exception_key_1205">Instantaneous Undervoltage of Grid</string>
    <string name="bmt_exception_key_1206">Grid Frequency: Overfrequency Level 1</string>
    <string name="bmt_exception_key_1207">Grid Frequency: Overfrequency Level 2</string>
    <string name="bmt_exception_key_1208">Grid Frequency: Underfrequency level 1</string>
    <string name="bmt_exception_key_1209">Grid Frequency: Underfrequency level 2</string>
    <string name="bmt_exception_key_1210">Abnormal Grid Envelope</string>
    <string name="bmt_exception_key_1211">Abnormal Phase Locking of Grid</string>
    <string name="bmt_exception_key_1212">Abnormal Detection of Stuck Buffer Relay</string>
    <string name="bmt_exception_key_1213">Abnormal Detection of Main Relay Sticking</string>
    <string name="bmt_exception_key_1300">Abnormal Inverter Insulation Detection</string>
    <string name="bmt_exception_key_1301">Abnormal Inverter Leakage Detection</string>
    <string name="bmt_exception_key_1302"></string>
    <string name="bmt_exception_key_1303">Inverter Bus Overvoltage Level 1</string>
    <string name="bmt_exception_key_1304">Inverter Bus Overvoltage Level 2</string>
    <string name="bmt_exception_key_1305">Inverter Bus Undervoltage Level 1</string>
    <string name="bmt_exception_key_1306">Inverter Bus Undervoltage Level 2</string>
    <string name="bmt_exception_key_1307">Inverter Bus Fault</string>
    <string name="bmt_exception_key_1308">Inverter Power Down</string>
    <string name="bmt_exception_key_1309">Transformer Overtemperature</string>
    <string name="bmt_exception_key_1310">Transformer Fault</string>
    <string name="bmt_exception_key_1311">Inverter Communication Fault</string>
    <string name="bmt_exception_key_1312">Inverter Fans Fault</string>
    <string name="bmt_exception_key_1400">PV Overvoltage</string>
    <string name="bmt_exception_key_1401">PV Undervoltage</string>
    <string name="bmt_exception_key_1402">PV Overcurrent</string>
    <string name="bmt_exception_key_1403">PV Radiator 1 Overtemperature</string>
    <string name="bmt_exception_key_1404">PV Radiator 1 Fault</string>
    <string name="bmt_exception_key_1600">Inverter Enabling Hardware Failure</string>
    <string name="bmt_exception_key_1601">Communication Fault between DC and Inverter</string>

    <string name="bmt_exception_key_2000">EV Leakage Protection</string>
    <string name="bmt_exception_key_2001">EV Overvoltage Protection</string>
    <string name="bmt_exception_key_2002">EV Undervoltage Protection</string>
    <string name="bmt_exception_key_2003">EV Short-circuit Protection</string>
    <string name="bmt_exception_key_2004">EV Overheating Protection</string>
    <string name="bmt_exception_key_2005">EV Overcurrent Level 1</string>
    <string name="bmt_exception_key_2006">EV Overcurrent Level 2</string>
    <string name="bmt_exception_key_2007">EV CP Level Exception</string>
    <string name="bmt_exception_key_2008">EV Relay Abnormal</string>
    <string name="bmt_exception_key_2009">EV Auxiliary Processor Exception</string>
    <string name="bmt_exception_key_2010">EV System 5v Exception</string>
    <string name="bmt_exception_key_2011">EV Connector Communication Failure</string>

    <string name="bmt_exception_key_3000">PV overvoltage</string>
    <string name="bmt_exception_key_3001">PV undervoltage</string>
    <string name="bmt_exception_key_3002">PV overcurrent</string>
    <string name="bmt_exception_key_3003">PV radiator 1 overtemperature</string>
    <string name="bmt_exception_key_3004">PV radiator 1 fault</string>

    <string name="bmt_exception_key_4000">Cabinet Water Sensor Alarm</string>
    <string name="bmt_exception_key_4001"></string>
    <string name="bmt_exception_key_4002">Fans in Cabinet Abnormal</string>
    <string name="bmt_exception_key_4003">Accessories of Cabinet Communication Failure</string>

    <string name="bmt_battery_exception_key">Battery Packs Exception</string>
    <string name="bmt_exception_key_5000">Battery DFET ON</string>
    <string name="bmt_exception_key_5001">Battery CFET ON</string>
    <string name="bmt_exception_key_5002">Battery Hardware Failure</string>
    <string name="bmt_exception_key_5003">Battery Discharge High Temperature Alarm</string>
    <string name="bmt_exception_key_5004">Battery Low Voltage Alarm</string>
    <string name="bmt_exception_key_5005">Battery Discharge Overcurrent Alarm</string>
    <string name="bmt_exception_key_5006">Battery Fet High Temperature Protection</string>
    <string name="bmt_exception_key_5007">Battery Charging High Temperature Protection</string>
    <string name="bmt_exception_key_5008">Battery Charging Low Temperature Protection</string>
    <string name="bmt_exception_key_5009">Battery Discharging High Temperature Protection</string>
    <string name="bmt_exception_key_5010">Battery Discharging Low Temperature Protection</string>
    <string name="bmt_exception_key_5011">Battery Discharging Short Circuit Protection</string>
    <string name="bmt_exception_key_5012">Battery Charging Overcurrent Protection</string>
    <string name="bmt_exception_key_5013">Battery Low Voltage Protection</string>
    <string name="bmt_exception_key_5014">Battery High Voltage Protection</string>
    <string name="bmt_exception_key_5015">Battery pack Communication Failure</string>

    <string name="bmt_exception_key_6000">Offline Overload Protection</string>
    <string name="bmt_exception_key_6001">The SOC of Pack is too Low</string>
    <string name="bmt_exception_key_6002">Bus Voltage is too Low</string>
    <string name="bmt_exception_key_6003">The Temperature of the System Test is too High</string>
    <string name="bmt_exception_key_6004">The Temperature of the System Test is too Low</string>
    <string name="bmt_exception_key_6005">Wrong Number of Cabinet Indication</string>
    <string name="bmt_exception_key_6006">Battery Performance Deterioration</string>
    <string name="bmt_exception_key_6007">Inverter and L-out Wiring Error</string>
    <string name="bmt_exception_key_6008">Meter Wiring Error</string>
    <string name="bmt_exception_key_6009">gb_box_unexist_for_hard 9</string>
    <string name="bmt_exception_key_6010">Battery in Maintenance</string>

    <string name="bmt_exception_key_7000">MCU Communication Failure</string>
    <string name="bmt_exception_key_7001">Three-phase Meter Communication Failure</string>

    <string name="bmt_exception_key_9000">Grid Outage, Emergency Reserve is Enabled</string>
    <string name="bmt_exception_key_9001">Battery Full</string>
    <string name="bmt_exception_key_9002">Low Battery</string>
    <string name="bmt_exception_key_9003">Battery is Added to cabinet #battery_index.</string>
    <string name="bmt_exception_key_9004">Battery is removed from cabinet #battery_index.</string>
    <string name="bmt_exception_key_9005">EV Car Plugged in and Smart Charging Awaits</string>
    <string name="bmt_exception_key_9006">EV Car Plugged in and Scheduled Charging Awaits</string>
    <string name="bmt_exception_key_9007">EV Car Charging Starts.</string>
    <string name="bmt_exception_key_9008">EV Car Charging End.</string>
    <string name="bmt_exception_key_9009">Heating Film Starts Working (Cabinet #battery_index)</string>
    <string name="bmt_exception_key_9010">Heating Film Stop Working (Cabinet #battery_index)</string>
    <string name="bmt_exception_key_9011">Fans Packs Start Working (Cabinet #cabinet_index).</string>
    <string name="bmt_exception_key_9012">Fans Packs Stop Working (Cabinet #cabinet_index).</string>
    <string name="bmt_exception_key_9013">Severe Weather Alarm</string>
    <string name="bmt_exception_key_9104">Device Offline</string>
    <string name="bmt_exception_key_9100">#device_name is deleted from #family.</string>

    <string name="bmt_exception_key_9300">Delaying EV Charge for Lower Rates.</string>
    <string name="bmt_exception_key_9303">EV charging will start at scheduled time.</string>
    <string name="bmt_exception_key_9305">EV starts charging.</string>
    <string name="bmt_exception_key_9309">EV Charging has ended.</string>
    <string name="bmt_exception_key_9310">EV charging was automatically limited to prevent fuse overload during peak demand. Tap to restart EV charge.</string>
    <string name="bmt_exception_key_9311">Your vehicle has completed the #charge_quantity kWh charge goal.</string>

    <string name="bmt_exception_content_1">#device_name, #family: The battery output has been turned off. Please charge the battery first.</string>
    <string name="bmt_exception_content_2">#device_name, #family: Please try restarting. If it cannot be solved, please contact customer service.</string>
    <string name="bmt_exception_content_3">#device_name, #family: Please reduce the power consumption and try to restart. If it cannot be solved, please contact customer service.</string>
    <string name="bmt_exception_content_4">#device_name, #family: Please pay attention to the environment. If it cannot be solved, please contact customer service.</string>
    <string name="bmt_exception_content_5">#device_name, #family: Please pay attention to the battery usage. If it cannot be solved, please contact customer service.</string>
    <string name="bmt_exception_content_6">#device_name, #family</string>
    <string name="bmt_exception_content_7">#device_name, #family: Weather forecasts indicate that there will be extreme weather in the next few days. It is recommended to turn on emergency charging and prepare energy storage in advance.</string>
    <string name="bmt_exception_content_8">#device_name, #family: Please try rebooting by tapping the button next to the screen. If it cannot be solved, please contact customer service.</string>

    <string name="bmt_exception_without_7001">No Three-phase Meter</string>
    <string name="bmt_exception_without_1203">No Grid Connection</string>
    <string name="bmt_exception_without_1204">No Grid Connection</string>
    <string name="bmt_exception_without_1205">No Grid Connection</string>

    <string name="bmt_exception_key_9203">#days days left on your 4G Traffic Package.</string>
    <string name="bmt_exception_content_9203">#device_name: #days days left on your 4G Traffic Package. To ensure constant protection, please recharge in time.</string>
    <string name="bmt_exception_key_9204">Your 4G Traffic Package has expired.</string>
</resources>