<resources>
    <string name="app_name">Emaldo</string>

    <!--common-->

    <!--main-->
    <string name="main_fragment_bar_title">HOME</string>
    <string name="main_fragment_listview_section">EventList</string>
    <string name="main_fragment_viewpager_chart_no_data">没有数据</string>
    <string name="toolbar_arm_text">Arm</string>
    <string name="toolbar_disarm_text">Disarm</string>
    <string name="toolbar_homearm_text">HomeArm</string>
    <string name="toolbar_more_text">More</string>
    <string name="toolbar_retract_text">Retract</string>
    <string name="toolbar_customize">Customize Toolbar</string>
    <string name="customize_title">Quick Config</string>
    <string name="customize_item_name">Add Accessory</string>
    <string name="customize_setting">Custom Setting</string>


    <string name="device_management_actionsheet_title">选择分享方式</string>
    <string name="device_management_actionsheet_create_qr">生成主机二维码</string>
    <string name="device_management_actionsheet_share_to_phone">发送分享码到手机</string>
    <string name="device_management_actionsheet_share_to_email">发送分享链接到邮箱</string>
    <string name="share_qr_device_name">DEVICE NAME</string>
    <string name="share_qr_device_note">Scan QR code to add this device</string>
    <string name="about_us_title">About Us</string>
    <string name="help_title">Help</string>


    <string name="exit_delay_time">Exit Delay</string>

    <string name="sos_setting_notification_title">短信通知</string>
    <string name="sos_setting_notification_content">当险情发生时，除通过网络报警外还可以通过sms进行报警，而这一切是无需你插入sim卡。</string>

    <string name="sos_setting_sos_message_more">(增值服务)</string>


    <string name="modify_plugs_rename">Rename it:</string>
    <string name="modify_plugs_title">Accessory Setting</string>
    <string name="qr_fail">二维码识别失败</string>


    <string name="action_settings">Settings</string>
    <string name="user_zone_title">User Setting</string>

    <string name="user_zone_inbox">Inbox</string>


    <string name="change_password_save">Save</string>


    <string name="change_email_description">Please enter your email address:</string>
    <string name="send_email_confirm_text">OK</string>

    <string name="bind_account_hint_user_name">username</string>


    <string name="my_family_text">My Families</string>
    <string name="my_device_action_title">主机操作</string>
    <string name="my_device_change_device">Switch device</string>
    <string name="my_device_rename_device">Rename device</string>
    <string name="my_device_delete_device">Delete device</string>

    <string name="faq_layout_title">FAQ</string>
    <string name="faq_settting_1">请设置安全问题1:</string>
    <string name="faq_setting_hint">请选择预设安全问题</string>
    <string name="faq_setting_answer_hint">您的答案</string>
    <string name="faq_settting_2">请设置安全问题2:</string>
    <string name="faq_settting_3">请设置安全问题3:</string>


    <string name="welcome_description_3">要添加主机还是登录？</string>


    <string name="ap_step_hint_3">（打开主机ap动画）</string>

    <string name="Retry">Retry</string>

    <string name="Reset">Reset</string>
    <string name="via_ethernet">Via  Ethernet</string>

    <string name="ap_step_three_lan">LAN</string>
    <string name="ap_step_three_4g">4G Network</string>
    <string name="not_supported">Not Supported</string>


    <string name="password_dialog_title_confirm">Re-enter your new passcode</string>

    <string name="connect">Connect</string>

    <!--<string name="connect_hint">连接中...</string>-->
    <string name="uploading_hint">Loading...</string>
    <string name="group_manage_family">Manage Families</string>

    <string name="home">Home</string>
    <string name="offline_hint">Device offline</string>
    <string name="offline_choose_other">Choose another device</string>
    <string name="offline_retry">Reconnect</string>
    <string name="sos_stop">Stop the alarm</string>
    <string name="sos_ignore">Ignore for this time</string>


    <!--local-->

    <string name="device_management_label">Device Management</string>
    <string name="device_managent_share_device">Share Device</string>
    <string name="device_managent_member_setting">Member Setting</string>
    <string name="users">Users</string>
    <string name="device_management_accessories_label">Accessories Management</string>
    <string name="device_managent_ip_camera">IP Camera</string>
    <string name="device_managent_smart_buttom">Smart Button</string>
    <string name="device_managent_signal_repeater_plug">Signal Repeater Plug</string>
    <string name="device_managent_smart_plug">Smart Plug</string>
    <string name="device_managent_security_accessories">Security Accessory</string>
    <string name="device_managent_wireless">Wireless Siren</string>
    <string name="device_managent_remote_control">Remote Control</string>
    <string name="device_managent_keypad">Keyboard/Key Tags</string>
    <string name="device_management_safe_label">Security Notification Settings</string>
    <string name="device_managent_knoce_over">Knock over to SOS</string>

    <string name="device_management_increase_label">增值服务</string>
    <string name="device_managent_contacts">Emergency Contacts</string>
    <string name="device_management_advanced_label">Advanced Setting</string>
    <string name="device_managent_advanced">Successfully set up after 72</string>
    <string name="device_management_add">Add Accessory</string>
    <string name="device_management_home_label">Scene</string>
    <string name="device_managent_home_arm">Custom Home Arm</string>
    <string name="device_managent_arm_rules">Arm Rules</string>
    <string name="device_managent_panel_status">Panel Status</string>
    <string name="device_management_add_scanQR">By scanning QR Code</string>
    <string name="device_management_add_scanQR_album">Scan QR Code from the album</string>
    <string name="device_management_add_cancel">Cancel</string>
    <string name="failed_try_again">Failed. Try again.</string>
    <string name="failed_family_signed">Failed, Family Signed.</string>
    <string name="reselect">Reselect</string>
    <string name="success">Succeed</string>

    <string name="advanced_setting_label">The below setting will directly affect the operation of the device, please think before making a change.</string>
    <string name="advanced_setting_entry_delay">Entry Delay</string>
    <string name="advanced_setting_exit_delay">Exit Delay</string>
    <string name="advanced_setting_device_sound">Voice prompt when changing alarm status</string>
    <string name="advanced_setting_sos_time">Siren Duration</string>
    <string name="advanced_setting_4g">4G Network Selection</string>
    <string name="advanced_setting_4g_auto">Automatic</string>
    <string name="advanced_setting_4g_apn">APN Node Name</string>
    <string name="advanced_setting_4g_hint_type">Type Here</string>
    <string name="advanced_setting_change_network">Change the network for device</string>
    <string name="advanced_setting_change_password">Reset the password for device</string>
    <string name="advanced_setting_reset">Reset device</string>
    <string name="advanced_setting_language">主机语言</string>
    <string name="advanced_setting_version">Version of firmware</string>
    <string name="advanced_setting_deviceid">Device ID</string>
    <string name="advanced_setting_sos_setting">Duress Alarm Setting</string>
    <string name="advanced_setting_cms">CMS</string>
    <string name="advanced_setting_sia_protocol">SIA Protocol</string>
    <string name="advanced_setting_primary_ip_address">Primary IP Address</string>
    <string name="advanced_setting_ip_address">IP Address</string>
    <string name="advanced_setting_port">Port</string>
    <string name="advanced_setting_secondary_ip_address">Secondary IP Address(Optional)</string>
    <string name="advanced_setting_account_number">Account Number</string>
    <string name="advanced_setting_encryption_label">Encryption(Optional)</string>
    <string name="advanced_setting_encryption">Encryption</string>
    <string name="advanced_setting_encryption_optional">Encryption(Optional)</string>
    <string name="advanced_setting_encryption_key">Encryption Key</string>
    <string name="advanced_setting_cms_hint">Type Here</string>
    <string name="advanced_setting_cms_network_tcp">TCP</string>
    <string name="advanced_setting_cms_network_udp">UDP</string>
    <string name="advanced_setting_restrict_mode">Offline Mode SMS</string>


    <string name="time_picker_countdown_voice_prompt">Countdown voice prompt</string>


    <string name="app_setting_system">System Setting</string>
    <string name="app_setting_language">App Language</string>
    <string name="app_setting_version">App Version</string>
    <string name="app_setting_password">Passcode Lock</string>
    <string name="app_setting_information">Information</string>
    <string name="app_setting_about_us">About us</string>
    <string name="third_party_licenses">Third Party Licenses</string>
    <string name="app_setting_help">Help</string>
    <string name="app_setting_rating">Encouragement and rating</string>
    <string name="app_setting_feedback">Feedback</string>
    <string name="app_setting_promotion">Promotion</string>
    <string name="customize_home_arem_description">Custom the working security accessories under home arm.</string>

    <string name="definehomearm_description">The device will delay alarm according to the below setting time. Alarm could be canceled if disarm before entry delay work.
</string>
    <string name="definehomearm_confirm">Confirm</string>

    <string name="feedback_note">Please write down your valuable feedback:
</string>
    <string name="feedback_email">Please leave an email address should you request a further contact:
</string>
    <string name="feedback_send">Send</string>

    <string name="feedback_send_success">Message sent. Thank you and we appreciate your valuable feedback.
</string>
    <string name="ok">OK</string>

    <string name="invalid_email_address">Invalid email address</string>

    <string name="ipc_control_move">Move</string>
    <string name="ipc_control_listener_off">Sound Off</string>
    <string name="ipc_control_listener_on">Sound On</string>
    <string name="ipc_control_talk">Talk</string>
    <string name="ipc_control_snapshot">Snapshot</string>


    <string name="smart_plugs_list_delete">Delete</string>
    <string name="menu_edit">Edit</string>
    <string name="change_permission_delete">Delete</string>
    <string name="smart_plugs_list_delete_yes">Yes</string>
    <string name="smart_plugs_list_delete_no">No</string>
    <string name="smart_plugs_list_delete_confirm">Confirm delete?</string>
    <string name="confirm_quit_family">Confirm Quit?</string>

    <string name="change_permission_confirm">This user is the last administrator in this system, confirm adjusting its authority?</string>
    <string name="Confirm">Confirm</string>
    <string name="Discard">Discard</string>
    <string name="Cancel">Cancel</string>

    <string name="password_dialog_title">Enter the password of device</string>
    <string name="set_password_dialog_title">Set a Password for the Panel</string>
    <string name="re_enter_password_dialog_title">Re-enter Your Password</string>
    <string name="re_password_panel_dialog_tittle">Set a new password</string>
    <string name="re_password_panel_dialog_tittle2">Confirm the password</string>
    <string name="password_dialog_title_2">Enter new password</string>
    <string name="set_sos_password_title">Set password for duress alarm</string>
    <string name="re_password_dialog_title">Confirm the password</string>
    <string name="change_device_name_title">Rename device</string>

    <string name="sos_message_dialog_save">Save</string>
    <string name="reset_password_hint">Reset will wipe all data of the device, including remove the relationship of all users and various accessories with this device. cautiously operating.</string>
    <string name="sos_message_dialog_title">Content</string>

    <string name="sos_setting_description">When you are under duress, you can input the duress alarm password to stop siren and send preset SOS message to other user.
</string>
    <string name="sos_setting_password_title">Set password for duress alarm</string>
    <string name="sos_setting_password_content">The system will activate the duress alarm function automatically as soon as you key in the password for duress alarm.</string>
    <string name="sos_setting_sos_message_title">Custom your SOS message</string>
    <string name="sos_setting_sos_message_content">Better be a special content which can only be understood by specific user. This message will be pushed to specific user when you are in danger.</string>
    <string name="sos_message_dialog_hint">Tap here to input the content sent when duress alarm activated. </string>
    <string name="loging_hint">Connecting</string>
    <string name="Back">Back</string>
    <string name="Next">Next</string>

    <string name="ipv4_hint">IP Address</string>
    <string name="mask_hint">Netmask</string>
    <string name="ap_step_dns">DNS</string>
    <string name="ap_step_gateway">Gateway</string>
    <string name="ap_step_lan_dhpc">DHCP</string>
    <string name="ap_step_lan_other">Manual</string>

    <string name="forget_password_hint">Username</string>
    <string name="login_sign_in">Login</string>
    <string name="login_pass_hint">Password</string>
    <string name="login_name_hint">Email / Username</string>
    <string name="change_password_forgot">Forget password?</string>
    <string name="send_verification_key">Verification code sent to:</string>
    <string name="change_phone_message_hint">Verification code</string>


    <string name="forget_new_password_hint">Set a new password</string>
    <string name="forget_confirm_password_hint">Confirm the password</string>

    <string name="forget_password_submit">Submit</string>
    <string name="forget_password_error_code">The verification code you entered is incorrect. Please check or request verification again.</string>
    <string name="type_anew">Type anew</string>
    <string name="request_again">Request again</string>

    <string name="welcome_description">Please write your Welcome Pattern here</string>
    <string name="welcome_description_welcome">（欢迎语）</string>
    <string name="welcome_title">Please write your Welcome Pattern here</string>

    <string name="welcome_add_device">Start to add a device</string>
    <string name="welcome_add_scan">Access point setting mode</string>
    <string name="welcome_add_scan_from_album">Scan the QR code shared by friends to add a host</string>

    <string name="welcome_login">Login</string>

    <string name="ap_step_wifi_pass_hint">Enter wifi password</string>
    <string name="ap_step_wifi_pass_hint_confirm">Retype wifi password</string>
    <string name="welcome_set_network">Configure network</string>
    <string name="welcome_exit_ap">Quit access point setting mode</string>

    <string name="ap_step_device_name_hint">Name your device or just use the default one</string>
    <string name="start_use">Start to use</string>


    <string name="ap_step_three_wifi_text">Choose wireless network</string>
    <string name="ap_step_three_lan_text">Choose wired network</string>
    <string name="ap_step_three_4g_text">Choose 4G Network</string>
    <string name="ap_step_4g_network_selection">4G Network Selection</string>
    <string name="ap_step_wifi_connect_result_success">Success in network connection</string>
    <string name="ap_step_two_hint">Please connect with the wifi started with iotc_</string>
    <string name="ap_step_one_hint">Please turn on the wifi setting button on the device.</string>
    <string name="modify_plugs_family">Family</string>
    <string name="modify_plugs_network">Sonic Wave</string>
    <string name="modify_plugs_network_2">Network Setting</string>
    <string name="modify_plugs_network_hint">Please enter Wi-Fi account name and password. After a tone to be emitted by the camera, please put your phone close to the camera to start paring.</string>
    <string name="modify_plugs_network_wifi_ssid">WiFi SSID</string>
    <string name="modify_plugs_network_wifi_password">Password</string>
    <string name="modify_plugs_network_sonic">Wifi Setting</string>

    <string name="ap_step_wifi_connect_result_hint_1">The previous wifi and password you selected is</string>
    <string name="ap_step_wifi_connect_result_retry">Reconnect the network
</string>
    <string name="ap_step_wifi_connect_result_reset">Reconfigure the network</string>

    <string name="ap_step_wifi_connect_result_fail">Fail to connect to network</string>
    <string name="ap_step_wifi_connect_result_wifi">Wifi</string>
    <string name="ap_step_wifi_connect_result_4g">4G</string>
    <string name="ap_step_wifi_connect_result_ethernet">Ethernet</string>
    <string name="ap_step_wifi_connect_result_password">Password</string>

    <string name="bind_account_hint_layout_description_text">We kindly suggest setting an account to get back all the device and accessories setting in case you change your mobile phone number.</string>
    <string name="bind_account_hint_layout_confirm_text">Bind with an account</string>
    <string name="bind_account_hint_layout_cancel_text">Not now</string>
    <string name="bind_account_title">Bind with an account</string>
    <string name="bind_account_hint">You can change your username only once</string>
    <string name="bind_account_wrong_hint_text">The user name can only be English character and Arab number, and more than 3 characters.</string>
    <string name="bind_account_password_hint">Set a new password</string>
    <string name="bind_account_confirm_password_hint">Confirm the password</string>
    <string name="bind_account_confirm_text">Bind</string>

    <string name="verification_content">We kindly suggest you verify your phone number and email, so that you can still use phone number or email to handle your account in case you forget login password. What\'s more, to enjoy more security services.</string>
    <string name="verification_email_text">Email verification</string>
    <string name="verification_phone_text">Phone number verification</string>
    <string name="verification_title">Verification</string>

    <string name="change_password_title">Reset Password</string>
    <string name="change_password_old_hint">Enter the old password</string>
    <string name="change_password_new_hint">Set a new password</string>
    <string name="change_password_confirm_hint">Confirm the password</string>

    <string name="change_phone_zone_hint">Choose an area code</string>
    <string name="change_phone_hint">Enter the mobile phone number</string>
    <string name="change_phone_code">Verification code</string>

    <string name="change_phone_confirm">Confirm</string>
    <string name="choose_phone_zone_title">Choose an area code</string>

    <string name="change_phone_title">Verification</string>

    <string name="change_phone_send">Request Verification code</string>


    <string name="change_email_layout">Verification</string>
    <string name="change_email_hint">Please enter your email address:</string>
    <string name="change_email_send">Send</string>
    <string name="next">Next</string>
    <string name="change_binding_text">Change the Binding</string>

    <string name="user_zone_phone_hint">Phone Setting</string>
    <string name="user_zone_email_hint">Mail Setting</string>
    <string name="user_zone_family_hint">My Families</string>
    <string name="user_zone_password_hint">Password</string>
    <string name="user_zone_logout">Logout</string>
    <string name="user_zone_more_setting_text">More security setting</string>
    <string name="user_zone_device_hint">My Devices</string>

    <!--<string name="user_zone_change_avatar_title">更换头像</string>-->
    <string name="user_zone_get_from_media">Import from album</string>
    <string name="user_zone_get_from_camera">Take photo</string>
    <string name="load_succeed">Load Success</string>
    <string name="load_fail">Load Fail</string>
    <string name="refresh_succeed">Refresh Success</string>
    <string name="refresh_fail">Refresh Fail</string>
    <string name="pull_to_refresh">Pull to refresh</string>
    <string name="pullup_to_load">Loading...</string>
    <string name="release_to_refresh">Release to refresh</string>
    <string name="refreshing">Refreshing</string>
    <string name="release_to_load">Release to load more</string>
    <string name="loading">Loading...</string>


    <string name="password_set_password">Turn Passcode On</string>
    <string name="password_set_password_close">Turn Passcode Off</string>
    <string name="password_set_password_change">Change Passcode</string>
    <string name="app_password_setting">Set Passcode</string>
    <string name="app_password_setting_one">Enter your new passcode</string>
    <string name="app_password_setting_confirm">Re-enter your new passcode</string>
    <string name="password_set_password_hint">After 5 consecutive incorrect passcode attempts, you will be log out of your account. When you log in, you can reset your passcode</string>
    <string name="password_check_title">Enter Passcode</string>
    <string name="password_check_wrong_title">Enter Passcode</string>
    <string name="password_check_del">Delete</string>

    <string name="contact_header_main">Users in this device</string>
    <string name="contact_header_other">Other contacts</string>

    <string name="contact_add">Add manually</string>
    <string name="contact_add_input">Import from contacts</string>
    <string name="contact_add_title">选择添加方式</string>
    <string name="contact_push_noti">Push Notification</string>
    <string name="contact_push_sms">SMS Notification</string>
    <string name="contact_push_phone">Phone call</string>
    <string name="contact_push_hint">SMS and call notification request the users to verify their phone number first.</string>
    <string name="contact_push_title">Notification</string>
    <string name="contact_edit_name">Name(optional)</string>
    <string name="contact_edit_delete">Delete</string>
    <string name="change_permission_title">User Setting</string>
    <string name="change_permission_admin">Admin</string>
    <string name="change_permission_user">User</string>
    <string name="change_permission_guest">Guest</string>
    <string name="sos_disarm_success">re-arm?</string>
    <string name="device_managent_sos_message">Notifications Language</string>
    <string name="device_status_sim">SIM</string>
    <string name="device_status_accessory">Accessory</string>
    <string name="device_status_accessory_all">All Accessories</string>
    <string name="device_status_accessory_normal">No exception was detected</string>
    <string name="device_status_accessory_low_battery">Low battary</string>
    <string name="device_status_accessory_apart">Apart</string>
    <string name="reset_device_content">Reset to default settings</string>
    <string name="reset_device_all">Erase all content and settings</string>
    <string name="had_removed_device">You have been removed from #family</string>
    <string name="device_pluged_in">#family: alarm panel pluged in</string>
    <string name="device_power_disconnect">#family: alarm panel power disconnect</string>
    <string name="device_plug_power_low">#family: #plugin low battary, please charge</string>

    <string name="wifi_other">Other</string>
    <string name="ethernet">Ethernet</string>
    <string name="ipc_time_hint">Last opened:</string>
    <string name="ipc_time_hint_2">never</string>
    <string name="my_family_current">Current Family</string>
    <string name="listview_empty">You have not add any accesories, please add one first.</string>
    <string name="my_family_other">Family Management</string>
    <string name="reset_device_content_hint">Confirm to reset device?</string>
    <string name="reset_device_content_confirm">Reset</string>
    <string name="password_not_match">The two passwords you entered did not match</string>
    <string name="ap_add_device_check_hint">Sending confirmation to device and resetting network, this may takes minutes, please be patient and do not quit the app.</string>
    <string name="failed_to_connect">Failed to connect device. Try again.</string>
    <string name="Rota">rotation</string>
    <string name="bad_ping">Poor device network</string>
    <string name="user_network_problem">Network anomaly, please check your network setting.</string>
    <string name="permisson_change">You are now authorized as</string>
    <string name="device_delete">Your authorization has been deleted.</string>
    <string name="exit_delay_hint">Will execute according to exit delay setting</string>
    <string name="entry_delay_hint">Will execute according to entry delay setting</string>
    <string name="sim_hint">No SIM card detected</string>
    <string name="more">More</string>
    <string name="modifyaccessoryhint">Name your accessory</string>
    <string name="update_hint">Firmware upgrading, please try connecting later</string>
    <string name="homearm_empty_hint">The homearm function will activate after adding door/PIR sensor.</string>
    <string name="homearm_hint_2">Choose accessories which can trigger the entry delay.</string>
    <string name="exit_app">Do you confirm to exit the app?</string>
    <string name="no_delay">No delay</string>
    <string name="silent">Silent</string>
    <string name="empty_sos_hint">You have not preset SOS message, please preset first</string>
    <string name="smart_plugs_info">SmartPlug Info</string>
    <string name="smart_plugs_status">SmartPlug Status</string>
    <string name="smart_plugs_on">ON</string>
    <string name="smart_plugs_off">OFF</string>
    <string name="share_qr_hint">This QR code can only be scanned once and will automatically expire in 60 minutes. If the code expires before use, please generate a new one. To share with more people, please generate a new code for each person you want to share your device with.</string>
    <string name="qr_expired">QR code expired, Please generate a new one.</string>
    <string name="qr_been_used">QR code has been used, Please generate a new one.</string>
    <string name="qr_been_bind">Existed device. No need to scan QR code to add it.</string>
    <string name="permission_no_sim_hint">SMS and call notification request the users to verify their phone number first.</string>
    <string name="contact_detail_text">Choose message type</string>
    <string name="contact_push_all">All Message</string>
    <string name="contact_push_call_sys">System Message</string>
    <string name="contact_push_call_info">Status Message</string>
    <string name="contact_push_call_sos">Alarm Message</string>
    <string name="contact_push_empty">No Message</string>
    <string name="choose_push_title">Choose message type</string>
    <string name="contact_sys_description">System daily log message</string>
    <string name="contact_sos_description">Alarm message triggered by accesories, users and device. Suggest turn on due concerns with security.</string>
    <string name="contact_info_description">Status of the device and users</string>
    <string name="devie_management_add_tiggle">Learn mode</string>
    <string name="tiggle_loading">Learning</string>
    <string name="tiggle_success">Learning successfully</string>
    <string name="tiggle_fail">Learning successfully</string>
    <string name="low_power_hint">Device battery lower than</string>
    <string name="has_know">OK</string>
    <string name="tiggle_has_plug">This accessory has been added, cannot be added duplicately.</string>
    <string name="ipc_has_plug">This device has already been added, you can configure the network for it in the original home, or delete it and add it again</string>
    <string name="tigger_alarm">Trigger the alarm</string>
    <string name="setting_custom_btn">Custom Remote Controller</string>
    <string name="user_logouted">Abnormal account, please login again.</string>
    <string name="custom_remote_hint">Choose a smart plug to be controlled by remote controller.</string>
    <string name="custom_remote_title">Custom Remote Controller</string>
    <string name="custom_remote_hint_no">You have not add any smart plugs, please add one first.</string>
    <string name="tiggle_deivce_title">Learn mode</string>
    <!--<string name="tiggle_hint">请接上配件电源或打开开关，使配件处于工作状态，见提示灯亮起或听到反馈响声，并稍候...</string>-->
    <string name="door_tiggle_hint_1">Door Window Sensor learning step1 description</string>
    <string name="door_tiggle_hint_2">Door Window Sensor learning step2 description</string>
    <string name="pir_tiggle_hint_1">Pir Sensor learning step1 description</string>
    <string name="pir_tiggle_hint_2">Pir Sensor learning step2 description</string>
    <string name="chuangao_pir_tiggle_hint_1">Third party Pir Sensor learning step1 description</string>
    <string name="chuangao_pir_tiggle_hint_2">Third party Pir Sensor learning step2 description</string>
    <string name="gas_tiggle_hint_1">Gas Sensor learning step1 description</string>
    <string name="gas_tiggle_hint_2">Gas Sensor learning step2 description</string>
    <string name="smoke_tiggle_hint_1">Smoke Sensor learning step1 description</string>
    <string name="smoke_tiggle_hint_2">Smoke Sensor learning step2 description</string>
    <string name="vibration_tiggle_hint_1">Vibration Sensor learning step1 description</string>
    <string name="vibration_tiggle_hint_2">Vibration Sensor learning step2 description</string>
    <string name="panic_tiggle_hint_1">Panic Button learning step1 description</string>
    <string name="panic_tiggle_hint_2">Panic Button learning step2 description</string>
    <string name="remote_tiggle_hint_1">Remote Controller learning step1 description</string>
    <string name="remote_tiggle_hint_2">Remote Controller learning step2 description</string>
    <string name="smart_plug_tiggle_hint_1">Smart Plug learning step1 description</string>
    <string name="smart_plug_tiggle_hint_2">Smart Plug learning step2 description</string>
    <string name="wireless_siren_tiggle_hint_1">Wireless Siren learning step1 description</string>
    <string name="wireless_siren_tiggle_hint_2">Wireless Siren learning step2 description</string>
    <string name="wireless_siren_tiggle_hint_other">Open the rear cover of the siren, press the button as above graphic instruction until the siren makes a sound. Then press Next.</string>
    <string name="wireless_tiggle_hint_1">Wireless Keypad learning step1 description</string>
    <string name="wireless_tiggle_hint_2">Wireless Keypad learning step2 description</string>
    <string name="tiggle_device_next">Start learning</string>
    <string name="learn_step_1">Learning Step 1</string>
    <string name="learn_step_2">Learning Step 2</string>
    <string name="learn_step_cancel">Stop learning?</string>
    <string name="learn_step_fail">Failed to detect the accessory, make sure the accessory is successfully triggered.</string>
    <string name="liquid_tiggle_hint_1">Liquid Sensor learning step1 description</string>
    <string name="liquid_tiggle_hint_2">Liquid Sensor learning step2 description</string>
    <string name="rfid_tiggle_hint_1">RFID learning step1 description</string>
    <string name="rfid_tiggle_hint_2">RFID learning step2 description</string>
    <string name="co_tiggle_hint_1">CO Detector learning step1 description</string>
    <string name="co_tiggle_hint_2">CO Detector learning step2 description</string>
    <string name="tiggle_ws_connect_error">Connection failed. Phone and device should be in the same network.</string>
    <string name="home_arm_add_plugs">Add Accessory</string>
    <string name="device_added">Device addded</string>
    <string name="change_message_type_text">简单中文</string>
    <string name="change_message_chooess_hint_text">Push message and SMS will show as the selected language</string>
    <string name="device_text_alarm">Alarm triggered by #family</string>
    <string name="device_text_status">The status of #family has been set as #status</string>
    <string name="device_text_password">#family: The password of alarm panel has been changed</string>
    <string name="device_text_reset">#family: alarm panel reseted</string>
    <string name="device_text_lowbettery">#family: alarm panel battery lower than #battery</string>
    <string name="device_text_lowpower">#plugin low power</string>
    <string name="device_text_auth_change">#family : your authority has been changed to #authority</string>
    <string name="device_text_plug">#family alarming, triggered by plugin #plugin</string>
    <string name="device_text_plug_user">#family alarming, triggered by user #user</string>
    <string name="device_password_change">Device password has changed</string>
    <string name="change_message_hint">Please set up push message and SMS language first.</string>
    <string name="device_power_off">Device power disconnect</string>
    <string name="device_power_on">Device pluged in</string>
    <string name="tiggle_first_success">An accessory has been detected, please continue.</string>
    <string name="exit_unbind_hint">You have not set up an account yet. You will lose all the data in case you reinstall or log out the app.</string>
    <string name="device_settting_hint">You have not set up an account yet. You will lose all the data in case you reinstall or log out the app.</string>
    <string name="device_settting_hint_set">Set up now.</string>
    <string name="illegal_ID">Illegal ID</string>
    <string name="app_private">Privacy Policy</string>
    <string name="device_status">#user has changed the status of #family: alarm panel as #status</string>
    <string name="siren_setting">Siren Setting</string>
    <string name="save">Save</string>
    <string name="siren_disarm">Prompt Tone - Disarm</string>
    <string name="siren_homearm">Prompt Tone - Home Arm</string>
    <string name="siren_arm">Prompt Tone - Arm</string>
    <string name="siren_homearm_light_mode">Light Mode - Home Arm</string>
    <string name="twinkle">Twinkle</string>
    <string name="siren_arm_light_mode">Light Mode - Arm</string>
    <string name="siren_disarm_light_mode">Light Mode - Disarm</string>
    <string name="always">Always</string>
    <string name="flash">Flash</string>
    <string name="siren_alert_time">Siren Time (Min)</string>
    <string name="siren_remind_volume">Volume - Prompt Tone</string>
    <string name="siren_setting_alarm_volume">Volume - Alarm</string>
    <string name="off">OFF</string>
    <string name="on">On</string>
    <string name="siren_sos">Light Mode - Alarm</string>
    <string name="password_wrong_relogin">Wrong passcode for 5 times, please log in again.</string>
    <string name="record_motionhint">Motion Detected</string>
    <string name="record_empty">No Record</string>
    <string name="record_one_hour">within 1 hour</string>
    <string name="record_three_hour">within 3 hours</string>
    <string name="record_six_hour">within 6 hours</string>
    <string name="record_one_day">within 1 day</string>
    <string name="record_three_day">within 3 days</string>
    <string name="record_more">more than 3 days</string>
    <string name="advanced_setting_contactid">Contact ID Setting</string>
    <string name="contact_id_hint">After turning on Contact ID protocol, when the alarm has been triggered, event messages will sent to the central monitoring stations. And the stations are linked with police office or fire department,etc..</string>
    <string name="contact_id_input_hint">CID Code</string>
    <string name="password_last_than_six">Passwords must be at least 6 characters in length</string>

    <string name="filter_vcode">0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ</string>
    <string name="error_phone_exist">This mobile number is already taken by another user.</string>
    <string name="error_email_exist">The email address is already taken by another user.</string>
    <string name="offical_plugin">The official accessory</string>
    <string name="device_managent_other_plugin">Third party accessory</string>
    <string name="device_offline">#family: alarm panel can\'t connect the network</string>
    <string name="ipc_wrong_pwd">Smart camera password wrong</string>
    <string name="ipc_disconnect">Smart camera offline, please check camera status.</string>
    <string name="ipc_not_online">Smart camera offline, please check camera status.</string>
    <string name="cid_cms">CMS Number</string>
    <string name="cid_code">CID Code</string>
    <string name="ipc_wifi_set_name">SSID</string>
    <string name="Quick_Actions">Quick Actions</string>
    <string name="user_not_bind_phone">You haven\'t bind mobile phone. Unable to reset password.</string>
    <string name="tiggle_device_hint_2">Please make sure your smart phone and device is in same Wifi router</string>
    <string name="tiggle_device_hint_brand">Please select the brand of accessory</string>
    <string name="tiggle_device_type_brand">Please select the type of accessory</string>
    <string name="save_to_album">Save to Album</string>
    <string name="share_mms">Share with MMS</string>
    <string name="forget_confirm_password_success">Password reset. Please log in again.</string>
    <string name="ipc_setting_title">IPC Setting</string>
    <string name="not_offical_plugin">Third party accessory</string>
    <string name="cancel">Cancel</string>
    <string name="phone_login">Log in via Phone</string>
    <string name="other_login">Log in via ID/Email</string>
    <string name="device_managent_plugin">Widget</string>
    <string name="login_or">or</string>
    <string name="cid_eror">CID Code can only be 0 to 9 and B to F. Has to be 4 characters in total.</string>
    <string name="antiinterfer_sos">Caution nearby interfering signal detected</string>
    <string name="timezone_setting_go">Go setting</string>
    <string name="timezone_setting_content">Please select main panel timezone first to activate more features.</string>
    <string name="timezone_setting_title">Timezone</string>
    <string name="send_wave">Sonic Wave</string>
    <string name="start">Start</string>
    <string name="wave_hint">Connecting, please wait for a minute. After a tone of success emitted by the camera, please click Next.</string>
    <string name="modify_plugs_hint">首次链接xxx</string>
    <string name="wave_hint_2">Please reset camera before sending sonic wave to configure wifi.</string>
    <string name="device_managent_heart_bit">Heart Bit</string>
    <string name="confirm_change_name">Change user name as:</string>
    <string name="plugin_offline_hint">Plugin no response</string>
    <string name="plugin_lowpower_hint">Plugin low battary, please check and replace battery.</string>
    <string name="ipc_online">Online</string>
    <string name="ipc_connecting">Connecting</string>
    <string name="ipc_off_online">offline</string>
    <string name="ipc_off_online_toast">Camera offline</string>
    <string name="syn_time_zone">Smart camera\'s timezone is synchronized with your smartphone, camera restarting, please try angin later</string>
    <string name="wifi_list">Wifi List</string>

    <string name="change_email_unbind">Unbind</string>
    <string name="change_email_unbind_hint">Unbind confirmation mail sent, please check your mail box.</string>

    <string name="ipc_setting_advance_setting">Advanced Setting</string>
    <string name="ipc_setting_detail">Details</string>
    <string name="ipc_status">Camera Status</string>
    <string name="ipc_password">Camera Password</string>
    <string name="ipc_setting_ipc_pid">ONVIF ID</string>
    <string name="ipc_setting_ipc_ip">IP Address</string>
    <string name="ipc_setting_offline">Camera offline</string>
    <string name="ipc_input_password">Camera Password</string>
    <string name="ipc_need_change_password">For system security, please reset camera password.</string>
    <string name="ipc_wrong_password">Wrong password. Please try the correct one or press the reset button of the camera, then to reconnect the camera by Network Cable.</string>
    <string name="ipc_wrong_password_wave">Wrong password. Please try the correct one or press the reset button of the camera, then to pair the camera by sonic wave to reconnect it to the network.</string>
    <string name="ipc_reset">Reset</string>
    <string name="ipc_input_password_hint">Input password</string>
    <string name="ipc_failed_to_connect_the_network">Failed to connect the network</string>
    <string name="ipc_reconnect_the_network">Reconnect the Network</string>
    <string name="ipc_reconfigure_the_network">Reconfigure the Network</string>
    <string name="siren_mute">Mute</string>
    <string name="siren_low">Low</string>
    <string name="siren_middle">Middle</string>
    <string name="siren_high">High</string>
    <string name="remember_password">Remember the password</string>
    <string name="copyright">Copyright @ 2016 Dinsafer. inc.</string>
    <string name="know_it">Got it</string>
    <string name="reset_siren">Return</string>
    <string name="siren_content">Turn on the alarm warning tones and lighting effect will consume more battery of the accessories, suggest connecting the accessories with power. </string>
    <string name="change_uid_title">Modify username</string>
    <string name="change_uid_unbind">Delete Account</string>
    <string name="unbind_uid_dialog_hint">Confirm delete account?</string>
    <string name="change_uid_unbind_confirm_hint">All data will be erased and cannot be recovered after deleting account.</string>
    <string name="change_uid_unbind_hint2">Account password</string>
    <string name="change_uid_unbind_input_again">Try again</string>
    <string name="change_uid_unbind_wrong_password">Password incorrect.</string>
    <string name="device_managent_doorbell">Visual Doorbell</string>
    <string name="device_managent_doorbell_cap">Visit Record</string>
    <string name="door_listview_empty">No visit record.</string>
    <string name="door_bell_cancel_btn">Close</string>
    <string name="door_bell_more_btn">Check More</string>

    <string name="ipc_off_wrong_password">Wrong Password</string>
    <string name="HD">HD</string>

    <string name="siren_test">Test</string>
    <string name="siren_help">Help</string>
    <string name="siren_testing">Test sent</string>
    <string name="siren_help_msg">After the siren paired successfully with the panel, you can press the test button to check the status of the siren. Siren beeps means everything is OK. The interval time between tests is 5 seconds.</string>
    <string name="common_function">Common Function</string>
    <string name="ipc_camera">IP Camera</string>
    <string name="main_section_sos">SOS</string>
    <string name="Finish">Finish</string>
    <string name="add_tuya_hint">Plug in, and please confirm that the indicator light is quick flashing (long press the reset button for 5s to enable the accessory to enter the quick flash status). There can be only one accessory waiting to be paired at the same time.</string>
    <string name="add_tuya_bulb_hint">Plug in, and please confirm that the bulb is quick flashing (\'On-Off-On-Off-On\' the bulb to enable the accessory to enter the quick flash status. Be careful not to turn off the light until it is on ). There can be only one accessory waiting to be paired at the same time.</string>
    <string name="add_tuya_title">Add Accessory</string>
    <string name="add_tuya_no_wifi">The phone was not connected to the wireless network, please go to the phone system to set up the connection.</string>
    <string name="add_tuya_to_setwifi">Leave for connection</string>
    <string name="add_tuya_has_wifi">Use the following wireless network connection:</string>
    <string name="add_tuya_other_wifi">Select other networks</string>
    <string name="add_tuya_fail">Add failed</string>
    <string name="add_tuya_add_again">Try again</string>
    <string name="tuya_edit_wifi_hint">Only 2.4G networks are supported.</string>
    <string name="tuya_edit_wifi_password_hint">Enter wifi password</string>
    <string name="edit_tuya_title">Configure Network Settings</string>
    <string name="offical_smart_plugin">Smart Plug</string>
    <string name="tuya_smart_plugin">WIFI Plug</string>
    <string name="tuya_color_light">WIFI Bulb</string>
    <string name="smart_plugin_on">On</string>
    <string name="smart_plugin_off">Off</string>
    <string name="smart_plugin_edit_finish">Done</string>
    <string name="tuya_light_color_mode">Colored Mode</string>
    <string name="tuya_light_white_mode">White Light Mode</string>
    <string name="tuya_light_color_hint">Hue</string>
    <string name="tuya_light_saturation_hint">Saturation</string>
    <string name="tuya_color_light_setting">Accessory Setting</string>
    <string name="tuya_light_brightness_hint">Brightness</string>
    <string name="tuya_plugin_offline">Offline</string>
    <string name="Done">Done</string>
    <string name="main_toolbar_status_btn">Status</string>
    <string name="main_toolbar_dashboard_btn">Dashboard</string>
    <string name="add_widget">Add widget</string>
    <string name="use_the_fol">The device has been deleted.</string>
    <!--widget -->

    <string name="device_management_door_sensor">Door Window Sensor</string>
    <string name="rta_setting_no_door_hint">Ready to Arm is only for smart window/door sensor, please add one first.</string>
    <string name="rta_setting_hint">Ready to Arm function enables you to get actual status, such as apart or close, of window/door sensor when you arm or homearm.</string>
    <string name="rta_setting_btn_buy">Get one now</string>
    <string name="ready_to_arm">Ready to Arm</string>
    <string name="door_sensor_item_open">Apart</string>
    <string name="door_sensor_close">Close</string>
    <string name="rta_dialog_btn_execute">Execute</string>
    <string name="rta_dialog_btn_cancel">Cancel</string>
    <string name="rta_dialog_btn_know">Got it</string>
    <string name="rta_dialog_title">Notice</string>
    <string name="rta_dialog_hint">Following sensors are still apart, confirm to change alarm status?</string>
    <string name="rta_dialog_item_plugin_name">Plugin name</string>
    <string name="rta_dialog_item_plugin_id">Plugin id</string>
    <string name="door_sensor_listview_empty">You have not add any door/window sensor, please add one first.</string>
    <string name="door_sensor_modify_view">Door Window Sensor</string>
    <string name="new_door_sensor">Door Window Sensor</string>
    <string name="old_door_sensor">Common window/door sensor</string>
    <string name="ready_to_arm_push_hint">#plugin change #family as #status, following sensors are still apart.</string>
    <string name="tip_grant_permission">Please grant application permission for better use experience</string>

    <!-- 触发配对 -->
    <string name="tuya_switch_tiggle_hint">Plug in, and please confirm that the indicator light is quick flashing. (There can be only one accessory waiting to be paired at the same time.)</string>
    <string name="tuya_light_tiggle_hint">Plug in, and please confirm that the bulb is quick flashing. (There can be only one accessory waiting to be paired at the same time.)</string>
    <string name="tuya_switch_tiggle_hint_help">Plug in, and long press the reset button for 5s to enable the accessory to enter the quick flash status.</string>
    <string name="tuya_light_tiggle_hint_help">Plug in, and \'On-Off-On-Off-On\' the bulb to enable the accessory to enter the quick flash status. Be careful not to turn off the light until it is on.</string>

    <!-- 防拆报警 -->
    <string name="door_tamper_alarm">Tamper Alarm</string>

    <!--继电器-->
    <string name="relay_name">Roller Shutter</string>
    <string name="tiggle_relay_hint_1">Roller Shutter learning step1 description</string>
    <string name="tiggle_relay_hint_2">Roller Shutter learning step2 description</string>

    <!--Smart button-->
    <string name="smart_button_hint_1">Smart Button learning step1 description</string>
    <string name="smart_button_hint_2">Smart Button learning step2 description</string>

    <string name="wired_bridge_hint_1">Wired bridge learning step1 description</string>
    <string name="wired_bridge_hint_2">Wired bridge learning step2 description</string>

    <!--ReadyToArmTipDialog-->
    <string name="do_not_see_again">Do not see again</string>
    <string name="ready_to_arm_off_tip">Ready to Arm is defaulted Off now, please go to Settings to turn it on if you like to use its features.</string>
    <string name="not_now">Not now</string>
    <string name="go_setting">Go setting</string>

    <string name="advanced_setting_current_net">Current network</string>
    <string name="advanced_setting_net_cable">Cable</string>
    <string name="wifi_signal">WiFi Signal</string>

    <!--BlueTooth ble-->
    <string name="ble_scan_right_top_word">No device found?</string>
    <string name="ble_scan_close_to_phone">Please put your mobile close to the device and wait for a minute.</string>
    <string name="ble_check_phone_bluetooth">Please check your bluetooth setting in your mobile phone.</string>
    <string name="ble_check_device_bluetooth">Panel bluetooth disconnected, please put your mobile phone close to the panel.</string>
    <string name="ble_check_ipc_bluetooth">Please check whether the camera is flashing blue light. If not, please turn off and turn on the camera again, to restart connection mode.</string>
    <string name="ble_config_ipc_network_hint">If the blue light is flashing but the device is still not found, please hold the reset button to clear the data, and then turn off and turn on the camera again.</string>
    <string name="ble_check_again">Search Again</string>
    <string name="ble_quit">Quit</string>
    <string name="ble_title_open_device_bletooth">Turn bluetooth of the device on</string>
    <string name="ble_hint_open_bluetooth">If no device found, please press the quick setting button on the back of the panel to restart the bluetooth.</string>
    <string name="ble_hint_reset_panel">If no panel found, please hold the reset button on the back of the panel until you hear a beep.</string>
    <string name="ble_btn_set_net">Configure network</string>
    <string name="ble_config_net_hint">Please put the phone close to the device, long press the quick setting button on the back of the device to turn on bluetooth. Press Next after hearing a prompt sound from the device.</string>
    <string name="ble_open_device_blue">Please check your bluetooth setting in your mobile phone.</string>
    <string name="ble_dialog_light_splash">Have you heard the prompt sound and saw a flashing indicator light from the device?</string>
    <string name="ble_list_item_status_added">Added</string>
    <string name="ble_list_item_status_offline">Offline</string>
    <string name="ble_toast_added_device">This device has already added in this account.</string>
    <string name="ble_toast_device_offline">Device offline, please try later.</string>
    <string name="ble_scan_list_title">Choose device</string>
    <string name="ble_quit_change_net_tip">Stop Changing the network for device?</string>
    <string name="ble_quit_change_net_yes">Yes</string>
    <string name="ble_quit_change_net_no">No</string>

    <!--switch bot-->
    <string name="switch_bot">SwitchBot</string>
    <string name="switch_bot_collect_tip">After starred your favorite SwitchBot, you can rename and configure it, and get more info.</string>
    <string name="switch_bot_list_not_data">No SwitchBot found</string>
    <string name="switch_bot_use_wall_mode">Use light switch Add-on</string>
    <string name="switch_bot_change_orientation">Inverse the on/off direction</string>
    <string name="switch_bot_firmware_version">Version of firmware</string>
    <string name="switch_bot_mac">BLE MAC</string>
    <string name="switch_bot_delete_tip">No long edit configurations if you unstar SwitchBot.</string>
    <!--<string name="switch_bot_offline_tip">switch bot插件已离线</string>-->

    <!--主机状态详情-->
    <string name="device_status_detail">Device status</string>
    <string name="ip_address">IP Address</string>
    <string name="battery_quantity">Battery</string>
    <string name="power_connected">power connected</string>
    <string name="power_disconnected">Use battery</string>
    <string name="sim_card">SIM</string>
    <string name="current_status">Current status</string>
    <string name="sim_normal">Normal</string>
    <string name="sim_not_exit">No SIM detected</string>
    <string name="sim_wrong">Error</string>
    <string name="network_status">Network status</string>
    <string name="imei_code">IMEI</string>
    <string name="imsi_code">IMSI</string>
    <string name="semaphore">Signal</string>
    <string name="pin_status">PIN</string>
    <string name="device_sin_pin_error_hint">The SIM PIN code error is detected, please close the PIN code through the mobile phone before use.</string>


    <string name="guest_no_push_hint">Guest will not receive any notifications.</string>
    <string name="guest_no_push_and_no_phone_hint">Users need to verify the mobile number to receive message notifications. Guest will not receive any notifications.</string>
    <string name="open_ble_hint">Please check your bluetooth setting in your mobile phone.</string>

    <string name="device_managent_more_support">More Support</string>
    <string name="device_managent_third_part_service">More</string>

    <string name="tip_hue_scan">Make sure the Hue Bridge is powered on and connected to the router of your current Wi-Fi network via the ethernet cable. Your phone has to be connected to the same Wi-Fi network. Then tap SEARCH below.</string>
    <string name="search">Search</string>
    <string name="philips_hue">Philips Hue</string>
    <string name="tip_hue_bridge_not_found">No new Hue Bridge found</string>
    <string name="tip_hue_offline">Please connect your phone with Hue bridge on the same network to control it.</string>
    <string name="tip_hue_no_light">No new light source(s) found. Power your light source(s) and turn on your wall light switchs. Then tap SEARCH below.</string>
    <string name="tip_hue_click_push_link">Press the push-link button on the Hue Bridge you want to connect to.</string>
    <string name="tip_hue_push_link_title">Push-link</string>
    <string name="Online">Online</string>
    <string name="Offline">Offline</string>
    <string name="Unknown">Unknown</string>
    <string name="Unavailable">Unavailable</string>
    <string name="hue_bridge_default_name">Hue bridge</string>
    <string name="tip_hue_no_new_light">No new light source(s) found</string>

    <string name="tip_app_rating_title">Enjoying #AppName</string>
    <string name="tip_app_rating_to_market">Love it</string>
    <string name="tip_app_rating_complaint">No,I have a complaint</string>

    <!--tuya用电量-->
    <string name="tuya_power">Electricity Consumption</string>
    <string name="tuya_total_power">总电量（度）</string>
    <string name="tuya_cur_current">Electric Current(mA)</string>
    <string name="tuya_cur_power_consume">Power(W)</string>
    <string name="tuya_cur_valtage">Voltage(V)</string>
    <string name="tuya_today_power">Today(KW.h)</string>
    <string name="tuya_month_power">Electricity consumption in #month (KW.h)</string>
    <string name="tuya_year">Year #year</string>
    <string name="January">January</string>
    <string name="February">February</string>
    <string name="March">March</string>
    <string name="April">April</string>
    <string name="May">May</string>
    <string name="June">June</string>
    <string name="July">July</string>
    <string name="August">August</string>
    <string name="September">September</string>
    <string name="October">October</string>
    <string name="November">November</string>
    <string name="December">December</string>

    <!-- CMS设置校验提示信息 -->
    <string name="primary_ip_not_null">Primary IP can not be null</string>
    <string name="primary_ip_error">Primary IP Error</string>
    <string name="primary_port_not_null">Primary Port can not be null</string>
    <string name="primary_port_must_number">Primary Port must be number</string>
    <string name="primary_port_must_between">Primary Port between 0~65535</string>
    <string name="secondary_port_must_number">Secondary Port must be number</string>
    <string name="secondary_port_must_between">Secondary Port must between 0~65535</string>
    <string name="secondary_ip_not_null">Secondary IP can not be null while Secondary Port not null</string>
    <string name="secondary_ip_error">Secondary IP Error</string>
    <string name="secondary_port_not_null">Secondary Port can not be null while Secondary IP not null</string>
    <string name="account_number_not_null">Account Number can not be null</string>
    <string name="account_number_must_number">Account Number must be number</string>
    <string name="encryption_not_opened">Encryption must opened while Encryption Key not null</string>
    <string name="encryption_not_null">Encryption is opened, Encryption Key can not be null</string>

    <string name="openGPStip">In order to connect and configure the device, the GPS switch needs to be turned on temporarily. The app does not follow your location. You can turn this off later in your phone\'s System Settings.</string>
    <string name="advance_4g_user_name">Username</string>
    <string name="advance_4g_user_password">Password</string>
    <string name="advance_4g_title">4G Network Selection</string>

    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <!--限制模式-->
    <string name="restrict_model_title">Offline Mode</string>
    <string name="restrict_model_exit">Exit</string>
    <string name="restrict_model_tip">Offline Mode allows you to send SMS to protect your security when device offline.</string>
    <string name="restrict_model_sim_error_tip">SIM card needs to be inserted and can be used normally to enter the Offline Mode.</string>
    <string name="restrict_model_dialog_input_phone_title">Panel Phone Number</string>

    <string name="item_plugin_low_battary">Low Battery</string>
    <string name="item_plugin_status_charging">Charging</string>
    <string name="item_plugin_status_pressed">Pressed</string>
    <string name="item_plugin_status_released">Released</string>

    <!--TODO 心赖IPC-->
    <string name="device_has_no_record">设备没有录像文件</string>
    <string name="face_check_sd_status">请检查SD卡状态</string>
    <string name="current_has_no_video">该时间段内没有视频</string>
    <string name="wifi_setting_safe">安全模式:</string>
    <string name="wifi_setting_pass_format">密钥格式:</string>
    <string name="wifi_setting_safe_open">开放系统</string>
    <string name="wifi_setting_safe_all">共享密钥</string>
    <string name="wifi_setting_pass_format_16">16进制数字</string>
    <string name="wifi_setting_pass_format_as">ASCII字符</string>
    <string name="password">密码:</string>
    <string name="showpwd">显示密码</string>
    <string name="wifi_manager">刷新WiFi</string>
    <string name="wifi_safe">安全性:</string>
    <string name="wifi_signal_strong">信号强度:</string>
    <string name="connecting">正在获取视频......</string>
    <string name="wifi_getparams">正在获取WiFi信息......</string>
    <string name="wifi_channel">通道号:</string>
    <string name="wifi_no_safe">无</string>
    <string name="wifi_not_connected">未连接</string>
    <string name="wifi_scanning">WiFi正在扫描,请稍候......</string>
    <string name="wifi_scan_failed">WiFi扫描失败</string>
    <string name="connected">已连接</string>
    <string name="wifi_set_success">WiFi设置成功,摄像机正在重启......</string>
    <!-- User 设置 -->
    <string name="user_admin">管理员</string>
    <string name="user_operator">操作者</string>
    <string name="user_visitor">参观者</string>
    <string name="user_setting">用户设置</string>
    <string name="user_set_failed">用户设置失败</string>
    <string name="user_set_success">用户设置成功,摄像机正在重启</string>
    <string name="user_name_no_empty">用户名不能为空</string>
    <string name="user_name">用户名:</string>
    <string name="user_getparams">正在获取用户信息......</string>
    <string name="user_getparams_failed">获取用户信息失败</string>
    <string name="user_name_toolong">用户名不能超过64位</string>
    <string name="user_pwd_toolong">密码不能超过64位</string>
    <string name="hint_input_old_pwd">请输入原密码</string>
    <string name="hint_input_new_pwd">请输入新密码</string>
    <string name="old_pwd">旧密码：</string>
    <string name="new_pwd">新密码：</string>
    <string name="new_pwd_again">确认密码：</string>
    <string name="hint_input_account1">再次确认密码</string>
    <!-- 设备设置 -->
    <string name="ipc_deviceinfo_id">ID</string>
    <string name="ipc_deviceinfo_type">产品型号</string>
    <string name="ipc_deviceinfo_ver">固件版本</string>
    <string name="ipc_ser_no">序列号</string>
    <string name="ipc_deviceinfo_reboot">重启设备</string>
    <string name="ipc_deviceinfo_resetnowifi">恢复出厂(清除WIFI)</string>
    <string name="ipc_deviceinfo_reset">恢复出厂(保留WIFI)</string>
    <string name="text_online_num">用户连接数</string>
    <string name="text_net_type">网络类型</string>
    <string name="text_mac_address">物理地址</string>
    <string name="text_ip_address">IP地址</string>
    <string name="text_subnet_mask">子网掩码</string>
    <string name="text_gateway">网关</string>
    <string name="text_dns1">DNS1</string>
    <string name="text_dns2">DNS2</string>
    <string name="text_net_type_link">有线</string>
    <string name="text_net_type_4g">4G</string>
    <string name="text_net_type_wifi">无线</string>
    <!-- 摄像机 SDCard 设置 -->
    <string name="sdcard_getparams">正在获取SDCard信息......</string>
    <string name="sdcard_total">SD卡总容量(M):</string>
    <string name="sdcard_remain">SD卡剩余量(M):</string>
    <string name="sdcard_state">SD卡状态:</string>
    <string name="sdcard_format">Format SD Card</string>
    <string name="sdcard_inserted">SD卡已经插入</string>
    <string name="sdcard_no_inserted">没有插SD卡</string>
    <string name="sdcard_coverage">录像覆盖:</string>
    <string name="sdcard_recordlength">录像时长(分钟):</string>
    <string name="sdcard_recordtime">定时录像:</string>
    <string name="sdcard_schedule">录像设置</string>
    <string name="sdcard_range">范围5-120</string>
    <string name="sdcard_set_failed">SD卡设置失败</string>
    <string name="sdcard_set_success">SD卡设置成功</string>
    <string name="ipc_device_info">设备信息</string>
    <string name="ipc_sd_status_no">sd卡不存在</string>
    <string name="ipc_sd_status_0">sd卡未初始化</string>
    <string name="ipc_sd_status_1">sd卡状态正常</string>
    <string name="ipc_sd_status_2">sd卡未格式化</string>
    <string name="ipc_sd_status_3">sd卡格式化OK</string>
    <string name="ipc_sd_status_4">sd卡只读</string>
    <string name="ipc_sd_status_5">sd卡正在格式化</string>
    <!-- 移动帧测 -->
    <string name="ipc_alarm_open">移动侦测开关</string>
    <string name="ipc_face_alarm_open">人脸识别开关</string>
    <string name="ipc_setvideo_starttime">开始时间</string>
    <string name="ipc_setvideo_endtime">结束时间</string>
    <string name="second_day">第二天</string>
    <string name="ipc_alarm_le">灵敏度</string>
    <string name="ipc_alarm_le_1">极高</string>
    <string name="ipc_alarm_le_2">高</string>
    <string name="ipc_alarm_le_3">中</string>
    <string name="ipc_alarm_le_4">低</string>
    <string name="ipc_alarm_le_5">极低</string>
    <string name="ipc_alarm_record">触发移动侦测录像</string>
    <string name="ipc_alarm_push">触发消息推送</string>
    <string name="ipc_alarm_fmq">触发语音警报</string>
    <string name="audio_type_default">默认</string>
    <string name="audio_type_one">音频  1</string>
    <!-- 时间设置 -->
    <string name="date_device_time">设备时钟时间</string>
    <string name="date_device_timezone">设备时区设定</string>
    <string name="date_ntp_server_auto_check">使用 NTP服务器自动校时</string>
    <string name="date_phone_time_auto_check">使用手机时间校准设备时钟</string>
    <string name="date_ntp_server">NTP服务器</string>
    <string name="date_middle_island">(GMT-11:00)中途岛,萨摩亚群岛</string>
    <string name="date_hawaii">(GMT -10:00) 夏威夷</string>
    <string name="date_alaska">(GMT-09:00)阿拉斯加</string>
    <string name="date_pacific_time">(GMT-08:00)太平洋时间(美国和加拿大)</string>
    <string name="date_mountain_time">(GMT-07:00)山地时间(美国和加拿大)</string>
    <string name="date_middle_part_time">(GMT-06:00)中部时间(美国和加拿大),墨西哥城</string>
    <string name="date_eastern_time">(GMT-05:00)东部时间(美国和加拿大),利马,波哥大</string>
    <string name="date_ocean_time">(GMT-04:00)大西洋时间(加拿大),圣地亚哥,拉巴斯</string>
    <string name="date_newfoundland">(GMT-03:30)纽芬兰</string>
    <string name="date_brasilia">(GMT-03:00)巴西利亚,布宜若斯艾丽斯,乔治敦</string>
    <string name="date_center_ocean">(GMT-02:00)中大西洋</string>
    <string name="date_cape_verde_island">(GMT-01:00)佛得角群岛</string>
    <string name="date_greenwich">(GMT)格林威治平时,伦敦,里斯本,卡萨布兰卡</string>
    <string name="date_brussels">(GMT+01:00)布鲁赛尔,巴黎,柏林,罗马,马德里,斯多哥尔摩, 贝尔格莱德, 布拉格</string>
    <string name="date_athens">(GMT +02:00) 雅典, 耶路撒冷, 开罗, 赫尔辛基</string>
    <string name="date_nairobi">(GMT +03:00) 内罗毕, 利雅得, 莫斯科</string>
    <string name="date_teheran">(GMT +03:30) 德黑兰</string>
    <string name="date_baku">(GMT +04:00) 巴库, 第比利斯, 阿布扎比, 马斯科特</string>
    <string name="date_kebuer">(GMT +04:30) 科布尔</string>
    <string name="date_islamabad">(GMT +05:00) 伊斯兰堡, 卡拉奇, 塔森干</string>
    <string name="date_calcutta">(GMT +05:30) 加尔各答, 孟买, 马德拉斯, 新德里</string>
    <string name="date_alamotu">(GMT +06:00) 阿拉木图, 新西伯利亚, 阿斯塔南, 达尔</string>
    <string name="date_bangkok">(GMT +07:00) 曼谷, 河内, 雅加达</string>
    <string name="date_beijing">(GMT +08:00) 北京, 新加坡, 台北</string>
    <string name="date_seoul">(GMT +09:00) 首尔, 雅库茨克, 东京</string>
    <string name="date_darwin">(GMT +09:30) 达尔文</string>
    <string name="date_guam">(GMT +10:00) 关岛, 墨尔本, 悉尼, 莫尔兹比港, 符拉迪沃斯托克</string>
    <string name="date_suolumen">(GMT +11:00)马加丹,所罗门群岛, 新喀里多尼亚</string>
    <string name="date_auckland">(GMT +12:00) 奥克兰, 惠灵顿, 斐济</string>
    <string name="date_ntp_server_time_nist_gov">time.nist.gov</string>
    <string name="date_ntp_server_time_kriss_re_kr">time.kriss.re.kr</string>
    <string name="date_ntp_server_time_windows_com">time.windows.com</string>
    <string name="date_ntp_server_time_nuri_net">time.nuri.net</string>
    <string name="date_setting_success">设备时钟时间设置成功</string>
    <string name="date_setting_failed">设备时钟时间设置失败</string>
    <string name="date_get_params">正在获取时钟信息...</string>
    <string name="auto_sync_date">自动同步</string>
    <!-- 添加相机/修改相机 界面 -->
    <string name="input_camera_name">请输入摄像机名称</string>
    <string name="add_camera">添加摄像机</string>
    <string name="edit_camera">修改摄像机</string>
    <string name="camera_name">名称</string>
    <string name="camera_addr">IP地址</string>
    <string name="input_camera_addr">请输入IP地址</string>
    <string name="input_camera_port">请输入端口</string>
    <string name="input_camera_user">请输入用户名</string>
    <string name="input_camera_id">请输入设备ID</string>
    <string name="camera_id">设备ID</string>
    <string name="scan_cameraid">扫描条码</string>
    <string name="scan_cameraid_fail">扫描失败,请重新扫描</string>
    <string name="add_search_result">搜索结果</string>
    <string name="add_search_no">没有搜索到,请重试...</string>
    <string name="add_twodimensioncode">正在启动二维码程序...</string>
    <string name="add_camer_no_add">您添加的相机已达到系统最大值，请删除某些无用的相机后再添加！</string>
    <string name="input_camera_all_include">该设备已经存在</string>
    <string name="add_camer_invi">ID号无效，不符合规则！</string>
    <string name="camera_user">用户名</string>
    <string name="camera_pwd">密码</string>
    <string name="device_ipc_motion">Motion Detect</string>
    <string name="device_ipc_record">Record</string>
    <string name="ipc_alarm_sound_text">Alarm Sound</string>
    <string name="ipc_motion_sensitivity_text">Motion Sensitivity</string>
    <string name="ipc_motion_time_text">Alarm Start Time</string>
    <string name="ipc_motion_end_time_text">Alarm End Time</string>
    <string name="motion_sensitivity_low">Low</string>
    <string name="motion_sensitivity_middle">Middle</string>
    <string name="motion_sensitivity_high">High</string>
    <string name="ipc_video">Video</string>
    <string name="ipc_status_online">Online</string>
    <string name="ipc_status_connecting">Connecting</string>
    <string name="ipc_status_wrong_password">Wrong Password</string>
    <string name="ipc_status_offline">Offline</string>
    <string name="ipc_offline_toast">Camera offline</string>
    <string name="ipc_xiaohei_header">HD Camera</string>
    <string name="ipc_heartlai_header">Heart Lai IPC</string>
    <string name="modify_plugs_more_setting">IPC Setting</string>
    <string name="ipc_wrong_password_ap">Wrong password. Please try the correct one or press the reset button of the camera, then to reconnect the camera by Network Cable.</string>
    <string name="empty_record_file">Empty record file</string>


    <string name="ipc_ap_hint">Please connect your mobile phone with #AP_Name WiFi network( default pw:01234567). If there is no #AP_Name on the wifi list, please long press the button back on the camera until it make a tone.</string>
    <string name="ipc_ap_change_wifi">Change Network</string>
    <string name="ipc_ap_wrong">Not connecting to assigned network, still continue setting network?</string>
    <string name="ipc_ap_add">Yes</string>
    <string name="ipc_ap_cancel">No</string>
    <string name="ipc_ap_exit">Quit?</string>
    <string name="modify_plugs_network_ap">Network Setting</string>

    <string name="calendar">Calendar</string>

    <string name="alarm_previous_day">Previous month</string>
    <string name="alarm_next_day">Next month</string>
    <string name="sunday">Sun</string>
    <string name="monday">Mon</string>
    <string name="tuesday">Tue</string>
    <string name="wednesday">Wed</string>
    <string name="thursday">Thu</string>
    <string name="friday">Fri</string>
    <string name="saturday">Sat</string>
    <string name="ipc_record_time_text">Record Start Time</string>
    <string name="ipc_record_end_time_text">Record End Time</string>

    <string name="ipc_sos_record_empty">No records. Turn on camera motion detect function, then system will record image automatically and push notifications when motion detected.</string>
    <string name="device_ipc_motion_record">Motion Records</string>
    <string name="ipc_motion_detected_push_content">#family:#plugin motion detected</string>
    <string name="check">Check</string>
    <string name="ignore">Ignore</string>

    <!-- SmartButton 相关 -->
    <string name="smart_button_name">Smart Button</string>
    <string name="smart_button_add_scene">Add Scenarios</string>
    <string name="smart_button_add_scene_remote_controller">Add a scenario for the custom button</string>
    <string name="smart_button_custom_remote_controller">Custom Remote Controller</string>
    <string name="smart_button_action">Actions</string>
    <string name="scene_security_commands">Security Commands</string>
    <string name="smart_button_delete_action">Delete action</string>
    <string name="scene_ring_doorbell">Chime</string>
    <string name="scene_switch_bulb">Switch the Bulb</string>
    <string name="scene_switch_plug">Switch the Plug</string>
    <string name="scene_control_roller_shutter">Control the roller shutter</string>
    <string name="scene_action_single_click">Single Press</string>
    <string name="scene_action_double_click">Double Press</string>
    <string name="scene_action_long_click">Long Press</string>
    <string name="smart_button_command">Command</string>
    <string name="smart_button_set_command">Set Command</string>
    <string name="smart_button_test_doorbell">Test the Chime</string>
    <string name="smart_button_ring_doorbell">Chime</string>
    <string name="smart_button_doorbell_music">Music</string>
    <string name="smart_button_doorbell_volume">Volume</string>
    <string name="smart_button_select_control_target">Select Object</string>
    <string name="smart_button_empty_target_hint">This scenario is only for #plugin, please add one first.</string>
    <string name="doorbell">Chime</string>
    <string name="bulb">Bulb</string>
    <string name="plug">Plug</string>
    <string name="smart_button_action_repeat">The action is occupied. Are you sure to overwrite it?</string>
    <string name="smart_button_action_set_success">Succeed</string>
    <string name="overwrite">Overwrite</string>
    <string name="modify_plugs_mode_setting">Mode</string>
    <string name="modify_plugs_chime_setting">Chime Setting</string>
    <string name="modify_plugs_always_push_status">Always Push Status</string>
    <string name="modify_plugs_always_push_status_hint">When the system is Disarmed or Home Armed (even if the accessory is not included in the Home Arm area), you can still receive the door/window status pushed by the APP.</string>
    <string name="block_mode_chime">Chime</string>
    <string name="block_mode_plugin">Bypass the Plugin</string>
    <string name="block_mode_tamper_alarm">Bypass Tamper Alarm</string>
    <string name="block_mode_normal">Normal</string>
    <string name="sure">Sure</string>
    <string name="block_plugin_content">Are you sure to bypass the sensor? You can enable it anytime.</string>
    <string name="block_tamper_content">Are you sure to bypass the tamper alarm? You can enable it anytime.</string>
    <string name="care_mode_switch_text">Intelligent Care Mode</string>
    <string name="care_mode_hint">When the Care Mode is turned on, the following functions will run automatically every time the system enters the Disarm/Home Arm state.</string>
    <string name="care_mode_life_motion_text">Life State Monitoring</string>
    <string name="care_mode_life_title">Care Mode</string>
    <string name="done">Done</string>
    <string name="life_motion_acc_text">Life Monitoring Accessories</string>
    <string name="life_monitor_acc_hint">If all of the above accessories fail to detect signs of activity within period of time, all users will be notified and the system alarm will be triggered.</string>
    <string name="care_mode_max_time_text">Maximum No-action Time</string>
    <string name="life_state_system_alarm_delay_text">System Alarm Delay</string>
    <string name="care_mode_plugin_hint">Please select the accessory installed in the room for monitoring the abnormal living condition.</string>
    <string name="door_sensor_item_block">Bypassed</string>
    <string name="item_plugin_status_tamper_enabled">Tamper Enabled</string>
    <string name="main_ipc_vip_alert_service">Alert Service</string>
    <string name="device_status_net_lan">LAN</string>
    <string name="device_status_net_4G">4G</string>
    <string name="device_status_net_wifi">WLAN</string>
    <string name="device_status_not_connect">Not Connected</string>

    <string name="device_status_offline_mode">Offline Mode</string>

    <string name="device_status_offline_mode_hint">In offline mode, you can send security commands via SMS by clicking the button below.</string>
    <string name="section_tittle_common_function">Common Function</string>
    <string name="section_tittle_shortcut">Shortcut</string>
    <string name="section_tittle_door_window_sensor">Door/Window Status</string>
    <string name="section_tittle_ipc">@string/video_monitoring</string>
    <string name="section_tittle_padding">PADDING</string>
    <string name="section_tittle_add_more">ADD_MORE</string>

    <string name="event_list">Event List</string>
    <string name="event_list_setting">@string/filter</string>
    <string name="event_list_setting_accessories_online_status">Accessories Online/Offline</string>
    <string name="event_list_setting_common_operations">Common Operations</string>
    <string name="event_list_setting_events_in_disarm_state">Events in Disarm State</string>
    <string name="event_list_setting_door_sensor_tittle">Door/Window Status</string>
    <string name="event_list_setting_tamper_tittle">Tamper Alarm</string>
    <string name="event_list_setting_accessories">Accessories</string>
    <string name="event_list_setting_select_all">@string/select_all</string>
    <string name="event_list_setting_clear_all">Clear All</string>
    <string name="event_list_setting_confirm">@string/Confirm</string>
    <string name="event_list_setting_exceptions">Exceptions</string>
    <string name="event_list_setting_outage_power_backup">Outage Power Backup</string>
    <string name="event_list_setting_normal_events">Normal Events</string>
    <string name="event_list_setting_motion_detection">Motion Detection</string>
    <string name="event_list_setting_alarm">Alarm</string>
    <string name="event_list_setting_security_status">Security Status</string>
    <string name="event_list_setting_door_window_status">Door/Window Status (Disarm)</string>
    <string name="event_list_setting_tamper_triggered">Tamper Triggered (Disarm)</string>
    <string name="event_list_setting_accessories_status">Accessories Status</string>
    <string name="event_list_setting_power_station">Power Station</string>
    <string name="event_list_setting_smart_camera">Smart Camera</string>
    <string name="event_list_setting_alarm_system">Alarm System</string>
    <string name="event_list_setting_family">Family</string>

    <string name="today">Today</string>
    <string name="yesterday">Yesterday</string>
    <string name="ipc_setting_record_setting">Recording settings</string>
    <string name="ipc_setting_continuous_recording">Continuous Recording</string>
    <string name="ipc_setting_video">Video</string>
    <string name="ipc_setting_video_files">Video Files</string>
    <string name="ipc_setting_motion_dection_and_recording">Motion Detection &amp; Recording</string>

    <string name="ipc_settting_cloud_backup_service">Alert &amp; Cloud Backup Services</string>
    <string name="ipc_settting_turn_on_service">Turn on Services</string>
    <string name="ipc_settting_service_setting">Service Settings</string>
    <string name="ipc_settting_others">Others</string>
    <string name="ipc_subscription_current_plan">Current Plan</string>
    <string name="ipc_subscription_remainder">Remainder</string>
    <string name="ipc_subscription_upgrade_service">Upgrade Service</string>
    <string name="ipc_subscription_change_plan">Change Plan</string>
    <string name="ipc_subscription_buy_addition_pack">Buy Supplemental Pack</string>

    <string name="ipc_subscription_real_time_alart">Real-time Alert</string>
    <string name="ipc_subscription_clound_backup">Cloud Backup</string>
    <string name="ipc_subscription_extra_sercurity">Extra Security</string>
    <string name="ipc_subscription_monthly_plan">Monthly Plan</string>
    <string name="ipc_subscription_supplemental_pack">Supplemental Pack</string>
    <string name="ipc_subscription_manage_your_subscription">Manage your subscription</string>
    <string name="ipc_subscription_month">month</string>
    <string name="ipc_subscription_off">off</string>
    <string name="ipc_subscription_alerts">alerts</string>
    <string name="ipc_subscription_plan_detail">#totals alerts &amp; cloud backup every month.</string>
    <string name="ipc_subscription_pack_alert">#amount alerts &amp; cloud backup additional</string>
    <string name="ipc_subscription_monthly_plan_des_v1">If you have 1-3 cameras, we recommend this plan.</string>
    <string name="ipc_subscription_monthly_plan_des_v2">If you have 4-6 cameras, we recommend this plan.</string>
    <string name="ipc_subscription_monthly_plan_des_v3">If you have 7-10 cameras, we recommend this plan.</string>
    <string name="ipc_subscription_monthly_plan_des_v4">Commercial security? This plan could provide more comprehensive protection.</string>
    <string name="ipc_subscription_pack_detail">#plan exclusive offer</string>
    <string name="ipc_subscription_no_subscription_detail">100 alerts/month for free.</string>
    <string name="ipc_subscription_current_plan_detail">Expires on #expire_date, auto-renewal enabled.</string>
    <string name="ipc_subscription_monthly_plan_des">Monthly package description Android</string>
    <string name="ipc_subscription_supplemental_pack_des">Supplemental Pack description Android</string>
    <string name="ipc_subscription_change_plan_dialog_tip">Your subscription will change to #plan plan, which will take effect next month after the charge.</string>
    <string name="ipc_subscription_upgrade_plan_dialog_tip">Your subscription will update to #plan plan, which will take effect immediately after the charge.</string>
    <string name="ipc_subscription_pay_success">Successfully changed to #plan plan.</string>
    <string name="ipc_subscription_pay_success_degrade">#plan will come into effect next month.</string>
    <string name="ipc_subscription_addition_pack_pay_success">#total alerts have been successfully added.</string>
    <string name="ipc_subscription_pay_cancel">With alert &amp; cloud backup services, you\'ll get better monitoring and data protection.</string>
    <string name="ipc_subscription_continue_to_subscription">Continue to Subscribe</string>
    <string name="ipc_subscription_check_purchase_fail_tip">Sorry, there was a problem with the internet, you will be notified again when the order has been verified, or you can refresh the page later.</string>
    <string name="ipc_subscription_billing_unable">The current version of Google Play is too low to purchase</string>
    <string name="ipc_subscription_update">Update</string>
    <string name="ipc_subscription_alert_service">Alert Service</string>
    <string name="ipc_subscription_alert_service_remain">#remainder alerts remaining</string>
    <string name="ipc_subscription_buy_ipc_hint">This function is only for recording devices such as cameras or video doorbells, please add one first.</string>
    <string name="ipc_subscription_buy_one">Buy One</string>
    <string name="ipc_subscription_more">More</string>

    <string name="ipc_subscription_alerts_remaining">alerts remaining</string>
    <string name="ipc_setting_recording_period">Time of Recording</string>
    <string name="ipc_setting_each_day">Everyday</string>
    <string name="ipc_setting_alarm_sound">Alarm Sound</string>
    <string name="ipc_setting_detection_period">Time of Recording</string>
    <string name="ipc_setting_alert_conditions">Alert Conditions</string>
    <string name="ipc_setting_alert_conditions_follow_device">Follow the Device Status</string>
    <string name="ipc_setting_alert_conditions_by_time">By Time</string>
    <string name="ipc_setting_alert_conditions_follow_device_tip">In the following state, after the camera detects movement, the system will send real-time videos and photos to you, and back them up in the cloud for 30 days.</string>
    <string name="ipc_setting_alert_conditions_by_time_tip">During the following time, after the camera detects movement, the system will send real-time videos and photos to you, and back them up in the cloud for 30 days.</string>
    <string name="ipc_settting_alert_period">Time of Alert</string>
    <string name="ipc_motion_detect_go_live">Go Live</string>
    <string name="ipc_motion_detect_check_the_video">Check the video</string>
    <string name="ipc_subscription_discount">#discount off</string>
    <string name="ipc_setting_not_save_tip">You have changed the settings, whether to save?</string>
    <string name="ipc_setting_alert_condition">Alert Conditions</string>
    <string name="alert_conditions">Alert Conditions</string>
    <string name="ipc_setting_alert_mode">Alert Mode</string>
    <string name="ipc_setting_alert_mode_normal">Normal Alert</string>
    <string name="ipc_setting_alert_mode_no_alert">No Alert, only Cloud Backup</string>
    <string name="ipc_subscription_alert_exhausted_title">Your motion detection alerts this month exhausted</string>
    <string name="ipc_subscription_alert_exhausted_content">You can upgrade service to continue real-time alert and cloud backup benefits.</string>
    <string name="care_mode_no_action_cancel">Cancel Alarm</string>
    <string name="care_mode_no_action_alarm">Alarm</string>
    <string name="care_mode_no_action_content">#family: no signs of activity in the home for more than #no_action_time hours, to avoid accidents, the system is about to alarm.</string>
    <string name="care_mode_sos_content">Life State Monitoring</string>
    <string name="arm_toast">Armed now</string>
    <string name="disarm_toast">Disarmed now</string>
    <string name="homearm_toast">Homearmed now</string>
    <string name="arm_delay_toast">To be armed in #delay_time second</string>
    <string name="Bypassed">Bypassed</string>
    <string name="rename_accessory">Rename Accessory</string>
    <string name="block_mode_tamper_alarm_hint">Tamper Alarm is bypassed temporarily, then it will not notify when tampered.</string>
    <string name="block_mode_block_plugin_hint">This plugin is bypassed temporarily, then it will not notify when triggered.</string>
    <string name="block_mode_chime_hint">When the Chime Mode is currently selected, the security function of this accessory will be disabled.</string>
    <string name="care_mode_no_plugin_hint">There are currently no accessories available for life monitoring.</string>
    <string name="rolling_door_tiggle_hint_1">Rolling Door Sensor learning step1 description</string>
    <string name="rolling_door_tiggle_hint_2">Rolling Door Sensor learning step2 description</string>
    <string name="permission_tip_camera_not_grant">Please allow camera access for using camera</string>
    <string name="permission_tip_album_not_grant">Please allow photo access to read and write photos</string>
    <string name="permission_tip_record_audio_not_grant">Please allow microphone access for IPC talking</string>
    <string name="permission_tip_contact_not_grant">Please allow contacts access for adding emergency contact, or add users</string>
    <string name="permission_tip_location_not_grant">Share your location for selecting time zone of the Panel in order to get better local service</string>

    <string name="permission_tip_bluetooth_not_grant">Please allow bluetooth access for device connecting</string>
    <string name="service_terms">Service Terms</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="ipc_motion_detection">Motion Detect</string>
    <string name="ipc_motion_detection_dscam">Motion Detection</string>
    <string name="No">No</string>
    <string name="Yes">Yes</string>
    <string name="add_device_or_family">Add Device / Join a Family</string>
    <string name="scan_the_device_qr_code">Scan the Device QR Code</string>
    <string name="scan_the_family_qr_code">Scan the Family QR Code</string>
    <string name="family_setting">Family Settings</string>
    <string name="app_setting">App Setting</string>
    <string name="family">Family</string>
    <string name="panel">Panel</string>

    <string name="advanced_setting">Advanced Setting</string>
    <string name="save_to_system_album">Save to System Album</string>
    <string name="delete">Delete</string>
    <string name="create_a_family">Create a Family</string>
    <string name="rename_family">Rename</string>
    <string name="join_a_family">Join a Family</string>
    <string name="default_family_name">HOME</string>
    <string name="rename">Rename</string>
    <string name="tap_to_scan">Tap to Scan</string>
    <string name="start_adding_a_device">Start Adding a Device</string>
    <string name="no_device_qr_code">No device QR code?</string>
    <string name="full_protection_for_your_home">Full Protection for Your Home</string>
    <string name="add_more">Add More</string>
    <string name="sign_up">Sign Up</string>
    <string name="email">Email</string>
    <string name="phone">Phone</string>
    <string name="Battery_Range">Battery Range</string>
    <string name="username_password">Username &amp; Password</string>
    <string name="create_an_account">Create an Account</string>

    <string name="send_email_verify_code_success_hint">The verification code has been sent to your email, please login to check your email</string>
    <string name="scene_tittle_full_arm">Full Arm</string>
    <string name="scene_content_full_arm">Description of Full Arm</string>
    <string name="scene_tittle_security_monitoring">Security Monitoring</string>
    <string name="scene_content_security_monitoring">Description of Security Monitoring</string>
    <string name="scene_tittle_life_radar">Smart Life Radar</string>

    <string name="scene_content_life_radar">Description of Smart Life Radar</string>

    <string name="scene_tittle_real_time_monitoring">Real-time monitoring</string>
    <string name="scene_content_real_time_monitoring">Description of Real-time monitoring</string>
    <string name="scene_tittle_recording">Motion Detection &amp; Recording</string>

    <string name="scene_content_recording">Description of Motion Detection &amp; Recording</string>
    <string name="scene_tittle_smart_following">Smart Following</string>
    <string name="scene_content_smart_following">Description of Smart Following</string>
    <string name="scene_tittle_protect_loved">Protect Your Loved Ones</string>
    <string name="scene_content_protect_loved">Description of Protecting Your Loved Ones</string>
    <string name="scene_tittle_clean_experience">Clean Shoveling Experience</string>
    <string name="scene_content_clean_experience">Description of Clean Shoveling Experience</string>
    <string name="scene_tittle_power_station">Power Station</string>
    <string name="scene_content_power_station">A home energy storage solution that tracks utility prices and optimizes energy deployment, along with more customizable features.</string>
    <string name="add_devices">Add Devices</string>
    <string name="plugin_smart_alarm_system">Smart Alarm System (Small pkg)</string>
    <string name="plugin_window_contact_sensor">Door Window Sensor</string>
    <string name="plugin_wireless_vibration_sensor">Vibration Sensor</string>
    <string name="plugin_wireless_garage_door_sensor">Rolling Door Window Sensor</string>
    <string name="plugin_wireless_pet_immune_motion_sensor">Wireless Pet-Immune Motion Sensor</string>
    <string name="plugin_wireless_indoor_siren">Wireless Indoor Siren</string>
    <string name="plugin_wireless_motion_sensor">PIR Sensor</string>
    <string name="plugin_wireless_outdoor_siren">Wireless Outdoor Siren</string>
    <string name="plugin_wireless_water_sensor">Liquid Sensor</string>
    <string name="plugin_wireless_smoke_sensor">Smoke Sensor</string>
    <string name="plugin_rfid_tag">RFID Tag</string>
    <string name="plugin_wireless_keypad">Wireless Keypad</string>
    <string name="plugin_life_radar">Smart Life Radar</string>
    <string name="plugin_wireless_outdoor_camera_bullet">Wireless Outdoor Camera (Bullet Type)</string>

    <string name="plugin_wireless_outdoor_camera_fixed">Wireless Indoor Camera (Fixed Type)</string>
    <string name="plugin_ptz_camera">PTZ Camera</string>
    <string name="plugin_wireless_battery_camera">Wireless Security Camera</string>
    <string name="plugin_solar_security_camera">Solar Security Camera</string>
    <string name="plugin_wired_security_camera">Wired Security Camera</string>
    <string name="plugin_bmt">@string/power_station</string>
    <string name="plugin_wireless_smart_bulb">WIFI Bulb</string>
    <string name="plugin_wifi_smart_plug_dsp01a">WIFI Plug</string>
    <string name="plugin_wireless_signal_repeater_plug">Signal Repeater Plug</string>
    <string name="plugin_wireless_smart_plug_dsph1">Smart Plug</string>
    <string name="plugin_wireless_smart_button">Smart Button</string>
    <string name="plugin_custom_remote_controller">Remote Controller</string>
    <string name="plugin_care_go_4g">Care Go 4G</string>
    <string name="plugin_care_go">Care Go</string>
    <string name="plugin_smart_cat_litter_box">Smart Cat Litter Box</string>
    <string name="find_password">Reset Password</string>
    <string name="switch_a_family">Switch Family</string>
    <string name="share_qr_code">Share QR Code</string>
    <string name="quit">Quit</string>
    <string name="only_one_family_hint">There is only one family, which cannot be deleted</string>
    <string name="has_another_family_member">To Delete a family, please clear other members first</string>
    <string name="device_initialization">Initialization</string>
    <string name="device_settings">Device Settings</string>
    <string name="advanced_settings">Advanced Settings</string>
    <string name="delete_the_panel">Delete the Panel</string>
    <string name="delete_the_panel_hint">Warning of Deleting the Panel</string>
    <string name="delete_the_panel_confirm">Are you sure you want to delete?</string>
    <string name="add">Add</string>
    <string name="add_more_no_accessories">No accessories yet? Buy one</string>
    <string name="no_panel_add_plugin_hint">There is no panel in the current family or the panel is offline, so accessories cannot be added</string>
    <string name="ble_step_had_panel_hint">There is already a panel in current family, you need to delete it before adding a new one. You can also add it to another family</string>
    <string name="ble_step_add_panel_had_bind_hint">Failed to add, please delete the device from the original family and add it again</string>
    <string name="ble_step_config_network_had_reset_hint">This panel have been reset, please delete the original one from the family and re-add it</string>
    <string name="ble_add_no_ipc_found">No camera found?</string>
    <string name="ble_ipc_scan_close_to_phone">Camera pairing guide</string>
    <string name="network_setting">Network Settings</string>
    <string name="reset">Reset</string>
    <string name="this_username_is_already_occupied">This username is already occupied</string>
    <string name="un_save_member_info_change">You have changed the settings, whether to save? </string>
    <string name="motion_detection">Motion Detection</string>
    <string name="motion_detection_con">Detection Conditions</string>
    <string name="motion_detection_range">Detection Range</string>
    <string name="motion_detection_acc">Detection Accuracy</string>
    <string name="medium">Medium</string>
    <string name="low">Low</string>
    <string name="high">High</string>
    <string name="only_current_admin_msg">At least one administrator is required in a family</string>
    <string name="by_time">By Time</string>
    <string name="reach_max_amount_of_family">The number of families has reached its limit</string>
    <string name="reach_max_member_of_family">Membership has reached its limit</string>
    <string name="already_at_family">@string/qr_been_bind</string>
    <string name="motion_detection_con_setting">Conditions Setting</string>
    <string name="detection_con_setting_hint">During the following time, after the camera detects movement, the system will send real-time videos and photos to you, and back them up in the cloud for 30 days.</string>
    <string name="conditions_setting">Conditions Setting</string>
    <string name="full_day">Full Day</string>
    <string name="firmware_upgrading">Firmware upgrading</string>
    <string name="normal_alert">System Message</string>
    <string name="critical_alert">Alarm Notifications</string>
    <string name="no_alert">No Alert</string>
    <string name="coming_soon">Comming soon</string>
    <string name="add_ipc_limit_error">The number of devices has reached its limit</string>
    <string name="the_number_of_devices_has_reached_its_limit">The number of devices has reached its limit</string>
    <string name="ipc_device_id">Device ID</string>
    <string name="dscam_advance_current_network">Current Network</string>
    <string name="ipc_change_network">Change Network</string>
    <string name="select_time_zone">Select timezone</string>
    <string name="sync_time_zone">SYNC with phone\'s timezone</string>
    <string name="ipc_reset_device">Resume to Default Setting</string>
    <string name="ipc_version">Camera Version</string>
    <string name="ipc_restore_device">Confirm to resume default settings? This operation only resets the user settings and does not clear device data.</string>
    <string name="saved_to_album">Saved to local album</string>
    <string name="ipc_sd_card">SD Card</string>
    <string name="ipc_space_usage">Space Usage</string>
    <string name="flip">Flip</string>
    <string name="ipc_sd_card_hint">Records will be overwritten automatically when the storage space is insufficient, please pay attention to the space usage.</string>
    <string name="ipc_no_sd_card">SD card not detected</string>
    <string name="dscam_advance_wifi_rssi">WiFi Signal</string>
    <string name="mac_address">MAC Address</string>
    <string name="filter">Filter</string>
    <string name="dscam_save_to_album">Saved to local album</string>
    <string name="start_talk">Intercom mode on, please speak</string>
    <string name="iap_14_days_of_rolling_storage_with_unlimited_space">14 days of rolling storage with unlimited space</string>
    <string name="iap_cloud_storage">Cloud Storage</string>
    <string name="iap_video_cloud_service">Video Cloud Service</string>
    <string name="iap_function_sub_title1">It\'s no need to worry about video loss due to SD card corruption.</string>
    <string name="iap_function_title2">Military-grade Encryption</string>
    <string name="iap_function_sub_title2">Only users with family access can decrypt videos. It\'s no need to worry about video leaks.</string>
    <string name="iap_function_title3">Push with Picture</string>
    <string name="iap_function_sub_title3">Get a snapshot of an alarm event.</string>
    <string name="iap_function_title4">Smooth Playback at Any Time</string>
    <string name="iap_function_sub_title4">Watch videos smoothly on your mobile phone anytime, anywhere, without the need to connect to a device, not limited by the state of the device.</string>
    <string name="iap_function_title5">Video Download</string>
    <string name="iap_function_sub_title5">Videos can be downloaded to your phone in mp4 format.</string>
    <string name="iap_function_title6">SD Card Insertion/Removal Alerts</string>
    <string name="iap_function_sub_title6">Receive instant alerts on SD card insertion and removal to keep track of storage status.</string>
    <string name="iap_function_title7">Daily Memories</string>
    <string name="iap_function_sub_title7">Daily compressed video automatically generated to record each day\'s story.</string>
    <string name="iap_cloud_storage_service_is_on">Cloud storage service is on</string>
    <string name="iap_free_trial">Free Trial</string>
    <string name="iap_transfered">Transfered</string>
    <string name="iap_what_is_cloud_storage">What is Cloud Storage?</string>
    <string name="iap_plan_name">#plan Days</string>
    <string name="horizontal_text">Horizontal Flip</string>
    <string name="ver_text">Vertical Flip</string>
    <string name="flip_reboot_hint">The flip will take effect after rebooting, confirm to continue?</string>
    <string name="night_version_mode">Night Vision Mode</string>
    <string name="ir_cut">IR-Cut</string>
    <string name="color_night">Color Night</string>
    <string name="ipc_remember_ssid_hint">Use the following wireless network connection:</string>
    <string name="select_other_network">Select Other Networks</string>
    <string name="fail_to_connect_network">Failed to connect the network.</string>

    <string name="advance_change_panel_input_old_pwd_title">Enter current password</string>
    <string name="detection_acc_hint_low">With this accuracy, the camera can identify humans, warm-blooded animals, cars and even hot air currents.</string>
    <string name="detection_acc_hint_medium">With this accuracy, the camera can identify humans, warm-blooded animals, cars.</string>
    <string name="detection_acc_hint_high">With this accuracy, the camera only identify humans.</string>
    <string name="ipc_save_snapshot_without_permission">Failed to save, please check if the relevant permission is allowed</string>
    <string name="ipc_del_tip">Camera #Camera_Name has been deleted.</string>
    <string name="bmt_del_tip">#device_name is deleted from #family.</string>
    <string name="choose_camera">Choose Camera</string>
    <string name="email_address">Email address</string>
    <string name="skip">Skip</string>
    <string name="switched_to_ir_cut">Switched to IR-Cut</string>
    <string name="switched_to_color_night">Switched to Color Night</string>
    <string name="switched_to_HD">Switched to HD</string>
    <string name="switched_to_STD">Switched to SD</string>
    <string name="quit_unbind_device">Quit and Unbind Device</string>
    <string name="force_delete_home_hint">You are the only administrator in the family and all devices will be unbinded after your quit. Devices need to be Reset to be added again.</string>
    <string name="no_alarm_panel">No alarm panel</string>
    <string name="select_chime">Select Chime</string>
    <string name="what_is_the_difference">What\'s the difference?</string>
    <string name="ipc_low_battery_tip">#Camera: Camera low battery. Please charge it.</string>
    <string name="ipc_video_files">Video Files</string>
    <string name="ipc_turn_on_storage">Turn on Storage</string>
    <string name="icp_space_usage">Space Usage</string>
    <string name="icp_sd_card_cannot_read">SD card cannot be read, please try to format it.</string>
    <string name="ble_ipc_scan_change_network_close_to_phone">Camera network configuring guide</string>
    <string name="family_service">Family Service</string>
    <string name="family_service_des">Unlimited cameras, 30-Day video history</string>
    <string name="single_camera_service">Single Camera Service</string>
    <string name="single_camera_service_des">For single camera, 30-Day video history</string>
    <string name="single_video_doorbell_service">Single Video Doorbell Service</string>
    <string name="single_video_doorbell_des">For single video doorbell | 30-Day video history</string>

    <string name="ai_mode_exit_edit">Are you sure you want to discard all changes?</string>
    <string name="ai_mode_initialize">Are you sure you want to initialize all the settings?</string>
    <string name="ai_mode_details_charge_discharge">After reaching the preset SOC, the battery enters standby state. Solar energy charges either batteries or powers loads based on priority settings, while grid power covers loads shortfall.</string>
    <string name="ai_mode_details_discharge">At standby state, Solar energy charges either batteries or powers loads based on priority settings, while grid power covers loads shortfall.</string>
    <string name="ai_details_warning">Pauses grid exports at electricity price.</string>

    <string-array name="smart_button_cmd_security">
        <item>Arm</item>
        <item>Home Arm</item>
        <item>SOS</item>
    </string-array>
    <string-array name="smart_button_cmd_tuya_plugin">
        <item>Turn On</item>
        <item>Turn Off</item>
    </string-array>
    <string-array name="smart_button_cmd_my_bulb_plug">
        <item>Only Turn On</item>
        <item>Only Turn Off</item>
        <item>Turn On/Off</item>
    </string-array>
    <string-array name="smart_button_cmd_music">
        <item>Tinkle</item>
        <item>Westminster Quarters</item>
        <item>For Elise</item>
        <item>Haydn</item>
        <item>Minuet</item>
    </string-array>
    <string-array name="smart_button_cmd_volume">
        <item>Mute</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
    </string-array>
    <string-array name="add_more_tab_root_tittle">
        <item>Home</item>
        <item>Personal</item>
        <item>Pets</item>
    </string-array>
    <string-array name="add_more_tab_root_tittle_helio_pro">
        <item>Home</item>
    </string-array>
    <string-array name="add_more_tab_root_tittle_customize">
        <item>Home</item>
    </string-array>
    <string-array name="add_more_tab_root_tittle_bmt">
        <item>Home</item>
    </string-array>
    <string-array name="add_more_tab_home_tittle">
        <item>Alarm System</item>
        <item>Elderly Care</item>
        <item>Video Monitoring</item>
        <item>Customized Scenes</item>
    </string-array>
    <string-array name="add_more_tab_home_tittle_helio_pro">
        <item>Alarm System</item>
        <item>Video Monitoring</item>
        <item>Customized Scenes</item>
    </string-array>
    <string-array name="add_more_tab_home_tittle_customize">
        <item>Alarm System</item>
        <item>Video Monitoring</item>
        <item>Customized Scenes</item>
    </string-array>
    <string-array name="add_more_tab_home_tittle_bmt">
        <item>Energy</item>
    </string-array>
    <string-array name="add_more_tab_personal_tittle">
        <item>Protection On-the-go</item>
    </string-array>
    <string-array name="add_more_tab_pets_tittle">
        <item>Easy Cat Care</item>
    </string-array>
    <string name="ble_check_location_permission">To scan Bluetooth LE devices, please allow location access for connecting. You can disable this permission after adding the device</string>
    <string name="ble_check_location_permission_modify_network">To scan Bluetooth LE devices, please allow location access for connecting. You can disable this permission later.</string>
    <string name="prime_services">Prime Services</string>
    <string name="prime_services_cloud_storage_hint">Better data security for recording devices such as cameras and video doorbells.</string>

    <string name="ble_doorbell_scan_close_to_phone">Video doorbell pairing guide</string>
    <string name="ble_check_doorbell_bluetooth">If the indicator light is flashing but still doesn\'t show up, insert a pin into the reset hole and hold it for 3 seconds until you hear a beep.</string>
    <string name="video_doorbell">Video Doorbell</string>
    <string name="chimes_added">Chimes Added</string>
    <string name="answer">Answer</string>
    <string name="add_a_chime">Add a Chime</string>
    <string name="test_the_chime">Test the Chime</string>
    <string name="accessory_settings">Accessory Settings</string>
    <string name="chime_help">After the chime paired successfully with the video doorbell, you can press the test button to check the status. Chime beeps indicate that everything works well. The interval time between tests is 5 seconds.</string>
    <string name="add_a_chime_tip">Open the back cover of the chime, remove the battery insulation strip to power it up, then scan the QR code on the back cover with your phone.</string>
    <string name="no_chime">No Chime</string>
    <string name="video_doorbell_network_config_guide">Video doorbell network configuring guide</string>
    <string name="ble_check_doorbell_bluetooth_config_network">Check that the indicator light is flashing, if not, turn it off and back on again.</string>
    <string name="no_video_doorbell_tip">There is no video doorbell online in the current family, please add one first</string>
    <string name="doorbell_save_name_error_tip">Sorry, the name was not saved. You can also rename it later on the settings page.</string>
    <string name="rename_later">Rename Later</string>
    <string name="Chime">Chime</string>
    <string name="add_chime_doorbell_offline_tip">Device offline, please try later.</string>
    <string name="add_chime_other_error_tip">Sorry, there was a problem with the internet, please try again. </string>
    <string name="add_chime_have_binded_tip">Sorry, the chime is already bound to another device. If you are sure to unbind it, please reset chime by inserting a pin into the hole.</string>
    <string name="change_chime_doorbell_tip">Please delete and reset chime before adding it to another device.</string>
    <string name="ipc_format_sd_card_success_hint">Format SD Card is completed</string>
    <string name="narrow">Narrow</string>
    <string name="wide">Wide</string>
    <string name="video_monitoring">Video Monitoring</string>

    <string name="firmware_version">Firmware Version</string>
    <string name="ipc_upgrade_dialog_nor">#plugin has a new version available, do you want to update now?</string>
    <string name="ipc_upgrade_dialog_force">#plugin Must update to new version to continue using.</string>
    <string name="update">Update</string>
    <string name="update_to_version">Update to #version</string>
    <string name="ipc_upgrade_state_des_downloading">please make sure that the camera network is working properly during the update process.</string>
    <string name="ipc_upgrade_state_des_complete">Your update is ready, now restarting.</string>
    <string name="ipc_upgrade_state_des_error">Sorry, there was a problem with the internet, please try again.</string>
    <string name="ipc_upgrade_state_des_low_battery">Camera Low battery. Please charge it and try again.</string>
    <string name="ipc_upgrade_state_des_doing_upgrade">The camera is now updating, and you can exit this page.</string>
    <string name="downloading">Downloading</string>
    <string name="restarting">Restarting</string>
    <string name="the_current_version_is_up_to_date">The current version is up to date.</string>
    <string name="ipc_wifi_version">Wi-Fi Version</string>
    <string name="ipc_ble_version">BLE Version</string>
    <string name="progress">Progress</string>
    <string name="update_configuration_files">Update configuration files</string>

    <string name="iap_esim_service">eSIM Service</string>
    <string name="iap_prime_services_esim_service_hint">esim description</string>
    <string name="iap_esim_service_is_on">eSim service is on</string>
    <string name="iap_expiration_date">Expiration Date:</string>
    <string name="iap_renew">Renew</string>
    <string name="get_your_free_family_service">Get your FREE Family Service</string>
    <string name="features">Features</string>
    <string name="redeem">Redeem</string>
    <string name="order">Orders</string>
    <string name="iap_QA">Q&amp;A</string>
    <string name="iap_QA_content">Q&amp;A contents</string>
    <string name="choose_a_device">Choose a Device</string>
    <string name="iap_renew_service_terms">Clicking Renew means you agree to the #service_terms and #privacy_policy</string>
    <string name="iap_service_terms">Service Terms</string>
    <string name="iap_privacy_policy">Privacy Policy</string>
    <string name="iap_cancel_pay_tip">Sure to quit? With this service, you will receive more powerful features and improved protection.</string>
    <string name="iap_continue_to_pay">Continue to Pay</string>
    <string name="iap_pay_fail_tip">Sorry, there was a problem with the internet, please try again.</string>
    <string name="iap_pay_success_tip">Your service has been successfully updated.</string>
    <string name="iap_redemption_code">Redemption Code</string>
    <string name="you_have_a_beta_user_club_invitation">You have a Beta User Club Invitation</string>
    <string name="here_is_your_gift">Here is your gift</string>
    <string name="video_cloud_service">Video Cloud Service</string>
    <string name="_6_month_for_family_service">6-Month for</string>
    <string name="i_love_that">I\'d Love That</string>
    <string name="beta_user_cloud_invite_input_email_tip">Enter email address to receive your gift :)</string>
    <string name="iap_beta_user_cloud_invite_success">Thanks for your participation. The next step has been sent to your email. Let\'s check it out.</string>
    <string name="got_it">Got It</string>
    <string name="iap_free_family_service_tip">Hey, here is your #service_name for 6 months</string>
    <string name="iap_free_family_service">FREE FAMILY SERVICE</string>
    <string name="get">GET</string>
    <string name="Manage">Manage</string>
    <string name="delete_device_with_service">After deletion, the Video Cloud Service will continue to be consumed. Confirm to delete?</string>
    <string name="delete_family_with_service">The Video Cloud Service has not be expired, the deletion of the family is considered as automatic waiver.</string>
    <string name="transfer">Transfer</string>
    <string name="quit_and_discard">Quit and Discard</string>
    <string name="free_trial_until">Free Trial until:</string>
    <string name="expired">Expired</string>
    <string name="iap_service_pending">Pending, #lenthOfService days remaining.</string>
    <string name="family_sales">Family Sales</string>
    <string name="delete_ipc_cloud_storage_not_expire">After deletion, the Video Cloud Service will continue to be consumed. Confirm to delete?</string>
    <string name="delete_ipc_cloud_storage_has_motion_record">After deletion, cloud videos of this camera may not be viewable. Videos within the expiration date can be restored after the device is re-added back to this family.</string>
    <string name="iap_save">Save #discount</string>


    <string name="permission_ok">Okay, I Understand</string>
    <string name="permission_not_show_again">Do not show this again</string>
    <string name="permission_go_setting">Go Settings</string>
    <string name="permission_notification">Notifications</string>
    <string name="permission_notification_hint">Device information and system status messages can help you manage your device and protect your security better, and we recommend that you allow the App to send notifications. You will be asked to give this permission when you click the button below. Please choose ALLOW.</string>
    <string name="permission_storage">Storage</string>
    <string name="permission_storage_lang_hint">The App can display the content based on your language, which requires saving a language pack, so the App needs to access storage permissions. You will be asked to give this permission when you click the button below. Please choose ALLOW.</string>
    <string name="permission_storage_picture_hint">The screenshot and recording of the monitoring screen and the screen overview display of the list require system file access permission. You will be asked to give this permission when you click the button below. Please choose ALLOW.</string>
    <string name="permission_location">Location</string>
    <string name="permission_location_ssid_hint">In order to configure the equipment more quickly, you need to authorize the app to use the location of the device. The app does not follow your location. You can turn this off later in your phone\'s System Settings. You will be asked to give this permission when you click the button below. We recommend that you choose WHILE USING THE APP.</string>
    <string name="permission_location_ble_1_hint">Bluetooth is required to configure the device, and device location is required to use the bluetooth. Therefore, the app needs the system location access temporarily.</string>
    <string name="permission_location_ble_action_hint">For details</string>
    <string name="permission_location_ble_2_hint">The app does not follow your location, and this permission is only enabled when scanning bluetooth devices. You can turn this off later in your phone\'s System Settings. You will be asked to give this permission when you click the button below. We recommend that you choose WHILE USING THE APP.</string>
    <string name="permission_gps_hint">During device configuration, you need to temporarily turn on the GPS. We will not track your location information and you can turn this off later in the System Settings.</string>
    <string name="do_not_show_this_again">Do not show this again</string>
    <string name="automatic_floodlight">Automatic Floodlight</string>
    <string name="floodlight_tip">When a dormant camera is awakened by motion detection, it will automatically turn on the floodlight if there is not enough light, which will increase power consumption.</string>

    <string name="enter_phone_number">Enter the mobile phone number</string>
    <string name="verification">Verification</string>
    <string name="via_email_username">Via Email or Username</string>
    <string name="via_phone">Via Phone</string>
    <string name="first_time_user">First time user?</string>
    <string name="remember">Remember</string>
    <string name="already_have_account">Already have an account?</string>
    <string name="via_email">Via Email</string>
    <string name="login">Login</string>
    <string name="verify">Verify</string>
    <string name="mail_setting">Mail Setting</string>
    <string name="phone_setting">Phone Setting</string>
    <string name="change_the_binding">Change the Binding</string>
    <string name="user_pwd_scope">0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ,._</string>
    <string name="username_contains_password">The username and password cannot be the same.</string>
    <string name="user_pwd_condition">The password should be between 6 and 40 characters and contains letters and numbers.</string>
    <string name="username_condition">Username is your unique ID (not case sensitive), you can use the default name directly.</string>
    <string name="username_format_illegal">The username should be between 3 and 40 characters and cannot contain spaces, colons or inverted commas.</string>
    <string name="email_format_illegal">Please enter a valid email.</string>
    <string name="phone_format_illegal">Please enter a valid cell phone number.</string>
    <string name="uid_or_pwd_error">Incorrect account or password.</string>
    <string name="set_password_tittle">Password</string>
    <string name="username">Username</string>

    <string name="motion_detection_alert_con_hint">Receive notifications only in the following panel states:</string>
    <string name="motion_detection_alert_con_panel_offline">The panel is currently offline. Settings will take effect when the panel comes online.</string>
    <string name="arm">Arm</string>
    <string name="disarm">Disarm</string>
    <string name="home_arm">Home Arm</string>
    <string name="sos">SOS</string>

    <string name="dscam_guide_tip_first_used">First time using a wireless battery camera? This is a list of installation and power saving tips for you.</string>
    <string name="dscam_v015_guide_tip_first_used">First time using a security camera? This is a list of installation tips for you.</string>
    <string name="dscam_guide_tip_motion_detection_frequency">Camera consuming too much power? You may need this battery saving tips.</string>
    <string name="take_a_look">Take a Look</string>
    <string name="user_guide">User Guide</string>
    <string name="dscam_guide_tittle_extend_battery_life">How to extend the battery life?</string>
    <string name="dscam_guide_tittle_detection_time">Set a reasonable motion detection time.</string>
    <string name="dscam_guide_content_detection_time">To reduce the power consumption caused by the camera being woken up frequently.</string>
    <string name="dscam_guide_tittle_detection_accuracy">Set the appropriate detection accuracy.</string>
    <string name="dscam_guide_content_detection_accuracy">If the detection accuracy is set to HIGH, the AI recognition process will increase power consumption.</string>
    <string name="dscam_guide_tittle_reduce_app_usage">Reduce App usage time.</string>
    <string name="dscam_guide_content_reduce_app_usage">The system waking up camera each time the App is launched also increases power consumption.</string>
    <string name="dscam_guide_tittle_reduce_trigger_pir">To reduce frequent triggering of the PIR sensor:</string>
    <string name="dscam_guide_content_reduce_trigger_pir">AVOID facing areas with strong light, temperature changes rapidly or the air stream flows frequently. AVOID facing high-traffic areas.</string>
    <string name="dscam_guide_tittle_improve_accuracy">How to improve the accuracy of motion detection?</string>
    <string name="dscam_guide_tittle_proper_field">Choose a proper field of view for the camera.</string>
    <string name="dscam_guide_content_proper_field_v005">1.8-2.2m is the recommended height when you choose wall mounting. Optimal range is 4m, where the maximum horizontal detection angle is 117 degrees. Detectable up to 12m, as the distance increases, the PIR detection angle decreases.</string>
    <string name="dscam_guide_content_proper_field_v006">2-3m is the recommended height when you choose wall mounting. Optimal range is 4m, where the maximum horizontal detection angle is 120 degrees. Detectable up to 8m, as the distance increases, the PIR detection angle decreases.</string>
    <string name="dscam_guide_tittle_placed_horizontally">Keep the lens placed horizontally.</string>
    <string name="dscam_guide_content_placed_horizontally">Keep the lens placed horizontally for best result.</string>
    <string name="dscam_guide_tittle_do_not_face_direction">Do not face the direction of the object approaching.</string>
    <string name="dscam_guide_content_do_not_face_direction">Objects coming head-on to the camera may not be detected by the motion sensor. It is easier to detect when the camera is facing the direction of the object crossing.</string>
    <string name="dscam_guide_tittle_solar_charge">How to use solar charging?</string>
    <string name="dscam_guide_tittle_ensure_sunlight">Ensure sufficient sunlight.</string>
    <string name="dscam_guide_content_ensure_sunlight">Please try to choose a place where the solar panel will receive maximum sunlight. Make sure that solar panel is fully exposed to direct sunlight.</string>
    <string name="copy">Copy</string>
    <string name="wired_bridge">Wired Bridge</string>
    <string name="alarm_sound">Alarm Sound</string>
    <string name="alarm_sound_tip">When motion detection is triggered, the device sounds a high-volume alarm for 15 seconds.</string>
    <string name="flash_alarm_tip">A 15s flashing alarm has been emitted.</string>
    <string name="flash_and_sound_alarm_tip">A 15s audible &amp; flashing alarm has been emitted.</string>

    <string name="resume">Resume</string>
    <string name="smart_camera">Smart Camera</string>

    <string name="follow_the_panel_status">Follow the Panel Status</string>
    <string name="debug_mode">Debug Mode</string>
    <string name="panel_debug_hint_before">The process of scanning the panel cannot be interrupted and you cannot exit , if you are ready, please click on to start</string>
    <string name="panel_debug_hint_debugging">Panel diagnostics are in progress, please do not exit the page.</string>
    <string name="panel_debug_success">Diagnosis completed</string>
    <string name="panel_debug_failed">Diagnostic failed</string>
    <string name="panel_debug_long_press_hint">Please press and hold the above text to copy the code, and send it to the technician.</string>
    <string name="ds_config_failed_network">Failed to connect the network.</string>
    <string name="ds_config_failed_server">Server connection failed</string>
    <string name="ds_config_failed_parse">Parse error</string>
    <string name="ds_config_failed_device">Device connection failed</string>
    <string name="ds_config_failed_no_response">No Response</string>

    <string name="only_battery_camera_support_video_timeline">Only Wireless Cameras support Video Timeline view.</string>
    <string name="select_all">Select All</string>

    <string-array name="english_week_string_array">
        <item>Sun</item>
        <item>Mon</item>
        <item>Tue</item>
        <item>Wed</item>
        <item>Thu</item>
        <item>Fri</item>
        <item>Sat</item>
    </string-array>
    <string name="no_record">No Record</string>
    <string name="video_time_line">Video Timeline</string>
    <string name="video_list">Video List</string>
    <string name="video_time_line_scroll_up_hint">Try scrolling up to view more</string>
    <string name="failed_to_save_photo">Fail to save the photo</string>
    <string name="no_task_downloading">No task being downloaded</string>
    <string name="download_list">Download List</string>
    <string name="video_list_page_hint">Only Wired Cameras support Video List view.</string>
    <string name="name_format_error_prefix">Please keep it within 1-40 characters and do not use the following characters:</string>
    <string name="name_format_error_char">/\\?*:\"&lt;&gt;|()#$%^&amp;!</string>
    <string name="follow_panel_status">Follow the Panel Status</string>
    <string name="motion_detection_hint_by_time">Motion detection is only enabled during the following periods.</string>
    <string name="motion_detection_hint_follow_panel">Motion detection is only enabled during the following panel states. (When the panel is offline, motion detection will always be on.)</string>
    <string name="motion_detection_follow_panel_select_empty">Please select at least one of them.</string>
    <string name="video_time_line_ipc_deleted">Camera #Camera_Name has been deleted.</string>
    <string name="ipc_timezone_unsync_hint">The timezone of your camera #Camera_Name is different from your phone. Do you need to sync it?</string>
    <string name="unsynced">Unsynced</string>
    <string name="preview">Preview</string>
    <string name="exit_play_with_recording">Now you are recording, sure to discard it?</string>
    <string name="save_live_record_no_space_tip">Unable to save due to lack of storage space.</string>

    <!-- 编辑首页widget start-->
    <string name="main_widget_cancel">Cancel</string>
    <string name="main_widget_delete">Delete Widget</string>
    <string name="main_widget_edit">Edit</string>
    <string name="main_widget_edit_done">Done</string>
    <string name="main_widget_edit_delete_tip">Are you sure you want to delete?</string>
    <!-- 编辑首页widget end -->

    <!-- 首页add widget start -->
    <string name="select_plugin_add_widget_list_fragment_bar_title">Add Widget</string>
    <string name="add_widget_fragment_bar_title">Widget</string>
    <string name="multi_screen">Multi-Screen</string>
    <!-- 首页add widget end -->


    <!-- power station start -->
    <string name="power_station_02">Power Station 02</string>
    <string name="power_station_kWh">kWh</string>
    <string name="power_station_kw">kw</string>
    <string name="power_station_solar">Solar</string>
    <string name="power_station_grid">Grid</string>
    <string name="power_station_extra_supply">Extra Supply</string>
    <string name="power_station_secure_supply">Secure Supply</string>
    <string name="power_station_vehicle">Vehicle</string>
    <string name="power_station_battery">Battery</string>
    <string name="power_station_keep_on_load">KeepON Load</string>
    <string name="power_station_additional_load">Additional Load</string>
    <string name="power_station_cdv_offline_val">-</string>
    <string name="power_station_cdv_zero">0</string>
    <string name="power_diagnostic_network">Diagnosis</string>
    <string name="power_battery_balance">Battery Balance</string>
    <string name="power_battery_lasts_for">Battery Lasts for</string>
    <string name="power_battery_need_to_charge">Need to Charge</string>
    <string name="power_price_insensitive">Price Insensitive</string>
    <string name="power_smart_charge">Smart Charge</string>
    <string name="power_extreme_saving">Extreme Saving</string>
    <string name="power_customize">Customize</string>
    <string name="power_emergency_charge">Emergency Charge</string>
    <string name="power_today_usage">Usage Today</string>
    <string name="power_impacts_and_strategies">Impacts &amp; Strategies</string>
    <string name="power_how_to_use">How to Use Your Power Station?</string>
    <string name="power_guide_lines_content">Maximize the benefits of your Power Station by using it properly.</string>
    <string name="power_guide_lines_warning">Warning</string>
    <string name="power_contact_customer_support">Contact Customer Support</string>
    <string name="power_restart">Restart Inverter</string>
    <string name="power_restart_inverter">Restart Inverter</string>
    <string name="power_ah">ah</string>
    <string name="power_h">h</string>
    <string name="power_min">min</string>

    <string name="power_battery_bar_status_text_1">Plenty Energy</string>
    <string name="power_battery_bar_status_text_2">Smart Reserve</string>
    <string name="power_battery_bar_status_text_3">Emergency Reserve</string>
    <string name="power_battery_bar_status_text_4">Low-power Alert</string>
    <string name="power_battery_bar_status_text_5">Low-power Protection</string>

    <string name="impact_strategies_reserve_mode">Reserve Mode</string>
    <string name="impact_strategies_reserve_mode_skip">Skip</string>
    <string name="impact_strategies_virtual_power_plant">Virtual Power Plant</string>
    <string name="impact_strategies_emergency_charge">Battery Emergency Charge</string>
    <string name="impact_strategies_emergency_charge_desc">Emergency charging of the power supply for a set period of time for special situations. When this function is on, it will override the charging rules for the currently selected mode.</string>
    <string name="impact_strategies_emergency_charge_start">Start</string>
    <string name="impact_strategies_emergency_charge_end">End</string>
    <string name="impact_strategies_reserve_mode_item_name_1">Price Insensitive</string>
    <string name="impact_strategies_reserve_mode_item_name_2">Smart Charge</string>
    <string name="impact_strategies_reserve_mode_item_name_3">Extremely Saving</string>
    <string name="impact_strategies_reserve_mode_item_name_4">Battery Emergency Charge</string>
    <string name="impact_strategies_reserve_mode_item_desc_1">For users whose Utility Rate Plan is Fixed.</string>
    <string name="impact_strategies_reserve_mode_item_desc_2">Track electricity market prices. For users whose Utility Rate Plan is Variable.</string>
    <string name="impact_strategies_reserve_mode_item_desc_3">Track electricity market prices with a higher price limit than Smart Charge.</string>
    <string name="impact_strategies_reserve_mode_item_2_price_desc_1">Within the battery Smart Reserve range (#smart_reserve), use Good Price: #price_limit.</string>
    <string name="impact_strategies_reserve_mode_item_2_price_desc_2">Within the Emergency Reserve range (#emergency_reserve), use Acceptable Price: #price_limit.</string>
    <string name="impact_strategies_reserve_mode_now">Now</string>
    <string name="impact_strategies_reserve_mode_next">Next</string>
    <string name="impact_strategies_reserve_mode_edit">Edit</string>
    <string name="impact_strategies_not_support_tips">The region of your device does not support price forecasting, so some modes are not available.</string>
    <string name="is_reserve_mode_hashtag_smart_reserve">#smart_reserve</string>
    <string name="is_reserve_mode_hashtag_emergency_reserve">#emergency_reserve</string>
    <string name="is_reserve_mode_hashtag_price_limit">#price_limit</string>


    <string name="impact_strategies_battery_reserve_settings">Battery Reserve Settings</string>
    <string name="impact_strategies_resume_to_default_setting">Resume to Default Setting</string>
    <string name="impact_strategies_confirm">Confirm</string>
    <string name="impact_strategies_batter_reserve_setting_good_price">Good Price</string>
    <string name="impact_strategies_batter_reserve_setting_lt_today_average">&lt;Today\'s average</string>
    <string name="impact_strategies_batter_reserve_setting_lt_today_average_multiply">&lt; Today\'s average × </string>
    <string name="impact_strategies_batter_reserve_setting_acceptable_price">Acceptable Price</string>
    <string name="impact_strategies_batter_reserve_setting_ignore">Ignore</string>
    <string name="impact_strategies_batter_reserve_setting_emergency_reserve">Emergency Reserve</string>
    <string name="impact_strategies_batter_reserve_setting_smart_reserve">Smart Reserve</string>
    <string name="impact_strategies_batter_reserve_setting_got_it">Got It</string>
    <string name="impact_strategies_batter_reserve_setting_note">Note</string>
    <string name="impact_strategies_now">Now</string>
    <string name="impact_strategies_less_than_average_today"> &lt;Average Today</string>
    <string name="impact_strategies_acceptable_higher_than_good">Please set Acceptable Price higher than Good Price.</string>
    <string name="impact_strategies_impacts">Impacts</string>
    <string name="impact_strategies_eco">Eco</string>
    <string name="impact_strategies_revenue">Revenue</string>
    <string name="impact_strategies_impacts_note">Based on average energy consumption of #product_consumption kWh/100km.</string>
    <string name="impact_strategies_hashtag_product_consumption">#product_consumption</string>
    <string name="impact_strategies_eco_rate">Eco Rate</string>
    <string name="impact_strategies_sum">Sum</string>
    <string name="impact_strategies_home_savings">Home Savings</string>
    <string name="impact_strategies_vpp_sold">VPP Sold</string>
    <string name="impact_strategies_no_more_data">No more data</string>
    <string name="impact_strategies_no_more_data_note">No statistics available. The device will automatically count the revenue data after using it for a period of time for you.</string>
    <string name="impact_strategies_hashtag_timezone">#timezone</string>

    <string name="user_guide_next">Next</string>
    <string name="user_guide_confirm">Conform</string>
    <string name="user_guide_quit">Quit</string>
    <string name="user_guide_go_settings">Go Settings</string>

    <string name="virtual_power_plant">Virtual Power Plant</string>
    <string name="virtual_power_plant_desc">Revenue is generated by selling excess electricity to the grid.</string>
    <string name="virtual_power_plant_tip_1">L-in Port is not yet connected to the Electric Cabinet.</string>
    <string name="virtual_power_plant_tip_2">Two-way meter is not detected.</string>
    <string name="virtual_power_plant_go_it">Got it</string>
    <string name="virtual_power_plant_take_a_look">Take a Look</string>
    <string name="sell_back_to_grid">Sell Back to Grid</string>

    <string name="ps_settings">Settings</string>
    <string name="ps_region">Region</string>
    <string name="ps_battery_overview">Battery Overview</string>
    <string name="ps_accessories">Security Accessories</string>
    <string name="ps_free_trial">Free Trial</string>
    <string name="ps_power_care_plus">Power Care Plus</string>
    <string name="ps_temperature_units">Temperature Units</string>
    <string name="ps_user_guide">User Guide</string>
    <string name="ps_advanced_settings">Advanced Settings</string>
    <string name="ps_advanced_settings_cancel">Cancel</string>
    <string name="ps_advanced_settings_celsius_unit">°C</string>
    <string name="ps_advanced_settings_fahrenheit_unit">°F</string>
    <string name="ps_region_enter_your_country">Enter your country</string>
    <string name="ps_region_recommend">Recommend</string>
    <string name="ps_region_regions_support">Regions Supported</string>
    <string name="ps_region_regions_support_desc">Choose a region for your device to use features like price forecasting or local services.</string>
    <string name="ps_region_regions_not_found">Not Found</string>
    <string name="ps_region_regions_nonsupport">Your selection will not support price forecasting, sure to continue?</string>
    <string name="ps_region_regions_confirm">Confirm</string>
    <string name="ps_region_regions_cancel">Cancel</string>
    <string name="ps_region_regions_electricity">Select Electricity Area</string>
    <string name="ps_battery_overview_total_capacity">Total Capacity</string>
    <string name="ps_battery_overview_cabinet">Cabinet #cabinet_index</string>
    <string name="ps_hashtag_cabinet_index">#cabinet_index</string>
    <string name="ps_battery_overview_cabinet_battery">Cabinet #battery_index</string>
    <string name="ps_hashtag_cabinet_battery_index">#battery_index</string>
    <string name="ps_battery_overview_percent_unit">%</string>
    <string name="ps_battery_overview_unknown">Unknown</string>
    <string name="ps_battery_overview_off">Off</string>
    <string name="ps_battery_overview_unavailable">Unavailable</string>
    <string name="ps_battery_details">Battery Details</string>
    <string name="ps_battery_id">ID</string>
    <string name="ps_battery_seat">Seat</string>
    <string name="ps_battery_charge_switch">Charge Switch</string>
    <string name="ps_battery_discharge_switch">Discharge Switch</string>
    <string name="ps_battery_toggle">Toggle</string>
    <string name="ps_battery_on">On</string>
    <string name="ps_battery_off">Off</string>
    <string name="ps_battery_capacity">Capacity</string>
    <string name="ps_battery_power_remaining">Power Remaining</string>
    <string name="ps_battery_cycle_times">Cycle Times</string>
    <string name="ps_battery_temperature_of_bms">Temperature of BMS</string>
    <string name="ps_battery_temperature_of_cell">Temperature of Cell #cell_index</string>
    <string name="ps_hashtag_cell_index">#cell_index</string>
    <string name="ps_battery_core_a">CoreA</string>
    <string name="ps_battery_core_b">CoreB</string>
    <string name="ps_battery_heating_film">Heating Film</string>
    <string name="ps_advanced_settings_current_network">Current Network</string>
    <string name="ps_advanced_settings_wifi_signal">WiFi Signal</string>
    <string name="ps_advanced_settings_wifi_ip_address">WiFi IP Address</string>
    <string name="ps_advanced_settings_wifi_mac_address">WiFi MAC Address</string>
    <string name="ps_advanced_settings_ethernet_ip_address">Ethernet IP Address</string>
    <string name="ps_advanced_settings_ethernet_mac_address">Ethernet MAC Address</string>
    <string name="ps_advanced_settings_change_network">Change Network</string>
    <string name="ps_advanced_settings_device_id">Device ID</string>
    <string name="ps_advanced_settings_mcu_id">MCU ID</string>
    <string name="ps_advanced_settings_firmware_version">Firmware Version</string>
    <string name="ps_advanced_settings_delete">Delete</string>
    <string name="ps_advanced_settings_delete_alert">After deletion, the #service_name will continue to be consumed. Confirm to delete?</string>
    <string name="ps_advanced_settings_delete_transfer">Transfer</string>
    <string name="ps_advanced_settings_network_diagnosis">Network Diagnosis</string>
    <string name="ps_advanced_settings_bluetooth_mode">Bluetooth Mode</string>
    <string name="ps_customer_feedback_tip">Please write down your feedback:</string>
    <string name="ps_customer_email_tip">Please leave an email address should you request a further contact:</string>
    <string name="ps_customer_send">Send</string>
    <string name="ps_customer_contact_customer_support">Contact Customer Support</string>
    <string name="ps_customer_contact_customer_call_space">Call +</string>
    <string name="ps_heating">Heating</string>
    <string name="ps_wifi_signal_none">None</string>
    <string name="ps_wifi_signal_weak">Weak</string>
    <string name="ps_wifi_signal_medium">Medium</string>
    <string name="ps_wifi_signal_strong">Strong</string>

    <string name="ps_advanced_settings_ethernet_troubleshooting">Ethernet troubleshooting</string>
    <string name="ps_advanced_settings_network_suggestions_1">1. Make sure all cables are securely connected and not damaged. Try replacing the cables to eliminate the possibility of a problem. 2. Check that the lights on the router are blinking properly? Is the power of the router normal? Restart the route can be solved? 3. Try to reboot IoT: short press the IoT button.</string>
    <string name="ps_advanced_settings_wifi_troubleshooting">Wi-Fi troubleshooting</string>
    <string name="ps_advanced_settings_network_suggestions_2_1">1. Make sure the device is connected to 2.4G Wi-Fi, not 5G Wi-Fi. 2. Troubleshoot network node anomalies: Can devices such as cell phones connect? Is the power of the router normal? Restart the route can be solved? 3. Troubleshoot the installation location: You can use your phone to check the Wi-Fi strength near the installation location. 4. Try to reboot IoT: short press the IoT button.</string>
    <string name="ps_advanced_settings_network_suggestions_2_2">1. Troubleshoot network node anomalies: Can devices such as cell phones connect? Is the power of the router normal? Restart the route can be solved? 2. Troubleshoot the installation location: You can use your phone to check the Wi-Fi strength near the installation location. 3. Try to reboot IoT: short press the IoT button.</string>
    <string name="ps_advanced_settings_4g_troubleshooting">4G troubleshooting</string>
    <string name="ps_advanced_settings_network_suggestions_3">1. Is the 4G Traffic Package service active? Only the devices within the service validity period can use 4G network. 2. Is the signal from nearby carriers working? The device supports multiple local carriers, you can use any 4G or 5G phone to test the signal.</string>
    <string name="ps_advanced_settings_none_work">None of the above methods work?</string>
    <string name="ps_advanced_settings_contact_us">Contact Us</string>
    <string name="ps_advanced_settings_network_scan_desc">Hold your phone close to the device that is turned on until you see the Bluetooth icon blinking on the device\'s screen.</string>
    <string name="ps_advanced_settings_network_scan_connect">Connect</string>
    <string name="ps_advanced_settings_network_scan_no_device_found">No device found?</string>
    <string name="ps_advanced_settings_network_scan_no_device_found_tip">Please make sure the device is power on, and put your mobile close to it.</string>
    <string name="ps_advanced_settings_network_scan_no_device_found_quit">Quit</string>
    <string name="ps_advanced_settings_network_scan_no_device_found_search_again">Search Again</string>
    <string name="ps_advanced_settings_network_choose_wifi_network">Choose wireless network</string>
    <string name="ps_advanced_settings_network_choose_wired_network">Choose wired network</string>
    <string name="ps_advanced_settings_network_network_settings">Network Settings</string>
    <string name="ps_settings_network_configure_network_settings">Configure Network Settings</string>
    <string name="ps_settings_network_configure_network_settings_confirm">Confirm</string>
    <string name="ps_settings_network_configure_network_failed">Fail to connect to network</string>
    <string name="ps_settings_network_configure_network_failed_info">The previous wifi and password you selected is</string>
    <string name="ps_settings_network_configure_network_failed_info_ssid">SSID</string>
    <string name="ps_settings_network_configure_network_failed_info_pwd">Password</string>
    <string name="ps_settings_network_configure_network_reselect">Select other networks</string>
    <string name="ps_settings_network_configure_network_quit">Quit</string>
    <string name="ps_accessory_fans_pack">Fans Pack #fan_index</string>
    <string name="ps_accessory_water_sensor">Water Sensor</string>
    <string name="ps_user_guide_solar_help_1">How to maintain battery health?</string>
    <string name="ps_user_guide_solar_help_2">How to sell back excess energy to the grid?</string>
    <string name="ps_user_guide_battery_help_1">How to improve the efficiency of solar generation?</string>
    <string name="ps_user_guide_battery_help_2">How to sell back excess energy to the grid?</string>
    <string name="ps_user_guide_contract_help_1">How to improve the efficiency of solar generation?</string>
    <string name="ps_user_guide_contract_help_2">How to maintain battery health?</string>

    <string name="ps_firm_update">Update</string>
    <string name="ps_firm_ignore">Ignore</string>
    <string name="ps_firm_update_error_hint">The battery is low. Please charge to more than 15% and meet at least one condition: there is a utility input, or the number of battery packs is more than 1.</string>
    <string name="ps_firm_update_unknown_error_hint">Failure due to unknown errors like communication, MCU failures, etc.</string>
    <string name="ps_firm_update_failed">Firmware update failed.</string>
    <string name="ps_firm_info_normal">The current version is up to date.</string>
    <string name="ps_firm_info_can_upgrade">During the update process, the on-grid device will turn off the EV-output and switch the KeepON Load to Grid. The off-grid equipment will turn off all outputs. Are you ready now?</string>
    <string name="ps_firm_info_can_upgrade_ps">During the update process, the on-grid device will switch the KeepON Load to Grid. The off-grid device will turn off all outputs. Are you ready now?</string>
    <string name="ps_firm_info_can_upgrade_vb">During the update process, the off-grid device will turn off all outputs. Are you ready now?</string>
    <string name="ps_firm_note_loading">Note: Please DO NOT turn off the power of device during the update process.</string>
    <string name="ps_firm_tips_loading">The upgrade may take some time, you do not have to wait here. The device will reboot automatically after completed.</string>
    <string name="ps_firm_info_other_upgrading">The device is now updating, and you can exit this page.</string>
    <string name="ps_firm_info_other_upgrading_succeed">Firmware update succeeded.</string>
    <string name="ps_firm_info_other_upgrading_failed">Sorry, there was a problem with the internet, please try again.</string>
    <string name="ps_firm_turn_off_and_start_now">Turn Off and Start Now</string>
    <string name="ps_firm_not_now">Not now</string>
    <string name="ps_firm_done">Done</string>
    <string name="ps_firm_retry">Retry</string>
    <string name="ps_firmware_version">Firmware Version</string>
    <string name="ps_firmware_version_upgrade">#plugin has a new version available, do you want to update now?</string>
    <string name="ps_firmware_version_upgrade_forced">#plugin Must update to new version to continue using.</string>
    <string name="ps_firmware_version_firmware_details">Firmware Details</string>
    <string name="ps_add_device_hint">Please press and hold the power button for a few seconds until the indicator light turns on, then the device is ready to connect.</string>
    <string name="ps_ble_add_device_hint">Put your mobile close to the power-on device. If still not found, it may need to be reset: Press the IoT button and hold it for 10 seconds until you hear 3 BEEPs.</string>
    <string name="ps_ble_change_network_hint">Put your mobile close to the power-on device. If still not found, please try to reboot by pressing the IoT button under the side cover.</string>
    <string name="ps_add_device_hint_3">Please ensure only one un-added device is powered on at a time.</string>

    <string name="ps_device_device_settings">Device Settings</string>
    <string name="ps_device_power_station">Power Station</string>
    <string name="ps_device_power_save">Save</string>
    <string name="ps_device_only_supported">Only 2.4G networks are supported.</string>
    <string name="ps_device_welcome">Welcome</string>
    <string name="ps_device_features">Features</string>
    <string name="ps_device_next">Next</string>
    <string name="ps_device_welcome_network">24/7 4G Backup</string>
    <string name="ps_device_welcome_weather_alert">Extreme Weather Alert</string>
    <string name="ps_device_welcome_energy_optimization">Energy / Load optimization</string>
    <string name="ps_device_welcome_report">Downloadable Impacts Report</string>
    <string name="ps_device_welcome_content_network">Wi-Fi and 4G networks keep your device always online.</string>
    <string name="ps_device_welcome_content_weather_alert">Extreme weather warning, allow you preparing energy storage in advance.</string>
    <string name="ps_device_welcome_content_energy_optimization">AI smart charge and discharge, which can integrate electricity price, weather and historical usage habits to help you optimize energy management and improve economic efficiency.</string>
    <string name="ps_device_welcome_content_report">You can download the statistics in the app\'s Usage, Impacts page, while you will receive a usage analysis report every month.</string>
    <string name="ps_device_retry">Retry</string>
    <string name="ps_welcome_desc">Your 4G traffic package is now on Free Trial</string>
    <string name="ps_welcome_tips">Wi-Fi and 4G networks keep your device always online.</string>

    <string name="ps_device_initialization">Initialization</string>
    <string name="ps_device_initialization_step_1">Time Zone Sync</string>
    <string name="ps_device_initialization_step_2">MCU Connection</string>
    <string name="ps_device_initialization_step_3">Profile: Three-phase Power</string>
    <string name="ps_device_initialization_step_4">Accessories Connection</string>
    <string name="ps_device_initialization_step_5">Accessories Connection</string>
    <string name="ps_device_initialization_step_6">Time Zone Sync</string>
    <string name="ps_device_initialization_step_7">Batteries Connection</string>
    <string name="ps_connecting">Connecting</string>

    <string name="ps_device_change_network_hint">Hold your phone close to the device that is turned on until you see the Bluetooth icon blinking on the device\'s screen.</string>
    <string name="ps_connect">Connect</string>
    <string name="ps_today">Today</string>
    <string name="ps_up_to_value">Up to 520km for an electric car.</string>
    <string name="ps_peak">Peak</string>

    <string name="electricity_day">Day</string>
    <string name="electricity_week">Week</string>
    <string name="electricity_month">Month</string>
    <string name="electricity_year">Year</string>
    <string name="electricity_lifetime">Lifetime</string>
    <string name="electricity_usage">Usage</string>
    <string name="electricity_battery">Battery</string>
    <string name="electricity_net_stats">Net Stats</string>
    <string name="electricity_battery_net_stats">Battery-Net Stats</string>
    <string name="electricity_grid_net_stats">Grid-Net Stats</string>
    <string name="electricity_solar_generation">Solar Generation</string>
    <string name="electricity_power_peak">Power Peak</string>
    <string name="electricity_high">High</string>
    <string name="electricity_low">Low</string>
    <string name="electricity_discharged">Discharged</string>
    <string name="electricity_charged">Charged</string>
    <string name="electricity_imported">Imported</string>
    <string name="electricity_exported">Exported</string>
    <string name="electricity_usage_tips">* Take 1:00 each day to 1:00 the next day as a statistical day.</string>
    <string name="electricity_power_level">Power Level</string>
    <string name="electricity_stats_note">The sum stats are dated in #timezone.</string>
    <string name="electricity_stats_pre_balancing_note">Pre-Balancing: The striped chart reflects the state of dynamic power adjustments before the balancing task to maximize benefits.</string>
    <string name="electricity_solar_energy">Solar Energy</string>
    <string name="electricity_grid_energy">Grid Energy</string>
    <string name="electricity_mppt_a">MPPT 1</string>
    <string name="electricity_mppt_b">MPPT 2</string>
    <string name="electricity_mppt_c">MPPT 3</string>
    <string name="electricity_mppt">MPPT</string>
    <string name="electricity_smart_imported">Smart Imported</string>
    <string name="electricity_emergency_imported">Emergency Imported</string>
    <string name="electricity_today">Today</string>
    <string name="electricity_yesterday">Yesterday</string>

    <string name="ps_system_exception_title_common">System Exception</string>
    <string name="ps_system_exception_content_common">@string/ps_exception_content_common</string>
    <string name="ps_vert_exception_title_common">Inverter Exception</string>
    <string name="ps_exception_content_common">#device_name, #family: The output has been turned off. Please contact after-sales service.</string>

    <string name="ps_vert_battery_exception_title_1">Bit0：电池过压（保护之后，关闭输出，恢复之后，正常输出</string>
    <string name="ps_vert_battery_exception_content_1">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_2">Bit1：电池欠压（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_battery_exception_content_2">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_3">Bit2：电池过温（保护之后，关闭输出，恢复之后，正常输出</string>
    <string name="ps_vert_battery_exception_content_3">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_4">Bit3：电池过流（锁死）</string>
    <string name="ps_vert_battery_exception_content_4">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_5">Bit4: 电池硬件过流（锁死</string>
    <string name="ps_vert_battery_exception_content_5">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_6">Bit5: 升压散热器1过温（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_battery_exception_content_6">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_7">Bit6：升压散热器2过温（保护之后，关闭输出，恢复之后，正常输出)</string>
    <string name="ps_vert_battery_exception_content_7">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_8">Bit7：升压散热器3过温（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_battery_exception_content_8">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_9">Bit8：升压散热器1故障（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_battery_exception_content_9">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_10">Bit9：升压散热器2故障（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_battery_exception_content_10">@string/ps_exception_content_common</string>
    <string name="ps_vert_battery_exception_title_11">Bit10：升压散热器3故障（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_battery_exception_content_11">@string/ps_exception_content_common</string>

    <string name="ps_inverter_exception_title_1">Bit0：逆变输出过压（锁死）</string>
    <string name="ps_inverter_exception_content_1">#device_name, #family: The output has been turned off. Please reduce the power consumption and restart the device. If necessary, contact after-sales service for expansion.</string>
    <string name="ps_inverter_exception_title_2">BIT1：逆变输出欠压（锁死）</string>
    <string name="ps_inverter_exception_content_2">@string/ps_exception_content_common</string>
    <string name="ps_inverter_exception_title_3">BIT2：预留</string>
    <string name="ps_inverter_exception_content_3">@string/ps_exception_content_common</string>
    <string name="ps_inverter_exception_title_4">BIT3：逆变电流直流分量高（锁死）</string>
    <string name="ps_inverter_exception_content_4">@string/ps_exception_content_common</string>
    <string name="ps_inverter_exception_title_5">BIT4：逆变电流过流（锁死）</string>
    <string name="ps_inverter_exception_content_5">@string/ps_exception_content_common</string>
    <string name="ps_inverter_exception_title_6">BIT5：逆变电流硬件过流（锁死）</string>
    <string name="ps_inverter_exception_content_6">@string/ps_exception_content_common </string>
    <string name="ps_inverter_exception_title_7">BIT6：逆变输出短路（锁死）</string>
    <string name="ps_inverter_exception_content_7">#device_name, #family: Try to restart the device. If the fault persists, contact the after-sales service.</string>
    <string name="ps_inverter_exception_title_8">BIT7：负载过载-105%（锁死</string>
    <string name="ps_inverter_exception_content_8">@string/ps_exception_content_common</string>
    <string name="ps_inverter_exception_title_9">BIT8：负载过载-120%（锁死）</string>
    <string name="ps_inverter_exception_content_9">#device_name, #family: Please pay attention to the operating environment of the equipment, and contact after-sales service if necessary.</string>
    <string name="ps_inverter_exception_title_10">BIT9：负载过载-200%（锁死）</string>
    <string name="ps_inverter_exception_content_10">@string/ps_exception_content_common</string>
    <string name="ps_inverter_exception_title_11">BIT10：逆变散热器过温（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_inverter_exception_content_11">@string/ps_exception_content_common</string>
    <string name="ps_inverter_exception_title_12">BIT11：逆变散热器故障（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_inverter_exception_content_12">@string/ps_exception_content_common </string>

    <string name="ps_vert_grid_exception_title_1">Bit0：电网瞬时值过压（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_1">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_2">Bit1：电网有效值1级过压（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_2">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_3">Bit2：电网有效值2级过压（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_3">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_4">Bit3：电网有效值1级欠压（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_4">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_5">Bit4：电网有效值2级欠压（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_5">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_6">Bit5：电网瞬时值欠压（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_6">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_7">Bit6：频率1级过频（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_7">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_8">Bit7：频率2级过频（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_8">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_9">Bit8：频率1级欠频（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_9">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_10">Bit9：频率2级欠频（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_10">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_11">Bit10：电网包络异常（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_11">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_12">Bit11：电网锁相异常（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_grid_exception_content_12">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_13">Bit12：缓冲继电器粘死检测（锁死）</string>
    <string name="ps_vert_grid_exception_content_13">@string/ps_exception_content_common</string>
    <string name="ps_vert_grid_exception_title_14">Bit13：主继电器粘死检测（锁死）</string>
    <string name="ps_vert_grid_exception_content_14">@string/ps_exception_content_common</string>

    <string name="ps_vert_system_exception_title_1">Bit0：绝缘检测异常（锁死）</string>
    <string name="ps_vert_system_exception_content_1">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_2">Bit1：漏电检测异常（锁死）</string>
    <string name="ps_vert_system_exception_content_2">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_3">Bit2：预留</string>
    <string name="ps_vert_system_exception_content_3">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_4">Bit3：母线1级过压（锁死）</string>
    <string name="ps_vert_system_exception_content_4">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_5">Bit4：母线2级过压（锁死）</string>
    <string name="ps_vert_system_exception_content_5">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_6">Bit5：母线1级欠压（锁死）</string>
    <string name="ps_vert_system_exception_content_6">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_7">Bit6：母线2级欠压（锁死）</string>
    <string name="ps_vert_system_exception_content_7">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_8">Bit7：母线故障（锁死）</string>
    <string name="ps_vert_system_exception_content_8">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_9">Bit8：PowerDown（锁死）</string>
    <string name="ps_vert_system_exception_content_9">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_10">Bit9：变压器过温（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_system_exception_content_10">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_11">Bit10：变压器故障（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_system_exception_content_11">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_12">Bit11：通信故障（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_system_exception_content_12">@string/ps_exception_content_common</string>
    <string name="ps_vert_system_exception_title_13">Bit12：风机故障（告警类，故障之后，维持输出）</string>
    <string name="ps_vert_system_exception_content_13">@string/ps_exception_content_common</string>

    <string name="ps_vert_mppt_exception_title_1">Bit0：光伏过压（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_mppt_exception_content_1">@string/ps_exception_content_common</string>
    <string name="ps_vert_mppt_exception_title_2">Bit1：光伏欠压（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_mppt_exception_content_2">@string/ps_exception_content_common</string>
    <string name="ps_vert_mppt_exception_title_3">Bit2：光伏过流（锁死）</string>
    <string name="ps_vert_mppt_exception_content_3">@string/ps_exception_content_common</string>
    <string name="ps_vert_mppt_exception_title_4">Bit3：光伏散热器1过温（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_mppt_exception_content_4">@string/ps_exception_content_common</string>
    <string name="ps_vert_mppt_exception_title_5">Bit4：光伏散热器1故障（保护之后，关闭输出，恢复之后，正常输出）</string>
    <string name="ps_vert_mppt_exception_content_5">@string/ps_exception_content_common</string>

    <string name="ps_vert_present_exception_title_1">Bit0：电池过压</string>
    <string name="ps_vert_present_exception_content_1">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_2">Bit1：电池欠压</string>
    <string name="ps_vert_present_exception_content_2">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_3">Bit2：逆变器过温</string>
    <string name="ps_vert_present_exception_content_3">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_4">Bit3：电网电压异常</string>
    <string name="ps_vert_present_exception_content_4">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_5">Bit4：电网频率异常</string>
    <string name="ps_vert_present_exception_content_5">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_6">Bit5：输出电压异常</string>
    <string name="ps_vert_present_exception_content_6">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_7">Bit6：输出短路</string>
    <string name="ps_vert_present_exception_content_7">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_8">Bit7：输出过载</string>
    <string name="ps_vert_present_exception_content_8">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_9">Bit8：逆变器故障</string>
    <string name="ps_vert_present_exception_content_9">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_10">Bit9：光伏过压</string>
    <string name="ps_vert_present_exception_content_10">@string/ps_exception_content_common</string>
    <string name="ps_vert_present_exception_title_11">Bit10：光伏故障</string>
    <string name="ps_vert_present_exception_content_11">@string/ps_exception_content_common</string>

    <string name="ps_vert_dc_exception_title_1">Bit0：逆变器使能硬件故障（锁死）DC判断到逆变使能没开但是可以通信时认为故障</string>
    <string name="ps_vert_dc_exception_content_1">@string/ps_exception_content_common</string>

    <string name="ps_system_exception_title_1">BIT0:离线过载保护。（超过4000W关闭输出）</string>
    <string name="ps_system_exception_content_1">#device_name, #family: The output has been turned off. Please reduce the power consumption and restart the device. If necessary, contact after-sales service for expansion.</string>
    <string name="ps_system_exception_title_2">BIT1：pack的soc过低（基础模式低于ALARM+2%关闭输出，其它模式按照SOC OFF的设定值关闭输出）</string>
    <string name="ps_system_exception_content_2">@string/ps_system_exception_content_common</string>
    <string name="ps_system_exception_title_3">BIT2:总线电压过低。（告警不关闭输出）</string>
    <string name="ps_system_exception_content_3">@string/ps_system_exception_content_common</string>
    <string name="ps_system_exception_title_4">BIT3:系统测试的温度过高（MCU测试采样的温度高于80度关闭输出）</string>
    <string name="ps_system_exception_content_4">@string/ps_system_exception_content_common</string>
    <string name="ps_system_exception_title_5">BIT4:系统测试的温度过低（MCU测试采样的温度低于-30度关闭输出）</string>
    <string name="ps_system_exception_content_5">@string/ps_system_exception_content_common</string>
    <string name="ps_system_exception_title_6">BIT5:机柜指示数量与实际读取的数量不匹配（容错处理，会告警不关闭输出）</string>
    <string name="ps_system_exception_content_6">@string/ps_system_exception_content_common</string>
    <string name="ps_system_exception_title_7">BIT6:电池的性能恶化告警（检测电池的健康度指标告警处理不关闭）</string>
    <string name="ps_system_exception_content_7">@string/ps_system_exception_content_common</string>

    <string name="well_device_name">#device_name</string>
    <string name="well_family">#family</string>

    <string name="event_list_arm">Arm</string>
    <string name="event_list_disarm">Disarm</string>
    <string name="event_list_homeArm">HomeArm</string>
    <string name="event_list_sos">SOS</string>
    <string name="event_list_device_password_changed">Device password changed</string>
    <string name="event_list_device_low_battery">Device low battery</string>
    <string name="event_list_duress_alarm">Duress Alarm</string>
    <string name="event_list_low_battery">Low Battery</string>
    <string name="event_list_anti_interference_alarm">Anti-interference Alarm</string>
    <string name="event_list_switch_on">Switch-ON</string>
    <string name="event_list_switch_off">Switch-OFF</string>
    <string name="event_list_smart_following">Smart Following</string>
    <string name="event_list_tamper_alarm">Tamper Alarm</string>
    <string name="event_list_no_response">No Response</string>
    <string name="event_list_resume_response">Resume Response</string>
    <string name="event_list_wifi_plug_on">WIFI Plug On</string>
    <string name="event_list_wifi_plug_off">WIFI Plug Off</string>
    <string name="event_list_wifi_blub_on">WIFI Blub On</string>
    <string name="event_list_wifi_blub_off">WIFI Blub Off</string>
    <string name="event_list_life_state_monitoring">Life State Monitoring</string>
    <string name="event_list_tamper_triggered">Tamper triggered</string>
    <string name="event_list_opened">Opened</string>
    <string name="event_list_closed">Closed</string>
    <string name="event_list_daily_memories">Daily Memories</string>
    <string name="error">Error</string>
    <string name="Report">Report</string>
    <string name="Other_feedback">Other feedback</string>
    <string name="Emaldo_AI_Mode">Emaldo AI Mode</string>
    <string name="Emaldo_AI">Emaldo AI</string>
    <string name="Powered_by">Powered by</string>

    <string-array name="electricity_circle_type_array">
        <item>@string/electricity_day</item>
        <item>@string/electricity_week</item>
        <item>@string/electricity_month</item>
        <item>@string/electricity_year</item>
        <item>@string/electricity_lifetime</item>
    </string-array>

    <string-array name="electricity_charge_type_array">
        <item>@string/electricity_net_stats</item>
        <item>@string/electricity_discharged</item>
        <item>@string/electricity_charged</item>
    </string-array>

    <string-array name="electricity_grid_type_array">
        <item>@string/electricity_net_stats</item>
        <item>@string/electricity_imported</item>
        <item>@string/electricity_exported</item>
    </string-array>

    <string-array name="impacts_strategies_type_array">
        <item>@string/electricity_week</item>
        <item>@string/electricity_month</item>
        <item>@string/electricity_year</item>
        <item>@string/electricity_lifetime</item>
    </string-array>

    <string-array name="ps_user_guide_list">
        <item>@string/ps_user_guide_solar</item>
        <item>@string/ps_user_guide_battery_health</item>
        <item>@string/ps_user_guide_grid</item>
    </string-array>

    <string name="ps_user_guide_solar">How to improve the efficiency of solar generation?</string>
    <string name="ps_user_guide_battery_health">How to maintain battery health?</string>
    <string name="ps_user_guide_grid">How to sell back excess energy to the grid?</string>


    <string-array name="ps_user_guide_solar_title">
        <item>How to improve the efficiency of solar generation?</item>
        <item>How to improve the efficiency of solar generation?</item>
        <item>How to improve the efficiency of solar generation?</item>
    </string-array>

    <string-array name="ps_user_guide_battery_title">
        <item>How to maintain battery health?</item>
        <item>How to maintain battery health?</item>
        <item>How to maintain battery health?</item>
    </string-array>

    <string-array name="ps_user_guide_contract_title">
        <item>How to sell back excess energy to the grid?</item>
        <item>How to sell back excess energy to the grid?</item>
        <item>How to sell back excess energy to the grid?</item>
        <item>How to sell back excess energy to the grid?</item>
    </string-array>

    <string-array name="ps_user_guide_solar_step">
        <item>Keep Suitable Installation Distance.</item>
        <item>Choosing the best orientation and tilt angle can increase solar efficiency by more than 20%.</item>
        <item>Remember to clean and maintain.</item>
    </string-array>

    <string-array name="ps_user_guide_battery_step">
        <item>Avoid over-discharge.</item>
        <item>Retain a certain amount of energy when idle for a long time.</item>
        <item>Keep Suitable placement environment.</item>
    </string-array>

    <string-array name="ps_user_guide_contract_step">
        <item>Contracting with VPP suppliers.</item>
        <item>Connect the L-in port to the Electric Cabinet.</item>
        <item>Install the Two-way Meter in the Electric Cabinet.</item>
        <item>Turn on the Virtual Power Plant function in the APP.</item>
    </string-array>

    <string-array name="ps_user_guide_solar_detail">
        <item>Allow adequate front-to-back spacing between the two solar panels, as well as a spacing from the building. You can check separately at different times and seasons to see if the solar panels will be obscured by shadows.</item>
        <item>Longer daylight hours: facing due south if you\'re in the northern hemisphere. Facing due north if you\'re in the southern hemisphere. Vertical sunlight: If you are near the equator, the solar panels are tilted at an angle close to horizontal. If you are near the poles, the tilt angle is close to vertical. Due to geographical factors, it is difficult to calculate directly, and empirical observation also make sense. Professional tools are also available, or consult your local dealer.</item>
        <item>Clean snow and dust from solar panels in a timely manner.</item>
    </string-array>

    <string-array name="ps_user_guide_battery_detail">
        <item>Over-discharge will affect the battery life. In normal use, please try to keep the power not less than 15%.</item>
        <item>When idle for a long time, please charge once every 6 months to keep the battery balance above 60% as much as possible.</item>
        <item>Avoid placing in high and low temperature environment.</item>

    </string-array>

    <string-array name="ps_user_guide_contract_detail">
        <item>The contract with the VPP provider is signed before the return transmission of electricity can generate revenue.</item>
        <item></item>
        <item>Two-way meter is used to count the amount of energy export to the grid.</item>
        <item>The device will start selling electricity after the function is turned on.</item>
    </string-array>

    <string-array name="user_guide_title">
        <item>Improving the efficiency of solar power generation</item>
        <item>Improving the efficiency of solar power generation</item>
        <item>Improving the efficiency of solar power generation</item>
        <item>Battery health care</item>
        <item>Battery health care</item>
        <item>Suitable environment</item>
        <item>How to turn on selling electricity</item>
        <item>How to turn on selling electricity</item>
        <item>How to turn on selling electricity</item>
        <item>How to turn on selling electricity</item>
    </string-array>


    <string-array name="user_guide_step">
        <item>1. Suitable installation distance.</item>
        <item>2. Solar Panel Angle.</item>
        <item>3. Maintenance.</item>
        <item>1. Avoid over-discharge.</item>
        <item>2. Retain a certain amount of power when idle for a long time</item>
        <item>Avoid placing in high and low temperature environment</item>
        <item>Contracting with suppliers.</item>
        <item>Connected the L-in Port to the Electric Cabinet.</item>
        <item>Install the Two-way Meter in the Electric Cabinet. </item>
        <item>Starting selling electricity</item>
    </string-array>

    <string-array name="user_guide_detail">
        <item>Allow adequate front-to-back spacing between the two solar panels, as well as a spacing from the building. You can check separately at different times and seasons to see if the solar panels will be blocked by shadows.</item>
        <item>The highest power generation efficiency is achieved at an angle of 45 degrees to the direct sunlight.</item>
        <item>Clean snow and dust from solar panels in a timely manner.</item>
        <item>Over-discharging will affect the battery life. Please do not let the power fall below 15% during usual use.</item>
        <item>When idle for a long time, please charge once every 6 months to keep the battery balance above 60% as much as possible. </item>
        <item>Try not to expose the cabinet to direct sunlight or temperatures below -10 °C.</item>
        <item>只有签订的电力供应合同中有卖电协议才可开通卖电功能</item>
        <item>文案文案</item>
        <item>文案文案</item>
        <item>在APP中开启卖电功能。Go Setting</item>
    </string-array>

    <string-array name="reserve_price_help_titles">
        <item>Good Price</item>
        <item>Acceptable Price</item>
        <item>Smart Reserve</item>
        <item>Emergency Reserve</item>
    </string-array>

    <string-array name="reserve_price_help_descs">
        <item>This is the price you think is a good deal, measured as a percentage of the day\'s average price.</item>
        <item>The floor price you can accept, measured as a percentage of the day\'s average price.</item>
        <item>For daily supply of system load. Within Smart Reserve, when electricity is in Good Price, the Grid power is allowed for charge to Smart Reserve balance (whether the balance is full or not depends on the PV generation).</item>
        <item>Reserve power in case of Grid power outage or emergency EV charging. Within Emergency Reserve, when electricity is in Acceptable Price, Grid power is allowed for charge to Emergency Reserve balance.</item>
    </string-array>

    <string-array name="ps_add_device_scan_step">
        <item>Please turn on the power of the device. When the above animation appears on the display of the device, it means that Bluetooth is on.</item>
        <item>Please put your mobile close to the panel and wait for a minute.</item>
    </string-array>

    <string-array name="ps_welcome_info_titles">
        <item>@string/ps_device_welcome_network</item>
        <item>@string/ps_device_welcome_weather_alert</item>
        <item>@string/ps_device_welcome_energy_optimization</item>
        <item>@string/ps_device_welcome_report</item>
    </string-array>
    <string-array name="ps_welcome_info_contents">
        <item>@string/ps_device_welcome_content_network</item>
        <item>@string/ps_device_welcome_content_weather_alert</item>
        <item>@string/ps_device_welcome_content_energy_optimization</item>
        <item>@string/ps_device_welcome_content_report</item>
    </string-array>

    <string-array name="ps_inverter_exception_codes">
        <item>逆变器_Hardware</item>
        <item>逆变器_Hardware</item>
        <item>逆变器_Hardware</item>
        <item>逆变器_Hardware</item>
        <item>逆变器_Hardware</item>
        <item>逆变器_Hardware</item>
    </string-array>

    <string-array name="ps_battery_exception_codes">
        <item>电池包_ElectrodeA_Release_HighTemp60</item>
        <item>电池包_ElectrodeA_Release_HighTemp50</item>
        <item>电池包_ElectrodeA_Release_LowTemp5</item>
        <item>电池包_ElectrodeA_Release_LowTemp10</item>
        <item>电池包_ElectrodeA_Charge_HighTemp50</item>
        <item>电池包_ElectrodeA_Charge_LowTemp0</item>
        <item>电池包_ElectrodeB_Release_HighTemp60</item>
        <item>电池包_ElectrodeB_Release_HighTemp50</item>
        <item>电池包_ElectrodeB_Release_lowTemp5</item>
        <item>电池包_ElectrodeB_Release_LowTemp10</item>
        <item>电池包_ElectrodeB_Charge_HighTemp50</item>
        <item>电池包_ElectrodeB_Charge_LowTemp0</item>
        <item>电池包_BMS_Release_HighTemp100</item>
        <item>电池包_BMS_Release_HighTemp95</item>
        <item>电池包_OverChargeAlert</item>
        <item>电池包_OverChargeProtect</item>
        <item>电池包_OverReleaseAlert</item>
        <item>电池包_OverReleaseProtect</item>
        <item>电池包_ShortCircuit</item>
    </string-array>

    <string-array name="ps_mppt_exception_codes">
        <item>MPPT_HardWare</item>
        <item>MPPT_Communicate</item>
    </string-array>

    <string-array name="ps_ev_exception_codes">
        <item>EV_LeakageCurrent</item>
        <item>EV_LeakageCurrentChecking</item>
        <item>EV_OverVoltage</item>
        <item>EV_lackVoltage</item>
        <item>EV_OverCurrent</item>
        <item>EV_OverTemperature</item>
        <item>EV_EarthWire</item>
        <item>EV_CPLevel</item>
        <item>EV_Relay</item>
        <item>EV_AssistCPU</item>
        <item>EV_System5V</item>
    </string-array>

    <string-array name="ps_vert_battery_exception_titles">
        <item>@string/bmt_exception_key_1000</item>
        <item>@string/bmt_exception_key_1001</item>
        <item>@string/bmt_exception_key_1002</item>
        <item>@string/bmt_exception_key_1003</item>
        <item>@string/bmt_exception_key_1004</item>
        <item>@string/bmt_exception_key_1005</item>
        <item>@string/bmt_exception_key_1006</item>
        <item>@string/bmt_exception_key_1007</item>
        <item>@string/bmt_exception_key_1008</item>
        <item>@string/bmt_exception_key_1009</item>
        <item>@string/bmt_exception_key_1010</item>
    </string-array>

    <string-array name="ps_vert_battery_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_inverter_exception_titles">
        <item>@string/bmt_exception_key_1100</item>
        <item>@string/bmt_exception_key_1101</item>
        <item>@string/bmt_exception_key_1102</item>
        <item>@string/bmt_exception_key_1103</item>
        <item>@string/bmt_exception_key_1104</item>
        <item>@string/bmt_exception_key_1105</item>
        <item>@string/bmt_exception_key_1106</item>
        <item>@string/bmt_exception_key_1107</item>
        <item>@string/bmt_exception_key_1108</item>
        <item>@string/bmt_exception_key_1109</item>
        <item>@string/bmt_exception_key_1110</item>
        <item>@string/bmt_exception_key_1111</item>
    </string-array>

    <string-array name="ps_inverter_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item></item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_vert_grid_exception_titles">
        <item>@string/bmt_exception_key_1200</item>
        <item>@string/bmt_exception_key_1201</item>
        <item>@string/bmt_exception_key_1202</item>
        <item>@string/bmt_exception_key_1203</item>
        <item>@string/bmt_exception_key_1204</item>
        <item>@string/bmt_exception_key_1205</item>
        <item>@string/bmt_exception_key_1206</item>
        <item>@string/bmt_exception_key_1207</item>
        <item>@string/bmt_exception_key_1208</item>
        <item>@string/bmt_exception_key_1209</item>
        <item>@string/bmt_exception_key_1210</item>
        <item>@string/bmt_exception_key_1211</item>
        <item>@string/bmt_exception_key_1212</item>
        <item>@string/bmt_exception_key_1213</item>
    </string-array>

    <string-array name="ps_vert_grid_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_vert_system_exception_titles">
        <item>@string/bmt_exception_key_1300</item>
        <item>@string/bmt_exception_key_1301</item>
        <item>@string/bmt_exception_key_1302</item>
        <item>@string/bmt_exception_key_1303</item>
        <item>@string/bmt_exception_key_1304</item>
        <item>@string/bmt_exception_key_1305</item>
        <item>@string/bmt_exception_key_1306</item>
        <item>@string/bmt_exception_key_1307</item>
        <item>@string/bmt_exception_key_1308</item>
        <item>@string/bmt_exception_key_1309</item>
        <item>@string/bmt_exception_key_1310</item>
        <item>@string/bmt_exception_key_1311</item>
        <item>@string/bmt_exception_key_1312</item>
    </string-array>

    <string-array name="ps_vert_system_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item></item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_vert_mppt_exception_titles">
        <item>@string/bmt_exception_key_1400</item>
        <item>@string/bmt_exception_key_1401</item>
        <item>@string/bmt_exception_key_1402</item>
        <item>@string/bmt_exception_key_1403</item>
        <item>@string/bmt_exception_key_1404</item>
    </string-array>

    <string-array name="ps_vert_mppt_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_vert_present_exception_titles">
        <item>@string/bmt_exception_key_1000</item>
        <item>@string/bmt_exception_key_1001</item>
        <item>@string/ps_vert_present_exception_title_3</item>
        <item>@string/ps_vert_present_exception_title_4</item>
        <item>@string/ps_vert_present_exception_title_5</item>
        <item>@string/ps_vert_present_exception_title_6</item>
        <item>@string/ps_vert_present_exception_title_7</item>
        <item>@string/ps_vert_present_exception_title_8</item>
        <item>@string/ps_vert_present_exception_title_9</item>
        <item>@string/ps_vert_present_exception_title_10</item>
        <item>@string/ps_vert_present_exception_title_11</item>
    </string-array>

    <string-array name="ps_vert_present_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/ps_vert_present_exception_content_3</item>
        <item>@string/ps_vert_present_exception_content_4</item>
        <item>@string/ps_vert_present_exception_content_5</item>
        <item>@string/ps_vert_present_exception_content_6</item>
        <item>@string/ps_vert_present_exception_content_7</item>
        <item>@string/ps_vert_present_exception_content_8</item>
        <item>@string/ps_vert_present_exception_content_9</item>
        <item>@string/ps_vert_present_exception_content_10</item>
        <item>@string/ps_vert_present_exception_content_11</item>
    </string-array>

    <string-array name="ps_vert_dc_exception_titles">
        <item>@string/bmt_exception_key_1600</item>
        <item>@string/bmt_exception_key_1601</item>
    </string-array>

    <string-array name="ps_vert_dc_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_ev_exception_titles">
        <item>@string/bmt_exception_key_2000</item>
        <item>@string/bmt_exception_key_2001</item>
        <item>@string/bmt_exception_key_2002</item>
        <item>@string/bmt_exception_key_2003</item>
        <item>@string/bmt_exception_key_2004</item>
        <item>@string/bmt_exception_key_2005</item>
        <item>@string/bmt_exception_key_2006</item>
    </string-array>

    <string-array name="ps_ev_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_mppt_exception_titles">
        <item>@string/bmt_exception_key_3000</item>
        <item>@string/bmt_exception_key_3001</item>
        <item>@string/bmt_exception_key_3002</item>
        <item>@string/bmt_exception_key_3003</item>
        <item>@string/bmt_exception_key_3004</item>
    </string-array>

    <string-array name="ps_mppt_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_cabinet_exception_titles">
        <item>@string/bmt_exception_key_4000</item>
        <item>@string/bmt_exception_key_4001</item>
        <item>@string/bmt_exception_key_4002</item>
        <item>@string/bmt_exception_key_4003</item>
    </string-array>

    <string-array name="ps_cabinet_exception_contents">
        <item>@string/bmt_exception_content_4</item>
        <item></item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_battery_exception_titles">
        <item>@string/bmt_exception_key_5002</item>
        <item>@string/bmt_exception_key_5003</item>
        <item>@string/bmt_exception_key_5004</item>
        <item>@string/bmt_exception_key_5005</item>
        <item>@string/bmt_exception_key_5006</item>
        <item>@string/bmt_exception_key_5007</item>
        <item>@string/bmt_exception_key_5008</item>
        <item>@string/bmt_exception_key_5009</item>
        <item>@string/bmt_exception_key_5010</item>
        <item>@string/bmt_exception_key_5011</item>
        <item>@string/bmt_exception_key_5012</item>
        <item>@string/bmt_exception_key_5013</item>
        <item>@string/bmt_exception_key_5014</item>
        <item>@string/bmt_exception_key_5015</item>
    </string-array>

    <string-array name="ps_battery_exception_contents">
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_1</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_system_exception_tittles">
        <item>@string/bmt_exception_key_6000</item>
        <item>@string/bmt_exception_key_6001</item>
        <item>@string/bmt_exception_key_6002</item>
        <item>@string/bmt_exception_key_6003</item>
        <item>@string/bmt_exception_key_6004</item>
        <item>@string/bmt_exception_key_6005</item>
        <item>@string/bmt_exception_key_6006</item>
        <item>@string/bmt_exception_key_6007</item>
        <item>@string/bmt_exception_key_6008</item>
        <item>@string/bmt_exception_key_6009</item>
        <item>@string/bmt_exception_key_6010</item>
    </string-array>

    <string-array name="ps_system_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_1</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_communication_exception_titles">
        <item>@string/bmt_exception_key_7000</item>
        <item>@string/bmt_exception_key_7001</item>
    </string-array>

    <string-array name="ps_communication_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps_charge_mode">
        <item>@string/ps_is_price_tracking_mode</item>
        <item>@string/ps_is_scheduled_mode</item>
        <item>@string/Emaldo_AI_Mode</item>
        <item>@string/impact_strategies_reserve_mode_item_name_4</item>
        <item>@string/emergency_charge_pending</item>
    </string-array>

    <!--    与ps_today_revenue_value_key_array关联,相关顺序需保持一致-->
    <string-array name="ps_today_revenue_note_array">
        <item>Up to #impacts_ebike km for an electric bicycle.</item>
        <item>Up to #impacts_vehicle km for an electric car.</item>
        <item>Up to #impacts_lamp hours for an energy-saving lamp.</item>
        <item>Up to #impacts_refrigerator hours for a small refrigerator.</item>
        <item>Up to #impacts_ac hours for a 1P air-conditioner</item>
        <item>Up to #impacts_fan hours for an ordinary electric fan.</item>
        <item>Up to #impacts_tv hours for a TV playing.</item>
        <item>Up to #impacts_beer bottles of beer producing.</item>
    </string-array>
    <!--    与ps_today_revenue_note_array关联,相关顺序需保持一致-->
    <string-array name="ps_today_revenue_value_key_array">
        <item>#impacts_ebike</item>
        <item>#impacts_vehicle</item>
        <item>#impacts_lamp</item>
        <item>#impacts_refrigerator</item>
        <item>#impacts_ac</item>
        <item>#impacts_fan</item>
        <item>#impacts_tv</item>
        <item>#impacts_beer</item>
    </string-array>

    <string name="ps_ev_charge">EV Charge</string>
    <string name="ps_ev_smart_charge">Smart Charge</string>
    <string name="ps_ev_instant_charge">Instant Charge</string>
    <string name="ps_ev_exit_instant_charge">Exit Instant Charge</string>
    <string name="ps_ev_off">OFF</string>
    <string name="ps_ev_charging">Charging</string>
    <string name="ps_ev_waiting_for_charge">Waiting for Charge</string>
    <string name="ps_ev_unauthorized">Unauthorized</string>
    <string name="ps_ev_charged">Charged</string>
    <string name="ps_ev_desc">To ensure KeepON Load supply, the system will dynamically adjust the EV charging power.</string>
    <string name="ps_ev_applied">Applied</string>
    <string name="ps_ev_go_settings">Go Settings</string>
    <string name="ps_ev_smart_charge_title_1">Lower Utility Rate</string>
    <string name="ps_ev_smart_charge_content_1">Vehicle charging is allowed only during hours when utility prices fall beyond #c2.</string>
    <string name="ps_ev_smart_charge_title_2">Solar-only</string>
    <string name="ps_ev_smart_charge_content_2">EV charge power will be limited to solar power.</string>
    <string name="ps_ev_smart_charge_title_3">Scheduled Charge</string>
    <string name="ps_ev_smart_charge_content_3">EV charging is allowed only for a preset time.</string>
    <string name="ps_ev_instant_charge_title_1">Until Fully Charged</string>
    <string name="ps_ev_instant_charge_content_1">After being fully charged, the EV car will automatically disconnect.</string>
    <string name="ps_ev_instant_charge_title_2">Fixed Charge</string>
    <string name="ps_ev_instant_charge_content_2">Set the amount of this charge to #charge_amount.</string>
    <string name="hashtag_charge_amount">#charge_amount</string>
    <string name="ps_ev_weekdays">Weekdays</string>
    <string name="ps_ev_weekends">Weekends</string>
    <string name="ps_ev_save">Save</string>
    <string name="ps_ev_mode_preference">Mode Preference</string>

    <string name="ps_is_learn_more">Learn More</string>
    <string name="ps_is_price_tracking_mode">Price Tracking Mode</string>
    <string name="ps_is_price_tracking_mode_subtitle">Automatically tracks prices and combines solar and battery conditions to get the most profitable charging and discharging strategy.</string>
    <string name="ps_is_scheduled_mode">Scheduled Mode</string>
    <string name="ps_is_scheduled_mode_subtitle">Execute the charging and discharging strategy according to the schedule.</string>
    <string name="ps_is_high">High</string>
    <string name="ps_is_ave_price">Ave-price</string>
    <string name="ps_is_low">Low</string>
    <string name="ps_is_battery_discharge">Battery Discharge</string>
    <string name="ps_is_charge_with_grid_power">Charge with grid power</string>
    <string name="ps_is_tracking_mode_desc">The system regularly updates the optimal prices and electricity range settings based on past data analysis. (Included in Power Care Plus service)</string>
    <string name="ps_is_scheduled_mode_same_with_weekdays">Same with weekdays</string>
    <string name="ps_is_battery_protection">Battery Protection</string>
    <string name="ps_is_battery_protection_suggestion">When the battery power is lower than 2%, the battery will stop discharging to protect the battery performance. If there is a grid connection, the battery will be charged first.</string>
    <string name="ps_is_low_power_alert_suggestion">When the power is lower than 12%, the system will send a low power alert. This part of electricity is still belong to the Emergency Reserve.</string>
    <string name="ps_is_emergency_reserve_suggestion">This part of power is used as emergency power supply in case of grid outage. During daily use, when the price is below #c3, charge with grid power is allowed. Otherwise, the load will be supplied by the grid, and solar energy will charge the battery first.</string>
    <string name="ps_is_smart_reserve_suggestion">In this power range, when the price is below #c2, charge with grid power is allowed; When the price is higher than #s2, battery discharge is allowed. In the middle area, the load is prioritized to be supplied by the grid, and solar energy is prioritized to charge the battery.</string>
    <string name="ai_mode_edit_seekBar_4">In this power range, when the price is below #c2, charge with grid power is allowed. When the price is higher than #s2, battery discharge is allowed. In the middle area, the load is prioritized to be supplied by the grid, and solar energy is prioritized to charge the battery.</string>
    <string name="ps_is_plenty_energy_suggestion">In this power range, when the price is below #c1, charge with grid power is allowed; When the price is higher than #s1, battery discharge is allowed. In the middle area, the load is prioritized to be supplied by the grid, and solar energy is prioritized to charge the battery.</string>
    <string name="ai_mode_edit_seekBar_5">In this power range, when the price is below #c1, charge with grid power is allowed. When the price is higher than #s1, battery discharge is allowed. In the middle area, the load is prioritized to be supplied by the grid, and solar energy is prioritized to charge the battery.</string>
    <string name="ps_is_when_battery">When Battery</string>
    <string name="ps_is_less_than_sign">&lt;</string>
    <string name="ps_is_greater_than_sign">&gt;</string>
    <string name="ps_is_scheduled_mode_value_1_desc">After reaching the preset value, it enters No Charge Nor Discharge state. Load prioritizes grid power, solar energy prioritizes battery charging.</string>
    <string name="ps_is_scheduled_mode_value_2_desc"> Load prioritizes grid power, solar energy prioritizes battery charging.</string>
    <string name="ps_is_scheduled_mode_value_3_desc">After reaching the preset value, it enters No Charge Nor Discharge state. Load prioritizes grid power, solar energy prioritizes battery charging.</string>
    <string name="ps_is_no_charge_nor_discharge">No Charge Nor Discharge</string>
    <string name="hashtag_c1">#c1</string>
    <string name="hashtag_c2">#c2</string>
    <string name="hashtag_c3">#c3</string>
    <string name="hashtag_s1">#s1</string>
    <string name="hashtag_s2">#s2</string>

    <string-array name="ps_is_mode_suggestion_array">
        <item>@string/ps_is_battery_protection_suggestion</item>
        <item>@string/ps_is_low_power_alert_suggestion</item>
        <item>@string/ps_is_emergency_reserve_suggestion</item>
        <item>@string/ai_mode_edit_seekBar_4</item>
        <item>@string/ai_mode_edit_seekBar_5</item>
    </string-array>

    <string-array name="ps_scheduled_mode_suggestion_array">
        <item>@string/ps_is_battery_protection_suggestion</item>
        <item>@string/ps_is_low_power_alert_suggestion</item>
        <item>This power is reserved for grid outage. The battery can be charged up to this range with grid power and continue charging with solar.</item>
        <item>The battery can be charged up to this range using grid power and continue to charge using solar power, or it can be discharged down to this range.</item>
        <item>The battery can be fully charged with grid power, or discharge down to this range.</item>
    </string-array>
    <!-- power station end -->

    <string name="energy">Energy</string>
    <string name="power_station">Power Station</string>
    <string name="alarm_system">Alarm System</string>
    <string name="reserve_mode_skip_hint">The device operates in Smart Charge mode first. You can set this up later in the Impacts &amp; Strategies page.</string>
    <string name="bmt_guide_tip_first_used">First time user? Here is a guide for you to maximize the use of your Power Station.</string>

    <string name="iap_power_care_plus">Power Care Plus</string>
    <string name="iap_power_care_desc">Full health monitor for Power Station and energy optimization for home.</string>
    <string name="iap_service_type_desc">#service_name is on</string>
    <string name="iap_4g_traffic_package">4G Traffic Package</string>

    <string name="iot">IOT</string>
    <string name="hardware">Hardware</string>
    <string name="mcu">MCU</string>
    <string name="inverter">Inverter</string>
    <string name="inverter_hardware_hs">Inverter (hardware_AC_DC)</string>
    <string name="inverter_hardware_bst">(mainchip_minorchip_lib_AC)</string>
    <string name="cabinet">Cabinet</string>
    <string name="cabinet_hardware">(hardware_firmware)</string>
    <string name="battery_packs">Battery Packs</string>
    <string name="bms">BMS</string>
    <string name="bms_hardware">(hardware_firmware)</string>
    <string name="hashtag_battery_index">#battery_index</string>
    <string name="keypad_password">Keypad Password</string>
    <string name="assign_keypad_password">Assign Keypad Password</string>
    <string name="reset_password">Reset Password</string>
    <string name="keypad_password_enable_hint">After being enabled, a dedicated keypad password will be generated for arming/disarming.Are you sure to continue?</string>
    <string name="keypad_password_disable_hint">After closing, the current password will become invalid. Are you sure to continue?</string>
    <string name="keypad_password_reset_hint">After resetting, a new keypad password will be regenerated for arming/disarming. Are you sure to continue?</string>
    <string name="keypad_reset_hint">Your keypad password has been reset. Please have a check.</string>
    <string name="keypad_deleted_hint">Your keypad password has been deleted. Please contact the administrator if necessary.</string>
    <string name="keypad_obtained_hint">You have obtained the keypad password. Please have a check.</string>
    <string name="family_service_activated">Family Service Activated</string>
    <string name="bind_inverter_id">Barcode is correct, submit</string>
    <string name="inverter_bind_conflict">The bindings are conflicting.</string>
    <string name="inverter_mcu_id_not_match">Inverter ID and MCU ID do not match.</string>

    <!--    2.1.2  -->
    <!--    ipc    -->
    <string name="ipc_scheduled_recording">Scheduled Recording</string>
    <string name="ipc_set_schedule">Set Schedule</string>
    <string name="ipc_daily_memories">Daily Memories</string>
    <string name="ipc_daily_memories_hint">Turn on 24-hour loop time-lapse recording and automatically generate daily memory clips.</string>
    <string name="ipc_start_at">Start at</string>
    <string name="ipc_daily_memories_expired_hint">Hey, this feature is part of the Video Cloud Service\'s value offerings and can be turned on immediately with a renewal.</string>
    <string name="ipc_daily_memories_time_hint">After modification, the shot just took will be discarded, confirm to continue?</string>
    <string name="scheduled_recording">Scheduled Recording</string>
    <string name="free_trial">Free Trial</string>
    <string name="ipc_setting_sd_card_error">The SD card of the camera is abnormal, please check it before turning on this function.</string>
    <string name="ipc_setting_low_power">Camera power is low, please charge it before turning on this function.</string>

    <!--    2.1.3  -->
    <string name="edit">Edit</string>
    <string name="download">Download</string>
    <string name="daily_memories_play_store_tip">The short film will be saved in the cloud for 30 days.</string>
    <string name="memory_of_date">Memory of #date</string>

    <string name="timer_picker_today">Today</string>
    <string name="timer_picker_tomorrow">Tomorrow</string>
    <string name="timer_picker_yesterday">Yesterday</string>
    <string name="battery_id">Battery ID</string>
    <string name="ps_battery_overview_heating_working">Heating Film Working</string>
    <string name="ps_battery_overview_heating_absent">Heating Film Absent</string>

    <string name="data_mode">Data Mode</string>
    <string name="true_data">True Data</string>
    <string name="fake_data">Fake Data</string>


    <!-- 2.1.4 -->
    <string name="plugin_adjustable_motion_sensor">Adjustable Motion Sensor</string>
    <string name="modify_plugs_pir_sensitivity_status">PIR Sensitivity</string>

    <string name="dialog_sensitivity_context">PIR trigger will be bypassed during sensitivity setting (each operation extends the bypass time by 5 minutes), are you sure to set it now?</string>
    <string name="dialog_sensitivity_set_up">Got it, keep setting up</string>

    <string name="pir_sensitivity_setting_guide_tip">Press and hold the trigger button for 3 seconds, the status light will flash quickly and then enter the configuration mode.</string>
    <string name="pir_sensitivity_setting_next_step">The device is ready, next step</string>
    <string name="pir_sensitivity_setting_note">Note</string>
    <string name="pir_sensitivity_setting_context1">With this sensitivity, the detection range is approximately #pir_distance.</string>
    <string name="pir_sensitivity_setting_context2">The anti-interference capability of the sensor decreases as the detection distance increases.</string>
    <string name="pir_sensitivity_setting_context3">Please test the effect by standing at a suitable distance after saving. The indicator of sensor flashes once, which means the target is detected.</string>

    <string name="pir_sensitivity_low">Low</string>
    <string name="pir_sensitivity_middle">Medium</string>
    <string name="pir_sensitivity_high">High</string>
    <!-- 2.1.4 -->

    <!-- 2.3.0 -->
    <string name="on_grid_configuration">On-grid Configuration</string>
    <string name="power_soft_start_rate">Power Soft-start Rate</string>
    <string name="app_fast_power_down">App Fast Power Down</string>
    <string name="app_overfrequency_load_shedding">Overfrequency Load Shedding</string>
    <string name="overfrequency_load_shedding_slope_setting">Overfrequency Load Shedding Slope Setting</string>
    <string name="underfrequency_loading">Underfrequency Loading</string>
    <string name="underfrequency_loading_slope">Underfrequency loading slope</string>
    <string name="qu_mode">QU Mode</string>
    <string name="cos_phi_p_mode">Cos Phi(P) Mode</string>
    <string name="anti_islanding_switch">Anti-islanding Switch</string>
    <string name="active_high_and_low_wear_enables_computing">Active High and Low Wear Enables (computing)</string>
    <string name="power_factor">Power Factor</string>
    <string name="boot_failure_reconnect_time">Boot Failure Reconnect Time</string>
    <string name="boot_failure_reconnect_slope">Boot Failure Reconnection Slope</string>
    <string name="level_1_grid_voltage_overvoltage">Level 1 Grid Voltage Overvoltage</string>
    <string name="level_2_grid_voltage_overvoltage">Level 2 Grid Voltage Overvoltage</string>
    <string name="level_1_grid_voltage_undervoltage">Level 1 Grid Voltage Undervoltage</string>
    <string name="level_2_grid_voltage_undervoltage">Level 2 Grid Voltage Undervoltage</string>
    <string name="level_1_grid_frequency_overfrequency">Level 1 Grid Frequency Overfrequency</string>
    <string name="level_2_grid_frequency_overfrequency">Level 2 Grid Frequency Overfrequency</string>
    <string name="class_1_grid_frequency_underfrequency">Class 1 Grid Frequency Underfrequency</string>
    <string name="class_2_grid_frequency_underfrequency">Class 2 Grid Frequency Underfrequency</string>
    <string name="resume_to_default_setting">Resume to Default Setting</string>
    <string name="enter_your_id_password">Enter your ID password</string>


    <string name="level_1_grid_overvoltage_protection_time">Level 1 Grid Overvoltage Protection Time</string>
    <string name="level_2_grid_overvoltage_protection_time">Level 2 Grid Overvoltage Protection Time</string>
    <string name="level_1_grid_undervoltage_protection_time">Level 1 Grid Undervoltage Protection Time</string>
    <string name="level_2_grid_undervoltage_protection_time">Level 2 Grid Undervoltage Protection Time</string>
    <string name="over_frequency_protection_time">Over-frequency Protection Time</string>
    <string name="overload_protection_time">Overload Protection Time</string>
    <string name="power_setting">Power Setting</string>
    <string name="underfrequency_on_boot_and_reconnect">Underfrequency on Boot and Reconnect</string>
    <string name="undervoltage_on_boot_and_reconnect">Undervoltage on Boot and Reconnect</string>
    <string name="overfrequency_on_boot_and_reconnect">Overfrequency on Boot and Reconnect</string>
    <string name="overvoltage_on_boot_and_reconnect">Overvoltage on Boot and Reconnect</string>

    <string name="overfrequency_protection_time">Overfrequency Protection Time</string>
    <!-- 2.3.0 -->

    <!-- 2.4.0 -->
    <string name="try_again">Try again</string>
    <string name="confirm_to_resume">Confirm to resume default settings? This operation only resets the user settings and does not clear device data.</string>
    <!-- 2.4.0 -->

    <string name="prime_service_grid_reward">Grid Rewards</string>
    <string name="prime_service_grid_reward_detail">Earn money on selling excess power back to the grid.</string>
    <string name="prime_service_extended_warranty">Extended Warranty up to 20 Years</string>
    <string name="prime_service_extended_warranty_detail">An extended warranty of up to 20 years provides peace of mind and added protection for your valuable investment.</string>
    <string name="prime_service_coming_soon">Coming soon</string>
    <string name="prime_service_grid_reward_state_checking">Checking the Device...</string>
    <string name="prime_service_grid_reward_state_free">Free Enrollment, Easy Earnings</string>
    <string name="prime_service_grid_reward_state_sign_more">Sign more Devices for Additional Income</string>
    <string name="prime_service_grid_reward_state_service_effec">Service in Effec</string>

    <string name="prime_service_grid_reward_permission_tip">Please check if the relevant permission is allowed.</string>

    <string name="prime_service_parallel_installation_update">Parallel Installation Update</string>
    <string name="prime_service_parallel_installation_update_content">Your single-device authorization for Grid Reward is now updated to a home authorization. If this does not suit you, cancel or assign devices to different households.</string>


    <string name="grid_title">grid rewards titele</string>
    <string name="grid_join">I Want to Join In</string>
    <string name="grid_content">grid rewards substance1</string>
    <string name="grid_title2">grid rewards subheading1</string>
    <string name="grid_content2_1">grid rewards substance2</string>
    <string name="grid_title3">grid rewards subheading2</string>
    <string name="grid_content3_1">grid rewards substance3</string>
    <string name="grid_title4">grid rewards subheading3</string>
    <string name="grid_content4_1">grid rewards substance4</string>
    <string name="grid_content4_2">grid rewards substance5</string>

    <string name="grid_register_as">Register as</string>
    <string name="grid_sign_as_company">a Company/Organization</string>
    <string name="grid_sign_as_person">an Individual</string>
    <string name="ID">ID</string>
    <string name="effective_time">Effective Time</string>

    <string name="contract_signing">contract signing title</string>
    <string name="contract_title">contract signing subheading</string>
    <string name="contract_preview">Power of Attorney</string>
    <string name="Preview">Preview</string>
    <string name="contract_content1">contract signing substance1</string>
    <string name="contract_content2">contract signing substance2</string>
    <string name="contract_content3">contract signing substance3</string>
    <string name="contract_content4">contract signing substance4</string>
    <string name="contract_content5">contract signing substance5</string>
    <string name="contract_content6">contract signing substance6</string>
    <string-array name="contract_content">
        <item>@string/contract_content1</item>
        <item>@string/contract_content2</item>
        <item>@string/contract_content3</item>
        <item>@string/contract_content4</item>
        <item>@string/contract_content5</item>
        <item>@string/contract_content6</item>
    </string-array>
    <string name="agree_sign">Agree and Sign</string>
    <string name="re_sign">Re-sign</string>
    <string name="agree_confirm">Agree and Confirm</string>

    <string name="signature_area">Signature Area</string>

    <string name="terminate_title">Terminate statement</string>
    <string name="terminate_family_name">Family Name</string>
    <string name="terminate_family_id">Family ID</string>
    <string name="terminate_context">I, #first, residing at #second_info, voluntarily terminate the authorization granted to #third_info, incorporated in #fourth_info, under business registration number #fifth_info, and registered office at #sixth_info, for the following devices:</string>
    <string name="terminate_preview">Terminate statement</string>
    <string name="terminate_tip">Terminating the contract means you will lose your monthly income. Are you sure?</string>
    <string name="consider_later">Consider Later</string>
    <string name="terminate_contract">Terminate Contract</string>
    <string name="terminate_del_tip">Failed, the device is enabled in Grid Rewards. Please terminate the contract and try again.</string>

    <string name="participation_hours">Participation Hours</string>
    <string name="related_information">Related Information</string>
    <string name="authorization_records">Authorization Records</string>
    <string name="emaldo_terms">Emaldo Terms and Conditions</string>
    <string name="power_of_attorney">power of attorney</string>
    <string name="terminate_statement">terminate statement</string>
    <string name="family_authorization_statement">Family Authorization Statement</string>
    <string name="family_authorization_statement_content">#family_name (all current &amp; future devices) have been enrolled in Grid Rewards since #time.</string>


    <string name="grid_done_title">grid rewards done titele</string>
    <string name="grid_done_contract_title1">grid rewards done subheading1</string>
    <string name="grid_done_contract_content1">grid rewards done substance1</string>
    <string name="grid_done_contract_title2">grid rewards done subheading2</string>
    <string name="grid_done_contract_content2">grid rewards done substance2</string>

    <string name="service_details">Service Details</string>
    <string name="terminate">Terminate</string>
    <string name="terminate_suc">Terminate Successful.</string>
    <string name="device">Device</string>
    <string name="power_of_attorney_company">Power of Attorney Company</string>
    <string name="device_state">Device State</string>

    <string name="id">ID</string>
    <string name="version">Version</string>
    <string name="ev">EV</string>
    <string name="battery">Battery</string>
    <string name="sidecar">Sidecar Version</string>
    <string name="new_identifier">(New)</string>
    <string name="old_identifier">(Old)</string>

    <string name="power">Power</string>
    <string name="total">Total</string>
    <string name="status">Status</string>
    <string name="realtime_power">Realtime Power</string>
    <string name="battery_view">Battery View</string>
    <string name="instant_ev_charge">Instant EV Charge</string>
    <string name="emergency_charge">Emergency Charge</string>
    <string name="price_today">Price Today</string>
    <string name="price">Price</string>
    <string name="Price_Level">Price Level</string>
    <string name="relative_price">Relative Price</string>
    <string name="market_price">Market Price</string>
    <string name="tariff">Tariff</string>
    <string name="average">Average</string>
    <string name="hashtag_charge_quantity">#charge_quantity</string>

    <string name="home_guide_content_1">Here is the Real-Time Current View of the device. Tap for more details.</string>
    <string name="home_guide_content_2">Tap to manage families or join a shared family.</string>
    <string name="home_guide_content_3">Review history events for all devices and families.</string>
    <string name="home_guide_content_4">Customize favorite short-cut with ease.</string>

    <string name="device_guide_content_1">Enter the EV Charge to customize at your will.</string>
    <string name="device_guide_content_2">Swipe to view the Battery Status.</string>
    <string name="device_guide_content_3">Here are the basic chart. Tap for more stats.</string>
    <string name="device_guide_content_4">At a glance, view Eco-achievements and customize the Battery Strategies.</string>

    <string name="relative_price_multiLine">Relative Price MultiLine</string>

    <string name="invite_member_without_app">Invite member without App</string>
    <string name="total_load_setting_desc">Select the current specification for the house main fuse (or air circuit-breaker). If none of the following options match, select the maximum value that is less than the actual specification.</string>
    <string name="total_load_settings">Total Load Settings</string>
    <string name="emergency_charge_pending">Emergency Charge Pending</string>

    <string name="add_a_device_from_installer">Add a device from installer</string>

    <string name="energy_settings">Energy Settings</string>
    <string name="third_party_pv">Third-Party PV</string>
    <string name="requirements_colon">Requirements:</string>
    <string name="utility_is_inputted_normally_period">Utility is inputted normally.</string>
    <string name="third_party_pv_virtual_plant_tips">Using third-party PV may affect the electricity selling of the whole house.</string>
    <string name="third_party_pv_successfully">Third-party PV setup successfully.</string>
    <string name="third_party_pv_failed">Third-party PV setup failed.</string>
    <string name="please_check_the_requirements_colon">Please check the requirements:</string>
    <string name="smartmeter_operates_normally_period">Smartmeter operates normally.</string>
    <string name="dual_power">Dual Power</string>
    <string name="solar_dual">Solar_Dual</string>
    <string name="using_third_party_pv_desc">When using third-party PV, the data reflects the offset between third-party PV and additional load.</string>
    <string name="per">Per</string>
    <string name="connect_device">Connect Device</string>

    <string name="contact_your_installer_to_upgrade">Contact your installer to upgrade</string>


    <string name="buy_traffic_package_hint">This function is only for Power Storage Solutions with 4G network, please add one first.</string>
    <string name="traffic_package_qa_content">Q&amp;A content of #service</string>

    <string name="your_hashtag_service_is_expired">Your #service is expired.</string>
    <string name="hashtag_service">#service</string>
    <string name="your_service_remaining_day">Your #service remaining trial period is #validity_period day(s).</string>
    <string name="hashtag_validity_period">#validity_period</string>
    <string name="your_service_validity_day">Your #service is valid for #validity_period Day(s)</string>
    <string name="welcome_back">Welcome Back</string>
    <string name="active">Active</string>
    <string name="delete_bmt_tips">After deletion, the #service Service will continue to be consumed. Confirm to delete?</string>
    <string name="delete_family_with_traffic_package_service">The #service Service has not yet expired, the deletion of the family is considered as automatic abandonment.</string>
    <string name="prime_service_signup_on_emaldo">(#prime_service signup on emaldo.com)</string>
    <string name="hashtag_prime_service">#prime_service</string>
    <string name="extended_warranty">Extended Warranty</string>
    <string name="hashtag_days">#days</string>
    <string name="power_store">Power Store</string>
    <string name="power_store_2_point_0">Power Store 2.0</string>

    <string name="power_core">Power Core</string>
    <string name="power_core_1_point_0">Power Core 1.0</string>
    <string name="power_core_2_point_0">Power Core 2.0</string>
    <string name="power_core_3_point_0">Power Core 3.0</string>
    <string name="power_pulse">Power Pulse</string>
    <string name="emaldo_power_station">Emaldo power station</string>
    <string name="product_name">Product Name</string>
    <string name="submit">Submit</string>
    <string name="model_do_not_match_tips">Device and product model do not match. Please modify it manually.</string>
    <string name="trough">Trough</string>

    <string name="user_information">User Information</string>
    <string name="company_name">Company Name</string>
    <string name="first_and_last_name">First and Last Name</string>
    <string name="eu_vat_number">EU VAT Number</string>
    <string name="address_information">Address Information</string>
    <string name="country">Country</string>
    <string name="city">City</string>
    <string name="street_name_and_number">Street Name and Number</string>
    <string name="electricity_supplier">Electricity Supplier</string>
    <string name="country_supported">Country Supported</string>
    <string name="enter_your_electricity_supplier">Enter your electricity supplier</string>
    <string name="electricity_supplier_supported">Electricity Supplier Supported</string>
    <string name="bank_account">Bank Account</string>
    <string name="sign_up_bank_account_desc">Your monthly earnings will be paid directly to your bank account.</string>
    <string name="sign_up_bank_account_note">Only one bank account per family.</string>
    <string name="name_of_account_holder">Name of Account Holder</string>
    <string name="IBAN">IBAN</string>
    <string name="contracted_devices">Contracted Devices</string>
    <string name="your_devices_tip">By signing this agreement, all devices you have added to the family will be enrolled in Grid Reward tasks.</string>
    <string name="your_devices">Your Devices</string>
    <string name="zip_code">Zip Code</string>
    <string name="area_mismatch">Area mismatch</string>
    <string name="Offgrid">Offgrid</string>
    <string name="Enabling">Enabling</string>
    <string name="Connecting">Connecting</string>
    <string name="Wrong_IBAN_entered">Wrong IBAN entered.</string>
    <string name="modify">Modify</string>
    <string name="change_IBAN_tip">Only one bank account per family is allowed. Are you sure you want to modify?</string>
    <string name="balancing_power">Balancing Power</string>
    <string name="Email_Address">Email Address</string>
    <string name="iban_hint_text">DK11 2222 3333 4444 55</string>
    <string name="PV_forecast">PV forecast</string>
    <string name="Low_solar">Low solar</string>
    <string name="Sufficient_solar">Sufficient solar</string>
    <string name="Intense_solar">Intense solar</string>
    <string name="plus_1_day">(+1 day)</string>
    <string name="Initialize">Initialize</string>
    <string name="Initialized">Initialized</string>
    <string name="No_solar">No solar</string>

    <string name="Charge_with_grid_power">Charge with grid power</string>
    <string name="Charge_with_grid">Charge With Grid</string>
    <string name="Await_Sun_Charge">Await Sun Charge</string>
    <string name="Await_Lower_Utility_Charge">Await Lower Utility Charge</string>
    <string name="Battery_Discharge">Battery Discharge</string>
    <string name="Battery_discharge">Battery Discharge</string>
    <string name="No_Charge_Nor_Discharge">No Charge Nor Discharge</string>
    <string name="AI_Mode_No_data">No data. Please try again later.</string>
    <string name="Charge">Charge</string>
    <string name="Edited">Edited</string>
    <string name="Discharge">Discharge</string>
    <string name="Await_Sun">Await Sun</string>
    <string name="Await_Lower_Utility">Await Lower Utility</string>
    <string name="ai_scheduled_detail_content_1">With today\'s low rates, it\'s a great time to save and charge the battery up to the plenty energy.</string>
    <string name="ai_scheduled_detail_content_2">Abundant solar energy is expected soon, let\'s hold off and charge with the sun.</string>
    <string name="ai_scheduled_detail_content_3">Lower utility rates is expected soon, let\'s hold off and charge at a better price.</string>
    <string name="ai_scheduled_detail_content_4">With grid power costs high, it\'s smart to supply the load with the battery and discharge to the smart reserve.</string>
    <string name="ai_scheduled_detail_content_5">With balanced supply-demand and flat price trends, further action is unnecessary.</string>
    <string name="ai_scheduled_detail_content_6">Good opportunity to save and charge the battery with today\'s favorable rates up to the smart reserve.</string>
    <string name="ai_scheduled_detail_content_7">Not a bad time to save and charge the battery with today\'s slightly reduced rates up to the emergency reserve.</string>
    <string name="ai_scheduled_detail_content_8">As utility costs exceed the average, it can save you considerable costs when running on battery in the plenty energy.</string>
    <string name="ai_scheduled_detail_notice">Noticed an inaccurate prediction? Click to #report and help refine the AI.</string>
    <string name="Low_Utility">Low Utility</string>
    <string name="High_Utility">High Utility</string>
    <string name="High_Solar">High Solar</string>
    <string name="Lower_Utility">Lower Utility</string>
    <string name="AI_Manual_Mode">AI Manual Mode</string>
    <string name="Charge_with_solar">Charge with solar</string>

    <!-- 2.0架构设备事件码-->
    <string-array name="ps2_inverter_exception_titles">
        <item>@string/ps_event_key_0100</item>
        <item>@string/ps_event_key_0101</item>
        <item>@string/ps_event_key_0102</item>
        <item>@string/ps_event_key_0103</item>
        <item>@string/ps_event_key_0104</item>
        <item>@string/ps_event_key_0105</item>
        <item>@string/ps_event_key_0106</item>
        <item>@string/ps_event_key_0107</item>
        <item>@string/ps_event_key_0108</item>
        <item>@string/ps_event_key_0109</item>
        <item>@string/ps_event_key_010A</item>
        <item>@string/ps_event_key_010B</item>
        <item>@string/ps_event_key_010C</item>
        <item>@string/ps_event_key_010D</item>
        <item>@string/ps_event_key_010E</item>
        <item>@string/ps_event_key_010F</item>
        <item>@string/ps_event_key_0110</item>
        <item>@string/ps_event_key_0111</item>
        <item>@string/ps_event_key_0113</item>
        <item>@string/ps_event_key_0114</item>
        <item>@string/ps_event_key_0115</item>
        <item>@string/ps_event_key_0116</item>
        <item>@string/ps_event_key_0117</item>
        <item>@string/ps_event_key_0118</item>
        <item>@string/ps_event_key_0119</item>
        <item>@string/ps_event_key_011A</item>
        <item>@string/ps_event_key_011B</item>
        <item>@string/ps_event_key_0120</item>
        <item>@string/ps_event_key_0121</item>
        <item>@string/ps_event_key_0122</item>
        <item>@string/ps_event_key_0123</item>
        <item>@string/ps_event_key_0124</item>
        <item>@string/ps_event_key_0125</item>
        <item>@string/ps_event_key_0126</item>
        <item>@string/ps_event_key_0127</item>
        <item>@string/ps_event_key_0128</item>
        <item>@string/ps_event_key_0129</item>
        <item>@string/ps_event_key_012A</item>
        <item>@string/ps_event_key_012B</item>
        <item>@string/ps_event_key_012C</item>
        <item>@string/ps_event_key_012D</item>
        <item>@string/ps_event_key_0130</item>
        <item>@string/ps_event_key_0131</item>
        <item>@string/ps_event_key_0132</item>
        <item>@string/ps_event_key_0133</item>
        <item>@string/ps_event_key_0134</item>
        <item>@string/ps_event_key_0135</item>
        <item>@string/ps_event_key_0136</item>
        <item>@string/ps_event_key_0137</item>
        <item>@string/ps_event_key_0138</item>
        <item>@string/ps_event_key_0139</item>
        <item>@string/ps_event_key_013A</item>
        <item>@string/ps_event_key_013B</item>
        <item>@string/ps_event_key_013C</item>
        <item>@string/ps_event_key_0140</item>
        <item>@string/ps_event_key_0141</item>
        <item>@string/ps_event_key_0142</item>
        <item>@string/ps_event_key_0143</item>
        <item>@string/ps_event_key_0144</item>
        <item>@string/ps_event_key_0150</item>
        <item>@string/ps_event_key_0151</item>
        <item>@string/ps_event_key_0152</item>
        <item>@string/ps_event_key_0153</item>
        <item>@string/ps_event_key_0154</item>
        <item>@string/ps_event_key_0155</item>
        <item>@string/ps_event_key_0156</item>
        <item>@string/ps_event_key_0157</item>
        <item>@string/ps_event_key_0158</item>
        <item>@string/ps_event_key_0159</item>
        <item>@string/ps_event_key_015A</item>
        <item>@string/ps_event_key_015B</item>
        <item>@string/ps_event_key_015C</item>
        <item>@string/ps_event_key_015D</item>
        <item>@string/ps_event_key_015E</item>
        <item>@string/ps_event_key_015F</item>
        <item>@string/ps_event_key_0160</item>
        <item>@string/ps_event_key_0161</item>
        <item>@string/ps_event_key_0162</item>
        <item>@string/ps_event_key_0163</item>
        <item>@string/ps_event_key_0164</item>
        <item>@string/ps_event_key_0165</item>
        <item>@string/ps_event_key_0166</item>
        <item>@string/ps_event_key_0167</item>
        <item>@string/ps_event_key_0168</item>
        <item>@string/ps_event_key_0169</item>
    </string-array>

    <string-array name="ps2_inverter_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>

        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>

        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>

        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>

        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>

        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>
        <item>@string/bmt_exception_content_3</item>

        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps2_battery_exception_titles">
        <item>@string/ps_event_key_0203</item>
        <item>@string/ps_event_key_0204</item>
        <item>@string/ps_event_key_0205</item>
        <item>@string/ps_event_key_0206</item>
        <item>@string/ps_event_key_0207</item>
        <item>@string/ps_event_key_0210</item>
        <item>@string/ps_event_key_0211</item>
        <item>@string/ps_event_key_0212</item>
        <item>@string/ps_event_key_0214</item>
        <item>@string/ps_event_key_0215</item>
        <item>@string/ps_event_key_0217</item>
        <item>@string/ps_event_key_0220</item>
        <item>@string/ps_event_key_0221</item>
        <item>@string/ps_event_key_0222</item>
        <item>@string/ps_event_key_0223</item>
        <item>@string/ps_event_key_0226</item>
        <item>@string/ps_event_key_0227</item>
        <item>@string/ps_event_key_0232</item>
        <item>@string/ps_event_key_0233</item>
        <item>@string/ps_event_key_0234</item>
        <item>@string/ps_event_key_0235</item>
        <item>@string/ps_event_key_0236</item>
        <item>@string/ps_event_key_0237</item>
        <item>@string/ps_event_key_0240</item>
        <item>@string/ps_event_key_0241</item>
        <item>@string/ps_event_key_0242</item>
        <item>@string/ps_event_key_0244</item>
        <item>@string/ps_event_key_0245</item>
        <item>@string/ps_event_key_0250</item>
        <item>@string/ps_event_key_0251</item>
        <item>@string/ps_event_key_0252</item>
        <item>@string/ps_event_key_0253</item>
        <item>@string/ps_event_key_0255</item>
        <item>@string/ps_event_key_0256</item>
        <item>@string/ps_event_key_0257</item>

        <item>@string/ps_event_key_0270</item>
        <item>@string/ps_event_key_0271</item>
        <item>@string/ps_event_key_0272</item>
        <item>@string/ps_event_key_0273</item>
        <item>@string/ps_event_key_0274</item>
        <item>@string/ps_event_key_0275</item>

        <item>@string/ps_event_key_0280</item>
        <item>@string/ps_event_key_0281</item>
        <item>@string/ps_event_key_0282</item>
        <item>@string/ps_event_key_0283</item>
        <item>@string/ps_event_key_0284</item>
        <item>@string/ps_event_key_0285</item>
        <item>@string/ps_event_key_0286</item>

        <item>@string/ps_event_key_0290</item>
        <item>@string/ps_event_key_0291</item>
        <item>@string/ps_event_key_0292</item>
        <item>@string/ps_event_key_0293</item>
        <item>@string/ps_event_key_0294</item>
        <item>@string/ps_event_key_0295</item>
        <item>@string/ps_event_key_0296</item>
        <item>@string/ps_event_key_0297</item>
    </string-array>

    <string-array name="ps2_battery_exception_contents">
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>

        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>

        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>

        <item>@string/bmt_exception_content_1</item>

        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>

        <item>@string/bmt_exception_content_1</item>

        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>

        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>

        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
        <item>@string/bmt_exception_content_5</item>
    </string-array>

    <string-array name="ps2_ev_exception_titles">
        <item>@string/ps_event_key_0301</item>
        <item>@string/ps_event_key_0302</item>
        <item>@string/ps_event_key_0303</item>
        <item>@string/ps_event_key_0304</item>
        <item>@string/ps_event_key_0305</item>
        <item>@string/ps_event_key_0306</item>
        <item>@string/ps_event_key_0307</item>
        <item>@string/ps_event_key_0308</item>
        <item>@string/ps_event_key_0309</item>
        <item>@string/ps_event_key_030A</item>
        <item>@string/ps_event_key_030B</item>
    </string-array>
    <string-array name="ps2_ev_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps2_cabinet_exception_titles">
        <item>@string/ps_event_key_0400</item>
    </string-array>
    <string-array name="ps2_cabinet_exception_contents">
        <item>@string/bmt_exception_content_4</item>
    </string-array>

    <string-array name="ps2_ota_exception_titles">
        <item>@string/ps_event_key_7001</item>
        <item>@string/ps_event_key_7002</item>
        <item>@string/ps_event_key_7003</item>
        <item>@string/ps_event_key_7004</item>
        <item>@string/ps_event_key_7005</item>
        <item>@string/ps_event_key_7006</item>
    </string-array>

    <string-array name="ps2_communication_exception_titles">
        <item>@string/ps_event_key_8001</item>
        <item>@string/ps_event_key_8002</item>
        <item>@string/ps_event_key_8003</item>
        <item>@string/ps_event_key_8004</item>
        <item>@string/ps_event_key_8005</item>
    </string-array>
    <string-array name="ps2_communication_exception_contents">
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
        <item>@string/bmt_exception_content_2</item>
    </string-array>

    <string-array name="ps2_event_exception_titles">
        <item>@string/ps_event_key_9000</item>
    </string-array>
    <string-array name="ps2_event_exception_contents">
        <item>@string/bmt_exception_content_1</item>
    </string-array>

    <string-array name="ps2_iot_exception_titles">
        <item>@string/ps_event_key_E010</item>
    </string-array>
    <string-array name="ps2_iot_exception_contents">
        <item>@string/bmt_exception_content_2</item>
    </string-array>
    
    <string name="balancing">Balancing</string>
    <string name="error_too_many_verifyCode">Too many attempts. Please try again in 1 minute.</string>
    <string name="error_wrong_verifyCode">Wrong verification code entered.</string>
    <string name="error_verifyCode_timeout">The verification code has expired.</string>

    <string name="pre_balancing">Pre-Balancing</string>

    <string name="explore_more">Explore More</string>

    <!--    Emaldo AI 1.1的key-->
    <string name="Edit_Battery_Range">Edit Battery Range</string>
    <string name="Manual_Override">Manual Override</string>
    <string name="Solar_Priority">Solar Priority</string>
    <string name="PV_Distribution_Preference">PV Distribution Preference</string>
    <string name="Load_First">Load First</string>
    <string name="Battery_Charge_First">Battery Charge First</string>
    <string name="Follow_Emaldo_AI">Follow Emaldo AI</string>
    <string name="Adjust_Power_Threshold">Adjust Power Threshold</string>
    <string name="Location">Location</string>
    <string name="ai_location_setup">AI Location Setup</string>
    <string name="ai_location_setup_hint">Al mode needs weather data to predict solar energy and optimize charging. Please share your approximate location (no precise details) Your privacy is strictly protected.</string>
    <string name="is_the_device_nearby">Is the device nearby?</string>
    <string name="is_the_device_nearby_hint">The AI configuration will update the location information. Is the device nearby?</string>
    <string name="set_later_in_ai_Mode">Set later in AI Mode</string>
    <string name="modify_ai_location_hint">Modifying your location will trigger AI model recalibration, reducing prediction accuracy about 7 days.</string>
    <string name="Acquire_precise_forecasts">Acquire precise forecasts</string>

    <!--    Emaldo AI 1.2的key-->
    <string name="Price_Peak">Price Peak</string>
    <string name="Price_Bottom">Price Bottom</string>
    <string name="emaldo_ai_retrieving_note">Emaldo AI is retrieving data for the optimal plan.</string>

    <!-- 保险丝相关key-->
    <string name="Continue">Continue</string>
    <string name="smart_ev_balancing">Smart EV Balancing</string>
    <string name="smart_ev_balancing_content">EV power shall auto-regulate during peak demand to prevent fuse overload.</string>
    <string name="smart_ev_balancing_close_tip">Disabling this may cause fuse overload during peak demand. Are you sure to continue?</string>
    <string name="Restart">Restart</string>
    <string name="smart_ev_notification">Overload condition resolved.  Confirm to continue EV charging?</string>


    <!-- 保险丝 -->
    <string name="higher_ratings">Higher Ratings</string>
    <string name="lower_ratings">Lower Ratings</string>
    <string name="medium_ratings">Medium Ratings</string>

    <string name="Repeat">Repeat</string>
    <string name="Participation_Hours">Participation Hours</string>
    <string name="participation_hours_note_1">Balancing tasks will only be assigned during your participation hours.</string>
    <string name="participation_hours_note_2">Suggest NOT to use high-power electrical appliances (e.g. EV charging, heater, washing machine) during this period to ensure stable service and your rewards.</string>
    <string name="Schedule">Schedule</string>
    <string name="All_Day">All-Day</string>
    <string name="delete_schedule_tips">Are you sure you want to delete this schedule?</string>
    <string name="conflicts_with_existing_tasks_tips">New settings conflicts with existing tasks. Do you want to discard or save settings?</string>
    <string name="conflicts_with_existing_tasks_discard_note">Discard - Keep current task, discard new settings.</string>
    <string name="conflicts_with_existing_tasks_save_note">Save - Stop current task, apply new settings.</string>
    <string name="Note">Note</string>
    <string name="Everyday">Everyday</string>
    <string name="participation_time_1_hour_tips">Please set at least 1 hour of participation time.</string>
    <string name="maximum_of_10_schedules_tips">You can add a maximum of 10 schedules only.</string>
    <string name="participate_in_balancing_24_7_tips">You are currently set to participate in balancing 24/7. You can update it anytime in Participation Hours.</string>
    <string name="Go_Settings">Go Settings</string>
    <string name="Setting_Guide">Setting Guide</string>
    <string name="Grid_Balancing_Hours_Irregular">Grid Balancing Hours (Irregular)</string>
    <string name="Actual_Active_Hours">Actual Active Hours</string>
    <string name="Your_preferred_participate_hours">Your preferred participate hours</string>
    <string name="Tips_with_colon">Tips:</string>
    <string name="Every_Sunday">Every Sunday</string>
    <string name="Every_Monday">Every Monday</string>
    <string name="Every_Tuesday">Every Tuesday</string>
    <string name="Every_Wednesday">Every Wednesday</string>
    <string name="Every_Thursday">Every Thursday</string>
    <string name="Every_Friday">Every Friday</string>
    <string name="Every_Saturday">Every Saturday</string>
    <string name="Please_set_at_least_1_hour_of_participation_time">Please set at least 1 hour of participation time.</string>
    <string name="Final_step_Please_set_your_Participation_Hours">Final step:  Please set your Participation Hours.</string>
    <string name="Modify_It">Modify It</string>
    <string name="Keep_Setting">Keep Setting</string>
    <string name="participation_hour_delete_current_task_tips">Deleting the setting will stop the current task. Are you sure you want to delete the setting?</string>
    <string name="Are_you_sure_you_want_to_discard_all_changes">Are you sure you want to discard all changes?</string>
    <string name="Please_select_at_least_one_date">Please select at least one date.</string>
    <string name="Family_has_no_devices_from_this_country_Please_choose_another_country">Family has no devices from this country. Please choose another country.</string>
</resources>
