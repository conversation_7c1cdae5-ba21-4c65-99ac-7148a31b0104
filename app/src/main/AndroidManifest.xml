<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.dinsafer.dinnet">

    <!--网络请求-->
    <application
        android:name="com.dinsafer.DinSaferApplication"
        android:allowBackup="true"
        android:allowNativeHeapPointerTagging="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup,android:supportsRtl">

        <activity
            android:name="com.dinsafer.module.daily.DailyMemoriesVideoPlayActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/ThemeWithoutAnim" />

        <activity
            android:name="com.dinsafer.module.powerstation.settings.BindInverterActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.dinsafer.module.user.LoginActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <activity android:name="com.dinsafer.module.powerstation.PowerStationActivity">

        </activity>
        <activity android:name="com.dinsafer.module.powerstation.electricity.ElectricityStatisticsActivity">

        </activity>
        <activity
            android:name="com.dinsafer.module.main.view.MainActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" /> <!-- TODO DinsaferPush start -->
        <activity
            android:name="com.dinsafer.module.spash.SpashActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="${OPEN_APP_SCHEME}" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.dinsafer.module.settting.ui.ScannerActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/ThemeWithoutAnim" />

        <activity
            android:name="com.dinsafer.module.iap.ScanRedeemActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.settting.ui.ReadCurrentPwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.settting.ui.RecordSearchActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.settting.ui.RecordPlayActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.settting.ui.GenerateNewPwdActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.ipc.heartlai.HeartLaiFullPlayFragment"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="false"
            android:screenOrientation="landscape" /> <!-- TODO DinsaferPush end -->
        <!-- TODO DinsaferPush 开始 -->
        <activity
            android:name="com.dinsafer.module.ipc.heartlai.HeartLaiFullPlayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="false"
            android:screenOrientation="landscape" /> <!-- TODO DinsaferPush 结束 -->
        <!-- TextureView 一定要硬件加速 -->
        <activity
            android:name="com.dinsafer.dscam.DsCamFullPlayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="true"
            android:screenOrientation="landscape" />

        <activity
            android:name="com.dinsafer.module.doorbell.play.DsDoorbellPlayFullscreenActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" />

        <activity
            android:name="com.dinsafer.module.doorbell.play.DsDoorbellPlayFocusModeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.dinsafer.dscam.DsCamV006FullPlayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="true"
            android:screenOrientation="landscape" />

        <activity
            android:name="com.dinsafer.dscam.DsCamMultiFullPlayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:hardwareAccelerated="true"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.dinsafer.module.other.PushActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="com.dinsafer.OpenClickNotificationActivity" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="${applicationId}" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.dinsafer.push.FCMPushActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="com.dinsafer.OpenFCMClickNotificationActivity" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="${applicationId}" />
            </intent-filter>
        </activity> <!-- TODO FCM 结束 -->
        <activity
            android:name="com.dinsafer.module.ipc.heartlai.HeartLaiRecordPlayActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="false"
            android:launchMode="singleTask"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name="com.dinsafer.module.settting.ui.IPCSosRecordActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.settting.ui.IPCHeartLaiMotionRecordActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- <activity android:name="com.facebook.react.devsupport.DevSettingsActivity"/> -->
        <!-- <activity -->
        <!-- android:name="com.dinsafer.module.app.password.PasswordActivity" -->
        <!-- android:launchMode="singleTask" -->
        <!-- android:theme="@android:style/Theme.NoTitleBar.Fullscreen" -->
        <!-- android:screenOrientation="portrait"> -->
        <!-- </activity> -->
        <activity
            android:name="com.dinsafer.module.settting.ui.IPCHeartLaiMotionRecordIJKPlayerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.ipc.common.video.IPCHeartLaiMotionRecordIJKPlayerActivity2"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/ThemeWithoutAnim" />

        <activity
            android:name="com.dinsafer.dscam.timeline.MotionRecordTimelinePlayerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.iap.IapRootActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.dscam.timeline.MotionRecordDownloadListActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.dinsafer.module.settting.ui.WebViewActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <meta-data
            android:name="XIAOMI_APP_ID"
            android:value="MI_2882303761517970988" />
        <meta-data
            android:name="XIAOMI_APP_KEY"
            android:value="MI_5151797012988" />

        <meta-data
            android:name="DinsaferPush_APPID"
            android:value="CXRqKjx2MzSAkdyucR9NDyPiiQR2vQcQ" />

        <meta-data
            android:name="DinsaferPush_SECRETKEY"
            android:value="KgEYfyhKs6oKMrMKAnpHF97ifV8n43co" />

        <meta-data
            android:name="DinsaferPushChannel_XiaoMi"
            android:value="${DPUSH_CHANNEL_XIAOMI}" />

        <meta-data
            android:name="DinsaferPushChannel_FCM"
            android:value="${DPUSH_CHANNEL_FCM}" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/icon_notification_tran_bg" /> <!-- 添加涂鸦    必要的service和receiver -->
        <!-- <service android:name="org.eclipse.paho.android.service.MqttService" /> -->
        <!-- <receiver android:name="com.tuya.smart.android.base.broadcast.NetworkBroadcastReceiver"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.net.conn.CONNECTIVITY_CHANGE" /> -->
        <!-- </intent-filter> -->
        <!-- </receiver> -->
        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="${UMENG_CHANNEL_VALUE}" />
        <meta-data
            android:name="TUYA_SMART_APPKEY"
            android:value="${TUYA_APPKEY}" />
        <meta-data
            android:name="TUYA_SMART_SECRET"
            android:value="${TUYA_APPSEC}" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <receiver
            android:name="com.dinsafer.receiver.MyBaseDinsaferPushReveiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="com.dinsfer.ACTION_ON_RECEIVE_TOKEN" />
                <action android:name="com.dinsfer.ACTION_ON_RECEIVE_COMMAND" />
                <action android:name="com.dinsfer.ACTION_ON_RECEIVE_NOTIFICATION" />
                <action android:name="com.dinsfer.ACTION_ON_RECEIVE_MESSAGE" />
                <action android:name="com.dinsfer.ACTION_ON_NOTIFICATION_CLICK" />

                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.dinsafer.NetworkConnectChangedReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
                <action android:name="android.net.wifi.STATE_CHANGE" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.tuya.smart.android.base.broadcast.NetworkBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.dinsafer.thirdparty.fcm.DinsaferFirebaseMessagingService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <service
            android:name="com.dinsafer.thirdparty.fcm.DinsaferFirebaseInstanceIdService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
            </intent-filter>
        </service>

        <service android:name="hsl.p2pipcam.activity.BridgeService" />

        <!-- 心赖IPC -->
        <service
            android:name="com.tuya.smart.android.hardware.service.GwBroadcastMonitorService"
            android:exported="true"
            android:label="UDPService"
            android:process=":monitor"

            tools:replace="android:exported">
            <intent-filter>
                <action android:name="tuya.intent.action.udp" />

                <category android:name="tuya" />
            </intent-filter>
        </service>

        <service android:name="com.tuya.smart.mqtt.MqttService" />

        <service android:name="com.tuya.smart.android.hardware.service.DevTransferService" />

        <service android:name="com.heartlai.ipc.BridgeService" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
    </application>
    <permission
        android:name="${applicationId}.permission.DINSAFRE_PUSH"
        android:protectionLevel="signature" />

    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- 录音相关权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />

    <uses-permission android:name="android.permission.ACCESS_COARSE_UPDATES" />

    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!--Zxing申请权限-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.READ_LOGS" />

    <!--蓝牙相关-->
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />

    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />

    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />

    <!--TODO DinsaferPush start-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Required -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="${applicationId}.permission.DINSAFRE_PUSH" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!--TODO DinsaferPush end-->

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!--    Android12新增的蓝牙权限,运行时权限-->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />


    <!-- android13权限 start-->
    <!-- Required only if your app targets Android 13. -->
    <!-- Declare one or more the following permissions only if your app needs
    to access data that's protected by them. -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- android13权限 end-->

</manifest>