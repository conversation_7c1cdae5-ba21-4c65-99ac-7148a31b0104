package com.dinsafer.ui.device;

import android.animation.Animator;
import android.animation.LayoutTransition;
import android.animation.TypeEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.GradientDrawable;
import android.media.AudioManager;
import android.media.SoundPool;
import android.util.AttributeSet;
import android.util.Base64;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSONObject;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dssupport.utils.HexUtil;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.viewanimator.AnimationListener;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.flyco.tablayout.listener.OnTabSelectListener;

import java.util.ArrayList;

/**
 * 自定义Toolbar
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/10 6:03 PM
 */
public class ToolbarTabLayout extends FrameLayout
        implements ValueAnimator.AnimatorUpdateListener, Animator.AnimatorListener {
    private final String TAG = ToolbarTabLayout.class.getSimpleName();

    /**
     * 控件默认值
     */
    private final int DEFAULT_BG_COLOR = Color.LTGRAY;
    private final int DEFAULT_ANIM_DURATION = 500;
    private final int DEFAULT_TEXT_SIZE_DP = 11;
    private final int DEFAULT_TEXT_COLOR = Color.WHITE;
    private final int DEFAULT_TEXT_COLOR_SELECTED = Color.BLACK;
    private final int DEFAULT_INDICATOR_WIDTH_DP = 44;
    private final int DEFAULT_INDICATOR_HEIGHT_DP = 44;
    private final int DEFAULT_INDICATOR_CORNER_RADIUS_DP = 29;
    private final int DEFAULT_INDICATOR_COLOR = Color.GRAY;
    private final int DEFAULT_MAIN_CONTAINER_WIDTH_DP = 44;
    private final int DEFAULT_MAIN_CONTAINER_HEIGHT_DP = 44;
    private final int DEFAULT_GROUP_DIVISION_DP = 16;

    private ArrayList<CustomTabEntity> mTabEntitys = new ArrayList<>();
    private CustomTabEntity mSosEntiry;
    private LinearLayout mTabsContainer;
    private FrameLayout mSosContainer;
    private @DeviceStatusHelper.DeviceArmStatus
    int mCurrentArmStatus = DeviceStatusHelper.ARM_STATUS_ARM;
    private @DeviceStatusHelper.ToolbarArmStatus
    int mToolbarStatus = DeviceStatusHelper.TOOLBAR_STATUS_DISABLE;
    private int mLastTab;
    private int mTabCount;
    private OnOperateListener mOperateListener;

    /**
     * 用于绘制显示器
     */
    private Rect mIndicatorRect = new Rect();
    private GradientDrawable mIndicatorDrawable = new GradientDrawable();

    /**
     * 用于绘制背景
     */
    private GradientDrawable mRectDrawable = new GradientDrawable();

    /**
     * title
     */
    private float mTextsize = DensityUtils.dp2px(getContext(), DEFAULT_TEXT_SIZE_DP);
    private int mTextSelectColor = DEFAULT_TEXT_COLOR;
    private int mTextUnselectColor = DEFAULT_TEXT_COLOR_SELECTED;
    private float mCounterTextSize = DensityUtils.dp2px(getContext(), DEFAULT_TEXT_SIZE_DP);
    private int mCounterTextColor = DEFAULT_TEXT_COLOR_SELECTED;

    /**
     * indicator
     */
    private int mIndicatorColor = DEFAULT_INDICATOR_COLOR;
    private float mIndicatorHeight = DensityUtils.dp2px(getContext(), DEFAULT_INDICATOR_HEIGHT_DP);
    private float mIndicatorWidth = DensityUtils.dp2px(getContext(), DEFAULT_INDICATOR_WIDTH_DP);
    private float mIndicatorCornerRadius = DensityUtils.dp2px(getContext(), DEFAULT_INDICATOR_CORNER_RADIUS_DP);
    private float mIndicatorMarginLeft;
    private float mIndicatorMarginTop;
    private float mIndicatorMarginRight;
    private float mIndicatorMarginBottom;
    private long mIndicatorAnimDuration = DEFAULT_ANIM_DURATION;
    private boolean mIndicatorAnimEnable = true; // 是否开启切换动画

    private OnTabSelectListener mListener;

    private OnLongClickListener mLongClickLister;

    /**
     * anim
     */
    private ValueAnimator mValueAnimator;
    private AccelerateDecelerateInterpolator mInterpolator = new AccelerateDecelerateInterpolator();

    // 是否需要计算indicator的位置
    // 开启页面布局动画，sos可见状态改变需要不断计算，否则，只需要计算第一次
    private boolean mIsNeedCalculateIndicator = true;

    /**
     * icon
     */
    private float mIconWidth = DensityUtils.dp2px(getContext(), DEFAULT_MAIN_CONTAINER_WIDTH_DP);
    private float mIconHeight = DensityUtils.dp2px(getContext(), DEFAULT_MAIN_CONTAINER_HEIGHT_DP);

    private boolean mShowSos = true;
    private int mSosIconId = R.drawable.btn_sos;
    private String mSosText = "SOS";

    private int mBgColor = DEFAULT_BG_COLOR;
    private int mGroupDivision = DensityUtils.dp2px(getContext(), DEFAULT_GROUP_DIVISION_DP);

    // 页面动画
    private LayoutTransition mLayoutTransition;
    private LayoutTransition.TransitionListener mTransitionListener;
    private boolean mOpenTransitionAnim = true;
    private boolean mIsLayoutAnim = false;

    private String mMyMessageId;
    private SoundPool soundPool;
    private int id;

    public ToolbarTabLayout(Context context) {
        super(context);
        init(null);
    }

    public ToolbarTabLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public ToolbarTabLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        DDLog.d(TAG, "onMeasure");
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        // Tab分组的宽度
        MarginLayoutParams layoutParams = (MarginLayoutParams) getLayoutParams();
        int width = getMeasuredWidth() - getPaddingLeft() - getPaddingRight()
                - layoutParams.leftMargin - layoutParams.rightMargin;
        FrameLayout.LayoutParams lp = (LayoutParams) mTabsContainer.getLayoutParams();
        lp.width = (int) (width - mIndicatorWidth - mGroupDivision);
        lp.gravity = mShowSos ? Gravity.START : Gravity.CENTER;
        mTabsContainer.setLayoutParams(lp);
    }

    private void init(@Nullable AttributeSet attrs) {
        obtainAttr(attrs);

        setWillNotDraw(false);//重写onDraw方法,需要调用这个方法来清除flag
        setClipChildren(false);
        setClipToPadding(false);

        initLayoutTransition();

        mSosEntiry = new ToolbarTabEntity(mSosText, mSosIconId, mSosIconId);

        mTabsContainer = new LinearLayout(getContext());
        addView(mTabsContainer);
        mSosContainer = new FrameLayout(getContext());
        addView(mSosContainer);
        initSosData();

        mValueAnimator = ValueAnimator.ofObject(new PointEvaluator(), mLastP, mCurrentP);
        mValueAnimator.addUpdateListener(this);
        mValueAnimator.addListener(this);

        // 默认数据
        ArrayList<CustomTabEntity> tabEntitys = new ArrayList<>();
        tabEntitys.add(new ToolbarTabEntity(
                getContext().getString(R.string.toolbar_arm_text), R.drawable.btn_arm_sel, R.drawable.btn_arm_nor));
        tabEntitys.add(new ToolbarTabEntity(
                getContext().getString(R.string.toolbar_disarm_text), R.drawable.btn_disarm_sel, R.drawable.btn_disarm_nor));
        tabEntitys.add(new ToolbarTabEntity(
                getContext().getString(R.string.toolbar_homearm_text), R.drawable.btn_homearm_sel, R.drawable.btn_homearm_nor));
        setTabData(tabEntitys);

        // 声音提示
        soundPool = new SoundPool(10, AudioManager.STREAM_SYSTEM, 5);
        soundPool.load(getContext(), R.raw.short_beep, 1);
        soundPool.setOnLoadCompleteListener(new SoundPool.OnLoadCompleteListener() {
            @Override
            public void onLoadComplete(SoundPool sp, int sampleId, int status) {

            }
        });

        setToolbarStatusDisable();
    }

    /**
     * 设置默认动画
     */
    private void initLayoutTransition() {
        DDLog.d(TAG, "initLayoutTransition");
        if (!mOpenTransitionAnim) {
            return;
        }

        // 默认View移除添加动画
        mLayoutTransition = new LayoutTransition();
        mTransitionListener = new LayoutTransition.TransitionListener() {
            @Override
            public void startTransition(LayoutTransition transition, ViewGroup container, View view, int transitionType) {
                mIsNeedCalculateIndicator = true;
                mIsLayoutAnim = true;
            }

            @Override
            public void endTransition(LayoutTransition transition, ViewGroup container, View view, int transitionType) {
                mIsNeedCalculateIndicator = true;
                mIsLayoutAnim = false;
                requestLayout();
            }
        };
        mLayoutTransition.addTransitionListener(mTransitionListener);
        setLayoutTransition(mLayoutTransition);
    }

    private void removeLayoutTransitionListener() {
        if (null != mLayoutTransition && null != mTransitionListener) {
            mLayoutTransition.removeTransitionListener(mTransitionListener);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        if (null != mLayoutTransition && null != mTransitionListener) {
            mLayoutTransition.removeTransitionListener(mTransitionListener);
            mLayoutTransition.addTransitionListener(mTransitionListener);
        }
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        removeLayoutTransitionListener();
        super.onDetachedFromWindow();
    }

    private void obtainAttr(@Nullable AttributeSet attrs) {
        if (null == attrs) {
            return;
        }

        final TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.ToolbarTabLayout);
        mBgColor = a.getColor(R.styleable.ToolbarTabLayout_ttlBgColor, DEFAULT_BG_COLOR);
        mIconWidth = a.getDimensionPixelSize(R.styleable.ToolbarTabLayout_ttlMainContainerWidth,
                DensityUtils.dp2px(getContext(), DEFAULT_MAIN_CONTAINER_WIDTH_DP));
        mIconHeight = a.getDimensionPixelSize(R.styleable.ToolbarTabLayout_ttlMainContainerHeight,
                DensityUtils.dp2px(getContext(), DEFAULT_MAIN_CONTAINER_HEIGHT_DP));
        mIndicatorWidth = a.getDimensionPixelSize(R.styleable.ToolbarTabLayout_ttlMainIndicatorWidth,
                DensityUtils.dp2px(getContext(), DEFAULT_INDICATOR_WIDTH_DP));
        mIndicatorHeight = a.getDimensionPixelSize(R.styleable.ToolbarTabLayout_ttlMainIndicatorHeight,
                DensityUtils.dp2px(getContext(), DEFAULT_INDICATOR_HEIGHT_DP));
        mIndicatorColor = a.getColor(R.styleable.ToolbarTabLayout_ttlMainIndicatorBgColor, DEFAULT_INDICATOR_COLOR);
        mIndicatorAnimDuration = a.getInt(R.styleable.ToolbarTabLayout_ttlLoadingDuration, DEFAULT_ANIM_DURATION);
        mIndicatorCornerRadius = a.getDimension(R.styleable.ToolbarTabLayout_ttlMainIndicatorCornerRadius,
                DensityUtils.dp2px(getContext(), DEFAULT_INDICATOR_CORNER_RADIUS_DP));
        mGroupDivision = (int) a.getDimension(R.styleable.ToolbarTabLayout_ttlGroupDivision,
                DensityUtils.dp2px(getContext(), DEFAULT_GROUP_DIVISION_DP));
        mTextsize = a.getDimensionPixelSize(R.styleable.ToolbarTabLayout_ttlTextSize,
                DensityUtils.dp2px(getContext(), DEFAULT_TEXT_SIZE_DP));
        mTextSelectColor = a.getColor(R.styleable.ToolbarTabLayout_ttlSelectedTextColor, DEFAULT_TEXT_COLOR_SELECTED);
        mTextUnselectColor = a.getColor(R.styleable.ToolbarTabLayout_ttlUnSelectedTextColor, DEFAULT_TEXT_COLOR);
        mCounterTextColor = a.getColor(R.styleable.ToolbarTabLayout_ttlCounterTextColor, DEFAULT_TEXT_COLOR_SELECTED);
        mCounterTextSize = a.getDimensionPixelSize(R.styleable.ToolbarTabLayout_ttlCounterTextSize,
                DensityUtils.dp2px(getContext(), DEFAULT_TEXT_SIZE_DP));
        mSosIconId = a.getResourceId(R.styleable.ToolbarTabLayout_ttlSosIcon, R.drawable.btn_sos);
        mSosText = a.getString(R.styleable.ToolbarTabLayout_ttlSosText);
        mOpenTransitionAnim = a.getBoolean(R.styleable.ToolbarTabLayout_ttlOpenTransitionAnim, true);

        a.recycle();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        DDLog.d(TAG, "onDraw");
        super.onDraw(canvas);

        int height = getHeight();
        int paddingLeft = getPaddingLeft();

        // draw background

        // Tab分组和SOS背景位置
        int mTabsContainerBgLeft;
        int mTabsContainerBgRight;
        if (mTabsContainer.getChildCount() > 0) {
            mTabsContainerBgLeft = mTabsContainer.getLeft() + mTabsContainer.getChildAt(0).getLeft()
                    + mTabsContainer.getChildAt(0).findViewById(R.id.fl_toolbar_main).getLeft();
            mTabsContainerBgRight = (int) (mTabsContainer.getLeft() +
                    mTabsContainer.getChildAt(mTabsContainer.getChildCount() - 1).getRight()
                    - (mTabsContainer.getChildAt(mTabsContainer.getChildCount() - 1).getWidth() - mIndicatorWidth) / 2);
        } else {
            mTabsContainerBgLeft = 0;
            mTabsContainerBgRight = 0;
        }

        mRectDrawable.setColor(mBgColor);
        mRectDrawable.setCornerRadius(mIndicatorCornerRadius);
        mRectDrawable.setBounds(mTabsContainerBgLeft, getPaddingTop(), mTabsContainerBgRight, (int) mIconHeight);
        mRectDrawable.draw(canvas);

        // sos背景
        if (mShowSos) {
            int mSosContainerBgLeft = mSosContainer.getChildAt(0).getLeft()
                    + mSosContainer.getChildAt(0).findViewById(R.id.fl_toolbar_main).getLeft();
            int mSosContainerBgRight = (int) (mSosContainer.getChildAt(0).getRight()
                    - (mSosContainer.getChildAt(0).getWidth() - mIndicatorWidth) / 2);

            mRectDrawable.setBounds(mSosContainerBgLeft, getPaddingTop(), mSosContainerBgRight, (int) mIconHeight);
            mRectDrawable.draw(canvas);
        }

        if (mIndicatorAnimEnable
                && DeviceStatusHelper.TOOLBAR_STATUS_NORMAL == mToolbarStatus) {
            if (mIsNeedCalculateIndicator) {
                calcIndicatorRect();
                // 不是动态布局中，不需要再次计算，会在动画中计算
                if (!mIsLayoutAnim) {
                    mIsNeedCalculateIndicator = false;
                }
            }
        } else {
            calcIndicatorRect();
        }

        //draw indicator line
        if (DeviceStatusHelper.TOOLBAR_STATUS_NORMAL == mToolbarStatus) {
            if (mIndicatorCornerRadius < 0 || mIndicatorCornerRadius > mIndicatorHeight / 2) {
                mIndicatorCornerRadius = mIndicatorHeight / 2;
            }
            mIndicatorDrawable.setColor(mIndicatorColor);
            mIndicatorDrawable.setBounds(paddingLeft + (int) mIndicatorMarginLeft + mIndicatorRect.left,
                    (int) mIndicatorMarginTop, (int) (paddingLeft + mIndicatorRect.right - mIndicatorMarginRight),
                    (int) (mIndicatorMarginTop + mIndicatorHeight));
            mIndicatorDrawable.setCornerRadius(mIndicatorCornerRadius);
            mIndicatorDrawable.draw(canvas);
        }
    }

    /**
     * 计算indicator的位置
     */
    private void calcIndicatorRect() {
        View currentTabView = mTabsContainer.getChildAt(this.mCurrentArmStatus);
        float left = currentTabView.getLeft();
        int parentLeft = mTabsContainer.getLeft();
        float indicatorLeft = parentLeft + left + (currentTabView.getWidth() - mIndicatorWidth) / 2;
        mIndicatorRect.left = (int) indicatorLeft;
        mIndicatorRect.right = (int) (mIndicatorRect.left + mIndicatorWidth);
    }

    /**
     * 初始化SOS item的数据
     */
    private void initSosData() {
        mSosContainer.removeAllViews();
        ToolbarTabItemView sosView = new ToolbarTabItemView(getContext());

        sosView.setOnTabIconClickListener(new ToolbarTabItemView.OnTabIconClickListener() {
            @Override
            public void onTabIconClick(View view) {
                if (DeviceStatusHelper.TOOLBAR_STATUS_DISABLE == mToolbarStatus) {
                    return;
                }

                if (DeviceStatusHelper.TOOLBAR_STATUS_OFFLINE_MODE == mToolbarStatus) {
                    DDLog.i(TAG, "离线模式点击");
                    toOfflineModeSos();
                    return;
                }

                // 正常模式下的点击
                toSos();
            }

            @Override
            public void onTabIconLongClick(View view) {
                if (null != mLongClickLister) {
                    mLongClickLister.onLongClick(view);
                }
            }
        });

        sosView.bindTabEntityWithTextColor(mSosEntiry, false,
                mTextsize, mTextSelectColor, mTextUnselectColor,
                mCounterTextSize, mCounterTextColor);
        sosView.setCountDownNum(10);
        sosView.setMainContainerDimen((int) mIconWidth, (int) mIconHeight);
        sosView.setTabUnSelect(false);

        mSosContainer.addView(sosView);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams((int) mIndicatorWidth + 20,
                LayoutParams.MATCH_PARENT, Gravity.END);
        sosView.setLayoutParams(lp);

    }

    /**
     * 未选中Item的图标是否使用选中的图标
     *
     * @return true: 使用
     */
    private boolean isUnSelectIconSelected() {
        return DeviceStatusHelper.TOOLBAR_STATUS_OFFLINE_MODE == mToolbarStatus;
    }

    /**
     * 关联数据
     *
     * @param tabEntitys
     */
    public void setTabData(ArrayList<CustomTabEntity> tabEntitys) {
        if (tabEntitys == null || tabEntitys.size() == 0) {
            throw new IllegalStateException("TabEntitys can not be NULL or EMPTY !");
        }

        this.mTabEntitys.clear();
        this.mTabEntitys.addAll(tabEntitys);

        notifyDataSetChanged();
    }

    /**
     * 更新数据
     */
    public void notifyDataSetChanged() {
        mTabsContainer.removeAllViews();
        this.mTabCount = mTabEntitys.size();
        ToolbarTabItemView tabView;
        for (int i = 0; i < mTabCount; i++) {
            tabView = new ToolbarTabItemView(getContext());
            tabView.setMainContainerDimen((int) mIconWidth, (int) mIconHeight);
            tabView.bindTabEntityWithTextColor(mTabEntitys.get(i), isUnSelectIconSelected(),
                    mTextsize, mTextSelectColor,
                    mTextUnselectColor, mCounterTextSize, mCounterTextColor);
            tabView.setClickIconTag(i);
            addTab(i, tabView);
        }

        updateTabStyles();
    }

    /**
     * 创建并添加tab
     */
    private void addTab(final int position, ToolbarTabItemView tabView) {
        tabView.setTabUnSelect(isUnSelectIconSelected());

        tabView.setOnTabIconClickListener(new ToolbarTabItemView.OnTabIconClickListener() {
            @Override
            public void onTabIconClick(View view) {
                int clickPosition = (Integer) view.getTag();

                if (DeviceStatusHelper.TOOLBAR_STATUS_DISABLE == mToolbarStatus) {
                    return;
                }

                // 不管点的是否同一个，都需要执行指令
                setCurrentTab(clickPosition);
                if (DeviceStatusHelper.TOOLBAR_STATUS_OFFLINE_MODE == mToolbarStatus) {
                    DDLog.i(TAG, "离线模式点击");
                    toOfflineModeArmCmd(clickPosition);
                } else {
                    DDLog.i(TAG, "正常情况下点击");
                    toArmCmd(clickPosition);
                }

                if (mCurrentArmStatus != clickPosition) {
                    if (mListener != null) {
                        mListener.onTabSelect(clickPosition);
                    }
                } else {
                    if (mListener != null) {
                        mListener.onTabReselect(clickPosition);
                    }
                }
            }

            @Override
            public void onTabIconLongClick(View view) {
                if (null != mLongClickLister) {
                    mLongClickLister.onLongClick(view);
                }
            }
        });


        /** 每一个Tab的布局参数 */
        LinearLayout.LayoutParams lp_tab = new LinearLayout.LayoutParams(0,
                LinearLayout.LayoutParams.MATCH_PARENT, 1.0f);
        mTabsContainer.addView(tabView, position, lp_tab);
    }

    //setter and getter
    private void setCurrentTab(int currentTab) {
        mLastTab = this.mCurrentArmStatus;
        this.mCurrentArmStatus = currentTab;
        updateTabSelection(currentTab);

        if (mIndicatorAnimEnable) {
            calcOffset();
        } else {
            invalidate();
        }
    }

    private void updateTabSelection(int position) {
        for (int i = 0; i < mTabCount; ++i) {

            ToolbarTabItemView tabView = (ToolbarTabItemView) mTabsContainer.getChildAt(i);

            if (DeviceStatusHelper.TOOLBAR_STATUS_DISABLE == mToolbarStatus) {
                tabView.setTabUnSelect(false);
                continue;
            }

            final boolean isSelect = i == position;
            if (isSelect) {
                tabView.setTabSelected();
            } else {
                tabView.setTabUnSelect(isUnSelectIconSelected());
            }
        }
    }

    private void calcOffset() {

        final View currentTabView = mTabsContainer.getChildAt(this.mCurrentArmStatus);
        int parentLeft = mTabsContainer.getLeft();
        mCurrentP.left = parentLeft + currentTabView.getLeft();
        mCurrentP.right = parentLeft + currentTabView.getRight();

        final View lastTabView = mTabsContainer.getChildAt(this.mLastTab);
        mLastP.left = parentLeft + lastTabView.getLeft();
        mLastP.right = parentLeft + lastTabView.getRight();

//        Log.d("AAA", "mLastP--->" + mLastP.left + "&" + mLastP.right);
//        Log.d("AAA", "mCurrentP--->" + mCurrentP.left + "&" + mCurrentP.right);
        if (mLastP.left == mCurrentP.left && mLastP.right == mCurrentP.right) {
            invalidate();
        } else {
            mValueAnimator.setObjectValues(mLastP, mCurrentP);
            mValueAnimator.setInterpolator(mInterpolator);

            if (mIndicatorAnimDuration < 0) {
                mIndicatorAnimDuration = DEFAULT_ANIM_DURATION;
            }
            mValueAnimator.setDuration(mIndicatorAnimDuration);
            mValueAnimator.start();
        }
    }

    private void updateTabStyles() {
        for (int i = 0; i < mTabCount; i++) {
            ToolbarTabItemView tabView = (ToolbarTabItemView) mTabsContainer.getChildAt(i);

            if (DeviceStatusHelper.TOOLBAR_STATUS_DISABLE == mToolbarStatus
                    || DeviceStatusHelper.TOOLBAR_STATUS_OFFLINE_MODE == mToolbarStatus) {
                DDLog.e(TAG, "当前Toolbar不可用，必须先调用setToolbarStatus修改Toolbar的状态");
                tabView.setTabUnSelect(isUnSelectIconSelected());
                continue;
            }

            if (i == mCurrentArmStatus) {
                tabView.setTabSelected();
            } else {
                tabView.setTabUnSelect(isUnSelectIconSelected());
            }
        }
    }

    public void setOnTabSelectListener(OnTabSelectListener listener) {
        this.mListener = listener;
    }

    public void setOnTabIconLongClickListener(OnLongClickListener listener) {
        this.mLongClickLister = listener;
    }

    /**
     * 修改SOS操作Item的可见状态
     *
     * @param visible 显示SOS操作图标
     */
    public void setSosVisible(boolean visible) {
        if (mShowSos == visible) {
            return;
        }

        mSosContainer.setVisibility(visible ? VISIBLE : GONE);
        mShowSos = visible;

        mIsNeedCalculateIndicator = true;
        requestLayout();
    }

    /**
     * 设置主机当前的布撤防状态
     *
     * @param deviceStatus
     */
    public void setDeviceStatus(@DeviceStatusHelper.DeviceArmStatus int deviceStatus) {
        DDLog.d(TAG, "setDeviceStatus, deviceStatus: " + deviceStatus);
        if (DeviceStatusHelper.TOOLBAR_STATUS_DISABLE == mToolbarStatus) {
            DDLog.e(TAG, "当前Toolbar不可用，必须先调用setToolbarStatus修改Toolbar的状态");
            return;
        }

        if (mCurrentArmStatus != deviceStatus) {
            setCurrentTab(deviceStatus);
        } else {
            if (DeviceStatusHelper.ARM_STATUS_ARM == mCurrentArmStatus) {
                getArmItemView().setTabSelected();
            } else if (DeviceStatusHelper.ARM_STATUS_DIS_ARM == mCurrentArmStatus) {
                getDisArmItemView().setTabSelected();
            } else if (DeviceStatusHelper.ARM_STATUS_HOME_ARM == mCurrentArmStatus) {
                getHomeArmItemView().setTabSelected();
            }

        }
    }

    /**
     * 标记Toolbar不能操作
     */
    public void setToolbarStatusDisable() {
        setToolbarStatus(DeviceStatusHelper.TOOLBAR_STATUS_DISABLE, mCurrentArmStatus);
    }


    public void setToolbarStatusEnable(boolean normal,
                                       @DeviceStatusHelper.DeviceArmStatus int deviceStatus) {
        setToolbarStatus(normal
                        ? DeviceStatusHelper.TOOLBAR_STATUS_NORMAL :
                        DeviceStatusHelper.TOOLBAR_STATUS_OFFLINE_MODE,
                deviceStatus);
    }

    /**
     * 修改Toolbar的状态
     *
     * @param toolbarStatus
     */
    private void setToolbarStatus(@DeviceStatusHelper.ToolbarArmStatus int toolbarStatus,
                                  @DeviceStatusHelper.DeviceArmStatus int deviceStatus) {
        this.mToolbarStatus = toolbarStatus;
        this.mCurrentArmStatus = deviceStatus;

//        setAlpha(DeviceStatusHelper.TOOLBAR_STATUS_DISABLE == toolbarStatus
//                ? 0.8f : 1.0f);
        mIsNeedCalculateIndicator = true;
        updateTabStyles();

        invalidate();
    }

    @Override
    public void onAnimationUpdate(ValueAnimator animation) {
//        DDLog.d(TAG, "onAnimationUpdate");
        View currentTabView = mTabsContainer.getChildAt(this.mCurrentArmStatus);
        IndicatorPoint p = (IndicatorPoint) animation.getAnimatedValue();

        float indicatorLeft = p.left + (currentTabView.getWidth() - mIndicatorWidth) / 2;
//        DDLog.d(TAG, "onAnimationUpdate, left: " + indicatorLeft);
        mIndicatorRect.left = (int) indicatorLeft;
        mIndicatorRect.right = (int) (mIndicatorRect.left + mIndicatorWidth);

        invalidate();
    }

    @Override
    public void onAnimationStart(Animator animation) {
//        DDLog.d(TAG, "onAnimationStart");
    }

    @Override
    public void onAnimationEnd(Animator animation) {
//        DDLog.d(TAG, "onAnimationEnd");
    }

    @Override
    public void onAnimationCancel(Animator animation) {

    }

    @Override
    public void onAnimationRepeat(Animator animation) {

    }

    class IndicatorPoint {
        public float left;
        public float right;
    }

    private IndicatorPoint mCurrentP = new IndicatorPoint();
    private IndicatorPoint mLastP = new IndicatorPoint();

    class PointEvaluator implements TypeEvaluator<IndicatorPoint> {
        @Override
        public IndicatorPoint evaluate(float fraction, IndicatorPoint startValue, IndicatorPoint endValue) {
            float left = startValue.left + fraction * (endValue.left - startValue.left);
            float right = startValue.right + fraction * (endValue.right - startValue.right);
            IndicatorPoint point = new IndicatorPoint();
            point.left = left;
            point.right = right;
            return point;
        }
    }

    /**
     * 可用模式下点击Icon指令逻辑处理
     *
     * @param index
     */
    private void toArmCmd(int index) {
        DDLog.d(TAG, "toArmCmd, index: " + index);
        switch (index) {
            case 0:
                toArm(true);
                break;
            case 1:
                toDisArm(true);
                break;
            case 2:
                toHomeArm(true);
                break;
            default:
                break;
        }
    }

    /**
     * 离线模式模式下点击Icon指令逻辑处理
     *
     * @param index
     */
    private void toOfflineModeArmCmd(int index) {
        DDLog.d(TAG, "toOfflineModeArmCmd, index: " + index);
        switch (index) {
            case 0:
                toOfflineModeArm();
                break;
            case 1:
                toOfflineModeDisArm();
                break;
            case 2:
                toOfflineModeHomeArm();
                break;
            default:
                break;
        }
    }

    /**
     * 更新语言
     */
    public void updateUi() {
        for (int i = 0; i < mTabsContainer.getChildCount(); i++) {
            ToolbarTabItemView view = (ToolbarTabItemView) mTabsContainer.getChildAt(i);
            view.updateUI();
        }

        for (int i = 0; i < mSosContainer.getChildCount(); i++) {
            ToolbarTabItemView view = (ToolbarTabItemView) mSosContainer.getChildAt(i);
            view.updateUI();
        }
    }

    public void toArm() {
        toArm(false);
    }

    public void toArm(final boolean clicked) {
        DDLog.d(TAG, "toArm");
        Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentDeviceId());
        if (null == device) {
            return;
        }

        initArm(false, false);
        initArmLoadAnim(false);
        device.submit(PanelParamsHelper.operationArm(PanelParamsHelper.OPERATE_ARM_ARM, true));

        notifyOperateListener(DeviceStatusHelper.ARM_STATUS_ARM, clicked);
    }

    public void toDisArm() {
        toDisArm(false);
    }

    /**
     * 点击了DisArm
     */
    public void toDisArm(final boolean clicked) {
        DDLog.d(TAG, "toDisArm");
        Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentDeviceId());
        if (null == device) {
            return;
        }
        initDisarm(false);
        initDisarmLoadAnim();
        device.submit(PanelParamsHelper.operationArm(PanelParamsHelper.OPERATE_ARM_DISARM, true));

        notifyOperateListener(DeviceStatusHelper.ARM_STATUS_DIS_ARM, clicked);
    }

    public void toHomeArm() {
        toHomeArm(false);
    }

    public void toHomeArm(final boolean clicked) {
        DDLog.d(TAG, "toHomeArm");
        Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentDeviceId());
        if (null == device) {
            return;
        }
        initHomeArm(false);
        initHomeArmLoadAnim();
        device.submit(PanelParamsHelper.operationArm(PanelParamsHelper.OPERATE_ARM_HOME_ARM, true));

        notifyOperateListener(DeviceStatusHelper.ARM_STATUS_HOME_ARM, clicked);
    }

    private void notifyOperateListener(@DeviceStatusHelper.DeviceArmStatus final int operation, final boolean clicked) {
        if (null != mOperateListener) {
            mOperateListener.onOperated(operation, clicked);
        }
    }


    public void initHomeArm() {
        initHomeArm(true);
    }

    public void initHomeArm(final boolean notify) {
        setDeviceStatus(DeviceStatusHelper.ARM_STATUS_HOME_ARM);
        setAllArmClickEnable(true);
        if (notify) {
            notifyOperateListener(DeviceStatusHelper.ARM_STATUS_HOME_ARM, false);
        }
    }

    public void initDisarm() {
        initDisarm(true);
    }

    public void initDisarm(final boolean notify) {
        setDeviceStatus(DeviceStatusHelper.ARM_STATUS_DIS_ARM);
        setAllArmClickEnable(true);
        if (notify) {
            notifyOperateListener(DeviceStatusHelper.ARM_STATUS_DIS_ARM, false);
        }
    }

    public void initArm(boolean isNeedToPlaySound, boolean isNeedToAnim, final boolean notify) {
        DDLog.d(TAG, "initArm");
        setDeviceStatus(DeviceStatusHelper.ARM_STATUS_ARM);
        setAllArmClickEnable(true);
        if (isNeedToPlaySound) {
            playSound(1, 1, 1, 0, 1, 1);
        }
        if (notify) {
            notifyOperateListener(DeviceStatusHelper.ARM_STATUS_ARM, false);
        }
    }

    public void initArm(boolean isNeedToPlaySound) {
        initArm(isNeedToPlaySound, true);
    }

    public void initArm(boolean isNeedToPlaySound, final boolean notify) {
        DDLog.i(TAG, "initArm");
        setDeviceStatus(DeviceStatusHelper.ARM_STATUS_ARM);
        setAllArmClickEnable(true);
        if (isNeedToPlaySound) {
            playSound(1, 1, 1, 0, 1, 1);
        }
        if (notify) {
            notifyOperateListener(DeviceStatusHelper.ARM_STATUS_ARM, false);
        }
    }


    public void initArmLoadAnim(final boolean isTimeCountLoading) {
        DDLog.d(TAG, "initArmLoadAnim");
        if (!isTimeCountLoading) {
            getArmItemView().setTabLoading();
        } else {
            final int countDownTime = DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getExitdelay();
            getArmItemView().setTabCountingDown(countDownTime, false);
        }
        setAllArmClickEnable(isTimeCountLoading);
    }

    public void initArmLoadAnim(final boolean isTimeCountLoading, AnimationListener.Stop stop) {
        DDLog.d(TAG, "initArmLoadAnim");
        if (!isTimeCountLoading) {
            getArmItemView().setTabLoading();
        } else {
            final int countDownTime = DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getExitdelay();
            getArmItemView().setTabCountingDown(countDownTime, false);
        }
        setAllArmClickEnable(isTimeCountLoading);
    }

    public void initArmLoadAnim(final int exitDelay, AnimationListener.Stop stop) {
        DDLog.d(TAG, "initArmLoadAnim");
        if (exitDelay <= 0) {
            getArmItemView().setTabLoading();
        } else {
            getArmItemView().setTabCountingDown(exitDelay, false);
        }
        setAllArmClickEnable(exitDelay > 0);
    }

    public void updateArmDelayCountDown(int countDownNum) {
        getArmItemView().setTabCountingDown(countDownNum, false);
    }

    public void initDisarmLoadAnim() {
        DDLog.d(TAG, "initDisarmLoadAnim");
        getDisArmItemView().setTabLoading();
        setAllArmClickEnable(false);
    }

    public void initHomeArmLoadAnim() {
        DDLog.d(TAG, "initHomeArmLoadAnim");
        getHomeArmItemView().setTabLoading();
        setAllArmClickEnable(false);
    }

    public void resetArm(boolean isNeedToPlaySound) {
        DDLog.d(TAG, "resetArm, isNeedToPlaySound: " + isNeedToPlaySound);
        setDeviceStatus(DeviceStatusHelper.ARM_STATUS_ARM);
        setAllArmClickEnable(true);
        if (isNeedToPlaySound) {
            playSound(1, 1, 1, 0, 1, 1);
        }
        notifyOperateListener(DeviceStatusHelper.ARM_STATUS_ARM, false);
    }

    /**
     * 设置Arm/DisArm/HomeArm的可点击状态
     *
     * @param enable true: 可点击
     */
    public void setAllArmClickEnable(boolean enable) {
        DDLog.d(TAG, "setArmStateEnable, enable: " + enable);
        ToolbarTabItemView tabView;
        for (int i = 0; i < mTabCount; ++i) {
            tabView = (ToolbarTabItemView) mTabsContainer.getChildAt(i);
            tabView.setClickIconEnable(enable);
        }
    }

    /**
     * 设置SOS按钮的可点击状态
     *
     * @param enable
     */
    public void setSosClickEnable(boolean enable) {
        DDLog.d(TAG, "setSosClickEnable, enable: " + enable);
        getSosItemView().setClickIconEnable(enable);
    }

    public ToolbarTabItemView getArmItemView() {
        return (ToolbarTabItemView) mTabsContainer.getChildAt(0);
    }

    public ToolbarTabItemView getDisArmItemView() {
        return (ToolbarTabItemView) mTabsContainer.getChildAt(1);
    }

    public ToolbarTabItemView getHomeArmItemView() {
        return (ToolbarTabItemView) mTabsContainer.getChildAt(2);
    }

    public ToolbarTabItemView getSosItemView() {
        return (ToolbarTabItemView) mSosContainer.getChildAt(0);
    }

    public void playSound(int soundID, float leftVolume, float rightVolume,
                          int priority, int loop, float rate) {
        DDLog.d(TAG, "playSound");
        id = soundPool.play(soundID, leftVolume, rightVolume, priority, loop, rate);
    }

    public String getMyMessageId() {
        return null == mMyMessageId ? "" : mMyMessageId;
    }

    public void resetSos() {
        DDLog.d(TAG, "resetSos");
        getSosItemView().setTabUnSelect(false);
        getSosItemView().setClickIconEnable(true);
    }

    /**
     * 点击了SOS按钮
     */
    public void toSos() {
        DDLog.d(TAG, "toSos");
        Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentDeviceId());
        if (null == device) {
            return;
        }

        getSosItemView().setClickIconEnable(false);
        getSosItemView().setTabLoading();
        device.submit(PanelParamsHelper.operationSos());
    }

    // ***************************** 离线模式操作 ******************** //

    //短信内容：{"userid": "jin001", "gmtime": 1584341556244140032, "cmd": "TASK_ARM"}
    //rc4后，base64
    private void goSystemSendSmsPage(String cmd) {
        final String userId = null != DinSDK.getUserInstance().getUser() ?
                DinSDK.getUserInstance().getUser().getUid() : "";
        long time = System.currentTimeMillis() * 1000000;
//        Log.d(TAG, "goSystemSendSmsPage: " + time);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userid", null == userId ? "" : userId);
        jsonObject.put("cmd", "TASK_" + cmd);
        jsonObject.put("gmtime", time);
        String format = jsonObject.toJSONString();
//        Log.d(TAG, "goSystemSendSmsPage-->info: " + format);
        String sc = DDSecretUtil.getSC(format);
//        Log.d(TAG, "goSystemSendSmsPage-->sc: " + sc);
        String base = Base64.encodeToString(HexUtil.hexStringToBytes(sc), Base64.NO_WRAP);
//        Log.d(TAG, "goSystemSendSmsPage-->base:" + base);
        String devicePhoneDbKey = DBKey.RESTRICT_DEVICE_PHONE
                + CommonDataUtil.getInstance().getCurrentDeviceId();
        // 从本地读取上一次保存的手机号
        String defaultPhone = DBUtil.Str(devicePhoneDbKey);
        DDSystemUtil.goSystemSendSmsPage(getContext(), defaultPhone, base);
    }

    /**
     * 离线模式下点击了Arm
     */
    public void toOfflineModeArm() {
        DDLog.d(TAG, "toOfflineModeArm");
        goSystemSendSmsPage("ARM");
    }

    /**
     * 离线模式下点击了DisArm
     */
    public void toOfflineModeDisArm() {
        DDLog.d(TAG, "toOfflineModeDisArm");
        goSystemSendSmsPage("DISARM");
    }

    /**
     * 离线模式下点击了HomeArm
     */
    public void toOfflineModeHomeArm() {
        DDLog.d(TAG, "toOfflineModeHomeArm");
        goSystemSendSmsPage("HOMEARM");
    }

    /**
     * 离线模式下点击了Sos
     */
    public void toOfflineModeSos() {
        DDLog.d(TAG, "toOfflineModeSos");
        goSystemSendSmsPage("SOS");
    }

    public void setOperateListener(OnOperateListener l) {
        this.mOperateListener = l;
    }

    public void setOperateLoadingState(int loadingState) {
        switch (loadingState) {
            case DeviceStatusHelper.ARM_STATUS_ARM:
                initArm(false, false);
                initArmLoadAnim(false);
                break;
            case DeviceStatusHelper.ARM_STATUS_DIS_ARM:
                initDisarm(false);
                initDisarmLoadAnim();
                break;
            case DeviceStatusHelper.ARM_STATUS_HOME_ARM:
                initHomeArm(false);
                initHomeArmLoadAnim();
                break;
            default:
                break;
        }
    }

    public void setOperateLoadingArmDelay(int second) {
        if (second > 0) {
            getArmItemView().setTabCountingDown(second, false);
            setAllArmClickEnable(true);
        }
    }

    /**
     * 点击操作成功后回调
     */
    public interface OnOperateListener {
        /**
         * @param operation 布撤防操作
         * @param clicked   是否用户点击 true:用户点击
         */
        void onOperated(@DeviceStatusHelper.DeviceArmStatus final int operation, final boolean clicked);
    }
}

