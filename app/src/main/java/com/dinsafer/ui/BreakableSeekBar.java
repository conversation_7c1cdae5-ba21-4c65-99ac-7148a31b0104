package com.dinsafer.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.util.DDLog;

import java.math.BigDecimal;


/**
 * <AUTHOR> WZH
 * @date : 28/9/2023 上午 11:16
 * @description :
 */
public class BreakableSeekBar extends View {

    private static final String TAG = BreakableSeekBar.class.getSimpleName();

    private Context mContext;
    private int mWidth;
    private int mPaddingLeft;
    private int mPaddingTop;
    private int mPaddingRight;
    private int mPaddingBottom;

    private float mMin;
    private float mMax;
    private float mProgress;
    private int mProgressColor;
    private int mThumbRadius;
    private int mThumbSpace;
    private float mThumbProgressSpace;  // 滑块和进度条的间距
    private int mOuterThumbColor;
    private int mInnerThumbColor;
    private float mBPRadius;  // 进度条和进度条背景圆角
    private boolean isThumbOverflow;  // 滑块是否一半溢出进度条
    private int mBgColor;
    private float mThumbCenterX;
    private boolean isThumbOnDragging;
    private float cy;
    private float dx;
    private float mThumbOffset;
    private Path mClipPath;
    private Path mBrokenClipPath1;
    private Path mBrokenClipPath2;

    private float mTrackLength;
    private float mLeft;
    private float mRight;
    private float mDelta;
    private RectF mBackgroundRectF;
    private RectF mBrokenRectF1;
    private RectF mBrokenRectF2;
    private RectF mProgressRectF;
    private Paint mBgPaint;


    private boolean isBreakable;
    private float mBreakStart;
    private float mBreakEnd;
    private int mBreakCellWidth = 10;
    private float mBreakCellSpace;
    private int mBreakCellHeight = 50;
    private int mBreakColor;
    private Path mBreakPath;
    private float mTopLeftX;
    private float mBottomLeftX;
    private float mTopRightX;
    private float mBottomRightX;
    private Paint mBreakPaint;

    private Paint mScalePaint;
    private int mScaleColor;
    private float mScaleHeight;
    private float mScaleOffset;
    private Paint mScaleTextPaint;
    private Rect mScaleTextRect;
    private float mScaleTextSize;
    private int mScaleTextColor;
    private int mTextScaleSpace;

    private Paint mProgressPaint;
    private Paint mThumbPaint;

    private float mMultiple = 1.0f;

    public BreakableSeekBar(Context context) {
        this(context, null);
    }

    public BreakableSeekBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BreakableSeekBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initAttr(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initAttr(Context context, AttributeSet attrs, int defStyleAttr) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.BreakableSeekBar, defStyleAttr, 0);
        mMin = ta.getFloat(R.styleable.BreakableSeekBar_bsb_min_val, 0.0f);
        mMax = ta.getFloat(R.styleable.BreakableSeekBar_bsb_max_val, 100f);
        mProgress = ta.getFloat(R.styleable.BreakableSeekBar_bsb_progress_val, 0.0f);
        mProgressColor = ta.getColor(R.styleable.BreakableSeekBar_bsb_progress_color,
                ContextCompat.getColor(context, R.color.color_brand_primary));
        mOuterThumbColor = ta.getColor(R.styleable.BreakableSeekBar_bsb_outer_thumb_color,
                ContextCompat.getColor(context, R.color.color_brand_primary));
        mInnerThumbColor = ta.getColor(R.styleable.BreakableSeekBar_bsb_inner_thumb_color,
                ContextCompat.getColor(context, R.color.white));
        mThumbRadius = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_thumb_radius_val, 9);
        mThumbSpace = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_thumb_space, 9);
        mThumbProgressSpace = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_thumb_progress_space, 0);
        mBPRadius = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_bp_radius, mThumbRadius);
        mBgColor = ta.getColor(R.styleable.BreakableSeekBar_bsb_bg_color,
                ContextCompat.getColor(context, R.color.color_brand_dark_03));
        isThumbOverflow = ta.getBoolean(R.styleable.BreakableSeekBar_bsb_is_thumb_overflow, false);
        isBreakable = ta.getBoolean(R.styleable.BreakableSeekBar_bsb_is_breakable, false);
        mBreakStart = ta.getFloat(R.styleable.BreakableSeekBar_bsb_break_start, -0.90f);
        mBreakEnd = ta.getFloat(R.styleable.BreakableSeekBar_bsb_break_end, 0.90f);
        mBreakCellWidth = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_break_cell_width, 5);
        mBreakCellHeight = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_break_cell_height, 4);
        mBreakCellSpace = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_break_cell_space, 4);
        mBreakColor = ta.getColor(R.styleable.BreakableSeekBar_bsb_break_cell_color,
                ContextCompat.getColor(context, R.color.color_white_03));
        mScaleColor = ta.getColor(R.styleable.BreakableSeekBar_bsb_scale_color,
                ContextCompat.getColor(context, R.color.color_white_03));
        mScaleTextSize = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_scale_text_size, DensityUtil.sp2px(context, 10));
        mScaleTextColor = ta.getColor(R.styleable.BreakableSeekBar_bsb_scale_color,
                ContextCompat.getColor(context, R.color.color_white_02));
        mTextScaleSpace = ta.getDimensionPixelSize(R.styleable.BreakableSeekBar_bsb_text_scale_space, DensityUtil.sp2px(context, 2));
        ta.recycle();
    }

    private void initView(Context context) {
        mBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mBgPaint.setStyle(Paint.Style.FILL);
        mBgPaint.setColor(mBgColor);

        mBreakPath = new Path();
        mBreakPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mBreakPaint.setStyle(Paint.Style.FILL);
        mBreakPaint.setColor(mBreakColor);

        mScalePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mScalePaint.setColor(mScaleColor);
        mScalePaint.setStrokeWidth(2);
        mScaleTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mScaleTextPaint.setTextSize(mScaleTextSize);
        mScaleTextPaint.setColor(mScaleTextColor);
        mScaleTextRect = new Rect();

        mProgressRectF = new RectF();
        mProgressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mThumbPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

        checkVal();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        mWidth = getMeasuredWidth();
        mPaddingLeft = getPaddingLeft();
        mPaddingTop = getPaddingTop();
        mPaddingRight = getPaddingRight();
        mPaddingBottom = getPaddingBottom();

        int mode = MeasureSpec.getMode(heightMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);
        mScaleTextPaint.getTextBounds("0", 0, 1, mScaleTextRect);
        if (mode == MeasureSpec.UNSPECIFIED || mode == MeasureSpec.AT_MOST) {
            height = mPaddingTop + mThumbRadius * 2 + mScaleTextRect.height() + mTextScaleSpace + mPaddingBottom;
        }
        setMeasuredDimension(mWidth, height);

        cy = mPaddingTop + mThumbRadius;

        mThumbOffset = isThumbOverflow ? mThumbRadius : 0;
        mScaleOffset = isThumbOverflow ? 0 : mThumbRadius;
        mLeft = mPaddingLeft + mThumbRadius;
        mRight = mWidth - mPaddingRight - mThumbRadius;
        mTrackLength = mRight - mLeft;

        if (isBreakable) {
            if (mBrokenRectF1 == null) {
                mBrokenRectF1 = new RectF();
            }

            mBrokenRectF1.left = mPaddingLeft + mThumbOffset;
            mBrokenRectF1.top = cy - mThumbRadius + mThumbProgressSpace;
            float percent1 = (mBreakStart - mMin) / (mMax - mMin);
            mBrokenRectF1.right = mPaddingLeft + mThumbRadius + mTrackLength * percent1;
            mBrokenRectF1.bottom = cy + mThumbRadius - mThumbProgressSpace;
            if (mBrokenClipPath1 == null) {
                mBrokenClipPath1 = new Path();
            }
            mBrokenClipPath1.reset();
            mBrokenClipPath1.addRoundRect(mBrokenRectF1,
                    mBPRadius, mBPRadius, Path.Direction.CW);

            if (mBrokenRectF2 == null) {
                mBrokenRectF2 = new RectF();
            }
            float percent2 = (mBreakEnd - mMin) / (mMax - mMin);
            mBrokenRectF2.left = mTrackLength * percent2 + mThumbRadius;
            mBrokenRectF2.top = cy - mThumbRadius + mThumbProgressSpace;
            mBrokenRectF2.right = getMeasuredWidth() - mPaddingRight - mThumbOffset;
            mBrokenRectF2.bottom = cy + mThumbRadius - mThumbProgressSpace;
            if (mBrokenClipPath2 == null) {
                mBrokenClipPath2 = new Path();
            }
            mBrokenClipPath2.addRoundRect(mBrokenRectF2,
                    mBPRadius, mBPRadius, Path.Direction.CW);
        }
        if (mBackgroundRectF == null) {
            mBackgroundRectF = new RectF();
        }
        mBackgroundRectF.left = mPaddingLeft + mThumbOffset;
        mBackgroundRectF.top = cy - mThumbRadius + mThumbProgressSpace;
        mBackgroundRectF.right = getMeasuredWidth() - mPaddingRight - mThumbOffset;
        mBackgroundRectF.bottom = cy + mThumbRadius - mThumbProgressSpace;
        mScaleHeight = mThumbRadius - (mBackgroundRectF.bottom - mBackgroundRectF.top) / 2f;
        if (mClipPath == null) {
            mClipPath = new Path();
        }
        mClipPath.reset();
        mClipPath.addRoundRect(mBackgroundRectF,
                mBPRadius, mBPRadius, Path.Direction.CW);
    }

    private void checkVal() {
        if (mMin == mMax) {
            mMin = 0.0f;
            mMax = 100.0f;
        }
        if (mMin > mMax) {
            float tmp = mMax;
            mMax = mMin;
            mMin = tmp;
        }
        if (mProgress < mMin) {
            mProgress = mMin;
        }
        if (mProgress > mMax) {
            mProgress = mMax;
        }
        mDelta = mMax - mMin;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (isBreakable) {
            drawBreak(canvas);
            if (mBrokenRectF1 != null) {
                canvas.drawRoundRect(mBrokenRectF1, mBPRadius, mBPRadius, mBgPaint);
            }
            if (mBrokenRectF2 != null) {
                canvas.drawRoundRect(mBrokenRectF2, mBPRadius, mBPRadius, mBgPaint);
            }
        } else {
            if (mBackgroundRectF != null) {
                canvas.drawRoundRect(mBackgroundRectF, mBPRadius, mBPRadius, mBgPaint);
            }
        }
        drawScale(canvas);

        float xLeft = mLeft;
        xLeft += mThumbRadius;

        if (!isThumbOnDragging) {
            mThumbCenterX = xLeft + mTrackLength / mDelta * (mProgress - mMin) - mThumbRadius;
        }

        if (mThumbCenterX > mLeft) {
            mProgressPaint.setColor(mProgressColor);
            mProgressRectF.left = mPaddingLeft + mThumbOffset;
            mProgressRectF.top = mBackgroundRectF.top;
            mProgressRectF.right = mProgress == mMin ? mPaddingLeft : mThumbCenterX;
            mProgressRectF.bottom = mBackgroundRectF.bottom;
            canvas.save();
            canvas.clipPath(mClipPath);
            canvas.drawRoundRect(mProgressRectF, mBPRadius, mBPRadius, mProgressPaint);
            canvas.restore();
        }

        mThumbPaint.setColor(mOuterThumbColor);
        canvas.drawCircle(mThumbCenterX, cy, mThumbRadius, mThumbPaint);
        mThumbPaint.setColor(mInnerThumbColor);
        canvas.drawCircle(mThumbCenterX, cy, mThumbRadius - mThumbSpace, mThumbPaint);
    }

    private void drawScale(Canvas canvas) {
//        canvas.drawLine(mBackgroundRectF.left + mScaleOffset, mBackgroundRectF.bottom, mBackgroundRectF.left + mScaleOffset, mBackgroundRectF.bottom + mScaleHeight, mScalePaint);
//        canvas.drawLine(mBackgroundRectF.right - mScaleOffset, mBackgroundRectF.bottom, mBackgroundRectF.right - mScaleOffset, mBackgroundRectF.bottom + mScaleHeight, mScalePaint);
//        String min = String.valueOf(mMin / mMultiple);
        String min = dealDecimal(mMin);
        mScaleTextPaint.getTextBounds(min, 0, min.length(), mScaleTextRect);
        float minX = mBackgroundRectF.left + mScaleOffset - mScaleTextRect.width() / 2f;
        if (minX < mPaddingLeft) {
            minX = mPaddingLeft;
        }
        canvas.drawText(min, minX, mBackgroundRectF.bottom + mScaleHeight + mScaleTextRect.height() + mTextScaleSpace, mScaleTextPaint);

//        String max = String.valueOf(mMax / mMultiple);
        String max = dealDecimal(mMax);
        mScaleTextPaint.getTextBounds(max, 0, max.length(), mScaleTextRect);
        float maxX = mBackgroundRectF.right - mScaleOffset - mScaleTextRect.width() / 2f;
        if (maxX + mScaleTextRect.width() > mWidth - mPaddingRight) {
            maxX = mWidth - mPaddingRight - mScaleTextRect.width() - 2;
        }
        canvas.drawText(max, maxX, mBackgroundRectF.bottom + mScaleHeight + mScaleTextRect.height() + mTextScaleSpace, mScaleTextPaint);
        if (isBreakable) {
//            canvas.drawLine(mBrokenRectF1.right, mBackgroundRectF.bottom, mBrokenRectF1.right, mBackgroundRectF.bottom + mScaleHeight, mScalePaint);
//            canvas.drawLine(mBrokenRectF2.left, mBackgroundRectF.bottom, mBrokenRectF2.left, mBackgroundRectF.bottom + mScaleHeight, mScalePaint);
            String breakStart = String.valueOf(mBreakStart);
            mScaleTextPaint.getTextBounds(breakStart, 0, breakStart.length(), mScaleTextRect);
            canvas.drawText(breakStart, mBrokenRectF1.right - mScaleTextRect.width() / 2f, mBackgroundRectF.bottom + mScaleHeight + mScaleTextRect.height() + mTextScaleSpace, mScaleTextPaint);

            String breakEnd = String.valueOf(mBreakEnd);
            mScaleTextPaint.getTextBounds(breakEnd, 0, breakEnd.length(), mScaleTextRect);
            canvas.drawText(breakEnd, mBrokenRectF2.left - mScaleTextRect.width() / 2f, mBackgroundRectF.bottom + mScaleHeight + mScaleTextRect.height() + mTextScaleSpace, mScaleTextPaint);
        }
    }

    private void drawBreak(Canvas canvas) {
        int count = (int) ((mWidth - mBPRadius * 2) / mBreakCellWidth);
        canvas.save();
        canvas.clipPath(mClipPath);
        for (int i = 0; i < count; i++) {
            mBreakPath.reset();
            if (i == 0) {
                mTopLeftX = mLeft + mBreakCellWidth * 2;
                mBottomLeftX = mLeft;
                mBottomRightX = mBottomLeftX + mBreakCellWidth;
                mTopRightX = mTopLeftX + mBreakCellWidth;

            } else {
                mTopLeftX = mTopLeftX + mBreakCellSpace + mBreakCellWidth;
                mBottomLeftX = mBottomLeftX + mBreakCellSpace + mBreakCellWidth;
                mBottomRightX = mBottomRightX + mBreakCellSpace + mBreakCellWidth;
                mTopRightX = mTopRightX + mBreakCellSpace + mBreakCellWidth;
            }
            mBreakPath.moveTo(mTopLeftX, mBackgroundRectF.top);
            mBreakPath.lineTo(mBottomLeftX, mBackgroundRectF.bottom);
            mBreakPath.lineTo(mBottomRightX, mBackgroundRectF.bottom);
            mBreakPath.lineTo(mTopRightX, mBackgroundRectF.top);
            canvas.drawPath(mBreakPath, mBreakPaint);
        }
        canvas.restore();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                performClick();
                isThumbOnDragging = isThumbTouched(event);
                if (isThumbOnDragging) {
                    getParent().requestDisallowInterceptTouchEvent(true);
                    invalidate();
                }
                dx = mThumbCenterX - event.getX();

                break;
            case MotionEvent.ACTION_MOVE:
                if (isThumbOnDragging) {
                    boolean flag = true;

                    mThumbCenterX = event.getX() + dx;
                    if (mThumbCenterX < mLeft) {
                        mThumbCenterX = mLeft;
                    }
                    if (mThumbCenterX > mRight) {
                        mThumbCenterX = mRight;
                    }

                    if (flag) {
                        mProgress = calculateProgress();
                        invalidate();
                        if (seekBarChangeListener != null) {
                            seekBarChangeListener.onProgressChanged(this, getProgress(), getProgressFloat(), true);
                        }
                    }
                }

                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(false);
                if (isBreakable) {
                    if (mProgress >= mBreakStart && mProgress <= mBreakEnd) {
                        if (mProgress - mBreakStart <= mBreakEnd - mProgress) {
                            mProgress = mBreakStart;
                        } else {
                            mProgress = mBreakEnd;
                        }
                    }
                }
                isThumbOnDragging = false;
                invalidate();
                break;
        }

        return isThumbOnDragging || super.onTouchEvent(event);
    }

    private boolean isThumbTouched(MotionEvent event) {
        if (!isEnabled())
            return false;

        float distance = mTrackLength / mDelta * (mProgress - mMin);
        float x = mLeft + distance;
        float y = getMeasuredHeight() / 2f;
        return (event.getX() - x) * (event.getX() - x) + (event.getY() - y) * (event.getY() - y)
                <= (mLeft + DensityUtil.dp2px(mContext, 8)) * (mLeft + DensityUtil.dp2px(mContext, 8));
    }

    private float calculateProgress() {
        return (mThumbCenterX - mLeft) * mDelta / mTrackLength + mMin;
    }

    public int getProgress() {
        return Math.round(processProgress());
    }

    public float getProgressFloat() {
        return formatFloat(processProgress());
    }

    private float processProgress() {
        final float progress = mProgress;

        return progress;
    }

    private float formatFloat(float value) {
        BigDecimal bigDecimal = BigDecimal.valueOf(value);
        return bigDecimal.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
    }

    public void setProgress(float progress) {
        if (progress < mMin) progress = mMin;
        if (progress > mMax) progress = mMax;
        mProgress = progress;
        postInvalidate();
    }

    public void setMaxProgress(float maxProgress) {
        mMax = maxProgress;
    }

    public void resetData(float min, float max, float progress, float multiple) {
        this.mMin = min;
        this.mMax = max;
        this.mProgress = progress;
        this.mMultiple = multiple;
        checkVal();
        invalidate();
    }

    public void resetData(float min, float max, float breakStart, float breakEnd, float progress) {
        this.mMin = min;
        this.mMax = max;
        this.mBreakStart = breakStart;
        this.mBreakEnd = breakEnd;
        this.mProgress = progress;
        checkVal();
        invalidate();
    }

    @SuppressLint("DefaultLocale")
    private String dealDecimal(float val) {
        String result = "";
        if (mMultiple == 10f) {
            result = String.format("%.1f", val / mMultiple);
        } else if (mMultiple == 100f) {
            result = String.format("%.2f", val / mMultiple);
        } else {
            result = String.format("%.0f", val);
        }
        return result;
    }

    public boolean isThumbOnDragging() {
        return isThumbOnDragging;
    }

    private OnSeekBarChangeListener seekBarChangeListener;

    public void setSeekBarChangeListener(OnSeekBarChangeListener seekBarChangeListener) {
        this.seekBarChangeListener = seekBarChangeListener;
    }

    public interface OnSeekBarChangeListener {
        void onProgressChanged(BreakableSeekBar breakableSeekBar, int progress, float progressFloat, boolean fromUser);
    }
}
