package com.dinsafer.ui.timeruler.menu;

import android.content.Context;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.annotation.CallSuper;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/9/8 11:47 上午
 */
public abstract class RecordMeneAnimationBaseView<V extends ViewDataBinding> extends FrameLayout
        implements IRecordAnimationMenu {

    protected V mBinding;
    protected OnRecordMenuClickListener mMenuClickListener;
    protected final RecordMenuAnimationHelper animationHelper;
    protected RecordMenuOpenStateListener menuOpenStateListener;
    private boolean menuVisible = false;

    public RecordMeneAnimationBaseView(@NonNull Context context) {
        this(context, null);
    }

    public RecordMeneAnimationBaseView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RecordMeneAnimationBaseView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        animationHelper = new RecordMenuAnimationHelper(this);
    }

    @CallSuper
    protected void init(Context context) {
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), provideMenuLayoutId(), this, true);
    }

    @Override
    protected void onDetachedFromWindow() {
        animationHelper.release();
        super.onDetachedFromWindow();
    }

    @Override
    public void showMenuDirect() {
        if (isMenuVisible()) {
            return;
        }
        animationHelper.showMenuDirect();
        menuVisible = true;
        onMenuShowed(false, false);
    }

    @Override
    public void showMenuAnima(RecordMenuAnimationListener listener) {
        if (isMenuVisible()) {
            return;
        }
        animationHelper.showMenuAnima(new RecordMenuAnimationListener() {
            @Override
            public void onStart() {
                if (null != listener) {
                    listener.onStart();
                }
            }

            @Override
            public void onStop() {
                menuVisible = true;
                boolean consumed = false;
                if (null != listener) {
                    listener.onStop();
                    consumed = true;
                }
                onMenuShowed(consumed, true);
            }
        });
    }

    @Override
    public void hideMenuDirect() {
        if (!isMenuVisible()) {
            return;
        }
        animationHelper.hideMenuDirect();
        menuVisible = false;
        onMenuHidden(false, false);
    }

    @Override
    public void hideMenuAnima(RecordMenuAnimationListener listener) {
        if (!isMenuVisible()) {
            return;
        }
        animationHelper.hideMenuAnima(new RecordMenuAnimationListener() {
            @Override
            public void onStart() {
                if (null != listener) {
                    listener.onStart();
                }
            }

            @Override
            public void onStop() {
                menuVisible = false;
                boolean consumed = false;
                if (null != listener) {
                    listener.onStop();
                    consumed = true;
                }
                onMenuHidden(consumed, true);
            }
        });
    }

    protected void onMenuHidden(final boolean consumed, final boolean animate) {
        if (!consumed && null != menuOpenStateListener) {
            menuOpenStateListener.onClosed(animate);
        }
    }

    protected void onMenuShowed(final boolean consumed, final boolean animate) {
        if (!consumed && null != menuOpenStateListener) {
            menuOpenStateListener.onOpened(animate);
        }
    }

    public boolean isMenuVisible() {
        return menuVisible;
    }

    public void setMenuOpenStateListener(RecordMenuOpenStateListener menuOpenStateListener) {
        this.menuOpenStateListener = menuOpenStateListener;
    }

    public void setOnMenuClickListener(OnRecordMenuClickListener menuClickListener) {
        this.mMenuClickListener = menuClickListener;
    }

    @LayoutRes
    protected abstract int provideMenuLayoutId();
}
