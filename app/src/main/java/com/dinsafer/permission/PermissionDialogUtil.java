package com.dinsafer.permission;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.ui.WebViewActivity;
import com.dinsafer.permission.event.CheckNotificationStateEvent;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.StringStyle;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationManagerCompat;
import androidx.fragment.app.FragmentActivity;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/9 2:09 下午
 */
public class PermissionDialogUtil {
    private static final String TAG = "Permission";
    private static final String F_TAG = "PERMISSION_DIALOG";

    private static CommonPermissionDialog mDialog;

    public static String tag() {
        return TAG;
    }

    public static void show(@NonNull FragmentActivity activity, @NonNull CommonPermissionDialog.Builder builder) {
        hide();

        mDialog = builder.build();
        mDialog.show(activity.getSupportFragmentManager(), F_TAG);
    }

    public static void hide() {
        if (null != mDialog && mDialog.isAdded() && !mDialog.isRemoving()) {
            mDialog.dismiss();
            mDialog = null;
        }
    }

    public static boolean hasNotificationPermission(Context context) {
        return NotificationManagerCompat.from(context).areNotificationsEnabled();
    }

    /**
     * 开启系统通知开关设置页
     */
    public static void openSystemNotificationSetting(@NonNull final Activity mContext) {
        DDLog.i("Permission", "openSystemNotificationSetting");
        try {
            Intent intent = new Intent();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
                intent.putExtra("android.provider.extra.APP_PACKAGE", mContext.getPackageName());
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
                intent.putExtra("app_package", mContext.getPackageName());
                intent.putExtra("app_uid", mContext.getApplicationInfo().uid);
            } else {
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.addCategory(Intent.CATEGORY_DEFAULT);
                intent.setData(Uri.parse("package:" + mContext.getPackageName()));
            }
            mContext.startActivity(intent);
        } catch (Exception e) {
            DDLog.e("Permission", "openSystemNotificationSetting error!");
            e.printStackTrace();
        }
    }

    public static void goIntentSetting(@NonNull Activity activity) {
        try {
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
            intent.setData(uri);
            activity.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void requestLocationPermission(@NonNull final MainActivity activity) {
        requestLocationPermission(activity, null);
    }

    public static void requestLocationPermission(@NonNull final MainActivity activity, final PermissionListener listener) {
        final String[] permission = PermissionUtil.getLocationPermissions();
        boolean denied = AndPermission.hasAlwaysDeniedPermission(activity, permission);
        activity.setNotNeedToLogin(true);
        AndPermission.with(activity)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    if (listener != null) {
                        listener.onGranted();
                    }
                })
                .onDenied(permissions -> {
                    DDLog.e(TAG, "Location permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(activity, permissions)) {
                        goIntentSetting(activity);
                    }
                })
                .start();
    }

    public static void requestLocationPermission(@NonNull final MainActivity activity, final PermissionCallback listener) {
        final String[] permission = PermissionUtil.getLocationPermissions();
        boolean denied = AndPermission.hasAlwaysDeniedPermission(activity, permission);
        activity.setNotNeedToLogin(true);
        AndPermission.with(activity)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    if (listener != null) {
                        listener.onGranted();
                    }
                })
                .onDenied(permissions -> {
                    DDLog.e(TAG, "Location permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(activity, permissions)) {
                        goIntentSetting(activity);
                        if (listener != null) {
                            listener.onDenied();
                        }
                    }
                })
                .start();
    }

    public static void showNeedBleLocationPermissionDialog(final MainActivity activity) {
        showNeedBleLocationPermissionDialog(activity, true, null, null);
    }

    public static void showNeedBleLocationPermissionDialog(final MainActivity activity,
                                                           final boolean cancelable,
                                                           final View.OnClickListener closeClickListener,
                                                           final View.OnClickListener okClickListener) {
        final CharSequence hint2 = StringStyle.format(DinSaferApplication.getAppContext(),
                Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_location_ble_action_hint)), R.style.permissionBleLocalDetail);
        SpannableString spStr = new SpannableString(hint2);
        spStr.setSpan(new ClickableSpan() {
            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(DinSaferApplication.getAppContext().getResources().getColor(R.color.text_blue_1));       //设置文字颜色
                ds.setUnderlineText(false);      //设置下划线
            }

            @Override
            public void onClick(View widget) {
                activity.setNotNeedToLogin(true);
                activity.startActivity(WebViewActivity.getStartIntent(activity, APIKey.HELP_URL_SCAN_BLE));
            }
        }, 0, hint2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        SpannableStringBuilder stringBuilder = new SpannableStringBuilder()
                .append(StringStyle.format(DinSaferApplication.getAppContext(),
                        Local.s(DinSaferApplication.getAppContext().getResources().getString(R.string.permission_location_ble_1_hint)), R.style.permissionBleLocal))
                .append(" ")
                .append(spStr)
                .append("\n")
                .append(StringStyle.format(DinSaferApplication.getAppContext(),
                        Local.s(DinSaferApplication.getAppContext().getResources().getString(R.string.permission_location_ble_2_hint)), R.style.permissionBleLocal));
        View.OnClickListener listener = okClickListener;
        if (okClickListener == null) {
            listener = v -> {
                requestLocationPermission(activity);
            };
        }
        CommonPermissionDialog.Builder commonPermissionDialog = new CommonPermissionDialog.Builder()
                .localTittle(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_location)))
                .localContent(stringBuilder)
                .enableDragClose(cancelable)
                .cancelable(cancelable)
                .autoDismissOnOkClick(cancelable)
                .localBtnText(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_ok)))
                .iconResId(R.drawable.img_permission_location)
                .onCloseClickListener(closeClickListener)
                .onOkClickListener(listener);
        PermissionDialogUtil.show(activity, commonPermissionDialog);
    }

    public static void showNeedWIFILocationDialog(@NonNull final MainActivity activity) {
        final CommonPermissionDialog.Builder builder = new CommonPermissionDialog.Builder()
                .localTittle(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_location)))
                .localContent(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_location_ssid_hint)))
                .enableDragClose(true)
                .localBtnText(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_ok)))
                .iconResId(R.drawable.img_permission_location)
                .onOkClickListener(v -> requestLocationPermission(activity));
        PermissionDialogUtil.show(activity, builder);
    }

    public static void checkNotificationPermission() {
        if (DBUtil.Bool(DBKey.KEY_DENY_CHECK_NOTIFICATION_PERMISSION)) {
            return;
        }

        EventBus.getDefault().post(new CheckNotificationStateEvent());
    }

    public static void showNeedNotificationPermissionDialog(@NonNull final MainActivity activity) {
        CommonPermissionDialog.Builder builder = new CommonPermissionDialog.Builder()
                .localTittle(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_notification)))
                .localContent(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_notification_hint)))
                .enableDragClose(true)
                .localBtnText(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_ok)))
                .iconResId(R.drawable.img_permission_notification)
                .localBottomHintText(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_not_show_again)))
                .onBottomHintClickListener(v -> {
                    DBUtil.Put(DBKey.KEY_DENY_CHECK_NOTIFICATION_PERMISSION, true);
                    PermissionDialogUtil.hide();
                })
                .onOkClickListener(v -> {
                    activity.setNotNeedToLogin(true);
                    PermissionDialogUtil.openSystemNotificationSetting(activity);
                });
        PermissionDialogUtil.show(activity, builder);
    }

    @RequiresApi(api = 31)
    public static void requestBluetoothPermission(@NonNull final MainActivity activity, PermissionListener listener) {
        final String[] permission = new String[]{
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT};
        boolean denied = AndPermission.hasAlwaysDeniedPermission(activity, permission);
        activity.setNotNeedToLogin(true);
        AndPermission.with(activity)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    DDLog.e(TAG, "Location permission granted!!!");
                    if (null != listener) {
                        listener.onGranted();
                    }
                })
                .onDenied(permissions -> {
                    DDLog.e(TAG, "Location permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(activity, permissions)) {
                        goIntentSetting(activity);
                    }
                })
                .start();
    }

    public static void showAILocationSetupPermissionDialog(@NonNull final MainActivity activity,
                                                           final View.OnClickListener clickListener) {
        if (DBUtil.Bool(DBKey.KEY_DENY_CHECK_AI_LOCATION_SETUP_PERMISSION)) {
            return;
        }
        CommonPermissionDialog.Builder builder = new CommonPermissionDialog.Builder()
                .localTittle(Local.s(DinSaferApplication.getAppContext().getString(R.string.ai_location_setup)))
                .localContent(Local.s(DinSaferApplication.getAppContext().getString(R.string.ai_location_setup_hint)))
                .enableDragClose(true)
                .localBtnText(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_ok)))
                .iconResId(R.drawable.img_ai_location_setup)
                .localBottomHintText(Local.s(DinSaferApplication.getAppContext().getString(R.string.permission_not_show_again)))
                .onBottomHintClickListener(v -> {
                    DBUtil.Put(DBKey.KEY_DENY_CHECK_AI_LOCATION_SETUP_PERMISSION, true);
                    PermissionDialogUtil.hide();
                })
                .onOkClickListener(clickListener);
        PermissionDialogUtil.show(activity, builder);
    }

    public interface PermissionListener {
        void onGranted();
    }

    public abstract static class PermissionCallback implements PermissionListener {
        public abstract void onDenied();
    }
}
