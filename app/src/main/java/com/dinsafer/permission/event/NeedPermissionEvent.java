package com.dinsafer.permission.event;

import io.reactivex.annotations.Nullable;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/3/10 15:08
 */
public class NeedPermissionEvent {
    private final String[] permissions;

    public NeedPermissionEvent(@Nullable String[] permissions) {
        this.permissions = permissions;
    }

    @Nullable
    public String[] getPermissions() {
        return permissions;
    }
}
