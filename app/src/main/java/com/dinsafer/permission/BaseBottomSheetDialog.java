package com.dinsafer.permission;

import android.app.Dialog;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;

import android.content.DialogInterface;
import android.os.Bundle;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.dinsafer.dinnet.R;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/8 2:17 下午
 */
public abstract class BaseBottomSheetDialog<V extends ViewDataBinding> extends BottomSheetDialogFragment {

    protected V mBinding;
    protected BottomSheetDialog mDialog;
    protected BottomSheetBehavior<FrameLayout> mBehavior;
    private boolean enableDragClose;
    private OnDismissCallback dismissCallback;

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        final Dialog dialog = super.onCreateDialog(savedInstanceState);
        mDialog = (BottomSheetDialog) dialog;
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), provideResId(), null, false);
        mBinding.getRoot().setBackgroundResource(R.drawable.shape_bg_dialog_bottom_sheet);
        dialog.setContentView(mBinding.getRoot());
        initView();
        return dialog;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(STYLE_NORMAL, R.style.BottomSheetDialog);
    }

    @Override
    public void onStart() {
        super.onStart();
        final FrameLayout view = mDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
        if (null == view || null == view.getLayoutParams()) {
            return;
        }

        final int height = provideDialogHeight();
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        layoutParams.height = height;
        view.setLayoutParams(layoutParams);

        mBehavior = BottomSheetBehavior.from(view);
        mBehavior.setPeekHeight(height);
        mBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);

        updateDragCloseState();
    }

    protected int dip2px(float dipValue) {
        final float scale = getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }

    protected int provideDialogHeight() {
        int peekHeight = getResources().getDisplayMetrics().heightPixels;
        return peekHeight - dip2px(30);
    }

    protected void setEnableDrag(final boolean enable) {
        this.enableDragClose = enable;
        if (null == mBehavior) {
            return;
        }

        updateDragCloseState();
    }

    /**
     * 设置是否可以下拉滑动关闭Dialog
     */
    private void updateDragCloseState() {
        if (enableDragClose) {
            mBehavior.setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
                @Override
                public void onStateChanged(@NonNull View view, int newState) {
                    if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                        dismiss();
                    }
                }

                @Override
                public void onSlide(@NonNull View view, float v) {

                }
            });
        } else {
            mBehavior.setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
                @Override
                public void onStateChanged(@NonNull View view, int newState) {
                    if (newState == BottomSheetBehavior.STATE_DRAGGING) {
                        mBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                    }
                }

                @Override
                public void onSlide(@NonNull View view, float v) {

                }
            });
        }
    }

    protected void initView() {
    }

    @LayoutRes
    protected abstract int provideResId();

    public void setDismissCallback(OnDismissCallback dismissCallback) {
        this.dismissCallback = dismissCallback;
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (dismissCallback != null) {
            dismissCallback.onDismiss();
        }
    }

    public interface OnDismissCallback {
        void onDismiss();
    }
}
