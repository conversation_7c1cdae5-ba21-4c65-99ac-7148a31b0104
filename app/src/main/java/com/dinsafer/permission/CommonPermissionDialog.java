package com.dinsafer.permission;

import android.os.Bundle;
import androidx.annotation.DrawableRes;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogCommonPermissionBinding;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/8 2:29 下午
 */
public class CommonPermissionDialog extends BaseBottomSheetDialog<DialogCommonPermissionBinding> {
    private static final String KEY_TITTLE = "KEY_TITTLE";
    private static final String KEY_CONTENT = "KEY_CONTENT";
    private static final String KEY_ICON_RES_ID = "KEY_ICON_RES_ID";
    private static final String KEY_BTN_TEXT = "KEY_BTN_TEXT";
    private static final String KEY_BOTTOM_HINT_TEXT = "KEY_BOTTOM_HINT_TEXT";
    private static final String KEY_AUTO_DISMISS_ON_OK = "KEY_AUTO_DISMISS_ON_OK";
    private static final String KEY_ENABLE_DRAG_CLOSE = "KEY_ENABLE_DRAG_CLOSE";

    private String tittle;
    private CharSequence content;
    private @DrawableRes
    int iconResId;
    private String btnText;
    private String bottomHintText;
    private boolean autoDismissOnOkClick;

    private View.OnClickListener onBottomHintClickListener;
    private View.OnClickListener onCloseClickListener;
    private View.OnClickListener onOkClickListener;

    private static CommonPermissionDialog newInstance(Builder builder) {
        CommonPermissionDialog dialog = new CommonPermissionDialog();
        dialog.setCancelable(builder.cancelable);
        dialog.onCloseClickListener(builder.onCloseClickListener);
        dialog.onOkClickListener(builder.onOkClickListener);
        dialog.onBottomHintClickListener(builder.onBottomHintClickListener);
        Bundle args = new Bundle();
        args.putString(KEY_TITTLE, builder.tittle);
        args.putCharSequence(KEY_CONTENT, builder.content);
        args.putInt(KEY_ICON_RES_ID, builder.iconResId);
        args.putString(KEY_BTN_TEXT, builder.btnText);
        args.putBoolean(KEY_ENABLE_DRAG_CLOSE, builder.enableDragClose);
        args.putString(KEY_BOTTOM_HINT_TEXT, builder.bottomHintText);
        args.putBoolean(KEY_AUTO_DISMISS_ON_OK, builder.autoDismissOnOkClick);
        dialog.setArguments(args);
        return dialog;
    }

    @Override
    protected void initView() {
        super.initView();
        Bundle arguments = getArguments();

        boolean dragClose = false;
        if (null != arguments) {
            tittle = arguments.getString(KEY_TITTLE);
            content = arguments.getCharSequence(KEY_CONTENT);
            bottomHintText = arguments.getString(KEY_BOTTOM_HINT_TEXT);
            iconResId = arguments.getInt(KEY_ICON_RES_ID);
            btnText = arguments.getString(KEY_BTN_TEXT);
            autoDismissOnOkClick = arguments.getBoolean(KEY_AUTO_DISMISS_ON_OK);
            dragClose = arguments.getBoolean(KEY_ENABLE_DRAG_CLOSE);
        }

        setEnableDrag(dragClose);
        mBinding.tvTittle.setText(tittle);
        mBinding.ivIcon.setImageResource(iconResId);
        mBinding.btnOk.setText(btnText);
        if (TextUtils.isEmpty(bottomHintText)) {
            mBinding.tvNotShowAgain.setVisibility(View.GONE);
        } else {
            mBinding.tvNotShowAgain.setVisibility(View.VISIBLE);
            mBinding.tvNotShowAgain.setText(bottomHintText);
            mBinding.tvNotShowAgain.setOnClickListener(v -> {
                if (null != onBottomHintClickListener) {
                    onBottomHintClickListener.onClick(v);
                }
            });
        }

        mBinding.tvContent.setText(content);
        //设置点击后的颜色为透明，否则会一直出现高亮
        mBinding.tvContent.setHighlightColor(getResources().getColor(R.color.text_blue_1));
        mBinding.tvContent.setMovementMethod(LinkMovementMethod.getInstance());

        mBinding.ivClose.setOnClickListener(v -> {
            if (null != onCloseClickListener) {
                onCloseClickListener.onClick(v);
            }
            dismiss();
        });
        mBinding.btnOk.setOnClickListener(v -> {
            if (null != onOkClickListener) {
                onOkClickListener.onClick(v);
            }
            if (autoDismissOnOkClick) {
                dismiss();
            }
        });
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_common_permission;
    }

    private void onCloseClickListener(final View.OnClickListener onCloseClickListener) {
        this.onCloseClickListener = onCloseClickListener;
    }

    private void onOkClickListener(final View.OnClickListener onOkClickListener) {
        this.onOkClickListener = onOkClickListener;
    }

    private void onBottomHintClickListener(final View.OnClickListener onBottomHintClickListener) {
        this.onBottomHintClickListener = onBottomHintClickListener;
    }

    public static class Builder {
        private String tittle;
        private CharSequence content = "";
        private @DrawableRes
        int iconResId;
        private String btnText = DinSaferApplication.getAppContext().getString(R.string.permission_ok);
        private String bottomHintText;
        private boolean autoDismissOnOkClick = true;
        private boolean enableDragClose = true;
        private boolean cancelable = true;

        private View.OnClickListener onBottomHintClickListener;
        private View.OnClickListener onCloseClickListener;
        private View.OnClickListener onOkClickListener;

        public Builder localTittle(final String tittle) {
            this.tittle = tittle;
            return this;
        }

        public Builder localContent(final CharSequence content) {
            this.content = content;
            return this;
        }

        public Builder iconResId(final @DrawableRes int iconResId) {
            this.iconResId = iconResId;
            return this;
        }

        public Builder localBtnText(final String btnText) {
            this.btnText = btnText;
            return this;
        }

        public Builder localBottomHintText(final String bottomHintText) {
            this.bottomHintText = bottomHintText;
            return this;
        }

        public Builder autoDismissOnOkClick(final boolean autoDismissOnOkClick) {
            this.autoDismissOnOkClick = autoDismissOnOkClick;
            return this;
        }

        public Builder enableDragClose(final boolean enableDragClose) {
            this.enableDragClose = enableDragClose;
            return this;
        }

        public Builder cancelable(final boolean cancelable) {
            this.cancelable = cancelable;
            return this;
        }

        public Builder onBottomHintClickListener(final View.OnClickListener onBottomHintClickListener) {
            this.onBottomHintClickListener = onBottomHintClickListener;
            return this;
        }

        public Builder onCloseClickListener(final View.OnClickListener onCloseClickListener) {
            this.onCloseClickListener = onCloseClickListener;
            return this;
        }

        public Builder onOkClickListener(final View.OnClickListener onOkClickListener) {
            this.onOkClickListener = onOkClickListener;
            return this;
        }

        public CommonPermissionDialog build() {
            return CommonPermissionDialog.newInstance(this);
        }
    }
}
