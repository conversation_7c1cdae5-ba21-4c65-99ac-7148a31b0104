package com.dinsafer.util;

import android.content.Intent;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafe.Dinsafe;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.common.PluginTypeTransformHelper;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDTag;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.IUser;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsaferpush.Const;
import com.dinsafer.dinsaferpush.core.AliasCallback;
import com.dinsafer.dinsaferpush.core.DLog;
import com.dinsafer.dinsaferpush.core.DinsaferPushManager;
import com.dinsafer.model.AlertServicePlanUpdateEvent;
import com.dinsafer.model.DeviceAlertServicePlanResponse;
import com.dinsafer.model.LoginResponse;
import com.dinsafer.model.MultiDataEntry;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.model.TuyaItem;
import com.dinsafer.model.event.GetDeviceInfoEvent;
import com.dinsafer.model.event.HadLoginEvent;
import com.dinsafer.model.event.NeedLogoutEvent;
import com.dinsafer.model.family.FetchHomeListEvent;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.panel.common.PanelDataKey;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.iq80.snappy.Main;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by Rinfon on 16/7/13.
 */
public class CommonDataUtil {

    public static final String WIRELESS_SIREN = PluginConstants.TYPE_14;

    public final String TAG = "CommonDataUtil";

    public static final String IPC_KEY = PluginConstants.TYPE_1F;

    public static final int TUYA_SMART_PLUGIN = 12;

    public static final String REMOTE_CONTROLLER = "Remote Controller";

    public static final String PIR = PluginConstants.NAME_PIR_SENSOR;
    public static final String DOOR_WINDOW = "Door Window Sensor";
    public static final String SMART_PLUGIN = "Smart Plug";
    public static final String SIGNAL_REPEATER_PLUGIN = "Signal Repeater Plug";

    private static volatile CommonDataUtil instance;

    private LoginResponse mUser;

    private MultiDataEntry multiDataEntry;

    private ConcurrentMap<String, Object> sTypeItem;

    private ConcurrentMap<String, Integer> sTypeIcon;

    private ConcurrentMap<String, Integer> rPluginIcon;

    private ConcurrentMap<Integer, Integer> mMainSectionItem;

    /**
     * 包含电量级别的配件ID
     */
    private List<String> mBatteryLevelPluginIds;

    /**
     * 包含R2A功能的配件ID
     */
    private List<String> mReady2ArmIds;

    /**
     * 包含防拆状态功能的配件ID
     */
    private List<String> mTamperIds;

    /**
     * 包含信号强度的配件ID
     */
    private List<String> mRssiIds;

    /**
     * 包含充电状态的配件ID
     */
    private List<String> mChargingIds;

    private String mPushAliasUserId;


    private CommonDataUtil() {
        EventBus.getDefault().register(this);
        sTypeItem = new ConcurrentHashMap<String, Object>();
        sTypeItem.put(PluginConstants.TYPE_13, PluginConstants.NAME_SMART_LIGHT);
        sTypeItem.put(PluginConstants.TYPE_14, PluginConstants.NAME_WIRELESS_SIREN); // 无线喇叭
        sTypeItem.put(PluginConstants.TYPE_22, PluginConstants.NAME_WIRELESS_SIREN); // 无线喇叭
        sTypeItem.put(PluginConstants.TYPE_21, PluginConstants.NAME_WIRELESS_SIREN); // 无线喇叭
        sTypeItem.put(PluginConstants.TYPE_15, PluginConstants.NAME_SMART_PLUG); // 智能插座
        sTypeItem.put(PluginConstants.TYPE_17, PluginConstants.NAME_DISTANCE_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_19, PluginConstants.NAME_VIBRATION_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_1B, PluginConstants.NAME_WIRELESS_KEYPAD);//无线键盘
        sTypeItem.put(PluginConstants.TYPE_1F, PluginConstants.NAME_IPC_CAMERA);
        sTypeItem.put(PluginConstants.TYPE_0B, PluginConstants.NAME_DOOR_WINDOW_SENSOR);// 门磁
        sTypeItem.put(PluginConstants.TYPE_09, PluginConstants.NAME_PIR_SENSOR);//红外
        sTypeItem.put(PluginConstants.TYPE_0A, PluginConstants.NAME_GAS_SENSOR);//气感
        sTypeItem.put(PluginConstants.TYPE_05, PluginConstants.NAME_SMOKE_SENSOR);// 烟雾感应器
        sTypeItem.put(PluginConstants.TYPE_02, PluginConstants.NAME_REMOTE_CONTROLLER);
        sTypeItem.put(PluginConstants.TYPE_08, PluginConstants.NAME_REMOTE_CONTROLLER); // 遥控器
        sTypeItem.put(PluginConstants.TYPE_01, PluginConstants.NAME_REMOTE_CONTROLLER);
        sTypeItem.put(PluginConstants.TYPE_04, PluginConstants.NAME_REMOTE_CONTROLLER);
        sTypeItem.put(PluginConstants.TYPE_07, PluginConstants.NAME_PANIC_BUTTON); // 紧急按钮
        sTypeItem.put(PluginConstants.TYPE_30, PluginConstants.NAME_PANIC_BUTTON);
        sTypeItem.put(PluginConstants.TYPE_0D, PluginConstants.NAME_REMOTE_CONTROLLER);
        sTypeItem.put(PluginConstants.TYPE_0E, PluginConstants.NAME_LIQUID_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_18, PluginConstants.NAME_LIQUID_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_50, PluginConstants.NAME_DOOR_WINDOW_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_5C, PluginConstants.NAME_PIR_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_06, PluginConstants.NAME_VIBRATION_SENSOR);  // 震动开关
        sTypeItem.put(PluginConstants.TYPE_CC, PluginConstants.NAME_VIBRATION_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_31, PluginConstants.NAME_CO_DETECTOR);
        sTypeItem.put(PluginConstants.TYPE_32, PluginConstants.NAME_OUTDOOR_PIR);
        sTypeItem.put(PluginConstants.TYPE_33, PluginConstants.NAME_OUTDOOR_BEAM);
        sTypeItem.put(PluginConstants.TYPE_37, PluginConstants.NAME_RFID_TAG);
        sTypeItem.put(PluginConstants.TYPE_1C, PluginConstants.NAME_DOOR_WINDOW_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_12, PluginConstants.NAME_ROLLER_SHUTTER);
        sTypeItem.put(PluginConstants.TYPE_11, PluginConstants.NAME_DOOR_WINDOW_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_16, PluginConstants.NAME_DOOR_WINDOW_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_24, PluginConstants.NAME_PIR_SENSOR); // 心跳红外
        sTypeItem.put(PluginConstants.TYPE_25, PluginConstants.NAME_DOOR_WINDOW_SENSOR); // 心跳门磁
        sTypeItem.put(PluginConstants.TYPE_1E, PluginConstants.NAME_REMOTE_CONTROLLER); // 遥控器
        sTypeItem.put(PluginConstants.TYPE_2C, PluginConstants.NAME_VIBRATION_SENSOR); // 震动门磁
        sTypeItem.put(PluginConstants.TYPE_2E, PluginConstants.NAME_LIQUID_SENSOR); // 水感
        sTypeItem.put(PluginConstants.TYPE_23, PluginConstants.NAME_PANIC_BUTTON); // 紧急按钮
        sTypeItem.put(PluginConstants.TYPE_2D, PluginConstants.NAME_SMOKE_SENSOR); // 烟感
        sTypeItem.put(PluginConstants.TYPE_4A, PluginConstants.NAME_ADJUSTABLE_MOTION_SENSOR);
        sTypeItem.put(PluginConstants.TYPE_4E, PluginConstants.NAME_SIGNAL_REPEATER_PLUG);


        sTypeIcon = new ConcurrentHashMap<String, Integer>();
        sTypeIcon.put(PluginConstants.TYPE_13, R.drawable.icon_sos_others);
        sTypeIcon.put(PluginConstants.TYPE_14, R.drawable.icon_sos_wireless_siren);
        sTypeIcon.put(PluginConstants.TYPE_22, R.drawable.icon_sos_wireless_siren);
        sTypeIcon.put(PluginConstants.TYPE_21, R.drawable.icon_sos_wireless_siren);
        sTypeIcon.put(PluginConstants.TYPE_15, R.drawable.icon_sos_others);
        sTypeIcon.put(PluginConstants.TYPE_17, R.drawable.icon_sos_others);
        sTypeIcon.put(PluginConstants.TYPE_19, R.drawable.icon_sos_shake_sensor);
        sTypeIcon.put(PluginConstants.TYPE_1B, R.drawable.icon_sos_keypad);
        sTypeIcon.put(PluginConstants.TYPE_1F, R.drawable.icon_sos_others);
        sTypeIcon.put(PluginConstants.TYPE_0B, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_1C, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_09, R.drawable.icon_sos_pir);
        sTypeIcon.put(PluginConstants.TYPE_0A, R.drawable.icon_sos_gas_sensor);
        sTypeIcon.put(PluginConstants.TYPE_05, R.drawable.icon_sos_tem_gas_sensor);
        sTypeIcon.put(PluginConstants.TYPE_02, R.drawable.icon_sos_remote_control);
        sTypeIcon.put(PluginConstants.TYPE_08, R.drawable.icon_sos_remote_control);
        sTypeIcon.put(PluginConstants.TYPE_01, R.drawable.icon_sos_remote_control);
        sTypeIcon.put(PluginConstants.TYPE_04, R.drawable.icon_sos_remote_control);
        sTypeIcon.put(PluginConstants.TYPE_07, R.drawable.icon_sos_others);
        sTypeIcon.put(PluginConstants.TYPE_30, R.drawable.icon_sos_urgentbtn_sensor);
        sTypeIcon.put(PluginConstants.TYPE_0D, R.drawable.icon_sos_remote_control);
        sTypeIcon.put(PluginConstants.TYPE_0E, R.drawable.icon_sos_water_sensor);
        sTypeIcon.put(PluginConstants.TYPE_18, R.drawable.icon_sos_water_sensor);
        sTypeIcon.put(PluginConstants.TYPE_50, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_5C, R.drawable.icon_sos_pir);
        sTypeIcon.put(PluginConstants.TYPE_06, R.drawable.icon_sos_shake_sensor);
        sTypeIcon.put(PluginConstants.TYPE_CC, R.drawable.icon_sos_shake_sensor);
        sTypeIcon.put(PluginConstants.TYPE_31, R.drawable.icon_sos_cosensor);
        sTypeIcon.put(PluginConstants.TYPE_32, R.drawable.icon_sos_pir);
        sTypeIcon.put(PluginConstants.TYPE_33, R.drawable.icon_sos_others);
        sTypeIcon.put(PluginConstants.TYPE_11, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_16, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_24, R.drawable.icon_sos_pir);
        sTypeIcon.put(PluginConstants.TYPE_25, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_1E, R.drawable.icon_sos_remote_control);
        sTypeIcon.put(PluginConstants.TYPE_2C, R.drawable.icon_sos_shake_sensor);
        sTypeIcon.put(PluginConstants.TYPE_2E, R.drawable.icon_sos_water_sensor);
        sTypeIcon.put(PluginConstants.TYPE_23, R.drawable.icon_sos_urgentbtn_sensor);
        sTypeIcon.put(PluginConstants.TYPE_2D, R.drawable.icon_sos_tem_gas_sensor);
        sTypeIcon.put(PluginConstants.TYPE_4A, R.drawable.icon_sos_pir);


        rPluginIcon = new ConcurrentHashMap<String, Integer>();
        rPluginIcon.put(PluginConstants.TYPE_13, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_14, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_22, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_21, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_15, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_17, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_19, R.drawable.icon_homearm_entry_delay_setting_vibration_sensor);
        rPluginIcon.put(PluginConstants.TYPE_1B, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_1F, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_0B, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_1C, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_09, R.drawable.icon_setting_pir);
        rPluginIcon.put(PluginConstants.TYPE_0A, R.drawable.icon_setting_gas);
        rPluginIcon.put(PluginConstants.TYPE_05, R.drawable.icon_setting_tem);
        rPluginIcon.put(PluginConstants.TYPE_02, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_08, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_01, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_04, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_07, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_30, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_0D, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_0E, R.drawable.icon_setting_water);
        rPluginIcon.put(PluginConstants.TYPE_18, R.drawable.icon_setting_water);
        rPluginIcon.put(PluginConstants.TYPE_50, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_5C, R.drawable.icon_setting_pir);
//        震动门磁
        rPluginIcon.put(PluginConstants.TYPE_06, R.drawable.icon_homearm_entry_delay_setting_vibration_sensor);
        rPluginIcon.put(PluginConstants.TYPE_CC, R.drawable.icon_homearm_entry_delay_setting_vibration_sensor);
        rPluginIcon.put(PluginConstants.TYPE_31, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_32, R.drawable.icon_setting_pir);
        rPluginIcon.put(PluginConstants.TYPE_33, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_11, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_16, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_24, R.drawable.icon_setting_pir);
        rPluginIcon.put(PluginConstants.TYPE_25, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_1E, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_2C, R.drawable.icon_homearm_entry_delay_setting_vibration_sensor);
        rPluginIcon.put(PluginConstants.TYPE_2E, R.drawable.icon_setting_water);
        rPluginIcon.put(PluginConstants.TYPE_23, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_2D, R.drawable.icon_setting_tem);
        rPluginIcon.put(PluginConstants.TYPE_4A, R.drawable.icon_setting_pir);


//        新主页图标集合
        mMainSectionItem = new ConcurrentHashMap<Integer, Integer>();
        mMainSectionItem.put(TuyaItem.IPC, R.drawable.icon_main_edit_ipc);
        mMainSectionItem.put(TuyaItem.ADDACCESSORY, R.drawable.icon_main_edit_accessory);
        mMainSectionItem.put(TuyaItem.SOS, R.drawable.icon_main_edit_sos);
        mMainSectionItem.put(TuyaItem.MOTION_RECORD, R.drawable.icon_main_edit_motion_record);
        mMainSectionItem.put(TuyaItem.SMARTPLUGIN_OFF, R.drawable.btn_shortcut_plug_off);
        mMainSectionItem.put(TuyaItem.SMARTPLUGIN_ON, R.drawable.btn_shortcut_plug_on);
        mMainSectionItem.put(TuyaItem.SMARTPLUGIN_LOADING, R.drawable.icon_main_btn_loading);
        mMainSectionItem.put(TuyaItem.TUYA_SMARTPLUGIN_LOADING, R.drawable.icon_main_btn_loading);
        mMainSectionItem.put(TuyaItem.TUYA_SMARTPLUGIN_OFFLINE, R.drawable.icon_main_switch_tuya_offline);
        mMainSectionItem.put(TuyaItem.TUYA_SMARTPLUGIN_ON, R.drawable.btn_shortcut_plug_tuya_on);
        mMainSectionItem.put(TuyaItem.TUYA_SMARTPLUGIN_OFF, R.drawable.btn_shortcut_plug_tuya_off);
        mMainSectionItem.put(TuyaItem.LIGHT_ON, R.drawable.btn_shortcut_bulb_tuya_on);
        mMainSectionItem.put(TuyaItem.LIGHT_OFF, R.drawable.btn_shortcut_bulb_tuya_off);
        mMainSectionItem.put(TuyaItem.LIGHT_OFFLINE, R.drawable.icon_main_bulb_tuya_offline);

        initPowerLevelPlugin();

        initPluginTypeIds();
    }

    /**
     * 添加新型包含电量级别的配件信息
     */
    private void initPowerLevelPlugin() {
        sTypeItem.put(PluginConstants.TYPE_2F, PluginConstants.NAME_WIRELESS_KEYPAD); // 无线键盘
        sTypeItem.put(PluginConstants.TYPE_3D, PluginConstants.NAME_ROLLING_DOOR_WINDOW_SENSOR);   // 卷帘门磁
        sTypeItem.put(PluginConstants.TYPE_3E, PluginConstants.NAME_SMART_PLUG); // 智能插座
        sTypeItem.put(PluginConstants.TYPE_4E, PluginConstants.NAME_SIGNAL_REPEATER_PLUG); // 中继插座
        sTypeItem.put(PluginConstants.TYPE_34, PluginConstants.NAME_WIRELESS_SIREN); // 室内警笛
        sTypeItem.put(PluginConstants.TYPE_35, PluginConstants.NAME_WIRELESS_SIREN); // 室外警笛
        sTypeItem.put(PluginConstants.TYPE_36, PluginConstants.NAME_PIR_SENSOR); // 普通红外
        sTypeItem.put(PluginConstants.TYPE_38, PluginConstants.NAME_DOOR_WINDOW_SENSOR); // 普通门磁
        sTypeItem.put(PluginConstants.TYPE_39, PluginConstants.NAME_LIQUID_SENSOR); // 水感
        sTypeItem.put(PluginConstants.TYPE_3A, PluginConstants.NAME_REMOTE_CONTROLLER); // 5键遥控
        sTypeItem.put(PluginConstants.TYPE_3B, PluginConstants.NAME_SMART_BUTTON); // 紧急按钮->更新为SmartButton
        sTypeItem.put(PluginConstants.TYPE_3C, PluginConstants.NAME_SMOKE_SENSOR); // 烟感
        sTypeItem.put(PluginConstants.TYPE_3F, PluginConstants.NAME_WIRED_BRIDGE);

        sTypeIcon.put(PluginConstants.TYPE_2F, R.drawable.icon_sos_keypad);
        sTypeIcon.put(PluginConstants.TYPE_3D, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_3E, R.drawable.icon_sos_others);
        sTypeIcon.put(PluginConstants.TYPE_4E, R.drawable.icon_eventlist_repeater);
        sTypeIcon.put(PluginConstants.TYPE_34, R.drawable.icon_sos_wireless_siren);
        sTypeIcon.put(PluginConstants.TYPE_35, R.drawable.icon_sos_wireless_siren);
        sTypeIcon.put(PluginConstants.TYPE_36, R.drawable.icon_sos_pir);
        sTypeIcon.put(PluginConstants.TYPE_38, R.drawable.icon_sos_doorwindow_sensor);
        sTypeIcon.put(PluginConstants.TYPE_39, R.drawable.icon_sos_water_sensor);
        sTypeIcon.put(PluginConstants.TYPE_3A, R.drawable.icon_sos_remote_control);
        sTypeIcon.put(PluginConstants.TYPE_3B, R.drawable.icon_eventlist_smartbtn);
        sTypeIcon.put(PluginConstants.TYPE_3C, R.drawable.icon_sos_tem_gas_sensor);
        sTypeIcon.put(PluginConstants.TYPE_3F, R.drawable.icon_eventlist_wired_bridge);

        rPluginIcon.put(PluginConstants.TYPE_2F, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_3D, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_3E, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_34, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_35, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_36, R.drawable.icon_setting_pir);
        rPluginIcon.put(PluginConstants.TYPE_38, R.drawable.icon_setting_door);
        rPluginIcon.put(PluginConstants.TYPE_39, R.drawable.icon_setting_water);
        rPluginIcon.put(PluginConstants.TYPE_3A, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_3B, R.drawable.icon_setting_others);
        rPluginIcon.put(PluginConstants.TYPE_3C, R.drawable.icon_setting_tem);
        rPluginIcon.put(PluginConstants.TYPE_3F, R.drawable.icon_setting_wired_bridge);
    }

    public static void init() {
        if (instance == null) {
            synchronized (CommonDataUtil.class) {
                if (instance == null) {
                    instance = new CommonDataUtil();
                }
            }
        }
    }


    public static CommonDataUtil getInstance() {
        return instance;
    }

//    public boolean isAdmin() {
//        return getMultiDataEntryResultBean() != null && getMultiDataEntryResultBean().getPermission() == LocalKey.ADMIN;
//    }

    /**
     * use{@link #getUserInfo()} after deprecated
     */
    @Deprecated()
    public LoginResponse getUser() {
        synchronized (lock) {
            if (mUser == null) {
                DDLog.i("fix", "user null try fix");
                tryToFixUser();
            }
            return mUser;
        }
    }

    @Nullable
    public DinUser getUserInfo() {
        final IUser loginUser = DinSDK.getUserInstance();
        if (null != loginUser) {
            return loginUser.getUser();
        }
        return null;
    }

    @Nullable
    public String getUserUid() {
        final DinUser userInfo = getUserInfo();
        if (null != userInfo) {
            return userInfo.getUid();
        }
        return null;
    }

    @Nullable
    public String getUserToken() {
        final DinUser userInfo = getUserInfo();
        if (null != userInfo) {
            return userInfo.getToken();
        }
        return null;
    }

    public boolean isBind() {
        return DinSDK.getUserInstance().isLogin() &&
                (!TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail()) ||
                        !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone()));
    }

    public boolean checkHasUser() {
        DDLog.i("fix", "checkhasuser");
        return DinSDK.getUserInstance().isLogin();
    }

    @Deprecated
    public void tryToFixUser() {
        if (DBUtil.Exists(DBKey.USER_KEY)) {
//            如果数据库有记录，则可能某些地方出错导致null
            String user = DBUtil.SGet(DBKey.USER_KEY);
            // mUser = JSON.parseObject(user, LoginResponse.class);
            DDLog.i("fix", "db has user try fix");
        } else {
//            如果数据库没有记录，伪造一个空user，防止奔溃
            DDLog.i("fix", "db user is empty try fix empty user");
            // mUser = LoginResponse.Builder();
        }
    }

    @Deprecated
    public void tryFixDBUserWhenUserIsEmpty() {
        try {
            if (DBUtil.Exists(DBKey.USER_KEY)) {
                String user = DBUtil.SGet(DBKey.USER_KEY);
                // LoginResponse loginuser = JSON.parseObject(user, LoginResponse.class);
                // if (TextUtils.isEmpty(loginuser.getResult().getUid())) {
                //     DDLog.i("fix", "db user is empty try to clear db");
                //     clearDB();
                // }
            }
        } catch (Exception ex) {
            DDLog.log(DDTag.EXCEPTION, ex.getMessage());
        }

    }

    private static Byte[] lock = new Byte[0];

    public void setUser(LoginResponse mUser) {
        synchronized (lock) {
            this.mUser = mUser;
        }
    }

    public String getCurrentDeviceId() {
        return currentPanelID;
    }

    public boolean isPanelOnline() {
        final Device panel = DinSDK.getHomeInstance().getDevice(currentPanelID);
        return null != panel
                && !panel.getFlagDeleted()
                && DeviceHelper.getBoolean(panel, PanelDataKey.Panel.IS_ONLINE, false);
    }

    public int getMainIconByType(int type) {
        return mMainSectionItem.get(type);
    }

    public String getCurrentDeviceName() {
        return DeviceHelper.getString(DinHome.getInstance().getDevice(currentPanelID),
                PanelDataKey.Panel.NAME, "");
    }

    public void autoLogin(final MainActivity activity) {
        autoLogin(activity, null);
        isBleToAdd = false;
    }

    /**
     * 1.2.8升级2.0.3由于更新了libsecretlib.so，会导致之前保存的密码错误
     */
    public void tryFixUserPwdIfNeedClean() {
        if (!DBUtil.Exists(DBKey.FIXED_USER)) {
            DBUtil.Delete(DBKey.REMEMBER_UID);
            DBUtil.Delete(DBKey.REMEMBER_PHONE_ZONE);
            DBUtil.Delete(DBKey.REMEMBER_PHONE);
            DBUtil.Delete(DBKey.USER_PASSWORD);
            DBUtil.Put(DBKey.FIXED_USER, true);
        }
    }

    /**
     * 1.2.8升级2.0.3由于更新了libsecretlib.so，会导致之前保存的密码错误
     */
    public void tryFixUserPwdIfNeedUpdate(@NonNull DinUser user) {
        final String pwd = user.getPassword();
        if (!DBUtil.Exists(DBKey.FIXED_USER) && DBUtil.Exists(DBKey.USER_PASSWORD) && !TextUtils.isEmpty(pwd)) {
            DBUtil.SPut(DBKey.USER_PASSWORD, pwd);
            DBUtil.Put(DBKey.FIXED_USER, true);
        }
    }

    public void autoLogin(final MainActivity activity, final String homeId) {
        DDLog.i(MainActivity.TEST_TIME, "autoLogin");
        DDLog.i("login", "LoginFailTest,tologin");
        DDLog.i(TAG, "统计时间==autoLogin==>Request time: "+(System.currentTimeMillis() - MainActivity.mStartTime)+"ms");
        try {
            UserManager.getInstance().autoLogin(new ILoginCallback() {
                @Override
                public void onSuccess(DinUser dinUser) {
                    DDLog.i(MainActivity.TEST_TIME, "autoLoginSuccess");
                    long afterLoginStart = System.currentTimeMillis();
                    DDLog.i(TAG, "统计时间==afterLoginStart===Start at"+afterLoginStart+"ms");
                    tryFixUserPwdIfNeedUpdate(dinUser);
                    CommonDataUtil.getInstance().setAlias(dinUser.getUser_id());
                    DBUtil.SPut(DBKey.USER_KEY, dinUser);
                    DBUtil.Put(DBKey.TOKEN, dinUser.getToken());
                    activity.removeAllCommonFragment();
                    activity.smoothToHome();
                    long afterLoginEnd = System.currentTimeMillis();
                    DDLog.i(TAG, "统计时间==afterLoginEnd===Start at"+afterLoginEnd+"ms");
                    DDLog.i(TAG, "统计时间==afterLogin==>Request time: "+(afterLoginEnd-afterLoginStart)+"ms");
                    DDLog.i(MainActivity.TEST_TIME, "fetchHomeList");
                    fetchHomeList(homeId);
                    EventBus.getDefault().post(new HadLoginEvent());
                }

                @Override
                public void onError(int i, String s) {
                    DDLog.e(TAG, "Auto login failed: i: " + i + ", s: " + s);
                    tryFixUserPwdIfNeedClean();
                    if (null != UserManager.getInstance().getUser()) {
                        loginFaillog(i + ": " + s,
                                UserManager.getInstance().getUser().getUid(),
                                UserManager.getInstance().getUser().getPassword());
                    }
                    EventBus.getDefault().post(new NeedLogoutEvent());
                }
            });
        } catch (Exception ex) {
            ex.printStackTrace();
            EventBus.getDefault().post(new NeedLogoutEvent());
        }
    }

    public void logUser() {
        if (UserManager.getInstance().getUser() != null) {
            DDLog.writeOnlineLog("user token is:" + UserManager.getInstance().getUser().getToken()
                    + ",user id is:" + UserManager.getInstance().getUser().getUid());
        } else {
            DDLog.writeOnlineLog("current user is null");
        }
    }

    public void uploadOnlineServerLog() {
//        有需要上传的日志
        if (APIKey.IS_UPLOAD_LOG && DDFileUtil.isFileExists(DDLog.mCacheLogPath)) {
            try {
                String log = DDFileUtil.getFileContents(new File(DDLog.mCacheLogPath));
                Call<StringResponseEntry> call = DinsafeAPI.getApi().getLogCallV2(log);
                call.enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        DDFileUtil.deleteFile(DDLog.mCacheLogPath);
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        DDFileUtil.deleteFile(DDLog.mCacheLogPath);
                    }
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 1、获取用户家庭列表
     * 2、获取指定家庭信息
     * 3、获取家庭的主机、涂鸦、IPC数据
     */
    public void fetchHomeList(String selectHomeId) {
        DDLog.i(MainActivity.TEST_TIME, "fetchHomeList");
        HomeManager.getInstance().fetchHomeList(new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                DDLog.i(MainActivity.TEST_TIME, "fetchHomeListSuccess");
                DDLog.i(TAG, "Success on fetchHomeList");
                long switchHomeStart = System.currentTimeMillis();
                DDLog.i(TAG, "统计时间switchHomeStart==="+switchHomeStart+"ms");
                if (HomeManager.getInstance().getHomeList().size() <= 0) {
                    DDLog.e(TAG, "Have no home now.");
                    EventBus.getDefault().post(new FetchHomeListEvent(true));
                    return;
                }

                // 确定要连接的home
                // 连接home ws
                // 切换主机
                boolean findTargetHome = false;
                if (!TextUtils.isEmpty(selectHomeId)) {
                    for (int i = 0; i < HomeManager.getInstance().getHomeList().size(); i++) {
                        if (selectHomeId.equals(HomeManager.getInstance().getHomeList().get(i).getHomeID())) {
                            findTargetHome = true;
                            break;
                        }
                    }
                }
                if (findTargetHome) {
                    long switchHomeEnd = System.currentTimeMillis();
                    DDLog.i(TAG, "统计时间switchHomeEnd==="+switchHomeEnd+"ms");
                    DDLog.i(TAG, "统计时间switchHome==>Request time: "+(switchHomeEnd - switchHomeStart)+"ms");
                    DDLog.i(MainActivity.TEST_TIME, "changeFamily");
                    HomeManager.getInstance().changeFamily(selectHomeId);
                } else {
                    final List<Home> homeList = HomeManager.getInstance().getHomeList();
                    final String currentId = DBUtil.Str(DBKey.CURRENT_HOME_ID);
                    int homeIndex = 0;
                    if (!TextUtils.isEmpty(currentId) && null != homeList && homeList.size() > 0) {
                        for (int i = 0; i < homeList.size(); i++) {
                            final String homeId = homeList.get(i).getHomeID();
                            if (currentId.equals(homeId)) {
                                homeIndex = i;
                                break;
                            }
                        }
                    }
                    long switchHomeEnd = System.currentTimeMillis();
                    DDLog.i(TAG, "统计时间switchHomeEnd==="+switchHomeEnd+"ms");
                    DDLog.i(TAG, "统计时间switchHome==>Request time: "+(switchHomeEnd - switchHomeStart)+"ms");
                    DDLog.i(MainActivity.TEST_TIME, "changeFamily");
                    HomeManager.getInstance().changeFamily(homeIndex);
                }
                EventBus.getDefault().post(new FetchHomeListEvent(true));
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error: i:" + i + ", s: " + s);
                EventBus.getDefault().post(new FetchHomeListEvent(false));
            }
        });
    }

    public void getAllData() {
        // TODO
//        EventBus.getDefault().post(new GetPanelInfoEvent());
    }

    public void getAllData(String id) {
        // TODO
//        EventBus.getDefault().post(new GetPanelInfoEvent(id));
    }

    public boolean checkHasKey(String key) {
        return sTypeItem.containsKey(key);
    }

    public String getSType(String sTypeID) {
        return (String) sTypeItem.get(sTypeID);
    }

    public String getASKNameByBSType(String sType) {
        return (String) sTypeItem.get(PluginTypeTransformHelper.transformAskSTypeBySType(sType));
    }

    public String getSTypeByID(String id) {
        String dinID = Dinsafe.str64ToHexStr(id);
        String sTypeID = dinID.substring(1, 3);
        final String dTypeString = dinID.substring(0, 1);
        int dType = Integer.parseInt(dTypeString, 16);

        return (String) sTypeItem.get(
                PluginTypeTransformHelper.transformSTypeByDTypeAndSType(dType, sTypeID));
    }

    public String getSTypeByDecodeid(String dinID) {
        String sTypeID = dinID.substring(1, 3);
        final String dTypeString = dinID.substring(0, 1);
        int dType = Integer.parseInt(dTypeString);

        return (String) sTypeItem.get(
                PluginTypeTransformHelper.transformSTypeByDTypeAndSType(dType, sTypeID));
    }

    /**
     * 根据小类获取设备类型名称
     *
     * @param sType
     * @return
     */
    public String getSTypeByType(String sType) {

        return (String) sTypeItem.get(sType);
    }

    /**
     * 根据DecodeId判断当前配件是否是警笛
     *
     * @param dinID decodeId
     * @return
     */
    public boolean checkIsSirenByDecodeId(String dinID) {
        if (TextUtils.isEmpty(dinID)
                || 4 > dinID.length()) {
            return false;
        }

        String sTypeID = dinID.substring(1, 3);
        return WIRELESS_SIREN.equals(sTypeID);
    }

    public boolean checkIsSiren(String id) {
        String dinID = Dinsafe.str64ToHexStr(id);
        String sTypeID = dinID.substring(1, 3);


//        旧版的逻辑，警笛id的12~13位!=01，才支持声光设置。加上这个判断

        DDLog.i("qr", "sTypeID :" + sTypeID + " dinID:" + dinID);
        if (WIRELESS_SIREN.equals(sTypeID)) {
            if (dinID.length() <= 11) {
                DDLog.i("qr", "<=11");
                return false;
            }
            String hasLight = dinID.substring(11, 13);
            DDLog.i("qr", "hasLight:" + hasLight);

            if (!hasLight.equals("01"))
                return true;
            else
                return false;
        } else {
            DDLog.i("qr", "false");
            return false;
        }

    }

    public int getIconByID(String id) {
        String dinID = Dinsafe.str64ToHexStr(id);
        String sTypeID = dinID.substring(1, 3);
        Integer iconResId = sTypeIcon.get(sTypeID);
        return null == iconResId ? 0 : iconResId;
    }

    public int getIconByBigIDAndSType(String bigType, String sType) {
        Integer iconResId = sTypeIcon.get(
                PluginTypeTransformHelper.transformSTypeByBigTypeAndSType(bigType, sType));
        return null == iconResId ? 0 : iconResId;
    }

    public String getNameByBigIDAndSType(int dType, String sTypeID) {
        return (String) sTypeItem.get(
                PluginTypeTransformHelper.transformSTypeByDTypeAndSType(dType, sTypeID));
    }

    public String getNameByStype(String stype) {
        if (TextUtils.isEmpty(stype)) {
            return null;
        }
        return (String) sTypeItem.get(stype);
    }

    public int getRIconByBigIDAndSType(String bigType, String sType) {
        Integer iconResId = rPluginIcon.get(
                PluginTypeTransformHelper.transformSTypeByBigTypeAndSType(bigType, sType));
        return null == iconResId ? 0 : iconResId;
    }

    public int getIconBySTypeId(String sTypeID) {
        Integer iconResId = sTypeIcon.get(sTypeID);
        return null == iconResId ? 0 : iconResId;
    }

    public void clearDB() {
        DDLog.d("cleardb", "cleardb");
        DBUtil.Delete(DBKey.CURRENT_HOME_ID);
        DBUtil.Delete(DBKey.USER_KEY);
        DBUtil.Delete(DBKey.USER_MAIN_DEVICE_LIST);
        DBUtil.Delete(DBKey.TOKEN);
//        DBUtil.Delete(DBKey.USER_PASSWORD);
        DBUtil.Delete(DBKey.DEVICE_TOKEN);
//        DBUtil.Delete(DBKey.TOOLBAR_DATA_ADMIN);
//        DBUtil.Delete(DBKey.TOOLBAR_DATA_USER);
        DBUtil.Delete(DBKey.APP_PASSWORD);
//        DBUtil.Delete(DBKey.APIKEY);

        DBUtil.Delete(DBKey.WIDGET_CURRENT_DEVICE_NAME);
        DBUtil.Delete(DBKey.WIDGET_CURRENT_DEVICE_TOKEN);
        DBUtil.Delete(DBKey.WIDGET_CURRENT_FAMILY_NAME);
        DBUtil.Delete(DBKey.WIDGET_FAMILY_LEVEL);
        DinSaferApplication.getAppContext().sendBroadcast(new Intent("com.dinsafer.widget.UPDATE_ALL"));
    }

    public boolean checkShowPlugin(String version) {
        if (DDSystemUtil.VersionComparison(version, APIKey.MIN_PLUGIN_VERSION) != -1) {
            return true;
        }
        return false;
    }

    public boolean checkChangeMessage(String version) {
        if (DDSystemUtil.VersionComparison(version, APIKey.MIN_CHANGEMESSAGE_VERSION) != -1) {
            return true;
        }
        return false;
    }

    private Handler mCheckHandler = new Handler();

    public void setAlias(final String userId) {
        mCheckHandler.removeCallbacksAndMessages(null);
        DinsaferPushManager.setAlias(userId, new AliasCallback() {
            @Override
            public void onSuccess() {
                DLog.e(Const.TAG, "setAlias --> success");
                mPushAliasUserId = userId;
            }

            @Override
            public void onFail(String message) {
                DLog.e(Const.TAG, "setAlias --> onFail: " + message);
            }
        });
    }

    /**
     * 反注册推送设置的别名
     */
    public void unSetAlias() {
//        TODO 退出涂鸦，在UserManager里面，退出用户的时候，再退出涂鸦，
        String userId;
        if (null != DinSDK.getUserInstance().getUser()
                && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUser_id())) {
            userId = DinSDK.getUserInstance().getUser().getUser_id();
        } else {
            userId = mPushAliasUserId;
        }

        if (!TextUtils.isEmpty(userId)) {
            DinsaferPushManager.unSetAlias(userId, new AliasCallback() {
                @Override
                public void onSuccess() {
                    Log.e(Const.TAG, "unSetAlias --> onSuccess: ");
                }

                @Override
                public void onFail(String s) {
                    Log.e(Const.TAG, "unSetAlias --> onFail: " + s);
                }
            });
            mPushAliasUserId = null;
        } else {
            DDLog.e(Const.TAG, "unSetAlias --> user is null, skip.");
        }
    }

    public String getWsIp() {
        if (DBUtil.Exists(DBKey.APIKEY)) {
            String tempUrl = "wss://" + DBUtil.Str(DBKey.APIKEY) + "/device/ws/v2/" + APIKey.APP_ID;
            return tempUrl;
        }
        return "wss://" + APIKey.DOMIAN + "/device/ws/v2/" + APIKey.APP_ID;
    }

    public ArrayList<TuyaItem> filterData(ArrayList<TuyaItem> datas) {
        ArrayList<TuyaItem> newData = new ArrayList<TuyaItem>();
        String dbData = DBUtil.Str(getCurrentDeviceId());
        for (int i = 0; i < datas.size(); i++) {
            newData.add(datas.get(i));
        }
        return newData;

    }

    public boolean isShowDeviceArmHomeArmSound() {
        if (TextUtils.isEmpty(currentPanelID)
                || null == DinHome.getInstance().getDevice(currentPanelID)
                || TextUtils.isEmpty(DeviceHelper.getString(DinHome.getInstance().getDevice(currentPanelID),
                PanelDataKey.Panel.FIRMWARE_VERSION, null))) {
            return false;
        }
        String currenthardwareversion = DeviceHelper.getString(DinHome.getInstance().getDevice(currentPanelID),
                PanelDataKey.Panel.FIRMWARE_VERSION, "");
        String[] tmp = currenthardwareversion.split("/");
        return APIKey.IS_OPEN_DEVICE_ARM_DISARM_SOUND
                && (DDSystemUtil.VersionComparison(tmp[0], APIKey.MIN_ARM_SOUND_VERSION) == 1);
    }

    private boolean isCanCoap = false;

    public boolean isCanCoap() {
        return isCanCoap;
    }

    public void setCanCoap(boolean canCoap) {
        isCanCoap = canCoap;
    }

    private boolean isBleToAdd = false;

    public boolean isCanToAddBleDevice() {
        return isCanToAddBleDevice;
    }

    public void setCanToAddBleDevice(boolean canToAddBleDevice) {
        isCanToAddBleDevice = canToAddBleDevice;
    }

    private boolean isCanToAddBleDevice = false;


    private boolean isNeedToUserView = false;

    public boolean isNeedToUserView() {
        return isNeedToUserView;
    }

    public void setNeedToUserView(boolean needToUserView) {
        isNeedToUserView = needToUserView;
    }

    public boolean isBleToAdd() {
        return isBleToAdd;
    }

    private boolean isBleToChange = false;

    public boolean isBleToChange() {
        return isBleToChange;
    }


    public void setBleToAdd(boolean bleToAdd) {
        isBleToAdd = bleToAdd;
    }

    public void setBleToChange(boolean bleToChange) {
        isBleToChange = bleToChange;
    }

    private void loginFaillog(String msg, String uid, String pwd) {
        try {
            DDLog.d("login", "LoginFailTest,原因:" + msg);

            DDLog.d("login", "LoginFailTest,uid:" + uid + ",pwd" + pwd);

            DDLog.d("login", "LoginFailTest,ip:" + DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getIp());


            String pingmsg = "";
            String ip = DinSaferApplication.getHttpdns().getIpByHostAsync(APIKey.DOMIAN);
            if (TextUtils.isEmpty(ip)) {
                pingmsg = "dns不正常";
            } else {
                int dnsTime = PingUtil.getAvgRTT(APIKey.SERVER_IP);
                if (dnsTime == -1 || dnsTime > 200) {
                    pingmsg = "dns(" + ip + ")不正常";
                } else {
                    pingmsg = "dns(" + ip + ")正常";
                }
            }


            DDLog.d("login", "LoginFailTest,ping" + APIKey.DOMIAN + ":" + pingmsg);
        } catch (Exception e) {
        }
    }

    /**
     * 这里存储switchbot的状态类别是因为在黑名单编辑列表中需要知道switchbot的状态
     * 而CommonDataUtil.getInstance().getMultiDataEntry().getResult().getSwitchbotdata()只能获取收藏列表而没有状态
     * <p>
     * 不是通过给mainsection传值是因为，单纯给switchbotMainSection传，长按其他按钮类似'添加配件'时，没有switchbot状态列表数据传递给MainSectionEditFragment。
     * 所以放在了这里
     */
    private ArrayList<TuyaItem> switchBotStatusList = new ArrayList<TuyaItem>();

    public ArrayList<TuyaItem> getSwitchBotStatusList() {
        return switchBotStatusList;
    }

    public void setSwitchBotStatusList(ArrayList<TuyaItem> switchBotStatusList) {
        this.switchBotStatusList = switchBotStatusList;
    }

    /**
     * 初始化不同类别的配件ID
     */
    private void initPluginTypeIds() {
        mBatteryLevelPluginIds = new ArrayList<>();
        // 门磁
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_2C);
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_38);
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_3D);
        // 安防配件
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_36); // 红外
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_4A); // 红外
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_39); // 水感
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_3B); // 紧急按钮
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_3C); // 烟感
        // 遥控、键盘
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_2F);
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_3A);
        // 警笛
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_34);
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_35);
        // 有线桥接器
        mBatteryLevelPluginIds.add(PluginConstants.TYPE_3F);

        mReady2ArmIds = new ArrayList<>();
        // 门磁
        mReady2ArmIds.add(PluginConstants.TYPE_11);
        mReady2ArmIds.add(PluginConstants.TYPE_1C);
        mReady2ArmIds.add(PluginConstants.TYPE_25);
        mReady2ArmIds.add(PluginConstants.TYPE_38);
        mReady2ArmIds.add(PluginConstants.TYPE_3D);

        mTamperIds = new ArrayList<>();
        mTamperIds.add(PluginConstants.TYPE_2C);
        mTamperIds.add(PluginConstants.TYPE_2F);
        mTamperIds.add(PluginConstants.TYPE_3D);
        mTamperIds.add(PluginConstants.TYPE_34);
        mTamperIds.add(PluginConstants.TYPE_35);
        mTamperIds.add(PluginConstants.TYPE_36);
        mTamperIds.add(PluginConstants.TYPE_4A);
        mTamperIds.add(PluginConstants.TYPE_38);
        mTamperIds.add(PluginConstants.TYPE_39);
        mTamperIds.add(PluginConstants.TYPE_3F);

        mRssiIds = new ArrayList<>();
        mRssiIds.add(PluginConstants.TYPE_2C);
        mRssiIds.add(PluginConstants.TYPE_2F);
        mRssiIds.add(PluginConstants.TYPE_3D);
        mRssiIds.add(PluginConstants.TYPE_3E);
        mRssiIds.add(PluginConstants.TYPE_34);
        mRssiIds.add(PluginConstants.TYPE_35);
        mRssiIds.add(PluginConstants.TYPE_36);
        mRssiIds.add(PluginConstants.TYPE_4A);
        mRssiIds.add(PluginConstants.TYPE_38);
        mRssiIds.add(PluginConstants.TYPE_39);
        mRssiIds.add(PluginConstants.TYPE_3A);
        mRssiIds.add(PluginConstants.TYPE_3B);
        mRssiIds.add(PluginConstants.TYPE_3C);
        mRssiIds.add(PluginConstants.TYPE_3F);

        mChargingIds = new ArrayList<>();
        mChargingIds.add(PluginConstants.TYPE_2F);
    }

    /**
     * 配件是否有需要从websocket返回的电量百分比信息
     */
    public boolean isPluginHasBatteryLevel(String typeId) {
        return mBatteryLevelPluginIds.contains(typeId);
    }

    /**
     * 配件是否有需要从websocket返回的开合状态信息
     */
    public boolean isPluginCanReady2Arm(String typeId) {
        return mReady2ArmIds.contains(typeId);
    }

    /**
     * 配件是否有需要从websocket返回的防拆状态信息
     */
    public boolean isPluginHadTamper(String typeId) {
        return mTamperIds.contains(typeId);
    }

    /**
     * 配件是否有需要从websocket返回的配件信号强度信息
     */
    public boolean isPluginHadRssi(String typeId) {
        return mRssiIds.contains(typeId);
    }

    /**
     * 配件是否需要从websocket返回的充电状态信息
     */
    public boolean isPluginCanCharging(String typeId) {
        return mChargingIds.contains(typeId);
    }

    /**
     * 配件是否包含从websocket返回的状态
     */
    public boolean isPluginHadWebsocketStatus(String typeId) {
        return isPluginHasBatteryLevel(typeId)
                || isPluginCanReady2Arm(typeId)
                || isPluginHadTamper(typeId)
                || isPluginHadRssi(typeId)
                || isPluginCanCharging(typeId);
    }

    /**
     * 判断当前自研插座是否新型ASK类型
     *
     * @param stype
     * @return
     */
    public boolean isSmartPlugAsk(String stype) {
        return "3E".equals(stype);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onGetAllDataEvent(GetDeviceInfoEvent getAllDataEvent) {
        if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    requestCurrentIPCAlertServicePlan();
                }
            }, 1000);
        }
    }

    private DeviceAlertServicePlanResponse.AlertServicePlanInfo alertServicePlanInfo = null;

    public DeviceAlertServicePlanResponse.AlertServicePlanInfo getAlertServicePlanInfo() {
        return alertServicePlanInfo;
    }

    public void requestCurrentIPCAlertServicePlan() {
        Call<DeviceAlertServicePlanResponse> currentIPCAlertServicePlanCall = DinsafeAPI.getApi()
                .getCurrentIPCAlertServicePlan(CommonDataUtil.getInstance().getCurrentDeviceId());
        currentIPCAlertServicePlanCall.enqueue(new Callback<DeviceAlertServicePlanResponse>() {
            @Override
            public void onResponse(Call<DeviceAlertServicePlanResponse> call, Response<DeviceAlertServicePlanResponse> response) {
                alertServicePlanInfo = response.body().getResult();
                EventBus.getDefault().post(new AlertServicePlanUpdateEvent());
            }

            @Override
            public void onFailure(Call<DeviceAlertServicePlanResponse> call, Throwable t) {
            }
        });

    }

    /**
     * 更新订阅信息
     *
     * @param remainder
     * @param ot_remainder
     * @param subscriptionInfo
     */
    public void updateAlertServiceInfo(Integer remainder, Integer ot_remainder, DeviceAlertServicePlanResponse.AlertServicePlanInfo.SubscriptionInfo subscriptionInfo) {
        //更新订阅信息
        alertServicePlanInfo.setRemainder(remainder);
        alertServicePlanInfo.setOt_remainder(ot_remainder);
//        alertServicePlanInfo.setGmtime(ipcAlertServiceSetting.getGmtime());
        alertServicePlanInfo.setSubscription(subscriptionInfo);
        EventBus.getDefault().post(new AlertServicePlanUpdateEvent());

    }

    private String currentPanelID;

    public String getCurrentPanelID() {
        return currentPanelID;
    }

    public void setCurrentPanelID(String currentPanelID) {
        this.currentPanelID = currentPanelID;
    }

    /**
     * 当前家庭是否有主机
     *
     * @return true: 当前家庭已有主机
     */
    public boolean isHadPanel() {
        return !TextUtils.isEmpty(currentPanelID);
    }

    /**
     * 当前家庭是否有未被删除的主机
     *
     * @return true: 当前家庭已有主机
     */
    public boolean isHadPanelNotDeleted() {
        final Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentDeviceId());
        return !TextUtils.isEmpty(currentPanelID)
                && null != device
                && !device.getFlagDeleted();
    }

    public void setPushAliasUserId(String pushAliasUserId) {
        this.mPushAliasUserId = pushAliasUserId;
    }

    /**
     * 获取RF配件默认名字
     *
     * @param device
     * @return
     */
    public String getDefaultRFDeviceName(Device device) {
        String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
        final String decodeId = DeviceHelper.getString(device, PanelDataKey.DECODE_ID, "");
        final String pluginId = device.getId();
        String subcategory = device.getSubCategory();
        if (TextUtils.isEmpty(subcategory)) {
            subcategory = DeviceHelper.getString(device, PanelDataKey.SUBCATEGORY, "");
        }
        if (TextUtils.isEmpty(subcategory)) {
            subcategory = DeviceHelper.getString(device, PanelDataKey.S_TYPE, "");
        }
        if (TextUtils.isEmpty(pluginName)) {
            String name;
            if (!TextUtils.isEmpty(decodeId)) {
                //  如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                name = CommonDataUtil.getInstance().getSTypeByDecodeid(decodeId);
            } else if (pluginId.startsWith("!")) {
                name = CommonDataUtil.getInstance().getASKNameByBSType(subcategory);
            } else {
                name = CommonDataUtil.getInstance().getSTypeByID(pluginId);
            }
            if (TextUtils.isEmpty(name)) {
                pluginName = "_" + pluginId;
            } else {
                pluginName = Local.s(name) + "_" + pluginId;
            }
        }

        return null == pluginName ? "" : pluginName;
    }
}
