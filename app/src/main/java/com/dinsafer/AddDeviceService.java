package com.dinsafer;


import android.annotation.TargetApi;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import android.text.TextUtils;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.AddDeviceResultEvent;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.ws.IWebSocketCallBack;
import com.dinsafer.ws.WebSocketManager;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

import okhttp3.Response;
import okhttp3.WebSocket;


/**
 * Created by Rinfon on 16/8/23.
 */
public class AddDeviceService extends Service implements IWebSocketCallBack {

    private static final String TAG = "AddDeviceService";
    public static final int SERVICE_ID = 889;
    public static final String USERTOKEN = "usertoken";
    public static final String DEVICETOKEN = "devicetoken";
    private WebSocketServiceBinder webSocketServiceBinder;
    private String deviceToken, userToken;

    private WebSocketManager mWebSocketManager;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        DDLog.d(TAG, "onBind()");
        if (webSocketServiceBinder == null) {
            webSocketServiceBinder = new WebSocketServiceBinder();
            userToken = intent.getStringExtra(USERTOKEN);
            deviceToken = intent.getStringExtra(DEVICETOKEN);
            DDLog.i(TAG, "onBind" + userToken + ":" + deviceToken);
        }
        return webSocketServiceBinder;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        DDLog.d(TAG, "onCreate()");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForeground(SERVICE_ID, new Notification());
            startMyOwnForeground(SERVICE_ID);
        }

        mWebSocketManager = new WebSocketManager(false, false,
                CommonDataUtil.getInstance().getWsIp());
        mWebSocketManager.addCallBack(this);
    }

    @Override
    public void onStart(Intent intent, int startId) {
        super.onStart(intent, startId);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return super.onStartCommand(intent, flags, startId);
    }


    public synchronized void connectWebSocket() {

        if (mWebSocketManager.getWebSocket() != null) {
            mWebSocketManager.stop();
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                startConnectWebSocket();
            }
        }).start();
    }


    private void startConnectWebSocket() {
        DDLog.i(TAG, "startConnectWebSocket");
        mWebSocketManager.start();
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
        try {
            String message = userToken + "&" + deviceToken
                    + "_" + System.currentTimeMillis() * 1000;
            webSocket.send(DDSecretUtil.getSC(message));
        } catch (Exception e) {
            DDLog.i(TAG, "Unable to send messages: " + e.getMessage());
        }
    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {

    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {

    }

    @Override
    public void onMessage(String messageStr) {
        DDLog.i(TAG, "MESSAGE: " + messageStr);
        if ("1".equals(messageStr)) {
        } else {
            try {
                JSONObject jsonObject = new JSONObject(messageStr);
                if (LocalKey.EVENT_RESULT.equals(jsonObject.getString("Action"))) {
                    String type = jsonObject.getString("Cmd");
                    AddDeviceResultEvent event = new AddDeviceResultEvent(type, jsonObject.getInt("Status")
                            , jsonObject.getString("MessageId"));
                    if (TextUtils.isEmpty(jsonObject.getString("Result"))) {
                        event.setReslut("");
                    } else {
                        event.setReslut(DDSecretUtil.getReverSC(jsonObject.getString("Result")));
                    }
                    EventBus.getDefault().post(event);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void toCloseWs() {
        if (mWebSocketManager.getWebSocket() != null) {
            mWebSocketManager.close();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mWebSocketManager != null && mWebSocketManager.getWebSocket() != null) {
            mWebSocketManager.close();
            mWebSocketManager.release();
            mWebSocketManager = null;
        }
    }


    public class WebSocketServiceBinder extends Binder {

        public AddDeviceService getService() {
            return AddDeviceService.this;
        }

    }

    public String getDeviceToken() {
        return deviceToken;
    }

    public void setDeviceToken(String deviceToken) {
        this.deviceToken = deviceToken;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }


    @TargetApi(Build.VERSION_CODES.O)
    private void startMyOwnForeground(int id) {
        String NOTIFICATION_CHANNEL_ID = getResources().getString(R.string.app_name);
        String channelName = "My Background Service";
        NotificationChannel chan = new NotificationChannel(NOTIFICATION_CHANNEL_ID, channelName, NotificationManager.IMPORTANCE_NONE);
        chan.setLightColor(Color.BLUE);
        chan.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        assert manager != null;
        manager.createNotificationChannel(chan);

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID);
        Notification notification = notificationBuilder.setOngoing(true)
                .setSmallIcon(R.mipmap.icon_notification_tran_bg)
                .setContentTitle("")
                .setPriority(NotificationManager.IMPORTANCE_MIN)
                .setCategory(Notification.CATEGORY_SERVICE)
                .build();
        // if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        //     startForeground(id, notification, FOREGROUND_SERVICE_TYPE_DATA_SYNC);
        // } else {
        //     startForeground(id, notification);
        // }
    }
}
