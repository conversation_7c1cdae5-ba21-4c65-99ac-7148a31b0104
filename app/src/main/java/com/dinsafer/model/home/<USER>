package com.dinsafer.model.home;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.IntDef;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.common.PluginManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.config.PsVersion1EventCode;
import com.dinsafer.config.PsVersion2EventCode;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.model.EventListComparator;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;

import org.json.JSONArray;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页EventList数据处理工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/27 4:10 PM
 */
public class EventListHelper {
    private static final String TAG = EventListHelper.class.getSimpleName();
    private Map<String, String> mCmdMap;
    private Map<String, String> mBmtOnePointZeroCmdMap;
    private Map<String, String> mBmtTwoPointZeroCmdMap;

    private EventListHelper() {
        mEventComparator = new EventListComparator();
    }

    private static class Holder {
        private static final EventListHelper instance = new EventListHelper();
    }

    public static EventListHelper getInstance() {
        return Holder.instance;
    }

    private EventListComparator mEventComparator;

    public EventListComparator getEventComparator() {
        return mEventComparator;
    }

    /**
     * 事件级别定义
     */
    public final static int EVENT_LEVEL_ALARM = 0;
    public final static int EVENT_LEVEL_SECURITY = 1;
    public final static int EVENT_LEVEL_NORMAL = 2;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({EVENT_LEVEL_ALARM, EVENT_LEVEL_SECURITY, EVENT_LEVEL_NORMAL})
    public @interface EventLevel {
    }

    public static final int MARK_EXCEPTIONS = 1;
    public static final int MARK_OUTAGE_POWER_BACKUP = 1 << 1;
    public static final int MARK_POWER_STATION_NORMAL_EVENTS = 1 << 2;
    public static final int MARK_MOTION_DETECTION = 1 << 3;
    public static final int MARK_SMART_CAMERA_NORMAL_EVENTS = 1 << 4;
    public static final int MARK_ALARM = 1 << 5;
    public static final int MARK_SECURITY_STATUS = 1 << 6;
    public static final int MARK_DOOR_WINDOW_STATUS = 1 << 7;
    public static final int MARK_TAMPER_TRIGGERED = 1 << 8;
    public static final int MARK_ALARM_SYSTEM_NORMAL_EVENTS = 1 << 9;
    public static final int MARK_ACCESSORIES_STATUS = 1 << 10;
    public static final int MARK_FAMILY_NORMAL_EVENTS = 1 << 11;
    public static final int MARK_DAILY_MEMORIES_EVENTS = 1 << 12;

    private final int[] mMarkType = new int[]{
            MARK_EXCEPTIONS, MARK_OUTAGE_POWER_BACKUP, MARK_POWER_STATION_NORMAL_EVENTS
            , MARK_MOTION_DETECTION, MARK_SMART_CAMERA_NORMAL_EVENTS, MARK_ALARM, MARK_SECURITY_STATUS
            , MARK_DOOR_WINDOW_STATUS, MARK_TAMPER_TRIGGERED, MARK_ALARM_SYSTEM_NORMAL_EVENTS, MARK_ACCESSORIES_STATUS
            , MARK_FAMILY_NORMAL_EVENTS, MARK_DAILY_MEMORIES_EVENTS};

    public int[] getAllMarkType() {
        return mMarkType;
    }

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({MARK_EXCEPTIONS, MARK_OUTAGE_POWER_BACKUP, MARK_POWER_STATION_NORMAL_EVENTS
            , MARK_MOTION_DETECTION, MARK_SMART_CAMERA_NORMAL_EVENTS, MARK_ALARM, MARK_SECURITY_STATUS
            , MARK_DOOR_WINDOW_STATUS, MARK_TAMPER_TRIGGERED, MARK_ALARM_SYSTEM_NORMAL_EVENTS, MARK_ACCESSORIES_STATUS
            , MARK_FAMILY_NORMAL_EVENTS, MARK_DAILY_MEMORIES_EVENTS})
    public @interface MarkType {
    }

    public static final int EVENT_TYPE_POWER_STATION = 0;
    public static final int EVENT_TYPE_SMART_CAMERA = 1;
    public static final int EVENT_TYPE_ALARM_SYSTEM = 2;
    public static final int EVENT_TYPE_ACCESSORIES = 3;
    public static final int EVENT_TYPE_FAMILY = 4;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({EVENT_TYPE_POWER_STATION, EVENT_TYPE_SMART_CAMERA, EVENT_TYPE_ALARM_SYSTEM, EVENT_TYPE_ACCESSORIES, EVENT_TYPE_FAMILY})
    public @interface EventType {
    }

    private Map<Integer, Boolean> eventVisibleMap;

    public void initFilter() {
        if (DBUtil.Exists(getFilterKey())) {
            localFilter = DBUtil.Num(getFilterKey());
        } else {
            // 默认全选
//            final int powerStation = MARK_EXCEPTIONS | MARK_OUTAGE_POWER_BACKUP | MARK_POWER_STATION_NORMAL_EVENTS;
//            final int smartCamera = MARK_MOTION_DETECTION | MARK_SMART_CAMERA_NORMAL_EVENTS | MARK_DAILY_MEMORIES_EVENTS;
//            final int alarmSystem = MARK_ALARM | MARK_SECURITY_STATUS | MARK_DOOR_WINDOW_STATUS
//                    | MARK_TAMPER_TRIGGERED | MARK_ALARM_SYSTEM_NORMAL_EVENTS;
//            final int accessories = MARK_ACCESSORIES_STATUS;
//            final int family = MARK_FAMILY_NORMAL_EVENTS;
//            if (AppConfig.EventListFilter.POWER_STATION) {
//                localFilter |= powerStation;
//            } else {
//                localFilter &= ~powerStation;
//            }
//            if (AppConfig.EventListFilter.SMART_CAMERA) {
//                localFilter |= smartCamera;
//            } else {
//                localFilter &= ~smartCamera;
//            }
//            if (AppConfig.EventListFilter.ALARM_SYSTEM) {
//                localFilter |= alarmSystem;
//            } else {
//                localFilter &= ~alarmSystem;
//            }
//            if (AppConfig.EventListFilter.ACCESSORIES) {
//                localFilter |= accessories;
//            } else {
//                localFilter &= ~accessories;
//            }
//            if (AppConfig.EventListFilter.FAMILY) {
//                localFilter |= family;
//            } else {
//                localFilter &= ~family;
//            }
            updateEventSonTypeVisible(true);
        }
//        eventVisibleMap = null;
    }

    public String getFilterKey() {
        return DBKey.KEY_EVENT_LIST_BY_FILTER + HomeManager.getInstance().getCurrentHome().getHomeID();
    }

    private int localFilter;

    /**
     * 获取EventList分类名
     *
     * @param context
     * @param name
     * @return
     */
    public String getCmdName(Context context, String name, String model) {
        String actualName = "";
        if (TextUtils.isEmpty(model)) {
            if (mCmdMap == null) {
                String[] supportKey = context.getResources().getStringArray(R.array.family_ipc_event_key);
                String[] supportValue = context.getResources().getStringArray(R.array.family_ipc_event_value);
                if (supportKey.length != supportValue.length) {
                    DDLog.e(TAG, "supportKey 和 supportValue数据长度不一致");
                    throw new ArrayIndexOutOfBoundsException("supportKey 和 supportValue数据长度不一致");
                }
                mCmdMap = new HashMap<>(supportKey.length);
                for (int i = 0; i < supportKey.length; i++) {
                    mCmdMap.put(supportKey[i], supportValue[i]);
                }
            }
            actualName = mCmdMap.get(name);
        } else {
            if (DinConst.TYPE_BMT_HP5000.equals(model) || DinConst.TYPE_BMT_HP5001.equals(model)) {
                if (mBmtOnePointZeroCmdMap == null) {
                    String[] supportKey = context.getResources().getStringArray(R.array.bmt_one_point_zero_event_key);
                    String[] supportValue = context.getResources().getStringArray(R.array.bmt_one_point_zero_event_value);
                    if (supportKey.length != supportValue.length) {
                        DDLog.e(TAG, "supportKey 和 supportValue数据长度不一致");
                        throw new ArrayIndexOutOfBoundsException("supportKey 和 supportValue数据长度不一致");
                    }
                    mBmtOnePointZeroCmdMap = new HashMap<>(supportKey.length);
                    for (int i = 0; i < supportKey.length; i++) {
                        mBmtOnePointZeroCmdMap.put(supportKey[i], supportValue[i]);
                    }
                }
                actualName = mBmtOnePointZeroCmdMap.get(name);
            } else if (DinConst.TYPE_BMT_POWERCORE20.equals(model)
                    || DinConst.TYPE_BMT_POWERCORE30.equals(model)
                    || DinConst.TYPE_BMT_POWERPULSE.equals(model)
                    || DinConst.TYPE_BMT_POWERSTORE.equals(model)) {
                if (mBmtTwoPointZeroCmdMap == null) {
                    String[] supportKey = context.getResources().getStringArray(R.array.bmt_two_point_zero_event_key);
                    String[] supportValue = context.getResources().getStringArray(R.array.bmt_two_point_zero_event_value);
                    if (supportKey.length != supportValue.length) {
                        DDLog.e(TAG, "supportKey 和 supportValue数据长度不一致");
                        throw new ArrayIndexOutOfBoundsException("supportKey 和 supportValue数据长度不一致");
                    }
                    mBmtTwoPointZeroCmdMap = new HashMap<>(supportKey.length);
                    for (int i = 0; i < supportKey.length; i++) {
                        mBmtTwoPointZeroCmdMap.put(supportKey[i], supportValue[i]);
                    }
                }
                actualName = mBmtTwoPointZeroCmdMap.get(name);
            }
        }
        return actualName != null ? Local.s(actualName) : name;
    }

    /**
     * 根据事件CMD类型获取事件级别
     *
     * @param cmd
     * @return
     */
    public int getEventLevelByCmd(String cmd, String bmtModel) {
        int eventLevel = EVENT_LEVEL_NORMAL;
        if (TextUtils.isEmpty(cmd)) {
            return eventLevel;
        }

        if (!TextUtils.isEmpty(bmtModel)) {
            if (DinConst.TYPE_BMT_HP5000.equals(bmtModel) || DinConst.TYPE_BMT_HP5001.equals(bmtModel)) {
                eventLevel = getPs1EventLevelByCmd(cmd);
                return eventLevel;
            } else if (DinConst.TYPE_BMT_POWERCORE20.equals(bmtModel)
                    || DinConst.TYPE_BMT_POWERCORE30.equals(bmtModel)
                    || DinConst.TYPE_BMT_POWERPULSE.equals(bmtModel)
                    || DinConst.TYPE_BMT_POWERSTORE.equals(bmtModel)) {
                eventLevel = getPs2EventLevelByCmd(cmd);
                return eventLevel;
            }
        }

        switch (cmd) {
            case LocalKey.NO_ACTION_SOS:
            case LocalKey.TASK_SOS:
            case LocalKey.TASK_INTIMIDATIONALARM_SOS:
            case LocalKey.TASK_ANTIINTERFER_SOS:
            case LocalKey.TASK_FC_SOS:
            case LocalKey.TASK_FC_SOS_PANEL:
                eventLevel = EVENT_LEVEL_ALARM;
                break;

            case LocalKey.ARM_KEY:
            case LocalKey.DISARM_KEY:
            case LocalKey.HOMEARM_KEY:
                eventLevel = EVENT_LEVEL_SECURITY;
                break;

            case LocalKey.SET_PASSWORD:
            case LocalKey.LOW_BATTERY:
            case LocalKey.EVENT_LOWERPOWER:
            case LocalKey.SET_SMART_PLUG_ENABLE_ON:
            case LocalKey.SET_SMART_PLUG_ENABLE_OFF:
            case LocalKey.TASK_SMARTFOLLOWING:
            case LocalKey.PLUGIN_OFFLINE:
            case LocalKey.PLUGIN_ONLINE:
            case LocalKey.TY_SWITCH_ON:
            case LocalKey.TY_SWITCH_OFF:
            case LocalKey.TY_BLUB_ON:
            case LocalKey.TY_BLUB_OFF:
            case LocalKey.IPC_LOW_BATTERY:
            default:
                eventLevel = EVENT_LEVEL_NORMAL;
                break;
        }

        return eventLevel;
    }

    private int getPs1EventLevelByCmd(String cmd) {
        int eventLevel = EVENT_LEVEL_NORMAL;

        switch (cmd) {
            case PsVersion1EventCode.EVENT_PS_0001:
            case PsVersion1EventCode.EVENT_PS_0002:
            case PsVersion1EventCode.EVENT_PS_0101:
            case PsVersion1EventCode.EVENT_PS_0102:
            case PsVersion1EventCode.EVENT_PS_0103:
            case PsVersion1EventCode.EVENT_PS_0104:
            case PsVersion1EventCode.EVENT_PS_0105:
            case PsVersion1EventCode.EVENT_PS_0106:
            case PsVersion1EventCode.EVENT_PS_0107:
            case PsVersion1EventCode.EVENT_PS_0201:
            case PsVersion1EventCode.EVENT_PS_1000:
            case PsVersion1EventCode.EVENT_PS_1001:
            case PsVersion1EventCode.EVENT_PS_1002:
            case PsVersion1EventCode.EVENT_PS_1003:
            case PsVersion1EventCode.EVENT_PS_1004:
            case PsVersion1EventCode.EVENT_PS_1005:
            case PsVersion1EventCode.EVENT_PS_1006:
            case PsVersion1EventCode.EVENT_PS_1007:
            case PsVersion1EventCode.EVENT_PS_1008:
            case PsVersion1EventCode.EVENT_PS_1009:
            case PsVersion1EventCode.EVENT_PS_1010:
            case PsVersion1EventCode.EVENT_PS_1100:
            case PsVersion1EventCode.EVENT_PS_1101:
            case PsVersion1EventCode.EVENT_PS_1102:
            case PsVersion1EventCode.EVENT_PS_1103:
            case PsVersion1EventCode.EVENT_PS_1104:
            case PsVersion1EventCode.EVENT_PS_1105:
            case PsVersion1EventCode.EVENT_PS_1106:
            case PsVersion1EventCode.EVENT_PS_1107:
            case PsVersion1EventCode.EVENT_PS_1108:
            case PsVersion1EventCode.EVENT_PS_1109:
            case PsVersion1EventCode.EVENT_PS_1110:
            case PsVersion1EventCode.EVENT_PS_1111:
            case PsVersion1EventCode.EVENT_PS_1200:
            case PsVersion1EventCode.EVENT_PS_1201:
            case PsVersion1EventCode.EVENT_PS_1202:
            case PsVersion1EventCode.EVENT_PS_1203:
            case PsVersion1EventCode.EVENT_PS_1204:
            case PsVersion1EventCode.EVENT_PS_1205:
            case PsVersion1EventCode.EVENT_PS_1206:
            case PsVersion1EventCode.EVENT_PS_1207:
            case PsVersion1EventCode.EVENT_PS_1208:
            case PsVersion1EventCode.EVENT_PS_1209:
            case PsVersion1EventCode.EVENT_PS_1210:
            case PsVersion1EventCode.EVENT_PS_1211:
            case PsVersion1EventCode.EVENT_PS_1212:
            case PsVersion1EventCode.EVENT_PS_1213:
            case PsVersion1EventCode.EVENT_PS_1300:
            case PsVersion1EventCode.EVENT_PS_1301:
            case PsVersion1EventCode.EVENT_PS_1302:
            case PsVersion1EventCode.EVENT_PS_1303:
            case PsVersion1EventCode.EVENT_PS_1304:
            case PsVersion1EventCode.EVENT_PS_1305:
            case PsVersion1EventCode.EVENT_PS_1306:
            case PsVersion1EventCode.EVENT_PS_1307:
            case PsVersion1EventCode.EVENT_PS_1308:
            case PsVersion1EventCode.EVENT_PS_1309:
            case PsVersion1EventCode.EVENT_PS_1310:
            case PsVersion1EventCode.EVENT_PS_1311:
            case PsVersion1EventCode.EVENT_PS_1312:

            case PsVersion1EventCode.EVENT_PS_1400:
            case PsVersion1EventCode.EVENT_PS_1401:
            case PsVersion1EventCode.EVENT_PS_1402:
            case PsVersion1EventCode.EVENT_PS_1403:
            case PsVersion1EventCode.EVENT_PS_1404:
            case PsVersion1EventCode.EVENT_PS_1600:
            case PsVersion1EventCode.EVENT_PS_1601:
            case PsVersion1EventCode.EVENT_PS_2000:
            case PsVersion1EventCode.EVENT_PS_2001:
            case PsVersion1EventCode.EVENT_PS_2002:
            case PsVersion1EventCode.EVENT_PS_2003:
            case PsVersion1EventCode.EVENT_PS_2004:
            case PsVersion1EventCode.EVENT_PS_2005:
            case PsVersion1EventCode.EVENT_PS_2006:

            case PsVersion1EventCode.EVENT_PS_3000:
            case PsVersion1EventCode.EVENT_PS_3001:
            case PsVersion1EventCode.EVENT_PS_3002:
            case PsVersion1EventCode.EVENT_PS_3003:
            case PsVersion1EventCode.EVENT_PS_3004:
            case PsVersion1EventCode.EVENT_PS_4000:
            case PsVersion1EventCode.EVENT_PS_4001:
            case PsVersion1EventCode.EVENT_PS_4002:
            case PsVersion1EventCode.EVENT_PS_4003:
//            case PsVersion1EventCode.EVENT_PS_5000:
//            case PsVersion1EventCode.EVENT_PS_5001:
            case PsVersion1EventCode.EVENT_PS_5002:
            case PsVersion1EventCode.EVENT_PS_5003:
            case PsVersion1EventCode.EVENT_PS_5004:
            case PsVersion1EventCode.EVENT_PS_5005:
            case PsVersion1EventCode.EVENT_PS_5006:
            case PsVersion1EventCode.EVENT_PS_5007:
            case PsVersion1EventCode.EVENT_PS_5008:
            case PsVersion1EventCode.EVENT_PS_5009:
            case PsVersion1EventCode.EVENT_PS_5010:
            case PsVersion1EventCode.EVENT_PS_5011:
            case PsVersion1EventCode.EVENT_PS_5012:
            case PsVersion1EventCode.EVENT_PS_5013:
            case PsVersion1EventCode.EVENT_PS_5014:
            case PsVersion1EventCode.EVENT_PS_5015:
            case PsVersion1EventCode.EVENT_PS_6000:
            case PsVersion1EventCode.EVENT_PS_6001:
            case PsVersion1EventCode.EVENT_PS_6002:
            case PsVersion1EventCode.EVENT_PS_6003:
            case PsVersion1EventCode.EVENT_PS_6004:
            case PsVersion1EventCode.EVENT_PS_6005:
            case PsVersion1EventCode.EVENT_PS_6006:
            case PsVersion1EventCode.EVENT_PS_6007:
            case PsVersion1EventCode.EVENT_PS_6010:
            case PsVersion1EventCode.EVENT_PS_7000:
            case PsVersion1EventCode.EVENT_PS_7001:
                eventLevel = EVENT_LEVEL_ALARM;
                break;

            case PsVersion1EventCode.EVENT_PS_9000:
                eventLevel = EVENT_LEVEL_SECURITY;
                break;

            case PsVersion1EventCode.EVENT_PS_9001:
            case PsVersion1EventCode.EVENT_PS_9002:
            case PsVersion1EventCode.EVENT_PS_9003:
            case PsVersion1EventCode.EVENT_PS_9004:
            case PsVersion1EventCode.EVENT_PS_9005:
            case PsVersion1EventCode.EVENT_PS_9006:
            case PsVersion1EventCode.EVENT_PS_9007:
            case PsVersion1EventCode.EVENT_PS_9008:
            case PsVersion1EventCode.EVENT_PS_9009:
            case PsVersion1EventCode.EVENT_PS_9010:
            case PsVersion1EventCode.EVENT_PS_9011:
            case PsVersion1EventCode.EVENT_PS_9012:
            case PsVersion1EventCode.EVENT_PS_9013:
            case PsVersion1EventCode.EVENT_PS_9104:
            case PsVersion1EventCode.EVENT_PS_9100:

            case PsVersion1EventCode.EVENT_PS_9300:
            case PsVersion1EventCode.EVENT_PS_9302:
            case PsVersion1EventCode.EVENT_PS_9303:
            case PsVersion1EventCode.EVENT_PS_9304:
            case PsVersion1EventCode.EVENT_PS_9305:
            case PsVersion1EventCode.EVENT_PS_9306:
            case PsVersion1EventCode.EVENT_PS_9309:
            case PsVersion1EventCode.EVENT_PS_9310:
            case PsVersion1EventCode.EVENT_PS_9311:
            case PsVersion1EventCode.EVENT_PS_9203:
            case PsVersion1EventCode.EVENT_PS_9204:
                eventLevel = EVENT_LEVEL_NORMAL;
                break;
        }
        return eventLevel;

    }

    private int getPs2EventLevelByCmd(String cmd) {
        int eventLevel = EVENT_LEVEL_NORMAL;
        switch (cmd) {
            case PsVersion2EventCode.EVENT_PS_0100:
            case PsVersion2EventCode.EVENT_PS_0101:
            case PsVersion2EventCode.EVENT_PS_0102:
            case PsVersion2EventCode.EVENT_PS_0103:
            case PsVersion2EventCode.EVENT_PS_0104:
            case PsVersion2EventCode.EVENT_PS_0105:
            case PsVersion2EventCode.EVENT_PS_0106:
            case PsVersion2EventCode.EVENT_PS_0107:
            case PsVersion2EventCode.EVENT_PS_0108:
            case PsVersion2EventCode.EVENT_PS_0109:
            case PsVersion2EventCode.EVENT_PS_010A:
            case PsVersion2EventCode.EVENT_PS_010B:
            case PsVersion2EventCode.EVENT_PS_010C:
            case PsVersion2EventCode.EVENT_PS_010D:
            case PsVersion2EventCode.EVENT_PS_010E:
            case PsVersion2EventCode.EVENT_PS_010F:
            case PsVersion2EventCode.EVENT_PS_0110:
            case PsVersion2EventCode.EVENT_PS_0111:
            case PsVersion2EventCode.EVENT_PS_0113:
            case PsVersion2EventCode.EVENT_PS_0114:
            case PsVersion2EventCode.EVENT_PS_0115:
            case PsVersion2EventCode.EVENT_PS_0116:
            case PsVersion2EventCode.EVENT_PS_0117:
            case PsVersion2EventCode.EVENT_PS_0118:
            case PsVersion2EventCode.EVENT_PS_0119:
            case PsVersion2EventCode.EVENT_PS_011A:
            case PsVersion2EventCode.EVENT_PS_011B:
            case PsVersion2EventCode.EVENT_PS_0120:
            case PsVersion2EventCode.EVENT_PS_0121:
            case PsVersion2EventCode.EVENT_PS_0122:
            case PsVersion2EventCode.EVENT_PS_0123:
            case PsVersion2EventCode.EVENT_PS_0124:
            case PsVersion2EventCode.EVENT_PS_0125:
            case PsVersion2EventCode.EVENT_PS_0126:
            case PsVersion2EventCode.EVENT_PS_0127:
            case PsVersion2EventCode.EVENT_PS_0128:
            case PsVersion2EventCode.EVENT_PS_0129:
            case PsVersion2EventCode.EVENT_PS_012A:
            case PsVersion2EventCode.EVENT_PS_012B:
            case PsVersion2EventCode.EVENT_PS_012C:
            case PsVersion2EventCode.EVENT_PS_012D:
            case PsVersion2EventCode.EVENT_PS_0130:
            case PsVersion2EventCode.EVENT_PS_0131:
            case PsVersion2EventCode.EVENT_PS_0132:
            case PsVersion2EventCode.EVENT_PS_0133:
            case PsVersion2EventCode.EVENT_PS_0134:
            case PsVersion2EventCode.EVENT_PS_0135:
            case PsVersion2EventCode.EVENT_PS_0136:
            case PsVersion2EventCode.EVENT_PS_0137:
            case PsVersion2EventCode.EVENT_PS_0138:
            case PsVersion2EventCode.EVENT_PS_0139:
            case PsVersion2EventCode.EVENT_PS_013A:
            case PsVersion2EventCode.EVENT_PS_013B:
            case PsVersion2EventCode.EVENT_PS_013C:
            case PsVersion2EventCode.EVENT_PS_0140:
            case PsVersion2EventCode.EVENT_PS_0141:
            case PsVersion2EventCode.EVENT_PS_0142:
            case PsVersion2EventCode.EVENT_PS_0143:
            case PsVersion2EventCode.EVENT_PS_0144:
            case PsVersion2EventCode.EVENT_PS_0150:
            case PsVersion2EventCode.EVENT_PS_0151:
            case PsVersion2EventCode.EVENT_PS_0152:
            case PsVersion2EventCode.EVENT_PS_0153:
            case PsVersion2EventCode.EVENT_PS_0154:
            case PsVersion2EventCode.EVENT_PS_0155:
            case PsVersion2EventCode.EVENT_PS_0156:
            case PsVersion2EventCode.EVENT_PS_0157:
            case PsVersion2EventCode.EVENT_PS_0158:
            case PsVersion2EventCode.EVENT_PS_0159:
            case PsVersion2EventCode.EVENT_PS_015A:
            case PsVersion2EventCode.EVENT_PS_015B:
            case PsVersion2EventCode.EVENT_PS_015C:
            case PsVersion2EventCode.EVENT_PS_015D:
            case PsVersion2EventCode.EVENT_PS_015E:
            case PsVersion2EventCode.EVENT_PS_015F:
            case PsVersion2EventCode.EVENT_PS_0160:
            case PsVersion2EventCode.EVENT_PS_0161:
            case PsVersion2EventCode.EVENT_PS_0162:
            case PsVersion2EventCode.EVENT_PS_0163:
            case PsVersion2EventCode.EVENT_PS_0164:
            case PsVersion2EventCode.EVENT_PS_0165:
            case PsVersion2EventCode.EVENT_PS_0166:
            case PsVersion2EventCode.EVENT_PS_0167:
            case PsVersion2EventCode.EVENT_PS_0168:
            case PsVersion2EventCode.EVENT_PS_0169:
            case PsVersion2EventCode.EVENT_PS_0203:
            case PsVersion2EventCode.EVENT_PS_0204:
            case PsVersion2EventCode.EVENT_PS_0205:
            case PsVersion2EventCode.EVENT_PS_0206:
            case PsVersion2EventCode.EVENT_PS_0207:
            case PsVersion2EventCode.EVENT_PS_0210:
            case PsVersion2EventCode.EVENT_PS_0211:
            case PsVersion2EventCode.EVENT_PS_0212:
            case PsVersion2EventCode.EVENT_PS_0214:
            case PsVersion2EventCode.EVENT_PS_0215:
            case PsVersion2EventCode.EVENT_PS_0217:
            case PsVersion2EventCode.EVENT_PS_0220:
            case PsVersion2EventCode.EVENT_PS_0221:
            case PsVersion2EventCode.EVENT_PS_0222:
            case PsVersion2EventCode.EVENT_PS_0223:
            case PsVersion2EventCode.EVENT_PS_0226:
            case PsVersion2EventCode.EVENT_PS_0227:
            case PsVersion2EventCode.EVENT_PS_0232:
            case PsVersion2EventCode.EVENT_PS_0233:
            case PsVersion2EventCode.EVENT_PS_0234:
            case PsVersion2EventCode.EVENT_PS_0235:
            case PsVersion2EventCode.EVENT_PS_0236:
            case PsVersion2EventCode.EVENT_PS_0237:
            case PsVersion2EventCode.EVENT_PS_0240:
            case PsVersion2EventCode.EVENT_PS_0241:
            case PsVersion2EventCode.EVENT_PS_0242:
            case PsVersion2EventCode.EVENT_PS_0244:
            case PsVersion2EventCode.EVENT_PS_0245:
            case PsVersion2EventCode.EVENT_PS_0250:
            case PsVersion2EventCode.EVENT_PS_0251:
            case PsVersion2EventCode.EVENT_PS_0252:
            case PsVersion2EventCode.EVENT_PS_0253:
            case PsVersion2EventCode.EVENT_PS_0255:
            case PsVersion2EventCode.EVENT_PS_0256:
            case PsVersion2EventCode.EVENT_PS_0257:
            case PsVersion2EventCode.EVENT_PS_0270:
            case PsVersion2EventCode.EVENT_PS_0271:
            case PsVersion2EventCode.EVENT_PS_0272:
            case PsVersion2EventCode.EVENT_PS_0273:
            case PsVersion2EventCode.EVENT_PS_0274:
            case PsVersion2EventCode.EVENT_PS_0275:
            case PsVersion2EventCode.EVENT_PS_0280:
            case PsVersion2EventCode.EVENT_PS_0281:
            case PsVersion2EventCode.EVENT_PS_0282:
            case PsVersion2EventCode.EVENT_PS_0283:
            case PsVersion2EventCode.EVENT_PS_0284:
            case PsVersion2EventCode.EVENT_PS_0285:
            case PsVersion2EventCode.EVENT_PS_0286:
            case PsVersion2EventCode.EVENT_PS_0290:
            case PsVersion2EventCode.EVENT_PS_0291:
            case PsVersion2EventCode.EVENT_PS_0292:
            case PsVersion2EventCode.EVENT_PS_0293:
            case PsVersion2EventCode.EVENT_PS_0294:
            case PsVersion2EventCode.EVENT_PS_0295:
            case PsVersion2EventCode.EVENT_PS_0296:
            case PsVersion2EventCode.EVENT_PS_0297:
            case PsVersion2EventCode.EVENT_PS_0301:
            case PsVersion2EventCode.EVENT_PS_0302:
            case PsVersion2EventCode.EVENT_PS_0303:
            case PsVersion2EventCode.EVENT_PS_0304:
            case PsVersion2EventCode.EVENT_PS_0305:
            case PsVersion2EventCode.EVENT_PS_0306:
            case PsVersion2EventCode.EVENT_PS_0307:
            case PsVersion2EventCode.EVENT_PS_0308:
            case PsVersion2EventCode.EVENT_PS_0309:
            case PsVersion2EventCode.EVENT_PS_030A:
            case PsVersion2EventCode.EVENT_PS_030B:
            case PsVersion2EventCode.EVENT_PS_0400:
            case PsVersion2EventCode.EVENT_PS_7001:
            case PsVersion2EventCode.EVENT_PS_7002:
            case PsVersion2EventCode.EVENT_PS_7003:
            case PsVersion2EventCode.EVENT_PS_7004:
            case PsVersion2EventCode.EVENT_PS_7005:
            case PsVersion2EventCode.EVENT_PS_7006:
            case PsVersion2EventCode.EVENT_PS_8001:
            case PsVersion2EventCode.EVENT_PS_8002:
            case PsVersion2EventCode.EVENT_PS_8003:
            case PsVersion2EventCode.EVENT_PS_8004:
            case PsVersion2EventCode.EVENT_PS_8005:
            case PsVersion2EventCode.EVENT_PS_9000:
            case PsVersion2EventCode.EVENT_PS_E010:
                eventLevel = EVENT_LEVEL_ALARM;
                break;

            case PsVersion2EventCode.EVENT_PS_9110:
                eventLevel = EVENT_LEVEL_SECURITY;
                break;

            case PsVersion2EventCode.EVENT_PS_9001:
            case PsVersion2EventCode.EVENT_PS_9002:
            case PsVersion2EventCode.EVENT_PS_9003:
            case PsVersion2EventCode.EVENT_PS_9004:
            case PsVersion2EventCode.EVENT_PS_9005:
            case PsVersion2EventCode.EVENT_PS_9009:
            case PsVersion2EventCode.EVENT_PS_900A:
            case PsVersion2EventCode.EVENT_PS_9030:
            case PsVersion2EventCode.EVENT_PS_9033:
            case PsVersion2EventCode.EVENT_PS_9035:
            case PsVersion2EventCode.EVENT_PS_9039:
            case PsVersion2EventCode.EVENT_PS_903A:
            case PsVersion2EventCode.EVENT_PS_903B:
            case PsVersion2EventCode.EVENT_PS_9050:
            case PsVersion2EventCode.EVENT_PS_9051:
            case PsVersion2EventCode.EVENT_PS_9100:
            case PsVersion2EventCode.EVENT_PS_9101:
            case PsVersion2EventCode.EVENT_PS_9102:
            case PsVersion2EventCode.EVENT_PS_9103:
            case PsVersion2EventCode.EVENT_PS_9111:
                eventLevel = EVENT_LEVEL_NORMAL;
                break;
        }
        return eventLevel;

    }

    public int getLocalFilter() {
        return localFilter;
    }


    public JSONArray getRequestFilterParams() {

        final boolean exceptions = (EventListHelper.MARK_EXCEPTIONS & localFilter) != 0;
        final boolean outagePowerBackup = (EventListHelper.MARK_OUTAGE_POWER_BACKUP & localFilter) != 0;
        final boolean powerStationNormalEvents = (EventListHelper.MARK_POWER_STATION_NORMAL_EVENTS & localFilter) != 0;
        final boolean motionDetection = (EventListHelper.MARK_MOTION_DETECTION & localFilter) != 0;
        final boolean smartCameraNormalEvents = (EventListHelper.MARK_SMART_CAMERA_NORMAL_EVENTS & localFilter) != 0;
        final boolean dailyMemoriesEvents = (EventListHelper.MARK_DAILY_MEMORIES_EVENTS & localFilter) != 0;
        final boolean alarm = (EventListHelper.MARK_ALARM & localFilter) != 0;
        final boolean securityStatus = (EventListHelper.MARK_SECURITY_STATUS & localFilter) != 0;
        final boolean doorWindowStatus = (EventListHelper.MARK_DOOR_WINDOW_STATUS & localFilter) != 0;
        final boolean tamperTriggered = (EventListHelper.MARK_TAMPER_TRIGGERED & localFilter) != 0;
        final boolean alarmSystemNormalEvents = (EventListHelper.MARK_ALARM_SYSTEM_NORMAL_EVENTS & localFilter) != 0;
        final boolean accessoryStatus = (EventListHelper.MARK_ACCESSORIES_STATUS & localFilter) != 0;
        final boolean familyNormalEvents = (EventListHelper.MARK_FAMILY_NORMAL_EVENTS & localFilter) != 0;

        JSONArray params = new JSONArray();
        if (exceptions) {
            params.put(0);
        }
        if (outagePowerBackup) {
            params.put(1);
        }
        if (powerStationNormalEvents) {
            params.put(2);
        }
        if (motionDetection) {
            params.put(3);
        }
        if (smartCameraNormalEvents) {
            params.put(4);
        }

        if (dailyMemoriesEvents) {
            params.put(12);
        }

        if (alarm) {
            params.put(5);
        }
        if (securityStatus) {
            params.put(6);
        }
        if (doorWindowStatus) {
            params.put(7);
        }
        if (tamperTriggered) {
            params.put(8);
        }
        if (alarmSystemNormalEvents) {
            params.put(9);
        }
        if (accessoryStatus) {
            params.put(10);
        }
        if (familyNormalEvents) {
            params.put(11);
        }

        return params;
    }


    public void updateLocalMark(@MarkType int mark, boolean opened) {
        if (opened) {
            localFilter |= mark;
        } else {
            localFilter &= ~mark;
        }
        DBUtil.Put(getFilterKey(), localFilter);
    }


    public boolean checkSupportChangeMark(int index) {
        boolean support;
        switch (index) {
            case 0:
            case 1:
            case 2:
                support = getEventTypeVisible(EVENT_TYPE_POWER_STATION);
                break;
            case 3:
            case 4:
            case 12:
                support = getEventTypeVisible(EVENT_TYPE_SMART_CAMERA);
                break;
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
                support = getEventTypeVisible(EVENT_TYPE_ALARM_SYSTEM);
                break;
            case 10:
                support = getEventTypeVisible(EVENT_TYPE_ACCESSORIES);
                break;
            case 11:
                support = getEventTypeVisible(EVENT_TYPE_FAMILY);
                break;
            default:
                support = true;
                break;
        }
        return support;
    }

    public boolean getEventTypeVisible(@EventType int type) {
        if (eventVisibleMap == null) {
            updateEventTypeVisible();
        }
        if (!eventVisibleMap.containsKey(type)) {
            return false;
        }
        return Boolean.TRUE.equals(eventVisibleMap.get(type));
    }

    public void updateEventTypeVisible() {
        if (eventVisibleMap == null) {
            eventVisibleMap = new HashMap<>();
        }
        loadEventTypeVisible(eventVisibleMap);
    }

    public void updateEventSonTypeVisible(boolean ignore) {
        final int powerStation = MARK_EXCEPTIONS | MARK_OUTAGE_POWER_BACKUP | MARK_POWER_STATION_NORMAL_EVENTS;
        final int smartCamera = MARK_MOTION_DETECTION | MARK_SMART_CAMERA_NORMAL_EVENTS | MARK_DAILY_MEMORIES_EVENTS;
        final int alarmSystem = MARK_ALARM | MARK_SECURITY_STATUS | MARK_DOOR_WINDOW_STATUS
                | MARK_TAMPER_TRIGGERED | MARK_ALARM_SYSTEM_NORMAL_EVENTS;
        final int accessories = MARK_ACCESSORIES_STATUS;
        final int family = MARK_FAMILY_NORMAL_EVENTS;
        String parentType = DBUtil.Str(DBKey.KEY_PARENT_EVENT_LIST_BY_FILTER + HomeManager.getInstance().getCurrentHome().getHomeID());
        boolean ps = false;
        boolean sc = false;
        boolean al = false;
        boolean as = false;
        boolean fl = false;
        if (!TextUtils.isEmpty(parentType)) {
            String[] pts = parentType.split(",");
            ps = Boolean.parseBoolean(pts[0]);
            sc = Boolean.parseBoolean(pts[1]);
            al = Boolean.parseBoolean(pts[2]);
            as = Boolean.parseBoolean(pts[3]);
            fl = Boolean.parseBoolean(pts[4]);
        }
        if (getEventTypeVisible(EVENT_TYPE_POWER_STATION)) {
            if (ignore || !ps)
                localFilter |= powerStation;
        } else {
            localFilter &= ~powerStation;
        }
        if (getEventTypeVisible(EVENT_TYPE_SMART_CAMERA)) {
            if (ignore || !sc)
                localFilter |= smartCamera;
        } else {
            localFilter &= ~smartCamera;
        }
        if (getEventTypeVisible(EVENT_TYPE_ALARM_SYSTEM)) {
            if (ignore || !al)
                localFilter |= alarmSystem;
        } else {
            localFilter &= ~alarmSystem;
        }
        if (getEventTypeVisible(EVENT_TYPE_ACCESSORIES)) {
            if (ignore || !as)
                localFilter |= accessories;
        } else {
            localFilter &= ~accessories;
        }
        if (getEventTypeVisible(EVENT_TYPE_FAMILY)) {
            if (ignore || !fl)
                localFilter |= family;
        } else {
            localFilter &= ~family;
        }

        StringBuilder stringBuilder = new StringBuilder();
        List<Boolean> list = new ArrayList<>(eventVisibleMap.values());
        for (int i = 0; i < list.size(); i++) {
            stringBuilder.append(list.get(i));
            if (i < list.size() - 1) {
                stringBuilder.append(",");
            }
        }
        DBUtil.Put(DBKey.KEY_PARENT_EVENT_LIST_BY_FILTER + HomeManager.getInstance().getCurrentHome().getHomeID(), stringBuilder.toString());
    }

    private void loadEventTypeVisible(Map<Integer, Boolean> map) {
        map.put(EVENT_TYPE_POWER_STATION, BmtManager.getInstance().getNotDeleteDevicesSize() > 0 && AppConfig.EventListFilter.POWER_STATION);
        map.put(EVENT_TYPE_SMART_CAMERA, IPCManager.getInstance().getAllNotDeleteDevicesSize() > 0 && AppConfig.EventListFilter.SMART_CAMERA);
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final Device panelDevice = DinHome.getInstance().getDevice(panelId);
        map.put(EVENT_TYPE_ALARM_SYSTEM, (null != panelDevice && !panelDevice.getFlagDeleted()) && AppConfig.EventListFilter.ALARM_SYSTEM);
        map.put(EVENT_TYPE_ACCESSORIES, PluginManager.getInstance().getNotDeletedSmartPluginList().size() > 0 && AppConfig.EventListFilter.ACCESSORIES);
        map.put(EVENT_TYPE_FAMILY, AppConfig.EventListFilter.FAMILY);
    }
}
