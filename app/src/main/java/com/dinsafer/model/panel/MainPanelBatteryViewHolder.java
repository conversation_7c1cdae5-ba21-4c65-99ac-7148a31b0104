package com.dinsafer.model.panel;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.Constants;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module.powerstation.widget.BatteryChargeView;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.module.powerstation.widget.segmentbar.SegmentSlideBar;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.plugin.widget.util.DensityUtil;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.LottieLoadingView;
import com.dinsafer.ui.anim.ShakeAnimUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.TimeUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;

/**
 * @describe：
 * @date：2022/12/19
 * @author: create by Sydnee
 */
public class MainPanelBatteryViewHolder extends BaseViewHolder {

    private final String TAG = MainPanelBatteryViewHolder.class.getSimpleName();
    private Context mContext;
    private BaseFragment mBaseFragment;
    private RelativeLayout background;
    private TextView tvPluginName;
    private ImageView ivBluetooth;
    private ImageView ivWifi;
    private ImageView ivCellular;
    private ImageView ivLan;
    private ImageView ivDelete;
    private LocalTextView tvBalanceKey;
    private LocalTextView tvBalanceVal;
    private LocalTextView tvLastForHour;
    private LocalTextView tvLastForHourUnit;
    private LocalTextView tvLastForMinute;
    private LocalTextView tvLastForMinuteUnit;
    private BatteryChargeView powerBatteryView;
    private SegmentSlideBar segmentSlideBar;
    private ConstraintLayout clParent;
    private ConstraintLayout clUnavailable;
    private LocalTextView tvUnavailable;
    private ConstraintLayout clUpgrade;
    private ImageView ivWarning;
    private LocalTextView tvLastForKey;
    private LottieLoadingView lottieLoadingView;
    private RelativeLayout rlLoading;
    private ImageView ivModeLogo;
    private LocalTextView tvMode;
    private LinearLayout llMode;
    private LocalTextView tvOffline;
    private LocalTextView tvType;
    private LinearLayout llSign;

    private boolean batteryInputOn;
    private boolean batteryOutputOn; //是否打开了逆变器输出到电池
    private int mPhaseCount = 3;
    private String subCategory = DinConst.TYPE_BMT_HP5000;
    private int connectStatus;
    private List<Segment> segments = new ArrayList<>();

    private float mSmartReserve = 0.9f;
    private float mEmergencyReserve = 0.5f;
    private String mBatteryLastForKey = "Battery Balance";
    private boolean isEmergencyOn;
    private boolean isManualAIMode;

    private final BehaviorSubject<Map<String, Object>> mBatteryStatusSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mBatteryInfoSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mReserveModeSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mPTReserveSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mScheduledReserveSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mCustomScheduleSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mChargeModeSubject = BehaviorSubject.create();
    private Subscriber<Map<String, Object>> mBatteryInfoSubscriber;
    private Subscriber<Map<String, Object>> mReserveModePTSubscriber;
    private Subscriber<Map<String, Object>> mReserveModeScheduledSubscriber;
    private Subscriber<Map<String, Object>> mReserveModeAISubscriber;
    private final Map<String, Object> mChargeModeMap = new HashMap<>();
    private Device mDevice;

    public MainPanelBatteryViewHolder(BaseFragment baseFragment, View view) {
        super(view);
        mBaseFragment = baseFragment;
        mContext = view.getContext();
        background = itemView.findViewById(R.id.background);
        tvPluginName = itemView.findViewById(R.id.tv_plugin_name);
        ivBluetooth = itemView.findViewById(R.id.iv_bluetooth);
        ivWifi = itemView.findViewById(R.id.iv_wifi);
        ivCellular = itemView.findViewById(R.id.iv_cellular);
        ivLan = itemView.findViewById(R.id.iv_lan);
        ivDelete = itemView.findViewById(R.id.iv_delete);
        tvBalanceKey = itemView.findViewById(R.id.tv_balance_key);
        tvBalanceVal = itemView.findViewById(R.id.tv_balance_val);
        tvLastForHour = itemView.findViewById(R.id.tv_last_for_hour);
        tvLastForHourUnit = itemView.findViewById(R.id.tv_last_for_hour_unit);
        tvLastForMinute = itemView.findViewById(R.id.tv_last_for_minute);
        tvLastForMinuteUnit = itemView.findViewById(R.id.tv_last_for_minute_unit);
        powerBatteryView = itemView.findViewById(R.id.power_battery_view);
        segmentSlideBar = itemView.findViewById(R.id.ssb);
        clParent = itemView.findViewById(R.id.cl_parent);
        clUpgrade = itemView.findViewById(R.id.cl_upgrade);
        clUnavailable = itemView.findViewById(R.id.cl_unavailable);
        tvUnavailable = itemView.findViewById(R.id.tv_unavailable);
        ivWarning = itemView.findViewById(R.id.iv_warning);
        tvLastForKey = itemView.findViewById(R.id.tv_last_for_key);
        lottieLoadingView = itemView.findViewById(R.id.view_loading);
        rlLoading = itemView.findViewById(R.id.rl_loading);
        ivModeLogo = itemView.findViewById(R.id.iv_mode_logo);
        tvMode = itemView.findViewById(R.id.tv_mode);
        llMode = itemView.findViewById(R.id.ll_mode);
        tvOffline = itemView.findViewById(R.id.tv_offline);
        tvType = itemView.findViewById(R.id.tv_type);
        segmentSlideBar.post(() -> segmentSlideBar.setRemainSpace(background.getWidth() / 2f - powerBatteryView.getWidth() / 2f - DensityUtil.dp2px(mContext, 10)));
        llSign = itemView.findViewById(R.id.ll_sign);

        itemView.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                boolean isEdit = MainPanelHelper.getInstance().isPanelEditMode();
//                int visible = isEdit ? View.VISIBLE : View.GONE;
//                if (visible != ivDelete.getVisibility()) {
//                    ivDelete.setVisibility(visible);
//                }
                if (isEdit) {
                    powerBatteryView.setAnim(false);
                    ShakeAnimUtil.getInstance().shakeView(itemView, true, ShakeAnimUtil.BIG_TYPE);
                }
            }

            @Override
            public void onViewDetachedFromWindow(View v) {

            }
        });
        resetNameMaxWidth();
    }

    public void toSubscriber(Device device) {
        this.mDevice = device;

        initBatteryInfoSubscriber();
        initReserveModePTSubscriber();
        initReserveModeScheduledSubscriber();
        initReserveModeAISubscriber();
    }
    private void resetNameMaxWidth() {
        if (llSign == null) return;
        llSign.post(() -> {
            int containWidth = clParent.getWidth();
            int llSignWidth = llSign.getWidth();
            int tvOfflineWidth = llSign.getWidth();
            int ivWarningWidth = ivWarning.getWidth();
            int maxWidth = containWidth - DensityUtil.dp2px(mContext, 40)
                    - Math.max(llSignWidth, tvOfflineWidth) - ivWarningWidth;
            if (tvType != null) {
                tvType.setMaxWidth(maxWidth);
            }
            if (tvPluginName != null) {
                tvPluginName.setMaxWidth(maxWidth);
            }
        });
    }

    public void refreshText() {

        if (tvOffline != null) {
            tvOffline.setLocalText(mContext.getString(R.string.Offline));
        }

        if (tvType != null) {
            tvType.setLocalText(mContext.getString(R.string.battery_view));
        }
        if (tvBalanceKey != null) {
            tvBalanceKey.setLocalText(mContext.getString(R.string.power_battery_balance));
        }
        if (tvLastForKey != null) {
            tvLastForKey.setLocalText(mBatteryLastForKey);
        }
        if (tvLastForHourUnit != null) {
            tvLastForHourUnit.setLocalText(mContext.getString(R.string.power_h));
        }

        if (tvLastForMinuteUnit != null) {
            tvLastForMinuteUnit.setLocalText(mContext.getString(R.string.power_min));
        }
        if (tvUnavailable != null) {
            tvUnavailable.setLocalText(mContext.getResources().getString(R.string.Unavailable));
        }
    }

    private void initBatteryInfoSubscriber() {
        mBatteryInfoSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int percent = DeviceHelper.getInt(resultMap, PSKeyConstant.SOC, 0);
                int totalVolt = DeviceHelper.getInt(resultMap, PSKeyConstant.TOTAL_VOLT, 0);
                int curPower = DeviceHelper.getInt(resultMap, PSKeyConstant.CUR_POWER, 0);
                int remainTime = DeviceHelper.getInt(resultMap, PSKeyConstant.REMAIN_TIME, 0);
                int chargeTime = DeviceHelper.getInt(resultMap, PSKeyConstant.CHARGE_TIME, 0);
                int batteryWat = DeviceHelper.getInt(resultMap, BmtDataKey.BATTERY_WAT, 0);
                int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                mEmergencyReserve = emergencyReserve / 100f;
                mSmartReserve = smartReserve / 100f;
                if (percent > 100 || percent < 0) {
                    percent = 0;
                }

                double balance = totalVolt / 1000.0 * curPower / 1000.0;
                balance = new BigDecimal(balance).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                String balanceStr = String.valueOf(balance);
                if (balanceStr.endsWith(".0")) {
                    balanceStr = balanceStr.replace(".0", "");
                }
                int chargeStatus = BatteryChargeView.NORMAL;
                if (batteryWat > 0) {
                    chargeStatus = BatteryChargeView.DISCHARGING;
                }
                if (batteryWat < 0) {
                    chargeStatus = BatteryChargeView.CHARGING;
                }
                String timeKey = chargeStatus == BatteryChargeView.CHARGING ?
                        mContext.getString(R.string.power_battery_need_to_charge)
                        : mContext.getString(R.string.power_battery_lasts_for);
                String hmStr = TimeUtil.minute2HourMinute(chargeStatus == BatteryChargeView.CHARGING ? chargeTime : remainTime);
                String[] hmArr = hmStr.split(":");

                powerBatteryView.setSuccess(true);
                powerBatteryView.setOnline(true);
                powerBatteryView.setChargeStatus(chargeStatus, false);
                powerBatteryView.setEmergencyReserve(mEmergencyReserve);
                powerBatteryView.setSmartReserve(mSmartReserve);
                powerBatteryView.setProgress(percent / 100f, true);
                tvBalanceVal.setLocalText(balanceStr);
                tvLastForKey.setLocalText(timeKey);
                tvLastForHour.setLocalText(hmArr[0]);
                tvLastForMinute.setLocalText(hmArr[1]);

                segments.get(0).setMinValue(smartReserve / 100f);
                segments.get(1).setMinValue(emergencyReserve / 100f);
                segments.get(1).setMaxValue(smartReserve / 100f);
                segments.get(2).setMaxValue(emergencyReserve / 100f);
                segmentSlideBar.setProgress(percent / 100f, false);
                segmentSlideBar.post(() -> segmentSlideBar.setRemainSpace(background.getWidth() / 2f - powerBatteryView.getWidth() / 2f - DensityUtil.dp2px(mContext, 9.5f)));
                segmentSlideBar.invalidate();
            }
        };
        Observable<Map<String, Object>> observable = Observable.combineLatest(mBatteryStatusSubject, mBatteryInfoSubject, mChargeModeSubject, (batteryStatusMap, batteryInfoMap, chargeModeMap) -> {
            Map<String, Object> allMap = new HashMap<>();
            allMap.putAll(batteryStatusMap);
            allMap.putAll(batteryInfoMap);
            allMap.putAll(chargeModeMap);
            return allMap;
        });
        if (mBaseFragment != null) {
            observable.compose(mBaseFragment.bindToLifecycle());
        }
        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mBatteryInfoSubscriber);
    }

    private void initReserveModePTSubscriber() {
        mReserveModePTSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int reserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);
                isManualAIMode = false;
                if (!isEmergencyOn) {
                    setChargeMode(reserveMode - 1);
                }
                if (reserveMode == 1) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, reserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
            }
        };
        Observable<Map<String, Object>> observable = Observable.combineLatest(mReserveModeSubject, mPTReserveSubject, (reserveModeMap, ptReserveMap) -> {
            Map<String, Object> allMap = new HashMap<>();
            allMap.putAll(reserveModeMap);
            allMap.putAll(ptReserveMap);
            return allMap;
        });
        if (mBaseFragment != null) {
            observable.compose(mBaseFragment.bindToLifecycle());
        }
        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModePTSubscriber);
    }

    private void initReserveModeScheduledSubscriber() {
        mReserveModeScheduledSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int reserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);
                isManualAIMode = false;
                if (!isEmergencyOn) {
                    setChargeMode(reserveMode - 1);
                }
                if (reserveMode == 2) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, reserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
            }
        };

        Observable<Map<String, Object>> observable = Observable.combineLatest(mReserveModeSubject, mScheduledReserveSubject, (reserveModeMap, scheduledReserveMap) -> {
            Map<String, Object> allMap = new HashMap<>();
            allMap.putAll(reserveModeMap);
            allMap.putAll(scheduledReserveMap);
            return allMap;
        });
        if (mBaseFragment != null) {
            observable.compose(mBaseFragment.bindToLifecycle());
        }
        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModeScheduledSubscriber);
    }

    private void initReserveModeAISubscriber() {
        mReserveModeAISubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int reserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);

                isManualAIMode = false;
                if (reserveMode == 3) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    Integer[] weekdays = (Integer[]) MapUtils.get(resultMap, BmtDataKey.WEEKDAYS, null);
                    if (weekdays != null) {
                        boolean hasAIData = false;
                        for (int weekday : weekdays) {
                            if (weekday == -128) {
                                hasAIData = true;
                                emergencyReserve = DeviceHelper.getInt(mDevice, BmtDataKey.EMERGENCY_RESERVE, 0);
                                smartReserve = DeviceHelper.getInt(mDevice, BmtDataKey.SMART_RESERVE, 0);
                                break;
                            }
                        }
                        isManualAIMode = !hasAIData;
                    }
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, reserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
                if (!isEmergencyOn) {
                    if (reserveMode == 3 && isManualAIMode) {
                        setAIManual();
                    } else {
                        setChargeMode(reserveMode - 1);
                    }
                }
            }
        };

        Observable<Map<String, Object>> observable = Observable.combineLatest(mReserveModeSubject, mCustomScheduleSubject, (reserveModeMap, scheduledReserveMap) -> {
            Map<String, Object> allMap = new HashMap<>();
            allMap.putAll(reserveModeMap);
            allMap.putAll(scheduledReserveMap);
            return allMap;
        });
        if (mBaseFragment != null) {
            observable.compose(mBaseFragment.bindToLifecycle());
        }
        observable.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModeAISubscriber);
    }

    private void setChargeMode(int mode) {
        String[] modeList = mContext.getResources().getStringArray(R.array.ps_charge_mode);
        int[] icons = {R.drawable.icon_power_price_tracking_mode, R.drawable.icon_power_scheduled_mode,
                R.drawable.icon_power_mode_ai, R.drawable.icon_power_mode_emergency_charge,
                R.drawable.icon_power_mode_emergency_charge};
        ivModeLogo.setImageResource(icons[mode]);
        tvMode.setLocalText(modeList[mode]);
        llMode.setVisibility(View.VISIBLE);
    }

    private void setAIManual() {
        ivModeLogo.setImageResource(R.drawable.icon_power_mode_ai);
        tvMode.setLocalText(mContext.getResources().getString(R.string.AI_Manual_Mode));
        llMode.setVisibility(View.VISIBLE);
    }

    /**
     * Item布局可见性
     *
     * @param visible
     */
    public void setRootViewVisible(boolean visible) {
        background.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
    }

    /**
     * 设置根布局是否可以点击
     *
     * @param enable true: 可以被点击
     */
    public void setRootViewEnable(boolean enable) {
        DDLog.d(TAG, "setRootViewEnable, enable: " + enable);
        background.setEnabled(enable);
    }

    public void setRootViewEnableAlpha(boolean enable) {
        clParent.setAlpha(enable
                ? MainPanelHelper.VIEW_ENABLE_ALPHA
                : MainPanelHelper.VIEW_DISABLE_ALPHA);
    }

    public void setPluginName(String name) {
        tvPluginName.setVisibility(BmtUtil.isOverOne() ? View.VISIBLE : View.GONE);
        if (TextUtils.isEmpty(name)) {
            tvPluginName.setText("");
        } else {
            tvPluginName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
        }
    }


    public void setSegment() {
        segments.clear();
        segments.add(new Segment(0.9f, 1.0f, mContext.getString(R.string.power_battery_bar_status_text_1), mContext.getResources().getColor(R.color.power_station_battery_color_1)));
        segments.add(new Segment(0.6f, 0.9f, mContext.getString(R.string.power_battery_bar_status_text_2), mContext.getResources().getColor(R.color.power_station_battery_color_2)));
        segments.add(new Segment(0.12f, 0.6f, mContext.getString(R.string.power_battery_bar_status_text_3), mContext.getResources().getColor(R.color.power_station_battery_color_3)));
        segments.add(new Segment(0.02f, 0.12f, mContext.getString(R.string.power_battery_bar_status_text_4), mContext.getResources().getColor(R.color.power_station_battery_color_4)));
        segments.add(new Segment(0.0f, 0.02f, mContext.getString(R.string.power_battery_bar_status_text_5), mContext.getResources().getColor(R.color.power_station_battery_color_5)));
        segmentSlideBar.setSegments(segments, 4);
        segmentSlideBar.postDelayed(new Runnable() {
            @Override
            public void run() {
                segmentSlideBar.setProgress(0f, true);
            }
        }, 200);
    }

    /**
     * 设置整个Item的点击监听
     *
     * @param l
     */
    public void setRootViewClickListener(View.OnClickListener l) {
        clParent.setOnClickListener(l);
    }


    public void setUpgradeClickListener(View.OnClickListener l) {
        clUpgrade.setOnClickListener(l);
    }

    /**
     * 设置整个item的长按监听
     *
     * @param listener
     */
    public void setRootViewLongClickListener(View.OnLongClickListener listener) {
        clParent.setOnLongClickListener(listener);
        clUnavailable.setOnLongClickListener(listener);
    }

    public void setDeleteIconClickListener(View.OnClickListener listener) {
        ivDelete.setOnClickListener(listener);
    }

    /**
     * 被删除状态下，点击监听
     *
     * @param listener
     */
    public void setUnavailableStateClickListener(View.OnClickListener listener) {
        clUnavailable.setOnClickListener(listener);
    }

    /**
     * 修改编辑模式
     *
     * @param isEditMode true: 处于编辑模式
     */
    public void setEditMode(boolean isEditMode) {
        DDLog.d(TAG, "setEditMode, isEditMode: " + isEditMode);
//        ivDelete.setVisibility(isEditMode ? View.VISIBLE : View.GONE);
        float animVal = isEditMode ? 1f : 0f;
        ivDelete.animate().alpha(animVal).scaleX(animVal).scaleY(animVal).setDuration(ShakeAnimUtil.ANIM_TIME).start();
        rlLoading.setVisibility(View.GONE);
        powerBatteryView.setAnim(!isEditMode);
        if (isEditMode) {
            ShakeAnimUtil.getInstance().shakeView(background, true, ShakeAnimUtil.BIG_TYPE);
        }
    }

    /**
     * 加载中
     *
     * @param name 缓存的名称
     */
    public void setRootViewLoading(String name) {
        background.setEnabled(false);
        rlLoading.setVisibility(View.VISIBLE);
        clUpgrade.setVisibility(View.GONE);
        setRootViewDeletedState(false);
        if (!TextUtils.isEmpty(name)) {
            setPluginName(name);
        }
    }

    public void setRootViewDeletedState(boolean deletedState) {
        background.setEnabled(false);
        clUnavailable.setVisibility(deletedState ? View.VISIBLE : View.GONE);
        tvUnavailable.setLocalText(Local.s(mContext.getResources().getString(R.string.Unavailable)));
    }

    public void setRootViewUpgradeState(final boolean visible) {
        clUpgrade.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    /**
     * battery状态
     */
    public void updateBatteryStatus(BmtDeviceStatusChange event) {
        Log.d(TAG, "updateBatteryStatus.");
        connectStatus = event.getConnect_status();
        updateViewByOnlineStatus(connectStatus == BmtDeviceStatusChange.ONLINE);
        if (event.isShowUpdate()) {
            setRootViewUpgradeState(true);
        } else {
//            setRootViewEnableAlpha(connectStatus == BmtDeviceStatusChange.ONLINE);
            setRootViewUpgradeState(false);
        }
    }

    private void showWaring() {
        if (ivWarning != null && ivWarning.getVisibility() != View.VISIBLE) {
            ivWarning.setVisibility(View.VISIBLE);
        }
    }

    private void setWifiSignal(int wifiSignal) {
        if (wifiSignal < 2 || wifiSignal > 4) return;
        int wifiIcons[] = {R.drawable.icon_plugin_wifi_0, R.drawable.icon_plugin_wifi_1,
                R.drawable.icon_plugin_wifi_2, R.drawable.icon_plugin_wifi_3};
        ivWifi.setImageResource(wifiIcons[wifiSignal - 1]);
        ivWifi.setVisibility(View.VISIBLE);
    }

    private void setCellularSignal(int cellular) {
        final int index = cellular - 1;
        final int[] wifiIcons = {R.drawable.icon_plugin_list_signal_0, R.drawable.icon_plugin_list_signal_1,
                R.drawable.icon_plugin_list_signal_2, R.drawable.icon_plugin_list_signal_3};
        if (index < 0 || index >= wifiIcons.length) {
            return;
        }
        ivCellular.setImageResource(wifiIcons[index]);
        ivCellular.setVisibility(index > 0 ? View.VISIBLE : View.GONE);
        tvOffline.setVisibility(View.GONE);
    }

    private void setEthernetSignal(int ethernet) {
        ivLan.setVisibility(ethernet > 1 ? View.VISIBLE : View.GONE);
    }

    public void updateBatteryStatus(BatteryStatusEvent event) {
//        final String deviceId = event.getDeviceId();

//        chargeStatus = event.getStatus();
//        DDLog.d(TAG, "状态====" + chargeStatus);
//        powerBatteryView.setChargeStatus(chargeStatus);
    }

    public void updateChargeMode(ChargeModeEvent event) {
        int mode = event.getMode();
        if (mode == 4 || !isEmergencyOn) {
            if (event.isManual()) {
                setAIManual();
            } else {
                setChargeMode(mode - 1);
            }
        }
        if (event.isOnlyMode()) {
            return;
        }
        int smartReserve = event.getSmartReserve();
        int emergencyReserve = event.getEmergencyReserve();
        segments.get(0).setMinValue(smartReserve / 100f);
        segments.get(1).setMinValue(emergencyReserve / 100f);
        segments.get(1).setMaxValue(smartReserve / 100f);
        segments.get(2).setMaxValue(emergencyReserve / 100f);
        segmentSlideBar.invalidate();

        float er = emergencyReserve / 100f;
        float sr = smartReserve / 100f;
        mEmergencyReserve = er;
        mSmartReserve = sr;
        powerBatteryView.setEmergencyReserve(mEmergencyReserve);
        powerBatteryView.setSmartReserve(mSmartReserve);
        powerBatteryView.resetColor();
        powerBatteryView.invalidate();

    }

    public void updateBatteryCurrent(BmtGraphicUpdateEvent event) {
        if (!event.isGraphicBattery()) {
            return;
        }
        final String deviceId = event.getDeviceId();
        final String cmd = event.getCmd();
        final Map map = event.getData();
        Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
        int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
        DDLog.i(TAG, "updateBatteryCurrent. status: " + status);

        if (status == StatusConstant.STATUS_SUCCESS) {
            if (result != null && result.size() > 0) {
                switch (cmd) {

                    case BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO:
                        mBatteryStatusSubject.onNext(result);
                        break;

                    case DsCamCmd.GET_BATTERY_ALLINFO:
                        mBatteryInfoSubject.onNext(result);
                        break;
                    case DsCamCmd.GET_EMERGENCY_CHARGE:
                        isEmergencyOn = DeviceHelper.getBoolean(result, PSKeyConstant.ON, false);
                        if (isEmergencyOn) {
                            long currentMillis = System.currentTimeMillis();
                            long startTime = DeviceHelper.getLong(result, PSKeyConstant.START_TIME, 0) * 1000;
                            long endTime = DeviceHelper.getLong(result, PSKeyConstant.END_TIME, 0) * 1000;
                            if (currentMillis < startTime) {
                                setChargeMode(4);
                            } else if (currentMillis <= endTime) {
                                setChargeMode(3);
                            }
                        }
                        break;

                    case BmtCmd.GET_CURRENT_RESERVE_MODE:  // 获取模式
                        mReserveModeSubject.onNext(result);
                        break;

                    case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                        mPTReserveSubject.onNext(result);
                        break;

                    case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                        mScheduledReserveSubject.onNext(result);
                        break;

                    case BmtCmd.GET_CUSTOM_SCHEDULEMODE:
                        mCustomScheduleSubject.onNext(result);
                        break;

                    case BmtCmd.GET_VIEW_EXCEPTIONS:
                        ivWarning.setVisibility(BmtUtil.hasException(result) ? View.VISIBLE : View.GONE);
                        break;

                    case DsCamCmd.GET_COMMUNICATE_SIGNAL:
                        int wifi = DeviceHelper.getInt(result, PSKeyConstant.WIFI, 0);
                        int cellular = DeviceHelper.getInt(result, BmtDataKey.CELLULAR, 0);
                        int ethernet = DeviceHelper.getInt(result, BmtDataKey.ETHERNET, 0);
                        if (wifi > 0 && ethernet < 2) {
                            setWifiSignal(wifi);
                        }
                        if (cellular > 0) {
                            setCellularSignal(cellular);
                        }
                        setEthernetSignal(ethernet);
                        resetNameMaxWidth();
                        break;
                    default:
                        break;
                }
            }
        }
    }

    public void setPowerStationDefaultView() {
        updateViewByOnlineStatus(false);
    }

    public void reset(final int status, final boolean hasExceptions) {
        setSegment();
        powerBatteryView.setOnline(status == BmtDeviceStatusChange.ONLINE);
//        ivWarning.setVisibility(hasExceptions ? View.VISIBLE : View.GONE);
    }

    public void updateViewByOnlineStatus(final boolean online) {
        if (!online) {
            tvBalanceVal.setText(mContext.getString(R.string.power_station_cdv_offline_val));
            tvLastForHour.setText(mContext.getString(R.string.power_station_cdv_offline_val));
            tvLastForMinute.setText(mContext.getString(R.string.power_station_cdv_offline_val));
            powerBatteryView.setProgress(0.0f, true);
            powerBatteryView.setChargeStatus(BatteryChargeView.NORMAL, true);
            clUpgrade.setVisibility(View.GONE);
            ivWifi.setVisibility(View.GONE);
            ivCellular.setVisibility(View.GONE);
            tvOffline.setVisibility(View.VISIBLE);
        } else {
            tvOffline.setVisibility(View.GONE);
        }
        powerBatteryView.setOnline(online);
    }

    public void release() {
        if (mBatteryInfoSubscriber != null) {
            mBatteryInfoSubscriber.unsubscribe();
        }
        if (mReserveModePTSubscriber != null) {
            mReserveModePTSubscriber.unsubscribe();
        }
        if (mReserveModeScheduledSubscriber != null) {
            mReserveModeScheduledSubscriber.unsubscribe();
        }
        if (mReserveModeAISubscriber != null) {
            mReserveModeAISubscriber.unsubscribe();
        }
    }
}
