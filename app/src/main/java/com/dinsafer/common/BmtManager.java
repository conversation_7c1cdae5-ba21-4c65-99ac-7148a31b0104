package com.dinsafer.common;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dincore.common.IDeviceStatusListener;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DeviceRequestType;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.model.event.DeviceDeletedStateChangedEvent;
import com.dinsafer.model.event.DeviceLoadedStateChangedEvent;
import com.dinsafer.model.event.NeedGetAllDeviceEvent;
import com.dinsafer.model.event.WidgetFlagDeletedEvent;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.main.helper.MainWidgetListProvider;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.bean.BmtGraphicCacheInfo;
import com.dinsafer.module.powerstation.event.BatteryAccessoryStateChangedEvent;
import com.dinsafer.module.powerstation.event.BatteryIndexChangerEvent;
import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BatteryStatusInfoEvent;
import com.dinsafer.module.powerstation.event.BmtChartDataEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtExceptionEvent;
import com.dinsafer.module.powerstation.event.BmtGetFeatureEvent;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtListUpdateEvent;
import com.dinsafer.module.powerstation.event.ReOpenEvent;
import com.dinsafer.module.powerstation.widget.BatteryChargeView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.TestLog;
import com.dinsafer.util.TimeUtil;

import org.greenrobot.eventbus.EventBus;
import org.iq80.snappy.Main;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/12/5 12:02
 */
public class BmtManager implements IDeviceListChangeListener, IDeviceCallBack, IDeviceStatusListener {
    private static final String TAG = "BmtManager";
    private static final int MAX_PHASE_COUNT = 3;

    public static boolean needGetOutputInfo = true;
    private final ConcurrentHashMap<String, Integer> mDeviceStatusMap = new ConcurrentHashMap<>();
    @NonNull
    private final ConcurrentHashMap<String, Boolean> mDeviceBatteryInputOn = new ConcurrentHashMap<>();
    @NonNull
    private final ConcurrentHashMap<String, BmtGraphicCacheInfo> mDeviceGraphicCacheInfo = new ConcurrentHashMap<>();
    // 非强制升级BMT-忽略集合
    private final Set<String> mLocalIgnoredUpgradeDeviceIdSet = Collections.synchronizedSet(new HashSet<>());

    private final List<Device> mBmtDeviceList;
    private boolean isNotLoadCountries = true;

    public void fetchBmtDevice(IDefaultCallBack callBack) {
        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                DDLog.i(TAG, "fetchBmtDevice");
                final List<Device> bmtDevices = DinSDK.getHomeInstance()
                        .getAllDeviceByType(DinConst.TYPE_BMT_HP5000);
                final List<Device> plugins;
                if (null != bmtDevices) {
                    plugins = new ArrayList<>(bmtDevices);
                } else {
                    plugins = new ArrayList<>();
                }
                Map<String, Object> result = new HashMap<>();
                result.put("bmtPlugin", plugins);
                return result;
            }
        }).thenUI(result -> {
            synchronized (mBmtDeviceList) {
                mBmtDeviceList.clear();
            }
            if (result != null) {
                final Map<String, Object> resultMap = (Map<String, Object>) result;
                final List<Device> newDsCamList = DeviceHelper.getList(resultMap, "bmtPlugin");
                synchronized (mBmtDeviceList) {
                    mBmtDeviceList.addAll(newDsCamList);
                    for (Device bmtDevice : mBmtDeviceList) {
                        bmtDevice.registerDeviceCallBack(this);
                        bmtDevice.registerDeviceStatusListener(this);
                    }
                }
            }
            TestLog.i(TAG, "fetchBmtDevice, bmtCount: " + mBmtDeviceList.size());
            if (callBack != null) {
                callBack.onSuccess();
            }
            EventBus.getDefault().post(new BmtListUpdateEvent());
        });
    }

    /**
     * 获取BMT设备-需要使用这个接口获取BMT数据
     * <p>
     * type仅影响请求的逻辑，返回的都是内存中全部的数据，通过device内部状态区分
     *
     * @param type     0: 获取全部BMT设备数据，缓存加网络(循环请求到没有数据)。
     *                 -1：仅获取缓存，如果没有缓存，相当于0的情况。
     *                 1：仅请求网络一页数据（后面获取还有数据）。
     * @param callBack
     */
    public void fetchBmtDevice(@DeviceRequestType final int type, IDefaultCallBack callBack) {
        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                DDLog.i(TAG, "fetchBmtDevice, type: " + type);
                final List<Device> bmtDevices;
                if (DeviceRequestType.ALL_DEVICE == type) {
                    bmtDevices = DinSDK.getHomeInstance().getDeviceByType(DinConst.TYPE_BMT_HP5000);
                } else if (DeviceRequestType.DEVICE_NEXT_PAGE == type) {
                    bmtDevices = DinSDK.getHomeInstance().getDeviceByType(DinConst.TYPE_BMT_HP5000, false);
                } else {
                    bmtDevices = DinSDK.getHomeInstance().getDeviceByType(DinConst.TYPE_BMT_HP5000, true);
                }
                final List<Device> plugins;
                if (null != bmtDevices) {
                    plugins = new ArrayList<>(bmtDevices);
                } else {
                    plugins = new ArrayList<>();
                }
                Map<String, Object> result = new HashMap<>();
                result.put("bmtPlugin", plugins);
                return result;
            }
        }).thenUI(result -> {
            synchronized (mBmtDeviceList) {
                mBmtDeviceList.clear();
            }
            if (result != null) {
                final Map<String, Object> resultMap = (Map<String, Object>) result;
                final List<Device> newDsCamList = DeviceHelper.getList(resultMap, "bmtPlugin");
                synchronized (mBmtDeviceList) {
                    mBmtDeviceList.addAll(newDsCamList);
                    for (Device bmtDevice : mBmtDeviceList) {
                        bmtDevice.registerDeviceCallBack(this);
                        bmtDevice.registerDeviceStatusListener(this);
                    }
                }
            }
            TestLog.i(TAG, "fetchBmtDevice1, bmtCount: " + mBmtDeviceList.size());
            if (callBack != null) {
                callBack.onSuccess();
            }
            EventBus.getDefault().post(new BmtListUpdateEvent());
        });
    }

    @Nullable
    public Device getDeviceById(final String id, final String subCategory) {
        Device device = null;
        synchronized (mBmtDeviceList) {
            if (!TextUtils.isEmpty(id) && !TextUtils.isEmpty(subCategory) && mBmtDeviceList.size() > 0) {
                for (Device temp : mBmtDeviceList) {
                    if (id.equals(temp.getId()) && subCategory.equals(temp.getSubCategory())) {
                        device = temp;
                        break;
                    }
                }
            }
        }
        return device;
    }

    public String getDeviceNameById(final String id, final String subCategory) {
        String name = "";
        if (!TextUtils.isEmpty(id)) {
            synchronized (mBmtDeviceList) {
                if (!TextUtils.isEmpty(id) && !TextUtils.isEmpty(subCategory) && mBmtDeviceList.size() > 0) {
                    for (Device temp : mBmtDeviceList) {
                        if (id.equals(temp.getId()) && subCategory.equals(temp.getSubCategory())) {
                            name = (String) MapUtils.get(temp.getInfo(), "name", "");
                            break;
                        }
                    }
                }
            }
        }
        return name;
    }

    public boolean removeDeletedDevice(final String deviceId, final String subCategory) {
        DDLog.d(TAG, "removeDeletedDevice: " + deviceId);
        if (TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(subCategory)) {
            return false;
        }
        synchronized (mBmtDeviceList) {
            if (mBmtDeviceList.size() > 0) {
                Device temp;
                for (int i = 0; i < mBmtDeviceList.size(); i++) {
                    temp = mBmtDeviceList.get(i);
                    if (deviceId.equals(temp.getId()) && subCategory.equals(temp.getSubCategory())) {
                        mBmtDeviceList.remove(i);
                        checkResetNotLoadCountries();
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @NonNull
    public List<Device> getAllBmtDevice() {
        final List<Device> result;
        synchronized (mBmtDeviceList) {
            result = new ArrayList<>(mBmtDeviceList);
        }
        return result;
    }

    public List<Device> getPowerCoreDevice() {
        final List<Device> result;
        synchronized (mBmtDeviceList) {
            result = new ArrayList<>();
            for (Device device : mBmtDeviceList) {
                if (BmtUtil.isBmtDeviceHP5000(device)
                        || BmtUtil.isBmtDevicePowerCore20(device)
                        || BmtUtil.isBmtDevicePowerCore30(device)) {
                    result.add(device);
                }
            }
        }
        return result;
    }


    public List<Device> getPowerStoreDevice() {
        final List<Device> result;
        synchronized (mBmtDeviceList) {
            result = new ArrayList<>();
            for (Device device : mBmtDeviceList) {
                if (BmtUtil.isBmtDevicePowerStore(device)) {
                    result.add(device);
                }
            }
        }
        return result;
    }

    public List<Device> getPowerPulseDevice() {
        final List<Device> result;
        synchronized (mBmtDeviceList) {
            result = new ArrayList<>();
            for (Device device : mBmtDeviceList) {
                if (BmtUtil.isBmtDevicePowerPulse(device)) {
                    result.add(device);
                }
            }
        }
        return result;
    }

    public int getAllDevicesSize() {
        return mBmtDeviceList.size();
    }

    public int getNotDeleteDevicesSize() {
        int size = 0;
        synchronized (mBmtDeviceList) {
            for (Device device : mBmtDeviceList) {
                if (device.getFlagDeleted()) {
                    continue;
                }
                size++;
            }
        }
        return size;
    }

    public List<Device> getNotDeletedBmtDeviceList() {
        List<Device> list = new ArrayList<>();
        synchronized (mBmtDeviceList) {
            for (Device device : mBmtDeviceList) {
                if (!device.getFlagDeleted()) {
                    list.add(device);
                }
            }
        }
        return list;
    }

    public int getNotDeleteDevicesSizeBySubCategory(String subCategory) {
        int size = 0;
        if (!TextUtils.isEmpty(subCategory)) {
            synchronized (mBmtDeviceList) {
                for (Device device : mBmtDeviceList) {
                    String devSubcategory = device.getSubCategory();
                    if (device.getFlagDeleted()) {
                        continue;
                    }
                    if (TextUtils.isEmpty(devSubcategory)) {
                        continue;
                    }
                    if (!devSubcategory.equals(subCategory)) {
                        continue;
                    }
                    size++;
                }
            }
        }
        return size;
    }

    public int getNotDeleteDevicesSizeBySubCategories(String... subCategories) {
        int size = 0;
        if (subCategories.length > 0) {
            synchronized (mBmtDeviceList) {
                for (Device device : mBmtDeviceList) {
                    String devSubcategory = device.getSubCategory();
                    if (device.getFlagDeleted()) {
                        continue;
                    }
                    if (TextUtils.isEmpty(devSubcategory)) {
                        continue;
                    }
                    if (!Arrays.asList(subCategories).contains(devSubcategory)) {
                        continue;
                    }
                    size++;
                }
            }
        }
        return size;
    }

    public List<Device> getNotDeletedBmtDeviceListBySubCategory(String subCategory) {
        List<Device> list = new ArrayList<>();
        if (!TextUtils.isEmpty(subCategory)) {
            synchronized (mBmtDeviceList) {
                for (Device device : mBmtDeviceList) {
                    String devSubcategory = device.getSubCategory();
                    if (!device.getFlagDeleted()
                            && !TextUtils.isEmpty(devSubcategory)
                            && devSubcategory.equals(subCategory)) {
                        list.add(device);
                    }
                }
            }
        }
        return list;
    }

    public List<Device> getNotDeletedBmtDeviceListBySubCategories(String... subCategories) {
        List<Device> list = new ArrayList<>();
        if (subCategories.length > 0) {
            synchronized (mBmtDeviceList) {
                for (Device device : mBmtDeviceList) {
                    String devSubcategory = device.getSubCategory();
                    if (!device.getFlagDeleted()
                            && !TextUtils.isEmpty(devSubcategory)
                            && Arrays.asList(subCategories).contains(devSubcategory)) {
                        list.add(device);
                    }
                }
            }
        }
        return list;
    }

    public void connectAllDevice() {
        DDLog.i(TAG, "connectAllDevice===");
        final Map<String, Object> par = new HashMap<>();
        par.put("cmd", DsCamCmd.CMD_CONNECT);
        DDLog.i(MainActivity.TEST_TIME, "connectAllDevice");
        synchronized (mBmtDeviceList) {
            for (Device device : mBmtDeviceList) {
                device.submit(par);
            }
        }
    }

    public void getAllDeviceLoadUsage() {
        synchronized (mBmtDeviceList) {
            for (Device device : mBmtDeviceList) {
                submitCmd(device, BmtCmd.GET_STATS_LOADUSAGE_V2, 0);
            }
        }
    }

    public void connectDevice(Device device) {
        DDLog.i(MainActivity.TEST_TIME, "connectDevice");
        connectDevice(device, false);
    }

    /**
     * @param device
     * @param discardCache 是否忽略本地缓存的bmt信息，true:会在连接之前重新查询bmt信息
     */
    public void connectDevice(Device device, final boolean discardCache) {
        if (!BmtUtil.isBmtDevice(device)) {
            DDLog.e(TAG, "connectDevice: device null or device is not bmt device");
            return;
        }
        DDLog.i(TAG, "connectDevice===");
        final Map<String, Object> par = new HashMap<>();
        par.put("cmd", DsCamCmd.CMD_CONNECT);
        par.put("discardCache", discardCache);
        device.submit(par);
    }


    public void disconnectDevice(Device device) {
        if (!BmtUtil.isBmtDevice(device)) {
            DDLog.e(TAG, "disconnectDevice: device null or device is not bmt device");
            return;
        }
        final Map<String, Object> par = new HashMap<>();
        par.put("cmd", DsCamCmd.DISCONNECT);
        device.submit(par);
    }

    public void disconnectAllDevice() {
        final Map<String, Object> par = new HashMap<>();
        par.put("cmd", DsCamCmd.DISCONNECT);
        synchronized (mBmtDeviceList) {
            for (Device device : mBmtDeviceList) {
                device.submit(par);
            }
        }
    }

    public void releaseAllDevice() {
        synchronized (mBmtDeviceList) {
            final Map<String, Object> par = new HashMap<>();
            par.put("cmd", DsCamCmd.DISCONNECT);
            for (Device device : mBmtDeviceList) {
                device.submit(par);
                device.unregisterDeviceCallBack(this);
                device.unregisterDeviceStatusListener(this);
            }
            mBmtDeviceList.clear();
        }
        mDeviceStatusMap.clear();
        mDeviceBatteryInputOn.clear();
        mDeviceGraphicCacheInfo.clear();
        mLocalIgnoredUpgradeDeviceIdSet.clear();
    }

    public void addIgnoredUpgrade(final String deviceId, final String subcategory) {
        if (TextUtils.isEmpty(deviceId)) {
            return;
        }
        mLocalIgnoredUpgradeDeviceIdSet.add(deviceId + MainWidgetListProvider.UNDERLINE + subcategory);

        // 通知刷新界面
        final BmtGraphicCacheInfo bmtGraphicCacheInfo = mDeviceGraphicCacheInfo.get(deviceId + MainWidgetListProvider.UNDERLINE + subcategory);
        if (null != bmtGraphicCacheInfo) {
            final BmtGraphicUpdateEvent lastChipsStatusEvent = bmtGraphicCacheInfo.getBmtGraphicUpdateEventByCmd(BmtCmd.GET_CHIPS_STATUS);
            if (null != lastChipsStatusEvent) {
                EventBus.getDefault().post(lastChipsStatusEvent);
            }
        }
    }

    /**
     * 是否非强制升级的忽略升级
     *
     * @param deviceId
     * @return
     */
    public boolean isIgnoredUpgrade(final String deviceId, final String subcategory) {
        if (TextUtils.isEmpty(deviceId)) {
            return true;
        }
        return mLocalIgnoredUpgradeDeviceIdSet.contains(deviceId + MainWidgetListProvider.UNDERLINE + subcategory);
    }

    public BmtManager() {
        mBmtDeviceList = new ArrayList<>();
        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
    }

    @Override
    public void onDeviceAdd(Device device) {
        if (!BmtUtil.isBmtDevice(device)) {
            return;
        }

        synchronized (mBmtDeviceList) {
            mBmtDeviceList.add(device);
        }
        device.registerDeviceCallBack(this);
        device.registerDeviceStatusListener(this);
        connectDevice(device);
        EventBus.getDefault().post(new BmtListUpdateEvent(device.getId(), device.getSubCategory(), BmtListUpdateEvent.OPERATION_ADD));
        EventBus.getDefault().post(new NeedGetAllDeviceEvent(false, false, false, false));
    }

    @Override
    public void onDeviceRemove(Device dev) {
        final Device device = getDeviceById(dev.getId(), dev.getSubCategory());
        if (null != device) {
            device.unregisterDeviceCallBack(this);
            device.unregisterDeviceStatusListener(this);
            synchronized (mBmtDeviceList) {
                mBmtDeviceList.remove(device);
            }
            EventBus.getDefault().post(new BmtListUpdateEvent(dev.getId(), dev.getSubCategory(), BmtListUpdateEvent.OPERATION_DELETE));
            EventBus.getDefault().post(new NeedGetAllDeviceEvent(false, false, false, false));
        }
    }

    public static HashMap<String, Integer> mIndexMap = new HashMap<>();

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (MainPanelHelper.getInstance().isPanelEditMode()) return;
        final Device targetDevice = getDeviceById(deviceId, subCategory);
        final boolean isBmtDevice = BmtUtil.isBmtDevice(targetDevice);
        if (!isBmtDevice || TextUtils.isEmpty(cmd)) {
            return;
        }

        Log.v(TAG, "onCmdCallBack, bmtDevice: " + deviceId
                + "  " + subCategory
                + " /cmd:" + cmd
                + " /result:" + map.toString()
                + " /" + Thread.currentThread().getName());

        sendBmtGraphicUpdateEventIfNeed(deviceId, subCategory, cmd, map);

        final int status = (int) MapUtils.get(map, "status", -1);
        final Map<String, ?> result = DeviceHelper.getMap(map, PSKeyConstant.RESULT);
        int connect_status = -1;
        int previousStatus = -1;
        final BmtGraphicCacheInfo bmtGraphicCacheInfo = ensureGraphicCacheInfo(deviceId, subCategory);
        switch (cmd) {
            case DsCamCmd.CMD_CONNECT:
                connect_status = (int) MapUtils.get(map, "connect_status", -1);
                previousStatus = DeviceHelper.getInt(map, BmtDataKey.PREVIOUS_STATUS, -1);
                EventBus.getDefault().post(new BmtDeviceStatusChange(deviceId, subCategory, connect_status));
                if (BmtDeviceStatusChange.ONLINE == connect_status) {
                    mIndexMap.put(deviceId, 0);
//                    submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INFO, 0);
//                    submitCmd(targetDevice, BmtCmd.GET_MODE_V2, 1);
                    submitCmd(targetDevice, BmtCmd.GET_ADVANCE_INFO, 0);
                    mDeviceStatusMap.put(deviceId, BmtDeviceStatusChange.ONLINE);
                    submitCmd(targetDevice, BmtCmd.GET_FEATURE, 1);
                    submitCmd(targetDevice, BmtCmd.GET_AIMODE_SETTINGS, 0);

                    new Handler(Looper.getMainLooper()).postDelayed(() -> submitCmd(targetDevice, BmtCmd.GET_ALL_SUPPORT_FEATURES, 0), 3000);
                }
                break;
            case DsCamCmd.CONNECT_STATUS_CHANGED:
                connect_status = (int) MapUtils.get(map, "connect_status", -1);
                // 在线会触发DsCamCmd.CMD_CONNECT，所以无需重复处理
                if (connect_status != BmtDeviceStatusChange.ONLINE) {
                    DDLog.d(TAG, deviceId + "CONNECT_STATUS_CHANGED: " + connect_status);
                    mDeviceGraphicCacheInfo.remove(deviceId + MainWidgetListProvider.UNDERLINE + subCategory);
                    previousStatus = DeviceHelper.getInt(map, BmtDataKey.PREVIOUS_STATUS, -1);
                    EventBus.getDefault().post(new BmtDeviceStatusChange(deviceId, subCategory, connect_status, previousStatus));
                    mDeviceStatusMap.put(deviceId, BmtDeviceStatusChange.OFFLINE);
                } else {
                    mIndexMap.put(deviceId, 0);
//                    submitCmd(targetDevice, BmtCmd.GET_CHIPS_STATUS, 0);
//                    submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INFO, 0);
//                    submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INPUT_INFO, 1);
//                    submitCmd(targetDevice, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO, 1);
//                    submitCmd(targetDevice, DsCamCmd.GET_GLOBAL_EXCEPTIONS, 1);
//                    submitCmd(targetDevice, DsCamCmd.GET_COMMUNICATE_SIGNAL, 1);
                    DDLog.i(MainActivity.TEST_TIME, "connectDeviceSuccess");
                    getBmtInfo(targetDevice);
                    mDeviceStatusMap.put(deviceId, BmtDeviceStatusChange.ONLINE);
                }
                break;
            case DsCamCmd.SET_NAME:
                EventBus.getDefault().post(new NeedGetAllDeviceEvent(false, false, false, false));
                break;
            case DsCamCmd.GET_INVERTER_INPUT_INFO:
                onResultInverterInputInfo(deviceId, status, result);
                if (needGetOutputInfo) {
                    submitCmd(targetDevice, DsCamCmd.GET_INVERTER_OUTPUT_INFO, 1);
                }
                break;
            case DsCamCmd.GET_INVERTER_OUTPUT_INFO:
//                onResultInverterOutputInfo(deviceId, status, result);
//                submitCmd(targetDevice, DsCamCmd.GET_BATTERY_ALLINFO, 1);
//                if (needGetOutputInfo) {
//                    submitCmd(targetDevice, DsCamCmd.GET_MPPT_STATE, 1);
//                    submitCmd(targetDevice, DsCamCmd.GET_EV_STATE, 1);
//                    submitCmd(targetDevice, BmtCmd.GET_CURRENT_EVADVANCESTATUS, 1);
//                }
                break;
            case DsCamCmd.SET_INVERTER_OPEN:
//            case BmtCmd.RESTART_EV:
//                boolean on = DeviceHelper.getBoolean(result, BmtDataKey.ON, false);
//                if (on) {
//                    submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INPUT_INFO, 0);
//                    mIndexMap.put(deviceId, 0);
//                    submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INFO, 0);
//                    submitCmd(targetDevice, DsCamCmd.GET_GLOBAL_EXCEPTIONS, 0);
//                    startPolling();
//                } else {
//                    try {
//                        Thread.sleep(2000);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    submitCmdInverterOpen(targetDevice);
//                }
                break;
            case DsCamCmd.GET_BATTERY_ALLINFO:
//                submitCmd(targetDevice, DsCamCmd.GET_EMERGENCY_CHARGE, 0);
                break;
            case DsCamCmd.GET_EMERGENCY_CHARGE:
                submitCmd(targetDevice, BmtCmd.GET_CURRENT_RESERVE_MODE, 0);
                break;
            case DsCamCmd.SYSTEM_EXCEPTION:
            case DsCamCmd.INVERTER_EXCEPTION:
            case DsCamCmd.BATTERY_EXCEPTION:
            case DsCamCmd.MPPT_EXCEPTION:
            case DsCamCmd.EV_EXCEPTION:
            case DsCamCmd.COMMUNICATION_EXCEPTION:
            case DsCamCmd.CABINET_EXCEPTION:
                EventBus.getDefault().post(new BmtExceptionEvent(deviceId, subCategory, cmd, map));
                break;
            case DsCamCmd.GET_INVERTER_INFO:
                Integer inverterIndex = mIndexMap.get(deviceId);
                if (inverterIndex == null) return;
                inverterIndex = inverterIndex < 2 ? inverterIndex + 1 : 0;
                mIndexMap.put(deviceId, inverterIndex);
                if (inverterIndex > 0) {
//                    submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INFO, inverterIndex);
                }
                break;
            case BmtCmd.GET_CHIPS_STATUS:
                final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
                if (-1 != chipsStatus) {
                    mLocalIgnoredUpgradeDeviceIdSet.remove(deviceId + MainWidgetListProvider.UNDERLINE + subCategory);
                }
                break;

            case BmtCmd.BATTERY_STATUSINFO_NOTIFY:
                int socState = DeviceHelper.getInt(result, BmtDataKey.SOC_STATE, -1);
                if (-1 != socState) {
                    BatteryStatusInfoEvent event = new BatteryStatusInfoEvent(deviceId, subCategory, socState);
                    EventBus.getDefault().post(event);
                }
                break;

            case DsCamCmd.BATTERY_INDEX_CHANGED:
                int cabinetIndex = DeviceHelper.getInt(result, PSKeyConstant.CABINET_INDEX, -1);
                int cabinetPositionIndex = DeviceHelper.getInt(result, PSKeyConstant.CABINET_POSITION_INDEX, -1);
                boolean isAdd = DeviceHelper.getBoolean(result, PSKeyConstant.IS_ADD, false);
                if (cabinetIndex != -1 && cabinetPositionIndex != -1) {
                    BatteryIndexChangerEvent event = new BatteryIndexChangerEvent(deviceId, subCategory, cabinetIndex, cabinetPositionIndex, isAdd);
                    EventBus.getDefault().post(event);
                }
                break;

            case BmtCmd.BATTERY_ACCESSORYSTATE_CHANGED_CUSTOM:
                int index = DeviceHelper.getInt(result, PSKeyConstant.INDEX, -1);
                int cabinetIndexS = DeviceHelper.getInt(result, PSKeyConstant.CABINET_INDEX, -1);
                int cabinetPositionIndexS = DeviceHelper.getInt(result, PSKeyConstant.CABINET_POSITION_INDEX, -1);
                if (cabinetIndexS != -1 && cabinetPositionIndexS != -1) {
                    boolean heating = DeviceHelper.getBoolean(result, PSKeyConstant.HEATING, false);
                    boolean heatAvailable = DeviceHelper.getBoolean(result, PSKeyConstant.HEAT_AVAILABLE, false);
                    if (heatAvailable) {
                        BatteryAccessoryStateChangedEvent event = new BatteryAccessoryStateChangedEvent(deviceId, subCategory, heating, cabinetIndexS, cabinetPositionIndexS);
                        EventBus.getDefault().post(event);
                    }
                }
                break;

            case BmtCmd.GET_CURRENT_RESERVE_MODE:  // 获取模式
                int reserveMode = DeviceHelper.getInt(result, BmtDataKey.RESERVE_MODE, 0);
                if (reserveMode == 1) {
                    submitCmd(targetDevice, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE, 0);
                } else if (reserveMode == 2) {
                    submitCmd(targetDevice, BmtCmd.GET_SCHEDULE_RESERVE_MODE, 0);
                } else if (reserveMode == 3) {
                    submitCmd(targetDevice, BmtCmd.GET_CUSTOM_SCHEDULEMODE, 0);
                }
                break;

            case BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO:
//                submitCmd(targetDevice, DsCamCmd.GET_BATTERY_ALLINFO, 1);
//                submitCmd(targetDevice, DsCamCmd.GET_MPPT_STATE, 1);
//                submitCmd(targetDevice, BmtCmd.GET_CURRENT_EVADVANCESTATUS, 1);
                break;

            case BmtCmd.RESET_INVERTER:
                int delay = 3;
                if (status == 1) {
                    delay = DeviceHelper.getInt(result, BmtDataKey.DELAY, 3);
                }
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INPUT_INFO, 0);
                        mIndexMap.put(deviceId, 0);
//                        submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INFO, 0);
                        submitCmd(targetDevice, DsCamCmd.GET_GLOBAL_EXCEPTIONS, 0);
                        startPolling();
                    }
                }, delay * 1000);

                break;

            case BmtCmd.GET_STATS_LOADUSAGE_V2:
            case BmtCmd.GET_STATS_LOADUSAGE:
                BmtChartDataEvent bmtChartDataEvent = new BmtChartDataEvent(deviceId, subCategory, (Map<String, Object>) result);
                bmtGraphicCacheInfo.setLastChartDataEvent(bmtChartDataEvent);
                EventBus.getDefault().post(bmtChartDataEvent);
                break;

            case BmtCmd.GET_FEATURE:
                BmtGetFeatureEvent bmtGetFeatureEvent = new BmtGetFeatureEvent(deviceId, subCategory, (Map<String, Object>) result);
                bmtGraphicCacheInfo.setLastBmtGetFeatureEvent(bmtGetFeatureEvent);
                EventBus.getDefault().post(bmtGetFeatureEvent);
                break;
            default:
                break;
        }
    }

    @NonNull
    private BmtGraphicCacheInfo ensureGraphicCacheInfo(@NonNull final String deviceId, @NonNull final String subcategory) {
        synchronized (mDeviceGraphicCacheInfo) {
            BmtGraphicCacheInfo bmtGraphicCacheInfo = mDeviceGraphicCacheInfo.get(deviceId + MainWidgetListProvider.UNDERLINE + subcategory);
            if (null == bmtGraphicCacheInfo) {
                bmtGraphicCacheInfo = new BmtGraphicCacheInfo(deviceId, subcategory);
                mDeviceGraphicCacheInfo.put(deviceId + MainWidgetListProvider.UNDERLINE + subcategory, bmtGraphicCacheInfo);
            }
            return bmtGraphicCacheInfo;
        }
    }

    @Nullable
    public BmtGraphicCacheInfo getGraphicCacheInfo(final String deviceId, final String subcategory) {
        return mDeviceGraphicCacheInfo.get(deviceId + MainWidgetListProvider.UNDERLINE + subcategory);
    }

    public void removeDeviceGraphicCacheInfoByKey(final String deviceId, final String subcategory) {
        if (TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(subcategory)) return;
        mDeviceGraphicCacheInfo.remove(deviceId + MainWidgetListProvider.UNDERLINE + subcategory);
    }

    /**
     * 请求Device的电流图电池图信息
     *
     * @param deviceId
     */
    public void requestCurrentAndBatteryInfo(@NonNull final String deviceId, final String subcategory) {
        final Device targetDevice = getDeviceById(deviceId, subcategory);
        final boolean isBmtDevice = BmtUtil.isBmtDevice(targetDevice);
        if (!isBmtDevice || !BmtUtil.isDeviceConnected(targetDevice)) {
            return;
        }

        mIndexMap.put(deviceId, 0);
//        submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INFO, 0);
//        submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INPUT_INFO, 1);
//        submitCmd(targetDevice, DsCamCmd.GET_GLOBAL_EXCEPTIONS, 1);
//        submitCmd(targetDevice, DsCamCmd.GET_COMMUNICATE_SIGNAL, 1);
        getBmtInfo(targetDevice);
        submitCmd(targetDevice, BmtCmd.GET_STATS_LOADUSAGE_V2, 0);
    }

    public void getBmtInfo(Device targetDevice) {
        DDLog.i(MainActivity.TEST_TIME, "getGlobalCurrentFlowInfo");
        submitCmd(targetDevice, BmtCmd.GET_REGULATE_FREQUENCY_STATE, 0);
        submitCmd(targetDevice, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO, 0);
        int phaseCount = BmtUtil.isThreePhase(targetDevice) ? 3 : 1;
        for (int i = 0; i < phaseCount; i++) {
            submitCmd(targetDevice, BmtCmd.GET_INVERTER_INFO, i);
        }
        submitCmd(targetDevice, DsCamCmd.GET_BATTERY_ALLINFO, 0);
        submitCmd(targetDevice, BmtCmd.GET_CURRENT_EVADVANCESTATUS, 0);
        submitCmd(targetDevice, BmtCmd.GET_CURRENT_EV_CHARGING_MODE, 0);
        int iotVersionStatus = BmtUtil.getIotVersionGETargetVersion(targetDevice, "1.8.0");
        if (iotVersionStatus == 0) {
            submitCmd(targetDevice, BmtCmd.GET_MODE, 0);
        } else {
            submitCmd(targetDevice, BmtCmd.GET_MODE_V2, 0);
        }
        submitCmd(targetDevice, DsCamCmd.GET_EMERGENCY_CHARGE, 0);
        submitCmd(targetDevice, BmtCmd.GET_CHIPS_STATUS, 0);
        submitCmd(targetDevice, DsCamCmd.GET_GLOBAL_EXCEPTIONS, 1);
        submitCmd(targetDevice, BmtCmd.GET_VIEW_EXCEPTIONS, 1);
        submitCmd(targetDevice, DsCamCmd.GET_COMMUNICATE_SIGNAL, 1);
//        submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INFO, 0);
        submitCmd(targetDevice, DsCamCmd.GET_INVERTER_INPUT_INFO, 1);
    }

    private void onResultInverterOutputInfo(@NonNull final String deviceId, @NonNull final String subcategory, int status, Map<String, ?> result) {
        Map<String, Object> phase;
        boolean batteryOutputOn = false;
        if (StatusConstant.STATUS_SUCCESS == status) {
            for (int i = 0; i < MAX_PHASE_COUNT; i++) {
                phase = DeviceHelper.getMap(result, String.valueOf(i));
                boolean batteryOn = (boolean) MapUtils.get(phase, PSKeyConstant.BATTERY_ON, false);
                if (batteryOn) {
                    batteryOutputOn = true;
                    break;
                }
            }
        }
        final Boolean inputOn = mDeviceBatteryInputOn.get(deviceId);
        final boolean batteryInputOn = null != inputOn && inputOn;

        int batteryStatus = BatteryChargeView.NORMAL;
        if (batteryInputOn) {
            batteryStatus = BatteryChargeView.DISCHARGING;
        }
        if (batteryOutputOn) {
            batteryStatus = BatteryChargeView.CHARGING;
        }
        final BatteryStatusEvent batteryStatusEvent = new BatteryStatusEvent(deviceId, subcategory, batteryStatus);
        final BmtGraphicCacheInfo bmtGraphicCacheInfo = ensureGraphicCacheInfo(deviceId, subcategory);
        bmtGraphicCacheInfo.setLastBatteryStatusEvent(batteryStatusEvent);
        EventBus.getDefault().post(batteryStatusEvent);
    }

    private void onResultInverterInputInfo(@NonNull final String deviceId, int status, Map<String, ?> result) {
        Map<String, Object> phase;
        boolean batteryInputOn = false;
        if (StatusConstant.STATUS_SUCCESS == status) {
            for (int i = 0; i < MAX_PHASE_COUNT; i++) {
                phase = DeviceHelper.getMap(result, String.valueOf(i));
                boolean batteryOn = (boolean) MapUtils.get(phase, PSKeyConstant.BATTERY_ON, false);
                if (batteryOn) {
                    batteryInputOn = true;
                    break;
                }
            }
        }
        mDeviceBatteryInputOn.put(deviceId, batteryInputOn);
    }

    /**
     * 检查是否需要发送GraphicUpdateEvent
     * 如果需要，则发送GraphicUpdateEvent
     */
    private void sendBmtGraphicUpdateEventIfNeed(@NonNull final String deviceId, @NonNull final String subCategory, @NonNull final String cmd, Map<String, Object> map) {
        final boolean needUpdateCurrent = BmtUtil.isCmdNeedSendBmtCurrentUpdateEvent(cmd);
        final boolean needUpdateBattery = BmtUtil.isCmdNeedSendBmtBatteryUpdateEvent(cmd);
        BmtGraphicUpdateEvent event = null;
        if (needUpdateCurrent && needUpdateBattery) {
            event = new BmtGraphicUpdateEvent(deviceId, subCategory, cmd, map, BmtGraphicUpdateEvent.TYPE_CURRENT_BATTERY);
        } else if (needUpdateCurrent) {
            event = new BmtGraphicUpdateEvent(deviceId, subCategory, cmd, map, BmtGraphicUpdateEvent.TYPE_CURRENT);
        } else if (needUpdateBattery) {
            event = new BmtGraphicUpdateEvent(deviceId, subCategory, cmd, map, BmtGraphicUpdateEvent.TYPE_BATTERY);
        }

        if (null != event) {
            final BmtGraphicCacheInfo bmtGraphicCacheInfo = ensureGraphicCacheInfo(deviceId, subCategory);
            bmtGraphicCacheInfo.addGraphicUpdateEvent(event);
            EventBus.getDefault().post(event);
        }
    }

    private void submitCmd(Device device, String cmd, int index) {
        final Map<String, Object> params = new HashMap<>();
        if (device != null) {
            params.put(PSKeyConstant.CMD, cmd);
            switch (cmd) {
                case DsCamCmd.GET_INVERTER_INFO:
                    params.put(PSKeyConstant.INDEX, index);
                    break;
                case BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO:
                    params.put(BmtDataKey.DATA_MODE, DBUtil.Num(DBKey.KEY_CURRENT_INFO_DATA_MODE + device.getId()));
                    break;

                case BmtCmd.GET_STATS_LOADUSAGE_V2:
                    params.put(BmtDataKey.INTERVAL, ChartDataUtil.DAY);
                    params.put(BmtDataKey.OFFSET, 0);
                    break;
            }
            device.submit(params);
        }
    }

    @Override
    public void online(String s, String subCategory) {

    }

    @Override
    public void offline(String s, String subCategory, String s1) {

    }

    @Override
    public void onInfoUpdate(String id, String subCategory, int type) {
        Device bmtDevice = getDeviceById(id, subCategory);
        if (null == bmtDevice) {
            return;
        }
        if (FLAG_DELETED == type) {
            DDLog.d(TAG, "onInfoUpdate. " + bmtDevice.getId() + " " + bmtDevice.getSubCategory() + ", deleted state change: " + bmtDevice.getFlagDeleted());
            EventBus.getDefault().post(new WidgetFlagDeletedEvent(bmtDevice.getId(), bmtDevice.getSubCategory(), bmtDevice.getFlagDeleted(), MainPanelHelper.SECTION_TYPE_BMT));
            EventBus.getDefault().post(new DeviceDeletedStateChangedEvent(bmtDevice.getId(), bmtDevice.getSubCategory()));
            checkResetNotLoadCountries();
        } else if (FLAG_CACHE == type) {
            DDLog.d(TAG, "onInfoUpdate. " + bmtDevice.getId() + " " + bmtDevice.getSubCategory() + ", cache state change: " + bmtDevice.getFlagCache());

        } else if (FLAG_LOADED == type) {
            DDLog.d(TAG, "onInfoUpdate. " + bmtDevice.getId() + " " + bmtDevice.getSubCategory() + ", loaded state change: " + bmtDevice.getFlagLoaded());
            if (isNotLoadCountries && bmtDevice.getFlagLoaded() && !bmtDevice.getFlagDeleted()) {
                isNotLoadCountries = true;
                // MainPanelHelper添加GRID_REWARDS的时候需要先判断有没有未删除的设备
                // 所以移到这里, 要不然所有设备都删除了, 显示GRID_REWARDS入口
                MainPanelHelper.getInstance().getBmtRegionCountries();
            }
            EventBus.getDefault().post(new DeviceLoadedStateChangedEvent(bmtDevice.getId(), bmtDevice.getSubCategory(), PluginConstants.TYPE_BMT, false));
        }
    }

    public void setNotLoadCountries(boolean notLoadCountries) {
        isNotLoadCountries = notLoadCountries;
    }

    public void checkResetNotLoadCountries() {
        // 如果所有设备都删除了也置为未加载bmt国家列表
        if (getNotDeletedBmtDeviceList().size() == 0) {
            isNotLoadCountries = true;
        }
    }

    private static class Holder {
        private static final BmtManager INSTANCE = new BmtManager();

        private Holder() {
        }

    }

    public static BmtManager getInstance() {
        return BmtManager.Holder.INSTANCE;
    }


    public static String getDeviceName(Device device) {
        if (device == null) return "";
        String name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
        if (org.apache.http.util.TextUtils.isEmpty(name)) {
            name = (String) DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, "");
        }
        return name;
    }

    @NonNull
    public ConcurrentHashMap<String, Integer> getDeviceStatusMap() {
        return mDeviceStatusMap;
    }

    /**
     * 重启逆变器
     */
    private void submitCmdInverterOpen(Device device) {
        if (device != null) {
            BmtManager.getInstance().stopPolling();
            EventBus.getDefault().post(new ReOpenEvent(device.getId(), device.getSubCategory()));
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_INVERTER_OPEN);
            params.put(PSKeyConstant.ON, true);
            List<Integer> indexs = new ArrayList<>();
            int phaseCount = BmtUtil.isThreePhase(device) ? 3 : 1;
            for (int i = 0; i < phaseCount; i++) {
                indexs.add(i);
            }
            params.put(PSKeyConstant.INDEXS, indexs);
            device.submit(params);
        }
    }

    public void resetInverter(Device device, boolean needLoading) {
        if (device != null) {
            BmtManager.getInstance().stopPolling();
            if (needLoading) {
                EventBus.getDefault().post(new ReOpenEvent(device.getId(), device.getSubCategory()));
            }
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.RESET_INVERTER);
            device.submit(params);
        }
    }

    public void setEmergencyCharge(Device device, boolean isOn) {
        if (device != null) {
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_EMERGENCY_CHARGE);
            long changeStartTime = 0;
            long changeEndTime = 0;
            if (isOn) {
                changeStartTime = TimeUtil.getNowHourTimeMillis();
                changeEndTime = changeStartTime + 172800;
                changeStartTime = System.currentTimeMillis() / 1000l;

            }
            params.put(PSKeyConstant.START_TIME, changeStartTime);
            params.put(PSKeyConstant.END_TIME, changeEndTime);
            params.put(PSKeyConstant.ON, isOn);
            device.submit(params);
        }
    }

    /**
     * 即时充电
     *
     * @param device
     * @param open
     */
    public void setInstantCharge(Device device, boolean open) {
        if (device != null) {
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
            params.put(BmtDataKey.OPEN, open);
            device.submit(params);
        }
    }

    public void getViewExceptions(Device device) {
        if (device != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_VIEW_EXCEPTIONS);
            device.submit(params);
        }
    }

    /**
     * 获取电网并网配置(0xa034)
     *
     * @param device
     */
    public void getGridconnectionConfig(Device device) {
        if (device != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_GRIDCONNECTION_CONFIG);
            device.submit(params);
        }
    }

    /**
     * 恢复默认电网并网配置(0xa036)
     *
     * @param device
     */
    public void resumeGridconnectionConfig(Device device) {
        if (device != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.RESUME_GRIDCONNECTION_CONFIG);
            device.submit(params);
        }
    }

    public void getPVDist(Device device) {
        if (device != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_PV_DIST);
            device.submit(params);
            device.submit(params);
        }
    }

    /**
     * 设置 PV 供应优先级(0xa048)
     * @param device
     */
    public void setPVDist(Device device, int ai, int scheduled) {
        if (device != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(BmtDataKey.CMD, BmtCmd.SET_PV_DIST);
            params.put(BmtDataKey.AI, ai);
            params.put(BmtDataKey.SCHEDULED, scheduled);
            device.submit(params);
        }
    }

    /*************** 轮询 ****************/
    private Timer mTimer;

    public synchronized void startPolling() {
        if (mTimer == null) {
            mTimer = new Timer();
        }
        mTimer.schedule(new PollingBmtTask(BmtManager.getInstance()), 0, 15 * 1000);
    }

    public synchronized void stopPolling() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer.purge();
            mTimer = null;
        }
    }

    public void pollingBmt() {
        if (!needGetOutputInfo) return;
        for (Device device : mBmtDeviceList) {
            String deviceId = device.getId();
            if (!TextUtils.isEmpty(deviceId)) {
                if (BmtUtil.isDeviceConnected(device)) {
                    mIndexMap.put(device.getId(), 0);
                    // submitCmd(device, BmtCmd.GET_CHIPS_STATUS, 0);
//                    submitCmd(device, DsCamCmd.GET_INVERTER_INFO, 0);
//                    submitCmd(device, DsCamCmd.GET_INVERTER_INPUT_INFO, 0);
//                    submitCmd(device, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO, 1);
//                    submitCmd(device, DsCamCmd.GET_MODE, 0);
//                    submitCmd(device, DsCamCmd.GET_GLOBAL_EXCEPTIONS, 1);
//                    submitCmd(device, DsCamCmd.GET_COMMUNICATE_SIGNAL, 1);
                    getBmtInfo(device);
                }
            }
        }
    }

    public static class PollingBmtTask extends TimerTask {
        private final WeakReference<BmtManager> reference;

        public PollingBmtTask(BmtManager bmtManager) {
            reference = new WeakReference<>(bmtManager);
        }

        @Override
        public void run() {
            BmtManager bm = reference.get();
            if (bm != null) {
                bm.pollingBmt();
            }
        }
    }
}
