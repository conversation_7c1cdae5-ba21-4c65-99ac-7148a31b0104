package com.dinsafer;


import android.annotation.TargetApi;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.alibaba.fastjson.JSON;
import com.dinsafer.aop.annotations.Safer;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DeviceCmdAckEvent;
import com.dinsafer.model.DeviceEventListEvent;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.DeviceSimStatueEvent;
import com.dinsafer.model.EventListDataFixTime;
import com.dinsafer.model.MultiDataEntry;
import com.dinsafer.model.MultiDataEntry.ResultBean.EventlistBean;
import com.dinsafer.model.OfflineEvent;
import com.dinsafer.model.ShowBlockToastEvent;
import com.dinsafer.model.UnCloseDoorEntry;
import com.dinsafer.model.UserNetworkEvent;
import com.dinsafer.model.WebSocketEvent;
import com.dinsafer.model.event.NeedLogoutEvent;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.HttpHelper;
import com.dinsafer.ws.IWebSocketCallBack;
import com.dinsafer.ws.WebSocketManager;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.EOFException;
import java.util.HashMap;

import okhttp3.Response;
import okhttp3.WebSocket;


//import com.dinsafer.util.DDLog;

/**
 * Created by Rinfon on 16/8/23.
 */
public class WebSocketService extends Service implements IWebSocketCallBack {

    public static final String TAG = "WebSocketService";
    private WebSocketServiceBinder webSocketServiceBinder;

    private volatile int mRetryCount = 0;

    private HashMap<String, String> mHasHandleMessageId = new HashMap<String, String>();

    private boolean isConnect = false;

    private boolean isDisConnectByServer = false;


    //    最后一次关闭的原因，错误原因
    private String lastClose = "";
    private int SERVICE_ID = 798;

    private WebSocketManager mWebSocketManager;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        DDLog.d(TAG, "onBind: ");
        if (webSocketServiceBinder == null) {
            webSocketServiceBinder = new WebSocketServiceBinder();
        }
        return webSocketServiceBinder;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        DDLog.d(TAG, "onCreate: ");

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startMyOwnForeground(SERVICE_ID);
        }

        mWebSocketManager = new WebSocketManager(true, true,
                CommonDataUtil.getInstance().getWsIp());
        mWebSocketManager.addCallBack(this);
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {

        try {
            if (TextUtils.isEmpty(CommonDataUtil.getInstance().getUserToken())) {
                DDLog.writeOnlineLog("send ws text error,token is null");
                CommonDataUtil.getInstance().logUser();
            }
            String message = CommonDataUtil.getInstance().getUserToken()
                    + "&" +
                    DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getToken()
                    + "_" + System.currentTimeMillis() * 1000;
            webSocket.send(new String(DDSecretUtil.getSC(message)));
        } catch (Exception e) {
            DDLog.log(TAG, "Unable to send messages: " + e.getMessage());
        }
    }

    @Override
    public void onStart(Intent intent, int startId) {
        super.onStart(intent, startId);
//        Log.d(TAG, "onStart: ");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
//        Log.d(TAG, "onStartCommand: ");
        super.onStartCommand(intent, START_FLAG_REDELIVERY, startId);
        return START_NOT_STICKY;
    }

    boolean isNeedToShowOfficeFragment;

    public synchronized void connectWebSocket() {
//        if (client != null) {
//            isNeedToShowOfficeFragment = false;
//            client.dispatcher().cancelAll();
//            client.dispatcher().
//        }

        DDLog.logPoint("connectWebSocket DeviceInfoHelper.getInstance().getCurrentDeviceInfo() != null:" +
                (DeviceInfoHelper.getInstance().getCurrentDeviceInfo() != null));

        if (mWebSocketManager.getWebSocket() != null) {
            isNeedToShowOfficeFragment = false;
//            try {
//                mWebSocket.close(1000, null);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            这个函数有可能导致不断的重连
            mWebSocketManager.stop();
        } else {
            isNeedToShowOfficeFragment = true;
        }

        isConnect = false;
        if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo() != null
                && !TextUtils.isEmpty(DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getToken())) {
            startConnectWebSocket();
        }
    }


    private void startConnectWebSocket() {
        try {
            DDLog.logPoint("startConnectWebSocket");
            mWebSocketManager.start();
        } catch (Exception ex) {
            DDLog.logPoint("exception " + ex.getMessage());
        }

        // Trigger shutdown of the dispatcher's executor so this process can exit cleanly.
//        不能关闭，否者不能进行重连，要在关闭app的时候才关闭
//        client.dispatcher().executorService().shutdown();

    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        //        不能关闭，否者不能进行重连，要在关闭app的时候才关闭
//        writeExecutor.shutdown();
//        服务器关闭eofexception
        if (isDisConnectByServer) {
            isDisConnectByServer = false;
            return;
        }
//        有时候调用websocket close 不一定会去到onclosing，会回调到onFailure
        if (mWebSocketManager.isUserClose()) {
            isConnect = false;
            mWebSocketManager.setUserClose(true);
            return;
        }
        isConnect = false;
        lastClose = t.getMessage();
        DDLog.logPoint("onFailure:" + t.getMessage());
        String wifiSSID = DDSystemUtil.getWIFISSID(this);
        if (wifiSSID != null && wifiSSID.replace("\"", "").startsWith(APIKey.AP_NAME)) {
//           ap下不进行重连
        } else {
            if (isNeedToShowOfficeFragment || t instanceof EOFException) {
                DDLog.writeOnlineLog("ws 意外断开，准备离线，" + t.getMessage());
                toSendOffline();
            } else {
                isNeedToShowOfficeFragment = true;
            }
        }
    }

    @Override
    public void onMessage(String messageStr) {
        DDLog.log(TAG, "MESSAGE: " + messageStr);
        if ("1".equals(messageStr)) {
            isConnect = true;
            mRetryCount = 0;
            EventBus.getDefault().post(new WebSocketEvent(WebSocketEvent.CONNET_SUCCESS));
        } else if ("-1".equals(messageStr)) {
            isDisConnectByServer = true;
            DDLog.writeOnlineLog("ws 接收-1，直接退出登录");
            EventBus.getDefault().post(new NeedLogoutEvent());
        } else if ("-2".equals(messageStr)) {
            isConnect = false;
            DDLog.writeOnlineLog("ws 接收-2，准备离线");
            toSendOffline();
        } else {
            try {
                JSONObject jsonObject = new JSONObject(messageStr);
                if (LocalKey.EVENT_REVICE.equals(jsonObject.getString("Action"))) {

                    String result = jsonObject.getString("Result");
                    result = DDSecretUtil.getReverSC(result);
                    DDLog.log(TAG, "ack: " + result);
                    EventlistBean deviceCmdEntry = JSON.parseObject(result, EventlistBean.class);
                    if (isCMDArmDisarmHomeArm(DDJSONUtil.getString(jsonObject, "Cmd"))) {
                        if (!mHasHandleMessageId.containsKey(deviceCmdEntry.getMessageid())) {
                            doRevice(deviceCmdEntry);
                            mHasHandleMessageId.put(deviceCmdEntry.getMessageid(), deviceCmdEntry.getCmdType());

                            String result1 = null;
                            if (!TextUtils.isEmpty(jsonObject.getString("Result"))) {

                                result1 = DDSecretUtil.getReverSC(jsonObject.getString("Result"));
                                if (HttpHelper.checkIsJsonObject(result1)) {
                                    JSONObject resultJson = new JSONObject(result1);
                                    String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(resultJson);
                                    if (TextUtils.isEmpty(isStringValue)) {
                                        result1 = resultJson.toString();
                                    } else {
                                        result1 = isStringValue;
                                    }
                                }

                            }

                            JSONObject resultJson = new JSONObject(result1);

                            if (TextUtils.isEmpty(DDJSONUtil.getString(resultJson, "plugins"))) {
                                createResultEvent(jsonObject, deviceCmdEntry.getTime());
                            }

                        } else {
                            EventBus.getDefault().post(new EventListDataFixTime(deviceCmdEntry));
                        }
                    } else {
                        doRevice(deviceCmdEntry);
                    }
                } else if (LocalKey.EVENT_RESULT.equals(jsonObject.getString("Action"))) {
                    String type = jsonObject.getString("Cmd");
                    DeviceResultEvent event = new DeviceResultEvent(type, jsonObject.getInt("Status")
                            , jsonObject.getString("MessageId"));
                    if (isCMDArmDisarmHomeArm(type)) {
                        String result = null;
                        if (!TextUtils.isEmpty(jsonObject.getString("Result"))) {

                            result = DDSecretUtil.getReverSC(jsonObject.getString("Result"));
                            DDLog.i(TAG, "ws:decode result:" + result);
                            if (HttpHelper.checkIsJsonObject(result)) {
                                JSONObject resultJson = new JSONObject(result);
                                String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(type, resultJson);
                                if (TextUtils.isEmpty(isStringValue)) {
                                    result = resultJson.toString();
                                } else {
                                    result = isStringValue;
                                }
                            }

                        }

                        JSONObject resultJson = new JSONObject(result);
                        if (LocalKey.ARM_KEY.equals(type) || LocalKey.HOMEARM_KEY.equals(type)) {
                            handlerShowToast(type, resultJson);
                        }
                        if (!mHasHandleMessageId.containsKey(jsonObject.getString("MessageId"))) {
                            doResult(jsonObject, event);
                            mHasHandleMessageId.put(jsonObject.getString("MessageId"), type);
                        } else if (DDJSONUtil.getJSONarray(resultJson, "plugins") != null
                                && DDJSONUtil.getJSONarray(resultJson, "plugins").length() > 0) {

                            /**
                             * 上面的if是对messageid进行过滤。如果是自己去arm就不做处理，
                             * 但是，现在增加一个逻辑：当ready to arm开启时，即使是自己的arm也要处理。
                             * 而区别就在于，如果plugins有数据，即要显示ready to arm 弹窗，即需要处理
                             */

                            Gson gson = new Gson();
                            UnCloseDoorEntry.ResultBean unCloseDoorEntry = gson.fromJson(result, UnCloseDoorEntry.ResultBean.class);
                            if (unCloseDoorEntry.getPlugins().size() > 0) {
                                doResult(jsonObject, event);
                                mHasHandleMessageId.put(jsonObject.getString("MessageId"), type);
                            }
                        }
                    } else {
                        doResult(jsonObject, event);
                    }
                } else if (LocalKey.EVENT_PING.equals(jsonObject.getString("Action"))) {
                    String result = jsonObject.getString("Result");
                    result = DDSecretUtil.getReverSC(result);
                    JSONObject jsonObject1 = new JSONObject(result);
                    MultiDataEntry.ResultBean.PingBean pingBean = new MultiDataEntry.ResultBean.PingBean();
                    pingBean.setPingduration(jsonObject1.getLong("pingduration"));
                    pingBean.setPingtime(jsonObject1.getLong("pingtime"));
//                    if (CommonDataUtil.getInstance().getMultiDataEntry().getResult().getPing().size() >= 10) {
//                        CommonDataUtil.getInstance().getMultiDataEntry().getResult().getPing().remove(0);
//                    } else {
//                        CommonDataUtil.getInstance().getMultiDataEntry().getResult().getPing().add(pingBean);
//                    }
                    DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setIs_charge(jsonObject1.getBoolean("ischarge"));
                    DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setBattery_level(jsonObject1.getInt("batterylevel"));
                    DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setNetwork(jsonObject1.getInt("nettype"));
                    DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setIp(jsonObject1.getString("ipaddr"));
                } else if (LocalKey.SIM_ACTION.equals(jsonObject.getString("Action"))) {
                    String result = jsonObject.getString("Result");
                    result = DDSecretUtil.getReverSC(result);

                    try {
                        JSONObject resultJson = new JSONObject(result);
                        String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(resultJson);
                        if (!TextUtils.isEmpty(isStringValue)) {
                            int resultCode = Integer.valueOf(result);
                            EventBus.getDefault().post(new DeviceSimStatueEvent(resultCode));
                        }

                    } catch (Exception ex) {
                        int resultCode = Integer.valueOf(result);
                        EventBus.getDefault().post(new DeviceSimStatueEvent(resultCode));
                    }
                } else if (LocalKey.OFFLINE.equals(jsonObject.getString("Action"))) {
                    toCloseWs();
                    toReconect();
                } else if (LocalKey.CMD_ACK.equals(jsonObject.getString("Action"))) {

                    String type = jsonObject.getString("Cmd");
                    DeviceCmdAckEvent event = new DeviceCmdAckEvent(type, jsonObject.getInt("Status")
                            , jsonObject.getString("MessageId"));
                    if (TextUtils.isEmpty(jsonObject.getString("Result"))) {
                        event.setReslut("");
                    } else {
                        event.setReslut(DDSecretUtil.getReverSC(jsonObject.getString("Result")));
                    }
                    EventBus.getDefault().post(event);
                }
//                    取消ack
//                    toSendACKCmd(jsonObject.getString("MessageId"), jsonObject.getString("Action"));
            } catch (Exception e) {
                e.printStackTrace();
                DDLog.writeOnlineLog("收到未知ws信息，" +
                        "以前会弹网络错误弹窗，现在已注释,msg is：" + messageStr);
//                    EventBus.getDefault().post(new UserNetworkEvent());
            }
        }
    }

    private void handlerShowToast(String cmdType, JSONObject result) {
        if (DDJSONUtil.getJSONarray(result, "plugins") != null
                && DDJSONUtil.getJSONarray(result, "plugins").length() > 0
                && DDJSONUtil.getBoolean(result, "force")) {
            return;
        }
        JSONArray block = DDJSONUtil.getJSONarray(result, "block");
        if (block == null) {
            EventBus.getDefault().post(new ShowBlockToastEvent(cmdType));
            return;
        }
        String blockStr = "";
        for (int i = 0; i < block.length(); i++) {
            try {
                blockStr = blockStr + block.get(i) + " ";
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        EventBus.getDefault().post(new ShowBlockToastEvent(cmdType, blockStr));

    }

    public void doRevice(EventlistBean deviceCmdEntry) {
        DeviceEventListEvent deviceEventListEvent = new DeviceEventListEvent();
        deviceEventListEvent.setEntry(deviceCmdEntry);
        EventBus.getDefault().post(deviceEventListEvent);

    }

    public void doResult(JSONObject jsonObject, DeviceResultEvent event) throws JSONException {
        if (TextUtils.isEmpty(jsonObject.getString("Result"))) {
            event.setReslut("");
        } else {

            String result = DDSecretUtil.getReverSC(jsonObject.getString("Result"));
            DDLog.i(TAG, "ws:decode result:" + result);
            String type = jsonObject.getString("Cmd");
            if (HttpHelper.checkIsJsonObject(result)) {
                JSONObject resultJson = new JSONObject(result);
                String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(type, resultJson);
                if (TextUtils.isEmpty(isStringValue)) {
                    event.setReslut(resultJson.toString());
                } else {
                    event.setReslut(isStringValue);
                }
            } else {
                event.setReslut(result);
            }
        }

        EventBus.getDefault().post(event);
    }

    private void toSendACKCmd(String messageid, String cmdType) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("MessageId", messageid);
            jsonObject.put("Action", cmdType);
            if (DBUtil.Exists(DBKey.TOKEN))
                jsonObject.put("Token", DBUtil.Str(DBKey.TOKEN) + "");
            mWebSocketManager.send(jsonObject.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }


    private boolean isCMDArmDisarmHomeArm(String type) {
        return LocalKey.ARM_KEY.equals(type) || LocalKey.DISARM_KEY.equals(type)
                || LocalKey.HOMEARM_KEY.equals(type);
    }

    @Safer
    private void createResultEvent(JSONObject jsonObject, long gmtime) throws JSONException {
        String type = jsonObject.getString("Cmd");
        DeviceResultEvent event = new DeviceResultEvent(type, jsonObject.getInt("Status")
                , jsonObject.getString("MessageId"));
        JSONObject json = new JSONObject();
        json.put("gmtime", gmtime);
        event.setReslut(json.toString());
        EventBus.getDefault().post(event);
    }

    private void toReconect() {
        DDLog.logPoint("toReconect ");
        EventBus.getDefault().post(new OfflineEvent());
    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {
        DDLog.d(TAG, "onClose: ");
        isConnect = false;
        lastClose = reason;
        DDLog.logPoint("onClose:" + code);
//        if (isNeedToShowOfficeFragment) {
//            toSendOffline();
//        } else {
//            isNeedToShowOfficeFragment = true;
//        }
//        toStopSendPing();

    }

    private synchronized void toSendOffline() {
        mRetryCount++;
        DDLog.logPoint("mRetryCount" + mRetryCount);
        if (mRetryCount > 3) {
            mRetryCount = 0;
//            判断用户网络
//            if (DDSystemUtil.isNetworkAvailable(getApplicationContext())) {
//                EventBus.getDefault().post(new WebSocketEvent(WebSocketEvent.CONNET_CLOSE));
//            } else {
////                用户网络问题
//                EventBus.getDefault().post(new UserNetworkEvent());
//            }
            DDLog.writeOnlineLog("要离线了，3次ws重试失败");
            CommonDataUtil.getInstance().logUser();
            EventBus.getDefault().post(new UserNetworkEvent());
        } else {
//            connectWebSocket();
            CommonDataUtil.getInstance().getAllData();
        }

    }

    public boolean isConnect() {
        return isConnect;
    }

    public String getCloseReason() {
        return lastClose;
    }

    public void toCloseWs() {
        DDLog.logPoint("toCloseWs");
        mWebSocketManager.close();
        if (mWebSocketManager.getWebSocket() != null) {
            isNeedToShowOfficeFragment = false;
        }
    }


    private int reconnectCount = 0;


    @Override
    public void onDestroy() {
        super.onDestroy();
        DDLog.d(TAG, "onDestory()");
        DDLog.logPoint("onDestroy ");
        mWebSocketManager.removeCallBack(this);
        mWebSocketManager.close();
        mWebSocketManager.release();
    }

    public class WebSocketServiceBinder extends Binder {

        public WebSocketService getService() {
            return WebSocketService.this;
        }
    }

    @TargetApi(Build.VERSION_CODES.O)
    private void startMyOwnForeground(int id) {
        String NOTIFICATION_CHANNEL_ID = getResources().getString(R.string.app_name);
        String channelName = "My Background Service";
        NotificationChannel chan = new NotificationChannel(NOTIFICATION_CHANNEL_ID, channelName, NotificationManager.IMPORTANCE_NONE);
        chan.setLightColor(Color.BLUE);
        chan.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        assert manager != null;
        manager.createNotificationChannel(chan);

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID);
        Notification notification = notificationBuilder.setOngoing(true)
                .setSmallIcon(R.mipmap.icon_notification_tran_bg)
                .setContentTitle("")
                .setPriority(NotificationManager.IMPORTANCE_MIN)
                .setCategory(Notification.CATEGORY_SERVICE)
                .build();
        // if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        //     startForeground(id, notification, FOREGROUND_SERVICE_TYPE_DATA_SYNC);
        // } else {
        //     startForeground(id, notification);
        // }
    }
}
