package com.dinsafer.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.dinsafer.model.event.NetworkChangeEvent;
import com.dinsafer.util.NetworkCheckUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * 网络连接状态改变监听
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/7/14 5:20 PM
 */
public class NetworkChangeReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        EventBus.getDefault().post(new NetworkChangeEvent(NetworkCheckUtil.isNetworkAvailable()));
    }
}
