package com.dinsafer.receiver;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsaferpush.Const;
import com.dinsafer.dinsaferpush.PushChannel;
import com.dinsafer.dinsaferpush.core.AliasCallback;
import com.dinsafer.dinsaferpush.core.DLog;
import com.dinsafer.dinsaferpush.core.DinsaferPushManager;
import com.dinsafer.dinsaferpush.entity.DinsaferPushCommand;
import com.dinsafer.dinsaferpush.entity.DinsaferPushMessage;
import com.dinsafer.dinsaferpush.receiver.BaseDinsaferPushReceiver;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.module.spash.SpashActivity;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.push.PushUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDSystemUtil;
import com.nostra13.universalimageloader.core.ImageLoader;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.UUID;

public class MyBaseDinsaferPushReveiver extends BaseDinsaferPushReceiver {
    private String TAG = getClass().getSimpleName();
    public static final String DEVICEID = "deviceid";
    public static final String CMDTYPE = "cmdtype";
    public static final String SOUND = "sound";
    public static final String DEVICENAME = "devicename";
    public static final String CODE = "code";
    public static final String IMG = "img";
    public static final String PLUGIN_NAME = "pluginname";
    public static final String PLUGINID = "pluginid";
    public static final String CATEGORY = "category";
    public static final String SUBCATEGORY = "subcategory";
    public static final String ALERT = "cn.jpush.android.ALERT";
    public static final String CMD = "cmd";
    public static final String HOME_ID = "home_id";
    public static final String LEVEL = "level";
    public static final String MESSAGE = "message";
    public static final String TITLE = "title";
    public static final String IPC_NAME = "ipc_name";
    public static final String PID = "pid";
    public static final String IPC_PID = "ipc_pid";
    public static final String PROVIDER = "provider";
    public static final String EVENT_ID = "event_id";
    public static final String TRIGGERING_TIME = "triggering_time";
    public static final String ID = "id";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String HOME_NAME = "home_name";
    public static final String BMT_NAME = "bmt_name";
    public static final String MODEL = "model";
    public static final String URL = "url";

    public static final int NOTIFICATION_CONTENT_MAX_LENGTH = 62;
    private static int messageNotificationID = 1000;


    /**
     * 第三方推送通道注册Token回调
     *
     * @param context
     * @param command
     * @param token
     */
    @Override
    public void onToken(Context context, DinsaferPushCommand command, String token) {
        Log.d(Const.TAG, "MyBaseDinsaferPushReveiver --> onToken: token " + token);
        if (!TextUtils.isEmpty(token)) {
            if (DinSDK.getUserInstance().getUser() != null
                    && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUser_id())) {
                DinsaferPushManager.setAlias(DinSDK.getUserInstance().getUser().getUser_id(), new AliasCallback() {
                    @Override
                    public void onSuccess() {
                        DLog.e(Const.TAG, "setAlias --> success");
                        CommonDataUtil.getInstance().setPushAliasUserId(DinSDK.getUserInstance().getUser().getUser_id());
                    }

                    @Override
                    public void onFail(String message) {
                        DLog.e(Const.TAG, "setAlias --> onFail: " + message);
                    }
                });

            }
        }
    }

    /**
     * 动作回调
     *
     * @param context
     * @param command
     */
    @Override
    public void onCommandReceive(Context context, DinsaferPushCommand command) {
        Log.d(Const.TAG, "MyBaseDinsaferPushReveiver --> onCommandReceive: " + command.toString());
    }

    /**
     * 接收到通知
     *
     * @param context
     * @param message
     */
    @Override
    public void onNotificationReceive(Context context, DinsaferPushMessage message) {
        DLog.v(Const.TAG, "MyBaseDinsaferPushReveiver --> onNotificationReceive: " + message.toString());
        if (!CommonDataUtil.getInstance().checkHasUser()) {
            DLog.e(Const.TAG, "onNotificationReceive: 用户未登录,不显示推送通知");
            return;
        }
        if (PushChannel.XIAOMI.equals(message.getPushChannel())) {
            if (message.getExtra() != null) {
                String contentStr = (String) message.getExtra().get("content");
                try {
                    JSONObject jsonObject = new JSONObject(contentStr);
                    //应用在后台有sdk复制弹出通知
//                    showNotification(context, message.getTitle(), message.getDescription(), jsonObject);
                    if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, CMDTYPE))) {
                        PushUtil.handleDeviceInfo(DDJSONUtil.getString(jsonObject, CMDTYPE), DDJSONUtil.getInt(jsonObject, CODE), DDJSONUtil.getString(jsonObject, DEVICEID));
                    } else if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, CMD))) {
                        Bundle args = new Bundle();
                        args.putString("message", DDJSONUtil.getString(jsonObject, "description"));
                        args.putInt(LEVEL, DDJSONUtil.getInt(jsonObject, LEVEL));
                        PushUtil.handleHomeInfo(DDJSONUtil.getString(jsonObject, CMD), DDJSONUtil.getString(jsonObject, HOME_ID), args);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    /**
     * 接收到消息
     *
     * @param context
     * @param message
     */
    @Override
    public void onMessageReceive(Context context, DinsaferPushMessage message) {
        DLog.d(Const.TAG, "MyBaseDinsaferPushReveiver --> onMessageReceive: " + message.toString());
        if (!CommonDataUtil.getInstance().checkHasUser()) {
            DLog.e(Const.TAG, "onMessageReceive: 用户未登录,不显示推送通知");
            return;
        }
        if (PushChannel.FCM.equals(message.getPushChannel())) {
            //FCM消息
            JSONObject jsonObject = null;
            if (message.getExtra() != null) {
                jsonObject = new JSONObject(message.getExtra());
            }
            showNotification(context, message.getTitle(), message.getDescription(), jsonObject);
            if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, CMDTYPE))) {
                PushUtil.handleDeviceInfo(DDJSONUtil.getString(jsonObject, CMDTYPE), DDJSONUtil.getInt(jsonObject, CODE), DDJSONUtil.getString(jsonObject, DEVICEID));
            } else if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, CMD))) {
                Bundle args = new Bundle();
                args.putString("message", DDJSONUtil.getString(jsonObject, "description"));
                args.putInt(LEVEL, DDJSONUtil.getInt(jsonObject, LEVEL));
                PushUtil.handleHomeInfo(DDJSONUtil.getString(jsonObject, CMD), DDJSONUtil.getString(jsonObject, HOME_ID), args);
            }
        }
    }

    /**
     * 通知点击
     *
     * @param context
     * @param message
     */
    @Override
    public void onNotificationClick(Context context, DinsaferPushMessage message) {
        DLog.d(Const.TAG, "MyBaseDinsaferPushReveiver --> onNotificationClick: " + message.toString());

        //应用在后台时的小米通知由系统负责显示，点击通知回调onNotificationMessageClicked()
        if (PushChannel.XIAOMI.equals(message.getPushChannel())) {
            if (message.getExtra() != null) {
                String contentStr = (String) message.getExtra().get("content");
                try {
                    JSONObject jsonObject = new JSONObject(contentStr);
                    PushUtil.handleDeviceInfo(DDJSONUtil.getString(jsonObject, CMDTYPE), DDJSONUtil.getInt(jsonObject, CODE), DDJSONUtil.getString(jsonObject, DEVICEID));
                    Intent intent = getToSpashActivityIntent(context, jsonObject);
                    context.startActivity(intent);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    public void showNotification(Context context, String title, String content, JSONObject jsonObject) {
//        String title = DDJSONUtil.getString(jsonObject, DEVICENAME);
//        String content = DDJSONUtil.getString(jsonObject, "description");
        String soundFile = DDJSONUtil.getString(jsonObject, SOUND);
        String imageurl = DDJSONUtil.getString(jsonObject, IMG);
        String CHANNEL_ID = TextUtils.isEmpty(soundFile) ? context.getResources().getString(R.string.app_name) : soundFile;
        int soundId = context.getResources().getIdentifier(soundFile, "raw", context.getPackageName());
        Uri path = Uri.parse("android.resource://" + context.getPackageName() + "/" + soundId);
        content = content.length() > NOTIFICATION_CONTENT_MAX_LENGTH ? (content.substring(0, NOTIFICATION_CONTENT_MAX_LENGTH) + "...") : content;

        NotificationManager messageNotificationManager = (NotificationManager) context.getApplicationContext().getSystemService(context.NOTIFICATION_SERVICE);
        messageNotificationManager.cancelAll();

        Notification messageNotification;

        try {
            if (TextUtils.isEmpty(imageurl)) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    @SuppressLint("WrongConstant")
                    NotificationChannel channel = new NotificationChannel(PushUtil.getNotificationChannelId(CHANNEL_ID),
                            CHANNEL_ID,
                            NotificationManager.IMPORTANCE_HIGH);
                    channel.enableVibration(true);
                    channel.enableLights(true);
                    channel.setSound(path,
                            Notification.AUDIO_ATTRIBUTES_DEFAULT);

                    messageNotificationManager.createNotificationChannel(channel);
                    messageNotification = new Notification.Builder(context, PushUtil.getNotificationChannelId(CHANNEL_ID))
                            .setAutoCancel(true)
                            .setContentTitle(title)
                            .setContentText(content)
                            .setStyle(new Notification.BigTextStyle().bigText(content))
                            .setDefaults(Notification.DEFAULT_VIBRATE)
                            .setContentIntent(getNotificationPendingIntent(context, jsonObject))
                            .setSmallIcon(R.mipmap.icon_notification_tran_bg)
                            .setColorized(!DDSystemUtil.BRAND_SAMSUN.equals(DDSystemUtil.getDeviceBrand()))
                            .setColor(ContextCompat.getColor(context, R.color.notification_icon_bg_color))
                            .setWhen(System.currentTimeMillis())
                            .setChannelId(PushUtil.getNotificationChannelId(CHANNEL_ID))
                            .build();
                } else {
                    Notification.Builder builder = new Notification.Builder(context)
                            .setAutoCancel(true)
                            .setContentTitle(title)
                            .setContentText(content)
                            .setStyle(new Notification.BigTextStyle().bigText(content))
                            .setDefaults(Notification.DEFAULT_VIBRATE)
                            .setContentIntent(getNotificationPendingIntent(context, jsonObject))
                            .setWhen(System.currentTimeMillis())
                            .setSound(path);

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        builder.setSmallIcon(R.mipmap.icon_notification_tran_bg);
                    } else {
                        builder.setSmallIcon(R.mipmap.ic_launcher);
                    }
                    messageNotification = builder.build();
                }

                messageNotification.flags = Notification.FLAG_AUTO_CANCEL;
                //发布消息
                messageNotificationManager.notify(messageNotificationID, messageNotification);
            } else {
                String url = DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.DOOR_BELL_SERVER_IP + imageurl);
                Notification.BigPictureStyle style = new Notification.BigPictureStyle();
                style.setBigContentTitle(title);
                style.setSummaryText(content);

                Notification.Builder builder = new Notification.Builder(context)
                        .setAutoCancel(true)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setStyle(new Notification.BigTextStyle().bigText(content))
                        .setDefaults(Notification.DEFAULT_VIBRATE)
                        .setContentIntent(getNotificationPendingIntent(context, jsonObject))
                        .setWhen(System.currentTimeMillis())
                        .setSound(path);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    builder.setSmallIcon(R.mipmap.icon_notification_tran_bg);
                } else {
                    builder.setSmallIcon(R.mipmap.ic_launcher);
                }
                new sendNotification(DinSaferApplication.getAppContext(),
                        builder, url,
                        style).execute();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Intent getToSpashActivityIntent(Context context, JSONObject jsonObject) {
        Intent messageIntent = new Intent(context, SpashActivity.class);
        messageIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        messageIntent.putExtra(DEVICEID, DDJSONUtil.getString(jsonObject, DEVICEID));
        messageIntent.putExtra(CMDTYPE, DDJSONUtil.getString(jsonObject, CMDTYPE));
        messageIntent.putExtra(SOUND, DDJSONUtil.getString(jsonObject, SOUND));
        messageIntent.putExtra(DEVICENAME, DDJSONUtil.getString(jsonObject, DEVICENAME));
        messageIntent.putExtra(CODE, DDJSONUtil.getInt(jsonObject, CODE));
        messageIntent.putExtra(IMG, DDJSONUtil.getString(jsonObject, IMG));
        messageIntent.putExtra(ALERT, DDJSONUtil.getString(jsonObject, "description"));
        messageIntent.putExtra(PLUGIN_NAME, DDJSONUtil.getString(jsonObject, PLUGIN_NAME));
        messageIntent.putExtra(PLUGINID, DDJSONUtil.getString(jsonObject, PLUGINID));
        messageIntent.putExtra(CATEGORY, DDJSONUtil.getString(jsonObject, CATEGORY));
        messageIntent.putExtra(SUBCATEGORY, DDJSONUtil.getString(jsonObject, SUBCATEGORY));

        //IPC_MOTION_DETECTED push
        messageIntent.putExtra("id", DDJSONUtil.getString(jsonObject, "id"));
        messageIntent.putExtra("home_id", DDJSONUtil.getString(jsonObject, "home_id"));
        messageIntent.putExtra("ipcname", DDJSONUtil.getString(jsonObject, "ipcname"));
        messageIntent.putExtra("title", DDJSONUtil.getString(jsonObject, "title"));
        messageIntent.putExtra(HOME_NAME, DDJSONUtil.getString(jsonObject, HOME_NAME));
        messageIntent.putExtra(BMT_NAME, DDJSONUtil.getString(jsonObject, BMT_NAME));
        messageIntent.putExtra("message", DDJSONUtil.getString(jsonObject, "description"));
        messageIntent.putExtra("ipc_pid", DDJSONUtil.getString(jsonObject, "ipc_pid"));
        messageIntent.putExtra("provider", DDJSONUtil.getString(jsonObject, "provider"));
        messageIntent.putExtra("cover", DDJSONUtil.getString(jsonObject, "cover"));
        messageIntent.putExtra("gmtime", DDJSONUtil.getLong(jsonObject, "gmtime"));
        messageIntent.putExtra(EVENT_ID, DDJSONUtil.getString(jsonObject, EVENT_ID));
        messageIntent.putExtra(TRIGGERING_TIME, DDJSONUtil.getString(jsonObject, TRIGGERING_TIME));

        messageIntent.putExtra(MODEL, DDJSONUtil.getString(jsonObject, MODEL));

        // 权限改变或被移出家庭
        String homeId = DDJSONUtil.getString(jsonObject, "homeid");
        if (TextUtils.isEmpty(homeId)) {
            homeId = DDJSONUtil.getString(jsonObject, "home_id");
        }
        messageIntent.putExtra(CMD, DDJSONUtil.getString(jsonObject, CMD));
        messageIntent.putExtra(HOME_ID, homeId);
        messageIntent.putExtra(LEVEL, DDJSONUtil.getInt(jsonObject, LEVEL));

        // 活动推送
        messageIntent.putExtra(ACTIVITY_ID, DDJSONUtil.getString(jsonObject, ACTIVITY_ID));

        // 超链接
        messageIntent.putExtra(URL, DDJSONUtil.getString(jsonObject, URL));
        return messageIntent;
    }

    private PendingIntent getNotificationPendingIntent(Context context, JSONObject jsonObject) {
        return PendingIntent.getActivity(context, UUID.randomUUID().hashCode(), getToSpashActivityIntent(context, jsonObject), PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT);
    }


    class sendNotification extends AsyncTask<String, Void, Bitmap> {

        Context ctx;
        Notification.Builder mBuild;
        String imageUrl;
        Notification.BigPictureStyle bigPictureStyle;

        public sendNotification(Context context, Notification.Builder build, String imageurl, Notification.BigPictureStyle style) {
            super();
            this.ctx = context;
            this.mBuild = build;
            this.imageUrl = imageurl;
            this.bigPictureStyle = style;
        }

        @Override
        protected Bitmap doInBackground(String... params) {
            return ImageLoader.getInstance().loadImageSync(imageUrl);
        }

        @Override
        protected void onPostExecute(Bitmap result) {

            super.onPostExecute(result);
            try {
                NotificationManager notificationManager = (NotificationManager) ctx
                        .getSystemService(Context.NOTIFICATION_SERVICE);
                this.bigPictureStyle.bigPicture(result);
                this.mBuild.setStyle(bigPictureStyle);
                Notification messageNotification = this.mBuild
                        .build();
                messageNotification.flags = Notification.FLAG_AUTO_CANCEL;
                //发布消息
                notificationManager.notify(1000, messageNotification);


            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
