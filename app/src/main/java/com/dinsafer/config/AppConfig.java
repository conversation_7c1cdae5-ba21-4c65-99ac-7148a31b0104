package com.dinsafer.config;

import android.text.TextUtils;

import com.dinsafer.dinsdk.DinConst;

/**
 * 功能或配件配置开关
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/5/19 11:44 上午
 */
public class AppConfig {
    /**
     * 功能配置
     */
    public static class Functions {
        // 是否支持SmartWidget
        public static final boolean SUPPORT_SMART_WIDGET = false;

        // 是否支持桌面小工具-需要手动修改AndroidManifest.xml文件
        public static final boolean SUPPORT_WIDGET = true;

        // 是否支持触发配对添加配件
        public static final boolean SUPPORT_TRIGGER_ADD_PLUGIN = false;

        // 是否支持CareMode
        public static final boolean SUPPORT_CARE_MODE = false;

        // 是否支持心赖-云存储
        public static final boolean SUPPORT_CLOUD_SERVICE_HEARTLAI = false;

        // 是否支持DSCAM-v005-云存储
        public static final boolean SUPPORT_CLOUD_SERVICE_DSCAM_V005 = false;

        // 是否支持DSCAM-v006-云存储
        public static final boolean SUPPORT_CLOUD_SERVICE_DSCAM_V006 = false;

        // 是否支持DSCAM-v015-云存储
        public static final boolean SUPPORT_CLOUD_SERVICE_DSCAM_V015 = false;

        // 是否支持DS_DOORBELL-云存储
        public static final boolean SUPPORT_CLOUD_SERVICE_DS_DOORBELL = Plugins.SUPPORT_DS_DOORBELL;

        // 是否支持-云存储-总开关
        public static final boolean SUPPORT_CLOUD_SERVICE = SUPPORT_CLOUD_SERVICE_HEARTLAI
                || SUPPORT_CLOUD_SERVICE_DSCAM_V005
                || SUPPORT_CLOUD_SERVICE_DSCAM_V006
                || SUPPORT_CLOUD_SERVICE_DS_DOORBELL;


        public static final boolean SUPPORT_TRAFFIC_PACKAGE_SERVICE_BMT5000 = true;
        public static final boolean SUPPORT_TRAFFIC_PACKAGE_SERVICE_BMT5001 = true;
        public static final boolean SUPPORT_TRAFFIC_PACKAGE_SERVICE_POWERCORE20 = true;
        public static final boolean SUPPORT_TRAFFIC_PACKAGE_SERVICE_POWERSTORE = true;
        public static final boolean SUPPORT_TRAFFIC_PACKAGE_SERVICE_POWERPULSE = true;

        // 是否支持TrafficPackage
        public static final boolean SUPPORT_TRAFFIC_PACKAGE_SERVICE = SUPPORT_TRAFFIC_PACKAGE_SERVICE_BMT5000
                || SUPPORT_TRAFFIC_PACKAGE_SERVICE_BMT5001 || SUPPORT_TRAFFIC_PACKAGE_SERVICE_POWERCORE20 ||
                SUPPORT_TRAFFIC_PACKAGE_SERVICE_POWERSTORE || SUPPORT_TRAFFIC_PACKAGE_SERVICE_POWERPULSE;

        // 是否支持首页PrimeServices入口
        public static final boolean SUPPORT_PRIME_SERVICES = SUPPORT_CLOUD_SERVICE || SUPPORT_TRAFFIC_PACKAGE_SERVICE;

        // 是否支持CMS
        public static final boolean SUPPORT_CMS = false;

        // 是否支持CMS设置用选择网络类型设置
        public static final boolean SUPPORT_SELECT_CMS_NETWORK_TYPE = false;

        // 是否支持AddMore
        public static final boolean SUPPORT_ADD_MORE = true;

        // addMore中只有一个TAB时是否需要隐藏TAB
        public static final boolean SUPPORT_ADD_MORE_HIDE_TAB_FOR_ONE_ITEM = true;

        // 是否支持4G网络设置
        public static final boolean SUPPORT_4G_NETWORK_SETTING = false;

        // 是否支持限制模式
        public static final boolean SUPPORT_RESTRICT_MODE = false;

        // 是否支持移动帧测
        public static final boolean SUPPORT_MOTION_DETECTED = SUPPORT_CLOUD_SERVICE;

        // 是否支持DSCAM-v005移动帧测
        public static final boolean SUPPORT_MOTION_DETECTED_DSCAM_V005 = false;

        // 是否支持DSCAM-v006移动帧测
        public static final boolean SUPPORT_MOTION_DETECTED_DSCAM_V006 = false;

        // 是否支持DS_DOORBELL移动帧测
        public static final boolean SUPPORT_MOTION_DETECTED_DS_DOORBELL = false;

        // 是否支持心赖移动帧测
        public static final boolean SUPPORT_MOTION_DETECTED_HEARTLAI = false;

        // 是否支持SmartButton可控制配件列表页配件为空时显示购买按钮
        public static final boolean SUPPORT_SMART_BUTTON_BUY = Plugins.SUPPORT_SMART_BUTTON || Plugins.SUPPORT_PIR_SENSOR || Plugins.SUPPORT_REMOTE_CONTROLLER;

        // 是否支持自研IPC直播录像
        public static final boolean SUPPORT_DSCAM_LIVE_RECORD = false;

        public static boolean isSupportCloudService(String provider) {
            if (TextUtils.isEmpty(provider)) {
                return false;
            }
            switch (provider) {
                case DinConst.TYPE_DSCAM:
                    return SUPPORT_CLOUD_SERVICE_DSCAM_V005;
                case DinConst.TYPE_DSCAM_VOO6:
                    return SUPPORT_CLOUD_SERVICE_DSCAM_V006;
                case DinConst.TYPE_DSCAM_VO15:
                    return SUPPORT_CLOUD_SERVICE_DSCAM_V015;
                default:
                    throw new IllegalArgumentException("unsupported " + provider);
            }
        }
        // 是否支持键盘多密码功能
        public static final boolean SUPPORT_KEYPAD_PWD = true;

        // 是否支持使用手机号的功能，如手机号登录、注册、忘记密码等
        public static final boolean SUPPORT_PHONE_FUNCTION = true;
    }

    /**
     * 配件支持配置
     */
    public static class Plugins {
        // 是否支持V005摄像头
        public static final boolean SUPPORT_DSCAM_V005 = false;

        // 是否支持V006摄像头
        public static final boolean SUPPORT_DSCAM_V006 = false;

        // 是否支持V015摄像头
        public static final boolean SUPPORT_DSCAM_V015 = false;

        // 是否支持DS_DOORBELL
        public static final boolean SUPPORT_DS_DOORBELL = false;

        // 是否支持心赖摄像头
        public static final boolean SUPPORT_HEARTLAI = false;

        // 是否支持BMT电源
        public static final boolean SUPPORT_BMT_HP5000 = true;

        public static final boolean SUPPORT_BMT_HP5001 = true;

        public static final boolean SUPPORT_BMT_POWERCORE20 = true;

        public static final boolean SUPPORT_BMT_POWERSTORE = true;

        public static final boolean SUPPORT_BMT_POWER_PULSE = true;

        public static final boolean SUPPORT_BMT_POWERCORE30 = true;

        // 是否支持摄像头
        public static final boolean SUPPORT_IPC = SUPPORT_DSCAM_V005
                || SUPPORT_DSCAM_V006
                || SUPPORT_DSCAM_V015
                || SUPPORT_HEARTLAI;

        // 是否支持SmartButton
        public static final boolean SUPPORT_SMART_BUTTON = false;

        //是否支持Signal Repeater Plug
        public static final boolean SUPPORT_SIGNAl_REPEATER_PLUG = true;

        //是否支持SmartPlug
        public static final boolean SUPPORT_SMART_PLUG = false;

        // 是否支持Door Window Sensor
        public static final boolean SUPPORT_DOOR_WINDOW_SENSOR = false;

        // 是否支持Vibration Sensor
        public static final boolean SUPPORT_VIBRATION_SENSOR = false;

        // 是否支持Rolling Door Window Sensor
        public static final boolean SUPPORT_ROLLING_DOOR_WINDOW_SENSOR = false;

        // 是否支持PIR Sensor
        public static final boolean SUPPORT_PIR_SENSOR = false;

        // 是否支持Wireless Pet-Immune Motion Sensor
        public static final boolean SUPPORT_WIRELESS_PET_IMMUNE_MOTION_SENSOR = false;

        // 是否支持Wireless Outdoor Siren
        public static final boolean SUPPORT_WIRELESS_OUTDOOR_SIREN = false;

        // 是否支持Wireless Indoor Siren
        public static final boolean SUPPORT_WIRELESS_INDOOR_SIREN = false;

        // 是否支持Wired Bridge
        public static final boolean SUPPORT_WIRED_BRIDGE = false;

        //是否支持遥控
        public static final boolean SUPPORT_REMOTE_CONTROLLER = false;

        // 是否支持主机
        public static final boolean SUPPORT_PANEL = false;
    }

    /**
     * 事件列表过滤条件
     */
    public static class EventListFilter {
        // 是否需要电站的过滤条件
        public static final boolean POWER_STATION = true;

        // 是否需要智能摄像头的过滤条件
        public static final boolean SMART_CAMERA = false;

        // 是否需要报警系统的过滤条件
        public static final boolean ALARM_SYSTEM = false;

        // 是否需要配件的过滤条件
        public static final boolean ACCESSORIES = false;

        // 是否需要家庭的过滤条件
        public static final boolean FAMILY = true;
    }
}
