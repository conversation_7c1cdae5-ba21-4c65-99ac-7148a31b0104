package com.dinsafer.module.pirSensitivity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.utils.DDLog;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPirSensitivitySettingGuideBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.pirSensitivity.event.PirSensitivityEnterSettingEvent;
import com.dinsafer.module.pirSensitivity.event.PirSettingModeTimeoutEvent;
import com.dinsafer.module.settting.ui.Builder;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Map;

/**
 * @describe：
 * @date：2023/9/8
 * @author: create by Sydnee
 */
public class PirSensitivitySettingGuideFragment extends MyBaseFragment<FragmentPirSensitivitySettingGuideBinding> implements IDeviceCallBack {

    private static final String DATA = "data";
    private static final String TAG = PirSensitivitySettingGuideFragment.class.getSimpleName();
    private Builder builder;
    private String pluginId;
    private Device mPluginDevice;

    public static PirSensitivitySettingGuideFragment newInstance(Builder builder) {
        PirSensitivitySettingGuideFragment fragment = new PirSensitivitySettingGuideFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelable(DATA, builder);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_pir_sensitivity_setting_guide;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        DDLog.d(TAG, "initView.");
        mBinding.commonTitleBar.commonBarTitle.setText("");
        mBinding.tvTip.setLocalText(getString(R.string.pir_sensitivity_setting_guide_tip));
        toNext(false);
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        builder = (Builder) getArguments().getParcelable(DATA);
        if (builder == null) {
            removeSelf();
        }
        findDevice();
    }

    private void findDevice() {
        pluginId = builder.getId();
        mPluginDevice = DinHome.getInstance().getDevice(pluginId);
        DDLog.d(TAG, "findDevice. mPluginDevice： " + mPluginDevice);
        if (null != mPluginDevice) {
            mPluginDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> {
            exitPirSettingMode();
        });

        mBinding.btnNext.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(PirSensitivitySettingFragment.newInstance(builder));
            removeSelf();
        });
    }

    @Override
    public boolean onBackPressed() {
        exitPirSettingMode();
        return true;
    }

    public void toNext(boolean visible) {
        mBinding.clNext.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        DDLog.i(TAG, "onCmdCallBack: deviceId: " + deviceId + "  cmd: " + cmd + "  map: " + map);
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        switch (cmd) {
            case PluginCmd.EXIT_PIR_SETTING_MODE:
                getMainActivity().cancelExitPirSettingMsg();
                removeSelf();
                break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this::onCmdCallBack);
        }

        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PirSensitivityEnterSettingEvent event) {
        DDLog.i(TAG, "PirSensitivityEnterSettingEvent. pluginId: " + event.getPluginId());
        if (TextUtils.isEmpty(event.getPluginId()) || !event.getPluginId().equals(mPluginDevice.getId())) {
            return;
        }
        getMainActivity().sendExitPirSettingMsg();
        toNext(true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PirSettingModeTimeoutEvent event) {
        DDLog.i(TAG, "PirSettingModeTimeoutEvent.");
        closeTimeOutLoadinFramgmentWithErrorAlert();
        removeSelf();
    }

    private void exitPirSettingMode() {
        DDLog.d(TAG, "exitPirSettingMode");
        if (null != mPluginDevice) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            mPluginDevice.submit(PanelParamsHelper.exitPirSettingMode());
        } else {
            showErrorToast();
            DDLog.e(TAG, "No plugin device.");
        }
    }
}
