package com.dinsafer.module.pirSensitivity;


import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPirSensitivitySettingBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.pirSensitivity.event.PirSensitivityEnterSettingEvent;
import com.dinsafer.module.pirSensitivity.event.PirSensitivityUpdateEvent;
import com.dinsafer.module.pirSensitivity.event.PirSettingModeTimeoutEvent;
import com.dinsafer.module.settting.ui.Builder;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.ScaleBar;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @describe：
 * @date：2023/9/8
 * @author: create by Sydnee
 */
public class PirSensitivitySettingFragment extends MyBaseFragment<FragmentPirSensitivitySettingBinding> implements IDeviceCallBack {

    private final static String TAG = PirSensitivitySettingFragment.class.getSimpleName();
    private static final String DATA = "data";

    private Builder builder;
    private String pluginId;
    private Device mPluginDevice;
    private int sensitivity;

    public static PirSensitivitySettingFragment newInstance(Builder builder) {
        PirSensitivitySettingFragment fragment = new PirSensitivitySettingFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelable(DATA, builder);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_pir_sensitivity_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.title.commonBarTitle.setLocalText(getString(R.string.modify_plugs_pir_sensitivity_status));

        Bitmap unSelect = ((BitmapDrawable) getResources().getDrawable(R.drawable.choose_nor)).getBitmap();
        Bitmap select = ((BitmapDrawable) getResources().getDrawable(R.drawable.choose_sel)).getBitmap();
        List<ScaleBar.ScaleBarItem> mData = new ArrayList<>();
        ScaleBar.ScaleBarItem scaleBarItem = new ScaleBar.ScaleBarItem();
        scaleBarItem.setName(Local.s("Low"));
        scaleBarItem.setIndex(0);
        scaleBarItem.setUnSelectImage(unSelect);
        scaleBarItem.setSelectImage(select);
        mData.add(scaleBarItem);

        ScaleBar.ScaleBarItem medium = new ScaleBar.ScaleBarItem();
        medium.setName(Local.s("Medium"));
        medium.setIndex(1);
        medium.setUnSelectImage(unSelect);
        medium.setSelectImage(select);
        mData.add(medium);

        ScaleBar.ScaleBarItem high = new ScaleBar.ScaleBarItem();
        high.setName(Local.s("High"));
        high.setIndex(3);
        high.setUnSelectImage(unSelect);
        high.setSelectImage(select);
        mData.add(high);

        mBinding.scaleBar.setData(mData);
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        builder = (Builder) getArguments().getParcelable(DATA);
        if (builder == null) {
            removeSelf();
        }
        findDevice();
    }

    private void findDevice() {
        pluginId = builder.getId();
        sensitivity = builder.getSensitivity();
        mPluginDevice = DinHome.getInstance().getDevice(pluginId);
        DDLog.d(TAG, "findDevice. mPluginDevice： " + mPluginDevice);
        if (null != mPluginDevice) {
            mPluginDevice.registerDeviceCallBack(this);
            refreshUI();
        }
    }

    private void refreshUI() {
        DDLog.d(TAG, "refreshUI. sensitivity： " + sensitivity);
        int meters = 0;
        if (PanelConstant.PirSensitivityType.PIR_SENSITIVITY_LOW == sensitivity) {
            mBinding.scaleBar.setSelectIndex(0);
            meters = 3;
        } else if (PanelConstant.PirSensitivityType.PIR_SENSITIVITY_MIDDLE == sensitivity) {
            mBinding.scaleBar.setSelectIndex(1);
            meters = 6;
        } else if (PanelConstant.PirSensitivityType.PIR_SENSITIVITY_HIGH == sensitivity) {
            mBinding.scaleBar.setSelectIndex(2);
            meters = 9;
        }

        mBinding.hint1.setText(Local.s(getString(R.string.pir_sensitivity_setting_context1))
                .replace("#pir_distance", meters + "m"));
        mBinding.hint2.setLocalText(getString(R.string.pir_sensitivity_setting_context2));
    }


    @Override
    public void initListener() {
        super.initListener();
        mBinding.title.commonBarBack.setOnClickListener(v -> {
            exitPirSettingMode();
        });

        mBinding.scaleBar.setOnIndexChangeListener(new ScaleBar.OnIndexChangeListener() {
            @Override
            public void onIndexChangeed(int index) {
                DDLog.d(TAG, "onIndexChangeed. index: " + index);
                if (index == 0) {
                    sensitivity = PanelConstant.PirSensitivityType.PIR_SENSITIVITY_LOW;
                } else if (index == 1) {
                    sensitivity = PanelConstant.PirSensitivityType.PIR_SENSITIVITY_MIDDLE;
                } else if (index == 2) {
                    sensitivity = PanelConstant.PirSensitivityType.PIR_SENSITIVITY_HIGH;
                }
                refreshUI();
            }
        });

        mBinding.save.setOnClickListener(v -> {
            toSetPirSensitivity();
        });
    }

    @Override
    public boolean onBackPressed() {
        exitPirSettingMode();
        return true;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        final boolean operateSelf = DeviceHelper.getBoolean(map, PanelDataKey.CmdResult.OWNER, false);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        switch (cmd) {
            case PluginCmd.SET_SENSITIVITY:
                if (!operateSelf) {
                    return;
                }
                String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    sensitivity = DDJSONUtil.getInt(jsonObject, PanelDataKey.PirSensitivityInfo.SENSITIVITY);
                    refreshUI();
                    showSuccess();
                    getMainActivity().sendExitPirSettingMsg();
                    EventBus.getDefault().post(new PirSensitivityUpdateEvent(deviceId, sensitivity));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case PluginCmd.EXIT_PIR_SETTING_MODE:
                getMainActivity().cancelExitPirSettingMsg();
                removeSelf();
                break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this::onCmdCallBack);
        }
        EventBus.getDefault().unregister(this);
    }

    private void toSetPirSensitivity() {
        DDLog.d(TAG, "toSetPirSensitivity");
        if (null != mPluginDevice) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            mPluginDevice.submit(PanelParamsHelper.setPirSensitivity(sensitivity));
        } else {
            showErrorToast();
            DDLog.e(TAG, "No plugin device.");
        }
    }

    private void exitPirSettingMode() {
        DDLog.d(TAG, "exitPirSettingMode");
        if (null != mPluginDevice) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            mPluginDevice.submit(PanelParamsHelper.exitPirSettingMode());
        } else {
            showErrorToast();
            DDLog.e(TAG, "No plugin device.");
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PirSettingModeTimeoutEvent event) {
        DDLog.i(TAG, "PirSettingModeTimeoutEvent.");
        closeTimeOutLoadinFramgmentWithErrorAlert();
        removeSelf();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PirSensitivityEnterSettingEvent event) {
        DDLog.i(TAG, "PirSensitivityEnterSettingEvent. pluginId: " + event.getPluginId());
        if (TextUtils.isEmpty(event.getPluginId()) || !event.getPluginId().equals(mPluginDevice.getId())) {
            return;
        }
        getMainActivity().sendExitPirSettingMsg();
    }
}
