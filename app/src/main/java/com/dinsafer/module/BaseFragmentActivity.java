package com.dinsafer.module;

import android.os.Bundle;
import android.util.Log;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.util.ActivityController;
import com.trello.rxlifecycle.components.support.RxFragmentActivity;

/**
 * Created by Rinfon on 16/6/14.
 */

/**
 * 基类BaseFragmentActivity
 * Created by rinfon on 16/3/22.
 */
public abstract class BaseFragmentActivity extends RxFragmentActivity {
    protected String TAG = getClass().getSimpleName();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.e("location", getClass().getName());
        ActivityController.getInstance().addActivity(this);
        if (!initVariables()) {
            finish();
            return;
        }
        initViews(savedInstanceState);
        loadData();
    }

    /**
     * 初始化变量
     */
    protected abstract boolean initVariables();

    /**
     * 初始化控件
     *
     * @param savedInstanceState
     */
    protected abstract void initViews(Bundle savedInstanceState);

    /**
     * 初始化数据
     */
    protected abstract void loadData();


    @Override
    public void onStart() {
        super.onStart();
        //EasyTracker.getInstance(this).activityStart(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        //EasyTracker.getInstance(this).activityStop(this);
    }

    @Override
    protected void onDestroy() {
        ActivityController.getInstance().removeActivity(this);
        super.onDestroy();
    }

    private AlertDialog errorDialog;

    public void showErrorToast() {
        runOnUiThread(() -> {
            if (errorDialog != null && errorDialog.isShowing()) {
                errorDialog.dismiss();
            }
            errorDialog = AlertDialog.createBuilder(this)
                    .setOk(getResources().getString(R.string.ok))
                    .setContent(getResources().getString(R.string.failed_try_again))
                    .setOKListener(() -> {
                        errorDialog.dismiss();
                    })
                    .preBuilder();
            errorDialog.show();
        });
    }
}