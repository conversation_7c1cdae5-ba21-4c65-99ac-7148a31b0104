package com.dinsafer.module;

import android.Manifest;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.permission.event.NeedPermissionEvent;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PermissionUtil;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;

/**
 * Live video page with permission request
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/3/10 15:29
 */
public abstract class BaseLiveVideoActivity extends BaseFragmentActivity {

    protected void showPermissionNotGrantTip(final String tip, final String[] permission) {
        AlertDialog.createBuilder(this)
                .setOk(getString(R.string.go_setting))
                .setOKListener(() -> {
                    toClose();
                    EventBus.getDefault().post(new NeedPermissionEvent(permission));
                })
                .setCancel(getString(R.string.cancel))
                .setContent(tip)
                .preBuilder()
                .show();
    }

    protected void requestAudioPermission() {
        final String[] permission = new String[]{Manifest.permission.RECORD_AUDIO};
        boolean denied = AndPermission.hasAlwaysDeniedPermission(this, permission);
        AndPermission.with(this)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    toClose();
                })
                .onDenied(permissions -> {
                    DDLog.e(PermissionDialogUtil.tag(), "Audio permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(this, permissions)) {
                        showPermissionNotGrantTip(getString(R.string.permission_tip_record_audio_not_grant), permission);
                    }
                })
                .start();
    }

    protected void requestReadImagesPermission() {
        final String[] permission = PermissionUtil.getStoragePermissions();
        requestStoragePermission(permission);
    }

    protected void requestReadVideoPermission() {
        final String[] permission = PermissionUtil.getStoragePermissions();
        requestStoragePermission(permission);
    }

    protected void requestStoragePermission(String[] permission) {
        if (null == permission) {
            return;
        }
        boolean denied = AndPermission.hasAlwaysDeniedPermission(this, permission);
        AndPermission.with(this)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    toClose();
                })
                .onDenied(permissions -> {
                    DDLog.e("", "Storage permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(this, permissions)) {
                        showPermissionNotGrantTip(getString(R.string.permission_tip_album_not_grant), permission);
                    }
                })
                .start();
    }

    protected abstract void toClose();
}
