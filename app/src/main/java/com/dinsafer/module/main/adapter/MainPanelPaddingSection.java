package com.dinsafer.module.main.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.dinsafer.dinnet.R;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/**
 * 首页panel底部的padding
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/1/5 6:33 PM
 */
public class MainPanelPaddingSection extends StatelessSection {
    public MainPanelPaddingSection() {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.main_section_panel_item_padding)
                .itemResourceId(R.layout.main_section_panel_item_padding)
                .build());
    }

    @Override
    public int getContentItemsTotal() {
        return 0;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return null;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {

    }
}
