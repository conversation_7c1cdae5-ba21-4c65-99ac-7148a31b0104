package com.dinsafer.module.main.helper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.panel.MainPanelFunctionItem;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.iap.ListBigBannerResponse;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.entry.PluginMapItem;
import com.dinsafer.module.main.model.BaseMainItemModel;
import com.dinsafer.module.main.model.MainAddMorePluginModel;
import com.dinsafer.module.main.model.MainBannerModel;
import com.dinsafer.module.main.model.MainBatteryModel;
import com.dinsafer.module.main.model.MainBmtEVModel;
import com.dinsafer.module.main.model.MainBmtEmergencyModel;
import com.dinsafer.module.main.model.MainBmtTodayUsageModel;
import com.dinsafer.module.main.model.MainBmtV2Model;
import com.dinsafer.module.main.model.MainFunctionModel;
import com.dinsafer.module.main.model.MainHostModel;
import com.dinsafer.module.main.model.MainIpcModel;
import com.dinsafer.module.main.model.MainMultiScreenPluginModel;
import com.dinsafer.module.main.model.MainPanelArmDelayRecorder;
import com.dinsafer.module.main.model.MainPluginModel;
import com.dinsafer.module.main.model.MainPowerPulseCurrentModel;
import com.dinsafer.module.main.model.MainPowerStoreCurrentModel;
import com.dinsafer.module.main.model.MainShortcutPluginModel;
import com.dinsafer.module.main.model.MainSmallHostModel;
import com.dinsafer.module.main.model.MainSmallIpcModel;
import com.dinsafer.module.main.model.OnWidgetItemListener;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.ui.device.MainDeviceStatusView;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.PluginWidgetStyleUtil;
import com.dinsafer.util.TestLog;
import com.dinsafer.util.UpgradeUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.apache.http.util.TextUtils;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class MainWidgetListProvider {

    private final static String TAG = MainWidgetListProvider.class.getSimpleName();
    // 因为多ipc组合的卡片没有对应的pluginId，由MULTI_SCREEN_ID代替
    public final static String MULTI_SCREEN_ID = "multi_screen_id";
    // FUNCTION_ID代替常用小工具的pluginId
    public final static String FUNCTION_ID = "function_id";
    public final static String BANNER_ID = "banner_id";
    public final static String UNDERLINE = "_";
    private static volatile MainWidgetListProvider instance;

    // 源数据
    private ArrayList<Device> mBmtSectionList = new ArrayList<>();
    private ArrayList<Device> mPowerStoreSectionList = new ArrayList<>();
    private ArrayList<Device> mPowerPulseSectionList = new ArrayList<>();
    private ArrayList<Device> mIpcSectionList = new ArrayList<>();
    private ArrayList<TuyaItemPlus> mShortcutList = new ArrayList<>();
    private ArrayList<TuyaItemPlus> mDoorSensorSectionList = new ArrayList<>();
    private ArrayList<MainPanelFunctionItem> mFunctionList = new ArrayList<>();
    private List<ListBigBannerResponse.BigBannerBean> mBannerData = new ArrayList<>();
    private PanelDevice mPanelDevice = null;

    // 所有源数据
    private volatile LinkedHashMap<String, PluginMapItem> allPluginMap = new LinkedHashMap<>();
    // 缓存配件列表状态信息
    private volatile ArrayList<MainWidgetBean> cacheLayoutList = new ArrayList<>();
    // 提供视图数据
    private volatile ArrayList<BaseMainItemModel<?>> bindModelList = new ArrayList<>();
    private final Map<String, MainPanelArmDelayRecorder> mPanelArmDelayRecorderMap = new HashMap<>();

    private OnWidgetItemListener onWidgetItemListener;
    private MainDeviceStatusView.OnDeviceStatusActionClickListener onDeviceStatusActionClickListener;

    private String mHomeId;
    private String mUserId;

    public void setOnWidgetItemListener(OnWidgetItemListener onWidgetItemListener) {
        this.onWidgetItemListener = onWidgetItemListener;
    }

    public void setOnDeviceStatusActionClickListener(MainDeviceStatusView.OnDeviceStatusActionClickListener onDeviceStatusActionClickListener) {
        this.onDeviceStatusActionClickListener = onDeviceStatusActionClickListener;
    }

    public ArrayList<MainWidgetBean> getCacheLayoutList() {
        return cacheLayoutList;
    }

    public MainWidgetListProvider() {

    }

    public static MainWidgetListProvider getInstance() {
        if (instance == null) {
            synchronized (MainWidgetListProvider.class) {
                if (instance == null) {
                    instance = new MainWidgetListProvider();
                }
            }
        }
        return instance;
    }

    /**
     * 更新列表布局信息
     */
    public void refreshCacheLayoutWithHomeId(String homeId) {
        DDLog.i(TAG, "refreshCacheLayoutWithHomeId. homeId： " + homeId);
        clearAll();
        mHomeId = homeId;
        if (DinSDK.getUserInstance().isLogin()) {
            mUserId = DinSDK.getUserInstance().getUser().getUid();
            DDLog.i(TAG, "refreshCacheLayoutWithHomeId. mUserId： " + mUserId);
            String key = DBKey.KEY_MAIN_PANEL_CACHE_LAYOUT + UNDERLINE + mUserId + UNDERLINE + mHomeId;
            if (DBUtil.Exists(key)) {
                String cacheStr = DBUtil.Str(key);
                TestLog.i(TAG, "cacheStr:  " + cacheStr);
                if (!TextUtils.isEmpty(cacheStr)) {
                    Gson gson = new Gson();
                    cacheLayoutList = gson.fromJson(cacheStr, new TypeToken<ArrayList<MainWidgetBean>>() {
                    }.getType());
                    int functionVersion = DBUtil.Num(key + UpgradeUtil.MAIN_FUNCTION_VERSION_STR);
                    if (functionVersion < 1) {
                        changeSectionFunctionPlace();
                        DBUtil.Put(key + UpgradeUtil.MAIN_FUNCTION_VERSION_STR, UpgradeUtil.MAIN_FUNCTION_VERSION);
                    }
                    final boolean updateTuya = checkRemoveTuyaAndSaveCache(false);
                    final boolean updateMultiScreenCardIds = upgradeMultiScreenCardId(false);
                    if (updateTuya || updateMultiScreenCardIds) {
                        saveCacheLayout();
                    }
                }
            }
        }

        updateBannerItem(null);
        DDLog.i(TAG, "refreshCacheLayoutWithHomeId. --->  cacheLayoutList: " + cacheLayoutList);
    }

    /**
     * 检查缓存中是否有涂鸦的配件，没有则不处理；有则删除并更新到本地缓存中
     */
    private boolean checkRemoveTuyaAndSaveCache(final boolean needSave) {
        if (null == cacheLayoutList || cacheLayoutList.size() == 0) {
            return false;
        }

        boolean updated = false;
        final ArrayList<MainWidgetBean> tuyaLayoutCacheList = new ArrayList<>();
        for (MainWidgetBean bean : cacheLayoutList) {
            if (MainPanelHelper.SECTION_TYPE_SHORTCUT == bean.getBindModelType()
                    && TextUtils.isEmpty(bean.getFatherId())) {
                tuyaLayoutCacheList.add(bean);
            }
        }

        if (tuyaLayoutCacheList.size() > 0) {
            DDLog.w(TAG, "Remove tuya cache Layout, size: " + tuyaLayoutCacheList.size());
            cacheLayoutList.removeAll(tuyaLayoutCacheList);
            if (needSave) {
                saveCacheLayout();
            }
            updated = true;
        }
        return updated;
    }

    /**
     * 更新缓存中多屏卡片的ID，
     * 更新ID后，多屏卡片将记录自己的多屏IPC的Id
     */
    private boolean upgradeMultiScreenCardId(final boolean needSave) {
        if (null == cacheLayoutList || 0 == cacheLayoutList.size()) {
            return false;
        }
        boolean hadUpdated = false;
        for (MainWidgetBean bean : cacheLayoutList) {
            if (MainPanelHelper.SECTION_TYPE_IPC == bean.getBindModelType()
                    && PluginWidgetStyleUtil.IPC_MORE.equals(bean.getLayoutType())
                    && MULTI_SCREEN_ID.equals(bean.getPluginId())) {
                bean.setPluginId(genDsCamMultiScreenId());
                hadUpdated = true;
            }
        }

        if (hadUpdated && needSave) {
            saveCacheLayout();
        }
        return hadUpdated;
    }

    private void changeSectionFunctionPlace() {

        if (cacheLayoutList.size() < 2) {
            return;
        }

        int functionIndex = 0;
        for (int j = 0; j < cacheLayoutList.size(); j++) {
            if (FUNCTION_ID.equals(cacheLayoutList.get(j).getPluginId())) {
                functionIndex = j;
                break;
            }
        }
        DDLog.d(TAG, "changeSectionFunctionPlace: functionIndex: " + functionIndex);
        if (functionIndex != (cacheLayoutList.size() - 1)) {
            MainWidgetBean fun = cacheLayoutList.get(functionIndex);
            cacheLayoutList.remove(functionIndex);
            cacheLayoutList.add(fun);
        }
    }

    private void clearAll() {
        resetAndCleanOperateArmDelayListener(CommonDataUtil.getInstance().getCurrentDeviceId());
        cacheLayoutList.clear();
        allPluginMap.clear();
        bindModelList.clear();
        mBannerData.clear();
        mFunctionList.clear();
        mBmtSectionList.clear();
        mPowerStoreSectionList.clear();
        mPowerPulseSectionList.clear();
        mDoorSensorSectionList.clear();
        mShortcutList.clear();
        mIpcSectionList.clear();
        mPanelDevice = null;
        mUserId = null;
        mHomeId = null;
    }

    /**
     * 判断此家庭是否缓存了配件列表信息
     *
     * @param homeId
     * @return
     */
    public boolean isCacheLayoutEmptyWithHome(String homeId) {
        DDLog.d(TAG, "isCacheLayoutEmptyWithHome. homeId : " + homeId);
        if (TextUtils.isEmpty(homeId)) {
            return false;
        }
        if (DBUtil.Exists(DBKey.KEY_MAIN_PANEL_CACHE_LAYOUT + UNDERLINE + mUserId + UNDERLINE + homeId)) {
            return TextUtils.isEmpty(DBUtil.Str(DBKey.KEY_MAIN_PANEL_CACHE_LAYOUT + UNDERLINE + mUserId + UNDERLINE + homeId));
        }
        return true;
    }

    public int curCacheLayoutListSize() {
        return cacheLayoutList.size();
    }

    /**
     * 增加widget
     *
     * @param bean
     */
    public void addCacheLayoutItem(MainWidgetBean bean) {
        if (null == bean || TextUtils.isEmpty(bean.getPluginId())) {
            DDLog.e(TAG, "addCacheLayoutItem. MainWidgetBean is null!");
            return;
        }

        DDLog.d(TAG, "addCacheLayoutItem. MainWidgetBean: " + bean);
        int index = 0;
        int sameIndex = -1;
        for (int i = 0; i < cacheLayoutList.size(); i++) {
            MainWidgetBean mainWidgetBean = cacheLayoutList.get(i);
            int bindModelType = mainWidgetBean.getBindModelType();
            if (MainPanelHelper.SECTION_TYPE_FUNCTION == bindModelType) {
                index = i;
            }
            if (cacheLayoutList.get(i).getLayoutType().equals(bean.getLayoutType())
                    && cacheLayoutList.get(i).getPluginId().equals(bean.getPluginId())) {
                sameIndex = i;
            }
        }

        // 不允许添加多个其他类型的卡片，但可以添加多个多屏卡片
        final boolean isMultiScreenItem = MainPanelHelper.SECTION_TYPE_IPC == bean.getBindModelType()
                && PluginWidgetStyleUtil.IPC_MORE.equals(bean.getLayoutType())
                && MULTI_SCREEN_ID.equals(bean.getPluginId());
        if (sameIndex > -1 && !isMultiScreenItem) {
            if (index > sameIndex) {
                index--;
            }
            cacheLayoutList.remove(sameIndex);
        }
        final boolean isGuest = HomeManager.getInstance().isGuest();
        if (isGuest) {
            cacheLayoutList.add(bean);
        } else {
            cacheLayoutList.add(index, bean);
        }
        saveCacheLayout();

        // 从黑名单中删除
        MainPanelHelper.getInstance().deletePluginFromBlackList(bean.getPluginId());
    }

    /**
     * 配件未被删除，移除对应的position的widget
     *
     * @param position
     * @param addBlackList 加入黑名单; 加入则为true;
     */
    public void removeCacheLayoutItem(int position, @NonNull final MainWidgetBean widgetBean, boolean addBlackList) {
        final int cacheIndex = cacheLayoutList.indexOf(widgetBean);
        final int targetPosition;
        if (cacheIndex != -1) {
            targetPosition = cacheIndex;
        } else {
            targetPosition = position;
        }

        DDLog.d(TAG, "removeCacheLayoutItem: position: " + position + ", cacheIndex:  " + cacheIndex + ", targetPosition: " + targetPosition);
        if (targetPosition < 0 || targetPosition >= cacheLayoutList.size()) {
            return;
        }

        if (addBlackList) {
            int count = 0;
            String pluginId = cacheLayoutList.get(targetPosition).getPluginId();
            for (MainWidgetBean mainPanelItemBean : cacheLayoutList) {
                if (mainPanelItemBean.getPluginId().equals(pluginId)) {
                    count++;
                }
            }
            DDLog.e(TAG, "removeCacheLayoutItem: pluginId: " + pluginId + " has " + count + " widgets.");
            if (count < 2) {
                allPluginMap.remove(pluginId);
                JSONArray newBlackList = MainPanelHelper.getInstance().getBlackList();
                newBlackList.put(pluginId);
                MainPanelHelper.getInstance().updateBlackList(newBlackList);
            }
        }

        cacheLayoutList.remove(targetPosition);
        saveCacheLayout();
    }

    /**
     * 配件已被删除，删除对应的pluginId的所有widget
     *
     * @param pluginId
     */
    public void deleteWidgetInCacheLayout(final String pluginId) {
        DDLog.d(TAG, "deletePluginInCacheLayout: pluginId: " + pluginId);
        if (TextUtils.isEmpty(pluginId)) {
            return;
        }

        if (!TextUtils.isEmpty(CommonDataUtil.getInstance().getCurrentPanelID())
                && pluginId.equals(CommonDataUtil.getInstance().getCurrentPanelID())) {
            // 是主机，删除主机，以及主机下对应的配件对应的所有widget
            Iterator<MainWidgetBean> it = cacheLayoutList.iterator();
            while (it.hasNext()) {
                MainWidgetBean widgetBean = it.next();
                if (pluginId.equals(widgetBean.getPluginId()) || pluginId.equals(widgetBean.getFatherId())) {
                    allPluginMap.remove(widgetBean.getPluginId());
                    // 从黑名单中删除
                    MainPanelHelper.getInstance().deletePluginFromBlackList(pluginId);
                    it.remove();
                }
            }
        } else {
            Iterator<MainWidgetBean> iterator = cacheLayoutList.iterator();
            while (iterator.hasNext()) {
                MainWidgetBean bean = iterator.next();
                final String layoutCardId = bean.getPluginId();
                if (isDsCamMultiScreenId(layoutCardId)) {
                    // 如果删除的是自研IPC，需要移除该IPC在多屏卡片中的记录
                    final List<String> multiScreenCardDsCamIds = DsCamUtils.getCurrentHomeLocalMultiScreenCardDsCamIds(layoutCardId);
                    if (multiScreenCardDsCamIds.contains(pluginId)) {
                        DDLog.d("MultiPlay", "delete on multiPlayCard: " + layoutCardId);
                        multiScreenCardDsCamIds.remove(pluginId);
                        final int size = multiScreenCardDsCamIds.size();
                        if (size > 0) {
                            DsCamUtils.saveCurrentHomeMultiScreenCardDsCamIds(layoutCardId, multiScreenCardDsCamIds);
                        } else {
                            DsCamUtils.deleteCurrentHomeMultiScreenCardDsCamIds(layoutCardId);
                            iterator.remove();
                        }
                    }
                } else if (pluginId.equals(layoutCardId)) {
                    iterator.remove();
                }
            }
            allPluginMap.remove(pluginId);
            MainPanelHelper.getInstance().deletePluginFromBlackList(pluginId);
        }
        DDLog.d(TAG, "deletePluginInCacheLayout: cacheLayoutList size: " + cacheLayoutList.size());
        saveCacheLayout();
    }

    /***
     * 获取首页显示的bindModel
     */
    public ArrayList<BaseMainItemModel<?>> getBindModelList(BaseFragment baseFragment) {
        updateCacheList();
        buildBindModelList(baseFragment);
        DDLog.d(TAG, "getBindModelList: cacheLayoutList: size: " + cacheLayoutList.size() + ", model size: " + bindModelList.size());
        return bindModelList;
    }

    /**
     * 主机
     *
     * @param panelDevice
     */
    public void updatePanelDeviceItem(PanelDevice panelDevice) {
        this.mPanelDevice = panelDevice;
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_PANEL_DEVICE);

        if (null != panelDevice && !TextUtils.isEmpty(panelDevice.getId())) {
            ensurePanelArmDelayRecorders(panelDevice.getId());
        } else {
            cleanPanelArmDelayRecorders();
        }
    }

    /**
     * IPC
     *
     * @param ipcSectionList
     */
    public void updateIPCList(ArrayList<Device> ipcSectionList) {
        if (ipcSectionList == null || ipcSectionList.size() <= 0) {
            return;
        }
        mIpcSectionList.clear();
        mIpcSectionList.addAll(ipcSectionList);
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_IPC);
    }

    /**
     * 快捷方式
     *
     * @param shortcutList
     */
    public void updateShortcutList(ArrayList<TuyaItemPlus> shortcutList) {
        if (shortcutList == null || shortcutList.size() <= 0) {
            return;
        }
        DDLog.i(TAG, "updateShortcutItems: shortcutList size:" + shortcutList.size());
        mShortcutList.clear();
        mShortcutList.addAll(shortcutList);
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_SHORTCUT);

    }

    /**
     * 门磁等配件
     */
    public void updateDoorSensorList(ArrayList<TuyaItemPlus> doorSensorSectionList) {
        if (doorSensorSectionList == null || doorSensorSectionList.size() <= 0) {
            return;
        }
        mDoorSensorSectionList.clear();
        DDLog.i(TAG, "updatePluginItems: doorSensorSectionList size:" + doorSensorSectionList.size());
        mDoorSensorSectionList.addAll(doorSensorSectionList);

        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_DOOR_SENSOR);
    }

    /**
     * BMT
     */
    public void updateBmtList(ArrayList<Device> bmtSectionList) {
        if (bmtSectionList == null || bmtSectionList.size() <= 0) {
            return;
        }
        DDLog.i(TAG, "updateBmtItems: updateBmtList size:" + bmtSectionList.size());
        mBmtSectionList.clear();
        mBmtSectionList.addAll(bmtSectionList);
        changePowerStationBlackList(mBmtSectionList);
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_BMT);
    }

    /**
     * BMT-PowerStore
     */
    public void updatePowerStoreList(ArrayList<Device> powerStoreSectionList) {
        if (powerStoreSectionList == null || powerStoreSectionList.size() <= 0) {
            return;
        }
        DDLog.i(TAG, "updateBmtItems: updateBmtList size:" + powerStoreSectionList.size());
        mPowerStoreSectionList.clear();
        mPowerStoreSectionList.addAll(powerStoreSectionList);
        changePowerStationBlackList(mPowerStoreSectionList);
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_POWER_STORE);
    }

    /**
     * BMT-PowerPulse
     */
    public void updatePowerPulseList(ArrayList<Device> powerPulseSectionList) {
        if (powerPulseSectionList == null || powerPulseSectionList.size() <= 0) {
            return;
        }
        DDLog.i(TAG, "updateBmtItems: updateBmtList size:" + powerPulseSectionList.size());
        mPowerPulseSectionList.clear();
        mPowerPulseSectionList.addAll(powerPulseSectionList);
        changePowerStationBlackList(mPowerPulseSectionList);
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_POWER_PULSE);
    }

    /**
     * 常用工具
     */
    public void updateFunctionItem() {
        mFunctionList.clear();
        MainPanelHelper.getInstance().initFunctionData(mFunctionList);
        DDLog.i(TAG, "updateFunctionItem: mFunctionList size:" + mFunctionList.size());
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_FUNCTION);
    }

    /**
     * banner
     */
    public void updateBannerItem(List<ListBigBannerResponse.BigBannerBean> data) {
        mBannerData.clear();
        if (null != data && data.size() > 0) {
            mBannerData.addAll(data);
        }
        DDLog.i(TAG, "updateBannerItem. ");
        updateAllPluginMap(MainPanelHelper.SECTION_TYPE_BANNER);
    }

    /**
     * 添加更多
     *
     * @param baseFragment
     * @return
     */
    public BaseMainItemModel<?> getAddMoreItem(BaseFragment baseFragment, MainAddMorePluginModel.OnItemClickListener onItemClickListener) {
        DDLog.i(TAG, "addAddMoreItem: ");
        MainAddMorePluginModel bindModel = new MainAddMorePluginModel(baseFragment, R.string.add_more, R.string.full_protection_for_your_home);
        bindModel.setItemClickListener(onItemClickListener);
        return bindModel;
    }

    private void updateAllPluginMap(int sectionType) {
        DDLog.d(TAG, "updateAllPluginMap:" + allPluginMap.size() + "  sectionType:" + sectionType);
        switch (sectionType) {
            case MainPanelHelper.SECTION_TYPE_BANNER:
                allPluginMap.remove(BANNER_ID);
                allPluginMap.put(BANNER_ID, new PluginMapItem(null, MainPanelHelper.SECTION_TYPE_BANNER));
                break;

            case MainPanelHelper.SECTION_TYPE_FUNCTION:
                allPluginMap.remove(FUNCTION_ID);
                allPluginMap.put(FUNCTION_ID, new PluginMapItem(null, MainPanelHelper.SECTION_TYPE_FUNCTION));
                break;

            case MainPanelHelper.SECTION_TYPE_BMT:
                for (Device device : mBmtSectionList) {
                    if (!MainPanelHelper.getInstance().checkIsInBlackList(device.getId() + UNDERLINE + device.getSubCategory())) {
                        allPluginMap.put(device.getId() + UNDERLINE + device.getSubCategory(), new PluginMapItem(device, MainPanelHelper.SECTION_TYPE_BMT));
                    } else {
                        allPluginMap.remove(device.getId() + UNDERLINE + device.getSubCategory());
                    }
                }
                break;

            case MainPanelHelper.SECTION_TYPE_POWER_STORE:
                for (Device device : mPowerStoreSectionList) {
                    if (!MainPanelHelper.getInstance().checkIsInBlackList(device.getId() + UNDERLINE + device.getSubCategory())) {
                        allPluginMap.put(device.getId() + UNDERLINE + device.getSubCategory(), new PluginMapItem(device, MainPanelHelper.SECTION_TYPE_POWER_STORE));
                    } else {
                        allPluginMap.remove(device.getId() + UNDERLINE + device.getSubCategory());
                    }
                }
                break;

            case MainPanelHelper.SECTION_TYPE_POWER_PULSE:
                for (Device device : mPowerPulseSectionList) {
                    if (!MainPanelHelper.getInstance().checkIsInBlackList(device.getId() + UNDERLINE + device.getSubCategory())) {
                        allPluginMap.put(device.getId() + UNDERLINE + device.getSubCategory(), new PluginMapItem(device, MainPanelHelper.SECTION_TYPE_POWER_PULSE));
                    } else {
                        allPluginMap.remove(device.getId() + UNDERLINE + device.getSubCategory());
                    }
                }
                break;

            case MainPanelHelper.SECTION_TYPE_IPC:
                for (Device device : mIpcSectionList) {
                    if (!MainPanelHelper.getInstance().checkIsInBlackList(device.getId())) {
                        allPluginMap.put(device.getId(), new PluginMapItem(device, MainPanelHelper.SECTION_TYPE_IPC));
                    } else {
                        allPluginMap.remove(device.getId());
                    }
                }
                break;

            case MainPanelHelper.SECTION_TYPE_PANEL_DEVICE:
                if (mPanelDevice != null) {
                    if (!MainPanelHelper.getInstance().checkIsInBlackList(mPanelDevice.getId())) {
                        allPluginMap.put(mPanelDevice.getId(), new PluginMapItem(mPanelDevice
                                , MainPanelHelper.SECTION_TYPE_PANEL_DEVICE));
                    } else {
                        allPluginMap.remove(mPanelDevice.getId());
                    }
                }
                break;

            case MainPanelHelper.SECTION_TYPE_DOOR_SENSOR:
                for (TuyaItemPlus tuyaItemPlus : mDoorSensorSectionList) {
                    if (!MainPanelHelper.getInstance().checkIsInBlackList(tuyaItemPlus.getId())) {
                        allPluginMap.put(tuyaItemPlus.getId(), new PluginMapItem(tuyaItemPlus
                                , MainPanelHelper.SECTION_TYPE_DOOR_SENSOR));
                    } else {
                        allPluginMap.remove(tuyaItemPlus.getId());
                    }
                }
                break;

            case MainPanelHelper.SECTION_TYPE_SHORTCUT:
                for (TuyaItemPlus tuyaItemPlus : mShortcutList) {
                    if (!MainPanelHelper.getInstance().checkIsInBlackList(tuyaItemPlus.getId())) {
                        allPluginMap.put(tuyaItemPlus.getId(), new PluginMapItem(tuyaItemPlus, MainPanelHelper.SECTION_TYPE_SHORTCUT));
                    } else {
                        allPluginMap.remove(tuyaItemPlus.getId());
                    }
                }
                break;
        }

        DDLog.d(TAG, "updateAllPluginMap:  finally: " + allPluginMap.size());
    }

    private void updateCacheList() {
        TestLog.w(TAG, "updateCacheList, " +
                "BmtCount" + BmtManager.getInstance().getAllDevicesSize()
                + ",allPluginMapSize: " + allPluginMap.size());

        int index = -1;
        if (CollectionUtil.isListNotEmpty(cacheLayoutList)) {
            for (int i = 0; i < cacheLayoutList.size(); i++) {
                MainWidgetBean mainWidgetBean = cacheLayoutList.get(i);
                int bindModelType = mainWidgetBean.getBindModelType();
                if (MainPanelHelper.SECTION_TYPE_FUNCTION == bindModelType) {
                    index = i;
                    break;
                }
            }
        }

        final boolean isGuest = HomeManager.getInstance().isGuest();
        if (isGuest) {
            // 如果是访客权限，检查layoutCache中是否包含该权限下不支持的配件类型，如果有，全部移除
            final ArrayList<MainWidgetBean> needDeleteCacheBeanList = new ArrayList<>();
            for (MainWidgetBean cacheBean : cacheLayoutList) {
                final int cacheBeanSectionType = cacheBean.getBindModelType();
                final String layoutType = cacheBean.getLayoutType();
                boolean isIgnoredLayoutType = !TextUtils.isEmpty(layoutType)
                        && MainPanelHelper.getInstance().isIgnoredLayoutTypeForGuest(cacheBean.getLayoutType());
                if (MainPanelHelper.getInstance().isIgnoredSectionTypeForGuest(cacheBeanSectionType)
                        || isIgnoredLayoutType) {
                    needDeleteCacheBeanList.add(cacheBean);
                }
            }
            if (needDeleteCacheBeanList.size() > 0) {
                DDLog.i(TAG, "updateCacheList, delete cache layout, size: " + needDeleteCacheBeanList.size());
                cacheLayoutList.removeAll(needDeleteCacheBeanList);
                index = -1;
            }
        }

        for (Map.Entry<String, PluginMapItem> map : allPluginMap.entrySet()) {
            if (isNeedChangePowerStationCache(map)) {
                continue;
            }

            if (isExitLayoutCache(map.getKey())) {
                continue;
            }

            final int sectionType = map.getValue().getSectionType();
            if (isGuest && (MainPanelHelper.getInstance().isIgnoredSectionTypeForGuest(sectionType))) {
                // 如果是访客权限，并且配件类型是该权限下不支持的配件类型，直接跳过不处理
                DDLog.v(TAG, "updateCacheList ignore section type: " + sectionType);
                continue;
            }

            switch (sectionType) {
                case MainPanelHelper.SECTION_TYPE_POWER_PULSE:
                    Device powerPulse = (Device) map.getValue().getObj();
                    if (index > -1) {
                        cacheLayoutList.add(index, new MainWidgetBean(powerPulse, PluginWidgetStyleUtil.POWER_PULSE_CURRENT
                                , sectionType));
                        index++;
                    } else {
                        cacheLayoutList.add(new MainWidgetBean(powerPulse, PluginWidgetStyleUtil.POWER_PULSE_CURRENT
                                , sectionType));
                    }

                    break;

                case MainPanelHelper.SECTION_TYPE_POWER_STORE:
                    Device powerStore = (Device) map.getValue().getObj();
                    if (index > -1) {
                        cacheLayoutList.add(index, new MainWidgetBean(powerStore, PluginWidgetStyleUtil.POWER_STORE_CURRENT
                                , sectionType));
                        index++;
                    } else {
                        cacheLayoutList.add(new MainWidgetBean(powerStore, PluginWidgetStyleUtil.POWER_STORE_CURRENT
                                , sectionType));
                    }

                    break;

                case MainPanelHelper.SECTION_TYPE_BMT:
                    Device bmtDevice = (Device) map.getValue().getObj();
                    if (index > -1) {
                        cacheLayoutList.add(index, new MainWidgetBean(bmtDevice, PluginWidgetStyleUtil.BMT
                                , sectionType));
                        index++;
                    } else {
                        cacheLayoutList.add(new MainWidgetBean(bmtDevice, PluginWidgetStyleUtil.BMT
                                , sectionType));
                    }

                    break;

                case MainPanelHelper.SECTION_TYPE_IPC:
                    Device ipcDevice = (Device) map.getValue().getObj();
                    if (!ipcDevice.getFlagCache()) {
                        if (index > -1) {
                            cacheLayoutList.add(index, new MainWidgetBean(ipcDevice, PluginWidgetStyleUtil.IPC
                                    , sectionType));
                            index++;
                        } else {
                            cacheLayoutList.add(new MainWidgetBean(ipcDevice, PluginWidgetStyleUtil.IPC
                                    , sectionType));
                        }
                    }
                    break;

                case MainPanelHelper.SECTION_TYPE_DOOR_SENSOR:
                    TuyaItemPlus ds = (TuyaItemPlus) map.getValue().getObj();
                    if (!ds.isFlagCache()) {
                        if (index > -1) {
                            cacheLayoutList.add(index, new MainWidgetBean(ds, PluginWidgetStyleUtil.PLUGIN
                                    , sectionType));
                            index++;
                        } else {
                            cacheLayoutList.add(new MainWidgetBean(ds, PluginWidgetStyleUtil.PLUGIN
                                    , sectionType));
                        }
                    }
                    break;

                case MainPanelHelper.SECTION_TYPE_SHORTCUT:
                    TuyaItemPlus shortcut = (TuyaItemPlus) map.getValue().getObj();
                    if (!shortcut.isFlagCache()) {
                        if (index > -1) {
                            cacheLayoutList.add(index, new MainWidgetBean(shortcut, PluginWidgetStyleUtil.SHORTCUT
                                    , sectionType));
                            index++;
                        } else {
                            cacheLayoutList.add(new MainWidgetBean(shortcut, PluginWidgetStyleUtil.SHORTCUT
                                    , sectionType));
                        }
                    }
                    break;

                case MainPanelHelper.SECTION_TYPE_PANEL_DEVICE:
                    PanelDevice panelDevice = (PanelDevice) map.getValue().getObj();
                    if (!panelDevice.getFlagCache()) {
                        if (index > -1) {
                            cacheLayoutList.add(index, new MainWidgetBean(panelDevice, PluginWidgetStyleUtil.PANEL
                                    , sectionType));
                            index++;
                        } else {
                            cacheLayoutList.add(new MainWidgetBean(panelDevice, PluginWidgetStyleUtil.PANEL
                                    , sectionType));
                        }
                    }
                    break;

                case MainPanelHelper.SECTION_TYPE_FUNCTION:
                    MainWidgetBean bean = new MainWidgetBean();
                    bean.setPluginId(FUNCTION_ID); // plugin id 不能为空
                    bean.setBindModelType(sectionType);
                    cacheLayoutList.add(bean);
                    break;

                case MainPanelHelper.SECTION_TYPE_BANNER:
                    MainWidgetBean banner = new MainWidgetBean();
                    banner.setPluginId(BANNER_ID); // plugin id 不能为空
                    banner.setBindModelType(sectionType);
                    cacheLayoutList.add(0, banner);
                    break;
            }
        }
        DDLog.d(TAG, "updateCacheList: finally cacheLayoutList size:" + cacheLayoutList.size());
        saveCacheLayout();
    }

    /**
     * 判断device是否已经存在cacheLayoutList
     *
     * @param targetId
     * @return
     */
    private boolean isExitLayoutCache(String targetId) {
        if (TextUtils.isEmpty(targetId)) {
            DDLog.e(TAG, "isExitLayoutCache. targetId is null");
            return false;
        }

        for (int j = 0; j < cacheLayoutList.size(); j++) {
            if (targetId.equals(cacheLayoutList.get(j).getPluginId())) {
                return true;
            }
        }
        return false;
    }

    private int exitLayoutCacheIndex(String targetId) {
        if (TextUtils.isEmpty(targetId)) {
            DDLog.e(TAG, "isExitLayoutCache. targetId is null");
            return -1;
        }

        for (int j = 0; j < cacheLayoutList.size(); j++) {
            if (targetId.equals(cacheLayoutList.get(j).getPluginId())) {
                return j;
            }
        }
        return -1;
    }

    /**
     * 修改3.3.2之前版本bmt的缓存信息
     *
     * @param map
     * @return
     */
    private boolean isNeedChangePowerStationCache(Map.Entry<String, PluginMapItem> map) {
        PluginMapItem value = map.getValue();
        if (isPowerStationType(value.getSectionType())) {
            int index = exitLayoutCacheIndex(((Device) value.getObj()).getId());
            if (index < 0 || index > cacheLayoutList.size()) {
                return false;
            }

            if (TextUtils.isEmpty(cacheLayoutList.get(index).getSubCategory())) {
                MainWidgetBean widgetBean = cacheLayoutList.get(index);
                cacheLayoutList.remove(index);
                widgetBean.setPluginId(map.getKey());
                widgetBean.setSubCategory((((Device) value.getObj()).getSubCategory()));
                cacheLayoutList.add(index, widgetBean);
            }
            return true;
        }
        return false;
    }

    /**
     * 修改3.3.2之前版本bmt的黑名单信息
     *
     * @param deviceList
     */
    private void changePowerStationBlackList(List<Device> deviceList) {
        DDLog.d(TAG, "changePowerStationBlackList: " + deviceList.size());
        for (Device dev : deviceList) {
            if (MainPanelHelper.getInstance().checkIsInBlackList(dev.getId())) {
                MainPanelHelper.getInstance().deletePluginFromBlackList(dev.getId());
                JSONArray newBlackList = MainPanelHelper.getInstance().getBlackList();
                newBlackList.put(dev.getId() + UNDERLINE + dev.getSubCategory());
                MainPanelHelper.getInstance().updateBlackList(newBlackList);
            }
        }
    }

    /**
     * 构建列表布局
     *
     * @param baseFragment
     */
    private void buildBindModelList(BaseFragment baseFragment) {
        cleanAllOperateArmDelayListener(null != mPanelDevice ? mPanelDevice.getId() : null);
        bindModelList.clear();
        MainWidgetBean bean;
        for (int index = 0; index < cacheLayoutList.size(); index++) {
            bean = cacheLayoutList.get(index);
            // 在黑名单中则不显示
            if (MainPanelHelper.getInstance().checkIsInBlackList(bean.getPluginId())) {
                DDLog.d(TAG, "cacheLayout in black list index: " + index);
                continue;
            }

            if (MainPanelHelper.SECTION_TYPE_FUNCTION == bean.getBindModelType()) {
                bindModelList.add(new MainFunctionModel(baseFragment.getContext()
                        , baseFragment.getMainActivity(), bean, mFunctionList, onWidgetItemListener));

            } else if (MainPanelHelper.SECTION_TYPE_BANNER == bean.getBindModelType()) {
                bindModelList.add(new MainBannerModel(baseFragment.getContext()
                        , baseFragment.getMainActivity(), bean, mBannerData));

            } else {
                int layoutID = PluginWidgetStyleUtil.getInstance().getWidgetStyle(bean.getLayoutType());
                DDLog.d(TAG, "buildBindModelList. layoutID: " + layoutID);
                if (layoutID == 0) {
                    continue;
                }
                switch (bean.getBindModelType()) {
                    case MainPanelHelper.SECTION_TYPE_POWER_PULSE:
                        Device powerPulse = BmtManager.getInstance().getDeviceById(bean.getPluginId().split(UNDERLINE)[0], bean.getSubCategory());
                        if (powerPulse != null) {
                            String name = getDeviceName(powerPulse);
                            if (!TextUtils.isEmpty(name)) {
                                bean.setName(name);
                            }
                        }
                        if (PluginWidgetStyleUtil.POWER_PULSE_CURRENT.equals(bean.getLayoutType())) {
                            DDLog.d(TAG, "SECTION_TYPE_POWER_PULSE: cacheLayoutList size: 成功");
                            bindModelList.add(new MainPowerPulseCurrentModel(baseFragment, powerPulse, bean, onWidgetItemListener, index));
                        }
                        break;

                    case MainPanelHelper.SECTION_TYPE_POWER_STORE:
                        Device powerStore = BmtManager.getInstance().getDeviceById(bean.getPluginId().split(UNDERLINE)[0], bean.getSubCategory());
                        if (powerStore != null) {
                            String name = getDeviceName(powerStore);
                            if (!TextUtils.isEmpty(name)) {
                                bean.setName(name);
                            }
                        }
                        if (PluginWidgetStyleUtil.POWER_STORE_CURRENT.equals(bean.getLayoutType())) {
                            DDLog.d(TAG, "SECTION_TYPE_POWER_STORE: cacheLayoutList size: 成功");
                            bindModelList.add(new MainPowerStoreCurrentModel(baseFragment, powerStore, bean, onWidgetItemListener, index));
                        } else if (PluginWidgetStyleUtil.POWER_STORE_BATTERY.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainBatteryModel(baseFragment, powerStore, bean, onWidgetItemListener, index));
                        } else if (PluginWidgetStyleUtil.POWER_STORE_EMERGENCY.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainBmtEmergencyModel(baseFragment, powerStore, bean, onWidgetItemListener, index));
                        } else if (PluginWidgetStyleUtil.POWER_STORE_TODAY_USAGE.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainBmtTodayUsageModel(baseFragment, powerStore, bean, onWidgetItemListener, index));
                        }
                        break;

                    case MainPanelHelper.SECTION_TYPE_BMT:
                        Device bmtDevice = BmtManager.getInstance().getDeviceById(bean.getPluginId().split(UNDERLINE)[0], bean.getSubCategory());
                        if (bmtDevice != null) {
                            String name = getDeviceName(bmtDevice);
                            if (!TextUtils.isEmpty(name)) {
                                bean.setName(name);
                            }
                        }
                        if (PluginWidgetStyleUtil.BMT.equals(bean.getLayoutType())) {
                            DDLog.d(TAG, "SECTION_TYPE_BMT: cacheLayoutList size: 成功");
                            bindModelList.add(new MainBmtV2Model(baseFragment, bmtDevice, bean, onWidgetItemListener, index));
                        } else if (PluginWidgetStyleUtil.BATTERY.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainBatteryModel(baseFragment, bmtDevice, bean, onWidgetItemListener, index));
                        } else if (PluginWidgetStyleUtil.BMT_EV.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainBmtEVModel(baseFragment, bmtDevice, bean, onWidgetItemListener, index));
                        } else if (PluginWidgetStyleUtil.BMT_EMERGENCY.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainBmtEmergencyModel(baseFragment, bmtDevice, bean, onWidgetItemListener, index));
                        } else if (PluginWidgetStyleUtil.BMT_TODAY_USAGE.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainBmtTodayUsageModel(baseFragment, bmtDevice, bean, onWidgetItemListener, index));
                        }
                        break;

                    case MainPanelHelper.SECTION_TYPE_IPC:
                        Device device = null;
                        if (!allPluginMap.isEmpty() && allPluginMap.containsKey(bean.getPluginId())) {
                            device = (Device) allPluginMap.get(bean.getPluginId()).getObj();
                            if (device != null) {
                                String name = getDeviceName(device);
                                if (!TextUtils.isEmpty(name)) {
                                    bean.setName(name);
                                }
                            }
                        }
                        if (PluginWidgetStyleUtil.IPC.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainIpcModel(baseFragment.getMainActivity(), baseFragment.getContext()
                                    , device, bean, onWidgetItemListener, true, index));
                        } else if (PluginWidgetStyleUtil.IPC_SMALL.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainSmallIpcModel(baseFragment.getMainActivity(), baseFragment.getContext()
                                    , device, bean, onWidgetItemListener, true, index));
                        } else if (PluginWidgetStyleUtil.IPC_MORE.equals(bean.getLayoutType())) {
                            dealMultiScreen(baseFragment, index, bean);
                        }
                        break;

                    case MainPanelHelper.SECTION_TYPE_PANEL_DEVICE:
                        PanelDevice panelDevice = null;
                        if (!allPluginMap.isEmpty() && allPluginMap.containsKey(bean.getPluginId())) {
                            panelDevice = (PanelDevice) allPluginMap.get(bean.getPluginId()).getObj();
                        }
                        if (PluginWidgetStyleUtil.PANEL.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainHostModel(baseFragment, panelDevice, getPanelArmDelayRecorder(panelDevice),
                                    bean, onWidgetItemListener, onDeviceStatusActionClickListener, index));
                        } else if (PluginWidgetStyleUtil.PANEL_SMALL.equals(bean.getLayoutType())) {
                            bindModelList.add(new MainSmallHostModel(baseFragment, panelDevice, getPanelArmDelayRecorder(panelDevice),
                                    bean, onWidgetItemListener, onDeviceStatusActionClickListener, index));
                        }
                        break;

                    case MainPanelHelper.SECTION_TYPE_DOOR_SENSOR:
                        TuyaItemPlus doorItemPlus = null;
                        if (!allPluginMap.isEmpty() && allPluginMap.containsKey(bean.getPluginId())) {
                            doorItemPlus = (TuyaItemPlus) allPluginMap.get(bean.getPluginId()).getObj();
                            if (doorItemPlus != null && !TextUtils.isEmpty(doorItemPlus.getName())) {
                                bean.setName(doorItemPlus.getName());
                            }
                        }
                        bindModelList.add(new MainPluginModel(baseFragment, doorItemPlus,
                                bean, onWidgetItemListener, index));
                        break;

                    case MainPanelHelper.SECTION_TYPE_SHORTCUT:
                        TuyaItemPlus tuyaItemPlus = null;
                        if (!allPluginMap.isEmpty() && allPluginMap.containsKey(bean.getPluginId())) {
                            tuyaItemPlus = (TuyaItemPlus) allPluginMap.get(bean.getPluginId()).getObj();
                            if (tuyaItemPlus != null && !TextUtils.isEmpty(tuyaItemPlus.getName())) {
                                bean.setName(tuyaItemPlus.getName());
                            }
                        }
                        bindModelList.add(new MainShortcutPluginModel(baseFragment, tuyaItemPlus
                                , bean, onWidgetItemListener, index));
                        break;
                }
            }

        }

        DDLog.d(TAG, "buildBindModelList: bindModelList size: " + bindModelList.size());
    }


    private void dealMultiScreen(BaseFragment baseFragment, final int adapterItemId, final MainWidgetBean bean) {
        final String multiScreenCardId = bean.getPluginId();
        boolean hadMultiPlayDscamId = false;
        if (!TextUtils.isEmpty(multiScreenCardId)) {
            final List<String> localMultiPlayIds = DsCamUtils.getCurrentHomeLocalMultiScreenCardDsCamIds(multiScreenCardId);
            hadMultiPlayDscamId = localMultiPlayIds.size() > 0;
        }

        // 还没请求完Dscam设备并且卡卡没有已经缓存的多屏播放摄像头ID
        if (!IPCManager.getInstance().isHadLoadDscam() && !hadMultiPlayDscamId) {
            return;
        }

        // 已经请求完Dscam设备数据并且没有Dscam设备
        if (IPCManager.getInstance().isHadLoadDscam() && !DsCamUtils.supportStaticMultiPlay()) {
            return;
        }

        bindModelList.add(new MainMultiScreenPluginModel(baseFragment.getContext(), baseFragment.getMainActivity(),
                bean, onWidgetItemListener, adapterItemId));
    }

    /**
     * 缓存列表布局信息
     */
    private void saveCacheLayout() {
        // 缓存数据
        Gson gson = new Gson();
        String listToJsonString = gson.toJson(cacheLayoutList);
        DDLog.i(TAG, "saveCacheLayout: listToJsonString: " + listToJsonString
                + "  mUserId: " + mUserId + "   curHomeName: " + mHomeId);
        TestLog.i(TAG, "saveCacheLayout, homeId: " + HomeManager.getInstance().getCurrentHome().getHomeID()
                + " : " + TestLog.getStackTrace());
        TestLog.i(TAG, "saveCacheLayout: listToJsonString: " + listToJsonString
                + "  mUserId: " + mUserId + "   curHomeName: " + mHomeId);
        DBUtil.Put(DBKey.KEY_MAIN_PANEL_CACHE_LAYOUT + UNDERLINE + mUserId + UNDERLINE + mHomeId, listToJsonString);
    }

    private String getDeviceName(Device device) {
        String name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
        if (TextUtils.isEmpty(name)) {
            name = (String) DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, "");
        }
        return name;
    }

    public MainBmtV2Model getMainBmtModelByDeviceId(String deviceId, String subcategory) {
        if (CollectionUtil.isListNotEmpty(bindModelList) && !TextUtils.isEmpty(deviceId)) {
            for (BaseMainItemModel mainItemModel : bindModelList) {
                if (mainItemModel instanceof MainBmtV2Model) {
                    String dId = ((MainBmtV2Model) mainItemModel).getDeviceId();
                    String dSub = ((MainBmtV2Model) mainItemModel).getWidgetBean().getSubCategory();
                    if (!TextUtils.isEmpty(dId) && deviceId.equals(dId) && subcategory.equals(dSub)) {
                        return (MainBmtV2Model) mainItemModel;
                    }
                }
            }
        }
        return null;
    }

    public MainBatteryModel getMainBatteryModelByDeviceId(String deviceId, String subcategory) {
        if (CollectionUtil.isListNotEmpty(bindModelList) && !TextUtils.isEmpty(deviceId)) {
            for (BaseMainItemModel mainItemModel : bindModelList) {
                if (mainItemModel instanceof MainBatteryModel) {
                    String dId = ((MainBatteryModel) mainItemModel).getDeviceId();
                    String dSub = ((MainBatteryModel) mainItemModel).getWidgetBean().getSubCategory();
                    if (!TextUtils.isEmpty(dId) && deviceId.equals(dId) && subcategory.equals(dSub)) {
                        return (MainBatteryModel) mainItemModel;
                    }
                }
            }
        }
        return null;
    }

    @Nullable
    private MainPanelArmDelayRecorder getPanelArmDelayRecorder(@Nullable final Device dev) {
        if (null != dev && !TextUtils.isEmpty(dev.getId())) {
            return ensurePanelArmDelayRecorders(dev.getId());
        }
        return null;
    }

    @Nullable
    private MainPanelArmDelayRecorder getPanelArmDelayRecorder(@Nullable final String id) {
        if (TextUtils.isEmpty(id)) {
            return null;
        }
        return mPanelArmDelayRecorderMap.get(id);
    }

    private MainPanelArmDelayRecorder ensurePanelArmDelayRecorders(@NonNull final String deviceId) {
        if (mPanelArmDelayRecorderMap.containsKey(deviceId)) {
            return mPanelArmDelayRecorderMap.get(deviceId);
        }
        final MainPanelArmDelayRecorder r = new MainPanelArmDelayRecorder(deviceId);
        mPanelArmDelayRecorderMap.put(deviceId, r);
        return r;
    }

    private void cleanPanelArmDelayRecorders() {
        for (MainPanelArmDelayRecorder value : mPanelArmDelayRecorderMap.values()) {
            value.cancelScheduleArmDelay();
        }
        mPanelArmDelayRecorderMap.clear();
    }

    public void setOperateArmDelay(final String deviceId, final int delay) {
        final MainPanelArmDelayRecorder panelArmDelayRecorder = getPanelArmDelayRecorder(deviceId);
        if (null != panelArmDelayRecorder) {
            panelArmDelayRecorder.tryScheduleArmDelay(delay);
        }
    }

    public void cancelOperateArmDelay(@Nullable final String deviceId) {
        final MainPanelArmDelayRecorder panelArmDelayRecorder = getPanelArmDelayRecorder(deviceId);
        if (null != panelArmDelayRecorder) {
            panelArmDelayRecorder.resetOperatingStatus();
            panelArmDelayRecorder.cancelScheduleArmDelay();
        }
    }

    public void cleanAllOperateArmDelayListener(@Nullable final String deviceId) {
        final MainPanelArmDelayRecorder panelArmDelayRecorder = getPanelArmDelayRecorder(deviceId);
        if (null != panelArmDelayRecorder) {
            panelArmDelayRecorder.cleanAllListener();
        }
    }

    public void resetAndCleanOperateArmDelayListener(@Nullable final String deviceId) {
        final MainPanelArmDelayRecorder panelArmDelayRecorder = getPanelArmDelayRecorder(deviceId);
        if (null != panelArmDelayRecorder) {
            panelArmDelayRecorder.resetAndCleanListener();
        }
    }

    /**
     * 创建多屏多屏播放卡片的ID
     */
    @NonNull
    public synchronized static String genDsCamMultiScreenId() {
        return MULTI_SCREEN_ID + "_" + System.nanoTime();
    }

    /**
     * 判断ID是否多屏卡片的ID
     *
     * @param pluginId 首页卡片ID
     * @return true: 是多屏卡片的ID；false:非多屏卡片的ID
     */
    public boolean isDsCamMultiScreenId(@Nullable final String pluginId) {
        if (!TextUtils.isEmpty(pluginId)) {
            final String idPrefix = MULTI_SCREEN_ID + "_";
            return pluginId.startsWith(idPrefix);
        }
        return false;
    }

    /**
     * 判断配件对应的BindModel类型
     *
     * @param type
     * @return
     */
    public boolean isPowerStationType(int type) {
        boolean isPowerStationType = false;
        switch (type) {
            case MainPanelHelper.SECTION_TYPE_POWER_PULSE:
            case MainPanelHelper.SECTION_TYPE_BMT:
            case MainPanelHelper.SECTION_TYPE_POWER_STORE:
                isPowerStationType = true;
                break;
        }
        return isPowerStationType;
    }
}
