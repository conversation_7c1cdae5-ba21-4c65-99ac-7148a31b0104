package com.dinsafer.module.main.helper;

import android.graphics.Canvas;

import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.module.main.adapter.MainPanelBindMultiAdapter;
import com.dinsafer.module.main.model.BaseMainItemModel;

import io.reactivex.annotations.NonNull;
import io.reactivex.annotations.Nullable;

/**
 * @describe：首页widget卡片列表辅助器，控制recyclerview的item的拖拽
 * @date：2022/10/17
 * @author: create by Sydnee
 */
public class MainRvItemTouchHelper extends ItemTouchHelper.Callback {
    private MainPanelBindMultiAdapter<BaseMainItemModel<?>> adapter;

    public MainRvItemTouchHelper(MainPanelBindMultiAdapter<BaseMainItemModel<?>> adapter) {
        this.adapter = adapter;
    }

    @Override
    public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        int swipeFlag = 0;
        int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
        return makeMovementFlags(dragFlags, swipeFlag);
    }

    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
        int curPosition = viewHolder.getAdapterPosition();
        int targetPosition = target.getAdapterPosition();
        if (curPosition == targetPosition || null == adapter) {
            return false;
        }
        return adapter.onItemMove(curPosition, targetPosition);
    }


    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
    }

    @Override
    public boolean isLongPressDragEnabled() {
        return false;
    }

    @Override
    public void onSelectedChanged(@Nullable RecyclerView.ViewHolder viewHolder, int actionState) {
        super.onSelectedChanged(viewHolder, actionState);
    }

    @Override
    public void onChildDraw(@NonNull Canvas c, @NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, float dX, float dY, int actionState, boolean isCurrentlyActive) {
        super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive);

    }
}
