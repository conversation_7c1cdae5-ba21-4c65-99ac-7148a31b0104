package com.dinsafer.module.main.model;

import android.content.Context;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.ui.anim.ShakeAnimUtil;
import com.dinsafer.util.PluginWidgetStyleUtil;

/**
 * @author: create by <PERSON><PERSON><PERSON>
 * @date：2022/9/27
 * @describe：
 */
public class MainSmallIpcModel extends BaseMainIpcModel {

    public MainSmallIpcModel(MainActivity mainActivity, Context context, Device device
            , MainWidgetBean widgetBean, OnWidgetItemListener onWidgetItemListener, boolean isCanEnterEditMode, final int adapterItemId) {
        super(mainActivity, context, device, widgetBean, onWidgetItemListener, isCanEnterEditMode, PluginWidgetStyleUtil.IPC_SMALL, adapterItemId, ShakeAnimUtil.SMALL_TYPE);
    }

    @Override
    public boolean showStateIcon() {
        return false;
    }

    @Override
    public boolean isCameraVideoPlayIconNull() {
        return true;
    }

}
