package com.dinsafer.module.main.view;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentSelectPluginAddWidgetListBinding;
import com.dinsafer.model.event.AddWidgetEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.helper.AddWidgetProvider;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.BindRecyclerViewUtil;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * @describe：
 * @date：2022/10/27
 * @author: create by Sydnee
 */
public class SelectPluginAddWidgetListFragment extends BaseFragment {

    private FragmentSelectPluginAddWidgetListBinding mBinding;
    private BindMultiAdapter<BaseBindModel> adapter;

    public static SelectPluginAddWidgetListFragment newInstance() {
        return new SelectPluginAddWidgetListFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_select_plugin_add_widget_list, container, false);
        initListener();
        initData();
        initRV();
        return mBinding.getRoot();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonBarBack.setOnClickListener(v -> {
            removeSelf();
        });
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        adapter = new BindMultiAdapter<>();
        adapter.setHeaderAndEmpty(true);
        mBinding.rvAllPlugin.setDrawingCacheEnabled(true);
        mBinding.rvAllPlugin.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);
        mBinding.rvAllPlugin.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvAllPlugin.setAdapter(adapter);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(AddWidgetEvent event) {
        DDLog.d(TAG, "AddWidgetEvent.");
        removeSelf();
    }

    private void initRV() {
        adapter.removeAllHeaderView();
        adapter.setNewData(null);

        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext()
                , AddWidgetProvider.getBmtItems(this)));

        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                AddWidgetProvider.getIpcItems(this)));

        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                AddWidgetProvider.getAccessoryItems(this, APIKey.IS_SETTING_ITEM_HAVE_LOADING)));
    }



}
