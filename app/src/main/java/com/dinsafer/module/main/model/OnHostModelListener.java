package com.dinsafer.module.main.model;

import com.dinsafer.model.DeviceSimStatueEvent;

/**
 * @describe：
 * @date：2022/11/24
 * @author: create by Sydnee
 */
public interface OnHostModelListener extends BaseModelListener{

    void updatePing(int netType, int sim, int rssi, boolean isCharge, int batteryLevel);

    void toArm();

    void toDisArm();

    void toResetSos();

    void initDeviceStatus(int status);

    void toInitArm();

    void toInitDisArm();

    void toInitHomeArm();

    void playSound(int soundID, int leftVolume, int rightVolume, int priority, int loop, int rate);

    void updatePowerCallBackAction();

    void updateSimStatus(DeviceSimStatueEvent event);

    void changeDeviceStateErrorHadSim();

    void changeDeviceStateNormal(int network,int sim, int rssi, boolean isCharge,int batteryLevel,int armStatus);

    void changeDeviceStateOfflineMode();

    void changeDeviceStatePanelUpgrading();

    void changeDeviceToolbarStateByUserPermission(boolean showSos);

    void onOperationArmAck(int exitDelay);

    /**
     * 更新延时布防倒计时
     *
     * @param seconds
     */
    void updateArmCountDown(int seconds);

    void onLoadedInfoUpdate();
}
