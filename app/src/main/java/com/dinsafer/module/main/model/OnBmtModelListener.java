package com.dinsafer.module.main.model;

import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;

public interface OnBmtModelListener extends BaseModelListener {

    void updateBmtStatus(BmtDeviceStatusChange event);

    void updateChargeMode(ChargeModeEvent event);

    void updateBmtCurrent(BmtGraphicUpdateEvent event);

    void updateBmtBatteryStatus(BatteryStatusEvent event);

    void setPowerStationDefaultView();

    /**
     * BMT基础信息加载状态改变
     *
     * @param deviceId bmt的ID
     */
    void updateBmtLoadState(final String deviceId, final String subcategory);

    void resetInverterStatus();
}
