package com.dinsafer.module.main.model;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.model.panel.MainPanelBmtTodayUsageVH;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.bean.BmtGraphicCacheInfo;
import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BmtChartDataEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtShowUpdateDialogEvent;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.PluginWidgetStyleUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.List;
import java.util.Map;

public class MainBmtTodayUsageModel extends BaseMainItemModel<ViewDataBinding> implements OnBmtTodayUsageModelListener {

    private final static String TAG = MainBmtTodayUsageModel.class.getSimpleName();
    private BaseFragment mBaseFragment;
    private int layoutId;
    private Device device;
    private OnWidgetItemListener onWidgetItemListener;
    private MainPanelBmtTodayUsageVH itemHolder;
    private String name;
    private int mChipsStatus = 0;

    public MainBmtTodayUsageModel(BaseFragment baseFragment, Device device, @NonNull MainWidgetBean bean,
                                  OnWidgetItemListener bmtItemListener, int adapterItemId) {
        super(baseFragment.getContext(), bean, null != bean ? bean.getPluginId() : null, PluginWidgetStyleUtil.BMT_TODAY_USAGE, adapterItemId);
        mBaseFragment = baseFragment;
        this.device = device;
        this.onWidgetItemListener = bmtItemListener;
        this.layoutId = PluginWidgetStyleUtil.getInstance().getWidgetStyle(bean.getLayoutType());
    }

    @Override
    public int getLayoutID() {
        return layoutId;
    }

    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding viewDataBinding) {
        DDLog.i(TAG, "convert");
        if (device != null) {
            mChipsStatus = DeviceHelper.getInt(device, "chipsStatus", Mcu.Chips.ChipsUpdateState.uptodate.getCode());
        }

        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            itemHolder = new MainPanelBmtTodayUsageVH(mBaseFragment, viewDataBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, itemHolder);
        } else {
            itemHolder = (MainPanelBmtTodayUsageVH) tag;
        }
        itemHolder.setRootViewVisible(true);
        itemHolder.setRootViewEnable(!MainPanelHelper.getInstance().isPanelEditMode());
        itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
        itemHolder.refreshText();

        initListener(holder);

        if (null == device || !device.getFlagLoaded()) {
            name = getWidgetBean().getName();
            itemHolder.setRootViewLoading(name);

            if (getWidgetBean().isNullDevice()) {
                itemHolder.setRootViewDeletedState(true);
            }
            return;
        }
        DDLog.i(TAG, "device info:" + device);
        name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
        itemHolder.setPluginName(name);

        itemHolder.setRootViewDeletedState(device.getFlagDeleted());
        // 从缓存数据中更新视图
        final BmtGraphicCacheInfo graphicCacheInfo = BmtManager.getInstance().getGraphicCacheInfo(device.getId(), device.getSubCategory());
        if (null != graphicCacheInfo) {
            // final BatteryStatusEvent lastBatteryStatusEvent = graphicCacheInfo.getLastBatteryStatusEvent();
            // if (null != lastBatteryStatusEvent) {
            //     itemHolder.updateBatteryStatus(lastBatteryStatusEvent);
            // }
            final List<BmtGraphicUpdateEvent> graphicUpdateEvents = graphicCacheInfo.getBmtGraphicUpdateEvents();
            if (!graphicUpdateEvents.isEmpty()) {
                for (BmtGraphicUpdateEvent event : graphicUpdateEvents) {
                    final String cmd = event.getCmd();
                    if (BmtCmd.GET_CHIPS_STATUS.equals(cmd)) {
                        final Map<String, Object> map = event.getData();
                        final Map<String, Object> result = DeviceHelper.getMap(map, PSKeyConstant.RESULT);
                        final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
                        if (-1 != chipsStatus) {
                            mChipsStatus = chipsStatus;
                        }
                    }
                }
            }
            BmtChartDataEvent chartDataEvent = graphicCacheInfo.getLastBmtChartDataEvent();
            if (chartDataEvent != null) {
                updateBmtTodayUsage(chartDataEvent);
            }
        }
        final boolean deviceConnected = BmtUtil.isDeviceConnected(device);
        updateRootViewAlpha();
    }

    private void updateRootViewAlpha() {
        if (itemHolder != null) {
            final int chipStatus = mChipsStatus;
            final boolean showMarker = BmtUtil.isNeedShowUpgradeMarker(chipStatus);
            final boolean deviceConnected = BmtUtil.isDeviceConnected(device);
            itemHolder.setRootViewUpgradeState(showMarker && deviceConnected);
//            if (MainPanelHelper.getInstance().isPanelEditMode()) {
//                itemHolder.setRootViewEnableAlpha(false);
//            } else {
//                itemHolder.setRootViewEnableAlpha(true);
//            }
        }
    }

    @Override
    public void onViewDeleted(String pluginId, boolean flagDeleted) {

    }

    @Override
    public void onViewDeleted(String pluginId, String subcategory, boolean flagDeleted) {
        if (null == itemHolder || null == device) {
            return;
        }
        if (TextUtils.isEmpty(pluginId) || !pluginId.equals(device.getId()) || !subcategory.equals(device.getSubCategory())) {
            return;
        }
        itemHolder.setRootViewDeletedState(device.getFlagDeleted());
    }

    @Override
    public void checkDeviceIsNull() {
        boolean isDeleted = false;
        if (null == device) {
            isDeleted = true;
        } else if (device.getFlagDeleted()) {
            isDeleted = true;
        }
        getWidgetBean().setNullDevice(isDeleted);
        itemHolder.setRootViewDeletedState(isDeleted);
    }

    @Override
    public void updateBmtStatus(BmtDeviceStatusChange event) {
        if (itemHolder != null) {
            event.setShowUpdate(BmtUtil.showOfflineChipsStatus(device) && BmtUtil.isDeviceConnected(device));
            itemHolder.updateBmtStatus(event);
        }
    }

    @Override
    public void updateChargeMode(ChargeModeEvent event) {

    }

    @Override
    public void updateBmtCurrent(BmtGraphicUpdateEvent event) {
        if (TextUtils.isEmpty(event.getDeviceId()) || device == null) {
            return;
        }
        if (!event.getDeviceId().equals(device.getId()) || !event.getSubCategory().equals(device.getSubCategory())) {
            return;
        }
        String cmd = event.getCmd();
        if (BmtCmd.GET_CHIPS_STATUS.equals(cmd)) {
            final Map<String, Object> map = event.getData();
            final Map<String, Object> result = DeviceHelper.getMap(map, PSKeyConstant.RESULT);
            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
            if (-1 != chipsStatus) {
                mChipsStatus = chipsStatus;
            }
            updateRootViewAlpha();
        }
    }

    @Override
    public void updateBmtBatteryStatus(BatteryStatusEvent event) {

    }

    @Override
    public void setPowerStationDefaultView() {

    }

    @Override
    public void updateBmtLoadState(String deviceId, String subcategory) {
        if (TextUtils.isEmpty(deviceId) || device == null) {
            return;
        }
        if (!deviceId.equals(device.getId()) || !subcategory.equals(device.getSubCategory())) {
            return;
        }

        itemHolder.setRootViewVisible(true);
        itemHolder.setRootViewEnable(!MainPanelHelper.getInstance().isPanelEditMode());
        itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());

        if (null == device || !device.getFlagLoaded()) {
            name = getWidgetBean().getName();
            itemHolder.setRootViewLoading(name);

            // if (widgetBean.isNullDevice()) {
            //     itemHolder.setRootViewDeletedState(true);
            // }
            return;
        }

        itemHolder.setRootViewDeletedState(device.getFlagDeleted());
    }

    @Override
    public void resetInverterStatus() {

    }

    private void initListener(BaseViewHolder holder) {
        DeviceCallBackManager.getInstance().addBmtTodayUsageModelListener(this);

        itemHolder.setUpgradeClickListener(v -> {
            if (null == onWidgetItemListener || null == device) {
                DDLog.e(TAG, "当前Item没有对应的device，不能点击");
                return;
            }
            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            // 需要升级
            final boolean needUpgrade = true;
            if (needUpgrade && null != device) {
                EventBus.getDefault().post(new BmtShowUpdateDialogEvent(device.getId(), device.getSubCategory(), mChipsStatus));
            }
        });

        itemHolder.setRootViewClickListener(v -> {
            if (null == onWidgetItemListener || null == device) {
                DDLog.e(TAG, "当前Item没有对应的device，不能点击");
                return;
            }
            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            onWidgetItemListener.onBmtOrBatteryItemClick(MainPanelHelper.getInstance().isPanelEditMode()
                    , MainPanelHelper.SECTION_TYPE_BMT, PowerStationRoot2Fragment.SHOW_STATS, device, name);

        });

        itemHolder.setRootViewLongClickListener(view -> {
            if (null != onWidgetItemListener) {
                onWidgetItemListener.onItemLongClick(MainPanelHelper.getInstance().isPanelEditMode(), holder, holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());

            }
            return true;
        });


        itemHolder.setDeleteIconClickListener(view -> {
            if (null == onWidgetItemListener || !MainPanelHelper.getInstance().isPanelEditMode()) {
                return;
            }
            DeviceCallBackManager.getInstance().removeBmtTodayModelListener(this);
            onWidgetItemListener.onDeleteIconClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });

        itemHolder.setUnavailableStateClickListener(v -> {
            if (null == onWidgetItemListener) {
                return;
            }
            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            onWidgetItemListener.onUnavailableStateViewClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });
    }

    @Override
    public void updateBmtTodayUsage(BmtChartDataEvent bmtChartDataEvent) {
        if (TextUtils.isEmpty(bmtChartDataEvent.getDeviceId()) || device == null) {
            return;
        }
        if (!bmtChartDataEvent.getDeviceId().equals(device.getId()) || !bmtChartDataEvent.getSubcategory().equals(device.getSubCategory())) {
            return;
        }

        if (itemHolder != null) {
            itemHolder.updateBmtTodayUsage(bmtChartDataEvent);
        }
    }
}
