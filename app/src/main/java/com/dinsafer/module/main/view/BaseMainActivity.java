package com.dinsafer.module.main.view;

import android.Manifest;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutTopToastBinding;
import com.dinsafer.model.FragmentListChangeEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.BaseFragmentActivity;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.login.LoadingTextFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;

/**
 * Created by Rinfon on 16/6/16.
 * 主界面父类
 */
public class BaseMainActivity extends BaseFragmentActivity {

    public static final int REQUEST_PERMISSION_CAMERA_CODE = 1112;

    private LoadingFragment loadingFragment;

    private ArrayList<BaseFragment> mFragmentList = new ArrayList<BaseFragment>();

    private ArrayList<String> mFragmentNameList = new ArrayList<String>();

    private OnPermissionResult onPermissionResult;

    protected LayoutTopToastBinding topToastBinding;

    private LoadingTextFragment mLoadingTextFragment;
    private ViewGroup decorView;


    @Override
    protected boolean initVariables() {
        return true;
    }

    @CallSuper
    @Override
    protected void initViews(Bundle savedInstanceState) {
        initTopToast();
    }

    @Override
    protected void loadData() {

    }

    private void initTopToast() {
        topToastBinding = LayoutTopToastBinding.inflate(LayoutInflater.from(this));
        decorView = (ViewGroup) getWindow().getDecorView();
        decorView.addView(topToastBinding.getRoot());
        ViewGroup.LayoutParams lp = topToastBinding.getRoot().getLayoutParams();
        lp.width = ViewGroup.LayoutParams.MATCH_PARENT;
        lp.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        topToastBinding.commonTopToast.setPadding(topToastBinding.commonTopToast.getPaddingLeft(),
                getStatusBarHeight(), topToastBinding.commonTopToast.getPaddingRight(),
                topToastBinding.commonTopToast.getPaddingBottom());
    }

    public final void showLoadingTextFragment() {
        if (mLoadingTextFragment == null) {
            mLoadingTextFragment = LoadingTextFragment.newInstance();
        }
        addCommonFragment(mLoadingTextFragment);
    }

    public final void closeLoadingTextFragment() {
        if (mLoadingTextFragment != null) {
            removeCommonFragmentAndData(mLoadingTextFragment, true);
        }
    }

    public final void showLoadingFragment(int blackOrBlue, String description) {
        closeLoadingFragment();
        loadingFragment = LoadingFragment.newInstance(blackOrBlue, description);
        addCommonFragment(loadingFragment);
    }

    public final void showLoadingFragment(int blackOrBlue, String description, boolean canBack) {
        closeLoadingFragment();
        loadingFragment = LoadingFragment.newInstance(blackOrBlue, description, canBack);
        addCommonFragment(loadingFragment);
    }

    public final void closeLoadingFragment() {
        if (loadingFragment != null)
            removeCommonFragmentAndData(loadingFragment, true);

        // 修复偶尔不知道什么情况有一个loading一直无法关闭的问题
        if (isCommonFragmentExist(LoadingFragment.class.getName())) {
            removeCommonFragment(LoadingFragment.class.getName());
        }
    }

    public final void showTimeOutLoadinFramgment() {
        showLoadingFragment(LoadingFragment.BLACK, "");
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
            }
        }, LocalKey.TIMEOUT);
    }

    public final void showTimeOutLoadinFramgment(long milliseconds) {
        showLoadingFragment(LoadingFragment.BLACK, "");
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
            }
        }, milliseconds);
    }

    public final void closeTimeOutLoadinFramgmentWithErrorAlert() {
        closeLoadingFragmentWithTimeOut();
    }

    /**
     * 关闭Loading并移除超时后自动取消Loading的Message
     */
    public final void closeLoadingFragmentWithTimeOut() {
        closeLoadingFragment();
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
    }

    Handler timeoutHandler;

    public final void showTimeOutLoadinFramgmentWithErrorAlert() {
        showLoadingFragment(LoadingFragment.BLACK, "");
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
                showErrorToast();
            }
        }, LocalKey.TIMEOUT);
    }

    Handler timeoutHandler2;

    public final void showTimeOutLoadinFramgmentWithCallBack(ILoadingCallBack callback) {
        showLoadingFragment(LoadingFragment.BLACK, "");
        if (timeoutHandler2 != null) {
            timeoutHandler2.removeCallbacksAndMessages(null);
        }
        timeoutHandler2 = new Handler();
        timeoutHandler2.postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
                if (callback != null) {
                    callback.onTimeout();
                }
            }
        }, LocalKey.TIMEOUT + 20 * 1000);
    }

    public final void showTimeOutLoadinFramgmentWithCallBack(final long timeoutMillis, ILoadingCallBack callback) {
        showLoadingFragment(LoadingFragment.BLACK, "");
        if (timeoutHandler2 != null) {
            timeoutHandler2.removeCallbacksAndMessages(null);
        }
        timeoutHandler2 = new Handler();
        timeoutHandler2.postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
                if (callback != null) {
                    callback.onTimeout();
                }
            }
        }, timeoutMillis > 0 ? timeoutMillis : LocalKey.TIMEOUT);
    }

    /**
     * 关闭Loading并移除超时后自动取消Loading的Message
     */
    public final void closeLoadingFragmentWithCallBack() {
        closeLoadingFragment();
        if (timeoutHandler2 != null) {
            timeoutHandler2.removeCallbacksAndMessages(null);
        }
    }


    public final void showTimeOutLoadinFramgmentWithBack() {
        showLoadingFragment(LoadingFragment.BLACK, "", true);
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
            }
        }, LocalKey.TIMEOUT);
    }

    public final void showBlueTimeOutLoadinFramgmentWithBack() {
        showLoadingFragment(LoadingFragment.BLUE, "");
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
            }
        }, LocalKey.TIMEOUT);
    }

    public final void showBlueTimeOutLoadinFramgment() {
        showLoadingFragment(LoadingFragment.BLUE, "", true);
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
            }
        }, LocalKey.TIMEOUT);
    }

    public final void showLoadingFragmentWithMarginTop(int blackOrBlue, String description, int marginTop) {
        closeLoadingFragment();
        loadingFragment = LoadingFragment.newInstance(blackOrBlue, description, marginTop);
        addCommonFragment(loadingFragment);
    }

    public final void showTimeOutLoadinFramgmentWithMarginTop(int marginTop) {
        showLoadingFragmentWithMarginTop(LoadingFragment.BLACK, "", marginTop);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
            }
        }, LocalKey.TIMEOUT);
    }

    /**
     * 判断是否包含相同的fragment
     *
     * @param fragmentName
     * @return
     */
    public boolean isCommonFragmentExist(String fragmentName) {
        if (mFragmentList != null) {
            for (int i = 0; i < mFragmentList.size(); i++) {
                if (null != mFragmentList.get(i) && mFragmentList.get(i).getClass().getName().equals(fragmentName)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否包含apstep开头的页面
     *
     * @return
     */
    public boolean isApStepFragmentExit() {
        try {
            if (mFragmentList != null) {
                for (int i = 0; i < mFragmentList.size(); i++) {
                    if (mFragmentList.get(i) != null) {
                        if (mFragmentList.get(i).getClass().getSimpleName().toLowerCase().startsWith("blestep")
                                || mFragmentList.get(i).getClass().getSimpleName().toLowerCase().startsWith("apstep")) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isHasFragment() {
        if (mFragmentList == null || mFragmentNameList.size() <= 0) {
            return false;

        }
        return true;
    }

    /**
     * 添加一个fragment页面
     *
     * @param mFragment
     */
    public void addCommonFragment(final BaseFragment mFragment) {
        try {
            this.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (mFragment == null)
                        return;

                    if (!mFragmentList.contains(mFragment) && !mFragmentNameList.contains(mFragment.getClass().getName())) {
                        if (mFragmentList.size() > 0) {
                            mFragmentList.get(mFragmentList.size() - 1).onPauseFragment();
                        }

                        mFragmentList.add(mFragment);
                        mFragmentNameList.add(mFragment.getClass().getName());

                        FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
                        fragmentTransaction.addToBackStack(mFragment.getClass().getName());
                        //add的最后一个参数，是设置tag
                        fragmentTransaction.add(
                                R.id.main_common_fragment_container, mFragment, mFragment.getClass().getSimpleName()).show(mFragment);
                        fragmentTransaction.commitAllowingStateLoss();

                        if (mFragmentList.size() == 1) {
                            EventBus.getDefault().post(new FragmentListChangeEvent(true));
                        }
                    }

                    DDLog.d("location", "Fragment'name is " + mFragment.getClass().getName() + ";mFragment.getTag() is " + mFragment.getTag());
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void removeCommonFragmentAndData(BaseFragment mFragment, boolean isAnim) {
        try {
            String name = mFragment.getClass().getName();
            removeCommonFragment(mFragment, isAnim);
            mFragment.onExitFragment();
            if (mFragmentList.contains(mFragment)) {
                mFragmentList.remove(mFragment);
                mFragmentNameList.remove(mFragment.getClass().getName());
            }

//            loading关闭，不执行生命周期，因为在welcome页面showloading 会出现死循环
            if (mFragmentList.size() > 0 && !name.equals(LoadingFragment.class.getName())) {
                mFragmentList.get(mFragmentList.size() - 1).onEnterFragment();
            }
            if (mFragmentList.size() == 0) {
                EventBus.getDefault().post(new FragmentListChangeEvent(false));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void removeCommonFragment(BaseFragment mFragment, boolean isAnim) {
        try {
            FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
            if (isAnim)
                fragmentTransaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE);
            fragmentTransaction.remove(mFragment);
            fragmentTransaction.commitAllowingStateLoss();
//            为什么要用getChildFragmentManager，因为有可能导致addfragment的时候没有添加成功
//            不能用这个，有可能导致Android Fragment java.lang.IllegalStateException: No host
//            mFragment.getChildFragmentManager().popBackStackImmediate(mFragment.getClass().getName(), FragmentManager.POP_BACK_STACK_INCLUSIVE);
//            getSupportFragmentManager().popBackStackImmediate(mFragment.getClass().getName(), FragmentManager.POP_BACK_STACK_INCLUSIVE);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void removeCommonFragment(String name) {
        if (mFragmentList.size() == 0)
            return;
        for (int i = 0; i < mFragmentList.size(); i++) {
            if (mFragmentNameList.get(i).equals(name)) {
                removeCommonFragmentAndData(mFragmentList.get(i), true);
                break;
            }
        }


    }

    public void removeCommonExcludeFragment(String name) {
        if (mFragmentList.size() == 0)
            return;
        for (int i = 0; i < mFragmentList.size(); i++) {
            if (!mFragmentNameList.get(i).equals(name)) {
                removeCommonFragmentAndData(mFragmentList.get(i), false);
                break;
            }
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    public void removeAllCommonFragment() {
        if (mFragmentList.size() == 0)
            return;
        for (int i = 0; i < mFragmentList.size() - 1; i++)
            removeCommonFragment(mFragmentList.get(i), false);
        removeCommonFragment(mFragmentList.get(mFragmentList.size() - 1), true);
        mFragmentList.clear();
        mFragmentNameList.clear();
    }


    public void removeToFragment(String className) {
        if (mFragmentList.size() == 0)
            return;

        int index = -1;
        for (int i = 0; i < mFragmentList.size(); i++) {
            if (mFragmentList.get(i).getClass().getName().equals(className)) {
                index = i;
                break;
            }
        }

        if (index == -1 || index == mFragmentList.size() - 1) {
            Log.e(TAG, String.format("removeToFragment: %s not found in stack", className));
            return;
        }

        for (int i = index + 1; i < mFragmentList.size() - 1; i++) {
            removeCommonFragment(mFragmentList.get(i), false);
        }
        removeCommonFragment(mFragmentList.get(mFragmentList.size() - 1), true);
        for (int i = mFragmentList.size() - 1; i > index; i--) {
            mFragmentList.remove(i);
            mFragmentNameList.remove(i);
        }
        if (mFragmentList.size() > 0) {
            mFragmentList.get(mFragmentList.size() - 1).onEnterFragment();
        }
        if (mFragmentList.size() == 0) {
            EventBus.getDefault().post(new FragmentListChangeEvent(false));
        }
    }

    public boolean isFragmentInTop(BaseFragment baseFragment) {
        try {
            if (mFragmentList.get(mFragmentList.size() - 1).equals(baseFragment)) {
                return true;
            }
        } catch (Exception ex) {
            return false;
        }
        return false;

    }

    public boolean isFragmentInTopExcludeLoading(BaseFragment baseFragment) {
        boolean found = false;
        try {
            int index = mFragmentList.size() - 1;
            while (index >= 0) {
                BaseFragment temp = mFragmentList.get(index);
                if (temp instanceof LoadingFragment) {
                    index--;
                    continue;
                }
                if (temp.equals(baseFragment)) {
                    found = true;
                }
                break;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return found;
    }

    public boolean isFragmentInTop(String fragmentname) {
        if (mFragmentList != null && mFragmentList.size() > 0) {
            int size = mFragmentList.size() - 1;
            if (mFragmentList.get(size).getClass().getSimpleName().toLowerCase().equals(fragmentname.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    public void showErrorToast() {
        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!isFinishing() && !isDestroyed()) {
                    AlertDialog.createBuilder(BaseMainActivity.this)
                            .setOk("OK")
                            .setContent(getResources().getString(R.string.failed_try_again))
                            .preBuilder()
                            .show();
                }
            }
        });
    }

    public final void showToast(final String msg) {

        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {

                AlertDialog.createBuilder(BaseMainActivity.this)
                        .setOk("OK")
                        .setContent(msg)
                        .preBuilder()
                        .show();
            }
        });
    }

    public final void showToast(final String confirmTxt, final String msg) {
        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!isFinishing() && !isDestroyed()) {
                    AlertDialog.createBuilder(BaseMainActivity.this)
                            .setOk(confirmTxt)
                            .setContent(msg)
                            .preBuilder()
                            .show();
                }
            }
        });
    }


    public final void showToast(final String msg, final String ok, final String cancel,
                                final AlertDialog.AlertOkClickCallback onclick, final AlertDialog.AlertCancelClickCallback oncancel) {

        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.createBuilder(BaseMainActivity.this)
                        .setOk(ok)
                        .setAutoDissmiss(true)
                        .setCancel(cancel)
                        .setContent(msg)
                        .setOKListener(onclick)
                        .setCancelListener(oncancel)
                        .preBuilder()
                        .show();

            }
        });
    }


    public final void showToast(final String msg, final AlertDialog.AlertOkClickCallback onclick) {

        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.createBuilder(BaseMainActivity.this)
                        .setOk("OK")
                        .setContent(msg)
                        .setOKListener(onclick)
                        .preBuilder()
                        .show();
            }
        });
    }

    public final void showToast(final String msg, final boolean isShowCancel, final AlertDialog.AlertOkClickCallback onclick) {

        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (isShowCancel) {
                    AlertDialog.createBuilder(BaseMainActivity.this)
                            .setOk("OK")
                            .setCancel(getResources().getString(R.string.Cancel))
                            .setContent(msg)
                            .setOKListener(onclick)
                            .preBuilder()
                            .show();
                } else {
                    AlertDialog.createBuilder(BaseMainActivity.this)
                            .setOk("OK")
                            .setContent(msg)
                            .setOKListener(onclick)
                            .preBuilder()
                            .show();
                }
            }
        });
    }


    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        return super.dispatchTouchEvent(ev);
    }

    public ArrayList<BaseFragment> getFragmentList() {
        return mFragmentList;
    }


    public void popCommonFragment(boolean isAnim) {
        try {
            if (mFragmentList.size() > 0) {
                BaseFragment fragment = mFragmentList.get(mFragmentList.size() - 1);
                removeCommonFragment(fragment, isAnim);
                fragment.onExitFragment();
                mFragmentList.remove(fragment);
                mFragmentNameList.remove(fragment.getClass().getName());
                String name = fragment.getClass().getName();
//            loading关闭，不执行生命周期，因为在welcome页面showloading 会出现死循环
                if (mFragmentList.size() > 0 && !name.equals(LoadingFragment.class.getName())) {
                    mFragmentList.get(mFragmentList.size() - 1).onEnterFragment();
                }
                if (mFragmentList.size() == 0) {
                    EventBus.getDefault().post(new FragmentListChangeEvent(false));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showTopToast(String message) {
        showTopToast(R.drawable.icon_toast_succeed, message);
    }

    public void showTopToast(int icon, String message) {
        showTopToast(icon, message, "");
    }

    public void showTopToast(int icon, String message, String blockPlugin) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!TextUtils.isEmpty(message)) {
                    if (TextUtils.isEmpty(blockPlugin)) {
                        topToastBinding.commonTopToastBlock.setVisibility(View.GONE);
                        topToastBinding.commonTopToastBlockPlugin.setVisibility(View.GONE);
                        topToastBinding.ivLine2.setVisibility(View.GONE);
                        topToastBinding.ivLine1.setVisibility(View.VISIBLE);
                    } else {
                        topToastBinding.commonTopToastBlock.setLocalText(getString(R.string.Bypassed));
                        topToastBinding.commonTopToastBlockPlugin.setText(blockPlugin);
                        topToastBinding.commonTopToastBlock.setVisibility(View.VISIBLE);
                        topToastBinding.commonTopToastBlockPlugin.setVisibility(View.VISIBLE);
                        topToastBinding.ivLine2.setVisibility(View.VISIBLE);
                        topToastBinding.ivLine1.setVisibility(View.GONE);

                    }
                    final Drawable leftIcon;
                    if (icon > 0) {
                        leftIcon = getResources().getDrawable(icon);
                        leftIcon.setBounds(0, 0, leftIcon.getMinimumWidth(), leftIcon.getMinimumHeight());
                    } else {
                        leftIcon = null;
                    }
                    topToastBinding.commonTopToast.setCompoundDrawables(leftIcon, null, null, null);
                    topToastBinding.commonTopToast.setLocalText(message);
                    topToastBinding.commonTopToastLy.showToast(decorView);
                }
            }
        });
    }


    public ArrayList<String> getFragmentNameList() {
        return mFragmentNameList;
    }

    public boolean needCameraPermission() {
        return DDSystemUtil.isMarshmallow()
                && ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED;
    }

    public void requestCameraPermission(OnPermissionResult onPermissionResult) {
        this.onPermissionResult = onPermissionResult;
        String[] permissions = {Manifest.permission.CAMERA};
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            requestPermissions(permissions, REQUEST_PERMISSION_CAMERA_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull @NotNull String[] permissions,
                                           @NonNull @NotNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (null != onPermissionResult) {
            if (REQUEST_PERMISSION_CAMERA_CODE == requestCode) {
                // 相机权限申请结果
                onPermissionResult.onPermissionResult(requestCode, permissions, grantResults);
            }
        }
        onPermissionResult = null;
    }

    public int getStatusBarHeight() {
        int statusBarHeight = DensityUtil.dp2px(this, 20);
        Resources resources = getResources();
        int resId = resources.getIdentifier("status_bar_height", "dimen", "android");
        if (resId > 0) {
            statusBarHeight = resources.getDimensionPixelSize(resId);
        }
        return statusBarHeight;
    }

    public interface ILoadingCallBack {
        void onTimeout();
    }
}
