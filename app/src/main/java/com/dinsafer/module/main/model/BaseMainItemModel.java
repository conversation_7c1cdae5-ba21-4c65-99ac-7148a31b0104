package com.dinsafer.module.main.model;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;

import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.DDLog;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 首页Item的Model基类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/2/23 16:20
 */
public abstract class BaseMainItemModel<V extends ViewDataBinding> extends BindModel<V> {

    private final long mAdapterItemId;

    /**
     * 关联的配件ID
     */
    @NonNull
    private final String mModelId;

    /**
     * 视图类型
     */
    @NonNull
    private final String mModelType;

    @NonNull
    private final MainWidgetBean mWidgetBean;

    public BaseMainItemModel(Context context, @NonNull final MainWidgetBean widgetBean, final String modelId, final String modelType, final int adapterItemId) {
        super(context);
        this.mWidgetBean = widgetBean;
        mAdapterItemId = genAdapterItemId(modelId, modelType, adapterItemId);
        this.mModelId = null == modelId ? "" : modelId;
        this.mModelType = null == modelType ? "" : modelType;
    }

    @NonNull
    public String getModelId() {
        return mModelId;
    }

    @NonNull
    public String getModelType() {
        return mModelType;
    }

    public long getAdapterItemId() {
        return mAdapterItemId;
    }

    protected long genAdapterItemId(final String modelId, final String modelType, final int adapterItemId) {
        if (!TextUtils.isEmpty(modelId) && !TextUtils.isEmpty(modelType)) {
            final String key = modelType + "-" + modelId;
            return key.hashCode() + adapterItemId;
        }
        return System.currentTimeMillis();
    }

    @NonNull
    public MainWidgetBean getWidgetBean() {
        return mWidgetBean;
    }
}
