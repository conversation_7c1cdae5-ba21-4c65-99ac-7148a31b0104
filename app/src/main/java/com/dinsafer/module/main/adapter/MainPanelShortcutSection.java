package com.dinsafer.module.main.adapter;

import static com.dinsafer.model.TuyaItem.LIGHT_LOADING;
import static com.dinsafer.model.TuyaItem.LIGHT_OFF;
import static com.dinsafer.model.TuyaItem.LIGHT_ON;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_LOADING;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_OFF;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_ON;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_LOADING;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_OFF;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_ON;

import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.model.panel.MainPanelShortcutItemViewHolder;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;

import java.util.ArrayList;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;

/**
 * 首页Panel Shortcut 分组
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/2 5:49 PM
 */
public class MainPanelShortcutSection extends MainPanelBaseSection<TuyaItemPlus> {
    private final int ROW_ITEM_COUNT = 2;

    public MainPanelShortcutSection(Context context, String tittle, ArrayList<TuyaItemPlus> datas) {
        super(context, SectionParameters.builder()
                .itemResourceId(R.layout.main_section_panel_item_shortcut)
                .headerResourceId(R.layout.main_section_panel_item_header)
                .build(), tittle, datas);
    }

    public MainPanelShortcutSection(Context context, String tittle, ArrayList<TuyaItemPlus> datas,
                                    OnItemClickListener itemClickListener) {
        super(context, SectionParameters.builder()
                .itemResourceId(R.layout.main_section_panel_item_shortcut)
                .headerResourceId(R.layout.main_section_panel_item_header)
                .build(), tittle, datas);
        setOnItemClickListener(itemClickListener);
    }

    @Override
    public int getRowItemCount() {
        return ROW_ITEM_COUNT;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new MainPanelShortcutItemViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        MainPanelShortcutItemViewHolder itemHolder = (MainPanelShortcutItemViewHolder) holder;
        itemHolder.setEditMode(false);

        if (position >= mData.size()) {
            itemHolder.setStatusEmpty();
            itemHolder.setRootViewEnable(MainPanelHelper.getInstance().isPanelEditMode()
                    || MainPanelHelper.getInstance().isFunctionEnable());
            itemHolder.setRootViewVisible(false);
            return;
        }
        itemHolder.setRootViewVisible(true);

        TuyaItemPlus itemData = mData.get(position);
        itemHolder.setRootViewEnable(MainPanelHelper.getInstance().isPanelEditMode()
                || MainPanelHelper.getInstance().isFunctionEnable()
                || isTuyaPlug(itemData.getType())
                || isTuyaLight(itemData.getType()));

        if (itemData.isEmptyLoadingView()) {
            itemHolder.setStatusEmpty();
            return;
        }

        String name = itemData.getName();
        final String id = itemData.getId();
        if (TextUtils.isEmpty(name)) {
            if (!TextUtils.isEmpty(itemData.getDecodeid())) {
                //   如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                name = CommonDataUtil.getInstance().getSTypeByDecodeid(itemData.getDecodeid());
            } else if (id.startsWith("!")) {
                if (!TextUtils.isEmpty(itemData.getStype())) {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(itemData.getStype());
                } else {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(itemData.getSub_category());
                }
            } else {
                name = CommonDataUtil.getInstance().getSTypeByID(id);
            }
            name = Local.s(name) + "_" + id;
        }
        itemHolder.setPluginName(name);

        itemHolder.setRootViewClickListener((View v) -> {
            int pluginType = itemData.getType();
            if (!MainPanelHelper.getInstance().isPanelEditMode()
                    && !MainPanelHelper.getInstance().isFunctionEnable()
                    && !isTuyaLight(pluginType)
                    && !isTuyaPlug(pluginType)) {
                DDLog.e(TAG, "当前Item不能点击");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                // 编辑模式下
                itemData.setShow(!itemData.isShow());
                itemHolder.setSelected(itemData.isShow());
                if (null != mOnItemClickListener) {
                    mOnItemClickListener.onItemClick(mTitle, true, v, position);
                }
            } else {
                // 非编辑模式下-访客不能点击
                if (null != mOnItemClickListener
                        && LocalKey.GUEST
                        != DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getPermission()) {
                    mOnItemClickListener.onItemClick(mTitle, false, v, position);
                }
            }
        });
        itemHolder.setQuickStartClickListener((View v) -> {
                    int pluginType = itemData.getType();
                    if (!MainPanelHelper.getInstance().isPanelEditMode()
                            && !MainPanelHelper.getInstance().isFunctionEnable()
                            && isTuyaLight(pluginType)
                            && isTuyaPlug(pluginType)) {
                        DDLog.e(TAG, "当前Item不能点击2");
                        return;
                    }

                    //  点击Item QuickStart icon
                    if (isOfficialSmartPlug(pluginType)) {
                        // 自研插座
                        if (requestChangeSmartPlugStatus(itemData, itemHolder)) {
                            itemHolder.setQuickStartEnable(false);
                            itemHolder.setQuickStartLoading(true);
                            itemData.setType(SMARTPLUGIN_LOADING);
                        }
                    } else {
                        DDLog.e(TAG, "Unhandle plugin type, pluginType: " + pluginType);
                    }
                }
        );

        if (itemData.isNeedLoading()
                && itemData.isLoading()) {
            // 还在loading
            itemHolder.setSelected(itemData.isShow());
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusLoading("");
            return;
        }

        if (itemData.isNeedOnlineState()
                && !itemData.isOnline()) {
            // 离线
            itemHolder.setSelected(itemData.isShow());
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusFinish(mContext, itemData.isNeedOnlineState(), itemData.isOnline());
            return;
        }

        // 在线
        updateStateFinished(itemData, itemHolder);
    }

    /**
     * 修改为完成的状态
     *
     * @param itemData
     * @param itemHolder
     */
    private void updateStateFinished(TuyaItemPlus itemData
            , MainPanelShortcutItemViewHolder itemHolder) {
        itemHolder.setStatusFinish(mContext, itemData.isNeedOnlineState(), itemData.isOnline());

        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 编辑模式
            itemHolder.setEditMode(true);
            itemHolder.setSelected(itemData.isShow());
        } else {
            // 非编辑模式
            itemHolder.setEditMode(false);
            if (SMARTPLUGIN_LOADING == itemData.getType()
                    || TUYA_SMARTPLUGIN_LOADING == itemData.getType()
                    || LIGHT_LOADING == itemData.getType()) {
                itemHolder.setQuickStartEnable(false);
                itemHolder.setQuickStartLoading(true);
            } else {
                itemHolder.setQuickStartEnable(true);
                itemHolder.setQuickStartLoading(false);
                itemHolder.setQuickStartIconRes(
                        CommonDataUtil.getInstance().getMainIconByType(itemData.getType()));
            }
        }
    }

    /**
     * 请求修改自家插座的开关状态
     * <p>
     * status 0:关; 1:开
     */
    private boolean requestChangeSmartPlugStatus(TuyaItemPlus data,
                                                 MainPanelShortcutItemViewHolder itemHolder) {
        DDLog.d(TAG, "requestChangeSmartPlugStatus");
        if (data.getType() != SMARTPLUGIN_ON
                && data.getType() != SMARTPLUGIN_OFF) {
            DDLog.e(TAG, "Error type.");
            return false;
        }

        // 修改开关状态
        final int lastStatus = data.getType();
        int status = SMARTPLUGIN_ON == lastStatus ? 0 : 1;

        Device device = DinHome.getInstance().getDevice(data.getId());
        if (null != device) {
            device.submit(PanelParamsHelper.changePlugOn(1 == status));

            final Handler handler = new Handler();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        DDLog.e(TAG, "update smart plug state timeout.");
                        if (data.getType() == SMARTPLUGIN_LOADING) {
                            data.setType(lastStatus);
                            updateStateFinished(data, itemHolder);
                        }
                    } catch (Exception ex) {
                        DDLog.e(TAG, "update light state error.");
                    }
                }
            }, LocalKey.TIMEOUT);
            return true;
        } else {
            DDLog.e(TAG, "No plugin device");
            return false;
        }
    }

    /**
     * 是否自家插座
     *
     * @param type
     * @return true: 是自家插座
     */
    private boolean isOfficialSmartPlug(int type) {
        return type == SMARTPLUGIN_ON || type == SMARTPLUGIN_OFF
                || type == SMARTPLUGIN_LOADING;
    }

    /**
     * 是否涂鸦灯泡
     *
     * @param type
     * @return true: 是涂鸦灯泡
     */
    private boolean isTuyaLight(int type) {
        return LIGHT_ON == type
                || LIGHT_OFF == type
                || LIGHT_LOADING == type;
    }


    /**
     * 是否涂鸦插座
     *
     * @param type
     * @return true: 是涂鸦插座
     */
    private boolean isTuyaPlug(int type) {
        return TUYA_SMARTPLUGIN_ON == type
                || TUYA_SMARTPLUGIN_OFF == type
                || TUYA_SMARTPLUGIN_LOADING == type;
    }

}
