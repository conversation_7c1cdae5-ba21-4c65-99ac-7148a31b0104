package com.dinsafer.module.main.view;

import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.IPCKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.DeviceCmdEvent;
import com.dinsafer.model.RemoveSosFinish;
import com.dinsafer.model.SOSFragmentShowEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.model.SosStatusEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.BaseTextDrawable;
import com.dinsafer.ui.CircularView;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.TimeTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RandomStringUtils;
import com.nostra13.universalimageloader.core.ImageLoader;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by Rinfon on 16/9/1.
 */
public class SOSFragment extends BaseFragment {


    LocalTextView sosTitle;
    CircularView sosAvator;
    TextView sosName;
    LocalTextView sosDescription;
    LocalCustomButton sosStop;
    LocalCustomButton sosIgnore;
    TimeTextView sosTime;

    public long startSosTime;
    LocalTextView sosIntimidationDescription;

    private SoundPool soundPool;

    private int id;

    public static SosStatusEntry sosStatusEntry;

    public static SOSFragment newInstance() {

        SOSFragment sosFragment = new SOSFragment();
//        Bundle bundle = new Bundle();
//        bundle.putSerializable("data", sosStatusEntry);
//        sosFragment.setArguments(bundle);
        return sosFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.sos_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);

        initData();
        getMainActivity().showSOSLayout(false);
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.sos_stop).setOnClickListener( v -> toStop());
        rootView.findViewById(R.id.sos_ignore).setOnClickListener( v -> toIgnore());
    }

    private void __bindViews(View rootView) {
        sosTitle = rootView.findViewById(R.id.sos_title);
        sosAvator = rootView.findViewById(R.id.sos_avator);
        sosName = rootView.findViewById(R.id.sos_name);
        sosDescription = rootView.findViewById(R.id.sos_description);
        sosStop = rootView.findViewById(R.id.sos_stop);
        sosIgnore = rootView.findViewById(R.id.sos_ignore);
        sosTime = rootView.findViewById(R.id.sos_time);
        sosIntimidationDescription = rootView.findViewById(R.id.sos_intimidation_description);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        soundPool.stop(id);
        soundPool.release();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(SOSevent ev) {
        initData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(RemoveSosFinish ev) {

//            取消报警成功
        closeLoadingFragment();
        removeSelf();
        if (ev.isOperateSelf()) {
            AlertDialog.createBuilder(getDelegateActivity())
                    .setOk("Yes")
                    .setCancel("No")
                    .setOKListener(new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            EventBus.getDefault().post(new DeviceCmdEvent(LocalKey.ARM_KEY));
                        }
                    })
                    .setContent(getResources().getString(R.string.sos_disarm_success))
                    .preBuilder()
                    .show();
        }

    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: ");
        EventBus.getDefault().post(new SOSFragmentShowEvent());
    }

    @Override
    public boolean onBackPressed() {
        return true;
    }


    @Override
    public void initData() {
        super.initData();
        startSosTime = DDDateUtil.getUTCTime(sosStatusEntry.getResult().getTime());
        sosTime.setStartTime(startSosTime);
        final String familyName = HomeManager.getInstance().getCurrentHome().getHomeName();
        sosTitle.setText(familyName == null ? "" : familyName);
        sosStop.setLocalText(getResources().getString(R.string.sos_stop));
        sosIgnore.setLocalText(getResources().getString(R.string.sos_ignore));
        sosDescription.setLocalText(getResources().getString(R.string.tigger_alarm));

        if (!TextUtils.isEmpty(sosStatusEntry.getResult().getIntimidationmessage())) {
            if (LocalKey.TASK_ANTIINTERFER_SOS.equals(sosStatusEntry.getCmd())) {
                sosIntimidationDescription.setText(sosStatusEntry.getResult().getIntimidationmessage());
            } else {
                sosIntimidationDescription.setLocalText(sosStatusEntry.getResult().getIntimidationmessage());
            }
            sosIntimidationDescription.setVisibility(View.VISIBLE);
            sosDescription.setVisibility(View.GONE);
            sosAvator.setVisibility(View.INVISIBLE);
            sosName.setVisibility(View.GONE);
        } else {
            sosIntimidationDescription.setVisibility(View.GONE);
            sosDescription.setVisibility(View.VISIBLE);
            String name = "";
            i("uid:" + sosStatusEntry.getResult().getUid());
            if (LocalKey.NO_ACTION_SOS.equals(sosStatusEntry.getCmd())) {
                sosAvator.setVisibility(View.VISIBLE);
                sosAvator.setImageResource(R.drawable.icon_eventlist_care_mode);
                sosDescription.setVisibility(View.VISIBLE);
                sosName.setText(Local.s(getString(R.string.care_mode_sos_content)));
                sosName.setVisibility(View.VISIBLE);
            } else if (!TextUtils.isEmpty(sosStatusEntry.getResult().getUid())) {
                name = sosStatusEntry.getResult().getUid();
                BaseTextDrawable drawable = DDImageUtil.getBaseTextDrawable(getDelegateActivity(), sosStatusEntry.getResult().getUid()
                        , DinSDK.getUserInstance().getUser().getUid().equals(sosStatusEntry.getResult().getUid()));

                int w = sosAvator.getLayoutParams().height;
                sosAvator.setBaseTextDrawable(drawable, w, w);
                if (!TextUtils.isEmpty(sosStatusEntry.getResult().getPhoto())) {
                    ImageLoader.getInstance().displayImage(APIKey.UPLOAD_SERVER_IP + sosStatusEntry.getResult().getPhoto(),
                            sosAvator);
                }
                sosName.setText(name);
            } else if (LocalKey.TASK_ANTIINTERFER_SOS.equals(sosStatusEntry.getCmd())) {
//                防干扰报警
                sosAvator.setVisibility(View.INVISIBLE);
                sosDescription.setVisibility(View.GONE);
                sosName.setText(sosStatusEntry.getResult().getIntimidationmessage());
                sosName.setVisibility(View.VISIBLE);
            } else if (sosStatusEntry.getResult().getIsdevice()) {
                name = sosStatusEntry.getResult().getPluginname();
                sosAvator.setImageResource(R.drawable.icon_sos_device_knockover);
                if (APIKey.EVENT_LIST_ALARM_PANEL.equals(name)) {
                    name = Local.s(name);
                }
                sosName.setText(name);
            } else {
                try {
                    if (!TextUtils.isEmpty(sosStatusEntry.getResult().getPluginname())) {
                        name = sosStatusEntry.getResult().getPluginname();
                    } else {

//                    String dinID = Dinsafe.str64ToHexStr(sosStatusEntry.getResult().getPluginid());
//                        String sType = CommonDataUtil.getInstance().getSTypeByID(sosStatusEntry.getResult().getPluginid());
                        String sType = CommonDataUtil.getInstance().getNameByBigIDAndSType(Integer.parseInt(sosStatusEntry.getResult().getCategory())
                                , sosStatusEntry.getResult().getSubcategory());
                        name = (Local.s(sType) + "_" + sosStatusEntry.getResult().getPluginid());
                    }

                    if ((IPCKey.OTHER_PLUGIN + "").equals(sosStatusEntry.getResult().getCategory())) {
                        sosAvator.setImageResource(CommonDataUtil.getInstance().getIconBySTypeId(sosStatusEntry.getResult().getSubcategory()));
                    } else {
                        sosAvator.setImageResource(CommonDataUtil.getInstance().getIconByBigIDAndSType(sosStatusEntry.getResult().getCategory()
                                , sosStatusEntry.getResult().getSubcategory()));
                    }
                } catch (Exception ex) {

                }
                sosName.setText(name);
            }

            if (LocalKey.TASK_FC_SOS.equals(sosStatusEntry.getCmd())
                    || LocalKey.TASK_FC_SOS_PANEL.equals(sosStatusEntry.getCmd())) {
                //防拆报警
                sosDescription.setLocalText(getResources().getString(R.string.door_tamper_alarm));
            }

        }


        if (soundPool != null) {
            soundPool.stop(id);
            soundPool.release();
        }
        soundPool = new SoundPool(10, AudioManager.STREAM_SYSTEM, 5);
        soundPool.load(getDelegateActivity(), R.raw.sos, 1);
        soundPool.setOnLoadCompleteListener(new SoundPool.OnLoadCompleteListener() {
            @Override
            public void onLoadComplete(SoundPool sp, int sampleId, int status) {
                id = soundPool.play(1, 1, 1, 0, 999999999, 1);
            }
        });

    }

    private String messageId = "";

    public void toStop() {
        messageId = RandomStringUtils.getMessageId();
        getMainActivity().showSOSLayout(false);
        DeviceCmdEvent deviceCmdEvent = new DeviceCmdEvent(LocalKey.DISARM_KEY);
        deviceCmdEvent.setMessageid(messageId);
        EventBus.getDefault().post(deviceCmdEvent);
        showTimeOutLoadinFramgment();
//        removeSelf();

    }

    public void toIgnore() {
        removeSelf();
        getMainActivity().showSOSLayout(true);
    }
}
