package com.dinsafer.module.main.model;

import com.dinsafer.dincore.common.Device;

/**
 * @describe：
 * @date：2022/11/25
 * @author: create by Sydnee
 */
public interface OnShortcutModelListener extends BaseModelListener {

    void updateShortcutName(String pluginId, String name);

    void updateOnlineStateShortCut(String sendId, boolean online, boolean isPanelDeviceOffline);

    void updateTuyaColorLightStatus(Device device, boolean isPanelDeviceOffline);

    void updateTuyaSmartPluginStatus(Device device, boolean isPanelDeviceOffline);

    void changeOldSmartPlugState(String shortcutId, boolean enable, boolean isPanelDeviceOffline);

    void changeAskSmartPlugState(String shortcutId, boolean enable, boolean isPanelDeviceOffline);

    void changeOfficialSmartPlugStateByPanelDevice(boolean isPanelDeviceOffline);

    void onLoadedUpdate(String shortcutId, final boolean loaded);
}
