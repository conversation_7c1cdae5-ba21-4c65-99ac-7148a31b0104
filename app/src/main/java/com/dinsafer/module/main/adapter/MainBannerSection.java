package com.dinsafer.module.main.adapter;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.convenientbanner.holder.CBViewHolderCreator;
import com.bigkoo.convenientbanner.holder.Holder;
import com.bigkoo.convenientbanner.listener.OnItemClickListener;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemMainBannerBinding;
import com.dinsafer.module.iap.BetaUserClubInvitationFragment;
import com.dinsafer.module.iap.ListBigBannerResponse;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DensityUtils;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.Section;
import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/4/6
 */
public class MainBannerSection extends Section {
    private String TAG = "MainBannerSection";
    private BaseMainActivity activity;
    private List<ListBigBannerResponse.BigBannerBean> data;

    public MainBannerSection(BaseMainActivity activity) {
        super(SectionParameters.builder()
                .itemResourceId(R.layout.item_main_banner)
                .build());
        this.activity = activity;
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return super.getHeaderViewHolder(view);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new BannerViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {

        BannerViewHolder itemHolder = (BannerViewHolder) holder;
        GridLayoutManager.LayoutParams rootLayoutParm = (GridLayoutManager.LayoutParams) itemHolder.mBinding.cvRoot.getLayoutParams();
        rootLayoutParm.topMargin = DensityUtils.dp2px(activity.getApplicationContext(), isVisible() ? 22 : 0);
        itemHolder.mBinding.cvRoot.setLayoutParams(rootLayoutParm);
        itemHolder.mBinding.banner.setPages(new CBViewHolderCreator() {
            @Override
            public Holder createHolder(View itemView) {
                return new MyBannerItemHolder(itemView);
            }

            @Override
            public int getLayoutId() {
                return R.layout.item_prime_service_banner;
            }
        }, this.data);
        itemHolder.mBinding.banner.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(int position) {
                activity.addCommonFragment(BetaUserClubInvitationFragment.newInstance(data.get(position).getTask_id()));
            }
        });

        if (this.data != null && this.data.size() > 1) {
            itemHolder.mBinding.banner.setPageIndicator(new int[]{R.drawable.shape_main_banner_indicator_nor, R.drawable.shape_main_banner_indicator_sel});
            itemHolder.mBinding.banner.setCanLoop(true);
            itemHolder.mBinding.banner.startTurning(4000);
        } else {
            itemHolder.mBinding.banner.setCanLoop(false);
            itemHolder.mBinding.banner.removeIndicatorViews();
            itemHolder.mBinding.banner.stopTurning();
        }

        try {
            //修改indicator底部边距
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) itemHolder.mBinding.banner.getLoPageTurningPoint().getLayoutParams();
            layoutParams.topMargin = DensityUtils.dp2px(activity.getApplicationContext(), 2);
            layoutParams.bottomMargin = DensityUtils.dp2px(activity.getApplicationContext(), 4);
            itemHolder.mBinding.banner.getLoPageTurningPoint().setLayoutParams(layoutParams);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    class BannerViewHolder extends RecyclerView.ViewHolder {
        private ItemMainBannerBinding mBinding;

        public BannerViewHolder(@NonNull View itemView) {
            super(itemView);
            mBinding = DataBindingUtil.bind(itemView);
        }

    }

    class MyBannerItemHolder extends Holder<ListBigBannerResponse.BigBannerBean> {

        public MyBannerItemHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initView(View itemView) {


        }

        @Override
        public void updateUI(ListBigBannerResponse.BigBannerBean data) {
            itemView.setTag(R.id.tv_title, data);
            itemView.setFocusable(true);
            itemView.setClickable(true);
            try {
                ((LocalTextView) itemView.findViewById(R.id.tv_title)).setLocalText(data.getView_dataset().getBnr_title_txt());
                ((LocalTextView) itemView.findViewById(R.id.tv_title)).setTextColor(Color.parseColor(data.getView_dataset().getBnr_title_clr()));
            } catch (Exception e) {

            }
            try {
                ((LocalTextView) itemView.findViewById(R.id.tv_des)).setText(data.getView_dataset().getBnr_desc_txt());
                ((LocalTextView) itemView.findViewById(R.id.tv_des)).setTextColor(Color.parseColor(data.getView_dataset().getBnr_desc_clr()));
            } catch (Exception e) {

            }
            try {
                ((ImageView) itemView.findViewById(R.id.btn_go)).setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(data.getView_dataset().getBnr_action_clr())));
            } catch (Exception e) {

            }
            itemView.findViewById(R.id.tv_preview).setVisibility(data.isPreviewStatus() ? View.VISIBLE : View.GONE);
        }

    }

    public void setVisibility(boolean visiable) {
        if (isVisible() != visiable) {
            setVisible(visiable);
        }
    }

    public void setData(List<ListBigBannerResponse.BigBannerBean> data) {
        this.data = data;
        if (this.data != null && this.data.size() > 0) {
            setVisibility(true);
        } else {
            setVisibility(false);
        }
    }
}
