package com.dinsafer.module.main.task;

import androidx.annotation.NonNull;

import com.dinsafer.model.event.NeedGetAllDeviceEvent;
import com.dinsafer.module.ipc.common.video.global.base.AbstractGlobalTask;
import com.dinsafer.module.ipc.common.video.global.base.IGlobalTaskListener;
import com.dinsafer.module.ipc.common.video.global.base.TaskManager;
import com.dinsafer.util.DDLog;

/**
 * 请求Device管理类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/5 11:02 上午
 */
public class DeviceRequestManager {

    private final static String TAG = "DeviceRequestManager";
    private final TaskManager mTaskManager;

    public void addTask(@NonNull NeedGetAllDeviceEvent loadEvent, @NonNull final DeviceRequestListener listener) {
        final String taskId = "xxxx";
        AbstractGlobalTask<Boolean, IGlobalTaskListener<Boolean>> lastTask = mTaskManager.findTask(taskId);
        if (null != lastTask) {
            DDLog.i(TAG, taskId + " is exit!");
            return;
        }

        final DeviceRequestTask task = new DeviceRequestTask(mTaskManager, taskId, NeedGetAllDeviceEvent.createFrom(loadEvent));
        mTaskManager.addTask(task, listener);
    }

    public void cancelTaskById(@NonNull final String taskId) {
        DDLog.i(TAG, "cancelTaskById, taskId: " + taskId);
        mTaskManager.cancelTaskById(taskId);
    }

    private DeviceRequestManager() {
        mTaskManager = new TaskManager();
    }

    private static final class Holder {
        private static final DeviceRequestManager INSTANCE = new DeviceRequestManager();
    }

    public static DeviceRequestManager get() {
        return Holder.INSTANCE;
    }
}
