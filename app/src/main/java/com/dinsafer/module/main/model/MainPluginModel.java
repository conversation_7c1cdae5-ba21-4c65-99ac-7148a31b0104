package com.dinsafer.module.main.model;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.model.panel.MainPanelPluginItemViewHolder;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.PluginWidgetStyleUtil;

/**
 * @author: creat by Sydnee
 * @date：2022/9/27
 * @describe：
 */
public class MainPluginModel extends BaseMainItemModel<ViewDataBinding> implements OnPluginModelListener {

    private final static String TAG = MainPluginModel.class.getSimpleName();
    private TuyaItemPlus tuyaItemPlus;
    private Context mContext;
    private int layoutId;
    private String cacheName;
    private String name;
    private OnWidgetItemListener onWidgetItemListener;
    private MainPanelPluginItemViewHolder itemHolder;

    public MainPluginModel(BaseFragment baseFragment, TuyaItemPlus tuyaItemPlus, @NonNull MainWidgetBean bean
            , OnWidgetItemListener onWidgetItemListener, final int adapterItemId) {
        super(baseFragment.getContext(), bean, null != bean ? bean.getPluginId() : null, PluginWidgetStyleUtil.PLUGIN, adapterItemId);
        this.mContext = baseFragment.getContext();
        this.onWidgetItemListener = onWidgetItemListener;
        this.tuyaItemPlus = tuyaItemPlus;
        this.layoutId = PluginWidgetStyleUtil.getInstance().getWidgetStyle(bean.getLayoutType());
        this.cacheName = bean.getName();
    }


    @Override
    public int getLayoutID() {
        return layoutId;
    }

    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding mainPanelItemPluginBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            itemHolder = new MainPanelPluginItemViewHolder(mainPanelItemPluginBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, itemHolder);
        } else {
            itemHolder = (MainPanelPluginItemViewHolder) tag;
        }

        itemHolder.setRootViewVisible(true);
        itemHolder.setRootViewEnable(true);
        itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());

        initListener(holder);

        if (tuyaItemPlus == null || !tuyaItemPlus.isFlagLoaded()) {
            // 切换家庭中先显示加载中
            itemHolder.setStatusLoading(cacheName);
            if (getWidgetBean().isNullDevice() || (null != tuyaItemPlus && tuyaItemPlus.isFlagDeleted())) {
                // 请求数据后发现无该device
                itemHolder.setStatusFinishNoState(mContext);
                itemHolder.setRootViewDeleted(true);
            }
            return;
        }
        name = tuyaItemPlus.getName();
        final String id = tuyaItemPlus.getId();
        if (TextUtils.isEmpty(name)) {
            if (!TextUtils.isEmpty(tuyaItemPlus.getDecodeid())) {
                //   如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                name = CommonDataUtil.getInstance().getSTypeByDecodeid(tuyaItemPlus.getDecodeid());
            } else if (id.startsWith("!")) {
                if (!TextUtils.isEmpty(tuyaItemPlus.getStype())) {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(tuyaItemPlus.getStype());
                } else {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(tuyaItemPlus.getSub_category());
                }
            } else {
                name = CommonDataUtil.getInstance().getSTypeByID(id);
            }
            name = Local.s(name) + "_" + id;
        }
        if (name.contains(id) && !TextUtils.isEmpty(cacheName)) {
            // 若有缓存的name
            name = cacheName;
        }
        itemHolder.setPluginName(name);

        boolean isPanelDeviceOffline = !CommonDataUtil.getInstance().isPanelOnline();
        changePluginState(isPanelDeviceOffline);

        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 编辑模式
            itemHolder.setEditMode(true);
            itemHolder.setSelected(tuyaItemPlus.isShow());
        } else {
            // 非编辑模式
            itemHolder.setEditMode(false);
            changePluginStateIcon();
        }

        // 是否已被删除
        itemHolder.setRootViewDeleted(tuyaItemPlus.isFlagDeleted());

    }


    private void initListener(BaseViewHolder holder) {
        DeviceCallBackManager.getInstance().addOnPluginModelListener(this);

        itemHolder.setRootViewClickListener((View v) -> {
            if (null == onWidgetItemListener || null == tuyaItemPlus) {
                DDLog.e(TAG, "当前Item没有对应的device，不能点击");
                return;
            }
            if (MainPanelHelper.getInstance().isPanelEditMode()
                    || !MainPanelHelper.getInstance().isFunctionEnable()) {
                DDLog.e(TAG, "当前Item不能点击");
                return;
            }

            onWidgetItemListener.onItemClick(MainPanelHelper.getInstance().isPanelEditMode()
                    , MainPanelHelper.SECTION_TYPE_DOOR_SENSOR, tuyaItemPlus, getModelId(), getModelType());
        });

        itemHolder.setRootViewLongClickListener(view -> {
            if (null != onWidgetItemListener) {
                onWidgetItemListener.onItemLongClick(MainPanelHelper.getInstance().isPanelEditMode(), holder, holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
            }
            return true;
        });

        itemHolder.setDeleteIconClickListener(view -> {
            if (null == onWidgetItemListener || !MainPanelHelper.getInstance().isPanelEditMode()) {
                return;
            }
            DeviceCallBackManager.getInstance().removeOnPluginModelListener(this);
            onWidgetItemListener.onDeleteIconClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });

        itemHolder.setUnavailableStateClickListener(View -> {
            if (null == onWidgetItemListener) {
                DDLog.e(TAG, "当前Item没有对应的device，不能点击");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            onWidgetItemListener.onUnavailableStateViewClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });
    }

    @Override
    public void onDo(View view) {
        super.onDo(view);
    }

    /**
     * 状态图标可见性及图标
     */
    private void changePluginStateIcon() {
        if (tuyaItemPlus.isHaveApart()) {
            itemHolder.setPluginStatusIconGone(true);
            itemHolder.setPluginStatueIconRes(MainPanelHelper.getInstance().getDoorApartStateIcon(
                    tuyaItemPlus.isNeedBlock(), tuyaItemPlus.isApart()));
        } else {
            itemHolder.setPluginStatusIconGone(false);
        }
    }

    private void changePluginState(boolean isPanelDeviceOffline) {
        DDLog.d(TAG, "changePluginState. isPanelDeviceOffline: " + isPanelDeviceOffline);
        if (isPanelDeviceOffline) {
            // 主机离线
            itemHolder.setStatusFinish(mContext, tuyaItemPlus.isOnline(), true);
            return;
        }

        if (!tuyaItemPlus.isFlagLoaded()) {
            // 还在loading
            if (TextUtils.isEmpty(name)) {
                name = cacheName;
            }
            itemHolder.setStatusLoading(name);
            return;
        }

        if (tuyaItemPlus.isNeedOnlineState()
                && !tuyaItemPlus.isOnline()) {
            // 离线
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusFinish(mContext, false, false);
            return;
        }

        // 在线
        if (tuyaItemPlus.isNeedOnlineState()) {
            itemHolder.setStatusFinish(mContext, tuyaItemPlus.isOnline(), false);
        } else {
            itemHolder.setStatusFinishNoState(mContext);
        }
    }

    /**
     * 更改配件名称
     *
     * @param pluginId
     * @param name
     */
    @Override
    public void updatePluginName(String pluginId, String name) {
        if (TextUtils.isEmpty(pluginId)
                || TextUtils.isEmpty(name)
                || null == tuyaItemPlus) {
            return;
        }

        if (!pluginId.equals(tuyaItemPlus.getId())) {
            return;
        }

        tuyaItemPlus.setName(name);
        itemHolder.setPluginName(name);
    }

    @Override
    public void updatePluginLivingState(String sendId, boolean online, boolean isPanelDeviceOffline) {
        // 更新门磁在线状态
        if (TextUtils.isEmpty(sendId) || null == tuyaItemPlus) {
            return;
        }

        if (!sendId.equals(tuyaItemPlus.getSendid())) {
            return;
        }
        tuyaItemPlus.setOnline(online);
        tuyaItemPlus.setNeedOnlineState(true);
        tuyaItemPlus.setFlagLoaded(true);
        changePluginState(isPanelDeviceOffline);
    }

    @Override
    public void updatePluginStateBlock(String pluginId) {
        if (TextUtils.isEmpty(pluginId) || null == tuyaItemPlus) {
            return;
        }

        if (!pluginId.equals(tuyaItemPlus.getId())) {
            return;
        }

        boolean haveApart = true;
        if (!CommonDataUtil.getInstance().isPluginCanReady2Arm(tuyaItemPlus.getStype())
                && !tuyaItemPlus.isNeedBlock()) {
            haveApart = false;
        }
        tuyaItemPlus.setHaveApart(haveApart);
        changePluginStateIcon();
    }

    @Override
    public void updatePluginStateDoorSensor(String pluginId, boolean isApart) {
        if (TextUtils.isEmpty(pluginId) || null == tuyaItemPlus) {
            return;
        }

        if (!pluginId.equals(tuyaItemPlus.getId())) {
            return;
        }
        DDLog.d(TAG, "updatePluginStateDoorSensor. pluginId: " + pluginId + "  isApart: " + isApart);
        tuyaItemPlus.setHaveApart(true);
        tuyaItemPlus.setApart(isApart);
        changePluginStateIcon();
    }

    @Override
    public void updatePluginLivingStateByPanelDevice(boolean panelDeviceOffline) {
        if (null == tuyaItemPlus) {
            return;
        }

        changePluginState(panelDeviceOffline);
    }

    @Override
    public void onLoadedInfoUpdate(String pluginId, boolean loaded) {
        if (TextUtils.isEmpty(pluginId) || null == tuyaItemPlus
                || !pluginId.equals(tuyaItemPlus.getId()) || null == itemHolder) {
            return;
        }

        tuyaItemPlus.setFlagLoaded(loaded);

        itemHolder.setRootViewVisible(true);
        itemHolder.setRootViewEnable(true);
        itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());

        if (tuyaItemPlus == null || !tuyaItemPlus.isFlagLoaded()) {
            // 切换家庭中先显示加载中
            getWidgetBean().setNullDevice(false);
            itemHolder.setStatusLoading(cacheName);
            return;
        }

        boolean isPanelDeviceOffline = !CommonDataUtil.getInstance().isPanelOnline();
        changePluginState(isPanelDeviceOffline);

        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 编辑模式
            itemHolder.setEditMode(true);
            itemHolder.setSelected(tuyaItemPlus.isShow());
        } else {
            // 非编辑模式
            itemHolder.setEditMode(false);
            changePluginStateIcon();
        }

        // 是否已被删除
        itemHolder.setRootViewDeleted(tuyaItemPlus.isFlagDeleted());
    }

    @Override
    public void onViewDeleted(String pluginId, boolean flagDeleted) {
        DDLog.d(TAG, "onViewDeleted: " + pluginId + " is deleted ? " + flagDeleted);
        if (TextUtils.isEmpty(pluginId) || null == tuyaItemPlus) {
            return;
        }

        if (!pluginId.equals(tuyaItemPlus.getId())) {
            return;
        }

        tuyaItemPlus.setFlagDeleted(flagDeleted);
        itemHolder.setRootViewDeleted(tuyaItemPlus.isFlagDeleted());
    }

    @Override
    public void onViewDeleted(String pluginId, String subcategory, boolean flagDeleted) {

    }

    @Override
    public void checkDeviceIsNull() {
        DDLog.d(TAG, "checkDeviceIsNull: tuyaItemPlus is null = " + (tuyaItemPlus == null));
        boolean isDeleted = false;
        if (null == tuyaItemPlus) {
            isDeleted = true;
        } else if (tuyaItemPlus.isFlagDeleted()) {
            isDeleted = true;
        }
        getWidgetBean().setNullDevice(isDeleted);
        itemHolder.setRootViewDeleted(isDeleted);
    }
}