package com.dinsafer.module.main.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.dinsafer.module.BaseFragment;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/6/20.
 */
public class CommonPagerAdapter extends FragmentStatePagerAdapter {

    private ArrayList<BaseFragment> mDatas;

    public CommonPagerAdapter(FragmentManager fm, ArrayList<BaseFragment> datas) {
        super(fm);
        mDatas = datas;
    }

    @Override
    public Fragment getItem(int position) {
        return mDatas.get(position);
    }

    public void remove(int i) {
        mDatas.remove(i);
        notifyDataSetChanged();
    }

    public void add(int index, BaseFragment fragment) {
        mDatas.add(index, fragment);
        notifyDataSetChanged();
    }


    @Override
    public int getCount() {
        return mDatas.size();
    }

}
