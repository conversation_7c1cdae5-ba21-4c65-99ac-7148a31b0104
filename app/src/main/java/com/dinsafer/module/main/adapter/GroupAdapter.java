package com.dinsafer.module.main.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.GroupListItemBinding;

import java.util.ArrayList;

import androidx.databinding.DataBindingUtil;

/**
 * Created by Rinfon on 16/6/29.
 */
public class GroupAdapter extends BaseAdapter {

    private Context mContext;

    private ArrayList<GroupItem> mData;

    public GroupAdapter(Context context, ArrayList<GroupItem> mData) {
        this.mContext = context;
        this.mData = mData;
    }

    public ArrayList<GroupItem> getData() {
        return mData;
    }

    public void setData(ArrayList mData) {
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int postion, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.group_list_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        holder.binding.groupListTitle.setText(mData.get(postion).getTitle());
        if (mData.get(postion).isSelect()) {
            holder.binding.groupListSelect.setVisibility(View.VISIBLE);
            holder.binding.groupListTitle.setTextColor(mContext.getResources().getColor(R.color.color_brand_text));
        } else {
            holder.binding.groupListSelect.setVisibility(View.INVISIBLE);
            holder.binding.groupListTitle.setTextColor(mContext.getResources().getColor(R.color.color_white_01));
        }
        return convertView;
    }

    public int getSelectedPosition() {
        int position = 0;
        if (mData != null) {
            for (int i = 0; i < mData.size(); i++) {
                GroupItem groupItem = mData.get(i);
                if (groupItem.isSelect()) {
                    position = i;
                    break;
                }
            }
        }
        return position;
    }

    static class ViewHolder {
        GroupListItemBinding binding;

        ViewHolder(View view) {
            binding = DataBindingUtil.bind(view);
        }
    }
}
