package com.dinsafer.module.main.model;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.MainPanelItemFunctionListBinding;
import com.dinsafer.model.panel.MainPanelFunctionItem;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.main.adapter.FunctionListAdapter;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.ui.anim.ShakeAnimUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PluginWidgetStyleUtil;

import java.util.ArrayList;

/**
 * @describe：常用工具
 * @date：2022/11/9
 * @author: create by Sydnee
 */
public class MainFunctionModel extends BaseMainItemModel<MainPanelItemFunctionListBinding> {

    private MainActivity mMainActivity;
    private Context mContext;
    private ArrayList<MainPanelFunctionItem> mData;
    private FunctionListAdapter functionAdapter;
    private OnWidgetItemListener onWidgetItemListener;

    public MainFunctionModel(Context context, MainActivity mainActivity, @NonNull MainWidgetBean bean, ArrayList<MainPanelFunctionItem> mData,
                             OnWidgetItemListener onWidgetItemListener) {
        super(context, bean, PluginWidgetStyleUtil.MODEL_ID_COMMON_FUNCTIONS_DEF, PluginWidgetStyleUtil.MODEL_TYPE_COMMON_FUNCTIONS_DEF, 0);
        this.mMainActivity = mainActivity;
        this.mContext = context;
        this.mData = mData;
        this.onWidgetItemListener = onWidgetItemListener;
    }

    @Override
    public int getLayoutID() {
        return R.layout.main_panel_item_function_list;
    }

    @Override
    public void convert(BaseViewHolder holder, MainPanelItemFunctionListBinding mBinding) {
        DDLog.d("MainFunctionModel", "convert: ");
        boolean editMode = MainPanelHelper.getInstance().isPanelEditMode();
        GridLayoutManager.LayoutParams rootLayoutParm = (GridLayoutManager.LayoutParams) mBinding.clRoot.getLayoutParams();
//        rootLayoutParm.topMargin = DensityUtils.dp2px(mMainActivity.getApplicationContext(), editMode ? 0 : 15);
//        rootLayoutParm.bottomMargin = DensityUtils.dp2px(mMainActivity.getApplicationContext(), editMode ? 0 : 30);
//        if (editMode) {
//            rootLayoutParm.height = DensityUtils.dp2px(mMainActivity.getApplicationContext(), 0);
//        } else {
//            rootLayoutParm.height = GridLayoutManager.LayoutParams.WRAP_CONTENT;
//        }
//        mBinding.clRoot.setLayoutParams(rootLayoutParm);
//        mBinding.clRoot.setVisibility(editMode ? View.GONE : View.VISIBLE);
//        mBinding.rvFunction.setOnTouchListener(new View.OnTouchListener() {
//            @Override
//            public boolean onTouch(View view, MotionEvent motionEvent) {
//                if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
//                    mBinding.clRoot.performLongClick();
//                }
//                return false;
//            }
//        });


        functionAdapter = new FunctionListAdapter(mMainActivity, new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                if (onWidgetItemListener != null) {
                    onWidgetItemListener.onItemLongClick(MainPanelHelper.getInstance().isPanelEditMode(), holder, holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
                }
                return true;
            }
        });
        mBinding.rvFunction.setLayoutManager(new LinearLayoutManager(mContext
                , LinearLayoutManager.HORIZONTAL, false));
        mBinding.rvFunction.setAdapter(functionAdapter);

        functionAdapter.setData(mData);
        functionAdapter.notifyDataSetChanged();

        if (editMode) {
            mBinding.rvFunction.postDelayed(() -> shakeView(mBinding.rvFunction), 50);
        }

        holder.itemView.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(@NonNull View view) {
                if (MainPanelHelper.getInstance().isPanelEditMode()) {
//                    ShakeAnimUtil.getInstance().shakeView(holder.itemView, true, ShakeAnimUtil.SMALL_TYPE);
                    holder.itemView.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            shakeView(mBinding.rvFunction);
                        }
                    }, 100);
                }
            }

            @Override
            public void onViewDetachedFromWindow(@NonNull View view) {

            }
        });
    }

    private void shakeView(RecyclerView recyclerView) {
        for (int i = 0; i < recyclerView.getChildCount(); i++) {
            View view = recyclerView.getChildAt(i);
            ShakeAnimUtil.getInstance().shakeView(view, true, ShakeAnimUtil.FUNCTION_TYPE);
        }
    }
}
