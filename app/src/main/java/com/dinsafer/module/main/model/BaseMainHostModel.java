package com.dinsafer.module.main.model;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.model.DeviceSimStatueEvent;
import com.dinsafer.model.panel.HostItemDelegate;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.device.DeviceStatusHelper;
import com.dinsafer.ui.device.MainDeviceStatusView;
import com.dinsafer.ui.device.ToolbarTabLayout;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PluginWidgetStyleUtil;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @describe：
 * @date：2022/11/21
 * @author: create by Sydnee
 */
public abstract class BaseMainHostModel extends BaseMainItemModel<ViewDataBinding> implements OnHostModelListener {
    private final int ARM_STATUS_UNKNOWN = -1;

    private final static String TAG = BaseMainHostModel.class.getSimpleName();
    private int layoutId;
    private OnWidgetItemListener onWidgetItemListener;
    private PanelDevice mPanelDevice;
    private Context mContext;
    private MainDeviceStatusView.OnDeviceStatusActionClickListener onDeviceStatusActionClickListener;

    private MainPanelArmDelayRecorder mArmDelayRecorder;

    /**
     * 当前正在进行的布撤防操作-发送了指令但还没有收到成功的回复
     */
    private final AtomicInteger mOperatingArmStatus = new AtomicInteger(ARM_STATUS_UNKNOWN);

    private final ToolbarTabLayout.OnOperateListener mOnOperateListener = (operation, clicked) -> {
        DDLog.d(TAG, this + "@onOperate " + operation + ",clicked: " + clicked);
        if (clicked) {
            if (null != mArmDelayRecorder) {
                mArmDelayRecorder.setOperatingStatus(operation);
            }
            mOperatingArmStatus.set(operation);
        } else {
            mOperatingArmStatus.set(ARM_STATUS_UNKNOWN);
        }
    };

    private final MainPanelArmDelayRecorder.OnArmDelayProgressUpdateListener mArmDelayUpdate = progress -> {
        DDLog.d(TAG, "onDeviceStatusActionClickListener, progress: " + progress);
        HostItemDelegate hostItemDelegate = getHostItemDelegate();
        if (null != hostItemDelegate) {
            updateArmCountDown(progress);
        }
    };

    public BaseMainHostModel(BaseFragment baseFragment, PanelDevice panelDevice, @Nullable MainPanelArmDelayRecorder armDelayRecorder, @NonNull MainWidgetBean bean, OnWidgetItemListener onWidgetItemListener
            , MainDeviceStatusView.OnDeviceStatusActionClickListener onDeviceStatusActionClickListener, String modelType, final int adapterItemId) {
        super(baseFragment.getContext(), bean, null != bean ? bean.getPluginId() : null, modelType, adapterItemId);
        this.mContext = baseFragment.getContext();
        this.mArmDelayRecorder = armDelayRecorder;
        this.mPanelDevice = panelDevice;
        this.layoutId = PluginWidgetStyleUtil.getInstance().getWidgetStyle(bean.getLayoutType());
        this.onWidgetItemListener = onWidgetItemListener;
        this.onDeviceStatusActionClickListener = onDeviceStatusActionClickListener;
        if (null != mArmDelayRecorder) {
            mArmDelayRecorder.addArmDelayProgressUpdateListener(mArmDelayUpdate);
        }
    }

    public abstract HostItemDelegate getHostItemDelegate();


    @Override
    public int getLayoutID() {
        return layoutId;
    }

    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding viewDataBinding) {
    }

    protected void initView(BaseViewHolder holder) {
        DDLog.d(TAG, "initView.");
        getHostItemDelegate().setRootViewEnable(
                MainPanelHelper.getInstance().isPanelEditMode()
                        || MainPanelHelper.getInstance().isFunctionEnable());
        getHostItemDelegate().setRootViewVisible(true);
        getHostItemDelegate().setEditMode(MainPanelHelper.getInstance().isPanelEditMode());

        initListener(holder);

        if (mPanelDevice == null || !mPanelDevice.getFlagLoaded()) {
            getHostItemDelegate().changeDeviceStateLoading(false);

            if (getWidgetBean().isNullDevice()) {
                getHostItemDelegate().changeDeviceStateDeleted(true);
            }
            return;
        }

        getHostItemDelegate().updateUI();

        boolean online = CommonDataUtil.getInstance().isPanelOnline();
        boolean upgrading = DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.UPGRADING, false);
        if (!online) {
            // 不管是否有卡，都显示带有离线模式按钮的离线页
            changeDeviceStateErrorHadSim();
        } else {
            if (upgrading) {
                changeDeviceStatePanelUpgrading();
            } else {
                int armStatus = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.ARM_STATUS, 0);
                int network = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.NET_TYPE, 0);
                int sim = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.SIM_STATUS, 0);
                int batteryLevel = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.BATTERY_LEVEL, 0);
                int rssi = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.WIFI_RSSI, 3);
                boolean isCharge = DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.IS_CHARGE, false);
                changeDeviceStateNormal(network, sim, rssi, isCharge, batteryLevel, armStatus);
            }
        }

        int permission = HomeManager.getInstance().getCurrentHome().getLevel();
        changeDeviceToolbarStateByUserPermission(LocalKey.GUEST != permission);

        boolean deletedState = mPanelDevice.getFlagDeleted();
        getHostItemDelegate().changeDeviceStateDeleted(deletedState);

        resumeOperatingStatus();

        // 当有主机时，ToolbarTabLayout必须先调用setToolbarStatus修改Toolbar的状态，编辑状态下，ToolbarTabLayout点击无效
        getHostItemDelegate().setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
    }

    /**
     * 恢复之前延时布防或者正在操作的布撤防状态-列表上下滚动后被回收了，所以需要进行恢复的处理
     */
    private void resumeOperatingStatus() {
        if (!MainPanelHelper.getInstance().isPanelEditMode()) {
            int armDelaySecond = -1;
            int operatingStatus = ARM_STATUS_UNKNOWN;
            if (null != mArmDelayRecorder) {
                armDelaySecond = mArmDelayRecorder.getArmDelaySecond();
                operatingStatus = mArmDelayRecorder.getOperatingStatus();
            }
            final int localOperatingStatus = mOperatingArmStatus.get();
            if (armDelaySecond > 0) {
                // 正在延时布防倒数
                getHostItemDelegate().setOperateLoadingArmDelay(armDelaySecond);
            } else if (ARM_STATUS_UNKNOWN != operatingStatus
                    && localOperatingStatus == operatingStatus) {
                // 正在执行默一布撤防操作
                getHostItemDelegate().setOperateLoadingState(operatingStatus);
            }
        }
    }

    private void initListener(BaseViewHolder holder) {
        DeviceCallBackManager.getInstance().addOnHostModelListener(this);

        getHostItemDelegate().setOnDeviceStatusActionClickListener(onDeviceStatusActionClickListener);

        getHostItemDelegate().setRootViewLongClickListener(view -> {
            if (null != onWidgetItemListener) {
                onWidgetItemListener.onItemLongClick(MainPanelHelper.getInstance().isPanelEditMode(), holder, holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
            }
            return true;
        });

        getHostItemDelegate().setDeleteIconClickListener(view -> {
            if (null == onWidgetItemListener || !MainPanelHelper.getInstance().isPanelEditMode()) {
                return;
            }
            DeviceCallBackManager.getInstance().removeOnHostModelListener(this);
            onWidgetItemListener.onDeleteIconClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });

        getHostItemDelegate().setRootViewDeleteStateClickListener(view -> {
            if (null == onWidgetItemListener) {
                DDLog.e(TAG, "当前Item没有对应的device，不能点击");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            onWidgetItemListener.onUnavailableStateViewClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });

        getHostItemDelegate().setOperateListener(mOnOperateListener);
    }


    @Override
    public void onViewDeleted(String pluginId, boolean flagDeleted) {
        if (TextUtils.isEmpty(pluginId) || null == mPanelDevice) {
            return;
        }

        if (!pluginId.equals(mPanelDevice.getId())) {
            return;
        }
        DDLog.d(TAG, "onViewDeleted. pluginId: " + pluginId + " flagDeleted:" + flagDeleted);
        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 退出编辑模式再更新UI
            return;
        }
        getHostItemDelegate().changeDeviceStateDeleted(mPanelDevice.getFlagDeleted());
    }

    @Override
    public void onViewDeleted(String pluginId, String subcategory, boolean flagDeleted) {

    }

    @Override
    public void checkDeviceIsNull() {
        DDLog.d(TAG, "checkDeviceIsNull. PanelDevice is null ？" + (null == mPanelDevice));
        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 退出编辑模式再更新UI
            return;
        }
        boolean isDeleted = false;
        if (null == mPanelDevice) {
            isDeleted = true;
        } else if (mPanelDevice.getFlagDeleted()) {
            isDeleted = true;
        }
        getWidgetBean().setNullDevice(isDeleted);
        getHostItemDelegate().changeDeviceStateDeleted(isDeleted);
    }

    /**
     * 更新ping值
     */
    @Override
    public void updatePing(int netType, int sim, int rssi, boolean isCharge, int batteryLevel) {
        getHostItemDelegate().refreshPing(netType, sim, rssi, isCharge, batteryLevel);
    }

    @Override
    public void updateSimStatus(DeviceSimStatueEvent event) {
        getHostItemDelegate().refreshSimStatus(event);
    }

    @Override
    public void updatePowerCallBackAction() {
        DDLog.d(TAG, "updatePowerCallBackAction");
        getHostItemDelegate().refreshBatteryStatus(DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.IS_CHARGE, false),
                DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.BATTERY_LEVEL, 0));
    }

    @Override
    public void initDeviceStatus(int status) {
        getHostItemDelegate().initDeviceToolbarStatus(status);
    }

    /**
     * 布防
     */
    @Override
    public void toArm() {
        getHostItemDelegate().toolBarToArm();
    }

    /**
     * 撤防
     */
    @Override
    public void toDisArm() {
        getHostItemDelegate().toolBarToDisArm();
    }

    @Override
    public void toResetSos() {
        getHostItemDelegate().toolBarToResetSos();
    }

    @Override
    public void toInitArm() {
        DDLog.d(TAG, "toInitArm");
        getHostItemDelegate().toolBarInitArm(false);
    }

    @Override
    public void toInitDisArm() {
        DDLog.d(TAG, "toInitDisArm");
        getHostItemDelegate().toolBarInitDisArm();
    }

    @Override
    public void toInitHomeArm() {
        DDLog.d(TAG, "toInitHomeArm");
        getHostItemDelegate().toolBarInitHomeArm();
    }

    @Override
    public void playSound(int soundID, int leftVolume, int rightVolume, int priority, int loop, int rate) {
        getHostItemDelegate().toolBarPlaySound(soundID, leftVolume, rightVolume, priority, loop, rate);
    }

    @Override
    public void changeDeviceStateErrorHadSim() {
        getHostItemDelegate().changeDeviceStateErrorHadSim();
    }

    @Override
    public void changeDeviceStateNormal(int network, int sim, int rssi, boolean isCharge, int batteryLevel, int armStatus) {
        DDLog.d(TAG, "changeDeviceStateNormal：" + network + " " + rssi + " " + isCharge + " " + batteryLevel + " " + armStatus);
        getHostItemDelegate().changeDeviceStateNormal(network, sim, rssi, isCharge, batteryLevel, armStatus);
    }

    @Override
    public void changeDeviceStateOfflineMode() {
        getHostItemDelegate().changeDeviceStateOfflineMode();
    }

    @Override
    public void changeDeviceStatePanelUpgrading() {
        getHostItemDelegate().changeDeviceStatePanelUpgrading();
    }

    /**
     * 显示紧急报警
     *
     * @param showSos
     */
    @Override
    public void changeDeviceToolbarStateByUserPermission(boolean showSos) {
        getHostItemDelegate().toolBarSetSos(showSos);
    }

    /**
     * 延迟布防
     *
     * @param exitDelay
     */
    @Override
    public void onOperationArmAck(int exitDelay) {
        DDLog.i(TAG, "onOperationArmAck, exitDelay: " + exitDelay);
        // 其他人布防，也需要标记在
        mOperatingArmStatus.set(DeviceStatusHelper.ARM_STATUS_ARM);
        getHostItemDelegate().operationArmAck(exitDelay);
    }

    @Override
    public void updateArmCountDown(int seconds) {
        DDLog.d(TAG, "updateArmCountDown: " + seconds);
        getHostItemDelegate().updateArmCountDown(seconds);
    }

    @Override
    public void onLoadedInfoUpdate() {
        getHostItemDelegate().setRootViewEnable(
                MainPanelHelper.getInstance().isPanelEditMode()
                        || MainPanelHelper.getInstance().isFunctionEnable());
        getHostItemDelegate().setRootViewVisible(true);
        getHostItemDelegate().setEditMode(MainPanelHelper.getInstance().isPanelEditMode());

        if (mPanelDevice == null || !mPanelDevice.getFlagLoaded()) {
            getHostItemDelegate().changeDeviceStateLoading(false);

            if (getWidgetBean().isNullDevice()) {
                getHostItemDelegate().changeDeviceStateDeleted(true);
            }
            return;
        }

        getHostItemDelegate().updateUI();

        boolean online = CommonDataUtil.getInstance().isPanelOnline();
        boolean upgrading = DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.UPGRADING, false);
        if (!online) {
            // 不管是否有卡，都显示带有离线模式按钮的离线页
            changeDeviceStateErrorHadSim();
        } else {
            if (upgrading) {
                changeDeviceStatePanelUpgrading();
            } else {
                int armStatus = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.ARM_STATUS, 0);
                int network = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.NET_TYPE, 0);
                int sim = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.SIM_STATUS, 0);
                int batteryLevel = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.BATTERY_LEVEL, 0);
                int rssi = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.WIFI_RSSI, 3);
                boolean isCharge = DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.IS_CHARGE, false);
                changeDeviceStateNormal(network, sim, rssi, isCharge, batteryLevel, armStatus);
            }
        }

        int permission = HomeManager.getInstance().getCurrentHome().getLevel();
        changeDeviceToolbarStateByUserPermission(LocalKey.GUEST != permission);

        boolean deletedState = mPanelDevice.getFlagDeleted();
        getHostItemDelegate().changeDeviceStateDeleted(deletedState);

        // 当有主机时，ToolbarTabLayout必须先调用setToolbarStatus修改Toolbar的状态，编辑状态下，ToolbarTabLayout点击无效
        getHostItemDelegate().setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
    }
}
