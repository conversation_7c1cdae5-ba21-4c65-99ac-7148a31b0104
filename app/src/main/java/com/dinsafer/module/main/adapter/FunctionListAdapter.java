package com.dinsafer.module.main.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.caremode.CareModeFragment;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.IPCKey;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dscam.timeline.MotionRecordTimelinePlayerActivity;
import com.dinsafer.model.panel.MainPanelFunctionItem;
import com.dinsafer.model.panel.MainPanelFunctionItemViewHolder;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.iap.PrimeServicesFragment;
import com.dinsafer.module.iap.powercare.BmtPrimeServicesFragment;
import com.dinsafer.module.main.view.EventListFragment;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.powerstation.gridrewards.GridRewardsFragment;
import com.dinsafer.module.powerstation.gridrewards.GridRewardsNotFirstFragment;
import com.dinsafer.module.settting.ui.IPCSosRecordListFragment;
import com.dinsafer.module.settting.ui.SimplePlugsListFragment;
import com.dinsafer.module_base.smartwidget.SmartWidgetBuilder;
import com.dinsafer.module_base.smartwidget.apis.ISmartWidget;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.therouter.TheRouter;

import java.util.ArrayList;

/**
 * @describe：
 * @date：2022/11/10
 * @author: create by Sydnee
 */
public class FunctionListAdapter extends RecyclerView.Adapter<MainPanelFunctionItemViewHolder> {

    private final static String TAG = FunctionListAdapter.class.getSimpleName();
    private ArrayList<MainPanelFunctionItem> mData = new ArrayList<>();
    private View itemView;
    private Context mContext;
    private MainActivity mMainActivity;
    private View.OnLongClickListener longClickListener;

    public FunctionListAdapter(MainActivity mainActivity, View.OnLongClickListener listener) {
        this.mMainActivity = mainActivity;
        longClickListener = listener;
    }

    public void setData(ArrayList<MainPanelFunctionItem> mData) {
        this.mData = mData;
    }

    @NonNull
    @Override
    public MainPanelFunctionItemViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        this.mContext = viewGroup.getContext();
        itemView = LayoutInflater.from(mContext).inflate(R.layout.main_function_item, viewGroup, false);
        return new MainPanelFunctionItemViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull MainPanelFunctionItemViewHolder itemHolder, int position) {

        if (position >= mData.size()) {
            // 空白填充
            itemHolder.setBackgroundColor(mContext.getResources().getColor(R.color.bgColorPage));
            itemHolder.setViewDataEmpty(true);
            itemHolder.setRootViewEnable(true);
            itemHolder.setRootViewVisible(false);
            return;
        }
        itemHolder.setRootViewVisible(true);

        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) itemHolder.itemView.getLayoutParams();
        layoutParams.leftMargin = DensityUtil.dp2px(mContext, position == 0 ? 18 : 5);
        layoutParams.rightMargin = DensityUtil.dp2px(mContext, position == mData.size() - 1 ? 18 : 5);
        itemHolder.itemView.setLayoutParams(layoutParams);

        MainPanelFunctionItem itemData = mData.get(position);

        itemHolder.setRootViewClickListener((View v) -> {
            if (null == mMainActivity) {
                DDLog.e(TAG, "MainActivity is empty.");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑状态下不能跳转.");
                return;
            }

            MainActivity mainActivity = mMainActivity;

            switch (itemData.getType()) {
                case MainPanelHelper.FUNCTION_TYPE_EXTRA_SECURITY:
                    if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE) {
                        mainActivity.addCommonFragment(PrimeServicesFragment.newInstance());
                    } else if (AppConfig.Functions.SUPPORT_TRAFFIC_PACKAGE_SERVICE) {
                        mainActivity.addCommonFragment(BmtPrimeServicesFragment.newInstance());
                    }
                    break;
                case MainPanelHelper.FUNCTION_TYPE_SMART_WIDGET:
                    toSmartWidget();
                    break;
                case MainPanelHelper.FUNCTION_TYPE_SMART_BUTTON:
                    mainActivity.addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.SMART_BUTTON
                            , mainActivity.getResources().getString(R.string.smart_button_name)));
                    break;
                case MainPanelHelper.FUNCTION_TYPE_CARE_MODE:
                    mainActivity.addCommonFragment(CareModeFragment.newInstance());
                    break;
                case MainPanelHelper.FUNCTION_TYPE_VIDEO_LIST:
                    mainActivity.addCommonFragment(IPCSosRecordListFragment.newInstance());
                    break;
                case MainPanelHelper.FUNCTION_TYPE_VIDEO_TIME_LINE:
                    MotionRecordTimelinePlayerActivity.start(mainActivity);
                    mainActivity.setNotNeedToLogin(true);
                    break;
                case MainPanelHelper.FUNCTION_TYPE_ADD_EVENT_LIST:
                    mainActivity.addCommonFragment(EventListFragment.newInstance());
                    break;
                case MainPanelHelper.FUNCTION_TYPE_GRID_REWARDS:
                    getFamilyBalanceContractInfo(position);
                    break;
                default:
                    break;
            }
        });
        itemHolder.setRootViewLongClickListener(longClickListener);

        // 数据填充
        itemHolder.setViewDataEmpty(false);
        itemHolder.setRootViewEnable(getViewEnableState(itemData.getType()));
        itemHolder.setNormalData(itemData.getName(), itemData.getIconResId(),
                mContext.getResources().getColor(itemData.getEnableBgColorResId()));
    }

    private void getFamilyBalanceContractInfo(int position) {
        mMainActivity.showTimeOutLoadinFramgment();
        DinHome.getInstance().getFamilyBalanceContractInfo(HomeManager.getInstance().getCurrentHome().getHomeID(),
                new IDefaultCallBack2<>() {
                    @Override
                    public void onSuccess(FamilyBalanceContractInfoResponse.ResultBean resultBean) {
                        mMainActivity.closeLoadingFragment();
                        if (null != resultBean && resultBean.isSigning()) {
                            mMainActivity.addCommonFragment(GridRewardsNotFirstFragment.newInstance(mData.get(position).getBmtRegionCountries(), resultBean));
                        } else {
                            mMainActivity.addCommonFragment(GridRewardsFragment.newInstance(mData.get(position).getBmtRegionCountries(), resultBean));
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        mMainActivity.closeLoadingFragment();
                        mMainActivity.showErrorToast();
                    }
                });
    }


    @Override
    public int getItemCount() {
        return mData.size();
    }

    /**
     * 跳转SmartWidget页
     */
    private void toSmartWidget() {
        if (null == mMainActivity) {
            return;
        }

        if (!APIKey.IS_OPEN_PLUGIN) {
            DDLog.e(TAG, "APIKey.IS_OPEN_PLUGIN is false.");
            return;
        }

        mMainActivity.setNotNeedToLogin(true);
        TheRouter.get(ISmartWidget.class).start(mMainActivity,
                new SmartWidgetBuilder()
                        .setAppId(APIKey.APP_ID)
                        .setHomeId(HomeManager.getInstance().getCurrentHome().getHomeID())
                        .setDeviceId(CommonDataUtil.getInstance().getCurrentPanelID())
                        .setUserToken(DinSDK.getUserInstance().getUser().getToken())
                        .setIp(DinSaferApplication.getHttpdns().getIpByHostAsync(APIKey.SERVER_IP))
                        .setDomain(APIKey.SERVER_IP)
                        .setConfigAssertFileName("widgetConfig.json"));
    }

    /**
     * 获取Item是否可以被点击的状态
     *
     * @param type
     * @return
     */
    private boolean getViewEnableState(@MainPanelHelper.PanelFunctionType int type) {
        // 编辑模式下，都不能点击
//        if (MainPanelHelper.getInstance().isPanelEditMode()) {
//            return false;
//        }

        boolean enable;
        switch (type) {
            case MainPanelHelper.FUNCTION_TYPE_EXTRA_SECURITY:
            case MainPanelHelper.FUNCTION_TYPE_VIDEO_LIST:
            case MainPanelHelper.FUNCTION_TYPE_VIDEO_TIME_LINE:
            case MainPanelHelper.FUNCTION_TYPE_ADD_EVENT_LIST:
            case MainPanelHelper.FUNCTION_TYPE_GRID_REWARDS:
                enable = true;
                break;
            case MainPanelHelper.FUNCTION_TYPE_SMART_WIDGET:
            case MainPanelHelper.FUNCTION_TYPE_SMART_BUTTON:
            case MainPanelHelper.FUNCTION_TYPE_CARE_MODE:
            default:
                enable = MainPanelHelper.getInstance().isFunctionEnable();
                break;

        }
        return enable;
    }


}
