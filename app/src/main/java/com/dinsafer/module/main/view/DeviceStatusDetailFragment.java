package com.dinsafer.module.main.view;

import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.RelativeLayout;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDeviceStatusDetailBinding;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.SimDataEntry;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.AccessoryStatusItemView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;
import com.dinsafer.util.RandomStringUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2019/3/22
 */
public class DeviceStatusDetailFragment extends MyBaseFragment<FragmentDeviceStatusDetailBinding> implements IDeviceCallBack {

    /**
     * 主机SIN卡
     */
    private static final int SIN_PIN_ERROR_CODE = 3;

    private SimDataEntry.ResultBean simData;

    Animation mRotateAnim;
    private Device mPanelDevice;

    public static DeviceStatusDetailFragment newInstance() {

        Bundle args = new Bundle();

        DeviceStatusDetailFragment fragment = new DeviceStatusDetailFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        initSimCard();
        initPower();
        initNetwork();
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_device_status_detail;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        showTimeOutLoadinFramgment();
        findPanel();

        mBinding.title.commonBarTitle.setLocalText(getString(R.string.device_managent_panel_status));
        mBinding.advancedSettingLabel.setLocalText(getString(R.string.network_status));
        mBinding.advancedSettingNet.setLocalText(getString(R.string.advanced_setting_current_net));
        mBinding.tvIp.setLocalText(getString(R.string.ip_address));
        mBinding.tvBattery.setLocalText(getString(R.string.battery_quantity));
        mBinding.tvSim.setLocalText(getString(R.string.sim_card));
        mBinding.tvSimStatus.setLocalText(getString(R.string.current_status));
        mBinding.tvSinPinErrorHint.setLocalText(getString(R.string.device_sin_pin_error_hint));
        mBinding.tvWifiSignal.setLocalText(getString(R.string.wifi_signal));
        mBinding.tvWifiAddress.setLocalText(getString(R.string.mac_address));

        mBinding.title.commonBarBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDelegateActivity().removeCommonFragmentAndData(DeviceStatusDetailFragment.this, true);
            }
        });

        mBinding.tvSimStatusInfo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                tiggleSimWrongLayoutVisibility();
            }
        });

        mBinding.ivTabIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                tiggleSimWrongLayoutVisibility();
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinding.accessoryStatusLoading.clearAnimation();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        EventBus.getDefault().unregister(this);
    }

    /**
     * 初始化loading的动画
     */
    private void initLoadingAnim() {
        mRotateAnim = AnimationUtils.loadAnimation(DinSaferApplication.getAppContext(), R.anim.rotation);
        LinearInterpolator lin = new LinearInterpolator();
        mRotateAnim.setInterpolator(lin);
    }

    private void initAccessoryStatus() {
        DDLog.i(TAG, "initAccessoryStatus");
        initLoadingAnim();
        changeAccessoryStatusToLoading();
        requestAccessoryStatus();
    }

    /**
     * TODO 请求服务器获取配置转态信息
     */
    private void requestAccessoryStatus() {
        DDLog.i(TAG, "requestAccessoryStatus");
        final String uid = CommonDataUtil.getInstance().getUserUid();
        final String panelToken = DeviceHelper.getString(DinSDK.getHomeInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID()),
                PanelDataKey.Panel.DEVICE_TOKEN, null);
        if (TextUtils.isEmpty(uid) || TextUtils.isEmpty(panelToken)) {
            DDLog.w(TAG, "Empty uid or panel token");
            onRequestError();
            return;
        }
        DinsafeAPI.getApi().getHomeExceptionAccessoryInfo(RandomStringUtils.getMessageId(),
                        panelToken,uid)
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "onResponse");
                        if (1 != response.body().getStatus()) {
                            onRequestError();
                        }
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        DDLog.e(TAG, "onFailure, message: " + t.getMessage());
                        onRequestError();
                    }
                });
    }

    /**
     * TODO 处理获取配件信息错误的情况
     * 目前出错时显示成没有配件异常
     */
    private void onRequestError() {
        DDLog.i(TAG, "onRequestError");
        changeAccessoryStatusToNormal();
    }

    /**
     * 修改页面转态至加载异常配件中
     */
    private void changeAccessoryStatusToLoading() {
        DDLog.i(TAG, "changeAccessoryStatusToLoading");
        mBinding.llAccessoryContainer.setVisibility(View.GONE);
        mBinding.rlAccessoryNormal.setVisibility(View.VISIBLE);
        mBinding.tvAccessoryStatusValue.setVisibility(View.GONE);
        mBinding.accessoryStatusLoading.setVisibility(View.VISIBLE);
        mBinding.accessoryStatusLoading.startAnimation(mRotateAnim);
    }

    /**
     * 切换到所有配件都正常
     */
    private void changeAccessoryStatusToNormal() {
        DDLog.i(TAG, "changeAccessoryStatusToLoading");
        mBinding.llAccessoryContainer.setVisibility(View.GONE);
        mBinding.rlAccessoryNormal.setVisibility(View.VISIBLE);
        mBinding.tvAccessoryStatusValue.setVisibility(View.VISIBLE);
        mBinding.accessoryStatusLoading.setVisibility(View.GONE);
        mBinding.accessoryStatusLoading.clearAnimation();
    }

    /**
     * 显示错误配件列表
     * 仅仅显示错误的视图，更新数据需要调用{@link #updateAccessoryErrorList()}
     */
    private void changeAccessoryStatusToError() {
        DDLog.i(TAG, "changeAccessoryStatusToError");
        mBinding.accessoryStatusLoading.clearAnimation();
        mBinding.rlAccessoryNormal.setVisibility(View.GONE);
        mBinding.llAccessoryContainer.setVisibility(View.VISIBLE);
    }

    /**
     * 更新配件异常信息列表
     */
    private void updateAccessoryErrorList() {
        DDLog.i(TAG, "updateAccessoryErrorList");
        mBinding.llAccessoryContainer.removeAllViews();

        String localLowBattery = Local.s(getResources().getString(R.string.device_status_accessory_low_battery));
        String localApart = Local.s(getResources().getString(R.string.device_status_accessory_apart));
        String localLowBatteryApart = localLowBattery + " " + localApart;

        AccessoryStatusItemView child;
        // TODO 更新配件个数 配件情况异常，列出异常配件项
        int length = 1;
        // 只有一个配件
        if (1 == length) {
            child = new AccessoryStatusItemView(getContext());
            // TODO 更新展示内容
            child.setTitleContent("aaa", localLowBattery);
            mBinding.llAccessoryContainer.addView(child);
            return;
        }

        // 有一个以上配件
        for (int i = 0; i < length; i++) {
            child = new AccessoryStatusItemView(getContext());
            // TODO 更新展示内容
            child.setTitleContent("title: " + i, localLowBatteryApart);
            if (0 == i) {
                // 顶部
                child.setItemViewType(AccessoryStatusItemView.TYPE_TOP);
            } else if (length - 1 == i) {
                // 底部
                child.setItemViewType(AccessoryStatusItemView.TYPE_BOTTOM);
            } else {
                // 中间
                child.setItemViewType(AccessoryStatusItemView.TYPE_MID);
            }
            mBinding.llAccessoryContainer.addView(child);
        }
    }

    private void initNetwork() {
        int netType = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.NET_TYPE, 0);
        String ssid = DeviceHelper.getString(mPanelDevice, PanelDataKey.Panel.SSID, "");
        mBinding.advancedSettingNetName.setText(DeviceInfoHelper.getInstance().geNetworkName(getContext(), netType, ssid));
        mBinding.tvIpAddress.setText(DeviceHelper.getString(mPanelDevice, PanelDataKey.Panel.LAN_IP, ""));

        // wifi 显示信号强度和MAC地址
        if (1 == netType) {
            mBinding.rlWifiAddress.setVisibility(View.VISIBLE);
            mBinding.rlWifiSignal.setVisibility(View.VISIBLE);

            int signal = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.WIFI_RSSI, 0);
            String macAddress = DeviceHelper.getString(mPanelDevice, PanelDataKey.Panel.WIFI_MAC_ADDRESS, "");
            mBinding.tvWifiSignalName.setText(String.valueOf(signal));
            mBinding.tvWifiAddressName.setText(macAddress);
        } else {
            mBinding.rlWifiAddress.setVisibility(View.GONE);
            mBinding.rlWifiSignal.setVisibility(View.GONE);
        }
    }

    private void initPower() {
        mBinding.tvBatteryStatus.setLocalText(getString(DeviceInfoHelper.getInstance().getBatteryStatusText(
                DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.IS_CHARGE, false))));
        int batteryLevel = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.BATTERY_LEVEL, 0);
        String value = batteryLevel > 0 ? batteryLevel + "%" : "-";
        mBinding.tvBatteryQuantity.setText(value);
    }

    private void initSimCard() {
        int sim = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.SIM_STATUS, 0);
        int pinErrorVisible = DeviceInfoHelper.getInstance().isSimPinError(sim) ? View.VISIBLE : View.GONE;
        mBinding.tvSinPinErrorHint.setVisibility(pinErrorVisible);
        mBinding.tvSimStatusInfo.setLocalText(getString(DeviceInfoHelper.getInstance().getSimStatusText(sim)));
        if (DeviceInfoHelper.getInstance().isSimWrong(sim)) {
            mBinding.tvSimStatusInfo.setEnabled(true);
            mBinding.ivTabIcon.setVisibility(View.VISIBLE);
            mBinding.layoutSimWrong.tvImei.setLeftText(Local.s(getResources().getString(R.string.imei_code)));
            mBinding.layoutSimWrong.tvImsi.setLeftText(Local.s(getResources().getString(R.string.imsi_code)));
            mBinding.layoutSimWrong.tvSemaphore.setLeftText(Local.s(getResources().getString(R.string.semaphore)));
//            mBinding.layoutSimWrong.tvRegisterStatus.setLeftText(R.string.register_status);
            mBinding.layoutSimWrong.tvPin.setLeftText(Local.s(getResources().getString(R.string.pin_status)));
//            mBinding.layoutSimWrong.tvCops.setLeftText(R.string.cops);

            getSimData();
        } else {
            mBinding.tvSimStatusInfo.setEnabled(false);
            mBinding.layoutSimWrong.getRoot().setVisibility(View.GONE);
            mBinding.ivTabIcon.setVisibility(View.GONE);
            closeLoadingFragment();
        }
    }

    private void tiggleSimWrongLayoutVisibility() {
        mBinding.layoutSimWrong.getRoot().setVisibility(mBinding.layoutSimWrong.getRoot().getVisibility() == View.GONE ? View.VISIBLE : View.GONE);
        if (mBinding.layoutSimWrong.getRoot().getVisibility() == View.VISIBLE) {
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mBinding.vLine.getLayoutParams();
            layoutParams.leftMargin = DensityUtils.dp2px(getContext(), 15);
            mBinding.vLine.setLayoutParams(layoutParams);

            ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(mBinding.ivTabIcon, "rotation", 0, 180);
            objectAnimator.setDuration(200);
            objectAnimator.start();
        } else {
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mBinding.vLine.getLayoutParams();
            layoutParams.leftMargin = 0;
            mBinding.vLine.setLayoutParams(layoutParams);

            ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(mBinding.ivTabIcon, "rotation", 180, 0);
            objectAnimator.setDuration(200);
            objectAnimator.start();
        }
    }


    private void getSimData() {
        if (null == mPanelDevice) {
            DDLog.e(TAG, "Empty panel device.");
            closeLoadingFragment();
            showErrorToast();
            return;
        }

        showLoadingFragment(0);
        mPanelDevice.submit(PanelParamsHelper.getSimCardInfo());
    }

    private void setSimData(SimDataEntry.ResultBean simData) {
        if (simData != null) {
            DDLog.d(TAG, "setSimData: " + simData.toString());
            mBinding.layoutSimWrong.tvImei.setRightText(simData.getImei());
            mBinding.layoutSimWrong.tvImsi.setRightText(simData.getImsi());
            mBinding.layoutSimWrong.tvPin.setRightText(simData.getPin());
            mBinding.layoutSimWrong.tvSemaphore.setRightIcon(simData.getCsqIcon());
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceResultEvent ev) {
        if (LocalKey.GET_HOME_EXCEPTION_ACCESSORY.equals(ev.getCmdType())) {
            DDLog.i(TAG, "On device result, CMD: " + LocalKey.GET_HOME_EXCEPTION_ACCESSORY);
            // 处理获取异常配件信息结果
            if (1 == ev.getStatus()) {
                // TODO 成功拿到配件状态，根据是否有异常配件显示异常配件列表或配件转态正常
            } else {
                DDLog.e(TAG, "On device result error, result:" + ev.getReslut());
                onRequestError();
            }
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice
                || !deviceId.equals(mPanelDevice.getId())) {
            return;
        }

        final int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, -1);
        if (PanelCmd.GET_SIM_CARD_INFO.equals(cmd)) {
            DDLog.i(TAG, "获取到Sim卡信息");
            closeLoadingFragment();
            if (PanelDataKey.CmdResult.SUCCESS == status) {
                Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
                simData = new SimDataEntry.ResultBean();
                simData.setD_phone(DeviceHelper.getString(resultMap, PanelDataKey.SimInfo.D_PHONE, ""));
                simData.setPin(DeviceHelper.getString(resultMap, PanelDataKey.SimInfo.PIN, ""));
                simData.setImei(DeviceHelper.getString(resultMap, PanelDataKey.SimInfo.IMEI, ""));
                simData.setImsi(DeviceHelper.getString(resultMap, PanelDataKey.SimInfo.IMSI, ""));
                simData.setCsq(DeviceHelper.getString(resultMap, PanelDataKey.SimInfo.CSQ, ""));
                setSimData(simData);
            }
        }


    }
}
