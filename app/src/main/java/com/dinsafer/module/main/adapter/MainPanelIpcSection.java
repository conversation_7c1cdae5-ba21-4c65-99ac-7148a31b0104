package com.dinsafer.module.main.adapter;

import android.content.Context;

import androidx.recyclerview.widget.RecyclerView;

import android.view.View;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;

import java.util.ArrayList;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;

/**
 * 首页Panel Camera 分组
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/24 5:02 PM
 */
public class MainPanelIpcSection extends MainPanelBaseSection<Device> {
    private final int ROW_ITEM_COUNT = 1;

    public MainPanelIpcSection(Context context, String tittle, ArrayList<Device> datas) {
        super(context, SectionParameters.builder()
                .itemResourceId(R.layout.main_section_panel_item_ipc)
                .headerResourceId(R.layout.main_section_panel_item_header)
                .build(), tittle, datas);
    }

    @Override
    public int getRowItemCount() {
        return ROW_ITEM_COUNT;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        MainPanelIpcItemViewHolder viewHolder = new MainPanelIpcItemViewHolder(view, -1);
        return viewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        MainPanelIpcItemViewHolder itemHolder = (MainPanelIpcItemViewHolder) holder;
        itemHolder.bindCurrentIndex(position);
        itemHolder.setIpcData(mData.get(position));
    }

}
