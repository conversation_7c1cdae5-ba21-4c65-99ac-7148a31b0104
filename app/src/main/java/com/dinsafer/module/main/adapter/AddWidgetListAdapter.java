package com.dinsafer.module.main.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.common.Constants;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.main.entry.AddWidgetItemBean;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2022/10/28
 * @author: create by Sydnee
 */
public class AddWidgetListAdapter extends RecyclerView.Adapter<AddWidgetListAdapter.ViewHolder> {

    private List<AddWidgetItemBean> mData = new ArrayList<>();
    private OnItemClickListener onItemClickListener;


    public void setData(List<AddWidgetItemBean> mData) {
        this.mData = mData;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_add_widget_list, viewGroup, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        AddWidgetItemBean itemBean = mData.get(i);
        String name = itemBean.getName();
        if (TextUtils.isEmpty(name)) {
            viewHolder.devicePlugName.setText("");
        } else {
            viewHolder.devicePlugName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
        }
        viewHolder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClickListener(i);
            }
        });
    }


    @Override
    public int getItemCount() {
        return mData.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private TextView devicePlugName;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            devicePlugName = itemView.findViewById(R.id.device_plug_name);
        }
    }

    public interface OnItemClickListener {
        void onItemClickListener(int position);
    }
}
