package com.dinsafer.module.main.view;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.UnCloseDoorEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.adapter.ReadyToArmDialogAdapter;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;

import java.util.ArrayList;
import java.util.Map;

/**
 * Created by LT on 2018/8/26.
 */
public class ReadyToArmDialogFragment extends BaseFragment
        implements IDeviceCallBack {

    LocalTextView title;
    View line;
    LocalTextView readyToArmHint;
    LocalCustomButton btnExecute;
    LocalCustomButton btnCancal;
    LocalCustomButton btnKnow;
    RecyclerView notClosedDoorRecyclerView;

    private int view_status;
    public static final int EXECUTE_HINT = 0;
    public static final int JUST_KNOW_IT = 1;
    public String task;
    private boolean isNeedtoRequestData = false;
    public static final String TAG = "ReadyToArmDialogFragment";

    private ArrayList<UnCloseDoorEntry.ResultBean.PluginsBean> mData;
    private ReadyToArmDialogAdapter readyToArmDialogAdapter;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.ready_to_arm_dialog, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.btn_execute).setOnClickListener( v -> toExecute());
        rootView.findViewById(R.id.btn_cancal).setOnClickListener( v -> toCancal());
        rootView.findViewById(R.id.btn_know).setOnClickListener( v -> knowIt());
    }

    private void __bindViews(View rootView) {
        title = rootView.findViewById(R.id.title);
        line = rootView.findViewById(R.id.line);
        readyToArmHint = rootView.findViewById(R.id.ready_to_arm_hint);
        btnExecute = rootView.findViewById(R.id.btn_execute);
        btnCancal = rootView.findViewById(R.id.btn_cancal);
        btnKnow = rootView.findViewById(R.id.btn_know);
        notClosedDoorRecyclerView = rootView.findViewById(R.id.not_closed_door_recycler_view);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            showErrorToast();
            removeSelf();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void initData() {
        super.initData();

        title.setLocalText(getDelegateActivity().getResources().getString(R.string.rta_dialog_title));
        readyToArmHint.setLocalText(getDelegateActivity().getResources().getString(R.string.rta_dialog_hint));
        btnCancal.setLocalText(getDelegateActivity().getResources().getString(R.string.rta_dialog_btn_cancel));
        btnKnow.setLocalText(getDelegateActivity().getResources().getString(R.string.rta_dialog_btn_know));
        btnExecute.setLocalText(getDelegateActivity().getResources().getString(R.string.rta_dialog_btn_execute));

        switch (view_status) {
            case EXECUTE_HINT:
                btnCancal.setVisibility(View.VISIBLE);
                btnExecute.setVisibility(View.VISIBLE);
                btnKnow.setVisibility(View.GONE);
                break;
            case JUST_KNOW_IT:
                btnCancal.setVisibility(View.GONE);
                btnExecute.setVisibility(View.GONE);
                btnKnow.setVisibility(View.VISIBLE);

                changeHint();
                break;
            default:
                btnCancal.setVisibility(View.GONE);
                btnExecute.setVisibility(View.GONE);
                btnKnow.setVisibility(View.VISIBLE);
                break;
        }

        if (isNeedtoRequestData) {
            requestNotClosedDoorListData();
        }


        //初始化列表
        notClosedDoorRecyclerView.setLayoutManager(new LinearLayoutManager(getDelegateActivity(), LinearLayoutManager.VERTICAL, false));
        readyToArmDialogAdapter = new ReadyToArmDialogAdapter(mData);
        notClosedDoorRecyclerView.setAdapter(readyToArmDialogAdapter);
    }


    public static ReadyToArmDialogFragment newInstance(String task) {

        ReadyToArmDialogFragment readyToArmDialogFragment = new ReadyToArmDialogFragment();
        readyToArmDialogFragment.task = task;
        readyToArmDialogFragment.view_status = JUST_KNOW_IT;
        readyToArmDialogFragment.isNeedtoRequestData = true;


        return readyToArmDialogFragment;
    }

    /**
     * 如果是知道了页面，就要修改提示
     */
    public void changeHint() {
        String mPluginName = "有个遥控器";
        String mDevice;
        String familyName;
        String mState;
        if (!TextUtils.isEmpty(getMainActivity().pluginName)) {
            mPluginName = getMainActivity().pluginName;
        } else {
            String pluginId = getMainActivity().pluginId;
            String name;
            if (pluginId.startsWith("!")) {
                // 不知道什么原因导致偶尔没有category,默认当做新类型配件处理
                int category = 10;
                try {
                    category = Integer.parseInt(getMainActivity().category);
                }catch (Exception e){
                    DDLog.e(TAG, "Unknown category: " + getMainActivity().category);
                    e.printStackTrace();
                }
                name = CommonDataUtil.getInstance().getNameByBigIDAndSType(category
                        , getMainActivity().subCategory);
            } else {
                name = CommonDataUtil.getInstance().getSTypeByID(getMainActivity().pluginId);
            }
            mPluginName = Local.s(name) + "_" + getMainActivity().pluginId;
        }

        mDevice = CommonDataUtil.getInstance().getCurrentDeviceName();
        familyName = HomeManager.getInstance().getCurrentHome().getHomeName();
        switch (task) {
            case LocalKey.ARM_KEY:
                mState = Local.s(getMainActivity().getResources().getString(R.string.toolbar_arm_text));
                break;
            case LocalKey.HOMEARM_KEY:
                mState = Local.s(getMainActivity().getResources().getString(R.string.toolbar_homearm_text));
                break;
            default:
                mState = Local.s(getMainActivity().getResources().getString(R.string.toolbar_arm_text));
                break;
        }

        String hint = Local.s(getMainActivity().getResources().getString(R.string.ready_to_arm_push_hint));

        readyToArmHint.setLocalText(replacePluginString(hint, mPluginName, familyName, mState));
    }


    public static ReadyToArmDialogFragment newInstance(String task, UnCloseDoorEntry.ResultBean resultBean) {
        ReadyToArmDialogFragment readyToArmDialogFragment = new ReadyToArmDialogFragment();
        readyToArmDialogFragment.task = task;
        readyToArmDialogFragment.mData = (ArrayList<UnCloseDoorEntry.ResultBean.PluginsBean>) resultBean.getPlugins();
        if (resultBean.isForce()) {
            readyToArmDialogFragment.view_status = EXECUTE_HINT;
        } else {
            readyToArmDialogFragment.view_status = JUST_KNOW_IT;
        }

        return readyToArmDialogFragment;
    }


    public static ReadyToArmDialogFragment newInstance(int view_status) {
        ReadyToArmDialogFragment readyToArmDialogFragment = new ReadyToArmDialogFragment();
        readyToArmDialogFragment.view_status = view_status;

        return readyToArmDialogFragment;
    }

    public void toExecute() {
        mPanelDevice.submit(PanelParamsHelper.operationArm(
                LocalKey.ARM_KEY.equals(task)
                        ? PanelParamsHelper.OPERATE_ARM_ARM
                        : PanelParamsHelper.OPERATE_ARM_HOME_ARM, true, false));
        removeSelf();
    }

    public void toCancal() {
        mPanelDevice.submit(PanelParamsHelper.rollbackArmState());
        removeSelf();
    }

    public void knowIt() {
        /**
         * 知道了页面，是别人改变了安防
         * 要更新安防的状态，发送MainRebackLastStatus的通知去改变toolbar和eventlist
         */
//        if(task == LocalKey.ARM_KEY){
//            getMainActivity().lastArmState = LocalKey.ARM_STATUS;
//
//        }else if(task == LocalKey.HOMEARM_KEY){
//            getMainActivity().lastArmState = LocalKey.HOME_STATUS;
//        }
//
//        CommonDataUtil.getInstance().getMultiDataEntry().getResult().setArmstate(getMainActivity().lastArmState);
//        EventBus.getDefault().post(new MainRebackLastStatus());
        removeSelf();
    }

    /**
     * 推送进来的， 知道了页面需要请求数据
     */
    private void requestNotClosedDoorListData() {
        if (null == mPanelDevice) {
            DDLog.e(TAG, "Error, empty panel, requestNotClosedDoorListData");
            return;
        }
        isNeedtoRequestData = false;
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.getUnClosePlugs(LocalKey.HOMEARM_KEY.equals(task)));
    }

    @Override
    public boolean onBackPressed() {
        return true;
    }

    private String replacePluginString(String sourceStr, String pluginStr, String familyName, String statusStr) {
        sourceStr = sourceStr.replace("#plugin", pluginStr);
        sourceStr = sourceStr.replace("#family", familyName);
        sourceStr = sourceStr.replace("#status", statusStr);
        return sourceStr;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice
                || !deviceId.equals(mPanelDevice.getId())) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.GET_UNCLOSE_PLUGS.equals(cmd)) {
                // 获取到未关闭的配件数据
                onGetUnclosePlugin(status, map);
            }
            isSelfOperate = false;
        }
    }

    private void onGetUnclosePlugin(int status, Map<String, Object> map) {
        DDLog.i(TAG, "On onGetUnclosePlugin result: " + map);
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
            Gson gson = new Gson();
            JsonParser parser = new JsonParser();
            JsonArray jsonArray = parser.parse(result).getAsJsonArray();

            for (JsonElement obj : jsonArray) {
                UnCloseDoorEntry.ResultBean.PluginsBean newDoorSensorStatus = gson.fromJson(obj,
                        UnCloseDoorEntry.ResultBean.PluginsBean.class);
                mData.add(newDoorSensorStatus);
            }

            readyToArmDialogAdapter.updateData(mData);
        }
    }
}