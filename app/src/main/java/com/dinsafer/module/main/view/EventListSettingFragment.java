package com.dinsafer.module.main.view;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.annotation.Nullable;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentEventListSettingBinding;
import com.dinsafer.model.home.EventListHelper;
import com.dinsafer.module.BaseFragment;

/**
 * EventList相关设置页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/27 12:24 PM
 */
public class EventListSettingFragment extends BaseFragment {

    private FragmentEventListSettingBinding mBinding;

    public static EventListSettingFragment newInstance() {
        return new EventListSettingFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_event_list_setting,
                container, false);
        initLocalStatus();
        initListener();
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void initListener() {
        super.initListener();
//        mBinding.commonBarBack.setOnClickListener((View v) -> {
//            removeSelf();
//        });
//        mBinding.switchAlarm.setOnSwitchStateChangeListener((boolean ison) -> {
//            EventListHelper.getInstance().updateLocalMark(EventListHelper.MARK_ALARM, ison);
//        });
//        mBinding.switchSecurityStatus.setOnSwitchStateChangeListener((boolean ison) -> {
//            EventListHelper.getInstance().updateLocalMark(EventListHelper.MARK_SECURITY_STATUS, ison);
//        });
//        mBinding.switchAccessoriesOnlineStatus.setOnSwitchStateChangeListener((boolean ison) -> {
//            EventListHelper.getInstance().updateLocalMark(EventListHelper.MARK_ACCESSORY_ONLINE_STATUS, ison);
//        });
//        mBinding.switchCommonOperations.setOnSwitchStateChangeListener((boolean ison) -> {
//            EventListHelper.getInstance().updateLocalMark(EventListHelper.MARK_OPERATION, ison);
//        });
//        mBinding.switchDoorSensor.setOnSwitchStateChangeListener((boolean ison) -> {
//            EventListHelper.getInstance().updateLocalMark(EventListHelper.MARK_DOOR_WINDOW_STATUS, ison);
//        });
//        mBinding.switchTamper.setOnSwitchStateChangeListener((boolean ison) -> {
//            EventListHelper.getInstance().updateLocalMark(EventListHelper.MARK_TEMPER_ALARM, ison);
//        });
    }

    @Override
    public void initData() {
        super.initData();

        mBinding.commonBarTitle.setLocalText(getString(R.string.event_list_setting));
        mBinding.tvDoorSensorTittle.setLocalText(getString(R.string.event_list_setting_door_sensor_tittle));
        mBinding.tvTamperTittle.setLocalText(getString(R.string.event_list_setting_tamper_tittle));
        mBinding.tvAlarm.setLocalText(getString(R.string.event_list_setting_alarm));
        mBinding.tvSecurityStatus.setLocalText(getString(R.string.event_list_setting_security_status));
        mBinding.tvAccessoriesOnlineStatus.setLocalText(getString(R.string.event_list_setting_accessories_online_status));
        mBinding.tvCommonOperations.setLocalText(getString(R.string.event_list_setting_common_operations));
        mBinding.tvTittleInDisarmState.setLocalText(getString(R.string.event_list_setting_events_in_disarm_state));
    }

    private void initLocalStatus() {
//        int localStatus = EventListHelper.getInstance().getLocalFilter();
//        mBinding.switchAlarm.setOn((EventListHelper.MARK_ALARM & localStatus) != 0);
//        mBinding.switchSecurityStatus.setOn((EventListHelper.MARK_SECURITY_STATUS & localStatus) != 0);
//        mBinding.switchAccessoriesOnlineStatus.setOn((EventListHelper.MARK_ACCESSORY_ONLINE_STATUS & localStatus) != 0);
//        mBinding.switchCommonOperations.setOn((EventListHelper.MARK_OPERATION & localStatus) != 0);
//        mBinding.switchDoorSensor.setOn((EventListHelper.MARK_DOOR_WINDOW_STATUS & localStatus) != 0);
//        mBinding.switchTamper.setOn((EventListHelper.MARK_TEMPER_ALARM & localStatus) != 0);
    }
}
