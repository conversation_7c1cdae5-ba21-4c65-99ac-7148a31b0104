package com.dinsafer.module.main.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.home.EventListHelper;
import com.dinsafer.module_home.bean.EventListByFilterBean;
import com.dinsafer.ui.BaseTextDrawable;
import com.dinsafer.ui.CircularView;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.SectionedBaseAdapter;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.dinsafer.aop.annotations.Safer;

import java.util.ArrayList;
import java.util.List;

import androidx.cardview.widget.CardView;

/**
 * 首页EventList数据处理工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/27 4:23 PM
 */
public class EventListAdapter extends SectionedBaseAdapter {
    private final String TAG = EventListAdapter.class.getSimpleName();

    private Activity mActivity;

    private ArrayList<EventListByFilterBean> mData;
    private List<SectionInfo> mSectionInfo = new ArrayList<>();

    private int[] backgroundColor = {R.color.colorMainFragmentListViewItemBG_1,
            R.color.colorMainFragmentListViewItemBG_2};

    private int[] levelColors = {R.color.color_minor_1,
            R.color.color_minor_3
            , R.color.color_white_03};

    private OnItemClickListener<EventListByFilterBean> onItemClickListener;

    public EventListAdapter(Activity mActivity, ArrayList<EventListByFilterBean> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    public void setOnItemClickListener(OnItemClickListener<EventListByFilterBean> onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    @Override
    public Object getItem(int section, int position) {
        return null;
    }

    @Override
    public long getItemId(int section, int position) {
        return 0;
    }

    @Override
    public int getSectionCount() {
        return mSectionInfo.size();
    }

    @Override
    public int getCountForSection(int section) {
        return mSectionInfo.get(section).getCount();
    }

    @Safer
    @Override
    public View getItemView(int section, int position, View convertView, ViewGroup parent) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.item_event_list, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                DDLog.e(TAG, "Error on get tag.");
                e.printStackTrace();
            }
        }
        int _position = 0;

        if (0 == section) {
            _position = position;
        } else {
            for (int i = 0; i < section; i++) {
                _position += mSectionInfo.get(i).getCount();
            }
            _position += position;
        }

        final EventListByFilterBean itemData = mData.get(_position);

        String name = itemData.getUser();
        if (TextUtils.isEmpty(name)) {
            try {
                String sType = CommonDataUtil.getInstance().getNameByBigIDAndSType(itemData.getCategory()
                        , itemData.getSub_category());
                name = (Local.s(sType) + "_" + itemData.getPlugin_id());
            } catch (Exception ex) {
                DDLog.e(TAG, "Error on get plugin name.");
                ex.printStackTrace();
            }
        } else if (APIKey.EVENT_LIST_DELETE_USER.equals(name)
                || APIKey.EVENT_LIST_ALARM_PANEL.equals(name)) {
            name = Local.s(name);
        }

        BaseTextDrawable drawable = DDImageUtil.getBaseTextDrawable(mActivity, name
                , UserManager.getInstance().getUser().getUid().equals(name));
        int w = holder.mainFragmentItemAvatar.getLayoutParams().height;
        holder.mainFragmentItemAvatar.setBaseTextDrawable(drawable, w, w);
        final String photo = itemData.getPhoto();
        if (!TextUtils.isEmpty(photo))
            Log.i("image", APIKey.UPLOAD_SERVER_IP + photo);
        ImageLoader.getInstance().displayImage(APIKey.UPLOAD_SERVER_IP + photo,
                holder.mainFragmentItemAvatar);

        holder.mainFragmentItem.setBackgroundColor(
                mActivity.getResources().getColor(backgroundColor[_position % backgroundColor.length]));
        int powerLevel = EventListHelper.getInstance().getEventLevelByCmd(itemData.getCmd_name(), itemData.getBmt_model());
        DDLog.i("EventListAdapter","itemData.getCmd_name(): " + itemData.getCmd_name() +
                "   itemData.getBmt_model(): " + itemData.getBmt_model());
        holder.mainFragmentListViewItenStatus.setBackgroundColor(
                mActivity.getResources().getColor(levelColors[powerLevel]));
        if (LocalKey.UPDATE_AUTH.equals(itemData.getCmd_name())) {
            holder.mainFragmentListviewItemName.setText(getText(mData.get(_position)));
        } else if (LocalKey.UP_POWER.equals(itemData.getCmd_name())) {
            EventListByFilterBean.Data data = itemData.getData();
            boolean isCharge = data != null && data.isPowerstatus();
            if (isCharge) {
                holder.mainFragmentListviewItemName.setText(Local.s(mActivity.getResources()
                        .getString(R.string.device_power_on)));
            } else {
                holder.mainFragmentListviewItemName.setText(Local.s(mActivity.getResources()
                        .getString(R.string.device_power_off)));
            }
        } else {
            String cmdKey = EventListHelper.getInstance().getCmdName(mActivity, itemData.getCmd_name(), itemData.getBmt_model());
            EventListByFilterBean.Data data = itemData.getData();
            if (data != null) {
                String cabinetIndex = data.getCabinet_index();
                if (cabinetIndex != null) {
                    cmdKey = cmdKey.replace(mActivity.getString(R.string.ps_hashtag_cabinet_index), cabinetIndex);
                }
                String batteryIndex = data.getBattery_index();
                if (batteryIndex != null) {
                    cmdKey = cmdKey.replace(mActivity.getString(R.string.hashtag_battery_index), batteryIndex);
                }
                Integer chargeQuantity = data.getCharge_quantity();
                if (chargeQuantity != null) {
                    cmdKey = cmdKey.replace(mActivity.getString(R.string.hashtag_charge_quantity), String.valueOf(chargeQuantity));
                }
                Integer day = data.getDay();
                if (day != null) {
                    cmdKey = cmdKey.replace(mActivity.getString(R.string.hashtag_days), String.valueOf(day));
                }
            }
            holder.mainFragmentListviewItemName.setText(cmdKey);
        }
        holder.mainFragmentListviewItemTypeText.setText(name);
        holder.mainFragmentListviewItemTimeText.setText(DDDateUtil.getTimeText((itemData.getTime())));
        DDLog.i("EventListAdapter","itemData.getTime(): " + itemData.getTime());
        if (LocalKey.ADD_DAILY_MEMORIES.equals(itemData.getCmd_name())) {
            holder.cvVideo.setVisibility(View.VISIBLE);
            int final_position = _position;
            holder.cvVideo.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(final_position, itemData);
                    }
                }
            });
            if (!TextUtils.isEmpty(itemData.getScreenshot())) {
                ImageLoader.getInstance().displayImage(itemData.getScreenshot(), holder.ivDailyMemoriesScreenShot);
            } else {
                holder.ivDailyMemoriesScreenShot.setImageResource(R.drawable.img_multiscreen_default_5);
            }
        } else {
            holder.cvVideo.setVisibility(View.GONE);
        }
        return convertView;
    }

    private String getText(EventListByFilterBean eventlistBean) {
        final EventListByFilterBean.Data data = eventlistBean.getData();
        String uid = null == data ? "" : data.getUid();
        int newPermission = null == data ? 0 : data.getNewpermission();
        int oldPermission = null == data ? 0 : data.getOldpermission();

        StringBuilder itemName = new StringBuilder();
        if (newPermission != -1) {
            itemName.append(uid).append(" ");
            itemName.append(Local.s(getPermissionText(oldPermission)));
            itemName.append(" -> ");
            itemName.append(Local.s(getPermissionText(newPermission)));
        } else {
            itemName.append(uid);
            itemName.append(" " + Local.s("removed from device"));
        }
        return itemName.toString();
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        sectionHeaderHolder mHolder = null;
        if (convertView == null) {
            mHolder = new sectionHeaderHolder();
            LayoutInflater inflator = (LayoutInflater) parent.getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflator.inflate(R.layout.item_event_list_header, null);
            mHolder.mainFragmentName = (LocalTextView) convertView.findViewById(R.id.main_fragment_name);
            convertView.setTag(mHolder);
        } else {
            try {
                mHolder = (sectionHeaderHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        mHolder.mainFragmentName.setLocalText(mSectionInfo.get(section).getTittle());
        return convertView;
    }

    private String getPermissionText(int i) {
        if (i == LocalKey.ADMIN) {
            return mActivity.getResources().getString(R.string.change_permission_admin);
        } else if (i == LocalKey.USER) {
            return mActivity.getResources().getString(R.string.change_permission_user);
        } else {
            return mActivity.getResources().getString(R.string.change_permission_guest);
        }
    }

    /**
     * 更新分节信息
     *
     * @param newSectionInfo
     * @param clean          是否清除之前的分节信息
     */
    public void updateSectionInfo(List<SectionInfo> newSectionInfo, boolean clean) {
        if (null == newSectionInfo
                || 0 >= newSectionInfo.size()) {
            DDLog.e(TAG, "updateSectionInfo, Error!!!!!");
            return;
        }

        if (clean) {
            mSectionInfo.clear();
        }

        if (0 < mSectionInfo.size()
                && mSectionInfo.get(mSectionInfo.size() - 1).getTittle()
                .equals(newSectionInfo.get(0).getTittle())) {
            mSectionInfo.get(mSectionInfo.size() - 1).setCount(
                    mSectionInfo.get(mSectionInfo.size() - 1).getCount()
                            + newSectionInfo.get(0).getCount());
            newSectionInfo.remove(0);
        }

        if (0 < newSectionInfo.size()) {
            mSectionInfo.addAll(newSectionInfo);
        }

        DDLog.i(TAG, "Section total count: " + mSectionInfo.size());

        this.notifyDataSetChanged();
    }

    public static class SectionInfo {
        private int count;
        private String tittle;

        public SectionInfo(int count, String tittle) {
            this.count = count;
            this.tittle = tittle;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public String getTittle() {
            return tittle;
        }

        public void setTittle(String tittle) {
            this.tittle = tittle;
        }

        @Override
        public String toString() {
            return "SectionInfo{" +
                    "count=" + count +
                    ", tittle='" + tittle + '\'' +
                    '}';
        }
    }


    static class sectionHeaderHolder {
        LocalTextView mainFragmentName;
    }

    static class ViewHolder {

        CircularView mainFragmentItemAvatar;

        LinearLayout mainFragmentItem;

        TextView mainFragmentListviewItemName;

        TextView mainFragmentListviewItemTypeText;

        TextView mainFragmentListviewItemTimeText;

        View mainFragmentListViewItenStatus;

        CardView cvVideo;

        ImageView ivDailyMemoriesScreenShot;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            mainFragmentItemAvatar = view.findViewById(R.id.main_framgent_listview_item_avatar);
            mainFragmentItem = view.findViewById(R.id.main_fragment_item_layout);
            mainFragmentListviewItemName = view.findViewById(R.id.main_fragment_listview_item_name);
            mainFragmentListviewItemTypeText = view.findViewById(R.id.main_fragment_listview_item_type_text);
            mainFragmentListviewItemTimeText = view.findViewById(R.id.main_fragment_listview_item_time_text);
            mainFragmentListViewItenStatus = view.findViewById(R.id.main_fragment_listview_item_status);
            cvVideo = view.findViewById(R.id.cv_video);
            ivDailyMemoriesScreenShot = view.findViewById(R.id.iv_daily_memories_screenshot);
        }
    }

    public interface OnItemClickListener<T> {
        void onItemClick(int pos, T data);
    }
}
