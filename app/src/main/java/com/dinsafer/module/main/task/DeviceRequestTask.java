package com.dinsafer.module.main.task;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dinsdk.DeviceRequestType;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.model.event.NeedGetAllDeviceEvent;
import com.dinsafer.module.ipc.common.video.global.base.AbstractGlobalTask;
import com.dinsafer.module.ipc.common.video.global.base.TaskManager;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.util.DDLog;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import androidx.annotation.NonNull;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/5 11:11 上午
 */
public class DeviceRequestTask extends AbstractGlobalTask<Boolean, DeviceRequestListener> {
    @NonNull
    private final NeedGetAllDeviceEvent mLoadEvent;
    private final AtomicInteger mTotalCount;
    private final AtomicInteger mLoadCount;
    private final byte[] mLock = new byte[1];
    private final AtomicBoolean mLocked = new AtomicBoolean(false);
    private final int TIMEOUT = 10_000;

    public DeviceRequestTask(TaskManager taskManager, String taskId, @NonNull NeedGetAllDeviceEvent loadEvent) {
        super(taskManager, taskId);
        this.mLoadEvent = loadEvent;
        mTotalCount = new AtomicInteger(mLoadEvent.getLoadTotalCount());
        mLoadCount = new AtomicInteger(0);
    }

    @Override
    protected Boolean doTask() throws Exception {
        if (mTotalCount.get() == 0) {
            DDLog.i(TAG, TAG + "-->无需请求数据");
            return true;
        }

        synchronized (mLock) {
            // 主机
            if (mLoadEvent.isRequestPanel()
                    && AppConfig.Plugins.SUPPORT_PANEL) {
                DDLog.i(TAG, "需要获取主机Device");
                DinSDKHelper.getInstance().excute(new ExecutorAction<List<Device>>() {
                    @Override
                    public List<Device> runAction() {
                        return DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.PANEL);
                    }
                }).then((deviceList) -> onLoaded((List<Device>) deviceList, PanelConstant.DeviceType.PANEL));
            }

            // 摄像头Device
            if (mLoadEvent.isRequestIpc()) {
                if (AppConfig.Plugins.SUPPORT_DSCAM_V005
                        || AppConfig.Plugins.SUPPORT_DSCAM_V006
                        || AppConfig.Plugins.SUPPORT_DSCAM_V015
                        || AppConfig.Plugins.SUPPORT_DS_DOORBELL) {
                    DDLog.i(TAG, "需要获取自研IPC Device");
                    IPCManager.getInstance().fetchDsDevices(DeviceRequestType.ALL_DEVICE, new IDefaultCallBack() {
                        @Override
                        public void onSuccess() {
                            onLoaded(null, DinConst.TYPE_DSCAM);
                        }

                        @Override
                        public void onError(int i, String s) {
                            onLoaded(null, DinConst.TYPE_DSCAM);
                        }
                    });
                }

                if (AppConfig.Plugins.SUPPORT_HEARTLAI) {
                    DDLog.i(TAG, "需要获取心赖IPC Device");
                    IPCManager.getInstance().fetchHeartLaiDevices(DeviceRequestType.ALL_DEVICE, new IDefaultCallBack() {
                        @Override
                        public void onSuccess() {
                            onLoaded(null, DinConst.TYPE_HEARTLAI);
                        }

                        @Override
                        public void onError(int i, String s) {
                            onLoaded(null, DinConst.TYPE_HEARTLAI);
                        }
                    });
                }
            }

            // BMT电源
            if (mLoadEvent.isRequestBmt()
                    && (AppConfig.Plugins.SUPPORT_BMT_HP5000 || AppConfig.Plugins.SUPPORT_BMT_HP5001
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE30
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE20
                    || AppConfig.Plugins.SUPPORT_BMT_POWERSTORE
                    || AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE)) {
                DDLog.i(TAG, "需要获取自研BMTDevice");
                DDLog.i(MainActivity.TEST_TIME, "requestBmt");
                BmtManager.getInstance().fetchBmtDevice(DeviceRequestType.ALL_DEVICE, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        DDLog.i(MainActivity.TEST_TIME, "requestBmtSuccess");
                        BmtManager.getInstance().connectAllDevice();
                        BmtManager.getInstance().getAllDeviceLoadUsage();
                        onLoaded(null, DinConst.TYPE_BMT_HP5000);
                    }

                    @Override
                    public void onError(int i, String s) {
                        onLoaded(null, DinConst.TYPE_BMT_HP5000);
                    }
                });
            }

            DDLog.i(TAG, taskId + ": 等待请求结果...");
            mLocked.set(true);
            mLock.wait(TIMEOUT);
        }

        final boolean success = mTotalCount.get() == mLoadCount.get();
        DDLog.i(TAG, taskId + ": 请求结果, success: " + success);

        return success;
    }

    private synchronized void onLoaded(List<Device> devices, String type) {
        DDLog.v(TAG, taskId + ": onLoaded, type=" + type + ", isCanceled=" + isCanceled());
        if (isCanceled()) {
            tryNotify();
            return;
        }

        int current = mLoadCount.incrementAndGet();
        int total = mTotalCount.get();
        DDLog.i(TAG, taskId + ": onLoaded,type=" + type + ", total=" + total + ", current=" + current);

        if (null != listener) {
            listener.onLoad(type, devices, total, current);
        }

        if (current == total) {
            tryNotify();
        }
    }

    private void tryNotify() {
        synchronized (mLock) {
            try {
                if (mLocked.get()) {
                    mLocked.set(false);
                    mLock.notify();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
