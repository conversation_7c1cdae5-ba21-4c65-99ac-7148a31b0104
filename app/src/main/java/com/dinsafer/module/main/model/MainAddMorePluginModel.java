package com.dinsafer.module.main.model;

import android.content.Context;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.GridLayoutManager;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.MainPanelAddMoreViewHolder;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.addmore.view.AddMoreFragment;
import com.dinsafer.module.addmore.view.AddMoreHelper;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.settting.ui.ScannerActivity;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.PluginWidgetStyleUtil;

/**
 * @describe：AddMore卡片
 * @date：2022/10/17
 * @author: create by Sydnee
 */
public class MainAddMorePluginModel extends BaseMainItemModel<ViewDataBinding> {


    private final static String TAG = MainAddMorePluginModel.class.getSimpleName();
    private BaseFragment baseFragment;
    private Context mContext;
    private int fullProtectResId;
    private int addMoreResId;
    private OnItemClickListener itemClickListener;
    private MainPanelAddMoreViewHolder itemHolder;

    public MainAddMorePluginModel(BaseFragment baseFragment, int addMoreResId, int fullProtectResId) {
        super(baseFragment.getContext(), new MainWidgetBean(PluginWidgetStyleUtil.MODEL_ID_ADD_MORE_DEF, "", PluginWidgetStyleUtil.MODEL_TYPE_ADD_MORE_DEF, -1, "", ""),
                PluginWidgetStyleUtil.MODEL_ID_ADD_MORE_DEF, PluginWidgetStyleUtil.MODEL_TYPE_ADD_MORE_DEF, 0);
        this.baseFragment = baseFragment;
        this.mContext = baseFragment.getContext();
        this.addMoreResId = addMoreResId;
        this.fullProtectResId = fullProtectResId;
    }

    /**
     * 设置点击监听
     */
    public void setItemClickListener(OnItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    @Override
    public int getLayoutID() {
        return R.layout.main_panel_item_add_more;
    }

    @Override
    public void onDo(View v) {
    }

    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding viewDataBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            itemHolder = new MainPanelAddMoreViewHolder(viewDataBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, itemHolder);
        } else {
            itemHolder = (MainPanelAddMoreViewHolder) tag;
        }

        FrameLayout flRoot = holder.itemView.findViewById(R.id.fl_root);
        GridLayoutManager.LayoutParams rootLayoutParam = (GridLayoutManager.LayoutParams) flRoot.getLayoutParams();

        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            rootLayoutParam.height = DensityUtils.dp2px(mContext.getApplicationContext(), 0);
            flRoot.setLayoutParams(rootLayoutParam);
            itemHolder.itemView.setVisibility(View.GONE);
            itemHolder.setAddMoreBtnEnable(false);
        } else {
            rootLayoutParam.height = DensityUtils.dp2px(mContext.getApplicationContext(), LinearLayout.LayoutParams.WRAP_CONTENT);
            flRoot.setLayoutParams(rootLayoutParam);
            itemHolder.itemView.setVisibility(View.VISIBLE);
            itemHolder.setAddMoreBtnEnable(true);
            itemHolder.setAddMoreClickListener(v -> {
                if (null == baseFragment.getMainActivity()) {
                    DDLog.e(TAG, "MainActivity is empty.");
                    return;
                }
                if (!AppConfig.Functions.SUPPORT_ADD_MORE ||
                        AddMoreHelper.FUNCTION_MODE_OEM == AddMoreHelper.getInstance().getFunctionMode()) {
                    ScannerActivity.startScan(baseFragment.getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
                } else {
                    baseFragment.getMainActivity().addCommonFragment(AddMoreFragment.newInstance());
                }
            });
        }

        itemHolder.setTextResId(R.string.add_more, R.string.full_protection_for_your_home);
        itemHolder.setAddMoreButtonVisible(HomeManager.getInstance().getCurrentHome().getLevel() > LocalKey.USER);

        itemHolder.setEditClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (itemClickListener != null) {
                    itemClickListener.onEditClickListener();
                }
            }
        });

        itemHolder.setAddMoreClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (itemClickListener != null) {
                    itemClickListener.onAddMoreClickListener();
                }
            }
        });

    }


    public interface OnItemClickListener {
        void onAddMoreClickListener();

        void onEditClickListener();
    }
}
