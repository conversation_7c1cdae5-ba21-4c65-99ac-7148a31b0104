package com.dinsafer.module.main.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.alibaba.fastjson.JSONObject;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.BmtManager;
import com.dinsafer.common.CoapController;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.config.PsVersion1EventCode;
import com.dinsafer.config.PsVersion2EventCode;
import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.api.ILoginStateChangedListener;
import com.dinsafer.dincore.user.api.ILogoutCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityMainBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamNetWorkSetting;
import com.dinsafer.dscam.DsCamNetworkBleScanFragment;
import com.dinsafer.dscam.guide.event.MotionFrequencyPushEvent;
import com.dinsafer.dscam.upgrade.DsCamUpgradeEvent;
import com.dinsafer.dscam.upgrade.DsCamUpgradeFragment;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.AdEntry;
import com.dinsafer.model.AppStateEvent;
import com.dinsafer.model.AppStatePreEvent;
import com.dinsafer.model.CareModeNoActionEvent;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.CloseAllDeviceEvent;
import com.dinsafer.model.DoorbellCallPushEvent;
import com.dinsafer.model.IPCAlertExhaustedEvent;
import com.dinsafer.model.IPCMotionDetectedPushEvent;
import com.dinsafer.model.LifeEvent;
import com.dinsafer.model.PopupEntry;
import com.dinsafer.model.ScanQREvent;
import com.dinsafer.model.WebSocketEvent;
import com.dinsafer.model.WindowFocusChangedEvent;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.model.event.GetAllDeviceFinishedEvent;
import com.dinsafer.model.event.GoAddMoreEvent;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.model.event.HadLogoutPreEvent;
import com.dinsafer.model.event.HideKeyboardOnTouchBlankEvent;
import com.dinsafer.model.event.NeedLoginAgainEvent;
import com.dinsafer.model.event.NeedLogoutEvent;
import com.dinsafer.model.event.NetworkChangeEvent;
import com.dinsafer.model.event.OMSNotificationEvent;
import com.dinsafer.model.family.FcmAuthorityUpdatedEvent;
import com.dinsafer.model.family.FcmIpcLowBatteryEvent;
import com.dinsafer.model.family.FcmKeypadMemberPwdUpdateEvent;
import com.dinsafer.model.family.FcmMemberDeleteEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.ad.AdFragment;
import com.dinsafer.module.app.password.PasswordActivity;
import com.dinsafer.module.daily.DailyMemoriesPushEvent;
import com.dinsafer.module.iap.NewActivityEvent;
import com.dinsafer.module.ipc.common.video.global.motion.MotionDownloadManager;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.main.adapter.CommonNonSaveStatePagerAdapter;
import com.dinsafer.module.pirSensitivity.event.PirSettingModeTimeoutEvent;
import com.dinsafer.module.popup.PopupFragment;
import com.dinsafer.module.powerstation.event.BmtDeadlineEvent;
import com.dinsafer.module.powerstation.event.PushBmtEVEvent;
import com.dinsafer.module.powerstation.event.PushBmtExceptionEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.DoorBellCapListFragment;
import com.dinsafer.module.settting.ui.ImageDialog;
import com.dinsafer.module.settting.ui.SettingFragment;
import com.dinsafer.module.user.LoginActivity;
import com.dinsafer.module.user.UserZoneFragment;
import com.dinsafer.module_base.smartwidget.CloseSmartWidgetEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.HomeConstants;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.receiver.MyBaseDinsaferPushReveiver;
import com.dinsafer.receiver.NetworkChangeReceiver;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.ActionsheetDismiss;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.NavigatorUtil;
import com.dinsafer.util.viewanimator.AnimationBuilder;
import com.dinsafer.util.viewanimator.ViewAnimator;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class MainActivity extends BaseMainActivity
        implements PasswordActivity.IPasswordCallBack, ILoginStateChangedListener, com.dinsafer.util.ActivityManager.OnAppFrontBackChangedListener {

    private String TAG = getClass().getSimpleName();

    private static final int MAIN_POSITION = 0;

    private static final int SETTING_POSITION = 1;

    private static final int USER_POSITION = 2;

    public static final int REQUEST_PERMISSION_STORAGE_CODE = 1;

    private ActivityMainBinding mBinding;

    private ArrayList<BaseFragment> mPagerDatas;

    private AnimationBuilder sosAnimBuilder;

    private CommonNonSaveStatePagerAdapter mMainFragmentPagerAdapter;

    private boolean isArm;

    private boolean isHasStart = false;

    private boolean isFirstStart = false;

    private int mCurrentStatue;

    private String doorBellImageUrl = "";

    private String doorBellContent = "";

    private AlertDialog mPinErrorDialog;

    private FcmAuthorityUpdatedEvent mAuthorityUpdateEvent;
    private IPCMotionDetectedPushEvent ipcMotionDetectedPushEvent;
    private DoorbellCallPushEvent doorbellCallPushEvent;
    private MotionFrequencyPushEvent motionFrequencyPushEvent;
    private IPCAlertExhaustedEvent ipcAlertExhaustedEvent;
    private CareModeNoActionEvent careModeNoActionEvent;
    private FcmMemberDeleteEvent mMemberDeleteEvent;
    private PushBmtExceptionEvent mPushBmtExceptionEvent;
    private PushBmtEVEvent mPushBmtEVEvent;
    private BmtDeadlineEvent mBmtDeadlineEvent;
    private FcmKeypadMemberPwdUpdateEvent mFcmKeypadMemberPwdUpdateEvent;
    private DailyMemoriesPushEvent dailyMemoriesPushEvent;
    private OMSNotificationEvent mPushTitleMessageEvent;
    private final NetworkChangeReceiver mNetChangeReceiver = new NetworkChangeReceiver();
    private String wantSelectHomeId = null;

    private boolean hideKeyboardOnTouchBlank = false;

    private boolean isFromLogin;
    private static final String IS_FROM_LOGIN = "isFromLogin";
    private static final int EXIT_APP = 0X01;

    public static long mStartTime;

    public static void start(Context context) {
        Intent starter = new Intent(context, MainActivity.class);
        starter.putExtra(IS_FROM_LOGIN, true);
        context.startActivity(starter);
    }

    private String keyRegistry = "androidx.lifecycle.BundlableSavedStateRegistry.key";
    private String keyFragment = "android:support:fragments";

    public final static String TEST_TIME = "test_time";

    private static final int EXIT_PIR_SETTING = 0X02;
    private final static long PIR_SETTING_TIMEOUT = 5 * 60 * 1000;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == EXIT_APP) {
                Process.killProcess(Process.myPid());    //获取PID
                System.exit(0);   //常规java、c#的标准退出法，返回值为0代表正常退出
            } else if (msg.what == EXIT_PIR_SETTING) {
                EventBus.getDefault().post(new PirSettingModeTimeoutEvent());
            }
        }
    };

    @Override
    protected boolean initVariables() {
        super.initVariables();
//        初始化跳转工具
        NavigatorUtil.init(this);

        initDB();
//        初始化变量
        DDLog.i("mainactivity", DBUtil.Str(DBKey.APIKEY));

        transateDB();

        initMainData();

        UserManager.getInstance().setLoginStateChangedListener(this);

        return true;
    }

    public boolean isFirstStart() {
        return isFirstStart;
    }

    public void setFirstStart(boolean firstStart) {
        isFirstStart = firstStart;
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        super.initViews(savedInstanceState);
        DDLog.i(TEST_TIME, "initViews");
        isFirstStart = true;
        setIsLandscape(false);
//        初始化界面
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_main);
//        View decorView = getWindow().getDecorView();
//// Hide both the navigation bar and the status bar.
//// SYSTEM_UI_FLAG_FULLSCREEN is only available on Android 4.1 and higher, but as
//// a general rule, you should design your app to hide the status bar whenever you
//// hide the navigation bar.
//        decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
//                | View.SYSTEM_UI_FLAG_FULLSCREEN
//                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);


        EventBus.getDefault().register(this);

//       判断有没有user
//        doAfterStart();

        sosAnimBuilder = ViewAnimator.animate(mBinding.sosImage)
                .rotation(5, -5)
                .duration(100)
                .repeatCount(ValueAnimator.INFINITE)
                .repeatMode(ValueAnimator.REVERSE);
        mBinding.mainSosHintLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toShowSos();
            }
        });

//         放在sos之后执行
//        DDSystemUtil.checkUpdate(this);

        mPinErrorDialog = AlertDialog.createBuilder(this)
                .setOk("OK")
                .setContent(getString(R.string.device_sin_pin_error_hint))
                .preBuilder();
        mStartTime = System.currentTimeMillis();
        if (TextUtils.isEmpty(DBUtil.SGet(DBKey.CHECK_USER_PASSWORD))) {
            EventBus.getDefault().post(new NeedLogoutEvent());
        }
    }

    @Override
    protected void loadData() {
        super.loadData();
//        初始化数据
        initViewPager();
    }


    /**
     * 初始化主页的viewpager 里面的3个fragment
     */
    private void initViewPager() {
        mPagerDatas = new ArrayList<BaseFragment>();

        mPagerDatas.add(MainMiddleNewFragment.newInstance());
        mPagerDatas.add(SettingFragment.newInstance());
        mPagerDatas.add(UserZoneFragment.newInstance());

        mMainFragmentPagerAdapter = new CommonNonSaveStatePagerAdapter(this.getSupportFragmentManager(), mPagerDatas);

        mBinding.mainViewpager.setOffscreenPageLimit(3);
        mBinding.mainViewpager.setAdapter(mMainFragmentPagerAdapter);

        mBinding.mainViewpager.setCurrentItem(MAIN_POSITION);
    }

    public void smoothToUser() {
        smoothToPosition(USER_POSITION);
    }

    public void smoothToSetting() {
        smoothToPosition(SETTING_POSITION);
    }

    public void smoothToHome() {
        smoothToPosition(MAIN_POSITION);
    }

    private void smoothToPosition(int position) {
        if (position < 0 || position > mMainFragmentPagerAdapter.getCount()) {
            return;
        }
        mCurrentStatue = position;
        mBinding.mainViewpager.setCurrentItem(position, true);
    }

    public int getCurrentStatue() {
        return mCurrentStatue;
    }

    @Override
    public void onBackPressed() {
        try {
            if (!ActionSheet.mDismissed) {
                EventBus.getDefault().post(new ActionsheetDismiss());
            } else if (getFragmentList().size() == 0) {
                if (mCurrentStatue == MAIN_POSITION) {
                    exitApp();
                    return;
                } else if (USER_POSITION == mCurrentStatue) {
                    smoothToSetting();
                } else {
                    smoothToHome();
                }
            }
            if (((BaseFragment) getFragmentList().get(getFragmentList().size() - 1)).onBackPressed())
                return;
            removeCommonFragmentAndData((BaseFragment) getFragmentList().get(getFragmentList().size() - 1), true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void exitApp() {
        AlertDialog builder = AlertDialog.createBuilder(this)
                .setContent(getResources().getString(R.string.exit_app))
                .setOk(getResources().getString(R.string.Confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        exitAppDirect();
                    }
                })
                .setCancel(getResources().getString(R.string.Cancel))
                .preBuilder();
        builder.show();
    }

    public void exitAppDirect() {
        destory();
        DinHome.getInstance().stopE2EConnection(true);
        toCloseWs(false);
        mHandler.sendEmptyMessageDelayed(EXIT_APP, 200);
    }


    @Override
    protected void onDestroy() {
        unregisterReceiver(mNetChangeReceiver);
        destory();
        super.onDestroy();
    }

    private void destory() {
        BmtManager.getInstance().stopPolling();
        EventBus.getDefault().unregister(this);
        // CommonDataUtil.getInstance().tryFixDBUserWhenUserIsEmpty();
    }

    public void setIsLandscape(boolean isLandscape) {
        if (isLandscape && getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_USER);
        } else {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    @Subscribe
    public void onEvent(GetAllDeviceFinishedEvent ev) {
        DDLog.i("GetAllDeviceFinishedEvent", "GetAllDeviceFinishedEvent");
        if (!TextUtils.isEmpty(doorBellImageUrl)
                && DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getPermission() == LocalKey.ADMIN) {
            doorBellImageUrl = DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.DOOR_BELL_SERVER_IP + doorBellImageUrl);
            toShowDoorBellDialog(doorBellContent, doorBellImageUrl);
            setDoorBellImageUrl("");
        } else if (ipcAlertExhaustedEvent != null) {
            EventBus.getDefault().postSticky(ipcAlertExhaustedEvent);
            ipcAlertExhaustedEvent = null;
        } else if (null != mAuthorityUpdateEvent) {
            EventBus.getDefault().postSticky(mAuthorityUpdateEvent);
            mAuthorityUpdateEvent = null;
        } else if (null != mMemberDeleteEvent) {
            EventBus.getDefault().postSticky(mMemberDeleteEvent);
            mMemberDeleteEvent = null;
        } else if (doorbellCallPushEvent != null) {
            EventBus.getDefault().postSticky(doorbellCallPushEvent);
            doorbellCallPushEvent = null;
        } else if (null != motionFrequencyPushEvent) {
            EventBus.getDefault().postSticky(motionFrequencyPushEvent);
            motionFrequencyPushEvent = null;
        } else if (null != mPushBmtExceptionEvent) {
            EventBus.getDefault().postSticky(mPushBmtExceptionEvent);
            mPushBmtExceptionEvent = null;
        } else if (null != mFcmKeypadMemberPwdUpdateEvent) {
            EventBus.getDefault().postSticky(mFcmKeypadMemberPwdUpdateEvent);
            mFcmKeypadMemberPwdUpdateEvent = null;
        } else if (null != mPushBmtEVEvent) {
            EventBus.getDefault().postSticky(mPushBmtEVEvent);
            mPushBmtEVEvent = null;
        } else if (null != dailyMemoriesPushEvent) {
            EventBus.getDefault().postSticky(dailyMemoriesPushEvent);
            dailyMemoriesPushEvent = null;
        } else if (null != mBmtDeadlineEvent) {
            EventBus.getDefault().postSticky(mBmtDeadlineEvent);
            mBmtDeadlineEvent = null;
        } else if (null != mPushTitleMessageEvent) {
            EventBus.getDefault().postSticky(mPushTitleMessageEvent);
            mPushTitleMessageEvent = null;
        }
//        mBackgroundBinder.getService().connectWebSocket();

        // 显示主机SIM卡PIN码错误提示对话框
        if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo().isSimPinError()
                && !mPinErrorDialog.isShowing()) {
            DDLog.i(TAG, "Show SIM PIN ERROR dialog.");
            mPinErrorDialog.setContent(getString(R.string.device_sin_pin_error_hint));
            mPinErrorDialog.setOKText("OK");
            mPinErrorDialog.show();
        }

        getImagePopup();
        checkAppUpgrade();
    }

    public void setDoorBellImageUrl(String doorBellImageUrl) {
        this.doorBellImageUrl = doorBellImageUrl;
    }

    public void toShowDoorBellDialog(String content, String url) {
        ImageDialog.createBuilder(this)
                .setTitle(getResources().getString(R.string.device_managent_doorbell_cap))
                .setContent(content)
                .setAutoDismiss(true)
                .setImgUrl(url)
                .setOk(getResources().getString(R.string.door_bell_cancel_btn))
                .setCancel(getResources().getString(R.string.door_bell_more_btn))
                .setCancelListener(new ImageDialog.AlertClickCallback() {
                    @Override
                    public void onClick(ImageDialog dialog) {
                        MainActivity.this.addCommonFragment(DoorBellCapListFragment.newInstance());
                    }
                })
                .preBuilder()
                .show();
    }

    @Subscribe
    public void onNetworkStateChange(NetworkChangeEvent event) {
        DDLog.i(TAG, "onNetworkStateChange");
        if (null != event && event.isAvailable() && DDSystemUtil.isAppOnForeground(this)) {
            reConnectWebSocket(false);
        }
    }

    /**
     * @param cleanCache true:清除缓存中的主机和首页配件信息
     */
    public void toCloseWs(boolean cleanCache) {
        DDLog.i(TAG, "尝试断开连接WebSocket");
        Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentDeviceId());
        if (null != device) {
            device.submit(PanelParamsHelper.operationWsConnection(PanelParamsHelper.WEB_SOCKET_DISCONNECT));
        }
    }

    public boolean wsIsConnect() {
        return true;
    }

    public String closeReason() {
        return "";
    }

    public void reConnectWebSocket(boolean showLoading) {
        DDLog.i(TAG, "尝试重新连接WebSocket");
        Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentDeviceId());
        if (null != device) {
            if (showLoading) {
                showLoadingFragment(LoadingFragment.BLACK, "");
            }
            device.submit(PanelParamsHelper.operationWsConnection(PanelParamsHelper.WEB_SOCKET_CONNECT));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(WebSocketEvent ev) {
        if (ev.getType() == WebSocketEvent.CONNET_SUCCESS) {
            closeLoadingFragment();

            if (careModeNoActionEvent != null) {
                EventBus.getDefault().postSticky(careModeNoActionEvent);
                careModeNoActionEvent = null;
            }
        } else if (ev.getType() == WebSocketEvent.CONNET_CLOSE) {
            if (CommonDataUtil.getInstance().checkHasUser()) {
                EventBus.getDefault().post(new DeviceOfflineEvent());
//                addCommonFragment(BleOfflineFragment.newInstance());
            }
        }

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        FIXME 测试代码
//        CommonDataUtil.getInstance().loginTuya(
//                "and_U6dHa90Yp81614591753670",
//                "and_k6gxHJBgXA1614591753670", "49",
//                false);
        if (savedInstanceState != null) {
            /**
             * 1.在oncreate恢复fragmentList数据：
             *          因为这里有可能增加Welcome页面，而被回收前的top就是Welcome页面的话，
             *          那么addCommonFragment这里需要FragmentList去判断welcome是否已有，避免重复添加。
             * 2.在onRestoreInstanceState恢复其他数据：
             *          有可能oncreate onstart中初始了数据，那不是我们想要被回收掉的数据
             */
            resumeFragmentList(savedInstanceState);
        }

        if (getIntent() != null
                && getIntent().getExtras() != null
                && getIntent().getExtras().containsKey(IS_FROM_LOGIN)) {
            isFromLogin = getIntent().getBooleanExtra(IS_FROM_LOGIN, false);
        }
        if (getIntent() != null && ((getIntent().getExtras() != null
                && (getIntent().getExtras().containsKey(MyBaseDinsaferPushReveiver.CMDTYPE)
                || getIntent().getExtras().containsKey(MyBaseDinsaferPushReveiver.CMD)))
                || getIntent().getData() != null)) {
            onNewIntent(getIntent());
        } else {
            String wifiSSID = DDSystemUtil.getWIFISSID(this);
            if (wifiSSID != null && wifiSSID.startsWith(APIKey.AP_NAME)) {

            } else if (!isNotNeedToLogin) {
                if (DBUtil.Exists(DBKey.APP_PASSWORD)) {
                    PasswordActivity activity = PasswordActivity.newInstance(true);
                    activity.setCallBack(this);
                    addCommonFragment(activity);
                } else {
                    if (isFromLogin) {
                        CommonDataUtil.getInstance().fetchHomeList(null);
                    } else {
                        doAfterStart();
                    }
                }
                isNotNeedToLogin = true;
            } else {
                isNotNeedToLogin = false;
            }
        }

        IntentFilter netChangeFilter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        registerReceiver(mNetChangeReceiver, netChangeFilter);
        com.dinsafer.util.ActivityManager.get().addAppFrontBackChangedListener(this);
    }

    private void transateDB() {
        DDLog.i("encode", "transateDB");
        if (DBUtil.Exists(DBKey.ISENCODE_DB) && DBUtil.Bool(DBKey.ISENCODE_DB)) {
            return;
        }
        encodeDB();
//        if (DBUtil.Exists(DBKey.ISENCODE_DB)) {
//            if (DBUtil.Bool(DBKey.ISENCODE_DB)) {
//                return;
//            } else {
//                encodeDB();
//            }
//        } else {
//            encodeDB();
//        }

    }

    private void encodeDB() {
        if (DBUtil.Exists(DBKey.USER_KEY)) {
            DDLog.i("encodeDB", "encode user key");
            DBUtil.SPut(DBKey.USER_KEY, DBUtil.Str(DBKey.USER_KEY));
        }

        if (DBUtil.Exists(DBKey.USER_PASSWORD)) {
            DDLog.i("encodeDB", "encode remember");
            DBUtil.SPut(DBKey.USER_PASSWORD, DDSecretUtil.getReverSCWithOutSnappy(DBUtil.Str(DBKey.USER_PASSWORD)));
        }

//        密码不需要再次加密，因为旧版本已经加密了，不存在兼容问题
//        if (DBUtil.Exists(DBKey.USER_PASSWORD)) {
//            DDLog.i("encodeDB","encode user password");
//            DBUtil.SPut(DBKey.USER_PASSWORD, DBUtil.Str(DBKey.USER_PASSWORD));
//        }

        if (DBUtil.Exists(DBKey.APP_PASSWORD)) {
            DDLog.i("encodeDB", "encode app password");
            DBUtil.SPut(DBKey.APP_PASSWORD, DBUtil.Str(DBKey.APP_PASSWORD));
        }

        DBUtil.Put(DBKey.ISENCODE_DB, true);
    }

    private static int getStatusBarHeight(Context context) {
        int statusBarHeight = 0;
        Resources res = context.getResources();
        int resourceId = res.getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            statusBarHeight = res.getDimensionPixelSize(resourceId);
        }
        return statusBarHeight;
    }

    public void showSOSLayout(boolean isShow) {
        try {
            isArm = isShow;
            if (isShow) {
                sosAnimBuilder.start();
                mBinding.sosTime.setStartTime(DDDateUtil.getUTCTime(SOSFragment.sosStatusEntry.getResult().getTime()));
                mBinding.mainSosHintLayout.setVisibility(View.VISIBLE);
            } else {
                mBinding.mainSosHintLayout.setVisibility(View.GONE);
                sosAnimBuilder.cancel();
            }
        } catch (Exception e) {
            //e.printStackTrace();
        }
    }

    public boolean isArm() {
        return isArm;
    }

    public void setArm(boolean arm) {
        isArm = arm;
    }

    public void toShowSos() {
        EventBus.getDefault().post(new CloseAllDeviceEvent());
        EventBus.getDefault().post(new CloseSmartWidgetEvent());
        addCommonFragment(SOSFragment.newInstance());
    }

    private void initDB() {
        try {
            if (!DBUtil.ISInit()) {
                DBUtil db = new DBUtil(DinSaferApplication.getAppContext());
            }
        } catch (Exception e) {
            DDLog.log("DB", "数据库异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    //    当点击通知栏后,回到到这个方法
    @Override
    protected void onNewIntent(final Intent intent) {
        super.onNewIntent(intent);
        DDLog.i("onNewIntent", "onNewIntent");
        setIntent(intent);
        doorBellImageUrl = "";
        doorBellContent = "";
        if (null == intent) {
            DDLog.e(TAG, "No data on newIntent");
            return;
        }

        Uri uri = getIntent().getData();
        if (uri != null) {
            String uriStr = uri.toString();
            if (!TextUtils.isEmpty(uriStr) && uriStr.equals(APIKey.ADD_MORE_URI)) {
                EventBus.getDefault().postSticky(new GoAddMoreEvent());
            }
        }

        if (null == intent.getExtras()
                || 0 >= intent.getExtras().size()) {
            DDLog.e(TAG, "No data on newIntent");
            return;
        }

        Bundle extras = getIntent().getExtras();

        for (String s : intent.getExtras().keySet()) {
            Log.d(TAG, "onNewIntent:--------- key:" + s + "/value;" + intent.getExtras().get(s));
        }

        // 1、处理加入家庭功能之后的逻辑
        final String cmd = extras.getString(MyBaseDinsaferPushReveiver.CMD);
        if (!TextUtils.isEmpty(cmd)) {
            DDLog.i(TAG, "处理加入家庭功能后的CMD");

            final String homeId = extras.getString(MyBaseDinsaferPushReveiver.HOME_ID);
            final String message = extras.getString(MyBaseDinsaferPushReveiver.MESSAGE);
            final String title = extras.getString(MyBaseDinsaferPushReveiver.TITLE);
            final String id = extras.getString(MyBaseDinsaferPushReveiver.ID);
            final String model = extras.getString(MyBaseDinsaferPushReveiver.MODEL);
            if (!TextUtils.isEmpty(model)) {
                int eventType = DinConst.TYPE_BMT_HP5000.equals(model) ? PsVersion1EventCode.checkEvent(cmd) : PsVersion2EventCode.checkEvent(cmd);
                switch (eventType) {
                    case PsVersion1EventCode.TYPE_EVENT_EXCEPTION:
                        String deviceId = extras.getString(MyBaseDinsaferPushReveiver.ID);
                        mPushBmtExceptionEvent = new PushBmtExceptionEvent(deviceId, model, cmd, homeId, message, title);
                        break;
                    case PsVersion1EventCode.TYPE_EVENT_TOAST:
                        showTopToast(title);
                        break;
                    case PsVersion1EventCode.TYPE_EVENT_EV:
                        String evDeviceId = extras.getString(MyBaseDinsaferPushReveiver.ID);
                        mPushBmtEVEvent = new PushBmtEVEvent(cmd, homeId, evDeviceId, model, title, message);
                        break;
                    case PsVersion1EventCode.TYPE_EVENT_DEADLINE:
                        String homeName = extras.getString(MyBaseDinsaferPushReveiver.HOME_NAME);
                        String bmtName = extras.getString(MyBaseDinsaferPushReveiver.BMT_NAME);
                        StringBuilder sb = new StringBuilder();
                        if (!TextUtils.isEmpty(homeName)) {
                            sb.append(homeName);
                        }
                        if (!TextUtils.isEmpty(bmtName)) {
                            if (!TextUtils.isEmpty(homeName)) {
                                sb.append(", ");
                            }
                            sb.append(bmtName);
                            sb.append(": ");
                        } else {
                            if (!TextUtils.isEmpty(homeName)) {
                                sb.append(": ");
                            }
                        }
                        sb.append(message);
                        mBmtDeadlineEvent = new BmtDeadlineEvent(cmd, homeId, id, model, title, sb.toString());
                        break;

                }
            }

            switch (cmd) {
                case HomeConstants.CMD.AUTHORITY_UPDATED:
                    int level = extras.getInt(MyBaseDinsaferPushReveiver.LEVEL);
                    mAuthorityUpdateEvent = new FcmAuthorityUpdatedEvent(homeId, level, message, false);
                    break;
                case HomeConstants.CMD.MEMBER_DELETED:
                    mMemberDeleteEvent = new FcmMemberDeleteEvent(homeId, message, false);
                    break;
                case HomeConstants.CMD.IPC_LOW_BATTERY:
                    final String ipcName = extras.getString(MyBaseDinsaferPushReveiver.IPC_NAME);
                    String pid = extras.getString(MyBaseDinsaferPushReveiver.PID);
                    final String provider = extras.getString(MyBaseDinsaferPushReveiver.PROVIDER);
                    EventBus.getDefault().postSticky(new FcmIpcLowBatteryEvent(homeId, ipcName, pid, message, provider));
                    break;
                case LocalKey.IPC_MOTION_DETECTED_HOME:
                case LocalKey.HOME_IPC_MOTION_DETECTED_EVENT:
                    HashMap<String, Object> extra = new HashMap<>();
                    for (String key : intent.getExtras().keySet()) {
                        extra.put(key, intent.getExtras().get(key));
                    }

                    if (!TextUtils.isEmpty(homeId)) {
                        if (isNeedShowLoadingFragment()) {
                            showLoadingFragment(LoadingFragment.BLUE, getResources().getString(R.string.loging_hint));
                        }
                        CommonDataUtil.getInstance().setBleToAdd(false);
                        CommonDataUtil.getInstance().autoLogin(this, homeId);
                        isNotNeedToLogin = true;
                    }

                    try {
                        JSONObject jsonObject = new JSONObject(extra);
                        ipcMotionDetectedPushEvent = new IPCMotionDetectedPushEvent(homeId, jsonObject.toJSONString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    EventBus.getDefault().postSticky(ipcMotionDetectedPushEvent);
                    ipcMotionDetectedPushEvent = null;
                    break;
                case LocalKey.DOORBELL_CALL:
                    extra = new HashMap<>();
                    for (String key : intent.getExtras().keySet()) {
                        extra.put(key, intent.getExtras().get(key));
                    }

                    if (!TextUtils.isEmpty(homeId)) {
                        if (isNeedShowLoadingFragment()) {
                            showLoadingFragment(LoadingFragment.BLUE, getResources().getString(R.string.loging_hint));
                        }
                        CommonDataUtil.getInstance().setBleToAdd(false);
                        CommonDataUtil.getInstance().autoLogin(this, homeId);
                        isNotNeedToLogin = true;
                    }

                    try {
                        JSONObject jsonObject = new JSONObject(extra);
                        doorbellCallPushEvent = new DoorbellCallPushEvent(homeId, jsonObject.toJSONString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                case HomeConstants.CMD.FREQUENCY_IS_TOO_HIGH:
                    pid = extras.getString(MyBaseDinsaferPushReveiver.IPC_PID);
                    if (!TextUtils.isEmpty(homeId)) {
                        if (isNeedShowLoadingFragment()) {
                            showLoadingFragment(LoadingFragment.BLUE, getResources().getString(R.string.loging_hint));
                        }
                        CommonDataUtil.getInstance().setBleToAdd(false);
                        CommonDataUtil.getInstance().autoLogin(this, homeId);
                        isNotNeedToLogin = true;
                    }
                    motionFrequencyPushEvent = new MotionFrequencyPushEvent(homeId, pid);
                    break;
                case LocalKey.ADD_DAILY_MEMORIES:
                    if (!TextUtils.isEmpty(homeId)) {
                        if (isNeedShowLoadingFragment()) {
                            showLoadingFragment(LoadingFragment.BLUE, getResources().getString(R.string.loging_hint));
                        }
                        CommonDataUtil.getInstance().setBleToAdd(false);
                        CommonDataUtil.getInstance().autoLogin(this, homeId);
                        isNotNeedToLogin = true;
                    }
                    HashMap<String, Object> extra1 = new HashMap<>();
                    for (String key : intent.getExtras().keySet()) {
                        extra1.put(key, intent.getExtras().get(key));
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(extra1);
                        dailyMemoriesPushEvent = new DailyMemoriesPushEvent(homeId, jsonObject.toJSONString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                case HomeConstants.CMD.KEYPAD_MEMBER_PWD_ENABLE:
                case HomeConstants.CMD.KEYPAD_MEMBER_PWD_UNABLE:
                case HomeConstants.CMD.KEYPAD_MEMBER_PWD_RESET:
                    EventBus.getDefault().postSticky(new FcmKeypadMemberPwdUpdateEvent(homeId, cmd, message));
                    break;

                case LocalKey.NEW_ACTIVITY:
                    String activityID = extras.getString(MyBaseDinsaferPushReveiver.ACTIVITY_ID);
                    DDLog.e(TAG, "NEW_ACTIVITY.  id: " + activityID);
                    EventBus.getDefault().postSticky(new NewActivityEvent(activityID));
                    break;

                case LocalKey.OMS_NOTIFICATION:
                    String url = extras.getString(MyBaseDinsaferPushReveiver.URL);
                    mPushTitleMessageEvent = new OMSNotificationEvent(cmd, title, message, url);
                    break;
                default:
                    DDLog.e(TAG, "Unhandled cmd: " + cmd);
                    break;
            }
            return;
        }

        // 2、处理添加家庭功能之前的逻辑
        if (extras.containsKey(MyBaseDinsaferPushReveiver.CMDTYPE)) {
//            通知栏点击跳转
//            1.获取deviceid，然后进行切换主机
            int code = intent.getExtras().getInt(MyBaseDinsaferPushReveiver.CODE);
            String cmdType = intent.getExtras().getString(MyBaseDinsaferPushReveiver.CMDTYPE);
            smoothToHome();

            removeAllCommonFragment();
            DDLog.i("onNewIntent", "toChangeDeviceById");
            final String homeId = intent.getStringExtra(MyBaseDinsaferPushReveiver.HOME_ID);
            if (!TextUtils.isEmpty(homeId)) {
                if (isNeedShowLoadingFragment()) {
                    showLoadingFragment(LoadingFragment.BLUE, getResources().getString(R.string.loging_hint));
                }
                CommonDataUtil.getInstance().setBleToAdd(false);
                CommonDataUtil.getInstance().autoLogin(this, homeId);
            }
            isNotNeedToLogin = true;
            doorBellImageUrl = intent.getStringExtra(MyBaseDinsaferPushReveiver.IMG);
            doorBellContent = intent.getStringExtra(MyBaseDinsaferPushReveiver.ALERT);

            if (!TextUtils.isEmpty(cmdType) &&
                    ((cmdType.equals(LocalKey.ARM_KEY) || cmdType.equals(LocalKey.HOMEARM_KEY)) && code == 1)) {
                isOpenDialogReadyToArm = true;
                readyToArmCmd = cmdType;

                pluginId = intent.getExtras().getString(MyBaseDinsaferPushReveiver.PLUGINID);
                pluginName = intent.getExtras().getString(MyBaseDinsaferPushReveiver.PLUGIN_NAME);

                category = intent.getExtras().getString(MyBaseDinsaferPushReveiver.CATEGORY);
                subCategory = intent.getExtras().getString(MyBaseDinsaferPushReveiver.SUBCATEGORY);
//                    addCommonFragment(ReadyToArmDialogFragment.newInstance(cmdType));
            } else if (!TextUtils.isEmpty(cmdType)
                    && (cmdType.equals(LocalKey.IPC_MOTION_DETECTED))) {
//                //IPC_MOTION_DETECTED
                HashMap<String, Object> extra = new HashMap<>();
                for (String key : intent.getExtras().keySet()) {
                    extra.put(key, intent.getExtras().get(key));
                }

                try {
                    JSONObject jsonObject = new JSONObject(extra);
                    ipcMotionDetectedPushEvent = new IPCMotionDetectedPushEvent(homeId, jsonObject.toJSONString());
                    EventBus.getDefault().postSticky(ipcMotionDetectedPushEvent);
                    ipcMotionDetectedPushEvent = null;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (!TextUtils.isEmpty(cmdType) && (cmdType.equals(LocalKey.IPC_ALERT_EXHAUSTED))) {
//                //IPC_ALERT_EXHAUSTED
                HashMap<String, Object> extra = new HashMap<>();
                for (String key : intent.getExtras().keySet()) {
                    extra.put(key, intent.getExtras().get(key));
                }

                try {
                    JSONObject jsonObject = new JSONObject(extra);
                    ipcAlertExhaustedEvent = new IPCAlertExhaustedEvent(jsonObject.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (!TextUtils.isEmpty(cmdType)
                    && (cmdType.equals(LocalKey.NO_ACTION_NOTICE))) {
                careModeNoActionEvent = new CareModeNoActionEvent();
            } else if (!TextUtils.isEmpty(cmdType)
                    && (cmdType.equals(LocalKey.DOORBELL_CALL))) {
//                //DOORBELL_CALL
                HashMap<String, Object> extra = new HashMap<>();
                for (String key : intent.getExtras().keySet()) {
                    extra.put(key, intent.getExtras().get(key));
                }

                try {
                    JSONObject jsonObject = new JSONObject(extra);
                    doorbellCallPushEvent = new DoorbellCallPushEvent(homeId, jsonObject.toJSONString());
//                        EventBus.getDefault().postSticky(ipcMotionDetectedPushEvent);
//                    Log.d(TAG, "onNewIntent: postSticky IPCMotionDetectedPushEvent");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private boolean isNotNeedToLogin = false;

    public boolean isOpenDialogReadyToArm = false;

    public String readyToArmCmd;
    public String pluginName;
    public String pluginId;
    public String category;
    public String subCategory;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ScanQREvent ev) {
        isNotNeedToLogin = true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(CloseActivityEvent ev) {
        isNotNeedToLogin = ev.isNotNeedLoading();
    }

    public boolean isNotNeedToLogin() {
        return isNotNeedToLogin;
    }

    public void setNotNeedToLogin(boolean notNeedToLogin) {
        isNotNeedToLogin = notNeedToLogin;
    }

    @Override
    public void onStart() {
        super.onStart();
//        只要在ap模式下，就不会进入主页
        String wifiSSID = DDSystemUtil.getWIFISSID(this);
        if (wifiSSID != null && wifiSSID.startsWith(APIKey.AP_NAME)) {
            LoginActivity.start(this, false);
            finish();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        DDLog.i(TEST_TIME, "PageDisplayed");
        String wifiSSID = DDSystemUtil.getWIFISSID(this);
        DDLog.i(TAG, wifiSSID + ":" + isApStepFragmentExit());
        if ((wifiSSID != null && wifiSSID.replace("\"", "").startsWith(APIKey.AP_NAME)) || isApStepFragmentExit()) {

        } else if (!isNotNeedToLogin) {
            if (DinSDK.getUserInstance().isLogin()) {
                if (DBUtil.Exists(DBKey.APP_PASSWORD)) {
                    PasswordActivity activity = PasswordActivity.newInstance(true);
                    activity.setCallBack(new PasswordActivity.IPasswordCallBack() {
                        @Override
                        public void onSuccess() {
                            if (isNeedShowLoadingFragment()) {
                                showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
                            }
                            checkAndChangeHome();
                        }

                        @Override
                        public void onFail() {
                            MainActivity.this.onFail();
                        }
                    });
                    addCommonFragment(activity);
                } else {
                    if (isNeedShowLoadingFragment()) {
                        showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
                    }
                    checkAndChangeHome();
                }
            } else {
                doAfterStart();
            }
        } else {
            isNotNeedToLogin = false;
        }
        BmtManager.getInstance().startPolling();
    }

    @Override
    public void onStop() {
        super.onStop();
        BmtManager.getInstance().stopPolling();
    }

    private void checkAndChangeHome() {
        DDLog.i(TAG, "checkAndChangeHome.");
        if (mAuthorityUpdateEvent != null) {
            CommonDataUtil.getInstance().fetchHomeList(mAuthorityUpdateEvent.getHomeId());
        } else if (ipcMotionDetectedPushEvent != null) {
            CommonDataUtil.getInstance().fetchHomeList(ipcMotionDetectedPushEvent.getHomeId());
        } else if (doorbellCallPushEvent != null) {
            CommonDataUtil.getInstance().fetchHomeList(doorbellCallPushEvent.getHomeId());
        } else if (null != motionFrequencyPushEvent) {
            CommonDataUtil.getInstance().fetchHomeList(motionFrequencyPushEvent.getHomeId());
        } else if (null != dailyMemoriesPushEvent) {
            CommonDataUtil.getInstance().fetchHomeList(dailyMemoriesPushEvent.getHomeId());
        } else {
            CommonDataUtil.getInstance().fetchHomeList(wantSelectHomeId);
            wantSelectHomeId = null;
        }
    }

    private void doAfterStart() {
        DDLog.i(TAG, "doAfterStart.");
        if (null == DinCore.getUserInstance().getUser()
                || TextUtils.isEmpty(DinCore.getUserInstance().getUser().getUid())) {
            Log.e(TAG, "doAfterStart: 未登录，跳转登录页");
            CommonDataUtil.getInstance().tryFixUserPwdIfNeedClean();
            LoginActivity.start(this, false);
            finish();
        } else {
//            Log.w(TAG, "doAfterStart: 已登录，重新自动登录");
//            if (isNeedShowLoadingFragment()) {
//                showLoadingFragment(LoadingFragment.BLUE, getResources().getString(R.string.loging_hint));
//            }
//            CommonDataUtil.getInstance().autoLogin(this, null);
            CommonDataUtil.getInstance().fetchHomeList(null);
        }
    }

    /**
     * 是否需要显示LoadingFragmeng
     * 在首页的时候不需要显示LoadingFragment
     *
     * @return true 需要显示LoadingFragment
     */
    private boolean isNeedShowLoadingFragment() {
        final ArrayList<BaseFragment> fragmentList = getFragmentList();
        final boolean hadFragment = null != fragmentList && fragmentList.size() > 0;
        final boolean notOnMainPage = SETTING_POSITION == mCurrentStatue || USER_POSITION == mCurrentStatue;
        return hadFragment || notOnMainPage;
    }

    @Override
    public void onSuccess() {
        doAfterStart();
    }

    @Override
    public void onFail() {
        CommonDataUtil.getInstance().unSetAlias();
        CommonDataUtil.getInstance().clearDB();
        CommonDataUtil.getInstance().setUser(null);
        UserManager.getInstance().cleanCache();
        removeAllCommonFragment();
        showSOSLayout(false);
        toCloseWs(true);
        LoginActivity.start(this, false);
        finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(NeedLogoutEvent ev) {
        if (!isApStepFragmentExit() && null != DinSDK.getUserInstance().getUser()
                && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUid())) {
            toLogout(true, false);
        }
    }

    /**
     * 退出登录
     *
     * @param isNeedToRequestServer 是否需要请求服务器的退出登录接口
     *                              true:请求服务器退出登录接口
     */
    public void toLogout(boolean isNeedToRequestServer, boolean isAbnormalLogout) {
        EventBus.getDefault().post(new HadLogoutPreEvent());
        CommonDataUtil.getInstance().unSetAlias();

        if (isNeedToRequestServer) {
            DinSDK.getUserInstance().logout(new ILogoutCallback() {
                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "logout success");
                    toCloseWs(true);
                    CommonDataUtil.getInstance().clearDB();
                    LoginActivity.start(MainActivity.this, isAbnormalLogout);
                    finish();
                    EventBus.getDefault().post(new HadLogoutEvent());
                }
            });
        } else {
            toCloseWs(true);
            CommonDataUtil.getInstance().clearDB();
            DinSDK.getUserInstance().cleanCache();
            LoginActivity.start(this, isAbnormalLogout);
            finish();
            EventBus.getDefault().post(new HadLogoutEvent());
        }

    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    private void initMainData() {
        if (!DBUtil.Exists(DBKey.IS_IN_MAIN_SECTION)) {
            DBUtil.Put(DBKey.IS_IN_MAIN_SECTION, false);
        }
    }

    @Override
    public Resources getResources() {
        Resources res = super.getResources();
        Configuration config = new Configuration();
        config.setToDefaults();
        res.updateConfiguration(config, res.getDisplayMetrics());
        return res;
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        DDLog.d(TAG, "onWindowFocusChanged:" + hasFocus);
        try {
            if (!getFragmentList().isEmpty()) {
                ((BaseFragment) getFragmentList().get(getFragmentList().size() - 1)).onWindowFocusChanged(hasFocus);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        DDLog.d(TAG, "hasFocus:" + hasFocus);
        if (hasFocus) {
            EventBus.getDefault().post(new WindowFocusChangedEvent());
            try {
                checkIsCoap();
            } catch (Exception e) {

            }
        } else {
            CoapController.getInstance().clean();
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        // Bundle stats:
        //   android:viewHierarchyState [size=1164]
        //     android:views [size=1060]
        //   androidx.lifecycle.BundlableSavedStateRegistry.key [size=522172]
        //     android:support:activity-result [size=120860]
        //       KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS [size=114516]
        //       KEY_COMPONENT_ACTIVITY_REGISTERED_RCS [size=5748]
        //     android:support:fragments [size=401052]
        //       android:support:fragments [size=400980]

        // 不用系统保存状态了，我们自己的Fragment在恢复的时候发现没有时回重新add进去
        final Bundle registryBundle = outState.getBundle("androidx.lifecycle.BundlableSavedStateRegistry.key");
        if (null != registryBundle) {
            registryBundle.putBundle("android:support:fragments", null);
        }
        // 部分手机：
        // Bundle stats:
        // android:viewHierarchyState [size=1740]
        // android:views [size=1636]
        // android:support:fragments [size=593892]
        outState.putParcelable("android:support:fragments", null);

        Gson gson = new Gson();
        //转换成json数据，再保存
        String strJson = gson.toJson(getFragmentNameList());

        DDLog.d(TAG, "onSaveInstanceState,now fragment lists names are " + getFragmentNameList().toString());
        outState.putString("fragment_name_list", strJson);

        outState.putBoolean("isArm", isArm);
        outState.putBoolean("isHasStart", isHasStart);
        outState.putBoolean("isFirstStart", isFirstStart);
        outState.putInt("mCurrentStatue", mCurrentStatue);
        outState.putString("doorBellImageUrl", doorBellImageUrl);
        outState.putString("doorBellContent", doorBellContent);

        if (outState != null && outState.containsKey(keyRegistry)) {
            outState.remove(keyRegistry);
        }
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        DDLog.d(TAG, "onRestoreInstanceState，恢复数据");

        isArm = savedInstanceState.getBoolean("isArm", isArm);
        isHasStart = savedInstanceState.getBoolean("isHasStart", isHasStart);
        isFirstStart = savedInstanceState.getBoolean("isFirstStart", isFirstStart);
        mCurrentStatue = savedInstanceState.getInt("mCurrentStatue", mCurrentStatue);
        doorBellImageUrl = savedInstanceState.getString("doorBellImageUrl", doorBellImageUrl);
        doorBellContent = savedInstanceState.getString("doorBellContent", doorBellContent);

    }

    private void resumeFragmentList(Bundle savedInstanceState) {
        /**
         *  --------恢复fragmentlist--------
         *
         *  fragment管理器默认会处理突然会回收的事件，所以fragment都还在，只需要我们自己去找回
         *  通过 tag
         */
        Gson gson = new Gson();
        ArrayList<String> fragmentNameList = new ArrayList<String>();

        DDLog.d(TAG, "resumeFragmentList,恢复数据之前，fragment lists are " + getFragmentList().toString());
        fragmentNameList = gson.fromJson(savedInstanceState.getString("fragment_name_list"), new TypeToken<List<String>>() {
        }.getType());

        for (String simpleName : fragmentNameList) {
            DDLog.d(TAG, "resumeFragmentList，simpleName is " + simpleName);
            if (!simpleName.contains("LoadingFragment") && !getFragmentNameList().contains(simpleName)) {
                BaseFragment baseFragment = (BaseFragment) getSupportFragmentManager().findFragmentByTag(simpleName);
                if (baseFragment != null) {
                    getFragmentList().add(baseFragment);
                    getFragmentNameList().add(simpleName);
                }
            }
        }
        DDLog.d(TAG, "resumeFragmentList,恢复数据之后，fragment lists are " + getFragmentList().toString());
    }

    private void checkIsCoap() {

        /**
         * 判断是否可以使用局域网
         */
        CommonDataUtil.getInstance().setCanCoap(false);


        final String lanIp = DeviceHelper.getString(DinSDK.getHomeInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID()),
                PanelDataKey.Panel.LAN_IP, null);
        if (TextUtils.isEmpty(lanIp)) {
            DDLog.d(getClass().getSimpleName(), "coap: " + " null  & not online");
            CommonDataUtil.getInstance().setCanCoap(false);
            CoapController.getInstance().clean();

        } else {
            CoapController.getInstance().setIp(lanIp);
            CoapController.getInstance().getIsCanCoap(new CoapController.CallBack() {
                @Override
                public void onSuccess() {
                }

                @Override
                public void onFail() {
                    DDLog.d(getClass().getSimpleName(), "call by coap: " + "check can coap use fail");
                    CommonDataUtil.getInstance().setCanCoap(false);
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(NeedLoginAgainEvent ev) {
        DDLog.i(TAG, "NeedLoginAgainEvent");
        CommonDataUtil.getInstance().autoLogin(this);
    }

    @Override
    public void onLogout() {
        DDLog.i(TAG, "onLogout");
        toLogout(false, true);
    }

    @Override
    public void onNeedAutoLogin() {
        DDLog.i(TAG, "onNeedAutoLogin");
        CommonDataUtil.getInstance().autoLogin(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DsCamNetWorkSetting ev) {
        addCommonFragment(DsCamNetworkBleScanFragment.newInstance(ev.getDeviceID()));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DsCamUpgradeEvent ev) {
        addCommonFragment(DsCamUpgradeFragment.newInstance(ev.getDeviceId(), true));
    }

    @Override
    public void onAppFrontBackChanged(boolean front) {
        DDLog.i(TAG, "onAppFrontBackChanged. front: " + front);
        // APP前后台切换
        EventBus.getDefault().post(new AppStatePreEvent(!front));
        if (front) {
            MotionDownloadManager.get().resumeAll();
        } else {
            MotionDownloadManager.get().pauseAll();
            if (!isNotNeedToLogin
                    && null != DinSDK.getUserInstance()
                    && DinSDK.getUserInstance().isLogin()) {
                DinHome.getInstance().stopE2EConnection(true);
                toCloseWs(false);
                EventBus.getDefault().post(new LifeEvent());
            }
        }
        EventBus.getDefault().post(new AppStateEvent(!front));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(HideKeyboardOnTouchBlankEvent event) {
        hideKeyboardOnTouchBlank = event.isHideKeyboardOnTouchBlank();
    }

    public void setHideKeyboardOnTouchBlank(boolean hideKeyboardOnTouchBlank) {
        this.hideKeyboardOnTouchBlank = hideKeyboardOnTouchBlank;
    }

    /**
     * 点击空白区域隐藏键盘.
     */
    @Override
    public boolean dispatchTouchEvent(MotionEvent me) {
        if (hideKeyboardOnTouchBlank && me.getAction() == MotionEvent.ACTION_DOWN) {  //把操作放在用户点击的时候
            View v = getCurrentFocus();      //得到当前页面的焦点,ps:有输入框的页面焦点一般会被输入框占据
            if (isShouldHideKeyboard(v, me)) { //判断用户点击的是否是输入框以外的区域
                v.clearFocus();
                hideKeyboard(v.getWindowToken());   //收起键盘
            }
        }
        return super.dispatchTouchEvent(me);
    }

    /**
     * 根据EditText所在坐标和用户点击的坐标相对比，来判断是否隐藏键盘，因为当用户点击EditText时则不能隐藏
     *
     * @param v
     * @param event
     * @return
     */
    private boolean isShouldHideKeyboard(View v, MotionEvent event) {
        if (v != null && (v instanceof EditText)) {  //判断得到的焦点控件是否包含EditText
            int[] l = {0, 0};
            v.getLocationInWindow(l);
            int left = l[0],    //得到输入框在屏幕中上下左右的位置
                    top = l[1],
                    bottom = top + v.getHeight(),
                    right = left + v.getWidth();
            if (event.getX() > left && event.getX() < right
                    && event.getY() > top && event.getY() < bottom) {
                // 点击位置如果是EditText的区域，忽略它，不收起键盘。
                return false;
            } else {
                return true;
            }
        }
        // 如果焦点不是EditText则忽略
        return false;
    }

    /**
     * 获取InputMethodManager，隐藏软键盘
     *
     * @param token
     */
    private void hideKeyboard(IBinder token) {
        if (token != null) {
            try {
                InputMethodManager im = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                im.hideSoftInputFromWindow(token, InputMethodManager.HIDE_NOT_ALWAYS);
            } catch (Exception e) {

            }
        }
    }

    private boolean checkAdTime(AdEntry adEntry) {
        if (!DBUtil.Exists(DBKey.AD_SHOW_TIME)) {
            DBUtil.Put(DBKey.AD_SHOW_TIME, System.currentTimeMillis());
            return true;
        } else {
            long time = DBUtil.Lum(DBKey.AD_SHOW_TIME);
            long current = System.currentTimeMillis();
            //int,广告的显示规则,数组中各个成员的rule都是一样的
            //-1：每次都显示，0：只显示一次，>0：广告出现的周期，单位秒，大于等于这个周期的时间都需要显示
            if (DBUtil.Exists(DBKey.AD_SHOW_TIME) && adEntry.getResult().get(0).getRule() == 0) {
                return false;
            } else if ((current - time) / 1000 >= adEntry.getResult().get(0).getRule()) {
                DBUtil.Put(DBKey.AD_SHOW_TIME, current);
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 检查APP更新
     */
    public void checkAppUpgrade() {
        if (isFirstStart()) {
            AdEntry adEntry = DDSystemUtil.getAdList(true);
            DDLog.i(TAG, "adEntry:" + adEntry);
            DDLog.i(TAG, "getMainActivity().isFirstStart():" + isFirstStart());
            if (adEntry != null && checkAdTime(adEntry) && !isApStepFragmentExit()) {
                DDLog.i(TAG, "size" + adEntry.getResult().size());
                addCommonFragment(AdFragment.newInstance());
            }
            setFirstStart(false);
            DDSystemUtil.checkUpdate(this);
        }
    }

    public void getImagePopup() {
        if (isFirstStart()) {
            DDSystemUtil.getPopupList(CommonDataUtil.getInstance().getUserInfo().getUser_id(), new IDefaultCallBack2<ArrayList<PopupEntry.ImageItem>>() {
                @Override
                public void onSuccess(ArrayList<PopupEntry.ImageItem> popupEntryList) {
                    DDLog.i(TAG, "getImagePopup. onSuccess:" + popupEntryList);
                    DDLog.i(TAG, "getMainActivity().isFirstStart():" + isFirstStart());
                    if (popupEntryList != null && !isApStepFragmentExit()) {
                        addCommonFragment(PopupFragment.getInstance((ArrayList<PopupEntry.ImageItem>) popupEntryList));
                    }
                }

                @Override
                public void onError(int i, String s) {
                    DDLog.e(TAG, "getImagePopup. onError: " + i + " " + s);
                }
            });

        }
    }

    /**
     * 设置红外灵敏度模式进入倒计时5min
     */
    public void sendExitPirSettingMsg() {
        DDLog.i(TAG, "sendExitPirSettingMsg");
        if (mHandler.hasMessages(EXIT_PIR_SETTING)) {
            mHandler.removeMessages(EXIT_PIR_SETTING);
        }
        mHandler.sendEmptyMessageDelayed(EXIT_PIR_SETTING, PIR_SETTING_TIMEOUT);
    }

    public void cancelExitPirSettingMsg() {
        DDLog.i(TAG, "cancelExitPirSettingMsg");
        if (mHandler.hasMessages(EXIT_PIR_SETTING)) {
            mHandler.removeMessages(EXIT_PIR_SETTING);
        }
    }
}
