package com.dinsafer.module.main.model;

import androidx.annotation.NonNull;

import com.dinsafer.util.DDLog;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * 记录主机延时布防操作倒计时的执行状态
 * 首页卡片滑动后多张主机卡片状态同步
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/5/12 14:34
 */
public class MainPanelArmDelayRecorder {
    private static final String TAG = "MainPanelArmDelayRecorder";
    private final int ARM_STATUS_UNKNOWN = -1;

    @NonNull
    private final String panelId;
    private final List<WeakReference<OnArmDelayProgressUpdateListener>> mListener;

    private final AtomicInteger mOperatingArmStatus = new AtomicInteger(ARM_STATUS_UNKNOWN);
    private final AtomicBoolean mCounting = new AtomicBoolean(false);

    public MainPanelArmDelayRecorder(@NonNull String panelId) {
        this.panelId = panelId;
        mListener = new ArrayList<>();
    }

    private final AtomicInteger mCounter = new AtomicInteger(-1);
    private Subscription mCounterSubscribe;

    /**
     * 点击Icon操作时，先记录当前操作的布撤防状态、
     */
    public void setOperatingStatus(int status) {
        cancelScheduleArmDelay();
        mOperatingArmStatus.set(status);
    }

    public void resetOperatingStatus() {
        mOperatingArmStatus.set(ARM_STATUS_UNKNOWN);
    }

    public int getOperatingStatus() {
        return mOperatingArmStatus.get();
    }

    /**
     * 获取当前延时布防的倒计时
     *
     * @return -1 表示当前不在倒计时
     */
    public int getArmDelaySecond() {
        return mCounter.get();
    }

    /**
     * 尝试开始延时布防的倒计时
     * 如果倒计时时间<=0，会取消之前的倒计时
     *
     * @param exitDelay 倒计时时间
     * @return 是否开启倒计时
     */
    public boolean tryScheduleArmDelay(final int exitDelay) {
        cancelScheduleArmDelay();
        if (0 >= exitDelay) {
            resetOperatingStatus();
            return false;
        }
        DDLog.d(TAG, "trySchedule, exitDelay = " + exitDelay);
        mCounter.set(exitDelay);
        mCounting.set(true);
        mCounterSubscribe = Observable.interval(0, 1, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                        DDLog.d(TAG, "onCompleted");
                        mCounter.set(-1);
                    }

                    @Override
                    public void onError(Throwable e) {
                        DDLog.e(TAG, "onError: ");
                        e.printStackTrace();

                        mCounter.set(-1);
                        notifyArmDelayUpdate(0);
                        cancelScheduleArmDelay();
                    }

                    @Override
                    public void onNext(Object o) {
                        if (!mCounting.get()) {
                            return;
                        }
                        int result = mCounter.getAndDecrement();
                        if (result > 0) {
                            notifyArmDelayUpdate(result);
                        } else {
                            notifyArmDelayUpdate(0);
                            cancelScheduleArmDelay();
                        }
                    }
                });
        return true;
    }

    public void cancelScheduleArmDelay() {
        DDLog.v(TAG, "cancelScheduleArmDelay");
        mCounting.set(false);
        if (null != mCounterSubscribe
                && !mCounterSubscribe.isUnsubscribed()) {
            mCounterSubscribe.unsubscribe();
            mCounterSubscribe = null;
        }
        mCounter.set(-1);
    }

    public void addArmDelayProgressUpdateListener(@NonNull OnArmDelayProgressUpdateListener l) {
        synchronized (mListener) {
            boolean existed = false;
            for (int i = 0; i < mListener.size(); i++) {
                OnArmDelayProgressUpdateListener local = mListener.get(i).get();
                if (local == l) {
                    existed = true;
                    break;
                }
            }
            if (!existed) {
                mListener.add(new WeakReference<>(l));
            }
        }
    }

    public void removeArmDelayProgressUpdateListener(@NonNull OnArmDelayProgressUpdateListener l) {
        synchronized (mListener) {
            int index = -1;
            for (int i = 0; i < mListener.size(); i++) {
                OnArmDelayProgressUpdateListener local = mListener.get(i).get();
                if (local == l) {
                    index = i;
                    break;
                }
            }
            if (-1 != index) {
                mListener.remove(index);
            }
        }
    }

    public void resetAndCleanListener() {
        cancelScheduleArmDelay();
        synchronized (mListener) {
            mListener.clear();
        }
    }

    public void cleanAllListener() {
        synchronized (mListener) {
            mListener.clear();
        }
    }

    private void notifyArmDelayUpdate(int second) {
        synchronized (mListener) {
            final List<WeakReference<OnArmDelayProgressUpdateListener>> needDeleted = new ArrayList<>();
            for (WeakReference<OnArmDelayProgressUpdateListener> l : mListener) {
                OnArmDelayProgressUpdateListener listener = l.get();
                if (listener != null) {
                    listener.onArmDelayUpdate(second);
                } else {
                    needDeleted.add(l);
                }
            }

            if (needDeleted.size() > 0) {
                mListener.removeAll(needDeleted);
            }
            DDLog.v(TAG, "notifyArmDelayUpdate, listener's size=" + mListener.size());
        }
    }

    public interface OnArmDelayProgressUpdateListener {
        /**
         * 延时布防秒数更新
         *
         * @param progress 当前所剩秒数
         */
        void onArmDelayUpdate(int progress);
    }
}
