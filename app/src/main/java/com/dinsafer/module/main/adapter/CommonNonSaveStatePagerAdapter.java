package com.dinsafer.module.main.adapter;

import android.os.Bundle;
import android.os.Parcelable;

import androidx.fragment.app.FragmentManager;

import com.dinsafer.module.BaseFragment;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/6/20.
 */
public class CommonNonSaveStatePagerAdapter extends CommonPagerAdapter {

    public CommonNonSaveStatePagerAdapter(FragmentManager fm, ArrayList<BaseFragment> datas) {
        super(fm, datas);
    }

    @Override
    public Parcelable saveState() {
        Bundle bundle = (Bundle) super.saveState();
        bundle.putParcelableArray("states", null);
        return bundle;
    }
}
