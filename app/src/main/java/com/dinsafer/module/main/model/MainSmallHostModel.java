package com.dinsafer.module.main.model;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.HostItemDelegate;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.model.panel.MainPanelSmallHostViewHolder;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.ui.device.MainDeviceStatusView;
import com.dinsafer.util.PluginWidgetStyleUtil;

/**
 * @describe：
 * @date：2022/11/21
 * @author: create by Sydnee
 */
public class MainSmallHostModel extends BaseMainHostModel {
    private volatile MainPanelSmallHostViewHolder smallHostViewHolder;

    public MainSmallHostModel(BaseFragment baseFragment, PanelDevice panelDevice, MainPanelArmDelayRecorder armDelayRecorder, @NonNull MainWidgetBean bean, OnWidgetItemListener onWidgetItemListener,
                              MainDeviceStatusView.OnDeviceStatusActionClickListener onDeviceStatusActionClickListener, final int adapterItemId) {
        super(baseFragment, panelDevice, armDelayRecorder, bean, onWidgetItemListener, onDeviceStatusActionClickListener, PluginWidgetStyleUtil.PANEL_SMALL, adapterItemId);
    }

    @Override
    public HostItemDelegate getHostItemDelegate() {
        return smallHostViewHolder;
    }

    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding viewDataBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            smallHostViewHolder = new MainPanelSmallHostViewHolder(viewDataBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, smallHostViewHolder);
        } else {
            smallHostViewHolder = (MainPanelSmallHostViewHolder) tag;
        }

        initView(holder);
        smallHostViewHolder.initListener();
    }

}
