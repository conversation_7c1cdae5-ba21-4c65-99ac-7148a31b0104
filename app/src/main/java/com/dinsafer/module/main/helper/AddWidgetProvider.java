package com.dinsafer.module.main.helper;

import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.common.PluginManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.view.AddWidgetFragment;
import com.dinsafer.module.settting.ui.model.BaseDeviceSettingPlugModel;
import com.dinsafer.module.settting.ui.model.DeviceSettingDeviderModel;
import com.dinsafer.module.settting.ui.model.DeviceSettingTitleModel;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.SettingInfoHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2022/10/27
 * @author: create by Sydnee
 */
public class AddWidgetProvider {

    /**
     * BMT电源
     */
    public static List<BindModel> getBmtItems(BaseFragment baseFragment) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList<BindModel> data = new ArrayList<>();
        if (AppConfig.Plugins.SUPPORT_BMT_HP5000
                && BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_HP5000) > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.power_core_1_point_0),
                    R.drawable.icon_device_setting_power,
                    BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_HP5000),
                    false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            DinConst.TYPE_BMT_HP5000,
                            baseFragment.getResources().getString(R.string.power_core_1_point_0)));
                }
            });

            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE20
                && BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERCORE20) > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.power_core_2_point_0),
                    R.drawable.icon_device_setting_power,
                    BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERCORE20),
                    false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            DinConst.TYPE_BMT_POWERCORE20,
                            baseFragment.getResources().getString(R.string.power_core_2_point_0)));
                }
            });

            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE30
                && BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERCORE30) > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.power_core_3_point_0),
                    R.drawable.icon_device_setting_power,
                    BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERCORE30),
                    false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            DinConst.TYPE_BMT_POWERCORE30,
                            baseFragment.getResources().getString(R.string.power_core_3_point_0)));
                }
            });

            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (AppConfig.Plugins.SUPPORT_BMT_POWERSTORE
                && BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERSTORE) > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.power_store),
                    R.drawable.icon_device_setting_power,
                    BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERSTORE),
                    false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            DinConst.TYPE_BMT_POWERSTORE,
                            baseFragment.getResources().getString(R.string.power_store)));
                }
            });

            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE
                && BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERPULSE) > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.power_pulse),
                    R.drawable.icon_device_setting_power,
                    BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERPULSE),
                    false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            DinConst.TYPE_BMT_POWERPULSE,
                            baseFragment.getResources().getString(R.string.power_pulse)));
                }
            });

            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (data.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.energy)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            data.remove(data.size() - 1);
            groupItems.addAll(data);
        }
        return groupItems;
    }

    /**
     * IPC管理
     */
    public static List<BindModel> getIpcItems(BaseFragment baseFragment) {


        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList<BindModel> data = new ArrayList<>();

        // IPC
        if (AppConfig.Plugins.SUPPORT_IPC
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && IPCManager.getInstance().getAllNotDeleteDevicesSize() > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.smart_camera),
                    R.drawable.icon_device_setting_ipc, IPCManager.getInstance().getAllNotDeleteDevicesSize(),
                    false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            AddWidgetFragment.DEVICE_TYPE_IPC,
                            baseFragment.getResources().getString(R.string.smart_camera)));
                }
            });

            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 自研门铃
        if (AppConfig.Plugins.SUPPORT_DS_DOORBELL
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && IPCManager.getInstance().getNotDeletedDoorbellList().size() > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.video_doorbell), R.drawable.icon_device_setting_video_doorbell,
                    IPCManager.getInstance().getNotDeletedDoorbellList().size(), false, false) {
                @Override
                public void onDo(View view) {
                    super.onDo(view);
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            DinConst.TYPE_DSDOORBELL,
                            baseFragment.getResources().getString(R.string.video_doorbell)));
                }
            });
            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (data.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.video_monitoring)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            data.remove(data.size() - 1);
            groupItems.addAll(data);
        }
        return groupItems;
    }

    /**
     * 获取需要显示的主机和配件Item
     */
    public static List<BindModel> getAccessoryItems(BaseFragment baseFragment, boolean haveLoading) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList<BindModel> list = new ArrayList<>();

        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final Device device = DinHome.getInstance().getDevice(panelId);
        final boolean haveDevice = null != device && !device.getFlagDeleted();
        boolean upgrading = DeviceHelper.getBoolean(device, PanelDataKey.Panel.UPGRADING, false);
        boolean panelOnline = CommonDataUtil.getInstance().isPanelOnline();
        panelOnline = panelOnline && !upgrading;
        int count;

        // 主机
        if (haveDevice) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.panel),
                    R.drawable.icon_device_setting_panel, -1, haveLoading, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            PanelConstant.DeviceType.PANEL,
                            baseFragment.getResources().getString(R.string.panel)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 门窗探测器
        count = PluginManager.getInstance().getNotDeletedDoorSensorList().size();
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_management_door_sensor),
                    R.drawable.icon_device_setting_door_sensor, count,
                    haveLoading, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            PanelConstant.DeviceType.DOOR_WINDOW_SENSOR,
                            baseFragment.getResources().getString(R.string.device_management_door_sensor)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 中继插座
        count = PluginManager.getInstance().getNotDeletedRepeaterPlugList().size();
        if (count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_signal_repeater_plug),
                    R.drawable.icon_device_setting_repeater, count,
                    haveLoading, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG,
                            baseFragment.getResources().getString(R.string.device_managent_signal_repeater_plug)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 智能插座
        count = PluginManager.getInstance().getNotDeletedSmartPlugListWithoutRepeater().size();
        if (count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_smart_plug),
                    R.drawable.icon_device_setting_switch, count,
                    haveLoading, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(AddWidgetFragment.newInstance(
                            PanelConstant.DeviceType.SMART_PLUG,
                            baseFragment.getResources().getString(R.string.device_managent_smart_plug)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (list.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(),
                    baseFragment.getResources().getString(R.string.alarm_system)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            list.remove(list.size() - 1);
            groupItems.addAll(list);
        }

        return groupItems;
    }
}
