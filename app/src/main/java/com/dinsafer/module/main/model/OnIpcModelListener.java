package com.dinsafer.module.main.model;

import androidx.annotation.Nullable;

import com.dinsafer.dincore.common.Device;

/**
 * @describe：
 * @date：2022/12/1
 * @author: create by Sydnee
 */
public interface OnIpcModelListener extends BaseModelListener {

    /**
     * @param multiScreenId 当前更新的多屏卡片的ID。
     *                      null表示更新的非首页卡片的ID，需要检查更新全部卡片；
     *                      非空表示更新的是指定的多屏卡片
     */
    void onMultiPlayDsCamIdsChange(@Nullable final String multiScreenId);

    void onCamConnectStatus(String id, Device device);
}
