package com.dinsafer.module.main.model;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.module.main.entry.MainWidgetBean;

/**
 * @describe：
 * @date：2022/9/30
 * @author: create by Sydnee
 */
public interface OnWidgetItemListener {
    /**
     * widget点击回调
     *
     * @param isEditMode
     * @param sectionType
     * @param tuyaItemPlus
     */
    void onItemClick(boolean isEditMode, int sectionType, TuyaItemPlus tuyaItemPlus, @NonNull String modelId, @NonNull String modelType);

    /**
     * bmt / battery widget点击回调
     *
     * @param isEditMode
     * @param sectionType
     * @param device
     * @param name
     */
    void onBmtOrBatteryItemClick(boolean isEditMode, int sectionType, int bmtOrBattery, Device device, String name);

    /**
     * widget长按回调
     *
     * @param isEditMode
     * @param viewHolder
     * @param position
     */
    void onItemLongClick(boolean isEditMode, RecyclerView.ViewHolder viewHolder, int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType);

    /**
     * widget点击删除按钮回调
     *
     * @param position
     */
    void onDeleteIconClick(int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType);

    /**
     * widget在已被删除的状态，点击回调
     *
     * @param position
     */
    void onUnavailableStateViewClick(int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType);
}
