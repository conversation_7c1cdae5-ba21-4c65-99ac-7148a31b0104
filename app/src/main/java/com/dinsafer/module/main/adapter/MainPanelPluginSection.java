package com.dinsafer.module.main.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.model.panel.MainPanelPluginItemViewHolder;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;

import java.util.ArrayList;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;

/**
 * 首页Panel 插件 分组
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/2 5:49 PM
 */
public class MainPanelPluginSection extends MainPanelBaseSection<TuyaItemPlus> {
    private final int ROW_ITEM_COUNT = 2;

    public MainPanelPluginSection(Context context, String tittle, ArrayList<TuyaItemPlus> datas) {
        super(context, SectionParameters.builder()
                .itemResourceId(R.layout.main_section_panel_item_plugin)
                .headerResourceId(R.layout.main_section_panel_item_header)
                .build(), tittle, datas);
    }

    public MainPanelPluginSection(Context context, String tittle, ArrayList<TuyaItemPlus> datas,
                                  OnItemClickListener itemClickListener) {
        super(context, SectionParameters.builder()
                .itemResourceId(R.layout.main_section_panel_item_plugin)
                .headerResourceId(R.layout.main_section_panel_item_header)
                .build(), tittle, datas);
        setOnItemClickListener(itemClickListener);
    }

    @Override
    public int getRowItemCount() {
        return ROW_ITEM_COUNT;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new MainPanelPluginItemViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        MainPanelPluginItemViewHolder itemHolder = (MainPanelPluginItemViewHolder) holder;
        itemHolder.setRootViewEnable(
                MainPanelHelper.getInstance().isPanelEditMode()
                        || MainPanelHelper.getInstance().isFunctionEnable());

        if (position >= mData.size()) {
            itemHolder.setStatusEmpty();
            itemHolder.setRootViewVisible(false);
            return;
        }
        itemHolder.setRootViewVisible(true);

        TuyaItemPlus itemData = mData.get(position);
        String name = itemData.getName();
        final String id = itemData.getId();
        if (TextUtils.isEmpty(name)) {
            if (!TextUtils.isEmpty(itemData.getDecodeid())) {
                //   如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                name = CommonDataUtil.getInstance().getSTypeByDecodeid(itemData.getDecodeid());
            } else if (id.startsWith("!")) {
                if (!TextUtils.isEmpty(itemData.getStype())) {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(itemData.getStype());
                } else {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(itemData.getSub_category());
                }
            } else {
                name = CommonDataUtil.getInstance().getSTypeByID(id);
            }
            name = Local.s(name) + "_" + id;
        }
        itemHolder.setPluginName(name);

        itemHolder.setRootViewClickListener((View v) -> {
            if (!MainPanelHelper.getInstance().isPanelEditMode()
                    && !MainPanelHelper.getInstance().isFunctionEnable()) {
                DDLog.e(TAG, "当前Item不能点击");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                // 编辑模式下
                itemData.setShow(!itemData.isShow());
                itemHolder.setSelected(itemData.isShow());
                if (null != mOnItemClickListener) {
                    mOnItemClickListener.onItemClick(mTitle, true, v, position);
                }
            } else {
                // 非编辑模式下
                if (null != mOnItemClickListener) {
                    mOnItemClickListener.onItemClick(mTitle, false, v, position);
                }
            }
        });


        if (itemData.isNeedLoading()
                && itemData.isLoading()) {
            // 还在loading
            itemHolder.setSelected(itemData.isShow());
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusLoading(null);
            return;
        }

        boolean isPanelDeviceOffline = DeviceInfoHelper.getInstance().getCurrentDeviceInfo().isDeviceOffline();
        if (itemData.isNeedOnlineState()
                && !itemData.isOnline()) {
            // 离线
            itemHolder.setSelected(itemData.isShow());
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusFinish(mContext, false, isPanelDeviceOffline);
            return;
        }

        // 在线
        if (itemData.isNeedOnlineState()) {
            itemHolder.setStatusFinish(mContext, itemData.isOnline(), isPanelDeviceOffline);
        } else {
            itemHolder.setStatusFinishNoState(mContext);
        }

        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 编辑模式
            itemHolder.setEditMode(true);
            itemHolder.setSelected(itemData.isShow());
        } else {
            // 非编辑模式
            itemHolder.setEditMode(false);
            // 状态图标可见性及图标
            if (itemData.isHaveApart()) {
                itemHolder.setPluginStatusIconGone(true);
                itemHolder.setPluginStatueIconRes(MainPanelHelper.getInstance().getDoorApartStateIcon(
                        itemData.isNeedBlock(), itemData.isApart()));
            } else {
                itemHolder.setPluginStatusIconGone(false);
            }
        }
    }
}
