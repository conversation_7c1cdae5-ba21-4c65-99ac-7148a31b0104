package com.dinsafer.module.main.helper;

import android.graphics.Color;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.DeviceSimStatueEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.iap.ListBigBannerResponse;
import com.dinsafer.module.main.model.OnBannerModelListener;
import com.dinsafer.module.main.model.OnBmtEmergencyModelListener;
import com.dinsafer.module.main.model.OnBmtModelListener;
import com.dinsafer.module.main.model.OnBmtTodayUsageModelListener;
import com.dinsafer.module.main.model.OnHostModelListener;
import com.dinsafer.module.main.model.OnIpcModelListener;
import com.dinsafer.module.main.model.OnPluginModelListener;
import com.dinsafer.module.main.model.OnShortcutModelListener;
import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BmtChartDataEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGetFeatureEvent;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module.powerstation.event.HomePowerStationDefaultEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.util.AlertDialogManager;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：管理首页widget对应的device状态监听
 * @date：2022/11/24
 * @author: create by Sydnee
 */
public class DeviceCallBackManager {

    private final static String TAG = DeviceCallBackManager.class.getSimpleName();
    private static volatile DeviceCallBackManager INSTANCE;
    private final List<OnHostModelListener> mHostModelListeners;
    private final List<OnPluginModelListener> mPluginModelListeners;
    private final List<OnShortcutModelListener> mShortcutModelListeners;
    private final List<OnIpcModelListener> mIpcModelListeners;
    private final List<OnBmtModelListener> mBmtModelListeners;
    private final List<OnBmtTodayUsageModelListener> mBmtTodayUsageModelListeners;
    private final List<OnBmtEmergencyModelListener> mBmtEmergencyModelListeners;
    private OnBannerModelListener mBannerModelListener = null;

    public DeviceCallBackManager() {
        this.mHostModelListeners = new ArrayList<>();
        this.mPluginModelListeners = new ArrayList<>();
        this.mShortcutModelListeners = new ArrayList<>();
        this.mIpcModelListeners = new ArrayList<>();
        this.mBmtModelListeners = new ArrayList<>();
        this.mBmtTodayUsageModelListeners = new ArrayList<>();
        this.mBmtEmergencyModelListeners = new ArrayList<>();
    }

    public static DeviceCallBackManager getInstance() {
        if (INSTANCE == null) {
            synchronized (DeviceCallBackManager.class) {
                if (INSTANCE == null) {
                    INSTANCE = new DeviceCallBackManager();
                }
            }
        }
        return INSTANCE;
    }

    public void clearAll() {
        this.mHostModelListeners.clear();
        this.mPluginModelListeners.clear();
        this.mShortcutModelListeners.clear();
        this.mIpcModelListeners.clear();
        this.mBmtModelListeners.clear();
        this.mBmtTodayUsageModelListeners.clear();
        this.mBmtEmergencyModelListeners.clear();
    }

    public void checkDeviceIsNull() {
        DDLog.d(TAG, "checkDeviceIsNull.");

        synchronized (mBmtEmergencyModelListeners) {
            if (mBmtEmergencyModelListeners.size() > 0) {
                for (OnBmtEmergencyModelListener l : mBmtEmergencyModelListeners) {
                    l.checkDeviceIsNull();
                }
            }
        }

        synchronized (mBmtTodayUsageModelListeners) {
            if (mBmtTodayUsageModelListeners.size() > 0) {
                for (OnBmtTodayUsageModelListener l : mBmtTodayUsageModelListeners) {
                    l.checkDeviceIsNull();
                }
            }
        }

        synchronized (mBmtModelListeners) {
            if (mBmtModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtModelListeners) {
                    l.checkDeviceIsNull();
                }
            }
        }

        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                for (OnPluginModelListener l : mPluginModelListeners) {
                    l.checkDeviceIsNull();
                }
            }
        }

        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                for (OnShortcutModelListener l : mShortcutModelListeners) {
                    l.checkDeviceIsNull();
                }
            }
        }

        synchronized (mIpcModelListeners) {
            if (mIpcModelListeners.size() > 0) {
                for (OnIpcModelListener l : mIpcModelListeners) {
                    l.checkDeviceIsNull();
                }
            }
        }

        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                for (OnHostModelListener l : mHostModelListeners) {
                    l.checkDeviceIsNull();
                }
            }
        }
    }

    // ===================================      BMT start  =================================//

    public void addBmtModelListener(OnBmtModelListener listener) {
        synchronized (mBmtModelListeners) {
            if (null == listener || mBmtModelListeners.contains(listener)) {
                return;
            }
            mBmtModelListeners.add(listener);
            DDLog.d(TAG, "addBmtModelListener: mBmtModelListeners size: " + mBmtModelListeners.size());
        }
    }

    public void removeBmtModelListener(OnBmtModelListener l) {
        synchronized (mBmtModelListeners) {
            if (null == l || !mBmtModelListeners.contains(l)) {
                return;
            }
            mBmtModelListeners.remove(l);
            DDLog.d(TAG, "removeBmtModelListener: mBmtModelListeners size: " + mBmtModelListeners.size());
        }
    }

    public void addBmtTodayUsageModelListener(OnBmtTodayUsageModelListener listener) {
        synchronized (mBmtTodayUsageModelListeners) {
            if (null == listener || mBmtTodayUsageModelListeners.contains(listener)) {
                return;
            }
            mBmtTodayUsageModelListeners.add(listener);
            DDLog.d(TAG, "addBmtTodayUsageModelListener: mBmtTodayUsageModelListeners size: " + mBmtTodayUsageModelListeners.size());
        }
    }

    public void removeBmtTodayModelListener(OnBmtTodayUsageModelListener l) {
        synchronized (mBmtTodayUsageModelListeners) {
            if (null == l || !mBmtTodayUsageModelListeners.contains(l)) {
                return;
            }
            mBmtTodayUsageModelListeners.remove(l);
            DDLog.d(TAG, "removeBmtTodayModelListener: mBmtTodayUsageModelListeners size: " + mBmtTodayUsageModelListeners.size());
        }
    }

    public void addBmtEmergencyModelListener(OnBmtEmergencyModelListener listener) {
        synchronized (mBmtEmergencyModelListeners) {
            if (null == listener || mBmtEmergencyModelListeners.contains(listener)) {
                return;
            }
            mBmtEmergencyModelListeners.add(listener);
            DDLog.d(TAG, "addBmtEmergencyModelListener: mBmtEmergencyModelListener size: " + mBmtEmergencyModelListeners.size());
        }
    }

    public void removeBmtEmergencyListener(OnBmtEmergencyModelListener l) {
        synchronized (mBmtEmergencyModelListeners) {
            if (null == l || !mBmtEmergencyModelListeners.contains(l)) {
                return;
            }
            mBmtEmergencyModelListeners.remove(l);
            DDLog.d(TAG, "removeBmtEmergencyListener: mBmtEmergencyModelListener size: " + mBmtEmergencyModelListeners.size());
        }
    }

    public void refreshBmtWidgetDeletedState(String pluginId, String subcategory, boolean flagDeleted) {
        synchronized (mBmtModelListeners) {
            if (mBmtModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtModelListeners) {
                    l.onViewDeleted(pluginId, subcategory, flagDeleted);
                }
            }
        }

        synchronized (mBmtTodayUsageModelListeners) {
            if (mBmtTodayUsageModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtTodayUsageModelListeners) {
                    l.onViewDeleted(pluginId, subcategory, flagDeleted);
                }
            }
        }

        synchronized (mBmtEmergencyModelListeners) {
            if (mBmtEmergencyModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtEmergencyModelListeners) {
                    l.onViewDeleted(pluginId, subcategory, flagDeleted);
                }
            }
        }
    }

    public void updateBmtStatus(BmtDeviceStatusChange event) {
        synchronized (mBmtModelListeners) {
            if (mBmtModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtModelListeners) {
                    l.updateBmtStatus(event);
                }
            }
        }

        synchronized (mBmtTodayUsageModelListeners) {
            if (mBmtTodayUsageModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtTodayUsageModelListeners) {
                    l.updateBmtStatus(event);
                }
            }
        }

        synchronized (mBmtEmergencyModelListeners) {
            if (mBmtEmergencyModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtEmergencyModelListeners) {
                    l.updateBmtStatus(event);
                }
            }
        }
    }

    public void updateBmtBatteryStatus(BatteryStatusEvent event) {
        synchronized (mBmtModelListeners) {
            if (mBmtModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtModelListeners) {
                    l.updateBmtBatteryStatus(event);
                }
            }
        }
    }

    public void updateChargeMode(ChargeModeEvent event) {
        synchronized (mBmtModelListeners) {
            if (mBmtModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtModelListeners) {
                    l.updateChargeMode(event);
                }
            }
        }
    }

    public void updateBmtCurrent(BmtGraphicUpdateEvent event) {
        synchronized (mBmtModelListeners) {
            if (mBmtModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtModelListeners) {
                    l.updateBmtCurrent(event);
                }
            }
        }

        synchronized (mBmtEmergencyModelListeners) {
            if (mBmtEmergencyModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtEmergencyModelListeners) {
                    l.updateBmtCurrent(event);
                }
            }
        }
    }


    public void updateBmtLoadedStatus(String deviceId, String subcategory) {
        synchronized (mBmtModelListeners) {
            if (mBmtModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtModelListeners) {
                    l.updateBmtLoadState(deviceId, subcategory);
                }
            }
        }

        synchronized (mBmtTodayUsageModelListeners) {
            if (mBmtTodayUsageModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtTodayUsageModelListeners) {
                    l.updateBmtLoadState(deviceId, subcategory);
                }
            }
        }

        synchronized (mBmtEmergencyModelListeners) {
            if (mBmtEmergencyModelListeners.size() > 0) {
                for (OnBmtModelListener l : mBmtEmergencyModelListeners) {
                    l.updateBmtLoadState(deviceId, subcategory);
                }
            }
        }
    }

    public void updateBmtTodayUsage(BmtChartDataEvent event) {
        synchronized (mBmtTodayUsageModelListeners) {
            if (mBmtTodayUsageModelListeners.size() > 0) {
                for (OnBmtTodayUsageModelListener l : mBmtTodayUsageModelListeners) {
                    l.updateBmtTodayUsage(event);
                }
            }
        }
    }

    public void getFeature(BmtGetFeatureEvent event) {
        synchronized (mBmtEmergencyModelListeners) {
            if (mBmtEmergencyModelListeners.size() > 0) {
                for (OnBmtEmergencyModelListener l : mBmtEmergencyModelListeners) {
                    l.getFeature(event);
                }
            }
        }
    }

    // ===================================      BMT end  =================================//


    // ===================================     ipc部分 start     =================================//
    public boolean addOnIpcModelListener(final OnIpcModelListener l) {
        synchronized (mIpcModelListeners) {
            if (null == l || mIpcModelListeners.contains(l)) {
                return false;
            }
            mIpcModelListeners.add(l);
            DDLog.d(TAG, "addOnIpcModelListener: mIpcModelListeners size: " + mIpcModelListeners.size());
            return true;
        }
    }

    public void removeOnIpcModelListener(final OnIpcModelListener l) {
        synchronized (mIpcModelListeners) {
            if (null == l || !mIpcModelListeners.contains(l)) {
                return;
            }
            mIpcModelListeners.remove(l);
            DDLog.d(TAG, "removeOnIpcModelListener: mIpcModelListeners size: " + mIpcModelListeners.size());
        }
    }

    public void refreshIpcWidgetDeletedState(String pluginId, boolean flagDeleted) {
        synchronized (mIpcModelListeners) {
            if (mIpcModelListeners.size() > 0) {
                for (OnIpcModelListener l : mIpcModelListeners) {
                    l.onViewDeleted(pluginId, flagDeleted);
                }
            }
        }
    }

    public void refreshCamConnectStatus(String pluginId, Device device) {
        synchronized (mIpcModelListeners) {
            if (mIpcModelListeners.size() > 0) {
                for (OnIpcModelListener l : mIpcModelListeners) {
                    l.onCamConnectStatus(pluginId, device);
                }
            }
        }
    }

    public void refreshMultiScreen(@Nullable final String multiScreenId) {
        synchronized (mIpcModelListeners) {
            if (mIpcModelListeners.size() > 0) {
                for (OnIpcModelListener l : mIpcModelListeners) {
                    l.onMultiPlayDsCamIdsChange(multiScreenId);
                }
            }
        }
    }
    // ===================================     ipc部分 end       =================================//


    // ===================================     banner部分 start =================================//
    public void addOnBannerModelListener(final OnBannerModelListener l) {
        this.mBannerModelListener = l;
    }

    public void setBannerData(List<ListBigBannerResponse.BigBannerBean> data) {
        DDLog.d(TAG, "setBannerData.");
        if (null != mBannerModelListener) {
            mBannerModelListener.setData(data);
        }
    }

    // ===================================     banner部分 end   =================================//


    // ===================================     shortcut部分 start =================================//
    public void addOnShortcutModelListener(final OnShortcutModelListener l) {
        synchronized (mShortcutModelListeners) {
            if (null == l || mShortcutModelListeners.contains(l)) {
                return;
            }
            mShortcutModelListeners.add(l);
            DDLog.d(TAG, "addOnShortcutModelListener: mShortcutModelListeners size: " + mShortcutModelListeners.size());
        }
    }

    public void removeOnShortcutModelListener(final OnShortcutModelListener l) {
        synchronized (mShortcutModelListeners) {
            if (null == l || !mShortcutModelListeners.contains(l)) {
                return;
            }
            mShortcutModelListeners.remove(l);
            DDLog.d(TAG, "removeOnShortcutModelListener: mShortcutModelListeners size: " + mShortcutModelListeners.size());
        }
    }

    public boolean refreshShortcutDeletedState(String pluginId, boolean flagDeleted) {
        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "refreshShortcutDeletedState");
                for (OnShortcutModelListener l : mShortcutModelListeners) {
                    l.onViewDeleted(pluginId, flagDeleted);
                }
                return true;
            }
            return false;
        }
    }

    public void updateShortcutName(String pluginId, String name) {
        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "updateShortcutName");
                for (OnShortcutModelListener listener : mShortcutModelListeners) {
                    listener.updateShortcutName(pluginId, name);
                }
            }
        }
    }

    public void onShortcutLoadedUpdate(String pluginId, final boolean loaded) {
        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "onShortcutLoadedUpdate");
                for (OnShortcutModelListener listener : mShortcutModelListeners) {
                    listener.onLoadedUpdate(pluginId, loaded);
                }
            }
        }
    }

    public void updateOnlineStateShortCut(String sendId, boolean online, boolean isPanelDeviceOffline) {
        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "updateOnlineStateShortCut");
                for (OnShortcutModelListener listener : mShortcutModelListeners) {
                    listener.updateOnlineStateShortCut(sendId, online, isPanelDeviceOffline);
                }
            }
        }
    }

    public void updateTuyaColorLightStatus(Device device, boolean isPanelDeviceOffline) {
        if (null == device) {
            return;
        }

        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "updataTuyaColorLightStatus");
                for (OnShortcutModelListener listener : mShortcutModelListeners) {
                    listener.updateTuyaColorLightStatus(device, isPanelDeviceOffline);
                }
            }
        }
    }

    public void updateTuyaSmartPluginStatus(Device device, boolean isPanelDeviceOffline) {
        if (null == device) {
            return;
        }

        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "updataTuyaSmartPluginStatus");
                for (OnShortcutModelListener listener : mShortcutModelListeners) {
                    listener.updateTuyaSmartPluginStatus(device, isPanelDeviceOffline);
                }
            }
        }
    }

    public void changeOldSmartPlugState(JSONObject jsonObject, boolean isPanelDeviceOffline) {
        if (null == jsonObject) {
            return;
        }

        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "changeOldSmartPlugState");
                try {

                    String shortcutId = DDJSONUtil.getString(jsonObject, "pluginid");
                    boolean enable = LocalKey.KNOCK_TO_SOS_SUCCESS.equals(DDJSONUtil.getString(jsonObject
                            , "plugin_item_smart_plug_enable"));

                    for (OnShortcutModelListener listener : mShortcutModelListeners) {
                        listener.changeOldSmartPlugState(shortcutId, enable, isPanelDeviceOffline);
                    }
                    DDLog.i(TAG, "更新旧插座开关状态");
                } catch (Exception e) {
                    DDLog.e(TAG, "changeOldSmartPlugState-ERROR!!!!!!!!!!!!!1");
                    e.printStackTrace();
                }

            }
        }
    }

    public void changeAskSmartPlugState(JSONObject jsonObject, boolean isPanelDeviceOffline) {
        if (null == jsonObject) {
            return;
        }

        try {
            String shortcutId = DDJSONUtil.getString(jsonObject, "pluginid");
            boolean enable = LocalKey.STATUS_OPENED_ASK_PLUG == DDJSONUtil.getInt(jsonObject
                    , "plugin_item_smart_plug_enable");
            changeAskSmartPlugState(shortcutId, enable, isPanelDeviceOffline);
        } catch (Exception e) {
            DDLog.e(TAG, "changeAskSmartPlugState-ERROR!!!!!!!!!!!!!");
            e.printStackTrace();
        }
    }

    public void changeAskSmartPlugState(final String pluginId, final boolean enable, boolean isPanelDeviceOffline) {
        if (TextUtils.isEmpty(pluginId)) {
            return;
        }

        for (OnShortcutModelListener listener : mShortcutModelListeners) {
            listener.changeAskSmartPlugState(pluginId, enable, isPanelDeviceOffline);
        }
        DDLog.i(TAG, "更新ASK插座开关状态");
    }

    public void changeOfficialSmartPlugStateByPanelDevice(boolean panelDeviceOffline) {
        synchronized (mShortcutModelListeners) {
            if (mShortcutModelListeners.size() > 0) {
                DDLog.d(TAG, "changeOfficialSmartPlugStateByPanelDevice");
                for (OnShortcutModelListener listener : mShortcutModelListeners) {
                    listener.changeOfficialSmartPlugStateByPanelDevice(panelDeviceOffline);
                }
            }
        }
    }

    // ===================================     shortcut部分 end   =================================//


    // ===================================      plugin部分 start  =================================//
    public void addOnPluginModelListener(final OnPluginModelListener l) {
        synchronized (mPluginModelListeners) {
            if (null == l || mPluginModelListeners.contains(l)) {
                return;
            }
            mPluginModelListeners.add(l);
        }
    }

    public void removeOnPluginModelListener(final OnPluginModelListener l) {
        synchronized (mPluginModelListeners) {
            if (null == l || !mPluginModelListeners.contains(l)) {
                return;
            }
            mPluginModelListeners.remove(l);
            DDLog.d(TAG, "removeOnPluginModelListener: mPluginModelListeners size: " + mPluginModelListeners.size());
        }
    }

    public boolean refreshPluginDeletedState(String pluginId, boolean flagDeleted) {
        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                DDLog.d(TAG, "refreshPluginDeletedState");
                for (OnPluginModelListener l : mPluginModelListeners) {
                    l.onViewDeleted(pluginId, flagDeleted);
                }
                return true;
            }
            return false;
        }
    }

    public void updatePluginName(String pluginId, String name) {
        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                DDLog.d(TAG, "updatePluginName");
                for (OnPluginModelListener listener : mPluginModelListeners) {
                    listener.updatePluginName(pluginId, name);
                }
            }
        }
    }

    public void updateOnlineStateDoorSensor(String sendID, boolean online, boolean isPanelDeviceOffline) {
        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                DDLog.d(TAG, "updateOnlineStateDoorSensor");
                for (OnPluginModelListener listener : mPluginModelListeners) {
                    listener.updatePluginLivingState(sendID, online, isPanelDeviceOffline);
                }
            }
        }
    }

    public void updatePluginStateBlock(String pluginId) {
        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                DDLog.d(TAG, "updatePluginStateBlock");
                for (OnPluginModelListener listener : mPluginModelListeners) {
                    listener.updatePluginStateBlock(pluginId);
                }
            }
        }
    }

    public void updatePluginStateDoorSensor(String pluginId, boolean isApart) {
        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                DDLog.d(TAG, "updatePluginStateDoorSensor");
                for (OnPluginModelListener listener : mPluginModelListeners) {
                    listener.updatePluginStateDoorSensor(pluginId, isApart);
                }
            }
        }
    }

    public void updateOnlineStateDoorSensorByPanelDevice(boolean panelDeviceOffline) {
        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                DDLog.d(TAG, "updateOnlineStateDoorSensorByPanelDevice");
                for (OnPluginModelListener listener : mPluginModelListeners) {
                    listener.updatePluginLivingStateByPanelDevice(panelDeviceOffline);
                }
            }
        }
    }

    public void onPluginLoadedUpdate(final String deviceId, final boolean loaded) {
        synchronized (mPluginModelListeners) {
            if (mPluginModelListeners.size() > 0) {
                DDLog.d(TAG, "onPluginLoadedUpdate");
                for (OnPluginModelListener listener : mPluginModelListeners) {
                    listener.onLoadedInfoUpdate(deviceId, loaded);
                }
            }
        }
    }
    // ===================================      plugin部分 end    =================================//


    // ===================================      主机部分 start    =================================//
    public void addOnHostModelListener(final OnHostModelListener l) {
        synchronized (mHostModelListeners) {
            if (null == l || mHostModelListeners.contains(l)) {
                return;
            }
            mHostModelListeners.add(l);
            DDLog.d(TAG, "addOnHostModelListener: mHostModelListeners size: " + mHostModelListeners.size());
        }
    }

    public void removeOnHostModelListener(final OnHostModelListener l) {
        synchronized (mHostModelListeners) {
            if (null == l || !mHostModelListeners.contains(l)) {
                return;
            }
            mHostModelListeners.remove(l);
            DDLog.d(TAG, "removeOnHostModelListener: mHostModelListeners size: " + mHostModelListeners.size());
        }
    }

    public void refreshHostDeletedState(String pluginId, boolean flagDeleted) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "refreshHostDeletedState");
                for (OnHostModelListener l : mHostModelListeners) {
                    l.onViewDeleted(pluginId, flagDeleted);
                }
            }
        }
    }

    public void updatePing(String result, PanelDevice mPanelDevice) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                {
                    DDLog.d(TAG, "updatePing");
                    if (TextUtils.isEmpty(result)
                            || !CommonDataUtil.getInstance().isPanelOnline()
                            || DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.UPGRADING, false)) {
                        DDLog.e(TAG, "主机离线或升级中，不能刷新Ping值");
                        return;
                    }
                    try {
                        JSONObject jsonObject1 = new JSONObject(result);
                        boolean isCharge = jsonObject1.getBoolean("ischarge");
                        int batteryLevel = jsonObject1.getInt("batterylevel");
                        int netType = jsonObject1.getInt("nettype");
                        String ipaddr = jsonObject1.getString("ipaddr");
                        int rssi = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.WIFI_RSSI, 3);
                        int sim = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.SIM_STATUS, 0);

                        for (OnHostModelListener listener : mHostModelListeners) {
                            listener.updatePing(netType, sim, rssi, isCharge, batteryLevel);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

            }
        }
    }

    public void updateSimStatus(DeviceSimStatueEvent ev) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "updateSimStatus");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.updateSimStatus(ev);
                }
            }
        }
    }

    public void updatePowerCallBackAction(BaseFragment baseFragment, String result) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "updateSimStatus");

                try {
                    JSONObject jsonObject = new JSONObject(result);
                    String hint = DDJSONUtil.getString(jsonObject, "message");
                    AlertDialog.createBuilder(baseFragment.getDelegateActivity())
                            .setAutoDissmiss(true)
                            .setCanCancel(false)
                            .setContentColor(baseFragment.getContext().getResources().getColor(R.color.common_dialog_content))
                            .setBackgroundTint(Color.WHITE)
                            .setType(AlertDialogManager.DialogType.PANEL_CHARGING_STATE_CHANGED)
                            .setOk(baseFragment.getContext().getResources().getString(R.string.send_email_confirm_text))
                            .setContent(hint)
                            .preBuilder()
                            .show();
                    // 更新电量
                    for (OnHostModelListener listener : mHostModelListeners) {
                        listener.updatePowerCallBackAction();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    public void initDeviceStatus(PanelDevice mPanelDevice) {
        MainWidgetListProvider.getInstance().cancelOperateArmDelay(CommonDataUtil.getInstance().getCurrentDeviceId());
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "initDeviceStatus");
                int status = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.ARM_STATUS, -1);
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.initDeviceStatus(status);
                }
            }
        }
    }

    public void toArm() {
        MainWidgetListProvider.getInstance().cancelOperateArmDelay(CommonDataUtil.getInstance().getCurrentDeviceId());
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "toArm");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.toArm();
                }
            }
        }
    }

    public void toDisArm() {
        MainWidgetListProvider.getInstance().cancelOperateArmDelay(CommonDataUtil.getInstance().getCurrentDeviceId());
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "toDisArm");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.toDisArm();
                }
            }
        }
    }

    public void toResetSos() {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "toResetSos");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.toResetSos();
                }
            }
        }
    }

    public void toInitArm(String result) {
        MainWidgetListProvider.getInstance().cancelOperateArmDelay(CommonDataUtil.getInstance().getCurrentDeviceId());
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "toInitArm");
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    boolean isLatestCmd = DDJSONUtil.getBoolean(jsonObject, PanelDataKey.Panel.IS_LATEST_CMD);
                    if (isLatestCmd) {
                        for (OnHostModelListener listener : mHostModelListeners) {
                            listener.toInitArm();
                        }
                        playSound(1, 1, 1, 0, 1, 1);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void toInitDisArm() {
        MainWidgetListProvider.getInstance().cancelOperateArmDelay(CommonDataUtil.getInstance().getCurrentDeviceId());
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "toInitDisArm");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.toInitDisArm();
                }
            }
        }
    }

    public void toInitHomeArm(String result, int size) {
        MainWidgetListProvider.getInstance().cancelOperateArmDelay(CommonDataUtil.getInstance().getCurrentDeviceId());
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "toInitDisArm");

                try {
                    JSONObject jsonObject = new JSONObject(result);
                    boolean isLatestCmd = DDJSONUtil.getBoolean(jsonObject, PanelDataKey.Panel.IS_LATEST_CMD);
                    if (isLatestCmd) {
                        if (size == 0) {
                            for (OnHostModelListener listener : mHostModelListeners) {
                                listener.toInitHomeArm();
                            }
                        }
                        playSound(1, 1, 1, 0, 0, 1);
                    } else {
                        DDLog.i(TAG, "ignore arm");
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    public void playSound(int soundID, int leftVolume, int rightVolume, int priority, int loop, int rate) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "playSound");
                mHostModelListeners.get(0).playSound(soundID, leftVolume, rightVolume, priority, loop, rate);
            }
        }
    }

    public void changeDeviceStateErrorHadSim() {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "changeDeviceStateErrorHadSim");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.changeDeviceStateErrorHadSim();
                }
            }
        }
    }

    public void changeDeviceStateNormal(PanelDevice mPanelDevice) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.i(TAG, "changeDeviceStateNormal.");
                int armStatus = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.ARM_STATUS, 0);
                int network = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.NET_TYPE, 0);
                int sim = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.SIM_STATUS, 0);
                int batteryLevel = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.BATTERY_LEVEL, 0);
                int rssi = DeviceHelper.getInt(mPanelDevice, PanelDataKey.Panel.WIFI_RSSI, 3);
                boolean isCharge = DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.IS_CHARGE, false);

                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.changeDeviceStateNormal(network, sim, rssi, isCharge, batteryLevel, armStatus);
                }
            }
        }
    }

    public void changeDeviceStateOffline() {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.i(TAG, "changeDeviceStateOffline.");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.changeDeviceStateOfflineMode();
                }
            }
        }
    }

    public void changeDeviceStatePanelUpgrading() {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.i(TAG, "changeDeviceStatePanelUpgrading.");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.changeDeviceStatePanelUpgrading();
                }
            }
        }
    }

    public void changeDeviceToolbarStateByUserPermission(boolean showSos) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.i(TAG, "changeDeviceToolbarStateByUserPermission.");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.changeDeviceToolbarStateByUserPermission(showSos);
                }
            }
        }
    }

    public void onOperationArmAck(int exitDelay) {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.i(TAG, "onOperationArmAck.");
                for (OnHostModelListener listener : mHostModelListeners) {
                    listener.onOperationArmAck(exitDelay);
                }
            }
        }
    }

    public void onLoadedInfoUpdate() {
        synchronized (mHostModelListeners) {
            if (mHostModelListeners.size() > 0) {
                DDLog.d(TAG, "onLoadedInfoUpdate");
                for (OnHostModelListener l : mHostModelListeners) {
                    l.onLoadedInfoUpdate();
                }
            }
        }
    }

    // ===================================      主机部分 end      =================================//


}
