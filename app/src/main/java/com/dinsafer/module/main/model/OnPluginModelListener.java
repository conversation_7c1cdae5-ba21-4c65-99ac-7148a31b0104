package com.dinsafer.module.main.model;

/**
 * @describe：
 * @date：2022/11/25
 * @author: create by <PERSON><PERSON><PERSON>
 */
public interface OnPluginModelListener extends BaseModelListener {

    void updatePluginName(String pluginId, String name);

    void updatePluginLivingState(String sendId, boolean online, boolean isPanelDeviceOffline);

    void updatePluginStateBlock(String pluginId);

    void updatePluginStateDoorSensor(String pluginId, boolean isApart);

    void updatePluginLivingStateByPanelDevice(boolean panelDeviceOffline);

    void onLoadedInfoUpdate(String pluginId, boolean loaded);
}
