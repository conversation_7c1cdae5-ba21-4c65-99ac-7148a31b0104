package com.dinsafer.module.main.view;

import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.airbnb.lottie.LottieAnimationView;
import com.dinsafer.common.BmtManager;
import com.dinsafer.common.Constants;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.common.PluginManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAddWidgetBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dinsdk.DinPluginHelper;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.TuyaItem;
import com.dinsafer.model.event.AddWidgetEvent;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.main.adapter.AddWidgetListAdapter;
import com.dinsafer.module.main.entry.AddWidgetItemBean;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.MainWidgetListProvider;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.electricity.chart.SectionLineChart;
import com.dinsafer.module.powerstation.electricity.manager.LineChartManager;
import com.dinsafer.module.powerstation.widget.BatteryChargeView;
import com.dinsafer.module.powerstation.widget.currentdiagramview.LoadSaturationView;
import com.dinsafer.module.powerstation.widget.currentdiagramview.PowerPulseLoadSaturationView;
import com.dinsafer.module.powerstation.widget.currentdiagramview.PowerStoreLoadSaturationView;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.module.powerstation.widget.segmentbar.SegmentSlideBar;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.plugin.widget.util.DensityUtil;
import com.dinsafer.ui.BmtDeviceTagView;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.device.DeviceStatusHelper;
import com.dinsafer.ui.device.MainDeviceStatusView;
import com.dinsafer.ui.device.ToolbarTabLayout;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.PluginWidgetStyleUtil;
import com.dinsafer.util.SettingInfoHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.annotations.NonNull;

/**
 * @describe：添加widget页面
 * @date：2022/10/28
 * @author: create by Sydnee
 */
public class AddWidgetFragment extends BaseFragment implements AddWidgetListAdapter.OnItemClickListener, ViewPager.OnPageChangeListener {

    private final static String TAG = AddWidgetFragment.class.getSimpleName();
    private final static String DEVICE_TYPE = "deviceType";
    private final static String TITLE_TEXT = "titleText";
    private final static int CONTENT_STATE_DEVICE_LIST = 1;
    private final static int CONTENT_STATE_WIDGET_LIST = 2;
    // 特殊类型：ipc
    public final static String DEVICE_TYPE_IPC = "DeviceTypeIpc";
    private FragmentAddWidgetBinding mBinding;
    private String titleText;
    private String deviceType;
    private int curContentState = CONTENT_STATE_DEVICE_LIST;
    private Device selectedDevice;
    private String selectedPanelDeviceId;
    private int selectedDevicePosition;
    private List<Device> pluginList;
    private List<Device> doorSensorList = new ArrayList<>();
    private List<Device> smartPluginList = new ArrayList<>();
    private List<Device> repeaterPlugList = new ArrayList<>();
    private List<Device> heartLaiIpcList = new ArrayList<>();
    private List<Device> dscamList = new ArrayList<>();
    private List<Device> videoDoorbellList = new ArrayList<>();
    private List<Device> bmtList = new ArrayList<>();
    private List<AddWidgetItemBean> data;
    private AddWidgetListAdapter adapter;

    private ArrayList<View> widgetViews = new ArrayList<View>(); // 将要显示的布局存放到list数组
    private ViewPagerAdapter mAdapter;
    private ImageView[] dotViews;
    private int curViewPagerSelectedIndex;
    private int[] widgetSample;
    private int[] widgetStyleArray;

    private ArrayList<MainWidgetBean> cacheLayoutList;
    private List<Boolean> mEnabledList;
    private boolean isGridToBattery;

    public static AddWidgetFragment newInstance(String deviceType, String titleText) {
        AddWidgetFragment addWidgetFragment = new AddWidgetFragment();
        Bundle bundle = new Bundle();
        bundle.putString(DEVICE_TYPE, deviceType);
        bundle.putString(TITLE_TEXT, titleText);
        addWidgetFragment.setArguments(bundle);
        return addWidgetFragment;
    }

    private IDeviceCallBack mDeviceCallBack = (deviceId, subCategory, cmd, map) -> {
        Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
        if (selectedDevice == null
                || !selectedDevice.getId().equals(deviceId)
                || !selectedDevice.getSubCategory().equals(subCategory)
                || result == null)
            return;
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                switch (cmd) {
                    case BmtCmd.GET_FEATURE:
                        closeLoadingFragment();
                        isGridToBattery = DeviceHelper.getBoolean(result, BmtDataKey.GRID_TO_BATTERY, false);
                        initWidgetViews();
                        initIndicator();
                        break;
                }
            }
        });
    };


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_add_widget, container, false);
        initData();
        initListener();
        mBinding.rvPlugin.setVisibility(View.VISIBLE);
        return mBinding.getRoot();
    }

    @Override
    public void initData() {
        super.initData();
        if (getArguments() != null) {
            titleText = getArguments().getString(TITLE_TEXT, "");
            deviceType = getArguments().getString(DEVICE_TYPE, "");
        }
        changeContentView(CONTENT_STATE_DEVICE_LIST);
        pluginList = new ArrayList<>();
        data = new ArrayList<>();
        mEnabledList = new ArrayList<>();
        adapter = new AddWidgetListAdapter();
        adapter.setOnItemClickListener(this);
        mBinding.rvPlugin.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvPlugin.setAdapter(adapter);

        if (TextUtils.isEmpty(deviceType)) {
            return;
        }

        switch (deviceType) {
            case PanelConstant.DeviceType.PANEL:
                getPanel();
                break;
            case DEVICE_TYPE_IPC:
                getIPC();
                break;
            case DinConst.TYPE_DSDOORBELL:
                getDoorbell();
                break;
            case PanelConstant.DeviceType.SMART_PLUG:
                getSmartPlugin();
                break;
            case PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG:
                getRepeaterPlug();
                break;
            case PanelConstant.DeviceType.DOOR_WINDOW_SENSOR:
                getDoorSensorPlugins();
                break;
            case DinConst.TYPE_BMT_HP5000:
            case DinConst.TYPE_BMT_POWERCORE20:
            case DinConst.TYPE_BMT_POWERCORE30:
            case DinConst.TYPE_BMT_POWERSTORE:
            case DinConst.TYPE_BMT_POWERPULSE:
                getBmt();
                break;
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonBarBack.setOnClickListener(v -> {
            if (curContentState == CONTENT_STATE_DEVICE_LIST || deviceType.equals(PanelConstant.DeviceType.PANEL)) {
                removeSelf();
            } else {
                changeContentView(CONTENT_STATE_DEVICE_LIST);
            }
        });

        mBinding.btnAdd.setOnClickListener(v -> {
            DDLog.d(TAG, "initListener:  deviceType: " + deviceType);
            MainWidgetBean mainWidgetBean = new MainWidgetBean();
            if (null != selectedDevice) {
                mainWidgetBean.setFatherId(selectedDevice.getFatherId());
            }
            String type = "";
            switch (deviceType) {
                case PanelConstant.DeviceType.PANEL:
                    mainWidgetBean.setBindModelType(MainPanelHelper.SECTION_TYPE_PANEL_DEVICE);
                    mainWidgetBean.setPluginId(selectedPanelDeviceId);
                    type = curViewPagerSelectedIndex == 0 ? PluginWidgetStyleUtil.PANEL : PluginWidgetStyleUtil.PANEL_SMALL;
                    break;
                case PanelConstant.DeviceType.DOOR_WINDOW_SENSOR:
                    mainWidgetBean.setBindModelType(MainPanelHelper.SECTION_TYPE_DOOR_SENSOR);
                    mainWidgetBean.setPluginId(selectedDevice.getId());
                    mainWidgetBean.setName(data.get(selectedDevicePosition).getName());
                    type = PluginWidgetStyleUtil.PLUGIN;
                    break;
                case DEVICE_TYPE_IPC:
                case DinConst.TYPE_DSDOORBELL:
                    mainWidgetBean.setPluginId(selectedDevice.getId());
                    if (curViewPagerSelectedIndex == 0) {
                        type = PluginWidgetStyleUtil.IPC;
                    } else if (curViewPagerSelectedIndex == 1
                            && widgetSample[curViewPagerSelectedIndex] == PluginWidgetStyleUtil.ipcSampleStyleArray[1]) {
                        type = PluginWidgetStyleUtil.IPC_MORE;
                        mainWidgetBean.setPluginId(MainWidgetListProvider.genDsCamMultiScreenId());
                    } else {
                        type = PluginWidgetStyleUtil.IPC_SMALL;
                    }
                    mainWidgetBean.setBindModelType(MainPanelHelper.SECTION_TYPE_IPC);
                    mainWidgetBean.setName(data.get(selectedDevicePosition).getName());
                    break;
                case PanelConstant.DeviceType.SMART_PLUG:
                case PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG:
                    mainWidgetBean.setPluginId(selectedDevice.getId());
                    mainWidgetBean.setBindModelType(MainPanelHelper.SECTION_TYPE_SHORTCUT);
                    mainWidgetBean.setName(data.get(selectedDevicePosition).getName());
                    type = PluginWidgetStyleUtil.SHORTCUT;
                    break;
                case DinConst.TYPE_BMT_HP5000:
                case DinConst.TYPE_BMT_POWERCORE20:
                case DinConst.TYPE_BMT_POWERCORE30:
                    mainWidgetBean.setPluginId(selectedDevice.getId() + MainWidgetListProvider.UNDERLINE + selectedDevice.getSubCategory());
                    mainWidgetBean.setSubCategory(selectedDevice.getSubCategory());
                    mainWidgetBean.setBindModelType(MainPanelHelper.SECTION_TYPE_BMT);
                    mainWidgetBean.setName(data.get(selectedDevicePosition).getName());
//                    type = curViewPagerSelectedIndex == 0 ? PluginWidgetStyleUtil.BMT : PluginWidgetStyleUtil.BATTERY;
                    if (curViewPagerSelectedIndex == 0) {
                        type = PluginWidgetStyleUtil.BMT;
                    } else if (curViewPagerSelectedIndex == 1) {
                        type = PluginWidgetStyleUtil.BATTERY;
                    } else if (curViewPagerSelectedIndex == 2) {
                        type = PluginWidgetStyleUtil.BMT_TODAY_USAGE;
                    } else if (curViewPagerSelectedIndex == 3) {
                        type = PluginWidgetStyleUtil.BMT_EV;
                    } else if (curViewPagerSelectedIndex == 4) {
                        type = PluginWidgetStyleUtil.BMT_EMERGENCY;
                    }
                    break;

                case DinConst.TYPE_BMT_POWERSTORE:
                    mainWidgetBean.setPluginId(selectedDevice.getId() + MainWidgetListProvider.UNDERLINE + selectedDevice.getSubCategory());
                    mainWidgetBean.setSubCategory(selectedDevice.getSubCategory());
                    mainWidgetBean.setBindModelType(MainPanelHelper.SECTION_TYPE_POWER_STORE);
                    mainWidgetBean.setName(data.get(selectedDevicePosition).getName());
                    if (curViewPagerSelectedIndex == 0) {
                        type = PluginWidgetStyleUtil.POWER_STORE_CURRENT;
                    } else if (curViewPagerSelectedIndex == 1) {
                        type = PluginWidgetStyleUtil.POWER_STORE_BATTERY;
                    } else if (curViewPagerSelectedIndex == 2) {
                        type = PluginWidgetStyleUtil.POWER_STORE_TODAY_USAGE;
                    } else if (curViewPagerSelectedIndex == 3) {
                        type = PluginWidgetStyleUtil.POWER_STORE_EMERGENCY;
                    }
                    break;

                case DinConst.TYPE_BMT_POWERPULSE:
                    mainWidgetBean.setPluginId(selectedDevice.getId() + MainWidgetListProvider.UNDERLINE + selectedDevice.getSubCategory());
                    mainWidgetBean.setSubCategory(selectedDevice.getSubCategory());
                    mainWidgetBean.setBindModelType(MainPanelHelper.SECTION_TYPE_POWER_PULSE);
                    mainWidgetBean.setName(data.get(selectedDevicePosition).getName());
                    if (curViewPagerSelectedIndex == 0) {
                        type = PluginWidgetStyleUtil.POWER_PULSE_CURRENT;
                    }
                    break;
            }

            mainWidgetBean.setLayoutType(type);
            AddWidgetEvent addMainWidgetEvent = new AddWidgetEvent(mainWidgetBean, true);
            EventBus.getDefault().post(addMainWidgetEvent);
            removeSelf();
        });
    }

    /**
     * 包括自研IPC和心赖IPC
     */
    private void getIPC() {
        data.clear();
        dscamList.clear();
        heartLaiIpcList.clear();

        dscamList = IPCManager.getInstance().getNotDeletedDsCamList();
        heartLaiIpcList = IPCManager.getInstance().getNotDeletedHearLaiList();
        for (Device device : dscamList) {
            String name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
            pluginList.add(device);
            data.add(new AddWidgetItemBean(name));
        }
        for (Device device : heartLaiIpcList) {
            String name = DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, "");
            pluginList.add(device);
            data.add(new AddWidgetItemBean(name));
        }
        adapter.setData(data);
        adapter.notifyDataSetChanged();
    }

    private void getDoorbell() {
        // doorbell
        data.clear();
        videoDoorbellList.clear();
        videoDoorbellList = IPCManager.getInstance().getNotDeletedDoorbellList();
        for (Device device : videoDoorbellList) {
            String name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
            pluginList.add(device);
            data.add(new AddWidgetItemBean(name));
        }
        adapter.setData(data);
        adapter.notifyDataSetChanged();
    }

    private void getDoorSensorPlugins() {
        data.clear();
        doorSensorList.clear();
        doorSensorList.addAll(PluginManager.getInstance().getNotDeletedDoorSensorList());
        for (Device device : doorSensorList) {
            final String name = DinPluginHelper.getPluginNameOrGenDefaultName(device);
            Log.d(TAG, "getDoorSensorPlugins. name: " + name);
            pluginList.add(device);
            data.add(new AddWidgetItemBean(name));
        }
        adapter.setData(data);
        adapter.notifyDataSetChanged();
    }

    private void getSmartPlugin() {
        data.clear();
        smartPluginList.clear();
        smartPluginList.addAll(PluginManager.getInstance().getNotDeletedSmartPlugListWithoutRepeater());
        for (Device device : smartPluginList) {
            final String name = DinPluginHelper.getPluginNameOrGenDefaultName(device);
            pluginList.add(device);
            data.add(new AddWidgetItemBean(name));
        }
        adapter.setData(data);
        adapter.notifyDataSetChanged();
    }

    private void getRepeaterPlug() {
        data.clear();
        repeaterPlugList.clear();
        repeaterPlugList.addAll(PluginManager.getInstance().getNotDeletedRepeaterPlugList());
        for (Device device : repeaterPlugList) {
            final String name = DinPluginHelper.getPluginNameOrGenDefaultName(device);
            pluginList.add(device);
            data.add(new AddWidgetItemBean(name));
        }
        adapter.setData(data);
        adapter.notifyDataSetChanged();
    }

    private void getBmt() {
        data.clear();
        bmtList.clear();
        bmtList.addAll(BmtManager.getInstance().getNotDeletedBmtDeviceListBySubCategory(deviceType));
        for (Device device : bmtList) {
            String name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
            pluginList.add(device);
            data.add(new AddWidgetItemBean(name));
        }
        adapter.setData(data);
        adapter.notifyDataSetChanged();
    }

    private void getPanel() {
        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                return null;
            }
        }).thenUI(o -> {
            selectedDevicePosition = 0;
            selectedDevice = null;
            selectedPanelDeviceId = CommonDataUtil.getInstance().getCurrentPanelID();
            changeContentView(CONTENT_STATE_WIDGET_LIST);
        });

    }

    @Override
    public void onItemClickListener(int position) {
        DDLog.d(TAG, "onItemClickListener. position: " + position);
        if (selectedDevice != null) {
            selectedDevice.unregisterDeviceCallBack(mDeviceCallBack);
        }
        for (AddWidgetItemBean bean : data) {
            bean.setSelect(false);
        }
        data.get(position).setSelect(!data.get(position).isSelect());
        selectedDevicePosition = position;
        selectedDevice = pluginList.get(position);
        if (selectedDevice != null) {
            selectedDevice.registerDeviceCallBack(mDeviceCallBack);
        }
        adapter.notifyDataSetChanged();
        changeContentView(CONTENT_STATE_WIDGET_LIST);
    }

    private String getPluginQuantityName(Device device) {
        return DeviceHelper.getString(device, PanelDataKey.NAME, "");
    }

    private void changeContentView(int contentState) {
        curContentState = contentState;
        mBinding.rvPlugin.setVisibility(CONTENT_STATE_DEVICE_LIST == contentState ? View.VISIBLE : View.GONE);
        mBinding.rlContent.setVisibility(CONTENT_STATE_DEVICE_LIST == contentState ? View.GONE : View.VISIBLE);
        if (CONTENT_STATE_DEVICE_LIST == contentState) {
            if (selectedDevice != null) {
                selectedDevice.registerDeviceCallBack(mDeviceCallBack);
            }
            mBinding.commonBarTitle.setLocalText(titleText);
            mBinding.llIndicator.removeAllViews();
            widgetViews.clear();
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
            widgetSample = null;
            widgetStyleArray = null;
            dotViews = null;
        } else {
            mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.add_widget_fragment_bar_title));
            if (!TextUtils.isEmpty(deviceType) && (deviceType.equals(DinConst.TYPE_BMT_HP5000)
                    || deviceType.equals(DinConst.TYPE_BMT_POWERCORE20)
                    || deviceType.equals(DinConst.TYPE_BMT_POWERCORE30)
                    || deviceType.equals(DinConst.TYPE_BMT_POWERSTORE)
                    || deviceType.equals(DinConst.TYPE_BMT_POWERPULSE))) {
                showTimeOutLoadinFramgment();
                Map<String, Object> map = new HashMap<>();
                map.put(BmtDataKey.CMD, BmtCmd.GET_FEATURE);
                selectedDevice.submit(map);
            } else {
                initWidgetViews();
                initIndicator();
            }
        }
    }

    /**
     * 初始化viewpager
     */
    private void initWidgetViews() {
        LayoutInflater inflater = getLayoutInflater();

        initWidgetData();

        if (null == widgetSample) {
            DDLog.e(TAG, "error!!!!!!");
            return;
        }

        if (deviceType.equals(DEVICE_TYPE_IPC)) {
            if (!DsCamUtils.supportStaticMultiPlay() || HeartLaiUtils.isHeartLaiDevice(selectedDevice)) {
                widgetSample = removeArrayIndex(1, widgetSample);
                widgetStyleArray = removeArrayIndex(1, widgetStyleArray);
            }
        }
        DDLog.i(TAG, "initWidgetViews. cardStyle length: " + widgetSample.length);
        mEnabledList.clear();
        widgetViews.clear();
        checkCacheLayoutList();
        for (int j : widgetSample) {
            View container = inflater.inflate(j, null);
            if (deviceType.equals(DEVICE_TYPE_IPC)) {
                dealIpcPreview(container, j);
            } else if (deviceType.equals(PanelConstant.DeviceType.PANEL)) {
                dealHostPreview(container, j);
            } else if (deviceType.equals(DinConst.TYPE_BMT_HP5000)
                    || deviceType.equals(DinConst.TYPE_BMT_POWERCORE30)
                    || deviceType.equals(DinConst.TYPE_BMT_POWERCORE20)) {
                dealBmtPreview(container, j);
            } else if (deviceType.equals(DinConst.TYPE_BMT_POWERSTORE)) {
                dealPowerStorePreview(container, j);
            } else if (deviceType.equals(DinConst.TYPE_BMT_POWERPULSE)) {
                dealPowerPulsePreview(container, j);
            } else {
                dealPreview(container);
            }

            widgetViews.add(container);
        }
        mAdapter = new ViewPagerAdapter(widgetViews);
        mBinding.viewpager.setAdapter(mAdapter);
        mBinding.viewpager.addOnPageChangeListener(this);
        if (CollectionUtil.isListNotEmpty(mEnabledList)) {
            setAddEnabled(mEnabledList.get(0));
        }
    }

    private void initWidgetData() {
        switch (deviceType) {
            case PanelConstant.DeviceType.PANEL:
                widgetSample = PluginWidgetStyleUtil.getInstance().getWidgetSampleArray(MainPanelHelper.SECTION_TYPE_PANEL_DEVICE);
                widgetStyleArray = PluginWidgetStyleUtil.getInstance().getWidgetStyleArray(MainPanelHelper.SECTION_TYPE_PANEL_DEVICE);
                break;
            case PanelConstant.DeviceType.DOOR_WINDOW_SENSOR:
                widgetSample = PluginWidgetStyleUtil.getInstance().getWidgetSampleArray(MainPanelHelper.SECTION_TYPE_DOOR_SENSOR);
                widgetStyleArray = PluginWidgetStyleUtil.getInstance().getWidgetStyleArray(MainPanelHelper.SECTION_TYPE_DOOR_SENSOR);
                break;
            case DEVICE_TYPE_IPC:
            case DinConst.TYPE_DSDOORBELL:
                widgetSample = PluginWidgetStyleUtil.getInstance().getWidgetSampleArray(MainPanelHelper.SECTION_TYPE_IPC);
                widgetStyleArray = PluginWidgetStyleUtil.getInstance().getWidgetStyleArray(MainPanelHelper.SECTION_TYPE_IPC);
                break;
            case PanelConstant.DeviceType.SMART_PLUG:
            case PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG:
                widgetSample = PluginWidgetStyleUtil.getInstance().getWidgetSampleArray(MainPanelHelper.SECTION_TYPE_SHORTCUT);
                widgetStyleArray = PluginWidgetStyleUtil.getInstance().getWidgetStyleArray(MainPanelHelper.SECTION_TYPE_SHORTCUT);
                break;
            case DinConst.TYPE_BMT_HP5000:
            case DinConst.TYPE_BMT_POWERCORE20:
            case DinConst.TYPE_BMT_POWERCORE30:
                widgetSample = PluginWidgetStyleUtil.getInstance().getWidgetSampleArray(MainPanelHelper.SECTION_TYPE_BMT);
                widgetStyleArray = PluginWidgetStyleUtil.getInstance().getWidgetStyleArray(MainPanelHelper.SECTION_TYPE_BMT);
                if (!SettingInfoHelper.getInstance().isAdmin() || !isGridToBattery) {
                    widgetSample = removeArrayIndex(4, widgetSample);
                    widgetStyleArray = removeArrayIndex(4, widgetStyleArray);
                }
                break;

            case DinConst.TYPE_BMT_POWERSTORE:
                widgetSample = PluginWidgetStyleUtil.getInstance().getWidgetSampleArray(MainPanelHelper.SECTION_TYPE_POWER_STORE);
                widgetStyleArray = PluginWidgetStyleUtil.getInstance().getWidgetStyleArray(MainPanelHelper.SECTION_TYPE_POWER_STORE);
                if (!SettingInfoHelper.getInstance().isAdmin() || !isGridToBattery) {
                    widgetSample = removeArrayIndex(3, widgetSample);
                    widgetStyleArray = removeArrayIndex(3, widgetStyleArray);
                }
                break;

            case DinConst.TYPE_BMT_POWERPULSE:
                widgetSample = PluginWidgetStyleUtil.getInstance().getWidgetSampleArray(MainPanelHelper.SECTION_TYPE_POWER_PULSE);
                widgetStyleArray = PluginWidgetStyleUtil.getInstance().getWidgetStyleArray(MainPanelHelper.SECTION_TYPE_POWER_PULSE);
                break;
        }

    }

    /**
     * 初始化指示器
     */
    private void initIndicator() {
        // 有分页才显示指示器
        if (2 > widgetViews.size()) {
            return;
        }
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        //设置小圆点左右之间的间隔
        params.setMargins(8, 0, 8, 0);
        dotViews = new ImageView[widgetViews.size()];
        curViewPagerSelectedIndex = 0;
        for (int i = 0; i < widgetViews.size(); i++) {
            ImageView imageView = new ImageView(getContext());
            imageView.setLayoutParams(params);
            imageView.setImageResource(R.drawable.circle_white04_shape);
            //默认启动时，选中第一个小圆点
            imageView.setSelected(i == curViewPagerSelectedIndex);
            dotViews[i] = imageView;
            dotViews[curViewPagerSelectedIndex].setImageResource(R.drawable.circle_white01_shape); // 设置第一个页面已被选择
            mBinding.llIndicator.addView(imageView);
        }
    }

    private void dealHostPreview(View container, int layoutId) {
        DDLog.d(TAG, "dealHostPreview.");
        ToolbarTabLayout stlActionTypeTab = container.findViewById(R.id.stl_action_type_tab);
        stlActionTypeTab.updateUi();
        int permission = HomeManager.getInstance().getCurrentHome().getLevel();
        stlActionTypeTab.setSosVisible(LocalKey.GUEST != permission);

        MainDeviceStatusView mainDeviceStatusView = container.findViewById(R.id.mdsv_device_status);
        if (layoutId == PluginWidgetStyleUtil.hostSampleStyleArray[0]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.PANEL);
        } else if (layoutId == PluginWidgetStyleUtil.hostSampleStyleArray[1]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.PANEL_SMALL);
        }
        if (mainDeviceStatusView != null) {
            mainDeviceStatusView.updaUI();
            mainDeviceStatusView.setDeviceStatusNormal(
                    DeviceStatusHelper.getInstance().getNetStatusByNetworkValue(1),
                    DeviceStatusHelper.getInstance().getSimStatusBySimValue(1), -39,
                    true, 100);
        }
    }

    private void dealPreview(View container) {
        DDLog.d(TAG, "dealPreview.");
        ImageView statusIcon = container.findViewById(R.id.iv_status);
        if (statusIcon != null) {
            switch (deviceType) {
                case PanelConstant.DeviceType.DOOR_WINDOW_SENSOR:
                    setViewAddStatue(container, PluginWidgetStyleUtil.PLUGIN);
                    statusIcon.setImageResource(R.drawable.icon_cell_door_sensor_apart);
                    break;
                case PanelConstant.DeviceType.SMART_PLUG:
                case PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG:
                    setViewAddStatue(container, PluginWidgetStyleUtil.SHORTCUT);
                    statusIcon.setImageResource(CommonDataUtil.getInstance().getMainIconByType(TuyaItem.SMARTPLUGIN_ON));
                    break;
            }
        }

        LocalTextView tvName = container.findViewById(R.id.tv_name);
        if (null != tvName) {
            tvName.setText(data.get(selectedDevicePosition).getName());
        }
    }

    private void checkCacheLayoutList() {
        if (CollectionUtil.isListEmpty(cacheLayoutList)) {
            String mUserId = DinSDK.getUserInstance().getUser().getUid();
            String mHomeId = HomeManager.getInstance().getCurrentHome().getHomeID();
            if (DBUtil.Exists(DBKey.KEY_MAIN_PANEL_CACHE_LAYOUT + MainWidgetListProvider.UNDERLINE + mUserId + MainWidgetListProvider.UNDERLINE + mHomeId)) {
                String cacheStr = DBUtil.Str(DBKey.KEY_MAIN_PANEL_CACHE_LAYOUT + MainWidgetListProvider.UNDERLINE + mUserId + MainWidgetListProvider.UNDERLINE + mHomeId);
                if (!TextUtils.isEmpty(cacheStr)) {
                    Gson gson = new Gson();
                    cacheLayoutList = gson.fromJson(cacheStr, new TypeToken<ArrayList<MainWidgetBean>>() {
                    }.getType());
                }
            }
        }
    }

    private void dealBmtPreview(View container, int layoutId) {
        String name = data.get(selectedDevicePosition).getName();
        TextView tvName = null;
        ConstraintLayout clParent = container.findViewById(R.id.cl_parent);
        if (layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[0] ||
                layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[1] ||
                layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[2]) {
            tvName = container.findViewById(R.id.tv_plugin_name);
            tvName.setVisibility(BmtUtil.isOverOne() ? View.VISIBLE : View.GONE);
            if (TextUtils.isEmpty(name)) {
                tvName.setText("");
            } else {
                tvName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
            }
        }
        if (layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[0] ||
                layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[1]) {
            LinearLayout llSign = container.findViewById(R.id.ll_sign);
            LocalTextView tvType = container.findViewById(R.id.tv_type);
            if (llSign != null) {
                TextView finalTvName = tvName;
                llSign.post(() -> {
                    int containWidth = clParent.getWidth();
                    int llSignWidth = llSign.getWidth();
                    int maxWidth = containWidth - DensityUtil.dp2px(getContext(), 40)
                            - llSignWidth;
                    if (tvType != null) {
                        tvType.setMaxWidth(maxWidth);
                    }
                    if (finalTvName != null) {
                        finalTvName.setMaxWidth(maxWidth);
                    }
                });
            }
        }
        // bmt
        if (layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[0]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.BMT);
            LoadSaturationView loadSaturationView = container.findViewById(R.id.load_saturation_view);
            if (loadSaturationView != null) {
                loadSaturationView.setDefaultVal("0", "6", "0", "0",
                        "1", "0");
                loadSaturationView.setDefaultBattery(85, true,
                        BatteryChargeView.NORMAL, 0.8f);
                loadSaturationView.setMode(1);
                return;
            }
        } else if (layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[1]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.BATTERY);
            // battery
            SegmentSlideBar segmentSlideBar = container.findViewById(R.id.ssb);
            BatteryChargeView powerBatteryView = container.findViewById(R.id.power_battery_view);
            if (segmentSlideBar != null && powerBatteryView != null) {
                int percent = 15;
                List<Segment> segments = new ArrayList<>();
                segments.add(new Segment(0.9f, 1.0f, getString(R.string.power_battery_bar_status_text_1), getContext().getResources().getColor(R.color.power_station_battery_color_1)));
                segments.add(new Segment(0.6f, 0.9f, getString(R.string.power_battery_bar_status_text_2), getContext().getResources().getColor(R.color.power_station_battery_color_2)));
                segments.add(new Segment(0.12f, 0.6f, getString(R.string.power_battery_bar_status_text_3), getContext().getResources().getColor(R.color.power_station_battery_color_3)));
                segments.add(new Segment(0.02f, 0.12f, getString(R.string.power_battery_bar_status_text_4), getContext().getResources().getColor(R.color.power_station_battery_color_4)));
                segments.add(new Segment(0.0f, 0.02f, getString(R.string.power_battery_bar_status_text_5), getContext().getResources().getColor(R.color.power_station_battery_color_5)));
                segmentSlideBar.setSegments(segments, 4);
                powerBatteryView.setOnline(true);
                powerBatteryView.setSuccess(true);
                powerBatteryView.setProgress(percent / 100f, true);
                powerBatteryView.setChargeStatus(BatteryChargeView.NORMAL, true);

                segmentSlideBar.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        segmentSlideBar.setRemainSpace(clParent.getWidth() / 2f - powerBatteryView.getWidth() / 2f - DensityUtil.dp2px(getContext(), 9.5f));
                        segmentSlideBar.setProgress(percent / 100f, true);
                    }
                }, 200);
            }
        } else if (layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[2]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.BMT_TODAY_USAGE);
            SectionLineChart lcUsage = container.findViewById(R.id.lc_usage);
            LocalTextView tvUsage = container.findViewById(R.id.tv_usage);
            LocalTextView tvValue = container.findViewById(R.id.tv_value);
            LocalTextView tvPeak = container.findViewById(R.id.tv_peak);
            LocalTextView tvUnit = container.findViewById(R.id.tv_unit);
            LinearLayout llValue = container.findViewById(R.id.ll_value);
            LineChartManager lineChartManager = new LineChartManager(container.getContext(), lcUsage);
            int hourCount = 1440;
            int interval = 5;
            int size = hourCount / interval + 1;
            List<List<Float>> chartData = new ArrayList<>();
            for (int i = 0; i < hourCount / interval; i++) {
                List<Float> sonData = new ArrayList<>(4);
                sonData.add(i * 5.0f);
                sonData.add(ChartDataUtil.getRandom(3000, 0) * 1.0f);
                sonData.add(ChartDataUtil.getRandom(1000, 0) * 1.0f);
                sonData.add(ChartDataUtil.getRandom(1000, 0) * 1.0f);
                chartData.add(sonData);
            }

            float sumData = getSumVal(chartData, interval, true);
            String usage = ChartDataUtil.getPowerTransferVal(sumData, sumData, false);
            tvValue.setLocalText(usage);
            String unit = ChartDataUtil.getPowerUnit(sumData, true);
            tvUnit.setLocalText(unit);
            float peakVal = lineChartManager.getYMax(chartData, true);
            String peakStr = ChartDataUtil.getPowerTransferVal(peakVal, peakVal, true)
                    + ChartDataUtil.getPowerUnit(peakVal, false);
            tvPeak.setText(Local.s(getString(R.string.ps_peak)) + " " + peakStr);
            lineChartManager.initChart(interval, chartData, true, hourCount);
            lineChartManager.setData(chartData, false, true, hourCount, true);
            TextView finalTvName1 = tvName;
            llValue.post(() -> {
                int containWidth = clParent.getWidth();
                int llValueWidth = llValue.getWidth();
                int tvPeakWidth = tvPeak.getWidth();
                int maxWidth = containWidth - DensityUtil.dp2px(getContext(), 40)
                        - Math.max(llValueWidth, tvPeakWidth);
                if (tvUsage != null) {
                    tvUsage.setMaxWidth(maxWidth);
                }
                if (finalTvName1 != null) {
                    finalTvName1.setMaxWidth(maxWidth);
                }
            });
        } else if (layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[3]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.BMT_EV);
            LottieAnimationView lavVehicle = container.findViewById(R.id.lav_vehicle);
            BmtDeviceTagView tvPluginName = container.findViewById(R.id.tv_plugin_name);
            tvPluginName.post(new Runnable() {
                @Override
                public void run() {
                    tvPluginName.setText(Local.s(getString(R.string.instant_ev_charge)),
                            name.equals(Constants.POWER_STATION) ? Local.s(name) : name, BmtUtil.isOverOne());
                }
            });
            lavVehicle.setFrame(96);
        } else if (layoutId == PluginWidgetStyleUtil.bmtSampleStyleArray[4]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.BMT_EMERGENCY);
            BatteryChargeView batteryChargeView = container.findViewById(R.id.battery_charge_view);
            batteryChargeView.setOnline(true);
            batteryChargeView.setSuccess(true);
            batteryChargeView.setChargeStatus(BatteryChargeView.NORMAL, false);
            batteryChargeView.setProgress(1.0f, true);
            BmtDeviceTagView tvPluginName = container.findViewById(R.id.tv_plugin_name);
            tvPluginName.post(new Runnable() {
                @Override
                public void run() {
                    tvPluginName.setText(Local.s(getString(R.string.emergency_charge)),
                            name.equals(Constants.POWER_STATION) ? Local.s(name) : name, BmtUtil.isOverOne());
                }
            });
        }
    }

    private void dealPowerStorePreview(View container, int layoutId) {
        String name = data.get(selectedDevicePosition).getName();
        TextView tvName = null;
        ConstraintLayout clParent = container.findViewById(R.id.cl_parent);
        if (layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[0] ||
                layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[1] ||
                layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[2]) {
            tvName = container.findViewById(R.id.tv_plugin_name);
            tvName.setVisibility(BmtUtil.isOverOne() ? View.VISIBLE : View.GONE);
            if (TextUtils.isEmpty(name)) {
                tvName.setText("");
            } else {
                tvName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
            }
        }
        if (layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[0] ||
                layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[1]) {
            LinearLayout llSign = container.findViewById(R.id.ll_sign);
            LocalTextView tvType = container.findViewById(R.id.tv_type);
            if (llSign != null) {
                TextView finalTvName = tvName;
                llSign.post(() -> {
                    int containWidth = clParent.getWidth();
                    int llSignWidth = llSign.getWidth();
                    int maxWidth = containWidth - DensityUtil.dp2px(getContext(), 40)
                            - llSignWidth;
                    if (tvType != null) {
                        tvType.setMaxWidth(maxWidth);
                    }
                    if (finalTvName != null) {
                        finalTvName.setMaxWidth(maxWidth);
                    }
                });
            }
        }
        // bmt
        if (layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[0]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.POWER_STORE_CURRENT);
            PowerStoreLoadSaturationView loadSaturationView = container.findViewById(R.id.lsv_power_store);
            if (loadSaturationView != null) {
                loadSaturationView.setDefaultVal("0", "0", "6", "1");
                loadSaturationView.setDefaultBattery(85, true,
                        BatteryChargeView.NORMAL, 0.8f);
                loadSaturationView.setMode(1);
                return;
            }
        } else if (layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[1]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.POWER_STORE_BATTERY);
            // battery
            SegmentSlideBar segmentSlideBar = container.findViewById(R.id.ssb);
            BatteryChargeView powerBatteryView = container.findViewById(R.id.power_battery_view);
            if (segmentSlideBar != null && powerBatteryView != null) {
                int percent = 15;
                List<Segment> segments = new ArrayList<>();
                segments.add(new Segment(0.9f, 1.0f, getString(R.string.power_battery_bar_status_text_1), getContext().getResources().getColor(R.color.power_station_battery_color_1)));
                segments.add(new Segment(0.6f, 0.9f, getString(R.string.power_battery_bar_status_text_2), getContext().getResources().getColor(R.color.power_station_battery_color_2)));
                segments.add(new Segment(0.12f, 0.6f, getString(R.string.power_battery_bar_status_text_3), getContext().getResources().getColor(R.color.power_station_battery_color_3)));
                segments.add(new Segment(0.02f, 0.12f, getString(R.string.power_battery_bar_status_text_4), getContext().getResources().getColor(R.color.power_station_battery_color_4)));
                segments.add(new Segment(0.0f, 0.02f, getString(R.string.power_battery_bar_status_text_5), getContext().getResources().getColor(R.color.power_station_battery_color_5)));
                segmentSlideBar.setSegments(segments, 4);
                powerBatteryView.setOnline(true);
                powerBatteryView.setSuccess(true);
                powerBatteryView.setProgress(percent / 100f, true);
                powerBatteryView.setChargeStatus(BatteryChargeView.NORMAL, true);

                segmentSlideBar.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        segmentSlideBar.setRemainSpace(clParent.getWidth() / 2f - powerBatteryView.getWidth() / 2f - DensityUtil.dp2px(getContext(), 9.5f));
                        segmentSlideBar.setProgress(percent / 100f, true);
                    }
                }, 200);
            }
        } else if (layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[2]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.POWER_STORE_TODAY_USAGE);
            SectionLineChart lcUsage = container.findViewById(R.id.lc_usage);
            LocalTextView tvUsage = container.findViewById(R.id.tv_usage);
            LocalTextView tvValue = container.findViewById(R.id.tv_value);
            LocalTextView tvPeak = container.findViewById(R.id.tv_peak);
            LocalTextView tvUnit = container.findViewById(R.id.tv_unit);
            LinearLayout llValue = container.findViewById(R.id.ll_value);
            LineChartManager lineChartManager = new LineChartManager(container.getContext(), lcUsage);
            int hourCount = 1440;
            int interval = 5;
            int size = hourCount / interval + 1;
            List<List<Float>> chartData = new ArrayList<>();
            for (int i = 0; i < hourCount / interval; i++) {
                List<Float> sonData = new ArrayList<>(4);
                sonData.add(i * 5.0f);
                sonData.add(ChartDataUtil.getRandom(3000, 0) * 1.0f);
                sonData.add(ChartDataUtil.getRandom(1000, 0) * 1.0f);
                sonData.add(ChartDataUtil.getRandom(1000, 0) * 1.0f);
                chartData.add(sonData);
            }

            float sumData = getSumVal(chartData, interval, true);
            String usage = ChartDataUtil.getPowerTransferVal(sumData, sumData, false);
            tvValue.setLocalText(usage);
            String unit = ChartDataUtil.getPowerUnit(sumData, true);
            tvUnit.setLocalText(unit);
            float peakVal = lineChartManager.getYMax(chartData, true);
            String peakStr = ChartDataUtil.getPowerTransferVal(peakVal, peakVal, true)
                    + ChartDataUtil.getPowerUnit(peakVal, false);
            tvPeak.setText(Local.s(getString(R.string.ps_peak)) + " " + peakStr);
            lineChartManager.initChart(interval, chartData, true, hourCount);
            lineChartManager.setData(chartData, false, true, hourCount, true);
            TextView finalTvName1 = tvName;
            llValue.post(() -> {
                int containWidth = clParent.getWidth();
                int llValueWidth = llValue.getWidth();
                int tvPeakWidth = tvPeak.getWidth();
                int maxWidth = containWidth - DensityUtil.dp2px(getContext(), 40)
                        - Math.max(llValueWidth, tvPeakWidth);
                if (tvUsage != null) {
                    tvUsage.setMaxWidth(maxWidth);
                }
                if (finalTvName1 != null) {
                    finalTvName1.setMaxWidth(maxWidth);
                }
            });
        } else if (layoutId == PluginWidgetStyleUtil.powerStoreSampleStyleArray[3]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.POWER_STORE_EMERGENCY);
            BatteryChargeView batteryChargeView = container.findViewById(R.id.battery_charge_view);
            batteryChargeView.setOnline(true);
            batteryChargeView.setSuccess(true);
            batteryChargeView.setChargeStatus(BatteryChargeView.NORMAL, false);
            batteryChargeView.setProgress(1.0f, true);
            BmtDeviceTagView tvPluginName = container.findViewById(R.id.tv_plugin_name);
            tvPluginName.post(new Runnable() {
                @Override
                public void run() {
                    tvPluginName.setText(Local.s(getString(R.string.emergency_charge)),
                            name.equals(Constants.POWER_STATION) ? Local.s(name) : name, BmtUtil.isOverOne());
                }
            });
        }
    }

    private void dealPowerPulsePreview(View container, int layoutId) {
        String name = data.get(selectedDevicePosition).getName();
        TextView tvName = null;
        ConstraintLayout clParent = container.findViewById(R.id.cl_parent);
        if (layoutId == PluginWidgetStyleUtil.powerPulseSampleStyleArray[0]) {
            tvName = container.findViewById(R.id.tv_plugin_name);
            tvName.setVisibility(BmtUtil.isOverOne() ? View.VISIBLE : View.GONE);
            if (TextUtils.isEmpty(name)) {
                tvName.setText("");
            } else {
                tvName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
            }
        }
        if (layoutId == PluginWidgetStyleUtil.powerPulseSampleStyleArray[0]) {
            LinearLayout llSign = container.findViewById(R.id.ll_sign);
            LocalTextView tvType = container.findViewById(R.id.tv_type);
            if (llSign != null) {
                TextView finalTvName = tvName;
                llSign.post(() -> {
                    int containWidth = clParent.getWidth();
                    int llSignWidth = llSign.getWidth();
                    int maxWidth = containWidth - DensityUtil.dp2px(getContext(), 40)
                            - llSignWidth;
                    if (tvType != null) {
                        tvType.setMaxWidth(maxWidth);
                    }
                    if (finalTvName != null) {
                        finalTvName.setMaxWidth(maxWidth);
                    }
                });
            }
        }
        // bmt
        if (layoutId == PluginWidgetStyleUtil.powerPulseSampleStyleArray[0]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.POWER_PULSE_CURRENT);
            PowerPulseLoadSaturationView loadSaturationView = container.findViewById(R.id.lsv_power_pulse);
            if (loadSaturationView != null) {
                loadSaturationView.setDefaultVal("88", "0");
                loadSaturationView.setDefaultBattery(85, true,
                        BatteryChargeView.NORMAL, 0.8f);
                loadSaturationView.setMode(1);
                return;
            }
        }
    }

    private float getSumVal(List<List<Float>> data, int interval, boolean isBSensorInstall) {
        float sum = 0;
        if (CollectionUtil.isListNotEmpty(data)) {
            List<Float> sumData = getSumData(data, isBSensorInstall);
            for (Float val : sumData) {
                sum = sum + val;
            }
        }
        return sum * interval / 60;
    }

    private List<Float> getSumData(List<List<Float>> data, boolean isBSensorInstall) {
        List<Float> sumData = new ArrayList<>();
        if (CollectionUtil.isListNotEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                List<Float> sonData = data.get(i);
                float sum = 0;
                if (CollectionUtil.isListNotEmpty(sonData)) {
                    if (sonData.size() > 1) {
                        sum = sum + sonData.get(1);
                    }
                    if (sonData.size() > 2) {
                        if (isBSensorInstall) {
                            sum = sum + sonData.get(2);
                        }
                    }
                    if (sonData.size() > 3) {
                        sum = sum + sonData.get(3);
                    }
                    sumData.add(sum);
                    Collections.sort(sumData);
                }
            }
        }
        return sumData;
    }

    private void setAddEnabled(boolean isEnabled) {
        mBinding.btnAdd.setAlpha(isEnabled ? 1f : 0.5f);
        mBinding.btnAdd.setEnabled(isEnabled);
    }


    /**
     * 处理ipc的三种卡片
     *
     * @param container
     * @param curIndex
     */
    private void dealIpcPreview(View container, int curIndex) {
        DDLog.d(TAG, "dealIpcPreview.");
        LocalTextView tvName = container.findViewById(R.id.tv_name);
        if (curIndex == PluginWidgetStyleUtil.ipcSampleStyleArray[1]) {
            setViewAddStatue(container, PluginWidgetStyleUtil.IPC_MORE);
            tvName.setText(Local.s(getString(R.string.multi_screen)));
            LinearLayout llContent = container.findViewById(R.id.ll_content);
            ArrayList<ImageView> list = new ArrayList<>();
            list.add(container.findViewById(R.id.iv_cv1));
            list.add(container.findViewById(R.id.iv_cv2));
            list.add(container.findViewById(R.id.iv_cv3));
            list.add(container.findViewById(R.id.iv_cv4));
            final int[] defImage = new int[]{R.drawable.img_multiscreen_default_1, R.drawable.img_multiscreen_default_2
                    , R.drawable.img_multiscreen_default_3, R.drawable.img_multiscreen_default_4};
            File file;
            ArrayList<String> dsCamIds = DsCamUtils.getMultiPlayDsCamIds();
            for (int i = 0; i < dsCamIds.size(); i++) {
                file = getPreview(IPCManager.getInstance().getDsCamDeviceByID(dsCamIds.get(i)));
                if (file != null && file.exists()) {
                    list.get(i).setImageURI(Uri.fromFile(file));
                } else {
                    list.get(i).setImageResource(defImage[i]);
                }
            }
            if (list.size() > dsCamIds.size()) {
                for (int j = dsCamIds.size(); j < list.size(); j++) {
                    list.get(j).setVisibility(View.GONE);
                }
                if (dsCamIds.size() < 3) {
                    ViewGroup.LayoutParams params = llContent.getLayoutParams();
                    params.height = (int) getResources().getDimension(R.dimen.panel_ipc_small_item_height);
                    llContent.setLayoutParams(params);
                }
            }

        } else {
            tvName.setText(data.get(selectedDevicePosition).getName());
            CameraVideoView cvv = container.findViewById(R.id.cvv);
            File file = getPreview(selectedDevice);
            if (file != null && file.exists()) {
                cvv.setCoverImageUri(Uri.fromFile(file));
            } else {
                cvv.setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
            }

            // ipc小卡片不显示播放按钮
            if (curIndex == PluginWidgetStyleUtil.ipcSampleStyleArray[2]) {
                setViewAddStatue(container, PluginWidgetStyleUtil.IPC_SMALL);
                cvv.setPlayIconResId(0);
            } else {
                setViewAddStatue(container, PluginWidgetStyleUtil.IPC);
                LocalTextView tvBattery = container.findViewById(R.id.tv_battery);
                if (null != tvBattery) {
                    final boolean showBatteryIcon = DsCamUtils.isDsCamV015Device(selectedDevice);
                    tvBattery.setVisibility(!showBatteryIcon ? View.VISIBLE : View.GONE);
                }
            }
        }

    }

    private File getPreview(Device device) {
        //  预览图
        final String snapshotPath = DeviceHelper.getString(device, HeartLaiConstants.ATTR_SNAPSHOT, "");
        if (!TextUtils.isEmpty(snapshotPath)) {
            File file = new File(snapshotPath);
            return file;
        }
        return null;
    }


    private int[] removeArrayIndex(int num, int[] array) {
        int[] newArray = new int[array.length - 1];
        for (int i = 0; i < newArray.length; i++) {
            if (num < 0 || num >= array.length) {
                throw new RuntimeException("下标越界！");
            }
            if (i < num) {
                newArray[i] = array[i];
            } else {
                newArray[i] = array[i + 1];
            }
        }
        return newArray;
    }

    private void setViewAddStatue(View container, String layoutType) {
        final boolean isAdd = isDeviceAdded(layoutType);
        container.setAlpha(isAdd ? 0.5f : 1.0f);
        mEnabledList.add(!isAdd);
    }

    private boolean isDeviceAdded(String layoutType) {
        if (CollectionUtil.isListNotEmpty(cacheLayoutList)) {
            if (selectedDevice != null) {
                String devId = selectedDevice.getId();
                if (BmtUtil.isBmtDevice(selectedDevice)) {
                    devId = selectedDevice.getId() + MainWidgetListProvider.UNDERLINE + selectedDevice.getSubCategory();
                }
                if (MainPanelHelper.getInstance().checkIsInBlackList(devId)) {
                    return false;
                }
                for (MainWidgetBean mainWidgetBean : cacheLayoutList) {
                    String widgetId = mainWidgetBean.getPluginId();
                    if (!TextUtils.isEmpty(widgetId)
                            && widgetId.equals(devId)
                            && mainWidgetBean.getLayoutType().equals(layoutType)) {
                        return true;
                    }
                }
            } else {
                if (!TextUtils.isEmpty(selectedPanelDeviceId)) {
                    if (MainPanelHelper.getInstance().checkIsInBlackList(selectedPanelDeviceId)) {
                        return false;
                    }
                    for (MainWidgetBean mainWidgetBean : cacheLayoutList) {
                        String widgetId = mainWidgetBean.getPluginId();
                        if (!TextUtils.isEmpty(selectedPanelDeviceId) && !TextUtils.isEmpty(widgetId)
                                && widgetId.equals(selectedPanelDeviceId) && mainWidgetBean.getLayoutType().equals(layoutType)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    @Override
    public void onPageScrolled(int i, float v, int i1) {
    }

    @Override
    public void onPageSelected(int index) {
        curViewPagerSelectedIndex = index;
        for (int i = 0; i < dotViews.length; i++) {
            dotViews[i].setSelected(curViewPagerSelectedIndex == i);
            dotViews[i].setImageResource(curViewPagerSelectedIndex == i
                    ? R.drawable.circle_white01_shape : R.drawable.circle_white04_shape);
        }
        if (CollectionUtil.isListNotEmpty(mEnabledList)) {
            setAddEnabled(mEnabledList.get(index));
        }
    }

    @Override
    public void onPageScrollStateChanged(int i) {

    }


    class ViewPagerAdapter extends PagerAdapter {


        private ArrayList<View> mViewList;

        public ViewPagerAdapter(ArrayList<View> viewList) {
            mViewList = viewList;
        }

        @Override
        public int getCount() {
            return mViewList.size();
        }


        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return view == object;
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            container.addView(mViewList.get(position));
            return mViewList.get(position);
        }

        @Override
        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            if (position < mViewList.size()) {
                container.removeView(mViewList.get(position));
            }
        }
    }
}
