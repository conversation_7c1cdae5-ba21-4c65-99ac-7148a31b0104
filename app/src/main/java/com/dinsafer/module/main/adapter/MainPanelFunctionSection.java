package com.dinsafer.module.main.adapter;

import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.caremode.CareModeFragment;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.IPCKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dscam.timeline.MotionRecordTimelinePlayerActivity;
import com.dinsafer.model.panel.MainPanelFunctionItem;
import com.dinsafer.model.panel.MainPanelFunctionItemViewHolder;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.iap.PrimeServicesFragment;
import com.dinsafer.module.main.view.EventListFragment;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.powerstation.gridrewards.GridRewardsNotFirstFragment;
import com.dinsafer.module.settting.ui.IPCSosRecordListFragment;
import com.dinsafer.module.settting.ui.SimplePlugsListFragment;
import com.dinsafer.module_base.smartwidget.SmartWidgetBuilder;
import com.dinsafer.module_base.smartwidget.apis.ISmartWidget;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.therouter.TheRouter;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;

/**
 * 首页Panel Function 分组
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/2 5:49 PM
 */
@Deprecated
public class MainPanelFunctionSection extends MainPanelBaseSection<MainPanelFunctionItem> {

    private WeakReference<MainActivity> mWeakMainActivity;

    private final int ROW_ITEM_COUNT = 3;

    public MainPanelFunctionSection(MainActivity mainActivity, String tittle,
                                    ArrayList<MainPanelFunctionItem> datas) {
        super(mainActivity, SectionParameters.builder()
                .itemResourceId(R.layout.main_section_panel_item_function)
                .headerResourceId(R.layout.main_section_panel_item_header)
                .build(), tittle, datas);
        mWeakMainActivity = new WeakReference<>(mainActivity);
    }

    @Override
    public int getRowItemCount() {
        return ROW_ITEM_COUNT;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new MainPanelFunctionItemViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        MainPanelFunctionItemViewHolder itemHolder = (MainPanelFunctionItemViewHolder) holder;

        if (position >= mData.size()) {
            // 空白填充
            itemHolder.setBackgroundColor(mContext.getResources().getColor(R.color.bgColorPage));
            itemHolder.setViewDataEmpty(true);
            itemHolder.setRootViewEnable(true);
            itemHolder.setRootViewVisible(false);
            return;
        }
        itemHolder.setRootViewVisible(true);

        MainPanelFunctionItem itemData = mData.get(position);

        itemHolder.setRootViewClickListener((View v) -> {
            if (null == mWeakMainActivity.get()) {
                DDLog.e(TAG, "MainActivity is empty.");
                return;
            }

            MainActivity mainActivity = mWeakMainActivity.get();

            switch (itemData.getType()) {
                case MainPanelHelper.FUNCTION_TYPE_EXTRA_SECURITY:
                    mainActivity.addCommonFragment(PrimeServicesFragment.newInstance());
                    break;
                case MainPanelHelper.FUNCTION_TYPE_SMART_WIDGET:
                    toSmartWidget();
                    break;
                case MainPanelHelper.FUNCTION_TYPE_SMART_BUTTON:
                    mainActivity.addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.SMART_BUTTON
                            , mainActivity.getResources().getString(R.string.smart_button_name)));
                    break;
                case MainPanelHelper.FUNCTION_TYPE_CARE_MODE:
                    mainActivity.addCommonFragment(CareModeFragment.newInstance());
                    break;
                case MainPanelHelper.FUNCTION_TYPE_VIDEO_LIST:
                    mainActivity.addCommonFragment(IPCSosRecordListFragment.newInstance());
                    break;
                case MainPanelHelper.FUNCTION_TYPE_VIDEO_TIME_LINE:
                    MotionRecordTimelinePlayerActivity.start(mainActivity);
                    mainActivity.setNotNeedToLogin(true);
                    break;
                case MainPanelHelper.FUNCTION_TYPE_ADD_EVENT_LIST:
                    mainActivity.addCommonFragment(EventListFragment.newInstance());
                    break;
                case MainPanelHelper.FUNCTION_TYPE_GRID_REWARDS:
                    break;
                default:
                    break;
            }
        });

        // 数据填充
        itemHolder.setViewDataEmpty(false);
        itemHolder.setRootViewEnable(getViewEnableState(itemData.getType()));
        itemHolder.setNormalData(itemData.getName(), itemData.getIconResId(),
                mContext.getResources().getColor(itemData.getEnableBgColorResId()));
    }

    /**
     * 获取Item是否可以被点击的状态
     *
     * @param type
     * @return
     */
    private boolean getViewEnableState(@MainPanelHelper.PanelFunctionType int type) {
//        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
//        final Device device = DinHome.getInstance().getDevice(panelId);
//        if (TextUtils.isEmpty(panelId)
//                || null == device) {
//            return false;
//        }
        // 编辑模式下，都不能点击
        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            return false;
        }

        boolean enable;
        switch (type) {
            case MainPanelHelper.FUNCTION_TYPE_EXTRA_SECURITY:
            case MainPanelHelper.FUNCTION_TYPE_VIDEO_LIST:
            case MainPanelHelper.FUNCTION_TYPE_VIDEO_TIME_LINE:
            case MainPanelHelper.FUNCTION_TYPE_ADD_EVENT_LIST:
            case MainPanelHelper.FUNCTION_TYPE_GRID_REWARDS:
                enable = true;
                break;
            case MainPanelHelper.FUNCTION_TYPE_SMART_WIDGET:
            case MainPanelHelper.FUNCTION_TYPE_SMART_BUTTON:
            case MainPanelHelper.FUNCTION_TYPE_CARE_MODE:
            default:
                enable = MainPanelHelper.getInstance().isFunctionEnable();
                break;

        }
        return enable;
    }

    /**
     * 跳转SmartWidget页
     */
    private void toSmartWidget() {
        if (null == mWeakMainActivity
                || null == mWeakMainActivity.get()) {
            return;
        }

        if (!APIKey.IS_OPEN_PLUGIN) {
            DDLog.e(TAG, "APIKey.IS_OPEN_PLUGIN is false.");
            return;
        }

        mWeakMainActivity.get().setNotNeedToLogin(true);
        TheRouter.get(ISmartWidget.class).start(mWeakMainActivity.get(),
                new SmartWidgetBuilder()
                        .setAppId(APIKey.APP_ID)
                        .setHomeId(HomeManager.getInstance().getCurrentHome().getHomeID())
                        .setDeviceId(CommonDataUtil.getInstance().getCurrentPanelID())
                        .setUserToken(DinSDK.getUserInstance().getUser().getToken())
                        .setIp(DinSaferApplication.getHttpdns().getIpByHostAsync(APIKey.SERVER_IP))
                        .setDomain(APIKey.SERVER_IP)
                        .setConfigAssertFileName("widgetConfig.json"));
    }

}
