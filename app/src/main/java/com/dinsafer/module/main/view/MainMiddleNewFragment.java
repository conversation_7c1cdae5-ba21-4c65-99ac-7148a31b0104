package com.dinsafer.module.main.view;

import static com.dinsafer.config.IPCKey.SMART_PLUGS;
import static com.dinsafer.config.LocalKey.HOME_IPC_MOTION_DETECTED_EVENT;

import android.Manifest;
import android.animation.ValueAnimator;
import android.app.Dialog;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.caremode.CareModeNoActionDialog;
import com.dinsafer.common.BmtManager;
import com.dinsafer.common.Constants;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.common.PluginManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.IPCKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.config.PsVersion1EventCode;
import com.dinsafer.config.PsVersion2EventCode;
import com.dinsafer.dialog.CommonAlertDialog;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dincore.common.IDeviceStatusListener;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.MainFragmentNewBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DeviceRequestType;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dscam.DsCamMultiFullPlayActivity;
import com.dinsafer.dscam.DsCamStatusChange;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.dscam.guide.DsDeviceGuideTipDialog;
import com.dinsafer.dscam.guide.event.MotionFrequencyPushEvent;
import com.dinsafer.dscam.guide.event.ShowDsCamGuideTipEvent;
import com.dinsafer.dscam.timeline.MotionRecordTimelinePlayerActivity;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.AppStateEvent;
import com.dinsafer.model.AppStatePreEvent;
import com.dinsafer.model.BleGetDeviceOnlineEvent;
import com.dinsafer.model.CareModeNoActionEvent;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.CloseAllDeviceEvent;
import com.dinsafer.model.DeviceCmdEvent;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.DeviceSimStatueEvent;
import com.dinsafer.model.DoorbellCallPushEvent;
import com.dinsafer.model.FamilyInfoUpdateEvent;
import com.dinsafer.model.FamilySwitchEvent;
import com.dinsafer.model.GetBmtRegionCountriesEvent;
import com.dinsafer.model.GoMotionRecordListFragmentEvent;
import com.dinsafer.model.IPCAlertExhaustedEvent;
import com.dinsafer.model.IPCMotionDetectedPushEvent;
import com.dinsafer.model.KnockToSosChange;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.RemoveSosFinish;
import com.dinsafer.model.SOSFragmentShowEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.model.SelfTestEvent;
import com.dinsafer.model.ShowSuccessToastEvent;
import com.dinsafer.model.SosStatusEntry;
import com.dinsafer.model.TuyaItem;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.UnCloseDoorEntry;
import com.dinsafer.model.UserDeviceListChangeEvent;
import com.dinsafer.model.UserPermissonUpdata;
import com.dinsafer.model.UserUidChangeEvent;
import com.dinsafer.model.WebSocketEvent;
import com.dinsafer.model.event.AddWidgetEvent;
import com.dinsafer.model.event.CheckDeviceEvent;
import com.dinsafer.model.event.CheckFamilyBalanceContractInfoEvent;
import com.dinsafer.model.event.DeviceLoadedStateChangedEvent;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.model.event.GetAllDeviceFinishedEvent;
import com.dinsafer.model.event.GetPluginQuantityEvent;
import com.dinsafer.model.event.GoAddMoreEvent;
import com.dinsafer.model.event.GoShareQREvent;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.model.event.HadLogoutPreEvent;
import com.dinsafer.model.event.IpcSyncTimezoneEvent;
import com.dinsafer.model.event.KeypadMemberPwdUpdatedEvent;
import com.dinsafer.model.event.MultiPlayDsCamIdsChangeEvent;
import com.dinsafer.model.event.NeedGetAllDeviceEvent;
import com.dinsafer.model.event.NeedLoadInfoPluginsEvent;
import com.dinsafer.model.event.NeedRemovePluginsEvent;
import com.dinsafer.model.event.PanelUpgradeStateChangeEvent;
import com.dinsafer.model.event.PluginListUpdateEvent;
import com.dinsafer.model.event.OMSNotificationEvent;
import com.dinsafer.model.event.UpdateFamilyPanelStateEvent;
import com.dinsafer.model.event.WidgetFlagDeletedEvent;
import com.dinsafer.model.family.FamilyListChangeEvent;
import com.dinsafer.model.family.FcmAuthorityUpdatedEvent;
import com.dinsafer.model.family.FcmIpcLowBatteryEvent;
import com.dinsafer.model.family.FcmKeypadMemberPwdUpdateEvent;
import com.dinsafer.model.family.FcmMemberDeleteEvent;
import com.dinsafer.model.family.FetchHomeListEvent;
import com.dinsafer.model.home.EventListHelper;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.addmore.view.AddMoreFragment;
import com.dinsafer.module.addmore.view.AddMoreHelper;
import com.dinsafer.module.daily.DailyMemoriesPushEvent;
import com.dinsafer.module.daily.DailyMemoriesVideoPlayActivity;
import com.dinsafer.module.doorbell.play.DsDoorbellPlayFocusModeActivity;
import com.dinsafer.module.doorbell.play.DsDoorbellPlayFullscreenActivity;
import com.dinsafer.module.family.view.CreateFamilyFragment;
import com.dinsafer.module.iap.BetaUserClubInvitationFragment;
import com.dinsafer.module.iap.BetaUserInviteSuccessEvent;
import com.dinsafer.module.iap.CloudStorageServiceHelper;
import com.dinsafer.module.iap.IPCSubscriptionFragment;
import com.dinsafer.module.iap.ListBigBannerResponse;
import com.dinsafer.module.iap.NewActivityEvent;
import com.dinsafer.module.iap.powercare.TrafficPackageServiceHelper;
import com.dinsafer.module.iap.powercare.TrafficPackageServiceRenewFragment;
import com.dinsafer.module.ipc.common.video.IPCHeartLaiMotionRecordIJKPlayerActivity2;
import com.dinsafer.module.ipc.common.video.global.motion.MotionDownloadManager;
import com.dinsafer.module.ipc.heartlai.GoNetworkConfigFragmentEvent;
import com.dinsafer.module.ipc.heartlai.HeartLaiFullPlayActivity;
import com.dinsafer.module.ipc.heartlai.event.HeartLaiConnectStatusChangeEvent;
import com.dinsafer.module.ipc.heartlai.event.IPCDelEvent;
import com.dinsafer.module.ipc.heartlai.event.IPCInfoChangeEvent;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module.ipc.heartlai.event.NeedGetAllHeartLaiEvent;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.main.adapter.MainPanelBindMultiAdapter;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.module.main.helper.MainGridLayoutManager;
import com.dinsafer.module.main.helper.MainRvItemTouchHelper;
import com.dinsafer.module.main.helper.MainWidgetListProvider;
import com.dinsafer.module.main.model.BaseMainItemModel;
import com.dinsafer.module.main.model.MainAddMorePluginModel;
import com.dinsafer.module.main.model.MainBatteryModel;
import com.dinsafer.module.main.model.MainBmtEmergencyModel;
import com.dinsafer.module.main.model.MainBmtV2Model;
import com.dinsafer.module.main.model.MainPowerStoreCurrentModel;
import com.dinsafer.module.main.model.OnWidgetItemListener;
import com.dinsafer.module.main.task.DeviceRequestListener;
import com.dinsafer.module.main.task.DeviceRequestManager;
import com.dinsafer.module.other.RestrictModelInputPhoneDialog;
import com.dinsafer.module.pirSensitivity.event.PirSensitivityEnterSettingEvent;
import com.dinsafer.module.powerstation.PSFirmWareVersionFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.dialog.EVEventDialog;
import com.dinsafer.module.powerstation.electricity.ElectricityStatisticsFragment;
import com.dinsafer.module.powerstation.ev.PSEVChargeV3Fragment;
import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BmtChartDataEvent;
import com.dinsafer.module.powerstation.event.BmtChipsStatusReloadEvent;
import com.dinsafer.module.powerstation.event.BmtDeadlineEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGetFeatureEvent;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtListUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtShowUpdateDialogEvent;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module.powerstation.event.HomePowerStationDefaultEvent;
import com.dinsafer.module.powerstation.event.PushBmtEVEvent;
import com.dinsafer.module.powerstation.event.PushBmtExceptionEvent;
import com.dinsafer.module.powerstation.event.ReOpenEvent;
import com.dinsafer.module.powerstation.event.ShowBmtGuideTipEvent;
import com.dinsafer.module.powerstation.event.ShowHomeGuideEvent;
import com.dinsafer.module.powerstation.gridrewards.GridRewardsFragment;
import com.dinsafer.module.powerstation.gridrewards.GridRewardsNotFirstFragment;
import com.dinsafer.module.powerstation.gridrewards.PSAuthorizationRecordsFragment;
import com.dinsafer.module.powerstation.gridrewards.PSTerminateFragment;
import com.dinsafer.module.powerstation.guide.PSGuideFragment;
import com.dinsafer.module.powerstation.impacts.ReserveModeFragment;
import com.dinsafer.module.settting.ui.AdvancedSettingFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.ApStepHeartLaiIpcFragment;
import com.dinsafer.module.settting.ui.BaseToastFragment;
import com.dinsafer.module.settting.ui.DefineHomeArmFragment;
import com.dinsafer.module.settting.ui.IPCAlertExhaustedPushDialog;
import com.dinsafer.module.settting.ui.IPCSosRecordListFragment;
import com.dinsafer.module.settting.ui.MotionDetectedPushDialog;
import com.dinsafer.module.settting.ui.MyFamilyFragment;
import com.dinsafer.module.settting.ui.ScannerActivity;
import com.dinsafer.module.settting.ui.ShareQR;
import com.dinsafer.module.settting.ui.WebViewActivity;
import com.dinsafer.module.settting.ui.event.NotificationLanguageChangeEvent;
import com.dinsafer.module_base.smartwidget.CloseSmartWidgetEvent;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.api.IHomeCallBack;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.module_home.bean.HomeConstants;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.nova.Widget;
import com.dinsafer.panel.bean.device.DoorSensorDevice;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.permission.event.CheckNotificationStateEvent;
import com.dinsafer.permission.event.NeedPermissionEvent;
import com.dinsafer.receiver.MyBaseDinsaferPushReveiver;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.MainPanelWidgetItemDivider;
import com.dinsafer.ui.MainTittleBar;
import com.dinsafer.ui.anim.ShakeAnimUtil;
import com.dinsafer.ui.device.MainDeviceStatusView;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.ActivityManager;
import com.dinsafer.util.AlertDialogManager;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;
import com.dinsafer.util.LocationHelper;
import com.dinsafer.util.ModifyPluginInfoHelper;
import com.dinsafer.util.NetworkCheckUtil;
import com.dinsafer.util.PingUtil;
import com.dinsafer.util.PluginWidgetStyleUtil;
import com.dinsafer.util.SettingInfoHelper;
import com.dinsafer.util.TestLog;
import com.dinsafer.util.comparator.TuyaItemPlusComparator;
import com.dinsafer.util.comparator.TuyaItemPlusComparator2;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


/**
 * 新的应用主页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/1 5:57 PM
 */
public class MainMiddleNewFragment extends BaseFragment
        implements MainTittleBar.OnTittleLayoutClickListener,
        MainDeviceStatusView.OnDeviceStatusActionClickListener,
        IDeviceCallBack, IDeviceStatusListener, IDeviceListChangeListener, IHomeCallBack,
        ViewTreeObserver.OnGlobalLayoutListener {

    private final String TAG = MainMiddleNewFragment.class.getSimpleName();

    // Panel item分割线宽度
    private final static int PANEL_ITEM_DIVIDER_WIDTH_DP = 8;
    private final static long DURATION_LOAD_MORE_MILLIS = 10 * 1000L;

    private MainFragmentNewBinding mBinding;

    private ArrayList<TuyaItemPlus> mShortcutSectionList;
    private ArrayList<Device> mIpcSectionList;
    private ArrayList<Device> mBmtSectionList;
    private ArrayList<Device> mPowerStoreSectionList;
    private ArrayList<Device> mPowerPulseSectionList;

    private ArrayList<TuyaItemPlus> mDoorSensorSectionList;
    private ArrayList<TuyaItemPlus> mSmartPlugPluginList = new ArrayList<>(); // 插座
    private ArrayList<TuyaItemPlus> mDoorSensorPluginList = new ArrayList<>(); // 门磁
    // 源数据
    private ArrayList<Device> mSmartPluginDeviceList = new ArrayList<>();
    private ArrayList<Device> mDoorSensorDeviceList = new ArrayList<>();

    private static final byte[] mFetchAddDeviceLock = new byte[0];

    private PanelDevice mPanelDevice;

    private volatile boolean mLoadingDevices;
    private boolean selfOperate;

    // 首页门磁转态更新的map，防止为未加载完首页数据前拿到status导致配件状态丢失的问题
    private Map mPluginStatusMap;
    private volatile boolean mCheckNotificationPermission = false;

    private final static Handler deviceOfflineDialogHandler = new Handler(Looper.getMainLooper());
    private final Handler mTaskHandler = new Handler(Looper.getMainLooper());

    private MainPanelBindMultiAdapter<BaseMainItemModel<?>> pluginWidgetAdapter;
    private ItemTouchHelper itemTouchHelper;
    private final ArrayList<BaseMainItemModel<?>> pluginWidgetList = new ArrayList<>();

    private MainTittleBar mainTittleLayout;

    private boolean isBackground;

    private Animation mShakeAnimation;

    /**
     * 检查Device是否为空
     */
    private final Runnable mCheckDeviceNullTask = () -> DeviceCallBackManager.getInstance().checkDeviceIsNull();

    /**
     * 添加主机后刷新界面
     */
    private final Runnable mAddPanelRefreshTask = () -> {
        getDelegateActivity().removeAllCommonFragment();
        if (CommonDataUtil.getInstance().isHadPanelNotDeleted()) {
            // 当前主机离线重新添加
            DDLog.w(TAG, "主机离线重新添加");
            EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
        } else {
            // 当前没有主机新添加
            DDLog.w(TAG, "没有主机新添加");
            HomeManager.getInstance().refreshFamilyInfo();
        }
        getMainActivity().showLoadingFragment(LoadingFragment.BLACK, "");
    };

    /**
     * 主机配网后刷新界面
     */
    private final Runnable mReConfigPanelNetworkTask = () -> {
        getMainActivity().removeCommonFragment(BaseToastFragment.class.getName());
        EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
        getMainActivity().showLoadingFragment(LoadingFragment.BLACK, "");
    };

    // 是否需要获取家庭信息
    private boolean isNeedHomeLocation = true;
    // 是否从设置权限界面回来
    // (注: 这个字段只供重新isNeedHomeLocation设置用)
    // 以后其它具体功能具体分析
    private boolean isAfterSetting = false;


    public static MainMiddleNewFragment newInstance() {
        return new MainMiddleNewFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.main_fragment_new,
                container, false);
        EventBus.getDefault().register(this);
        DinSDK.getHomeInstance().addHomeStatusCallback(this);
        initListener();
        initData();
        initMainWidgetRecycleView();
        initMainWidgetListProvider();
        if (DBUtil.Exists(DBKey.CURRENT_HOME_ID) && !TextUtils.isEmpty(DBUtil.Str(DBKey.CURRENT_HOME_ID))) {
            changeMainContentWithCacheWidgetList(DBUtil.Str(DBKey.CURRENT_HOME_ID));
        } else {
            changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_EMPTY);
        }
        changeMainContentEmptyScanVisible(false);
        initAlertService();
        updataUI();
        return mBinding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isAfterSetting) {
            if (ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                isNeedHomeLocation = true;
            }
        }
        isAfterSetting = false;
        DDLog.i(TAG, "XXX===onResume");
    }

    private void getHomeLocation() {
        if (isNeedHomeLocation && getDelegateActivity().getFragmentList().size() == 0) {
            isNeedHomeLocation = false;
            DinSDK.getHomeInstance().getHomeLocation(HomeManager.getInstance().getCurrentHome().getHomeID(),
                    new IDefaultCallBack2<HomeLocationResponse.ResultBean>() {
                        @Override
                        public void onSuccess(HomeLocationResponse.ResultBean resultBean) {
                            if (resultBean != null) {
                                double latitude = resultBean.getLatitude();
                                double longitude = resultBean.getLongitude();
                                if (latitude == 0 && longitude == 0 && resultBean.isNeed_location()) {
                                    if (ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                                        showDeviceNearby();
                                    } else {
                                        PermissionDialogUtil.showAILocationSetupPermissionDialog(getMainActivity(),
                                                v -> showDeviceNearby());
                                    }
                                }
                            }
                        }

                        @Override
                        public void onError(int i, String s) {
                        }
                    });
        }
    }

    /**
     * 附近设备弹窗
     */
    private void showDeviceNearby() {
        CommonAlertDialog.createBuilder(getDelegateActivity())
                .setTitleTxt(Local.s(getString(R.string.is_the_device_nearby)))
                .setContentTxt(Local.s(getString(R.string.is_the_device_nearby_hint)))
                .setConfirmTxt(getString(R.string.Confirm))
                .setCancelTxt(getString(R.string.set_later_in_ai_Mode))
                .setAutoDismiss(true)
                .setShowCancel(true)
                .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                    @Override
                    public void onConfirm(CommonAlertDialog dialog) {
                        PermissionDialogUtil.requestLocationPermission(getMainActivity(),
                                new PermissionDialogUtil.PermissionCallback() {
                                    @Override
                                    public void onGranted() {
                                        submitLocation();
                                    }

                                    @Override
                                    public void onDenied() {
                                        isAfterSetting = true;
                                    }
                                });
                    }

                    @Override
                    public void onCancel(CommonAlertDialog dialog) {

                    }
                })
                .builder().show();
    }

    private void submitLocation() {
        LocationHelper.getInstance(getContext()).getLocation(location -> {
            if (location != null) {
                double latitude = location.getLatitude();
                double longitude = location.getLongitude();
                DinSDK.getHomeInstance().bmtSaveLocation(HomeManager.getInstance().getCurrentHome().getHomeID(),
                        latitude, longitude, new IDefaultCallBack() {
                            @Override
                            public void onSuccess() {
                                DDLog.i(TAG, "bmtSaveLocation success");
                                LocationHelper.getInstance(getContext()).removeListener();
                            }

                            @Override
                            public void onError(int i, String s) {
                                DDLog.i(TAG, "bmtSaveLocation failed");
                                LocationHelper.getInstance(getContext()).removeListener();
                            }
                        });
            } else {
                DDLog.i(TAG, "getLocation failed");
            }
        });
    }

    /**
     * 检查是否已开启通知权限
     */
    private void checkNotificationPermission() {
        if (!DinSDK.getUserInstance().isLogin()
                || PermissionDialogUtil.hasNotificationPermission(getMainActivity())) {
            return;
        }

        PermissionDialogUtil.showNeedNotificationPermissionDialog(getMainActivity());
    }

    @Override
    public void onDestroyView() {
        //退出登录不会走这里
        EventBus.getDefault().unregister(this);
        DeviceCallBackManager.getInstance().clearAll();
        mTaskHandler.removeCallbacksAndMessages(null);
        for (BaseMainItemModel model : pluginWidgetList) {
            if (model instanceof MainBmtV2Model) {
                ((MainBmtV2Model) model).release();
            }
            if (model instanceof MainBatteryModel) {
                ((MainBatteryModel) model).release();
            }

            if (model instanceof MainBmtEmergencyModel) {
                ((MainBmtEmergencyModel) model).release();
            }

            if (model instanceof MainPowerStoreCurrentModel) {
                ((MainPowerStoreCurrentModel) model).release();
            }

            if (model instanceof MainPowerStoreCurrentModel) {
                ((MainPowerStoreCurrentModel) model).release();
            }
        }
        pluginWidgetList.clear();
        pluginWidgetAdapter = null; // 清除adapter
        ShakeAnimUtil.getInstance().stopShake();
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.destory();
            DinSDK.getHomeInstance().removeHomeStatueCallback(this);
            DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(this);
            if (deviceOfflineDialogHandler != null) {
                deviceOfflineDialogHandler.removeCallbacksAndMessages(null);
            }
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mainTittleLayout = new MainTittleBar(getContext(), mBinding.layoutTitleBar.getRoot());
        mainTittleLayout.setTittleClickListener(this);
        mBinding.mainFragmentBlackView.setOnClickListener((View view) -> mainTittleLayout.hideChangeDeviceDialog());

        View.OnClickListener scanListener = view -> {
            DDLog.i(TAG, "On scan click.");
            if (null == HomeManager.getInstance().getHomeList()
                    || 0 >= HomeManager.getInstance().getHomeList().size()) {
                getMainActivity().addCommonFragment(CreateFamilyFragment.newInstance(null));
            } else {
                ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
            }
        };
        mBinding.layoutEmpty.tvTabScan.setOnClickListener(scanListener);
        mBinding.layoutEmpty.ivScan.setOnClickListener(scanListener);

        mBinding.layoutEmpty.tvHelp.setOnClickListener(v -> {
            DDLog.i(TAG, "On help click.");
            getMainActivity().setNotNeedToLogin(true);
            startActivity(WebViewActivity.getStartIntent(getContext(), APIKey.ADD_MORE_HELP_URL_V2));
        });

        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
    }


    /**
     * 修改Panel的编辑状态
     *
     * @param isEdit true: 处于编辑转态
     * @return true: 进行了模式切换
     */
    private boolean setPanelEditMode(boolean isEdit) {
        DDLog.d(TAG, "setPanelEditMode, isEdit: " + isEdit);
        if (MainPanelHelper.getInstance().isPanelEditMode() == isEdit) {
            DDLog.e(TAG, "Old panel edit mode is same with new, do nothing.");
            return false;
        }
        MainPanelHelper.getInstance().setPanelEditMode(isEdit);
        return true;
    }

    /**
     * 编辑模式下点击了保存按钮
     */
    private void savePanelEditMode() {
        DDLog.d(TAG, "savePanelEditMode");
        if (!setPanelEditMode(false)) {
            return;
        }
        if (mainTittleLayout != null) {
            mainTittleLayout.setEditBarStatus(false);
        }
        addAllSection();
        mBinding.llSmallRefresh.setEnablePureLoadingMore(true);
        ShakeAnimUtil.getInstance().stopShake();
    }

    /**
     * 非编辑模式下点击了Edit进入编辑模式
     */
    private void enterPanelEditMode() {
        DDLog.d(TAG, "enterPanelEditMode");
        if (!setPanelEditMode(true)) {
            return;
        }
        mBinding.rvPluginWidget.scrollToPosition(0);
        if (mainTittleLayout != null) {
            mainTittleLayout.setEditBarStatus(true);
        }
        mBinding.rvPluginWidget.postDelayed(new Runnable() {
            @Override
            public void run() {
                refreshWidgetListWithData(false);
            }
        }, 300);
        mBinding.llSmallRefresh.setEnablePureLoadingMore(false);

    }

    /**
     * 初始化配件列表
     */
    private void initMainWidgetRecycleView() {
        DDLog.i(TAG, "initMainPluginRecycleView");
        mShortcutSectionList = new ArrayList<>();
        mDoorSensorSectionList = new ArrayList<>();
        mIpcSectionList = new ArrayList<>();
        mBmtSectionList = new ArrayList<>();
        mPowerStoreSectionList = new ArrayList<>();
        mPowerPulseSectionList = new ArrayList<>();

        pluginWidgetAdapter = new MainPanelBindMultiAdapter<>();
        pluginWidgetAdapter.setHeaderAndEmpty(true);
        pluginWidgetAdapter.setNewData(pluginWidgetList);
        pluginWidgetAdapter.setHasStableIds(true);

        mBinding.rvPluginWidget.setAdapter(pluginWidgetAdapter);

        GridLayoutManager gridLayoutManager = new MainGridLayoutManager(getContext(), 2);
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                int viewType = pluginWidgetAdapter.getItemViewType(position);
                if (viewType == MainPanelBindMultiAdapter.TYPE_PLUGIN
                        || viewType == MainPanelBindMultiAdapter.TYPE_SHORTCUT
                        || viewType == MainPanelBindMultiAdapter.TYPE_SMALL_IPC
                        || viewType == MainPanelBindMultiAdapter.TYPE_BMT_EV
                        || viewType == MainPanelBindMultiAdapter.TYPE_BMT_EMERGENCY) {
                    return 1;
                }
                return 2;
            }
        });
        gridLayoutManager.setSmoothScrollbarEnabled(true);
        gridLayoutManager.setAutoMeasureEnabled(true);
        mBinding.rvPluginWidget.setLayoutManager(gridLayoutManager);
        MainPanelWidgetItemDivider itemDivider = new MainPanelWidgetItemDivider(DensityUtils.dp2px(getContext()
                , PANEL_ITEM_DIVIDER_WIDTH_DP));
        mBinding.rvPluginWidget.addItemDecoration(itemDivider);

        itemTouchHelper = new ItemTouchHelper(new MainRvItemTouchHelper(pluginWidgetAdapter));
        itemTouchHelper.attachToRecyclerView(mBinding.rvPluginWidget);
    }

    private void initMainWidgetListProvider() {
        MainWidgetListProvider.getInstance().setOnDeviceStatusActionClickListener(this);
        MainWidgetListProvider.getInstance().setOnWidgetItemListener(new OnWidgetItemListener() {

            @Override
            public void onItemClick(boolean isEditMode, int sectionType, TuyaItemPlus tuyaItemPlus, @NonNull String modelId, @NonNull String modelType) {
                DDLog.i(TAG, "onItemClick: isEditMode: " + isEditMode);
                if (isEditMode) {
                    DDLog.i(TAG, "编辑模式下，点击Item不处理");
                    return;
                }

                if (MainPanelHelper.SECTION_TYPE_SHORTCUT == sectionType) {
                    if (HomeManager.getInstance().isGuest()) {
                        DDLog.e(TAG, "访客不能点击ShortCutItem");
                        return;
                    }
                    // 修改涂鸦配件或我们的插座
                    if (tuyaItemPlus.isTuya()) {
                        // 涂鸦配件
                        showErrorToast();
                    } else {
                        // 修改我们的插座
                        if (tuyaItemPlus.isAskPlug()) {
                            final Device device = DinHome.getInstance().getDevice(tuyaItemPlus.getId());
                            if (null != device) {
                                showTimeOutLoadinFramgmentWithErrorAlert();
                                device.submit(PanelParamsHelper.getPluginDetail());
                            } else {
                                DDLog.e(TAG, "Empty smart plug");
                                showErrorToast();
                            }
                        } else {
                            ModifyPluginInfoHelper.getInstance().modifyPluginInfo(
                                    getMainActivity(), tuyaItemPlus.getDecodeid(), tuyaItemPlus.getId(),
                                    tuyaItemPlus.getName(), true, true);
                        }
                    }
                } else if (MainPanelHelper.SECTION_TYPE_DOOR_SENSOR == sectionType) {
                    if (!HomeManager.getInstance().isAdmin()) {
                        DDLog.e(TAG, "非管理员不能进入门磁设置页");
                        return;
                    }
                    if (PluginConstants.CATEGORY_10 == tuyaItemPlus.getCategory()) {
                        final Device device = DinHome.getInstance().getDevice(tuyaItemPlus.getId());
                        if (null != device) {
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            device.submit(PanelParamsHelper.getPluginDetail());
                        } else {
                            DDLog.e(TAG, "Empty smart plug");
                            showErrorToast();
                        }
                    } else {
                        ModifyPluginInfoHelper.getInstance().modifyPluginInfo(
                                getMainActivity(), tuyaItemPlus.getDecodeid(), tuyaItemPlus.getId(),
                                tuyaItemPlus.getName(), true);
                    }
                } else {
                    DDLog.e(TAG, "暂未处理该类型的item点击事件");
                }
            }

            @Override
            public void onBmtOrBatteryItemClick(boolean isEditMode, int sectionType, int bmtOrBattery, Device device, String name) {
                if (isEditMode || null == device) {
                    DDLog.i(TAG, "编辑模式下，点击Item不处理");
                    return;
                }

                if (MainPanelHelper.SECTION_TYPE_BMT == sectionType) {
                    if (bmtOrBattery == PowerStationRoot2Fragment.SHOW_EV) {
                        getDelegateActivity().addCommonFragment(PSEVChargeV3Fragment.newInstance(device.getId(), device.getSubCategory()));
                    } else if (bmtOrBattery == PowerStationRoot2Fragment.SHOW_EMERGENCY) {
                        if (!HomeManager.getInstance().isAdmin()) {
                            DDLog.e(TAG, "非管理员不能进入门磁设置页");
                            return;
                        }
                        getDelegateActivity().addCommonFragment(ReserveModeFragment.newInstance(device.getId(), device.getSubCategory()));
                    } else if (bmtOrBattery == PowerStationRoot2Fragment.SHOW_STATS) {
                        getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(device.getId(), device.getSubCategory()));
                    } else {
                        getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstance(name, device.getId(), device.getSubCategory(), bmtOrBattery));
                    }
                }
            }

            @Override
            public void onItemLongClick(boolean isEditMode, RecyclerView.ViewHolder viewHolder, int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType) {
                DDLog.i(TAG, "onItemLongClick: isEditMode: " + isEditMode + "  viewHolder: "
                        + viewHolder.toString() + "  position: " + position);
                if (isEditMode) {
                    // 编辑模式下
                    Vibrator vibrator = (Vibrator) getActivity().getSystemService(Service.VIBRATOR_SERVICE);
                    vibrator.vibrate(100);
                    itemTouchHelper.startDrag(viewHolder);
                } else {
                    // 非编辑模式下，提示弹窗
                    if (modelType.equals(PluginWidgetStyleUtil.MODEL_TYPE_COMMON_FUNCTIONS_DEF)) {
                        toDeleteOrEditWidget(position, widgetBean, Local.s(getResources().getString(R.string.main_widget_edit)));
                    } else {
                        toDeleteOrEditWidget(position, widgetBean, Local.s(getResources().getString(R.string.main_widget_edit)),
                                Local.s(getResources().getString(R.string.main_widget_delete)));
                    }

                }
            }

            @Override
            public void onDeleteIconClick(int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType) {
                DDLog.i(TAG, "onDeleteIconClick: position: " + position);
                showDeletedWidgetTipDialog(position, widgetBean);
            }

            private AlertDialog unavailableTipDialog;

            @Override
            public void onUnavailableStateViewClick(int positio, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType) {
                DDLog.d(TAG, "onUnavailableStateViewClick. position: " + positio + "  pluginId: " + modelId);
                if (MainPanelHelper.getInstance().isPanelEditMode()) {
                    // 编辑模式下不处理
                    return;
                }
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (unavailableTipDialog != null && unavailableTipDialog.isShowing()) {
                            unavailableTipDialog.dismiss();
                        }
                        unavailableTipDialog = AlertDialog.createBuilder(getActivity())
                                .setOk(Local.s(getResources().getString(R.string.got_it)))
                                .setContent(Local.s(getResources().getString(R.string.use_the_fol)))
                                .setOKListener(() -> {
                                    // DinCore删除对应的device cache
                                    String deviceId = modelId.contains(MainWidgetListProvider.UNDERLINE) ? modelId.split(MainWidgetListProvider.UNDERLINE)[0] : modelId;
                                    DDLog.d(TAG, "onUnavailableStateViewClick. deviceId: " + deviceId);
                                    DinHome.getInstance().removeDeviceCacheByIdAndSub(deviceId, widgetBean.getSubCategory());
                                    deleteWidget(modelId);
                                    if (!TextUtils.isEmpty(modelType)) {
                                        switch (modelType) {
                                            case PluginWidgetStyleUtil.PANEL:
                                            case PluginWidgetStyleUtil.PANEL_SMALL:
                                                if (null != mPanelDevice) {
                                                    mPanelDevice.unregisterDeviceCallBack(MainMiddleNewFragment.this);
                                                    mPanelDevice.unregisterDeviceStatusListener(MainMiddleNewFragment.this);
                                                    mPanelDevice = null;
                                                }
                                                break;
                                            case PluginWidgetStyleUtil.SHORTCUT:
                                                PluginManager.getInstance().removeDeletedDevice(modelId);
                                                break;
                                            case PluginWidgetStyleUtil.PLUGIN:
                                                PluginManager.getInstance().removeDeletedDevice(modelId);
                                                break;
                                            case PluginWidgetStyleUtil.BMT:
                                            case PluginWidgetStyleUtil.BATTERY:
                                            case PluginWidgetStyleUtil.BMT_EV:
                                            case PluginWidgetStyleUtil.BMT_EMERGENCY:
                                            case PluginWidgetStyleUtil.BMT_TODAY_USAGE:
                                            case PluginWidgetStyleUtil.POWER_STORE_CURRENT:
                                            case PluginWidgetStyleUtil.POWER_STORE_BATTERY:
                                            case PluginWidgetStyleUtil.POWER_STORE_EMERGENCY:
                                            case PluginWidgetStyleUtil.POWER_STORE_TODAY_USAGE:
                                            case PluginWidgetStyleUtil.POWER_PULSE_CURRENT:
                                                BmtManager.getInstance().removeDeletedDevice(deviceId, widgetBean.getSubCategory());
                                                break;
                                            case PluginWidgetStyleUtil.IPC:
                                            case PluginWidgetStyleUtil.IPC_SMALL:
                                                IPCManager.getInstance().removeDeletedDevice(modelId);
                                                break;
                                            case PluginWidgetStyleUtil.IPC_MORE:
                                                DsCamUtils.deleteCurrentHomeLocalMultiScreenCardDsCamIds(modelId);
                                                break;
                                        }
                                    }

                                    EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
                                })
                                .preBuilder();
                        unavailableTipDialog.show();
                    }
                });
            }
        });
    }

    @Override
    public void initData() {
        super.initData();
    }


    /**
     * 初始化Function的数据
     */
    private void initFunctionData() {
        DDLog.i(TAG, "initFunctionData");
        MainWidgetListProvider.getInstance().updateFunctionItem();
    }

    /**
     * 移除除所有
     */
    private void removeAllWidget() {
        DDLog.d(TAG, "removeAllSection");

        mSmartPluginDeviceList.clear();
        mSmartPlugPluginList.clear();

        mDoorSensorDeviceList.clear();
        mDoorSensorPluginList.clear();

        mDoorSensorSectionList.clear();
        mShortcutSectionList.clear();
        mIpcSectionList.clear();
        mBmtSectionList.clear();
        mPowerStoreSectionList.clear();
        mPowerPulseSectionList.clear();

        pluginWidgetList.clear();
        if (pluginWidgetAdapter != null) {
            pluginWidgetAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 添加首页Section
     */
    private void addAllSection() {
        DDLog.d(TAG, "addAllSection.");
        int[] sectionTypes = MainPanelHelper.getInstance().getSectionTypesWithSort();

        for (int sectionType : sectionTypes) {
            if (MainPanelHelper.SECTION_TYPE_BMT == sectionType) {
                addBmtSection();
            } else if (MainPanelHelper.SECTION_TYPE_POWER_STORE == sectionType) {
                addPowerStoreSection();
            } else if (MainPanelHelper.SECTION_TYPE_POWER_PULSE == sectionType) {
                addPowerPulseSection();
            } else if (MainPanelHelper.SECTION_TYPE_IPC == sectionType) {
                addIpcSection();
            } else if (MainPanelHelper.SECTION_TYPE_PANEL_DEVICE == sectionType) {
                MainWidgetListProvider.getInstance().updatePanelDeviceItem(mPanelDevice);
            } else if (MainPanelHelper.SECTION_TYPE_DOOR_SENSOR == sectionType) {
                addDoorSensorSection();
            } else if (MainPanelHelper.SECTION_TYPE_SHORTCUT == sectionType) {
                addShortcutSection();
            } else if (MainPanelHelper.SECTION_TYPE_FUNCTION == sectionType) {
                addTypeFunction();
            } else {
                DDLog.e(TAG, "Unhandle this section type: " + sectionType);
            }
        }

        refreshWidgetListWithData(false);
    }

    private void addTypeFunction() {
        MainWidgetListProvider.getInstance().updateFunctionItem();
    }

    /**
     * 添加shortcut分组数据
     * 自家插座>涂鸦插座>涂鸦灯泡
     */
    private void addShortcutSection() {
        DDLog.d(TAG, "addShortcutSection.");
        mShortcutSectionList.clear();
        mShortcutSectionList.addAll(mSmartPlugPluginList);

        DDLog.d(TAG, "addShortcutSection: mShortcutSectionList: " + mShortcutSectionList.size());
        MainWidgetListProvider.getInstance().updateShortcutList(mShortcutSectionList);
        // 此处不需刷新界面，所有数据更新完后统一刷新
    }

    /**
     * 添加DoorSensor分组数据
     */
    private void addDoorSensorSection() {
        DDLog.d(TAG, "addDoorSensorSection.");

        mDoorSensorSectionList.clear();
        mDoorSensorSectionList.addAll(mDoorSensorPluginList);
        MainWidgetListProvider.getInstance().updateDoorSensorList(mDoorSensorSectionList);
        // 此处不需刷新界面，所有数据更新完后统一刷新
    }

    /**
     * 添加Camera分组数据
     */
    private void addIpcSection() {
        DDLog.d(TAG, "addIpcSection.");

        mIpcSectionList.clear();
        mIpcSectionList.addAll(IPCManager.getInstance().getAllDevice());
        MainWidgetListProvider.getInstance().updateIPCList(mIpcSectionList);

        // 此处不需刷新界面，所有数据更新完后统一刷新

    }

    /**
     * 添加BMT分组数据
     */
    private void addBmtSection() {
        DDLog.d(TAG, "addBmtSection.");
        mBmtSectionList.clear();
        mBmtSectionList.addAll(BmtManager.getInstance().getPowerCoreDevice());
        DDLog.d(TAG, "mBmtSectionList===" + mBmtSectionList.size());
        MainWidgetListProvider.getInstance().updateBmtList(mBmtSectionList);
        showHomeGuide();
    }

    /**
     * 首页引导
     */
    private void showHomeGuide() {
        // 这个功能是emaldo有, HP没有的
        if ((CollectionUtil.isListNotEmpty(mBmtSectionList) || CollectionUtil.isListNotEmpty(mPowerStoreSectionList) || CollectionUtil.isListNotEmpty(mPowerPulseSectionList)) &&
                !getDelegateActivity().isHasFragment()) {
            boolean isHomeGuideShowed = DBUtil.Bool(DBKey.KEY_HOME_GUIDE_SHOWED);
            if (!isHomeGuideShowed) {
                getDelegateActivity().addCommonFragment(PSGuideFragment.newInstance(PSGuideFragment.HOME_GUIDE));
                DBUtil.Put(DBKey.KEY_HOME_GUIDE_SHOWED, true);
            }
        }
    }

    /* *********************** 配件列表状态改变修改页面-START ************************** */

    private ActionSheet deleteOrEditWidgetDialog;

    /**
     * 非编辑模式下，提示删除或进入编辑模式
     */
    private void toDeleteOrEditWidget(int position, @NonNull final MainWidgetBean widgetBean, String... titles) {
        deleteOrEditWidgetDialog = ActionSheet.createBuilder(getDelegateActivity().getApplicationContext()
                        , getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.main_widget_cancel)))
                .setOtherButtonTitles(titles)
                .setLastButtonTextColor(titles.length > 1 ? getResources().getColor(R.color.dialog_warning) : Color.BLACK)
                .setCancelableOnTouchOutside(false)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            // 进入编辑模式
                            enterPanelEditMode();
                        } else {
                            showDeletedWidgetTipDialog(position, widgetBean);
                        }

                    }
                }).show();
    }

    private AlertDialog deletedWidgetTipDialog;

    /**
     * 删除widget提示弹窗
     */
    private void showDeletedWidgetTipDialog(final int position, @NonNull final MainWidgetBean widgetBean) {
        if (deletedWidgetTipDialog != null && deletedWidgetTipDialog.isShowing()) {
            deletedWidgetTipDialog.dismiss();
        }
        BaseMainItemModel<?> localModel = null;
        if (0 <= position && position < pluginWidgetList.size()) {
            localModel = pluginWidgetList.get(position);
        }

        if (null == localModel) {
            showErrorToast();
            return;
        }

        final BaseMainItemModel<?> selectedModel = localModel;
        deletedWidgetTipDialog = AlertDialog.createBuilder(getActivity())
                .setOk(Local.s(getResources().getString(R.string.Yes)))
                .setOKListener(() -> {
                    // 删除widget item
                    BaseMainItemModel<?> currentModel = null;
                    if (position < pluginWidgetList.size()) {
                        currentModel = pluginWidgetList.get(position);
                    }

                    if (currentModel == null || !currentModel.getModelId().equals(selectedModel.getModelId())) {
                        showErrorToast();
                        return;
                    }

                    MainWidgetListProvider.getInstance().removeCacheLayoutItem(position, widgetBean, true);
                    pluginWidgetList.remove(position);
                    pluginWidgetAdapter.notifyItemRemoved(position);
                })
                .setCancel(Local.s(getResources().getString(R.string.No)))
                .setContent(Local.s(getResources().getString(R.string.main_widget_edit_delete_tip)))
                .preBuilder();
        deletedWidgetTipDialog.show();
    }

    private void refreshWidgetListWithData(boolean isCacheData) {
        DDLog.i(TAG, "refreshWidgetListWithData：isCacheData: " + isCacheData);
        DDLog.i(TAG, "refreshWidgetListWithData：isPanelEditMode: " + MainPanelHelper.getInstance().isPanelEditMode());
        pluginWidgetList.clear();
        DeviceCallBackManager.getInstance().clearAll();
        pluginWidgetList.addAll(MainWidgetListProvider.getInstance().getBindModelList(this));

        // 需要添加AddMore的模块
        if (!isCacheData) {
            pluginWidgetList.add(MainWidgetListProvider.getInstance().getAddMoreItem(this
                    , new MainAddMorePluginModel.OnItemClickListener() {
                        @Override
                        public void onAddMoreClickListener() {
                            DDLog.d(TAG, "onAddMoreClickListener: ");
                            if (!AppConfig.Functions.SUPPORT_ADD_MORE ||
                                    AddMoreHelper.FUNCTION_MODE_OEM == AddMoreHelper.getInstance().getFunctionMode()) {
                                ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
                            } else {
                                getMainActivity().addCommonFragment(AddMoreFragment.newInstance());
                            }
                        }

                        @Override
                        public void onEditClickListener() {
                            DDLog.d(TAG, "onEditClickListener: ");
                            enterPanelEditMode();
                        }
                    }));
        }

        runOnMainThread(() -> {
            if (pluginWidgetAdapter != null) {
                pluginWidgetAdapter.notifyDataSetChanged();
            }
        });

        if (!isCacheData) {
            // 加载缓存的widget数据时，无需检查
            EventBus.getDefault().post(new CheckDeviceEvent());
        }
        if (!DBUtil.Bool(DBKey.KEY_DENY_CHECK_AI_LOCATION_SETUP_PERMISSION) &&
                CollectionUtil.isListNotEmpty(BmtManager.getInstance().getNotDeletedBmtDeviceList()) && DBUtil.Bool(DBKey.KEY_HOME_GUIDE_SHOWED)) {
            getHomeLocation();
        }
    }

    /**
     * 添加BMT PowerStore分组数据
     */
    private void addPowerStoreSection() {
        DDLog.d(TAG, "addPowerStoreSection.");
        mPowerStoreSectionList.clear();
        mPowerStoreSectionList.addAll(BmtManager.getInstance().getPowerStoreDevice());
        DDLog.d(TAG, "addPowerStoreSection===" + mPowerStoreSectionList.size());
        MainWidgetListProvider.getInstance().updatePowerStoreList(mPowerStoreSectionList);
        showHomeGuide();
    }

    private void addPowerPulseSection() {
        DDLog.d(TAG, "addPowerPulseSection.");
        mPowerPulseSectionList.clear();
        mPowerPulseSectionList.addAll(BmtManager.getInstance().getPowerPulseDevice());
        DDLog.d(TAG, "addPowerStoreSection===" + mPowerPulseSectionList.size());
        MainWidgetListProvider.getInstance().updatePowerPulseList(mPowerPulseSectionList);
        showHomeGuide();
    }

    /**
     * 切换家庭前，先查找该家庭账号是否缓存过配件列表信息；存在则先显示配件列表在loading，否则页面显示loading；
     *
     * @param homeId
     */
    private void changeMainContentWithCacheWidgetList(String homeId) {
        if (TextUtils.isEmpty(homeId)) {
            DDLog.e(TAG, "changeMainContentWithCacheWidgetList. homeId is null!");
            return;
        }

        DDLog.i(TAG, "changeMainContentWithCacheWidgetList, homeId: " + homeId);
        MainWidgetListProvider.getInstance().refreshCacheLayoutWithHomeId(homeId);
        //  更新新主页黑名单
        MainPanelHelper.getInstance().initBlackListFromCache(homeId);

        if (MainWidgetListProvider.getInstance().isCacheLayoutEmptyWithHome(homeId)) {
            changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_HANDOVER_FAMILY_NO_CACHE);
        } else {
            if (MainWidgetListProvider.getInstance().curCacheLayoutListSize() == 0) {
                changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_EMPTY);
            } else {
                changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_PANEL);
//                initFunctionData();
                refreshWidgetListWithData(true);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ShowHomeGuideEvent event) {
        showHomeGuide();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(AddWidgetEvent event) {
        final MainWidgetBean bean = event.getBean();
        final boolean fromUser = event.isFromUser();
        final int bindModelType = bean.getBindModelType();
        final String pluginId = bean.getPluginId();
        DDLog.d(TAG, "AddWidgetEvent: " + bean);
        MainWidgetListProvider.getInstance().addCacheLayoutItem(event.getBean());
        switch (bindModelType) {
            case MainPanelHelper.SECTION_TYPE_PANEL_DEVICE:
                MainWidgetListProvider.getInstance().updatePanelDeviceItem(mPanelDevice);
                refreshWidgetListWithData(false);
                break;
            case MainPanelHelper.SECTION_TYPE_DOOR_SENSOR:
                updateHomePluginCache();
                updateHomePluginListWithPluginCache(true);
                break;
            case MainPanelHelper.SECTION_TYPE_SHORTCUT:
                if (PluginManager.getInstance().isSmartPlugin(event.getBean().getPluginId())) {
                    // 只能插座
                    updateHomePluginCache();
                    updateHomePluginListWithPluginCache(true);
                    break;
                }
                break;
            default:
                refreshWidgetListWithData(false);
                break;
        }

        if (pluginWidgetAdapter.getData().size() > 1) {
            mBinding.rvPluginWidget.scrollToPosition(pluginWidgetAdapter.getData().size() - 1);
        }

        if (fromUser && MainPanelHelper.SECTION_TYPE_BMT == bindModelType && !TextUtils.isEmpty(pluginId)) {
            BmtManager.getInstance().requestCurrentAndBatteryInfo(pluginId, bean.getSubCategory());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(CheckDeviceEvent event) {
        DDLog.d(TAG, "CheckDeviceEvent.");
        mTaskHandler.removeCallbacks(mCheckDeviceNullTask);
        mTaskHandler.postDelayed(mCheckDeviceNullTask, 60 * 1000);
    }

    /* *********************** 配件列表状态改变修改页面-END ************************** */


    /* ************************ 标题栏点击回调-START ********************************* */
    @Override
    public void onOpenPopupWindow() {
        DDLog.i(TAG, "onOpenPopupWindow.");
        if (mLoadingDevices) {
            return;
        }
        mBinding.mainFragmentBlackView.setVisibility(View.VISIBLE);
        mainTittleLayout.showChangeDeviceDialog();
    }

    @Override
    public void onHidePopupWindow() {
        DDLog.i(TAG, "onHidePopupWindow");
        mBinding.mainFragmentBlackView.setVisibility(View.GONE);
    }

    @Override
    public void onSettingIconClick() {
        DDLog.i(TAG, "onSettingIconClick");
        DinHome.getInstance().getUploadImageKey(getContext().getCacheDir().getAbsolutePath()+ File.separator+"test.jpg", "test", new IDefaultCallBack2<String>() {

            @Override
            public void onSuccess(String key) {
                DDLog.d(TAG, "getUploadImageKey: " + key);
            }

            @Override
            public void onError(int i, String s) {
                DDLog.d(TAG, "getUploadImageKey. onError: " + s);
            }
        });
//        if (!MainPanelHelper.getInstance().isSettingEnable()) {
//            DDLog.e(TAG, "当前情况下不能进入设置页");
//            return;
//        }
//
//        ((MainActivity) getActivity()).smoothToSetting();
    }

    @Override
    public void onAddIconClick() {
        DDLog.i(TAG, "onAddIconClick");
        // 点击添加widget按钮
        getMainActivity().addCommonFragment(SelectPluginAddWidgetListFragment.newInstance());
    }

    @Override
    public void onEditDoneClick() {
        DDLog.i(TAG, "onEditDoneClick");
        // 点击了编辑完成按钮
        savePanelEditMode();
    }

    @Override
    public void onFaimlyClick(int index) {
        DDLog.w(TAG, "onFamilyClick. index: " + index + " HomeName: "
                + HomeManager.getInstance().getHomeList().get(index).getHomeName());
        isNeedHomeLocation = true;
        HomeManager.getInstance().changeFamily(index);
    }

    @Override
    public void onManageFamilyClick() {
        DDLog.i(TAG, "onManageFamilyClick");
        getDelegateActivity().addCommonFragment(MyFamilyFragment.newInstance());
    }

    @Override
    public void onJoinFamilyClick() {
        DDLog.i(TAG, "onJoinFamilyClick");
        ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_FAMILY);
    }

    /**
     * 切换家庭账号时，未有缓存家庭设备列表数据的情况下，控制显示loading
     */
    private void controlHandoverFamilyAnim(boolean isStart) {
        DDLog.i(TAG, "onManageFamilyClick. isStart：" + isStart);
        if (isStart) {
            String uri = "json/animation_home_progress.json";
            mBinding.layoutHandoverFamilyLoading.handoverAnimView.setRepeatCount(ValueAnimator.INFINITE);
            mBinding.layoutHandoverFamilyLoading.handoverAnimView.setAnimation(uri);
            mBinding.layoutHandoverFamilyLoading.handoverAnimView.playAnimation();
            refreshHandoverProgressBar(20);
        } else {
            refreshHandoverProgressBar(100);
            mBinding.layoutHandoverFamilyLoading.handoverAnimView.clearAnimation();
        }
    }

    /**
     * 更新进度条
     *
     * @param progress
     */
    private void refreshHandoverProgressBar(int progress) {
        DDLog.d(TAG, "refreshHandoverProgressBar: " + progress);
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                mBinding.layoutHandoverFamilyLoading.handoverProgressBar.setProgress(progress);
            }
        });
    }

    /* ************************ 标题栏点击回调-END ********************************* */

    /* ************************ 设备状态点击回调-START ********************************* */

    @Override
    public void onRetryClick() {
        DDLog.i(TAG, "onRetryClick");
        HomeManager.getInstance().refreshFamilyInfo();
    }

    @Override
    public void onNotConnectModeClick() {
        DDLog.i(TAG, "onNotConnectModeClick");
        // 设备状态-离线模式按钮点击事件处理
        if (null == mPanelDevice) {
            DDLog.e(TAG, "Empty panel device");
            showErrorToast();
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        selfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.getSimCardInfo());
    }

    /**
     * 显示确认主机手机号的对话框
     * <p>
     * 如果从网络获取的手机号不为空，对话框输入框默认内容为从网络获取的手机号；
     * 如果从网络获取的手机号为空，从本地存储中读取手机号；
     * 如果本地的手机号也为空，输入框默认不填充任何内容。
     * </P>
     *
     * @param webPhone 服务器返回的主机手机号
     */
    private void showConfirmPhoneDialog(String webPhone) {
        DDLog.i(TAG, "showConfirmPhoneDialog, webPhone: " + webPhone);
        String defaultPhone = webPhone;
        String devicePhoneDbKey = DBKey.RESTRICT_DEVICE_PHONE
                + CommonDataUtil.getInstance().getCurrentDeviceId();
        if (TextUtils.isEmpty(webPhone)) {
            // 从本地读取上一次保存的手机号
            defaultPhone = DBUtil.Str(devicePhoneDbKey);
            DDLog.d(TAG, "get default phone from DB, phone: " + defaultPhone);
        }

        RestrictModelInputPhoneDialog.createBuilder(getContext())
                .setCurrentPhone(defaultPhone)
                .setOKListener(new RestrictModelInputPhoneDialog.AlertClickCallback() {
                    @Override
                    public void onOkClick(RestrictModelInputPhoneDialog dialog, String inputPhone) {
                        DDLog.i(TAG, "input phone: " + inputPhone);

                        if (TextUtils.isEmpty(inputPhone.trim())) {
                            DDLog.e(TAG, "the input phone is null");
                            return;
                        }

                        //  将用户确认的手机号保存在本地
                        DBUtil.Put(devicePhoneDbKey, inputPhone);
                        DDLog.d(TAG, "save phone: " + inputPhone);
                        changeDeviceStateOfflineMode();

                    }

                    @Override
                    public void onCancelClick(RestrictModelInputPhoneDialog dialog) {
                    }
                })
                .preBuilder()
                .show();
    }

    @Override
    public void onDeviceStateViewRootClick() {
        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            DDLog.e(TAG, "当前Item不能点击");
            return;
        }

        if (LocalKey.ADMIN == HomeManager.getInstance().getCurrentHome().getLevel()) {
            getDelegateActivity().addCommonFragment(DeviceStatusDetailFragment.newInstance());
        }
    }

    private AlertDialog offlineModeTipDialog;

    @Override
    public void onOfflineModeTipClick() {
        DDLog.d(TAG, "onOfflineModeTipClick.");
        if (!MainPanelHelper.getInstance().isPanelEditMode()) {
            runOnMainThread(() -> {
                if (offlineModeTipDialog != null && offlineModeTipDialog.isShowing()) {
                    offlineModeTipDialog.dismiss();
                }
                offlineModeTipDialog = AlertDialog.createBuilder(getActivity())
                        .setOk(Local.s(getResources().getString(R.string.got_it)))
                        .setContent(Local.s(getResources().getString(R.string.device_status_offline_mode_hint)))
                        .preBuilder();
                offlineModeTipDialog.show();
            });
        }
    }
    /* ************************ 设备状态点击回调-END ********************************* */


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        DDLog.d(TAG, "LanguageUpdataEvent");
        updataUI();
        updataWidgetLanguage();
    }

    @Subscribe
    @Deprecated
    public void onEvent(UserDeviceListChangeEvent event) {
        DDLog.i("login", "onEvent" + DBUtil.Num(DBKey.CURRENT_DEVICE));
        createMainHomeList();
        initDeviceStatus();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(FamilyListChangeEvent event) {
        DDLog.d(TAG, "onEvent, FamilyListChangeEvent");
        createMainHomeList();
        if (0 >= HomeManager.getInstance().getHomeList().size()) {
            changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_EMPTY);
            return;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(FamilySwitchEvent event) {
        DDLog.i(TAG, "onEvent, FamilySwitchEvent, success: " + event.isSuccess());
        long afterSwitchStart = System.currentTimeMillis();
        DDLog.i(TAG, "afterSwitchStart===" + afterSwitchStart + "ms");
        createMainHomeList();
        if (event.isSuccess()) {
            // 切换成功
            mTaskHandler.removeCallbacks(mCheckDeviceNullTask);

            if (null != DinHome.getInstance().getCurrentHomeInfo()
                    && null != DinHome.getInstance().getCurrentHomeInfo().getDevice()
                    && !TextUtils.isEmpty(DinHome.getInstance().getCurrentHomeInfo().getDevice().getDeviceid())) {
                CommonDataUtil.getInstance().setCurrentPanelID(
                        DinHome.getInstance().getCurrentHomeInfo().getDevice().getDeviceid());
            } else {
                CommonDataUtil.getInstance().setCurrentPanelID("");
            }

            if (event.isFamilyChanged()) {
                DDLog.w(TAG, "onEvent, FamilySwitchEvent, family changed");
                // 每次切换家庭置为未加载bmt国家列表
                BmtManager.getInstance().setNotLoadCountries(true);
                // 每次切换家庭清除bmt国家列表
                MainPanelHelper.getInstance().getBmtRegionCountryList().clear();
                removeAllWidget();
                IPCManager.getInstance().releaseAllDevice();
                BmtManager.getInstance().releaseAllDevice();
                PluginManager.getInstance().releaseAllDevice();
                changeMainContentWithCacheWidgetList(HomeManager.getInstance().getCurrentHome().getHomeID());
            }

            DBUtil.removeWidgetInfo();
            getMainActivity().showSOSLayout(false);
            EventListHelper.getInstance().initFilter();
            long afterSwitchEnd = System.currentTimeMillis();
            DDLog.i(TAG, "afterSwitchEnd===" + afterSwitchEnd + "ms");
            DDLog.i(TAG, "统计时间afterSwitchEnd==>Request time: " + (afterSwitchEnd - afterSwitchStart) + "ms");
            EventBus.getDefault().post(new CheckFamilyBalanceContractInfoEvent());
            EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
        } else {
            // 切换失败
            closeLoadingFragment();
            showErrorToast();
        }
        mBinding.rvPluginWidget.scrollToPosition(0);
        if (mainTittleLayout != null) {
            mainTittleLayout.setAppBarExpand(true);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(FamilyInfoUpdateEvent event) {
        DDLog.i(TAG, "onEvent, FamilyInfoUpdateEvent, success: " + event.isSuccess());
        if (event.isSuccess()) {
            createMainHomeList();
            // 刷新成功
            if (null != DinHome.getInstance().getCurrentHomeInfo()
                    && null != DinHome.getInstance().getCurrentHomeInfo().getDevice()
                    && !TextUtils.isEmpty(DinHome.getInstance().getCurrentHomeInfo().getDevice().getDeviceid())) {
                CommonDataUtil.getInstance().setCurrentPanelID(
                        DinHome.getInstance().getCurrentHomeInfo().getDevice().getDeviceid());
            } else {
                CommonDataUtil.getInstance().setCurrentPanelID("");
            }

            DBUtil.removeWidgetInfo();
            EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
        } else {
            // 刷新失败
            closeLoadingFragment();
            showErrorToast();
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEvent(CheckFamilyBalanceContractInfoEvent event) {
        if (!DBUtil.Bool(DBKey.KEY_HOME_GUIDE_SHOWED)) return;
        getHomeLocation();
        String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        if (DBUtil.Exists(DBKey.KEY_PARALLEL_INSTALLATION_UPDATE + "_" + homeId)
                && DBUtil.Bool(DBKey.KEY_PARALLEL_INSTALLATION_UPDATE + "_" + homeId)) {
            return;
        }
        DinHome.getInstance().getFamilyBalanceContractInfo(homeId,
                new IDefaultCallBack2<>() {
                    @Override
                    public void onSuccess(FamilyBalanceContractInfoResponse.ResultBean resultBean) {
                        if (null != resultBean) {
                            if (resultBean.getSigned_devices().size() > 0) {
                                showParallelInstallationUpdateDialog();
                            } else {
                                DBUtil.Put(DBKey.KEY_PARALLEL_INSTALLATION_UPDATE + "_" + homeId, true);
                            }
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        DDLog.e(TAG, "getFamilyBalanceContractInfo. onError : " + i + " " + s);
                    }
                });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AppStateEvent event) {
        DDLog.i(TAG,"AppStateEvent: " + event.isBackground());
        if (event.isBackground() && mParallelInstallationUpdateDialog != null) {
            front = true;
        }
    }

    private boolean front = false;
    private EVEventDialog mParallelInstallationUpdateDialog;

    private void showParallelInstallationUpdateDialog() {
        if (front) {
            front = false;
            return;
        }
        mParallelInstallationUpdateDialog = new EVEventDialog.Builder(getContext())
                .setCoverRes(R.drawable.img_grid_rewards_add_family_devices_dialog)
                .setTitle(Local.s(getContext().getString(R.string.prime_service_parallel_installation_update)))
                .setSubtitle(Local.s(getContext().getString(R.string.prime_service_parallel_installation_update_content)))
                .setCenter(true)
                .setBtnText(Local.s(getContext().getString(R.string.Confirm)))
                .setSubBtnText(Local.s(getContext().getString(R.string.terminate)))
                .setCheckListener(new EVEventDialog.OnCheckListener() {
                    @Override
                    public void onCheck(EVEventDialog dialog) {
                        DinHome.getInstance().updateBalanceContractData(HomeManager.getInstance().getCurrentHome().getHomeID(), new IDefaultCallBack() {

                            @Override
                            public void onSuccess() {
                                dialog.dismiss();
                                showSuccess();
//                                getDelegateActivity().addCommonFragment(PSAuthorizationRecordsFragment.newInstance());
                            }

                            @Override
                            public void onError(int i, String s) {
                                getMainActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.failed_try_again));
                            }
                        });
                    }

                    @Override
                    public void onSubBtnClick(EVEventDialog dialog) {
                        dialog.dismiss();
                        getDelegateActivity().addCommonFragment(PSTerminateFragment.newInstanceFromContractDetail(HomeManager.getInstance().getCurrentHome().getHomeName()
                                , HomeManager.getInstance().getCurrentHome().getHomeID()));
                    }
                })
                .build();
        mParallelInstallationUpdateDialog.show();
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEvent(NeedGetAllDeviceEvent event) {
        long deviceStart = System.currentTimeMillis();
        DDLog.i(TAG, "deviceStart===" + deviceStart + "ms");

        DDLog.i(TAG, "onEvent: NeedGetAllDeviceEvent");
        DBUtil.Put(DBKey.WIDGET_CURRENT_FAMILY_NAME, HomeManager.getInstance().getCurrentHome().getHomeName());
        DBUtil.Put(DBKey.WIDGET_FAMILY_LEVEL, HomeManager.getInstance().getCurrentHome().getLevel());
        CloudStorageServiceHelper.getInstance().fetchProductSchedules("", false);
        TrafficPackageServiceHelper.getInstance().fetchProductSchedules("", false);
        mLoadingDevices = true;
        long deviceEnd = System.currentTimeMillis();
        DDLog.i(TAG, "deviceEnd===" + deviceEnd + "ms");
        DDLog.i(TAG, "统计时间getDevice==>Request time: " + (deviceEnd - deviceStart) + "ms");
        DeviceRequestManager.get().addTask(event, new DeviceRequestListener() {
            @Override
            public void onLoad(@NonNull String type, @Nullable List<Device> devices, int total, int current) {
                DDLog.i(TAG, "DeviceRequestManager onLoad: " + type);
                switch (type) {
                    case DinConst.TYPE_PANEL:
                        onPanelLoaded(devices);
                        refreshHandoverProgressBar(30);
                        break;
                    case DinConst.TYPE_DSCAM:
                        refreshHandoverProgressBar(50);
                        break;
                    case DinConst.TYPE_HEARTLAI:
                        refreshHandoverProgressBar(70);
                        break;
                    case DinConst.TYPE_BMT_HP5000:
                        refreshHandoverProgressBar(80);
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onSuccess(@Nullable Boolean result) {
                DDLog.i(TAG, "DeviceRequestManager onSuccess");
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        initBannerData();
                        refreshMainContentTypeWithData();
                        checkNeedHandlePushData();
                        if (event.isRequestIpc()) {
//                添加自研IPC的时候，添加完成了，IPCManager 会发送NeedGetAllDeviceEvent，然后就会来到这里
//                导致了loading提前关闭了，但是我还在添加的过程中，就会出现问题
//                所以，当接收到NeedGetAllDeviceEvent事件，并且是ipcmanager发过来的话，我就不会关闭loading
//                等对应的添加流程去关闭loading
                            closeLoadingFragment();
                        }
                        checkUpdatePluginStatus();
                        refreshHandoverProgressBar(100);
                    }
                });
            }

            @Override
            public void onException(@NonNull Throwable error) {
                error.printStackTrace();
            }
        });

    }

    private void onPanelLoaded(List<Device> deviceList) {
        try {
            mSmartPlugPluginList.clear();
            mDoorSensorPluginList.clear();
            // 如果之前缓存了一个主机信息，下次重新进来的时候，
            // 1. 没有删除主机，返回该主机Device并且数据已经是加载完毕了的。
            // 2. 其他用户删除了旧主机，没有添加新主机，仅返回缓存中的Device并且不会删除缓存。
            // 3. 其他用户删除了旧主机并添加了新主机，会返回新旧主机的Device并且删除旧主机的缓存，保存为新主机的缓存。
            PanelDevice lastDeletedPanel = null;
            PanelDevice currentPanel = null;
            if (null != deviceList && 0 < deviceList.size()) {
                for (int i = 0; i < deviceList.size(); i++) {
                    Device device = deviceList.get(i);
                    if (!(device instanceof PanelDevice)) {
                        continue;
                    }

                    // 目前最多只会返回两个主机的Device
                    if (null != lastDeletedPanel && null != currentPanel) {
                        break;
                    }

                    PanelDevice panelDevice = (PanelDevice) device;
                    if (panelDevice.getFlagDeleted()) {
                        DDLog.i(TAG, "获取到已经被删除的主机Device");
                        // 拿到已经被删除的主机
                        lastDeletedPanel = panelDevice;
                        continue;
                    }

                    // 正常的主机
                    currentPanel = panelDevice;
                }

                if (null != mPanelDevice) {
                    mPanelDevice.unregisterDeviceCallBack(this);
                    mPanelDevice.unregisterDeviceStatusListener(this);
                    mPanelDevice = null;
                }

                if (currentPanel == null) {
                    mPanelDevice = lastDeletedPanel;
                    lastDeletedPanel = null;
                } else {
                    mPanelDevice = currentPanel;
                }
                EventBus.getDefault().post(new UpdateFamilyPanelStateEvent());
            } else {
                if (null != mPanelDevice) {
                    mPanelDevice.unregisterDeviceCallBack(this);
                    mPanelDevice.unregisterDeviceStatusListener(this);
                    mPanelDevice = null;
                }
            }


            if (null != mPanelDevice) {
                DDLog.d(TAG, "onEvent: PanelDevice: " + mPanelDevice.getId());
                // 初始化监听
                CommonDataUtil.getInstance().setCurrentPanelID(mPanelDevice.getId());
                mPanelDevice.registerDeviceCallBack(this);
                mPanelDevice.registerDeviceStatusListener(this);

                // 获取配件数量
                mPanelDevice.submit(PanelParamsHelper.getPlugsAndMembers());

                DDLog.i(TAG, TAG + "-->需要获取配件 ");
                PluginManager.getInstance().fetchPlugin(mPanelDevice, DeviceRequestType.ALL_DEVICE
                        , new IDefaultCallBack() {
                            @Override
                            public void onSuccess() {
                                updateHomePluginCache();
                                runOnMainThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        updateHomePluginListWithPluginCache(!mLoadingDevices);
                                    }
                                });
                            }

                            @Override
                            public void onError(int i, String s) {

                            }
                        });

                //widget
                DBUtil.Put(DBKey.WIDGET_CURRENT_DEVICE_TOKEN, DeviceHelper.getString(mPanelDevice,
                        PanelDataKey.Panel.DEVICE_TOKEN, ""));
                DBUtil.Put(DBKey.WIDGET_CURRENT_DEVICE_NAME, DeviceHelper.getString(mPanelDevice,
                        PanelDataKey.Panel.NAME, ""));
                DBUtil.Put(Constants.KEY_SERVICE_STATUS, DeviceHelper.getInt(mPanelDevice,
                        PanelDataKey.Panel.ARM_STATUS, LocalKey.DISARM_STATUS));
                DBUtil.Put(DBKey.WIDGET_CURRENT_DEVICE_STATUS, CommonDataUtil.getInstance().isPanelOnline());
                DBUtil.Put(DBKey.WIDGET_UID, DinSDK.getUserInstance().getUser().getUid());
                DBUtil.SPut(DBKey.WIDGET_USER_TOKEN, DinSDK.getUserInstance().getUser().getToken());
                DBUtil.SPut(DBKey.WIDGET_HOME_ID, HomeManager.getInstance().getCurrentHome().getHomeID());
            } else {
                EventBus.getDefault().post(new UpdateFamilyPanelStateEvent());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEvent(NeedLoadInfoPluginsEvent event) {
        DDLog.d(TAG, "NeedLoadInfoPluginsEvent. panelDevice is not null ? " + (mPanelDevice != null));
        if (mPanelDevice != null) {
            mPanelDevice.submit(PanelParamsHelper.loadPluginInfo(PluginManager.getInstance().getNeedLoadInfoPluginList()));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(NeedRemovePluginsEvent event) {
        DDLog.d(TAG, "NeedRemovePlugins. ");
        for (String id : PluginManager.getInstance().getNeedRemovePluginList()) {
            deleteWidget(id);
        }

        updateHomePluginCache();
        updateHomePluginListWithPluginCache(true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PluginListUpdateEvent event) {
        DDLog.d(TAG, "PluginListUpdateEvent. id: " + event.getDeviceId() + "  getOperationType: " + event.getOperationType());
        if (event.getOperationType() == PluginListUpdateEvent.OPERATION_DELETE) {
            deleteWidget(event.getDeviceId());
        }

        updateHomePluginCache();
        updateHomePluginListWithPluginCache(true);
    }

    private void refreshMainContentTypeWithData() {
        DDLog.d(TAG, "refreshMainContentTypeWithData.");
        TestLog.i(TAG, "refreshMainContentTypeWithData.");
        if (CommonDataUtil.getInstance().isHadPanel()) {
            DDLog.i(TAG, "有主机");
            changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_PANEL);
            boolean online = CommonDataUtil.getInstance().isPanelOnline();
            boolean upgrading = DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.UPGRADING, false);

            // 1、主机离线
            if (!online) {
                // 不管是否有卡，都显示带有离线模式按钮的离线页
                changeDeviceStateErrorHadSim();

                initViewStateByPermission();
                addAllSection();
            } else {
                // 2、主机在线
                if (upgrading) {
                    changeDeviceStatePanelUpgrading();
                } else {
                    changeDeviceStateNormal();
                }
                initViewStateByPermission();
                addAllSection();

                i("adEntry sos:" + DeviceInfoHelper.getInstance().getCurrentDeviceInfo().isSos());
                if (DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.SOS, false)) {
                    mPanelDevice.submit(PanelParamsHelper.getPanelSosInfo());
                } else {
                    getMainActivity().showSOSLayout(false);
                    getMainActivity().removeCommonFragment(SOSFragment.class.getName());
                }

                if (getMainActivity().isOpenDialogReadyToArm) {
                    getMainActivity().isOpenDialogReadyToArm = false;
                    showReadyToArmDialog();
                }

                if (null == EventBus.getDefault().getStickyEvent(CareModeNoActionEvent.class)) {
                    // 不为空的时候，表示点击通知进来的，会在接收CareModeNoActionEvent事件的时候再检查
                    checkCareModeTime();
                }
            }
        } else {
            DDLog.i(TAG, "没有主机");
            if ((null != IPCManager.getInstance().getAllDevice()
                    && 0 < IPCManager.getInstance().getAllDevice().size())
                    || 0 < BmtManager.getInstance().getAllBmtDevice().size()) {
                // 有与主机无关的配件
                changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_NO_PANEL);
                initViewStateByPermission();
                addAllSection();
                changeSettingUserFunctionEnable(true, true, true);
            } else {
                // 没有与主机无关的配件
                changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_EMPTY);
                removeAllWidget();
            }
        }
        mLoadingDevices = false;
        EventBus.getDefault().post(new GetAllDeviceFinishedEvent());

        if (mCheckNotificationPermission) {
            mCheckNotificationPermission = false;
            checkNotificationPermission();
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(FetchHomeListEvent event) {
        DDLog.i(TAG, "OnFetchHomeListEvent ");
        createMainHomeList();

        if (!event.isSuccess()) {
            showErrorToast();
            closeLoadingFragment();
        } else if (event.isSuccess()
                && 0 >= HomeManager.getInstance().getHomeList().size()) {
            closeLoadingFragment();
            changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_EMPTY);
            DDLog.e(TAG, "Have no home now.");
        }
        // 成功且家庭列表有数据，此处不处理，继续获取家庭信息-
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(CloseAllDeviceEvent ev) {
        mainTittleLayout.hideChangeDeviceDialog();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(WebSocketEvent ev) {
        mainTittleLayout.hideChangeDeviceDialog();
    }

    @Subscribe
    public void onEventMainThread(SelfTestEvent event) {
        if (event.getCode().equals(SelfTestEvent.ARM_TASK)) {
            runOnMainThread(() -> DeviceCallBackManager.getInstance().toArm());
        }
    }

    public void onEventMainThread(DeviceResultEvent ev) {
        DDLog.d(TAG, "DeviceResultEvent, result: " + ev.getReslut());

        if (LocalKey.SET_KNOCK_OVER_TO_SOS.equals(ev.getCmdType())) {
            boolean isKnock = DeviceInfoHelper.getInstance().getCurrentDeviceInfo().isKnock_over_to_sos();
            if (ev.getStatus() == 1) {
                String result = ev.getReslut().replace("\"", "");
                if (LocalKey.KNOCK_TO_SOS_SUCCESS.equals(result)) {
                    EventBus.getDefault().post(new KnockToSosChange(true));
                } else {
                    EventBus.getDefault().post(new KnockToSosChange(false));
                }
            } else {
                EventBus.getDefault().post(new KnockToSosChange(isKnock));
            }
        } else if (ev.getStatus() == 1) {
            if (SetHomearmCallBackAction(ev) || SetSostextCallBackAction(ev)
                    || DoorBellCallBackAction(ev)) {
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceCmdEvent ev) {
        if (LocalKey.DISARM_KEY.equals(ev.getType())) {
            if (!TextUtils.isEmpty(ev.getMessageid()) && null != mPanelDevice)
                mPanelDevice.submit(PanelParamsHelper.operationArm(PanelParamsHelper.OPERATE_ARM_DISARM, true));
            else {
                runOnMainThread(() -> DeviceCallBackManager.getInstance().toDisArm());
            }
        } else if (LocalKey.ARM_KEY.equals(ev.getType())) {
            runOnMainThread(() -> DeviceCallBackManager.getInstance().toArm());
        }
    }

    @Subscribe
    public void onSosEvent(SOSevent soSevent) {
        DDLog.d(TAG, "onSosEvent");
        runOnMainThread(() -> DeviceCallBackManager.getInstance().toResetSos());
    }

    @Subscribe
    public void onSosEvent(SOSFragmentShowEvent soSevent) {
        DDLog.d(TAG, "SOSFragmentShowEvent");
        if (ipcMotionDetectionAlertDialog != null && ipcMotionDetectionAlertDialog.isShowing()) {
            ipcMotionDetectionAlertDialog.dismiss();
            ipcMotionDetectionAlertDialog = null;
        }
        if (dscamGuideTipDialog != null && dscamGuideTipDialog.isShowing()) {
            dscamGuideTipDialog.dismiss();
            dscamGuideTipDialog = null;
        }
    }

    @Subscribe
    public void onEventMainThread(UserUidChangeEvent event) {
    }

    private long lastPermissionUpdateTime;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(UserPermissonUpdata ev) {
        DDLog.d(TAG, "UserPermissonUpdata");

        final long current = System.currentTimeMillis();
        if (current - lastPermissionUpdateTime < 1000) {
            return;
        }
        lastPermissionUpdateTime = current;

        onUserPermissionUpdate();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(GetBmtRegionCountriesEvent event) {
        initFunctionData();
        pluginWidgetAdapter.notifyDataSetChanged();
    }

    /* ************************ ipc-START  ********************************* */
    private void updateIpcListWithPluginCache(boolean autoRefresh) {
        DDLog.i(TAG, "updateIpcListWithPluginCache. autoRefresh: " + autoRefresh + " mLoadingDevices: " + mLoadingDevices);
        if (mLoadingDevices) {
            return;
        }
        addIpcSection();
        if (autoRefresh) {
            refreshWidgetListWithData(false);
        }
    }
    /* ************************ ipc-END    ********************************* */


    /* ************************ 配件相关-START  ********************************* */

    private void checkUpdatePluginStatus() {
        if (null != mPluginStatusMap && mPluginStatusMap.size() > 0) {
            try {
                DDLog.i(TAG, "checkUpdatePluginStatus");
                String resultStr = DeviceHelper.getString(mPluginStatusMap, PanelDataKey.CmdResult.RESULT, "");
                String cmd = DeviceHelper.getString(mPluginStatusMap, PanelDataKey.CmdResult.CMD, "");
                updatePluginState(cmd, resultStr);
                mPluginStatusMap = null;
            } catch (Exception e) {
                DDLog.e(TAG, "Error on checkUpdatePluginStatus");
                e.printStackTrace();
            }
        }
    }

    private void updateHomePluginCache() {
        mDoorSensorDeviceList.clear();
        mDoorSensorDeviceList.addAll(PluginManager.getInstance().getDoorSensorList());
        mSmartPluginDeviceList.clear();
        mSmartPluginDeviceList.addAll(PluginManager.getInstance().getSmartPluginList());
    }


    private void updateHomePluginListWithPluginCache(boolean autoRefresh) {
        updateDoorSensorList(mDoorSensorDeviceList);
        updateSmartPluginList(mSmartPluginDeviceList);
        if (autoRefresh && !mLoadingDevices) {
            addAllSection();
        }
    }

    private void updateDoorSensorList(final List<Device> datas) {
        ArrayList<TuyaItemPlus> tempDoorSensorPluginList = new ArrayList<>();
        tempDoorSensorPluginList.addAll(mDoorSensorPluginList);
        mDoorSensorPluginList.clear();
        TuyaItemPlus pluginItem;
        String id, name, decodeId, subcategory, stype;
        for (Device device : datas) {
            id = device.getId();
            name = DeviceHelper.getString(device, PanelDataKey.NAME, "");
            decodeId = DeviceHelper.getString(device, PanelDataKey.DECODE_ID, null);
            subcategory = DeviceHelper.getString(device, PanelDataKey.SUBCATEGORY, null);
            stype = DeviceHelper.getString(device, PanelDataKey.S_TYPE, null);

            pluginItem = new TuyaItemPlus(name, id);
            pluginItem.setCategory(DeviceHelper.getInt(device, PanelDataKey.CATEGORY, 10));
            pluginItem.setSendid(DeviceHelper.getString(device, PanelDataKey.SEND_ID, null));
            pluginItem.setStype(stype);
            pluginItem.setOnline(DeviceHelper.getBoolean(device, PanelDataKey.IS_ONLINE, false));
            pluginItem.setDecodeid(decodeId);
            pluginItem.setSub_category(subcategory);
            pluginItem.setNeedLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_LOADING_STATE, false));
            pluginItem.setNeedOnlineState(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_ONLINE_STATE, true));
            pluginItem.setLoadingStatus(
                    PanelConstant.PluginLoadingState.SUCCESS
                            == DeviceHelper.getInt(device, PanelDataKey.LOADING_STATE, 0)
                            ?
                            TuyaItemPlus.LOADING_STATUS_SUCCESS
                            : TuyaItemPlus.LOADING_STATUS_LOADING);
            pluginItem.setFlagCache(device.getFlagCache());
            pluginItem.setFlagDeleted(device.getFlagDeleted());
            pluginItem.setFlagLoaded(device.getFlagLoaded());
            pluginItem.setFatherId(device.getFatherId());
            pluginItem.setType(TuyaItem.DOOR_SENSOR_NEW);
            pluginItem.setBlock(DeviceHelper.getInt(device, PanelDataKey.BLOCK, 0));
            pluginItem.setHaveApart(pluginItem.isNeedBlock());

            final int index = findIndexInTargetList(tempDoorSensorPluginList, id);
            if (index >= 0 && tempDoorSensorPluginList.get(index).isHaveApart()) {
                pluginItem.setBlock(tempDoorSensorPluginList.get(index).getBlock());
                pluginItem.setHaveApart(tempDoorSensorPluginList.get(index).isHaveApart());
                pluginItem.setApart(tempDoorSensorPluginList.get(index).isApart());
            }

            mDoorSensorPluginList.add(pluginItem);
            device.registerDeviceCallBack(MainMiddleNewFragment.this);
        }

        if (mDoorSensorPluginList.size() > 0) {
            Collections.sort(mDoorSensorPluginList, new TuyaItemPlusComparator());
        }

        tempDoorSensorPluginList.clear();
    }

    private void updateSmartPluginList(final List<Device> datas) {
        ArrayList<TuyaItemPlus> tempSmartPlugPluginList = new ArrayList<>();
        tempSmartPlugPluginList.addAll(mSmartPlugPluginList);

        mSmartPlugPluginList.clear();
        TuyaItemPlus pluginItem;
        String id, name, decodeId, subcategory, stype;
        for (Device device : datas) {
            id = device.getId();
            name = DeviceHelper.getString(device, PanelDataKey.NAME, "");
            decodeId = DeviceHelper.getString(device, PanelDataKey.DECODE_ID, null);
            subcategory = device.getSubCategory();
            if (TextUtils.isEmpty(subcategory)) {
                subcategory = DeviceHelper.getString(device, PanelDataKey.SUBCATEGORY, null);
            }
            stype = DeviceHelper.getString(device, PanelDataKey.S_TYPE, null);

            if (TextUtils.isEmpty(name)) {
                String defPluginTypeName;
                if (id.startsWith("!")) {
                    defPluginTypeName = PluginTypeHelper.getInstance().getASKNameByBSType(stype);
                } else {
                    defPluginTypeName = PluginTypeHelper.getInstance().getSTypeByID(id);
                }
                name = Local.s(defPluginTypeName) + "_" + id;
            }
            pluginItem = new TuyaItemPlus(name, id);
            pluginItem.setCategory(DeviceHelper.getInt(device, PanelDataKey.CATEGORY, 10));
            pluginItem.setBlock(DeviceHelper.getInt(device, PanelDataKey.BLOCK, 0));
            pluginItem.setSendid(DeviceHelper.getString(device, PanelDataKey.SEND_ID, null));
            pluginItem.setStype(stype);
            pluginItem.setDecodeid(decodeId);
            pluginItem.setSub_category(subcategory);
            pluginItem.setNeedLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_LOADING_STATE, false));
            pluginItem.setNeedOnlineState(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_ONLINE_STATE, true));
            pluginItem.setLoadingStatus(
                    PanelConstant.PluginLoadingState.SUCCESS
                            == DeviceHelper.getInt(device, PanelDataKey.LOADING_STATE, 0)
                            ?
                            TuyaItemPlus.LOADING_STATUS_SUCCESS
                            : TuyaItemPlus.LOADING_STATUS_LOADING);
            pluginItem.setFlagCache(device.getFlagCache());
            pluginItem.setFlagDeleted(device.getFlagDeleted());
            pluginItem.setFlagLoaded(device.getFlagLoaded());
            pluginItem.setFatherId(device.getFatherId());
            boolean isOn = PanelConstant.PluginSwitchState.OPENED == DeviceHelper.getInt(device, PanelDataKey.PLUGIN_SWITCH_STATE, 0);
            pluginItem.setType(isOn ? TuyaItem.SMARTPLUGIN_ON
                    : TuyaItem.SMARTPLUGIN_OFF);
            pluginItem.setAskPlug(DeviceHelper.getBoolean(device, PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false));
            pluginItem.setOnline(DeviceHelper.getBoolean(device, PanelDataKey.IS_ONLINE, false));

            final int index = findIndexInTargetList(tempSmartPlugPluginList, id);
            if (index >= 0) {
                pluginItem.setType(tempSmartPlugPluginList.get(index).getType());
            }

            mSmartPlugPluginList.add(pluginItem);
            device.registerDeviceCallBack(MainMiddleNewFragment.this);
        }

        if (mSmartPlugPluginList.size() > 0) {
            Collections.sort(mSmartPlugPluginList, new TuyaItemPlusComparator2());
        }

        tempSmartPlugPluginList.clear();
    }
    /* ************************ 配件相关-END  ********************************* */

    private void initDeviceStatus() {
        DDLog.d(TAG, "initDeviceStatus");
        runOnMainThread(() -> DeviceCallBackManager.getInstance().initDeviceStatus(mPanelDevice));
    }

    /**
     * 判断plugins的值有没有数据（是否有门窗没关）
     * 如果没有-->表示流程执行完毕
     * 如果有-->显示ready to arm弹窗
     * 判断force值————true为显示执行界面 (前提是同一个messageid)
     * false为显示知道了界面
     */

    private boolean newArmCallBackAction(String result, boolean operateSelf, int size) {
        try {
            Gson gson = new Gson();
            JSONObject jsonObject = new JSONObject(result);
            if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "plugins"))) {
                UnCloseDoorEntry.ResultBean unCloseDoorEntry = gson.fromJson(result, UnCloseDoorEntry.ResultBean.class);
                if (unCloseDoorEntry.getPlugins().size() > 0) {
                    if (!unCloseDoorEntry.isForce()) {
                        //界面显示知道了。—————————>这里是遥控触发
                        //"知道了"页面的hint，需要知道 pluginid 和 pluginname
                        if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "pluginid"))) {
                            getMainActivity().pluginId = DDJSONUtil.getString(jsonObject, "pluginid");

                        }
                        if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "pluginname"))) {
                            getMainActivity().pluginName = DDJSONUtil.getString(jsonObject, "pluginname");
                        }

                        if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "category"))) {
                            getMainActivity().category = DDJSONUtil.getString(jsonObject, "category");
                        }
                        if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "subcategory"))) {
                            getMainActivity().subCategory = DDJSONUtil.getString(jsonObject, "subcategory");
                        }

                        ArmCallBackAction(result, size);
                        showReadyToArmDialog(LocalKey.ARM_KEY, unCloseDoorEntry);
                    } else {
                        if (operateSelf) {
                            showReadyToArmDialog(LocalKey.ARM_KEY, unCloseDoorEntry);
                        } else {
                            initDeviceStatus();
                        }
                    }
                } else {
                    //无数据，跟ready to arm 没关。或没有门是开着的。或者提示框点击确认执行
                    ArmCallBackAction(result, size);
                }
            } else {
                ArmCallBackAction(result, size);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return true;
    }


    private boolean ArmCallBackAction(String result, int size) {
        if (size == 0) {
            runOnMainThread(() -> DeviceCallBackManager.getInstance().toInitArm(result));
        }
        //更新widget
        DBUtil.Put(Constants.KEY_SERVICE_STATUS, Widget.ArmStatus.STATUS_ARM);
        return true;
    }


    private boolean DisArmCallBackAction(String result, boolean operateSelf, int size) {

        try {
            JSONObject jsonObject = new JSONObject(result);
            boolean isLatestCmd = DDJSONUtil.getBoolean(jsonObject, PanelDataKey.Panel.IS_LATEST_CMD);
            if (isLatestCmd) {
                if (size == 0) {
                    runOnMainThread(() -> DeviceCallBackManager.getInstance().toInitDisArm());
                    getMainActivity().removeCommonFragment(SOSFragment.class.getName());
                    getMainActivity().showTopToast(R.drawable.btn_disarm_sel, getString(R.string.disarm_toast));
                }
                getMainActivity().showSOSLayout(false);
                EventBus.getDefault().post(new RemoveSosFinish(operateSelf));
                runOnMainThread(() -> DeviceCallBackManager.getInstance().playSound(
                        1, 1, 1, 0, 2, 1));
            } else {
                i("ignore arm");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        //更新widget
        DBUtil.Put(Constants.KEY_SERVICE_STATUS, Widget.ArmStatus.STATUS_DISARM);
        return true;
    }

    private boolean newHomeArmCallBackAction(String result, boolean operateSelf, int size) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "plugins"))) {
                Gson gson = new Gson();
                UnCloseDoorEntry.ResultBean unCloseDoorEntry = gson.fromJson(result, UnCloseDoorEntry.ResultBean.class);
                if (unCloseDoorEntry.getPlugins().size() > 0) {
                    /**
                     * plugin 有数据，触发ready to arm
                     */
                    if (!unCloseDoorEntry.isForce()) {
                        //界面显示知道了。—————————>这里是遥控触发

                        //"知道了"页面的hint，需要知道 pluginid 和 pluginname
                        if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "pluginid"))) {
                            getMainActivity().pluginId = DDJSONUtil.getString(jsonObject, "pluginid");

                        }
                        if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "pluginname"))) {
                            getMainActivity().pluginName = DDJSONUtil.getString(jsonObject, "pluginname");
                        }

                        HomeArmCallBackAction(result, size);
                        showReadyToArmDialog(LocalKey.HOMEARM_KEY, unCloseDoorEntry);
                    } else {
                        /**
                         * 这里是手机触发的ready to arm界面
                         * 先行删除eventlist数据。因为这次的eventlist是无效的。
                         */
                        if (operateSelf) {
                            /**
                             * messageId 是自己的的时候，出现有"强制执行"按钮的ready to arm提示页面
                             */
                            showReadyToArmDialog(LocalKey.HOMEARM_KEY, unCloseDoorEntry);
                        } else {
                            //如果不是自己的，就回到上一个状态
                            initDeviceStatus();
                        }
                    }
                } else {
                    //无数据，跟ready to arm 没关。或没有门是开着的。或者提示框点击确认执行
                    HomeArmCallBackAction(result, size);
                }
            } else {
                HomeArmCallBackAction(result, size);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return true;
    }

    private boolean HomeArmCallBackAction(String result, int size) {
        runOnMainThread(() -> DeviceCallBackManager.getInstance().toInitHomeArm(result, size));
        //更新widget
        DBUtil.Put(Constants.KEY_SERVICE_STATUS, Widget.ArmStatus.STATUS_HOMEARM);
        return true;
    }


    private boolean SetHomearmCallBackAction(DeviceResultEvent ev) {
        if (LocalKey.SET_HOMEARM.equals(ev.getCmdType())) {
            if (!getDelegateActivity().isCommonFragmentExist(DefineHomeArmFragment.class.getName())) {
                DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setHas_homearm_set(true);
                DeviceInfoHelper.getInstance().saveDeviceInfo();
            }
            return true;
        }
        return false;
    }

    private boolean SetSostextCallBackAction(DeviceResultEvent ev) {
        if (LocalKey.SET_SOSTEXT.equals(ev.getCmdType())) {
            DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setHas_sos_text_set(true);
            DeviceInfoHelper.getInstance().saveDeviceInfo();
            return true;
        }
        return false;
    }

    private boolean SetPasswordCallBackAction(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            String uid = DDJSONUtil.getString(jsonObject, "userid");
            String message = DDJSONUtil.getString(jsonObject, "message");
            if (!uid.equals(DinSDK.getUserInstance().getUser().getUid())) {
                AlertDialog.createBuilder(getDelegateActivity())
                        .setAutoDissmiss(true)
//                            .setRound1(false)
                        .setType(AlertDialogManager.DialogType.PANEL_PASSWORD_CHANGED)
                        .setContentColor(getResources().getColor(R.color.common_dialog_content))
                        .setBackgroundTint(Color.WHITE)
                        .setOk(Local.s(getResources().getString(R.string.send_email_confirm_text)))
                        .setContent(message)
                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {

                            }
                        })
                        .preBuilder()
                        .show();
            }
        } catch (JSONException e) {
            e.printStackTrace();
            return true;
        }
        return true;
    }

    private boolean LowBatteryCallBackAction(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            String message = DDJSONUtil.getString(jsonObject, "message");
            AlertDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
//                        .setRound1(false)
                    .setType(AlertDialogManager.DialogType.PANEL_LOW_BATTERY)
                    .setContentColor(getResources().getColor(R.color.common_dialog_content))
                    .setBackgroundTint(Color.WHITE)
                    .setOk(Local.s(getResources().getString(R.string.has_know)))
                    .setContent(message)
                    .preBuilder()
                    .show();
        } catch (JSONException e) {
            e.printStackTrace();
            return true;
        }
        return true;
    }

    private boolean SetDeviceTextCallBackAction(String result) {
        if (null != mPanelDevice && null != mPanelDevice.getInfo()) {
            mPanelDevice.getInfo().put(PanelDataKey.Panel.IS_MESSAGE_SET, true);
        }

        return true;
    }

    private boolean EventLowerPowerCallBackAction(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
            if (LocalKey.EVENT_LOWERPOWER.equals(operateCmd)) {
                String message = DDJSONUtil.getString(jsonObject, "message");
                String postfix = "";
                try {
                    postfix = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                AlertDialog.createBuilder(getDelegateActivity())
                        .setAutoDissmiss(true)
//                        .setRound1(false)
                        .setContentColor(getResources().getColor(R.color.common_dialog_content))
                        .setBackgroundTint(Color.WHITE)
                        .setOk(Local.s(getResources().getString(R.string.has_know)))
                        .setPostfix(postfix)
                        .setType(AlertDialogManager.DialogType.PLUGIN_LOW_BATTERY)
                        .setContent(message)
                        .preBuilder()
                        .show();
                return true;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }

    private boolean EventPluginLiveCallBackAction(String result) {
        updatePluginLivingState(result);
        try {
            JSONObject jsonObject = new JSONObject(result);
            String message = DDJSONUtil.getString(jsonObject, "message");
            String postfix = "";
            try {
                final String sendId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID);
                final String stype = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE);
                if (!TextUtils.isEmpty(sendId)
                        && !TextUtils.isEmpty(stype)) {
                    postfix = sendId + "-" + stype;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            AlertDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
                    .setContentColor(getResources().getColor(R.color.common_dialog_content))
                    .setBackgroundTint(Color.WHITE)
                    .setPostfix(postfix)
                    .setType(AlertDialogManager.DialogType.PLUGIN_ONLINE_STATE_CHANGED)
                    .setOk(Local.s(getResources().getString(R.string.has_know)))
                    .setContent(message)
                    .preBuilder()
                    .show();
        } catch (JSONException e) {
            e.printStackTrace();
            return true;
        }
        return true;
    }

    private boolean DoorBellCallBackAction(DeviceResultEvent ev) {
        if (LocalKey.DOORBELL_CAPTURE.equals(ev.getCmdType())) {
            try {
                if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getPermission() == LocalKey.ADMIN) {
                    JSONObject jsonObject = new JSONObject(ev.getReslut());
                    String url = DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.DOOR_BELL_SERVER_IP + DDJSONUtil.getString(jsonObject, "img"));
                    getMainActivity().toShowDoorBellDialog(DDJSONUtil.getString(jsonObject, "message"),
                            url);
                    getMainActivity().setDoorBellImageUrl("");
                }
            } catch (JSONException e) {
            }
            return true;
        }
        return false;
    }

    /**
     * 更新配件离线、在线状态
     *
     * @param result {@link LocalKey#PLUGIN_ONLINE}  {@link LocalKey#PLUGIN_OFFLINE}
     */
    private void updatePluginLivingState(String result) {
        DDLog.i(TAG, "updatePluginLivingState, ev: " + result);
        try {
            JSONObject jsonObject = new JSONObject(result);
            final String operatorCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);

            boolean online = LocalKey.PLUGIN_ONLINE.equals(operatorCmd);
            final String stype = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE);
            int category = MainPanelHelper.getInstance().getAskPluginType(stype);
            String sendId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID);
            if (TextUtils.isEmpty(sendId)) {
                DDLog.e(TAG, "Send id为空");
                return;
            }

            if (SMART_PLUGS == category) {
                // 更新插座在线状态
                updateOnlineStateShortCut(sendId, online);
            } else if (IPCKey.DOOR_SENSOR == category) {
                // 更新门磁在线状态
                updateOnlineStateDoorSensor(sendId, online);
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on updatePluginOnlineState");
            e.printStackTrace();
        }
    }

    /**
     * 更新插座的在线状态
     *
     * @param sendId
     * @param online
     * @return
     */
    private boolean updateOnlineStateShortCut(String sendId, boolean online) {
        DDLog.i(TAG, "updateOnlineStateShortCut, sendId: " + sendId + ", online: " + online);
        boolean panelDeviceState = !CommonDataUtil.getInstance().isPanelOnline();
        runOnMainThread(() -> DeviceCallBackManager.getInstance().updateOnlineStateShortCut(sendId, online, panelDeviceState));

        int index = findIndexInTargetListBySendId(mShortcutSectionList, sendId);
        if (0 <= index) {
            mShortcutSectionList.get(index).setOnline(online);
            mShortcutSectionList.get(index).setNeedOnlineState(true);
            mShortcutSectionList.get(index).setFlagLoaded(true);
            DDLog.i(TAG, "Update plug online state.");
            return true;
        }

        // 没有在DoorSensor显示列表中，尝试源数据中修改
        index = findIndexInTargetListBySendId(mSmartPlugPluginList, sendId);

        if (index >= 0) {
            mSmartPlugPluginList.get(index).setOnline(online);
            mSmartPlugPluginList.get(index).setNeedOnlineState(true);
            mSmartPlugPluginList.get(index).setFlagLoaded(true);
            DDLog.i(TAG, "Update plug online state in source list.");
            return true;
        }

        return false;
    }

    /**
     * 更新门磁的在线状态
     *
     * @param sendid
     * @param online
     * @return
     */
    private boolean updateOnlineStateDoorSensor(String sendid, boolean online) {
        DDLog.i(TAG, "updateOnlineStateDoorSensor, sendid: " + sendid + ", online: " + online);
        boolean panelDeviceState = !CommonDataUtil.getInstance().isPanelOnline();
        runOnMainThread(() -> DeviceCallBackManager.getInstance().updateOnlineStateDoorSensor(sendid, online, panelDeviceState));


        // 尝试源数据中修改
        int index = findIndexInTargetListBySendId(mDoorSensorPluginList, sendid);
        if (index >= 0) {
            mDoorSensorPluginList.get(index).setOnline(online);
            mDoorSensorPluginList.get(index).setNeedOnlineState(true);
            mDoorSensorPluginList.get(index).setFlagLoaded(true);
            DDLog.i(TAG, "Update door sensor online in source list.");
            return true;
        }

        // 在DoorSensor显示列表中
        index = findIndexInTargetListBySendId(mDoorSensorSectionList, sendid);
        if (0 <= index) {
            mDoorSensorSectionList.get(index).setOnline(online);
            mDoorSensorSectionList.get(index).setNeedOnlineState(true);
            mDoorSensorSectionList.get(index).setFlagLoaded(true);
            DDLog.i(TAG, "Update door sensor online state.");
            return true;
        }
        return false;
    }

    /**
     * 处理配件状态改变事件
     * 电量、防拆、信号量
     *
     * @param result {@link LocalKey#TASK_PLUGIN_STATUS}
     */
    private void updatePluginState(@NonNull String cmd, String result) {
        DDLog.i(TAG, "updatePluginState, ev: " + result);
        try {
            JSONArray js;
            try {
                JSONObject jObj = new JSONObject(result);
                if (LocalKey.TASK_DS_STATUS.equals(cmd)) {
                    String message = DDJSONUtil.getString(jObj, "message");
                    String postfix = "";
                    try {
                        JSONArray plugins = DDJSONUtil.getJSONarray(jObj, "plugins");
                        if (null != plugins && plugins.length() > 0) {
                            postfix = DDJSONUtil.getString((JSONObject) plugins.get(0), "id");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (!TextUtils.isEmpty(message)) {
                        AlertDialog.createBuilder(getDelegateActivity())
                                .setAutoDissmiss(true)
                                .setContentColor(getResources().getColor(R.color.common_dialog_content))
                                .setType(AlertDialogManager.DialogType.DOOR_SENSOR_STATE_CHANGED)
                                .setPostfix(postfix)
                                .setBackgroundTint(Color.WHITE)
                                .setOk(Local.s(getResources().getString(R.string.ok)))
                                .setContent(message)
                                .preBuilder()
                                .show();
                    }
                }

                js = DDJSONUtil.getJSONarray(jObj, "plugins");
            } catch (Exception e) {
                js = new JSONArray(result);
            }

            if (null == js || 0 >= js.length()) {
                return;
            }

            JSONObject itemJson;
            String itemId;
            boolean enable;
            for (int i = 0; i < js.length(); i++) {
                itemJson = (JSONObject) js.get(i);
                if (DDJSONUtil.has(itemJson, "enable")) {
                    itemId = DDJSONUtil.getString(itemJson, "id");
                    enable = DDJSONUtil.getBoolean(itemJson, "enable");
                    if (updatePluginStateDoorSensor(itemId, enable)) {
                        DDLog.i(TAG, "在DoorSensor中找到并更新状态");
                        continue;
                    }

                    // TODO 如果没在DoorSensor分组，更新其他分组的状态
                }
            }
        } catch (JSONException e) {
            DDLog.e(TAG, "Error on updatePluginState");
            e.printStackTrace();
        }
    }

    /**
     * 处理配件Block状态改变
     *
     * @param reslut {@link LocalKey#SET_PLUGIN_BLOCK}
     */
    private void updatePluginStateBlock(String reslut) {
        DDLog.i(TAG, "updatePluginStateBlock");
        try {
            JSONObject itemJson = new JSONObject(reslut);
            String itemId;
            int block;
            if (DDJSONUtil.has(itemJson, NetKeyConstants.NET_KEY_BLOCK)) {
                itemId = DDJSONUtil.getString(itemJson, NetKeyConstants.NET_KEY_PLUGIN__ID);
                block = DDJSONUtil.getInt(itemJson, NetKeyConstants.NET_KEY_BLOCK);
                if (updatePluginStateBlockDoorSensor(itemId, block)) {
                    DDLog.i(TAG, "在DoorSensor中找到并更新Block状态");
                }

                // TODO 如果没在DoorSensor分组，更新其他分组的Block状态
            }
        } catch (JSONException e) {
            DDLog.e(TAG, "Error on updatePluginState");
            e.printStackTrace();
        }
    }

    /**
     * 更新配件的状态(DoorSensor分组)
     *
     * @param pluginId
     * @param isApart  是否处于开的状态，true:开; false:关
     * @return 是否在DoorSensor分组中找到并修改
     */
    private boolean updatePluginStateDoorSensor(String pluginId, boolean isApart) {
        DDLog.d(TAG, "updatePluginState, ID: " + pluginId);
        if (TextUtils.isEmpty(pluginId)) {
            DDLog.e(TAG, "pluginId empty.");
            return false;
        }

        // 更新widget
        runOnMainThread(() -> DeviceCallBackManager.getInstance().updatePluginStateDoorSensor(pluginId, isApart));

        // 先尝试在显示的DoorSensor列表中修改
        int index = findIndexInTargetList(mDoorSensorSectionList, pluginId);
        if (index >= 0) {
            if (!CommonDataUtil.getInstance()
                    .isPluginCanReady2Arm(mDoorSensorSectionList.get(index).getStype())) {
                DDLog.e(TAG, "当前DoorSensor没有开合状态, pluginId: " + pluginId);
                return false;
            }

            mDoorSensorSectionList.get(index).setHaveApart(true);
            mDoorSensorSectionList.get(index).setApart(isApart);

            DDLog.i(TAG, "Update door sensor state.");
            return true;
        }

        // 没有在DoorSensor显示列表中
        ArrayList<TuyaItemPlus> targetList = mDoorSensorPluginList;
        index = findIndexInTargetList(targetList, pluginId);

        if (index >= 0) {
            if (!CommonDataUtil.getInstance()
                    .isPluginCanReady2Arm(targetList.get(index).getStype())) {
                DDLog.e(TAG, "当前DoorSensor没有开合状态2, pluginId: " + pluginId);
                return false;
            }

            targetList.get(index).setHaveApart(true);
            targetList.get(index).setApart(isApart);
            DDLog.i(TAG, "Update door sensor state in black list.");
            return true;
        }
        return false;
    }

    /**
     * 更新配件的Block状态(DoorSensor分组)
     *
     * @param pluginId
     * @param block    block状态
     * @return 是否在DoorSensor中找到并修改
     */
    private boolean updatePluginStateBlockDoorSensor(String pluginId, int block) {
        DDLog.d(TAG, "updatePluginStateBlockDoorSensor, ID: " + pluginId);
        if (TextUtils.isEmpty(pluginId)) {
            DDLog.e(TAG, "plugin id empty.");
            return false;
        }

        DeviceCallBackManager.getInstance().updatePluginStateBlock(pluginId);

        // 没有在DoorSensor显示列表中，尝试源数据中修改
        int index = findIndexInTargetList(mDoorSensorPluginList, pluginId);

        if (index >= 0) {
            mDoorSensorPluginList.get(index).setBlock(block);
            boolean haveApart = true;
            if (!CommonDataUtil.getInstance()
                    .isPluginCanReady2Arm(mDoorSensorPluginList.get(index).getStype())
                    && !mDoorSensorPluginList.get(index).isNeedBlock()) {
                haveApart = false;
            }

            mDoorSensorPluginList.get(index).setHaveApart(haveApart);
            DDLog.i(TAG, "Update door sensor block state in source list.");
            return true;
        }

        // 先尝试在显示的DoorSensor列表中修改
        index = findIndexInTargetList(mDoorSensorSectionList, pluginId);
        DDLog.d(TAG, "updatePluginStateBlockDoorSensor, index: " + index);

        if (0 <= index) {
            mDoorSensorSectionList.get(index).setBlock(block);
            boolean haveApart = true;
            if (!CommonDataUtil.getInstance()
                    .isPluginCanReady2Arm(mDoorSensorSectionList.get(index).getStype())
                    && !mDoorSensorSectionList.get(index).isNeedBlock()) {
                haveApart = false;
            }
            mDoorSensorSectionList.get(index).setHaveApart(haveApart);
            DDLog.i(TAG, "Update door sensor block state.");
            return true;
        }

        return false;
    }

    /**
     * 更新配件名字
     *
     * @param pluginId 配件ID
     * @param name     配件新名字
     */
    private void updatePluginName(String pluginId, String name) {
        DDLog.i(TAG, "updatePluginName, pluginId: " + pluginId + ", name: " + name);

        // 插座
        boolean isSuccess = updatePluginNameFromShortcutSection(pluginId, name);
        if (isSuccess) {
            return;
        }

        isSuccess = updatePluginNameFromDoorSensorSection(pluginId, name);
        if (isSuccess) {
            return;
        }
    }

    /**
     * 从Shortcut分组中修改配件名字
     *
     * @param pluginId
     * @param name
     * @return 是否已经修改
     */
    private boolean updatePluginNameFromShortcutSection(String pluginId, String name) {
        DDLog.d(TAG, "updatePluginNameFromShortcutSection, name: " + name);
        if (TextUtils.isEmpty(pluginId)
                || TextUtils.isEmpty(name)) {
            return false;
        }
        runOnMainThread(() -> DeviceCallBackManager.getInstance().updateShortcutName(pluginId, name));


        boolean isSuccess = false;
        int index;
        if ((index = findIndexInTargetList(mSmartPlugPluginList, pluginId)) >= 0) {
            mSmartPlugPluginList.get(index).setName(name);
            isSuccess = true;
            DDLog.d(TAG, "updatePluginNameFromShortcutSection");
        }

        if (isSuccess) {
            index = findIndexInTargetList(mShortcutSectionList, pluginId);
            if (index >= 0) {
                mShortcutSectionList.get(index).setName(name);
                DDLog.i(TAG, "Update plug name.");
            }
        }

        return isSuccess;
    }

    /**
     * 从DoorSensor分组中修改配件名字
     *
     * @param pluginId
     * @param name
     * @return 是否已经修改
     */
    private boolean updatePluginNameFromDoorSensorSection(String pluginId, String name) {
        DDLog.d(TAG, "updatePluginNameFromDoorSensorSection, name: " + name);
        if (TextUtils.isEmpty(pluginId)
                || TextUtils.isEmpty(name)) {
            return false;
        }

        runOnMainThread(() -> DeviceCallBackManager.getInstance().updatePluginName(pluginId, name));

        boolean isSuccess = false;
        int index;
        if ((index = findIndexInTargetList(mDoorSensorPluginList, pluginId)) >= 0) {
            mDoorSensorPluginList.get(index).setName(name);
            isSuccess = true;
            DDLog.d(TAG, "updatePluginNameFromShortcutSection-door sensor list");
        }

        if (isSuccess) {
            index = findIndexInTargetList(mDoorSensorSectionList, pluginId);
            if (index >= 0) {
                mDoorSensorSectionList.get(index).setName(name);
                DDLog.i(TAG, "Update door sensor name.");
            }
        }

        return isSuccess;
    }

    /**
     * 删除配件对应的widget
     *
     * @param pluginId 需要删除的配件ID
     */
    private void deleteWidget(String pluginId) {
        DDLog.i(TAG, "deleteWidget, pluginId: " + pluginId);
        MainWidgetListProvider.getInstance().deleteWidgetInCacheLayout(pluginId);
    }


    /**
     * 修改旧插座的开关状态
     *
     * @param result
     */
    private void changeOldSmartPlugState(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            // 查找白名单
            String plugId = changeOldSmartPlugState(mSmartPlugPluginList, jsonObject);
            int index = findIndexInTargetList(mShortcutSectionList, plugId);

        } catch (Exception e) {
            DDLog.e(TAG, "changeOldSmartPlugState-ERROR!!!!!!!!!");
        }
    }

    /**
     * 更新旧插座开关状态
     *
     * @param srcList
     * @param jsonObject
     * @return 更新的插座的ID
     */
    private String changeOldSmartPlugState(ArrayList<TuyaItemPlus> srcList, JSONObject
            jsonObject) {
        if (null == srcList
                || 0 == srcList.size()
                || null == jsonObject) {
            return null;
        }

        try {
            TuyaItemPlus item;
            for (int i = 0; i < srcList.size(); i++) {
                item = srcList.get(i);
                if (item.getId().equals(DDJSONUtil.getString(jsonObject, "pluginid"))) {
                    if (LocalKey.KNOCK_TO_SOS_SUCCESS
                            .equals(DDJSONUtil.getString(jsonObject,
                                    "plugin_item_smart_plug_enable"))) {
                        item.setType(TuyaItem.SMARTPLUGIN_ON);
                    } else {
                        item.setType(TuyaItem.SMARTPLUGIN_OFF);
                    }
                    item.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                    DDLog.i(TAG, "更新旧插座开关状态");
                    return item.getId();
                }
            }
        } catch (Exception e) {
            DDLog.e(TAG, "changeOldSmartPlugState-ERROR!!!!!!!!!!!!!1");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 更新新插座开关状态
     *
     * @param srcList
     * @param jsonObject
     * @return 更新的插座的ID
     */
    private String changeAskSmartPlugState(ArrayList<TuyaItemPlus> srcList, JSONObject
            jsonObject) {
        if (null == srcList
                || 0 == srcList.size()
                || null == jsonObject) {
            return null;
        }

        try {
            TuyaItemPlus item;
            for (int i = 0; i < srcList.size(); i++) {
                item = srcList.get(i);
                if (item.getId().equals(DDJSONUtil.getString(jsonObject, "pluginid"))) {
                    if (LocalKey.STATUS_OPENED_ASK_PLUG == DDJSONUtil.getInt(jsonObject,
                            "plugin_item_smart_plug_enable")) {
                        item.setType(TuyaItem.SMARTPLUGIN_ON);
                    } else {
                        item.setType(TuyaItem.SMARTPLUGIN_OFF);
                    }
                    item.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                    DDLog.i(TAG, "更新ASK插座开关状态");
                    return item.getId();
                }
            }
        } catch (Exception e) {
            DDLog.e(TAG, "changeAskSmartPlugState-ERROR!!!!!!!!!!!!!1");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查找指定Id在列表中的下标
     *
     * @param srcList
     * @param sendId
     * @return
     */
    private int findIndexInTargetListBySendId(ArrayList<TuyaItemPlus> srcList, String sendId) {
        if (0 >= srcList.size()
                || TextUtils.isEmpty(sendId)) {
            return -1;
        }

        for (int i = 0; i < srcList.size(); i++) {
            if (sendId.equals(srcList.get(i).getSendid())) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 查找指定Id在列表中的下标
     *
     * @param srcList
     * @param id
     * @return
     */
    private int findIndexInTargetList(ArrayList<TuyaItemPlus> srcList, String id) {
        if (0 >= srcList.size()
                || TextUtils.isEmpty(id)) {
            return -1;
        }

        for (int i = 0; i < srcList.size(); i++) {
            if (id.equals(srcList.get(i).getId())) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 修改新ASK插座的开关状态
     *
     * @param result
     */
    private void changeAskSmartPlugState(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            // 查找并修改
            String plugId = changeAskSmartPlugState(mSmartPlugPluginList, jsonObject);
            int index = findIndexInTargetList(mShortcutSectionList, plugId);

        } catch (Exception e) {
            DDLog.e(TAG, "changeOldSmartPlugState-ERROR!!!!!!!!!");
        }
    }

    private void showReadyToArmDialog(String task, UnCloseDoorEntry.ResultBean resultBean) {
        DDLog.d(TAG, "showReadyToArmDialog");
        //关掉DeviceList
        EventBus.getDefault().post(new CloseAllDeviceEvent());

        /**
         * 如果当前是SOSFragment， 就不添加ReadyToArmDialogFragment
         * 如果当前是ReadyToArmDialgoFragment, 就先删除
         */
        if (!getDelegateActivity().isFragmentInTop(SOSFragment.class.getSimpleName())) {
            if (getDelegateActivity().isFragmentInTop(ReadyToArmDialogFragment.class.getSimpleName())) {
                getDelegateActivity().popCommonFragment(true);
            }
            getDelegateActivity().addCommonFragment(ReadyToArmDialogFragment.newInstance(task, resultBean));
        }
    }

    private void showReadyToArmDialog() {
        //关掉DeviceList
        EventBus.getDefault().post(new CloseAllDeviceEvent());

        /**
         * 如果当前是SOSFragment， 就不添加ReadyToArmDialogFragment
         * 如果当前是ReadyToArmDialgoFragment, 就先删除
         */
        if (!getDelegateActivity().isFragmentInTop(SOSFragment.class.getSimpleName())) {
            if (getDelegateActivity().isFragmentInTop(ReadyToArmDialogFragment.class.getSimpleName())) {
                getDelegateActivity().popCommonFragment(true);
            }
            if (!TextUtils.isEmpty(getMainActivity().readyToArmCmd)) {
                getDelegateActivity().addCommonFragment(ReadyToArmDialogFragment.newInstance(getMainActivity().readyToArmCmd));
            }

        }
    }

    /* *********************** 旧离线页面代码-START ************************** */

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOnlineEvent ev) {
        DDLog.i(TAG, "DeviceOnlineEvent");
        getMainActivity().reConnectWebSocket(false);
        showTimeOutLoadinFramgment();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOfflineEvent ev) {
        DDLog.i(TAG, "DeviceOfflineEvent");
        showToast(getString(R.string.offline_hint));
        onDeviceStateError();
        DBUtil.Put(DBKey.WIDGET_CURRENT_DEVICE_STATUS, false);
    }

    /* *********************** 旧离线页面代码-END ************************** */

    private void onGetSOSStatus(Map<String, Object> resultMap) {
        DDLog.d(TAG, "onGetSOSStatus");

        if (null == resultMap) {
            getMainActivity().showSOSLayout(false);
            return;
        }

        SosStatusEntry sosStatusEntry = new SosStatusEntry();
        SosStatusEntry.ResultBean resultBean = new SosStatusEntry.ResultBean();
        resultBean.setIsdevice(DeviceHelper.getBoolean(resultMap, PanelDataKey.SosInfo.IS_DEVICE, false));
        resultBean.setSosalarm(DeviceHelper.getBoolean(resultMap, PanelDataKey.SosInfo.SOS_ALARM, false));
        resultBean.setTime(DeviceHelper.getLong(resultMap, PanelDataKey.SosInfo.TIME, 0));
        resultBean.setUid(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.UID, ""));
        resultBean.setPhoto(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.PHOTO, ""));
        resultBean.setPluginid(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.PLUGIN_ID, ""));
        resultBean.setPluginname(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.PLUGIN_NAME, ""));
        resultBean.setIntimidationmessage(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.INTIMIDATION_MESSAGE, ""));
        resultBean.setSubcategory(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.SUBCATEGORY, ""));
        resultBean.setCategory(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.CATEGORY, ""));
        resultBean.setSostype(DeviceHelper.getString(resultMap, PanelDataKey.SosInfo.SOS_TYPE, ""));
        sosStatusEntry.setResult(resultBean);

        if (sosStatusEntry.getResult().isSosalarm()) {
            sosStatusEntry.getResult().setTime(sosStatusEntry.getResult().getTime() / 1000000);
            sosStatusEntry.setCmd(sosStatusEntry.getResult().getSostype());
            SOSFragment.sosStatusEntry = sosStatusEntry;
            EventBus.getDefault().post(new CloseAllDeviceEvent());
            EventBus.getDefault().post(new CloseSmartWidgetEvent());
            getDelegateActivity().addCommonFragment(SOSFragment.newInstance());
        } else {
            getMainActivity().showSOSLayout(false);
        }

        if (careModeDialog != null && careModeDialog.isShowing()) {
            careModeDialog.dismiss();
        }
    }

    /* *********************** 主机状态改变切换页面-START ************************** */

    /**
     * 主机离线时调用该方法
     * 方法内部会根据情况显示相应的错误页
     */
    private void onDeviceStateError() {
        DDLog.e(TAG, "onDeviceStateError");

        if (null != DeviceInfoHelper.getInstance().getCurrentDeviceInfo()
                && DeviceInfoHelper.getInstance().getCurrentDeviceInfo().isSimNormal()) {
            changeDeviceStateErrorHadSim();
        } else {
            changeDeviceStateErrorHadSim();
        }
    }

    /**
     * 根据用户权限，修改Toolbar中SOS item的显示状态
     *
     * @param showSos true: 显示SOS
     */
    private void changeDeviceToolbarStateByUserPermission(boolean showSos) {
        DDLog.i(TAG, "changeDeviceToolbarStateByUserPermission, showSos: " + showSos);
        runOnMainThread(() -> DeviceCallBackManager.getInstance().changeDeviceToolbarStateByUserPermission(showSos));
    }


    /**
     * 根据用户权限-初始化相关视图
     */
    private void initViewStateByPermission() {
        DDLog.i(TAG, "initViewStateByPermission");
        changeDeviceToolbarStateByUserPermission(!HomeManager.getInstance().isGuest());
        initFunctionData();
    }

    /**
     * 用户权限改变-更新视图
     */
    private void onUserPermissionUpdate() {
        DDLog.i(TAG, "onUserPermissionUpdate");
        mBinding.rvPluginWidget.scrollToPosition(0);
        initViewStateByPermission();
        addAllSection();
    }


    /**
     * 主机状态正常
     */
    private void changeDeviceStateNormal() {
        DDLog.i(TAG, "changeDeviceStateNormal.  ");
        DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setDeviceOffline(false);
        runOnMainThread(() -> {
            DeviceCallBackManager.getInstance().changeDeviceStateNormal(mPanelDevice);
            DeviceCallBackManager.getInstance().updateOnlineStateDoorSensorByPanelDevice(false);
            DeviceCallBackManager.getInstance().changeOfficialSmartPlugStateByPanelDevice(false);
        });
        changeSettingUserFunctionEnable(true, true, true);
    }

    /**
     * 主机状态错误且上次有SIM卡
     */
    private void changeDeviceStateErrorHadSim() {
        DDLog.i(TAG, "changeDeviceStateErrorHadSim");
        DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setDeviceOffline(true);

        runOnMainThread(() -> {
            DeviceCallBackManager.getInstance().changeDeviceStateErrorHadSim();
            DeviceCallBackManager.getInstance().updateOnlineStateDoorSensorByPanelDevice(true);
            DeviceCallBackManager.getInstance().changeOfficialSmartPlugStateByPanelDevice(true);
        });
        changeSettingUserFunctionEnable(false, true, true);
    }

    /**
     * 主机状态为正在升级中
     */
    private void changeDeviceStatePanelUpgrading() {
        DDLog.i(TAG, "changeDeviceStatePanelUpgrading");
        runOnMainThread(() -> DeviceCallBackManager.getInstance().changeDeviceStatePanelUpgrading());

        changeSettingUserFunctionEnable(false, true, true);
        EventBus.getDefault().post(new PanelUpgradeStateChangeEvent());
    }

    /**
     * 主机处于离线模式
     */
    private void changeDeviceStateOfflineMode() {
        DDLog.i(TAG, "changeDeviceStateOfflineMode");
        DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setDeviceOffline(true);

        runOnMainThread(() -> {
            DeviceCallBackManager.getInstance().changeDeviceStateOffline();
            DeviceCallBackManager.getInstance().updateOnlineStateDoorSensorByPanelDevice(true);
            DeviceCallBackManager.getInstance().changeOfficialSmartPlugStateByPanelDevice(true);
        });


        changeSettingUserFunctionEnable(false, true, true);
    }

    /**
     * 修改View的可点击状态
     * 主机离线和正常状态切换后，切换item、setting、user的可点击状态
     *
     * @param enable true:可点击
     */
    private void changeFunctionViewEnableState(boolean enable) {
        DDLog.i(TAG, "changeViewEnableState, enable: " + enable);
        MainPanelHelper.getInstance().setIsFunctionEnable(enable);
    }

    /**
     * 修复首页左上User和右上setting图标的可点击状态
     */
    private void changeSettingAndUserViewEnable(boolean userEnable, boolean settingEnable) {
        DDLog.i(TAG, "changeSettingAndUserViewEnable");
        MainPanelHelper.getInstance().setSettingAndUserEnable(userEnable, settingEnable);
        mainTittleLayout.setSettingUserIconEnable(userEnable, settingEnable);
    }

    private void changeSettingUserFunctionEnable(boolean functionEnable, boolean userEnable,
                                                 boolean settingEnable) {
        changeFunctionViewEnableState(functionEnable);
        changeSettingAndUserViewEnable(userEnable, settingEnable);
    }


    /**
     * 切换主机显示的视图
     *
     * @param type 主页视图类型
     */
    private void changeMainContentType(@MainPanelHelper.MainContentType int type) {
        DDLog.d(TAG, "changeMainContentType, type: " + type);
        switch (type) {
            case MainPanelHelper.MAIN_CONTENT_TYPE_HANDOVER_FAMILY_NO_CACHE:
                mBinding.llSmallRefresh.setVisibility(View.GONE);
                mBinding.layoutEmpty.clEmpty.setVisibility(View.GONE);
                controlHandoverFamilyAnim(true);
                mBinding.layoutHandoverFamilyLoading.clHandoverFamily.setVisibility(View.VISIBLE);
                break;
            case MainPanelHelper.MAIN_CONTENT_TYPE_NO_PANEL:
            case MainPanelHelper.MAIN_CONTENT_TYPE_PANEL:
                mBinding.llSmallRefresh.setVisibility(View.VISIBLE);
                mBinding.layoutEmpty.clEmpty.setVisibility(View.GONE);
                controlHandoverFamilyAnim(false);
                mBinding.layoutHandoverFamilyLoading.clHandoverFamily.setVisibility(View.GONE);
                break;
            case MainPanelHelper.MAIN_CONTENT_TYPE_EMPTY:
            default:
                mBinding.llSmallRefresh.setVisibility(View.GONE);
                mBinding.layoutEmpty.clEmpty.setVisibility(View.VISIBLE);
                controlHandoverFamilyAnim(false);
                mBinding.layoutHandoverFamilyLoading.clHandoverFamily.setVisibility(View.GONE);
                changeMainContentEmptyScanVisible(true);
                break;
        }
        MainPanelHelper.getInstance().setMainContentType(type);
    }

    private void changeMainContentEmptyScanVisible(boolean visible) {
        mBinding.layoutEmpty.tvHelp.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
        mBinding.layoutEmpty.tvTabScan.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
        mBinding.layoutEmpty.ivScan.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
    }

    /* *********************** 主机状态改变切换页面-END ************************** */

    /* *********************** 老人模式-配件没响应事件-START ************************** */

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(CareModeNoActionEvent ev) {
        DDLog.d(TAG, "On event, CareModeNoActionEvent");
        EventBus.getDefault().removeStickyEvent(ev);
        checkCareModeTime();
    }

    CareModeNoActionDialog careModeDialog;

    private boolean CareModePluginNoAction(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            int noActionTime = DDJSONUtil.getInt(jsonObject, "no_action_time");
            int noActionTimeHour = noActionTime / (60 * 60);
            int delayTime = DDJSONUtil.getInt(jsonObject, "alarm_delay_time");
            long lastNoActionTime = DDJSONUtil.getLong(jsonObject, "last_no_action_time");
            if (lastNoActionTime / 1000000 + delayTime * 1000 < System.currentTimeMillis()) {
                if (careModeDialog != null && careModeDialog.isShowing()) {
                    careModeDialog.dismiss();
                }
                return true;
            }
            int startTime = (int) (delayTime - ((System.currentTimeMillis() - lastNoActionTime / 1000000) / 1000));
            String content = Local.s(getString(R.string.care_mode_no_action_content))
                    .replace("#family", HomeManager.getInstance().getCurrentHome().getHomeName())
                    .replace("#no_action_time", noActionTimeHour + "");
            if (careModeDialog != null && careModeDialog.isShowing()) {
                careModeDialog.dismiss();
            }
            careModeDialog = CareModeNoActionDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
                    .setContent(content)
                    .setStartTime(startTime)
                    .preBuilder();
            careModeDialog.show();
        } catch (JSONException e) {
            e.printStackTrace();
            return true;
        }
        return true;
    }

    private boolean CareModePluginCancelAction(String result) {
        if (careModeDialog != null && careModeDialog.isShowing()) {
            careModeDialog.dismiss();
        }
        return true;
    }


    private void checkCareModeTime() {
        if (null == mPanelDevice || mPanelDevice.getFlagDeleted()) {
            return;
        }

        long delayTime = DeviceHelper.getLong(mPanelDevice, PanelDataKey.Panel.ALARM_DELAY_TIME, 0);
        long lastNoActionTime = DeviceHelper.getLong(mPanelDevice, PanelDataKey.Panel.LAST_NO_ACTION_TIME, 0);
        if (lastNoActionTime == 0 || (lastNoActionTime / 1000 + delayTime * 1000 < System.currentTimeMillis())) {
            if (careModeDialog != null && careModeDialog.isShowing()) {
                careModeDialog.dismiss();
            }
            return;
        }

        int startTime = (int) (delayTime - ((System.currentTimeMillis() - lastNoActionTime / 1000000) / 1000));
        long noActionTimeHour = DeviceHelper.getLong(mPanelDevice, PanelDataKey.Panel.NO_ACTION_TIME, 0) / (60 * 60);
        String content = Local.s(getString(R.string.care_mode_no_action_content))
                .replace("#family", HomeManager.getInstance().getCurrentHome().getHomeName())
                .replace("#no_action_time", noActionTimeHour + "");
        if (careModeDialog != null && careModeDialog.isShowing()) {
            careModeDialog.dismiss();
        }
        careModeDialog = CareModeNoActionDialog.createBuilder(getDelegateActivity())
                .setAutoDissmiss(true)
                .setContent(content)
                .setStartTime(startTime)
                .preBuilder();
        careModeDialog.show();
    }

    /* *********************** 老人模式-配件没响应事件-END ************************** */
    /* *********************** 主页旧代码-START ************************** */
    private void createMainHomeList() {
        mainTittleLayout.refreshHomeList();
    }

    private void updataWidgetLanguage() {
        /*
         *更新widget语言
         */
        /*
         * 存储widget需要的文字
         */
        Context context = DinSaferApplication.getAppContext();
        DBUtil.Put(DBKey.WIDGET_REQUEST_FAIL, Local.s(context.getResources().getString(R.string.failed_try_again)));
        DBUtil.Put(DBKey.WIDGET_OFFLINE_DEVICE, Local.s(context.getResources().getString(R.string.offline_hint)));
        DBUtil.Put(DBKey.WIDGET_NO_PANNEL, Local.s(context.getResources().getString(R.string.no_alarm_panel)));
        DBUtil.Put(DBKey.WIDGET_STRING_ARM, Local.s(context.getResources().getString(R.string.toolbar_arm_text)));
        DBUtil.Put(DBKey.WIDGET_STRING_HOMEARM, Local.s(context.getResources().getString(R.string.toolbar_homearm_text)));
        DBUtil.Put(DBKey.WIDGET_STRING_DISARM, Local.s(context.getResources().getString(R.string.toolbar_disarm_text)));
        DBUtil.Put(DBKey.WIDGET_STRING_SOS, Local.s(context.getResources().getString(R.string.main_section_sos)));
    }

    private void updataUI() {
        Log.d(TAG, "updataUI: ");
        mainTittleLayout.updateUI();
        mBinding.layoutEmpty.tvHelp.setLocalText(getString(R.string.help_title));
        mBinding.layoutEmpty.tvTabScan.setLocalText(getString(R.string.start_adding_a_device));

        pluginWidgetAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceSimStatueEvent ev) {
        DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setSim(ev.getSimStatus());

        DeviceCallBackManager.getInstance().updateSimStatus(ev);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleGetDeviceOnlineEvent ev) {
        DDLog.i("Ble", "BleGetDeviceOnlineEvent");
        /**
         * 这里为了防止，在还没有走完所有流程，就收到绑定成功的结果导致直接去登录
         * 出现的原因：当去到绑定流程时，退出app然后又进入app，这时，device会继续发送上次没处理完的结果。
         */
        if (!CommonDataUtil.getInstance().isCanToAddBleDevice()) {
            return;
        } else {
            CommonDataUtil.getInstance().setCanToAddBleDevice(false);
        }

        CommonDataUtil.getInstance().setBleToAdd(true);
        if (getDelegateActivity().isCommonFragmentExist(AdvancedSettingFragment.class.getName())) {
            CommonDataUtil.getInstance().setBleToChange(true);
        } else {
            getMainActivity().smoothToHome();
            CommonDataUtil.getInstance().setNeedToUserView(false);
        }
        DDLog.d("login", "LoginFailTest,网络，isNetworkAvailable:" + DDSystemUtil.isNetworkAvailable(getContext()));
        if (ev.isAddPanelMode()) {
            // 添加主机成功
            showMsgFragment("", true, getResources().getString(R.string.success), "");
            mTaskHandler.removeCallbacks(mAddPanelRefreshTask);
            mTaskHandler.postDelayed(mAddPanelRefreshTask, LocalKey.SUCCESS_TOAST_TIME);
        } else {
            // 配置网络成功
            showMsgFragment("", true, getResources().getString(R.string.success), "");
            mTaskHandler.removeCallbacks(mReConfigPanelNetworkTask);
            mTaskHandler.postDelayed(mReConfigPanelNetworkTask, LocalKey.SUCCESS_TOAST_TIME);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ShowSuccessToastEvent ev) {
        DDLog.d(TAG, "bletest ShowSuccessToastEvent");
        if (CommonDataUtil.getInstance().isBleToChange()) {
            getMainActivity().smoothToSetting();
        } else if (CommonDataUtil.getInstance().isNeedToUserView()) {
            getMainActivity().smoothToUser();
        } else {
            getMainActivity().smoothToHome();
        }
        CommonDataUtil.getInstance().setNeedToUserView(false);
        CommonDataUtil.getInstance().setBleToAdd(false);
        showMsgFragment("", true, getResources().getString(R.string.success), "");
//        closeLoadingFragment();
        new Handler().postDelayed(new Runnable() {
            public void run() {
                getDelegateActivity().removeAllCommonFragment();
                if (CommonDataUtil.getInstance().isBleToChange()) {
                    getMainActivity().addCommonFragment(AdvancedSettingFragment.newInstance());
                }
            }
        }, LocalKey.SUCCESS_TOAST_TIME);

    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onIPCMotionDetectedPushEvent(IPCMotionDetectedPushEvent
                                                     ipcMotionDetectedPushEvent) {
        Log.d(TAG, "onIPCMotionDetectedPushEvent: ");
        EventBus.getDefault().removeStickyEvent(ipcMotionDetectedPushEvent);
        try {
            handIPCMotionDetected(new JSONObject(ipcMotionDetectedPushEvent.getExtra()), true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Dialog ipcMotionDetectionAlertDialog;
    private Dialog dscamGuideTipDialog;
    private JSONObject motionDetectedData;
    private long lastMotionDialogShowedTimeMillis = 0;
    private static final long DURATION_MOTION_DIALOG_INTERVAL = 5 * 1000;

    private void handIPCMotionDetected(JSONObject jsonObject, boolean needShowLoading) throws JSONException {
        String ipc_id = DDJSONUtil.getString(jsonObject, "ipc_pid");
        if (getMainActivity().isApStepFragmentExit()
                || DinSDK.getUserInstance().getUser() == null
                || (ActivityController.getInstance().currentActivity() != null
                && ActivityController.getInstance().currentActivity() instanceof HeartLaiFullPlayActivity
                && !TextUtils.isEmpty(ipc_id)
                && ipc_id.equals(((HeartLaiFullPlayActivity) ActivityController.getInstance().currentActivity()).mCameraPid))
                || (ActivityController.getInstance().currentFragment() != null && ActivityController.getInstance().currentFragment() instanceof SOSFragment)) {
            //添加主机过程不显示
            return;
        }

        if (!SettingInfoHelper.getInstance().isAdminOrUser()) {
            // Guest权限不接收IPC的移动侦测
            DDLog.i(TAG, "handIPCMotionDetected. Guest权限不接收IPC的移动侦测");
            return;
        }

        if (ActivityController.getInstance().currentActivity() != null
                && ActivityController.getInstance().currentActivity() instanceof DsCamMultiFullPlayActivity
                && !TextUtils.isEmpty(ipc_id)
                && ((DsCamMultiFullPlayActivity) ActivityController.getInstance().currentActivity()).isContainIPC(ipc_id)) {
            Log.w(TAG, "handIPCMotionDetected: v005/v006直播界面不弹移动侦测 " + ipc_id);
            return;
        }

        final long currentTimeMillis = System.currentTimeMillis();
        if (ipcMotionDetectionAlertDialog != null && ipcMotionDetectionAlertDialog.isShowing()) {
            if (currentTimeMillis - lastMotionDialogShowedTimeMillis <= DURATION_MOTION_DIALOG_INTERVAL) {
                return;
            }
            ipcMotionDetectionAlertDialog.dismiss();
            ipcMotionDetectionAlertDialog = null;
        }

        String id = DDJSONUtil.getString(jsonObject, "id");
        String home_id = DDJSONUtil.getString(jsonObject, MyBaseDinsaferPushReveiver.HOME_ID);
        String homename = DDJSONUtil.getString(jsonObject, "homename");
        String provider = DDJSONUtil.getString(jsonObject, "provider");
        String ipcname = DDJSONUtil.getString(jsonObject, "ipcname");
        String cover = DDJSONUtil.getString(jsonObject, "cover");
        String time = DDSystemUtil.timeStamp2Date(DDJSONUtil.getLong(jsonObject, "gmtime") / 1000000, "yyyy.MM.dd HH:mm:ss");
        String message = DDJSONUtil.getString(jsonObject, "message");
        String eventId = DDJSONUtil.getString(jsonObject, MyBaseDinsaferPushReveiver.EVENT_ID);
        long triggerTimeMillis = DDJSONUtil.getLong(jsonObject, MyBaseDinsaferPushReveiver.TRIGGERING_TIME) * 1000;
        if (ActivityController.getInstance().currentActivity() != null
                && !ActivityController.getInstance().currentActivity().isFinishing()) {

            MotionDetectedPushDialog.AlertClickCallback okListener = new MotionDetectedPushDialog.AlertClickCallback() {

                @Override
                public void onClick(MotionDetectedPushDialog dialog) {
                    ActivityController.getInstance().finishActivity(HeartLaiFullPlayActivity.class);
                    ActivityController.getInstance().finishActivity(DsCamMultiFullPlayActivity.class);
                    ActivityController.getInstance().finishActivity(DsDoorbellPlayFocusModeActivity.class);
                    ActivityController.getInstance().finishActivity(DsDoorbellPlayFullscreenActivity.class);

                    if (TextUtils.isEmpty(home_id) || TextUtils.isEmpty(ipc_id)) {
                        showErrorToast();
                        return;
                    }
                    if (HomeManager.getInstance().getCurrentHome() != null && home_id.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                        EventBus.getDefault().post(new CloseActivityEvent());
                        if (HeartLaiConstants.PROVIDER_HEARTLAI.equals(provider)) {
                            HeartLaiFullPlayActivity.startActivity(getContext(), ipc_id);
                        } else if (DinConst.TYPE_DSCAM.equalsIgnoreCase(provider)) {
                            // DsCamFullPlayActivity.startActivity(getContext(), ipc_id);
                            final ArrayList<String> params = new ArrayList<>();
                            params.add(ipc_id);
                            DsCamMultiFullPlayActivity.start(getMainActivity(), params);
                        } else if (DinConst.TYPE_DSCAM_VOO6.equalsIgnoreCase(provider)) {
                            // DsCamV006FullPlayActivity.startActivity(getContext(), ipc_id);
                            final ArrayList<String> params = new ArrayList<>();
                            params.add(ipc_id);
                            DsCamMultiFullPlayActivity.start(getMainActivity(), params);
                        } else if (DinConst.TYPE_DSCAM_VO15.equalsIgnoreCase(provider)) {
                            final ArrayList<String> params = new ArrayList<>();
                            params.add(ipc_id);
                            DsCamMultiFullPlayActivity.start(getMainActivity(), params);
                        }
                        return;
                    }

                    motionDetectedData = jsonObject;
                    showTimeOutLoadinFramgment();
                    HomeManager.getInstance().changeFamily(home_id);
                }
            };

            MotionDetectedPushDialog.AlertClickCallback cancelListener = new MotionDetectedPushDialog.AlertClickCallback() {
                @Override
                public void onClick(MotionDetectedPushDialog dialog) {
                    EventBus.getDefault().post(new PlayMotionDetectedRecordEvent());
                    DBUtil.Put(eventId, true);
                    if (HeartLaiConstants.PROVIDER_HEARTLAI.equals(provider)) {
                        IPCHeartLaiMotionRecordIJKPlayerActivity2.start(getDelegateActivity(), eventId, ipc_id, provider, ipcname);
                        getMainActivity().setNotNeedToLogin(true);
                    } else if (DinConst.TYPE_DSCAM.equalsIgnoreCase(provider)
                            || DinConst.TYPE_DSCAM_VOO6.equalsIgnoreCase(provider)
                            || DinConst.TYPE_DSCAM_VO15.equalsIgnoreCase(provider)) {
                        MotionRecordTimelinePlayerActivity.start(getDelegateActivity(), ipc_id, eventId, triggerTimeMillis);
                        getMainActivity().setNotNeedToLogin(true);
                    }
                }
            };

            if (TextUtils.isEmpty(cover)) {
                ipcMotionDetectionAlertDialog = AlertDialog.createBuilder(ActivityController.getInstance().currentActivity())
                        .setOk(getString(R.string.ipc_motion_detect_go_live))
                        .setCancel(getString(R.string.ignore))
                        .setContent(message)
                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                if (okListener != null) {
                                    okListener.onClick(null);
                                }
                            }
                        })
                        .setShowLoading(needShowLoading)
                        .setDeviceId(ipc_id)
                        .setProvider(provider)
                        .preBuilder();
            } else {
                ipcMotionDetectionAlertDialog = MotionDetectedPushDialog.createBuilder(ActivityController.getInstance().currentActivity())
                        .setOk(getString(R.string.ipc_motion_detect_go_live))
                        .setCancel(getString(R.string.ipc_motion_detect_check_the_video))
                        .setImgUrl(cover)
                        .setTitle(time)
                        .setContent(message)
                        .setOKListener(okListener)
                        .setCancelListener(cancelListener)
                        .setShowLoading(needShowLoading)
                        .setDeviceId(ipc_id)
                        .setProvider(provider)
                        .preBuilder();
            }
            lastMotionDialogShowedTimeMillis = currentTimeMillis;
            ipcMotionDetectionAlertDialog.show();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onDoorbellCallPushEvent(DoorbellCallPushEvent
                                                doorbellCallPushEvent) {
        Log.d(TAG, "onDoorbellCallPushEvent: ");
        EventBus.getDefault().removeStickyEvent(doorbellCallPushEvent);
        try {
            handleDoorbellCall(new JSONObject(doorbellCallPushEvent.getExtra()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private MotionDetectedPushDialog doorbellCallAlertDialog;
    private JSONObject doorbellCallData;

    private void handleDoorbellCall(JSONObject jsonObject) throws JSONException {
        String ipc_id = DDJSONUtil.getString(jsonObject, "ipc_pid");
        if (getMainActivity().isApStepFragmentExit()
                || DinSDK.getUserInstance().getUser() == null
                || (ActivityController.getInstance().currentActivity() != null
                && ActivityController.getInstance().currentActivity() instanceof DsDoorbellPlayFocusModeActivity
                && !TextUtils.isEmpty(ipc_id)
                && ipc_id.equals(((DsDoorbellPlayFocusModeActivity) ActivityController.getInstance().currentActivity()).mCameraPid))
                || (ActivityController.getInstance().currentFragment() != null && ActivityController.getInstance().currentFragment() instanceof SOSFragment)) {
            //添加主机过程不显示
            return;
        }

        if (ActivityController.getInstance().currentActivity() != null
                && ActivityController.getInstance().currentActivity() instanceof DsDoorbellPlayFullscreenActivity
                && !TextUtils.isEmpty(ipc_id)
                && ipc_id.equals(((DsDoorbellPlayFullscreenActivity) ActivityController.getInstance().currentActivity()).mCameraPid)) {
            Log.w(TAG, "handleDoorbellCall: 直播界面不弹Call弹窗 " + ipc_id);
            return;
        }
        if (doorbellCallAlertDialog != null && doorbellCallAlertDialog.isShowing()) {
            doorbellCallAlertDialog.dismiss();
            doorbellCallAlertDialog = null;
        }

        String id = DDJSONUtil.getString(jsonObject, "id");
        String home_id = DDJSONUtil.getString(jsonObject, MyBaseDinsaferPushReveiver.HOME_ID);
        String homename = DDJSONUtil.getString(jsonObject, "homename");
        String provider = DDJSONUtil.getString(jsonObject, "provider");
        String ipcname = DDJSONUtil.getString(jsonObject, "ipcname");
        String cover = DDJSONUtil.getString(jsonObject, "cover");
        String time = DDSystemUtil.timeStamp2Date(DDJSONUtil.getLong(jsonObject, "gmtime") / 1000000, "yyyy.MM.dd HH:mm:ss");
        String title = DDJSONUtil.getString(jsonObject, "title");
        String message = DDJSONUtil.getString(jsonObject, "message");
        String eventId = DDJSONUtil.getString(jsonObject, MyBaseDinsaferPushReveiver.EVENT_ID);
        if (ActivityController.getInstance().currentActivity() != null
                && !ActivityController.getInstance().currentActivity().isFinishing()) {
            doorbellCallAlertDialog = MotionDetectedPushDialog.createBuilder(ActivityController.getInstance().currentActivity())
                    .setOk(getString(R.string.ignore))
                    .setCancel(getString(R.string.answer))
                    .setImgUrl(cover)
                    .setTitle(title)
                    .setContent(message)
                    .setOnDialogInitListener(new MotionDetectedPushDialog.OnDialogInitListener() {
                        @Override
                        public void onDialogInit(ViewDataBinding binding) {
                            binding.getRoot().findViewById(R.id.iv_close).setVisibility(View.INVISIBLE);
                            LocalTextView tvTitle = (LocalTextView) binding.getRoot().findViewById(R.id.dialog_hint);
                            tvTitle.setBackgroundColor(getResources().getColor(R.color.transparent));
                            tvTitle.setTextSize(18);
                            tvTitle.setTextColor(getResources().getColor(R.color.black));
                            tvTitle.setPadding(0, 0, 0, 0);

                            LocalCustomButton btnIgnore = (LocalCustomButton) binding.getRoot().findViewById(R.id.ok_btn);
                            btnIgnore.setNormalColor(R.color.color_brand_light_01);
                            btnIgnore.setPressedColor(R.color.color_brand_light_01);
                            btnIgnore.setStrokeColor(R.color.color_brand_light_01);
                            btnIgnore.setTextColor(getResources().getColor(R.color.color_brand_primary));
                        }
                    })
                    .setOKListener(new MotionDetectedPushDialog.AlertClickCallback() {

                        @Override
                        public void onClick(MotionDetectedPushDialog dialog) {
                            dialog.dismiss();
                        }
                    })
                    .setCancelListener(new MotionDetectedPushDialog.AlertClickCallback() {
                        @Override
                        public void onClick(MotionDetectedPushDialog dialog) {
                            ActivityController.getInstance().finishActivity(HeartLaiFullPlayActivity.class);
                            ActivityController.getInstance().finishActivity(DsCamMultiFullPlayActivity.class);
                            if (TextUtils.isEmpty(home_id) || TextUtils.isEmpty(ipc_id)) {
                                showErrorToast();
                                return;
                            }
                            if (HomeManager.getInstance().getCurrentHome() != null && home_id.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                                EventBus.getDefault().post(new CloseActivityEvent());
                                DsDoorbellPlayFocusModeActivity.startActivity(getMainActivity(), ipc_id, true, false, false, title);
                                return;
                            }

                            doorbellCallData = jsonObject;
                            showTimeOutLoadinFramgment();
                            HomeManager.getInstance().changeFamily(home_id);
                        }
                    })
                    .preBuilder();
            doorbellCallAlertDialog.show();
        }
    }

    /**
     * 切换家庭后跳转到ipc直播界面
     */
    private void checkNeedHandlePushData() {
        if (motionDetectedData == null) {
            return;
        }

        String home_id = DDJSONUtil.getString(motionDetectedData, MyBaseDinsaferPushReveiver.HOME_ID);
        String ipc_id = DDJSONUtil.getString(motionDetectedData, "ipc_pid");
        String provider = DDJSONUtil.getString(motionDetectedData, "provider");

        motionDetectedData = null;
        if (TextUtils.isEmpty(home_id) || TextUtils.isEmpty(ipc_id) || TextUtils.isEmpty(provider)) {
            Log.d(TAG, "checkNeedHandlePushData: homeid or ipcid is null!");
            return;
        }
        if (HomeManager.getInstance().getCurrentHome() != null && home_id.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
            EventBus.getDefault().post(new CloseActivityEvent());
            if (HeartLaiConstants.PROVIDER_HEARTLAI.equals(provider)) {
                HeartLaiFullPlayActivity.startActivity(getContext(), ipc_id);
            } else if (DinConst.TYPE_DSCAM.equals(provider)) {
                // DsCamFullPlayActivity.startActivity(getContext(), ipc_id);
                final ArrayList<String> params = new ArrayList<>();
                params.add(ipc_id);
                DsCamMultiFullPlayActivity.start(getMainActivity(), params);
            } else if (DinConst.TYPE_DSCAM_VOO6.equals(provider)) {
                // DsCamV006FullPlayActivity.startActivity(getContext(), ipc_id);
                final ArrayList<String> params = new ArrayList<>();
                params.add(ipc_id);
                DsCamMultiFullPlayActivity.start(getMainActivity(), params);
            } else if (DinConst.TYPE_DSCAM_VO15.equals(provider)) {
                final ArrayList<String> params = new ArrayList<>();
                params.add(ipc_id);
                DsCamMultiFullPlayActivity.start(getMainActivity(), params);
            }
        }

    }

//    private void getMotionRecordAccessUrl(String homeId, String id, String ipcId, String provider, String ipcname) {
//        showTimeOutLoadinFramgmentWithErrorAlert();
//        DinSDK.getHomeInstance().getMotionDetectionRecordVideoUrl(homeId, id, new IDefaultCallBack2<String>() {
//            @Override
//            public void onSuccess(String s) {
//                closeTimeOutLoadinFramgmentWithErrorAlert();
//                DBUtil.Put(id, true);
//                IPCHeartLaiMotionRecordIJKPlayerActivity.start(getDelegateActivity(), s, ipcId, provider, ipcname);
//                getMainActivity().setNotNeedToLogin(true);
//            }
//
//            @Override
//            public void onError(int i, String s) {
//                closeTimeOutLoadinFramgmentWithErrorAlert();
//                showErrorToast();
//            }
//        });
//    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIPCAlertExhaustedEvent(DeviceResultEvent deviceResultEvent) {
        if (LocalKey.IPC_ALERT_EXHAUSTED.equals(deviceResultEvent.getCmdType())) {
            Log.e(TAG, "onIPCAlertExhaustedEvent: " + deviceResultEvent.getReslut());
            try {
                handleIPCAlertExhaustedEvent(new JSONObject(deviceResultEvent.getReslut()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onIPCAlertExhaustedPushEvent(IPCAlertExhaustedEvent ipcAlertExhaustedEvent) {
        Log.d(TAG, "onIPCAlertExhaustedEventEvent: ");
        EventBus.getDefault().removeStickyEvent(ipcAlertExhaustedEvent);
        try {
            handleIPCAlertExhaustedEvent(new JSONObject(ipcAlertExhaustedEvent.getExtra()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleIPCAlertExhaustedEvent(JSONObject jsonObject) {

        if (!AppConfig.Functions.SUPPORT_CLOUD_SERVICE || getMainActivity().isApStepFragmentExit() || DinSDK.getUserInstance().getUser() == null) {
            //添加主机过程不显示
            return;
        }

        String message = DDJSONUtil.getString(jsonObject, "message");
        IPCAlertExhaustedPushDialog alertDialog = new IPCAlertExhaustedPushDialog.Builder(getContext())
                .setContent(getString(R.string.ipc_subscription_alert_exhausted_content))
                .setTitle(message)
                .setOk(getString(R.string.ignore))
                .setCancel(getString(R.string.ipc_subscription_upgrade_service))
                .setCancelListener(dialog -> MainMiddleNewFragment.this.getDelegateActivity()
                        .addCommonFragment(IPCSubscriptionFragment.newInstance())).preBuilder();
        alertDialog.show();
    }

    /* *********************** 主页旧代码-END ************************** */

    private void initAlertService() {
        if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE) {
            CloudStorageServiceHelper.getInstance().getServiceConfig();
        }
    }

    // @Subscribe(threadMode = ThreadMode.MAIN)
    // public void onEventMainThread(AlertServicePlanUpdateEvent ev) {
    //     initAlertService();
    // }

    public void showBlockToast(Map<String, Object> result) {
        DDLog.i(TAG, "showBlockToast, result： " + result);
        final String plugin = DeviceHelper.getString(result, PanelDataKey.PLUGIN, "");
        final String operateCmd = DeviceHelper.getString(result, PanelDataKey.CmdResult.OPERATION_CMD, null);

        if (TextUtils.isEmpty(plugin)) {
            if (LocalKey.ARM_KEY.equals(operateCmd)) {
                getMainActivity().showTopToast(R.drawable.btn_arm_sel, getString(R.string.arm_toast));
            } else {
                getMainActivity().showTopToast(R.drawable.btn_homearm_sel, getString(R.string.homearm_toast));
            }
            return;
        }

        if (LocalKey.ARM_KEY.equals(operateCmd)) {
            getMainActivity().showTopToast(R.drawable.btn_arm_sel, getString(R.string.arm_toast), plugin);
        } else {
            getMainActivity().showTopToast(R.drawable.btn_homearm_sel, getString(R.string.homearm_toast), plugin);
        }
    }

    /* ************************ DinSDK设备改变回调-START ********************************* */
    @Override
    public void onDeviceAdd(Device device) {
        i("onDeviceAdd:" + device);

    }


    @Override
    public void onDeviceRemove(Device s) {
        i("onDeviceRemove:" + s);
        runOnMainThread(() -> {
            if (null == s) {
                DDLog.e(TAG, "Empty device ");
                return;
            }

            if (s.equals(CommonDataUtil.getInstance().getCurrentPanelID())) {
                DDLog.i(TAG, "移除或重置了主机");
                getMainActivity().runOnUiThread(() -> {
                    deleteWidget(s.getId());
                    DinHome.getInstance().removeDeviceCacheById(s.getId());
                    CommonDataUtil.getInstance().setCurrentPanelID("");
                    EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
                    getMainActivity().removeAllCommonFragment();
                    getMainActivity().smoothToHome();
                    showTimeOutLoadinFramgmentWithErrorAlert();
                });
                return;
            }

        });
    }

    /* ************************ DinSDK设备改变回调-end ********************************* */


    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        DDLog.i(TAG, "onCmdCallBack, s: " + deviceId + ", s1: " + cmd + ", map: " + map);
        final String currentDeviceId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || TextUtils.isEmpty(currentDeviceId)) {
            DDLog.e(TAG, "Empty deviceId or cmd");
            return;
        }

        if (!deviceId.equals(currentDeviceId)) {
            onPluginCmdCallBack(deviceId, cmd, map);
            return;
        }

        onPanelCmdCallBack(deviceId, cmd, map);
    }

    public void onPanelCmdCallBack(String deviceId, String cmd, Map map) {
        DDLog.i(TAG, "onPanelCmdCallBack");
        String resultStr;
        Map<String, Object> resultMap;
        final int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, -1);
        final int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        final boolean operateSelf = DeviceHelper.getBoolean(map, PanelDataKey.CmdResult.OWNER, false);

        switch (cmd) {
            case PanelCmd.OPERATION_ARM:
                if (PanelDataKey.CmdResult.SUCCESS != status) {
                    DDLog.e(TAG, "Error operation arm cmd result");
                    break;
                }
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                if (1 == resultType) {
                    onOperationArmResult(resultStr, operateSelf);
                } else if (0 == resultType) {
                    onOperationArmAck(resultStr);
                }
                break;
            case PanelCmd.OPERATION_SOS:
            case PanelCmd.CAREMODE_NOACTION_SOS:
                if (PanelDataKey.CmdResult.SUCCESS == status) {
                    onOperateSosResult(DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, ""));
                }
                break;
            case PanelCmd.SHOW_BLOCK_TOAST:
                showBlockToast(DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT));
                break;
            case PanelCmd.PANEL_PING_INFO:
                runOnMainThread(() -> DeviceCallBackManager.getInstance().updatePing(DeviceHelper.getString(map
                        , PanelDataKey.CmdResult.RESULT, ""), mPanelDevice));

                break;
            case PluginCmd.PLUGIN_STATE_CHANGE:
            case LocalKey.TASK_DS_STATUS:
                if (PanelDataKey.CmdResult.SUCCESS != status) {
                    DDLog.e(TAG, "error status: " + status);
                    break;
                }

                if (mLoadingDevices) {
                    mPluginStatusMap = map;
                } else {
                    resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                    updatePluginState(cmd, resultStr);
                }
                break;
            case PanelCmd.CAREMODE_NOACTION:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                CareModePluginNoAction(resultStr);
                break;
            case PanelCmd.CARE_MODE_CANCEL_SOS:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                CareModePluginCancelAction(resultStr);
                break;
            case PluginCmd.PLUGIN_POWER_CHANGE:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                EventLowerPowerCallBackAction(resultStr);
                break;
            case PanelCmd.PANEL_LOWPOWER:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                LowBatteryCallBackAction(resultStr);
                break;
            case PanelCmd.SET_PANEL_PASSWORD:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                SetPasswordCallBackAction(resultStr);
                break;
            case PanelCmd.SET_MESSAGE_LANGUAGE:
            case PanelCmd.SET_MESSAGE_TEMPLATE:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                SetDeviceTextCallBackAction(resultStr);
                break;
            case PanelCmd.PANEL_UPGRADING:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                onDeviceUpgrading(resultStr);
                break;
            case PluginCmd.PLUGIN_ONLINE_CHANGE:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                EventPluginLiveCallBackAction(resultStr);
                break;
            case PanelCmd.PANEL_POWERCHANGED:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                runOnMainThread(() -> DeviceCallBackManager.getInstance().updatePowerCallBackAction(this, resultStr));
                break;
            case PanelCmd.ON_ARM_STATE_ROLLBACK:
                initDeviceStatus();
                runOnMainThread(() -> DeviceCallBackManager.getInstance().toResetSos());
                break;
            case PanelCmd.GET_PANEL_SOSINFO:
                if (PanelDataKey.CmdResult.SUCCESS == status) {
                    resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
                    onGetSOSStatus(resultMap);
                } else {
                    getMainActivity().showSOSLayout(false);
                }
                break;
            case PluginCmd.PLUGIN_DELETE:
            case PanelOperatorConstant.CMD.ADD_PLUGIN:
            case PanelOperatorConstant.CMD.ADD_NEWASKPLUGIN:
                EventBus.getDefault().post(new GetPluginQuantityEvent(true));
                break;
            case PanelCmd.GET_SIM_CARD_INFO:
                if (selfOperate) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    if (PanelDataKey.CmdResult.SUCCESS == status) {
                        resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
                        final String dPhone = DeviceHelper.getString(resultMap, PanelDataKey.SimInfo.D_PHONE, "");
                        final int sim = DeviceHelper.getInt(resultMap, PanelDataKey.SimInfo.SIM, 0);
                        if (DeviceInfoHelper.getInstance().isSimNormal(sim)) {
                            showConfirmPhoneDialog(dPhone);
                        } else {
                            showToast(getString(R.string.restrict_model_sim_error_tip));
                        }
                    }
                    selfOperate = false;
                }
                break;
            case PanelCmd.USER_NETWORK_ERROR:
                if (CommonDataUtil.getInstance().checkHasUser()
                        && !getMainActivity().isApStepFragmentExit()) {
                    DDLog.writeOnlineLog("show network error in eventbus check user info --->>>>");
                    CommonDataUtil.getInstance().logUser();
                    closeLoadingFragment();
                    toShowNetWorkError();
                }
                break;
            case PanelCmd.PANEL_OFFLINE:
                changeDeviceStateErrorHadSim();
                EventBus.getDefault().post(new DeviceOfflineEvent());
                break;
            case PanelCmd.OPERATION_WS_CONNECTION:
                DDLog.i(TAG, "成功连接上Websocket");
                getMainActivity().runOnUiThread(() -> {
                    if (!mLoadingDevices
                            && null != mPanelDevice) {
                        if (!getMainActivity().isApStepFragmentExit()) {
                            closeLoadingFragment();
                        }
                        DDLog.i(TAG, "即将刷新主机状态视图");
                        boolean online = CommonDataUtil.getInstance().isPanelOnline();
                        if (online) {
                            changeDeviceStateNormal();
                        } else {
                            changeDeviceStateErrorHadSim();
                        }
                    }
                    if (deviceOfflineDialogHandler != null) {
                        deviceOfflineDialogHandler.removeCallbacksAndMessages(null);
                    }
                });
                break;
            case PanelCmd.RESET_PANEL:
                if (PanelDataKey.CmdResult.SUCCESS == status) {
                    boolean owner = DeviceHelper.getBoolean(map, PanelDataKey.CmdResult.OWNER, false);
                    if (owner) {
                        return;
                    }
                    resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                    onPanelReset(resultStr);
                }
                break;
            case PanelCmd.LOAD_PLUGIN_INFO:
                if (PanelDataKey.CmdResult.SUCCESS == status) {
                    resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
                    List<Map<String, Object>> list = DeviceHelper.getList(resultMap, PanelDataKey.PLUGIN_INFO);
                    if (null != list && list.size() > 0) {
                        for (Map<String, Object> temp : list) {
                            final String stype = DeviceHelper.getString(temp, PanelDataKey.S_TYPE, "");
                            int category = MainPanelHelper.getInstance().getAskPluginType(stype);
                            String sendId = DeviceHelper.getString(temp, PanelDataKey.SEND_ID, "");
                            boolean online = DeviceHelper.getBoolean(temp, PanelDataKey.IS_ONLINE, false);
                            String name = DeviceHelper.getString(temp, PanelDataKey.NAME, "");
                            String pluginID = DeviceHelper.getString(temp, PanelDataKey.ID, "");

                            if (TextUtils.isEmpty(sendId)) {
                                DDLog.e(TAG, "Send id为空");
                                return;
                            }
                            if (IPCKey.SMART_PLUGS == category) {
                                // 更新插座在线状态
                                updateOnlineStateShortCut(sendId, online);

                                // 更新开关状态
                                final ArrayList<TuyaItemPlus> targetList = mSmartPlugPluginList;
                                final int index = findIndexInTargetListBySendId(targetList, sendId);
                                if (index >= 0) {
                                    TuyaItemPlus tuyaItemPlus = targetList.get(index);
                                    String id = tuyaItemPlus.getId();
                                    Device device = DinSDK.getHomeInstance().getDevice(id);
                                    boolean isOn = PanelConstant.PluginSwitchState.OPENED == DeviceHelper.getInt(device, PanelDataKey.PLUGIN_SWITCH_STATE, 0);
                                    tuyaItemPlus.setType(isOn ? TuyaItem.SMARTPLUGIN_ON
                                            : TuyaItem.SMARTPLUGIN_OFF);
                                    boolean panelDeviceState = !CommonDataUtil.getInstance().isPanelOnline();
                                    runOnMainThread(() -> DeviceCallBackManager.getInstance().changeAskSmartPlugState(id, isOn, panelDeviceState));
                                }
                            } else if (IPCKey.DOOR_SENSOR == category) {
                                // 更新门磁在线状态
                                updateOnlineStateDoorSensor(sendId, online);
                            }

                            if (!TextUtils.isEmpty(pluginID)) {
                                updatePluginName(pluginID, name);
                            }
                        }
                    }
                    updateHomePluginListWithPluginCache(!mLoadingDevices);
                }
                break;
            case PluginCmd.PIR_SETTING_ENABLED:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                try {
                    JSONObject jsonObject = new JSONObject(resultStr);
                    final String pirId = DDJSONUtil.getString(jsonObject, PanelDataKey.PLUGIN_ID);
                    if (!TextUtils.isEmpty(pirId)) {
                        EventBus.getDefault().post(new PirSensitivityEnterSettingEvent(pirId));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
        }
    }

    private boolean isShowNetWorkError = false;
    private AlertDialog mNetWorkErrorDialog;

    public void toShowNetWorkError() {
        if (isShowNetWorkError) {
            return;
        }
        new Thread(
                new Runnable() {
                    @Override
                    public void run() {
                        // PingUtil.getPacketLoss 会阻塞线程
                        DDLog.writeOnlineLog("ping domain:" + PingUtil.getPacketLoss(APIKey.SERVER_IP));
                    }
                }
        ).start();

        isShowNetWorkError = true;
        deviceOfflineDialogHandler.removeCallbacksAndMessages(null);
        deviceOfflineDialogHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                closeLoadingFragment();
                mNetWorkErrorDialog = AlertDialog.createBuilder(getActivity())
                        .setAutoDissmiss(false)
                        .setCanCancel(false)
                        .setOk(Local.s(getResources().getString(R.string.send_email_confirm_text)))
                        .setContent(Local.s(getResources().getString(R.string.user_network_problem)))
                        .setContentColor(getResources().getColor(R.color.common_dialog_content))
                        .setBackgroundTint(Color.WHITE)
                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                if (NetworkCheckUtil.isNetworkAvailable()) {
                                    mNetWorkErrorDialog.dismiss();
                                    isShowNetWorkError = false;
                                    getMainActivity().reConnectWebSocket(true);
                                } else {
                                    mNetWorkErrorDialog.dismiss();
                                    isShowNetWorkError = false;
                                    showLoadingFragment(LoadingFragment.BLACK, "");
                                    toShowNetWorkError();
                                }
                            }
                        })
                        .preBuilder();
                mNetWorkErrorDialog.show();

            }
        }, 2000);
    }

    /**
     * 主机被Reset
     */
    private void onPanelReset(String resultStr) {
        try {
            JSONObject jsonObject = new JSONObject(resultStr);
            String hint = DDJSONUtil.getString(jsonObject, "message");
            AlertDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
                    .setCanCancel(false)
                    .setContentColor(getResources().getColor(R.color.common_dialog_content))
                    .setBackgroundTint(Color.WHITE)
                    .setOk(Local.s(getResources().getString(R.string.send_email_confirm_text)))
                    .setContent(hint)
                    .preBuilder()
                    .show();
        } catch (Exception e) {
            DDLog.e(TAG, "Error onPanelReset");
            e.printStackTrace();
        }
    }

    /**
     * sos cmd result 处理
     *
     * @param result
     */
    private void onOperateSosResult(String result) {
        DDLog.i(TAG, "onOperateSosResult, result: " + result);
        try {
            JSONObject jsonObject = new JSONObject(result);
            final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
            if (LocalKey.TASK_ANTIINTERFER_SOS.equals(operateCmd)) {
                SOSFragment.sosStatusEntry = new SosStatusEntry();
                SOSFragment.sosStatusEntry.setResult(new SosStatusEntry.ResultBean());
                SOSFragment.sosStatusEntry.setCmd(operateCmd);
                SOSFragment.sosStatusEntry.getResult().setIntimidationmessage(DDJSONUtil.getString(jsonObject, "intimidationmessage"));
                SOSFragment.sosStatusEntry.getResult().setTime(DDJSONUtil.getLong(jsonObject, "time") / 1000000);
                EventBus.getDefault().post(new CloseAllDeviceEvent());
                EventBus.getDefault().post(new CloseSmartWidgetEvent());
                getDelegateActivity().addCommonFragment(SOSFragment.newInstance());
            } else {
                initDeviceStatus();

                SOSFragment.sosStatusEntry = new SosStatusEntry();
                SOSFragment.sosStatusEntry.setResult(new SosStatusEntry.ResultBean());
                SOSFragment.sosStatusEntry.setCmd(operateCmd);
                SOSFragment.sosStatusEntry.getResult().setTime(DDJSONUtil.getLong(jsonObject, "time") / 1000000);
                SOSFragment.sosStatusEntry.getResult().setUid(DDJSONUtil.getString(jsonObject, "uid"));
                SOSFragment.sosStatusEntry.getResult().setPhoto(DDJSONUtil.getString(jsonObject, "photo"));
                SOSFragment.sosStatusEntry.getResult().setPluginid(DDJSONUtil.getString(jsonObject, "pluginid"));
                SOSFragment.sosStatusEntry.getResult().setPluginname(DDJSONUtil.getString(jsonObject, "pluginname"));
                SOSFragment.sosStatusEntry.getResult().setIsdevice(DDJSONUtil.getBoolean(jsonObject, "isdevice"));
                SOSFragment.sosStatusEntry.getResult().setIntimidationmessage(DDJSONUtil.getString(jsonObject, "intimidationmessage"));
                SOSFragment.sosStatusEntry.getResult().setCategory(DDJSONUtil.getString(jsonObject, "category"));
                SOSFragment.sosStatusEntry.getResult().setSubcategory(DDJSONUtil.getString(jsonObject, "subcategory"));
                EventBus.getDefault().post(new CloseAllDeviceEvent());
                EventBus.getDefault().post(new SOSevent());
                EventBus.getDefault().post(new CloseSmartWidgetEvent());
                getDelegateActivity().addCommonFragment(SOSFragment.newInstance());

                if (LocalKey.NO_ACTION_SOS.equals(operateCmd)) {
                    if (careModeDialog != null && careModeDialog.isShowing()) {
                        careModeDialog.dismiss();
                    }
                }

                if (LocalKey.TASK_INTIMIDATIONALARM_SOS.equals(operateCmd)) {
                    runOnMainThread(() -> DeviceCallBackManager.getInstance().toInitDisArm());
                }
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error onOperateSosResult!");
            e.printStackTrace();
        }
    }

    private void onOperationArmAck(String result) {
        DDLog.i(TAG, "onOperationArmAck, result: " + result);

        try {
            JSONObject jsonObject = new JSONObject(result);
            final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
            final int exitDelay = DDJSONUtil.getInt(jsonObject, PanelDataKey.Panel.EXIT_DELAY);

            if (LocalKey.ARM_KEY.equals(operateCmd)) {
                if (0 < exitDelay) {
                    getMainActivity().showTopToast(R.drawable.btn_arm_sel, Local.s(getString(R.string.arm_delay_toast)).replace("#delay_time",
                            "" + exitDelay));
                    runOnMainThread(() -> {
                        DeviceCallBackManager.getInstance().onOperationArmAck(exitDelay);
                        MainWidgetListProvider.getInstance().setOperateArmDelay(CommonDataUtil.getInstance().getCurrentDeviceId(), exitDelay);
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void onOperationArmResult(String result, boolean operateSelf) {
        DDLog.i(TAG, "onOperationArmResult, result: " + result + ", self: " + operateSelf);
        try {
            final JSONObject jsonObject = new JSONObject(result);
            final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
            final int operateQueueSize = DDJSONUtil.getInt(jsonObject, PanelDataKey.Panel.OPERATE_QUEUE_SIZE);
            switch (operateCmd) {
                case LocalKey.ARM_KEY:
                    newArmCallBackAction(result, operateSelf, Math.max(operateQueueSize, 0));
                    break;
                case LocalKey.DISARM_KEY:
                    DisArmCallBackAction(result, operateSelf, Math.max(operateQueueSize, 0));
                    break;
                case LocalKey.HOMEARM_KEY:
                    newHomeArmCallBackAction(result, operateSelf, Math.max(operateQueueSize, 0));
                    break;
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error onOperationArmResult");
            e.printStackTrace();
        }
    }

    public void onPluginCmdCallBack(String deviceId, String cmd, Map map) {
        DDLog.i(TAG, "onPluginCmdCallBack");
        String resultStr;
        String resultMap;
        final int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, -1);
        switch (cmd) {
            case PluginCmd.PLUG_CHANGE_ON:
                if (PanelDataKey.CmdResult.SUCCESS != status) {
                    DDLog.e(TAG, "error status: " + status);
                    break;
                }

                try {
                    resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                    JSONObject jsonObject = new JSONObject(resultStr);
                    final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                    boolean panelDeviceState = !CommonDataUtil.getInstance().isPanelOnline();
                    if (LocalKey.SET_SMART_PLUG_ENABLE.equals(operateCmd)) {
                        changeOldSmartPlugState(resultStr);
                        runOnMainThread(() -> DeviceCallBackManager.getInstance().changeOldSmartPlugState(jsonObject, panelDeviceState));
                    } else {
                        changeAskSmartPlugState(resultStr);
                        runOnMainThread(() -> DeviceCallBackManager.getInstance().changeAskSmartPlugState(jsonObject, panelDeviceState));
                    }


                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case PluginCmd.PLUGIN_SETNAME:
                if (PanelDataKey.CmdResult.SUCCESS != status) {
                    DDLog.e(TAG, "error status: " + status);
                    break;
                }

                try {
                    resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                    JSONObject jsonObject = new JSONObject(resultStr);
                    final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                    String pluginId, name;
                    if (LocalKey.SET_NEWASKPLUGINDATA.equals(operateCmd)) {
                        pluginId = DDJSONUtil.getString(jsonObject, "plugin_id");
                        name = DDJSONUtil.getString(jsonObject, "name");
                        updatePluginName(pluginId, name);
                    } else {
                        pluginId = DDJSONUtil.getString(jsonObject, "pluginid");
                        name = DDJSONUtil.getString(jsonObject, "plugin_item_name");
                    }
                    updatePluginName(pluginId, name);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case PluginCmd.PLUGIN_CONFIG_BLOCK:
                if (PanelDataKey.CmdResult.SUCCESS != status) {
                    DDLog.e(TAG, "error status: " + status);
                    break;
                }

                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                updatePluginStateBlock(resultStr);
                break;
            case PluginCmd.PLUGIN_DELETE:
                if (PanelDataKey.CmdResult.SUCCESS != status) {
                    DDLog.e(TAG, "error status: " + status);
                    break;
                }
                try {
                    resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                    JSONObject jsonObject = new JSONObject(resultStr);
                    final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                    String deleteId;
                    if (LocalKey.DELETE_PLUGIN.equals(operateCmd)) {
                        deleteId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                    } else {
                        deleteId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ID);
                    }
                    EventBus.getDefault().post(new PluginListUpdateEvent(deleteId, PluginListUpdateEvent.OPERATION_DELETE));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case PluginCmd.GET_PLUGIN_DETAIL:
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (PanelDataKey.CmdResult.SUCCESS != status) {
                    DDLog.e(TAG, "error status: " + status);
                    break;
                }
                try {
                    resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                    JSONObject askData = new JSONObject(resultStr);
                    if (DinHome.getInstance().getDevice(deviceId) instanceof DoorSensorDevice) {
                        ModifyPluginInfoHelper.getInstance().modifyDoorSensorAndPirPluginInfo(getMainActivity(), askData);
                    } else {
                        ModifyPluginInfoHelper.getInstance().modifyAskPluginInfo(getMainActivity(), askData);
                    }
                } catch (JSONException e) {
                    DDLog.e(TAG, "Error on GET_PLUGIN_DETAIL");
                    e.printStackTrace();
                }
                break;
//            case PluginCmd.PLUGIN_POWER_CHANGE:
//                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
//                EventLowerPowerCallBackAction(resultStr);
//                break;
            case PluginCmd.PLUGIN_ONLINE_CHANGE:
                resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                EventPluginLiveCallBackAction(resultStr);
                break;
        }
    }


    @Override
    public void online(String s, String subCategory) {
        DDLog.i(TAG, "online, s: " + s + " " + subCategory);

    }

    @Override
    public void offline(String s, String subCategory, String s1) {
        DDLog.i(TAG, "offline, s: " + s + "s1: " + s1);

    }

    @Override
    public void onInfoUpdate(String id, String subCategory, int type) {
        DDLog.i(TAG, "onInfoUpdate panel device, id: " + id + " type: " + type);
        if (TextUtils.isEmpty(id)) {
            return;
        }

        if (!id.equals(mPanelDevice.getId())) {
            return;
        }
        // 只处理主机
        if (FLAG_DELETED == type) {
            MainWidgetListProvider.getInstance().updatePanelDeviceItem(mPanelDevice);
            EventBus.getDefault().post(new WidgetFlagDeletedEvent(id, mPanelDevice.getFlagDeleted(), MainPanelHelper.SECTION_TYPE_PANEL_DEVICE));
        } else if (FLAG_LOADED == type) {
            EventBus.getDefault().post(new DeviceLoadedStateChangedEvent(id, PluginConstants.TYPE_PANEL));
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WidgetFlagDeletedEvent event) {
        DDLog.i(TAG, "WidgetFlagDeletedEvent: " + event.getPluginId() + " is delete ? " + event.isDeletedState());
        runOnMainThread(() -> {
            switch (event.getBindModelType()) {
                case MainPanelHelper.SECTION_TYPE_IPC:
                    DeviceCallBackManager.getInstance().refreshIpcWidgetDeletedState(event.getPluginId(), event.isDeletedState());
                    break;
                case MainPanelHelper.SECTION_TYPE_SHORTCUT:
                    int index = findIndexInTargetList(mShortcutSectionList, event.getPluginId());
                    if (index >= 0) {
                        mShortcutSectionList.get(index).setFlagDeleted(event.isDeletedState());
                    }
                    boolean isRefreshShortcut = DeviceCallBackManager.getInstance().refreshShortcutDeletedState(event.getPluginId(), event.isDeletedState());
                    if (!isRefreshShortcut || !event.isDeletedState()) {
                        if (PluginManager.getInstance().isSmartPlugin(event.getPluginId())) {
                            updateHomePluginCache();
                            updateHomePluginListWithPluginCache(true);
                            break;
                        }
                    }
                    break;
                case MainPanelHelper.SECTION_TYPE_DOOR_SENSOR:
                    index = findIndexInTargetList(mDoorSensorPluginList, event.getPluginId());
                    if (index >= 0) {
                        mDoorSensorPluginList.get(index).setFlagDeleted(event.isDeletedState());
                    }
                    boolean isRefreshPlugin = DeviceCallBackManager.getInstance().refreshPluginDeletedState(event.getPluginId(), event.isDeletedState());
                    if (!isRefreshPlugin || !event.isDeletedState()) {
                        // 更新源数据
                        updateHomePluginCache();
                        updateHomePluginListWithPluginCache(true);
                    }
                    break;
                case MainPanelHelper.SECTION_TYPE_PANEL_DEVICE:
                    DeviceCallBackManager.getInstance().refreshHostDeletedState(event.getPluginId(), event.isDeletedState());
                    break;
                case MainPanelHelper.SECTION_TYPE_BMT:
                    DeviceCallBackManager.getInstance().refreshBmtWidgetDeletedState(event.getPluginId(), event.getSubcategory(), event.isDeletedState());
                    break;
            }

        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onGoNetworkConfigFragmentEvent(GoNetworkConfigFragmentEvent event) {
        if (TextUtils.isEmpty(event.getId()) || IPCManager.getInstance().getHeartLaiDeviceByID(event.getId()) == null) {
            return;
        }
        getDelegateActivity().addCommonFragment(
                ApStepHeartLaiIpcFragment.newInstance(
                        DeviceHelper.getString(IPCManager.getInstance().getHeartLaiDeviceByID(event.getId()), HeartLaiConstants.ATTR_SOURCE_DATA, "{}"),
                        false,
                        HeartLaiUtils.getSourceDataIpcType(IPCManager.getInstance().getHeartLaiDeviceByID(event.getId()))
                )
        );
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEvent(NeedGetAllHeartLaiEvent event) {
        DDLog.d(TAG, "NeedGetAllHeartLaiEvent.  RequestHeartLai: " + event.isRequestHeartLai());
        synchronized (mFetchAddDeviceLock) {
            // 心赖
            if (event.isRequestHeartLai()) {
                IPCManager.getInstance().fetchHeartLaiDevices(DeviceRequestType.ALL_DEVICE, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        synchronized (mFetchAddDeviceLock) {
                            mFetchAddDeviceLock.notify();
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        synchronized (mFetchAddDeviceLock) {
                            mFetchAddDeviceLock.notify();
                        }
                    }
                });
            }

            try {
                DDLog.i(TAG, "等待获取心赖ipc结果");
                mFetchAddDeviceLock.wait();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        runOnMainThread(() -> updateIpcListWithPluginCache(true));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHeartLaiConnectStatusChangeEvent(HeartLaiConnectStatusChangeEvent event) {
        DDLog.d(TAG, "HeartLaiConnectStatusChangeEvent: " + event.getId() + " /status:" + event.getConnectStatus());
        updateIpcListWithPluginCache(false);
        DeviceCallBackManager.getInstance().refreshCamConnectStatus(event.getId(), event.getDevice());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onDsCamConnectStatusChangeEvent(DsCamStatusChange event) {
        DDLog.d(TAG, "onDsCamConnectStatusChangeEvent: " + event.getDeviceID());
        DeviceCallBackManager.getInstance().refreshCamConnectStatus(event.getDeviceID(), null);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(DeviceLoadedStateChangedEvent event) {
        DDLog.d(TAG, "DeviceLoadedStateChangedEvent: " + event);
        if (PluginConstants.TYPE_DSCAM.equals(event.getDeviceType())
                || PluginConstants.TYPE_HEART_LAI.equals(event.getDeviceType())) {
            if (!isBackground) {
                DeviceCallBackManager.getInstance().refreshCamConnectStatus(event.getDeviceId(), null);
            }
        } else if (PluginConstants.TYPE_BMT.equals(event.getDeviceType())) {
            DeviceCallBackManager.getInstance().updateBmtLoadedStatus(event.getDeviceId(), event.getSubcategory());
        } else if (PluginConstants.TYPE_PANEL.equals(event.getDeviceType())) {
            DeviceCallBackManager.getInstance().onLoadedInfoUpdate();
        } else if (PluginConstants.TYPE_SHORTCUT.equals(event.getDeviceType())) {
            DeviceCallBackManager.getInstance().onShortcutLoadedUpdate(event.getDeviceId(), event.isLoaded());
        } else if (PluginConstants.TYPE_DOOR_SENSOR.equals(event.getDeviceType())) {
            DeviceCallBackManager.getInstance().onPluginLoadedUpdate(event.getDeviceId(), event.isLoaded());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AppStatePreEvent event) {
        DDLog.d(TAG, "AppStatePreEvent: " + event.isBackground());
        isBackground = event.isBackground();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(IpcSyncTimezoneEvent event) {
        DDLog.d(TAG, "onEvent, IpcSyncedTimezoneEvent: " + event.getDeviceId());
        if (event.isRequest()) {
            return;
        }
        pluginWidgetAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIPCInfoChangeEvent(IPCInfoChangeEvent event) {
        DDLog.d(TAG, "onIPCInfoChangeEvent: ");
        closeTimeOutLoadinFramgmentWithErrorAlert();
        DeviceCallBackManager.getInstance().refreshCamConnectStatus(event.getId(), null);

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIPCDelEvent(IPCDelEvent event) {
        DDLog.i(TAG, "IPCDelEvent: id : " + event.getId());
        deleteWidget(event.getId());
        EventBus.getDefault().post(new NeedGetAllDeviceEvent(false, false, true));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(IPCListUpdateEvent event) {
        DDLog.i(TAG, "IPCListUpdateEvent: id : " + event.getDeviceId() + " getDeviceType: "
                + event.getDeviceType() + " getOperationType: " + event.getOperationType());
        if (event.getOperationType() == IPCListUpdateEvent.OPERATION_DELETE) {
            deleteWidget(event.getDeviceId());
        } else if (event.getOperationType() == IPCListUpdateEvent.OPERATION_ADD) {
            // IPCManager.getInstance().connectAllDevice();
        } else {
            runOnMainThread(() -> updateIpcListWithPluginCache(true));
        }
    }

    @Subscribe
    public void onEvent(BmtChipsStatusReloadEvent ev) {
        final String deviceId = ev.getDeviceId();
        if (TextUtils.isEmpty(deviceId)) {
            return;
        }

        final Device bmtDev = BmtManager.getInstance().getDeviceById(deviceId, ev.getSubcategory());
        if (null != bmtDev) {
            final Map<String, String> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_CHIPS_STATUS);
            bmtDev.submit(params);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BmtShowUpdateDialogEvent event) {
        if (!HomeManager.getInstance().isAdmin()) {
            DDLog.e(TAG, "非管理员不能进入升级界面");
            return;
        }
        DDLog.i(TAG, "onEvent, BmtShowUpdateDialogEvent: " + event);
        final String deviceId = event.getDeviceId();
        final String subcategory = event.getSubcategory();
        final int chipsStatus = event.getChipStatus();
        final Device bmtDev = BmtManager.getInstance().getDeviceById(deviceId, subcategory);
        final boolean needShowUpgrade = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//        final boolean waitForUpgrade = BmtUtil.isWaitForUpdate(chipsStatus);
        final boolean upgrading = BmtUtil.isUpdating(chipsStatus);
        if (null == bmtDev || !needShowUpgrade) {
            return;
        }

        if (upgrading) {
            // 正在升级中，直接跳转
            getDelegateActivity().addCommonFragment(PSFirmWareVersionFragment.newInstance(deviceId, subcategory));
            return;
        }

        final String msg = BmtUtil.isForceUpdate(chipsStatus) ?
                Local.s(getString(R.string.ps_firmware_version_upgrade_forced))
                        .replace("#plugin", DeviceHelper.getString(bmtDev, DinConst.INFO_NAME, ""))
                : Local.s(getString(R.string.ps_firmware_version_upgrade))
                .replace("#plugin", DeviceHelper.getString(bmtDev, DinConst.INFO_NAME, ""));
        AlertDialog builder = AlertDialog.createBuilder(getContext())
                .setContent(msg)
                .setOk(Local.s(getResources().getString(R.string.ps_firm_update)))
                .setOKListener(() -> getDelegateActivity().addCommonFragment(PSFirmWareVersionFragment.newInstance(deviceId, subcategory)))
                .setCancel(Local.s(getResources().getString(R.string.ps_firm_ignore)))
                .setCancelListener(() -> {
//                    if (waitForUpgrade) {
//                        BmtManager.getInstance().addIgnoredUpgrade(deviceId);
//                    }
                })
                .preBuilder();
        builder.show();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(MultiPlayDsCamIdsChangeEvent event) {
        DDLog.i(TAG, "MultiPlayDsCamIdsChangeEvent.");
        runOnMainThread(() -> DeviceCallBackManager.getInstance().refreshMultiScreen(event.getMultiScreenId()));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BmtListUpdateEvent event) {
        DDLog.i(TAG, "BmtListUpdateEvent: id : " + event.getDeviceId()
                + " getOperationType: " + event.getOperationType());
        if (event.getOperationType() == BmtListUpdateEvent.OPERATION_DELETE) {
            deleteWidget(event.getDeviceId() + MainWidgetListProvider.UNDERLINE + event.getSubcategory());
        }
    }

    //********************* BMT-START****************************//

    /**
     * BMT状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        DDLog.i(TAG, "BmtDeviceStatusChange" + event.toString());
        DeviceCallBackManager.getInstance().updateBmtStatus(event);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGraphicUpdateEvent event) {
        DDLog.i(TAG, "BmtCurrentUpdateEvent: " + event.toString());
        DeviceCallBackManager.getInstance().updateBmtCurrent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BatteryStatusEvent event) {
        DDLog.d(TAG, "BatteryStatusEvent 状态" + event.getStatus());
        DeviceCallBackManager.getInstance().updateBmtBatteryStatus(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ChargeModeEvent event) {
        DDLog.d(TAG, "ChargeModeEvent");
        DeviceCallBackManager.getInstance().updateChargeMode(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onPushBmtExceptionEventMainThread(PushBmtExceptionEvent event) {
        DDLog.e(TAG, "onPushBmtExceptionEventMainThread推送消息 : " + event.getMessage());
        EventBus.getDefault().removeStickyEvent(event);
        String content = event.getMessage();
        String title = event.getTitle();
        String deviceId = event.getDeviceId();
        String subcategory = event.getSubcategory();
        String homeId = event.getHomeId();
        String name = BmtManager.getInstance().getDeviceNameById(deviceId, subcategory);
        showBmtExceptionDialog(deviceId, subcategory, homeId, name, title, content, true);
        otherOperate(event.getCmd(), deviceId, subcategory);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onPushBmtEVEvent(PushBmtEVEvent event) {
        EventBus.getDefault().removeStickyEvent(event);
        String cmd = event.getCmd();
        String homeId = event.getHomeId();
        String deviceId = event.getDeviceId();
        String subcategory = event.getSubcategory();
        String title = event.getTitle();
        String subtitle = event.getSubtitle();
        String name = BmtManager.getInstance().getDeviceNameById(deviceId, subcategory);
        showBmtEVEventDialog(cmd, homeId, title, subtitle,
                name, deviceId, subcategory);
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onBmtExceptionEventMainThread(BmtExceptionEvent event) {
//        DDLog.i(TAG, "BmtExceptionEvent" + event.toString());
//        if (!DDSystemUtil.isAppOnForeground(getActivity()) || HomeManager.getInstance().isGuest())
//            return;
//        String deviceId = event.getDeviceId();
//        Device device = BmtManager.getInstance().getDeviceById(deviceId);
//        final String cmd = event.getCmd();
//        Map<String, Object> result = (Map<String, Object>) MapUtils.get(event.getData(), PSKeyConstant.RESULT, null);
//        if (device != null) {
//            String name = BmtManager.getDeviceName(device);
//            String familyName = HomeManager.getInstance().getCurrentHome().getHomeName();
//            if (result != null) {
//                if (DsCamCmd.INVERTER_EXCEPTION.equals(cmd)) {
//                    List<WarningBean> warningBeanForInverter = ExceptionWarning.createWarningBeanForInverter(getContext(), result);
//                    if (warningBeanForInverter.size() > 0) {
//                        for (WarningBean warningBean : warningBeanForInverter) {
//                            if (warningBean != null) {
//                                String title = Local.s(warningBean.getTitle());
//                                String content = Local.s(warningBean.getContent());
//                                content = content.replace(getContext().getString(R.string.well_device_name), name)
//                                        .replace(getContext().getString(R.string.well_family), familyName);
//                                showBmtExceptionDialog(deviceId, name, title, content, true);
//                            }
//                        }
//                    }
//                } else {
//                    List<Integer> exceptions = (List<Integer>) MapUtils.get(result, PSKeyConstant.EXCEPTIONS, null);
//                    if (CollectionUtil.isListNotEmpty(exceptions)) {
//                        for (Integer exception : exceptions) {
//                            WarningBean warningBean = ExceptionWarning.createWarningBean(getContext(), event.getCmd(), exception);
//                            if (warningBean != null) {
//                                String title = Local.s(warningBean.getTitle());
//                                String content = Local.s(warningBean.getContent());
//                                content = content.replace(getContext().getString(R.string.well_device_name), name)
//                                        .replace(getContext().getString(R.string.well_family), familyName);
//                                showBmtExceptionDialog(deviceId, name, title, content, true);
//                            }
////                        String msg = event.getCmd() + ": " + exception;
////                        postDialogMessage(msg);
//                        }
//                    }
//                }
//            }
//        }
//
//    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onEventMainThreadBatteryStatusInfo(BatteryStatusInfoEvent event) {
//        String deviceId = event.getDeviceId();
//        int socState = event.getSocState();
//        Device device = BmtManager.getInstance().getDeviceById(deviceId);
//        String name = BmtManager.getDeviceName(device);
//        /**
//         *  full = 0,                // 满电
//         * 	lowSOCAlert,            // 低电提醒（11%～15%）
//         * 	lowSOCWarning,        // 低电警告（6%～10%）
//         * 	lowSOCProtect,        // 低电保护（0%～5%）
//         */
//        switch (socState) {
//            case 0:
//                getMainActivity().showTopToast(Local.s(getString(R.string.bmt_exception_key_9001)), "");
//                break;
//
//            case 1:
//            case 2:
//            case 3:
//                String content = Local.s(getString(R.string.bmt_exception_content_6));
//                content = content.replace(getString(R.string.well_device_name), name)
//                        .replace(getString(R.string.well_family), ExceptionWarning.currentFamily);
//                showBmtExceptionDialog("", name, Local.s(getString(R.string.bmt_exception_key_9002)), content, true);
//                break;
//        }
//    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onEventMainThreadBatteryIndexChange(BatteryIndexChangerEvent event) {
//        String deviceId = event.getDeviceId();
//        Device device = BmtManager.getInstance().getDeviceById(deviceId);
//        String name = BmtManager.getDeviceName(device);
//        int cabinetIndex = event.getCabinetIndex();
//        int cabinetPositionIndex = event.getCabinetPositionIndex();
//        boolean isAdd = event.isAdd();
//        String cabinetIndexStr = (cabinetIndex + 1) < 10 ? "0" + (cabinetIndex + 1) : "" + (cabinetIndex + 1);
//        String cabinetPositionIndexStr = (cabinetPositionIndex + 1) + "";
//        String title = Local.s(isAdd ? getString(R.string.bmt_exception_key_9003) : getString(R.string.bmt_exception_key_9004));
//        title = title.replace(getString(R.string.hashtag_battery_index), cabinetIndexStr + "_" + cabinetPositionIndexStr);
//        String content = Local.s(getString(R.string.bmt_exception_content_6));
//        content = content.replace(getString(R.string.well_device_name), name)
//                .replace(getString(R.string.well_family), ExceptionWarning.currentFamily);
//        showBmtExceptionDialog(deviceId, name, title, content, false);
//
//    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onEventMainThreadBatteryAccessoryStateChange(BatteryAccessoryStateChangedEvent event) {
//        String deviceId = event.getDeviceId();
//        Device device = BmtManager.getInstance().getDeviceById(deviceId);
//        String name = BmtManager.getDeviceName(device);
//        int index = event.getIndex();
//        boolean isHeating = event.isHeating();
//        String title = Local.s(isHeating ? getString(R.string.bmt_exception_key_9009) : getString(R.string.bmt_exception_key_9010));
//        int cabinetIndex = event.getCabinetIndex();
//        int cabinetPositionIndex = event.getCabinetPositionIndex();
//        String cabinetIndexStr = (cabinetIndex + 1) < 10 ? "0" + (cabinetIndex + 1) : "" + (cabinetIndex + 1);
//        String cabinetPositionIndexStr = (cabinetPositionIndex + 1) + "";
//        title = title.replace(getString(R.string.hashtag_battery_index), cabinetIndexStr + "_" + cabinetPositionIndexStr);
//        String content = Local.s(getString(R.string.bmt_exception_content_6));
//        content = content.replace(getString(R.string.well_device_name), name)
//                .replace(getString(R.string.well_family), ExceptionWarning.currentFamily);
////        showBmtExceptionDialog(deviceId, name, title, content, false);
//        getMainActivity().showTopToast(title, "");
//    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMainHomePowerStation(HomePowerStationDefaultEvent event) {
        String deviceId = event.getDeviceId();
        String subcategory = event.getSubcategory();
        MainBmtV2Model mainBmtModel = MainWidgetListProvider.getInstance().getMainBmtModelByDeviceId(deviceId, subcategory);
        if (mainBmtModel != null) {
            mainBmtModel.setPowerStationDefaultView();
        }

        MainBatteryModel mainBatteryModel = MainWidgetListProvider.getInstance().getMainBatteryModelByDeviceId(deviceId, subcategory);
        if (mainBatteryModel != null) {
            mainBatteryModel.setPowerStationDefaultView();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReOpenEvent(ReOpenEvent event) {
        String deviceId = event.getDeviceId();
        String subcategory = event.getSubcategory();
        MainBmtV2Model mainBmtModel = MainWidgetListProvider.getInstance().getMainBmtModelByDeviceId(deviceId, subcategory);
        if (mainBmtModel != null) {
            mainBmtModel.resetInverterStatus();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtChartDataEvent event) {
        DDLog.d(TAG, "BmtChartDataEvent");
        if (event != null) {
            event.setTop(!getMainActivity().isHasFragment());
        }
        DeviceCallBackManager.getInstance().updateBmtTodayUsage(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGetFeatureEvent event) {
        DDLog.d(TAG, "BmtGetFeatureEvent");
        DeviceCallBackManager.getInstance().getFeature(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onBmtDeadlineVEvent(BmtDeadlineEvent event) {
        EventBus.getDefault().removeStickyEvent(event);
        String cmd = event.getCmd();
        String homeId = event.getHomeId();
        String deviceId = event.getDeviceId();
        String subcategory = event.getSubcategory();
        String title = event.getTitle();
        String subtitle = event.getSubtitle();
        String name = BmtManager.getInstance().getDeviceNameById(deviceId, subcategory);
        showBmtDeadlineEventDialog(homeId, title, subtitle, deviceId, subcategory);
    }
    //********************* BMT-END****************************//

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onPushTitleMessageEvent(OMSNotificationEvent event) {
        EventBus.getDefault().removeStickyEvent(event);
        String cmd = event.getCmd();
        String title = event.getTitle();
        String message = event.getMessage();
        String url = event.getUrl();
        switch (cmd) {
            case LocalKey.OMS_NOTIFICATION:
                postNotificationDialogMessage(title, message, R.color.color_white_01, R.color.color_brand_dark_03, url);
                break;
        }
    }

    //********************* 家庭通讯回调-START****************************//
    @Override
    public void onConnect() {
        DDLog.i(TAG, "On home connect.");
    }

    @Override
    public void onDisconnect(String s) {
        DDLog.i(TAG, "On home disconnect, s: " + s);

    }

    @Override
    public void onMessage(String msg) {
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                DDLog.i(TAG, "On home message, msg: " + msg);
                try {
                    JSONObject msgObj = new JSONObject(msg);
                    final String cmd = DDJSONUtil.getString(msgObj, "cmd");
                    final String homeId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.HOME_ID);
                    final String operatorUserId = DDJSONUtil.getString(msgObj, "userid");
                    final String model = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.MODEL);
                    String title = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.TITLE);
                    final String message = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.MESSAGE);
                    if (!TextUtils.isEmpty(cmd)) {
                        if (!TextUtils.isEmpty(model)) {
                            int eventType = DinConst.TYPE_BMT_HP5000.equals(model) ? PsVersion1EventCode.checkEvent(cmd) : PsVersion2EventCode.checkEvent(cmd);
                            switch (eventType) {
                                case PsVersion1EventCode.TYPE_EVENT_EXCEPTION:
                                    String deviceId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.ID);
                                    String name = BmtManager.getInstance().getDeviceNameById(deviceId, model);
                                    showBmtExceptionDialog(deviceId, model, homeId, name, title, message, true);
                                    otherOperate(cmd, deviceId, model);
                                    break;
                                case PsVersion1EventCode.TYPE_EVENT_TOAST:
                                    String toastMessage = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.MESSAGE);
                                    String toastTitle = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.TITLE);
                                    getMainActivity().showTopToast(Local.s(toastTitle));
                                    break;
                                case PsVersion1EventCode.TYPE_EVENT_EV:
                                    String evMessage = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.MESSAGE);
                                    String evDeviceId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.ID);
                                    String evTitle = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.TITLE);
                                    String evName = BmtManager.getInstance().getDeviceNameById(evDeviceId, model);
                                    showBmtEVEventDialog(cmd, homeId, evTitle, evMessage,
                                            evName, evDeviceId, model);
                                    break;
                                case PsVersion1EventCode.TYPE_EVENT_DEADLINE:
                                    String deadlineMessage = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.MESSAGE);
                                    String deadlineDeviceId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.ID);
                                    String deadlineTitle = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.TITLE);
                                    String homeName = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.HOME_NAME);
                                    String bmtName = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.BMT_NAME);
                                    StringBuilder sb = new StringBuilder();
                                    if (!TextUtils.isEmpty(homeName)) {
                                        sb.append(homeName);
                                    }
                                    if (!TextUtils.isEmpty(bmtName)) {
                                        if (!TextUtils.isEmpty(homeName)) {
                                            sb.append(", ");
                                        }
                                        sb.append(bmtName);
                                        sb.append(": ");
                                    } else {
                                        if (!TextUtils.isEmpty(homeName)) {
                                            sb.append(": ");
                                        }
                                    }
                                    sb.append(deadlineMessage);
                                    showBmtDeadlineEventDialog(homeId, deadlineTitle, sb.toString(), deadlineDeviceId, model);
                                    break;
                            }
                        }

                        switch (cmd) {
                            case HomeConstants.CMD.AUTHORITY_UPDATED:
                                onHomeMemberAuthorityUpdate(msgObj);
                                break;
                            case HomeConstants.CMD.MEMBER_DELETED:
                                onHomeMemberDelete(msgObj);
                                break;
                            case "HOME_IPC_MOTION_DETECTED":
                            case HOME_IPC_MOTION_DETECTED_EVENT:
                                handIPCMotionDetected(msgObj, false);
                                break;
                            case HomeConstants.CMD.DEVICE_UPGRADING:
                                if (null != mPanelDevice
                                        && null != mPanelDevice.getInfo()) {
                                    mPanelDevice.getInfo().put(PanelDataKey.Panel.UPGRADING, true);
                                }
                                onDeviceUpgrading(msg);
                                break;
                            case HomeConstants.CMD.DEVICE_UPGRADE_COMPLETE:
                                if (null != mPanelDevice
                                        && null != mPanelDevice.getInfo()) {
                                    mPanelDevice.getInfo().put(PanelDataKey.Panel.UPGRADING, false);
                                }
                                onDeviceUpgradeComplete(msgObj);
                                break;
                            case HomeConstants.CMD.DEVICE_DELETED:
                                onDeleteOfflinePanel(msgObj);
                                break;
                            case HomeConstants.CMD.FORCE_LOGOUT:
                                // 不用处理了，会在DinCore中退出登录并回调到MainActivity的onLogout
                                break;
                            case HomeConstants.CMD.DEVICE_ADD:
                                if (!TextUtils.isEmpty(operatorUserId)
                                        && null != DinSDK.getUserInstance().getUser()
                                        && operatorUserId.equals(DinSDK.getUserInstance().getUser().getUser_id())) {
                                    DDLog.i(TAG, "Add by self, do nothing...");
                                    break;
                                }

                                if (!getMainActivity().isApStepFragmentExit()
                                        || getMainActivity().isFragmentInTop(ApStepHeartLaiIpcFragment.class.getSimpleName())) {
                                    showLoadingFragment(LoadingFragment.BLACK);
                                    HomeManager.getInstance().refreshFamilyInfo();
                                }
                                break;

                            case DsCamCmd.DEL_IPC:
                                postDialogMessage(Local.s(getString(R.string.ipc_del_tip)).replace("#Camera_Name", DDJSONUtil.getString(msgObj, "name")));
                                break;
                            case DsCamCmd.DEL_BMT:
                                ActivityManager.get().finishAllActivityExcludeMain();
                                postDialogMessage(Local.s(getString(R.string.bmt_del_tip))
                                        .replace("#device_name", DDJSONUtil.getString(msgObj, "name"))
                                        .replace("#family", HomeManager.getInstance().getCurrentHome().getHomeName()));
                                break;
                            case HomeConstants.CMD.IPC_LOW_BATTERY:
                                if (!SettingInfoHelper.getInstance().isAdminOrUser()) {
                                    DDLog.i(TAG, "IPC_LOW_BATTERY. Guest权限不接收");
                                    return;
                                }
                                final String pid = DDJSONUtil.getString(msgObj, "pid");
                                final String msg = DDJSONUtil.getString(msgObj, "message");
                                postDialogMessage(AlertDialogManager.DialogType.IPC_LOW_BATTERY, pid, msg);
                                break;
                            case LocalKey.DOORBELL_CALL:
                                handleDoorbellCall(msgObj);
                                break;
                            case HomeConstants.CMD.FREQUENCY_IS_TOO_HIGH:
                                if (!SettingInfoHelper.getInstance().isAdminOrUser()) {
                                    DDLog.i(TAG, "FREQUENCY_IS_TOO_HIGH. Guest权限不接收");
                                    return;
                                }
                                final String ipcPid = DDJSONUtil.getString(msgObj, "ipc_pid");
                                showDsDeviceGuideTip(ipcPid, "", DsDeviceGuideTipDialog.TIP_TYPE_FREQUENCY_MOTION_DSCAM);
                                break;
                            case HomeConstants.CMD.KEYPAD_MEMBER_PWD_UNABLE:
                            case HomeConstants.CMD.KEYPAD_MEMBER_PWD_ENABLE:
                            case HomeConstants.CMD.KEYPAD_MEMBER_PWD_RESET:
                                onKeypadMemberPwdUpdate(cmd, msgObj);
                                EventBus.getDefault().post(new KeypadMemberPwdUpdatedEvent(cmd));
                                break;
                            case LocalKey.ADD_DAILY_MEMORIES:
                                showDailyMemoriesDialog(msgObj);
                                break;

                            case CommonCmdEvent.CMD.UPDATE_REGION:
                                if (!TextUtils.isEmpty(homeId) &&
                                        homeId.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                                    initFunctionData();
                                    pluginWidgetAdapter.notifyDataSetChanged();
                                }
                                break;

                            case LocalKey.OMS_NOTIFICATION:
                                if (TextUtils.isEmpty(title) && AddMoreHelper.getInstance().getFunctionMode() == AddMoreHelper.FUNCTION_MODE_BMT) {
                                    title = Local.s(getString(R.string.app_name));
                                }
                                String url = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.URL);
                                postNotificationDialogMessage(title, message, R.color.color_white_01, R.color.color_brand_dark_03, url);
                                break;
                            default:
                                DDLog.e(TAG, "Unhandled cmd: " + cmd);
                                break;
                        }
                    }
                } catch (Exception e) {
                    DDLog.e(TAG, "Error on process message from home msct!!!");
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 需要其它操作
     *
     * @param cmd
     * @param deviceId
     */
    private void otherOperate(String cmd, String deviceId, String model) {
        if (TextUtils.isEmpty(cmd) || TextUtils.isEmpty(deviceId)) return;
        if (cmd.equals(PsVersion1EventCode.EVENT_PS_0201)) {
            Device device = BmtManager.getInstance().getDeviceById(deviceId, model);
            if (device != null) {
                int phaseCount = BmtUtil.isThreePhase(device) ? 3 : 1;
                for (int i = 0; i < phaseCount; i++) {
                    final Map<String, Object> params = new HashMap<>();
                    params.put(BmtDataKey.CMD, BmtCmd.GET_INVERTER_INFO);
                    params.put(BmtDataKey.INDEX, i);
                    device.submit(params);
                }
            }
        }
    }


    /**
     * EV事件弹窗
     *
     * @param event
     * @param title
     * @param subtitle
     */
    private EVEventDialog mEVEventDialog;

    private void showBmtEVEventDialog(String event, String homeId, String title, String subtitle,
                                      String name, String deviceId, String subcategory) {
        if (getDelegateActivity().isFragmentInTop(PowerStationRoot2Fragment.class.getSimpleName())) {
            PowerStationRoot2Fragment fragment = (PowerStationRoot2Fragment) getDelegateActivity().getFragmentList().get(0);
            if (fragment.isShowWarmingDialog()) {
                // 正在显示异常列表页面
                return;
            }
        }
        int imgRes;
        switch (event) {
            case PsVersion1EventCode.EVENT_PS_9305:
            case PsVersion1EventCode.EVENT_PS_9306:
            case PsVersion2EventCode.EVENT_PS_9035:
                imgRes = R.drawable.img_ev_live_charging;
                break;

            case PsVersion1EventCode.EVENT_PS_9309:
            case PsVersion1EventCode.EVENT_PS_9310:
            case PsVersion1EventCode.EVENT_PS_9311:
            case PsVersion2EventCode.EVENT_PS_9039:
            case PsVersion2EventCode.EVENT_PS_903A:
            case PsVersion2EventCode.EVENT_PS_903B:
                imgRes = R.drawable.img_ev_live_completed;
                break;

            default:
                imgRes = R.drawable.img_ev_live_waiting;
                break;
        }
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                String subBtnText = PsVersion1EventCode.EVENT_PS_9310.equals(event) || PsVersion2EventCode.EVENT_PS_903A.equals(event) ? Local.s(getString(R.string.ignore)) : Local.s(getString(R.string.got_it));
                if (mEVEventDialog == null) {
                    mEVEventDialog = new EVEventDialog.Builder(getContext())
                            .setCoverRes(imgRes)
                            .setTitle(title)
                            .setSubtitle(subtitle)
                            .setSubBtnText(subBtnText)
                            .setCheckListener(new EVEventDialog.OnCheckListener() {
                                @Override
                                public void onCheck(EVEventDialog dialog) {
                                    DDLog.i(TAG, "homeId===" + homeId + ",CurrentHome===" + HomeManager.getInstance().getCurrentHome().getHomeID());
                                    if (!TextUtils.isEmpty(homeId) && homeId.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                                        getDelegateActivity().addCommonFragment(PSEVChargeV3Fragment.newInstance(deviceId, subcategory));
                                    }
                                    dialog.dismiss();
                                }

                                @Override
                                public void onSubBtnClick(EVEventDialog dialog) {
                                    dialog.dismiss();
                                }
                            }).build();
                    mEVEventDialog.show();
                } else {
                    mEVEventDialog.resetRes(imgRes, title, subtitle, null, subBtnText,
                            new EVEventDialog.OnCheckListener() {
                                @Override
                                public void onCheck(EVEventDialog dialog) {
                                    DDLog.i(TAG, "homeId===" + homeId + ",CurrentHome===" + HomeManager.getInstance().getCurrentHome().getHomeID());
                                    if (!TextUtils.isEmpty(homeId) && homeId.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                                        getDelegateActivity().addCommonFragment(PSEVChargeV3Fragment.newInstance(deviceId, subcategory));
                                    }
                                    dialog.dismiss();
                                }

                                @Override
                                public void onSubBtnClick(EVEventDialog dialog) {
                                    dialog.dismiss();
                                }
                            }, true);
                    mEVEventDialog.setData();
                    if (!mEVEventDialog.isShowing()) {
                        mEVEventDialog.show();
                    }
                }
            }
        });
    }

    private CommonAlertDialog mDeadlineEventDialog;

    private void showBmtDeadlineEventDialog(String homeId, String title, String subtitle, String deviceId, String subcategory) {
        if (mDeadlineEventDialog != null) {
            mDeadlineEventDialog.dismiss();
            mDeadlineEventDialog = null;
        }
        mainTittleLayout.hideChangeDeviceDialog();
        mDeadlineEventDialog = CommonAlertDialog.createBuilder(getDelegateActivity())
                .setTitleTxt(title)
                .setContentTxt(subtitle)
                .setConfirmTxt(getString(R.string.iap_renew))
                .setCancelTxt(getString(R.string.ignore))
                .setAutoDismiss(true)
                .setShowCancel(true)
                .setTitleTypeface(ResourcesCompat.getFont(getContext(), R.font.poppins_medium))
                .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                    @Override
                    public void onConfirm(CommonAlertDialog dialog) {
                        if (!TextUtils.isEmpty(homeId) && homeId.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                            getDelegateActivity().addCommonFragment(TrafficPackageServiceRenewFragment.newInstance(deviceId, subcategory, ""
                                    , 4, false));
                        }
                    }

                    @Override
                    public void onCancel(CommonAlertDialog dialog) {

                    }
                })
                .builder();
        mDeadlineEventDialog.show();
    }
    //********************* 家庭通讯回调-END****************************//

    /**
     * 删除离线主机通知
     */
    private void onDeleteOfflinePanel(JSONObject msgObj) {
        DDLog.i(TAG, "onDeleteOfflinePanel");
        try {
            String homeId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.HOME_ID);
            String message = DDJSONUtil.getString(msgObj, "message");
            if (!TextUtils.isEmpty(homeId)
                    && homeId.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                postDialogMessage(message);
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on process onDeleteOfflinePanel");
            e.printStackTrace();
        }
    }

    /**
     * 切换首页界面并通知其他-主机升级中
     */
    private void onDeviceUpgrading(String resultStr) {
        DDLog.i(TAG, "onDeviceUpgrading");
        getMainActivity().runOnUiThread(this::changeDeviceStatePanelUpgrading);
    }

    /**
     * 切换首页界面并通知其他-主机升级完成
     */
    private void onDeviceUpgradeComplete(JSONObject msgObj) {
        DDLog.i(TAG, "onDeviceUpgradeComplete");
        getMainActivity().runOnUiThread(this::changeDeviceStateNormal);
        EventBus.getDefault().post(new PanelUpgradeStateChangeEvent());
    }

    private void onHomeMemberDelete(JSONObject msgObj) {
        DDLog.i(TAG, "onHomeMemberDelete");
        try {
            // 先刷新显示的家庭列表
            EventBus.getDefault().post(new FamilyListChangeEvent());

            String homeId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.HOME_ID);
            if (HomeManager.getInstance().getHomeList().size() > 0) {
                // 还有其他房间
                if ((null == DinSDK.getHomeInstance().getCurrentHome()
                        || TextUtils.isEmpty(DinSDK.getHomeInstance().getCurrentHome().getHomeID()))) {
                    // 删除当前房间，需要切换房间
                    HomeManager.getInstance().changeFamily(0);
                }
                // 删除了其他房间，不需要处理
            }

            String message = DDJSONUtil.getString(msgObj, "message");
            postDialogMessage(message);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on onHomeMemberDelete!!!");
            e.printStackTrace();
        }
    }

    private void onHomeMemberAuthorityUpdate(JSONObject msgObj) {
        DDLog.i(TAG, "onHomeMemberAuthorityUpdate");
        try {
            getMainActivity().removeAllCommonFragment();
            getMainActivity().smoothToHome();

            String homeId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.HOME_ID);
            String message = DDJSONUtil.getString(msgObj, "message");
            if (null != DinSDK.getHomeInstance().getCurrentHome()
                    && null != DinSDK.getHomeInstance().getCurrentHome().getHomeID()
                    && DinSDK.getHomeInstance().getCurrentHome().getHomeID().equals(homeId)) {
                EventBus.getDefault().post(new UserPermissonUpdata());
            }

            postDialogMessage(AlertDialogManager.DialogType.USER_PERMISSION_CHANGED, "", message);

            DBUtil.Put(DBKey.WIDGET_FAMILY_LEVEL, HomeManager.getInstance().getCurrentHome().getLevel());
        } catch (Exception e) {
            DDLog.e(TAG, "Error on onHomeMemberAuthorityUpdate!!!");
            e.printStackTrace();
        }
    }

    private void onKeypadMemberPwdUpdate(@NonNull final String cmd, @NonNull JSONObject msgObj) {
        DDLog.i(TAG, "onKeypadMemberPwdUpdate");
        try {
            String homeId = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.HOME_ID);
            String message = DDJSONUtil.getString(msgObj, MyBaseDinsaferPushReveiver.MESSAGE);
            if (HomeConstants.CMD.KEYPAD_MEMBER_PWD_UNABLE.equals(cmd)) {
                getDelegateActivity().showTopToast(0, message);
            } else {
                postDialogMessageBlueBg(AlertDialogManager.DialogType.KEYPAD_MEMBER_PWD_UPDATE, "",
                        message, getString(R.string.got_it), null);
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on onKeypadMemberPwdUpdate!!!");
            e.printStackTrace();
        }
    }

    /**
     * 蓝底的弹窗
     */
    private void postDialogMessageBlueBg(final @AlertDialogManager.DialogType int type,
                                         final String postfix, final String msg,
                                         @Nullable final String okStr,
                                         @Nullable final AlertDialog.AlertOkClickCallback okClickListener) {
        if (null == msg) {
            DDLog.e(TAG, "Empty msg bluBg!!!");
            return;
        }

        getDelegateActivity().runOnUiThread(() -> AlertDialog.createBuilder(getDelegateActivity())
                .setAutoDissmiss(true)
                .setOk(TextUtils.isEmpty(okStr) ? getResources().getString(R.string.send_email_confirm_text) : okStr)
                .setContent(msg)
                .setType(type)
                .setPostfix(postfix)
                .setOKListener(okClickListener)
                .preBuilder()
                .show());
    }

    private void postDialogMessage(final @AlertDialogManager.DialogType int type,
                                   final String postfix, final String msg) {
        if (null == msg) {
            DDLog.e(TAG, "Empty msg!!!");
            return;
        }
        getMainActivity().runOnUiThread(() -> {
            AlertDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
                    .setContentColor(getResources().getColor(R.color.common_dialog_content))
                    .setBackgroundTint(Color.WHITE)
                    .setOk(getResources().getString(R.string.send_email_confirm_text))
                    .setContent(msg)
                    .setType(type)
                    .setPostfix(postfix)
                    .setOKListener(new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {

                        }
                    })
                    .preBuilder()
                    .show();
        });
    }

    private void postDialogMessage(final @AlertDialogManager.DialogType int type,
                                   final String postfix, final String title, final String msg, int textColor, int bgColor) {
        if (null == msg) {
            DDLog.e(TAG, "Empty msg!!!");
            return;
        }
        getMainActivity().runOnUiThread(() -> {
            AlertDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
                    .setContentColor(getResColor(textColor))
                    .setBackgroundTint(getResColor(bgColor))
                    .setOk(getResources().getString(R.string.send_email_confirm_text))
                    .setContent(msg)
                    .setTitle(title)
                    .setType(type)
                    .setPostfix(postfix)
                    .setOKListener(new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {

                        }
                    })
                    .preBuilder()
                    .show();
        });
    }

    private CommonAlertDialog commonAlertDialog;
    private String showDeviceId;

    private void showBmtExceptionDialog(String deviceId, String model, String homeId, String name, String title, String content, boolean isCheck) {
        BmtManager.getInstance().getViewExceptions(BmtManager.getInstance().getDeviceById(deviceId, model));
        if (getDelegateActivity().isFragmentInTop(PowerStationRoot2Fragment.class.getSimpleName())) {
            PowerStationRoot2Fragment fragment = (PowerStationRoot2Fragment) getDelegateActivity().getFragmentList().get(0);
            if (fragment.isShowWarmingDialog()) {
                // 正在显示异常列表页面
                return;
            }
        }
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                if (deviceId.equals(showDeviceId)
                        && commonAlertDialog != null
                        && commonAlertDialog.isShowing()) {
                    return;
                }

                commonAlertDialog = CommonAlertDialog.createBuilder(getDelegateActivity())
                        .setTitleTxt(title)
                        .setContentTxt(content)
                        .setConfirmTxt(isCheck ? getString(R.string.check) : getString(R.string.got_it))
                        .setCancelTxt(getString(R.string.ignore))
                        .setAutoDismiss(true)
                        .setShowCancel(isCheck)
                        .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                            @Override
                            public void onConfirm(CommonAlertDialog dialog) {
                                if (isCheck) {
                                    if (!TextUtils.isEmpty(homeId) && homeId.equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                                        getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstance(name, deviceId, model, PowerStationRoot2Fragment.SHOW_BMT));
                                    }
                                }
                            }

                            @Override
                            public void onCancel(CommonAlertDialog dialog) {

                            }
                        })
                        .builder();
                commonAlertDialog.show();
                showDeviceId = deviceId;
            }
        });
    }

    private void postDialogMessage(final String msg) {
        postDialogMessage(AlertDialogManager.DialogType.DEFAULT, "", msg);
    }

    private void postNotificationDialogMessage(final String title, final String msg, int textColor, int bgColor, String url) {
        if (null == msg) {
            DDLog.e(TAG, "Empty msg!!!");
            return;
        }
        getMainActivity().runOnUiThread(() -> {

            if (!TextUtils.isEmpty(url)) {
                AlertDialog.createBuilder(getDelegateActivity())
                        .setAutoDissmiss(true)
                        .setContentColor(getResColor(textColor))
                        .setBackgroundTint(getResColor(bgColor))
                        .setCancel(getResources().getString(R.string.got_it))
                        .setOk(getResources().getString(R.string.explore_more))
                        .setContent(msg)
                        .setTitle(title)
                        .setOKListener(() -> {
                            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                            getMainActivity().startActivity(intent);
                        })
                        .setType(AlertDialogManager.DialogType.DEFAULT)
                        .setPostfix("")
                        .preBuilder()
                        .show();
                return;
            }

            AlertDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
                    .setContentColor(getResColor(textColor))
                    .setBackgroundTint(getResColor(bgColor))
                    .setCancel(getResources().getString(R.string.got_it))
                    .setContent(msg)
                    .setTitle(title)
                    .setType(AlertDialogManager.DialogType.DEFAULT)
                    .setPostfix("")
                    .preBuilder()
                    .show();
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(FcmAuthorityUpdatedEvent ev) {
        DDLog.d(TAG, "On event, FcmAuthorityUpdatedEvent");
        EventBus.getDefault().removeStickyEvent(ev);
        List<Home> homeList = HomeManager.getInstance().getHomeList();
        final String targetHomeId = ev.getHomeId();
        if (!TextUtils.isEmpty(targetHomeId)
                && null != homeList && 0 < homeList.size()) {
            if (ev.isNeedRefresh()) {
                DDLog.i(TAG, "需要更新数据");
                for (int i = 0; i < homeList.size(); i++) {
                    if (targetHomeId.equals(homeList.get(i).getHomeID())) {
                        homeList.get(i).setLevel(ev.getLevel());
                        break;
                    }
                }
            }
            postDialogMessage(AlertDialogManager.DialogType.USER_PERMISSION_CHANGED, "", ev.getMessage());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(FcmKeypadMemberPwdUpdateEvent ev) {
        DDLog.d(TAG, "On event, FcmKeypadMemberPwdUpdateEvent");
        EventBus.getDefault().removeStickyEvent(ev);
        final String cmd = ev.getCmd();
        final String msg = ev.getMessage();
        if (!TextUtils.isEmpty(cmd) && !TextUtils.isEmpty(msg)) {
            if (!HomeConstants.CMD.KEYPAD_MEMBER_PWD_UNABLE.equals(cmd)) {
                postDialogMessageBlueBg(AlertDialogManager.DialogType.KEYPAD_MEMBER_PWD_UPDATE, "",
                        msg, getString(R.string.got_it), null);
            }
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(FcmIpcLowBatteryEvent ev) {
        DDLog.d(TAG, "On event, FcmIpcLowBatteryEvent");
        EventBus.getDefault().removeStickyEvent(ev);
        if (!SettingInfoHelper.getInstance().isAdminOrUser()) {
            DDLog.i(TAG, "FcmIpcLowBatteryEvent. Guest权限不接收");
            return;
        }
        String msg = ev.getMessage();
        if (TextUtils.isEmpty(msg)) {
            msg = Local.s(getString(R.string.ipc_low_battery_tip))
                    .replace("#Camera", ev.getIpcName());
        }
        postDialogMessage(AlertDialogManager.DialogType.IPC_LOW_BATTERY, ev.getPid(), msg);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(FcmMemberDeleteEvent ev) {
        DDLog.d(TAG, "On event, FcmMemberDeleteEvent");
        EventBus.getDefault().removeStickyEvent(ev);
        // 先刷新显示的家庭列表
        EventBus.getDefault().post(new FamilyListChangeEvent(true));

        List<Home> homeList = HomeManager.getInstance().getHomeList();
        final String targetHomeId = ev.getHomeId();

        if (!TextUtils.isEmpty(targetHomeId)
                && null != homeList && 0 < homeList.size()) {
            if (ev.isNeedRefresh()) {
                DDLog.i(TAG, "需要更新数据");
                for (int i = 0; i < homeList.size(); i++) {
                    if (targetHomeId.equals(homeList.get(i).getHomeID())) {
                        homeList.remove(i);
                        break;
                    }
                }

                if (0 < homeList.size()) {
                    // 还有其他房间
                    if ((null == DinSDK.getHomeInstance().getCurrentHome()
                            || TextUtils.isEmpty(DinSDK.getHomeInstance().getCurrentHome().getHomeID()))) {
                        // 删除当前房间，需要切换房间
                        HomeManager.getInstance().changeFamily(0);
                    }
                    // 删除了其他房间，不需要处理
                }
            }
            postDialogMessage(ev.getMessage());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(HadLogoutEvent ev) {
        changeMainContentType(MainPanelHelper.MAIN_CONTENT_TYPE_EMPTY);
        changeMainContentEmptyScanVisible(false);
        IPCManager.getInstance().releaseAllDevice();
        CloudStorageServiceHelper.getInstance().reset();
        TrafficPackageServiceHelper.getInstance().reset();
        MotionDownloadManager.get().cancelAll();
    }

    // @Subscribe(threadMode = ThreadMode.MAIN)
    // public void onEventMainThread(HadLoginEvent ev) {
    //     CloudStorageServiceHelper.getInstance().getServiceConfig();
    // }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(GoMotionRecordListFragmentEvent ev) {
        getDelegateActivity().addCommonFragment(IPCSosRecordListFragment.newInstance());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(NeedPermissionEvent ev) {
        goIntentSetting();
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(GoAddMoreEvent ev) {
        EventBus.getDefault().removeStickyEvent(ev);
        getDelegateActivity().addCommonFragment(AddMoreFragment.newInstance());
        getDelegateActivity().removeCommonExcludeFragment(AddMoreFragment.class.getName());
    }

    @Subscribe()
    public void onEvent(CheckNotificationStateEvent ev) {
        mCheckNotificationPermission = true;
    }

    @Override
    public void onGlobalLayout() {
    }

    private void initBannerData() {
        String homeID = HomeManager.getInstance().getCurrentHome().getHomeID();
        if (TextUtils.isEmpty(homeID)) {
            return;
        }
        DDLog.d(TAG, "initBannerData");
        DinsafeAPI.getApi().listBigBanner().enqueue(new Callback<ListBigBannerResponse>() {
            @Override
            public void onResponse(Call<ListBigBannerResponse> call, Response<ListBigBannerResponse> response) {
                if (response != null && response.body() != null && response.body().getStatus() == 1
                        && response.body().getResult() != null && response.body().getResult().size() > 0) {
                    List<ListBigBannerResponse.BigBannerBean> data = new ArrayList<>();
                    final Set<String> ignoreSet = new HashSet<>();
                    try {
                        final JSONArray ignoreList;
                        if (DBUtil.Exists(DBKey.KEY_IGNORE_TASK_ID + UserManager.getInstance().getUser().getUser_id())) {
                            ignoreList = new JSONArray(DBUtil.Str(DBKey.KEY_IGNORE_TASK_ID + UserManager.getInstance().getUser().getUser_id()));
                        } else {
                            ignoreList = new JSONArray();
                        }
                        if (ignoreList.length() > 0) {
                            for (int i = 0; i < ignoreList.length(); i++) {
                                String id = ignoreList.getString(i);
                                ignoreSet.add(id);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    for (ListBigBannerResponse.BigBannerBean model : response.body().getResult()) {
                        boolean ignored = false;
                        try {
                            ignored = ignoreSet.contains(model.getTask_id());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (!ignored) {
                            data.add(model);
                        }
                    }
                    setBannerData(data);

                } else {
                    setBannerData(null);
                }
            }

            @Override
            public void onFailure(Call<ListBigBannerResponse> call, Throwable t) {
                setBannerData(null);
            }
        });

    }

    private void setBannerData(List<ListBigBannerResponse.BigBannerBean> data) {
        DDLog.d(TAG, "setBannerData. data: " + data);
        MainWidgetListProvider.getInstance().updateBannerItem(data);
        runOnMainThread(() -> DeviceCallBackManager.getInstance().setBannerData(data));
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onNewActivityPushEvent(NewActivityEvent newActivityEvent) {
        DDLog.d(TAG, "onNewActivityPushEvent.");
        EventBus.getDefault().removeStickyEvent(newActivityEvent);
        String activityID = newActivityEvent.getActivityId();
        if (!TextUtils.isEmpty(activityID)) {
            getDelegateActivity().addCommonFragment(BetaUserClubInvitationFragment.newInstance(activityID));
        }
    }

    @Subscribe
    public void onBetaUserInviteSuccessEvent(BetaUserInviteSuccessEvent event) {
        initBannerData();
    }

    @Subscribe
    public void onNotificationLanguageChangeEvent(NotificationLanguageChangeEvent event) {
        initBannerData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ShowDsCamGuideTipEvent event) {
        showDsDeviceGuideTip(event.getDeviceId(), "", DsDeviceGuideTipDialog.TIP_TYPE_FIRST_USER_DSCAM);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ShowBmtGuideTipEvent event) {
        showDsDeviceGuideTip(event.getDeviceId(), event.getSubcategory(), DsDeviceGuideTipDialog.TIP_TYPE_FIRST_USER_BMT);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(MotionFrequencyPushEvent ev) {
        DDLog.d(TAG, "On event, MotionFrequencyPushEvent, ev: " + ev.toString());
        EventBus.getDefault().removeStickyEvent(ev);
        if (!SettingInfoHelper.getInstance().isAdminOrUser()) {
            DDLog.i(TAG, "MotionFrequencyPushEvent. Guest权限不接收");
            return;
        }
        showDsDeviceGuideTip(ev.getDeviceId(), "", DsDeviceGuideTipDialog.TIP_TYPE_FREQUENCY_MOTION_DSCAM);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEventMainThread(DailyMemoriesPushEvent ev) {
        DDLog.d(TAG, "On event, DailyMemoriesPushEvent, ev: " + ev.toString());
        EventBus.getDefault().removeStickyEvent(ev);
        try {
            showDailyMemoriesDialog(new JSONObject(ev.getExtra()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showDailyMemoriesDialog(JSONObject jsonObject) throws Exception {
        String id = DDJSONUtil.getString(jsonObject, "id");
        String home_id = DDJSONUtil.getString(jsonObject, MyBaseDinsaferPushReveiver.HOME_ID);
        String homename = DDJSONUtil.getString(jsonObject, "homename");
        String ipc_id = DDJSONUtil.getString(jsonObject, "ipc_pid");
        String provider = DDJSONUtil.getString(jsonObject, "provider");
        String ipcname = DDJSONUtil.getString(jsonObject, "ipcname");
        String cover = DDJSONUtil.getString(jsonObject, "cover");
        String time = DDSystemUtil.timeStamp2Date(DDJSONUtil.getLong(jsonObject, "gmtime") / 1000000, "yyyy.MM.dd HH:mm:ss");
        String message = DDJSONUtil.getString(jsonObject, "message");
        String eventId = DDJSONUtil.getString(jsonObject, MyBaseDinsaferPushReveiver.EVENT_ID);

        MotionDetectedPushDialog.createBuilder(ActivityController.getInstance().currentActivity())
                .setOk(getString(R.string.take_a_look))
                .setImgUrl(cover)
                .setTitle(time)
                .setContent(message)
                .setOKListener(dialog -> {
                    showTimeOutLoadinFramgmentWithErrorAlert();
                    DinHome.getInstance().getDailyMemoriesVideoUrl(id, new IDefaultCallBack2<DailyMemoriesVideoResponse>() {
                        @Override
                        public void onSuccess(DailyMemoriesVideoResponse response) {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            String url = response.getResult().getUrl();
                            Log.i(TAG, "onSuccess: " + url);
                            if (!TextUtils.isEmpty(url)) {
                                dialog.dismiss();
                                getMainActivity().setNotNeedToLogin(true);
                                DailyMemoriesVideoPlayActivity.start(getActivity(), url, "", response.getResult().getStartingTime(), id);
                            } else {
                                showErrorToast();
                            }
                        }

                        @Override
                        public void onError(int i, String s) {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showErrorToast();
                        }
                    });
                })
                .setShowLoading(false)
                .setDeviceId(ipc_id)
                .setProvider(provider)
                .preBuilder()
                .show();
    }

    public void showDsDeviceGuideTip(final String deviceId, final String sub,
                                     @DsDeviceGuideTipDialog.GuideTipType int type) {
        if (TextUtils.isEmpty(deviceId)) {
            DDLog.e(TAG, "showDsCamGuideTip, empty deviceId!!!!");
            return;
        }

        if (dscamGuideTipDialog != null && dscamGuideTipDialog.isShowing()) {
            dscamGuideTipDialog.dismiss();
            dscamGuideTipDialog = null;
        }

        dscamGuideTipDialog = new DsDeviceGuideTipDialog.Builder(getDelegateActivity(), deviceId, type)
                .setSubcategory(sub)
                .preBuilder();
        dscamGuideTipDialog.show();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(HadLogoutPreEvent event) {
        MainPanelHelper.getInstance().setPanelEditMode(false);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(GoShareQREvent event) {
        getDelegateActivity().addCommonFragment(ShareQR.newInstance(event.getQrcode(),
                HomeManager.getInstance().getCurrentHome().getHomeName(), LocalKey.FROM_INSTALLER));
    }
}
