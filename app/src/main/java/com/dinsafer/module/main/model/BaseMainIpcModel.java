package com.dinsafer.module.main.model;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.ipc.player.IVideoViewListener;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.adapter.ipc.DsCamIpcItemDelegate;
import com.dinsafer.module.settting.adapter.ipc.DsDoorbellItemDelegate;
import com.dinsafer.module.settting.adapter.ipc.HeartLaiIpcItemDelegate;
import com.dinsafer.module.settting.adapter.ipc.IPCItemDelegate;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.ui.anim.ShakeAnimUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PluginWidgetStyleUtil;

/**
 * @author: create by Sydnee
 * @date：2022/9/27
 * @describe：
 */
public abstract class BaseMainIpcModel extends BaseMainItemModel<ViewDataBinding> implements IVideoViewListener
        , OnIpcModelListener {

    private Device device;
    private MainActivity mMainActivity;
    private IPCItemDelegate heartLaiIpcItemDelegate;
    private IPCItemDelegate dsCamIpcItemDelegate;
    private IPCItemDelegate dsDoorbellItemDelegate;
    private boolean isCanEnterEditMode = false;
    protected OnWidgetItemListener onWidgetItemListener;
    private Context mContext;
    private int layoutId;
    private String cacheName;
    private MainPanelIpcItemViewHolder itemHolder;
    private int mIPCType;

    public BaseMainIpcModel(MainActivity mainActivity, Context context, Device device
            , @NonNull MainWidgetBean widgetBean, OnWidgetItemListener onWidgetItemListener,
                            boolean isCanEnterEditMode, final String modelType, final int adapterItemId, int type) {
        super(context, widgetBean, null != widgetBean ? widgetBean.getPluginId() : null, modelType, adapterItemId);
        this.mContext = context;
        this.device = device;
        this.mMainActivity = mainActivity;
        this.onWidgetItemListener = onWidgetItemListener;
        this.isCanEnterEditMode = isCanEnterEditMode;
        this.cacheName = widgetBean.getName();
        this.layoutId = PluginWidgetStyleUtil.getInstance().getWidgetStyle(widgetBean.getLayoutType());
        mIPCType = type;
        heartLaiIpcItemDelegate = new HeartLaiIpcItemDelegate(mMainActivity, showStateIcon(), cacheName);
        dsCamIpcItemDelegate = new DsCamIpcItemDelegate(mMainActivity, showStateIcon(), cacheName);
        dsDoorbellItemDelegate = new DsDoorbellItemDelegate(mMainActivity, showStateIcon(), cacheName);

    }

    public abstract boolean showStateIcon();

    public abstract boolean isCameraVideoPlayIconNull();

    @Override
    public int getLayoutID() {
        return layoutId;
    }

    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding mainPanelItemIpcBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            itemHolder = new MainPanelIpcItemViewHolder(mainPanelItemIpcBinding.getRoot(), mIPCType);
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, itemHolder);
        } else {
            itemHolder = (MainPanelIpcItemViewHolder) tag;
        }

        if (isCameraVideoPlayIconNull()) {
            itemHolder.setCameraVideoViewPlayIcon(0);
        }

        itemHolder.bindCurrentIndex(holder.getAdapterPosition());

        itemHolder.setVideoViewListener(this);
        itemHolder.setRootViewEnable(true);

        itemHolder.setIsEditMode(isCanEnterEditMode && MainPanelHelper.getInstance().isPanelEditMode());

        initListener(holder);

        if (device == null) {
            itemHolder.setRootViewLoading(cacheName);
            if (getWidgetBean().isNullDevice()) {
                // 请求数据后发现无该device
                itemHolder.setRootViewDeleted(true);
            }
            return;
        }

        itemHolder.setRootViewDeleted(device.getFlagDeleted());

        getIpcItemDelegate(device).onBindItemViewHolder(itemHolder, holder.getAdapterPosition(), device);

    }

    private void initListener(BaseViewHolder holder) {
        DeviceCallBackManager.getInstance().addOnIpcModelListener(this);

        itemHolder.setDeleteIconClickListener(view -> {
            if (null == onWidgetItemListener || !MainPanelHelper.getInstance().isPanelEditMode()) {
                return;
            }
            DeviceCallBackManager.getInstance().removeOnIpcModelListener(this);
            onWidgetItemListener.onDeleteIconClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });

        itemHolder.setRootViewLongClickListener(view -> {
            if (null != onWidgetItemListener) {
                onWidgetItemListener.onItemLongClick(MainPanelHelper.getInstance().isPanelEditMode(), holder, holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
            }
            return true;
        });

        itemHolder.setRootViewClickListener((View v) -> {
            if (null == onWidgetItemListener || !isCanEnterEditMode) {
                return;
            }
            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            onWidgetItemListener.onItemClick(MainPanelHelper.getInstance().isPanelEditMode()
                    , MainPanelHelper.SECTION_TYPE_IPC, null, getModelId(), getModelType());
        });

        itemHolder.setUnavailableStateClickListener(v -> {
            if (null == onWidgetItemListener) {
                DDLog.e(TAG, "当前Item不能点击");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            onWidgetItemListener.onUnavailableStateViewClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());
        });
    }

    private IPCItemDelegate getIpcItemDelegate(Device device) {
        if (HeartLaiUtils.isHeartLaiDevice(device)) {
            return heartLaiIpcItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSCAM)) {
            return dsCamIpcItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSDOORBELL)) {
            return dsDoorbellItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSCAM_VOO6)) {
            return dsCamIpcItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSCAM_VO15)) {
            return dsCamIpcItemDelegate;
        }
        return heartLaiIpcItemDelegate;
    }

    @Override
    public void onPlayIconClick(int position, CameraVideoView videoViewRoot, View parent) {
        getIpcItemDelegate(device).onPlayIconClick(device, position, videoViewRoot, parent);
    }

    @Override
    public void onErrorIconClick(int position, CameraVideoView videoViewRoot, View parent) {
        getIpcItemDelegate(device).onErrorIconClick(device, position, videoViewRoot, parent);
    }

    @Override
    public void onFullscreenIconClick(int position, CameraVideoView videoView, View parent) {
        getIpcItemDelegate(device).onFullscreenIconClick(device, position, videoView, parent);
    }

    @Override
    public void onViewDeleted(String pluginId, boolean flagDeleted) {
        DDLog.i(TAG, "onViewDeleted: " + pluginId + " is delete ? " + flagDeleted);
        if (TextUtils.isEmpty(pluginId) || null == device) {
            return;
        }

        if (!pluginId.equals(device.getId())) {
            return;
        }

        itemHolder.setRootViewDeleted(device.getFlagDeleted());
        getIpcItemDelegate(device).onBindItemViewHolder(itemHolder, itemHolder.getAdapterPosition(), device);
    }

    @Override
    public void checkDeviceIsNull() {
        DDLog.d(TAG, "checkDeviceIsNull: device is null ? " + (device == null));
        boolean isDeleted = false;
        if (null == device) {
            isDeleted = true;
        } else if (device.getFlagDeleted()) {
            isDeleted = true;
        }
        getWidgetBean().setNullDevice(isDeleted);
        itemHolder.setRootViewDeleted(isDeleted);
    }

    @Override
    public void onMultiPlayDsCamIdsChange(@Nullable final String multiScreenId) {

    }

    @Override
    public void onCamConnectStatus(String pluginId, Device dev) {
        DDLog.d(TAG, "onCamConnectStatus. pluginId: " + pluginId);
        if (TextUtils.isEmpty(pluginId) || null == device) {
            return;
        }

        if (!pluginId.equals(device.getId())) {
            return;
        }

        if (null != dev) {
            device = dev;
        }
        itemHolder.setRootViewDeleted(device.getFlagDeleted());
        getIpcItemDelegate(device).onBindItemViewHolder(itemHolder, itemHolder.getAdapterPosition(), device);
    }

    @Override
    public void onViewDeleted(String pluginId, String subcategory, boolean flagDeleted) {

    }
}
