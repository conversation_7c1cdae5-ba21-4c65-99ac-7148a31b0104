package com.dinsafer.module.main.model;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.MainPanelItemMoreIpcBinding;
import com.dinsafer.dscam.DsCamMultiFullPlayActivity;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.model.panel.MainPanelMultiScreenViewHolder;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.module.main.helper.MainWidgetListProvider;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.PluginWidgetStyleUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：多个ipc组合卡片
 * @date：2022/11/14
 * @author: create by Sydnee
 */
public class MainMultiScreenPluginModel extends BaseMainItemModel<MainPanelItemMoreIpcBinding> implements OnIpcModelListener {


    // 更新dsCamIds结果状态定义
    // 没有更新
    private static final int UPDATE_RESULT_DEF = 0;
    // 更新了列表但列表中的IPC都是已经被删除了的
    private static final int UPDATE_RESULT_ALL_DELETED = -1;
    // 更新了列表并且列表中的IPC都没有被删除
    private static final int UPDATE_RESULT_UPDATED = 1;

    private final ArrayList<String> dsCamIds = new ArrayList<>();
    private MainActivity mMainActivity;
    private Context mContext;
    private MainPanelMultiScreenViewHolder itemHolder;
    private OnWidgetItemListener onWidgetItemListener;


    public MainMultiScreenPluginModel(Context context, MainActivity mainActivity, @NonNull MainWidgetBean bean,
                                      OnWidgetItemListener onWidgetItemListener, final int adapterItemId) {
        super(context, bean, null != bean ? bean.getPluginId() : null, PluginWidgetStyleUtil.IPC_MORE, adapterItemId);
        this.mContext = context;
        this.mMainActivity = mainActivity;
        this.onWidgetItemListener = onWidgetItemListener;

        updateDscamIdsAndDeleteState(getModelId());
    }

    @Override
    public int getLayoutID() {
        return R.layout.main_panel_item_more_ipc;
    }

    @Override
    public void convert(BaseViewHolder holder, MainPanelItemMoreIpcBinding mBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            itemHolder = new MainPanelMultiScreenViewHolder(mBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, itemHolder);
        } else {
            itemHolder = (MainPanelMultiScreenViewHolder) tag;
        }

        itemHolder.setRootViewEnable(true);

        initListener(holder);
        itemHolder.setTitleName(Local.s(mContext.getString(R.string.multi_screen)));
        itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
        itemHolder.setIsLoading(!IPCManager.getInstance().isHadLoadDscam());

        final boolean allDeleted = IPCManager.getInstance().isHadLoadDscam() && (null == getWidgetBean() || getWidgetBean().isNullDevice());
        boolean show = !allDeleted;
        // itemHolder.setRootViewDeleted(allDeleted);
        GridLayoutManager.LayoutParams rootLayoutParam = (GridLayoutManager.LayoutParams) itemHolder.itemView.getLayoutParams();
        rootLayoutParam.height = DensityUtils.dp2px(mContext.getApplicationContext(), show ? GridLayoutManager.LayoutParams.WRAP_CONTENT : 0);
        itemHolder.itemView.setLayoutParams(rootLayoutParam);
        itemHolder.itemView.setVisibility(show ? View.VISIBLE : View.GONE);

        if (show) {
            itemHolder.createMultiScreenView(dsCamIds);
        }
    }

    private void initListener(BaseViewHolder holder) {
        itemHolder.setDeleteIconClickListener(view -> {
            if (null == onWidgetItemListener || !MainPanelHelper.getInstance().isPanelEditMode()) {
                return;
            }
            DeviceCallBackManager.getInstance().removeOnIpcModelListener(this);
            onWidgetItemListener.onDeleteIconClick(holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());

        });

        itemHolder.setRootViewLongClickListener(view -> {
            if (null != onWidgetItemListener) {
                onWidgetItemListener.onItemLongClick(MainPanelHelper.getInstance().isPanelEditMode(), holder, holder.getAdapterPosition(), getWidgetBean(), getModelId(), getModelType());

            }

            return true;
        });

        itemHolder.setRootViewClickListener((View v) -> {
            if (null == onWidgetItemListener) {
                return;
            }
            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }
            if (!IPCManager.getInstance().isHadLoadDscam()) {
                DDLog.e(TAG, "未加载完DsCamIPC数据，当前Item不能点击");
                return;
            }

            DsCamMultiFullPlayActivity.startForMultiScreenCard(mMainActivity, getModelId(), dsCamIds);
            // 非编辑模式下
            onWidgetItemListener.onItemClick(false, MainPanelHelper.SECTION_TYPE_IPC, null, getModelId(), getModelType());

        });

        itemHolder.setUnavailableStateClickListener(View -> {
            if (null == onWidgetItemListener) {
                DDLog.e(TAG, "当前Item没有对应的device，不能点击");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                DDLog.e(TAG, "编辑模式下，当前Item不能点击");
                return;
            }

            onWidgetItemListener.onUnavailableStateViewClick(holder.getAdapterPosition(),getWidgetBean(), getModelId(), getModelType());
        });

        final boolean newAdded = DeviceCallBackManager.getInstance().addOnIpcModelListener(this);
        // 如果进入直播页，首页多屏卡片重新被添加后，需要手动刷新多屏的ID
        if (newAdded) {
            DDLog.i(TAG, "addOnIpcModelListener:newAdded");
            updateDsCamIdList(getModelId());
        }
    }


    @Override
    public void onViewDeleted(String pluginId, boolean flagDeleted) {

    }

    @Override
    public void onViewDeleted(String pluginId, String subcategory, boolean flagDeleted) {

    }

    @Override
    public void checkDeviceIsNull() {

    }

    /**
     * 更新dsCamIds的列表
     *
     * @param changedMultiScreenId 当前更新的首页多屏卡片的ID
     * @return 0： 没有更新列表；
     * 1：更新并有未删除的IPC
     * -1：更新并全部的IPC已经被删除
     */
    private int updateDsCamIdList(@Nullable final String changedMultiScreenId) {
        final String myMultiScreenId = getModelId();
        DDLog.i(TAG, "updateDsCamIdList, myId: " + myMultiScreenId + ", changedId: " + changedMultiScreenId);
        if (!TextUtils.isEmpty(changedMultiScreenId) && !changedMultiScreenId.equals(myMultiScreenId)) {
            return UPDATE_RESULT_DEF;
        }

        // 没有指定更新多屏的卡片或者指定更新当前的多屏卡片，则更新当前多屏卡片管理的IPC的Id
        int result = UPDATE_RESULT_UPDATED;
        String multiScreenType;
        dsCamIds.clear();
        if (TextUtils.isEmpty(myMultiScreenId) || MainWidgetListProvider.MULTI_SCREEN_ID.equals(myMultiScreenId)) {
            dsCamIds.addAll(DsCamUtils.getMultiPlayDsCamIds());
            multiScreenType = "DEFAULT";
        } else {
            final List<String> localMultiScreenIds = DsCamUtils.getCurrentHomeLocalMultiScreenCardDsCamIds(myMultiScreenId);
            if (0 == localMultiScreenIds.size()) {
                // 当前没有缓存数据，获取默认的多屏IPC的ID列表
                final ArrayList<String> defMultiScreenIds = DsCamUtils.getDefMultiPlayDsCamIds();
                dsCamIds.addAll(defMultiScreenIds);
                multiScreenType = "No cache";
                // 首次发现没有缓存时可以选择进行缓存
                if (dsCamIds.size() > 0) {
                    DsCamUtils.saveCurrentHomeMultiScreenCardDsCamIds(myMultiScreenId, dsCamIds);
                }
            } else {
                final List<String> notDeleteLocalMultiScreenIds = DsCamUtils.filterDeletedDsCamIds(localMultiScreenIds);
                if (0 == notDeleteLocalMultiScreenIds.size()) {
                    // 缓存的IPC都被删除了，需要显示不可用的蒙蔽
                    dsCamIds.addAll(localMultiScreenIds);
                    result = UPDATE_RESULT_ALL_DELETED;
                    multiScreenType = "Cache all deleted";
                } else {
                    // 还有没有删除的IPC，直接显示
                    dsCamIds.addAll(notDeleteLocalMultiScreenIds);
                    multiScreenType = "Cache not deleted";
                }
            }
        }
        DDLog.i(TAG, "updateDsCamIdList, ready for update, myId: " + myMultiScreenId + ", changedId: " + changedMultiScreenId +
                ", type: " + multiScreenType + ", result: " + result);
        return result;
    }

    private void updateDscamIdsAndDeleteState(@Nullable final String multiScreenId) {
        // 更新ID列表
        int update = updateDsCamIdList(multiScreenId);
        if (null != getWidgetBean()) {
            if (update > UPDATE_RESULT_DEF) {
                getWidgetBean().setNullDevice(false);
            } else if (update < UPDATE_RESULT_DEF) {
                getWidgetBean().setNullDevice(true);
            }
        }
    }

    @Override
    public void onMultiPlayDsCamIdsChange(@Nullable final String multiScreenId) {
        updateDscamIdsAndDeleteState(multiScreenId);
        DDLog.d(TAG, "onMultiPlayDsCamIdsChange: new data : " + dsCamIds);
        final boolean allDeleted = IPCManager.getInstance().isHadLoadDscam() && (null == getWidgetBean() || getWidgetBean().isNullDevice());
        boolean show = !allDeleted;
        itemHolder.setIsLoading(!IPCManager.getInstance().isHadLoadDscam());

        // itemHolder.setRootViewDeleted(allDeleted);
        GridLayoutManager.LayoutParams rootLayoutParam = (GridLayoutManager.LayoutParams) itemHolder.itemView.getLayoutParams();
        rootLayoutParam.height = DensityUtils.dp2px(mContext.getApplicationContext(), show ? GridLayoutManager.LayoutParams.WRAP_CONTENT : 0);
        itemHolder.itemView.setLayoutParams(rootLayoutParam);
        itemHolder.itemView.setVisibility(show ? View.VISIBLE : View.GONE);

        if (show) {
            itemHolder.createMultiScreenView(dsCamIds);
        }
    }

    @Override
    public void onCamConnectStatus(String id, Device device) {
        if (null != itemHolder && null != dsCamIds) {
            itemHolder.createMultiScreenView(dsCamIds);
        }
    }
}
