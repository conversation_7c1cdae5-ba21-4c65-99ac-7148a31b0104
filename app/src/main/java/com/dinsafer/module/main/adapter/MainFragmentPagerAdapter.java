package com.dinsafer.module.main.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.dinsafer.module.BaseFragment;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/6/20.
 */
public class MainFragmentPagerAdapter extends FragmentPagerAdapter {

    private ArrayList<BaseFragment> mDatas;

    public MainFragmentPagerAdapter(FragmentManager fm, ArrayList<BaseFragment> datas) {
        super(fm);
        mDatas = datas;
    }

    @Override
    public Fragment getItem(int position) {
        return mDatas.get(position);
    }

    @Override
    public int getCount() {
        return mDatas.size();
    }

}
