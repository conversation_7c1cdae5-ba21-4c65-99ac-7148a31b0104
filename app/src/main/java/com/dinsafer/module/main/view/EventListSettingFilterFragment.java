package com.dinsafer.module.main.view;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentTransaction;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentEventListSettingFilterBinding;
import com.dinsafer.model.home.EventListHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.FlowLayout;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentTransaction;

/**
 * @describe：event list 过滤选择
 * @date：2023/5/25
 * @author: create by Sydnee
 */
public class EventListSettingFilterFragment extends BaseFragment implements View.OnTouchListener {

    private FragmentEventListSettingFilterBinding mBinding;
    private int mTabSize;
    private List<Boolean> markList;
    private float touchY;
    private final static int MOVE_DIS = 400;

    public static EventListSettingFilterFragment newInstance() {
        return new EventListSettingFilterFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_event_list_setting_filter, container, false);
        initData();
        initListener();
        initLocalStatus();
        return mBinding.getRoot();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
    }

    @Override
    public void initData() {
        super.initData();
        mTabSize = getMainActivity().getResources().getDimensionPixelSize(R.dimen.textMediumSize);
        mBinding.commonBarTitle.setLocalText(getString(R.string.event_list_setting));
        mBinding.commonBarConfirm.setLocalText(getString(R.string.event_list_setting_confirm));

        boolean showPowerStation = EventListHelper.getInstance().getEventTypeVisible(EventListHelper.EVENT_TYPE_POWER_STATION);
        mBinding.tvTittlePowerStation.setVisibility(showPowerStation ? View.VISIBLE : View.GONE);
        mBinding.rlTittlePowerStation.setVisibility(showPowerStation ? View.VISIBLE : View.GONE);

        boolean showSmartCamera = EventListHelper.getInstance().getEventTypeVisible(EventListHelper.EVENT_TYPE_SMART_CAMERA);
        mBinding.tvTittleSmartCamera.setVisibility(showSmartCamera ? View.VISIBLE : View.GONE);
        mBinding.rlTittleSmartCamera.setVisibility(showSmartCamera ? View.VISIBLE : View.GONE);

        boolean showAlarmSystem = EventListHelper.getInstance().getEventTypeVisible(EventListHelper.EVENT_TYPE_ALARM_SYSTEM);
        mBinding.tvTittleAlarmSystem.setVisibility(showAlarmSystem ? View.VISIBLE : View.GONE);
        mBinding.rlTittleAlarmSystem.setVisibility(showAlarmSystem ? View.VISIBLE : View.GONE);

        boolean showAccessories = EventListHelper.getInstance().getEventTypeVisible(EventListHelper.EVENT_TYPE_ACCESSORIES);
        mBinding.tvTittleAccessories.setVisibility(showAccessories ? View.VISIBLE : View.GONE);
        mBinding.rlTittleAccessories.setVisibility(showAccessories ? View.VISIBLE : View.GONE);

        boolean showFamily = EventListHelper.getInstance().getEventTypeVisible(EventListHelper.EVENT_TYPE_FAMILY);
        mBinding.tvTittleFamily.setVisibility(showFamily ? View.VISIBLE : View.GONE);
        mBinding.rlTittleFamily.setVisibility(showFamily ? View.VISIBLE : View.GONE);
    }


    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonBarConfirm.setOnClickListener(v -> removeSelf());

        mBinding.commonBarConfirm.setOnClickListener(v -> {
            int[] markType = EventListHelper.getInstance().getAllMarkType();
            for (int i = 0; i < markType.length; i++) {
                EventListHelper.getInstance().updateLocalMark(markType[i], markList.get(i));
            }
            removeSelf();
        });

        mBinding.commonBarSelectAll.setOnClickListener(v -> {
            boolean selected = mBinding.commonBarSelectAll.isSelected();
            mBinding.commonBarSelectAll.setLocalText(selected ? getString(R.string.event_list_setting_select_all) : getString(R.string.event_list_setting_clear_all));
            mBinding.commonBarSelectAll.setSelected(!selected);
            final int count = markList.size();
            markList.clear();
            for (int i = 0; i < count; i++) {
                if (EventListHelper.getInstance().checkSupportChangeMark(i)) {
                    markList.add(!selected);
                } else {
                    markList.add(false);
                }
            }
            updateStatus();
        });

        mBinding.scrollView.setOnTouchListener(this);
        mBinding.getRoot().setOnTouchListener(this);
    }

    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        TranslateAnimation animation = null;
        if (FragmentTransaction.TRANSIT_FRAGMENT_CLOSE == transit) {
            if (!enter) {
                animation = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0,
                        Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 1);
            }
        }
        if (animation == null) {
            animation = new TranslateAnimation(0, 0, 0, 0);
        }
        animation.setDuration(300);
        return animation;
    }

    private void initLocalStatus() {
        int localStatus = EventListHelper.getInstance().getLocalFilter();
        int[] markType = EventListHelper.getInstance().getAllMarkType();
        markList = new ArrayList<>();
        boolean allSelected = true;
        for (int i = 0; i < markType.length; i++) {
            if (((markType[i] & localStatus) == 0)
                    && EventListHelper.getInstance().checkSupportChangeMark(i)) {
                allSelected = false;
            }
            markList.add((markType[i] & localStatus) != 0);
        }
        mBinding.commonBarSelectAll.setLocalText(allSelected ? getString(R.string.event_list_setting_clear_all) : getString(R.string.event_list_setting_select_all));
        mBinding.commonBarSelectAll.setSelected(allSelected);
        updateStatus();
    }


    private void updateStatus() {
        if (markList == null || markList.size() < EventListHelper.getInstance().getAllMarkType().length) {
            DDLog.e(TAG, "markList is error ！");
            return;
        }
        //这里的顺序要跟EventListHelper.mMarkType中的顺序对应
        mBinding.powerStationFlowlayout.removeAllViews();
        mBinding.powerStationFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_exceptions), markList.get(0), 0));
        mBinding.powerStationFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_outage_power_backup), markList.get(1), 1));
        mBinding.powerStationFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_normal_events), markList.get(2), 2));

        mBinding.smartCameraFlowlayout.removeAllViews();
        mBinding.smartCameraFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_normal_events), markList.get(4), 4));
        mBinding.smartCameraFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_motion_detection), markList.get(3), 3));
        if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE) {
            mBinding.smartCameraFlowlayout.addView(createTabView(getResources().getString(R.string.ipc_daily_memories), markList.get(12), 12));
        }

        mBinding.alarmSystemFlowlayout.removeAllViews();
        mBinding.alarmSystemFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_alarm), markList.get(5), 5));
        mBinding.alarmSystemFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_security_status), markList.get(6), 6));
        mBinding.alarmSystemFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_door_window_status), markList.get(7), 7));
        mBinding.alarmSystemFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_tamper_triggered), markList.get(8), 8));
        mBinding.alarmSystemFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_normal_events), markList.get(9), 9));

        mBinding.accessoriesFlowlayout.removeAllViews();
        mBinding.accessoriesFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_accessories_status), markList.get(10), 10));

        mBinding.familyFlowlayout.removeAllViews();
        mBinding.familyFlowlayout.addView(createTabView(getResources().getString(R.string.event_list_setting_normal_events), markList.get(11), 11));
    }

    public TextView createTabView(final String text, boolean isBlue, int tag) {
        TextView tv = new TextView(getMainActivity());
        tv.setTag(tag);
        tv.setText(Local.s(text));
        tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTabSize);
        tv.setSelected(isBlue);
        tv.setBackgroundResource(isBlue ? R.drawable.filter_sel : R.drawable.filter_nol);
        tv.setTextColor(isBlue ? getResources().getColor(R.color.text_brand) : getResources().getColor(R.color.color_white_02));

        FlowLayout.LayoutParams lap = new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT,
                FlowLayout.LayoutParams.WRAP_CONTENT);
        final int paddingHorizontal = 20;
        final int paddingVertical = 15;
        lap.setMargins(10, paddingVertical, 10, paddingVertical);
        tv.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        tv.setLayoutParams(lap);
        tv.setOnClickListener(v -> {
            final boolean selected = !v.isSelected();
            if (v.getTag() instanceof Integer) {
                final int index = (int) v.getTag();
                if (index < markList.size()) {
                    markList.add(index, selected);
                    markList.remove(index + 1);
                }
                v.setBackgroundResource(selected ? R.drawable.filter_sel : R.drawable.filter_nol);
                ((TextView) v).setTextColor(selected ? getResources().getColor(R.color.text_brand) : getResources().getColor(R.color.color_white_02));
                v.setSelected(selected);
                checkAllSelected();
            }
        });

        return tv;
    }

    private void checkAllSelected() {
        if (CollectionUtil.isListEmpty(markList)) return;
        boolean allSelected = true;
        for (int i = 0; i < markList.size(); i++) {
            if (EventListHelper.getInstance().checkSupportChangeMark(i) && markList.get(i) == false) {
                allSelected = false;
                break;
            }
        }
        mBinding.commonBarSelectAll.setLocalText(allSelected ? getString(R.string.event_list_setting_clear_all) : getString(R.string.event_list_setting_select_all));
        mBinding.commonBarSelectAll.setSelected(allSelected);
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                touchY = event.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                if (event.getY() - touchY > MOVE_DIS) {
                    removeSelf();
                }
                break;
        }
        return true;
    }
}
