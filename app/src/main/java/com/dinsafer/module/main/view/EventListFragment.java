package com.dinsafer.module.main.view;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentEventListBinding;
import com.dinsafer.model.home.EventListHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.daily.DailyMemoriesVideoPlayActivity;
import com.dinsafer.module.main.adapter.EventListAdapter;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.EventListByFilterBean;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.scwang.smart.refresh.footer.ClassicsFooter;
import com.scwang.smart.refresh.header.ClassicsHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

/**
 * EventList显示页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/27 12:24 PM
 */
public class EventListFragment extends BaseFragment {

    private FragmentEventListBinding mBinding;

    private EventListAdapter mainFragmentListAdapter;
    private ArrayList<EventListByFilterBean> dataList;

    private SimpleDateFormat mDateFormat;

    private String mToday;
    private String mYesterday;

    private String mTodayDate, mYesterdayDate;

    private boolean isLoadMore;
    private int mLastFilter = -1;

    public static EventListFragment newInstance() {
        return new EventListFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_event_list,
                container, false);
        initListener();
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        showTimeOutLoadinFramgmentWithErrorAlert();
        requestEventListData(false);
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonBarBack.setOnClickListener((View v) -> {
            removeSelf();
        });
        mBinding.commonBarRight.setOnClickListener((View v) -> {
            mLastFilter = EventListHelper.getInstance().getLocalFilter();
            getMainActivity().addCommonFragment(EventListSettingFilterFragment.newInstance());
        });
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        if (-1 != mLastFilter && mLastFilter != EventListHelper.getInstance().getLocalFilter()) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            requestEventListData(false);
        }
        setFilterStatus();
    }

    private void setFilterStatus() {
        int localStatus = EventListHelper.getInstance().getLocalFilter();
        int[] markType = EventListHelper.getInstance().getAllMarkType();
        boolean allSelected = true;
        for (int i = 0; i < markType.length; i++) {
            if (((markType[i] & localStatus) == 0)
                    && EventListHelper.getInstance().checkSupportChangeMark(i)) {
                allSelected = false;
            }
        }
        Drawable drawable = getResources()
                .getDrawable(R.drawable.icon_nav_filter);
        drawable.setTint(allSelected ? getResources().getColor(R.color.color_white_01) : getResources().getColor(R.color.text_brand));
        mBinding.commonBarRight.setImageDrawable(drawable);
    }

    @Override
    public void initData() {
        super.initData();
        EventListHelper.getInstance().updateEventTypeVisible();
        EventListHelper.getInstance().updateEventSonTypeVisible(false);
        initDate();

        mBinding.commonBarTitle.setLocalText(getString(R.string.event_list));

        initListView();
        setFilterStatus();
    }

    /**
     * 初始化用于比较的日期
     */
    private void initDate() {
        mToday = getContext().getString(R.string.today);
        mYesterday = getContext().getString(R.string.yesterday);

        mDateFormat = new SimpleDateFormat("yyyy.MM.dd");

        Calendar calendar = Calendar.getInstance();
        mTodayDate = mDateFormat.format(calendar.getTime());
        calendar.add(Calendar.DATE, -1);
        mYesterdayDate = mDateFormat.format(calendar.getTime());
        DDLog.i(TAG, "Today: " + mTodayDate + ", Yesterday: " + mYesterdayDate);
    }

    /**
     * 初始化ListView
     */
    private void initListView() {
        DDLog.i(TAG, "initListView");

        dataList = new ArrayList<>();
        mainFragmentListAdapter = new EventListAdapter(getActivity(), dataList);
        mBinding.mainFragmentListview.setAdapter(mainFragmentListAdapter);
        mainFragmentListAdapter.setOnItemClickListener(new EventListAdapter.OnItemClickListener<EventListByFilterBean>() {
            @Override
            public void onItemClick(int pos, EventListByFilterBean data) {
                Log.i(TAG, "onItemClick: " + pos + ", data:" + data.toString());
                requestDailyMemoriesVideoUrl(pos, data.getRid());
            }
        });

        ClassicsHeader header = new CommonRefreshHeader(getContext());
        header.setEnableLastTime(false);
        header.setAccentColor(getResources().getColor(R.color.white));
        mBinding.refreshLayout.setRefreshHeader(header);
        ClassicsFooter footer = new CommonRefreshFooter(getContext());
        footer.setAccentColor(getResources().getColor(R.color.white));
        mBinding.refreshLayout.setRefreshFooter(footer);
        mBinding.refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshlayout) {
                Log.d(TAG, "onRefresh: ");
                requestEventListData(false);
            }
        });
        mBinding.refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(RefreshLayout refreshlayout) {
                Log.d(TAG, "onLoadMore: ");
                requestEventListData(true);
            }
        });
    }

    private void requestDailyMemoriesVideoUrl(int position, String rid) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinHome.getInstance().getDailyMemoriesVideoUrl(rid, new IDefaultCallBack2<DailyMemoriesVideoResponse>() {
            @Override
            public void onSuccess(DailyMemoriesVideoResponse response) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                String url = response.getResult().getUrl();
                Log.i(TAG, "onSuccess: " + url);
                if (!TextUtils.isEmpty(url)) {
                    getMainActivity().setNotNeedToLogin(true);
                    DailyMemoriesVideoPlayActivity.start(getActivity(), url, "", response.getResult().getStartingTime(), rid);
                } else {
                    showErrorToast(Local.s(getString(R.string.got_it)), Local.s(getString(R.string.Unavailable)));
                    removeEventListByFilterBeanItem(position);
                }
            }

            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast(Local.s(getString(R.string.got_it)), Local.s(getString(R.string.Unavailable)));
                removeEventListByFilterBeanItem(position);
            }
        });

    }

    /**
     * 删除某一项事件
     * @param position
     */
    private void removeEventListByFilterBeanItem(int position) {
        if (null == dataList || dataList.size() == 0) {
            return;
        }
        if (0 > position || dataList.size() < position) {
            return;
        }

        dataList.remove(position);
        List<EventListAdapter.SectionInfo> sectionInfos = new ArrayList<>();
        processEventListData(dataList, sectionInfos);
        mainFragmentListAdapter.updateSectionInfo(sectionInfos, true);
    }


    /**
     * @param isLoadMore true:上拉加载更多，false: 刷新
     *                   请求EventList数据
     */
    private void requestEventListData(boolean isLoadMore) {
        DDLog.i(TAG, "requestEventListData, isLoadMore: " + isLoadMore);
        this.isLoadMore = isLoadMore;

        long lastTime = System.currentTimeMillis() * 1000 * 1000;
        if (isLoadMore && dataList.size() > 0) {
            lastTime = dataList.get(dataList.size() - 1).getTime();
        }


        DinHome.getInstance().getEventListDataByFilter(20, lastTime, EventListHelper.getInstance().getRequestFilterParams().toString(), new IDefaultCallBack2<EventListByFilterEntry>() {
            @Override
            public void onSuccess(EventListByFilterEntry eventListEntry) {
                if (!EventListFragment.this.isAdded()) {
                    DDLog.e(TAG, "Error, EventListFragment is not add.");
                    return;
                }

                DDLog.e(TAG, "Error, EventListFragment is not add." + eventListEntry.toString());

                List<EventListByFilterBean> eventList = eventListEntry.getResult().getEvent_list();
                if (isLoadMore) {
                    // 加载更多
                    if (CollectionUtil.isListEmpty(eventList)) {
                        mBinding.refreshLayout.finishLoadMore(0, true, true);
                        return;
                    }

                    List<EventListAdapter.SectionInfo> sectionInfos = new ArrayList<>();
                    boolean success = processEventListData(eventList, sectionInfos);

                    if (success) {
                        dataList.addAll(eventList);
                        mainFragmentListAdapter.updateSectionInfo(sectionInfos, false);
                        mBinding.refreshLayout.finishLoadMore(true);
                    } else {
                        mBinding.refreshLayout.finishLoadMore(false);
                        DDLog.e(TAG, "Error on update load more ui.");
                    }
                } else {
                    // 刷新
                    if (CollectionUtil.isListEmpty(eventList)) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        mBinding.refreshLayout.finishRefresh(true);
                        showEmptyView();
                        return;
                    }

                    mBinding.flEmpty.setVisibility(View.GONE);
                    mBinding.refreshLayout.setVisibility(View.VISIBLE);
                    List<EventListAdapter.SectionInfo> sectionInfos = new ArrayList<>();
                    dataList.clear();
                    boolean success = processEventListData(eventList, sectionInfos);
                    if (success) {
                        dataList.addAll(eventList);
                        mainFragmentListAdapter.updateSectionInfo(sectionInfos, true);

                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        mBinding.refreshLayout.finishRefresh(true);
                    } else {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                        mBinding.refreshLayout.finishRefresh(false);
                        DDLog.e(TAG, "Error on refresh ui.");
                    }
                }
            }

            @Override
            public void onError(int i, String s) {
                if (!EventListFragment.this.isAdded()) {
                    DDLog.e(TAG, "Error, EventListFragment is not add.");
                    return;
                }
                DDLog.e(TAG, "Error i: " + i + ", msg: " + s);

                if (isLoadMore) {
                    mBinding.refreshLayout.finishLoadMore(false);
                } else {
                    mBinding.refreshLayout.finishLoadMore(false);
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showErrorToast();
                }
            }
        });
    }


    /**
     * 处理EventList的数据
     *
     * @param eventListDatas Event数据
     * @param sectionInfos   分节信息数据
     */
    private boolean processEventListData(List<EventListByFilterBean> eventListDatas,
                                         List<EventListAdapter.SectionInfo> sectionInfos) {
        DDLog.i(TAG, "processEventListData");
        if (null == eventListDatas
                || 0 >= eventListDatas.size()
                || null == sectionInfos) {
            DDLog.e(TAG, "Param Error.");
            return false;
        }

        // 排序
        Collections.sort(eventListDatas, EventListHelper.getInstance().getEventComparator());

        sectionInfos.clear();
        EventListByFilterBean item;
        EventListAdapter.SectionInfo sectionInfo = null;
        for (int i = 0; i < eventListDatas.size(); i++) {
            item = eventListDatas.get(i);
            String date = mDateFormat.format(item.getTime() / 1000000);
            String sectionTittle;
            if (mTodayDate.equals(date)) {
                sectionTittle = mToday;
            } else if (mYesterdayDate.equals(date)) {
                sectionTittle = mYesterday;
            } else {
                sectionTittle = date;
            }

            if (null != sectionInfo && sectionInfo.getTittle().equals(sectionTittle)) {
                // 同一个分组，追加数量
                sectionInfo.setCount(sectionInfo.getCount() + 1);
            } else {
                // 不同分组，创建新的分组信息
                sectionInfo = new EventListAdapter.SectionInfo(1, sectionTittle);
                sectionInfos.add(sectionInfo);
            }
        }

        return true;
    }

    /**
     * 显示空视图
     */
    private void showEmptyView() {
        DDLog.i(TAG, "showEmptyView");
        mBinding.flEmpty.setVisibility(View.VISIBLE);
        mBinding.refreshLayout.setVisibility(View.GONE);
    }

}
