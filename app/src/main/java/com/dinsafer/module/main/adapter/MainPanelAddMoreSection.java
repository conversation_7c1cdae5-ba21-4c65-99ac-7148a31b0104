package com.dinsafer.module.main.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.MainPanelAddMoreViewHolder;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.addmore.view.AddMoreFragment;
import com.dinsafer.module.addmore.view.AddMoreHelper;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.ui.ScannerActivity;
import com.dinsafer.util.DDLog;

import java.lang.ref.WeakReference;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/**
 * 首页有主机时底部的AddMore
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/30 4:20 PM
 */
public class MainPanelAddMoreSection extends StatelessSection {
    private static final String TAG = MainPanelAddMoreSection.class.getSimpleName();

    private final WeakReference<MainActivity> mWeakMainActivity;

    public MainPanelAddMoreSection(MainActivity mainActivity) {
        super(SectionParameters.builder()
                .itemResourceId(R.layout.main_section_panel_item_add_more)
                .build());
        mWeakMainActivity = new WeakReference<>(mainActivity);
    }

    @Override
    public int getContentItemsTotal() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        return new MainPanelAddMoreViewHolder(view);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        DDLog.i(TAG, "onBindItemViewHolder");
        MainPanelAddMoreViewHolder itemHolder = (MainPanelAddMoreViewHolder) holder;
        itemHolder.setTextResId(R.string.add_more, R.string.full_protection_for_your_home);
        itemHolder.setAddMoreButtonVisible(HomeManager.getInstance().getCurrentHome().getLevel() > LocalKey.USER);
        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            itemHolder.setAddMoreBtnEnable(false);
        } else {
            itemHolder.setAddMoreBtnEnable(true);
            itemHolder.setAddMoreClickListener(v -> {
                if (null == mWeakMainActivity.get()) {
                    DDLog.e(TAG, "MainActivity is empty.");
                    return;
                }
                if (!AppConfig.Functions.SUPPORT_ADD_MORE ||
                        AddMoreHelper.FUNCTION_MODE_OEM == AddMoreHelper.getInstance().getFunctionMode()) {
                    ScannerActivity.startScan(mWeakMainActivity.get(), false, ScannerActivity.FROM_ADD_DEVICE);
                } else {
                    mWeakMainActivity.get().addCommonFragment(AddMoreFragment.newInstance());
                }
            });
        }
        itemHolder.setFullProtectClickListener(v -> {
            // TODO 处理FullProtect的点击事件
        });
    }
}
