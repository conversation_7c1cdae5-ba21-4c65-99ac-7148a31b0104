package com.dinsafer.module.main.view;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.Local;
import com.scwang.smart.refresh.header.ClassicsHeader;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/8/19
 */
public class CommonRefreshHeader extends ClassicsHeader {

    public CommonRefreshHeader(Context context) {
        super(context);

//        protected String mTextPulling;//"下拉可以刷新";
//        protected String mTextRefreshing;//"正在刷新...";
//        protected String mTextLoading;//"正在加载...";
//        protected String mTextRelease;//"释放立即刷新";
//        protected String mTextFinish;//"刷新完成";
//        protected String mTextFailed;//"刷新失败";
//        protected String mTextUpdate;//"上次更新 M-d HH:mm";
//        protected String mTextSecondary;//"释放进入二楼";

        mTextPulling = Local.s(getResources().getString(R.string.release_to_load));
        mTextRelease = Local.s(getResources().getString(R.string.release_to_load));
        mTextLoading = Local.s(getResources().getString(R.string.loading));
        mTextRefreshing = Local.s(getResources().getString(R.string.loading));
        mTextFinish = " ";
        mTextFailed = Local.s(getResources().getString(R.string.loading));
        mTextSecondary = Local.s(getResources().getString(R.string.release_to_load));


    }


}
