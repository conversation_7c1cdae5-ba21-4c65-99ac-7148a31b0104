package com.dinsafer.module.main.adapter;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.UnCloseDoorEntry;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.Local;


import java.util.ArrayList;

/**
 * Created by LT on 2018/8/27.
 *
 */
public class ReadyToArmDialogAdapter extends RecyclerView.Adapter<ReadyToArmDialogAdapter.ViewHolder> {
    private ArrayList<UnCloseDoorEntry.ResultBean.PluginsBean> mData;

    public ReadyToArmDialogAdapter(ArrayList<UnCloseDoorEntry.ResultBean.PluginsBean> data) {
        this.mData = data;
    }

    public void updateData(ArrayList<UnCloseDoorEntry.ResultBean.PluginsBean> data) {
        this.mData = data;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.ready_to_arm_dialog_item, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.pluginId.setLocalText(mData.get(position).getId());

        if(TextUtils.isEmpty(mData.get(position).getName())){
            holder.pluginName.setLocalText(Local.s( Local.s("Door Window Sensor")+ "_" + mData.get(position).getId()));
        }else {
            holder.pluginName.setLocalText(mData.get(position).getName());
        }
    }

    @Override
    public int getItemCount() {
        return null == mData ? 0 : mData.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        LocalTextView pluginName;
        LocalTextView pluginId;

        public ViewHolder(View itemView) {
            super(itemView);
            __bindViews(itemView);
        }

        private void __bindViews(View itemView) {
            pluginName = itemView.findViewById(R.id.plugin_name);
            pluginId = itemView.findViewById(R.id.plugin_id);
        }
    }
}
