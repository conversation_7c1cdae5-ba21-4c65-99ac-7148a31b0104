package com.dinsafer.module.main.view;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.Local;
import com.scwang.smart.refresh.footer.ClassicsFooter;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/8/19
 */
public class CommonRefreshFooter extends ClassicsFooter {
    public CommonRefreshFooter(Context context) {
        super(context);
//        protected String mTextPulling;//"上拉加载更多";
//        protected String mTextRelease;//"释放立即加载";
//        protected String mTextLoading;//"正在加载...";
//        protected String mTextRefreshing;//"正在刷新...";
//        protected String mTextFinish;//"加载完成";
//        protected String mTextFailed;//"加载失败";
//        protected String mTextNothing;//"没有更多数据了";

        mTextPulling = Local.s(getResources().getString(R.string.release_to_load));
        mTextRelease = Local.s(getResources().getString(R.string.release_to_load));
        mTextLoading = Local.s(getResources().getString(R.string.loading));
        mTextRefreshing = Local.s(getResources().getString(R.string.loading));
        mTextFinish = " ";
        mTextFailed = Local.s(getResources().getString(R.string.loading));
        mTextNothing = Local.s("No more data");
    }
}
