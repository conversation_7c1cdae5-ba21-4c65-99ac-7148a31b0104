package com.dinsafer.module.main.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.dinsafer.model.panel.MainPanelHeaderViewHolder;

import java.util.ArrayList;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/**
 * 首页Panel Section基类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/2 5:49 PM
 */
public abstract class MainPanelBaseSection<D> extends StatelessSection {
    protected final String TAG = this.getClass().getSimpleName();

    protected ArrayList<D> mData;
    protected String mTitle;
    protected Context mContext;
    protected OnItemClickListener mOnItemClickListener;

    public MainPanelBaseSection(Context context, SectionParameters sectionParameters,
                                String tittle, ArrayList<D> datas) {
        super(sectionParameters);
        this.mContext = context;
        this.mData = datas;
        this.mTitle = tittle;
    }

    @Override
    public int getContentItemsTotal() {
        if (0 != getRowItemCount()
                && mData.size() % getRowItemCount() != 0) {
            return mData.size() + (getRowItemCount() - mData.size() % getRowItemCount());
        }
        return mData.size();
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new MainPanelHeaderViewHolder(view);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        MainPanelHeaderViewHolder headerHolder = (MainPanelHeaderViewHolder) holder;
        headerHolder.mTitle.setLocalText(mTitle);
    }

    public ArrayList<D> getData() {
        return mData;
    }

    public String getTitle() {
        return mTitle;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    /**
     * 获取列数
     *
     * @return
     */
    public abstract int getRowItemCount();

    public interface OnItemClickListener {
        /**
         * Item点击回调
         *
         * @param sectionTittle     分节标题
         * @param isEditMode        是否编辑模式
         * @param clickView         点击的视图
         * @param positionInSection 在分节中的下标
         */
        void onItemClick(String sectionTittle, boolean isEditMode, View clickView, int positionInSection);
    }
}
