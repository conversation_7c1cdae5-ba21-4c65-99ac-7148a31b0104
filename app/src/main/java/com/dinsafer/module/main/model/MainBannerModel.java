package com.dinsafer.module.main.model;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.convenientbanner.holder.CBViewHolderCreator;
import com.bigkoo.convenientbanner.holder.Holder;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemMainBannerBinding;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.iap.BetaUserClubInvitationFragment;
import com.dinsafer.module.iap.ListBigBannerResponse;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.PluginWidgetStyleUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2022/11/27
 * @author: create by Sydnee
 */
public class MainBannerModel extends BaseMainItemModel<ItemMainBannerBinding> implements OnBannerModelListener {

    private MainActivity activity;
    private final List<ListBigBannerResponse.BigBannerBean> data = new ArrayList<>();
    private BannerViewHolder itemHolder;
    private boolean isVisible;
    private final CBViewHolderCreator mCreator = new CBViewHolderCreator() {
        @Override
        public Holder createHolder(View itemView) {
            return new BannerItemHolder(itemView);
        }

        @Override
        public int getLayoutId() {
            return R.layout.item_prime_service_banner;
        }
    };

    public MainBannerModel(Context context, MainActivity mainActivity, @NonNull MainWidgetBean bean, List<ListBigBannerResponse.BigBannerBean> data) {
        super(context,bean, PluginWidgetStyleUtil.MODEL_ID_BANNER_DEF, PluginWidgetStyleUtil.MODEL_TYPE_BANNER_DEF, 0);
        this.activity = mainActivity;
        setData(data);
    }

    public void setVisible(boolean visible) {
        this.isVisible = visible;
    }


    @Override
    public void setData(List<ListBigBannerResponse.BigBannerBean> data) {
        this.data.clear();
        if (null != data && data.size() > 0) {
            this.data.addAll(data);
        }
        if (this.data.size() > 0) {
            setVisible(true);
        } else {
            setVisible(false);
        }
        setPages();
        changeState();
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_main_banner;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemMainBannerBinding itemMainBannerBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            itemHolder = new BannerViewHolder(itemMainBannerBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, itemHolder);
        } else {
            itemHolder = (BannerViewHolder) tag;
        }
        itemHolder.mBinding.banner.setPages(mCreator, this.data);
        itemHolder.mBinding.banner.setOnItemClickListener(position -> {
            if (position >= data.size()) {
                return;
            }
            final ListBigBannerResponse.BigBannerBean clickItem = data.get(position);
            if (clickItem != null && null != activity) {
                activity.addCommonFragment(BetaUserClubInvitationFragment.newInstance(clickItem.getTask_id()));
            }
        });
        DDLog.d("MainBannerModel", "convert. ");
        DeviceCallBackManager.getInstance().addOnBannerModelListener(this);
        changeState();
        setPages();
    }

    private void changeState() {
        if (null == itemHolder) {
            return;
        }
        boolean show = isVisible && !MainPanelHelper.getInstance().isPanelEditMode();
        DDLog.d("MainBannerModel", "changeState. show : " + show);
        GridLayoutManager.LayoutParams rootLayoutParam = (GridLayoutManager.LayoutParams) itemHolder.mBinding.cvRoot.getLayoutParams();
        rootLayoutParam.topMargin = DensityUtils.dp2px(activity.getApplicationContext(), show ? 15 : 0);
        rootLayoutParam.height = DensityUtils.dp2px(activity.getApplicationContext(), show ? 72 : 0);
        itemHolder.mBinding.cvRoot.setLayoutParams(rootLayoutParam);
        itemHolder.mBinding.cvRoot.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    private void setPages() {
        DDLog.d("MainBannerModel", "setPages. ");
        if (null == itemHolder) {
            return;
        }

        if (data.size() < 1) {
            return;
        }

        if (this.data != null && this.data.size() > 1) {
            itemHolder.mBinding.banner.setPageIndicator(new int[]{R.drawable.shape_main_banner_indicator_nor, R.drawable.shape_main_banner_indicator_sel});
            itemHolder.mBinding.banner.setCanLoop(true);
            itemHolder.mBinding.banner.startTurning(4000);
        } else {
            itemHolder.mBinding.banner.setCanLoop(false);
            itemHolder.mBinding.banner.removeIndicatorViews();
            itemHolder.mBinding.banner.stopTurning();
        }

        try {
            //修改indicator底部边距
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) itemHolder.mBinding.banner.getLoPageTurningPoint().getLayoutParams();
            layoutParams.topMargin = DensityUtils.dp2px(activity.getApplicationContext(), 2);
            layoutParams.bottomMargin = DensityUtils.dp2px(activity.getApplicationContext(), 4);
            itemHolder.mBinding.banner.getLoPageTurningPoint().setLayoutParams(layoutParams);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    static class BannerViewHolder extends RecyclerView.ViewHolder {
        private final ItemMainBannerBinding mBinding;

        public BannerViewHolder(@NonNull View itemView) {
            super(itemView);
            mBinding = DataBindingUtil.bind(itemView);
        }

    }

    static class BannerItemHolder extends Holder<ListBigBannerResponse.BigBannerBean> {

        public BannerItemHolder(View itemView) {
            super(itemView);
        }

        @Override
        protected void initView(View itemView) {


        }

        @Override
        public void updateUI(ListBigBannerResponse.BigBannerBean data) {
            itemView.setTag(R.id.tv_title, data);
            itemView.setFocusable(true);
            itemView.setClickable(true);
            try {
                ((LocalTextView) itemView.findViewById(R.id.tv_title)).setLocalText(data.getView_dataset().getBnr_title_txt());
                ((LocalTextView) itemView.findViewById(R.id.tv_title)).setTextColor(Color.parseColor(data.getView_dataset().getBnr_title_clr()));
            } catch (Exception e) {

            }
            try {
                ((LocalTextView) itemView.findViewById(R.id.tv_des)).setText(data.getView_dataset().getBnr_desc_txt());
                ((LocalTextView) itemView.findViewById(R.id.tv_des)).setTextColor(Color.parseColor(data.getView_dataset().getBnr_desc_clr()));
            } catch (Exception e) {

            }
            try {
                ((ImageView) itemView.findViewById(R.id.btn_go)).setBackgroundTintList(ColorStateList.valueOf(Color.parseColor(data.getView_dataset().getBnr_action_clr())));
                itemView.findViewById(R.id.tv_preview).setVisibility(data.isPreviewStatus() ? View.VISIBLE : View.GONE);
            } catch (Exception e) {

            }
        }

    }


}
