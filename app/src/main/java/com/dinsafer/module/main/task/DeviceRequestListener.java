package com.dinsafer.module.main.task;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.module.ipc.common.video.global.base.IGlobalTaskListener;

import java.util.List;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/5 11:13 上午
 */
public interface DeviceRequestListener extends IGlobalTaskListener<Boolean> {

    void onLoad(@NonNull final String type, @Nullable final List<Device> devices, int total, int current);
}
