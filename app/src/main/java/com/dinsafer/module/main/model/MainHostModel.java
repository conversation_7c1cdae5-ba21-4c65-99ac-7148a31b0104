package com.dinsafer.module.main.model;

import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.HostItemDelegate;
import com.dinsafer.model.panel.MainPanelHostViewHolder;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.ui.device.MainDeviceStatusView;
import com.dinsafer.util.PluginWidgetStyleUtil;

/**
 * @describe：主机卡片
 * @date：2022/10/17
 * @author: create by Sydnee
 */
public class MainHostModel extends BaseMainHostModel {

    private final static String TAG = MainHostModel.class.getSimpleName();
    private MainPanelHostViewHolder hostViewHolder;

    public MainHostModel(BaseFragment baseFragment, PanelDevice panelDevice, MainPanelArmDelayRecorder armDelayRecorder, MainWidgetBean bean, OnWidgetItemListener onWidgetItemListener
            , MainDeviceStatusView.OnDeviceStatusActionClickListener onDeviceStatusActionClickListener, final int adapterItemId) {
        super(baseFragment, panelDevice, armDelayRecorder, bean, onWidgetItemListener, onDeviceStatusActionClickListener, PluginWidgetStyleUtil.PANEL, adapterItemId);
    }

    @Override
    public HostItemDelegate getHostItemDelegate() {
        return hostViewHolder;
    }

    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding viewDataBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            hostViewHolder = new MainPanelHostViewHolder(viewDataBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, hostViewHolder);
        } else {
            hostViewHolder = (MainPanelHostViewHolder) tag;
        }

        initView(holder);
    }

    @Override
    public void onViewDeleted(String pluginId, String subcategory, boolean flagDeleted) {

    }
}
