package com.dinsafer.module.main.model;

import static com.dinsafer.model.TuyaItem.LIGHT_LOADING;
import static com.dinsafer.model.TuyaItem.LIGHT_OFF;
import static com.dinsafer.model.TuyaItem.LIGHT_ON;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_LOADING;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_OFF;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_ON;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_LOADING;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_OFF;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_ON;

import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.model.TuyaItem;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.model.panel.MainPanelShortcutItemViewHolder;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.helper.DeviceCallBackManager;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.PluginWidgetStyleUtil;

/**
 * @author: creat by Sydnee
 * @date：2022/9/27
 * @describe：
 */
public class MainShortcutPluginModel extends BaseMainItemModel<ViewDataBinding> implements OnShortcutModelListener {

    private final static String TAG = MainShortcutPluginModel.class.getSimpleName();
    private final TuyaItemPlus tuyaItemPlus;
    private final Context mContext;
    private final String cacheName;
    private String name;
    private final OnWidgetItemListener onWidgetItemListener;
    private final int layoutId;
    private MainPanelShortcutItemViewHolder itemHolder;
    private boolean isOfficialSmartPlug = false;
    private BaseFragment baseFragment;

    public MainShortcutPluginModel(BaseFragment baseFragment, TuyaItemPlus tuyaItemPlus, MainWidgetBean bean,
                                   OnWidgetItemListener onWidgetItemListener, final int adapterItemId) {
        super(baseFragment.getContext(),bean, null != bean ? bean.getPluginId() : null, PluginWidgetStyleUtil.SHORTCUT, adapterItemId);
        this.baseFragment = baseFragment;
        this.tuyaItemPlus = tuyaItemPlus;
        this.mContext = baseFragment.getContext();
        this.onWidgetItemListener = onWidgetItemListener;
        this.layoutId = PluginWidgetStyleUtil.getInstance().getWidgetStyle(bean.getLayoutType());
        this.cacheName = bean.getName();
    }

    @Override
    public int getLayoutID() {
        return this.layoutId;
    }


    @Override
    public void convert(BaseViewHolder holder, ViewDataBinding mainPanelItemShortcutBinding) {
        final Object tag = holder.itemView.getTag(R.id.main_plugin_item_tag_id);
        if (null == tag) {
            itemHolder = new MainPanelShortcutItemViewHolder(mainPanelItemShortcutBinding.getRoot());
            holder.itemView.setTag(R.id.main_plugin_item_tag_id, itemHolder);
        } else {
            itemHolder = (MainPanelShortcutItemViewHolder) tag;
        }
        initListener(holder);

        itemHolder.setRootViewVisible(true);
        itemHolder.setRootViewEnable(true);
        itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());

        if (tuyaItemPlus == null || !tuyaItemPlus.isFlagLoaded()) {
            // 切换家庭中先显示加载中
            itemHolder.setStatusLoading(cacheName);
            if (getWidgetBean().isNullDevice() || (null != tuyaItemPlus && tuyaItemPlus.isFlagDeleted())) {
                // 请求数据后发现无该device
                itemHolder.setRootViewDeleted(true);
            }
            return;
        }

        isOfficialSmartPlug = isOfficialSmartPlug(tuyaItemPlus.getType());

        name = tuyaItemPlus.getName();
        final String id = tuyaItemPlus.getId();
        if (TextUtils.isEmpty(name)) {
            if (!TextUtils.isEmpty(tuyaItemPlus.getDecodeid())) {
                //   如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                name = CommonDataUtil.getInstance().getSTypeByDecodeid(tuyaItemPlus.getDecodeid());
            } else if (id.startsWith("!")) {
                if (!TextUtils.isEmpty(tuyaItemPlus.getStype())) {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(tuyaItemPlus.getStype());
                } else {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(tuyaItemPlus.getSub_category());
                }
            } else {
                name = CommonDataUtil.getInstance().getSTypeByID(id);
            }
            name = Local.s(name) + "_" + id;
        }
        if (!TextUtils.isEmpty(cacheName)
                && name.contains(id)) {
            name = cacheName;
        }
        itemHolder.setPluginName(name);

        itemHolder.setQuickStartClickListener((View v) -> {
                    int pluginType = tuyaItemPlus.getType();
                    if (!MainPanelHelper.getInstance().isPanelEditMode()
                            && !MainPanelHelper.getInstance().isFunctionEnable()
                            && isTuyaLight(pluginType)
                            && isTuyaPlug(pluginType)) {
                        DDLog.e(TAG, "当前Item不能点击2");
                        return;
                    }

                    //  点击Item QuickStart icon
                    if (isOfficialSmartPlug(pluginType)) {
                        // 自研插座
                        if (requestChangeSmartPlugStatus(tuyaItemPlus, itemHolder)) {
                            itemHolder.setQuickStartEnable(false);
                            itemHolder.setQuickStartLoading(true);
                            tuyaItemPlus.setType(SMARTPLUGIN_LOADING);
                        }
                    } else {
                        DDLog.e(TAG, "Unhandle plugin type, pluginType: " + pluginType);
                    }
                }
        );

        if (isOfficialSmartPlug) {
            boolean isPanelDeviceOffline = !CommonDataUtil.getInstance().isPanelOnline();
            changeOnlineStateByPanelDevice(isPanelDeviceOffline);
        } else {
            changeOnlineStateView();
        }

        // 是否已被删除
        itemHolder.setRootViewDeleted(tuyaItemPlus.isFlagDeleted());

    }

    private void initListener(BaseViewHolder holder) {
        DeviceCallBackManager.getInstance().addOnShortcutModelListener(this);

        itemHolder.setRootViewLongClickListener(view -> {
            boolean isPanelEditMode = MainPanelHelper.getInstance().isPanelEditMode();
            if (null != onWidgetItemListener) {
                onWidgetItemListener.onItemLongClick(isPanelEditMode, holder, holder.getAdapterPosition(),getWidgetBean(), getModelId(), getModelType());
            }
            return true;
        });

        itemHolder.setRootViewClickListener((View v) -> {
            if (null == onWidgetItemListener || null == tuyaItemPlus) {
                DDLog.e(TAG, "当前Item没有对应的device，不能点击");
                return;
            }

            int pluginType = tuyaItemPlus.getType();
            if (!MainPanelHelper.getInstance().isPanelEditMode()
                    && !MainPanelHelper.getInstance().isFunctionEnable()
                    && !isTuyaLight(pluginType)
                    && !isTuyaPlug(pluginType)) {
                DDLog.e(TAG, "当前Item不能点击");
                return;
            }

            onWidgetItemListener.onItemClick(MainPanelHelper.getInstance().isPanelEditMode()
                    , MainPanelHelper.SECTION_TYPE_SHORTCUT, tuyaItemPlus, getModelId(), getModelType());
        });

        itemHolder.setDeleteIconClickListener(view -> {
            if (null == onWidgetItemListener || !MainPanelHelper.getInstance().isPanelEditMode()) {
                return;
            }
            DeviceCallBackManager.getInstance().removeOnShortcutModelListener(this);
            onWidgetItemListener.onDeleteIconClick(holder.getAdapterPosition(),getWidgetBean(), getModelId(), getModelType());
        });

        itemHolder.setUnavailableStateClickListener(view -> {
            if (null == onWidgetItemListener) {
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                return;
            }

            onWidgetItemListener.onUnavailableStateViewClick(holder.getAdapterPosition(),getWidgetBean(), getModelId(), getModelType());
        });
    }

    /**
     * tuyaItemPlus状态
     */
    private void changeOnlineStateView() {

        if (!tuyaItemPlus.isFlagLoaded()) {
            // 还在loading
            itemHolder.setStatusLoading(name);
            return;
        }


        if (tuyaItemPlus.isNeedOnlineState()
                && !tuyaItemPlus.isOnline()) {
            // 离线
            itemHolder.setSelected(tuyaItemPlus.isShow());
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusFinish(mContext, tuyaItemPlus.isNeedOnlineState(), tuyaItemPlus.isOnline());
            return;
        }

        // 在线
        updateStateFinished(tuyaItemPlus, itemHolder);
    }

    /**
     * 主机离线更改tuyaItemPlus状态
     *
     * @param isPanelDeviceOffline
     */
    private void changeOnlineStateByPanelDevice(boolean isPanelDeviceOffline) {
        DDLog.d(TAG, "changeOnlineStateByPanelDevice. ");
        if (isPanelDeviceOffline) {
            itemHolder.setStatusFinish(mContext, true, false);
            itemHolder.setStateTextByPanelDevice(mContext, true);
            return;
        }
        changeOnlineStateView();
    }

    /**
     * 修改为完成的状态
     *
     * @param itemData
     * @param itemHolder
     */
    private void updateStateFinished(TuyaItemPlus itemData, MainPanelShortcutItemViewHolder itemHolder) {
        itemHolder.setStatusFinish(mContext, itemData.isNeedOnlineState(), itemData.isOnline());

        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 编辑模式
            itemHolder.setEditMode(true);
            itemHolder.setSelected(itemData.isShow());
            itemHolder.setQuickStartEnable(false);
            itemHolder.setQuickStartLoading(false);
        } else {
            // 非编辑模式
            itemHolder.setEditMode(false);
            if (SMARTPLUGIN_LOADING == itemData.getType()
                    || TUYA_SMARTPLUGIN_LOADING == itemData.getType()
                    || LIGHT_LOADING == itemData.getType()) {
                itemHolder.setQuickStartEnable(false);
                itemHolder.setQuickStartLoading(true);
            } else {
                itemHolder.setQuickStartEnable(true);
                itemHolder.setQuickStartLoading(false);
            }
        }
        itemHolder.setQuickStartIconRes(
                CommonDataUtil.getInstance().getMainIconByType(itemData.getType()));
    }

    /**
     * 请求修改自家插座的开关状态
     * <p>
     * status 0:关; 1:开
     */
    private boolean requestChangeSmartPlugStatus(TuyaItemPlus data,
                                                 MainPanelShortcutItemViewHolder itemHolder) {
        DDLog.d(TAG, "requestChangeSmartPlugStatus");
        if (data.getType() != SMARTPLUGIN_ON
                && data.getType() != SMARTPLUGIN_OFF) {
            DDLog.e(TAG, "Error type.");
            baseFragment.showErrorToast();
            return false;
        }

        // 修改开关状态
        final int lastStatus = data.getType();
        int status = SMARTPLUGIN_ON == lastStatus ? 0 : 1;

        Device device = DinHome.getInstance().getDevice(data.getId());
        if (null != device) {
            device.submit(PanelParamsHelper.changePlugOn(1 == status));

            final Handler handler = new Handler();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        DDLog.e(TAG, "update smart plug state timeout.");
                        DDLog.e(TAG, "smart plug  type: " + data.getType());
                        if (data.getType() == SMARTPLUGIN_LOADING) {
                            data.setType(lastStatus);
                            updateStateFinished(data, itemHolder);
                        }
                    } catch (Exception ex) {
                        DDLog.e(TAG, "update light state error.");
                    }
                }
            }, LocalKey.TIMEOUT);
            return true;
        } else {
            DDLog.e(TAG, "No plugin device");
            return false;
        }
    }

    /**
     * 是否自家插座
     * f
     *
     * @param type
     * @return true: 是自家插座
     */
    private boolean isOfficialSmartPlug(int type) {
        return type == SMARTPLUGIN_ON || type == SMARTPLUGIN_OFF
                || type == SMARTPLUGIN_LOADING;
    }

    /**
     * 是否涂鸦灯泡
     *
     * @param type
     * @return true: 是涂鸦灯泡
     */
    private boolean isTuyaLight(int type) {
        return LIGHT_ON == type
                || LIGHT_OFF == type
                || LIGHT_LOADING == type;
    }


    /**
     * 是否涂鸦插座
     *
     * @param type
     * @return true: 是涂鸦插座
     */
    private boolean isTuyaPlug(int type) {
        return TUYA_SMARTPLUGIN_ON == type
                || TUYA_SMARTPLUGIN_OFF == type
                || TUYA_SMARTPLUGIN_LOADING == type;
    }


    @Override
    public void updateShortcutName(String pluginId, String name) {
//        DDLog.e(TAG, "updateShortcutName. pluginId: " + pluginId + "  name: " + name);
        if (null == tuyaItemPlus) {
            return;
        }
        if (TextUtils.isEmpty(pluginId)
                || TextUtils.isEmpty(name)) {
            return;
        }

        if (!pluginId.equals(tuyaItemPlus.getId())) {
            return;
        }

        tuyaItemPlus.setName(name);
        itemHolder.setPluginName(name);
    }

    @Override
    public void updateOnlineStateShortCut(String sendId, boolean online, boolean isPanelDeviceOffline) {
        if (null == tuyaItemPlus) {
            return;
        }
        if (TextUtils.isEmpty(sendId) || !sendId.equals(tuyaItemPlus.getSendid())) {
            return;
        }

        tuyaItemPlus.setOnline(online);
        tuyaItemPlus.setNeedOnlineState(true);
        tuyaItemPlus.setFlagLoaded(true);
        if (isOfficialSmartPlug) {
            changeOnlineStateByPanelDevice(isPanelDeviceOffline);
        } else {
            changeOnlineStateView();
        }
    }

    @Override
    public void updateTuyaColorLightStatus(Device device, boolean isPanelDeviceOffline) {
        if (null == tuyaItemPlus || null == device) {
            return;
        }
        if (!device.getId().equals(tuyaItemPlus.getId())) {
            return;
        }
//        DDLog.e(TAG, "updateTuyaColorLightStatus. device: " + device.getId() + "  tuya: " + tuyaItemPlus.getId());
        tuyaItemPlus.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
        boolean isOnline = ((Integer) MapUtils.get(device.getInfo(), DinConst.INFO_STATE, -1)) == 1;
        if (isOnline) {
            tuyaItemPlus.setOnline(true);
            boolean isOn = (boolean) MapUtils.get(device.getInfo(), DinConst.INFO_TUYA_ISON, false);
            if (isOn) {
                tuyaItemPlus.setType(TuyaItem.LIGHT_ON);
            } else {
                tuyaItemPlus.setType(TuyaItem.LIGHT_OFF);
            }
        } else {
            tuyaItemPlus.setOnline(false);
        }

        if (isOfficialSmartPlug) {
            changeOnlineStateByPanelDevice(isPanelDeviceOffline);
        } else {
            changeOnlineStateView();
        }
    }

    @Override
    public void updateTuyaSmartPluginStatus(Device device, boolean isPanelDeviceOffline) {
        if (null == tuyaItemPlus || null == device) {
            return;
        }
        if (!device.getId().equals(tuyaItemPlus.getId())) {
            return;
        }
//        DDLog.e(TAG, "updateTuyaSmartPluginStatus. device: " + device.getId() + "  tuya: " + tuyaItemPlus.getId());
        tuyaItemPlus.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
        boolean isOnline = ((Integer) MapUtils.get(device.getInfo(), DinConst.INFO_STATE, -1)) == 1;
        if (isOnline) {
            tuyaItemPlus.setOnline(true);
            boolean isOn = (boolean) MapUtils.get(device.getInfo(), DinConst.INFO_TUYA_ISON, false);
            if (isOn) {
                tuyaItemPlus.setType(TuyaItem.TUYA_SMARTPLUGIN_ON);
            } else {
                tuyaItemPlus.setType(TuyaItem.TUYA_SMARTPLUGIN_OFF);
            }
        } else {
            tuyaItemPlus.setOnline(false);
        }

        if (isOfficialSmartPlug) {
            changeOnlineStateByPanelDevice(isPanelDeviceOffline);
        } else {
            changeOnlineStateView();
        }
    }

    @Override
    public void changeOldSmartPlugState(String shortcutId, boolean enable, boolean isPanelDeviceOffline) {
        if (null == tuyaItemPlus) {
            return;
        }
        if (TextUtils.isEmpty(shortcutId) || !shortcutId.equals(tuyaItemPlus.getId())) {
            return;
        }

        if (enable) {
            tuyaItemPlus.setType(TuyaItem.SMARTPLUGIN_ON);
        } else {
            tuyaItemPlus.setType(TuyaItem.SMARTPLUGIN_OFF);
        }
        tuyaItemPlus.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);

        if (isOfficialSmartPlug) {
            changeOnlineStateByPanelDevice(isPanelDeviceOffline);
        } else {
            changeOnlineStateView();
        }
    }

    @Override
    public void changeAskSmartPlugState(String shortcutId, boolean enable, boolean isPanelDeviceOffline) {
        if (null == tuyaItemPlus) {
            return;
        }
        if (TextUtils.isEmpty(shortcutId) || !shortcutId.equals(tuyaItemPlus.getId())) {
            return;
        }

        if (enable) {
            tuyaItemPlus.setType(TuyaItem.SMARTPLUGIN_ON);
        } else {
            tuyaItemPlus.setType(TuyaItem.SMARTPLUGIN_OFF);
        }
        tuyaItemPlus.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);

        if (isOfficialSmartPlug) {
            changeOnlineStateByPanelDevice(isPanelDeviceOffline);
        } else {
            changeOnlineStateView();
        }
    }

    @Override
    public void changeOfficialSmartPlugStateByPanelDevice(boolean isPanelDeviceOffline) {
        if (null == tuyaItemPlus) {
            return;
        }
        DDLog.d(TAG, "changeOfficialSmartPlugStateByPanelDevice. isOfficialSmartPlug: " + isOfficialSmartPlug);
        if (!isOfficialSmartPlug) {
            return;
        }

        changeOnlineStateByPanelDevice(isPanelDeviceOffline);
    }

    @Override
    public void onLoadedUpdate(String shortcutId, final boolean loaded) {
        if (TextUtils.isEmpty(shortcutId) || null == tuyaItemPlus
                || !shortcutId.equals(tuyaItemPlus.getId()) || null == itemHolder) {
            return;
        }
        tuyaItemPlus.setFlagLoaded(loaded);

        itemHolder.setRootViewVisible(true);
        itemHolder.setRootViewEnable(true);
        itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());

        if (!tuyaItemPlus.isFlagLoaded()) {
            // 切换家庭中先显示加载中
            getWidgetBean().setNullDevice(false);
            itemHolder.setStatusLoading(cacheName);
            return;
        }

        isOfficialSmartPlug = isOfficialSmartPlug(tuyaItemPlus.getType());

        if (isOfficialSmartPlug) {
            boolean isPanelDeviceOffline = !CommonDataUtil.getInstance().isPanelOnline();
            changeOnlineStateByPanelDevice(isPanelDeviceOffline);
        } else {
            changeOnlineStateView();
        }

        // 是否已被删除
        itemHolder.setRootViewDeleted(tuyaItemPlus.isFlagDeleted());
    }

    @Override
    public void onViewDeleted(String shortcutId, boolean flagDeleted) {
        if (null == tuyaItemPlus) {
            return;
        }
        if (TextUtils.isEmpty(shortcutId) || !shortcutId.equals(tuyaItemPlus.getId())) {
            return;
        }
        tuyaItemPlus.setFlagDeleted(flagDeleted);
        itemHolder.setRootViewDeleted(tuyaItemPlus.isFlagDeleted());
    }

    @Override
    public void onViewDeleted(String pluginId, String subcategory, boolean flagDeleted) {

    }

    @Override
    public void checkDeviceIsNull() {
        DDLog.d(TAG, "checkDeviceIsNull: tuyaItemPlus is null = " + (tuyaItemPlus == null));
        boolean isDeleted = false;
        if (null == tuyaItemPlus) {
            isDeleted = true;
        } else if (tuyaItemPlus.isFlagDeleted()) {
            isDeleted = true;
        }
        getWidgetBean().setNullDevice(isDeleted);
        itemHolder.setRootViewDeleted(isDeleted);
    }
}
