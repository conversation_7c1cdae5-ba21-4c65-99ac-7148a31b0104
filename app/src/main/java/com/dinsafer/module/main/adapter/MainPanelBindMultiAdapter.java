package com.dinsafer.module.main.adapter;

import android.util.Log;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.module.main.helper.IMainRvItemTouchHelper;
import com.dinsafer.module.main.helper.MainWidgetListProvider;
import com.dinsafer.module.main.model.BaseMainItemModel;
import com.dinsafer.module.main.model.MainAddMorePluginModel;
import com.dinsafer.module.main.model.MainBannerModel;
import com.dinsafer.module.main.model.MainBatteryModel;
import com.dinsafer.module.main.model.MainBmtEVModel;
import com.dinsafer.module.main.model.MainBmtEmergencyModel;
import com.dinsafer.module.main.model.MainBmtModel;
import com.dinsafer.module.main.model.MainBmtTodayUsageModel;
import com.dinsafer.module.main.model.MainFunctionModel;
import com.dinsafer.module.main.model.MainHostModel;
import com.dinsafer.module.main.model.MainIpcModel;
import com.dinsafer.module.main.model.MainMultiScreenPluginModel;
import com.dinsafer.module.main.model.MainPluginModel;
import com.dinsafer.module.main.model.MainPowerPulseCurrentModel;
import com.dinsafer.module.main.model.MainPowerStoreCurrentModel;
import com.dinsafer.module.main.model.MainShortcutPluginModel;
import com.dinsafer.module.main.model.MainSmallHostModel;
import com.dinsafer.module.main.model.MainSmallIpcModel;
import com.dinsafer.ui.rv.BindRecyclerViewAdapter;

import java.util.Collections;
import java.util.List;

/**
 * @describe：首页widget配件列表的适配器
 * @date：2022/10/13
 * @author: create by Sydnee
 */
public class MainPanelBindMultiAdapter<T extends BaseMainItemModel<?>> extends BindRecyclerViewAdapter<ViewDataBinding, T>
        implements IMainRvItemTouchHelper {

    private final static String TAG = MainPanelBindMultiAdapter.class.getSimpleName();
    public final static int TYPE_IPC = 1;
    public final static int TYPE_SMALL_IPC = 2;
    public final static int TYPE_PLUGIN = 3;
    public final static int TYPE_SHORTCUT = 4;
    public final static int TYPE_PANEL_DEVICE = 5;
    public final static int TYPE_SMALL_PANEL_DEVICE = 6;
    public final static int TYPE_ADD_MORE = 7;
    public final static int TYPE_FUNCTION = 8;
    public final static int TYPE_MULTI_SCREEN = 9;
    public final static int TYPE_BANNER = 10;
    public final static int TYPE_BMT = 11;
    public final static int TYPE_BATTERY = 12;
    public final static int TYPE_BMT_EV = 13;
    public final static int TYPE_BMT_EMERGENCY = 14;
    public final static int TYPE_BMT_TODAY_USAGE = 15;
    public final static int TYPE_POWER_STORE_CURRENT = 16;
    public final static int TYPE_POWER_PULSE_CURRENT = 17;
    private int layoutId = -1;

    public MainPanelBindMultiAdapter() {
        super(0);
    }

    @Override
    protected BaseViewHolder onCreateDefViewHolder(ViewGroup parent, int viewType) {
//        Log.d(TAG, "onCreateDefViewHolder: data size:" + mData.size() + "/ viewType:" + viewType + "/layout:" + mData.get(viewType).getLayoutID());
//        Log.d(TAG, "onCreateDefViewHolder: " + viewType + "  layoutId: "  + layoutId);
        return createBaseViewHolder(parent, layoutId);

    }


    @Override
    public int getItemViewType(int position) {
        int viewType = -1;
        if (getItem(position) instanceof MainPluginModel) {
            viewType = TYPE_PLUGIN;
        } else if (getItem(position) instanceof MainIpcModel) {
            viewType = TYPE_IPC;
        } else if (getItem(position) instanceof MainSmallIpcModel) {
            viewType = TYPE_SMALL_IPC;
        } else if (getItem(position) instanceof MainShortcutPluginModel) {
            viewType = TYPE_SHORTCUT;
        } else if (getItem(position) instanceof MainHostModel) {
            viewType = TYPE_PANEL_DEVICE;
        } else if (getItem(position) instanceof MainSmallHostModel) {
            viewType = TYPE_SMALL_PANEL_DEVICE;
        } else if (getItem(position) instanceof MainAddMorePluginModel) {
            viewType = TYPE_ADD_MORE;
        } else if (getItem(position) instanceof MainFunctionModel) {
            viewType = TYPE_FUNCTION;
        } else if (getItem(position) instanceof MainBannerModel) {
            viewType = TYPE_BANNER;
        } else if (getItem(position) instanceof MainBmtModel) {
            viewType = TYPE_BMT;
        } else if (getItem(position) instanceof MainBatteryModel) {
            viewType = TYPE_BATTERY;
        } else if (getItem(position) instanceof MainMultiScreenPluginModel) {
            viewType = TYPE_MULTI_SCREEN;
        } else if (getItem(position) instanceof MainBmtEVModel) {
            viewType = TYPE_BMT_EV;
        } else if (getItem(position) instanceof MainBmtEmergencyModel) {
            viewType = TYPE_BMT_EMERGENCY;
        } else if (getItem(position) instanceof MainBmtTodayUsageModel) {
            viewType = TYPE_BMT_TODAY_USAGE;
        } else if (getItem(position) instanceof MainPowerStoreCurrentModel) {
            viewType = TYPE_POWER_STORE_CURRENT;
        } else if (getItem(position) instanceof MainPowerPulseCurrentModel) {
            viewType = TYPE_POWER_PULSE_CURRENT;
        }
        // 获取对应的layout
        layoutId = getItem(position).getLayoutID();
//        Log.d(TAG, "getItemViewType:position:" + position + "   viewType:" + viewType + "     layoutId:" + layoutId);
        return viewType;
    }

    @Nullable
    @Override
    public T getItem(int position) {
        T item = super.getItem(position);
//        Log.d(TAG, "getItem: pos:" + position + "---" + item.getClass().getSimpleName());
        return item;
    }

    @Override
    public boolean onItemMove(int curPosition, int targetPosition) {
        // 对应更新缓存widget layout 列表
        final List<?> cacheLayoutList = MainWidgetListProvider.getInstance().getCacheLayoutList();
        final List<T> dataList = getData();
        final int dataItemCount = dataList.size();
        if (curPosition == targetPosition || curPosition < 0 || targetPosition < 0
                || curPosition > dataItemCount - 1 || targetPosition > dataItemCount - 1) {
            return false;
        }

        final int cacheCurPos = cacheLayoutList.indexOf(dataList.get(curPosition).getWidgetBean());
        final int cacheTargetPos = cacheLayoutList.indexOf(dataList.get(targetPosition).getWidgetBean());
        Log.d(TAG, "onMove: curPosition: " + curPosition + " targetPosition:" + targetPosition + ", cacheCurPos: " + cacheCurPos + ", cacheTargetPos: " + cacheTargetPos);

        if (curPosition < targetPosition) {
            for (int i = curPosition; i < targetPosition; i++) {
                Collections.swap(dataList, i, i + 1);
            }

            if (cacheCurPos > -1 && cacheTargetPos > -1 && cacheCurPos < cacheTargetPos) {
                for (int i = cacheCurPos; i < cacheTargetPos; i++) {
                    Collections.swap(cacheLayoutList, i, i + 1);
                }
            }
        } else {
            for (int i = curPosition; i > targetPosition; i--) {
                Collections.swap(dataList, i, i - 1);
            }
            if (cacheTargetPos > -1 && cacheTargetPos <= cacheCurPos) {
                for (int i = cacheCurPos; i > cacheTargetPos; i--) {
                    Collections.swap(cacheLayoutList, i, i - 1);
                }
            }
        }

        notifyItemMoved(curPosition, targetPosition);
//        notifyItemRangeChanged(Math.min(curPosition, targetPosition), Math.abs(curPosition - targetPosition) + 1);
        return true;
    }

    @Override
    public long getItemId(int position) {
        List<T> data = getData();
        if (position < data.size()) {
            return data.get(position).getAdapterItemId();
        }
        return super.getItemId(position);
    }
}
