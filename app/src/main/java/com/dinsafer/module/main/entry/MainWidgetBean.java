package com.dinsafer.module.main.entry;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dinsdk.DinPluginHelper;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.module.main.helper.MainWidgetListProvider;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.util.MapUtils;

import org.apache.http.util.TextUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * @describe：
 * @date：2022/10/8
 * @author: create by Sydnee
 */
public class MainWidgetBean implements Serializable {

    // 配件ID
    private String pluginId;
    private String subCategory;
    private String name;
    // 首页卡片类型，默认是卡片类型数组的第一个
    private String layoutType;
    // 配件对应的BindModel类型
    private int bindModelType;
    private String fatherId;
    private boolean isNullDevice;


    public MainWidgetBean() {
        this("", "", "", -1, "", "");
    }

    public MainWidgetBean(TuyaItemPlus tuyaItemPlus, String layoutType, int bindModelType) {
        this(tuyaItemPlus.getId(), tuyaItemPlus.getSub_category(), layoutType, bindModelType,
                DinPluginHelper.getPluginNameOrGenDefaultName(tuyaItemPlus), tuyaItemPlus.getFatherId());
    }

    public MainWidgetBean(Device device, String layoutType, int bindModelType) {
        this.pluginId = device.getId();
        if (MainWidgetListProvider.getInstance().isPowerStationType(bindModelType)) {
            this.pluginId = device.getId() + MainWidgetListProvider.UNDERLINE + device.getSubCategory();
        }
        this.subCategory = device.getSubCategory();
        this.layoutType = layoutType;
        this.bindModelType = bindModelType;
        this.name = getDeviceName(device);
    }

    public MainWidgetBean(String pluginId, String subCategory, String layoutType, int bindModelType, String name, String fatherId) {
        this.pluginId = pluginId;
        this.subCategory = subCategory;
        this.layoutType = layoutType;
        this.bindModelType = bindModelType;
        this.name = name;
        this.fatherId = fatherId;
    }


    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public int getBindModelType() {
        return bindModelType;
    }

    public void setBindModelType(int bindModelType) {
        this.bindModelType = bindModelType;
    }

    public void setLayoutType(String layoutType) {
        this.layoutType = layoutType;
    }

    public String getLayoutType() {
        return layoutType;
    }

    public void setFatherId(String fatherId) {
        this.fatherId = fatherId;
    }

    public String getFatherId() {
        return fatherId;
    }

    public void setNullDevice(boolean nullDevice) {
        isNullDevice = nullDevice;
    }

    public boolean isNullDevice() {
        return isNullDevice;
    }


    @Override
    public String toString() {
        return "MainWidgetBean{" +
                "pluginId='" + pluginId + '\'' +
                ", subCategory='" + subCategory + '\'' +
                ", name='" + name + '\'' +
                ", layoutType='" + layoutType + '\'' +
                ", bindModelType=" + bindModelType +
                ", fatherId='" + fatherId + '\'' +
                ", isNullDevice=" + isNullDevice +
                '}';
    }

    public String getDeviceName(Device device) {
        String name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
        if (TextUtils.isEmpty(name)) {
            name = (String) DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, "");
        }
        return name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MainWidgetBean that = (MainWidgetBean) o;
        return bindModelType == that.bindModelType && Objects.equals(pluginId, that.pluginId) && Objects.equals(subCategory, this.subCategory) && Objects.equals(layoutType, that.layoutType) && Objects.equals(fatherId, that.fatherId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pluginId, subCategory, layoutType, bindModelType, fatherId);
    }
}
