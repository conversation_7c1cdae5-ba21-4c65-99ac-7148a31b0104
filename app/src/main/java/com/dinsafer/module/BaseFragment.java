package com.dinsafer.module;


import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.CallSuper;
import androidx.annotation.CheckResult;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.add.ui.BleCheckBluetoothDialog;
import com.dinsafer.module.interfaces.IBaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.main.view.ReadyToArmDialogFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.BaseToastFragment;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.aop.annotations.Safer;
import com.trello.rxlifecycle.FragmentEvent;
import com.trello.rxlifecycle.FragmentLifecycleProvider;
import com.trello.rxlifecycle.LifecycleTransformer;
import com.trello.rxlifecycle.RxLifecycle;

import rx.Observable;
import rx.subjects.BehaviorSubject;

/**
 * Created by Rinfon on 16/6/14.
 */
public abstract class BaseFragment extends Fragment implements IBaseFragment, FragmentLifecycleProvider {

    public final String TAG = this.getClass().getName();

    private final BehaviorSubject<FragmentEvent> lifecycleSubject = BehaviorSubject.create();


    @Override
    @NonNull
    @CheckResult
    public final Observable<FragmentEvent> lifecycle() {
        return lifecycleSubject.asObservable();
    }

    @Override
    @NonNull
    @CheckResult
    public final <T> LifecycleTransformer<T> bindUntilEvent(@NonNull FragmentEvent event) {
        return RxLifecycle.bindUntilEvent(lifecycleSubject, event);
    }

    @Override
    @NonNull
    @CheckResult
    public final <T> LifecycleTransformer<T> bindToLifecycle() {
        return RxLifecycle.bindFragment(lifecycleSubject);
    }

    /**
     * Fragment Content view
     */
    private View inflateView;
    /**
     * 所属Activity
     */
    protected Activity activity;
    /**
     * 记录是否已经创建了,防止重复创建
     */
    private boolean viewCreated;

    /**
     * 显示Toast消息
     *
     * @param msg 消息文本
     */
    public final void showToast(@NonNull final String msg) {

        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.createBuilder(activity)
                        .setOk(getResources().getString(R.string.ok))
                        .setContent(msg)
                        .preBuilder()
                        .show();
            }
        });
    }

    public final void showSuccess() {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
//                AlertDialog.createBuilder(activity)
//                        .setOk("OK")
//                        .setIsShowSuccessView(true)
//                        .setContent(activity.getResources().getString(R.string.success))
//                        .preBuilder()
//                        .show();
//                ToastHUD.createBuilder(activity)
//                        .setIsShowSuccessView(true)
//                        .setIsSuccess(true)
//                        .setContent(getString(R.string.success))
//                        .preBuilder()
//                        .show();
                getDelegateActivity().showTopToast(activity.getResources().getString(R.string.success));
            }
        });
    }


    private AlertDialog errorDialog;

    public final void showErrorToast() {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (errorDialog != null && errorDialog.isShowing()) {
                    errorDialog.dismiss();
                }
                errorDialog = AlertDialog.createBuilder(activity)
                        .setOk(getResources().getString(R.string.ok))
                        .setContent(getResources().getString(R.string.failed_try_again))
                        .preBuilder();
                errorDialog.show();
            }
        });
    }

    public final void showErrorToast(String confirmStr, String tip) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (errorDialog != null && errorDialog.isShowing()) {
                    errorDialog.dismiss();
                }
                errorDialog = AlertDialog.createBuilder(activity)
                        .setOk(confirmStr)
                        .setContent(tip)
                        .preBuilder();
                errorDialog.show();
            }
        });
    }

    public void toOpenInput() {
        try {
            InputMethodManager imm = (InputMethodManager) getDelegateActivity().getSystemService(
                    Activity.INPUT_METHOD_SERVICE);
            imm.showSoftInput(getDelegateActivity().getCurrentFocus(), 0);
        } catch (Exception ex) {

        }
    }

    public void toCloseInput() {
        try {
            InputMethodManager imm = (InputMethodManager) getDelegateActivity().getSystemService(
                    Activity.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(getDelegateActivity().getCurrentFocus().getWindowToken(), 0);
        } catch (Exception ex) {

        }
    }

    /**
     * 基础消息页面
     *
     * @param title
     * @param content
     * @param btnText
     */
    public final void showMsgFragment(String title, boolean isSuccess, String content, String btnText) {
        getDelegateActivity().addCommonFragment(BaseToastFragment.newInstance(title, isSuccess, content, btnText));
    }


    public final void showLoadingFragment(int blackOrBlue) {
        getDelegateActivity().showLoadingFragment(blackOrBlue, "");
    }

    public final void showTimeOutLoadinFramgment() {
        getDelegateActivity().showTimeOutLoadinFramgment();
    }

    public final void showTimeOutLoadinFramgment(long milliseconds) {
        getDelegateActivity().showTimeOutLoadinFramgment(milliseconds);
    }

    public final void showTimeOutLoadinFramgmentWithErrorAlert() {
        getDelegateActivity().showTimeOutLoadinFramgmentWithErrorAlert();
    }

    public final void showTimeOutLoadinFramgmentWithCallBack(BaseMainActivity.ILoadingCallBack callback) {
        getDelegateActivity().showTimeOutLoadinFramgmentWithCallBack(callback);
    }

    public final void showTimeOutLoadinFramgmentWithCallBack(final long timeoutMillis, BaseMainActivity.ILoadingCallBack callback) {
        getDelegateActivity().showTimeOutLoadinFramgmentWithCallBack(timeoutMillis, callback);
    }

    public final void closeLoadingFragmentWithCallBack() {
        getDelegateActivity().closeLoadingFragmentWithCallBack();
    }


    public final void closeTimeOutLoadinFramgmentWithErrorAlert() {
        getDelegateActivity().closeTimeOutLoadinFramgmentWithErrorAlert();
    }

    public final void showBlueTimeOutLoadinFramgment() {
        getDelegateActivity().showBlueTimeOutLoadinFramgment();
    }

    public final void showTimeOutLoadinFramgmentWithBack() {
        getDelegateActivity().showTimeOutLoadinFramgmentWithBack();
    }

    public final void showBlueTimeOutLoadinFramgmentWithBack() {
        getDelegateActivity().showBlueTimeOutLoadinFramgmentWithBack();
    }

    public final void showLoadingFragment(int blackOrBlue, String description) {
        getDelegateActivity().showLoadingFragment(blackOrBlue, description);
    }

    public final void showTimeOutLoadinFramgmentWithMarginTop(int marginTop) {
        getDelegateActivity().showTimeOutLoadinFramgmentWithMarginTop(marginTop);
    }

    public final void closeLoadingFragment() {
        // 所有主动取消Loading的，取消超时处理逻辑
        getDelegateActivity().closeLoadingFragmentWithTimeOut();
    }

    /**
     * 输出日志
     *
     * @param msg
     */
    public final void i(@NonNull String msg) {
        DDLog.i(TAG, msg);
    }


    /**
     * 显示Toast消息
     *
     * @param resId 消息文本字符串资源ID
     */
    public final void showToast(@StringRes int resId) {
        showToast(getResources().getString(resId));
    }

//    /**
//     * 发送消息,用于各个组件之间通信
//     *
//     * @param event 消息事件对象
//     */
//    public final <EVENT extends BaseEvent> void sendMessage(@NonNull EVENT event) {
//        // 发布事件
//        EventBus.getDefault().post(event);
//    }

    @CallSuper
    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @CallSuper
    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        lifecycleSubject.onNext(FragmentEvent.ATTACH);
        this.activity = activity;
    }

    Animation baseEnterAnim = null;

    Animation baseOuterAnim = null;

    Handler animHandler = new Handler();

    Runnable finishAnimRunnable = new Runnable() {
        @Override
        public void run() {
            onFinishAnim();
        }
    };


    public Animation getBaseEnterAnim() {
        return baseEnterAnim;
    }

    public void setBaseEnterAnim(Animation baseEnterAnim) {
        this.baseEnterAnim = baseEnterAnim;
    }

    public Animation getBaseOuterAnim() {
        return baseOuterAnim;
    }

    public void setBaseOuterAnim(Animation baseOuterAnim) {
        this.baseOuterAnim = baseOuterAnim;
    }

    @Override
    public Animation onCreateAnimation(int transit, final boolean enter, int nextAnim) {
        Animation anim;

        if (baseEnterAnim != null && enter) {
            anim = baseEnterAnim;
        } else if (baseOuterAnim != null && !enter) {
            anim = baseOuterAnim;
        } else if (this instanceof LoadingFragment) {
            if (enter) {
                anim = AnimationUtils.loadAnimation(getActivity(), android.R.anim.fade_in);
            } else {
                anim = AnimationUtils.loadAnimation(getActivity(), android.R.anim.fade_out);
            }
            anim.setDuration(200);
        } else if (this instanceof ReadyToArmDialogFragment) {
            return null;
        } else {
            if (enter) {
                anim = AnimationUtils.loadAnimation(getActivity(), R.anim.fragment_slide_left_enter);
            } else {
                anim = AnimationUtils.loadAnimation(getActivity(), R.anim.fragment_slide_right_exit);
            }
        }
        anim.setAnimationListener(new Animation.AnimationListener() {
            public void onAnimationEnd(Animation animation) {
                if (enter) {
//                    滑动进入需要200ms,所以动画加载在完成后再进行加载
//                    animHandler.postDelayed(finishAnimRunnable, 200);
                }
            }

            public void onAnimationRepeat(Animation animation) {
            }

            public void onAnimationStart(Animation animation) {
            }
        });

        return anim;
    }

    public BaseMainActivity getDelegateActivity() {
        if (activity instanceof BaseMainActivity)
            return (BaseMainActivity) activity;
        else
            return null;
    }

    public MainActivity getMainActivity() {
        if (activity instanceof MainActivity)
            return (MainActivity) activity;
        else
            return null;
    }

    public void removeSelf() {
        ((BaseMainActivity) activity).removeCommonFragmentAndData(this, true);
    }


    @Override
    final public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        lifecycleSubject.onNext(FragmentEvent.CREATE);
        ActivityController.getInstance().addFragment(this);
        // 防止重复调用onCreate方法，造成在initData方法中adapter重复初始化问题
//        if (!viewCreated) {
//            viewCreated = true;
//            initData();
//        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        DDLog.d(TAG, "The opening fragment is " + getClass().getSimpleName());
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    final public void onViewCreated(View view, Bundle savedInstanceState) {
        lifecycleSubject.onNext(FragmentEvent.CREATE_VIEW);
        if (!viewCreated) {
            viewCreated = true;
            initDialog();
            initListener();
            animHandler.postDelayed(finishAnimRunnable, 200);
        }
    }

    @CallSuper
    @Override
    public void onStart() {
        super.onStart();
        lifecycleSubject.onNext(FragmentEvent.START);
    }

    @CallSuper
    @Override
    public void onDestroyView() {
        ActivityController.getInstance().removeFragment(this);
        lifecycleSubject.onNext(FragmentEvent.DESTROY_VIEW);
        toCloseInput();
        super.onDestroyView();
        // 解决ViewPager中的问题
        if (null != inflateView) {
            ((ViewGroup) inflateView.getParent()).removeView(inflateView);
        }
        if (errorDialog != null && errorDialog.isShowing()) {
            errorDialog.dismiss();
            errorDialog = null;
        }
    }

    public boolean onBackPressed() {
        return false;
    }

    @CallSuper
    @Override
    public void onPause() {
        lifecycleSubject.onNext(FragmentEvent.PAUSE);
        super.onPause();
    }

    @CallSuper
    @Override
    public void onStop() {
        lifecycleSubject.onNext(FragmentEvent.STOP);
        super.onStop();
    }

    @CallSuper
    @Override
    public void onDetach() {
        lifecycleSubject.onNext(FragmentEvent.DETACH);
        super.onDetach();
//        try {
//            Field childFragmentManager = Fragment.class.getDeclaredField("mChildFragmentManager");
//            childFragmentManager.setAccessible(true);
//            childFragmentManager.set(this, null);
//
//        } catch (NoSuchFieldException e) {
//            throw new RuntimeException(e);
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException(e);
//        }
    }

    @CallSuper
    @Override
    public void onResume() {
        super.onResume();
        lifecycleSubject.onNext(FragmentEvent.RESUME);
    }

    @CallSuper
    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    @CallSuper
    @Override
    public void onDestroy() {
        lifecycleSubject.onNext(FragmentEvent.DESTROY);
        super.onDestroy();
    }

    public static final int REQUEST_PERMISSION_CAMERA_CODE = 1;

    public static final int REQUEST_PERMISSION_AUDIO_CODE = 2;

    public static final int REQUEST_PERMISSION_LOCATION_CODE = 3;

    public static final int REQUEST_PERMISSION_STORAGE = 4;

    public static final int REQUEST_PERMISSION_READ_CONTACT = 5;

    public void requestCameraPermission() {
        getMainActivity().setNotNeedToLogin(true);
        String[] permissions = {Manifest.permission.CAMERA};
        requestPermissions(permissions, REQUEST_PERMISSION_CAMERA_CODE);
    }

    public void requestReadImagePermission() {
        getMainActivity().setNotNeedToLogin(true);
        String[] permissions = PermissionUtil.getStoragePermissions();
        requestPermissions(permissions, REQUEST_PERMISSION_STORAGE);
    }

    public void requestLocationPermisiions() {
        getMainActivity().setNotNeedToLogin(true);
        String[] permissions = PermissionUtil.getLocationPermissions();
        requestPermissions(permissions, REQUEST_PERMISSION_LOCATION_CODE);
    }


    public void requestAudioPermisiions() {
        getMainActivity().setNotNeedToLogin(true);
        String[] permissions = {
                Manifest.permission.RECORD_AUDIO};
        requestPermissions(permissions, REQUEST_PERMISSION_AUDIO_CODE);
    }

    public void requestReadContactPermisiions() {
        getMainActivity().setNotNeedToLogin(true);
        String[] permissions = {
                Manifest.permission.READ_CONTACTS};
        requestPermissions(permissions, REQUEST_PERMISSION_READ_CONTACT);
    }

    @Safer
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.d(TAG, "onRequestPermissionsResult-->requestCode:" + requestCode + "/grantResult:" + grantResults[0]);
        boolean granted = grantResults[0] == PackageManager.PERMISSION_GRANTED;
        if (requestCode == REQUEST_PERMISSION_CAMERA_CODE && (!granted || (granted && ContextCompat.checkSelfPermission(getActivity(),
                Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED))) {
            showPermissionNotGrantTip(getString(R.string.permission_tip_camera_not_grant));
            return;
        }

        if (requestCode == REQUEST_PERMISSION_STORAGE && (!granted || (granted && PermissionUtil.isStoragePermissionDeny(getActivity())))) {
            showPermissionNotGrantTip(getString(R.string.permission_tip_album_not_grant));
            return;
        }

        if (requestCode == REQUEST_PERMISSION_LOCATION_CODE && (!granted || (granted && PermissionUtil.isLocationPermissionsDeny(getActivity())))) {
            showPermissionNotGrantTip(getString(R.string.permission_tip_location_not_grant));
            return;
        }

        if (requestCode == REQUEST_PERMISSION_AUDIO_CODE && (!granted || (granted && ContextCompat.checkSelfPermission(getActivity(),
                Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED))) {
            showPermissionNotGrantTip(getString(R.string.permission_tip_record_audio_not_grant));
            return;
        }

        if (requestCode == REQUEST_PERMISSION_READ_CONTACT && (!granted || (granted && ContextCompat.checkSelfPermission(getActivity(),
                Manifest.permission.READ_CONTACTS)
                != PackageManager.PERMISSION_GRANTED))) {
            showPermissionNotGrantTip(getString(R.string.permission_tip_contact_not_grant));
            return;
        }
    }

    protected void showPermissionNotGrantTip(String tip) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getString(R.string.go_setting))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        goIntentSetting();
                    }
                })
                .setCancel(getString(R.string.cancel))
                .setContent(tip)
                .preBuilder()
                .show();
    }

    protected void goIntentSetting() {
        PermissionDialogUtil.goIntentSetting(getDelegateActivity());
    }

    @CallSuper
    @Override
    public void initData() {
    }

    @CallSuper
    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
    }

    @CallSuper
    @Override
    public void initListener() {
    }

    @CallSuper
    @Override
    public void initDialog() {
    }

    @Override
    public void onEnterFragment() {

    }

    @Override
    public void onPauseFragment() {

    }

    @Override
    public void onExitFragment() {

    }

    @Override
    public void onFinishAnim() {

    }

    public void onWindowFocusChanged(boolean hasFocus) {

    }


    public void toOpenGPS(int code) {
        final BleCheckBluetoothDialog dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_PHONE_GPS);
        dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
            @Override
            public void clickOk() {
                dialog.dismiss();
                startActivityForResult(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS),
                        code);
            }

            @Override
            public void clickCanal() {
                dialog.dismiss();
            }
        });
        dialog.show();

        // AlertDialog build = AlertDialog.createBuilder(getContext())
        //         .setOKListener(new AlertDialog.AlertOkClickCallback() {
        //             @Override
        //             public void onOkClick() {
        //                 startActivityForResult(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS),
        //                         code);
        //             }
        //         })
        //         .setOk(getResources().getString(R.string.permission_go_setting))
        //         .setContent(getResources().getString(R.string.openGPStip))
        //         .setCancel(getResources().getString(R.string.cancel))
        //         .preBuilder();
        // build.show();
    }

    public interface OpenGPSCancelCallback {
        void cancel();
    }

    public void toOpenGPS(int code, OpenGPSCancelCallback openGPSCancelCallback) {
        final BleCheckBluetoothDialog dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_PHONE_GPS);
        dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
            @Override
            public void clickOk() {
                dialog.dismiss();
                startActivityForResult(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS),
                        code);
            }

            @Override
            public void clickCanal() {
                dialog.dismiss();
                if (null != openGPSCancelCallback) {
                    openGPSCancelCallback.cancel();
                }
            }
        });
        dialog.show();
    }

    public void runOnMainThread(Runnable runnable) {
        if (activity != null) {
            activity.runOnUiThread(runnable);
        }
    }

    public int getResColor(int colorId) {
        return getContext().getResources().getColor(colorId);
    }
}
