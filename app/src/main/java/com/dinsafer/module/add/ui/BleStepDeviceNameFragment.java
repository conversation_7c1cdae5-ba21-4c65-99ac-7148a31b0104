package com.dinsafer.module.add.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.EditText;
import android.widget.ImageView;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.BleCloseTimerEvent;
import com.dinsafer.model.BleStartScanEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.add.PanelBinder;
import com.dinsafer.panel.add.bean.PanelCmdResult;
import com.dinsafer.panel.add.callback.IPanelCmdCallback;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * 现在已经没有设置主机名这一个步骤了
 * <p>
 * Created by Rinfon on 16/7/8.
 */
@Deprecated
public class BleStepDeviceNameFragment extends BaseFragment implements IPanelCmdCallback, TextWatcher {
    private final static String KEY_PANEL_TYPE = "PANEL_TYPE";
    private final static String KEY_PANEL_SUPPORT_4G = "SUPPORT_4G";

    LocalCustomButton apStepNext;

    EditText apStepDeviceName;
    ImageView loadingIcon;
    ImageView commonBarBack;
    BleStepIndicatorGroupView indicatorGroupView;

    private PanelBinder mPanelBinder;
    private int mPanelType;
    private boolean mSupport4G;


    public static BleStepDeviceNameFragment newInstance(int panelType, boolean support4G) {
        BleStepDeviceNameFragment apStepFragment = new BleStepDeviceNameFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_PANEL_TYPE, panelType);
        bundle.putBoolean(KEY_PANEL_SUPPORT_4G, support4G);
        apStepFragment.setArguments(bundle);
        return apStepFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.ble_step_device_name_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        EventBus.getDefault().register(this);
        mPanelType = getArguments().getInt(KEY_PANEL_TYPE);
        mSupport4G = getArguments().getBoolean(KEY_PANEL_SUPPORT_4G);
        initView(rootView, savedInstanceState);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.ap_step_exit).setOnClickListener( v -> toSetName());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toback());
    }

    private void __bindViews(View rootView) {
        apStepNext = rootView.findViewById(R.id.ap_step_exit);
        apStepDeviceName = rootView.findViewById(R.id.ap_step_device_name);
        loadingIcon = rootView.findViewById(R.id.loading_icon);
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        indicatorGroupView = rootView.findViewById(R.id.ap_step_indicator);
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        indicatorGroupView.initIndicators(LocalKey.BLE_ADD_PANEL_STEP_TOTAL, 0);
    }

    @Override
    public void initData() {
        super.initData();
        apStepNext.setLocalText(getResources().getString(R.string.Next));
        apStepDeviceName.setText(Local.s("My NExT 001"));
        apStepDeviceName.addTextChangedListener(this);

        operatingAnim = AnimationUtils.loadAnimation(DinSaferApplication.getAppContext(), R.anim.rotation);
        LinearInterpolator lin = new LinearInterpolator();
        operatingAnim.setInterpolator(lin);
        loadingIcon.setImageResource(R.drawable.icon_main_btn_loading);
        loadingIcon.setVisibility(View.GONE);
    }

    private boolean findPanelBinder() {
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof PanelBinder)) {
            DDLog.e(TAG, "Error panel binder.");
            return false;
        }
        mPanelBinder = (PanelBinder) pluginBinder;
        mPanelBinder.addPanelCmdResultListener(this);
        return true;
    }

    public void toSetName() {
        if (TextUtils.isEmpty(apStepDeviceName.getText())) {
            return;
        }
        showTimeOutLoadingAnim();

        mPanelBinder.setDeviceName(apStepDeviceName.getText().toString());
    }

    @Override
    public boolean onBackPressed() {
        return isNoCanBack;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelBinder) {
            mPanelBinder.removePanelCmdResultListener(this);
            mPanelBinder.disconnectAllBle();
        }
        clean();
        EventBus.getDefault().post(new BleStartScanEvent());
        EventBus.getDefault().unregister(this);
        apStepDeviceName.removeTextChangedListener(this);
    }

    public void toback() {
        if (isNoCanBack) {
            return;
        }
        removeSelf();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (!findPanelBinder()) {
            showErrorToast();
            removeSelf();
        }
    }

    private Animation operatingAnim;
    private Subscription subscribe;

    private void showTimeOutLoadingAnim() {
        loadingIcon.startAnimation(operatingAnim);
        loadingIcon.setVisibility(View.VISIBLE);
        apStepNext.setLocalText("");
        apStepNext.setEnabled(false);
        commonBarBack.setEnabled(false);
        apStepDeviceName.setEnabled(false);
        isNoCanBack = true;
        if (subscribe != null && !subscribe.isUnsubscribed()) {
            subscribe.unsubscribe();
        }
        subscribe = Observable.interval(APIKey.BLE_OPERATE_TIMEOUT, TimeUnit.MILLISECONDS)
                .take(1)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        fail();
                    }
                });
    }

    private void closeLoadingAnimAndResume() {
        loadingIcon.clearAnimation();
        loadingIcon.setVisibility(View.GONE);
        apStepNext.setLocalText(getResources().getString(R.string.Next));
        apStepNext.setEnabled(true);
        commonBarBack.setEnabled(true);
        apStepDeviceName.setEnabled(true);
        isNoCanBack = false;
    }

    private boolean isNoCanBack = false;

    /**
     * 失败处理
     */
    private void fail() {
        DDLog.d(TAG, "onFail");
        showErrorToast();
        clean();
    }

    /**
     * 清空处理：定时器、loading
     */
    private void clean() {
        DDLog.d(TAG, "clean");
        if (subscribe != null && !subscribe.isUnsubscribed()) {
            subscribe.unsubscribe();
        }
        closeLoadingAnimAndResume();
    }


    /**
     * 关闭定时器，用于其他弹窗出现的时候————ble断开的时候
     *
     * @param ev
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleCloseTimerEvent ev) {
        clean();
    }


    @Override
    public void onPanelResult(PanelCmdResult panelCmdResult) {
        DDLog.i(TAG, "onPanelResult： " + panelCmdResult);
        /**
         *  int, 1:完成，0:失败， 2:result返回SSID
         *  SSID会一个一个的返回，直到status=1为结束
         */
        final int status = panelCmdResult.getStatus();
        final String cmd = panelCmdResult.getCmd();
        if (TextUtils.isEmpty(cmd)) {
            DDLog.e(TAG, "Empty panel adder cmd.");
            return;
        }

        switch (cmd) {
            case LocalKey.BLE_CMD_SET_DEVICE_NAME:
                if (status == 1) {
                    clean();
                    getDelegateActivity().addCommonFragment(BleStepDeivcePasswordFragment.newInstance(mPanelType, mSupport4G));
                } else {
                    fail();
                }
                break;
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        final int textLen = s.length();
        if (0 >= textLen || 40 < textLen) {
            apStepNext.setAlpha(0.5f);
            apStepNext.setEnabled(false);
        } else {
            apStepNext.setAlpha(1.0f);
            apStepNext.setEnabled(true);
        }
    }
}

