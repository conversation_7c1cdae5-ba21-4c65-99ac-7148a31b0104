package com.dinsafer.module.settting.ui;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.event.GoAddMoreEvent;
import com.dinsafer.module.BaseFragmentActivity;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.ActivityManager;

import org.greenrobot.eventbus.EventBus;

/**
 * WebView
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/7/2 2:20 PM
 */
public class WebViewActivity extends BaseFragmentActivity {
    private static final String TAG = WebViewActivity.class.getSimpleName();
    private static final String KEY_URL = "url";

    LocalTextView commonBarTitle;

    WebView mWebview;

    private String mUrl;

    public static Intent getStartIntent(Context context, String url) {
        Intent startIntent = new Intent(context, WebViewActivity.class);
        startIntent.putExtra(KEY_URL, url);
        return startIntent;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mWebview.destroy();
    }


    public void close() {
        onBackPressed();
    }

    @Override
    protected boolean initVariables() {
        return true;
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        Log.d(TAG, "onCreate: ");
        setContentView(R.layout.activity_webview);
        __bindViews();
        __bindClicks();
        mUrl = getIntent().getStringExtra(KEY_URL);
    }

    private void __bindClicks() {
        findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews() {
        commonBarTitle = findViewById(R.id.common_bar_title);
        mWebview = findViewById(R.id.setting_webview);
    }

    @Override
    protected void loadData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.help_title));
        mWebview.getSettings().setJavaScriptEnabled(true);
        mWebview.getSettings().setAllowFileAccess(true);
        mWebview.setBackgroundColor(Color.TRANSPARENT);
        mWebview.getSettings()
                .setPluginState(WebSettings.PluginState.ON);
        // 解决跳转蓝牙扫描需要定位权限错误的问题
        // https://github.com/fullstackreact/react-native-oauth/issues/87
        mWebview.getSettings().setUserAgentString(mWebview.getSettings().getUserAgentString().replace("; wv", ""));
        mWebview.setWebViewClient(new WebViewClient() {

            public void onPageFinished(WebView view, String url) {
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url.equals(APIKey.ADD_MORE_URI)) {
                    EventBus.getDefault().post(new GoAddMoreEvent());
                    ActivityManager.get().finishAllActivityExcludeMain();
                    return true;
                }
                return super.shouldOverrideUrlLoading(view, url);
            }
        });
        mWebview.loadUrl(mUrl);
    }

    @Override
    public void onBackPressed() {
        if (mWebview.canGoBack()) {
            mWebview.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
