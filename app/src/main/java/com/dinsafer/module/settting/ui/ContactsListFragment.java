package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;

import com.dinsafer.aop.annotations.Safer;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.ContactItem;
import com.dinsafer.model.PhoneZoneCloseEvent;
import com.dinsafer.model.UpdatePluginNumber;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.ContactsListAdapter;
import com.dinsafer.module.settting.interfaces.IUpdataList;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ContactsListFragment extends BaseFragment implements IUpdataList, ChoosePhoneZoneFragment.IChoosePhoneZoneCallBack {

    LocalTextView commonBarTitle;

    ListView contactListView;
    LocalTextView contactNoSimHint;
    ImageView ivAdd;

    private ContactsListAdapter contactsListAdapter;

    private ArrayList<ContactItem> arrayList = new ArrayList<ContactItem>();
    private ArrayList<ContactItem> members = new ArrayList<>();
    private ArrayList<ContactItem> contacts = new ArrayList<>();

    private final AtomicInteger mResultCount = new AtomicInteger(2);


    public static ContactsListFragment newInstance() {
        return new ContactsListFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.contact_list_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener(v -> close());
        ivAdd.setOnClickListener(view -> toAddRegisterMember());
        contactListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                toItemClick(parent, view, position, id);
            }
        });
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        ivAdd = rootView.findViewById(R.id.iv_add);
        contactListView = rootView.findViewById(R.id.contact_list_view);
        contactNoSimHint = rootView.findViewById(R.id.contact_no_sim_hint);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_member_setting));
        contactNoSimHint.setLocalText(getResources().getString(R.string.sim_hint));
    }

    @Safer
    public void updataSIM() {
        if (DeviceHelper.getInt(DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID()),
                PanelDataKey.Panel.SIM_STATUS, 1) != 1) {
            contactNoSimHint.setVisibility(View.VISIBLE);
        } else {
            contactNoSimHint.setVisibility(View.GONE);
        }
    }


    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        showTimeOutLoadinFramgment();
        DinSDK.getHomeInstance().queryHomeMemberList(HomeManager.getInstance().getCurrentHome().getHomeID(),
                new IDefaultCallBack2<List<HomeMember>>() {
                    @Override
                    public void onSuccess(List<HomeMember> homeMembers) {
                        members.clear();
                        ContactItem contactItem;
                        for (HomeMember homeMember : homeMembers) {
                            contactItem = new ContactItem();
                            contactItem.setType(ContactsListAdapter.TYPE_ITEM);
                            contactItem.setSms(homeMember.isSms());
                            contactItem.setPush(homeMember.isPush());
                            contactItem.setPhoto(homeMember.getAvatar());
                            contactItem.setBindPhone(homeMember.isBind_phone());
                            contactItem.setUid(homeMember.getUid());
                            contactItem.setUserId(homeMember.getUser_id());
//                            contactItem.setPush_info(homeMember.isPush_info());
                            contactItem.setPush_sos(homeMember.isPush_sos());
                            contactItem.setPush_sys(homeMember.isPush_sys());
//                            contactItem.setSms_info(homeMember.isSms_info());
                            contactItem.setSms_sos(homeMember.isSms_sos());
                            contactItem.setSms_sys(homeMember.isSms_sys());
                            contactItem.setPermission(homeMember.getLevel());
                            contactItem.setName(homeMember.getUid());
                            members.add(contactItem);
                        }

                        mResultCount.decrementAndGet();
                        if (ContactsListFragment.this.isAdded()) {
                            createListView();
                        }

                        HomeManager.getInstance().getFamilyMemberAvatars();
                    }

                    @Override
                    public void onError(int i, String s) {
                        mResultCount.decrementAndGet();
                        DDLog.e(TAG, "Error: i: " + i + ", s: " + s);
                        if (ContactsListFragment.this.isAdded()) {
                            createListView();
                        }
                    }
                });

        DinSDK.getHomeInstance().listHomeContact(HomeManager.getInstance().getCurrentHome().getHomeID(),
                new IDefaultCallBack2<List<HomeContact>>() {
                    @Override
                    public void onSuccess(List<HomeContact> homeContacts) {
                        contacts.clear();
                        ContactItem contactItem;
                        for (HomeContact homeContact : homeContacts) {
                            contactItem = new ContactItem();
                            contactItem.setType(ContactsListAdapter.TYPE_OTHER);
                            contactItem.setSms(homeContact.isSms());
                            contactItem.setPhone(homeContact.getPhone());
                            contactItem.setName(homeContact.getName());
                            contactItem.setTime(homeContact.getAddtime());
//                            contactItem.setSms_info(homeContact.isSms_info());
                            contactItem.setSms_sos(homeContact.isSms_sos());
                            contactItem.setSms_sys(homeContact.isSms_sys());
                            contactItem.setContactid(homeContact.getContact_id());
                            contacts.add(contactItem);
                        }

                        mResultCount.decrementAndGet();
                        if (ContactsListFragment.this.isAdded()) {
                            createListView();
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        mResultCount.decrementAndGet();
                        DDLog.e(TAG, "Error: i: " + i + ", s: " + s);
                        if (ContactsListFragment.this.isAdded()) {
                            createListView();
                        }
                    }
                });
    }

    /**
     * 添加其他已注册的用户
     */
    private void toAddRegisterMember() {
        if (HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.ADMIN) {
            ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(getResources().getString(R.string.change_permission_admin)),
                            Local.s(getResources().getString(R.string.change_permission_guest)),
                            Local.s(getString(R.string.what_is_the_difference)))
                    .setCancelableOnTouchOutside(true)
                    .setLastButtonTextColor(getContext().getResources().getColor(R.color.permission_description_text_color))
                    .setListener(new ActionSheet.ActionSheetListener() {
                        @Override
                        public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                        }

                        @Override
                        public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                            if (2 == index) {
                                getMainActivity().addCommonFragment(UserPermissionDescriptionFragment.newInstance());
                                return;
                            }

                            int level = LocalKey.GUEST;
                            if (index == 0) {
                                level = LocalKey.ADMIN;
                            } else if (index == 1) {
                                level = LocalKey.GUEST;
                            }
                            toShareQrCode(level);
                        }
                    }).show();
        } else if (HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.USER) {
            ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(getResources().getString(R.string.change_permission_user)),
                            Local.s(getResources().getString(R.string.change_permission_guest)),
                            Local.s(getString(R.string.what_is_the_difference)))
                    .setCancelableOnTouchOutside(true)
                    .setLastButtonTextColor(getContext().getResources().getColor(R.color.permission_description_text_color))
                    .setListener(new ActionSheet.ActionSheetListener() {
                        @Override
                        public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                        }

                        @Override
                        public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                            if (2 == index) {
                                getMainActivity().addCommonFragment(UserPermissionDescriptionFragment.newInstance());
                                return;
                            }

                            int level = LocalKey.GUEST;
                            if (index == 0) {
                                level = LocalKey.USER;
                            }
                            toShareQrCode(level);
                        }
                    }).show();
        }
    }

    private void toShareQrCode(int level) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getHomeInstance()
                .getInvitationFamilyMemberCode(
                        HomeManager.getInstance().getCurrentHome().getHomeID(),
                        level, new IDefaultCallBack2<String>() {
                            @Override
                            public void onSuccess(String s) {
                                getDelegateActivity().addCommonFragment(ShareQR.newInstance(s,
                                        HomeManager.getInstance().getCurrentHome().getHomeName(), level));
                            }

                            @Override
                            public void onError(int i, String s) {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                            }
                        });
    }

    /**
     * 添加手机号用户
     */
    private void toAddContactMember() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.contact_add))
                        , Local.s(getResources().getString(R.string.contact_add_input)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            ContactsEditFragment contactsEditFragment = ContactsEditFragment.newInstance(true, null);
                            contactsEditFragment.setCallBack(ContactsListFragment.this);
                            getDelegateActivity().addCommonFragment(contactsEditFragment);
                        } else if (index == 1) {
                            if (DDSystemUtil.isMarshmallow() && ContextCompat.checkSelfPermission(getActivity(),
                                    Manifest.permission.READ_CONTACTS)
                                    != PackageManager.PERMISSION_GRANTED) {
                                requestReadContactPermisiions();
                                return;
                            }
                            if (null == DinSDK.getUserInstance().getUser()
                                    || TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone())) {
                                toShowPhoneZoneDialog(ChoosePhoneZoneFragment.getCachePhoneZone());
                            } else {
                                if (!TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone())) {
                                    String[] phone = DinSDK.getUserInstance().getUser().getPhone().split(" ");
                                    if (phone != null && phone.length >= 2) {
                                        for (int i = 0; i < ChoosePhoneZoneFragment.countryCodes.length; i++) {
                                            if (phone[0].equals(ChoosePhoneZoneFragment.countryCodes[i])) {
                                                toShowPhoneZoneDialog(phone[0] + " " + ChoosePhoneZoneFragment.countryNames[i]);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }).show();

    }

    public void toShowPhoneZoneDialog(String initValue) {

        if (TextUtils.isEmpty(initValue))
            initValue = ChoosePhoneZoneFragment.countryCodes[APIKey.DEFAULT_PHONE_INDEX] + " "
                    + ChoosePhoneZoneFragment.countryNames[APIKey.DEFAULT_PHONE_INDEX];

        final String finalInitValue = initValue;
        PhoneZoneDialog.createBuilder(getMainActivity())
                .setOk(getResources().getString(R.string.change_phone_confirm))
                .setCancel(getResources().getString(R.string.cancel))
                .setContent(getResources().getString(R.string.change_phone_zone_hint))
                .setPhoneZoneValue(initValue)
                .setOKListener(new PhoneZoneDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(PhoneZoneDialog dialog, final String phoneZone) {
                        if (TextUtils.isEmpty(phoneZone))
                            return;
                        dialog.dismiss();
                        ContactsPickerFragment contactsPickerFragment = ContactsPickerFragment.newInstance();
                        contactsPickerFragment.setCallBack(new ContactsPickerFragment.IContactCallBack() {
                            @Override
                            public void onSelect(ArrayList<HashMap<String, String>> selectList) {
                                toAddContacts(phoneZone, selectList);
                            }
                        });
                        getDelegateActivity().addCommonFragment(contactsPickerFragment);
                    }

                    @Override
                    public void onPhoneZoneClick(PhoneZoneDialog dialog) {
                        dialog.dismiss();
                        ChoosePhoneZoneFragment fragment = ChoosePhoneZoneFragment.newInstance(finalInitValue, true);
                        fragment.setCallBack(ContactsListFragment.this);
                        getDelegateActivity().addCommonFragment(fragment);

                    }
                }).preBuilder()
                .show();
    }

    private void toAddContacts(String phonezone, ArrayList<HashMap<String, String>> selectList) {
        Log.e(TAG, "toAddContacts: " + "call back");
        if (selectList.size() > 0) {
            Log.e(TAG, "toAddContacts: " + "call back  in");
            showTimeOutLoadinFramgment();

            ArrayList<AddContactParams.ContactBean> contacts = new ArrayList<>();
            AddContactParams.ContactBean contact;
            for (HashMap<String, String> stringStringHashMap : selectList) {
                contact = new AddContactParams.ContactBean(stringStringHashMap.get("name"),
                        phonezone + " " + stringStringHashMap.get("phone"));
                contacts.add(contact);
            }

            AddContactParams params = new AddContactParams.Builder()
                    .setSms(true)
                    .setSms_sos(true)
                    .setSms_info(false)
                    .setSms_sys(false)
                    .addContacts(contacts)
                    .createAddContactParams();

            DinSDK.getHomeInstance().newHomeContact(HomeManager.getInstance().getCurrentHome().getHomeID(),
                    params, new IDefaultCallBack() {
                        @Override
                        public void onSuccess() {
                            onFinishAnim();
                            // 更新用户数量
                            if (null != HomeManager.getInstance().getMemberAvatars()) {
                                HomeManager.getInstance().getMemberAvatars().setTotal(
                                        HomeManager.getInstance().getMemberAvatars().getTotal() + selectList.size());
                                EventBus.getDefault().post(new UpdatePluginNumber());
                            }
                        }

                        @Override
                        public void onError(int i, String s) {
                            closeLoadingFragment();
                            showErrorToast();
                        }
                    });
        }
    }

    @Safer
    private void createListView() {
        DDLog.i(TAG, "createListView");
        if (0 < mResultCount.get()) {
            DDLog.i(TAG, "还需要等待另一个接口结果返回");
            return;
        }

        arrayList.clear();
        arrayList.addAll(members);

        if (CommonDataUtil.getInstance().isHadPanel()) {
            ContactItem otherHeader = new ContactItem();
            otherHeader.setType(ContactsListAdapter.TYPE_HEADER);
            otherHeader.setName(getResources().getString(R.string.invite_member_without_app));
            arrayList.add(otherHeader);
            arrayList.addAll(contacts);
        }

        contactsListAdapter = new ContactsListAdapter(getDelegateActivity(), arrayList);
        contactsListAdapter.setOnHeaderAddClickListener((view, tittle) -> {
            if (getString(R.string.contact_header_main).equals(tittle)) {
                toAddRegisterMember();
            } else if (getString(R.string.invite_member_without_app).equals(tittle)) {
//                toAddContactMember();
                ContactsEditFragment contactsEditFragment = ContactsEditFragment.newInstance(true, null);
                contactsEditFragment.setCallBack(ContactsListFragment.this);
                getDelegateActivity().addCommonFragment(contactsEditFragment);
            }
        });
        contactListView.setAdapter(contactsListAdapter);
        contactsListAdapter.notifyDataSetChanged();
        closeLoadingFragment();
        updataSIM();
    }

    public void toItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        final ContactItem clickedItem = arrayList.get(i);
        if (clickedItem.getType() == ContactsListAdapter.TYPE_HEADER) {
            return;
        }
        final String clickedUid = clickedItem.getUid();
        if (clickedItem.getType() == ContactsListAdapter.TYPE_ITEM) {
            final boolean editable = HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.ADMIN
                    || (HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.USER
                    && clickedUid != null
                    && DinSDK.getUserInstance().getUser() != null
                    && clickedUid.equals(DinSDK.getUserInstance().getUser().getUid()));

            if (!editable) {
                return;
            }

            int adminCount = 0;
            for (int j = 0; j < arrayList.size(); j++) {
                if (arrayList.get(j).getPermission() == LocalKey.ADMIN) {
                    adminCount++;
                }
            }
            ChangePermissionFragment fragment = ChangePermissionFragment.newInstance(clickedItem, adminCount);
            fragment.setCallback(this);
            getDelegateActivity().addCommonFragment(fragment);
            return;
        }

        if (clickedItem.getType() == ContactsListAdapter.TYPE_OTHER) {
            if (HomeManager.getInstance().getCurrentHome().getLevel() != LocalKey.ADMIN) {
                return;
            }
            ContactsEditFragment contactsEditFragment = ContactsEditFragment.newInstance(false, clickedItem);
            contactsEditFragment.setCallBack(ContactsListFragment.this);
            getDelegateActivity().addCommonFragment(contactsEditFragment);
        }
    }


    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void reload() {
        onFinishAnim();
    }

    @Override
    public void updata() {
        if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo()
                .getPermission() == LocalKey.GUEST || DeviceInfoHelper.getInstance().getCurrentDeviceInfo()
                .getPermission() == LocalKey.USER) {
            removeSelf();
            getMainActivity().smoothToHome();
        } else {
            contactsListAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void deletItem(ContactItem data) {
        HomeManager.getInstance().getFamilyMemberAvatars();

        final String userId = data.getUserId();
        final String uid = data.getUid();
        final String contactId = data.getContactid();
        if (!TextUtils.isEmpty(userId)) {
            for (int i = 0; i < arrayList.size(); i++) {
                if (userId.equals(arrayList.get(i).getUserId())) {
                    arrayList.remove(i);
                    break;
                }
            }
        } else if (!TextUtils.isEmpty(uid)) {
            for (int i = 0; i < arrayList.size(); i++) {
                if (uid.equals(arrayList.get(i).getUid())) {
                    arrayList.remove(i);
                    break;
                }
            }
        } else if (!TextUtils.isEmpty(contactId)) {
            for (int i = 0; i < arrayList.size(); i++) {
                if (contactId.equals(arrayList.get(i).getContactid())) {
                    arrayList.remove(i);
                    break;
                }
            }
        }
        contactsListAdapter.notifyDataSetChanged();
    }


    @Override
    public void onResult(String code, String name) {
        toShowPhoneZoneDialog(code + " " + name);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PhoneZoneCloseEvent ev) {
        toShowPhoneZoneDialog(ev.value);
    }
}


