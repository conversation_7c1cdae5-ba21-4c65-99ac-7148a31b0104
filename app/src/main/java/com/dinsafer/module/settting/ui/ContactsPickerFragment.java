package com.dinsafer.module.settting.ui;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.ContactsContract;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ContactPickerLayoutBinding;
import com.dinsafer.model.ContactBean;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.adapter.ContactsPickerAdapterNew;

import java.util.ArrayList;
import java.util.HashMap;

import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ContactsPickerFragment extends MyBaseFragment<ContactPickerLayoutBinding> {
    private ArrayList<ContactBean> resultList;

    private ContactsPickerAdapterNew mContactAdapter;

    private IContactCallBack callBack;

    public static ContactsPickerFragment newInstance() {
        return new ContactsPickerFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.contact_picker_layout;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonBarBack.setOnClickListener(view -> removeSelf());
    }

    /**
     * 根据关键字搜索
     */
    private void search(String str) {
        if (TextUtils.isEmpty(str)) {
            mContactAdapter.refreshList(resultList);
        } else {
            ArrayList<ContactBean> searchList = new ArrayList<>();
            int size = resultList.size();
            for (int i = 0; i < size; i++) {
                ContactBean bean = resultList.get(i);
                if (bean.isContains(str)) {
                    searchList.add(bean);
                }
            }
            mContactAdapter.refreshList(searchList);
            mBinding.contactPickerListview.scrollTo(0, 0);
        }
    }


    /**
     * 获取通讯录
     */
    public static ArrayList<ContactBean> getAllContacts(Context context) {
        ArrayList<ContactBean> resultList = new ArrayList<>(100);
        //生成ContentResolver对象
        ContentResolver contentResolver = context.getContentResolver();
        // 获得所有的联系人
        /*Cursor cursor = contentResolver.query(
                ContactsContract.Contacts.CONTENT_URI, null, null, null, null);
         */
        //这段代码和上面代码是等价的，使用两种方式获得联系人的Uri
        Cursor cursor = contentResolver.query(Uri.parse("content://com.android.contacts/contacts"), null, null, null, null);

        try {
            // 循环遍历
            if (cursor != null && cursor.moveToFirst()) {
                int idColumn = cursor.getColumnIndex(ContactsContract.Contacts._ID);
                int displayNameColumn = cursor.getColumnIndex(ContactsContract.Contacts.DISPLAY_NAME);
                do {
                    // 获得联系人的ID
                    String contactId = cursor.getString(idColumn);
                    // 获得联系人姓名
                    String displayName = cursor.getString(displayNameColumn);
                    //显示获得的联系人信息
                    // 查看联系人有多少个号码，如果没有号码，返回0
                    int phoneCount = cursor.getInt(cursor
                            .getColumnIndex(ContactsContract.Contacts.HAS_PHONE_NUMBER));
                    if (phoneCount > 0) {
                        // 获得联系人的电话号码列表
                        Cursor phoneCursor = contentResolver.query(
                                ContactsContract.CommonDataKinds.Phone.CONTENT_URI, null,
                                ContactsContract.CommonDataKinds.Phone.CONTACT_ID
                                        + "=" + contactId, null, null);
                        try {
                            if (phoneCursor != null && phoneCursor.moveToFirst()) {
                                do {
                                    //遍历所有的联系人下面所有的电话号码
                                    String phoneNumber = phoneCursor.getString(phoneCursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER));
                                    resultList.add(new ContactBean(phoneNumber, displayName));
                                } while (phoneCursor.moveToNext());
                            }
                        } catch (Exception ignored) {
                        } finally {
                            if (phoneCursor != null) {
                                phoneCursor.close();
                            }
                        }
                    }
                } while (cursor.moveToNext());
            }
        } catch (Exception ignored) {
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return resultList;
    }


    public void toSave() {
        if (callBack != null && resultList != null) {
            ArrayList<HashMap<String, String>> selectList = new ArrayList<>();
            int size = resultList.size();
            for (int i = 0; i < size; i++) {
                ContactBean contactBean = resultList.get(i);
                if (contactBean.isCheck) {
                    HashMap<String, String> contactMap = new HashMap<>(4);
                    contactMap.put("name", contactBean.getName());
                    contactMap.put("phone", contactBean.getPhone());
                    selectList.add(contactMap);
                }
            }
            callBack.onSelect(selectList);
        }
        removeSelf();
    }

    public IContactCallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(IContactCallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.contact_add_input));
        mBinding.commonBarLeft.setOnClickListener(v -> toSave());
        showLoadingFragment(LoadingFragment.BLUE);
        Observable.create((Observable.OnSubscribe<ArrayList<ContactBean>>) subscriber -> {
                    ArrayList<ContactBean> contactBeanList = getAllContacts(getDelegateActivity());
                    subscriber.onNext(contactBeanList);
                    subscriber.onCompleted();
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(this.bindToLifecycle())
                .subscribe(new Subscriber<ArrayList<ContactBean>>() {
                    @Override
                    public void onCompleted() {
                        if (getActivity() instanceof MainActivity) {
                            ((MainActivity) getActivity()).closeLoadingFragment();
                        }
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(ArrayList<ContactBean> resultList) {
                        ContactsPickerFragment.this.resultList = resultList;
                        mContactAdapter = new ContactsPickerAdapterNew(resultList);
                        mBinding.contactPickerListview.setAdapter(mContactAdapter);
                    }
                });

        mBinding.contactSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                search(editable.toString());
            }
        });
    }

    public interface IContactCallBack {
        void onSelect(ArrayList<HashMap<String, String>> selectList);
    }

}

