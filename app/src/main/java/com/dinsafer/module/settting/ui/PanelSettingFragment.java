package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPanelSettingBinding;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.BleConfigNetFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.main.view.DeviceStatusDetailFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/12/6 3:41 下午
 */
public class PanelSettingFragment extends MyBaseFragment<FragmentPanelSettingBinding>
        implements IDeviceCallBack {

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static PanelSettingFragment newInstance() {
        return new PanelSettingFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_panel_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        findPanel();
        mBinding.commonBar.commonBarTitle.setLocalText(R.string.panel);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.advancedSettingSosTime.setLocalText(getResources().getString(R.string.advanced_setting_sos_time));
        mBinding.tvDeviceArmDisarmSound.setLocalText(getResources().getString(R.string.advanced_setting_device_sound));

        mBinding.rlAdvancedSettingSosTime.setOnClickListener(v -> {
            showLoadingFragment(LoadingFragment.BLACK);
            mPanelDevice.submit(PanelParamsHelper.getSirenTime());
        });
        mBinding.switchDeviceArmDisarmSound.setOnTouchListener(new View.OnTouchListener() {
            int flag = 0;

            @Override
            public boolean onTouch(View v, MotionEvent event) {

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        flag = 0;
                        return true;
//                    case MotionEvent.ACTION_MOVE:
//                        flag = 1;
//                        return true;
                    case MotionEvent.ACTION_UP:
                        if (flag == 0) {
                            changeDeviceArmDisArmSoundStatus();
                        }
                        return true;
                }

                return false;
            }
        });

        mBinding.rlPanelStatus.setOnClickListener(v -> getDelegateActivity().addCommonFragment(DeviceStatusDetailFragment.newInstance()));
        mBinding.rlAdvanceSetting.setOnClickListener(v -> getDelegateActivity().addCommonFragment(AdvancedSettingFragment.newInstance()));
        mBinding.rlConfigPanelNetwork.setOnClickListener(v -> toChangeNetwork());
        mBinding.rlDeletePanel.setOnClickListener(v -> getDelegateActivity().addCommonFragment(DeletePanelFragment.newInstance()));

        // if (!CommonDataUtil.getInstance().isShowDeviceArmHomeArmSound()) {
        //     mBinding.rlDeviceArmDisarmSound.setVisibility(View.GONE);
        // }
    }


    public void toChangeNetwork() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                PermissionDialogUtil.showNeedBleLocationPermissionDialog(getMainActivity());
                return;
            }
            if (!DDSystemUtil.isOpenGPS(getContext())) {
                toOpenGPS(0);
                return;
            }
        } else {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), null);
                return;
            }
        }
        getMainActivity().addCommonFragment(BleConfigNetFragment.newInstance());
    }

    @Override
    public void initData() {
        super.initData();
        if (null == mPanelDevice) {
            return;
        }

        boolean panelOnline = CommonDataUtil.getInstance().isPanelOnline();
        boolean upgrading = DeviceHelper.getBoolean(mPanelDevice, PanelDataKey.Panel.UPGRADING, false);
        panelOnline = panelOnline && !upgrading;
        if (panelOnline) {
            // 主机在线
            mBinding.llPanelOnlineSetting.setAlpha(MainPanelHelper.VIEW_ENABLE_ALPHA);
            mBinding.llPanelOnline.setVisibility(View.VISIBLE);
            mBinding.llPanelOffline.setVisibility(View.GONE);
        } else {
            // 主机离线
            // 升级中也显示离线样式了
            mBinding.llPanelOnlineSetting.setAlpha(MainPanelHelper.VIEW_DISABLE_ALPHA);
            mBinding.llPanelOnline.setVisibility(View.GONE);
            mBinding.llPanelOffline.setVisibility(View.VISIBLE);
        }
        mBinding.llPanelOnlineSetting.setVisibility(View.VISIBLE);
        mBinding.vPanelOnlineDivider.setVisibility(View.VISIBLE);
        mBinding.rlAdvancedSettingSosTime.setEnabled(panelOnline);
        mBinding.switchDeviceArmDisarmSound.setEnabled(panelOnline);

        showLoadingFragment(LoadingFragment.BLACK);
        mPanelDevice.submit(PanelParamsHelper.getAdvancedSetting());
    }

    private void changeDeviceArmDisArmSoundStatus() {
        showLoadingFragment(LoadingFragment.BLACK);
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.setArmSound(!mBinding.switchDeviceArmDisarmSound.isOn()));
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPanelDevice != null) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    private void toChooseSirenTime(List<Integer> options, int settledTime) {
        ArrayList<String> time = new ArrayList<>();
        final List<Integer> data = options;
        int defaultIndex = 0;
        if (data != null) {
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i) == settledTime) {
                    defaultIndex = i;
                }
                if (data.get(i) == 0) {
                    time.add(Local.s(getResources().getString(R.string.silent)));
                } else {
                    time.add(data.get(i) + "min");
                }
            }
            TimePicker picker = TimePicker.newInstance(Local.s(getResources().getString(R.string.advanced_setting_sos_time)),
                    time, defaultIndex);
            picker.setCallBack(new TimePicker.ITimePickerCallBack() {
                @Override
                public void getSelect(String select, int selectIndex) {
                    showLoadingFragment(LoadingFragment.BLUE);
                    isSelfOperate = true;
                    mPanelDevice.submit(PanelParamsHelper.setSirenTime(data.get(selectIndex)));
                }
            });

            getDelegateActivity().addCommonFragment(picker);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        DDLog.i(TAG, "On advance setting cmd result: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (PanelCmd.GET_ADVANCED_SETTING.equals(cmd)) {
            // 获取高级设置信息
            onGetAdvanceSettingInfo(status, map);
        } else if (PanelCmd.GET_SIRENTIME.equals(cmd)) {
            // 获取主机报警鸣响时长信息
            onGetSirenTimeInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_ARM_SOUND.equals(cmd)) {
                // 设置主机布撤防提示音
                onSetArmSound(status, map);
            } else if (PanelCmd.SET_SIRENTIME.equals(cmd)) {
                // 设置主机布撤防提示音
                onSetSirenTimeInfo(status, map);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置高级设置信息
     */
    private void onGetAdvanceSettingInfo(int status, Map map) {
        DDLog.i(TAG, "onGetAdvanceSettingInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        boolean isOn = DeviceHelper.getBoolean(resultMap, PanelDataKey.AdvancedSetting.IS_ON, false);
        boolean offlineSms = DeviceHelper.getBoolean(resultMap, PanelDataKey.AdvancedSetting.OFFLINE_SMS, false);
        String panelName = DeviceHelper.getString(resultMap, PanelDataKey.AdvancedSetting.PANEL_NAME, "");
        mBinding.switchDeviceArmDisarmSound.setOn(isOn);
    }


    /**
     * 设置延时布防信息
     */
    private void onSetArmSound(int status, Map map) {
        DDLog.i(TAG, "onSetArmSound, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            mBinding.switchDeviceArmDisarmSound.setOn(!mBinding.switchDeviceArmDisarmSound.isOn());
        } else {
            showErrorToast();
        }
    }

    /**
     * 获取之前设置的主机报警鸣笛时长
     */
    private void onGetSirenTimeInfo(int status, Map map) {
        DDLog.i(TAG, "onGetSirenTimeInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        List<Integer> options = DeviceHelper.getList(resultMap, PanelDataKey.OPTIONS);
        int time = DeviceHelper.getInt(resultMap, PanelDataKey.TIME_COMMON, 0);
        toChooseSirenTime(options, time);
    }

    /**
     * 设置的主机报警鸣笛时长
     */
    private void onSetSirenTimeInfo(int status, Map map) {
        DDLog.i(TAG, "onSetSirenTimeInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            showSuccess();
        } else {
            showErrorToast();
        }
    }
}
