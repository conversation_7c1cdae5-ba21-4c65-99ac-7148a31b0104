package com.dinsafer.module.settting.ui;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;

import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ChangeEmailLayoutBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.user.modify.ChangeEmailVerifyCodeFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import androidx.annotation.NonNull;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ChangeEmailFragment extends MyBaseFragment<ChangeEmailLayoutBinding> {

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    public static ChangeEmailFragment newInstance() {
        return new ChangeEmailFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.change_email_layout;
    }

    @Override
    public void initData() {
        super.initData();
        if (null == DinSDK.getUserInstance().getUser()
                || TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail())) {
            mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.mail_setting));
        } else {
            mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.change_the_binding));
        }
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBackground.setOnClickListener(v -> closeInput());
        mBinding.etAccount.setHint(Local.s(getString(R.string.email_address)));
        mBinding.btnNext.setLocalText(getResources().getString(R.string.next));
        mBinding.etAccount.addTextChangedListener(mWatcher);
        mBinding.btnNext.setOnClickListener(v -> {
            setNextBtnEnable(false);
            onNextClick();
        });
        updateBtnStateEnable();
    }

    private void updateBtnStateEnable() {
        final String account = mBinding.etAccount.getText().toString().trim();
        boolean enable = !TextUtils.isEmpty(account);
        setNextBtnEnable(enable);
    }

    private void setNextBtnEnable(final boolean enable) {
        mBinding.btnNext.setAlpha(enable ? 1.0f : 0.5f);
        mBinding.btnNext.setEnabled(enable);
    }

    private void onNextClick() {
        final String account = mBinding.etAccount.getText().toString().trim();

        if (TextUtils.isEmpty(account)
                || !RegxUtil.isEmail(account)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.email_format_illegal));
            setNextBtnEnable(true);
            return;
        }

        toGetMessage(account);
    }

    private void toGetMessage(@NonNull final String account) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setNextBtnEnable(true);
        });
        DinSDK.getUserInstance().bindEmail(account, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeLoadingFragmentWithCallBack();
                if (ChangeEmailFragment.this.isAdded()) {
                    if (i == ErrorCode.ERROR_EMAIL_EXIST)
                        getMainActivity().showTopToast(R.drawable.icon_toast_fail, getResources().getString(R.string.error_email_exist));
                    else {
                        showErrorToast();
                    }
                } else {
                    showErrorToast();
                }
                setNextBtnEnable(true);
            }

            @Override
            public void onSuccess() {
                closeLoadingFragmentWithCallBack();
                if (!ChangeEmailFragment.this.isAdded()) {
                    return;
                }
                getMainActivity().addCommonFragment(ChangeEmailVerifyCodeFragment.newInstance(account));
                setNextBtnEnable(true);
            }
        });
    }

    public void closeInput() {
        toCloseInput();
    }

}

