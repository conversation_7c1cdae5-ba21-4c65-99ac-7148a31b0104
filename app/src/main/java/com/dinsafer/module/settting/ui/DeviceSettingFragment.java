package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.interpolator.view.animation.LinearOutSlowInInterpolator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.aop.annotations.MethodTrace;
import com.dinsafer.common.Constants;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.IPCKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.activtor.api.base.IPluginScanCallback;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DeviceSettingLayoutBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamBleScanFragment;
import com.dinsafer.model.AddAccessoryEvent;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.FamilyInfoUpdateEvent;
import com.dinsafer.model.FamilySwitchEvent;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.model.PluginUpdata;
import com.dinsafer.model.QRRequestResultEvent;
import com.dinsafer.model.ScanQREvent;
import com.dinsafer.model.SetSOSEvent;
import com.dinsafer.model.UpdatePluginNumber;
import com.dinsafer.model.UserPermissonUpdata;
import com.dinsafer.model.UserUidChangeEvent;
import com.dinsafer.model.event.DeviceDeletedStateChangedEvent;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.model.event.GetDeviceInfoEvent;
import com.dinsafer.model.event.GetFamilyMemberAvatarsEvent;
import com.dinsafer.model.event.GetPluginQuantityEvent;
import com.dinsafer.model.event.PanelUpgradeStateChangeEvent;
import com.dinsafer.model.event.UpdateFamilyPanelStateEvent;
import com.dinsafer.model.event.WidgetFlagDeletedEvent;
import com.dinsafer.model.family.FamilyListChangeEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.BLeStepScanDeviceFragment;
import com.dinsafer.module.add.ui.BleConfigNetFragment;
import com.dinsafer.module.doorbell.add.DsDoorbellBleScanFragment;
import com.dinsafer.module.doorbell.chime.AddChimeFragment;
import com.dinsafer.module.doorbell.chime.ChimeSettingFragment;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.main.view.DeviceStatusDetailFragment;
import com.dinsafer.module.powerstation.device.PSBleScanFragment;
import com.dinsafer.module.powerstation.event.BmtListUpdateEvent;
import com.dinsafer.module.settting.ui.model.DeviceSettingPlugProvider;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.activator.PluginActivatorManager;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.BindRecyclerViewUtil;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.NavigatorUtil;
import com.dinsafer.util.PermissionUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * Created by Rinfon on 16/6/20.
 */
public class DeviceSettingFragment extends MyBaseFragment<DeviceSettingLayoutBinding>
        implements ActionSheet.ActionSheetListener, IDeviceCallBack,
        IPluginScanCallback {

    private String messageId;
    private Device mPanelDevice;

    private BindMultiAdapter<BaseBindModel> adapter;

    /**
     * 是否正在获取配件数量信息
     */
    private boolean mIsPluginAmountLoading = false;
    private boolean mIsMemberAvatarLoading = false;

    /**
     * 是否当前页面触发的设置推送语言
     */
    private boolean mCurrentSetPushMessage;

    PluginActivatorManager mPluginActivtor;

    private boolean mIsFloatingActionBtnShow = false;

    public static DeviceSettingFragment newInstance() {
        return new DeviceSettingFragment();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        getDelegateActivity().setTheme(R.style.ActionSheetStyleiOS7);
        mBinding.fab.setOnClickListener(v -> toAddAccessory());
        adapter = new BindMultiAdapter<>();
        adapter.setHeaderAndEmpty(true);
        mBinding.rv.setDrawingCacheEnabled(true);
        mBinding.rv.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);
        mBinding.rv.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rv.setAdapter(adapter);
        mBinding.rv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (!mIsFloatingActionBtnShow) {
                    return;
                }
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    scaleShow(mBinding.fab);
                } else {
                    scaleHide(mBinding.fab);
                }
            }
        });

    }

    @Override
    public void initData() {
        super.initData();
        updataUserBind();
        updateUserPermission();
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: ");
        refresh();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (mPanelDevice != null) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        destoryPluginActivtor();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.device_setting_layout;
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        if (index == 0) {
            getMainActivity().setNotNeedToLogin(true);
            ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE_FAMILY);
        } else {
            getDelegateActivity().addCommonFragment(TiggleDeviceFragment.newInstance());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        updataUserBind();
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PluginUpdata ev) {
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(GetDeviceInfoEvent ev) {
        refresh();
        updataUserBind();
        updateUserPermission();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(GetPluginQuantityEvent ev) {
        // 开始加载或加载出错都显示正在loading，只有成功才显示数量
//        mIsPluginAmountLoading = ev.isStart()
//                || !ev.isSuccess();
        if (!ev.isStart() && ev.isSuccess()) {
            refresh();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(GetFamilyMemberAvatarsEvent ev) {
        // 开始加载或加载出错都显示正在loading，只有成功才显示数量
//        mIsMemberAvatarLoading = ev.isStart() || !ev.isSuccess();
        if (!ev.isStart() && ev.isSuccess()) {
            refresh();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(UserPermissonUpdata ev) {
//        updataItem();
        refresh();
        updateUserPermission();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(FamilySwitchEvent event) {
        i("FamilySwitchEvent: updata");
        updateUserPermission();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(FamilyInfoUpdateEvent event) {
        i("FamilyInfoUpdateEvent: updata");
        updateUserPermission();
    }


    @Subscribe
    public void onEventMainThread(UserUidChangeEvent event) {
        updataUserBind();
        updateUserPermission();
    }

    @Subscribe
    public void onEvent(UpdatePluginNumber ev) {
//        updataItem();
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(UpdateFamilyPanelStateEvent ev) {
        refresh();
        if (null != CommonDataUtil.getInstance().getCurrentPanelID()
                && null != DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID())) {
            if (null == mPanelDevice) {
                mPanelDevice = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID());
                mPanelDevice.registerDeviceCallBack(this);
            } else if (mPanelDevice != DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID())) {
                mPanelDevice.unregisterDeviceCallBack(this);
                mPanelDevice = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID());
                mPanelDevice.registerDeviceCallBack(this);
            }
        } else {
            if (null != mPanelDevice) {
                mPanelDevice.unregisterDeviceCallBack(this);
                mPanelDevice = null;
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(AddAccessoryEvent ev) {
        toAddAccessory();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(SetSOSEvent ev) {
        toSosMessage();
    }


    public void onEventMainThread(DeviceResultEvent ev) {
        if (ev.getCmdType().equals(LocalKey.SET_SOSTEXT) && ev.getMessageId().equals(messageId)) {
            closeLoadingFragment();
        }
    }

    @Subscribe
    public void onEvent(GetDeviceInfoEvent ev) {
        if (!ev.isSuccess()) {
            return;
        }
//        updateUI(CommonDataUtil.getInstance().getMultiDataEntry());
//        updataItem();
        refresh();
        updataUserBind();
        updateUserPermission();
    }

    @MethodTrace()
    private void refresh() {
        if (getContext() == null) {
            return;
        }

        DDLog.d("Bind", "refresh()");
        adapter.removeAllHeaderView();
        adapter.setNewData(null);
        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                DeviceSettingPlugProvider.getFamilyManageItems(this, APIKey.IS_SETTING_ITEM_HAVE_LOADING,
                        mIsMemberAvatarLoading)));
        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                DeviceSettingPlugProvider.getEnergyItems(this, APIKey.IS_SETTING_ITEM_HAVE_LOADING,
                        mIsMemberAvatarLoading)));
        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                DeviceSettingPlugProvider.getIpcItems(this, APIKey.IS_SETTING_ITEM_HAVE_LOADING,
                        mIsPluginAmountLoading)));
        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                DeviceSettingPlugProvider.getAccessoryItems(this, APIKey.IS_SETTING_ITEM_HAVE_LOADING,
                        mIsPluginAmountLoading)));
        if (APIKey.IS_SHOW_MORE_SUPPORT) {
            adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(), DeviceSettingPlugProvider.getMoreSupport(this)));
        }
        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                DeviceSettingPlugProvider.getCustomRuleItem(this, APIKey.IS_SETTING_ITEM_HAVE_LOADING,
                        mIsPluginAmountLoading)));
        // adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
        //         DeviceSettingPlugProvider.getPanelSettingItem(this, APIKey.IS_SETTING_ITEM_HAVE_LOADING,
        //                 mIsPluginAmountLoading)));
        adapter.addHeaderView(BindRecyclerViewUtil.getModelView(getContext(),
                DeviceSettingPlugProvider.getBottomPaddingItem(this)));
    }

    public static final int OVERLAY_PERMISSION_REQ_CODE = 1235;

    public boolean checkPerms() {

        if (DDSystemUtil.isMarshmallow() && PermissionUtil.isStoragePermissionDeny(getDelegateActivity())
                || PermissionUtil.isStoragePermissionDeny(getDelegateActivity())
                || ContextCompat.checkSelfPermission(getDelegateActivity(),
                Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
            requestAudioPermission();
            return false;
        }

        // Checking if device version > 22 and we need to use new permission model
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP_MR1) {
            // Checking if we can draw window overlay
            if (!Settings.canDrawOverlays(getDelegateActivity())) {
                // Requesting permission for window overlay(needed for all react-native apps)
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getActivity().getPackageName()));
                startActivityForResult(intent, OVERLAY_PERMISSION_REQ_CODE);
                return false;
            }

        }
        return true;
    }


    public void requestAudioPermission() {
        getMainActivity().setNotNeedToLogin(true);
        List<String> list = new ArrayList<>();
        list.addAll(Arrays.asList(PermissionUtil.getStoragePermissions()));
        list.addAll(Arrays.asList(PermissionUtil.getStoragePermissions()));
        list.add(Manifest.permission.RECORD_AUDIO);
        String[] permissions = list.toArray(new String[list.size()]);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            getMainActivity().requestPermissions(permissions, REQUEST_PERMISSION_CAMERA_CODE);
        }
    }

    private void updataUserBind() {
        mBinding.deviceSetttingHint.setVisibility(View.GONE);
//        if (null != DinSDK.getUserInstance().getUser() &&
//                !TextUtils.isEmpty(UserManager.getInstance().getUser().getPhone()) &&
//                !TextUtils.isEmpty(UserManager.getInstance().getUser().getEmail())) {
//            deviceSetttingHint.setVisibility(View.GONE);
//        } else {
//            String hint1 = Local.s(getResources().getString(R.string.device_settting_hint));
//            String hint2 = Local.s(getResources().getString(R.string.device_settting_hint_set));
//            deviceSetttingHint.setText(hint1);
//
//            SpannableString spStr = new SpannableString(hint2);
//
//            spStr.setSpan(new ClickableSpan() {
//                @Override
//                public void updateDrawState(TextPaint ds) {
//                    super.updateDrawState(ds);
//                    ds.setColor(getResources().getColor(R.color.text_blue_1));       //设置文字颜色
//                    ds.setUnderlineText(true);      //设置下划线
//                }
//
//                @Override
//                public void onClick(View widget) {
////                    getDelegateActivity().addCommonFragment(BindAccountFragment.newInstance());
//                }
//            }, 0, hint2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//
//            deviceSetttingHint.setHighlightColor(Color.BLUE); //设置点击后的颜色为透明，否则会一直出现高亮
//            deviceSetttingHint.append(spStr);
//            deviceSetttingHint.setMovementMethod(LinkMovementMethod.getInstance());
//            deviceSetttingHint.setVisibility(View.VISIBLE);
//        }
    }

    private boolean joinHome(String qrCode) {
        if (qrCode.startsWith(DinConst.SCAN_TYPE_HOME)) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            DinSDK.getHomeInstance().verifyInvitationFamilyMemberCode(qrCode, new IDefaultCallBack2<Home>() {
                @Override
                public void onSuccess(Home home) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    EventBus.getDefault().post(new FamilyListChangeEvent());
                    HomeManager.getInstance().changeFamily(home.getHomeID());
                    getMainActivity().removeAllCommonFragment();
                    getMainActivity().smoothToHome();
                    EventBus.getDefault().post(new QRRequestResultEvent(true));
//                    getMainActivity().showLoadingFragment(LoadingFragment.BLACK, "");
                }

                @Override
                public void onError(int i, String s) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    if (com.dinsafer.config.ErrorCode.ERROR_REACH_MAX_AMOUNT_OF_FAMILY == i) {
//                        showToast(getString(R.string.reach_max_amount_of_family));
                        EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.reach_max_amount_of_family)));
                    } else if (com.dinsafer.config.ErrorCode.ERROR_REACH_MAX_MEMBER_OF_FAMILY == i) {
//                        showToast(getString(R.string.reach_max_amount_of_family));
                        EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.reach_max_amount_of_family)));
                    } else if (com.dinsafer.config.ErrorCode.ERROR_ALREADY_AT_FAMILY == i) {
//                        showToast(getString(R.string.already_at_family));
                        EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.already_at_family)));
                    } else {
//                        showErrorToast();
                        EventBus.getDefault().post(new QRRequestResultEvent(false));
                    }
                }
            });
            return true;
        }
        return false;
    }

    private void createPluginActivtor() {
        destoryPluginActivtor();
        mPluginActivtor = DinSDK.getPluginActivtor();
        mPluginActivtor.setup(getContext(), PanelManager.getInstance().getCurrentPanelId(),
                PanelManager.getInstance().getCurrentPanelToken());
        mPluginActivtor.addScanCallBack(this);
    }

    private void destoryPluginActivtor() {
        if (mPluginActivtor != null) {
            mPluginActivtor.destroyActivator();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(final ScanQREvent ev) {
        final String qrCode = ev.getResult();
        DDLog.i(TAG, "qrCode: " + qrCode);
        if (TextUtils.isEmpty(qrCode)) {
            DDLog.e(TAG, "未识别到二维码");
            return;
        }

        // 加入家庭
        if (joinHome(qrCode)) {
            DDLog.i(TAG, "家庭二维码");
            return;
        }

        // 配件或其他类型的二维码
        showTimeOutLoadinFramgmentWithErrorAlert();
        createPluginActivtor();
        if (qrCode.equals(DinConst.TYPE_BMT_POWERSTORE2)) {
            mPluginActivtor.scan(DinConst.TYPE_BMT_POWERSTORE);
        } else {
            mPluginActivtor.scan(qrCode);
        }
        DDLog.i(TAG, "开始解析二维码对应的配件信息");
    }

    private void isSirenAdd(String key, Plugin plugin) {
        closeTimeOutLoadinFramgmentWithErrorAlert();
//        NavigatorUtil.getInstance().toModifyPlugsNameFragment("", key, true, true);
        try {
//            是ipc
            JSONObject jsonObject = new JSONObject(key);
            if (TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "t"))) {
                if (jsonObject.getInt("wave") == 1) {
                    ModifyPlugsFragment modifyPlugsFragment = ModifyPlugsFragment.newInstance("",
                            key, true, false, true, true);
                    getDelegateActivity().addCommonFragment(modifyPlugsFragment);
                } else {
                    NavigatorUtil.getInstance().toModifyPlugsNameFragment("", key, true, true);
                }
            } else {
                NavigatorUtil.getInstance().toModifyPlugsNameFragment("", key, true, true);
            }
        } catch (Exception ex) {
//            不是ipc
            NavigatorUtil.getInstance().toModifyPlugsNameFragment("", key, true, true, plugin);
        }
    }


    private ActionSheet dialog;

    public void toAddAccessory() {
        //没有修改其他配件（IS_OPEN_OTHER_PLUGIN为true）的情况，而是增加了一个触发配对的开关
        if (APIKey.IS_OPEN_OTHER_PLUGIN || AppConfig.Functions.SUPPORT_TRIGGER_ADD_PLUGIN) {
            dialog = ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(getResources().getString(R.string.device_management_add_scanQR)),
                            Local.s(getResources().getString(R.string.devie_management_add_tiggle)))
                    .setCancelableOnTouchOutside(true)
                    .setListener(this).show();
        } else {
            // dialog = ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
            //         .setTitle(false)
            //         .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
            //         .setOtherButtonTitles(Local.s(getResources().getString(R.string.device_management_add_scanQR)))
            //         .setCancelableOnTouchOutside(true)
            //         .setListener(this).show();
            getMainActivity().setNotNeedToLogin(true);
            ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE_FAMILY);
        }
    }

    public void toSosMessage() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (TextUtils.isEmpty(panelId)
                || null == DinHome.getInstance().getDevice(panelId)) {
            showErrorToast();
            return;
        }

        mCurrentSetPushMessage = true;
        showLoadingFragment(LoadingFragment.BLUE);
        DinHome.getInstance().getDevice(panelId).submit(PanelParamsHelper.getPanelMessage());
    }

    /**
     * 根据用户权限更新
     */
    private void updateUserPermission() {
        //只有管理员和用户才能添加设备
        if (mBinding.fab != null) {
            if (HomeManager.getInstance().getCurrentHome() != null &&
                    (HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.ADMIN
                            || HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.USER)) {
                mIsFloatingActionBtnShow = true;
                mBinding.fab.show();
            } else {
                mIsFloatingActionBtnShow = false;
                mBinding.fab.hide();
            }
        }
    }

    private static LinearOutSlowInInterpolator IN_INTERPOLATOR = new LinearOutSlowInInterpolator();

    /**
     * 滑动弹出按钮
     *
     * @param view
     */
    private void scaleShow(View view) {
        view.setVisibility(View.VISIBLE);
        ViewCompat.animate(view)
                .translationX(0)
                .alpha(1.0f)
                .setDuration(800)
                .setInterpolator(IN_INTERPOLATOR)
                .start();
    }

    /**
     * 滑动隐藏按钮
     *
     * @param view
     */
    private void scaleHide(View view) {
        view.setVisibility(View.VISIBLE);
        ViewCompat.animate(view)
                .translationX(300)
                .alpha(0f)
                .setDuration(800)
                .setInterpolator(IN_INTERPOLATOR)
                .start();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || !deviceId.equals(CommonDataUtil.getInstance().getCurrentPanelID())) {
            return;
        }
        final Device panel = DinHome.getInstance().getDevice(deviceId);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        switch (cmd) {
            case PanelCmd.GET_PLUGSANDMEMBERS:
                DDLog.i(TAG, "配件数量或用户数据更新");
                mIsPluginAmountLoading = false;
                refresh();
                break;
            case PanelCmd.GET_PANEL_MESSAGE:
                DDLog.i(TAG, "设置推送语言");
                if (!mCurrentSetPushMessage) {
                    return;
                }
                Map<String, Object> result = DeviceHelper.getMap(panel, PanelDataKey.CmdResult.RESULT);
                String lang = DeviceHelper.getString(result, PanelDataKey.PushLanguage.LANG, "");
                closeLoadingFragment();
                getDelegateActivity().addCommonFragment(ChangeMessageFragment.newInstance(false, lang));
                break;
        }
    }

    @Override
    public void onScanResult(int code, Plugin plugin) {
        DDLog.i(TAG, "onScanResult, code: " + code + "，plugin: " + plugin);
        closeTimeOutLoadinFramgmentWithErrorAlert();

        // ODM 不需要提示未添加主机
        // if (isPanelPlugin(plugin)
        //         && (!CommonDataUtil.getInstance().isHadPanelNotDeleted()
        //         || !CommonDataUtil.getInstance().isPanelOnline())) {
        //     DDLog.e(TAG, "不能在没有主机或主机离线的情况下通过扫码添加RF配件");
        //     showAddRFPluginOnPanelStateError();
        //     return;
        // }

        if (ErrorCode.ACTIVTOR_ALREAD_HAS_PLUGIN == code) {
            if (PluginConstants.TYPE_1F.equals(plugin.getPluginTypeName())) {
//                showToast(getResources().getString(R.string.ipc_has_plug));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.ipc_has_plug)));
            } else {
//                showToast(getResources().getString(R.string.tiggle_has_plug));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.tiggle_has_plug)));
            }
            return;
        } else if (ErrorCode.ACTIVTOR_BIND_IPC_REACHED_LIMIT == code) {
//            showToast(getResources().getString(R.string.the_number_of_devices_has_reached_its_limit));
            EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.the_number_of_devices_has_reached_its_limit)));
            return;
        } else if (ErrorCode.ACTIVTOR_ILLEGAID == code) {
//            showToast(getResources().getString(R.string.illegal_ID));
            EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            return;
        }

        if (1 != code || null == plugin) {
            DDLog.e(TAG, "Error, code != 1 or plugin is null.");
//            showErrorToast();
            EventBus.getDefault().post(new QRRequestResultEvent(false));
            return;
        }

        // 扫码到主机二维码
        if (DinConst.TYPE_PANEL.equals(plugin.getPluginTypeName())) {
            if (!AppConfig.Plugins.SUPPORT_PANEL) {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
                return;
            }
            EventBus.getDefault().post(new QRRequestResultEvent(true));
            if (CommonDataUtil.getInstance().isHadPanelNotDeleted()) {
                final String panelId = plugin.getQrCode();
                if (null != panelId
                        && panelId.equals(CommonDataUtil.getInstance().getCurrentPanelID())) {
                    boolean online = CommonDataUtil.getInstance().isPanelOnline();
                    if (online) {
                        getDelegateActivity().addCommonFragment(DeviceStatusDetailFragment.newInstance());
                    } else {
                        getDelegateActivity().addCommonFragment(BLeStepScanDeviceFragment.newInstance(panelId));
                    }
                } else {
                    showFamilyHadPanelDialog();
                }
            } else {
                getDelegateActivity().addCommonFragment(BLeStepScanDeviceFragment.newInstance());
            }
            return;
        }

        //dscam
        if (DinConst.TYPE_DSCAM.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_DSCAM_V005) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(DsCamBleScanFragment.newInstance(DinConst.TYPE_DSCAM));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        //dscam v006
        if (DinConst.TYPE_DSCAM_VOO6.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_DSCAM_V006) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(DsCamBleScanFragment.newInstance(DinConst.TYPE_DSCAM_VOO6));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        //dscam v015
        if (DinConst.TYPE_DSCAM_VO15.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_DSCAM_V015) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(DsCamBleScanFragment.newInstance(DinConst.TYPE_DSCAM_VO15));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        //doorbell
        if (DinConst.TYPE_DSDOORBELL.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_DS_DOORBELL) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(DsDoorbellBleScanFragment.newInstance());
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        //chime
        if (DinConst.TYPE_CHIME.equals(plugin.getPluginTypeName())) {
            if (!AppConfig.Plugins.SUPPORT_DS_DOORBELL) {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
                return;
            }

            if (IPCManager.getInstance().getAllDoorbellDevice() == null
                    || IPCManager.getInstance().getAllDoorbellDevice().size() == 0) {
                showNoDoorbellTip();
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                return;
            }
            EventBus.getDefault().post(new QRRequestResultEvent(true));
            if (ActivityController.getInstance().getFragment(AddChimeFragment.class) != null) {
                //添加门铃进来
                getDelegateActivity().addCommonFragment(ChimeSettingFragment.newInstance(((AddChimeFragment) ActivityController.getInstance().getFragment(AddChimeFragment.class)).getTargetDoorbellId(),
                        plugin.getPluginID(),
                        null,
                        ChimeSettingFragment.FROM_TYPE_ADD_DOORBELL));
            } else {
                //直接扫码进来
                getDelegateActivity().addCommonFragment(ChimeSettingFragment.newInstance("",
                        plugin.getPluginID(),
                        null,
                        ChimeSettingFragment.FROM_TYPE_SCAN));
            }
            return;
        }

        // 电池
        if (DinConst.TYPE_BMT_HP5000.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_BMT_HP5000) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_HP5000));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }
        if (DinConst.TYPE_BMT_HP5001.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_BMT_HP5001) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_HP5001));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }
        if (DinConst.TYPE_BMT_POWERCORE20.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE20) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERCORE20));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        if (DinConst.TYPE_BMT_POWERCORE30.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE30) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERCORE30));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        if (DinConst.TYPE_BMT_POWERSTORE.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_BMT_POWERSTORE) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERSTORE));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        if (DinConst.TYPE_BMT_POWERPULSE.equals(plugin.getPluginTypeName())) {
            if (AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE) {
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERPULSE));
            } else {
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            }
            return;
        }

        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(plugin.getSourceData());
        } catch (Exception e) {
            DDLog.e(TAG, "Error on new JSONObject");
            e.printStackTrace();
            jsonObject = new JSONObject();
        }
        final String sType = DDJSONUtil.getString(jsonObject, "stype");
        final String pluginTypeName = plugin.getPluginTypeName();

        if (AppConfig.Plugins.SUPPORT_PANEL && !TextUtils.isEmpty(plugin.getQrCode())) {
            DDLog.i(TAG, "QR CODE NOT NULL.");
            EventBus.getDefault().post(new QRRequestResultEvent(true));
            NavigatorUtil.getInstance().toModifyPlugsNameFragment("", plugin.getPluginID(),
                    plugin.getQrCode(), true, true, plugin);
            return;
        }

        if (AppConfig.Plugins.SUPPORT_PANEL && !plugin.getPluginID().startsWith("!")) {
            DDLog.i(TAG, "添加ID不是以!开头的配件");
            EventBus.getDefault().post(new QRRequestResultEvent(true));
            isSirenAdd(plugin.getPluginID(), plugin);
            return;
        }

        if (AppConfig.Plugins.SUPPORT_PANEL && IPCKey.NEW_QR_TYPE_DOORBELL.equals(String.valueOf(plugin.getPluginID().charAt(1)))) {
            DDLog.i(TAG, "NEW_QR_TYPE_DOORBELL");
            EventBus.getDefault().post(new QRRequestResultEvent(true));
            ModifyDoorBellFragment fragment =
                    ModifyDoorBellFragment.newInstance(0, "",
                            plugin.getPluginID(), true, plugin.getSourceData());
            getMainActivity().addCommonFragment(fragment);
            return;
        }

        if (AppConfig.Plugins.SUPPORT_PANEL && !APIKey.IS_SHOW_RELAY && DDJSONUtil.getString(jsonObject, "stype").equals("12")) {
            DDLog.e(TAG, "不支持12类型的继电器");
//            showToast(getResources().getString(R.string.illegal_ID));
            EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            return;
        }

        if (AppConfig.Plugins.SUPPORT_PANEL && !APIKey.IS_SHOW_SMART_BUTTON && DDJSONUtil.getString(jsonObject, "stype").equals("3B")) {
            DDLog.e(TAG, "不支持3B类型的SmartButton");
//            showToast(getResources().getString(R.string.illegal_ID));
            EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            return;
        }

        if (TextUtils.isEmpty(pluginTypeName)) {
            DDLog.e(TAG, "Empty plugin type name!!!!");
//            showToast(getResources().getString(R.string.illegal_ID));
            EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            return;
        }

        switch (pluginTypeName) {
            case PluginConstants.TYPE_1F:
                onScanResultIpc(code, plugin);
                break;
            case "WIFI Bulb":
            case "WIFI Plug":
//                showToast(getResources().getString(R.string.illegal_ID));
                EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
                break;
            default:
                if (!AppConfig.Plugins.SUPPORT_PANEL) {
//                    showToast(getResources().getString(R.string.illegal_ID));
                    EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
                    break;
                }
                DDLog.i(TAG, "添加其他类型配件");
                if (isPanelPlugin(plugin)
                        && (!CommonDataUtil.getInstance().isHadPanelNotDeleted()
                        || !CommonDataUtil.getInstance().isPanelOnline())) {
                    DDLog.e(TAG, "不能在没有主机或主机离线的情况下通过扫码添加RF配件");
                    EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.no_panel_add_plugin_hint)));
                    break;
                }
                EventBus.getDefault().post(new QRRequestResultEvent(true));
                Builder builder = new Builder();
                builder.setId(plugin.getPluginID())
                        .setAdd(true)
                        .setOffical(true)
                        .setShowDelete(false)
                        .setShowwave(false)
                        .setData(jsonObject);
                getDelegateActivity().addCommonFragment(ModifyASKPlugsFragment.newInstance(builder, plugin));
                break;
        }
    }

    private void showNoDoorbellTip() {
        AlertDialog.createBuilder(getContext())
                .setOk(getString(R.string.add))
                .setOKListener(() -> {
                    ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
                })
                .setCancel(getString(R.string.cancel))
                .setContent(getString(R.string.no_video_doorbell_tip))
                .preBuilder()
                .show();
    }

    /**
     * 是否主机配件
     *
     * @return true: 主机配件
     */
    private boolean isPanelPlugin(Plugin plugin) {
        if (null != plugin && !TextUtils.isEmpty(plugin.getPluginTypeName())) {
            final String pluginTypeName = plugin.getPluginTypeName();
            return !PluginConstants.TYPE_1F.equals(pluginTypeName)
                    && !"WIFI Bulb".equals(pluginTypeName)
                    && !"WIFI Plug".equals(pluginTypeName)
                    && !DinConst.TYPE_DSCAM.equals(pluginTypeName)
                    && !DinConst.TYPE_PANEL.equals(pluginTypeName)
                    && !DinConst.TYPE_DSCAM_VOO6.equals(pluginTypeName)
                    && !DinConst.TYPE_DSCAM_VO15.equals(pluginTypeName)
                    && !DinConst.TYPE_DSDOORBELL.equals(pluginTypeName)
                    && !DinConst.TYPE_BMT_HP5000.equals(pluginTypeName)
                    && !DinConst.TYPE_BMT_HP5001.equals(pluginTypeName)
                    && !DinConst.TYPE_BMT_POWERCORE20.equals(pluginTypeName)
                    && !DinConst.TYPE_BMT_POWERCORE30.equals(pluginTypeName);
        }

        return true;
    }

    /**
     * 家庭已经有主机提示
     */
    private void showFamilyHadPanelDialog() {
        AlertDialog.createBuilder(getContext())
                .setOk("OK")
                .setOKListener(() -> {
                    getMainActivity().removeAllCommonFragment();
                    getMainActivity().smoothToHome();
                })
                .setContent(getString(R.string.ble_step_had_panel_hint))
                .preBuilder()
                .show();
    }

    /**
     * 没有主机或主机离线的情况下添加主机配件提示
     */
    private void showAddRFPluginOnPanelStateError() {
        AlertDialog.createBuilder(getContext())
                .setOk("OK")
                .setContent(getString(R.string.no_panel_add_plugin_hint))
                .preBuilder()
                .show();
    }

    private void onScanResultIpc(int code, Plugin plugin) {
        DDLog.i(TAG, "onScanResultIpc");
        JSONObject object = null;
        try {
            object = new JSONObject(plugin.getSourceData());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (!AppConfig.Plugins.SUPPORT_HEARTLAI || !Constants.PROVIDER_HEARTLAI.equals(DDJSONUtil.getString(object, "provider"))) {
            DDLog.e(TAG, "No support ipc provider for this app.");
//            showToast(getResources().getString(R.string.illegal_ID));
            EventBus.getDefault().post(new QRRequestResultEvent(false, getString(R.string.illegal_ID)));
            return;
        }

        // 目前只处理心赖摄像头的二维码
        EventBus.getDefault().post(new QRRequestResultEvent(true));
        if (DDJSONUtil.getBoolean(object, "wave")) {
            getDelegateActivity().addCommonFragment(ModifyPlugsNetWorkFragment.newInstance(plugin.getPluginID(), true, plugin.getSourceData()));
        } else if (DDJSONUtil.getBoolean(object, "ap")) {
            int ipcType = DDJSONUtil.getInt(object, "ipc_type");
            getDelegateActivity().addCommonFragment(ApStepHeartLaiIpcFragment.newInstance(plugin.getSourceData(), true, ipcType));
        } else {
            Builder builder = new Builder();
            builder.setId(plugin.getPluginID())
                    .setAdd(true)
                    .setOffical(true)
                    .setShowDelete(false)
                    .setShowwave(false)
                    .setData(object);
            getDelegateActivity().addCommonFragment(ModifyASKPlugsFragment.newInstance(builder, plugin));
        }
    }

    public void toChangeNetwork() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            if (PermissionUtil.isLocationPermissionsDeny(getContext())) {
                PermissionDialogUtil.showNeedBleLocationPermissionDialog(getMainActivity());
                return;
            }
            if (!DDSystemUtil.isOpenGPS(getContext())) {
                toOpenGPS(0);
                return;
            }
        } else {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), null);
                return;
            }
        }
        getMainActivity().addCommonFragment(BleConfigNetFragment.newInstance());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIPCListUpdateEvent(IPCListUpdateEvent event) {
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIPCListUpdateEvent(BmtListUpdateEvent event) {
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PanelUpgradeStateChangeEvent event) {
        Log.d(TAG, "PanelUpgradeStateChangeEvent");
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WidgetFlagDeletedEvent event) {
        Log.d(TAG, "WidgetFlagDeletedEvent");
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(DeviceDeletedStateChangedEvent event) {
        refresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(DeviceOfflineEvent event) {
        Log.d(TAG, "DeviceOfflineEvent");
        refresh();
    }
}
