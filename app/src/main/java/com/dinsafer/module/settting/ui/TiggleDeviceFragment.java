package com.dinsafer.module.settting.ui;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.PopupWindow;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.aop.annotations.Safer;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dincore.activtor.api.base.IPluginScanCallback;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.TiggleDeviceLayoutBinding;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.CloseAllDeviceEvent;
import com.dinsafer.model.NewAskPlugInfo;
import com.dinsafer.model.WifiChangeEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.adapter.TiggleDeviceAdapter;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.add.plugin.ITriggerDeviceCallback;
import com.dinsafer.panel.add.plugin.TriggerDeviceBinder;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.NavigatorUtil;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Response;


/**
 * Created by Rinfon on 16/7/8.
 */
public class TiggleDeviceFragment extends MyBaseFragment<TiggleDeviceLayoutBinding> implements ITriggerDeviceCallback {
    private static final String KEY_TRIGGER_PLUGIN_TYPE = "trigger_plugin_type";

    private static String REMOTECONTROL = "Remote Controller";

    private static String PANIICBUTTON = "Emergency Button";

    private PopupWindow popupWindow;

    private PopupWindow officalPopupWindow;

    private int currentIndex = 0;

    private boolean isOfficalPlugin = true;

    private boolean isNewAskPlug = false;

    private static final String ALREADLY_ADD = "-50";

    private static final String WS_CLOSE = "-1";


    private String typeId = "";

    private View popview;

    private boolean isOtherSiren = false;

    private String currentTriggerType;

    //继电器
    private static final String RELAY = "12";
    private static final String RELAY_DTYPE = "10";
    private static final String RELAY_URL = "json/animation_add_relay.json";

    //Smart Button
    private static final String SMART_BUTTON = "3B";
    private static final String SMART_BUTTON_DTYPE = "10";
    private static final String SMART_BUTTON_URL = "json/animation_trigger_smart_button.json";

    private NewAskPlugInfo.ResultBean newAskPlugInfoResult;
    private List<Device> mOldDeviceList;
    private boolean isTimeOutToFindDevice = false;

    private TriggerDeviceBinder mTriggerDeviceBinder;
    private Plugin mTriggerPlugin;

    public static TiggleDeviceFragment newInstance() {
        return TiggleDeviceFragment.newInstance("");
    }

    public static TiggleDeviceFragment newInstance(String triggerPluginType) {
        TiggleDeviceFragment fragment = new TiggleDeviceFragment();
        Bundle args = new Bundle();
        args.putString(KEY_TRIGGER_PLUGIN_TYPE, triggerPluginType);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.tiggle_device_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        currentTriggerType = getArguments().getString(KEY_TRIGGER_PLUGIN_TYPE);
        LayoutInflater layoutInflater = (LayoutInflater) getDelegateActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        popview = layoutInflater.inflate(R.layout.tiggle_device_list, null);
        tiggleListView = (ListView) popview.findViewById(R.id.tiggle_device_list);
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> close());
        mBinding.tiggleDeviceCancel.setOnClickListener(v -> close());
        mBinding.tiggleDeviceOfficalSelect.setOnClickListener(v -> toSelectOffical());
        mBinding.tiggleDeviceSelect.setOnClickListener(v -> toSelectType());
        mBinding.tiggleDeviceBackground.setOnClickListener(v -> closeWindow());
        mBinding.tiggleDeviceScan.setOnClickListener(v -> toScan());
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        String panelIp = DeviceHelper.getString(DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID()),
                PanelDataKey.Panel.LAN_IP, "");
        if (!TextUtils.isEmpty(panelIp)) {
            DinSDK.getPluginActivtor().setup(DinSaferApplication.getAppContext());
            mTriggerDeviceBinder = (TriggerDeviceBinder) DinSDK.getPluginActivtor().createTriggerDeviceBinder(panelIp);
        }
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.tiggle_deivce_title));
        mBinding.tiggleDeviceScan.setLocalText(getResources().getString(R.string.tiggle_device_next));
        mBinding.tiggleDeviceCancel.setLocalText(getResources().getString(R.string.Cancel));
        mBinding.tiggleDeviceHint.setLocalText(getResources().getString(R.string.tiggle_device_type_brand));
        mBinding.tiggleDeviceOfficalHint.setLocalText(getResources().getString(R.string.tiggle_device_hint_brand));
        mBinding.tiggleDeviceOfficalHint2.setLocalText(getResources().getString(R.string.tiggle_device_hint_2));
        mOfficalList.add(getResources().getString(R.string.offical_plugin));
        mOfficalList.add(getResources().getString(R.string.not_offical_plugin));
        initOfficalPluginData();
        mOldDeviceList = new ArrayList<>();
    }


    public void initOtherPluginData() {
        mData.clear();
        mDataApng.clear();
        mDeviceList.clear();
        mBigType.clear();
        mDescription.clear();
        mDescription_2.clear();

        mData.add(PluginConstants.TYPE_0B);
        mDataApng.add("json/animation_trigger_doorsensor.json");
        mDeviceList.add(PluginConstants.NAME_DOOR_WINDOW_SENSOR);//0
        mBigType.add(PluginConstants.BIG_TYPE_0);
        mDescription.add(getResources().getString(R.string.door_tiggle_hint_1));//  1
        mDescription_2.add(getResources().getString(R.string.door_tiggle_hint_2));
        mSizeList.add(new int[]{404, 206});

        if (AppConfig.Plugins.SUPPORT_PIR_SENSOR
                || AppConfig.Plugins.SUPPORT_WIRELESS_PET_IMMUNE_MOTION_SENSOR) {
            mData.add(PluginConstants.TYPE_09);
            mDataApng.add("json/animation_trigger_pir_sensor_chuango.json");
            mDeviceList.add(PluginConstants.NAME_PIR_SENSOR);//0
            mBigType.add(PluginConstants.BIG_TYPE_0);
            mDescription.add(getResources().getString(R.string.chuangao_pir_tiggle_hint_1));
            mDescription_2.add(getResources().getString(R.string.chuangao_pir_tiggle_hint_2));
            mSizeList.add(new int[]{332, 267});
        }

        mData.add(PluginConstants.TYPE_05);
        mDataApng.add("json/animation_trigger_smoke_sensor.json");
        mDeviceList.add(PluginConstants.NAME_SMOKE_SENSOR);//0
        mBigType.add(PluginConstants.BIG_TYPE_0);
        mDescription.add(getResources().getString(R.string.smoke_tiggle_hint_1));
        mDescription_2.add(getResources().getString(R.string.smoke_tiggle_hint_2));
        mSizeList.add(new int[]{453, 238});

        mData.add(PluginConstants.TYPE_06);
        mDataApng.add("json/animation_trigger_shake_doorsensor.json");
        mDeviceList.add(PluginConstants.NAME_VIBRATION_SENSOR);//0
        mBigType.add(PluginConstants.BIG_TYPE_0);
        mDescription.add(getResources().getString(R.string.vibration_tiggle_hint_1));
        mDescription_2.add(getResources().getString(R.string.vibration_tiggle_hint_2));
        mSizeList.add(new int[]{261, 288});

//        mData.add(PluginConstants.TYPE_07);
//        mDataApng.add("json/animation_trigger_urgentbtn.json");
//        mDeviceList.add(PANIICBUTTON);//0
//        mBigType.add(PluginConstants.BIG_TYPE_0);
//        mDescription.add(getResources().getString(R.string.panic_tiggle_hint_1));
//        mDescription_2.add(getResources().getString(R.string.panic_tiggle_hint_2));
//        mSizeList.add(new int[]{416, 272});

        mData.add(PluginConstants.TYPE_01);
        mDataApng.add("json/animation_trigger_remote_control.json");
        mDeviceList.add(PluginConstants.NAME_REMOTE_CONTROLLER);//1
        mBigType.add(PluginConstants.BIG_TYPE_1);
        mDescription.add(getResources().getString(R.string.remote_tiggle_hint_1));
        mDescription_2.add(getResources().getString(R.string.remote_tiggle_hint_2));
        mSizeList.add(new int[]{339, 232});

        mData.add(PluginConstants.TYPE_0E);
        mDataApng.add("json/animation_trigger_watersensor.json");
        mDeviceList.add(PluginConstants.NAME_LIQUID_SENSOR);//0
        mBigType.add(PluginConstants.BIG_TYPE_0);
        mDescription.add(getResources().getString(R.string.liquid_tiggle_hint_1));
        mDescription_2.add(getResources().getString(R.string.liquid_tiggle_hint_2));
        mSizeList.add(new int[]{280, 258});

        mData.add(PluginConstants.TYPE_1B);
        mDataApng.add("json/animation_trigger_keypad.json");
        mDeviceList.add(PluginConstants.NAME_WIRELESS_KEYPAD);//1
        mBigType.add(PluginConstants.BIG_TYPE_1);
        mDescription.add(getResources().getString(R.string.wireless_tiggle_hint_1));
        mDescription_2.add(getResources().getString(R.string.wireless_tiggle_hint_2));
        mSizeList.add(new int[]{382, 216});

        if (AppConfig.Plugins.SUPPORT_WIRELESS_OUTDOOR_SIREN
                || AppConfig.Plugins.SUPPORT_WIRELESS_INDOOR_SIREN) {
            mData.add(PluginConstants.TYPE_14);
            mDataApng.add("json/animation_trigger_outdoor_siren_chuango.json");
            mDeviceList.add(PluginConstants.NAME_WIRELESS_SIREN);//4
            mBigType.add(PluginConstants.BIG_TYPE_4);
            mDescription.add(getResources().getString(R.string.wireless_siren_tiggle_hint_other));
            mDescription_2.add(getResources().getString(R.string.wireless_siren_tiggle_hint_other));
            mSizeList.add(new int[]{413, 238});
        }

        if (!TextUtils.isEmpty(currentTriggerType)) {
            String item;
            for (int i = 0; i < mDeviceList.size(); i++) {
                item = mDeviceList.get(i);
                if (currentTriggerType.equals(item)) {
                    mBinding.tiggleDeviceSelect.setText(Local.s(currentTriggerType));
                    currentIndex = i;
                    break;
                }
            }
        }
    }

    public void initOfficalPluginData() {

        mData.clear();
        mDataApng.clear();
        mDeviceList.clear();
        mBigType.clear();
        mDescription.clear();
        mDescription_2.clear();

        mData.add(PluginConstants.TYPE_0B); // 门磁 1
        mDataApng.add("json/animation_trigger_doorsensor.json"); // 1
        mDeviceList.add(PluginConstants.NAME_DOOR_WINDOW_SENSOR);//0 1
        mBigType.add(PluginConstants.BIG_TYPE_0);//  1
        mDescription.add(getResources().getString(R.string.door_tiggle_hint_1));//  1
        mDescription_2.add(getResources().getString(R.string.door_tiggle_hint_2));//  1
        mSizeList.add(new int[]{404, 206});

        mData.add(PluginConstants.TYPE_3D); // 门磁 1
        mDataApng.add("json/animation_trigger_rolling_doorsensor.json"); // 1
        mDeviceList.add(PluginConstants.NAME_ROLLING_DOOR_WINDOW_SENSOR);//0 1
        mBigType.add(PluginConstants.BIG_TYPE_10);//  1
        mDescription.add(getResources().getString(R.string.rolling_door_tiggle_hint_1));//  1
        mDescription_2.add(getResources().getString(R.string.rolling_door_tiggle_hint_2));//  1
        mSizeList.add(new int[]{404, 206});

        if (AppConfig.Plugins.SUPPORT_PIR_SENSOR
                || AppConfig.Plugins.SUPPORT_WIRELESS_PET_IMMUNE_MOTION_SENSOR) {
            mData.add(PluginConstants.TYPE_09);// 红外感应器 2
            mDataApng.add("json/animation_trigger_pir.json"); //2
            mDeviceList.add(PluginConstants.NAME_PIR_SENSOR);//0  2
            mBigType.add(PluginConstants.BIG_TYPE_0);//  2
            mDescription.add(getResources().getString(R.string.pir_tiggle_hint_1));//  2
            mDescription_2.add(getResources().getString(R.string.pir_tiggle_hint_2));//  2
            mSizeList.add(new int[]{332, 267});
        }

        mData.add(PluginConstants.TYPE_05);// 烟雾感应器 3
        mDataApng.add("json/animation_trigger_smoke_sensor.json"); //3
        mDeviceList.add(PluginConstants.NAME_SMOKE_SENSOR);//0  3
        mBigType.add(PluginConstants.BIG_TYPE_0);//  3
        mDescription.add(getResources().getString(R.string.smoke_tiggle_hint_1));//  3
        mDescription_2.add(getResources().getString(R.string.smoke_tiggle_hint_2));//  3
        mSizeList.add(new int[]{453, 238});

        mData.add(PluginConstants.TYPE_06);// 震动开关 4
        mDataApng.add("json/animation_trigger_shake_doorsensor.json"); //4
        mDeviceList.add(PluginConstants.NAME_VIBRATION_SENSOR);//0  4
        mBigType.add(PluginConstants.BIG_TYPE_0);//  4
        mDescription.add(getResources().getString(R.string.vibration_tiggle_hint_1));//  4
        mDescription_2.add(getResources().getString(R.string.vibration_tiggle_hint_2));//  4
        mSizeList.add(new int[]{261, 288});

//        mData.add(PluginConstants.TYPE_07);// 紧急按钮 5
//        mDataApng.add("json/animation_trigger_urgentbtn.json"); // 5
//        mDeviceList.add(PANIICBUTTON);//0  5
//        mBigType.add(PluginConstants.BIG_TYPE_0);//  5
//        mDescription.add(getResources().getString(R.string.panic_tiggle_hint_1));//  5
//        mDescription_2.add(getResources().getString(R.string.panic_tiggle_hint_2));//  5
//        mSizeList.add(new int[]{416, 272});

        mData.add(PluginConstants.TYPE_01); // 遥控器 6
        mDataApng.add("json/animation_trigger_remote_control.json"); //6
        mDeviceList.add(PluginConstants.NAME_REMOTE_CONTROLLER);//1   6
        mBigType.add(PluginConstants.BIG_TYPE_1);//  6
        mDescription.add(getResources().getString(R.string.remote_tiggle_hint_1));//  6
        mDescription_2.add(getResources().getString(R.string.remote_tiggle_hint_2));//  6
        mSizeList.add(new int[]{339, 232});

        mData.add(PluginConstants.TYPE_0E);// 水感 7
        mDataApng.add("json/animation_trigger_watersensor.json"); // 7
        mDeviceList.add(PluginConstants.NAME_LIQUID_SENSOR);//0 7
        mBigType.add(PluginConstants.BIG_TYPE_0);//  7
        mDescription.add(getResources().getString(R.string.liquid_tiggle_hint_1));//  7
        mDescription_2.add(getResources().getString(R.string.liquid_tiggle_hint_2));//  7
        mSizeList.add(new int[]{280, 258});

        mData.add(PluginConstants.TYPE_1B);// 无线键盘  8
        mDataApng.add("json/animation_trigger_keypad.json"); // 8
        mDeviceList.add(PluginConstants.NAME_WIRELESS_KEYPAD);//1  8
        mBigType.add(PluginConstants.BIG_TYPE_7);//  8
        mDescription.add(getResources().getString(R.string.wireless_tiggle_hint_1));//  8
        mDescription_2.add(getResources().getString(R.string.wireless_tiggle_hint_2));//  8
        mSizeList.add(new int[]{382, 216});

        if (AppConfig.Plugins.SUPPORT_WIRELESS_OUTDOOR_SIREN
                || AppConfig.Plugins.SUPPORT_WIRELESS_INDOOR_SIREN) {
            mData.add(PluginConstants.TYPE_14);// 无线喇叭   9
            mDataApng.add("json/animation_trigger_siren.json"); // 9
            mDeviceList.add(PluginConstants.NAME_WIRELESS_SIREN);//4    9
            mBigType.add(PluginConstants.BIG_TYPE_4);//   9
            mDescription.add(getResources().getString(R.string.wireless_siren_tiggle_hint_1));//  9
            mDescription_2.add(getResources().getString(R.string.wireless_siren_tiggle_hint_2));//  9
            mSizeList.add(new int[]{479, 266});
        }

        if (AppConfig.Plugins.SUPPORT_SIGNAl_REPEATER_PLUG) {
            mData.add(PluginConstants.TYPE_4E);
            mDataApng.add("json/animation_trigger_switch.json"); // 10
            mDeviceList.add(PluginConstants.NAME_SIGNAL_REPEATER_PLUG);//3  10
            mBigType.add(PluginConstants.BIG_TYPE_3);//  10
            mDescription.add(getResources().getString(R.string.smart_plug_tiggle_hint_1));//  10
            mDescription_2.add(getResources().getString(R.string.smart_plug_tiggle_hint_2));//  10
            mSizeList.add(new int[]{389, 247});
        }

        if (AppConfig.Plugins.SUPPORT_SMART_PLUG) {
            mData.add(PluginConstants.TYPE_15);  // 10
            mDataApng.add("json/animation_trigger_switch.json"); // 10
            mDeviceList.add(PluginConstants.NAME_SMART_PLUG);//3  10
            mBigType.add(PluginConstants.BIG_TYPE_3);//  10
            mDescription.add(getResources().getString(R.string.smart_plug_tiggle_hint_1));//  10
            mDescription_2.add(getResources().getString(R.string.smart_plug_tiggle_hint_2));//  10
            mSizeList.add(new int[]{389, 247});
        }

        mData.add(PluginConstants.TYPE_37);
        mDataApng.add("json/animation_trigger_rfid.json"); //
        mDeviceList.add(PluginConstants.NAME_RFID_TAG);//1
        mBigType.add(PluginConstants.BIG_TYPE_1);//
        mDescription.add(getResources().getString(R.string.rfid_tiggle_hint_1));//
        mDescription_2.add(getResources().getString(R.string.rfid_tiggle_hint_2));//
        mSizeList.add(new int[]{344, 206});

//        if (APIKey.IS_SHOW_RELAY) {
//            mData.add(RELAY);
//            mDataApng.add(RELAY_URL);
//            mDeviceList.add(getResources().getString(R.string.relay_name));
//            mBigType.add(RELAY_DTYPE);
//            mDescription.add(getResources().getString(R.string.tiggle_relay_hint_1));
//            mDescription_2.add(getResources().getString(R.string.tiggle_relay_hint_2));
//            mSizeList.add(new int[]{296, 216});
//        }

        if (APIKey.IS_SHOW_SMART_BUTTON) {
            mData.add(SMART_BUTTON);
//            TODO 修改smart button apng动画
            mDataApng.add(SMART_BUTTON_URL);
            mDeviceList.add(getResources().getString(R.string.smart_button_name));
            mBigType.add(SMART_BUTTON_DTYPE);
            mDescription.add(getResources().getString(R.string.smart_button_hint_1));
            mDescription_2.add(getResources().getString(R.string.smart_button_hint_2));
            mSizeList.add(new int[]{296, 216});
        }

        if (AppConfig.Plugins.SUPPORT_WIRED_BRIDGE) {
            mData.add(PluginConstants.TYPE_3F);
            mDataApng.add("json/animation_trigger_wired_bridge.json");
            mDeviceList.add(getResources().getString(R.string.wired_bridge));
            mBigType.add(PluginConstants.BIG_TYPE_10);
            mDescription.add(getResources().getString(R.string.wired_bridge_hint_1));
            mDescription_2.add(getResources().getString(R.string.wired_bridge_hint_2));
            mSizeList.add(new int[]{404, 206});
        }

        mBinding.tiggleDeviceSelect.setText(Local.s(mDeviceList.get(currentIndex)));

        if (!APIKey.IS_OPEN_OTHER_PLUGIN) {
            mBinding.tiggleDeviceOfficalSelect.setVisibility(View.GONE);
            mBinding.tiggleDeviceOfficalHint.setVisibility(View.GONE);
        }

        if (isOfficalPlugin) {
            mBinding.tiggleDeviceOfficalSelect.setText(Local.s(mOfficalList.get(0)));
        } else {
            mBinding.tiggleDeviceOfficalSelect.setText(Local.s(mOfficalList.get(1)));
        }

        if (!TextUtils.isEmpty(currentTriggerType)) {
            String item;
            for (int i = 0; i < mDeviceList.size(); i++) {
                item = mDeviceList.get(i);
                if (currentTriggerType.equals(item)) {
                    mBinding.tiggleDeviceSelect.setText(Local.s(currentTriggerType));
                    currentIndex = i;
                    break;
                }
            }
        }
    }

    private ListView tiggleListView;

    private TiggleDeviceAdapter tiggleAdapter;

    private TiggleDeviceAdapter officalAdapter;

    private ArrayList<String> mDeviceList = new ArrayList<String>();

    private ArrayList<String> mOfficalList = new ArrayList<String>();

    private ArrayList<String> mData = new ArrayList<String>();

    private ArrayList<String> mBigType = new ArrayList<String>();

    private ArrayList<String> mDataApng = new ArrayList<String>();

    private ArrayList<String> mDescription = new ArrayList<String>();

    private ArrayList<String> mDescription_2 = new ArrayList<String>();

    private ArrayList<int[]> mSizeList = new ArrayList<int[]>();

    private int mSuccessCount = 0;

    private String plugId, secondPlugId;

    private TiggleDialog dialog;

    private boolean isConnectedWifi = true;

    public void toSelectOffical() {
        if (officalPopupWindow == null) {

            officalAdapter = new TiggleDeviceAdapter(getDelegateActivity(), mOfficalList);

            officalPopupWindow = new PopupWindow(popview, mBinding.tiggleDeviceSelect.getWidth(),
                    ViewGroup.LayoutParams.WRAP_CONTENT);

            officalPopupWindow.setClippingEnabled(false);

        }

        tiggleListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                officalPopupWindow.dismiss();
                mBinding.tiggleDeviceOfficalSelect.setText(Local.s(mOfficalList.get(i)));
                if (i == 0) {
                    isOfficalPlugin = true;
                    initOfficalPluginData();
                } else {
                    isOfficalPlugin = false;
                    initOtherPluginData();
                }

                if (tiggleAdapter != null) {
                    tiggleAdapter.notifyDataSetChanged();
                }

                mBinding.tiggleDeviceSelect.setText(Local.s(mDeviceList.get(0)));
                currentIndex = 0;
            }
        });

        tiggleListView.setAdapter(officalAdapter);

        if (officalAdapter.getCount() > 4) {
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(mBinding.tiggleDeviceSelect.getWidth(), (4 * getResources().getDimensionPixelOffset(R.dimen.group_list_item_height)));
            tiggleListView.setLayoutParams(params);
        } else {
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(mBinding.tiggleDeviceSelect.getWidth(), (officalAdapter.getCount() * getResources().getDimensionPixelOffset(R.dimen.group_list_item_height)));
            tiggleListView.setLayoutParams(params);
        }

        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.dismiss();
        }

        if (officalPopupWindow.isShowing()) {
            officalPopupWindow.dismiss();
        } else {
            officalPopupWindow.setFocusable(false);
            officalPopupWindow.setOutsideTouchable(false);

            officalPopupWindow.showAsDropDown(mBinding.tiggleDeviceOfficalSelect, 0, 10);
        }
    }

    public void toSelectType() {


        if (popupWindow == null) {

            tiggleAdapter = new TiggleDeviceAdapter(getDelegateActivity(), mDeviceList);

            popupWindow = new PopupWindow(popview, mBinding.tiggleDeviceSelect.getWidth(),
                    ViewGroup.LayoutParams.WRAP_CONTENT);

            popupWindow.setClippingEnabled(false);

        }

        tiggleListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                popupWindow.dismiss();
                mBinding.tiggleDeviceSelect.setText(Local.s(mDeviceList.get(i)));
                currentIndex = i;
            }
        });

        tiggleListView.setAdapter(tiggleAdapter);

        if (tiggleAdapter.getCount() > 4) {
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(mBinding.tiggleDeviceSelect.getWidth(), (4 * getResources().getDimensionPixelOffset(R.dimen.group_list_item_height)));
            tiggleListView.setLayoutParams(params);
        } else {
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(mBinding.tiggleDeviceSelect.getWidth(), (tiggleAdapter.getCount() * getResources().getDimensionPixelOffset(R.dimen.group_list_item_height)));
            tiggleListView.setLayoutParams(params);
        }

        if (officalPopupWindow != null && officalPopupWindow.isShowing()) {
            officalPopupWindow.dismiss();
        }

        if (popupWindow.isShowing()) {
            popupWindow.dismiss();
        } else {
            popupWindow.setFocusable(false);
            popupWindow.setOutsideTouchable(false);

            popupWindow.showAsDropDown(mBinding.tiggleDeviceSelect, 0, 10);
        }
    }

    public void closeWindow() {
        if (popupWindow != null && popupWindow.isShowing()) {
            popupWindow.dismiss();
        }

        if (officalPopupWindow != null && officalPopupWindow.isShowing()) {
            officalPopupWindow.dismiss();
        }
    }

    private boolean isScaning = true;

    public void toScan() {

        closeWindow();

        if (currentIndex == -1) {
            return;
        }

        if (isPanelPlugin()
                && (!CommonDataUtil.getInstance().isHadPanelNotDeleted()
                || !CommonDataUtil.getInstance().isPanelOnline())) {
            DDLog.e(TAG, "不能在没有主机或主机离线的情况下通过触发配对的方式添加RF配件");
            showAddRFPluginOnPanelStateError();
            return;
        }

        if (!isOfficalPlugin && mDeviceList.get(currentIndex).equals("Wireless Siren")) {
            showLoadingFragment(LoadingFragment.BLUE);
            toStartScan();
        } else {
            showLoadingFragment(LoadingFragment.BLUE);
            toStartScan();
        }
    }

    /**
     * 是否主机配件
     *
     * @return true: 主机配件
     */
    private boolean isPanelPlugin() {
        return !mDeviceList.get(currentIndex).equals("WIFI Plug")
                && !mDeviceList.get(currentIndex).equals("WIFI Bulb");
    }

    private void toStartScan() {
        Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID());
        if (null == device) {
            DDLog.e(TAG, "mPanel is null.");
            closeLoadingFragment();
            AlertDialog.createBuilder(getDelegateActivity())
                    .setAutoDissmiss(true)
                    .setCanCancel(false)
                    .setContentColor(getResources().getColor(R.color.common_dialog_content))
                    .setBackgroundTint(Color.WHITE)
                    .setOk(getResources().getString(R.string.ok))
                    .setContent(getString(R.string.no_panel_add_plugin_hint))
                    .preBuilder()
                    .show();
            return;
        }

        if (null == mTriggerDeviceBinder) {
            DDLog.e(TAG, "mTriggerDeviceBinder is null.");
            closeLoadingFragment();
            showErrorToast();
            return;
        }

        mTriggerDeviceBinder.toStartScan(isOfficalPlugin, mData.get(currentIndex), this);
    }

    public void toScanOtherSirenPlugin() {
        plugId = "";
        secondPlugId = "";
        String nextString = getResources().getString(R.string.Next);
        String title = getResources().getString(R.string.learn_step_1);
        mSuccessCount = 1;
        isOtherSiren = true;
        isScaning = false;
        dialog = TiggleDialog.createBuilder(getDelegateActivity())
                .setAutoDismiss(false)
                .setCancel(getResources().getString(R.string.Cancel))
                .setOk(nextString)
                .setTitle(title)
                .setTiggleHint(mDescription.get(currentIndex))
                .setIconUrl(mDataApng.get(currentIndex))
                .setOKListener(new TiggleDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(TiggleDialog dialog) {
                        if (!isScaning) {
                            if (mSuccessCount == 1) {
                                dialog.setTitleString(getResources().getString(R.string.learn_step_2));
                                dialog.setContent(mDescription_2.get(currentIndex));
                            }
                            if (mSuccessCount >= 2) {
                                dialog.dismiss();
                                mSuccessCount = 0;
                                if (null != mTriggerDeviceBinder) {
                                    mTriggerDeviceBinder.destroyBinder();
                                }
                                closeWindow();
                                removeSelf();
                                NavigatorUtil.getInstance().toAddNotOfficalPlugsFragment("", typeId, mData.get(currentIndex), mTriggerPlugin);
                            } else {
                                String panelIp = DeviceHelper.getString(DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID()),
                                        PanelDataKey.Panel.LAN_IP, "");
                                if (!TextUtils.isEmpty(panelIp)) {
                                    toStartScan();
                                    dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[0] / 2)
                                            , DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[1] / 2)
                                            , 0
                                            , 0);

                                    dialog.setOkText(getResources().getString(R.string.tiggle_loading));
                                    dialog.setOkTextAlpha(0.5f);
                                    dialog.setApng(mDataApng.get(currentIndex), ValueAnimator.INFINITE);
                                } else {
                                    showErrorToast();
                                }
                            }
                            isScaning = true;
                        }
                    }

                    @Override
                    public void onCancel(TiggleDialog dialog) {
                        mSuccessCount = 0;
                        if (null != mTriggerDeviceBinder) {
                            mTriggerDeviceBinder.destroyBinder();
                        }
                    }
                })
                .setOkTextAlpha(1f)
                .setApngSize(DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[0] / 2)
                        , DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[1] / 2)
                        , 0
                        , 0)
                .preBuilder();
        dialog.show();
    }

    public void toScanPlugin() {
        plugId = "";
        secondPlugId = "";
        isOtherSiren = false;
        isScaning = true;
        String nextString = getResources().getString(R.string.tiggle_loading);
        String title = getResources().getString(R.string.learn_step_1);
//        toStartScan();
        dialog = TiggleDialog.createBuilder(getDelegateActivity())
                .setAutoDismiss(false)
                .setCancel(getResources().getString(R.string.Cancel))
                .setOk(nextString)
                .setTitle(title)
                .setTiggleHint(mDescription.get(currentIndex))
                .setIconUrl(mDataApng.get(currentIndex))
                .setOKListener(new TiggleDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(TiggleDialog dialog) {
                        if (!isScaning) {
                            if (mSuccessCount == 0) {
                                dialog.setTitleString(getResources().getString(R.string.learn_step_1));
                                dialog.setContent(mDescription.get(currentIndex));
                            } else if (mSuccessCount == 1) {
                                dialog.setTitleString(getResources().getString(R.string.learn_step_2));
                                dialog.setContent(mDescription_2.get(currentIndex));
                            }
                            if (mSuccessCount >= 2) {
                                dialog.dismiss();
                                mSuccessCount = 0;
                                if (null != mTriggerDeviceBinder) {
                                    mTriggerDeviceBinder.destroyBinder();
                                }
                                closeWindow();
                                removeSelf();
                                if (isOfficalPlugin) {
                                    if (isNewAskPlug) {
                                        String result = new Gson().toJson(newAskPlugInfoResult);
                                        DDLog.d(TAG, result);
                                        Builder builder = new Builder();
                                        builder.setId(typeId)
                                                .setAdd(true)
                                                .setOffical(true)
                                                .setShowDelete(false)
                                                .setShowwave(false)
                                                //TODO
//                                                .setShowAp(false)
                                                .setData(new Gson().toJson(newAskPlugInfoResult));

                                        getDelegateActivity().addCommonFragment(ModifyASKPlugsFragment.newInstance(builder, mTriggerPlugin));
                                        isNewAskPlug = false;
                                    } else {
                                        NavigatorUtil.getInstance().toModifyPlugsNameFragment("", typeId, true, isOfficalPlugin, mTriggerPlugin);
                                    }
                                } else {
                                    NavigatorUtil.getInstance().toAddNotOfficalPlugsFragment("", typeId, mData.get(currentIndex), mTriggerPlugin);
                                }
                            } else {
                                String panelIp = DeviceHelper.getString(DinHome.getInstance().getDevice(
                                                CommonDataUtil.getInstance().getCurrentPanelID()),
                                        PanelDataKey.Panel.LAN_IP, "");
                                if (!TextUtils.isEmpty(panelIp)) {
                                    dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[0] / 2)
                                            , DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[1] / 2)
                                            , 0
                                            , 0);
                                    toStartScan();
                                    dialog.setOkText(getResources().getString(R.string.tiggle_loading));
                                    dialog.setOkTextAlpha(0.5f);
                                    dialog.setApng(mDataApng.get(currentIndex), ValueAnimator.INFINITE);

                                } else {
                                    showErrorToast();
                                }
                            }
                            isScaning = true;
                        }
                    }

                    @Override
                    public void onCancel(TiggleDialog dialog) {
                        mSuccessCount = 0;
                        if (null != mTriggerDeviceBinder) {
                            mTriggerDeviceBinder.destroyBinder();
                        }
                    }
                })
                .setOkTextAlpha(0.5f)
                .setApngSize(DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[0] / 2)
                        , DisplayUtil.dip2px(getDelegateActivity(), mSizeList.get(currentIndex)[1] / 2), 0, 0)
                .preBuilder();
        dialog.show();
    }


    public void close() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Yes))
                .setContent(getResources().getString(R.string.learn_step_cancel))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (null != mTriggerDeviceBinder) {
                            mTriggerDeviceBinder.destroyBinder();
                        }
                        closeWindow();
                        removeSelf();
                    }
                })
                .setCancel(getResources().getString(R.string.No))
                .preBuilder()
                .show();

    }

    @Override
    public boolean onBackPressed() {
        getDelegateActivity().showToast(getResources().getString(R.string.learn_step_cancel),
                getResources().getString(R.string.Yes), getResources().getString(R.string.No), () -> {
                    if (null != mTriggerDeviceBinder) {
                        mTriggerDeviceBinder.destroyBinder();
                    }
                    closeWindow();
                    removeSelf();
                }, () -> {
                    closeWindow();
                });
        return true;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mTriggerDeviceBinder != null) {
            mTriggerDeviceBinder.destroyBinder();
        }
        EventBus.getDefault().unregister(this);
    }

    private String UNKNOW_SSID = "<unknown ssid>";

    private void toDoWithOutOfficalPlugin(String messageStr) {
        DDLog.d(TAG, "toDoWithOutOfficalPlugin");
        if (secondPlugId.equals(plugId) && !TextUtils.isEmpty(secondPlugId)) {
            dialog.setTitleString(getResources().getString(R.string.learn_step_2));
            dialog.setOkText(getResources().getString(R.string.Next));
            dialog.setOkTextAlpha(1f);
            typeId = messageStr;
            dialog.setContent(getResources().getString(R.string.tiggle_success));
            dialog.setApng("json/animation_succeed.json", 0);
            dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), 60)
                    , DisplayUtil.dip2px(getDelegateActivity(), 60)
                    , DisplayUtil.dip2px(getDelegateActivity(), 80)
                    , DisplayUtil.dip2px(getDelegateActivity(), 70));
            dialog.setTypeName(Local.s(mDeviceList.get(currentIndex)));
            dialog.setTypeID("");
        } else {
            dialog.setTitleString(getResources().getString(R.string.learn_step_2));
            dialog.setOkText("");
            dialog.setOkTextAlpha(1f);
            dialog.setContent(getResources().getString(R.string.learn_step_fail));
            dialog.setApng("json/animation_failed.json", 0);
            dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), 60)
                    , DisplayUtil.dip2px(getDelegateActivity(), 60)
                    , DisplayUtil.dip2px(getDelegateActivity(), 80)
                    , DisplayUtil.dip2px(getDelegateActivity(), 70));
        }

    }

    public void toDoWithOfficalPlugin(String messageStr) {
        DDLog.d(TAG, "toDoWithOfficalPlugin");
        if (secondPlugId.equals(plugId) && !TextUtils.isEmpty(secondPlugId)) {
//            dialog.setTitleString(getResources().getString(R.string.learn_step_2));
//            dialog.setOkText(getResources().getString(R.string.Next));
//            dialog.setOkTextAlpha(1f);

            //TODO NewAsk配件需要从后台获取配件信息
            if (secondPlugId.contains("newask")) {
                isNewAskPlug = true;
                String[] strings = secondPlugId.split("_");
                if (strings.length >= 2) {
                    getNewAskPlugInfo(strings[0], strings[1]);
                } else {
                    isNewAskPlug = false;
                    DDLog.e(TAG, "Error plug id:" + secondPlugId);
                    setDialogToErrorStatus();
                }
            } else {//TODO 非NewAsk配件
                isNewAskPlug = false;
                typeId = getBigTypeWithSpecialControl(messageStr) + getStypeWithSpecialRule(messageStr) + getTypeId(messageStr);
                if (typeId.length() < 11) {
                    typeId = typeId + "FFFF";
                }
                typeId = DDSecretUtil.hexStrToStr64(typeId);
                if (!TextUtils.isEmpty(typeId)) {
                    setDialogToSuccessStatus();
                    dialog.setOkText(getResources().getString(R.string.Next));
                    dialog.setOkTextAlpha(1f);
                } else {
                    setDialogToErrorStatus();
                }
            }

        } else {
            setDialogToErrorStatus();
        }
    }

    private void setDialogToErrorStatus() {
        dialog.setTitleString(getResources().getString(R.string.learn_step_2));
        dialog.setOkText("");
        dialog.setOkTextAlpha(1f);
        dialog.setContent(getResources().getString(R.string.learn_step_fail));
        dialog.setApng("json/animation_failed.json", 0);
        dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), 60)
                , DisplayUtil.dip2px(getDelegateActivity(), 60)
                , DisplayUtil.dip2px(getDelegateActivity(), 80)
                , DisplayUtil.dip2px(getDelegateActivity(), 70));
    }

    private void setDialogToSuccessStatus() {
        dialog.setContent(getResources().getString(R.string.tiggle_success));
        dialog.setApng("json/animation_succeed.json", 0);
        dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), 60)
                , DisplayUtil.dip2px(getDelegateActivity(), 60)
                , DisplayUtil.dip2px(getDelegateActivity(), 80)
                , DisplayUtil.dip2px(getDelegateActivity(), 70));
        String deviceType = "";
        if (isNewAskPlug && newAskPlugInfoResult != null) {
            deviceType = CommonDataUtil.getInstance().getASKNameByBSType(newAskPlugInfoResult.getStype());
        } else {
            deviceType = mDeviceList.get(currentIndex);
//            deviceType = CommonDataUtil.getInstance().getNameByStype(getStypeWithSpecialRule(secondPlugId));
        }
        dialog.setTypeName(Local.s(deviceType));
        dialog.setTypeID(typeId);
    }


    /**
     * NewAsk配件需要从后台获取ID
     */
    private void getNewAskPlugInfo(String stype, String sendId) {
        if (null == mTriggerDeviceBinder) {
            DDLog.e(TAG, "Empty trigger device binder.");
            setDialogToErrorStatus();
            return;
        }

        mTriggerDeviceBinder.getNewAskPlugInfo(stype, sendId, new IPluginScanCallback() {
            @Override
            public void onScanResult(int i, Plugin plugin) {
                if (1 != i
                        || null == plugin
                        || TextUtils.isEmpty(plugin.getPluginID())
                        || TextUtils.isEmpty(plugin.getSourceData())) {
                    DDLog.d(TAG, "onFailure");
                    setDialogToErrorStatus();
                    return;
                }

                DDLog.d(TAG, "onResponse");
                try {
                    mTriggerPlugin = plugin;
                    newAskPlugInfoResult = new Gson().fromJson(plugin.getSourceData(), NewAskPlugInfo.ResultBean.class);
                    typeId = plugin.getPluginID();
                    setDialogToSuccessStatus();
                    dialog.setOkText(getResources().getString(R.string.Next));
                    dialog.setOkTextAlpha(1f);
                } catch (Exception e) {
                    e.printStackTrace();
                    setDialogToErrorStatus();
                }
            }
        });
    }

    /**
     * 获取大类
     *
     * @param msg
     * @return
     */
    public String getBigTypeWithSpecialControl(String msg) {
        String id[] = msg.split("_");
        if (PANIICBUTTON.equals(mDeviceList.get(currentIndex)) && id[0].equals("30")) {
            return "8";
        } else {
            return mBigType.get(currentIndex);
        }
    }

    /**
     * 获取
     *
     * @param msg
     * @return
     */
    @Safer
    public String getTypeId(String msg) {
        String id[] = msg.split("_");
        return id[1];
    }

    /**
     * 获取小类
     *
     * @param msg
     * @return
     */
    @Safer
    public String getStypeWithSpecialRule(String msg) {
        String id[] = msg.split("_");
        if (REMOTECONTROL.equals(mDeviceList.get(currentIndex))) {
            return mData.get(currentIndex);
        } else {
            return id[0];
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(WifiChangeEvent ev) {
    }

    @Override
    public void onStart() {
        DDLog.d(TAG, "onStart");
        super.onStart();
    }

    @Override
    public void onResume() {
        DDLog.d(TAG, "onResume");
        super.onResume();
    }

    @Override
    public void onStop() {
        DDLog.d(TAG, "onStop");
        super.onStop();
    }

    @Override
    public void onPause() {
        DDLog.d(TAG, "onPause");
        super.onPause();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        //此方法不管用。因为出现dialog本身这个fragment就has no focus
        DDLog.d(TAG, "onWindowFocusChanged");
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(CloseAllDeviceEvent ev) {
        DDLog.d(TAG, "onEventMainThread(CloseAllDeviceEvent ev): maybe 有人报警");
        if (dialog != null) {
            dialog.dismiss();
        }
    }

    @Override
    public void onWsOpen(Response response) {
        closeLoadingFragment();
        getDelegateActivity().
                runOnUiThread(() -> {
                    if (dialog == null || !dialog.isShowing()) {
                        if (!isOfficalPlugin && mDeviceList.get(currentIndex).equals("Wireless Siren")) {
                            toScanOtherSirenPlugin();
                        } else {
                            toScanPlugin();
                        }
                    }
                });
    }

    @Override
    public void onWsFailure(Throwable e, Response response) {
        if (e instanceof ConnectException || e instanceof SocketTimeoutException) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showToast(Local.s(getResources().getString(R.string.tiggle_ws_connect_error)));
                    if (dialog != null)
                        dialog.dismiss();
                    mSuccessCount = 0;
                    isScaning = false;
                    secondPlugId = "";
                    mSuccessCount = 0;
                    closeWindow();
                    closeLoadingFragment();

                }
            });
        }
    }

    @Override
    public void onWsClosing(int i, String s) {

    }

    @Override
    public void onWsMessage(String messageStr) {
        if (TextUtils.isEmpty(messageStr)) {
            return;
        }

        if (messageStr.equals(ALREADLY_ADD)) {
            showToast(getResources().getString(R.string.tiggle_has_plug));
            if (dialog != null)
                dialog.dismiss();
            mSuccessCount = 0;
        } else if (messageStr.equals(WS_CLOSE)) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    dialog.setTitleString("");
                    dialog.setOkText("");
                    dialog.setOkTextAlpha(1f);
                    dialog.setContent(getResources().getString(R.string.learn_step_fail));
                    dialog.setApng("json/animation_failed.json", 0);
                    dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), 60)
                            , DisplayUtil.dip2px(getDelegateActivity(), 60)
                            , DisplayUtil.dip2px(getDelegateActivity(), 80)
                            , DisplayUtil.dip2px(getDelegateActivity(), 70));
                }
            });
            String wifiSSID = DDSystemUtil.getWIFISSID(getDelegateActivity());
            if (wifiSSID != null && wifiSSID.toLowerCase()
                    .equals(DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getSsid().toLowerCase())) {
                showToast(getResources().getString(R.string.learn_step_fail));
            } else {
                showErrorToast();
            }
//
            if (dialog != null)
                dialog.dismiss();
            mSuccessCount = 0;
        } else {
            mSuccessCount++;

            if (mSuccessCount == 1) {
                plugId = messageStr;
            } else if (mSuccessCount == 2) {
                secondPlugId = messageStr;
                if (isOtherSiren)
                    plugId = secondPlugId;
            }
            i(messageStr);
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {

                    if (mSuccessCount == 2) {
                        if (isOfficalPlugin)
                            toDoWithOfficalPlugin(messageStr);
                        else
                            toDoWithOutOfficalPlugin(messageStr);
                    } else if (mSuccessCount == 1) {
                        dialog.setTitleString(getResources().getString(R.string.learn_step_1));
                        dialog.setOkText(getResources().getString(R.string.Next));
                        dialog.setOkTextAlpha(1f);
                        dialog.setApng("json/animation_succeed.json", 0);
                        dialog.setContent(getResources().getString(R.string.tiggle_first_success));

                        dialog.setApngSize(DisplayUtil.dip2px(getDelegateActivity(), 60)
                                , DisplayUtil.dip2px(getDelegateActivity(), 60)
                                , DisplayUtil.dip2px(getDelegateActivity(), 80)
                                , DisplayUtil.dip2px(getDelegateActivity(), 70));
                    }
                }
            });
        }
        isScaning = false;
    }

    @Override
    public void onError(Throwable throwable) {
        showToast(Local.s(getResources().getString(R.string.tiggle_ws_connect_error)));
        closeLoadingFragment();
    }

    /**
     * 没有主机或主机离线的情况下添加主机配件提示
     */
    private void showAddRFPluginOnPanelStateError() {
        AlertDialog.createBuilder(getContext())
                .setOk("OK")
                .setContent(getString(R.string.no_panel_add_plugin_hint))
                .preBuilder()
                .show();
    }
}

