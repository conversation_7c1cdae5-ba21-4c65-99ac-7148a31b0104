package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.InputType;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.NumberEditText;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;
import com.jakewharton.rxbinding.view.RxView;
import com.jakewharton.rxbinding.widget.RxTextView;

import rx.Observable;
import rx.functions.Action1;
import rx.functions.Func2;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ChangePasswordFragment extends BaseFragment {

    LocalTextView commonBarTitle;

    NumberEditText changePasswordOldInput;

    ImageView changePasswordOldInputIcon;

    NumberEditText changePasswordNewInput;

    ImageView changePasswordNewInputIcon;

    NumberEditText changePasswordConfirmInput;

    ImageView changePasswordConfirmInputIcon;

    LocalCustomButton changePasswordSave;

    LocalTextView changePasswordForgot;

    public static ChangePasswordFragment newInstance() {
        return new ChangePasswordFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.change_password_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        changePasswordOldInput.setInputType(129);
        changePasswordNewInput.setInputType(129);
        changePasswordConfirmInput.setInputType(129);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.change_password_old_input_icon).setOnClickListener( v -> toShowOldPassword());
        rootView.findViewById(R.id.change_password_new_input_icon).setOnClickListener( v -> toShowNewPassword());
        rootView.findViewById(R.id.change_password_confirm_input_icon).setOnClickListener( v -> toShowConfirmPassword());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
        rootView.findViewById(R.id.change_password_save).setOnClickListener( v -> toSave());
        rootView.findViewById(R.id.common_background).setOnClickListener( v -> closeInput());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        changePasswordOldInput = rootView.findViewById(R.id.change_password_old_input);
        changePasswordOldInputIcon = rootView.findViewById(R.id.change_password_old_input_icon);
        changePasswordNewInput = rootView.findViewById(R.id.change_password_new_input);
        changePasswordNewInputIcon = rootView.findViewById(R.id.change_password_new_input_icon);
        changePasswordConfirmInput = rootView.findViewById(R.id.change_password_confirm_input);
        changePasswordConfirmInputIcon = rootView.findViewById(R.id.change_password_confirm_input_icon);
        changePasswordSave = rootView.findViewById(R.id.change_password_save);
        changePasswordForgot = rootView.findViewById(R.id.change_password_forgot);
    }

    @Override
    public void initData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.change_password_title));
        changePasswordSave.setLocalText(getResources().getString(R.string.change_password_save));
        changePasswordForgot.setLocalText(getResources().getString(R.string.change_password_forgot));
        changePasswordOldInput.setHint(Local.s(getResources().getString(R.string.change_password_old_hint)));
        changePasswordNewInput.setHint(Local.s(getResources().getString(R.string.change_password_new_hint)));
        changePasswordConfirmInput.setHint(Local.s(getResources().getString(R.string.change_password_confirm_hint)));

        Observable<CharSequence> ObservablePsw = RxTextView.textChanges(changePasswordNewInput);
        Observable<CharSequence> ObservablePassword = RxTextView.textChanges(changePasswordConfirmInput);

        Observable.combineLatest(ObservablePsw, ObservablePassword, new Func2<CharSequence, CharSequence, Boolean>() {
            @Override
            public Boolean call(CharSequence password, CharSequence password2) {
                return !TextUtils.isEmpty(password.toString())
                        && !TextUtils.isEmpty(password2.toString())
                        && password.toString().equals(password2.toString());
            }
        }).subscribe(new Action1<Boolean>() {
            @Override
            public void call(Boolean aBoolean) {
                if (aBoolean) {
                    changePasswordSave.setAlpha(1f);
                } else {
                    changePasswordSave.setAlpha(0.3f);
                }
                RxView.enabled(changePasswordSave).call(aBoolean);
            }
        });
    }

    public void toShowOldPassword() {
        if (changePasswordOldInput.getInputType() == 129) {
            changePasswordOldInputIcon.setImageResource(R.drawable.icon_form_show);
            changePasswordOldInput.setInputType(InputType.TYPE_CLASS_TEXT);

            //因为现在是一个小眼睛决定两个/三个EditText的可视状态。同时修改光标在最右边
            changePasswordNewInput.setInputType(InputType.TYPE_CLASS_TEXT);
            changePasswordConfirmInput.setInputType(InputType.TYPE_CLASS_TEXT);
            changePasswordOldInput.setSelection(changePasswordOldInput.getText().length());
            changePasswordNewInput.setSelection(changePasswordNewInput.getText().length());
            changePasswordConfirmInput.setSelection(changePasswordConfirmInput.getText().length());

        } else {
            changePasswordOldInputIcon.setImageResource(R.drawable.icon_form_hide);
            changePasswordOldInput.setInputType(129);

            changePasswordNewInput.setInputType(129);
            changePasswordConfirmInput.setInputType(129);
            changePasswordOldInput.setSelection(changePasswordOldInput.getText().length());
            changePasswordNewInput.setSelection(changePasswordNewInput.getText().length());
            changePasswordConfirmInput.setSelection(changePasswordConfirmInput.getText().length());
        }
    }

    public void toShowNewPassword() {
        if (changePasswordNewInput.getInputType() == 129) {
            changePasswordNewInputIcon.setImageResource(R.drawable.icon_form_show);
            changePasswordNewInput.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            changePasswordNewInputIcon.setImageResource(R.drawable.icon_form_hide);
            changePasswordNewInput.setInputType(129);
        }
    }

    public void toShowConfirmPassword() {
        if (changePasswordConfirmInput.getInputType() == 129) {
            changePasswordConfirmInputIcon.setImageResource(R.drawable.icon_form_show);
            changePasswordConfirmInput.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            changePasswordConfirmInputIcon.setImageResource(R.drawable.icon_form_hide);
            changePasswordConfirmInput.setInputType(129);
        }
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    public void toSave() {
        if (TextUtils.isEmpty(changePasswordNewInput.getText()) && TextUtils.isEmpty(changePasswordConfirmInput.getText())) {
//            showToast("密码不能为空");
            return;
        }
        if (toCheck()) {
            if (changePasswordNewInput.getText().toString().length() >= 6) {
                if (toCheckOldPassword()) {
                    showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loading));
                    changePasswordSave.setEnabled(false);
                    DinSDK.getUserInstance().changePassword(changePasswordOldInput.getText().toString(),
                            changePasswordNewInput.getText().toString(), new IResultCallback() {
                                @Override
                                public void onError(int i, String s) {
                                    changePasswordSave.setEnabled(true);
                                    closeLoadingFragment();
                                    showErrorToast();
                                }

                                @Override
                                public void onSuccess() {
                                    changePasswordSave.setEnabled(true);
                                    closeLoadingFragment();
                                    DBUtil.Put(DBKey.TOKEN, DinSDK.getUserInstance().getUser().getToken());
                                    if (DBUtil.contain(DBKey.USER_PASSWORD)) {
                                        DBUtil.SPut(DBKey.USER_PASSWORD, changePasswordNewInput.getText().toString());
                                    }
                                    close();
                                    showSuccess();
                                }
                            });
                } else {
//                    showToast("旧密码错误");
                    showErrorToast();
//                    showToast(getResources().getString(R.string.password_last_than_six));
                }

            } else {
                showToast(getResources().getString(R.string.password_last_than_six));
            }
        } else {
            showToast(Local.s(getResources().getString(R.string.password_not_match)));
        }
    }

    private boolean toCheck() {
        return changePasswordNewInput.getText().toString().equals(
                changePasswordConfirmInput.getText().toString());
    }

    private boolean toCheckOldPassword() {
        return true;
//        return DBUtil.SGet(DBKey.USER_PASSWORD).equals(changePasswordOldInput.getText().toString());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void closeInput() {
        toCloseInput();
    }

}

