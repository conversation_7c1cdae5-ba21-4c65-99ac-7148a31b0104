package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafe.Dinsafe;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.RandomStringUtils;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/12.
 */
public class ModifyPlugsFragment extends BaseFragment implements IDeviceCallBack, IPluginBindCallBack {
    private static final String KEY_PLUGIN_INFO = "PLUGIN_INFO";

    /**
     * 真实配件ID
     */
    public static final String ID = "id";

    /**
     * 二维码上面显示的id
     */
    public static final String QRCODE = "qrcode";

    public static final String ISOFFICAL = "isoffical";

    public static final String ISSHOWWAVE = "iswave";

    public static final String NAME = "name";

    public static final String ISADD = "isAdd";

    public static final String SIRENDATA = "sirendata";

    public static final String Delete = "delete";

    public static final String MESSAGEID = "messageid";

    public static final int ONE = 49;

    private static final String STYPE = "stype";

    LocalTextView commonBarTitle;
    ImageView commonBarTitleRightIcon;
    TextView modifyPlugsId;
    EditText modifyPlugsInput;
    LocalTextView modifyPlugsType;
    LocalTextView modifyPlugsNetwork;
    LocalTextView sirenSetting;
    //    @BindView(R.id.modify_name_delete)
//    LocalCustomButton modifyNameDelete;
    LocalTextView modifyPlugsHint;
    LocalCustomButton commonBarLeftIcon;

    private String messageId;
    private String ipcData;
    private String id;
    private String sirenData;
    private ICallBack callBack;
    private Device mPluginDevice;
    private boolean isSelfOperate;
    private Plugin mAddPlugin;

    public static ModifyPlugsFragment newAddNotOfficalInstance(String name, String id, String stype) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, true);
        args.putBoolean(Delete, false);
        args.putBoolean(ISOFFICAL, false);
        args.putString(STYPE, stype);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newAddNotOfficalInstance(String name, String id, String stype, Plugin plugin) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, true);
        args.putBoolean(Delete, false);
        args.putBoolean(ISOFFICAL, false);
        args.putString(STYPE, stype);
        args.putSerializable(KEY_PLUGIN_INFO, plugin);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newChangeNotOfficalInstance(String name, String id, String stype) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, false);
        args.putBoolean(Delete, true);
        args.putBoolean(ISOFFICAL, false);
        args.putString(STYPE, stype);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newInstance(String name, String id, boolean isAddPlug, boolean isOffical) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, !isAddPlug);
        args.putBoolean(ISOFFICAL, isOffical);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newInstance(String name, String id, boolean isAddPlug, boolean isOffical, Plugin plugin) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, !isAddPlug);
        args.putBoolean(ISOFFICAL, isOffical);
        args.putSerializable(KEY_PLUGIN_INFO, plugin);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    /**
     * @param name
     * @param qrcode    配件二维码，有可能跟id不一样
     * @param id
     * @param isAddPlug
     * @param isOffical
     * @return
     */
    public static ModifyPlugsFragment newInstance(String name, String qrcode, String id, boolean isAddPlug, boolean isOffical) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(QRCODE, qrcode);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, !isAddPlug);
        args.putBoolean(ISOFFICAL, isOffical);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newInstance(String name, String qrcode, String id, boolean isAddPlug, boolean isOffical, Plugin plugin) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(QRCODE, qrcode);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, !isAddPlug);
        args.putBoolean(ISOFFICAL, isOffical);
        args.putSerializable(KEY_PLUGIN_INFO, plugin);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newInstance(String name, String qrcode, String id, boolean isAddPlug, String sirenData, boolean isOffical) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(QRCODE, qrcode);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, !isAddPlug);
        args.putString(SIRENDATA, sirenData);
        args.putBoolean(ISOFFICAL, isOffical);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newInstance(String name, String id, boolean isAddPlug, String sirenData, boolean isOffical) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, !isAddPlug);
        args.putString(SIRENDATA, sirenData);
        args.putBoolean(ISOFFICAL, isOffical);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newInstance(String name, String id, boolean isAddPlug, boolean isShowDelete, boolean isOffical, boolean isShowWave) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, isShowDelete);
        args.putBoolean(ISOFFICAL, isOffical);
        args.putBoolean(ISSHOWWAVE, isShowWave);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyPlugsFragment newInstance(String name, String qrcode, String id,
                                                  boolean isAddPlug, boolean isShowDelete,
                                                  boolean isOffical, boolean isShowWave) {
        ModifyPlugsFragment modifyPlugsFragment = new ModifyPlugsFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(QRCODE, qrcode);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAddPlug);
        args.putBoolean(Delete, isShowDelete);
        args.putBoolean(ISOFFICAL, isOffical);
        args.putBoolean(ISSHOWWAVE, isShowWave);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.modify_plugs_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        mAddPlugin = (Plugin) getArguments().getSerializable(KEY_PLUGIN_INFO);
        if (getArguments().getBoolean(ISADD)) {
            DinSDK.getPluginActivtor().addBindCallBack(this);
        }
        initView(rootView, savedInstanceState);
        initData();
        findDevice();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.common_bar_right_icon).setOnClickListener( v -> clickMore());
        rootView.findViewById(R.id.modify_plugs_network).setOnClickListener( v -> toNetWork());
        rootView.findViewById(R.id.siren_setting).setOnClickListener( v -> toSirenSetting());
        rootView.findViewById(R.id.btn_save).setOnClickListener( v -> toSave());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarTitleRightIcon = rootView.findViewById(R.id.common_bar_right_icon);
        modifyPlugsId = rootView.findViewById(R.id.modify_plugs_id);
        modifyPlugsInput = rootView.findViewById(R.id.modify_plugs_input);
        modifyPlugsType = rootView.findViewById(R.id.modify_plugs_type);
        modifyPlugsNetwork = rootView.findViewById(R.id.modify_plugs_network);
        sirenSetting = rootView.findViewById(R.id.siren_setting);
        modifyPlugsHint = rootView.findViewById(R.id.modify_plugs_hint);
        commonBarLeftIcon = rootView.findViewById(R.id.btn_save);
    }

    private void findDevice() {
        String pluginId = id;
        if (!TextUtils.isEmpty(getArguments().getString(QRCODE))) {
            pluginId = getArguments().getString(QRCODE);
        }
        if (!TextUtils.isEmpty(pluginId)) {
            mPluginDevice = DinHome.getInstance().getDevice(pluginId);
            if (null != mPluginDevice) {
                mPluginDevice.registerDeviceCallBack(this);
            }
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        //初始化先设置为不可用
        commonBarLeftIcon.setEnabled(false);
        modifyPlugsInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                commonBarLeftIcon.setEnabled(s.length() > 0);
            }
        });

//        modifyNameDelete.setOnClickListener(v -> toDeleteItem());
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.modify_plugs_title));
        commonBarLeftIcon.setLocalText(getString(R.string.save));
        modifyPlugsInput.setHint(Local.s(getResources().getString(R.string.modifyaccessoryhint)));
//        modifyNameDelete.setLocalText(getResources().getString(R.string.change_permission_delete));
        String text = getArguments().getString(ID);
        boolean isOffical = getArguments().getBoolean(ISOFFICAL);
        sirenData = getArguments().getString(SIRENDATA);
        if (getArguments().getBoolean(Delete)) {
            commonBarTitleRightIcon.setVisibility(View.VISIBLE);
        } else {
            commonBarTitleRightIcon.setVisibility(View.GONE);
        }
        if (getArguments().getBoolean(ISADD)) {
            commonBarLeftIcon.setVisibility(View.VISIBLE);
        } else {
            commonBarLeftIcon.setVisibility(View.GONE);
            modifyPlugsInput.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icon_nav_edit, 0);
            modifyPlugsInput.setFocusable(false);
            modifyPlugsInput.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showChangeNameDialog();
                }
            });
        }
        id = text;
        if (isOffical) {
            try {
                JSONObject jsonObject = new JSONObject(text);
                id = jsonObject.getString("id");
                if (TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "t"))) {
                    ipcData = text;
                    if (jsonObject.getInt("wave") == 1 && getArguments().getBoolean(ISSHOWWAVE)) {
                        modifyPlugsNetwork.setLocalText(getResources().getString(R.string.modify_plugs_network));
                        modifyPlugsHint.setVisibility(View.GONE);
                    } else {
                        modifyPlugsNetwork.setVisibility(View.GONE);
                        modifyPlugsHint.setLocalText(getResources().getString(R.string.modify_plugs_hint));
                        modifyPlugsHint.setVisibility(View.GONE);
                    }
                } else {
                    JSONObject newJson = new JSONObject();
                    newJson.put("key", DDJSONUtil.getString(jsonObject, "key"));
                    newJson.put("id", DDJSONUtil.getString(jsonObject, "id"));
                    String str = Integer.toBinaryString(Integer.valueOf(DDJSONUtil.getString(jsonObject, "t"), 16));
                    while (str.length() < 5)
                        str = "0" + str;
                    if (str.charAt(0) == ONE) {
                        newJson.put("move", 1);
                    } else {
                        newJson.put("move", 0);
                    }

                    if (str.charAt(1) == ONE) {
                        newJson.put("monitor", 1);
                    } else {
                        newJson.put("monitor", 0);
                    }

                    if (str.charAt(2) == ONE) {
                        newJson.put("talk", 1);
                    } else {
                        newJson.put("talk", 0);
                    }

                    if (str.charAt(3) == ONE) {
                        newJson.put("wifi", 1);
                    } else {
                        newJson.put("wifi", 0);
                    }

                    if (str.charAt(4) == ONE) {
                        newJson.put("wave", 1);
                    } else {
                        newJson.put("wave", 0);
                    }
                    ipcData = newJson.toString();
                    if (newJson.getInt("wave") == 1) {
                        modifyPlugsNetwork.setLocalText(getResources().getString(R.string.modify_plugs_network));
                    } else {
                        modifyPlugsNetwork.setVisibility(View.GONE);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
                modifyPlugsNetwork.setVisibility(View.GONE);
                ipcData = null;
            }

//        无线警笛是15位
            if (checkIsPlugs()) {
                //检查小Type
                final String sType = CommonDataUtil.getInstance().getSTypeByID(id);
                modifyPlugsType.setLocalText(sType);
            } else {
                showToast(getResources().getString(R.string.illegal_ID));
                removeSelf();
            }

            if (CommonDataUtil.getInstance().checkIsSiren(id) && !getArguments().getBoolean(ISADD)) {
                sirenSetting.setLocalText(getResources().getString(R.string.siren_setting));
                sirenSetting.setVisibility(View.VISIBLE);
            } else {
                sirenSetting.setVisibility(View.GONE);
            }

            if (!TextUtils.isEmpty(getArguments().getString(QRCODE))) {
                modifyPlugsId.setText("ID:" + getArguments().getString(QRCODE));
            } else {
                modifyPlugsId.setText("ID:" + id);
            }

            if (!TextUtils.isEmpty(getArguments().getString(NAME))) {
                modifyPlugsInput.setText(getArguments().getString(NAME));
            } else {
//            String dinID = Dinsafe.str64ToHexStr(id);
//            modifyPlugsInput.setText(Local.s(sType) + "_" + dinID.substring(3, 7));
            }
        } else {
            modifyPlugsNetwork.setVisibility(View.GONE);
            modifyPlugsId.setVisibility(View.GONE);
            sirenSetting.setVisibility(View.GONE);
            modifyPlugsType.setLocalText(CommonDataUtil.getInstance().getSType(getArguments().getString(STYPE)));
            if (!TextUtils.isEmpty(getArguments().getString(NAME))) {
                modifyPlugsInput.setText(getArguments().getString(NAME));
            } else {
            }
        }

    }

    public boolean checkIsPlugs() {
        try {
            String code = Dinsafe.str64ToHexStr(id);

            if (code.length() == 11 || code.length() == 15) {
                String type = code.substring(1, 3);
                int bigType = Integer.parseInt(code.substring(0, 1));
                if (bigType >= 0 && bigType <= 9) {
                    return CommonDataUtil.getInstance().checkHasKey(type);
                }
            }
        } catch (Exception ex) {
            return false;
        }
        return false;

    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    public void clickMore() {
        showMoreActionDialog();
    }


    public void toNetWork() {
//        getMainActivity().showToast(getResources().getString(R.string.wave_hint_2),
//                new AlertDialog.AlertOkClickCallback() {
//                    @Override
//                    public void onOkClick() {
//                        getDelegateActivity().addCommonFragment(ModifyPlugsNetWorkFragment.newInstance(id, false));
//                    }
//                });
        getDelegateActivity().addCommonFragment(ModifyPlugsNetWorkFragment.newInstance(id, false, ""));
    }

    public void toSirenSetting() {
        getDelegateActivity().addCommonFragment(SirenSettingFragment.newInstance(sirenData, id));
    }

    public void toSave() {
        final String inputName = modifyPlugsInput.getText().toString().trim();
        if (TextUtils.isEmpty(inputName) || !RegxUtil.isLegalName(inputName)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail,  Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }

        if (getArguments().getBoolean(ISADD)) {
            if (null == mAddPlugin) {
                DDLog.e(TAG, "Error!!! Empty plugin info.");
                showErrorToast();
                return;
            }

            showLoadingFragment(LoadingFragment.BLACK, "");
            mAddPlugin.setPluginName(inputName);
            DinSDK.getPluginActivtor().bindDevice(mAddPlugin);
        } else {
            // 修改名字
            showTimeOutLoadinFramgmentWithErrorAlert();
            messageId = RandomStringUtils.getMessageId();
            toChangePluginName();
        }
    }

    public void toChangePluginName() {
        DDLog.i(TAG, "toChangePluginName");
        if (null != mPluginDevice) {
            DDLog.i(TAG, "修改名字");
            isSelfOperate = true;
            mPluginDevice.submit(PanelParamsHelper.setPluginName(modifyPlugsInput.getText().toString()));
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    public void toDeleteItem() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (null != mPluginDevice) {
                            showLoadingFragment(LoadingFragment.BLACK, "");
                            isSelfOperate = true;
                            mPluginDevice.submit(PanelParamsHelper.deletePlugin());
                        } else {
                            DDLog.e(TAG, "Null plugin device");
                            showErrorToast();
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        DinSDK.getPluginActivtor().removeBindCallBack(this);
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
        if (callBack != null) {
            callBack = null;
        }
    }

    public void showChangeNameDialog() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultName(modifyPlugsInput.getText().toString())
                .setContent(getResources().getString(R.string.rename_accessory))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
//                                            change ipc password
                        if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail,  Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                            dialog.dismiss();
                            return;
                        }
                        dialog.dismiss();
                        modifyPlugsInput.setText(string);
                        toSave();
                    }
                })
                .preBuilder()
                .show();
    }

    public interface ICallBack {
        void onDeletePlug(String id);

        void onChangeName(String name);
    }

    public ICallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(ICallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (isSelfOperate) {
            isSelfOperate = false;
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                showErrorToast();
                return;
            }

            if (PluginCmd.PLUGIN_SETNAME.equals(cmd)) {
                try {
                    String resultStr = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                    JSONObject jsonObject = new JSONObject(resultStr);
                    String name = jsonObject.getString("plugin_item_name");
                    EventBus.getDefault().post(new PlugsNameChangeEvent(name));
                    removeSelf();
                    if (callBack != null)
                        callBack.onChangeName(name);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (PluginCmd.PLUGIN_DELETE.equals(cmd)) {
                removeSelf();
                showSuccess();
                if (callBack != null)
                    callBack.onDeletePlug(id);
                EventBus.getDefault().post(new PluginDeleteEvent(id));
            }
        } else {
            String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                return;
            }
            if (PluginCmd.CHANGE_SIREN_SETTING.equals(cmd)) {
                DDLog.i(TAG, "CHANGE_SIREN_SETTING");
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                    if (LocalKey.SET_WIRELESS_SIREN_ADVANCED_SETTING.equals(operateCmd)) {
                        String pluginid = jsonObject.getString("pluginid");
                        if (id.equals(pluginid)) {
                            sirenData = jsonObject.getString("plugin_item_wireless_siren_advanced_setting");
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void onBindResult(int code, String msg) {
        DDLog.i(TAG, "onBindResult, code: " + code + ", msg: " + msg);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (code == 1) {
            removeSelf();
        } else if (ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL == code) {
            showErrorToast();
        } else if (ErrorCode.ACTIVTOR_ALREAD_HAS_PLUGIN == code) {
            removeSelf();
            showToast(getResources().getString(R.string.tiggle_has_plug));
        } else {
            showErrorToast();
        }
    }

    public void showMoreActionDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.delete)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (0 == index) {
                            toDeleteItem();
                        }
                    }
                }).show();
    }
}

