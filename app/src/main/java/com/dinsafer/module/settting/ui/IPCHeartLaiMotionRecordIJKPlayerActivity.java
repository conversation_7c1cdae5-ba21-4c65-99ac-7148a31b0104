package com.dinsafer.module.settting.ui;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityIpcSosRecordIjkplayerBinding;
import com.dinsafer.dscam.DsCamMultiFullPlayActivity;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.GoMotionRecordListFragmentEvent;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.module.BaseFragmentActivity;
import com.dinsafer.module.ipc.heartlai.HeartLaiFullPlayActivity;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.ScreenUtils;
import com.githang.statusbar.StatusBarCompat;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;

import tv.danmaku.ijk.media.player.IjkMediaPlayer;


public class IPCHeartLaiMotionRecordIJKPlayerActivity extends BaseFragmentActivity {
    private String TAG = "HeartLai";

    private ActivityIpcSosRecordIjkplayerBinding mBinding;

    private String url;
    private String ipcId;
    private String provider;
    private String ipcName;

    private boolean isFullScreen = false;

    private SimpleDateFormat sdf = new SimpleDateFormat("mm:ss");

    private boolean isAnimation = true;

    private Device device;

    public static void start(Context context, String url, String ipcId, String provider, String ipcname) {
        Intent starter = new Intent(context, IPCHeartLaiMotionRecordIJKPlayerActivity.class);
        starter.putExtra("url", url);
        starter.putExtra("ipcId", ipcId);
        starter.putExtra("provider", provider);
        starter.putExtra("ipcname", ipcname);
        context.startActivity(starter);
    }

    @Override
    protected boolean initVariables() {
        url = getIntent().getStringExtra("url");
        ipcId = getIntent().getStringExtra("ipcId");
        provider = getIntent().getStringExtra("provider");
        ipcName = getIntent().getStringExtra("ipcname");

        device = IPCManager.getInstance().getDsCamDeviceByID(ipcId);
        if (device == null) {
            device = IPCManager.getInstance().getHeartLaiDeviceByPID(ipcId);
        }
        return true;
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_ipc_sos_record_ijkplayer);
        StatusBarCompat.setStatusBarColor(this, getResources().getColor(R.color.black), false);

        mBinding.commonBar.commonBarBack.setOnClickListener(v -> finish());
        mBinding.btnBackLand.setOnClickListener(v -> finish());
        mBinding.btnBackLand2.setOnClickListener(v -> finish());
        mBinding.resumePause.setOnClickListener(v -> clickPlay());
        mBinding.btnFunctionPlayLand.setOnClickListener(v -> {
            mBinding.llFunctionBtnLand.setVisibility(View.GONE);
            clickPlay();
        });
        mBinding.glviewFullscreen.setOnClickListener(v -> toFullScreen());

        mBinding.videoView.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_UP)
                clickPlay();
            return true;
        });

        mBinding.btnGoLivePort.setLocalText(getString(R.string.ipc_motion_detect_go_live));
        mBinding.btnGoLivePort.setOnClickListener(v -> clickGoLive());
        mBinding.btnGoLiveLand.setLocalText(getString(R.string.ipc_motion_detect_go_live));
        mBinding.btnGoLiveLand.setOnClickListener(v -> clickGoLive());

        mBinding.btnAllMessagePort.setLocalText(getString(R.string.contact_push_all));
        mBinding.btnAllMessagePort.setOnClickListener(v -> clickAllMessage());
        mBinding.btnAllMessageLand.setLocalText(getString(R.string.contact_push_all));
        mBinding.btnAllMessageLand.setOnClickListener(v -> clickAllMessage());

        if (device == null) {
            mBinding.btnGoLivePort.setVisibility(View.GONE);
            mBinding.btnGoLiveLand.setVisibility(View.GONE);
        } else {
            mBinding.btnGoLivePort.setVisibility(View.VISIBLE);
            mBinding.btnGoLiveLand.setVisibility(View.VISIBLE);
        }

        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mBinding.rlPlayer.getLayoutParams();
        layoutParams.height = (int) (ScreenUtils.getScreenWidth(this) * 0.53625f);
        mBinding.rlPlayer.setLayoutParams(layoutParams);
    }

    @Override
    protected void loadData() {
        EventBus.getDefault().register(this);
        // init player
        IjkMediaPlayer.loadLibrariesOnce(null);
        IjkMediaPlayer.native_profileBegin("libijkplayer.so");

        mBinding.commonBar.commonBarTitle.setLocalText(ipcName);

        mBinding.videoView.setOnPreparedListener(mp -> {
            if (isAnimation) {
                isAnimation = false;
                mBinding.imgLoading.setVisibility(View.GONE);
                mBinding.imgLoading.clearAnimation();
                mBinding.glviewFullscreen.setVisibility(View.VISIBLE);
            }
            handler.post(run);
            mp.setOnCompletionListener(mp1 -> {
                mBinding.seekBar.setProgress(0);
                mBinding.seekBarFullscreen.setProgress(0);
                mBinding.videoView.seekTo(0);
                mBinding.videoView.pause();
                if (isFullScreen) {
                    mBinding.llFunctionBtnLand.setVisibility(View.VISIBLE);
                } else {
                    mBinding.resumePause.setVisibility(View.VISIBLE);
                }
            });
        });

        Animation operatingAnim = AnimationUtils.loadAnimation(this, R.anim.rotation);
        LinearInterpolator lin = new LinearInterpolator();
        operatingAnim.setInterpolator(lin);
        mBinding.imgLoading.startAnimation(operatingAnim);
        mBinding.glviewFullscreen.setVisibility(View.GONE);

        mBinding.videoView.setVideoPath(url);
        mBinding.videoView.start();
    }


    private void clickAllMessage() {
        if (ActivityController.getInstance().getFragment(IPCSosRecordListFragment.class) != null) {
            finish();
        } else {
            EventBus.getDefault().post(new GoMotionRecordListFragmentEvent());
            finish();
        }
    }

    private void clickGoLive() {
        if (DsCamUtils.isDsCamDevice(device)) {
//            DsCamFullPlayActivity.startActivity(this, ipcId);
            final ArrayList<String> params = new ArrayList<>();
            params.add(ipcId);
            DsCamMultiFullPlayActivity.start(this, params);
        } else if (HeartLaiUtils.isHeartLaiDevice(device)) {
            HeartLaiFullPlayActivity.startActivity(this, ipcId);
        } else if (DsCamUtils.isDsCamV006Device(device)) {
//            DsCamV006FullPlayActivity.startActivity(this, ipcId);
            final ArrayList<String> params = new ArrayList<>();
            params.add(ipcId);
            DsCamMultiFullPlayActivity.start(this, params);
        } else {
        }
    }

    public void clickPlay() {
        if (mBinding.videoView.isPlaying()) {
            mBinding.resumePause.setVisibility(View.VISIBLE);
            mBinding.videoView.pause();
        } else {
            mBinding.resumePause.setVisibility(View.GONE);
            mBinding.videoView.start();
        }
    }

    public void toFullScreen() {
        if (!isFullScreen) {
            makeVideoFullScreen(false);
        } else {
            exitVideoFullScreen(false);
        }
        isFullScreen = !isFullScreen;
    }

    private LinearLayout.LayoutParams defaultVideoViewParams;
    private int defaultScreenOrientationMode;

    private void makeVideoFullScreen(boolean autoRotate) {
        defaultScreenOrientationMode = getResources().getConfiguration().orientation;
        defaultVideoViewParams = (LinearLayout.LayoutParams) mBinding.rlPlayer.getLayoutParams();
        if (!autoRotate) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
        mBinding.controlView.setVisibility(View.GONE);
        mBinding.btnBackLand.setVisibility(View.VISIBLE);
        mBinding.fullscreenControl.setVisibility(View.VISIBLE);
        mBinding.commonBar.getRoot().setVisibility(View.GONE);
        mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_small_screen);
        mBinding.rlPlayer.postDelayed(new Runnable() {

            @Override
            public void run() {
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) new LinearLayout.LayoutParams(
                        RelativeLayout.LayoutParams.MATCH_PARENT,
                        RelativeLayout.LayoutParams.MATCH_PARENT);

                mBinding.rlPlayer.setLayoutParams(params);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);
    }

    private void exitVideoFullScreen(boolean autoRotate) {
        if (autoRotate) {
            setRequestedOrientation(defaultScreenOrientationMode);
        } else {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
        mBinding.controlView.setVisibility(View.VISIBLE);
        mBinding.btnBackLand.setVisibility(View.GONE);
        mBinding.fullscreenControl.setVisibility(View.GONE);
        mBinding.llFunctionBtnLand.setVisibility(View.GONE);
        mBinding.commonBar.getRoot().setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarTitle.setLocalText(ipcName);
        mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_full_screen);
        mBinding.rlPlayer.postDelayed(new Runnable() {

            @Override
            public void run() {
                mBinding.rlPlayer.setLayoutParams(defaultVideoViewParams);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.d(TAG, "onConfigurationChanged: ");
        // Checks the orientation of the screen
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            isFullScreen = true;
            makeVideoFullScreen(true);
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            isFullScreen = false;
            exitVideoFullScreen(true);
        }
    }

    private Handler handler = new Handler();
    private Runnable run = new Runnable() {
        @Override
        public void run() {
            // 获得当前播放时间和当前视频的长度
            // 获得当前播放时间和当前视频的长度
            int currentPosition = mBinding.videoView.getCurrentPosition();
            int duration = mBinding.videoView.getDuration();
            // 设置进度条的主要进度，表示当前的播放时间
            mBinding.seekBar.setProgress(currentPosition);
            mBinding.seekBar.setMax(duration);
            mBinding.seekBarFullscreen.setProgress(currentPosition);
            mBinding.seekbarTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
            mBinding.seekBarFullscreenTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
            mBinding.seekBarFullscreen.setMax(duration);

            handler.postDelayed(run, 1000);
        }
    };

    @Override
    public void onStop() {
        super.onStop();
        mBinding.videoView.release(true);
        mBinding.videoView.stopBackgroundPlay();
        IjkMediaPlayer.native_profileEnd();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        mBinding.videoView.setOnPreparedListener(null);
        mBinding.videoView.release(true);
        mBinding.videoView.stopBackgroundPlay();
        IjkMediaPlayer.native_profileEnd();
        handler.removeCallbacks(run);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayMotionDetectedRecordEvent(PlayMotionDetectedRecordEvent playMotionDetectedRecordEvent) {
        finish();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSOSevent(SOSevent soSevent) {
        finish();
    }
}

