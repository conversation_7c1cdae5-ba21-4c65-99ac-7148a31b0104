package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ChoosePhoneZoneItemBinding;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class TimePhoneZoneAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<String> mData;

    public String indexString;

    public TimePhoneZoneAdapter(Activity mActivity, ArrayList<String> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    public void setData(ArrayList<String> mData) {
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    public String getIndex() {
        return indexString;
    }

    public void setIndex(String index) {
        this.indexString = index;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.choose_phone_zone_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        holder.binding.choosePhoneZoneText.setText(mData.get(position));

        if (!TextUtils.isEmpty(indexString) && mData.get(position).equals(indexString)) {
            Drawable drawable = mActivity.getResources()
                    .getDrawable(R.drawable.btn_define_setting_select);
            drawable.setTint(ContextCompat.getColor(mActivity, R.color.color_brand_primary));
            holder.binding.choosePhoneZoneText.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null);
        } else {
            holder.binding.choosePhoneZoneText.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        }

        return convertView;
    }

    public void remove(int index) {
        if (mData != null && index >= 0 && index < mData.size()) {
            mData.remove(index);
            notifyDataSetChanged();
        }
    }

    static class ViewHolder {
        private ChoosePhoneZoneItemBinding binding;
        ViewHolder(View view) {
            binding = DataBindingUtil.bind(view);
        }
    }
}
