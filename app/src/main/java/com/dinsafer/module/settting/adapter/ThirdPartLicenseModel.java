package com.dinsafer.module.settting.adapter;

import androidx.annotation.NonNull;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemThirdPartLicenseBinding;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/12 4:44 下午
 */
public class ThirdPartLicenseModel implements BaseBindModel<ItemThirdPartLicenseBinding> {

    private final ThirdPartLicenseItem mItemData;

    public ThirdPartLicenseModel(final @NonNull ThirdPartLicenseItem licenseData) {
        this.mItemData = licenseData;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_third_part_license;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemThirdPartLicenseBinding binding) {
        binding.tvName.setText(mItemData.getName());
        binding.tvCopyright.setText(mItemData.getCopyright());
    }
}
