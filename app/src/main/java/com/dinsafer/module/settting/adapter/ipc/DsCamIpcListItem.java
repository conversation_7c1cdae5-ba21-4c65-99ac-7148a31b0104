package com.dinsafer.module.settting.adapter.ipc;

import androidx.recyclerview.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamIPCSettingFragment;
import com.dinsafer.dscam.DsCamPlayerManager;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.ipc.player.IVideoViewListener;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module_dscam.player.DsCamPlayer;
import com.dinsafer.module_dscam.player.IPlayerStatusListener;
import com.dinsafer.module_dscam.player.KRealTimePlayView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;

import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;
import io.github.luizgrp.sectionedrecyclerviewadapter.StatelessSection;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/26 11:56 AM
 */
public class DsCamIpcListItem extends StatelessSection implements IVideoViewListener {
    private static final String TAG = DsCamIpcListItem.class.getSimpleName();

    protected List<Device> mData;
    protected String mTitle;
    protected MainActivity mMainActivity;

    private int position;
    //    private CameraVideoView videoViewRoot;
    private View parent;

    public DsCamIpcListItem(MainActivity activity, String tittle, List<Device> datas) {
        super(SectionParameters.builder()
                .headerResourceId(R.layout.homearm_list_section_header)
                .itemResourceId(R.layout.main_section_panel_item_ipc)
                .build());
        this.mMainActivity = activity;
        this.mTitle = tittle;
        this.mData = datas;
    }

    @Override
    public int getContentItemsTotal() {
        return mData.size();
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new IpcHeaderViewHolder(view, true);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        IpcHeaderViewHolder headerHolder = (IpcHeaderViewHolder) holder;
        headerHolder.setHeader(mTitle);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        MainPanelIpcItemViewHolder viewHolder = new MainPanelIpcItemViewHolder(view, -1);
        viewHolder.setVideoViewListener(this);
        return viewHolder;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        Log.d(TAG, "onBindItemViewHolder: " + position);
        MainPanelIpcItemViewHolder itemHolder = (MainPanelIpcItemViewHolder) holder;
        itemHolder.bindCurrentIndex(position);
        Device device = mData.get(position);
        itemHolder.mTvIpcName.setText((String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, ""));
        //  状态
        itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        if (DsCamUtils.isDeviceConnected(device)) {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_online));
//             电量显示
            if (DsCamUtils.getDeviceBattery(device) > 0) {
                itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(getBatteryIcon(DsCamUtils.getDeviceBattery(device)), 0, 0, 0);
            }
        } else if (DsCamUtils.isDeviceConnecting(device)) {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_connecting));
            if (itemHolder.getCameraVideoView() != null) {
                itemHolder.getCameraVideoView().showLoading();
            }
        } else {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_offline));
            if (itemHolder.getCameraVideoView() != null) {
                itemHolder.getCameraVideoView().showError();
            }
        }
        itemHolder.mIvMore.setOnClickListener(v ->
                mMainActivity.addCommonFragment(DsCamIPCSettingFragment.newInstance(device.getId())));

        // TODO 预览图,helio里面做截图保存，不在dinsdk里面做
//        final String snapshotPath = device.getSnapshot();
//        if (!TextUtils.isEmpty(snapshotPath)) {
//            File file = new File(snapshotPath);
//            if (file.exists()) {
//                itemHolder.getCameraVideoView().setCoverImageUri(Uri.fromFile(file));
//            } else {
//                itemHolder.getCameraVideoView().setCoverImageUri(null);
//            }
//        } else {
//            itemHolder.getCameraVideoView().setCoverImageUri(null);
//        }
    }


    private int getBatteryIcon(int battery) {
        if (battery <= 0) {
            return 0;
        }
        if (battery > 60) {
            return R.drawable.icon_plugin_list_battery_full;
        } else if (battery > 20) {
            return R.drawable.icon_plugin_list_battery_half;
        } else {
            return R.drawable.icon_plugin_list_battery_low;
        }
    }

    @Override
    public void onPlayIconClick(int position, CameraVideoView videoViewRoot, View parent) {
        DDLog.i(TAG, "onPlayIconClick, position: " + position);
        videoViewRoot.getVideoContainer().removeAllViews();
//        TODO play,处理列表过多的时候，surfaceview销毁处理
        if (videoViewRoot == null) {
            return;
        }
        videoViewRoot.showLoading();
        play(position, videoViewRoot, videoViewRoot);
    }

    private void play(int position, CameraVideoView videoViewRoot, View parent) {
        KRealTimePlayView kRealTimePlayView = new KRealTimePlayView(mMainActivity, null);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
        videoViewRoot.getVideoContainer().addView(kRealTimePlayView, lp);
        DsCamPlayer player = DsCamPlayerManager.getInstance().getPlayer(mData.get(position).getId());
        if (player == null) {
            try {
                player = new DsCamPlayer.Builder()
                        .kRealTimePlayView(kRealTimePlayView)
                        .withDevice(mData.get(position))
                        .build();
            } catch (Exception e) {
                e.printStackTrace();
            }
            DsCamPlayerManager.getInstance().addPlayer(mData.get(position).getId(), player);

        } else {
            player.bindView(kRealTimePlayView);
        }
        player.loadData();
        player.play(new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                mMainActivity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        videoViewRoot.showPlaying();
                    }
                });
            }

            @Override
            public void onError(int i, String s) {
                mMainActivity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        videoViewRoot.showPlaying();
                    }
                });
            }
        });
        player.setStatusListener(new IPlayerStatusListener() {
            @Override
            public void onPrepared() {

            }

            @Override
            public void onStarted() {

            }

            @Override
            public void onPlaying() {

            }

            @Override
            public void onPaused() {

            }

            @Override
            public void onRelease() {

            }

            @Override
            public void onWaiting() {

            }

            @Override
            public void onError(int i, String s) {
                mMainActivity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        videoViewRoot.showPlay();
                    }
                });
            }

            @Override
            public void onCompletion() {
                mMainActivity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        videoViewRoot.showPlay();
                    }
                });
            }
        });
    }

    @Override
    public void onErrorIconClick(int position, CameraVideoView videoViewRoot, View parent) {
        connectIPC(position);
    }

    private void connectIPC(int position) {
        IPCManager.getInstance().connectDevice(mData.get(position),true);
    }

    @Override
    public void onFullscreenIconClick(int position, CameraVideoView videoView, View parent) {
        DDLog.i(TAG, "onFullscreenIconClick, position: " + position);
//        TODO 根据不用牌子的摄像头，进入不同的全屏
//        String pid = mData.get(position).getCameraPID();
//        CameraParamsVo cameraParamsVo = HeartLaiServiceManager.getInstance().findCameraByPid(pid);
        this.position = position;
//        this.videoViewRoot = videoView;
        this.parent = parent;
//        HeartLaiFullPlayActivity.startActivity(mMainActivity, cameraParamsVo.getDid(), pid);
    }

}
