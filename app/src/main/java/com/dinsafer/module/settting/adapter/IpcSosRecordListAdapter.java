package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.IPCSosRecord;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.ui.PhotoViewFragment;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.util.DBUtil;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.FailReason;
import com.nostra13.universalimageloader.core.listener.ImageLoadingListener;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Created by Rinfon on 16/7/1.
 */
public class IpcSosRecordListAdapter extends BaseAdapter {

    private Activity mActivity;

    private List<IPCMotionDetectionRecordResponse.RecordBean> mData;

    private SimpleDateFormat myFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    public IpcSosRecordListAdapter(Activity mActivity, List<IPCMotionDetectionRecordResponse.RecordBean> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    public List<IPCMotionDetectionRecordResponse.RecordBean> getData() {
        return mData;
    }

    public void setData(List<IPCMotionDetectionRecordResponse.RecordBean> mData) {
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup viewGroup) {
        ViewHolder itemHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.ipc_sos_record_list_item, null);
            itemHolder = new ViewHolder(convertView);
            convertView.setTag(itemHolder);
        } else {
            try {
                itemHolder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        itemHolder.ipcSosRecordIcon.setImageResource(-1);
        if (!TextUtils.isEmpty(mData.get(position).getCover())) {
            if (DBUtil.Exists(mData.get(position).getCover())) {
                ImageLoader.getInstance().displayImage(DBUtil.Str(mData.get(position).getCover()), itemHolder.ipcSosRecordIcon, new ImageLoadingListener() {
                    @Override
                    public void onLoadingStarted(String imageUri, View view) {

                    }

                    @Override
                    public void onLoadingFailed(String imageUri, View view, FailReason failReason) {
//                        String url = DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.SOS_RECORD_SERVER_IP + mData.get(position).getCover());
                        DBUtil.Put(mData.get(position).getCover(), mData.get(position).getCover());
                    }

                    @Override
                    public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage) {

                    }

                    @Override
                    public void onLoadingCancelled(String imageUri, View view) {

                    }
                });
            } else {
//                String url = DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.SOS_RECORD_SERVER_IP + mData.get(position).getCover());
                DBUtil.Put(mData.get(position).getCover(), mData.get(position).getCover());
                ImageLoader.getInstance().displayImage(mData.get(position).getCover(), itemHolder.ipcSosRecordIcon);
            }
        }
        itemHolder.ipcSosRecordTime.setText(myFmt.format(mData.get(position).getRecordtime() / 1000000));
        itemHolder.ipcSosRecordName.setText(mData.get(position).getIpcname());
        if (DBUtil.Exists(mData.get(position).getEvent_id())) {
            itemHolder.ipcSosRecordUnread.setVisibility(View.INVISIBLE);
            itemHolder.ipcSosRecordTime.setAlpha(0.8f);
            itemHolder.ipcSosRecordTime.setTextColor(mActivity.getResources().getColor(R.color.white));
        } else {
            itemHolder.ipcSosRecordUnread.setVisibility(View.VISIBLE);
            itemHolder.ipcSosRecordTime.setAlpha(1f);
            itemHolder.ipcSosRecordTime.setTextColor(mActivity.getResources().getColor(R.color.colorRoundBtn_1));
        }

//        final ViewHolder finalItemHolder = itemHolder;
//        itemHolder.ipcSosRecordNameLayout.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                DBUtil.Put(mData.get(position).getId(), true);
//                finalItemHolder.ipcSosRecordUnread.setVisibility(View.INVISIBLE);
//                finalItemHolder.ipcSosRecordTime.setAlpha(0.8f);
//                finalItemHolder.ipcSosRecordTime.setTextColor(mActivity.getResources().getColor(R.color.white));
//                Intent intent = new Intent(mActivity, IPCSosRecordActivity.class);
//                intent.putExtra("url", APIKey.SOS_RECORD_SERVER_IP + mData.get(position).getVideo());
//                intent.putExtra("ipcname", mData.get(position).getIpcname());
//                mActivity.startActivity(intent);
//                ((MainActivity) mActivity).setNotNeedToLogin(true);
//            }
//        });

        itemHolder.ipcSosRecordIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ((MainActivity) mActivity).addCommonFragment(PhotoViewFragment.newInstance(mData.get(position).getCover()));
            }
        });

        return convertView;
    }


    static class ViewHolder {
        ImageView ipcSosRecordIcon;
        TextView ipcSosRecordTime;
        TextView ipcSosRecordName;
        RelativeLayout ipcSosRecordNameLayout;
        ImageView ipcSosRecordUnread;
        ImageView ipcSosRecordNor;
        RelativeLayout contactListBackground;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            ipcSosRecordIcon = view.findViewById(R.id.ipc_sos_record_icon);
            ipcSosRecordTime = view.findViewById(R.id.ipc_sos_record_time);
            ipcSosRecordName = view.findViewById(R.id.ipc_sos_record_name);
            ipcSosRecordNameLayout = view.findViewById(R.id.ipc_sos_record_name_layout);
            ipcSosRecordUnread = view.findViewById(R.id.ipc_sos_record_unread);
            ipcSosRecordNor = view.findViewById(R.id.ipc_sos_record_nor);
            contactListBackground = view.findViewById(R.id.contact_list_background);
        }
    }
}
