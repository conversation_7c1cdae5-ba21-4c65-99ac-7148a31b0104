package com.dinsafer.module.settting.ui;

import android.bluetooth.BluetoothGatt;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.utils.HexUtil;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SwitchBotAdvanceSettingLayoutBinding;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.RandomStringUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.TimeUnit;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * Created by LT on 2019/3/28.
 */
public class SwitchBotAdvanceSettingFragment extends MyBaseFragment<SwitchBotAdvanceSettingLayoutBinding> {

    private Builder builder;

    public static final String CONFIG_ONE_BUTTON = "57036400";
    public static final String CONFIG_TWO_BUTTON = "57036410";

    private String id;
    private Boolean isBtnOne;
    private Boolean originStatus;
    private Subscription timer;

    public static SwitchBotAdvanceSettingFragment newInstance(String id, boolean isBtnOne) {
        SwitchBotAdvanceSettingFragment switchBotSettingFragment = new SwitchBotAdvanceSettingFragment();
        Bundle args = new Bundle();
        args.putString("id", id);
        args.putBoolean("isBtnOne", isBtnOne);

        switchBotSettingFragment.setArguments(args);
        return switchBotSettingFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.switch_bot_advance_setting_layout;
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.ipc_setting_advance_setting));
        mBinding.tvUseWallMode.setLocalText(getResources().getString(R.string.switch_bot_use_wall_mode));
        mBinding.tvChangeOrientation.setLocalText(getResources().getString(R.string.switch_bot_change_orientation));
        mBinding.tvFirmwareVersionKey.setLocalText(getResources().getString(R.string.switch_bot_firmware_version));
        mBinding.tvMacKey.setLocalText(getResources().getString(R.string.switch_bot_mac));

        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.switchChangeOrientation.setOnClickListener(v -> removeSelf());

        id = getArguments().getString("id");
        isBtnOne = getArguments().getBoolean("isBtnOne");
        mBinding.switchUseWallMode.setOn(!isBtnOne);

        mBinding.switchUseWallMode.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                if (APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE) {
                    final String uid = CommonDataUtil.getInstance().getUserUid();
                    final String panelToken = DeviceHelper.getString(DinSDK.getHomeInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID()),
                            PanelDataKey.Panel.DEVICE_TOKEN, null);
                    if (TextUtils.isEmpty(uid) || TextUtils.isEmpty(panelToken)) {
                        showErrorToast();
                        mBinding.switchUseWallMode.setOn(!isOn);
                        if (timer != null && !timer.isUnsubscribed()) {
                            timer.unsubscribe();
                        }
                        return;
                    }

                    messageId = RandomStringUtils.getMessageId();
                    showTimeOutLoadinFramgmentWithErrorAlert();
                    Call<StringResponseEntry> call = DinsafeAPI.getApi().changeSwitchBotUseMode(uid, panelToken, messageId, isOn, id);
                    call.enqueue(new Callback<StringResponseEntry>() {
                        @Override
                        public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                            if (response.body().getStatus() != 1) {
                                showErrorToast();
                                mBinding.switchUseWallMode.setOn(!isOn);
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                if (timer != null && !timer.isUnsubscribed()) {
                                    timer.unsubscribe();
                                }

                                return;
                            }
                        }

                        @Override
                        public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                            showErrorToast();
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            mBinding.switchUseWallMode.setOn(!isOn);
                            if (timer != null && !timer.isUnsubscribed()) {
                                timer.unsubscribe();
                            }

                        }
                    });

                } else {

                    showTimeOutLoadinFramgmentWithErrorAlert();
                    controlByBle(id, isOn);
                }
                originStatus = !isOn;
                timer = Observable.interval(LocalKey.TIMEOUT, TimeUnit.MILLISECONDS)
                        .take(1)
                        .compose(bindToLifecycle())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Subscriber<Object>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {

                            }

                            @Override
                            public void onNext(Object o) {
                                DDLog.d(TAG, "bot test timeout");

                                showErrorToast();
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mBinding.switchUseWallMode.setOn(originStatus);
                                if (timer != null && !timer.isUnsubscribed()) {
                                    timer.unsubscribe();
                                }

                            }
                        });
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (timer != null && !timer.isUnsubscribed()) {
            timer.unsubscribe();
        }
        BleManager.getInstance().disconnectAllDevice();
    }

    private String messageId;


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceResultEvent ev) {
        if (ev.getStatus() == 1 && ev.getCmdType().equals(LocalKey.CMD_SET_SWITCH_BOT) && APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE) {
            try {
                JSONObject json = new JSONObject(ev.getReslut());
                if (DDJSONUtil.getString(json, "id").equals(id)) {
                    isBtnOne = !DDJSONUtil.getBoolean(json, "actMode");
                    mBinding.switchUseWallMode.setOn(DDJSONUtil.getBoolean(json, "actMode"));
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    if (timer != null && !timer.isUnsubscribed()) {
                        timer.unsubscribe();
                    }

                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }


    private void controlByBle(String id, boolean isOn) {
        DDLog.d(TAG, "connecting~~~");
        BleManager.getInstance().connect(id, new BleGattCallback() {
            @Override
            public void onStartConnect() {

            }

            @Override
            public void onConnectFail(BleDevice bleDevice, BleException exception) {
                controlFail();
            }

            @Override
            public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {

                BleManager.getInstance().notify(bleDevice,
                        APIKey.SWITCH_BOT_UUID_SERVICE,
                        APIKey.SWITCH_BOT_UUID_CHRA_NOTIFY,
                        new BleNotifyCallback() {
                            @Override
                            public void onNotifySuccess() {
                                DDLog.d(TAG, "onNotifySuccess");
                            }

                            @Override
                            public void onNotifyFailure(BleException exception) {
                                DDLog.d(TAG, "onNotifyFailure");
                            }

                            @Override
                            public void onCharacteristicChanged(byte[] data) {
                                DDLog.d(TAG, "notify data is " + data[0]);
                                if (data[0] == 1) {
                                    mBinding.switchUseWallMode.setOn(!originStatus);
                                    if (advanceSettingListener != null) {
                                        advanceSettingListener.onChangeMode(id, !mBinding.switchUseWallMode.isOn());
                                    }
                                    if (timer != null && !timer.isUnsubscribed()) {
                                        timer.unsubscribe();
                                    }
                                }
                                closeTimeOutLoadinFramgmentWithErrorAlert();

                            }
                        });

                /**
                 * 开启通知成功失败，都去执行。
                 */

                DDLog.d(TAG, "connect success, to write");
                String data = isOn ? CONFIG_TWO_BUTTON : CONFIG_ONE_BUTTON;

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        BleManager.getInstance().write(
                                bleDevice,
                                APIKey.SWITCH_BOT_UUID_SERVICE,
                                APIKey.SWITCH_BOT_UUID_CHRA_WRITE,
                                HexUtil.hexStringToBytes(data),
                                new BleWriteCallback() {
                                    @Override
                                    public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
                                        DDLog.d(TAG, "onWriteSuccess");

                                        new Handler().postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                BleManager.getInstance().disconnect(bleDevice);
                                            }
                                        }, 1000);
                                    }

                                    @Override
                                    public void onWriteFailure(final BleException exception) {
                                        controlFail();
                                        BleManager.getInstance().disconnect(bleDevice);
                                    }
                                });
                    }
                }, 500);
            }

            @Override
            public void onDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {
            }
        });
    }

    private void controlFail() {
        mBinding.switchUseWallMode.setOn(originStatus);
        if (timer != null && !timer.isUnsubscribed()) {
            timer.unsubscribe();
        }
        showErrorToast();
        closeTimeOutLoadinFramgmentWithErrorAlert();
    }

    public interface AdvanceSettingListener {
        void onChangeMode(String id, boolean isOneBtn);
    }

    private AdvanceSettingListener advanceSettingListener;

    public AdvanceSettingListener getAdvanceSettingListener() {
        return advanceSettingListener;
    }

    public void setAdvanceSettingListener(AdvanceSettingListener advanceSettingListener) {
        this.advanceSettingListener = advanceSettingListener;
    }
}
