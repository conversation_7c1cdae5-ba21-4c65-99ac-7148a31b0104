package com.dinsafer.module.settting.ui;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.RelativeLayout;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.RelayControlLayoutBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

/**
 * Created by LT on 2018/10/23.
 */
public class RelayControlFragment extends MyBaseFragment<RelayControlLayoutBinding>
        implements ModifyASKPlugsFragment.ICallBack, IDeviceCallBack {

    private int index;
    /**
     * isCanClickBtn: 如果正在执行指令、正在转转转，其他按钮便不可控制
     */
    private boolean isCanClickBtn = true;
    private String curAction = PanelConstant.RelayAction.RELAY_ACTION_UP;
    private IRelayCallback iRelayCallback;

    private Device mPluginDevice;

    public static RelayControlFragment newInstance(int index, String name, String id, String sendid, String stype) {
        RelayControlFragment relayControlFragment = new RelayControlFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("index", index);
        bundle.putString("id", id);
        bundle.putString("name", name);
        bundle.putString("sendid", sendid);
        bundle.putString("stype", stype);
        relayControlFragment.setArguments(bundle);

        return relayControlFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.relay_control_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBarRight.setOnClickListener(v -> toEdit());
        mBinding.btnRelayUp.setOnTouchListener((v, event) -> toBtnUp(v, event));
        mBinding.btnRelayDown.setOnTouchListener((v, event) -> toBtnUp(v, event));
        mBinding.btnRelayStop.setOnTouchListener((v, event) -> toBtnUp(v, event));
        findDevice();
    }

    private void findDevice() {
        String pluginId = getArguments().getString("id");
        if (!TextUtils.isEmpty(pluginId)) {
            mPluginDevice = DinHome.getInstance().getDevice(pluginId);
            if (null != mPluginDevice) {
                mPluginDevice.registerDeviceCallBack(this);
            }
        }
    }

    @Override
    public void initData() {
        super.initData();

        index = getArguments().getInt("index");

        mBinding.commonBarTitle.setLocalText(getArguments().getString("name"));
        adjustBgCircle();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        closeWithOutErrorAlert();
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
    }


    /**
     * 右上角编辑
     */
    public void toEdit() {

        JSONObject askData = new JSONObject();
        try {
            askData.put("sendid", getArguments().getString("sendid"));
            askData.put("stype", getArguments().getString("stype"));
            askData.put("id", getArguments().getString("id"));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Builder builder = new Builder();
        builder.setId(getArguments().getString("id"))
                .setAdd(false)
                .setOffical(true)
                .setMessageIndex(index)
                .setShowDelete(true)
                .setName(getArguments().getString("name"))
                .setShowwave(false)
                .setData(askData)
                .setAskPlugin(true);

        ModifyASKPlugsFragment modifyASKPlugsFragment = ModifyASKPlugsFragment.newInstance(builder);
        modifyASKPlugsFragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);

    }

    @Override
    public void onDeletePlug(String id) {
        removeSelf();
        if (iRelayCallback != null) {
            iRelayCallback.onDeleteRelay(index);
        }
    }

    @Override
    public void onChangeName(int index, String name) {
        mBinding.commonBarTitle.setLocalText(name);
        getArguments().putString("name", name);
        if (iRelayCallback != null) {
            iRelayCallback.onChangeRelayName(index, name);
        }
    }

    /**
     * 通知远方去控制继电器
     */
    private void controlRelay() {
        if (null != mPluginDevice) {
            showWithErrorAlert(LocalKey.TIMEOUT);
            isCanClickBtn = false;
            changeBtnStatus();
            mPluginDevice.submit(PanelParamsHelper.controlRelyAction(curAction));
        } else {
            DDLog.e(TAG, "No rely device.");
            showErrorToast();
        }
    }

    public void fail() {
        showErrorToast();
    }

    /**
     * 设置对应的btn变成loading状态
     */
    private void loadingBtn() {
        if (!isCanClickBtn) {
            Animation operatingAnim = AnimationUtils.loadAnimation(getDelegateActivity(), R.anim.rotation);
            LinearInterpolator lin = new LinearInterpolator();
            operatingAnim.setInterpolator(lin);

            switch (curAction) {
                case LocalKey.RELAY_ACTION_UP:
                    mBinding.btnRelayUpLoading.startAnimation(operatingAnim);
                    mBinding.btnRelayUpLoading.setVisibility(View.VISIBLE);
                    mBinding.btnRelayUp.setVisibility(View.GONE);
                    break;
                case LocalKey.RELAY_ACTION_DOWN:
                    mBinding.btnRelayDownLoading.startAnimation(operatingAnim);
                    mBinding.btnRelayDownLoading.setVisibility(View.VISIBLE);
                    mBinding.btnRelayDown.setVisibility(View.GONE);
                    break;
                case LocalKey.RELAY_ACTION_STOP:
                    mBinding.btnRelayStopLoading.startAnimation(operatingAnim);
                    mBinding.btnRelayStopLoading.setVisibility(View.VISIBLE);
                    mBinding.btnRelayStop.setVisibility(View.GONE);
                    break;
            }
        } else {
            switch (curAction) {
                case LocalKey.RELAY_ACTION_UP:
                    mBinding.btnRelayUpLoading.clearAnimation();
                    mBinding.btnRelayUpLoading.setVisibility(View.GONE);
                    mBinding.btnRelayUp.setVisibility(View.VISIBLE);
                    break;
                case LocalKey.RELAY_ACTION_DOWN:
                    mBinding.btnRelayDownLoading.clearAnimation();
                    mBinding.btnRelayDownLoading.setVisibility(View.GONE);
                    mBinding.btnRelayDown.setVisibility(View.VISIBLE);
                    break;
                case LocalKey.RELAY_ACTION_STOP:
                    mBinding.btnRelayStopLoading.clearAnimation();
                    mBinding.btnRelayStopLoading.setVisibility(View.GONE);
                    mBinding.btnRelayStop.setVisibility(View.VISIBLE);
                    break;
            }
        }
    }

    private void changeBtnStatus() {
        if (isCanClickBtn) {
            mBinding.btnRelayUp.setEnabled(true);
            mBinding.btnRelayStop.setEnabled(true);
            mBinding.btnRelayDown.setEnabled(true);

            mBinding.btnRelayUp.setAlpha(1.0f);
            mBinding.btnRelayStop.setAlpha(1.0f);
            mBinding.btnRelayDown.setAlpha(1.0f);

        } else {
            mBinding.btnRelayUp.setEnabled(false);
            mBinding.btnRelayStop.setEnabled(false);
            mBinding.btnRelayDown.setEnabled(false);

            mBinding.btnRelayUp.setAlpha(0.5f);
            mBinding.btnRelayStop.setAlpha(0.5f);
            mBinding.btnRelayDown.setAlpha(0.5f);
        }

        loadingBtn();
    }

    public boolean toBtnUp(View v, MotionEvent motionEvent) {
        switch (motionEvent.getAction()) {
            case MotionEvent.ACTION_UP:
                switch (v.getId()) {
                    case R.id.btn_relay_up:
                        curAction = PanelConstant.RelayAction.RELAY_ACTION_UP;
                        break;

                    case R.id.btn_relay_down:
                        curAction = PanelConstant.RelayAction.RELAY_ACTION_DOWN;
                        break;

                    case R.id.btn_relay_stop:
                        curAction = PanelConstant.RelayAction.RELAY_ACTION_STOP;
                        break;
                }
                isCanClickBtn = false;
                controlRelay();

                break;
        }
        return false;
    }


    /**
     * 调整背景两个圆
     * 不缩放，出现一半在上面
     * <p>
     * 算法：上圆左边距= -（圆宽-屏幕宽）/2
     * 上圆上边距= - 圆高/2
     */
    private void adjustBgCircle() {

        mBinding.bgDown.setVisibility(View.VISIBLE);
        mBinding.bgUp.setVisibility(View.VISIBLE);

        int winWidth = getMainActivity().getWindowManager().getDefaultDisplay().getWidth();
        int winHeight = getMainActivity().getWindowManager().getDefaultDisplay().getHeight();
        Bitmap bmSrc = BitmapFactory.decodeResource(getResources(), R.drawable.img_relay_circle_bg_up);
        int bgCircleWidth = bmSrc.getWidth();
        int bgCircleHeight = bmSrc.getHeight();

        int marginLeft = -(bgCircleWidth - winWidth) / 2;
        int marginTop = -bgCircleHeight / 2;

        //不能盖住中间的按钮,而且上下要超出10dp,
        //即（标题+整个圆+按钮+20） 要大于屏蔽高度
        int commonBarTitleHeight = (int) getMainActivity().getResources().getDimension(R.dimen.common_title_bar_height);
        int btnHeight = (int) getMainActivity().getResources().getDimension(R.dimen.relay_btn_width);
        if ((mBinding.commonBarTitle.getHeight() + mBinding.btnRelayStop.getHeight() + bgCircleHeight +
                dp2px(20) + commonBarTitleHeight + btnHeight) >= winHeight) {
            marginTop = marginTop - btnHeight / 2 - dp2px(10);
        }

        RelativeLayout.LayoutParams upLayoutParams = (RelativeLayout.LayoutParams) mBinding.bgUp.getLayoutParams();
        upLayoutParams.setMargins(marginLeft, marginTop, 0, 0);

        RelativeLayout.LayoutParams downLayoutParams = (RelativeLayout.LayoutParams) mBinding.bgDown.getLayoutParams();
        downLayoutParams.setMargins(marginLeft, 0, 0, marginTop);

        mBinding.bgUp.setLayoutParams(upLayoutParams);
        mBinding.bgDown.setLayoutParams(downLayoutParams);
    }

    private int dp2px(int dp) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                dp, getMainActivity().getResources().getDisplayMetrics());
    }

    interface IRelayCallback {
        void onDeleteRelay(int index);

        void onChangeRelayName(int index, String name);
    }

    public void setIRelayCallback(IRelayCallback iRelayCallback) {
        this.iRelayCallback = iRelayCallback;
    }


    Handler timeoutHandler;

    public final void showWithErrorAlert(long time) {
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                fail();
                isCanClickBtn = true;
                changeBtnStatus();
                CommonDataUtil.getInstance().setCanCoap(false);
                DDLog.d(getClass().getSimpleName(), "call by coap: " + "timeout");
            }
        }, time);
    }

    public final void closeWithOutErrorAlert() {
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, relay control: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);

        closeWithOutErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            DDLog.d(TAG, "继电器执行成功");
        } else {
            DDLog.d(TAG, "继电器执行失败");
            fail();
        }

        isCanClickBtn = true;
        changeBtnStatus();
    }
}
