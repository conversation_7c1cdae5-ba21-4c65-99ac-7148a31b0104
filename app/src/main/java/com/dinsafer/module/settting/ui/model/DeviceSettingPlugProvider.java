package com.dinsafer.module.settting.ui.model;

import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.IPCKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.BuildConfig;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.timeline.MotionRecordTimelinePlayerActivity;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.battery.BmtCmdTestFragment;
import com.dinsafer.module.doorbell.play.DoorbellListFragment;
import com.dinsafer.module.main.view.DeviceStatusDetailFragment;
import com.dinsafer.module.settting.ui.AdvancedSettingFragment;
import com.dinsafer.module.settting.ui.ArmRulesFragment;
import com.dinsafer.module.settting.ui.BmtListFragment;
import com.dinsafer.module.settting.ui.ChangeMessageFragment;
import com.dinsafer.module.settting.ui.DeletePanelFragment;
import com.dinsafer.module.settting.ui.DeviceSettingFragment;
import com.dinsafer.module.settting.ui.DoorSensorListFragment;
import com.dinsafer.module.settting.ui.HeartBitPlugsListFragment;
import com.dinsafer.module.settting.ui.IPCListNewFragment;
import com.dinsafer.module.settting.ui.IPCSosRecordListFragment;
import com.dinsafer.module.settting.ui.OtherPluginListFragment;
import com.dinsafer.module.settting.ui.PanelSettingFragment;
import com.dinsafer.module.settting.ui.SafeSettingFragment;
import com.dinsafer.module.settting.ui.SecurityPlugsListFragment;
import com.dinsafer.module.settting.ui.SimplePlugsListFragment;
import com.dinsafer.module.settting.ui.SmartPlugsListFragment;
import com.dinsafer.module.settting.ui.SwitchBotListFragment;
import com.dinsafer.module.settting.ui.ThirdPartServiceFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.SettingInfoHelper;

import java.util.ArrayList;
import java.util.List;

public class DeviceSettingPlugProvider {

    /**
     * 家庭管理
     */
    public static List<BindModel> getFamilyManageItems(BaseFragment baseFragment, boolean haveLoading, boolean isLoading) {
        ArrayList<BindModel> data = new ArrayList();
        if (SettingInfoHelper.getInstance().showFamilyMemberSetting()) {
            data.add(new DeviceSettingDeviderModel(baseFragment, false));
            data.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.family)));

            data.add(new DeviceSettingDeviderModel(baseFragment, false));
            data.add(new DeviceSettingMemberManageModel(baseFragment, haveLoading, isLoading));

            if (SettingInfoHelper.getInstance().isAdmin()) {
                data.add(new DeviceSettingDeviderModel(baseFragment, true));
                data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.device_managent_sos_message),
                        -1, 0, false, false) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.showTimeOutLoadinFramgmentWithErrorAlert();
                        DinSDK.getHomeInstance().getHomeNotificationLanguage(HomeManager.getInstance().getCurrentHome().getHomeID(),
                                new IDefaultCallBack2<String>() {
                                    @Override
                                    public void onSuccess(String s) {
                                        baseFragment.getDelegateActivity().addCommonFragment(
                                                ChangeMessageFragment.newInstance(true,
                                                        TextUtils.isEmpty(s) ? "" : s));
                                    }

                                    @Override
                                    public void onError(int i, String s) {
                                        DDLog.e(TAG, "Error, i: " + i + ", s: " + s);
                                        baseFragment.closeTimeOutLoadinFramgmentWithErrorAlert();
                                    }
                                });
                    }
                });
            }
        }
        return data;
    }

    /**
     * 电池管理
     */
    public static List<BindModel> getEnergyItems(BaseFragment baseFragment, boolean haveLoading, boolean isLoading) {
        ArrayList<BindModel> data = new ArrayList();
        if ((AppConfig.Plugins.SUPPORT_BMT_HP5000 || AppConfig.Plugins.SUPPORT_BMT_HP5001
                || AppConfig.Plugins.SUPPORT_BMT_POWERCORE20 || AppConfig.Plugins.SUPPORT_BMT_POWERSTORE
                || AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE || AppConfig.Plugins.SUPPORT_BMT_POWERCORE30)
                && SettingInfoHelper.getInstance().isAdminOrUser()) {
            if (BmtManager.getInstance().getNotDeletedBmtDeviceList().size() > 0) {
                data.add(new DeviceSettingDeviderModel(baseFragment, false));
                data.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.energy)));
                data.add(new DeviceSettingDeviderModel(baseFragment, false));
            }

            int powerCore1Point0Count = BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_HP5000);
            int powerCore2Point0Count = BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERCORE20);
            int powerCore3Point0Count = BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERCORE30);
            int powerStoreCount = BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERSTORE);
            int powerPulseCount = BmtManager.getInstance().getNotDeleteDevicesSizeBySubCategory(DinConst.TYPE_BMT_POWERPULSE);

            if (powerCore1Point0Count > 0) {
                data.add(new BaseDeviceSettingPlugModel(baseFragment,
                        baseFragment.getResources().getString(R.string.power_core_1_point_0),
                        R.drawable.icon_device_setting_power, powerCore1Point0Count,
                        haveLoading, isLoading) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(BmtListFragment.newInstance(DinConst.TYPE_BMT_HP5000));
                    }
                });
                boolean enableLeftMargin = powerCore2Point0Count > 0 || powerCore3Point0Count > 0
                        || powerStoreCount > 0 || powerPulseCount > 0 || BuildConfig.DEBUG;
                data.add(new DeviceSettingDeviderModel(baseFragment, enableLeftMargin));
            }

            if (powerCore2Point0Count > 0) {
                data.add(new BaseDeviceSettingPlugModel(baseFragment,
                        baseFragment.getResources().getString(R.string.power_core_2_point_0),
                        R.drawable.icon_device_setting_power, powerCore2Point0Count,
                        haveLoading, isLoading) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(BmtListFragment.newInstance(DinConst.TYPE_BMT_POWERCORE20));
                    }
                });
                boolean enableLeftMargin = powerCore3Point0Count > 0
                        || powerStoreCount > 0 || powerPulseCount > 0 || BuildConfig.DEBUG;
                data.add(new DeviceSettingDeviderModel(baseFragment, enableLeftMargin));
            }

            if (powerCore3Point0Count > 0) {
                data.add(new BaseDeviceSettingPlugModel(baseFragment,
                        baseFragment.getResources().getString(R.string.power_core_3_point_0),
                        R.drawable.icon_device_setting_power, powerCore3Point0Count,
                        haveLoading, isLoading) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(BmtListFragment.newInstance(DinConst.TYPE_BMT_POWERCORE30));
                    }
                });
                boolean enableLeftMargin = powerStoreCount > 0 || powerPulseCount > 0 || BuildConfig.DEBUG;
                data.add(new DeviceSettingDeviderModel(baseFragment, enableLeftMargin));
            }


            if (powerStoreCount > 0) {
                data.add(new BaseDeviceSettingPlugModel(baseFragment,
                        baseFragment.getResources().getString(R.string.power_store),
                        R.drawable.icon_device_setting_power, powerStoreCount,
                        haveLoading, isLoading) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(BmtListFragment.newInstance(DinConst.TYPE_BMT_POWERSTORE));
                    }
                });
                data.add(new DeviceSettingDeviderModel(baseFragment, powerPulseCount > 0 || BuildConfig.DEBUG));
            }



            if (powerPulseCount > 0) {
                data.add(new BaseDeviceSettingPlugModel(baseFragment,
                        baseFragment.getResources().getString(R.string.power_pulse),
                        R.drawable.icon_device_setting_power, powerPulseCount,
                        haveLoading, isLoading) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(BmtListFragment.newInstance(DinConst.TYPE_BMT_POWERPULSE));
                    }
                });
                data.add(new DeviceSettingDeviderModel(baseFragment, BuildConfig.DEBUG));
            }


            if (BmtManager.getInstance().getNotDeletedBmtDeviceList().size() > 0) {
                if (BuildConfig.DEBUG) {
                    data.add(new BaseDeviceSettingPlugModel(baseFragment,
                            "BMT CMD TEST",
                            R.drawable.icon_device_setting_power, 0,
                            haveLoading, isLoading) {
                        @Override
                        public void onDo(View v) {
                            baseFragment.getDelegateActivity().addCommonFragment(BmtCmdTestFragment.newInstance());
                        }
                    });
                }
                data.add(new DeviceSettingDeviderModel(baseFragment, false));
            }
        }
        return data;
    }

    /**
     * IPC管理
     */
    public static List<BindModel> getIpcItems(BaseFragment baseFragment, boolean haveLoading, boolean isLoading) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList<BindModel> data = new ArrayList<>();

        // IPC
        if (AppConfig.Plugins.SUPPORT_IPC
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && IPCManager.getInstance().getAllNotDeleteDevicesSize() > 0) {
            data.add(new DeviceSettingIPCPlugModel(baseFragment, IPCManager.getInstance().getAllNotDeleteDevicesSize(), false, false));
            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        //自研门铃
        if (AppConfig.Plugins.SUPPORT_DS_DOORBELL
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && IPCManager.getInstance().getAllDoorbellDevice().size() > 0) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.video_doorbell), R.drawable.icon_device_setting_video_doorbell,
                    IPCManager.getInstance().getAllDoorbellDevice().size(), false, false) {
                @Override
                public void onDo(View view) {
                    super.onDo(view);
                    if (checkMainPassword()) {
                        baseFragment.getDelegateActivity().addCommonFragment(DoorbellListFragment.newInstance());
                    }
                }
            });
            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // Video Timeline：自研IPC及可视门铃录像入口，App功能列表中，勾选「自研IPC-云存储」才显示此入口（Family Settings页同）
        if ((AppConfig.Functions.SUPPORT_CLOUD_SERVICE_DSCAM_V005
                || AppConfig.Functions.SUPPORT_CLOUD_SERVICE_DSCAM_V006
                || AppConfig.Functions.SUPPORT_CLOUD_SERVICE_DS_DOORBELL)
                && SettingInfoHelper.getInstance().isAdminOrUser()) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.video_time_line),
                    -1, 0, false, false) {
                @Override
                public void onDo(View v) {
                    MotionRecordTimelinePlayerActivity.start(baseFragment.getDelegateActivity());
                    baseFragment.getMainActivity().setNotNeedToLogin(true);
                }
            });
            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // Video List：第三方IPC录像入口，App功能列表中，勾选「心籁-云存储」才显示此入口（Family Settings页同）
        if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE_HEARTLAI
                && SettingInfoHelper.getInstance().isAdminOrUser()) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.video_list),
                    -1, 0, false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(IPCSosRecordListFragment.newInstance());
                }
            });
            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (data.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.video_monitoring)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            data.remove(data.size() - 1);
            groupItems.addAll(data);
        }
        return groupItems;
    }

    /**
     * 获取需要显示的配件Item
     */
    public static List<BindModel> getAccessoryItems(BaseFragment baseFragment, boolean haveLoading, boolean isLoading) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList<BindModel> list = new ArrayList<>();

        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final Device device = DinHome.getInstance().getDevice(panelId);
        final boolean haveDevice = CommonDataUtil.getInstance().isHadPanelNotDeleted();
        boolean upgrading = DeviceHelper.getBoolean(device, PanelDataKey.Panel.UPGRADING, false);
        int count;

        // 主机
        if (SettingInfoHelper.getInstance().isAdmin() && haveDevice) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.panel),
                    R.drawable.icon_device_setting_panel, 0,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(PanelSettingFragment.newInstance());
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // SmartButton
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.SMART_BUTTON_COUNT, 0);
        if (APIKey.IS_SHOW_SMART_BUTTON
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_smart_buttom),
                    R.drawable.icon_device_setting_smartbtn, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.SMART_BUTTON
                            , baseFragment.getResources().getString(R.string.smart_button_name)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 信号中继插座
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.SIGNAL_REPEATER_PLUG_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_signal_repeater_plug),
                    R.drawable.icon_device_setting_repeater, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    if (checkMainPassword()) {
                        baseFragment.getDelegateActivity().addCommonFragment(
                                SmartPlugsListFragment.newInstance(PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG));
                    }
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 智能插座
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.SMART_PLUG_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_smart_plug),
                    R.drawable.icon_device_setting_switch, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    if (checkMainPassword()) {
                        baseFragment.getDelegateActivity().addCommonFragment(
                                SmartPlugsListFragment.newInstance(PanelConstant.DeviceType.SMART_PLUG));
                    }
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 无线警笛
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.SIREN_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_wireless),
                    R.drawable.icon_device_setting_siren, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.WIRELESS
                            , baseFragment.getResources().getString(R.string.device_managent_wireless)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 遥控
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.REMOTE_CONTROL_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_remote_control),
                    R.drawable.icon_device_setting_remote_control, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.RC_KEY
                            , baseFragment.getResources().getString(R.string.device_managent_remote_control)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 键盘
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.KEYPAD_ACCESSORY_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_keypad),
                    R.drawable.icon_device_setting_keypad, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.KEYPAD_KEY
                            , baseFragment.getResources().getString(R.string.device_managent_keypad)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 门窗探测器
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.DOOR_SENSOR_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_management_door_sensor),
                    R.drawable.icon_device_setting_door_sensor, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(DoorSensorListFragment.newInstance());
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 安防配件
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.SECURITY_ACCESSORY_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_security_accessories),
                    R.drawable.icon_device_setting_security, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SecurityPlugsListFragment.newInstance());
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // 继电器
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.RELAY_ACCESSORY_COUNT, 0);
        if (APIKey.IS_SHOW_RELAY
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.relay_name),
                    R.drawable.icon_device_setting_relay, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.RELAY
                            , baseFragment.getResources().getString(R.string.relay_name)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // DoorBell
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.DOOR_BELL_COUNT, 0);
        if (SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_doorbell),
                    R.drawable.icon_device_setting_visual_doorbell, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SimplePlugsListFragment.newInstance(IPCKey.DOOR_BELL
                            , baseFragment.getResources().getString(R.string.device_managent_doorbell)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // TODO HeartBit
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.DOOR_BELL_COUNT, 0);
        if (APIKey.IS_OPEN_HEART_BIT
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_heart_bit),
                    R.drawable.icon_device_setting_keypad, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(HeartBitPlugsListFragment.newInstance());
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // Other Plug
        count = DeviceHelper.getInt(device, PanelDataKey.PanelPluginQuantity.THIRD_PARTY_ACCESSORY_COUNT, 0);
        if (APIKey.IS_OPEN_OTHER_PLUGIN
                && SettingInfoHelper.getInstance().isAdminOrUser()
                && !upgrading
                && count > 0) {
            list.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_other_plugin),
                    R.drawable.icon_device_setting_third_party, count,
                    haveLoading, isLoading) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(
                            OtherPluginListFragment.newInstance(baseFragment.getResources().getString(R.string.device_managent_other_plugin)));
                }
            });
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (list.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(),
                    baseFragment.getResources().getString(R.string.alarm_system)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            list.remove(list.size() - 1);
            groupItems.addAll(list);
        }

        return groupItems;
    }

    public static List<BindModel> getMoreSupport(BaseFragment baseFragment) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList<BindModel> list = new ArrayList<>();

        //TODO Ble Switch Bot
        if (APIKey.IS_SHOW_SWITCH_BOT) {
            try {
                list.add(new BaseDeviceSettingPlugModel(baseFragment,
                        baseFragment.getResources().getString(R.string.switch_bot),
                        R.drawable.icon_device_setting_switchbot, 0, false, false) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(SwitchBotListFragment.newInstance());
                    }

                });
                list.add(new DeviceSettingDeviderModel(baseFragment, true));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //TODO PhilipsHue
        if (APIKey.IS_SHOW_SWITCH_HUE) {
            list.add(new DeviceSettingHueModel(baseFragment, false, false));
            list.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        list.add(new BaseDeviceSettingPlugModel(baseFragment,
                baseFragment.getResources().getString(R.string.device_managent_third_part_service),
                R.drawable.icon_device_setting_more_service, 0, false, false) {
            @Override
            public void onDo(View v) {
                baseFragment.getDelegateActivity().addCommonFragment(ThirdPartServiceFragment.newInstance());
            }

        });
        list.add(new DeviceSettingDeviderModel(baseFragment, true));

        if (list.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.device_managent_more_support)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            list.remove(list.size() - 1);
            groupItems.addAll(list);
        }

        return groupItems;
    }

    public static class DeviceSettingIPCPlugModel extends BaseDeviceSettingPlugModel {

        public DeviceSettingIPCPlugModel(BaseFragment baseFragment, int count, boolean haveLoading, boolean isLoading) {
            super(baseFragment, baseFragment.getResources().getString(R.string.smart_camera), R.drawable.icon_device_setting_ipc, count, haveLoading, isLoading);
        }

        @Override
        public void onDo(View v) {
            super.onDo(v);
            if (checkMainPassword()) {
                baseFragment.getDelegateActivity().addCommonFragment(IPCListNewFragment.newInstance());
            }
        }

    }

    private static boolean checkMainPassword() {
        return true;
    }

    /**
     * 自定义规则
     * 管理员可见
     */
    public static List<BindModel> getCustomRuleItem(final BaseFragment baseFragment, boolean haveLoading, boolean isLoading) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList data = new ArrayList();

        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final Device device = DinHome.getInstance().getDevice(panelId);
        final boolean haveDevice = CommonDataUtil.getInstance().isHadPanelNotDeleted();
        boolean upgrading = DeviceHelper.getBoolean(device, PanelDataKey.Panel.UPGRADING, false);
        boolean panelOnline = CommonDataUtil.getInstance().isPanelOnline();
        panelOnline = panelOnline && !upgrading;

        // Arm Rules
        if (SettingInfoHelper.getInstance().isAdmin() && panelOnline) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_managent_arm_rules), -1,
                    -1, false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(ArmRulesFragment.newInstance());
                }
            });
            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        // Security Notification Settings
        if (panelOnline && SettingInfoHelper.getInstance().isAdmin()) {
            data.add(new BaseDeviceSettingPlugModel(baseFragment,
                    baseFragment.getResources().getString(R.string.device_management_safe_label), -1,
                    -1, false, false) {
                @Override
                public void onDo(View v) {
                    baseFragment.getDelegateActivity().addCommonFragment(SafeSettingFragment.newInstance());
                }
            });
            data.add(new DeviceSettingDeviderModel(baseFragment, true));
        }

        if (data.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.device_management_home_label)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            data.remove(data.size() - 1);
            groupItems.addAll(data);
        }

        return groupItems;
    }

    /**
     * 主机设置
     */
    public static List<BindModel> getPanelSettingItem(final DeviceSettingFragment baseFragment, boolean haveLoading, boolean isLoading) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        ArrayList data = new ArrayList();

        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final Device device = DinHome.getInstance().getDevice(panelId);
        final boolean haveDevice = CommonDataUtil.getInstance().isHadPanelNotDeleted();
        boolean panelOnline = CommonDataUtil.getInstance().isPanelOnline();
        boolean upgrading = DeviceHelper.getBoolean(device, PanelDataKey.Panel.UPGRADING, false);
        panelOnline = panelOnline && !upgrading;

        if (SettingInfoHelper.getInstance().isAdmin()
                && haveDevice) {
            if (panelOnline) {
                // 主机在线
                // 主机状态
                data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.device_managent_panel_status), -1,
                        -1, false, false) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(DeviceStatusDetailFragment.newInstance());
                    }
                });
                data.add(new DeviceSettingDeviderModel(baseFragment, false));

                // 高级设置
                data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources().getString(R.string.advanced_setting), -1,
                        -1, false, false) {
                    @Override
                    public void onDo(View v) {
                        baseFragment.getDelegateActivity().addCommonFragment(AdvancedSettingFragment.newInstance());
                    }
                });
                data.add(new DeviceSettingDeviderModel(baseFragment, false));
            } else {
                // 主机离线
                // 主机离线配网
                if (!upgrading) {
                    data.add(new DeviceSettingConfigNetworkModel(baseFragment));
                    data.add(new DeviceSettingDeviderModel(baseFragment, false));

                    // 删除离线主机
                    data.add(new BaseDeviceSettingPlugModel(baseFragment, baseFragment.getResources()
                            .getString(R.string.delete_the_panel), -1,
                            -1, false, false, true) {
                        @Override
                        public void onDo(View v) {
                            baseFragment.getDelegateActivity().addCommonFragment(DeletePanelFragment.newInstance());
                        }
                    });
                    data.add(new DeviceSettingDeviderModel(baseFragment, false));
                }
            }
        }

        if (data.size() > 0) {
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(), baseFragment.getResources().getString(R.string.panel)));
            groupItems.add(new DeviceSettingDeviderModel(baseFragment, false));
            data.remove(data.size() - 1);
            groupItems.addAll(data);
        }

        return groupItems;
    }

    /**
     * 底部Padding设置
     */
    public static List<BindModel> getBottomPaddingItem(final DeviceSettingFragment baseFragment) {
        ArrayList<BindModel> groupItems = new ArrayList<>();
        groupItems.add(new DeviceSettingTitleModel(baseFragment.getContext(), ""));
        return groupItems;
    }
}
