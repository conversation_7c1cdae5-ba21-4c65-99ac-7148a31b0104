package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.animation.BounceInterpolator;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SecurityPlugsListLayoutBinding;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.PlugsData;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.model.SmartButtonActionChangeEvent;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.module.settting.adapter.MyFamilyListAdapter;
import com.dinsafer.module.settting.adapter.SecurityPlugsItem;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.NavigatorUtil;
import com.dinsafer.util.SettingInfoHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.dinsafer.util.SmartButtonUtil.SERVICE_ACTION_SINGLE_PRESS;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_MUSIC;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_VOLUME;

/**
 * Created by Rinfon on 16/7/12.
 */
public class SecurityPlugsListFragment extends BaseTimeoutPluginFragment<SecurityPlugsListLayoutBinding>
        implements ModifyDoorSensorAndPirPlugsFragment.ICallBack {

    private ArrayList<PlugsData> mData;
    private SecurityPlugsItem plugsItem;

    private int messageIndex;

    private static final String OLD_PIR_SENSOR_09 = "09"; // 红外
    private static final String OLD_DOOR_WINDOW_SENSOR_0B = "0B"; // 普通门磁
    private static final String OLD_VIBRATION_SENSOR_06 = "06"; // 震动门磁
    private static final String OLD_LIQUID_SENSOR_0E = "0E"; // 水感
    private static final String OLD_PANIC_BUTTON_07 = "07"; // 紧急按钮
    private static final String OLD_SMOKE_SENSOR_05 = "05"; // 烟感
    private static final String WIRED_BRIDGE = PluginConstants.TYPE_3F;
    private static final String ADJUSTABLE_MOTION_SENSOR = PluginConstants.TYPE_4A;

    private ArrayList<Device> mPluginDevices;
    boolean selfOperate;

    public static SecurityPlugsListFragment newInstance() {
        SecurityPlugsListFragment simplePlugsListFragment = new SecurityPlugsListFragment();
        return simplePlugsListFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.security_plugs_list_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        showBlueTimeOutLoadinFramgment();
        findPanel();
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.listviewEmpty.setLocalText(getResources().getString(R.string.listview_empty));
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_security_accessories));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> toClose());

        if (SettingInfoHelper.getInstance().isRFPluginItemClickable()
                && CommonDataUtil.getInstance().isPanelOnline()) {
            SwipeMenuCreator creator = new SwipeMenuCreator() {

                @Override
                public void create(SwipeMenu menu) {
                    if (menu.getViewType() == MyFamilyListAdapter.TYPE_ITEM) {
                        // create "delete" item
                        SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                        // set item background
                        deleteItem.setBackground(R.color.colorDelete);
                        // set item width
                        deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                        deleteItem.setTitleSize(13);

                        deleteItem.setTitleColor(Color.WHITE);
                        // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                        deleteItem.setTitle(Local.s(getResources().getString(R.string.smart_plugs_list_delete)));
                        // add to menu
                        menu.addMenuItem(deleteItem);
                    }
                }
            };

//        set creator
            mBinding.securityListview.setMenuCreator(creator);
            mBinding.securityListview.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
            mBinding.securityListview.setCloseInterpolator(new BounceInterpolator());
            mBinding.securityListview.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
                @Override
                public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                    switch (i1) {
                        case 0:
                            // delete
                            toDeleteItem(i);

                            break;
                    }
                    // false : close the menu; true : not close the menu
                    return false;
                }
            });
        }
        mBinding.securityListview.setEmptyView(mBinding.listviewEmpty);
        mBinding.securityListview.setOnItemClickListener((parent, view, position, id) -> toChangePlugName(position));
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }
        createPlugsList();
    }

    private void toDeleteItem(final int i) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        final Device device = DinHome.getInstance().getDevice(mData.get(i).getPlugId());
                        if (null != device) {
                            DDLog.i(TAG, "Delete plugin.");
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            selfOperate = true;
                            messageIndex = i;
                            device.submit(PanelParamsHelper.deletePlugin());
                        } else {
                            DDLog.e(TAG, "No plugin Device.");
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    /**
     * 初始化plugg数据列表
     */
    private void createPlugsList() {
        mData = new ArrayList<>();
        plugsItem = new SecurityPlugsItem(getActivity(), mData);
        mBinding.securityListview.setAdapter(plugsItem);
        mPluginDevices = new ArrayList<>();

        startTimeoutCountDown();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> devices = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.SECURITY_ACCESSORY);
                if (null != devices && 0 < devices.size()) {
                    for (Device device : devices) {
                        device.registerDeviceCallBack(SecurityPlugsListFragment.this);
                    }
                    mPluginDevices.addAll(devices);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createPlugsList-getdata");
            LinkedHashMap<String, ArrayList<PlugsData>> dataMap = new LinkedHashMap<>();
            dataMap.put(ADJUSTABLE_MOTION_SENSOR, new ArrayList<>());
            dataMap.put(OLD_PIR_SENSOR_09, new ArrayList<>());
            dataMap.put(OLD_DOOR_WINDOW_SENSOR_0B, new ArrayList<>());
            dataMap.put(OLD_VIBRATION_SENSOR_06, new ArrayList<>());
            dataMap.put(OLD_LIQUID_SENSOR_0E, new ArrayList<>());
            dataMap.put(OLD_PANIC_BUTTON_07, new ArrayList<>());
            dataMap.put(OLD_SMOKE_SENSOR_05, new ArrayList<>());
            dataMap.put(WIRED_BRIDGE, new ArrayList<>());
            if (null != mPanelDevice && 0 < mPluginDevices.size()) {
                PlugsData plug;
                String subcategory;
                ArrayList<PlugsData> plugsData;
                for (Device device : mPluginDevices) {
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    subcategory = DeviceHelper.getString(device, PanelDataKey.SUBCATEGORY, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }

                    plug = new PlugsData();
                    plug.setName(subcategory)
                            .setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""))
                            .setDescription(pluginName)
                            .setPlugId(device.getId());
                    if (null != DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA)) {
                        plug.setHasSignalLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_SIGNAL_LEVEL, false))
                                .setCanTamper(DeviceHelper.getBoolean(device, PanelDataKey.CAN_TAMPER, false))
                                .setCanCharging(DeviceHelper.getBoolean(device, PanelDataKey.CAN_CHARGING, false))
                                .setHasBatteryLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_BATTERY_LEVEL, false))
                                .setHasWebsocketLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_WEB_SOCKET_LOADING, false))
                                .setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                    }

                    plugsData = dataMap.get(subcategory);
                    if (null != plugsData) {
                        plugsData.add(plug);
                    }
                }
            }

            for (String key : dataMap.keySet()) {
                if (dataMap.get(key).size() > 0) {
                    String name = DisplayUtil.getNameBySubCategoryID(dataMap.get(key).get(0).getName());

                    PlugsData plugsData = new PlugsData();
                    plugsData.setName("")
                            .setDescription(name);
                    mData.add(plugsData);
                    mData.addAll(dataMap.get(key));
                }
            }
            if (mData.size() <= 0) {
                mBinding.listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                mBinding.listviewEmpty.setVisibility(View.GONE);
            }
            plugsItem.setData(mData);
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (null != mPluginDevices && 0 < mPluginDevices.size()) {
            for (Device device : mPluginDevices) {
                device.unregisterDeviceCallBack(this);
            }
        }
        DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.SECURITY_ACCESSORY);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOfflineEvent event) {
        DDLog.d(TAG, "DeviceOfflineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOnlineEvent event) {
        DDLog.d(TAG, "DeviceOnlineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PlugsNameChangeEvent ev) {
        plugsItem.changeName(messageIndex, ev.getName());
    }

    public void toChangePlugName(int index) {
        if (!SettingInfoHelper.getInstance().isRFPluginItemClickable()) {
            DDLog.e(TAG, "当前权限下Item不能被点击哦$_$");
            return;
        }

        if (!CommonDataUtil.getInstance().isPanelOnline()) {
            DDLog.e(TAG, "当前主机离线，Item不能被点击哦");
            return;
        }

        messageIndex = index;
        if (!TextUtils.isEmpty(mData.get(index).getName())) {
            if (mData.get(index).getAskData() != null) {
                messageIndex = index;
                Builder builder = new Builder();
                boolean isOffline = false;
                boolean isLowPower = false;

                if ((DDJSONUtil.has(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_KEEP_LIVE)
                        && !DDJSONUtil.getBoolean(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_KEEP_LIVE)
                        && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE
                        && !DDJSONUtil.getString(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_S_TYPE).equals("1C"))
                        || mData.get(index).isLoadStatusError()) {
                    isOffline = true;
                } else if (DDJSONUtil.has(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_POWER)
                        && !DDJSONUtil.getBoolean(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_POWER)) {
                    isLowPower = true;
                }
                // 如果是有电量值的配件，优先以电量值的电量转态为标准
                if (mData.get(index).isHasBatteryLevel()) {
                    isLowPower = mData.get(index).isBatteryLevelLowBattery();
                }
                builder.setId(DDJSONUtil.getString(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_ID))
                        .setAdd(false)
                        .setOffical(true)
                        .setOffline(isOffline)
                        .setLowPower(isLowPower)
                        .setMessageIndex(messageIndex)
                        .setShowDelete(true)
                        .setName(mData.get(index).getDescription())
                        .setShowwave(false)
                        .setSensitivity(mData.get(index).getSensitivity())
                        .setData(mData.get(index).getAskData());
                ModifyDoorSensorAndPirPlugsFragment modifyASKPlugsFragment =
                        ModifyDoorSensorAndPirPlugsFragment.newInstance(builder);
                modifyASKPlugsFragment.setCallBack(this);
                getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);
            } else {

                if (!TextUtils.isEmpty(mData.get(index).getDecodeid())) {
                    String oldPluginId = DDSecretUtil.hexStrToStr64(mData.get(index).getDecodeid());
                    NavigatorUtil.getInstance().toModifyPlugsNameFragment(mData.get(index).getDescription(),
                            mData.get(index).getPlugId(), oldPluginId, false, true);
                } else {
                    NavigatorUtil.getInstance().toModifyPlugsNameFragment(mData.get(index).getDescription(), mData.get(index).getPlugId(), false, true);
                }


            }
        }
    }

    //    心跳配件改名字
    @Override
    public void onChangeName(int index, String name) {
        plugsItem.changeName(messageIndex, name);
    }

    @Override
    public void onBlockModeChange(String pluginID, int mode) {
        DDLog.i(TAG, "onBlockModeChange:" + mode);
        for (int i = 0; i < mData.size(); i++) {
            if (pluginID.equals(mData.get(i).getPlugId())) {
                JSONObject askData = mData.get(i).getAskData();
                try {
                    askData.put(NetKeyConstants.NET_KEY_BLOCK, mode);
                    mData.get(i).setAskData(askData);
                    return;
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void onPirSensitivityChange(String pluginID, int sensitivity) {
        DDLog.i(TAG, "onPirSensitivityChange: " + sensitivity);
        for (int i = 0; i < mData.size(); i++) {
            if (pluginID.equals(mData.get(i).getPlugId())) {
                mData.get(i).setSensitivity(sensitivity);
            }
        }
    }

    @Override
    public void onDelete(String id) {
        Log.e(TAG, "onDeletePlug: " + id);
        if (mData != null && mData.size() > 0) {
            int index = -1;
            for (int i = 0; i < mData.size(); i++) {
                Log.w(TAG, "onDelete: " + mData.get(i).getPlugId());
                if (!TextUtils.isEmpty(mData.get(i).getPlugId()) && mData.get(i).getPlugId().equals(id)) {
                    index = i;
                    break;
                }
            }
            Log.e(TAG, "onDeletePlug: " + index);
            if (index >= 0 && index < mData.size()) {
                mData.remove(index);
                final int checkIndex = index - 1;
                if (checkIndex >= 0 && checkIndex < mData.size()) {
                    if (TextUtils.isEmpty(mData.get(checkIndex).getName())) {
                        // HEADER
                        mData.remove(checkIndex);
                    }
                }

                plugsItem.setData(mData);
                plugsItem.notifyDataSetChanged();

                if (!hasPlugin()) {
                    mBinding.listviewEmpty.setVisibility(View.VISIBLE);
                    mBinding.securityListview.setVisibility(View.GONE);
                } else {
                    mBinding.listviewEmpty.setVisibility(View.GONE);
                    mBinding.securityListview.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    private boolean hasPlugin() {
        if (mData == null || mData.size() == 0) {
            return false;
        }
        for (PlugsData mDatum : mData) {
            if (!TextUtils.isEmpty(mDatum.getPlugId())) {
                return true;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPluginDeleteEvent(PluginDeleteEvent pluginDeleteEvent) {
        try {
            onDelete(pluginDeleteEvent.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(SmartButtonActionChangeEvent ev) {
//        closeTimeOutLoadinFramgmentWithErrorAlert();
//        getDelegateActivity().removeToFragment(this.getClass().getName());
        for (PlugsData data : mData) {
            if (ev.getSmartButtonData().getTargetId().equals(data.getPlugId())) {
                JSONObject configData = DDJSONUtil.getJSONObject(data.getAskData(), SERVICE_ACTION_SINGLE_PRESS);
                try {
                    configData.put(SERVICE_KEY_MUSIC, ev.getNewAction().getActionData().getMusicIndex());
                    configData.put(SERVICE_KEY_VOLUME, ev.getNewAction().getActionData().getVolumeIndex());
                    plugsItem.setData(mData);
                    plugsItem.notifyDataSetChanged();
                    break;
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onPluginStatusUpdate() {
        DDLog.i(TAG, "onPluginStatusUpdate");
        plugsItem.setData(mData);
        plugsItem.notifyDataSetChanged();
    }

    @Override
    protected ArrayList<PlugsData> getCurrentPluginList() {
        return mData;
    }


    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || 1 != resultType) {
            return;
        }

        if (null != mPanelDevice
                && deviceId.equals(mPanelDevice.getId())) {
            onPanelCmdCallback(deviceId, cmd, map);
            return;
        }

        onPluginCmdCallback(deviceId, cmd, map);
    }

    private void onPanelCmdCallback(String deviceId, String cmd, Map map) {
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            return;
        }
        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
        if (PluginCmd.PLUGIN_STATE_CHANGE.equals(cmd)) {
            updatePluginBattery(result);
        }
    }

    /**
     * 配件CMD
     */
    private void onPluginCmdCallback(String deviceId, String cmd, Map map) {
        DDLog.i(TAG, "onPluginCmdCallback");
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            if (selfOperate) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
                selfOperate = false;
            }
            return;
        }

        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
        if (selfOperate && PluginCmd.PLUGIN_DELETE.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            plugsItem.remove(messageIndex);
            selfOperate = false;
            plugsItem.notifyDataSetChanged();
            return;
        }

        if (PluginCmd.PLUGIN_ONLINE_CHANGE.equals(cmd)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                String sendid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID);
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getAskData() != null) {
                        if (DDJSONUtil.getString(mData.get(i).getAskData(), NetKeyConstants.NET_KEY_SEND_ID).equals(sendid)) {
                            mData.get(i).getAskData().put(NetKeyConstants.NET_KEY_KEEP_LIVE,
                                    !LocalKey.PLUGIN_OFFLINE.equals(operateCmd));
                            break;
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            plugsItem.notifyDataSetChanged();
        } else if (PluginCmd.PLUGIN_POWER_CHANGE.equals(cmd)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                String pluginid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getAskData() != null) {
                        //  新配件才显示低电
                        if (mData.get(i).getPlugId().equals(pluginid)) {
                            mData.get(i).getAskData().put(NetKeyConstants.NET_KEY_POWER,
                                    !LocalKey.EVENT_LOWERPOWER.equals(operateCmd));
                            break;
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            plugsItem.notifyDataSetChanged();
            showSuccess();
        } else if (PluginCmd.PLUGIN_CONFIG_BLOCK.equals(cmd)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                String pluginid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ID);
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getAskData() != null) {
                        // 新配件才显示低电
                        if (mData.get(i).getPlugId().equals(pluginid)) {
                            mData.get(i).getAskData().put(NetKeyConstants.NET_KEY_BLOCK,
                                    DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_BLOCK));
                            break;
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            plugsItem.notifyDataSetChanged();
        }
    }
}
