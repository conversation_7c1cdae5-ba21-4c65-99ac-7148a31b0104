package com.dinsafer.module.settting.ui.model;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemDeviceSettingTitleBinding;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindModel;

public class DeviceSettingTitleModel extends BindModel<ItemDeviceSettingTitleBinding> {

    private String title;

    public DeviceSettingTitleModel(Context context,String title) {
        super(context);
        this.title = title;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_device_setting_title;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder,ItemDeviceSettingTitleBinding itemDeviceSettingTitleBinding) {
        itemDeviceSettingTitleBinding.deviceManagementLabel.setLocalText(getTitle());
    }

    public String getTitle() {
        return title;
    }
}
