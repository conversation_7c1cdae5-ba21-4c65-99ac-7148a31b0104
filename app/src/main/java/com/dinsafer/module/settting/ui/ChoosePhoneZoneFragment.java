package com.dinsafer.module.settting.ui;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ChoosePhoneZoneLayoutBinding;
import com.dinsafer.model.PhoneZoneCloseEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.AreaCodeItemModel;
import com.dinsafer.ui.IndexView;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DBUtil;

import org.greenrobot.eventbus.EventBus;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ChoosePhoneZoneFragment extends BaseFragment {

    private ChoosePhoneZoneLayoutBinding mBinding;

    private BindMultiAdapter<BaseBindModel> mAdapter;

    private ArrayList<BaseBindModel> mData;

    public static String[] countryNames = new String[]{"Afghanistan", "Albania", "Algeria", "American Samoa", "Andorra", "Angola", "Anguilla", "Antarctica", "Antigua and Barbuda", "Argentina", "Armenia", "Aruba", "Australia", "Austria", "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bermuda", "Bhutan", "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "British Virgin Islands", "Brunei", "Bulgaria", "Burkina Faso", "Burma (Myanmar)", "Burundi", "Cambodia", "Cameroon", "Canada", "Cape Verde", "Cayman Islands", "Central African Republic", "Chad", "Chile", "China", "Christmas Island", "Cocos (Keeling) Islands", "Colombia", "Comoros", "Cook Islands", "Costa Rica", "Croatia", "Cuba", "Cyprus", "Czech Republic", "Democratic Republic of the Congo", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "Ecuador", "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Ethiopia", "Falkland Islands", "Faroe Islands", "Fiji", "Finland", "France", "French Polynesia", "Gabon", "Gambia", "Gaza Strip", "Georgia", "Germany", "Ghana", "Gibraltar", "Greece", "Greenland", "Grenada", "Guam", "Guatemala", "Guinea", "Guinea-Bissau", "Guyana", "Haiti", "Holy See (Vatican City)", "Honduras", "Hong Kong", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland", "Isle of Man", "Israel", "Italy", "Ivory Coast", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg", "Macau", "Macedonia", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Mauritania", "Mauritius", "Mayotte", "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Montserrat", "Morocco", "Mozambique", "Namibia", "Nauru", "Nepal", "Netherlands", "Netherlands Antilles", "New Caledonia", "New Zealand", "Nicaragua", "Niger", "Nigeria", "Niue", "Norfolk Island", "North Korea", "Northern Mariana Islands", "Norway", "Oman", "Pakistan", "Palau", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Pitcairn Islands", "Poland", "Portugal", "Puerto Rico", "Qatar", "Republic of the Congo", "Romania", "Russia", "Rwanda", "Saint Barthelemy", "Saint Helena", "Saint Kitts and Nevis", "Saint Lucia", "Saint Martin", "Saint Pierre and Miquelon", "Saint Vincent and the Grenadines", "Samoa", "San Marino", "Sao Tome and Principe", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa", "South Korea", "Spain", "Sri Lanka", "Sudan", "Suriname", "Swaziland", "Sweden", "Switzerland", "Syria", "Taiwan", "Tajikistan", "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tokelau", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan", "Turks and Caicos Islands", "Tuvalu", "US Virgin Islands", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Venezuela", "Vietnam", "Wallis and Futuna", "West Bank", "Yemen", "Zambia", "Zimbabwe"};

    public static String[] countryCodes = new String[]{"+93", "+355", "+213", "+1684", "+376", "+244", "+1264", "+672", "+1268", "+54", "+374", "+297", "+61", "+43", "+994", "+1242", "+973", "+880", "+1246", "+375", "+32", "+501", "+229", "+1441", "+975", "+591", "+387", "+267", "+55", "+1284", "+673", "+359", "+226", "+95", "+257", "+855", "+237", "+1", "+238", "+1345", "+236", "+235", "+56", "+86", "+61", "+61", "+57", "+269", "+682", "+506", "+385", "+53", "+357", "+420", "+243", "+45", "+253", "+1767", "+1809", "+593", "+20", "+503", "+240", "+291", "+372", "+251", "+500", "+298", "+679", "+358", "+33", "+689", "+241", "+220", "+970", "+995", "+49", "+233", "+350", "+30", "+299", "+1473", "+1671", "+502", "+224", "+245", "+592", "+509", "+39", "+504", "+852", "+36", "+354", "+91", "+62", "+98", "+964", "+353", "+44", "+972", "+39", "+225", "+1876", "+81", "+962", "+7", "+254", "+686", "+381", "+965", "+996", "+856", "+371", "+961", "+266", "+231", "+218", "+423", "+370", "+352", "+853", "+389", "+261", "+265", "+60", "+960", "+223", "+356", "+692", "+222", "+230", "+262", "+52", "+691", "+373", "+377", "+976", "+382", "+1664", "+212", "+258", "+264", "+674", "+977", "+31", "+599", "+687", "+64", "+505", "+227", "+234", "+683", "+672", "+850", "+1670", "+47", "+968", "+92", "+680", "+507", "+675", "+595", "+51", "+63", "+870", "+48", "+351", "+1", "+974", "+242", "+40", "+7", "+250", "+590", "+290", "+1869", "+1758", "+1599", "+508", "+1784", "+685", "+378", "+239", "+966", "+221", "+381", "+248", "+232", "+65", "+421", "+386", "+677", "+252", "+27", "+82", "+34", "+94", "+249", "+597", "+268", "+46", "+41", "+963", "+886", "+992", "+255", "+66", "+670", "+228", "+690", "+676", "+1868", "+216", "+90", "+993", "+1649", "+688", "+1340", "+256", "+380", "+971", "+44", "+1", "+598", "+998", "+678", "+58", "+84", "+681", "+970", "+967", "+260", "+263"};

    public static String SPACE = " ";

    private IChoosePhoneZoneCallBack mCallBack;

    private static final String PHONEZONEINDEX = "phonezoneindex";

    private static final String NEEDTONOTIFY = "isNeedToNotifyClose";

    private static final String CACHE_PHONE_ZONE = "CACHE_PHONE_ZONE";

    private ArrayList<String> indexs;
    private LinkedHashMap<String, Integer> indexPosMap;

    private int lastScrollIndexPos = 0;


    public static ChoosePhoneZoneFragment newInstance(String phoneZoneIndex) {
        ChoosePhoneZoneFragment fragment = new ChoosePhoneZoneFragment();
//        不能用这种方式传递接口，否则会crash
        Bundle args = new Bundle();
        args.putString(PHONEZONEINDEX, phoneZoneIndex);
        fragment.setArguments(args);
        return fragment;
    }

    public static ChoosePhoneZoneFragment newInstance(String phoneZoneIndex, boolean isNeedToNotifyClose) {
        ChoosePhoneZoneFragment fragment = new ChoosePhoneZoneFragment();
//        不能用这种方式传递接口，否则会crash
        Bundle args = new Bundle();
        args.putString(PHONEZONEINDEX, phoneZoneIndex);
        args.putBoolean(NEEDTONOTIFY, isNeedToNotifyClose);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.choose_phone_zone_layout, container, false);
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void initData() {
        super.initData();
        String currentIndex = getArguments().getString(PHONEZONEINDEX);
        isNeedToNotifyClose = getArguments().getBoolean(NEEDTONOTIFY);

        mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.choose_phone_zone_title));
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> close());
        mData = new ArrayList<BaseBindModel>();
        indexs = new ArrayList<>();
        indexPosMap = new LinkedHashMap<>();
        AreaCodeItemModel areaCodeItemModel;

//        LinkedHashMap<String, String> codeMap = new LinkedHashMap<>();
//        for (int i = 0; i < countryNames.length; i++) {
//            codeMap.put(countryNames[i], countryCodes[i]);
//        }
//
//        List<Map.Entry<String, String>> infoIds =
//                new ArrayList<Map.Entry<String, String>>(codeMap.entrySet());
//        Collections.sort(infoIds, new Comparator<Map.Entry<String, String>>() {
//            public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
////                return (o2.getValue() - o1.getValue());
//                return (o1.getKey()).toString().compareTo(o2.getKey());
//            }
//        });
//
//        StringBuilder sbName = new StringBuilder();
//        StringBuilder sbCode = new StringBuilder();
//        for (int i = 0; i < infoIds.size(); i++) {
//            sbName.append("\"")
//                    .append(infoIds.get(i).getKey())
//                    .append("\",");
//
//            sbCode.append("\"")
//                    .append(infoIds.get(i).getValue())
//                    .append("\",");
//
//        }
//
//        Log.d(TAG, "initData-->name: " + sbName.toString());
//        Log.d(TAG, "initData-->code: " + sbCode.toString());

        for (int i = 0; i < countryCodes.length; i++) {
            areaCodeItemModel = new AreaCodeItemModel(countryCodes[i], countryNames[i], false);
            if (!TextUtils.isEmpty(currentIndex) && currentIndex.equals(countryCodes[i] + " " + countryNames[i])) {
                areaCodeItemModel.setChecked(true);
            } else {
                areaCodeItemModel.setChecked(false);
            }
            mData.add(areaCodeItemModel);
            String index = String.valueOf(countryNames[i].charAt(0));
            if (!indexs.contains(index)) {
                indexs.add(index);

                areaCodeItemModel.setGroupTittle(index);
                areaCodeItemModel.setShowGroupTittle(true);
            }
            if (!indexPosMap.containsKey(index)) {
                indexPosMap.put(index, i);
            }
        }

        mAdapter = new BindMultiAdapter<>();
        mBinding.rv.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rv.setHasFixedSize(true);
        mBinding.rv.setAdapter(mAdapter);
        mAdapter.openLoadAnimation();
        mAdapter.setOnBindItemClickListener((OnBindItemClickListener<BaseBindModel>) (v, position, model) -> {
            if (model instanceof AreaCodeItemModel) {
                toResult((AreaCodeItemModel) model);
            }
        });

        mAdapter.setNewData(mData);

        mBinding.indexView.setData(indexs);
        mBinding.indexView.setOnTouchIndexViewCallback(new IndexView.OnTouchIndexViewCallback() {
            @Override
            public void onTouchIndex(int pos, String text) {
                if (pos == lastScrollIndexPos) {
                    Log.w(TAG, "onTouchIndex: ");
                    return;
                }
//                mBinding.tvIndex.setVisibility(View.VISIBLE);
//                mBinding.tvIndex.setText(text);
                ((LinearLayoutManager) mBinding.rv.getLayoutManager()).scrollToPositionWithOffset(indexPosMap.get(text), 0);
                lastScrollIndexPos = pos;
            }

            @Override
            public void onCancelTouchIndex() {
//                mBinding.tvIndex.setVisibility(View.GONE);
            }
        });

    }

    public void toResult(AreaCodeItemModel model) {
        if (mCallBack != null) {
            cachePhoneZone(model.getCode() + " " + model.getName());
            mCallBack.onResult(model.getCode(), model.getName());
            isNeedToNotifyClose = false;
            close();
        }

    }

    public IChoosePhoneZoneCallBack getCallBack() {
        return mCallBack;
    }

    public void setCallBack(IChoosePhoneZoneCallBack mCallBack) {
        this.mCallBack = mCallBack;
    }


    boolean isNeedToNotifyClose = false;

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        this.mCallBack = null;
        if (isNeedToNotifyClose)
            EventBus.getDefault().post(new PhoneZoneCloseEvent(getArguments().getString(PHONEZONEINDEX)));
    }

    public static String getCachePhoneZone() {
        return DBUtil.Str(CACHE_PHONE_ZONE);
    }

    private void cachePhoneZone(String phoneZone) {
        if (TextUtils.isEmpty(phoneZone)) {
            return;
        }
        DBUtil.Put(CACHE_PHONE_ZONE, phoneZone);
    }

    public interface IChoosePhoneZoneCallBack extends Serializable {
        void onResult(String code, String name);
    }

}

