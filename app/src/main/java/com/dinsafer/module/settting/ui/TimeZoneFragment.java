package com.dinsafer.module.settting.ui;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.TimeZoneLayout2Binding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.TimeZoneItemModel;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.IndexView;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rinfon on 16/7/8.
 */
public class TimeZoneFragment extends BaseFragment
        implements IDeviceCallBack {
    private TimeZoneLayout2Binding mBinding;

    private BindMultiAdapter<BaseBindModel> mAdapter;
    private ArrayList<BaseBindModel> mData;
    boolean isCanClose = false;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    private ArrayList<String> indexs;
    private LinkedHashMap<String, Integer> indexPosMap;
    private int lastScrollIndexPos = 0;

    private TimeZoneItemModel currentSelectModel;

    public static TimeZoneFragment newInstance(boolean isCanClose) {
        TimeZoneFragment fragment = new TimeZoneFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean("isCanClose", isCanClose);
        fragment.setArguments(bundle);
        return fragment;
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.getTimezone());
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        showTimeOutLoadinFramgmentWithErrorAlert();
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), R.layout.time_zone_layout2, container, false);
        findPanel();
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.select_time_zone));
        isCanClose = getArguments().getBoolean("isCanClose");
        if (isCanClose) {
            mBinding.commonBarBack.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBarBack.setVisibility(View.GONE);
        }

        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.btnSave.setOnClickListener(v -> toSave());

        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.btnSave.setAlpha(0.5f);
        mBinding.btnSave.setEnabled(false);
        mBinding.btnSave.setVisibility(View.VISIBLE);

        mData = new ArrayList<BaseBindModel>();
        indexs = new ArrayList<>();
        indexPosMap = new LinkedHashMap<>();

        mAdapter = new BindMultiAdapter<>();
        mBinding.rv.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rv.setHasFixedSize(true);
        mBinding.rv.setAdapter(mAdapter);
        mAdapter.openLoadAnimation();
        mAdapter.setOnBindItemClickListener((OnBindItemClickListener<BaseBindModel>) (v, position, model) -> {
            if (model instanceof TimeZoneItemModel) {
                toResult((TimeZoneItemModel) model);
            }
        });

        mAdapter.setNewData(mData);

        mBinding.indexView.setData(indexs);
        mBinding.indexView.setOnTouchIndexViewCallback(new IndexView.OnTouchIndexViewCallback() {
            @Override
            public void onTouchIndex(int pos, String text) {
                if (pos == lastScrollIndexPos) {
                    return;
                }
//                mBinding.tvIndex.setVisibility(View.VISIBLE);
//                mBinding.tvIndex.setText(text);
                ((LinearLayoutManager) mBinding.rv.getLayoutManager()).scrollToPositionWithOffset(indexPosMap.get(text), 0);
                lastScrollIndexPos = pos;
            }

            @Override
            public void onCancelTouchIndex() {
//                mBinding.tvIndex.setVisibility(View.GONE);
            }
        });
    }

    public void toResult(TimeZoneItemModel model) {
        if (currentSelectModel != null) {
            currentSelectModel.setChecked(false);
        }
        model.setChecked(true);
        currentSelectModel = model;
        mAdapter.notifyDataSetChanged();
        mBinding.btnSave.setAlpha(1f);
        mBinding.btnSave.setEnabled(true);
    }

    @Override
    public boolean onBackPressed() {
        if (isCanClose) {
            return false;
        } else {
            return true;
        }
    }

    public void toSave() {
        if (currentSelectModel == null) {
            return;
        }
        showTimeOutLoadinFramgmentWithErrorAlert();
        String timezone = currentSelectModel.getCorrectTimezone();
        if (timezone.equals("America/Honolulu")) {
            timezone = "Pacific/Honolulu";
        }

        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.setTimezone(timezone));
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (PanelCmd.GET_TIMEZONE.equals(cmd)) {
            onGetTimezoneInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_TIMEZONE.equals(cmd)) {
                onSetTimezoneInfo(status, map);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置的时区信息
     */
    private void onGetTimezoneInfo(int status, Map map) {
        DDLog.i(TAG, "onGetTimezoneInfo, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            // 获取数据出错时，允许返回，否则在进入Setting页时会出现无法退出该页面的问题
            isCanClose = true;
            mBinding.commonBarBack.setVisibility(View.VISIBLE);
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        String currentTimezone = DeviceHelper.getString(resultMap, PanelDataKey.Timezone.TIMEZONE, "");
        if (TextUtils.isEmpty(currentTimezone)) {
            currentTimezone = APIKey.DEVICE_DEFAULT_TIMEZONE;
        }
        List<String> timezoneList = DeviceHelper.getList(resultMap, PanelDataKey.Timezone.TIMEZONE_LIST);

        mData.clear();
        if (currentTimezone.equals("Pacific/Honolulu")) {
            currentTimezone = "America/Honolulu";
        }
        for (int i = 0; i < timezoneList.size(); i++) {
            if (timezoneList.get(i).equals("Pacific/Honolulu")) {
                timezoneList.remove(i);
                timezoneList.add(i, "America/Honolulu");
            }
        }

        TimeZoneItemModel timeZoneItemModel;
        String timezone;
        for (int i = 0; i < timezoneList.size(); i++) {
            timezone = timezoneList.get(i);
            timeZoneItemModel = new TimeZoneItemModel(timezone, false);
            mData.add(timeZoneItemModel);
            if (!TextUtils.isEmpty(timezone) && timezone.equals(currentTimezone)) {
                timeZoneItemModel.setChecked(true);
                currentSelectModel = timeZoneItemModel;
            }
        }

        Collections.sort(mData, new Comparator<BaseBindModel>() {
            @Override
            public int compare(BaseBindModel o1, BaseBindModel o2) {
                return ((TimeZoneItemModel) o1).getDisplayTimezone().compareTo(((TimeZoneItemModel) o2).getDisplayTimezone());
            }
        });

        int targetPosition = -1;
        for (int i = 0; i < mData.size(); i++) {
            TimeZoneItemModel itemModel = (TimeZoneItemModel) mData.get(i);
            String index = String.valueOf((itemModel).getDisplayTimezone().charAt(0));
            if (!indexs.contains(index)) {
                indexs.add(index);

                itemModel.setGroupTittle(index);
                itemModel.setShowGroupTittle(true);
            }
            if (!indexPosMap.containsKey(index)) {
                indexPosMap.put(index, i);
            }
            if ((itemModel).isSelected()) {
                targetPosition = i;
            }
        }

        mBinding.indexView.setData(indexs);

        if (!TextUtils.isEmpty(currentTimezone)) {
            mBinding.btnSave.setAlpha(1f);
            mBinding.btnSave.setEnabled(true);
        } else {
            mBinding.btnSave.setAlpha(0.5f);
            mBinding.btnSave.setEnabled(false);
        }
        mAdapter.notifyDataSetChanged();

        if (targetPosition >= 0) {
            final int position = targetPosition;
            mBinding.rv.post(() -> {
                mBinding.rv.smoothScrollToPosition(position);
            });
        }
    }

    /**
     * 设置时区
     */
    private void onSetTimezoneInfo(int status, Map map) {
        DDLog.i(TAG, "onSetTimezoneInfo, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            if (null != mPanelDevice.getInfo()) {
                if (currentSelectModel != null) {
                    mPanelDevice.getInfo().put(PanelDataKey.Panel.TIMEZONE, currentSelectModel.getCorrectTimezone());
                }
            }
            showSuccess();
            removeSelf();
        } else {
            showErrorToast();
        }
    }
}

