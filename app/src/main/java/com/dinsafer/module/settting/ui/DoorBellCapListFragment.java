package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.view.View;
import android.view.animation.BounceInterpolator;
import android.widget.AbsListView;
import android.widget.AdapterView;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DoorBellCapLayoutBinding;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DoorBell;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.adapter.DoorBellAdapter;
import com.dinsafer.ui.PhotoViewFragment;
import com.dinsafer.ui.PullToRefreshLayout;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;

import java.util.ArrayList;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by Rinfon on 16/7/12.
 */
public class DoorBellCapListFragment extends MyBaseFragment<DoorBellCapLayoutBinding> {

    private ArrayList<DoorBell.ResultBean> mData;

    private DoorBellAdapter adapter;

    private Call<DoorBell> mCall;


    public static DoorBellCapListFragment newInstance() {
        DoorBellCapListFragment simplePlugsListFragment = new DoorBellCapListFragment();
        return simplePlugsListFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.door_bell_cap_layout;
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBar.commonBarBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toClose();
            }
        });
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_doorbell_cap));
        mBinding.listviewEmpty.setLocalText(getResources().getString(R.string.door_listview_empty));
        mBinding.doorBellPulllayout.setOnRefreshListener(refreshListen);
        mBinding.doorBellCapListview.setIsCanPullUp(false);
        mBinding.doorBellPulllayout.notShowLoadText();

        SwipeMenuCreator creator = new SwipeMenuCreator() {

            @Override
            public void create(SwipeMenu menu) {
                // create "delete" item
                SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                // set item background
                deleteItem.setBackground(R.color.colorDelete);
                // set item width
                deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                deleteItem.setTitleSize(13);

                deleteItem.setTitleColor(Color.WHITE);
                // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                deleteItem.setTitle(Local.s(getResources().getString(R.string.smart_plugs_list_delete)));
                // add to menu
                menu.addMenuItem(deleteItem);
            }
        };

//        set creator
        mBinding.doorBellCapListview.setMenuCreator(creator);
        mBinding.doorBellCapListview.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
        mBinding.doorBellCapListview.setCloseInterpolator(new BounceInterpolator());
        mBinding.doorBellCapListview.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                switch (i1) {
                    case 0:
                        // delete
                        toDeleteItem(i);

                        break;
                }
                // false : close the menu; true : not close the menu
                return false;
            }
        });
        mBinding.doorBellCapListview.setEmptyView(mBinding.listviewEmpty);


        mBinding.doorBellCapListview.setOnScrollListener(new AbsListView.OnScrollListener() {

            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (mBinding.doorBellCapListview != null && mBinding.doorBellCapListview.getLastVisiblePosition() != -1) {
                    if (mBinding.doorBellCapListview.getLastVisiblePosition() == mBinding.doorBellCapListview.getAdapter().getCount() - 1 &&
                            mBinding.doorBellCapListview.getChildAt(mBinding.doorBellCapListview.getChildCount() - 1).getBottom() <= mBinding.doorBellCapListview.getHeight()) {
                        mBinding.doorBellCapListview.setIsCanPullUp(true);
                    } else {
                        mBinding.doorBellCapListview.setIsCanPullUp(false);
                    }
                }
            }
        });
        mBinding.doorBellCapListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                toView(position);
            }
        });
        showBlueTimeOutLoadinFramgment();
    }

    private PullToRefreshLayout.OnRefreshListener refreshListen = new PullToRefreshLayout.OnRefreshListener() {
        @Override
        public void onRefresh(PullToRefreshLayout pullToRefreshLayout) {

        }

        @Override
        public void onLoadMore(PullToRefreshLayout pullToRefreshLayout) {
            toGetList(mData.get(mData.size() - 1).getRecordtime());
        }
    };


    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        createPlugsList();
    }

    private void toDeleteItem(final int i) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        DinsafeAPI.getApi().getDeleteDoorBellCapCall(mData.get(i).getId(),
                                DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getToken())
                                .enqueue(new Callback<StringResponseEntry>() {
                                    @Override
                                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                                        closeTimeOutLoadinFramgmentWithErrorAlert();
                                        mData.remove(i);
                                        adapter.setData(mData);
                                        adapter.notifyDataSetChanged();
                                        if (mData.size() <= 0) {
                                            mBinding.listviewEmpty.setVisibility(View.VISIBLE);
                                        }
                                    }

                                    @Override
                                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                                        closeTimeOutLoadinFramgmentWithErrorAlert();
                                        showErrorToast();
                                    }
                                });
                    }
                })
                .preBuilder()
                .show();
    }

    /**
     * 初始化plugg数据列表
     */
    private void createPlugsList() {
        mData = new ArrayList<DoorBell.ResultBean>();
        adapter = new DoorBellAdapter(getActivity(), mData);
        mBinding.doorBellCapListview.setAdapter(adapter);
        showTimeOutLoadinFramgment();
        toGetList();
    }

    private void toGetList() {
        mCall = DinsafeAPI.getApi().getDoorBellCapCall(CommonDataUtil.getInstance().getCurrentDeviceId(), System.currentTimeMillis() * 1000000);
        mCall.enqueue(new Callback<DoorBell>() {
            @Override
            public void onResponse(Call<DoorBell> call, Response<DoorBell> response) {
                DoorBell doorBell = response.body();
                if (doorBell.getResult().size() > 0) {
                    mData.addAll(doorBell.getResult());
                    adapter.setData(mData);
                    adapter.notifyDataSetChanged();
                    mBinding.doorBellPulllayout.showLoadText();
                    mBinding.listviewEmpty.setVisibility(View.GONE);
                }
                closeLoadingFragment();
            }

            @Override
            public void onFailure(Call<DoorBell> call, Throwable t) {
                closeLoadingFragment();
            }
        });
    }

    private void toGetList(long time) {
        mCall = DinsafeAPI.getApi().getDoorBellCapCall(CommonDataUtil.getInstance().getCurrentDeviceId(), time);
        mCall.enqueue(new Callback<DoorBell>() {
            @Override
            public void onResponse(Call<DoorBell> call, Response<DoorBell> response) {
                DoorBell doorBell = response.body();
                if (doorBell.getResult().size() > 0) {
                    mData.addAll(doorBell.getResult());
                    adapter.setData(mData);
                    adapter.notifyDataSetChanged();
                    mBinding.listviewEmpty.setVisibility(View.GONE);
                    mBinding.doorBellPulllayout.setDelayHide(false);
                    mBinding.doorBellPulllayout.loadmoreFinish(PullToRefreshLayout.SUCCEED);
                }
                closeLoadingFragment();
            }

            @Override
            public void onFailure(Call<DoorBell> call, Throwable t) {
                closeLoadingFragment();
                mBinding.doorBellPulllayout.setDelayHide(true);
                mBinding.doorBellPulllayout.loadmoreFinish(PullToRefreshLayout.FAIL);
            }
        });
    }

    public void toView(int index) {
        getMainActivity().addCommonFragment(PhotoViewFragment.newInstance(
                DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.DOOR_BELL_SERVER_IP
                        + mData.get(index).getImg())));

    }

    public void toClose() {
        removeSelf();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mCall != null) {
            mCall.cancel();
        }
    }
}
