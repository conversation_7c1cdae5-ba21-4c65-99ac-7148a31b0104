package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ModifyPlugsLayoutBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

/**
 * Created by Rinfon on 16/7/12.
 */
public class ModifyDoorBellFragment extends MyBaseFragment<ModifyPlugsLayoutBinding>
        implements IPluginBindCallBack {
    private static final String KEY_PLUGIN_INFO = "PLUGIN_INFO";

    public static final String ID = "id";

    public static final String ASKDATA = "askdata";

    public static final String INDEX = "index";

    public static final String NAME = "name";

    public static final String ISADD = "isAdd";

    public static final String MESSAGEID = "messageid";

    private String id;
    private Plugin mAddPlugin;

    private ModifyASKPlugsFragment.ICallBack callBack;

    public static ModifyDoorBellFragment newInstance(int index, String name, String id, boolean isAdd, String askData) {
        ModifyDoorBellFragment modifyPlugsFragment = new ModifyDoorBellFragment();
        Bundle args = new Bundle();
        args.putInt(INDEX, index);
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAdd);
        args.putString(ASKDATA, askData);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyDoorBellFragment newInstance(int index, String name, String id, boolean isAdd, String askData, Plugin plugin) {
        ModifyDoorBellFragment modifyPlugsFragment = new ModifyDoorBellFragment();
        Bundle args = new Bundle();
        args.putInt(INDEX, index);
        args.putString(ID, id);
        args.putString(NAME, name);
        args.putBoolean(ISADD, isAdd);
        args.putString(ASKDATA, askData);
        args.putSerializable(KEY_PLUGIN_INFO, plugin);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.modify_plugs_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v->removeSelf());
        mBinding.btnSave.setOnClickListener(v->toSave());
    }

    @Override
    public void initData() {
        super.initData();
        mAddPlugin = (Plugin) getArguments().getSerializable(KEY_PLUGIN_INFO);
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.modify_plugs_title));
        mBinding.modifyPlugsInput.setHint(Local.s(getResources().getString(R.string.modifyaccessoryhint)));
        String text = getArguments().getString(ID);
        mBinding.modifyPlugsNetwork.setVisibility(View.GONE);
        mBinding.modifyPlugsHint.setVisibility(View.GONE);
        mBinding.sirenSetting.setVisibility(View.GONE);
        id = text;

        if (getArguments().getBoolean(ISADD)) {
            DinSDK.getPluginActivtor().addBindCallBack(this);
            mBinding.modifyPlugsType.setLocalText(getResources().getString(R.string.device_managent_doorbell));
            mBinding.modifyPlugsId.setText("ID:" + id);
            if (!TextUtils.isEmpty(getArguments().getString(NAME))) {
                mBinding.modifyPlugsInput.setText(getArguments().getString(NAME));
            }
        } else {
            mBinding.modifyPlugsId.setText("ID:" + id);
            mBinding.modifyPlugsType.setLocalText(getResources().getString(R.string.device_managent_doorbell));
            if (!TextUtils.isEmpty(getArguments().getString(NAME))) {
                mBinding.modifyPlugsInput.setText(getArguments().getString(NAME));
            }
        }

    }

    public void toSave() {
        final String inputName = mBinding.modifyPlugsInput.getText().toString().trim();
        if (TextUtils.isEmpty(inputName) || !RegxUtil.isLegalName(inputName)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail,  Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }

        if (getArguments().getBoolean(ISADD)) {
            toAdd();
        } else {
            toChangeName();
        }
    }

    private void toAdd() {
        if (TextUtils.isEmpty(mBinding.modifyPlugsInput.getText().toString().trim())) {
            return;
        }

        if (null == mAddPlugin) {
            DDLog.e(TAG, "Error!!! Empty plugin info.");
            showErrorToast();
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        mAddPlugin.setPluginName(mBinding.modifyPlugsInput.getText().toString().trim());
        DinSDK.getPluginActivtor().bindDevice(mAddPlugin);
    }

    private void toChangeName() {
        if (TextUtils.isEmpty(mBinding.modifyPlugsInput.getText().toString().trim())) {
            return;
        }
        showTimeOutLoadinFramgmentWithErrorAlert();

        DDLog.i(TAG, "toChangePluginName");
        Device device = DinHome.getInstance().getDevice(id);
        if (null != device) {
            DDLog.i(TAG, "修改名字");
            device.submit(PanelParamsHelper.setPluginName(mBinding.modifyPlugsInput.getText().toString()));
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    public ModifyASKPlugsFragment.ICallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(ModifyASKPlugsFragment.ICallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        DinSDK.getPluginActivtor().removeBindCallBack(this);
        if (callBack != null) {
            callBack = null;
        }
    }

    @Override
    public void onBindResult(int code, String msg) {
        DDLog.i(TAG, "onBindResult, code: " + code + ", msg: " + msg);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (code == 1) {
            removeSelf();
        } else if (ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL == code) {
            showErrorToast();
        } else if (ErrorCode.ACTIVTOR_ALREAD_HAS_PLUGIN == code) {
            removeSelf();
            showToast(getResources().getString(R.string.tiggle_has_plug));
        } else {
            showErrorToast();
        }
    }
}
