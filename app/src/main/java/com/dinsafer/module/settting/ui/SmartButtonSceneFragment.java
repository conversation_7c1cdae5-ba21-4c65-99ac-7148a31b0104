package com.dinsafer.module.settting.ui;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.IPCKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.SmartButtonActionChangeEvent;
import com.dinsafer.model.SmartButtonActionDeleteEvent;
import com.dinsafer.model.SmartButtonSceneData;
import com.dinsafer.model.SmartButtonTargetData;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.SmartButtonSceneItem;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CustomizeItemAnimator;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.SmartButtonUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;

import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_ACTION;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_ACTION_CONF;

/**
 * SmartButton场景选择和已配置行为展示页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/10/21 4:21 PM
 */
public class SmartButtonSceneFragment extends BaseFragment
        implements BaseQuickAdapter.OnItemClickListener, ModifyASKPlugsFragment.ICallBack,
        IDeviceCallBack {

    private static final String KEY_PLUG_DATA = "plug_data";
    private static final String DATA_KEY_NAME = "name";
    private static final String DATA_KEY_ID = "id";
    private static final String DATA_KEY_STYPE = "stype";
    private static final String DATA_KEY_SEND_ID = "sendid";
    private static final String DATA_KEY_PLUGIN_TYPE = "plugin_type";
    private static final String DATA_KEY_SETTLED_ACTION = "settled_action";

    // 允许设置的最大指令个数，超过后会隐藏Add Scene类型的Item，（修改为不需隐藏）
    private static final int MAX_ACTION_COUNT = Integer.MAX_VALUE;
    // Item添加和移除动画时长
    private static final int ANIM_DURATION_ADD_MILLIS = 300;
    private static final int ANIM_DURATION_REMOVE_MILLIS = 300;
    private static final int ANIM_DURATION_UPDATE_MILLIS = 700;
    // Item添加或移除后，更新数据并刷新界面的延时时长
    private static final int DURATION_NOTIFY_DATA_CHANGE_MILLIS = 800;
    // 返回页面后，需要时刷新页面时，刷新界面的延时时长
    private static final int DURATION_AFTER_ACTION_UPDATE_MILLIS = 400;
    private static final int DURATION_AFTER_TOP_TOAST_MILLIS = 300;

    ImageView commonBarRight;
    LocalTextView tvSceneActionTittle;
    RecyclerView lvSceneAction;

    private SmartButtonSceneItem mAdapter;
    private ArrayList<SmartButtonSceneData> mDatas;
    private ArrayList<SmartButtonSceneData> mSceneDatas;
    private ArrayList<SmartButtonSceneData> mActionDatas;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private SmartButtonActionChangeEvent mUpdateEvent;
    private LinearLayoutManager mLayoutManager;
    private Builder mBuilder;
    JSONObject mAskData;
    ModifyASKPlugsFragment.ICallBack mModifyCallback;
    private Call<ResponseBody> mConfigCall;
    private Call<StringResponseEntry> mDeleteCall;
    private String messageId;
    private int mCurrentDeleteActionIndex = -1;
    private SmartButtonTargetData mSmartButtonData;
    private boolean mInitError;
    /**
     * 配件类型-控制对象
     * 目前有以下的类型：
     * {@link com.dinsafer.config.IPCKey#SMART_BUTTON}
     * {@link com.dinsafer.config.IPCKey#RC_KEY}
     */
    private int mSourcePluginType;

    private Device mPluginDevice;
    private boolean isSelfOperate;

    public static SmartButtonSceneFragment newInstance(@NonNull Builder builder, int pluginType) {
        return newInstance(builder, pluginType, null);
    }

    /**
     * @param builder
     * @param pluginType
     * @param settledAction 已经设置的Action
     * @return
     */
    public static SmartButtonSceneFragment newInstance(@NonNull Builder builder, int pluginType,
                                                       SmartButtonSceneData settledAction) {
        SmartButtonSceneFragment fragment = new SmartButtonSceneFragment();
        Bundle args = new Bundle();
        args.putParcelable(KEY_PLUG_DATA, builder);
        args.putInt(DATA_KEY_PLUGIN_TYPE, pluginType);
        args.putParcelable(DATA_KEY_SETTLED_ACTION, settledAction);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.smart_button_scene_list_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        EventBus.getDefault().register(this);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.setting_back).setOnClickListener( v -> onViewClick(v));
        rootView.findViewById(R.id.common_bar_right).setOnClickListener( v -> onViewClick(v));
    }

    private void __bindViews(View rootView) {
        commonBarRight = rootView.findViewById(R.id.common_bar_right);
        tvSceneActionTittle = rootView.findViewById(R.id.tv_scene_action_tittle);
        lvSceneAction = rootView.findViewById(R.id.lv_scene_action);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mConfigCall) {
            mConfigCall.cancel();
        }
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
        if (null != mDeleteCall) {
            mDeleteCall.cancel();
        }
        mHandler.removeCallbacksAndMessages(null);
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void initData() {
        super.initData();
        showTimeOutLoadinFramgmentWithErrorAlert();
        getArgumentsFromOther();

        if (mInitError) {
            return;
        }

        tvSceneActionTittle.setText(mBuilder.getName());

        mDatas = new ArrayList<>();
        initActionData();
        initSceneData();

        mLayoutManager = new LinearLayoutManager(getContext());
        lvSceneAction.setLayoutManager(mLayoutManager);

        DividerItemDecoration divider = new DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL);
        Drawable drawable = ContextCompat.getDrawable(getContext().getApplicationContext(), R.drawable.shape_transparent_divider);
        divider.setDrawable(drawable);
        lvSceneAction.addItemDecoration(divider);

        mAdapter = new SmartButtonSceneItem(mDatas);
        mAdapter.openLoadAnimation(BaseQuickAdapter.SLIDEIN_BOTTOM);
        mAdapter.setNotDoAnimationCount(Integer.MAX_VALUE - 1);
        lvSceneAction.setAdapter(mAdapter);

        mAdapter.setOnItemClickListener(this);

        CustomizeItemAnimator defaultItemAnimator = new CustomizeItemAnimator();
        defaultItemAnimator.setAddDuration(ANIM_DURATION_ADD_MILLIS);
        defaultItemAnimator.setRemoveDuration(ANIM_DURATION_REMOVE_MILLIS);
        lvSceneAction.setItemAnimator(defaultItemAnimator);
    }

    /**
     * 从上个页面获取参数
     *
     * @return true: 成功获取参数
     */
    private void getArgumentsFromOther() {
        DDLog.i(TAG, "getArgumentsFromOther");
        mBuilder = getArguments().getParcelable(KEY_PLUG_DATA);
        mSourcePluginType = getArguments().getInt(DATA_KEY_PLUGIN_TYPE, IPCKey.SMART_BUTTON);
        try {
            mAskData = new JSONObject(mBuilder.getData());
            mSmartButtonData = new SmartButtonTargetData.Builder()
                    .setTargetName(DDJSONUtil.getString(mAskData, SmartButtonUtil.SERVICE_KEY_NAME))
                    .setTargetId(DDJSONUtil.getString(mAskData, DATA_KEY_ID))
                    .setSendid(DDJSONUtil.getString(mAskData, SmartButtonUtil.SERVICE_KEY_SEND_ID))
                    .setStype(DDJSONUtil.getString(mAskData, SmartButtonUtil.SERVICE_KEY_STYPE))
                    .setDtype(DDJSONUtil.getInt(mAskData, SmartButtonUtil.SERVICE_KEY_DTYPE))
                    .createSmartButtonTargetData();
        } catch (JSONException e) {
            DDLog.e(TAG, "ERROR");
            e.printStackTrace();
            return;
        }

        final String id = mBuilder.getId();
        if (!TextUtils.isEmpty(id)) {
            mPluginDevice = DinHome.getInstance().getDevice(id);
            mPluginDevice.registerDeviceCallBack(this);
        }

        mInitError = null == mBuilder
                || null == mAskData
                || null == mSmartButtonData
                || TextUtils.isEmpty(mSmartButtonData.getTargetId());
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (mInitError) {
            DDLog.e(TAG, "Error get param form last page.");
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            removeSelf();
        } else {
            if (IPCKey.SMART_BUTTON == mSourcePluginType) {
                requestSettleAction();
            } else if (IPCKey.RC_KEY == mSourcePluginType) {
                mActionDatas.clear();
                SmartButtonSceneData settledAction = getArguments().getParcelable(DATA_KEY_SETTLED_ACTION);
                if (null != settledAction) {
                    mActionDatas.add(settledAction);
                }
                initSettleActionData();
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }
        }
    }

    /**
     * 初始化动作数据
     */
    private void initActionData() {
        DDLog.i(TAG, "initActionData");
        mActionDatas = new ArrayList<>();
    }

    /**
     * 初始化显示已经设置的Action数据
     */
    private void initSettleActionData() {
        DDLog.i(TAG, "initSettleActionData");
        // 1、没有Action数据，更新列表数据
        if (isCurrentEmptyAction()) {
            DDLog.i(TAG, "Not add any action yet.");
            mDatas.addAll(mSceneDatas);
            mAdapter.notifyDataSetChanged();
            return;
        }

        // 2. 有Action数据，更新列表数据
        // 第一个数据需要显示头部
        addActionItemGroupTittle(mActionDatas.get(0));

        if (isCurrentFullAction()) {
            // 达到最数量的Action数据
            DDLog.i(TAG, "The amount's of action is max, hide the scene item.");
            mDatas.addAll(0, mActionDatas);
        } else {
            // 未达到最大数量
            mDatas.addAll(0, mActionDatas);
            mDatas.addAll(mSceneDatas);
        }
        mAdapter.notifyDataSetChanged();
    }

    /**
     * 初始化默认的场景数据
     */
    private void initSceneData() {
        DDLog.i(TAG, "initSceneData");
        int sceneTittleResId = IPCKey.RC_KEY == mSourcePluginType
                ? R.string.smart_button_add_scene_remote_controller
                : R.string.smart_button_add_scene;

        mSceneDatas = new ArrayList<>();
        if (IPCKey.RC_KEY != mSourcePluginType) {
            mSceneDatas.add(
                    new SmartButtonSceneData.Builder()
                            .setItemType(SmartButtonUtil.ITEM_TYPE_SCENE)
                            .setSceneType(SmartButtonUtil.SCENE_TYPE_SECURITY_CMD)
                            .createSmartButtonSceneData());
            mSceneDatas.add(
                    new SmartButtonSceneData.Builder()
                            .setItemType(SmartButtonUtil.ITEM_TYPE_SCENE)
                            .setSceneType(SmartButtonUtil.SCENE_TYPE_RING_BELL)
                            .createSmartButtonSceneData());
        }
        mSceneDatas.add(
                new SmartButtonSceneData.Builder()
                        .setItemType(SmartButtonUtil.ITEM_TYPE_SCENE)
                        .setSceneType(SmartButtonUtil.SCENE_TYPE_SWITCH_PLUG)
                        .createSmartButtonSceneData());
        // TODO 添加继电器的图标
//        mSceneDatas.add(
//                new SmartButtonSceneData.Builder()
//                        .setItemType(SmartButtonUtil.ITEM_TYPE_SCENE)
//                        .setSceneType(SmartButtonUtil.SCENE_TYPE_CONTROL_SHUTTER)
//                        .createSmartButtonSceneData());

        mSceneDatas.get(0).setShowTittle(true);
        mSceneDatas.get(0).setGroupTittle(Local.s(getString(sceneTittleResId)));

        mSceneDatas.add(
                new SmartButtonSceneData.Builder()
                        .setItemType(SmartButtonUtil.ITEM_TYPE_HEADER)
                        .createSmartButtonSceneData());

    }

    public void onViewClick(View v) {
        int clickViewId = v.getId();
        if (R.id.setting_back == clickViewId) {
            removeSelf();
        } else if (R.id.common_bar_right == clickViewId) {
            jump2ModifyPage();
        }
    }

    /**
     * 跳转改名页
     */
    private void jump2ModifyPage() {
        DDLog.i(TAG, "jump2ModifyPage");

        if (null == mBuilder) {
            showErrorToast();
            return;
        }

        // 改名后需要同步
        mBuilder.setName(DDJSONUtil.getString(mAskData, DATA_KEY_NAME));
        mBuilder.setData(mAskData);
        ModifyASKPlugsFragment modifyASKPlugsFragment = ModifyASKPlugsFragment.newInstance(mBuilder);
        modifyASKPlugsFragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);
    }

    /**
     * 显示Action卡片的操作菜单
     *
     * @param position Item在列表中的下标
     */
    private void showActionSheet(final int position) {
        DDLog.i(TAG, "showActionSheet, position: " + position);
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.Cancel)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.smart_button_delete_action))
                .setOtherButtonTitles(Local.s(getString(R.string.menu_edit)),
                        Local.s(getString(R.string.smart_button_delete_action)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                        DDLog.i(TAG, "onDismiss");
                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        DDLog.i(TAG, "onOtherButtonClick, position: " + position);
                        if (0 == index) {
                            // 编辑Action类型的Item
                            SmartButtonSceneData clickItem = mAdapter.getItem(position);
                            getDelegateActivity().addCommonFragment(
                                    SmartButtonSceneEditFragment.newInstance(clickItem, mSmartButtonData, mSourcePluginType));
                        } else if (1 == index) {
                            // 删除Action类型的Item
                            requestDeleteAction(position);
                        }
                    }
                }).show();
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        DDLog.i(TAG, "onItemClick, position: " + position);
        SmartButtonSceneData clickItem = mAdapter.getItem(position);
        if (SmartButtonUtil.ITEM_TYPE_HEADER == clickItem.getItemType()) {
            DDLog.i(TAG, "Click tittle, do nothing.");
            return;
        }

        // TODO 暂不支持继电器
        if (SmartButtonUtil.SCENE_TYPE_CONTROL_SHUTTER == clickItem.getSceneType()) {
            showErrorToast();
            return;
        }

        // 更新已使用的action信息
        mSmartButtonData.removeAllUsedActionMark();
        if (0 < mActionDatas.size()) {
            for (SmartButtonSceneData saveAction :
                    mActionDatas) {
                mSmartButtonData.setActionUse(saveAction.getActionData().getClickAction());
            }
        }

        if (SmartButtonUtil.ITEM_TYPE_SCENE == clickItem.getItemType()) {
            // 点击了Scene Item
            if (SmartButtonUtil.SCENE_TYPE_SECURITY_CMD == clickItem.getSceneType()) {
                getDelegateActivity().addCommonFragment(
                        SmartButtonSceneEditFragment.newInstance(clickItem, mSmartButtonData, mSourcePluginType));
            } else {
                getDelegateActivity().addCommonFragment(
                        SmartButtonSelectTargetFragment.newInstance(clickItem, mSmartButtonData, mSourcePluginType));
            }
        } else if (SmartButtonUtil.ITEM_TYPE_ACTION == clickItem.getItemType()) {
            // 点击了Action Item
            showActionSheet(position);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onActionChangedEvent(SmartButtonActionChangeEvent event) {
        DDLog.i(TAG, "onActionChangedEvent");
        mUpdateEvent = event;
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        DDLog.i(TAG, "onEnterFragment");

        // 重新进入页面后更新界面
        if (null == mUpdateEvent || null == mUpdateEvent.getNewAction()) {
            DDLog.d(TAG, "Empty action update event data, no need update action.");
            return;
        }

        lvSceneAction.scrollToPosition(-1);

        // 顶部提示
        mHandler.postDelayed(() -> {
            getMainActivity().showTopToast(R.drawable.icon_toast_succeed,
                    getString(R.string.smart_button_action_set_success));

        }, DURATION_AFTER_TOP_TOAST_MILLIS);

        // 更新列表数据
        mHandler.postDelayed(() -> {
            // Action更新逻辑
            // 1、判断新的ActionType是否已经存在

            // 1.1 新ActionType已经存在- 判断旧的ActionType是否存在
            // 1.1.1 旧ActionType存在，需要更新就ActionType的Item并移除重复ActionType的Item（将Item修改为已存在的ActionType）
            // 1.1.2 旧ActionType不存在，无需额外操作 （有冲突添加Item）

            // 1.2 新ActionType不存在
            // 1.2.1 旧ActionType存在，直接替换 （将Item修改为不存在的ActionType）
            // 1.2.2 旧ActionType不存在，直接添加 （无冲突添加Item）
            int updateIndex = findNewActionIndex();
            SmartButtonSceneData newAction = mUpdateEvent.getNewAction();
            int lastActionIndex = findActionIndexByActionType(mUpdateEvent.getSrcActionType());

            if (-1 == updateIndex) {
                if (-1 != lastActionIndex) {
                    // 替换旧的
                    DDLog.i(TAG, "Need update action.");
                    SmartButtonSceneData oldAction = mActionDatas.get(lastActionIndex);
                    updateAction(oldAction, newAction);
                    mAdapter.notifyItemChanged(lastActionIndex);
                } else {
                    // 新增
                    DDLog.i(TAG, "Need add action.");
                    addAction(newAction);
                }
            } else {
                // 新的ActionType已经存在，如果还存在就的ActinType，需要移除
                if (-1 != lastActionIndex
                        && lastActionIndex != updateIndex) {
                    DDLog.i(TAG, "Update action at position: " + lastActionIndex
                            + " and remove item at position: " + updateIndex);
                    SmartButtonSceneData oldAction = mActionDatas.get(lastActionIndex);
                    updateAction(oldAction, newAction);
                    mAdapter.notifyItemChanged(lastActionIndex);

                    mHandler.postDelayed(() -> removeAction(updateIndex),
                            ANIM_DURATION_UPDATE_MILLIS);
                } else {
                    // 没有修改ActionType，直接替换
                    DDLog.i(TAG, "Update action at position: " + updateIndex);
                    SmartButtonSceneData oldAction = mActionDatas.get(updateIndex);
                    updateAction(oldAction, newAction);
                    mAdapter.notifyItemChanged(updateIndex);
                }
            }

            // 置空操作时间，防止重复刷新
            mUpdateEvent = null;
        }, DURATION_AFTER_ACTION_UPDATE_MILLIS);
    }

    /**
     * 更新Action信息
     *
     * @param oldAction
     * @param newAction
     */
    private void updateAction(SmartButtonSceneData oldAction, SmartButtonSceneData newAction) {
        DDLog.i(TAG, "updateAction");
        if (null == oldAction || null == newAction) {
            return;
        }

        oldAction.setSceneType(newAction.getSceneType());
        oldAction.setActionData(newAction.getActionData());
        oldAction.setTargetData(newAction.getTargetData());
    }

    /**
     * 根据ActionType获取该Actin在列表中的下标
     *
     * @param actionType
     * @return -1: 未找到
     */
    private int findActionIndexByActionType(int actionType) {
        DDLog.i(TAG, "findActionIndexByActionType, actionType: " + actionType);
        int actionIndex = -1;
        if (0 < mActionDatas.size()) {
            SmartButtonSceneData action;
            for (int i = 0; i < mActionDatas.size(); i++) {
                action = mActionDatas.get(i);
                if (null != action.getActionData()
                        && actionType == action.getActionData().getClickAction()) {
                    actionIndex = i;
                    break;
                }
            }
        }
        DDLog.i(TAG, "last action index: " + actionIndex);
        return actionIndex;
    }

    /**
     * 根据保存的action，获取该action在列表中的下标
     *
     * @return action的下标，-1：没找到，表示是新增的
     */
    private int findNewActionIndex() {
        DDLog.i(TAG, "findNewActionIndex");
        int actionIndex = -1;
        if (null != mUpdateEvent
                && 0 != mActionDatas.size()
                && null != mUpdateEvent.getNewAction()
                && null != mUpdateEvent.getNewAction().getActionData()) {

            int clickAction = mUpdateEvent.getNewAction().getActionData().getClickAction();
            SmartButtonSceneData saveAction;
            for (int i = 0; i < mActionDatas.size(); i++) {
                saveAction = mActionDatas.get(i);
                if (clickAction == saveAction.getActionData().getClickAction()) {
                    actionIndex = i;
                    DDLog.i(TAG, "Find action: " + clickAction + " at index " + actionIndex);
                    break;
                }
            }
        }

        return actionIndex;
    }

    /**
     * 添加Action类型的Item
     * 1、如果当前没有Action类型数据，需要先添加Action类型的标题
     * 2、如果添加Action后达到允许设置的最大数量，需要隐藏Scene类型的Item
     *
     * @param itemData 需要添加的Action
     */
    private void addAction(SmartButtonSceneData itemData) {
        DDLog.i(TAG, "addAction");
        if (isCurrentFullAction()) {
            DDLog.e(TAG, "Cant't add action, because the amount of action is max.");
            return;
        }

        // 添加Action类型的Item
        if (isCurrentEmptyAction()) {
            // 第一个Action，需要显示Tittle
            addActionItemGroupTittle(itemData);
            mLayoutManager.scrollToPosition(0);
        }

        mActionDatas.add(itemData);
        int position = mActionDatas.size() - 1;
        mDatas.add(position, itemData);
        mAdapter.notifyItemInserted(position);

        // 已达到最大的Action数量，隐藏Scene类型的Item
        if (isCurrentFullAction()) {
            mHandler.postDelayed(() -> {
                mLayoutManager.scrollToPosition(0);
                mDatas.removeAll(mSceneDatas);
                mAdapter.notifyDataSetChanged();
            }, DURATION_NOTIFY_DATA_CHANGE_MILLIS);
        }
    }

    /**
     * 移除Action类型的Item
     *
     * @param position
     */
    private void removeAction(int position) {
        DDLog.i(TAG, "removeAction, position: " + position);
        if (0 > position || mActionDatas.size() <= position) {
            DDLog.e(TAG, "Delete action failed because of the index error.");
            return;
        }

        boolean isLastFullAction = isCurrentFullAction();
        mActionDatas.remove(position);
        mDatas.remove(position);
        mCurrentDeleteActionIndex = -1;
        mAdapter.notifyItemRemoved(position);
        EventBus.getDefault().post(new SmartButtonActionDeleteEvent(mBuilder.getId()));

        // 移除了第一个Action，且列表中还有Action,需要为新的第一个Action设置标题
        if (0 == position
                && !isCurrentEmptyAction()) {
            addActionItemGroupTittle(mActionDatas.get(0));
            mAdapter.notifyItemChanged(0);
        }

        // 移除前Action的Item数是最大值，移除后需要显示Scene类型的Item
        if (isLastFullAction) {
            mHandler.postDelayed(() -> {
                mDatas.addAll(mSceneDatas);
                mAdapter.notifyDataSetChanged();
            }, DURATION_NOTIFY_DATA_CHANGE_MILLIS);
        }
    }

    /**
     * 为Action类型的Item添加标题
     */
    private void addActionItemGroupTittle(SmartButtonSceneData tittleActionItem) {
        DDLog.i(TAG, "addActionItemGroupTittle");
        if (null == tittleActionItem) {
            DDLog.e(TAG, "Can't set tittle for empty action item.");
            return;
        }

        tittleActionItem.setShowTittle(true);
        tittleActionItem.setGroupTittle(Local.s(getString(R.string.smart_button_action)));
    }

    /**
     * 当前Action类型的Item是否已达到上限
     * {@link #mActionDatas} 中包含标题和Action类型的Item
     *
     * @return true:已达到上限
     */
    private boolean isCurrentFullAction() {
        return null != mActionDatas && MAX_ACTION_COUNT <= mActionDatas.size();
    }

    /**
     * 当前是否没有Action类型的Item
     * {@link #mActionDatas} 中包含标题和Action类型的Item
     *
     * @return true: 没有Action类型的Item
     */
    private boolean isCurrentEmptyAction() {
        return null == mActionDatas || 0 == mActionDatas.size();
    }

    @Override
    public void onDeletePlug(String id) {
        if (null != mModifyCallback) {
            mModifyCallback.onDeletePlug(id);
        }
        removeSelf();
    }

    @Override
    public void onChangeName(int index, String name) {
        DDLog.i(TAG, "onChangeName, index: " + index);
        if (null != mModifyCallback) {
            mModifyCallback.onChangeName(index, name);
        }

        try {
            mAskData.put(DATA_KEY_NAME, name);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        tvSceneActionTittle.setText(name);
    }


    public void setCallBack(ModifyASKPlugsFragment.ICallBack callBack) {
        this.mModifyCallback = callBack;
    }

    /**
     * 获取已经配置的Action
     */
    private void requestSettleAction() {
        DDLog.i(TAG, "requestSetAction");
        if (null == mAskData || null == mPluginDevice) {
            showErrorToast();
            return;
        }

        mPluginDevice.submit(PanelParamsHelper.getSmartButtonConfig());
    }

    /**
     * 请求删除Action
     *
     * @param position Action在列表的下标
     */
    private void requestDeleteAction(int position) {
        DDLog.i(TAG, "requestDeleteAction");
        mCurrentDeleteActionIndex = position;

        SmartButtonSceneData actionItem = mDatas.get(position);

        JSONObject actionConfig = new JSONObject();

        JSONObject deleteConfig = new JSONObject();
        try {
            deleteConfig.put(SERVICE_KEY_ACTION,
                    SmartButtonUtil.getActionStringByActionType(actionItem.getActionData().getClickAction()));
            deleteConfig.put(SERVICE_KEY_ACTION_CONF, actionConfig);
        } catch (JSONException e) {
            DDLog.e(TAG, "Error.");
            e.printStackTrace();
            showErrorToast();
            return;
        }

        if (null == mPluginDevice) {
            showErrorToast();
            DDLog.e(TAG, "requestDeleteAction - ERROR, no device");
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        ArrayList<JSONObject> actionConfigs = new ArrayList<>();
        actionConfigs.add(deleteConfig);
        isSelfOperate = true;
        mPluginDevice.submit(PanelParamsHelper.updateSmartButtonConfig(actionConfigs));
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PluginCmd.GET_SMART_BUTTON_CONFIG.equals(cmd)) {
            onGetSmartButtonConfig(status, map);
        } else if (PluginCmd.UPDATE_PLUGIN_CONFIG.equals(cmd) && isSelfOperate) {
            onUpdateSmartButtonConfig(status, map);
            isSelfOperate = false;
        } else if (PluginCmd.PLUGIN_DELETE.equals(cmd)) {
            removeSelf();
            EventBus.getDefault().post(new PluginDeleteEvent(deviceId));
        }
    }

    private void onUpdateSmartButtonConfig(int status, Map<String, Object> map) {
        DDLog.i(TAG, "onUpdateSmartButtonConfig");
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            DDLog.i(TAG, "Send cmd: UPDATE_SMART_BT_CONF, result: SUCCESS.");
            removeAction(mCurrentDeleteActionIndex);
            closeTimeOutLoadinFramgmentWithErrorAlert();
        } else {
            DDLog.e(TAG, "Send cmd: UPDATE_SMART_BT_CONF, result: ERROR");
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    private void onGetSmartButtonConfig(int status, Map<String, Object> map) {
        DDLog.i(TAG, "onGetSmartButtonConfig");
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
            try {
                JSONObject resultJson = new JSONObject(result);
                mActionDatas.addAll(SmartButtonUtil.parseJsonFromService(resultJson));
                initSettleActionData();
            } catch (Exception e) {
                DDLog.e(TAG, "Error");
                e.printStackTrace();
            }

            closeTimeOutLoadinFramgmentWithErrorAlert();
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }
}
