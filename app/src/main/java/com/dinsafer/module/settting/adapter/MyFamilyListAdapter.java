package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.FamilyListData;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class MyFamilyListAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<FamilyListData> mData;

    public static final int TYPE_ITEM = 1;

    public static final int TYPE_NOT_ADMIN_ITEM = 3;

    public static final int TYPE_HEADER = 0;

    public static final int TYPE_BOTTOM = 2;

    private ICallBack mCallBack;

    private boolean isClickFromUser = false;

    public MyFamilyListAdapter(Activity mActivity, ArrayList<FamilyListData> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    @Override
    public int getItemViewType(int position) {
        if (mData.get(position).isHeader()) {
            return 0;
        } else if (mData.get(position).isBottom()) {
            return TYPE_BOTTOM;
        } else if (mData.get(position).getLevel() != LocalKey.ADMIN) {
            return TYPE_NOT_ADMIN_ITEM;
        } else {
            return 1;
        }
    }

    @Override
    public int getViewTypeCount() {
        return 4;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public boolean isEnabled(int position) {
        int type = getItemViewType(position);
        if (type == TYPE_HEADER || type == TYPE_BOTTOM) {
            return false;
        }
        return super.isEnabled(position);
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup viewGroup) {
        int type = getItemViewType(position);
        if (type == TYPE_HEADER) {
            HeaderViewHolder holder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.my_device_item_header, null);
                holder = new HeaderViewHolder(convertView);
                convertView.setTag(holder);
            } else {
                try {
                    holder = (HeaderViewHolder) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            holder.myDeviceItemHeader.setLocalText(mData.get(position).getName());
        } else if (type == TYPE_BOTTOM) {
            BottomViewHolder holder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.my_family_item_join, null);
                holder = new BottomViewHolder(convertView, v -> {
                    if (mCallBack != null) {
                        mCallBack.onJoinButtonClick();
                    }
                });
                convertView.setTag(holder);
            } else {
                try {
                    holder = (BottomViewHolder) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            holder.btnJoin.setLocalText(mData.get(position).getName());
        } else {
            ViewHolder itemHolder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.my_device_item, null);
                itemHolder = new ViewHolder(convertView);
                convertView.setTag(itemHolder);
            } else {
                try {
                    itemHolder = (ViewHolder) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            boolean isSelect =  mData.get(position).isSelect();
                    itemHolder.myDeviceItemImageView.setVisibility(isSelect
                            ? View.VISIBLE
                            : View.GONE);
            itemHolder.myDeviceItemName.setText(mData.get(position).getName());
            itemHolder.myDeviceItemName.setTextColor(isSelect ?
                    mActivity.getResources().getColor(R.color.color_brand_text) :
                    mActivity.getResources().getColor(R.color.color_white_01));
            RelativeLayout.LayoutParams rlParams = (RelativeLayout.LayoutParams) itemHolder.myDeviceItemName.getLayoutParams();
            rlParams.leftMargin = isSelect ? 0 : DensityUtil.dp2px(mActivity, 18);
            itemHolder.myDeviceItemName.setLayoutParams(rlParams);

            RelativeLayout.LayoutParams lineParams = (RelativeLayout.LayoutParams) itemHolder.viewLine.getLayoutParams();
            lineParams.leftMargin = isSelect ? 0 : DensityUtil.dp2px(mActivity, 18);
            itemHolder.viewLine.setLayoutParams(lineParams);

            itemHolder.myDeviceItemImageView.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View view, MotionEvent motionEvent) {
                    if (mCallBack != null) {
                        mCallBack.onCurrentDeviceClick(position);
                    }
                    return true;
                }
            });
        }

        return convertView;
    }

    public void remove(int index) {
        if (mData != null && index >= 0 && index < mData.size()) {
            mData.remove(index);
            notifyDataSetChanged();
        }
    }

    static class ViewHolder {
        ImageView myDeviceItemImageView;
        TextView myDeviceItemName;
        View viewLine;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            myDeviceItemImageView = view.findViewById(R.id.my_device_item_checkbox);
            myDeviceItemName = view.findViewById(R.id.my_device_item_name);
            viewLine = view.findViewById(R.id.view_line);
        }
    }

    class HeaderViewHolder {
        LocalTextView myDeviceItemHeader;

        HeaderViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            myDeviceItemHeader = view.findViewById(R.id.my_device_item_header);
        }
    }

    class BottomViewHolder {
        LocalCustomButton btnJoin;

        BottomViewHolder(View view, View.OnClickListener onJoinButtonClick) {
            __bindViews(view);
            btnJoin.setOnClickListener(onJoinButtonClick);
        }

        private void __bindViews(View view) {
            btnJoin = view.findViewById(R.id.btn_join);
        }
    }

    public interface ICallBack {
        void onCurrentDeviceClick(int position);

        void onJoinButtonClick();
    }

    public void setCallBack(ICallBack mCallBack) {
        this.mCallBack = mCallBack;
    }
}
