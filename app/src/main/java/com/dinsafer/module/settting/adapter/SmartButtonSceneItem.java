package com.dinsafer.module.settting.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.SmartButtonActionData;
import com.dinsafer.model.SmartButtonSceneData;
import com.dinsafer.model.SmartButtonTargetData;
import com.dinsafer.util.Local;
import com.dinsafer.util.SmartButtonUtil;

import java.util.List;

/**
 * SmartButton 场景和已添加Action列表页的数据Adapter
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/10/26 2:19 PM
 */
public class SmartButtonSceneItem extends BaseMultiItemQuickAdapter<SmartButtonSceneData, BaseViewHolder> {

    private StringBuilder mSb;

    /**
     * Same as QuickAdapter#QuickAdapter(Context,int) but with
     * some initialization data.
     *
     * @param data A new list is created out of this one to avoid mutable list
     */
    public SmartButtonSceneItem(List<SmartButtonSceneData> data) {
        super(data);
        addItemType(SmartButtonUtil.ITEM_TYPE_HEADER, R.layout.item_smart_button_scene_header);
        addItemType(SmartButtonUtil.ITEM_TYPE_ACTION, R.layout.item_smart_button_scene_action);
        addItemType(SmartButtonUtil.ITEM_TYPE_SCENE, R.layout.item_smart_button_scene_scene);

        mSb = new StringBuilder();
    }

    @Override
    protected void convert(BaseViewHolder helper, SmartButtonSceneData item) {
        int viewType = item.getItemType();

        if (SmartButtonUtil.ITEM_TYPE_ACTION == viewType) {
            SmartButtonActionData actionData = item.getActionData();
            SmartButtonTargetData targetData = item.getTargetData();

            // Action类型Item
            // 组的Tittle
            if (item.isShowTittle()) {
                helper.setGone(R.id.i_tittle, true);
                helper.setText(R.id.tv_tittle_title, item.getGroupTittle());
            } else {
                helper.setGone(R.id.i_tittle, false);
            }

            // 标题——优先拼接直接设置的描述信息，如果没有，直接拼接CMD指令
            mSb.delete(0, mSb.length());
            mSb.append(Local.s(mContext.getResources().getString(
                    SmartButtonUtil.getSceneDesResId(item.getSceneType()))));
            if (!TextUtils.isEmpty(actionData.getCmdDesc())) {
                mSb.append(" - ");
                mSb.append(actionData.getCmdDesc());
            } else if (!TextUtils.isEmpty(actionData.getCmd())) {
                mSb.append(" - ");
                mSb.append(Local.s(actionData.getCmd()));
            }

            // cmd icon——优先显示直接设置的图标，如果没有，更加场景和cmd选择图标
            helper.setText(R.id.tv_action_action, mSb.toString());
            int iconId = (0 == item.getSceneIconResId())
                    ? SmartButtonUtil.getCmdIconIdBySceneAmdCmd(item.getSceneType(), actionData.getCmd())
                    : item.getSceneIconResId();
            if (0 == iconId) {
                helper.setVisible(R.id.iv_action_action, false);
            } else {
                helper.setVisible(R.id.iv_action_action, true);
                helper.setImageResource(R.id.iv_action_action, iconId);
            }

            if (null == targetData || TextUtils.isEmpty(targetData.getTargetName())) {
                helper.setVisible(R.id.tv_action_target, false);
            } else {
                helper.setVisible(R.id.tv_action_target, true);
                helper.setText(R.id.tv_action_target, targetData.getTargetName());
            }
            helper.setImageResource(R.id.iv_action_type,
                    SmartButtonUtil.getActionIconResId(actionData.getClickAction()));
            int desId = SmartButtonUtil.getActionDesResId(actionData.getClickAction());
            if (TextUtils.isEmpty(actionData.getClickActionDes())) {
                helper.setText(R.id.tv_action_type, 0 == desId
                        ? ""
                        : Local.s(mContext.getResources().getString(desId)));
            } else {
                helper.setText(R.id.tv_action_type, Local.s(actionData.getClickActionDes()));
            }

            // 添加ItemChild的点击监听，防止点击tittle也触发Item的点击事件
            helper.addOnClickListener(R.id.i_tittle);
        } else if (SmartButtonUtil.ITEM_TYPE_SCENE == viewType) {
            // Scene类型Item
            // 组的Tittle
            if (item.isShowTittle()) {
                helper.setGone(R.id.i_tittle, true);
                helper.setText(R.id.tv_tittle_title, item.getGroupTittle());
            } else {
                helper.setGone(R.id.i_tittle, false);
            }

            helper.setText(R.id.tv_scene_tittle, Local.s(mContext.getResources().getString(
                    SmartButtonUtil.getSceneDesResId(item.getSceneType())
            )));
            helper.setImageResource(R.id.iv_scene_icon, SmartButtonUtil.getSceneIconResId(item.getSceneType()));

            // 添加ItemChild的点击监听，防止点击tittle也触发Item的点击事件
            helper.addOnClickListener(R.id.i_tittle);
        } else {
            // 标题类型Item
            if (TextUtils.isEmpty(item.getSceneName())) {
                helper.setGone(R.id.l_left, false);
                helper.setGone(R.id.l_right, false);
                helper.setGone(R.id.tv_tittle_title, false);
                helper.setGone(R.id.s_space, true);
            } else {
                helper.setGone(R.id.l_left, true);
                helper.setGone(R.id.l_right, true);
                helper.setGone(R.id.tv_tittle_title, true);
                helper.setGone(R.id.s_space, false);
                helper.setText(R.id.tv_tittle_title, item.getSceneName());
            }
        }
    }
}
