package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.TextKeyListener;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.dinsafer.config.DBKey;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.UserUidChangeEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;

import androidx.annotation.Nullable;


/**
 * Created by Rinfon on 16/7/8.
 */
public class ChangeUidFragment extends BaseFragment implements ActionSheet.ActionSheetListener {


    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    LocalCustomButton changeUidSend;
    LocalTextView tvHint;
    EditText etContent;
    LinearLayout commonBackground;

    public static float BACKGROUND_ALPHA = 0.5f;

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    public static ChangeUidFragment newInstance() {
        return new ChangeUidFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.change_uid_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initView(rootView, savedInstanceState);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.btn_save).setOnClickListener( v -> toSend());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
        rootView.findViewById(R.id.common_background).setOnClickListener( v -> closeInput());
        rootView.findViewById(R.id.common_bar_left_icon).setOnClickListener( v -> showMoreActionDialog());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        changeUidSend = rootView.findViewById(R.id.btn_save);
        tvHint = rootView.findViewById(R.id.tv_hint);
        etContent = rootView.findViewById(R.id.et_content);
        commonBackground = rootView.findViewById(R.id.common_background);
    }

    @Override
    public void initData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.username));
        changeUidSend.setAlpha(BACKGROUND_ALPHA);
        setNextBtnEnable(false);
        changeUidSend.setLocalText(getString(R.string.save));
        tvHint.setLocalText(getResources().getString(R.string.username_condition));
        etContent.addTextChangedListener(mWatcher);
        etContent.setHint(Local.s(getString(R.string.username)));

        if (!TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUid())) {
            etContent.setText(DinSDK.getUserInstance().getUser().getUid());
        }
        etContent.setKeyListener(TextKeyListener.getInstance());
    }

    private void updateBtnStateEnable() {
        final String inputContent = etContent.getText().toString();
        boolean enable = !TextUtils.isEmpty(inputContent);
        setNextBtnEnable(enable);
    }

    protected void setNextBtnEnable(final boolean enable) {
        changeUidSend.setAlpha(enable ? 1.0f : 0.5f);
        changeUidSend.setEnabled(enable);
    }

    private void toConfirmUnbindAgain() {
        AlertDialog.createBuilder(getMainActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setContent(getResources().getString(R.string.change_uid_unbind_confirm_hint))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        toInputPassword();
                    }
                })
                .setCancelColor(getResources().getColor(R.color.colorLogout))
                .preBuilder()
                .show();
    }

    private void toInputPassword() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultName("")
                .setContent(getResources().getString(R.string.change_uid_unbind_hint2))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
                        if (!TextUtils.isEmpty(string)) {
                            dialog.dismiss();
                            if (string.equals(DBUtil.SGet(DBKey.USER_PASSWORD))) {
                                toDeleteAccount();
                            } else {
                                AlertDialog.createBuilder(getMainActivity())
                                        .setOk(getResources().getString(R.string.change_uid_unbind_input_again))
                                        .setCancel(getResources().getString(R.string.Cancel))
                                        .setContent(getResources().getString(R.string.change_uid_unbind_wrong_password))
                                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                                            @Override
                                            public void onOkClick() {
                                                toInputPassword();
                                            }
                                        })
                                        .setCancelColor(getResources().getColor(R.color.colorLogout))
                                        .preBuilder()
                                        .show();
                            }
                        }
                    }
                })
                .setCancelColor(getResources().getColor(R.color.colorLogout))
                .preBuilder()
                .show();
    }

    /**
     * 请求网络注销用户
     */
    private void toDeleteAccount() {
        showTimeOutLoadinFramgmentWithErrorAlert();

        DinSDK.getUserInstance().deleteAccount(new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error on toDeleteAccount, i: " + i + ", s: " + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
            }

            @Override
            public void onSuccess() {
                DinSDK.getHomeInstance().stopE2EConnection(true);
                DinSDK.getHomeInstance().releaseDeviceByType(PanelConstant.DeviceType.PANEL);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                DBUtil.Delete(DBKey.USER_PASSWORD);
                DBUtil.Delete(DBKey.REMEMBER_PHONE);
                DBUtil.Delete(DBKey.REMEMBER_PHONE_ZONE);
                DBUtil.Delete(DBKey.REMEMBER_UID);
                getMainActivity().toLogout(false, false);
            }
        });
    }

    public void toSend() {
        setNextBtnEnable(false);

        final String currentUid = etContent.getText().toString().trim();

        if (TextUtils.isEmpty(currentUid) || !RegxUtil.isLegalName(currentUid)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            setNextBtnEnable(true);
            return;
        }

        if (null == DinSDK.getUserInstance().getUser()
                || TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUser_id())) {
            showErrorToast();
            setNextBtnEnable(true);
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();

        DinSDK.getUserInstance().changeUid(currentUid, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ChangeUidFragment.this.isAdded()) {
                    return;
                }
                setNextBtnEnable(true);
                if (i == ErrorCode.SAME_USER_NAME) {
                    getMainActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.this_username_is_already_occupied));
                } else if (ErrorCode.SAME_PWD_WITH_USERNAME == i) {
                    getMainActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.username_contains_password));
                } else {
                    showErrorToast();
                }
            }

            @Override
            public void onSuccess() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ChangeUidFragment.this.isAdded()) {
                    return;
                }
                setNextBtnEnable(true);
                if (DBUtil.Exists(DBKey.REMEMBER_UID)) {
                    DBUtil.Put(DBKey.REMEMBER_UID, currentUid);
                }
                EventBus.getDefault().post(new UserUidChangeEvent());
                close();
            }
        });
    }

    public void close() {
        removeSelf();
    }

    public void closeInput() {
        toCloseInput();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void showMoreActionDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.change_uid_unbind)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setCancelableOnTouchOutside(true)
                .setListener(this).show();
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        if (0 == index) {
            AlertDialog.createBuilder(getMainActivity())
                    .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                    .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                    .setContent(getResources().getString(R.string.unbind_uid_dialog_hint))
                    .setOKListener(new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            toConfirmUnbindAgain();
                        }
                    })
                    .setCancelColor(getResources().getColor(R.color.colorLogout))
                    .preBuilder()
                    .show();
        }
    }
}

