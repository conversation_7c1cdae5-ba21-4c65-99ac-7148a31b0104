package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import androidx.databinding.DataBindingUtil;
import android.view.LayoutInflater;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogBetaUserCloudInviteSuccessBinding;


public class BetaUserCloudInviteSuccessDialog extends Dialog {

    Context mContext;

    private DialogBetaUserCloudInviteSuccessBinding mBinding;

    private AlertClickCallback alertClickCallback;

    public BetaUserCloudInviteSuccessDialog(Context context) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;

        mBinding = DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.dialog_beta_user_cloud_invite_success, null, false);
        setContentView(mBinding.getRoot());

        mBinding.okBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (alertClickCallback != null) {
                    alertClickCallback.onClick(BetaUserCloudInviteSuccessDialog.this);
                }
            }
        });

    }

    public void setAlertClickCallback(AlertClickCallback alertClickCallback) {
        this.alertClickCallback = alertClickCallback;
    }

    public interface AlertClickCallback {
        void onClick(BetaUserCloudInviteSuccessDialog dialog);
    }


}
