package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.common.PluginManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SmartPluginListLayoutBinding;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.model.TuyaItem;
import com.dinsafer.model.TuyaItemPlug;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.adapter.LightPlugGridAdapter;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.plugin.widget.util.CommonDataUtils;
import com.dinsafer.plugin.widget.util.Local;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.PanelItemDivider;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.ModifyPluginInfoHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import androidx.recyclerview.widget.GridLayoutManager;

import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_LOADING;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_OFF;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_ON;

/**
 * Created by Rinfon on 16/7/12.
 */
public class SmartPlugsListFragment extends MyBaseFragment<SmartPluginListLayoutBinding>
        implements ModifyASKPlugsFragment.ICallBack, IDeviceCallBack {

    private ArrayList<TuyaItemPlus> mData;
    private LightPlugGridAdapter mAdapter;

    private int messageIndex;

    private ArrayList<Device> mPluginDevices;

    private Device mPanelDevice;


    public static SmartPlugsListFragment newInstance(String deviceType) {
        SmartPlugsListFragment smartPlugsListFragment = new SmartPlugsListFragment();
        Bundle bundle = new Bundle();
        bundle.putString("TYPE", deviceType);
        smartPlugsListFragment.setArguments(bundle);
        return smartPlugsListFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.smart_plugin_list_layout;
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        showBlueTimeOutLoadinFramgment();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_smart_plug));
        mBinding.listviewEmpty.setLocalText(getResources().getString(R.string.listview_empty));
        mBinding.smartPluginFinish.setLocalText(getResources().getString(R.string.smart_plugin_edit_finish));

        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        String deviceType = getArguments().getString("TYPE");
        if (TextUtils.isEmpty(deviceType)) {
            removeSelf();
        }

        initRecyclerView();
        mPluginDevices = new ArrayList<>();
        if (PanelConstant.DeviceType.SMART_PLUG.equals(deviceType)) {
            mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_smart_plug));
            createPlugsList();
        } else if (PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG.equals(deviceType)) {
            mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_signal_repeater_plug));
            createRepeatersList();
        }
    }

    /**
     * 修改ASK类型的智能插座
     */
    private void openModifyAskPlugsPage(JSONObject askData) {
        if (null == mData || messageIndex < 0 || messageIndex >= mData.size()) {
            return;
        }

        boolean isOffline = false;
        boolean isLowPower = false;

        if ((DDJSONUtil.has(askData, NetKeyConstants.NET_KEY_KEEP_LIVE)
                && !DDJSONUtil.getBoolean(askData, NetKeyConstants.NET_KEY_KEEP_LIVE)
                && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE
                && !DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_S_TYPE).equals("1C"))) {
            isOffline = true;
        } else if (DDJSONUtil.has(askData, NetKeyConstants.NET_KEY_POWER)
                && !DDJSONUtil.getBoolean(askData, NetKeyConstants.NET_KEY_POWER)) {
            isLowPower = true;
        }

        TuyaItemPlug smartPlugsData = (TuyaItemPlug) mData.get(messageIndex);
        Builder builder = new Builder();
        builder.setName(smartPlugsData.getName())
                .setId(smartPlugsData.getId())
                .setAdd(false)
                .setShowDelete(true)
                .setAskPlugin(true)
                .setOffical(true)
                .setMessageIndex(messageIndex)
                .setShowwave(false)
                .setShowSiren(false)
                .setShowSirenTest(false)
                .setLowPower(isLowPower)
                .setOffline(isOffline)
                .setData(askData);
        ModifyASKPlugsFragment modifyASKPlugsFragment = ModifyASKPlugsFragment.newInstance(builder);
        modifyASKPlugsFragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);
    }

    private void initRecyclerView() {
        mData = new ArrayList<>();
        mAdapter = new LightPlugGridAdapter(mData);
        GridLayoutManager glm = new GridLayoutManager(getContext(), 2);
        PanelItemDivider panelItemDivider = new PanelItemDivider(getActivity(),
                this.getResources().getColor(R.color.transparent),
                DensityUtils.dp2px(getContext(), 10));
        mBinding.smartPlugsList.setLayoutManager(glm);
        mBinding.smartPlugsList.addItemDecoration(panelItemDivider);
        mBinding.smartPlugsList.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((BaseQuickAdapter adapter, View view, int position) -> {
            DDLog.i(TAG, "setOnItemClickListener, position: " + position);
            TuyaItemPlug tuyaItemPlus = (TuyaItemPlug) mData.get(position);
            messageIndex = position;

            if (isOfficialSmartPlug(tuyaItemPlus.getType()) && !CommonDataUtil.getInstance().isPanelOnline()) {
                DDLog.e(TAG, "当前主机离线，Item不能被点击哦");
                return;
            }

            if (PluginConstants.CATEGORY_10 == tuyaItemPlus.getCategory()) {
                final Device device = DinHome.getInstance().getDevice(tuyaItemPlus.getId());
                if (null != device) {
                    showTimeOutLoadinFramgmentWithErrorAlert();
                    device.submit(PanelParamsHelper.getPluginDetail());
                } else {
                    DDLog.e(TAG, "Empty smart plug");
                    showErrorToast();
                }
            } else {
                ModifyPluginInfoHelper.getInstance().modifyPluginInfo(
                        getMainActivity(), tuyaItemPlus.getDecodeid(), tuyaItemPlus.getId(),
                        tuyaItemPlus.getName(), true);
            }
        });
    }

    /**
     * 是否自家插座
     *
     * @param type
     * @return true: 是自家插座
     */
    private boolean isOfficialSmartPlug(int type) {
        return type == SMARTPLUGIN_ON || type == SMARTPLUGIN_OFF
                || type == SMARTPLUGIN_LOADING;
    }

    private void createRepeatersList() {
        mPanelDevice = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID());
        if (null != mPanelDevice) {
            mPanelDevice.registerDeviceCallBack(this);
            mPanelDevice.submit(PanelParamsHelper.getPlugsInfo());
        }
    }

    /**
     * 初始化plugg数据列表
     */
    private void createPlugsList() {

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.SMART_PLUG);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(SmartPlugsListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }

                return deviceList;
            }
        }).thenUI(o -> {
            if (null != mPluginDevices && 0 < mPluginDevices.size()) {
                for (Device device : mPluginDevices) {
                    boolean haveOnlineState = DeviceHelper.getBoolean(device, PanelDataKey.HAVE_ONLINE_STATE, false);
                    int switchState = DeviceHelper.getInt(device, PanelDataKey.PLUGIN_SWITCH_STATE, 0);
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    TuyaItemPlug plug = new TuyaItemPlug(pluginName, device.getId());
                    plug.setCategory(DeviceHelper.getInt(device, PanelDataKey.CATEGORY, 10));
                    plug.setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""));
                    plug.setAskPlug(DeviceHelper.getBoolean(device, PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false));
                    plug.setNeedOnlineState(haveOnlineState);
                    plug.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                    plug.setType(PanelConstant.PluginSwitchState.OPENED == switchState
                            ? TuyaItem.SMARTPLUGIN_ON
                            : TuyaItem.SMARTPLUGIN_OFF);
                    plug.setSendid(DeviceHelper.getString(device, PanelDataKey.SEND_ID, null));
                    plug.setStype(DeviceHelper.getString(device, PanelDataKey.S_TYPE, null));
                    plug.setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                    plug.setOnline(DeviceHelper.getBoolean(device, PanelDataKey.IS_ONLINE, false));
                    mData.add(plug);
                }
            }

            updateEmptyViewVisibleState();
            mAdapter.notifyDataSetChanged();
            closeLoadingFragment();
        });


    }

    /**
     * 判断数据是否为空
     */
    private void updateEmptyViewVisibleState() {
        if (null == mData || 0 >= mData.size()) {
            mBinding.listviewEmpty.setVisibility(View.VISIBLE);
            mBinding.smartPlugsList.setVisibility(View.GONE);
        } else {
            mBinding.listviewEmpty.setVisibility(View.GONE);
            mBinding.smartPlugsList.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
        }
        if (null != mPluginDevices && 0 < mPluginDevices.size()) {
            for (Device device : mPluginDevices) {
                device.unregisterDeviceCallBack(this);
            }
        }
        DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.SMART_PLUG);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOnlineEvent event) {
        DDLog.d(TAG, "DeviceOnlineEvent.");
        mAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOfflineEvent event) {
        DDLog.d(TAG, "DeviceOfflineEvent.");
        mAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PlugsNameChangeEvent ev) {
        mData.get(messageIndex).setName(ev.getName());
        mAdapter.notifyItemChanged(messageIndex);
    }

    @Override
    public void onDeletePlug(String id) {

    }

    @Override
    public void onChangeName(int index, String name) {
        mData.get(index).setName(name);
        mAdapter.notifyItemChanged(index);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)) {
            DDLog.e(TAG, "Empty deviceId or cmd");
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
        DDLog.i(TAG, "On plug cmd:  " + cmd + "     result: " + result);

        if (null != mPanelDevice && deviceId.equals(mPanelDevice.getId())) {
            if (PanelCmd.GET_PLUGS_INFO.equals(cmd)) {
                // 仅处理中继插座
                updateSignalRepeaterPlugins(result);
                return;
            }
        }

        if (PanelDataKey.CmdResult.SUCCESS != status) {
            DDLog.e(TAG, "Error status.");
        }

        if (PluginCmd.PLUGIN_DELETE.equals(cmd)) {
            if (1 != resultType) {
                DDLog.e(TAG, "Error result type.");
                return;
            }
            try {
                JSONObject jsonObject = new JSONObject(result);
                final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                String deleteId;
                if (LocalKey.DELETE_PLUGIN.equals(operateCmd)) {
                    deleteId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                } else {
                    deleteId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ID);
                }
                if (TextUtils.isEmpty(deleteId)) {
                    DDLog.e(TAG, "Error, empty delete plug id.");
                    return;
                }
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getId().equals(deleteId)) {
                        mData.remove(i);
                        break;
                    }
                }
                updateEmptyViewVisibleState();
                mAdapter.notifyDataSetChanged();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else if (PluginCmd.PLUG_CHANGE_ON.equals(cmd)) {
            if (1 != resultType) {
                DDLog.e(TAG, "Error result type.");
                return;
            }
            try {
                JSONObject jsonObject = new JSONObject(result);
                final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                String deleteId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                boolean isOn;
                if (LocalKey.SET_SMART_PLUG_ENABLE.equals(operateCmd)) {
                    isOn = LocalKey.KNOCK_TO_SOS_SUCCESS.equals(
                            DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ITEM__SMART__PLUG__ENABLE));
                } else {
                    isOn = LocalKey.STATUS_OPENED_ASK_PLUG
                            == DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ITEM__SMART__PLUG__ENABLE);
                }
                if (TextUtils.isEmpty(deleteId)) {
                    DDLog.e(TAG, "Error, empty delete plug id.");
                    return;
                }
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getId().equals(deleteId)) {
                        mData.get(i).setType(isOn ? TuyaItem.SMARTPLUGIN_ON
                                : TuyaItem.SMARTPLUGIN_OFF);
                        mAdapter.notifyDataSetChanged();
                        break;
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else if (PluginCmd.GET_PLUGIN_DETAIL.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            try {
                JSONObject askData = new JSONObject(result);
                openModifyAskPlugsPage(askData);
            } catch (JSONException e) {
                DDLog.e(TAG, "Error on GET_PLUGIN_DETAIL");
                e.printStackTrace();
            }
        }


    }

    private void updateSignalRepeaterPlugins(String result) {
        if (!TextUtils.isEmpty(result)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                JSONObject pluginInfo = DDJSONUtil.getJSONObject(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__INFO);
                if (null != pluginInfo) {
                    JSONArray jsonArray = DDJSONUtil.getJSONarray(pluginInfo, "signal_repeater_plug");
                    JSONObject repeaterItemJson;
                    TuyaItemPlug plug;
                    for (int i = 0; i < jsonArray.length(); i++) {
                        repeaterItemJson = jsonArray.getJSONObject(i);
                        String deviceId = DDJSONUtil.getString(repeaterItemJson, NetKeyConstants.NET_KEY_ID);
                        String pluginName = DDJSONUtil.getString(repeaterItemJson, NetKeyConstants.NET_KEY_NAME);
                        String dfName = CommonDataUtils.getASKNameByBSType(DDJSONUtil.getString(repeaterItemJson, NetKeyConstants.NET_KEY_S_TYPE));
                        if (TextUtils.isEmpty(pluginName)) {
                            pluginName = Local.s(dfName) + "_" + deviceId;
                        }

                        Device device = DinHome.getInstance().getDevice(deviceId);
                        if (null != device) {
                            device.registerDeviceCallBack(SmartPlugsListFragment.this);
                            mPluginDevices.add(device);

                            boolean haveOnlineState = DeviceHelper.getBoolean(device, PanelDataKey.HAVE_ONLINE_STATE, false);
                            int switchState = DeviceHelper.getInt(device, PanelDataKey.PLUGIN_SWITCH_STATE, 0);
                            plug = new TuyaItemPlug(pluginName, device.getId());
                            plug.setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""));
                            plug.setCategory(DeviceHelper.getInt(device, PanelDataKey.CATEGORY, 10));
                            plug.setAskPlug(DeviceHelper.getBoolean(device, PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false));
                            plug.setNeedOnlineState(haveOnlineState);
                            plug.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                            plug.setType(PanelConstant.PluginSwitchState.OPENED == switchState
                                    ? TuyaItem.SMARTPLUGIN_ON
                                    : TuyaItem.SMARTPLUGIN_OFF);
                            plug.setSendid(DeviceHelper.getString(device, PanelDataKey.SEND_ID, null));
                            plug.setStype(DeviceHelper.getString(device, PanelDataKey.S_TYPE, null));
                            plug.setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                            plug.setOnline(DeviceHelper.getBoolean(device, PanelDataKey.IS_ONLINE, false));
                            mData.add(plug);
                        }
                    }
                }

            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else {
            // 主机离线时
            List<Device> cacheRepeaterList = PluginManager.getInstance().getNotDeletedRepeaterPlugList();
            for (int i = 0; i < cacheRepeaterList.size(); i++) {
                TuyaItemPlug plug;
                Device device = cacheRepeaterList.get(i);
                if (null != device) {
                    device.registerDeviceCallBack(SmartPlugsListFragment.this);
                    mPluginDevices.add(device);

                    boolean haveOnlineState = DeviceHelper.getBoolean(device, PanelDataKey.HAVE_ONLINE_STATE, false);
                    int switchState = DeviceHelper.getInt(device, PanelDataKey.PLUGIN_SWITCH_STATE, 0);
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    plug = new TuyaItemPlug(pluginName, device.getId());
                    plug.setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""));
                    plug.setCategory(DeviceHelper.getInt(device, PanelDataKey.CATEGORY, 10));
                    plug.setAskPlug(DeviceHelper.getBoolean(device, PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false));
                    plug.setNeedOnlineState(haveOnlineState);
                    plug.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                    plug.setType(PanelConstant.PluginSwitchState.OPENED == switchState
                            ? TuyaItem.SMARTPLUGIN_ON
                            : TuyaItem.SMARTPLUGIN_OFF);
                    plug.setSendid(DeviceHelper.getString(device, PanelDataKey.SEND_ID, null));
                    plug.setStype(DeviceHelper.getString(device, PanelDataKey.S_TYPE, null));
                    plug.setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                    plug.setOnline(DeviceHelper.getBoolean(device, PanelDataKey.IS_ONLINE, false));
                    mData.add(plug);
                }
            }

        }

        updateEmptyViewVisibleState();
        mAdapter.notifyDataSetChanged();
        closeLoadingFragment();
    }
}
