package com.dinsafer.module.settting.ui;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ShareQrLayoutBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.YOffsetTextView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.GenerateImageUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;
import com.yanzhenjie.permission.AndPermission;

import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * Created by Rinfon on 16/7/8.
 */
public class ShareQR extends MyBaseFragment<ShareQrLayoutBinding> {
    public static final int REQUEST_PERMISSION_SD_CODE = 1210;

    private Bitmap qrBitmap;

    public static final String CODE = "code";
    public static final String NAME = "name";
    public static final String LEVEL = "level";
    private View mSaveCodeLayout;
    private ImageView mIvCode;
    private TextView mTvFamilyName;
    private YOffsetTextView mTvNote;
    private LocalTextView mTvScanTo;

    public static ShareQR newInstance(String qrCode, String name, int level) {
        Bundle bundle = new Bundle();
        bundle.putString(CODE, qrCode);
        bundle.putString(NAME, name);
        bundle.putInt(LEVEL, level);
        ShareQR shareQR = new ShareQR();
        shareQR.setArguments(bundle);
        return shareQR;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.share_qr_layout;
    }

    /**
     * 如果没有qr，先生成后保存下来，下次打开直接用缓存即可
     */
    public void createQR(String qr) {
        qrBitmap = GenerateImageUtils.createQRImage(qr);
        mBinding.shareQrImageview.setImageBitmap(qrBitmap);
        if (mIvCode != null) {
            mIvCode.setImageBitmap(qrBitmap);
        }
        String strDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        mBinding.shareQrNote.setLocalText(strDate);
        if (mTvNote != null) {
            mTvNote.setText(strDate);
        }
//        TODO 缓存文件
//        if (!checkHasQR()) {
//            qrBitmap = GenerateImageUtils.getInstance().createQRImage(qr);
//            shareQrImageview.setImageBitmap(qrBitmap);
//            saveQR(qrBitmap);
//        } else {
//            直接使用缓存文件
//        }

    }


    public void requestSdCardPermission() {
        getMainActivity().setNotNeedToLogin(true);
        String[] permission = PermissionUtil.getStoragePermissions();
        boolean denied = AndPermission.hasAlwaysDeniedPermission(getDelegateActivity(), permission);
        AndPermission.with(getDelegateActivity())
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    removeSelf();
                })
                .onDenied(permissions -> {
                    DDLog.e("", "Storage permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(getDelegateActivity(), permissions)) {
                        showPermissionNotGrantTip(getString(R.string.permission_tip_album_not_grant));
                    }
                })
                .start();
    }

    // 缓存图片
    public void saveQR(View v) {
        if (PermissionUtil.isStoragePermissionDeny(getDelegateActivity())) {
            requestSdCardPermission();
            return;
        }
        if (mSaveCodeLayout == null) return;
        //              截取该view 的图像；
        Bitmap viewBitmap = GenerateImageUtils.captureView(mSaveCodeLayout);
        //              保存图片；
        if (!TextUtils.isEmpty(GenerateImageUtils.saveImage(getDelegateActivity(), viewBitmap))) {
            getMainActivity().showTopToast(getResources().getString(R.string.saved_to_album));
        }
    }

    //    TODO 判断是否有缓存文件
    private boolean checkHasQR() {
        return false;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mSaveCodeLayout = View.inflate(getContext(), R.layout.layout_save_code, null);
        mIvCode = mSaveCodeLayout.findViewById(R.id.iv_code);
        mTvFamilyName = mSaveCodeLayout.findViewById(R.id.tv_name);
        mTvFamilyName.setText(getArguments().getString(NAME));
        mTvNote = mSaveCodeLayout.findViewById(R.id.tv_note);
        mTvScanTo = mSaveCodeLayout.findViewById(R.id.tv_scan_to);
        mTvScanTo.setLocalText("Scan to join a shared family");
    }

    @Override
    public void initData() {
        super.initData();
        int level = getArguments().getInt(LEVEL);

        if (level == LocalKey.FROM_INSTALLER) {
            mBinding.llLevel.setVisibility(View.GONE);
            mBinding.shareQrHint.setVisibility(View.GONE);
            if (mTvScanTo != null) {
                mTvScanTo.setVisibility(View.GONE);
            }
        } else {
            if (level == LocalKey.ADMIN) {
                mBinding.ivLevel.setImageResource(R.drawable.icon_member_profile_jurisdiction_admin);
                mBinding.tvLevel.setLocalText(getString(R.string.change_permission_admin));
            } else if (level == LocalKey.USER) {
                mBinding.ivLevel.setImageResource(R.drawable.icon_member_profile_jurisdiction_user);
                mBinding.tvLevel.setLocalText(getString(R.string.change_permission_user));
            } else {
                mBinding.ivLevel.setImageResource(R.drawable.icon_member_profile_jurisdiction_guest);
                mBinding.tvLevel.setLocalText(getResources().getString(R.string.change_permission_guest));
            }
        }
        mBinding.shareQrTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.share_qr_code));
        mBinding.shareQrTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.shareQrName.setText(getArguments().getString(NAME));
        mBinding.shareQrHint.setLocalText(getResources().getString(R.string.share_qr_hint));
        mBinding.btnSaveAlbum.setLocalText(getString(R.string.save_to_system_album));
        mBinding.btnSaveAlbum.setOnClickListener(v -> saveQR(v));
        if (mSaveCodeLayout != null) {
            mSaveCodeLayout.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
            mSaveCodeLayout.layout(0, 0, mSaveCodeLayout.getMeasuredWidth(), mSaveCodeLayout.getMeasuredHeight());
        }
        toGetShareQR();
    }

    private void toGetShareQR() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        createQR(getArguments().getString(CODE));
        closeTimeOutLoadinFramgmentWithErrorAlert();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (qrBitmap != null && !qrBitmap.isRecycled())
            qrBitmap.recycle();
    }
}
