package com.dinsafer.module.settting.ui;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;

import org.greenrobot.eventbus.EventBus;

/**
 * CMS设置Protocol2设置页面
 *
 * <AUTHOR>
 * @date 2019-11-28 12:02
 */
public class SecondProtocolFragment extends BaseFragment {


    public static SecondProtocolFragment newInstance() {
        return new SecondProtocolFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.second_protocol_layout, container, false);
        initData();
        return rootView;
    }

    @Override
    public void initData() {
        super.initData();

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }
}
