package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/12.
 */
public class ContactIdFragment extends BaseFragment
        implements ChoosePhoneZoneFragment.IChoosePhoneZoneCallBack, IDeviceCallBack {

    LocalTextView commonBarTitle;
    LocalTextView contactIdHit;
    IOSSwitch contactidSwitch;
    EditText contactId;
    EditText phoneZone;
    EditText phoneText;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static ContactIdFragment newInstance() {
        ContactIdFragment modifyPlugsFragment = new ContactIdFragment();
        return modifyPlugsFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        showTimeOutLoadinFramgment();
        View rootView = inflater.inflate(R.layout.contactid_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.common_bar_left_icon).setOnClickListener( v -> toSave());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        contactIdHit = rootView.findViewById(R.id.contact_id_hit);
        contactidSwitch = rootView.findViewById(R.id.contactid_switch);
        contactId = rootView.findViewById(R.id.contact_id);
        phoneZone = rootView.findViewById(R.id.phone_zone);
        phoneText = rootView.findViewById(R.id.phone_text);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.getPanelCidData());
    }

    InputFilter upperFilter = new InputFilter() {
        public CharSequence filter(CharSequence source, int start, int end,
                                   Spanned dest, int dstart, int dend) {
            return source.toString().toUpperCase();
        }
    };

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.advanced_setting_contactid));
        contactIdHit.setLocalText(getResources().getString(R.string.contact_id_hint));
        contactId.setHint(Local.s(getResources().getString(R.string.cid_code)));
        phoneZone.setHint(Local.s(getResources().getString(R.string.change_phone_zone_hint)));
        phoneText.setHint(Local.s(getResources().getString(R.string.cid_cms)));
        contactId.setFilters(new InputFilter[]{upperFilter});

        EventBus.getDefault().register(this);

        phoneZone.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                    toChoosePhoneZone();
                }
                return true;
            }
        });
    }

    public void toChoosePhoneZone() {
        ChoosePhoneZoneFragment fragment = ChoosePhoneZoneFragment.newInstance(phoneZone.getText().toString());
        fragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(fragment);
    }

    public void toClose() {
        removeSelf();
    }

    public void toSave() {
        if (TextUtils.isEmpty(contactId.getText()) || TextUtils.isEmpty(phoneText.getText())) {
            return;
        }

        if (!checkInput(contactId.getText().toString())) {
            showToast(getResources().getString(R.string.cid_eror));
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.setPanelCidData(contactidSwitch.isOn(),
                contactId.getText().toString(), "none", phoneText.getText().toString()));
    }

    public boolean checkInput(String string) {
        if (TextUtils.isEmpty(string) || string.length() != 4)
            return false;
        return true;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        EventBus.getDefault().unregister(this);
    }


    @Override
    public void onResult(String code, String name) {
        phoneZone.setText(code);
    }


    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (PanelCmd.GET_PANEL_CIDDATA.equals(cmd)) {
            // 获取CID信息
            onGetCIDInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_PANEL_CIDDATA.equals(cmd)) {
                // 设置CID信息
                onSetCIDData(status, map);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置的cid信息
     */
    private void onGetCIDInfo(int status, Map map) {
        DDLog.i(TAG, "onGetCIDInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        boolean enable = DeviceHelper.getBoolean(resultMap, PanelDataKey.Cid.ENABLE, false);
        String contactIdCode = DeviceHelper.getString(resultMap, PanelDataKey.Cid.CONTACT_ID_CODE, "");
        String countryCode = DeviceHelper.getString(resultMap, PanelDataKey.Cid.COUNTRY_CODE, "");
        String phone = DeviceHelper.getString(resultMap, PanelDataKey.Cid.PHONE, "");
        contactId.setText(contactIdCode);
        phoneZone.setText(countryCode);
        phoneText.setText(phone);
        contactidSwitch.setOn(enable);
    }

    /**
     * 设置Cid信息
     */
    private void onSetCIDData(int status, Map map) {
        DDLog.i(TAG, "onSetCIDData, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            showSuccess();
        } else {
            showErrorToast();
        }
    }
}
