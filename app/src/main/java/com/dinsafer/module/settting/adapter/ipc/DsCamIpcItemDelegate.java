package com.dinsafer.module.settting.adapter.ipc;

import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;

import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamIPCSettingFragment;
import com.dinsafer.dscam.DsCamMultiFullPlayActivity;
import com.dinsafer.dscam.DsCamNetWorkSetting;
import com.dinsafer.dscam.DsCamStatusChange;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.dscam.upgrade.DsCamUpgradeFragment;
import com.dinsafer.dscam.upgrade.DsCamUpgradeManager;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.ArrayList;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/6/28
 */
public class DsCamIpcItemDelegate extends BaseIPCItemDelegate {
    private final static String TAG = DsCamIpcItemDelegate.class.getSimpleName();
    private final static String DEFAULT_NAME = "ipc";
    private boolean showStateIcon;
    private String cacheName;

    public DsCamIpcItemDelegate(MainActivity mMainActivity, boolean showStateIcon, String cacheName) {
        super(mMainActivity);
        this.showStateIcon = showStateIcon;
        this.cacheName = cacheName;
    }

    @Override
    public void onBindItemViewHolder(MainPanelIpcItemViewHolder itemHolder, int position, Device device) {
        super.onBindItemViewHolder(itemHolder, position, device);
        String name = (String) MapUtils.get(device.getInfo(), DinConst.INFO_NAME, "");
        if (TextUtils.isEmpty(name)
                || (DEFAULT_NAME.equals(name) && !TextUtils.isEmpty(cacheName))) {
            name = cacheName;
        }
        itemHolder.mTvIpcName.setText(name);
        //  状态
        itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        itemHolder.mIvWifiRssi.setVisibility(View.GONE);
        itemHolder.setDotVisible(false);

        final boolean isV015 = DsCamUtils.isDsCamV015Device(device);

        if (!device.getFlagLoaded()) {
            itemHolder.mTvBattery.clearAnimation();
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_connecting));
            itemHolder.mTvBattery.setCompoundDrawablePadding(DensityUtils.dp2px(mMainActivity, 3));
            if (itemHolder.getCameraVideoView() != null) {
                itemHolder.getCameraVideoView().showLoading();
            }
        } else if (DsCamUtils.isDeviceConnecting(device)
                || (DsCamUtils.isDeviceConnected(device)
                && (DsCamUtils.getDeviceBattery(device) < 0) && !isV015)) {
            itemHolder.mTvBattery.clearAnimation();
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_connecting));
            itemHolder.mTvBattery.setCompoundDrawablePadding(DensityUtils.dp2px(mMainActivity, 3));
            if (itemHolder.getCameraVideoView() != null) {
                itemHolder.getCameraVideoView().showLoading();
            }

        } else if (DsCamUtils.isDeviceConnected(device)) {
            itemHolder.setDotVisible(!DsCamUtils.isIpcTimezoneSynced(device));
            itemHolder.mTvBattery.clearAnimation();

//             电量显示
            if (DsCamUtils.getDeviceIsCharge(device)) {
                if (DsCamUtils.getDeviceBattery(device) >= 0) {
                    itemHolder.mTvBattery.setLocalText("");
                    itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(R.drawable.icon_plugin_list_battery_ac, 0, 0, 0);
                    itemHolder.mTvBattery.setCompoundDrawablePadding(0);
                } else {
                    itemHolder.mTvBattery.setLocalText("");
                    itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(R.drawable.icon_plugin_list_status_loading, 0, 0, 0);
                    itemHolder.mTvBattery.setCompoundDrawablePadding(0);
                    itemHolder.mTvBattery.startAnimation(AnimationUtils.loadAnimation(mMainActivity, R.anim.rotation));
                }
            } else {
                if (DsCamUtils.getDeviceBattery(device) >= 0) {
                    itemHolder.mTvBattery.setLocalText("");
                    itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(getBatteryIcon(DsCamUtils.getDeviceBattery(device)), 0, 0, 0);
                    itemHolder.mTvBattery.setCompoundDrawablePadding(0);
                } else {
                    itemHolder.mTvBattery.setLocalText("");
                    if (!isV015) {
                        itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(R.drawable.icon_plugin_list_status_loading, 0, 0, 0);
                        itemHolder.mTvBattery.setCompoundDrawablePadding(0);
                        itemHolder.mTvBattery.startAnimation(AnimationUtils.loadAnimation(mMainActivity, R.anim.rotation));
                    }
                }
            }

            if (itemHolder.getCameraVideoView() != null) {
                if (checkNeedUpgrade(device, false)) {
                    itemHolder.getCameraVideoView().showError();
                    itemHolder.getCameraVideoView().getErrorIcon().setImageResource(R.drawable.icon_ipc_updating);
                } else {
                    itemHolder.getCameraVideoView().showPlay();
                }
            }
            // WIFI 信号
            itemHolder.mIvWifiRssi.setVisibility(View.VISIBLE);
            itemHolder.mIvWifiRssi.setImageResource(DsCamUtils.getDeviceWifiRssiIconResId(device));
        } else {
            itemHolder.mTvBattery.clearAnimation();
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_offline));
            itemHolder.mTvBattery.setCompoundDrawablePadding(DensityUtils.dp2px(mMainActivity, 3));
            itemHolder.mIvWifiRssi.setVisibility(View.GONE);
            if (itemHolder.getCameraVideoView() != null) {
                itemHolder.getCameraVideoView().showError();
                itemHolder.getCameraVideoView().getErrorIcon().setImageResource(R.drawable.icon_ipc_offline);
            }
        }

        itemHolder.mTvBattery.setVisibility(showStateIcon ? View.VISIBLE : View.GONE);
        if (showStateIcon && itemHolder.mIvWifiRssi.getVisibility() == View.VISIBLE) {
            itemHolder.mIvWifiRssi.setVisibility(View.VISIBLE);
        } else {
            itemHolder.mIvWifiRssi.setVisibility(View.GONE);
        }

        itemHolder.mIvMore.setOnClickListener(v -> {
            if (DsCamUtils.isDeviceConnecting(device)) {
                return;
            }
            mMainActivity.addCommonFragment(DsCamIPCSettingFragment.newInstance(device.getId()));
        });

        //  预览图,helio里面做截图保存，不在dinsdk里面做
        final String snapshotPath = DeviceHelper.getString(device, HeartLaiConstants.ATTR_SNAPSHOT, "");
        if (!TextUtils.isEmpty(snapshotPath)) {
            File file = new File(snapshotPath);
            if (file.exists()) {
                itemHolder.getCameraVideoView().setCoverImageUri(Uri.fromFile(file));
            } else {
                itemHolder.getCameraVideoView().setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
            }
        } else {
            itemHolder.getCameraVideoView().setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
        }
    }

    @Override
    public void onPlayIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {
        super.onPlayIconClick(device, position, videoViewRoot, parent);
        if (holder.isEditMode()) {
            return;
        }
        if (checkNeedUpgrade(device) || TextUtils.isEmpty(DeviceHelper.getString(device, "chip", ""))) {
            return;
        }
        goFullplayPage(device);
    }

    @Override
    public void onErrorIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {
        super.onErrorIconClick(device, position, videoViewRoot, parent);
        if (holder.isEditMode()) {
            return;
        }

        if (DsCamUtils.isDeviceConnected(device)) {
            if (checkNeedUpgrade(device) || TextUtils.isEmpty(DeviceHelper.getString(device, "chip", ""))) {
                return;
            }
            goFullplayPage(device);
        } else if (DsCamUtils.isDeviceConnecting(device)) {
            // Do Nothing
        } else {
            if (HomeManager.getInstance().isAdmin()) {
                showDeviceOfflineDialog(device);
            }
        }
    }

    @Override
    public void onFullscreenIconClick(Device device, int position, CameraVideoView videoView, View parent) {
        super.onFullscreenIconClick(device, position, videoView, parent);
        DDLog.i(TAG, "onFullscreenIconClick, position: " + position);
    }

    private int getBatteryIcon(int battery) {
        if (battery < 0) {
            return 0;
        }
        if (battery > 60) {
            return R.drawable.icon_plugin_list_battery_full;
        } else if (battery > 20) {
            return R.drawable.icon_plugin_list_battery_half;
        } else {
            return R.drawable.icon_plugin_list_battery_low;
        }
    }

    private boolean checkNeedUpgrade(Device device, boolean showDialog) {
        if (DsCamUtils.isDeviceConnected(device)) {
            int firmwareUpgradeState = DsCamUpgradeManager.getInstance().getFirmwareUpgradeState(device.getId());
            if (firmwareUpgradeState >= DsCamUpgradeManager.IPC_FIRMWARE_UPGRADE) {
                if (showDialog && HomeManager.getInstance().isAdmin()) {
                    showUpgradeDialog(device, firmwareUpgradeState == DsCamUpgradeManager.IPC_FIRMWARE_FORCE_UPGRADE);
                }
                return true;
            }
        }
        return false;
    }

    private boolean checkNeedUpgrade(Device device) {
        return checkNeedUpgrade(device, true);
    }

    private void showUpgradeDialog(Device device, boolean isForce) {
        String content = Local.s(mMainActivity.getResources().getString(isForce ? R.string.ipc_upgrade_dialog_force : R.string.ipc_upgrade_dialog_nor))
                .replace("#plugin", DeviceHelper.getString(device, "name", " "));
        AlertDialog.createBuilder(mMainActivity)
                .setContent(content)
                .setOk(mMainActivity.getString(R.string.update))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        mMainActivity.addCommonFragment(DsCamUpgradeFragment.newInstance(device.getId(), true));
                    }
                })
                .setCancel(mMainActivity.getString(R.string.ignore))
                .setCancelListener(new AlertDialog.AlertCancelClickCallback() {
                    @Override
                    public void onClick() {
                        if (!isForce) {
                            DsCamUpgradeManager.getInstance().ignoreFirmwarVersion(device);
                            EventBus.getDefault().post(new DsCamStatusChange(device.getId()));
                            EventBus.getDefault().post(new IPCListUpdateEvent());
                        }
                    }
                }).preBuilder().show();

    }

    public void showDeviceOfflineDialog(Device device) {
        if (ActivityController.getInstance().getFragment(DsCamUpgradeFragment.class) != null) {
            DDLog.w(TAG, "showDeviceOfflineDialog: ipc升级中不弹离线弹窗");
            return;
        }
        AlertDialogV2 offlineDialog = AlertDialogV2.createBuilder(mMainActivity)
                .setContent(mMainActivity.getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(mMainActivity.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(mMainActivity.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(mMainActivity.getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        IPCManager.getInstance().connectDevice(device, true);
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        EventBus.getDefault().post(new DsCamNetWorkSetting(device.getId()));
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    private void goFullplayPage(Device device) {
        mMainActivity.setNotNeedToLogin(false);

        final ArrayList<String> params = new ArrayList<>();
        params.add(device.getId());
        DsCamMultiFullPlayActivity.start(mMainActivity, params);
    }


}
