package com.dinsafer.module.settting.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.BaseAdapter;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SwitchBotItemBinding;
import com.dinsafer.model.SwitchBotEntry;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.ArrayList;

import androidx.databinding.DataBindingUtil;

/**
 * Created by LT on 2019/3/22.
 */
public class SwitchBotAdapter extends BaseAdapter {
    ArrayList<SwitchBotEntry> mData;
    private final String TAG = "SwitchBotAdapter bot";
    private final float EDITING_ALPHA = 0.5f;


    private static final int COLUMU = 2;

    @Override
    public int getCount() {
        if (mData.size() % COLUMU != 0) {
            return mData.size() + (COLUMU - mData.size() % COLUMU);
        }
        return mData.size();
    }

    @Override
    public Object getItem(int position) {
        return mData.get(position);
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(parent.getContext()).inflate(R.layout.switch_bot_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        holder.binding.switchBotByPress.clearAnimation();
        holder.binding.switchBotOn.clearAnimation();
        holder.binding.switchBotOff.clearAnimation();

        if (position >= mData.size()) {
            holder.binding.layoutSwitchBot.setVisibility(View.INVISIBLE);
            holder.binding.switchBotName.setVisibility(View.INVISIBLE);
            holder.binding.btnCollect.setVisibility(View.INVISIBLE);
            holder.binding.imgEdit.setVisibility(View.GONE);
            return convertView;
        } else {
            holder.binding.layoutSwitchBot.setVisibility(View.VISIBLE);
            holder.binding.switchBotName.setVisibility(View.VISIBLE);
            holder.binding.btnCollect.setVisibility(View.VISIBLE);
        }

        SwitchBotEntry switchBotEntry = mData.get(position);
        if (TextUtils.isEmpty(switchBotEntry.getName())) {
            holder.binding.switchBotName.setText(Local.s(parent.getContext().getResources().getString(R.string.switch_bot)));
        } else {
            holder.binding.switchBotName.setText(switchBotEntry.getName());
        }
        if (switchBotEntry.isNoStatus()) {
//            还没获取到状态
            holder.binding.layoutSwitchBot.setVisibility(View.VISIBLE);
            holder.binding.switchBotByPress.setVisibility(View.VISIBLE);
            holder.binding.switchBotName.setVisibility(View.VISIBLE);
            holder.binding.btnCollect.setVisibility(View.VISIBLE);

            holder.binding.switchBotOnOffOffline.setVisibility(View.INVISIBLE);
            holder.binding.switchBotOn.setVisibility(View.INVISIBLE);
            holder.binding.switchBotOff.setVisibility(View.INVISIBLE);
            holder.binding.imgEdit.setVisibility(View.GONE);
            holder.binding.switchBotByPress.setImageResource(R.drawable.icon_switchbot_returning_status);
            setEnableControlWithCollect(holder, false);
            return convertView;
        }

//        是否收藏
        if (switchBotEntry.isCollected()) {
            holder.binding.btnCollect.setImageResource(R.drawable.icon_cell_switchbot_collect_sel);
        } else {
            holder.binding.btnCollect.setImageResource(R.drawable.icon_cell_switchbot_collect_nor);
        }
        holder.binding.btnCollect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (collectListener != null) {
                    DDLog.d(TAG, "点击了收藏");
                    collectListener.onCollectChange(position);
                }
            }
        });

//        按钮数变化
        if (switchBotEntry.isOneBtn()) {
            //只显示一个按钮
            holder.binding.switchBotByPress.setVisibility(View.VISIBLE);
            holder.binding.switchBotOn.setVisibility(View.INVISIBLE);
            holder.binding.switchBotOff.setVisibility(View.INVISIBLE);
        } else {
            //显示两个按钮
            holder.binding.switchBotByPress.setVisibility(View.INVISIBLE);
            holder.binding.switchBotOn.setVisibility(View.VISIBLE);
            holder.binding.switchBotOff.setVisibility(View.VISIBLE);
        }

//        是否离线
        if (switchBotEntry.isOffline()) {
            if (switchBotEntry.isOneBtn()) {
                holder.binding.switchBotOnOffOffline.setVisibility(View.INVISIBLE);
                holder.binding.switchBotByPress.setImageResource(R.drawable.icon_switchbot_offline);
            } else {
                holder.binding.switchBotOnOffOffline.setVisibility(View.VISIBLE);
                holder.binding.switchBotOn.setImageResource(R.drawable.icon_switchbot_on_offline);
                holder.binding.switchBotOff.setImageResource(R.drawable.icon_switchbot_off_offline);
            }

            setEnableControl(holder, false);
        } else {
            holder.binding.switchBotOnOffOffline.setVisibility(View.INVISIBLE);
            if (switchBotEntry.isLoading()) {
                setEnableControl(holder, false);

                Animation operatingAnim = AnimationUtils.loadAnimation(parent.getContext(), R.anim.rotation);
                LinearInterpolator lin = new LinearInterpolator();
                operatingAnim.setInterpolator(lin);
                if (!switchBotEntry.isOneBtn()) {
                    if (switchBotEntry.isLoadingOn()) {

                        holder.binding.switchBotOn.setImageResource(R.drawable.icon_main_btn_loading);
                        holder.binding.switchBotOn.startAnimation(operatingAnim);

                        holder.binding.switchBotOff.setImageResource(switchBotEntry.isOn() ? R.drawable.icon_switchbot_off_nor : R.drawable.icon_switchbot_off_sel);
                    } else {

                        holder.binding.switchBotOff.setImageResource(R.drawable.icon_main_btn_loading);
                        holder.binding.switchBotOff.startAnimation(operatingAnim);

                        holder.binding.switchBotOn.setImageResource(switchBotEntry.isOn() ? R.drawable.icon_switchbot_on_sel : R.drawable.icon_switchbot_on_nor);
                    }
                } else {
                    holder.binding.switchBotByPress.setImageResource(R.drawable.icon_main_btn_loading);
                    holder.binding.switchBotByPress.startAnimation(operatingAnim);
                }


            } else {
                if (!switchBotEntry.isOneBtn()) {
                    holder.binding.switchBotOn.setImageResource(switchBotEntry.isOn() ? R.drawable.icon_switchbot_on_sel : R.drawable.icon_switchbot_on_nor);
                    holder.binding.switchBotOff.setImageResource(switchBotEntry.isOn() ? R.drawable.icon_switchbot_off_nor : R.drawable.icon_switchbot_off_sel);
                } else {
                    holder.binding.switchBotByPress.setImageResource(R.drawable.icon_switchbot);
                }
                setEnableControl(holder, true);
                holder.binding.switchBotByPress.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        DDLog.d(TAG, "点击了一个按钮的开关");
                        if (controlListener != null) {

                            ImageView view = (ImageView) v;
                            view.setImageResource(R.drawable.icon_main_btn_loading);
                            Animation operatingAnim = AnimationUtils.loadAnimation(parent.getContext(), R.anim.rotation);
                            LinearInterpolator lin = new LinearInterpolator();
                            operatingAnim.setInterpolator(lin);
                            view.startAnimation(operatingAnim);
                            controlListener.onControl(position, !switchBotEntry.isOn());

                        }
                    }
                });
                holder.binding.switchBotOn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        DDLog.d(TAG, "点击了两按钮中的on");
                        if (controlListener != null) {
                            ImageView view = (ImageView) v;
                            view.setImageResource(R.drawable.icon_main_btn_loading);
                            Animation operatingAnim = AnimationUtils.loadAnimation(parent.getContext(), R.anim.rotation);
                            LinearInterpolator lin = new LinearInterpolator();
                            operatingAnim.setInterpolator(lin);
                            view.startAnimation(operatingAnim);

                            controlListener.onControl(position, true);
                        }
                    }
                });
                holder.binding.switchBotOff.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        DDLog.d(TAG, "点击了两按钮中的off");
                        if (controlListener != null) {
                            ImageView view = (ImageView) v;
                            view.setImageResource(R.drawable.icon_main_btn_loading);
                            Animation operatingAnim = AnimationUtils.loadAnimation(parent.getContext(), R.anim.rotation);
                            LinearInterpolator lin = new LinearInterpolator();
                            operatingAnim.setInterpolator(lin);
                            view.startAnimation(operatingAnim);
                            controlListener.onControl(position, false);
                        }
                    }
                });
            }
        }

        holder.binding.btnCollect.setClickable(true);
        if (isEditMode) {
//            正在编辑
            setEditingStatus(holder, position, true);
            return convertView;
        }
        setEditingStatus(holder, position, false);
        return convertView;
    }

    public SwitchBotAdapter(ArrayList<SwitchBotEntry> mData) {
        this.mData = mData;
    }

    public class ViewHolder {
        SwitchBotItemBinding binding;

        public ViewHolder(View itemView) {
            binding = DataBindingUtil.bind(itemView);
        }
    }

    private void setEditingStatus(ViewHolder holder, int position, boolean isEditing) {
        if (isEditing) {
            //所有都变50透明
            // FIXME: 2019/3/28 bot 在控制时，编辑了的话，转转转也变百分之50？
            holder.binding.btnCollect.setAlpha(EDITING_ALPHA);
            holder.binding.switchBotName.setAlpha(EDITING_ALPHA);
            holder.binding.layoutSwitchBot.setAlpha(EDITING_ALPHA);

            if (mData.get(position).isCollected()) {
                holder.binding.imgEdit.setVisibility(View.VISIBLE);
//                holder.binding.layoutEdit.setClickable(true);
//                holder.binding.layoutEdit.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        DDLog.d(TAG, "点击了编辑");
//                        // LT-OS: bot item点击了编辑
//                    }
//                });
            } else {
                holder.binding.imgEdit.setVisibility(View.GONE);
            }
            setEnableControlWithCollect(holder, false);
            return;
        }
        holder.binding.btnCollect.setAlpha(1.0f);
        holder.binding.switchBotName.setAlpha(1.0f);
        holder.binding.layoutSwitchBot.setAlpha(1.0f);
        holder.binding.imgEdit.setVisibility(View.GONE);

        /**
         * 在线，loading状态一样不能点击，所以这里不能修改
         */
//        setEnableControlWithCollect(holder, true);
    }

    public interface EditListener {
        void onEdit(int pos);
    }

    private EditListener editListener;

    public void setEditListener(EditListener editListener) {
        this.editListener = editListener;
    }

    public interface CollectListener {
        void onCollectChange(int pos);
    }

    private CollectListener collectListener;

    public void setCollectListener(CollectListener collectListener) {
        this.collectListener = collectListener;
    }

    public interface ControlListener {
        void onControl(int pos, boolean isOn);
    }

    private ControlListener controlListener;

    public void setControlListener(ControlListener controlListener) {
        this.controlListener = controlListener;
    }


    public void remove(int index) {
        if (mData != null && index >= 0 && index < mData.size()) {
            mData.remove(index);
            notifyDataSetChanged();
        }
    }

    public void changeName(int index, String name) {
        mData.get(index).setName(name);
        notifyDataSetChanged();
    }

    private boolean isEditMode = false;

    public boolean isEditMode() {
        return isEditMode;
    }

    public void setEditMode(boolean editMode) {
        isEditMode = editMode;
    }

    private void setEnableControlWithCollect(ViewHolder holder, boolean isEnable) {
        holder.binding.btnCollect.setClickable(isEnable);
        holder.binding.switchBotOn.setClickable(isEnable);
        holder.binding.switchBotOff.setClickable(isEnable);
        holder.binding.switchBotByPress.setClickable(isEnable);
    }

    private void setEnableControl(ViewHolder holder, boolean isEnable) {
        holder.binding.switchBotOn.setClickable(isEnable);
        holder.binding.switchBotOff.setClickable(isEnable);
        holder.binding.switchBotByPress.setClickable(isEnable);
    }

}
