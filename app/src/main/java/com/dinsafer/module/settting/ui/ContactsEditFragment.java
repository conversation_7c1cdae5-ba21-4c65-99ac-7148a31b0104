package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ContactsEditLayoutBinding;
import com.dinsafer.model.ContactItem;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.interfaces.IUpdataList;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.FlowLayout;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ContactsEditFragment extends MyBaseFragment<ContactsEditLayoutBinding> implements ChoosePhoneZoneFragment.IChoosePhoneZoneCallBack {
    private boolean isAdd;

    private ContactItem member, mSourceData;

//    private boolean isSms, isCall;

    private IUpdataList callBack;

    private int mTabSize;

    public static ContactsEditFragment newInstance(boolean isAdd, ContactItem data) {
        ContactsEditFragment contactsEditFragment = new ContactsEditFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean("isAdd", isAdd);
        bundle.putSerializable("data", data);
        contactsEditFragment.setArguments(bundle);
        return contactsEditFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.contacts_edit_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBarRightIcon.setOnClickListener(v -> clickMore());
        mBinding.btnSave.setOnClickListener(v -> toSave());
    }

    public void toSave() {
        final String inputPhone = mBinding.changePhone.getText().toString().trim();
        final String inputName = mBinding.contactEditName.getText().toString().trim();

        if (TextUtils.isEmpty(inputPhone)) {
            showPhoneErrorBg(true);
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.phone_format_illegal));
            return;
        }
        showPhoneErrorBg(false);

        if (!TextUtils.isEmpty(inputName) && !RegxUtil.isLegalName(inputName)) {
            showNameErrorBg(true);
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }
        showNameErrorBg(false);

        showTimeOutLoadinFramgment();
        if (isAdd) {
            toAddContact();
        } else {
            toEditContact();
        }
    }

    private void showPhoneErrorBg(final boolean error) {
        mBinding.contactEditPhone.setBackgroundColor(getContext().getResources().getColor(error ? R.color.color_bg_input_error : R.color.transparent));
    }

    private void showNameErrorBg(final boolean error) {
        mBinding.rlContactEditName.setBackgroundColor(getContext().getResources().getColor(error ? R.color.color_bg_input_error : R.color.transparent));
    }

    //    @OnClick(R.id.contact_edit_delete)
    public void toDelete() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(Local.s(getResources().getString(R.string.smart_plugs_list_delete_yes)))
                .setCancel(Local.s(getResources().getString(R.string.smart_plugs_list_delete_no)))
                .setContent(Local.s(getResources().getString(R.string.smart_plugs_list_delete_confirm)))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgment();
                        DinSDK.getHomeInstance().removeHomeContact(
                                HomeManager.getInstance().getCurrentHome().getHomeID(),
                                mSourceData.getContactid(), new IDefaultCallBack() {
                                    @Override
                                    public void onSuccess() {
                                        closeLoadingFragment();
                                        if (callBack != null)
                                            callBack.deletItem(mSourceData);
                                        removeSelf();
                                    }

                                    @Override
                                    public void onError(int i, String s) {
                                        closeLoadingFragment();
                                        showErrorToast();
                                    }
                                });
                    }
                })
                .preBuilder()
                .show();
    }

    private boolean checkInput() {
        return !TextUtils.isEmpty(mBinding.changePhone.getText());
    }

    private void toAddContact() {
        String phone = "";
        if (Local.s("Default").equals(mBinding.contactEditPhoneZone.getText().toString())) {
            phone = mBinding.changePhone.getText().toString();
        } else {
            phone = mBinding.contactEditPhoneZone.getText().toString().split(" ")[0] + " " + mBinding.changePhone.getText().toString();

        }

        AddContactParams params = new AddContactParams.Builder()
                .setSms(member.isSms())
                .setSms_sos(member.isSms_sos())
                .setSms_info(member.isSms_info())
                .setSms_sys(member.isSms_sys())
                .addContact(new AddContactParams.ContactBean(mBinding.contactEditName.getText().toString(),
                        phone))
                .createAddContactParams();

        DinSDK.getHomeInstance().newHomeContact(HomeManager.getInstance().getCurrentHome().getHomeID(),
                params, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        closeLoadingFragment();
                        if (callBack != null) {
                            callBack.reload();
                        }
                        removeSelf();
                    }

                    @Override
                    public void onError(int i, String s) {
                        DDLog.e(TAG, "Error: i:" + i + "， s:" + s);
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

    private void toEditContact() {
        String phone = "";
        if (Local.s("Default").equals(mBinding.contactEditPhoneZone.getText().toString())) {
            phone = mBinding.changePhone.getText().toString();
        } else {
            phone = mBinding.contactEditPhoneZone.getText().toString().split(" ")[0] + " " + mBinding.changePhone.getText().toString();
        }
        member.setPhone(phone);
        member.setName(mBinding.contactEditName.getText().toString());

        HomeContact contact = new HomeContact();
        contact.setContact_id(member.getContactid());
        contact.setSms(member.isSms());
        contact.setSms_info(member.isSms_info());
        contact.setSms_sos(member.isSms_sos());
        contact.setSms_sys(member.isSms_sys());
        contact.setPhone(member.getPhone());
        contact.setName(member.getName());

        DinSDK.getHomeInstance().updateHomeContact(HomeManager.getInstance().getCurrentHome().getHomeID(),
                contact, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        closeLoadingFragment();
                        toSetData();
                        removeSelf();
                    }

                    @Override
                    public void onError(int i, String s) {
                        DDLog.e(TAG, "Error: i:" + i + "， s:" + s);
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

    @Override
    public void initData() {
        super.initData();
        mTabSize = getMainActivity().getResources().getDimensionPixelSize(R.dimen.textSmallSize);
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.contact_header_other));
        mBinding.contactEditPhoneZone.setHint(Local.s(getResources().getString(R.string.change_phone_zone_hint)));
        mBinding.changePhone.setHint(Local.s(getResources().getString(R.string.change_phone_hint)));
        mBinding.contactEditName.setHint(Local.s(getResources().getString(R.string.contact_edit_name)));
        mBinding.permissionSmsText.setLocalText(getResources().getString(R.string.contact_push_sms));
//        mBinding.contactEditDelete.setLocalText(getResources().getString(R.string.contact_edit_delete));
        mBinding.btnSave.setLocalText(getResources().getString(R.string.save));
        mBinding.permissionPhoneText.setLocalText(getResources().getString(R.string.contact_push_phone));
        mBinding.permissionSmsDetailText.setLocalText(getResources().getString(R.string.contact_detail_text));
        mBinding.permissionPhoneDetailText.setLocalText(getResources().getString(R.string.contact_detail_text));
        mBinding.permissionNoSimHint.setLocalText(getResources().getString(R.string.permission_no_sim_hint));


        mSourceData = (ContactItem) getArguments().getSerializable("data");
        member = new ContactItem();
        if (mSourceData != null) {
            member.setCall(mSourceData.isCall());
            member.setCall_info(mSourceData.isCall_info());
            member.setCall_sys(mSourceData.isCall_sys());
            member.setCall_sos(mSourceData.isCall_sos());
            member.setContactid(mSourceData.getContactid());
            member.setSms(mSourceData.isSms());
            member.setSms_sys(mSourceData.isSms_sys());
            member.setSms_sos(mSourceData.isSms_sos());
            member.setSms_info(mSourceData.isSms_info());
            member.setPush(mSourceData.isPush());
            member.setPush_sos(mSourceData.isPush_sos());
            member.setPush_info(mSourceData.isPush_info());
            member.setPush_sys(mSourceData.isPush_sys());
            member.setName(mSourceData.getName());
            member.setPermission(mSourceData.getPermission());
            member.setPhone(mSourceData.getPhone());
            member.setPhoto(mSourceData.getPhoto());
            member.setTime(mSourceData.getTime());
            member.setType(mSourceData.getType());
            member.setUid(mSourceData.getUid());
        } else {
            member.setCall(false);
            member.setCall_info(false);
            member.setCall_sys(false);
            member.setCall_sos(false);
            member.setSms(true);
            member.setSms_sys(false);
            member.setSms_sos(true);
            member.setSms_info(false);
        }
        mBinding.permissionNoSimHint.setVisibility(View.GONE);

//        if (TextUtils.isEmpty(member.getPhone())) {
//            permissionNoSimHint.setVisibility(View.VISIBLE);
//        } else {
//            permissionNoSimHint.setVisibility(View.GONE);
//        }

        setSmsDetailMenuVisible(member.isSms());
        setPhoneDetailMenuVisible(member.isCall());

        updateTabTextUI();

        mBinding.permissionPhoneDetailText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getDelegateActivity().addCommonFragment(ChoosePushFragment.newInstance(member, ChoosePushFragment.PHONE));
            }
        });

        mBinding.permissionSmsDetailText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getDelegateActivity().addCommonFragment(ChoosePushFragment.newInstance(member, ChoosePushFragment.SMS));
            }
        });

        mBinding.ivContact.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (DDSystemUtil.isMarshmallow() && ContextCompat.checkSelfPermission(getActivity(),
                        Manifest.permission.READ_CONTACTS)
                        != PackageManager.PERMISSION_GRANTED) {
                    requestReadContactPermisiions();
                    return;
                }
                ContactsPickerFragment contactsPickerFragment = ContactsPickerFragment.newInstance();
                contactsPickerFragment.setCallBack(new ContactsPickerFragment.IContactCallBack() {
                    @Override
                    public void onSelect(ArrayList<HashMap<String, String>> selectList) {
                        if (selectList != null && selectList.size() > 0) {
                            HashMap<String, String> stringStringHashMap = selectList.get(0);
                            mBinding.changePhone.setText(stringStringHashMap.get("phone"));
                            mBinding.contactEditName.setText(stringStringHashMap.get("name"));
                        }
                    }
                });
                getDelegateActivity().addCommonFragment(contactsPickerFragment);
            }
        });

        isAdd = getArguments().getBoolean("isAdd");

        if (isAdd) {
            mBinding.commonBarRightIcon.setVisibility(View.GONE);
            mBinding.contactEditPhoneZone.setText(TextUtils.isEmpty(ChoosePhoneZoneFragment.getCachePhoneZone()) ? APIKey.DEFAULT_PHONE_TEXT : ChoosePhoneZoneFragment.getCachePhoneZone());
            mBinding.ivContact.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBarRightIcon.setVisibility(View.VISIBLE);
            if (mSourceData != null) {
                String phone = mSourceData.getPhone();
                if (phone.contains(" ")) {
                    String phones[] = phone.split(" ");

                    for (int i = 0; i < ChoosePhoneZoneFragment.countryCodes.length; i++) {
                        if (phones[0].equals(ChoosePhoneZoneFragment.countryCodes[i])) {
                            mBinding.contactEditPhoneZone.setText(phones[0] + " " + ChoosePhoneZoneFragment.countryNames[i]);
                            break;
                        }
                    }
                    int i = phone.indexOf(" ");
                    String realPhone = phone.substring(i).replace(" ", "");
                    mBinding.changePhone.setText(realPhone);
                } else {
                    mBinding.contactEditPhoneZone.setText(Local.s("Default"));
                    mBinding.changePhone.setText(mSourceData.getPhone());
                }
                mBinding.contactEditName.setText(mSourceData.getName());
            }
        }

        mBinding.permissionSmsSwitch.setOn(member.isSms());
        mBinding.permissionPhoneSwitch.setOn(member.isCall());


        mBinding.permissionSmsSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                member.setSms(isOn);
                setSmsDetailMenuVisible(isOn);
            }
        });

        mBinding.permissionPhoneSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                member.setCall(isOn);
                setPhoneDetailMenuVisible(isOn);
            }
        });

        mBinding.contactEditPhoneZone.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                    toChoosePhoneZone();
                }
                return true;
            }
        });
    }


    public void toChoosePhoneZone() {

        String phone;
        if (Local.s("Default").equals(mBinding.contactEditPhoneZone.getText().toString())) {
            phone = mBinding.contactEditPhoneZone.getText().toString();
        } else {
            phone = mBinding.contactEditPhoneZone.getText().toString().split(" ")[0];

        }
        ChoosePhoneZoneFragment fragment = ChoosePhoneZoneFragment.newInstance(phone
                + ChoosePhoneZoneFragment.SPACE + phoneZoneName);
        fragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(fragment);
    }

    public void clickMore() {
        showMoreActionDialog();
    }

    private void setSmsDetailMenuVisible(boolean isShow) {
        if (isShow) {
            mBinding.permissionSmsMenu.setVisibility(View.VISIBLE);
        } else {
            mBinding.permissionSmsMenu.setVisibility(View.GONE);
        }
    }

    private void setPhoneDetailMenuVisible(boolean isShow) {
        if (isShow) {
            mBinding.permissionPhoneMenu.setVisibility(View.VISIBLE);
        } else {
            mBinding.permissionPhoneMenu.setVisibility(View.GONE);
        }
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        updateTabTextUI();
    }

    private void updateTabTextUI() {
        mBinding.permissionSmsFlowlayout.removeAllViews();
        mBinding.permissionPhoneFlowlayout.removeAllViews();

        if (member.isSms_info() && member.isSms_sos() && member.isSms_sys()) {
            mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_all), true));
        } else if (!member.isSms_info() && !member.isSms_sos() && !member.isSms_sys()) {
            mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_empty), false));
        } else {
            if (member.isSms_info()) {
                mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_info), false));
            }

            if (member.isSms_sys()) {
                mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sys), false));
            }

            if (member.isSms_sos()) {
                mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sos), false));
            }
        }

        if (member.isCall_info() && member.isCall_sos() && member.isCall_sys()) {
            mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_all), true));
        } else if (!member.isCall_info() && !member.isCall_sos() && !member.isCall_sys()) {
            mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_empty), false));
        } else {

            if (member.isCall_info()) {
                mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_info), false));
            }

            if (member.isCall_sys()) {
                mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sys), false));
            }

            if (member.isCall_sos()) {
                mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sos), false));
            }
        }
    }

    public TextView createTabView(final String text, boolean isBlue) {
        TextView tv = new TextView(getMainActivity());
        tv.setText(Local.s(text));
        tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTabSize);
        if (isBlue) {
            tv.setBackgroundResource(R.drawable.dd_tab_corner_blue_bg);
            tv.setTextColor(getResources().getColor(R.color.colorContacTabText));
        } else {
            tv.setBackgroundResource(R.drawable.dd_tab_corner_gray_bg);
            tv.setTextColor(getResources().getColor(R.color.colorContacTabText));
        }
        FlowLayout.LayoutParams lap = new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT,
                FlowLayout.LayoutParams.WRAP_CONTENT);
        final int margin = 30;
        lap.setMargins(0, 10, 0, 10);
        tv.setPadding(margin, margin, margin, margin);
        lap.bottomMargin = margin;
        lap.topMargin = margin;
        lap.leftMargin = margin + 10;
        lap.rightMargin = margin + 10;
        tv.setLayoutParams(lap);
        return tv;
    }

    private void toSetData() {
        mSourceData.setCall(member.isCall());
        mSourceData.setCall_info(member.isCall_info());
        mSourceData.setCall_sys(member.isCall_sys());
        mSourceData.setCall_sos(member.isCall_sos());
        mSourceData.setContactid(member.getContactid());
        mSourceData.setSms(member.isSms());
        mSourceData.setSms_sys(member.isSms_sys());
        mSourceData.setSms_sos(member.isSms_sos());
        mSourceData.setSms_info(member.isSms_info());
        mSourceData.setName(member.getName());
        mSourceData.setPermission(member.getPermission());
        mSourceData.setPhone(member.getPhone());
        mSourceData.setPhoto(member.getPhoto());
        mSourceData.setTime(member.getTime());
        mSourceData.setType(member.getType());
        mSourceData.setUid(member.getUid());
        if (callBack != null)
            callBack.updata();
    }

    private String phoneZoneName = "";

    @Override
    public void onResult(String code, String name) {
        mBinding.contactEditPhoneZone.setText(code + " " + name);
        phoneZoneName = name;
    }

    public IUpdataList getCallBack() {
        return callBack;
    }

    public void setCallBack(IUpdataList callBack) {
        this.callBack = callBack;
    }

    public void showMoreActionDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.delete)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (0 == index) {
                            toDelete();
                        }
                    }
                }).show();
    }
}

