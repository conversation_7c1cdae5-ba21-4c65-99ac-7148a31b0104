package com.dinsafer.module.settting.ui.model;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemDeviceSettingMemberManageBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.ui.ContactsListFragment;
import com.dinsafer.module_home.bean.MemberAvatars;
import com.dinsafer.ui.BaseTextDrawable;
import com.dinsafer.ui.CircularView;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.util.ArrayList;
import java.util.List;

/**
 * 主机设置  --  成员管理
 */
public class DeviceSettingMemberManageModel extends BaseDeviceSettingPlugModel<ItemDeviceSettingMemberManageBinding> {

    private List<MemberAvatars.AvatarsBean> memberInfoList;

    public DeviceSettingMemberManageModel(BaseFragment baseFragment, boolean haveLoading, boolean isLoading) {
        super(baseFragment, baseFragment.getResources().getString(R.string.device_managent_member_setting),
                -1, -1, haveLoading, isLoading);
        if (null != HomeManager.getInstance().getMemberAvatars()) {
            setPlugCount(HomeManager.getInstance().getMemberAvatars().getTotal());
            memberInfoList = HomeManager.getInstance().getMemberAvatars().getAvatars();
        }

        if (null == memberInfoList) {
            memberInfoList = new ArrayList<>();
        }
        DDLog.d(TAG, "count: " + getPlugCount() + ", list: " + memberInfoList.toString());
    }

    @Override
    public void onDo(View v) {
        baseFragment.getDelegateActivity().addCommonFragment(ContactsListFragment.newInstance());
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_device_setting_member_manage;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemDeviceSettingMemberManageBinding itemDeviceSettingMemberManageBinding) {
        super.convert(holder, itemDeviceSettingMemberManageBinding);

        if (isHaveLoading() && isLoading()) {
            itemDeviceSettingMemberManageBinding.pbStateLoading.setVisibility(View.VISIBLE);
            itemDeviceSettingMemberManageBinding.rl.removeAllViews();
        } else {
            itemDeviceSettingMemberManageBinding.pbStateLoading.setVisibility(View.GONE);
            if (getPlugCount() > 0) {
                addMember(itemDeviceSettingMemberManageBinding, memberInfoList);
            }
        }
    }

    private void addMember(ItemDeviceSettingMemberManageBinding itemDeviceSettingMemberManageBinding,
                           List<MemberAvatars.AvatarsBean> member_info) {
        if (member_info == null || member_info.size() == 0
                || UserManager.getInstance().getUser() == null
                || TextUtils.isEmpty(UserManager.getInstance().getUser().getUid())) {
            return;
        }
        itemDeviceSettingMemberManageBinding.rl.removeAllViews();

        int size = Math.min(member_info.size(), 3);

        String avatar, uid;
        for (int i = 0; i < size; i++) {
            avatar = member_info.get(i).getAvatar();
            uid = member_info.get(i).getName();
            CircularView circularView = new CircularView(baseFragment.getContext());
            circularView.setHasBorder(true);
            circularView.setBorderColor(baseFragment.getResources().getColor(R.color.colorMemberItemAvatarBorder));
            circularView.setBorderWidth(DensityUtils.dp2px(baseFragment.getContext(), 2));

            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(DensityUtils.dp2px(baseFragment.getContext(),
                    24), DensityUtils.dp2px(baseFragment.getContext(), 24));
            layoutParams.gravity = Gravity.RIGHT;
            layoutParams.rightMargin = (int) (((float) 2 - i) * DensityUtils.dp2px(baseFragment.getContext(), 10));
            circularView.setLayoutParams(layoutParams);

            itemDeviceSettingMemberManageBinding.rl.addView(circularView);

            if (TextUtils.isEmpty(avatar)) {
                BaseTextDrawable avatar1 = DDImageUtil.getBaseTextDrawable(baseFragment.getDelegateActivity(), uid
                        , UserManager.getInstance().getUser().getUid().equals(uid));
                int w = circularView.getLayoutParams().height;
                circularView.setBaseTextDrawable(avatar1, w, w);
            } else {
                ImageLoader.getInstance().displayImage(APIKey.UPLOAD_SERVER_IP + avatar, circularView);
            }
        }

    }
}
