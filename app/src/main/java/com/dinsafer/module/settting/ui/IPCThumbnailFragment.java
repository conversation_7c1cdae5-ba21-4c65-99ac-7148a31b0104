package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.IPCData;
import com.dinsafer.model.IPCThumbnailClick;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.IPCThumbnailAdapter;
import com.dinsafer.ui.LocalTextView;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/8.
 */
public class IPCThumbnailFragment extends BaseFragment {

    LocalTextView commonBarTitle;
    ImageView commonBarBack;
    GridView ipcThumbnailGridview;

    IPCThumbnailAdapter adapter;

    private ArrayList<IPCData> data;

    public static IPCThumbnailFragment newInstance() {
        IPCThumbnailFragment ipcThumbnailFragment = new IPCThumbnailFragment();
        return ipcThumbnailFragment;
    }

    public ArrayList<IPCData> getData() {
        return data;
    }

    public void setData(ArrayList<IPCData> data) {
        this.data = data;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.ipc_thumbnail_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        ipcThumbnailGridview = rootView.findViewById(R.id.ipc_thumbnail_gridview);
    }


    @Override
    public void initData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_ip_camera));
        adapter = new IPCThumbnailAdapter(getDelegateActivity(), data);
        ipcThumbnailGridview.setAdapter(adapter);
        this.setBaseEnterAnim(AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in));
        this.setBaseOuterAnim(AnimationUtils.loadAnimation(getActivity(), R.anim.fade_out));

        ipcThumbnailGridview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                EventBus.getDefault().post(new IPCThumbnailClick(i));
                removeSelf();
            }
        });

    }

    public void close() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

}

