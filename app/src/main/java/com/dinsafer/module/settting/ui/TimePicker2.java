package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.TimerPicker2Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.WheelView;
import com.dinsafer.util.CommonDataUtil;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/11.
 */
public class TimePicker2 extends MyBaseFragment<TimerPicker2Binding> {

    private static final String TITLE = "title";
    private static final String SECONDS = "seconds";
    private static final String IS_SOUND_ENABLD = "isSoundEnable";

    private ArrayList<String> minuteList = new ArrayList();
    private ArrayList<String> secondList = new ArrayList();

    private String mTitle;
    private int seconds;
    private boolean isSoundEnable;

    private ITimePickerCallBack callBack;

    public static TimePicker2 newInstance(String title, int seconds, boolean isSoundEnable) {
        TimePicker2 picker = new TimePicker2();
        Bundle args = new Bundle();
        args.putString(TITLE, title);
        args.putInt(SECONDS, seconds);
        args.putBoolean(IS_SOUND_ENABLD, isSoundEnable);
        picker.setArguments(args);
        return picker;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.timer_picker2;
    }

    @Override
    public void initData() {
        super.initData();
        mTitle = getArguments().getString(TITLE);
        seconds = getArguments().getInt(SECONDS);
        isSoundEnable = getArguments().getBoolean(IS_SOUND_ENABLD);
        mBinding.commonBar.commonBarTitle.setLocalText(mTitle);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.timerPickerSave.setLocalText(getResources().getString(R.string.sos_message_dialog_save));
        mBinding.timerPickerSave.setOnClickListener(v -> toSave());
        mBinding.tvDeviceArmDisarmSound.setLocalText(getResources().getString(R.string.time_picker_countdown_voice_prompt));
        mBinding.switchDeviceArmDisarmSound.setOn(isSoundEnable);

        if (!CommonDataUtil.getInstance().isShowDeviceArmHomeArmSound()) {
            mBinding.tvDeviceArmDisarmSound.setVisibility(View.GONE);
            mBinding.rlDeviceArmDisarmSound.setVisibility(View.GONE);
            mBinding.lineDeviceArmDisarmSoundBottom.setVisibility(View.GONE);
            mBinding.lineDeviceArmDisarmSoundTop.setVisibility(View.GONE);
        }

        initWhellData();
        initWheel();

    }

    private void initWheel() {
        int min = seconds / 60;
        int second = seconds % 60;

        mBinding.timerPickerMin.setData(minuteList);
        mBinding.timerPickerMin.setDefault(minuteList.indexOf(min + "min"));

        mBinding.timerPickerSeconds.setData(secondList);
        mBinding.timerPickerSeconds.setDefault(secondList.indexOf(second + "s") == -1 ? 0 : secondList.indexOf(second + "s"));

    }

    private void initWhellData() {

        for (int i = 0; i < 10; i++) {
            minuteList.add(i + "min");
        }

        for (int i = 0; i <= 5; i++) {
            secondList.add((i * 10) + "s");
        }

    }

    public ITimePickerCallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(ITimePickerCallBack callBack) {
        this.callBack = callBack;
    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }


    public void toSave() {
        if (callBack != null) {
            int min = Integer.parseInt(mBinding.timerPickerMin.getSelectedText().substring(0, mBinding.timerPickerMin.getSelectedText().lastIndexOf("min")));
            int second = Integer.parseInt(mBinding.timerPickerSeconds.getSelectedText().substring(0, mBinding.timerPickerSeconds.getSelectedText().lastIndexOf("s")));
            callBack.getSelect(min * 60 + second, mBinding.switchDeviceArmDisarmSound.isOn());
            removeSelf();
        }
    }

    public interface ITimePickerCallBack extends Serializable {
        void getSelect(int seconds, boolean isSoundEnable);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        callBack = null;
    }
}
