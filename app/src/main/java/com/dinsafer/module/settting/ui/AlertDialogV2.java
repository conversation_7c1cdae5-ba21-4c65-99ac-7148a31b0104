package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;


/**
 * Created by rinfon on 15/6/26.
 */
public class AlertDialogV2 extends Dialog {

    int layoutRes;//布局文件

    Context mContext;

    LocalCustomButton mOk, mOk2, mCancel;

    private LocalTextView mContent;

    private boolean isCanCancel = true;

    private ImageView mContentImageView;

    public AlertDialogV2(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.alert_dialog_v2;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mContent = (LocalTextView) view.findViewById(R.id.alert_dialog_content);
        mContent.setMovementMethod(ScrollingMovementMethod.getInstance());
        mOk = (LocalCustomButton) view.findViewById(R.id.alert_dialog_ok);
        mOk2 = (LocalCustomButton) view.findViewById(R.id.alert_dialog_ok_v2);
        mCancel = (LocalCustomButton) view.findViewById(R.id.alert_dialog_cancel);
        mContentImageView = (ImageView) view.findViewById(R.id.alert_dialog_content_imageview);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (builder.cancelClick != null) {
                    builder.cancelClick.onClick();
                }
            }
        });

        mOk.setOnClickListener(view1 -> {
            if (builder.isAutoDismiss)
                dismiss();
            if (builder.okClick != null) {
                builder.okClick.onOkClick();
            }
        });

        mOk2.setOnClickListener(v -> {
            if (builder.isAutoDismiss)
                dismiss();
            if (builder.okClickV2 != null) {
                builder.okClickV2.onOkClick();
            }
        });

        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOk.setVisibility(View.VISIBLE);
        } else {
            mOk.setVisibility(View.GONE);
        }

        if (builder.isShowOK2) {
            mOk2.setLocalText(builder.mOkV2);
            mOk2.setVisibility(View.VISIBLE);
        } else {
            mOk2.setVisibility(View.GONE);
        }

        if (builder.isShowOKView) {
            mContentImageView.setVisibility(View.VISIBLE);
        } else {
            mContentImageView.setVisibility(View.GONE);
        }

        if (builder.getOkColor() != 0) {
            mOk.setTextColor(builder.getOkColor());
        }
        if (builder.isShowCancel) {
            mCancel.setLocalText(builder.mCancel);
            mCancel.setVisibility(View.VISIBLE);
        } else {
            mCancel.setVisibility(View.GONE);
        }

        mContent.setLocalText(builder.mContent);
        isCanCancel = builder.isCanCancel;


    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }


    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        if (isCanCancel)
            super.cancel();
    }

    public void setOKText(String ok) {
        mOk.setLocalText(ok);
    }

    public void setOKV2Text(String ok) {
        mOk2.setLocalText(ok);
    }

    public void setCancelText(String cancel) {
        mCancel.setLocalText(cancel);
    }

    public void setOKClick(View.OnClickListener onclick) {
        mOk.setOnClickListener(onclick);
    }

    public void setCancel(View.OnClickListener onclick) {
        mCancel.setOnClickListener(onclick);
    }

    public void setContent(String content) {

        mContent.setLocalText(content);
    }

    public interface AlertOkClickCallback {

        default void onOkClick() {
        }

        default void onOkClick(Dialog dialog) {

        }
    }

    public interface AlertCancelClickCallback {

        void onClick();
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mOK, mOkV2;

        private boolean isShowOK = false;

        private boolean isShowOK2 = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isCanCancel = true;

        private boolean isAutoDismiss = true;

        private int OkColor = 0;

        private boolean isShowOKView;

        private AlertOkClickCallback okClick;

        private AlertOkClickCallback okClickV2;

        private AlertCancelClickCallback cancelClick;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setOKV2Listener(AlertOkClickCallback listener) {
            this.okClickV2 = listener;
            return this;
        }

        public Builder setCancelListener(AlertCancelClickCallback cancelClick) {
            this.cancelClick = cancelClick;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setOkV2(String ok) {
            mOkV2 = ok;
            isShowOK2 = true;
            return this;
        }

        public Builder setCancel(String cancel) {
            if (TextUtils.isEmpty(cancel)) {
                isShowCancel = false;
            } else {
                mCancel = cancel;
                isShowCancel = true;
            }
            return this;
        }

        public Builder setAutoDissmiss(boolean isAutoDismiss) {
            this.isAutoDismiss = isAutoDismiss;
            return this;
        }

        public Builder setCanCancel(boolean isCanCancel) {
            this.isCanCancel = isCanCancel;
            return this;
        }

        public Builder setIsShowOkView(boolean isShow) {
            this.isShowOKView = isShow;
            return this;
        }

        public int getOkColor() {
            return OkColor;
        }

        public Builder setOkColor(int okColor) {
            OkColor = okColor;
            return this;
        }

        public AlertDialogV2 preBuilder() {
            AlertDialogV2 alertDialog = new AlertDialogV2(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
