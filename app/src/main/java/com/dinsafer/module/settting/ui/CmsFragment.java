package com.dinsafer.module.settting.ui;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.CmsLayoutBinding;
import com.dinsafer.model.CmsProtocolModel;
import com.dinsafer.model.DeviceCmsSaveEnableEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.MainFragmentViewPager;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * CMS设置页面
 *
 * <AUTHOR>
 * @date 2019-11-26 15:41
 */
public class CmsFragment extends MyBaseFragment<CmsLayoutBinding> implements IDeviceCallBack {

    public static float BACKGROUND_ALPHA = 0.5f;

    private final String[] mTitles = {"SIA Protocol"};

    LocalCustomButton mCommonBarLeftIcon;
    LocalTextView mCommonBarTitle;
    SegmentTabLayout mCmsTab;
    MainFragmentViewPager mCmsViewpager;

    private List<CmsProtocolModel> mProtocols;
    private ArrayList<BaseFragment> mFragmentList;
    private int mCurrentProtocolIndex;
    private CommonPagerAdapter mAdapter;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static CmsFragment newInstance() {
        return new CmsFragment();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        showTimeOutLoadinFramgmentWithErrorAlert();
        __bindViews(inflateView);

        mBinding.title.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.title.commonBarRightText.setVisibility(View.VISIBLE);
        mBinding.title.commonBarRightText.setLocalText(R.string.reset);
        mBinding.title.commonBarRightText.setTextColor(getResources().getColor(R.color.color_brand_text));
        mBinding.title.commonBarRightText.setBackgroundResource(0);
        mBinding.title.commonBarRightText.setOnClickListener(v -> showResetCmsDialog());

        mBinding.btnSave.setOnClickListener(v -> toSave());

        findPanel();
        initData();
    }

    private void __bindViews(View rootView) {
        mCommonBarLeftIcon = rootView.findViewById(R.id.btn_save);
        mCommonBarTitle = rootView.findViewById(R.id.common_bar_title);
        mCmsTab = rootView.findViewById(R.id.cms_tab);
        mCmsViewpager = rootView.findViewById(R.id.cms_viewpager);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.title.commonBarTitle.setLocalText(getResources().getString(R.string.advanced_setting_cms));
        mBinding.btnSave.setLocalText(getString(R.string.save));
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();

        if (null == mPanelDevice) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.getCmsInfo());
    }

    /**
     * 修改左上角保存按钮的可点击状态和透明度
     *
     * @param canSave 是否可保存
     */
    public void enableSave(boolean canSave) {
        mBinding.btnSave.setEnabled(canSave);
        if (canSave) {
            mBinding.btnSave.setAlpha(1.0f);
        } else {
            mBinding.btnSave.setAlpha(BACKGROUND_ALPHA);
        }
    }

    private void showResetCmsDialog() {
        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setLastButtonTextColor(getResources().getColor(R.color.color_minor_1))
                .setOtherButtonTitles(Local.s(getString(R.string.reset)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            toSubmitDefaultConfig();
                        }
                    }
                }).show();
    }

    /**
     * 提交默认配置参数到服务器
     */
    private void toSubmitDefaultConfig() {
        final int currentIndex = mCurrentProtocolIndex;
        if (null == mProtocols || 0 == mProtocols.size() || mProtocols.size() <= currentIndex) {
            showErrorToast();
            return;
        }

        final CmsProtocolModel currentModel = mProtocols.get(currentIndex);

        // 构造默认的配置参数
        final CmsProtocolModel defaultConfigModel = new CmsProtocolModel();
        defaultConfigModel.setProtocolName(currentModel.getProtocolName());

        final CmsProtocolModel.CmsProtocolInfo configInfo = new CmsProtocolModel.CmsProtocolInfo();
        configInfo.setNetwork(SiaProtocolFragment.NETWORK_TYPE_UDP);
        defaultConfigModel.setInfo(configInfo);

        requestSubmit(defaultConfigModel);
    }

    public void toSave() {
        if (null == mFragmentList || 0 == mFragmentList.size()) {
            return;
        }

        BaseFragment baseFragment = mFragmentList.get(mCurrentProtocolIndex);
        if (!(baseFragment instanceof SiaProtocolFragment)) {
            return;
        }
        SiaProtocolFragment siaProtocolFragment = (SiaProtocolFragment) baseFragment;
        // 校验并获取用户输入或修改后的协议信息
        CmsProtocolModel protocolModel = siaProtocolFragment.getUpdateProtocolInfo();
        if (null == protocolModel) {
            showErrorToast();
            return;
        }

        requestSubmit(protocolModel);
    }

    private void requestSubmit(@NonNull final CmsProtocolModel protocolModel) {
        // 联网请求修改协议信息
        isSelfOperate = true;
        showTimeOutLoadinFramgment();
        JSONObject info = new JSONObject();
        try {
            info.put("primary_ip", strValueOrEmpty(protocolModel.getInfo().getPrimary_ip()));
            info.put("primary_port", protocolModel.getInfo().getPrimary_port());
            info.put("secondary_ip", strValueOrEmpty(protocolModel.getInfo().getSecondary_ip()));
            info.put("secondary_port", protocolModel.getInfo().getSecondary_port());
            info.put("account_number", strValueOrEmpty(protocolModel.getInfo().getAccount_number()));
            info.put("encryption", protocolModel.getInfo().isEncryption());
            info.put("encryption_key", strValueOrEmpty(protocolModel.getInfo().getEncryption_key()));
            info.put("network", protocolModel.getInfo().getNetwork());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        mPanelDevice.submit(PanelParamsHelper.setCmsInfo(protocolModel.getProtocolName(), info.toString()));
    }

    @NonNull
    private String strValueOrEmpty(@Nullable final String value) {
        return TextUtils.isEmpty(value) ? "" : value;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.cms_layout;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceCmsSaveEnableEvent ev) {
        enableSave(ev.isCanSave());
    }

    /**
     * 初始化ViewPager和Tab
     */
    public void initPageAndTab() {
        if (null == mProtocols || 0 == mProtocols.size()) {
            showErrorToast();
            return;
        }

        // 1、初始化顶部Tab和Viewpager
        mFragmentList = new ArrayList<BaseFragment>();
        String[] localTitles = new String[mProtocols.size()];
        for (int i = 0; i < mProtocols.size(); i++) {
            localTitles[i] = Local.s(mProtocols.get(i).getProtocolName());
            mFragmentList.add(SiaProtocolFragment.newInstance(mProtocols.get(i)));
        }
        mBinding.cmsTab.setTextsize(13);
        mBinding.cmsTab.setTabData(localTitles);

        mAdapter = new CommonPagerAdapter(getChildFragmentManager(), mFragmentList);
        mBinding.cmsViewpager.setAdapter(mAdapter);
        mBinding.cmsViewpager.setCanSlide(true);

        // 2、实现Tab和ViewPager联动
        mBinding.cmsTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                mCurrentProtocolIndex = position;
                toCloseInput();
                mBinding.cmsViewpager.setCurrentItem(position);
            }

            @Override
            public void onTabReselect(int position) {
            }
        });
        mBinding.cmsViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                i("null?:" + mBinding.cmsTab.getTabCount() + ",position:" + position);
                try {
                    mCurrentProtocolIndex = position;
                    toCloseInput();
                    mBinding.cmsTab.setCurrentTab(position);
                } catch (Exception ex) {
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }


        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (PanelCmd.GET_CMS_INFO.equals(cmd)) {
            onGetCmsInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_CMS_INFO.equals(cmd)) {
                closeLoadingFragment();
                if (PanelDataKey.CmdResult.SUCCESS == status) {
                    removeSelf();
                } else {
                    showErrorToast();
                }
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置的CMS
     */
    private void onGetCmsInfo(int status, Map map) {
        DDLog.i(TAG, "onGetSosMessageInfo, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        List<Map<String, Object>> protocolList = DeviceHelper.getList(resultMap, PanelDataKey.Cms.DATAS);
        if (0 == protocolList.size()) {
            // 没有设置协议
            mProtocols = new ArrayList<>(mTitles.length);
            CmsProtocolModel protocolModel;
            for (String title :
                    mTitles) {
                protocolModel = new CmsProtocolModel();
                protocolModel.setProtocolName(title);
                protocolModel.setInfo(new CmsProtocolModel.CmsProtocolInfo());
                mProtocols.add(protocolModel);
            }
        } else {
            // 已经设置了协议
            mProtocols = new ArrayList<>();
            CmsProtocolModel protocolModel;
            CmsProtocolModel.CmsProtocolInfo protocolInfo;

            for (Map<String, Object> protocolMap : protocolList) {
                protocolModel = new CmsProtocolModel();
                protocolInfo = new CmsProtocolModel.CmsProtocolInfo();
                protocolInfo.setPrimary_ip(DeviceHelper.getString(protocolMap, PanelDataKey.Cms.PRIMARY_IP, ""));
                protocolInfo.setPrimary_port(DeviceHelper.getInt(protocolMap, PanelDataKey.Cms.PRIMARY_PORT, 0));
                protocolInfo.setSecondary_ip(DeviceHelper.getString(protocolMap, PanelDataKey.Cms.SECONDARY_IP, ""));
                protocolInfo.setSecondary_port(DeviceHelper.getInt(protocolMap, PanelDataKey.Cms.SECONDARY_PORT, 0));
                protocolInfo.setAccount_number(DeviceHelper.getString(protocolMap, PanelDataKey.Cms.ACCOUNT_NUMBER, ""));
                protocolInfo.setEncryption(DeviceHelper.getBoolean(protocolMap, PanelDataKey.Cms.ENCRYPTION, false));
                protocolInfo.setEncryption_key(DeviceHelper.getString(protocolMap, PanelDataKey.Cms.ENCRYPTION_KEY, ""));
                protocolInfo.setNetwork(DeviceHelper.getString(protocolMap, PanelDataKey.Cms.NETWORK, ""));
                protocolModel.setProtocolName(DeviceHelper.getString(protocolMap, PanelDataKey.Cms.PROTOCOL_NAME, ""));
                protocolModel.setInfo(protocolInfo);
                mProtocols.add(protocolModel);
            }
        }

        initPageAndTab();
    }
}
