package com.dinsafer.module.settting.ui;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;

import java.util.Map;

/**
 * Created by LT on 2018/8/24.
 */
public class ReadyToArmSettingFragment extends BaseFragment
        implements IDeviceCallBack {

    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    LocalTextView readyToArmNoDoorSensorHint;
    LocalTextView readyToArmHint;
    IOSSwitch btnIsReadyToArm;
    ImageView imgBuy;
    LocalCustomButton btnBuy;

    public static final String TAG = "ReadyToArmSettingFragment";
    public static final int VIEW_STATUS_NO_BUY_NO_SWITCH = 0;
    public static final int VIEW_STATUS_BUY = 1;
    public static final int VIEW_STATUS_SWITCH = 2;
    private int view_status = 0;
    private String url = null;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static ReadyToArmSettingFragment newInstance() {
        return new ReadyToArmSettingFragment();
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }
        mPanelDevice.submit(PanelParamsHelper.getReadyToArmStatus());
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.ready_to_arm_setting_layout, container, false);
        showBlueTimeOutLoadinFramgment();
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
        rootView.findViewById(R.id.btn_buy).setOnClickListener( v -> toBuy());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        readyToArmNoDoorSensorHint = rootView.findViewById(R.id.ready_to_arm_no_door_sensor_hint);
        readyToArmHint = rootView.findViewById(R.id.ready_to_arm_hint);
        btnIsReadyToArm = rootView.findViewById(R.id.btn_is_ready_to_arm);
        imgBuy = rootView.findViewById(R.id.img_buy);
        btnBuy = rootView.findViewById(R.id.btn_buy);
    }

    @Override
    public void initData() {
        super.initData();

        btnIsReadyToArm.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                changeSwitchStatus(isOn);
            }
        });

        readyToArmNoDoorSensorHint.setVisibility(View.VISIBLE);
        btnBuy.setVisibility(View.INVISIBLE);
        imgBuy.setVisibility(View.INVISIBLE);
        btnIsReadyToArm.setVisibility(View.INVISIBLE);
        commonBarTitle.setLocalText(getResources().getString(R.string.ready_to_arm));
        readyToArmNoDoorSensorHint.setLocalText(getResources().getString(R.string.rta_setting_no_door_hint));
        readyToArmHint.setLocalText(getResources().getString(R.string.rta_setting_hint));
        btnBuy.setLocalText(getResources().getString(R.string.rta_setting_btn_buy));
    }

    /**
     * 修改界面状态：无开关无购买界面0，购买界面1，开关界面2
     */
    private void changeViewByStatus() {
        switch (view_status) {
            case VIEW_STATUS_BUY:
                //购买界面1
                readyToArmNoDoorSensorHint.setVisibility(View.VISIBLE);
                btnBuy.setVisibility(View.VISIBLE);
                imgBuy.setVisibility(View.VISIBLE);
                btnIsReadyToArm.setVisibility(View.INVISIBLE);
                break;
            case VIEW_STATUS_SWITCH:
                //开关界面2
                readyToArmNoDoorSensorHint.setVisibility(View.GONE);
                btnBuy.setVisibility(View.INVISIBLE);
                imgBuy.setVisibility(View.INVISIBLE);
                btnIsReadyToArm.setVisibility(View.VISIBLE);
                break;
            case VIEW_STATUS_NO_BUY_NO_SWITCH:
                //无开关无购买界面0
            default:
                readyToArmNoDoorSensorHint.setVisibility(View.VISIBLE);
                btnBuy.setVisibility(View.INVISIBLE);
                imgBuy.setVisibility(View.INVISIBLE);
                btnIsReadyToArm.setVisibility(View.INVISIBLE);
                break;
        }
    }

    public void close() {
        removeSelf();
    }

    /**
     * check界面状态，是否显示购买，或者开关
     */
    private void checkViewStatus(String localUrl, int count, boolean enable) {
        // LT-OS: rta setting界面判断有无新型门磁以及购买链接
        // LT-OS: rta 如果是开关界面，要requestSwitch
        if (count > 0) {
            //开关界面
            view_status = VIEW_STATUS_SWITCH;
            btnIsReadyToArm.setOn(enable);
        } else {
            if (!TextUtils.isEmpty(localUrl)) {
                //购买界面
                view_status = VIEW_STATUS_BUY;
                url = localUrl;
            } else {
                view_status = VIEW_STATUS_NO_BUY_NO_SWITCH;
            }
        }
        changeViewByStatus();
    }

    private void changeSwitchStatus(boolean isOn) {
        showBlueTimeOutLoadinFramgment();
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.setReadyToArmStatus(isOn));
    }

    public void toBuy() {
        // LT-OS: 购买跳转
        if (!TextUtils.isEmpty(url)) {
            try {
                Uri uri = Uri.parse(url);
                Intent intent = new Intent();
                intent.setAction("android.intent.action.VIEW");
                intent.setData(uri);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
                showErrorToast();
            }
        }
    }

    private void changeSwitchStatusFailed() {
        // LT-OS: 设置开关操作失败,不要有动画
        closeLoadingFragment();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (PanelCmd.GET_READYTOARM_STATUS.equals(cmd)) {
            onGetReadyToArmInfo(status, map);
        } else if (PanelCmd.SET_READYTOARM_STATUS.equals(cmd)
                && isSelfOperate
                && 1 == resultType) {
            closeLoadingFragment();
            if (PanelDataKey.CmdResult.SUCCESS == status) {
                String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
                btnIsReadyToArm.setOn(Boolean.parseBoolean(result));
            } else {
                DDLog.d(TAG, "设置ready to arm开关失败:主机执行失败");
                changeSwitchStatusFailed();
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置的ReadyToArm状态
     */
    private void onGetReadyToArmInfo(int status, Map map) {
        DDLog.i(TAG, "onGetSosMessageInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            view_status = VIEW_STATUS_SWITCH;
            changeViewByStatus();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        String url = DeviceHelper.getString(resultMap, PanelDataKey.ReadyToArm.URL, "");
        int count = DeviceHelper.getInt(resultMap, PanelDataKey.ReadyToArm.COUNT, 0);
        boolean enable = DeviceHelper.getBoolean(resultMap, PanelDataKey.ReadyToArm.ENABLE, false);
        checkViewStatus(url, count, enable);
    }
}
