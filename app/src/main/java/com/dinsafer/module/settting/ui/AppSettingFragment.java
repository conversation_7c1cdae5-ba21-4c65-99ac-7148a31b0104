package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.InputType;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.model.AdEntry;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.model.UserPermissonUpdata;
import com.dinsafer.model.event.GetAllDeviceFinishedEvent;
import com.dinsafer.model.event.KeypadMemberPwdUpdatedEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.ad.AdFragment;
import com.dinsafer.module.settting.ui.event.ShowDebugModeEvent;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.AppRatingUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.MultiClickCounter;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.Nullable;

/**
 * Created by Rinfon on 16/6/20.
 */
public class AppSettingFragment extends BaseFragment {


    LocalTextView appSettingSystemLabel;
    LocalTextView appSettingLanguage;
    ImageView appSettingLanguageNor;
    LocalTextView appSettingVersion;
    TextView appSettingVersionNor;
    LocalTextView appSettingInformationLabel;
    LocalTextView appSettingAboutUs;
    ImageView appSettingAboutNor;
    LocalTextView appSettingLicenses;
    ImageView appSettingLicensesNor;
    LocalTextView appSettingHelp;
    ImageView appSettingHelpNor;
    View appSettingHelpLine;
    LocalTextView appSettingFeedback;
    ImageView appSettingFeedbackNor;
    LocalTextView appSettingPassword;
    LocalTextView appSettingAd;
    ImageView appSettingAdNor;
    View appSettingAdLine;
    LocalTextView appSettingPrivate;
    ImageView appSettingPasswordNor;
    ImageView appSettingPrivateNor;
    LocalTextView appSettingRating;
    LocalTextView tvDebugMode;
    ImageView appSettingRatingNor;
    ImageView ivAppSettingRatingFingure;
    View vAppSettingRatingDownLine;
    LinearLayout llKeyboardPassword;
    TextView tvKeyboardPassword;
    ImageView ivKeyboardPasswordVisible;

    private MultiClickCounter mMultiClickCounter;


    public static AppSettingFragment newInstance() {
        return new AppSettingFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.app_setting_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        mMultiClickCounter = new MultiClickCounter(10, 2000);
        mMultiClickCounter.setListener(() -> {
            getMainActivity().addCommonFragment(SelfTestFragment.newInstance());
        });
        rootView.findViewById(R.id.app_setting_about_nor).setOnClickListener( v -> toAboutUS());
        rootView.findViewById(R.id.app_setting_about_us).setOnClickListener( v -> toAboutUS());
        rootView.findViewById(R.id.app_setting_licenses_nor).setOnClickListener( v -> toThirdPartLicenses());
        rootView.findViewById(R.id.app_setting_licenses).setOnClickListener( v -> toThirdPartLicenses());
        rootView.findViewById(R.id.app_setting_private_nor).setOnClickListener( v -> toPrivate());
        rootView.findViewById(R.id.app_setting_private).setOnClickListener( v -> toPrivate());
        rootView.findViewById(R.id.app_setting_language_nor).setOnClickListener( v -> toChangeAppLanguage());
        rootView.findViewById(R.id.app_setting_language).setOnClickListener( v -> toChangeAppLanguage());
        rootView.findViewById(R.id.app_setting_help_nor).setOnClickListener( v -> toHelp());
        rootView.findViewById(R.id.app_setting_help).setOnClickListener( v -> toHelp());
        rootView.findViewById(R.id.app_setting_feedback_nor).setOnClickListener( v -> toFeedback());
        rootView.findViewById(R.id.app_setting_feedback).setOnClickListener( v -> toFeedback());
        rootView.findViewById(R.id.app_setting_password_nor).setOnClickListener( v -> toPassword());
        rootView.findViewById(R.id.app_setting_password).setOnClickListener( v -> toPassword());
        rootView.findViewById(R.id.app_setting_ad_nor).setOnClickListener( v -> toShowAd());
        rootView.findViewById(R.id.app_setting_ad).setOnClickListener( v -> toShowAd());
        rootView.findViewById(R.id.tv_debug_mode).setOnClickListener( v -> enterDebugMode());
        rootView.findViewById(R.id.app_setting_system_label).setOnClickListener( v -> toChangeIP());
        rootView.findViewById(R.id.app_setting_rating).setOnClickListener( v -> onViewClicked(v));
        rootView.findViewById(R.id.app_setting_rating_nor).setOnClickListener( v -> onViewClicked(v));
    }

    private void __bindViews(View rootView) {
        appSettingSystemLabel = rootView.findViewById(R.id.app_setting_system_label);
        appSettingLanguage = rootView.findViewById(R.id.app_setting_language);
        appSettingLanguageNor = rootView.findViewById(R.id.app_setting_language_nor);
        appSettingVersion = rootView.findViewById(R.id.app_setting_version);
        appSettingVersionNor = rootView.findViewById(R.id.app_setting_version_nor);
        appSettingInformationLabel = rootView.findViewById(R.id.app_setting_information_label);
        appSettingAboutUs = rootView.findViewById(R.id.app_setting_about_us);
        appSettingAboutNor = rootView.findViewById(R.id.app_setting_about_nor);
        appSettingLicenses = rootView.findViewById(R.id.app_setting_licenses);
        appSettingLicensesNor = rootView.findViewById(R.id.app_setting_licenses_nor);
        appSettingHelp = rootView.findViewById(R.id.app_setting_help);
        appSettingHelpNor = rootView.findViewById(R.id.app_setting_help_nor);
        appSettingHelpLine = rootView.findViewById(R.id.app_setting_help_line);
        appSettingFeedback = rootView.findViewById(R.id.app_setting_feedback);
        appSettingFeedbackNor = rootView.findViewById(R.id.app_setting_feedback_nor);
        appSettingPassword = rootView.findViewById(R.id.app_setting_password);
        appSettingAd = rootView.findViewById(R.id.app_setting_ad);
        appSettingAdNor = rootView.findViewById(R.id.app_setting_ad_nor);
        appSettingAdLine = rootView.findViewById(R.id.app_setting_ad_line);
        appSettingPrivate = rootView.findViewById(R.id.app_setting_private);
        appSettingPasswordNor = rootView.findViewById(R.id.app_setting_password_nor);
        appSettingPrivateNor = rootView.findViewById(R.id.app_setting_private_nor);
        appSettingRating = rootView.findViewById(R.id.app_setting_rating);
        tvDebugMode = rootView.findViewById(R.id.tv_debug_mode);
        appSettingRatingNor = rootView.findViewById(R.id.app_setting_rating_nor);
        ivAppSettingRatingFingure = rootView.findViewById(R.id.iv_app_setting_rating_fingure);
        vAppSettingRatingDownLine = rootView.findViewById(R.id.v_app_setting_rating_down_line);
        llKeyboardPassword = rootView.findViewById(R.id.ll_keyboard_password);
        tvKeyboardPassword = rootView.findViewById(R.id.tv_keyboard_password);
        ivKeyboardPasswordVisible = rootView.findViewById(R.id.iv_keyboard_password_visible);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    public void toAboutUS() {
        getDelegateActivity().addCommonFragment(AboutUsFragment.newInstance());
    }

    public void toThirdPartLicenses() {
        getDelegateActivity().addCommonFragment(ThirdPartLicenseFragment.newInstance());
    }

    public void toPrivate() {
        getDelegateActivity().addCommonFragment(PrivateFragment.newInstance());
    }

    public void toChangeAppLanguage() {
        //LocalHelper helper = LocalHelper.Share(getMainActivity(), APIKey.LANGUAGEKEY);
        //helper.downloadList(true, true);
        showTimeOutLoadinFramgment(3 * 60 * 1000);
        LocalManager.getInstance().download(new LocalManager.DownloadListener() {
            @Override
            public void onLocalDownloadSuccess() {
                closeLoadingFragment();
                getMainActivity().addCommonFragment(LanguageFragment.newInstance());
            }

            @Override
            public void onLocalDownloadFail(String s) {
                showErrorToast();
            }
        });
    }

    public void toHelp() {
        getDelegateActivity().addCommonFragment(HelpFragment.newInstance());
    }

    public void toFeedback() {
        getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
    }

    public void toPassword() {
        getDelegateActivity().addCommonFragment(PasswordSettingFragment.newInstance());
    }

    public void toShowAd() {
        AdEntry adEntry = DDSystemUtil.getAdList(false);
        if (adEntry != null) {
            getDelegateActivity().addCommonFragment(AdFragment.newInstance());
        }
    }

    public void enterDebugMode() {
        getMainActivity().addCommonFragment(PanelDebugModeFragment.newInstance(CommonDataUtil.getInstance().getCurrentPanelID()));
    }

    @Override
    public void initData() {
        super.initData();
        appSettingVersionNor.setText(DDSystemUtil.getVersion(getDelegateActivity()) + "");
        appSettingAd.setLocalText(getResources().getString(R.string.app_setting_promotion));
        if (!DBUtil.Exists(DBKey.AD_LIST)) {
            appSettingAd.setVisibility(View.GONE);
            appSettingAdLine.setVisibility(View.GONE);
            appSettingAdNor.setVisibility(View.GONE);
        }
        updataUI();
        initKeypadPasswordSetting();
    }

    private long lastClickTime = -1;

    public void toChangeIP() {
//        long clickTime = System.currentTimeMillis();
//        i("clicktime:" + clickTime);
//        if ((clickTime - lastClickTime) / 5000 >= 1 && (clickTime - lastClickTime) / 10000 <= 0) {
//            lastClickTime = -1;
//            getMainActivity().addCommonFragment(SelfTestFragment.newInstance());
//        } else {
//            lastClickTime = clickTime;
//        }
        if (mMultiClickCounter != null) {
            mMultiClickCounter.click();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        updataUI();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ShowDebugModeEvent event) {
       tvDebugMode.setVisibility(View.VISIBLE);
    }

    private void initKeypadPasswordSetting() {
        ivKeyboardPasswordVisible.setOnClickListener(v -> {
            final boolean hidden = tvKeyboardPassword.getInputType() == 129;
            changeKeypadPasswordVisible(hidden);
        });
    }

    private void changeKeypadPasswordDetailVisible(final boolean opened) {
        if (opened) {
            llKeyboardPassword.setVisibility(View.VISIBLE);
        } else {
            llKeyboardPassword.setVisibility(View.GONE);
        }
    }

    /**
     * 修改是否明文密码
     *
     * @param visible true: 显示明文密码
     */
    private void changeKeypadPasswordVisible(final boolean visible) {
        if (visible) {
            ivKeyboardPasswordVisible.setImageResource(R.drawable.icon_form_show);
            tvKeyboardPassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            ivKeyboardPasswordVisible.setImageResource(R.drawable.icon_form_hide);
            tvKeyboardPassword.setInputType(129);
        }
    }


    private void updataUI() {
        appSettingSystemLabel.setLocalText(getResources().getString(R.string.app_setting_system));
        appSettingLanguage.setLocalText(getResources().getString(R.string.app_setting_language));
        appSettingVersion.setLocalText(getResources().getString(R.string.app_setting_version));
        appSettingInformationLabel.setLocalText(getResources().getString(R.string.app_setting_information));
        appSettingAboutUs.setLocalText(getResources().getString(R.string.app_setting_about_us));
        appSettingLicenses.setLocalText(getResources().getString(R.string.third_party_licenses));
        appSettingPrivate.setLocalText(getResources().getString(R.string.app_private));
        appSettingHelp.setLocalText(getResources().getString(R.string.app_setting_help));
        appSettingFeedback.setLocalText(getResources().getString(R.string.app_setting_feedback));
        appSettingPassword.setLocalText(getResources().getString(R.string.app_setting_password));
        appSettingAd.setLocalText(getResources().getString(R.string.app_setting_promotion));
        appSettingRating.setLocalText(getResources().getString(R.string.app_setting_rating));
        tvDebugMode.setLocalText(R.string.debug_mode);

        if (APIKey.IS_SHOW_RATING) {
            appSettingRating.setVisibility(View.VISIBLE);
            appSettingRatingNor.setVisibility(View.VISIBLE);
            ivAppSettingRatingFingure.setVisibility(View.VISIBLE);
            vAppSettingRatingDownLine.setVisibility(View.VISIBLE);
        }

        if (!APIKey.IS_SHOW_HELP || TextUtils.isEmpty(APIKey.HELP)) {
            appSettingHelp.setVisibility(View.GONE);
            appSettingHelpNor.setVisibility(View.GONE);
            appSettingHelpLine.setVisibility(View.GONE);
        }

        // TODO  更新密码
        changeKeypadPasswordDetailVisible(false);
        tvKeyboardPassword.setText("12345");
    }

    public void onViewClicked(View view) {
        AppRatingUtil.toAppMarket(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(GetAllDeviceFinishedEvent event) {
        updateKeypadPasswordSetting();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(UserPermissonUpdata event) {
        updateKeypadPasswordSetting();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(KeypadMemberPwdUpdatedEvent event) {
        updateKeypadPasswordSetting();
    }

    private void updateKeypadPasswordSetting() {
        final int permission = HomeManager.getInstance().getCurrentHome().getLevel();
        final boolean isMeGuest = LocalKey.GUEST >= permission;
        final boolean hadPanelNotDeleted = CommonDataUtil.getInstance().isHadPanelNotDeleted();

        if (AppConfig.Functions.SUPPORT_KEYPAD_PWD && hadPanelNotDeleted && isMeGuest) {
            requestMyKeypadPassword();
        } else {
            updateKeypadPasswordGroupSetting(false, null);
        }
    }

    private void requestMyKeypadPassword() {
        DDLog.i(TAG, "requestMyKeypadPassword");
        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final String userId = null == DinSDK.getUserInstance().getUser()
                ? ""
                : DinSDK.getUserInstance().getUser().getUser_id();

        DinSDK.getHomeInstance().getKeypadMemberPwdInfo(homeId, panelId, userId, new IDefaultCallBack2<KeypadMemberPwdInfoGetResponse>() {
            @Override
            public void onSuccess(KeypadMemberPwdInfoGetResponse keypadMemberPwdInfoGetResponse) {
                if (null == keypadMemberPwdInfoGetResponse || null == keypadMemberPwdInfoGetResponse.getResult()) {
                    showErrorToast();
                    return;
                }

                final KeypadMemberPwdInfoGetResponse.ResultBean result = keypadMemberPwdInfoGetResponse.getResult();
                final Boolean hasDevice = result.getHasDevice();
                final Boolean hasKeyBoard = result.getHasKeyBoard();
                final Boolean pwdEnable = result.getPwdEnable();
                final String pwd = result.getPwd();

                // 有主机并且有键盘并且自己是管理员或用户的时候才显示相关设置
                final boolean visible = null != hasDevice && hasDevice
                        && null != hasKeyBoard && hasKeyBoard;
                final boolean enabled = null != pwdEnable && pwdEnable;
                updateKeypadPasswordGroupSetting(visible && enabled, pwd);
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error on requestMemberKeypadPwdInfo, i: " + i + ", s: " + s);
            }
        });
    }

    private void updateKeypadPasswordGroupSetting(final boolean visible, final String keypadPwd) {
        if (visible) {
            changeKeypadPasswordDetailVisible(true);
            tvKeyboardPassword.setText(null == keypadPwd ? "" : keypadPwd);
            changeKeypadPasswordVisible(false);
        } else {
            changeKeypadPasswordDetailVisible(false);
        }
    }
}
