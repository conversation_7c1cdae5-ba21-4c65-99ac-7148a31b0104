package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;

import java.util.ArrayList;


/**
 * Created by Rinfon on 16/7/8.
 */
public class FAQFragment extends BaseFragment {

    TextView commonBarTitle;
    LocalCustomButton btnSave;

    private ArrayList<String> questionOne = new ArrayList<String>();

    private ArrayList<String> questionTwo = new ArrayList<String>();

    private ArrayList<String> questionThird = new ArrayList<String>();

    public static FAQFragment newInstance() {
        return new FAQFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.faq_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        getDelegateActivity().setTheme(R.style.ActionSheetStyleiOS7);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.btn_save).setOnClickListener( v -> close());
        rootView.findViewById(R.id.faq_setting_first).setOnTouchListener((v, event) -> toSelectFirst(v,event));
        rootView.findViewById(R.id.faq_setting_second).setOnTouchListener((v, event) -> toSelectFirst(v,event));
        rootView.findViewById(R.id.faq_setting_third).setOnTouchListener((v, event) -> toSelectFirst(v,event));
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        btnSave = rootView.findViewById(R.id.btn_save);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setText(getResources().getString(R.string.faq_layout_title));
        btnSave.setLocalText(getString(R.string.save));
        questionOne.add("选择预设安全问题");
        questionOne.add("我的生日");
        questionOne.add("我妈的生日");
        questionOne.add("我爸的生日");
        questionOne.add("我舅的生日");

        questionTwo = questionOne;
        questionThird = questionOne;
    }

    public boolean toSelectFirst(final View view, MotionEvent motionEvent) {
        if (motionEvent.getAction() == MotionEvent.ACTION_UP) {

            String[] data = null;

            if (view.getId() == R.id.faq_setting_first) {
                data = questionOne.toArray(new String[questionOne.size()]);
            } else if (view.getId() == R.id.faq_setting_second) {
                data = questionTwo.toArray(new String[questionTwo.size()]);
            } else {
                data = questionThird.toArray(new String[questionThird.size()]);
            }

            final String[] finalData = data;
            ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(true)
                    .setCancelButtonTitle(getResources().getString(R.string.device_management_add_cancel))
                    .setOtherButtonTitles(data)
                    .setCancelableOnTouchOutside(true)

                    .setListener(new ActionSheet.ActionSheetListener() {
                        @Override
                        public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                        }

                        @Override
                        public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                            if (view instanceof EditText) {
                                ((EditText) view).setText(finalData[index]);
                            }

                        }
                    }).show();
        }
        return true;
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

}

