package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.ContactItem;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.interfaces.IUpdataList;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ContactsPushSettingFragment extends BaseFragment {

    LocalTextView commonBarTitle;
    ImageView commonBarBack;
    LocalCustomButton btnSave;
    LocalTextView contactPushNoti;
    IOSSwitch contactPushNotiSwitch;
    LocalTextView contactPushSms;
    IOSSwitch contactPushSmsSwitch;
    LocalTextView contactPushPhone;
    IOSSwitch contactPushPhoneSwitch;
    LocalTextView contactPushHint;

    private ContactItem data;

    private IUpdataList callBack;

    private boolean isPush, isSms, isCall;

    public static ContactsPushSettingFragment newInstance(ContactItem data) {
        ContactsPushSettingFragment contactsPushSettingFragment = new ContactsPushSettingFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("data", data);
        contactsPushSettingFragment.setArguments(bundle);
        return contactsPushSettingFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.contact_push_setting_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.btn_save).setOnClickListener( v -> toSave());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        btnSave = rootView.findViewById(R.id.btn_save);
        contactPushNoti = rootView.findViewById(R.id.contact_push_noti);
        contactPushNotiSwitch = rootView.findViewById(R.id.contact_push_noti_switch);
        contactPushSms = rootView.findViewById(R.id.contact_push_sms);
        contactPushSmsSwitch = rootView.findViewById(R.id.contact_push_sms_switch);
        contactPushPhone = rootView.findViewById(R.id.contact_push_phone);
        contactPushPhoneSwitch = rootView.findViewById(R.id.contact_push_phone_switch);
        contactPushHint = rootView.findViewById(R.id.contact_push_hint);
    }

    @Override
    public void initData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.contact_push_title));
        btnSave.setLocalText(getString(R.string.save));
        contactPushNoti.setLocalText(getResources().getString(R.string.contact_push_noti));
        contactPushSms.setLocalText(getResources().getString(R.string.contact_push_sms));
        contactPushPhone.setLocalText(getResources().getString(R.string.contact_push_phone));
        contactPushHint.setLocalText(getResources().getString(R.string.contact_push_hint));
        data = (ContactItem) getArguments().getSerializable("data");
        isPush = data.isPush();
        isSms = data.isSms();
        isCall = data.isCall();
        if (TextUtils.isEmpty(data.getPhone())
                && !data.isBindPhone()) {
            toClosePhone();
            toCloseSMS();
        }
        if (TextUtils.isEmpty(data.getPhone())
                && !data.isBindPhone()) {
            contactPushHint.setVisibility(View.VISIBLE);
            contactPushSms.setEnabled(false);
            contactPushPhone.setEnabled(false);
            contactPushSmsSwitch.setEnabled(false);
            contactPushPhoneSwitch.setEnabled(false);
        } else {
            contactPushHint.setVisibility(View.INVISIBLE);
            contactPushSms.setEnabled(true);
            contactPushPhone.setEnabled(true);
            contactPushSmsSwitch.setEnabled(true);
            contactPushPhoneSwitch.setEnabled(true);
        }

        contactPushSmsSwitch.setOn(data.isSms());
        contactPushPhoneSwitch.setOn(data.isCall());
        contactPushNotiSwitch.setOn(data.isPush());
        contactPushNotiSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                isPush = isOn;
            }
        });
        contactPushPhoneSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                isCall = isOn;
            }
        });
        contactPushSmsSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                isSms = isOn;
            }
        });
    }

    private void toClosePush() {
        contactPushNoti.setAlpha(0.5f);
        contactPushNotiSwitch.setAlpha(0.5f);

    }

    private void toOpenPush() {
        contactPushNoti.setAlpha(1f);
        contactPushNotiSwitch.setAlpha(1f);
    }

    private void toCloseSMS() {
        contactPushSms.setAlpha(0.5f);
        contactPushSmsSwitch.setAlpha(0.5f);
        contactPushSmsSwitch.setEnabled(false);

    }

    private void toOpenSMS() {
        contactPushSms.setAlpha(1f);
        contactPushSmsSwitch.setAlpha(1f);
    }

    private void toClosePhone() {
        contactPushPhone.setAlpha(0.5f);
        contactPushPhoneSwitch.setAlpha(0.5f);
        contactPushSmsSwitch.setEnabled(false);
    }

    private void toOpenPhone() {
        contactPushPhone.setAlpha(1f);
        contactPushPhoneSwitch.setAlpha(1f);
    }


    public void toSave() {
        showTimeOutLoadinFramgment();
        DinsafeAPI.getApi().getContactsPushTypeCall(CommonDataUtil.getInstance().getCurrentDeviceId()
                , data.getUid(), isCall, isSms, isPush)
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        closeLoadingFragment();
                        StringResponseEntry s = response.body();
                        if (s.getStatus() == 1) {
                            if (callBack != null) {
                                data.setCall(isCall);
                                data.setSms(isSms);
                                data.setPush(isPush);
                                callBack.updata();
                            }
                            removeSelf();
                        } else {
                            showErrorToast();
                        }
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

    public IUpdataList getCallBack() {
        return callBack;
    }

    public void setCallBack(IUpdataList callBack) {
        this.callBack = callBack;
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        callBack = null;
    }

}

