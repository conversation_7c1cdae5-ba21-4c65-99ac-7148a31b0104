package com.dinsafer.module.settting.ui;

import android.os.Parcel;
import android.os.Parcelable;

import org.json.JSONObject;

public class Builder implements Parcelable {

    private String id;
    private boolean isOffical;
    private boolean isShowDelete;
    private boolean isShowwave;
    private String name;
    private boolean isAdd;
    private String Messageid;
    private String data;
    private String type;
    private int messageIndex;
    private boolean isLowPower;
    private boolean isOffline;
    private boolean isShowSiren;
    private boolean isShowSirenTest;
    private boolean isAskPlugin;
    private boolean isBtnOne;
    private boolean isShowMoreSetting;
    private String decodeid;
    private int sensitivity;

    public Builder() {
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public Builder setSensitivity(int sensitivity) {
        this.sensitivity = sensitivity;
        return this;
    }

    public boolean isBtnOne() {
        return isBtnOne;
    }

    public Builder setBtnOne(boolean btnOne) {
        isBtnOne = btnOne;
        return this;
    }

    public String getId() {
        return id;
    }

    public Builder setId(String id) {
        this.id = id;
        return this;
    }

    public boolean isOffical() {
        return isOffical;
    }

    public Builder setOffical(boolean offical) {
        isOffical = offical;
        return this;
    }

    public boolean isShowwave() {
        return isShowwave;
    }

    public Builder setShowwave(boolean showwave) {
        isShowwave = showwave;
        return this;
    }

    public String getName() {
        return name;
    }

    public Builder setName(String name) {
        this.name = name;
        return this;
    }

    public boolean isAdd() {
        return isAdd;
    }

    public Builder setAdd(boolean add) {
        isAdd = add;
        return this;
    }

    public String getMessageid() {
        return Messageid;
    }

    public Builder setMessageid(String messageid) {
        Messageid = messageid;
        return this;
    }

    public String getData() {
        return data;
    }

    public Builder setData(JSONObject data) {
        this.data = data.toString();
        return this;
    }

    public Builder setData(String data) {
        this.data = data;
        return this;
    }

    public String getType() {
        return type;
    }

    public Builder setType(String type) {
        this.type = type;
        return this;
    }

    public boolean isShowDelete() {
        return isShowDelete;
    }

    public Builder setShowDelete(boolean showDelete) {
        isShowDelete = showDelete;
        return this;
    }

    public int getMessageIndex() {
        return messageIndex;
    }

    public Builder setMessageIndex(int messageIndex) {
        this.messageIndex = messageIndex;
        return this;
    }

    public boolean isLowPower() {
        return isLowPower;
    }

    public Builder setLowPower(boolean lowPower) {
        isLowPower = lowPower;
        return this;
    }

    public boolean isOffline() {
        return isOffline;
    }

    public Builder setOffline(boolean offline) {
        isOffline = offline;
        return this;
    }

    public boolean isShowSiren() {
        return isShowSiren;
    }

    public Builder setShowSiren(boolean showSiren) {
        isShowSiren = showSiren;
        return this;
    }

    public Builder setShowMoreSetting(boolean isShowMoreSetting) {
        this.isShowMoreSetting = isShowMoreSetting;
        return this;
    }

    public boolean isShowMoreSetting() {
        return isShowMoreSetting;
    }

    public boolean isShowSirenTest() {
        return isShowSirenTest;
    }

    public Builder setShowSirenTest(boolean showSirenTest) {
        isShowSirenTest = showSirenTest;
        return this;
    }

    public boolean isAskPlugin() {
        return isAskPlugin;
    }

    public Builder setAskPlugin(boolean askPlugin) {
        isAskPlugin = askPlugin;
        return this;
    }

    public Builder setDecodeId(String decodeId) {
        this.decodeid = decodeId;
        return this;
    }

    public String getDecodeid() {
        return decodeid;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.id);
        dest.writeByte(this.isOffical ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isShowDelete ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isShowwave ? (byte) 1 : (byte) 0);
        dest.writeString(this.name);
        dest.writeByte(this.isAdd ? (byte) 1 : (byte) 0);
        dest.writeString(this.Messageid);
        dest.writeString(this.data);
        dest.writeString(this.type);
        dest.writeInt(this.messageIndex);
        dest.writeByte(this.isLowPower ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isOffline ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isShowSiren ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isShowSirenTest ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isAskPlugin ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isBtnOne ? (byte) 1 : (byte) 0);
    }

    protected Builder(Parcel in) {
        this.id = in.readString();
        this.isOffical = in.readByte() != 0;
        this.isShowDelete = in.readByte() != 0;
        this.isShowwave = in.readByte() != 0;
        this.name = in.readString();
        this.isAdd = in.readByte() != 0;
        this.Messageid = in.readString();
        this.data = in.readString();
        this.type = in.readString();
        this.messageIndex = in.readInt();
        this.isLowPower = in.readByte() != 0;
        this.isOffline = in.readByte() != 0;
        this.isShowSiren = in.readByte() != 0;
        this.isShowSirenTest = in.readByte() != 0;
        this.isAskPlugin = in.readByte() != 0;
        this.isBtnOne = in.readByte() != 0;
    }

    public static final Parcelable.Creator<Builder> CREATOR = new Parcelable.Creator<Builder>() {
        @Override
        public Builder createFromParcel(Parcel source) {
            return new Builder(source);
        }

        @Override
        public Builder[] newArray(int size) {
            return new Builder[size];
        }
    };
}
