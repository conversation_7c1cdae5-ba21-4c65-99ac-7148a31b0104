package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/12.
 */
public class DeletePanelFragment extends BaseFragment implements IDeviceCallBack {

    LocalTextView commonBarTitle;
    LocalTextView tvDeletePanelHint;
    LocalCustomButton btnDelete;
    LocalCustomButton btnCancel;

    private Device mPanelDevice;

    public static DeletePanelFragment newInstance() {
        return new DeletePanelFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.delete_panel_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.btn_delete).setOnClickListener( v -> toDeletePanel());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.btn_cancal).setOnClickListener( v -> toClose());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        tvDeletePanelHint = rootView.findViewById(R.id.tv_delete_panel_hint);
        btnDelete = rootView.findViewById(R.id.btn_delete);
        btnCancel = rootView.findViewById(R.id.btn_cancal);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.delete_the_panel));
        tvDeletePanelHint.setLocalText(getResources().getString(R.string.delete_the_panel_hint));
        btnDelete.setLocalText(getResources().getString(R.string.delete));
        btnCancel.setLocalText(getResources().getString(R.string.cancel));
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
        }
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
    }

    public void toDeletePanel() {
        AlertDialogDelete.createBuilder(getDelegateActivity())
                .setCancelListener(new AlertDialogDelete.AlertCancelClickCallback() {
                    @Override
                    public void onClick() {
                        doDeletePanel();
                    }
                })
                .setAutoDissmiss(true)
                .setContent(getResources().getString(R.string.delete_the_panel_confirm))
                .setOk(getResources().getString(R.string.cancel))
                .setCancel(getResources().getString(R.string.delete))
                .preBuilder()
                .show();
    }

    private void doDeletePanel() {
        DDLog.i(TAG, "doDeletePanel");
        showTimeOutLoadinFramgmentWithErrorAlert();
        mPanelDevice.submit(PanelParamsHelper.deletePanel());
    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPanelDevice != null) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelCmd.DELETE_PANEL.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                showErrorToast();
            }
        }
    }
}
