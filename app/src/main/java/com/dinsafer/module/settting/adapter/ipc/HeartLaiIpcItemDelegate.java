package com.dinsafer.module.settting.adapter.ipc;

import android.net.Uri;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.ipc.common.IPCSettingFragment;
import com.dinsafer.module.ipc.heartlai.GoNetworkConfigFragmentEvent;
import com.dinsafer.module.ipc.heartlai.HeartLaiFullPlayActivity;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.ApStepHeartLaiIpcFragment;
import com.dinsafer.module.settting.ui.EdittextDialog;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/6/28
 */
public class HeartLaiIpcItemDelegate extends BaseIPCItemDelegate {

    private boolean showStateIcon;
    private String cacheName;

    public HeartLaiIpcItemDelegate(MainActivity mMainActivity, boolean showStateIcon, String cacheName) {
        super(mMainActivity);
        this.showStateIcon = showStateIcon;
        this.cacheName = cacheName;
    }

    @Override
    public void onBindItemViewHolder(MainPanelIpcItemViewHolder itemHolder, int position, Device device) {
        super.onBindItemViewHolder(itemHolder, position, device);
        String name = DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, "");
        if (TextUtils.isEmpty(name)) {
            name = cacheName;
        }
        itemHolder.mTvIpcName.setText(name);
        // 状态
        itemHolder.setDotVisible(false);
        itemHolder.mTvBattery.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        itemHolder.mIvWifiRssi.setVisibility(View.GONE);
        if (!device.getFlagLoaded()) {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_connecting));
            itemHolder.getCameraVideoView().showLoading();
        } else if (HeartLaiUtils.isDeviceConnected(device)) {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_online));
            itemHolder.getCameraVideoView().showPlay();
            if (HeartLaiUtils.isDefaultPassword(device)) {
                itemHolder.getCameraVideoView().showError();
            }
        } else if (HeartLaiUtils.isDeviceConnecting(device)) {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_connecting));
            itemHolder.getCameraVideoView().showLoading();
        } else if (HeartLaiUtils.isDeviceWrongPassword(device)) {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_wrong_password));
            itemHolder.getCameraVideoView().showError();
        } else {
            itemHolder.mTvBattery.setLocalText(mMainActivity.getResources().getString(R.string.ipc_status_offline));
            itemHolder.getCameraVideoView().showError();
        }

        itemHolder.mTvBattery.setVisibility(showStateIcon ? View.VISIBLE : View.GONE);

        itemHolder.mIvMore.setOnClickListener(v -> clickMore(device));

        // 预览图
        final String snapshotPath = DeviceHelper.getString(device, HeartLaiConstants.ATTR_SNAPSHOT, "");
        if (!TextUtils.isEmpty(snapshotPath)) {
            File file = new File(snapshotPath);
            if (file.exists()) {
                itemHolder.getCameraVideoView().setCoverImageUri(Uri.fromFile(file));
            } else {
                itemHolder.getCameraVideoView().setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
            }
        } else {
            itemHolder.getCameraVideoView().setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
        }
    }

    @Override
    public void onPlayIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {
        super.onPlayIconClick(device, position, videoViewRoot, parent);
        if (holder.isEditMode()) {
            return;
        }

        if (HomeManager.getInstance().isAdmin()) {
            HeartLaiFullPlayActivity.startActivity(mMainActivity, DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""));
        } else {
            if (HeartLaiUtils.isDeviceConnected(device)) {
                HeartLaiFullPlayActivity.startActivity(mMainActivity, DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""));
            }
        }
    }

    @Override
    public void onErrorIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {
        super.onErrorIconClick(device, position, videoViewRoot, parent);
        if (holder.isEditMode()) {
            return;
        }
        if (checkIsDefaultPassword(device)) {
            return;
        }

        if (HeartLaiUtils.isDeviceWrongPassword(device)) {
            if (HomeManager.getInstance().isAdmin()) {
                toShowWrongPasswordDialog(device);
            }
            return;
        }

        if (HeartLaiUtils.isDeviceConnected(device)) {
            HeartLaiFullPlayActivity.startActivity(mMainActivity, DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""));
        } else if (HeartLaiUtils.isDeviceConnecting(device)) {
            // do nothing
        } else {
            // offline
            if (HomeManager.getInstance().isAdmin()) {
                showDeviceOfflineDialog(device);
            }
        }

    }

    @Override
    public void onFullscreenIconClick(Device device, int position, CameraVideoView videoView, View parent) {
        super.onFullscreenIconClick(device, position, videoView, parent);

        if (checkIsDefaultPassword(device)) {
            return;
        }

        if (HomeManager.getInstance().isAdmin()) {
            HeartLaiFullPlayActivity.startActivity(mMainActivity, DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""));
        } else {
            if (HeartLaiUtils.isDeviceConnected(device)) {
                HeartLaiFullPlayActivity.startActivity(mMainActivity, DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""));
            }
        }
    }

    private void clickMore(Device device) {
        if (checkIsDefaultPassword(device)) {
            return;
        }
        mMainActivity.addCommonFragment(IPCSettingFragment.newInstance(device.getId()));
    }

    private boolean checkIsDefaultPassword(Device device) {
        if (HeartLaiUtils.isDeviceConnected(device) && HeartLaiUtils.isDefaultPassword(device)) {
            if (LocalKey.ADMIN == HomeManager.getInstance().getCurrentHome().getLevel()) {
                showNeedChangePasswordDialog(device);
            }
            return true;
        }
        return false;
    }

    private void showNeedChangePasswordDialog(Device device) {
        mMainActivity.showToast(mMainActivity.getResources().getString(R.string.ipc_need_change_password),
                new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        EdittextDialog.createBuilder(mMainActivity)
                                .setOk(mMainActivity.getResources().getString(R.string.Confirm))
                                .setCancel(mMainActivity.getResources().getString(R.string.Cancel))
                                .setDefaultName(HeartLaiConstants.DEFAULT_HEARTLAI_PWD)
                                .setContent(mMainActivity.getResources().getString(R.string.ipc_input_password))
                                .setAutoDismiss(false)
                                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                                    @Override
                                    public void onOkClick(EdittextDialog dialog, String string) {
//                                            change ipc password
                                        if (TextUtils.isEmpty(string)) {
                                            return;
                                        }

                                        mMainActivity.showBlueTimeOutLoadinFramgmentWithBack();
                                        Map<String, Object> args = new HashMap<>();
                                        args.put("cmd", HeartLaiCmd.CMD_CHANGE_PASSWORD);
                                        args.put("password", string);
                                        device.submit(args);
                                        dialog.dismiss();
                                    }
                                })
                                .preBuilder()
                                .show();

                    }
                });
        return;
    }

    private AlertDialogV2 offlineDialog;

    public void showDeviceOfflineDialog(Device device) {
        if (offlineDialog != null && offlineDialog.isShowing()) {
            return;
        }
        AlertDialogV2.Builder builder = AlertDialogV2.createBuilder(mMainActivity)
                .setContent(mMainActivity.getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setCancel(mMainActivity.getResources().getString(R.string.cancel))
                .setOk(mMainActivity.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        Map<String, Object> args = new HashMap<>();
                        args.put("cmd", HeartLaiCmd.CMD_DISCONNECT);
                        device.submit(args);

                        Map<String, Object> args1 = new HashMap<>();
                        args1.put("cmd", HeartLaiCmd.CMD_CONNECT);
                        args1.put("discardCache", true);
                        device.submit(args1);
                    }
                });

        if (LocalKey.ADMIN == HomeManager.getInstance().getCurrentHome().getLevel()) {
            builder.setOkV2(mMainActivity.getResources().getString(R.string.ipc_reconfigure_the_network))
                    .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            EventBus.getDefault().post(new GoNetworkConfigFragmentEvent(device.getId()));
                        }
                    });
        }

        offlineDialog = builder
                .preBuilder();
        offlineDialog.show();
    }

    public void toShowWrongPasswordDialog(Device device) {
        try {
            if (HeartLaiUtils.isHeartLaiDevice(device)) {
                AlertDialogV2.createBuilder(mMainActivity)
                        .setOk(mMainActivity.getResources().getString(R.string.ipc_input_password_hint))
                        .setOkV2(mMainActivity.getResources().getString(R.string.reset))
                        .setCancel(mMainActivity.getResources().getString(R.string.Cancel))
                        .setContent(mMainActivity.getResources().getString(R.string.ipc_wrong_password_ap))
                        .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                EdittextDialog.createBuilder(mMainActivity)
                                        .setOk(mMainActivity.getResources().getString(R.string.Confirm))
                                        .setCancel(mMainActivity.getResources().getString(R.string.Cancel))
                                        .setDefaultName("")
                                        .setContent(mMainActivity.getResources().getString(R.string.ipc_input_password))
                                        .setAutoDismiss(true)
                                        .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                                            @Override
                                            public void onOkClick(EdittextDialog dialog, String string) {
                                                if (TextUtils.isEmpty(string)) {
                                                    return;
                                                }
                                                device.getInfo().put(HeartLaiConstants.ATTR_PASSWORD, string);

                                                Map<String, Object> args1 = new HashMap<>();
                                                args1.put("cmd", HeartLaiCmd.CMD_CONNECT);
                                                args1.put("discardCache", false);
                                                device.submit(args1);

                                                dialog.dismiss();
                                            }
                                        })
                                        .preBuilder()
                                        .show();
                            }
                        })
                        .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {

                                mMainActivity.addCommonFragment(
                                        ApStepHeartLaiIpcFragment.newInstance(
                                                DeviceHelper.getString(device, HeartLaiConstants.ATTR_SOURCE_DATA, "{}"),
                                                false,
                                                HeartLaiUtils.getSourceDataIpcType(device),
                                                true

                                        )
                                );
                            }
                        })
                        .preBuilder()
                        .show();
            }
        } catch (Exception e) {

        }
    }


}
