package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.WheelView;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/11.
 */
public class TimePicker extends BaseFragment {

    private static final String TITLE = "title";

    private static final String DATA = "data";


    LocalTextView commonBarTitle;

    WheelView timerPicker;
    LocalCustomButton timerPickerSave;


    private String mTitle;

    private ArrayList mData;

    private ITimePickerCallBack callBack;

    private int defaultIndex;

    public static TimePicker newInstance(String title, ArrayList data, int defaultIndex) {
        TimePicker picker = new TimePicker();
        Bundle args = new Bundle();
        args.putString(TITLE, title);
        args.putParcelableArrayList(DATA, data);
//        args.putSerializable("callback", callBack);
        args.putInt("defaultIndex", defaultIndex);
        picker.setArguments(args);
        return picker;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.timer_picker, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.timer_picker_save).setOnClickListener( v -> toSave());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        timerPicker = rootView.findViewById(R.id.timer_picker);
        timerPickerSave = rootView.findViewById(R.id.timer_picker_save);
    }


    @Override
    public void initData() {
//        callBack = (ITimePickerCallBack) getArguments().getSerializable("callback");
        mTitle = getArguments().getString(TITLE);
        mData = getArguments().getParcelableArrayList(DATA);
        commonBarTitle.setLocalText(mTitle);

        defaultIndex = getArguments().getInt("defaultIndex");
        timerPicker.setData(mData);
        timerPicker.setDefault(defaultIndex);
        timerPickerSave.setLocalText(getResources().getString(R.string.sos_message_dialog_save));
    }

    public ITimePickerCallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(ITimePickerCallBack callBack) {
        this.callBack = callBack;
    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void toSave() {
        if (callBack != null) {
            callBack.getSelect(timerPicker.getSelectedText(), timerPicker.getSelected());
            removeSelf();
        }
    }

    public interface ITimePickerCallBack extends Serializable {
        void getSelect(String select, int selectIndex);
    }

}
