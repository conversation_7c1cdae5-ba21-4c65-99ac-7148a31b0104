package com.dinsafer.module.settting.ui;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentThirdPartLicenseBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.ThirdPartLicenseItem;
import com.dinsafer.module.settting.adapter.ThirdPartLicenseModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.ThirdPartLicenseHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * 使用到的第三方库展示页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/12 4:20 下午
 */
public class ThirdPartLicenseFragment extends BaseFragment {

    private FragmentThirdPartLicenseBinding mBinding;
    private BindMultiAdapter<ThirdPartLicenseModel> mAdapter;
    private List<ThirdPartLicenseModel> mDataList;

    public static ThirdPartLicenseFragment newInstance() {
        return new ThirdPartLicenseFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_third_part_license, container, false);
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.third_party_licenses));

        mDataList = new ArrayList<>();
        List<ThirdPartLicenseItem> allLicenses = ThirdPartLicenseHelper.getAllDependencyLibraryLicenses();
        if (null != allLicenses && allLicenses.size() > 0) {
            for (ThirdPartLicenseItem licenseItem : allLicenses) {
                mDataList.add(new ThirdPartLicenseModel(licenseItem));
            }
        }
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        mBinding.rcvLicense.setLayoutManager(layoutManager);
        mAdapter = new BindMultiAdapter<>();
        mBinding.rcvLicense.setAdapter(mAdapter);
        mAdapter.setNewData(mDataList);
    }
}
