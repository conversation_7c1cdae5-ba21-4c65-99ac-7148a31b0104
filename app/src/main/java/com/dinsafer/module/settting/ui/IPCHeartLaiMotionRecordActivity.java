//package com.dinsafer.module.settting.ui;
//
//import android.app.Activity;
//import android.content.pm.ActivityInfo;
//import android.content.res.Configuration;
//import android.media.MediaPlayer;
//import android.os.Bundle;
//import android.os.Handler;
//import android.util.Log;
//import android.view.MotionEvent;
//import android.view.View;
//import android.view.animation.Animation;
//import android.view.animation.AnimationUtils;
//import android.view.animation.LinearInterpolator;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.RelativeLayout;
//import android.widget.TextView;
//
//import com.daimajia.numberprogressbar.NumberProgressBar;
//import com.dinsafer.config.DDGlobalEnv;
//import com.dinsafer.dinnet.R;
//import com.dinsafer.module.ipc.heartlai.record.DownloadListener;
//import com.dinsafer.module.ipc.heartlai.record.HeartLaiRecordFileUtil;
//import com.dinsafer.ui.FullScreenVideo;
//import com.dinsafer.ui.LocalTextView;
//import com.dinsafer.util.DDFileUtil;
//import com.githang.statusbar.StatusBarCompat;
//
//import java.text.SimpleDateFormat;
//
//import butterknife.BindView;
//import butterknife.ButterKnife;
//import butterknife.OnClick;
//
//
//
//public class IPCHeartLaiMotionRecordActivity extends Activity {
//    private String TAG = "HeartLai";
//
//    @BindView(R.id.common_bar_back)
//    ImageView commonBarBack;
//    @BindView(R.id.common_bar_title)
//    LocalTextView commonBarTitle;
//    @BindView(R.id.video_view)
//    FullScreenVideo videoView;
//    @BindView(R.id.resume_pause)
//    ImageView resumePause;
//    @BindView(R.id.glview_fullscreen)
//    ImageView glviewFullscreen;
//    @BindView(R.id.volume_fullscreen)
//    ImageView volumeFullscreen;
//    @BindView(R.id.seekBar_fullscreen)
//    NumberProgressBar seekBarFullscreen;
//    @BindView(R.id.fullscreen_control)
//    LinearLayout fullscreenControl;
//    @BindView(R.id.seekBar)
//    NumberProgressBar seekBar;
//    @BindView(R.id.resume_pause_btn)
//    ImageView resumePauseBtn;
//    @BindView(R.id.control_view)
//    LinearLayout controlView;
//    @BindView(R.id.contentLayout)
//    LinearLayout contentLayout;
//    @BindView(R.id.common_bar)
//    RelativeLayout commonbar;
//    @BindView(R.id.seekBar_fullscreen_time)
//    TextView seekBarFullscreenTime;
//    @BindView(R.id.seekbar_time)
//    TextView seekbarTime;
//    @BindView(R.id.img_loading)
//    ImageView imgLoading;
//
//    private String url;
//
//    private String ipcName;
//
//    private boolean isFullScreen = false;
//
//    private SimpleDateFormat sdf = new SimpleDateFormat("mm:ss");
//
//    private boolean isAnimation = true;
//
//    private String outputPath;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_ipc_sos_record);
//        ButterKnife.bind(this);
//        initData();
//        StatusBarCompat.setStatusBarColor(this, getResources().getColor(R.color.black), false);
//
//    }
//
//    @OnClick(R.id.glview_fullscreen)
//    public void toFullScreen() {
//        if (!isFullScreen)
//            makeVideoFullScreen();
//        else
//            exitVideoFullScreen();
//        isFullScreen = !isFullScreen;
//    }
//
//    private void initData() {
//        url = getIntent().getStringExtra("url");
//        ipcName = getIntent().getStringExtra("ipcname");
//        commonBarTitle.setLocalText(ipcName);
//
//        videoView.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
//            @Override
//            public void onPrepared(MediaPlayer mp) {
//                if (isAnimation) {
//                    isAnimation = false;
//                    imgLoading.setVisibility(View.GONE);
//                    imgLoading.clearAnimation();
//                    glviewFullscreen.setVisibility(View.VISIBLE);
//                }
//                handler.post(run);
////                mp.setOnBufferingUpdateListener(new MediaPlayer.OnBufferingUpdateListener() {
////                    @Override
////                    public void onBufferingUpdate(MediaPlayer mp, int percent) {
////                        // 获得当前播放时间和当前视频的长度
////                        int currentPosition = videoView.getCurrentPosition();
////                        int duration = videoView.getDuration();
//////                        int time = ((currentPosition * 100) / duration);
////                        // 设置进度条的主要进度，表示当前的播放时间
////                        seekBar.setProgress(currentPosition);
////                        seekBar.setMax(duration);
////                        seekBarFullscreen.setProgress(currentPosition);
////                        seekbarTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
////                        seekBarFullscreenTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
////                        seekBarFullscreen.setMax(duration);
////                    }
////                });
//                mp.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
//                    @Override
//                    public void onCompletion(MediaPlayer mp) {
//                        seekBar.setProgress(0);
//                        seekBarFullscreen.setProgress(0);
//                        resumePause.setVisibility(View.VISIBLE);
//                        videoView.seekTo(0);
//                        videoView.pause();
//                    }
//                });
//
//            }
//        });
//
//        videoView.setOnTouchListener(new View.OnTouchListener() {
//            @Override
//            public boolean onTouch(View v, MotionEvent event) {
//                if (event.getAction() == MotionEvent.ACTION_UP)
//                    clickPlay();
//                return true;
//            }
//        });
//
//        videoView.setOnErrorListener(new MediaPlayer.OnErrorListener() {
//            @Override
//            public boolean onError(MediaPlayer mp, int what, int extra) {
//                DDFileUtil.deleteFile(outputPath);
//                return false;
//            }
//        });
//
//        Animation operatingAnim = AnimationUtils.loadAnimation(this, R.anim.rotation);
//        LinearInterpolator lin = new LinearInterpolator();
//        operatingAnim.setInterpolator(lin);
//        imgLoading.startAnimation(operatingAnim);
//        glviewFullscreen.setVisibility(View.GONE);
//
//        handleUrl(url);
//    }
//
//    private void handleUrl(String url) {
//        outputPath = DDGlobalEnv.DINNET_RECORD_FILE_PATH + url + ".mp4";
//        if (DDFileUtil.isFileExists(outputPath)) {
//            Log.w(TAG, "handleUrl: 本地已存在转码后的视频文件 " + outputPath);
//            playVideo(outputPath);
//        } else {
//            downloadFile(url);
//        }
//    }
//
//    /**
//     * 下载视频文件.avi
//     *
//     * @param key
//     */
//    private void downloadFile(String key) {
//        HeartLaiRecordFileUtil.downlaodRecord(key, new DownloadListener() {
//            @Override
//            public void onStart() {
//                //运行在子线程
//                Log.d(TAG, "onStart: ");
//            }
//
//            @Override
//            public void onProgress(int progress) {
//                //运行在子线程
//                Log.d(TAG, "onProgress: " + progress);
//            }
//
//            @Override
//            public void onFinish(String path) {
//                //运行在子线程
//                Log.d(TAG, "onFinish: " + path);
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        codecVideo(path);
//                    }
//                });
//            }
//
//            @Override
//            public void onFail(String errorInfo) {
//                //运行在子线程
//                Log.d(TAG, "onFail: " + errorInfo);
//            }
//        });
//    }
//
//    /**
//     * 转码
//     *
//     * @param inputPath
//     */
//    private void codecVideo(String inputPath) {
////        inputPath = "/storage/emulated/0/Dinnet/.record/heartlai/4.avi";
//        long executionId = FFmpeg.executeAsync("-vcodec hevc -i " + inputPath + " -preset ultrafast -y -b:v 500k " + outputPath, new ExecuteCallback() {
//
//            @Override
//            public void apply(final long executionId, final int returnCode) {
//                if (returnCode == RETURN_CODE_SUCCESS) {
//                    Log.i(TAG, "Async command execution completed successfully.");
//                    playVideo(outputPath);
//                } else if (returnCode == RETURN_CODE_CANCEL) {
//                    Log.i(TAG, "Async command execution cancelled by user.");
//                } else {
//                    //转码失败删除本地文件重新下载
//                    DDFileUtil.deleteFile(inputPath);
//                    Log.i(TAG, String.format("Async command execution failed with rc=%d.", returnCode));
//                }
//            }
//        });
//    }
//
//    /**
//     * 播放
//     *
//     * @param path
//     */
//    private void playVideo(String path) {
//        videoView.setVideoPath(path);
//        videoView.start();
//    }
//
//    private RelativeLayout.LayoutParams defaultVideoViewParams;
//    private int defaultScreenOrientationMode;
//
//    // play video in fullscreen mode
//    private void makeVideoFullScreen() {
//
//        defaultScreenOrientationMode = getResources().getConfiguration().orientation;
//        defaultVideoViewParams = (RelativeLayout.LayoutParams) videoView.getLayoutParams();
//        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
//        controlView.setVisibility(View.GONE);
//        fullscreenControl.setVisibility(View.VISIBLE);
//        commonbar.setVisibility(View.GONE);
//        glviewFullscreen.setImageResource(R.drawable.icon_ipc_small_screen);
//        videoView.postDelayed(new Runnable() {
//
//            @Override
//            public void run() {
//                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) new RelativeLayout.LayoutParams(
//                        RelativeLayout.LayoutParams.MATCH_PARENT,
//                        RelativeLayout.LayoutParams.MATCH_PARENT);
//
//                videoView.setLayoutParams(params);
//                videoView.layout(10, 10, 10, 10);
//            }
//        }, 100);
//    }
//
//    @OnClick(R.id.resume_pause)
//    public void clickPlay() {
//        if (videoView.isPlaying()) {
//            resumePause.setVisibility(View.VISIBLE);
//            videoView.pause();
//        } else {
//            resumePause.setVisibility(View.GONE);
//            videoView.start();
//        }
//    }
//
//    @OnClick(R.id.common_bar_back)
//    public void toBack() {
//        finish();
//    }
//
//    // close fullscreen mode
//    private void exitVideoFullScreen() {
//        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
//        controlView.setVisibility(View.VISIBLE);
//        fullscreenControl.setVisibility(View.GONE);
//        commonbar.setVisibility(View.VISIBLE);
//        commonBarTitle.setLocalText(ipcName);
//        glviewFullscreen.setImageResource(R.drawable.icon_ipc_full_screen);
//        videoView.postDelayed(new Runnable() {
//
//            @Override
//            public void run() {
//                videoView.setLayoutParams(defaultVideoViewParams);
//                videoView.layout(10, 10, 10, 10);
//            }
//        }, 100);
//    }
//
//
//    @Override
//    public void onConfigurationChanged(Configuration newConfig) {
//        super.onConfigurationChanged(newConfig);
//
//        // Checks the orientation of the screen
//        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            isFullScreen = true;
//            defaultScreenOrientationMode = getResources().getConfiguration().orientation;
//            defaultVideoViewParams = (RelativeLayout.LayoutParams) videoView.getLayoutParams();
//            controlView.setVisibility(View.GONE);
//            fullscreenControl.setVisibility(View.VISIBLE);
//            commonbar.setVisibility(View.GONE);
//            videoView.postDelayed(new Runnable() {
//
//                @Override
//                public void run() {
//                    RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) new RelativeLayout.LayoutParams(
//                            RelativeLayout.LayoutParams.MATCH_PARENT,
//                            RelativeLayout.LayoutParams.MATCH_PARENT);
//
//                    videoView.setLayoutParams(params);
//                    videoView.layout(10, 10, 10, 10);
//                }
//            }, 100);
//        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
//            isFullScreen = false;
//            setRequestedOrientation(defaultScreenOrientationMode);
//            controlView.setVisibility(View.VISIBLE);
//            fullscreenControl.setVisibility(View.GONE);
//            commonbar.setVisibility(View.VISIBLE);
//            videoView.postDelayed(new Runnable() {
//
//                @Override
//                public void run() {
//                    videoView.setLayoutParams(defaultVideoViewParams);
//                    videoView.layout(10, 10, 10, 10);
//                }
//            }, 100);
//        }
//    }
//
//    private Handler handler = new Handler();
//    private Runnable run = new Runnable() {
//        public void run() {
//            // 获得当前播放时间和当前视频的长度
//            // 获得当前播放时间和当前视频的长度
//            int currentPosition = videoView.getCurrentPosition();
//            int duration = videoView.getDuration();
////                        int time = ((currentPosition * 100) / duration);
//            // 设置进度条的主要进度，表示当前的播放时间
//            seekBar.setProgress(currentPosition);
//            seekBar.setMax(duration);
//            seekBarFullscreen.setProgress(currentPosition);
//            seekbarTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
//            seekBarFullscreenTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
//            seekBarFullscreen.setMax(duration);
//
//            handler.postDelayed(run, 1000);
//        }
//    };
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        videoView.setOnPreparedListener(null);
//        handler.removeCallbacks(run);
//        FFmpeg.cancel();
//    }
//
//}
//
