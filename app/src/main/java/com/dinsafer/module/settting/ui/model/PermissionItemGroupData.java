package com.dinsafer.module.settting.ui.model;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/14 4:36 下午
 */
public class PermissionItemGroupData {
    private @PermissionItemGroupModel.GroupType
    int groupType;
    private String groupTittle;
    private List<String> functions;

    private PermissionItemGroupData(@PermissionItemGroupModel.GroupType int groupType,
                                    @NonNull String groupTittle, List<String> functions) {
        this.groupType = groupType;
        this.groupTittle = groupTittle;
        this.functions = functions;
    }

    public int getGroupType() {
        return groupType;
    }

    public void setGroupType(@PermissionItemGroupModel.GroupType int groupType) {
        this.groupType = groupType;
    }

    public String getGroupTittle() {
        return groupTittle;
    }

    public void setGroupTittle(String groupTittle) {
        this.groupTittle = groupTittle;
    }

    public List<String> getFunctions() {
        return functions;
    }

    public void setFunctions(List<String> functions) {
        this.functions = functions;
    }

    public static class Builder {
        private @PermissionItemGroupModel.GroupType
        int groupType = PermissionItemGroupModel.GROUP_TYPE_MID;
        private String groupTittle;
        private final List<String> functions;

        public Builder() {
            functions = new ArrayList<>();
        }

        public Builder setGroupType(@PermissionItemGroupModel.GroupType int groupType) {
            this.groupType = groupType;
            return this;
        }

        public Builder setGroupTittle(String groupTittle) {
            this.groupTittle = groupTittle;
            return this;
        }

        public Builder addFunctions(@NonNull List<String> functions) {
            this.functions.addAll(functions);
            return this;
        }

        public Builder addFunction(@NonNull String function) {
            this.functions.add(function);
            return this;
        }

        public PermissionItemGroupData create() {
            return new PermissionItemGroupData(groupType, groupTittle, functions);
        }
    }
}
