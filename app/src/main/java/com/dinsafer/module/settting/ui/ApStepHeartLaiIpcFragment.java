package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.IpcApLayoutBinding;
import com.dinsafer.model.AppStateEvent;
import com.dinsafer.model.UserDeviceListChangeEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.ipc.heartlai.setting.HeartLaiWifiListFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.add.HeartLaiBinder;
import com.dinsafer.module_heartlai.add.ap.APNetworkConfigurer;
import com.dinsafer.module_heartlai.add.impl.HeartLaiNetworkConfigurer;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

public class ApStepHeartLaiIpcFragment extends MyBaseFragment<IpcApLayoutBinding> {

    private final static String KEY_IPC_TYPE = "ipcType";

    private String pid = "";

    private Device model;

    static Handler handler = new Handler();

    private HeartLaiNetworkConfigurer networkConfigurer;
    private HeartLaiBinder ipcBinder;

    public static ApStepHeartLaiIpcFragment newInstance(String key, boolean isAdd, int ipcType) {
        return newInstance(key, isAdd, ipcType, false);
    }

    public static ApStepHeartLaiIpcFragment newInstance(String key, boolean isAdd, int ipcType, boolean isReset) {
        ApStepHeartLaiIpcFragment apStepIpcFragment = new ApStepHeartLaiIpcFragment();
        Bundle bundle = new Bundle();
        bundle.putString("data", key);
        bundle.putBoolean("isAdd", isAdd);
        bundle.putBoolean("isReset", isReset);
        bundle.putInt(KEY_IPC_TYPE, ipcType);
        apStepIpcFragment.setArguments(bundle);
        return apStepIpcFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.ipc_ap_layout;
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getArguments().getBoolean("isReset") ?
                getResources().getString(R.string.reset)
                : getResources().getString(R.string.modify_plugs_network_ap));
        EventBus.getDefault().register(this);
        mBinding.commonBarBack.setOnClickListener(v -> toClose());

        String hint = Local.s(getResources().getString(R.string.ipc_ap_hint));

        mBinding.ipcApNext.setLocalText(getResources().getString(R.string.ipc_ap_change_wifi));
        mBinding.ipcApNext.setOnClickListener(v -> toNext());
        String uri = "json/animation_open_ipc_ap.json";
        if (isTypeGun()) {
            uri = "json/animation_open_ipc_ap_2.json";
        } else if (isTypeShake()) {
            uri = "json/animation_open_ipc_ap_3.json";
        }
        mBinding.ipcApImage.setAnimation(uri);
        mBinding.ipcApImage.setRepeatCount(ValueAnimator.INFINITE);


        JSONObject object = null;
        if (getArguments().getBoolean("isAdd")) {
            ipcBinder = new HeartLaiBinder(getContext());
            networkConfigurer = (HeartLaiNetworkConfigurer) ipcBinder.getNetworkConfigurer();
        } else {
            networkConfigurer = new HeartLaiNetworkConfigurer();
        }
        try {
            object = new JSONObject(getArguments().getString("data"));
            object.put("password", HeartLaiConstants.DEFAULT_HEARTLAI_PWD);
            Log.d(TAG, "initData: " + object.toString());
            Bundle bundle = new Bundle();
            bundle.putString("id", DDJSONUtil.getString(object, "id"));
            bundle.putString("home_id", HomeManager.getInstance().getCurrentHome().getHomeID());
            bundle.putBoolean("isAdd", getArguments().getBoolean("isAdd"));
            bundle.putBoolean("isAutoDisconnectAp", true);
            bundle.putString("data", object.toString());

            networkConfigurer.setConfigParms(getDelegateActivity(), bundle);
        } catch (JSONException e) {

        }

        pid = DDJSONUtil.getString(object, "pid");
        DDLog.d(TAG, "id is " + pid);
        mBinding.ipcApHint.setLocalText(hint.replace(APIKey.IPC_AP_NAM,
                APIKey.HEART_IPC_AP + "***" + pid.substring(8, 17)));

        networkConfigurer.addConnectListener(new APNetworkConfigurer.OnConnectListener() {
            @Override
            public void onConnectStateChange(int state) {

            }

            @Override
            public void onConnectSuccess() {
                getDelegateActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        handler.removeCallbacksAndMessages(null);
//                    closeLoadingFragment();
                        HeartLaiWifiListFragment wifiListFragment = HeartLaiWifiListFragment.newInstance(DeviceHelper.getString(model, HeartLaiConstants.ATTR_CAMERA_PID, ""),
                                true, getArguments().getBoolean("isAdd"), true, networkConfigurer);
                        getMainActivity().addCommonFragment(wifiListFragment);
                    }
                });
            }

            @Override
            public void onConnectFail(int state) {

            }
        });

    }

    /**
     * 摄像头是否是枪机类型
     * 0:卡片
     * 1：枪机
     * 2：摇头
     *
     * @return true: 枪机类型摄像头
     */
    private boolean isTypeGun() {
        int ipcType = getArguments().getInt(KEY_IPC_TYPE);
        return 1 == ipcType;
    }

    private boolean isTypeShake() {
        int ipcType = getArguments().getInt(KEY_IPC_TYPE);
        return 2 == ipcType;
    }

    @Override
    public void onFinishAnim() {
        toStartAnim();
        if (!DDSystemUtil.isOpenGPS(getContext())) {
            toOpenGPS(0);
        }
    }

    @Override
    public void onEnterFragment() {
        toStartAnim();
    }

    private void toStartAnim() {
        if (mBinding.ipcApImage == null) {
            return;
        }

        mBinding.ipcApImage.playAnimation();
    }


    public void toNext() {
        if (!getDelegateActivity().isFragmentInTop(this)) {
            return;
        }

        if (DDSystemUtil.isMarshmallow() && ContextCompat.checkSelfPermission(getActivity(),
                Manifest.permission.ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            getMainActivity().setNotNeedToLogin(true);
            PermissionDialogUtil.showNeedWIFILocationDialog(getMainActivity());
            return;
        }

        if (!DDSystemUtil.isOpenGPS(getContext())) {
            toOpenGPS(0);
            return;
        }

        String wifiSSID = DDSystemUtil.getWIFISSID(getDelegateActivity());
        DDLog.d(TAG, "wifiSSID is " + wifiSSID);
        DDLog.d(TAG, "APIKey.IPC_AP is " + APIKey.HEART_IPC_AP + "***" + pid.substring(8, 17));
        if (wifiSSID != null && wifiSSID.equals(APIKey.HEART_IPC_AP + "***" + pid.substring(8, 17))) {
            showLoadingFragment(LoadingFragment.BLACK, "");
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    closeLoadingFragment();
                    showErrorToast();
                }
            }, 60 * 1000);
            if (HeartLaiUtils.isDeviceConnected(model)) {
                handler.removeCallbacksAndMessages(null);
//                    closeLoadingFragment();
                HeartLaiWifiListFragment wifiListFragment = HeartLaiWifiListFragment.newInstance(DeviceHelper.getString(model, HeartLaiConstants.ATTR_CAMERA_PID, ""),
                        true, getArguments().getBoolean("isAdd"), true, networkConfigurer);
                getMainActivity().addCommonFragment(wifiListFragment);
            } else {
                networkConfigurer.connect();
            }
        } else {
            getMainActivity().setNotNeedToLogin(true);
            startActivity(new Intent(android.provider.Settings.ACTION_WIFI_SETTINGS));
        }
    }

    private void toStopAnim() {
        getMainActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (mBinding.ipcApImage == null) {
                    return;
                }
                mBinding.ipcApImage.pauseAnimation();
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AppStateEvent event) {
        if (event.isBackground()
                || !getDelegateActivity().isFragmentInTop(this)) {
            return;
        }
        if (DDSystemUtil.isMarshmallow() && ContextCompat.checkSelfPermission(getActivity(),
                Manifest.permission.ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        if (!DDSystemUtil.isOpenGPS(getContext())) {
            return;
        }

        String wifiSSID = DDSystemUtil.getWIFISSID(getDelegateActivity());
        if (wifiSSID != null && wifiSSID.equals(APIKey.HEART_IPC_AP + "***" + pid.substring(8, 17))) {
            showLoadingFragment(LoadingFragment.BLACK, "");
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    closeLoadingFragment();
                    showErrorToast();
                }
            }, 60 * 1000);
//                    CameraManager.getInstance().reconnectCamera(model);
            networkConfigurer.reconnect();
        } else {
            getDelegateActivity().showToast(getResources().getString(R.string.ipc_ap_wrong),
                    getResources().getString(R.string.ipc_ap_add),
                    getResources().getString(R.string.ipc_ap_cancel),
                    new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {

                        }
                    },
                    new AlertDialog.AlertCancelClickCallback() {
                        @Override
                        public void onClick() {
                            if (getArguments().getBoolean("isAdd"))
                                getDelegateActivity().removeAllCommonFragment();
                            else
                                getDelegateActivity().removeToFragment(ModifyASKPlugsFragment.class.getName());
                        }
                    });
        }
    }

    public void toClose() {
        getDelegateActivity().showToast(getResources().getString(R.string.ipc_ap_exit),
                getResources().getString(R.string.ipc_ap_add),
                getResources().getString(R.string.ipc_ap_cancel),
                new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
//                        if (getArguments().getBoolean("isAdd"))
//                            getDelegateActivity().removeAllCommonFragment();
//                        else
//                            getDelegateActivity().removeToFragment(ModifyASKPlugsFragment.class.getName());
                        removeSelf();
                    }
                },
                new AlertDialog.AlertCancelClickCallback() {
                    @Override
                    public void onClick() {
                    }
                });
    }

    @Override
    public boolean onBackPressed() {
        toClose();
        return true;
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
        toStopAnim();
        handler.removeCallbacksAndMessages(null);
        if (!getMainActivity().wsIsConnect()) {
//            主页取消全屏loading
//            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
            EventBus.getDefault().post(new UserDeviceListChangeEvent());
            CommonDataUtil.getInstance().getAllData();
        }
        if (networkConfigurer != null) {
            networkConfigurer.destory();
        }
    }

}
