package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.app.password.PasswordActivity;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DBUtil;

import androidx.annotation.Nullable;

/**
 * Created by Rinfon on 16/7/8.
 */
public class PasswordSettingFragment extends BaseFragment {

    LocalTextView commonBarTitle;
    LocalTextView passwordSetPass;
    LocalTextView passwordSetPassChange;
    LocalTextView passwordHint;

    public static PasswordSettingFragment newInstance() {
        return new PasswordSettingFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.setting_password_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.password_set_pass).setOnClickListener( v -> toSetPassword());
        rootView.findViewById(R.id.password_set_pass_change).setOnClickListener( v -> toChangePasssword());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        passwordSetPass = rootView.findViewById(R.id.password_set_pass);
        passwordSetPassChange = rootView.findViewById(R.id.password_set_pass_change);
        passwordHint = rootView.findViewById(R.id.password_hint);
    }

    @Override
    public void initData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.app_setting_password));


        passwordHint.setLocalText(getResources().getString(R.string.password_set_password_hint));
        if (DBUtil.Exists(DBKey.APP_PASSWORD)) {
            passwordSetPass.setLocalText(getResources().getString(R.string.password_set_password_close));
            passwordSetPassChange.setLocalText(getResources().getString(R.string.password_set_password_change));
            passwordSetPassChange.setAlpha(0.8f);
            passwordSetPassChange.setEnabled(true);
        } else {
            passwordSetPass.setLocalText(getResources().getString(R.string.password_set_password));
            passwordSetPassChange.setLocalText(getResources().getString(R.string.password_set_password_change));
            passwordSetPassChange.setAlpha(0.5f);
            passwordSetPassChange.setEnabled(false);
        }
    }

    public void toSetPassword() {
        if (DBUtil.Exists(DBKey.APP_PASSWORD)) {
            PasswordActivity activity = PasswordActivity.newInstance(false);
            activity.setCallBack(new PasswordActivity.IPasswordCallBack() {
                @Override
                public void onSuccess() {
                    DBUtil.Delete(DBKey.APP_PASSWORD);
                    initData();
                }

                @Override
                public void onFail() {
                    getMainActivity().toLogout(true, false);
                }
            });
            getDelegateActivity().addCommonFragment(activity);
        } else {
            getDelegateActivity().addCommonFragment(AppPasswordFragment.newInstance());
        }
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        initData();
    }

    public void toChangePasssword() {
        PasswordActivity activity = PasswordActivity.newInstance(false);
        activity.setCallBack(new PasswordActivity.IPasswordCallBack() {
            @Override
            public void onSuccess() {
                getDelegateActivity().addCommonFragment(AppPasswordFragment.newInstance());
            }

            @Override
            public void onFail() {
                getMainActivity().toLogout(true, false);
            }
        });
        getDelegateActivity().addCommonFragment(activity);

    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        toCloseInput();
    }

}

