package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.LocalTextView;

/**
 * Created by Rinfon on 16/7/8.
 */
public class HelpFragment extends BaseFragment {

    LocalTextView commonBarTitle;

    WebView mWebview;

    public static HelpFragment newInstance() {
        return new HelpFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.setting_webview_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        mWebview = rootView.findViewById(R.id.setting_webview);
    }

    @Override
    public void initData() {
        showTimeOutLoadinFramgmentWithBack();
        commonBarTitle.setLocalText(getResources().getString(R.string.help_title));
        mWebview.getSettings().setJavaScriptEnabled(true);
        mWebview.getSettings().setAllowFileAccess(true);
        mWebview.setBackgroundColor(Color.TRANSPARENT);
        mWebview.getSettings()
                .setPluginState(WebSettings.PluginState.ON);
        mWebview.setWebViewClient(new WebViewClient() {

            public void onPageFinished(WebView view, String url) {
                closeLoadingFragment();
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                return super.shouldOverrideUrlLoading(view, url);
            }
        });
        mWebview.loadUrl(APIKey.HELP);
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

}

