package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentArmRulesBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.Map;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/11/16 5:54 下午
 */
public class ArmRulesFragment extends BaseFragment implements IDeviceCallBack {

    private FragmentArmRulesBinding mBinding;
    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static ArmRulesFragment newInstance() {
        return new ArmRulesFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater,
                             @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // showLoadingFragment(LoadingFragment.BLACK, "");
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_arm_rules, container, false);
        findPanel();
        initView(mBinding.getRoot(), savedInstanceState);
        initData();
        return mBinding.getRoot();
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            // closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        // mPanelDevice.submit(PanelParamsHelper.getAdvancedSetting());
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPanelDevice != null) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.device_managent_arm_rules));
        mBinding.tvCustomHomeArm.setLocalText(getString(R.string.device_managent_home_arm));
        mBinding.tvReadyToArm.setLocalText(getString(R.string.ready_to_arm));
        mBinding.advancedSettingEntryDelay.setLocalText(getResources().getString(R.string.advanced_setting_entry_delay));
        mBinding.advancedSettingExitDelay.setLocalText(getResources().getString(R.string.advanced_setting_exit_delay));

        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.rlCustomHomeArm.setOnClickListener(v -> getDelegateActivity().addCommonFragment(DefineHomeArmFragment.newInstance(false)));
        mBinding.rlReadyToArm.setOnClickListener(v -> getDelegateActivity().addCommonFragment(ReadyToArmSettingFragment.newInstance()));
        mBinding.rlAdvancedSettingEntryDelay.setOnClickListener(v -> getDelegateActivity().addCommonFragment(DefineHomeArmFragment.newInstance(true)));
        mBinding.rlAdvancedSettingExitDelay.setOnClickListener(v -> {
            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loading));
            mPanelDevice.submit(PanelParamsHelper.getExitDelay());
        });
    }

    private void toChooseExitDelayTime2(int time, boolean soundEnable) {
        TimePicker2 timePicker2 = TimePicker2.newInstance(Local.s(getResources().getString(R.string.exit_delay_time)), time, soundEnable);
        timePicker2.setCallBack(new TimePicker2.ITimePickerCallBack() {
            @Override
            public void getSelect(int seconds, boolean isSoundEnable) {
                showLoadingFragment(LoadingFragment.BLUE);
                isSelfOperate = true;
                mPanelDevice.submit(PanelParamsHelper.setExitDelay(seconds, isSoundEnable));
            }
        });

        getDelegateActivity().addCommonFragment(timePicker2);
    }


    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        DDLog.i(TAG, "On advance setting cmd result: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (PanelCmd.GET_EXITDELAY.equals(cmd)) {
            // 获取延时布防信息
            onGetExitDelayInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_EXITDELAY.equals(cmd)) {
                // 设置延时布防信息
                onSetExitDelay(status, map);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置的延时布防信息
     */
    private void onGetExitDelayInfo(int status, Map map) {
        DDLog.i(TAG, "onGetExitDelayInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        int time = DeviceHelper.getInt(resultMap, PanelDataKey.TIME_COMMON, 0);
        boolean soundEnable = DeviceHelper.getBoolean(resultMap, PanelDataKey.EXIT_DELAY_SOUND, false);
        toChooseExitDelayTime2(time, soundEnable);
    }

    /**
     * 设置延时布防信息
     */
    private void onSetExitDelay(int status, Map map) {
        DDLog.i(TAG, "onSetExitDelay, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            showSuccess();
        } else {
            showErrorToast();
        }
    }

}
