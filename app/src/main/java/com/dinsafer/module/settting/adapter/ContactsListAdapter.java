package com.dinsafer.module.settting.adapter;

import static com.dinsafer.model.panel.MainPanelHelper.VIEW_ENABLE_ALPHA;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.ContactItem;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.BaseTextDrawable;
import com.dinsafer.ui.CircularView;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.Local;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class ContactsListAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<ContactItem> mData;

    private int[] backgroundColor = {R.color.colorMainFragmentListViewItemBG_1,
            R.color.colorMainFragmentListViewItemBG_2};

    public static final int TYPE_ITEM = 1;

    public static final int TYPE_OTHER = 2;

    public static final int TYPE_HEADER = 0;

    private OnHeaderAddClickListener mOnHeaderAddClickListener;

    private final int mRole;

    public ContactsListAdapter(Activity mActivity, ArrayList<ContactItem> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
        mRole = HomeManager.getInstance().getCurrentHome().getLevel();
    }

    @Override
    public int getItemViewType(int position) {
        return mData.get(position).getType();
    }

    @Override
    public int getViewTypeCount() {
        return 3;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        int type = getItemViewType(position);
        if (type == TYPE_HEADER) {
            HeaderViewHolderNew holder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.contacts_item_header_new, null);
                holder = new HeaderViewHolderNew(convertView, mOnHeaderAddClickListener);
                convertView.setTag(holder);
            } else {
                try {
                    holder = (HeaderViewHolderNew) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            holder.setTittleSrc(mData.get(position).getName());
        } else if (type == TYPE_ITEM) {
            ViewHolder itemHolder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.contact_list_item, null);
                itemHolder = new ViewHolder(convertView);
                convertView.setTag(itemHolder);
            } else {
                try {
                    itemHolder = (ViewHolder) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            final String itemUid = mData.get(position).getUid();
            final boolean editable = mRole == LocalKey.ADMIN
                    || (mRole == LocalKey.USER
                    && itemUid != null
                    && DinSDK.getUserInstance().getUser() != null
                    && itemUid.equals(DinSDK.getUserInstance().getUser().getUid()));
            itemHolder.background.setAlpha(VIEW_ENABLE_ALPHA);
            itemHolder.contactItemAvatar.setVisibility(View.VISIBLE);
            itemHolder.contactItemStatus.setVisibility(View.VISIBLE);
            itemHolder.contactItemName.setText(mData.get(position).getUid());
            itemHolder.contactItemDescription.setText(getDescription(mData.get(position)));
            itemHolder.background.setBackgroundColor(mActivity.getResources().getColor(backgroundColor[position % backgroundColor.length]));
            itemHolder.contactItemNar.setVisibility(editable ? View.VISIBLE : View.INVISIBLE);
            BaseTextDrawable drawable;

            if (DinSDK.getUserInstance().getUser().getUid().equals(mData.get(position).getUid())) {
                drawable = DDImageUtil.getBaseTextDrawable(mActivity, "me"
                        , true);
            } else {
                drawable = DDImageUtil.getBaseTextDrawable(mActivity, mData.get(position).getUid()
                        , false);
            }
            int w = itemHolder.contactItemAvatar.getLayoutParams().height;
            itemHolder.contactItemAvatar.setBaseTextDrawable(drawable, w, w);
            if (!TextUtils.isEmpty(mData.get(position).getPhoto()))
                ImageLoader.getInstance().displayImage(APIKey.UPLOAD_SERVER_IP + mData.get(position).getPhoto()
                        , itemHolder.contactItemAvatar);

            if (TextUtils.isEmpty(mData.get(position).getPhone())
                    && !mData.get(position).isBindPhone()) {
                itemHolder.contactItemStatus.setAlpha(0.2f);
            } else {
                itemHolder.contactItemStatus.setAlpha(1f);
            }

            itemHolder.contactItemPermisssion.setImageDrawable(getPermissionDrawabel(mData.get(position).getPermission()));
            itemHolder.contactItemPermisssion.setVisibility(View.VISIBLE);

        } else if (type == TYPE_OTHER) {
            ViewHolder itemHolder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.contact_list_item, null);
                itemHolder = new ViewHolder(convertView);
                convertView.setTag(itemHolder);
            } else {
                try {
                    itemHolder = (ViewHolder) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            itemHolder.background.setAlpha(VIEW_ENABLE_ALPHA);
            itemHolder.contactItemAvatar.setVisibility(View.GONE);
            itemHolder.contactItemStatus.setVisibility(View.GONE);
            itemHolder.contactItemPermisssion.setVisibility(View.GONE);
            itemHolder.contactItemLayout.setVisibility(View.GONE);
            itemHolder.contactItemNar.setVisibility(LocalKey.ADMIN == mRole ? View.VISIBLE : View.INVISIBLE);

            if (TextUtils.isEmpty(mData.get(position).getName())) {
                itemHolder.contactItemName.setText(mData.get(position).getPhone());
            } else {
                itemHolder.contactItemName.setText(mData.get(position).getName());
            }

//            itemHolder.contactItemDescription.setText(getDescription(mData.get(position)));
            itemHolder.contactItemDescription.setText(mData.get(position).getPhone());
        }
        return convertView;
    }

    private String getDescription(ContactItem data) {
        String description = "";

        if (data.isPush()) {
            description = description + Local.s("Push");
        }

        if (data.isSms() && (!TextUtils.isEmpty(data.getPhone()) || data.isBindPhone())) {
            if (!TextUtils.isEmpty(description)) {
                description = description + ",";
            }
            description = description + Local.s("SMS");

        }

        if (data.isCall()) {
            if (!TextUtils.isEmpty(description)) {
                description = description + ",";
            }
            description = description + Local.s("call");
        }
        return description;

    }

    public void remove(int index) {
        if (mData != null && index >= 0 && index < mData.size()) {
            mData.remove(index);
            notifyDataSetChanged();
        }
    }

    private Drawable getPermissionDrawabel(int i) {
        if (i == LocalKey.ADMIN) {
            return mActivity.getResources().getDrawable(R.drawable.icon_member_profile_jurisdiction_admin);
        } else if (i == LocalKey.USER) {
            return mActivity.getResources().getDrawable(R.drawable.icon_member_profile_jurisdiction_user);
        } else {
            return mActivity.getResources().getDrawable(R.drawable.icon_member_profile_jurisdiction_guest);
        }
    }

    static class HeaderViewHolder {
        TextView contactsItemHeader;
        ImageView ivAdd;
        private String tittleSrc;

        HeaderViewHolder(View view, OnHeaderAddClickListener listener) {
            __bindViews(view);
            ivAdd.setOnClickListener((v) -> {
                if (null != listener) {
                    listener.onHeaderAddClick(ivAdd, tittleSrc);
                }
            });
        }

        private void __bindViews(View view) {
            contactsItemHeader = view.findViewById(R.id.contacts_item_header);
            ivAdd = view.findViewById(R.id.iv_add);
        }

        public void setTittleSrc(String tittleSrc) {
            this.tittleSrc = tittleSrc;
            contactsItemHeader.setText(Local.s(tittleSrc));
        }

        public String getTittleSrc() {
            return tittleSrc;
        }
    }

    static class HeaderViewHolderNew {
        TextView contactsItemHeader;
        private String tittleSrc;
        private LinearLayout llHeader;

        HeaderViewHolderNew(View view, OnHeaderAddClickListener listener) {
            __bindViews(view);
            llHeader.setOnClickListener((v) -> {
                if (null != listener) {
                    listener.onHeaderAddClick(llHeader, tittleSrc);
                }
            });
        }

        private void __bindViews(View view) {
            contactsItemHeader = view.findViewById(R.id.tv_name);
            llHeader = view.findViewById(R.id.ll_header);
        }

        public void setTittleSrc(String tittleSrc) {
            this.tittleSrc = tittleSrc;
            contactsItemHeader.setText(Local.s(tittleSrc));
        }

        public String getTittleSrc() {
            return tittleSrc;
        }
    }

    static class ViewHolder {
        CircularView contactItemAvatar;
        TextView contactItemName;
        TextView contactItemDescription;
        ImageView contactItemStatus;
        RelativeLayout background;
        View line;
        ImageView contactItemPermisssion;
        RelativeLayout contactItemLayout;
        ImageView contactItemNar;


        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            contactItemAvatar = view.findViewById(R.id.contact_item_avatar);
            contactItemName = view.findViewById(R.id.contact_item_name);
            contactItemDescription = view.findViewById(R.id.contact_item_description);
            contactItemStatus = view.findViewById(R.id.contact_item_status);
            background = view.findViewById(R.id.contact_list_background);
            line = view.findViewById(R.id.contact_item_line);
            contactItemPermisssion = view.findViewById(R.id.contact_item_permission);
            contactItemLayout = view.findViewById(R.id.contact_item_layout);
            contactItemNar = view.findViewById(R.id.contact_item_nor);
        }
    }

    public void setOnHeaderAddClickListener(OnHeaderAddClickListener onHeaderAddClickListener) {
        this.mOnHeaderAddClickListener = onHeaderAddClickListener;
    }

    public interface OnHeaderAddClickListener {
        void onHeaderAddClick(View childView, String header);
    }
}
