package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.GravityInt;
import androidx.core.content.ContextCompat;
import androidx.transition.Slide;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module_dscam.bean.DsCamConst;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.AlertDialogManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;


/**
 * Created by rinfon on 15/6/26.
 */
public class AlertDialog extends Dialog {
    private String TAG = getClass().getSimpleName();

    private Builder builder;
    private int layoutRes;

    private Context mContext;

    private RelativeLayout mAlertDialogContentLayout;

    private LocalCustomButton mOk, mCancel;

    private LocalTextView mContent;

    private LocalTextView mTitle;

    private boolean isCanCancel = true;

    private ImageView mContentImageView;


    private @AlertDialogManager.DialogType
    int type;
    private String postfix;

    private ImageView ivLoading;
    private LocalTextView tvLoading;

    public AlertDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        EventBus.getDefault().register(this);
        mContext = context;
        this.builder = builder;
        Configuration configuration = mContext.getResources().getConfiguration();
        if (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            this.layoutRes = R.layout.alert_dialog_land;
        } else {
            this.layoutRes = R.layout.alert_dialog;
        }
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mAlertDialogContentLayout = view.findViewById(R.id.alert_dialog_content_layout);
        mContent = (LocalTextView) view.findViewById(R.id.alert_dialog_content);
        mContent.setMovementMethod(ScrollingMovementMethod.getInstance());
        mTitle = view.findViewById(R.id.alert_dialog_title);
        mOk = (LocalCustomButton) view.findViewById(R.id.alert_dialog_ok);
        mCancel = (LocalCustomButton) view.findViewById(R.id.alert_dialog_cancel);
        mContentImageView = (ImageView) view.findViewById(R.id.alert_dialog_content_imageview);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onOkClick();
                }
            }
        });

        if (builder.getContentLayoutMarginTop() > -1
                || builder.getContentLayoutMarginBottom() > -1) {
            RelativeLayout.LayoutParams contentParams = (RelativeLayout.LayoutParams) mAlertDialogContentLayout.getLayoutParams();
            if (builder.getContentLayoutMarginTop() > -1) {
                contentParams.topMargin = builder.getContentLayoutMarginTop();
            }
            if (builder.getContentLayoutMarginBottom() > -1) {
                contentParams.bottomMargin = builder.getContentLayoutMarginBottom();
            }
            mAlertDialogContentLayout.setLayoutParams(contentParams);
        }

        if (builder.getContentTextPaddingBottom() > -1) {
            mContent.setPadding(mContent.getPaddingLeft(), mContent.getPaddingTop(),
                    mContent.getPaddingRight(), builder.getContentTextPaddingBottom());
        }

        if (builder.getContentLayoutMinHeight() > -1) {
            mAlertDialogContentLayout.setMinimumHeight(builder.getContentLayoutMinHeight());
        }

        if (builder.getContentTextMinHeight() > -1) {
            mContent.setMinHeight(builder.getContentTextMinHeight());
        }

        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOk.setVisibility(View.VISIBLE);
        } else {
            mOk.setVisibility(View.GONE);
        }

        if (builder.isShowContentImageView) {
            mContentImageView.setVisibility(View.VISIBLE);
            if (builder.getContentIcon() != null) {
                mContentImageView.setImageDrawable(builder.getContentIcon());
            } else {
                if (builder.isSuccess) {
                    mContentImageView.setImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.color_brand_primary)));
                    mContentImageView.setImageResource(R.drawable.icon_dialogue_succeed);
                } else {
                    mContentImageView.setImageResource(R.drawable.icon_dialogue_failed);
                }
            }
        } else {
            mContentImageView.setVisibility(View.GONE);
        }

        if (builder.getOkColor() != 0) {
            mOk.setTextColor(builder.getOkColor());
        }

        if (builder.getCancelColor() != 0) {
            mCancel.setTextColor(builder.getCancelColor());
        }
        if (builder.isShowCancel) {
            mCancel.setLocalText(builder.mCancel);
            mCancel.setVisibility(View.VISIBLE);
            if (builder.cancelClick != null) {
                mCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (builder.isAutoDismiss) {
                            dismiss();
                        }
                        builder.cancelClick.onClick();
                    }
                });
            }
        } else {
            mCancel.setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(builder.mTitle)) {
            mTitle.setLocalText(builder.mTitle);
            mTitle.setVisibility(View.VISIBLE);
        } else {
            mTitle.setVisibility(View.GONE);
        }

        mContent.setLocalText(builder.mContent);
        if (builder.contentGravityCenter) {
            mContent.setGravity(Gravity.CENTER);
        }
        isCanCancel = builder.isCanCancel;

        if (builder.getBackgroundTint() != 0) {
            ((RelativeLayout) view.findViewById(R.id.rl_bg)).setBackgroundTintList(ColorStateList.valueOf(builder.getBackgroundTint()));
        }

        if (builder.getContentColor() != 0) {
            mContent.setTextColor(builder.getContentColor());
        }

        if (builder.getContentGravity() != 0) {
            mContent.setGravity(builder.getContentGravity());
        }

        if (builder.getOkBtnSolidColor() != 0) {
            mOk.setPressedColor(builder.getOkBtnSolidColor());
            mOk.setNormalColor(builder.getOkBtnSolidColor());
        }

        if (builder.getOkBtnStrokeColor() != 0) {
            mOk.setStrokeColor(builder.getOkBtnStrokeColor());
        }

        if (builder.getCancelBtnStrokeColor() != 0) {
            mCancel.setStrokeColor(builder.getCancelBtnStrokeColor());
        }

        type = builder.getType();
        postfix = builder.getPostfix();

        ivLoading = view.findViewById(R.id.iv_loading);
        tvLoading = view.findViewById(R.id.tv_loading);
        if (builder.showLoading) {
            setToLoading();
        } else {
            ivLoading.clearAnimation();
            ivLoading.setVisibility(View.GONE);
            tvLoading.setVisibility(View.GONE);
        }

    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void show() {
        AlertDialogManager.get().tryRecordDialog(this);
        super.show();
    }

    @Override
    public void dismiss() {
        AlertDialogManager.get().removeDialog(this);
        EventBus.getDefault().unregister(this);
        super.dismiss();
        ivLoading.clearAnimation();
        mContext = null;
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        if (isCanCancel)
            super.cancel();
    }

    public void setOKText(String ok) {
        mOk.setLocalText(ok);
    }

    public void setCancelText(String cancel) {
        mCancel.setLocalText(cancel);
    }

    public void setOKClick(View.OnClickListener onclick) {
        mOk.setOnClickListener(onclick);
    }

    public void setCancel(View.OnClickListener onclick) {
        mCancel.setOnClickListener(onclick);
    }

    public void setContent(String content) {

        mContent.setLocalText(content);
    }

    public void setType(@AlertDialogManager.DialogType int type) {
        this.type = type;
    }

    public void setPostfix(String postfix) {
        this.postfix = postfix;
    }

    public int getType() {
        return type;
    }

    public String getPostfix() {
        return postfix;
    }

    /**
     * 获取用于缓存Dialog的key
     * <p>
     * DEFAULT 类型的Dialog不进行缓存
     *
     * @return NULL 表示不需要缓存
     */
    public String getDialogManagerCacheKey() {
        if (AlertDialogManager.DialogType.DEFAULT == type) {
            return null;
        }

        return type + "-" + postfix;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCListUpdateEvent ev) {
        if (!builder.showLoading || TextUtils.isEmpty(builder.provider)) {
            return;
        }

        Device device;
        if (builder.provider.toLowerCase().startsWith(DsCamConst.PROVIDER_DSCAM) && IPCListUpdateEvent.DEVICE_TYPE_DSCAM == ev.getDeviceType()) {
            device = IPCManager.getInstance().getDsCamDeviceByID(builder.deviceId);
        } else if (HeartLaiConstants.PROVIDER_HEARTLAI.equals(builder.provider) && IPCListUpdateEvent.DEVICE_TYPE_HEARTLAI == ev.getDeviceType()) {
            device = IPCManager.getInstance().getHeartLaiDeviceByPID(builder.deviceId);
        } else {
            return;
        }
        if (device != null) {
            setToConnectSuccess();
        } else {
            setToConnectFail();
        }

    }

    private void setToLoading() {
        ivLoading.setVisibility(View.VISIBLE);
        ivLoading.clearAnimation();
        ivLoading.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.rotating));
        tvLoading.setVisibility(View.VISIBLE);
        tvLoading.setLocalText(R.string.ipc_connecting);
        setButtonEnable(false);
    }

    private void setToConnectFail() {
        ivLoading.setVisibility(View.GONE);
        ivLoading.clearAnimation();
        tvLoading.setVisibility(View.VISIBLE);
        tvLoading.setLocalText(R.string.ds_config_failed_device);
        setButtonEnable(false);
    }

    private void setToConnectSuccess() {
        ivLoading.setVisibility(View.GONE);
        ivLoading.clearAnimation();
        tvLoading.setVisibility(View.GONE);
        setButtonEnable(true);
    }

    private void setButtonEnable(boolean enable) {
        if (enable) {
            mOk.setEnabled(true);
            mOk.setAlpha(1.0f);
        } else {
            mOk.setEnabled(false);
            mOk.setAlpha(0.5f);
        }
    }

    public interface AlertOkClickCallback {

        void onOkClick();
    }

    public interface AlertCancelClickCallback {

        void onClick();
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mTitle;

        private String mOK;

        private boolean isShowOK = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isCanCancel = true;

        private boolean isAutoDismiss = true;

        private int OkColor = 0;

        private int CancelColor = 0;

        private int contentColor = 0;

        private boolean isShowContentImageView;

        private boolean isSuccess = true;

        private int backgroundTint = 0;

        private int okBtnSolidColor = 0;

        private int okBtnStrokeColor = 0;

        private Drawable contentIcon = null;

        private int cancelBtnStrokeColor = 0;

        private AlertOkClickCallback okClick;

        private AlertCancelClickCallback cancelClick;

        private @AlertDialogManager.DialogType
        int type = AlertDialogManager.DialogType.DEFAULT;
        private String postfix = "";

        /**
         * 是否显示loading,ipc的移动侦测弹窗用到
         */
        private boolean showLoading = false;

        private String deviceId;
        private String provider;

        private boolean contentGravityCenter;
        private int contentGravity;
        private int contentLayoutMarginBottom = -1;
        private int contentLayoutMarginTop = -1;
        private int contentTextPaddingBottom = -1;
        private int contentLayoutMinHeight = -1;
        private int contentTextMinHeight = -1;

        public Builder(Context context) {
            mContext = context;
        }

        public Builder setContentGravityCenter(boolean contentGravityCenter) {
            this.contentGravityCenter = contentGravityCenter;
            return this;
        }

        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setTitle(String title) {
            mTitle = title;
            return this;
        }

        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setCancelListener(AlertCancelClickCallback cancelClick) {
            this.cancelClick = cancelClick;
            return this;
        }

        public Builder setCancel(String cancel) {
            if (TextUtils.isEmpty(cancel)) {
                isShowCancel = false;
            } else {
                mCancel = cancel;
                isShowCancel = true;
            }
            return this;
        }

        public Builder setAutoDissmiss(boolean isAutoDismiss) {
            this.isAutoDismiss = isAutoDismiss;
            return this;
        }

        public Builder setCanCancel(boolean isCanCancel) {
            this.isCanCancel = isCanCancel;
            return this;
        }

        public Builder setIsShowContentImageView(boolean isShow) {
            this.isShowContentImageView = isShow;
            return this;
        }

        public Builder setIsSuccess(boolean success) {
            this.isSuccess = success;
            return this;
        }

        public int getOkColor() {
            return OkColor;
        }

        public Builder setOkColor(int okColor) {
            OkColor = okColor;
            return this;
        }

        public int getCancelColor() {
            return CancelColor;
        }

        public Builder setCancelColor(int CancelColor) {
            this.CancelColor = CancelColor;
            return this;
        }

        public Builder setBackgroundTint(int backgroundTint) {
            this.backgroundTint = backgroundTint;
            return this;
        }

        public int getBackgroundTint() {
            return backgroundTint;
        }

        public Builder setContentColor(int contentColor) {
            this.contentColor = contentColor;
            return this;
        }

        public int getContentColor() {
            return contentColor;
        }

        public int getContentGravity() {
            return contentGravity;
        }

        public Builder setOkBtnSolidColor(int okBtnSolidColor) {
            this.okBtnSolidColor = okBtnSolidColor;
            return this;
        }

        public Builder setOkBtnStrokeColor(int okBtnStrokeColor) {
            this.okBtnStrokeColor = okBtnStrokeColor;
            return this;
        }

        public Builder setCancelBtnStrokeColor(int cancelBtnStrokeColor) {
            this.cancelBtnStrokeColor = cancelBtnStrokeColor;
            return this;
        }

        public Builder setContentIcon(Drawable contentIcon) {
            this.contentIcon = contentIcon;
            return this;
        }

        public Builder setType(@AlertDialogManager.DialogType int type) {
            this.type = type;
            return this;
        }

        public Builder setPostfix(String postfix) {
            this.postfix = postfix;
            return this;
        }

        public int getType() {
            return type;
        }

        public String getPostfix() {
            return postfix;
        }

        public int getOkBtnSolidColor() {
            return okBtnSolidColor;
        }

        public int getOkBtnStrokeColor() {
            return okBtnStrokeColor;
        }

        public int getCancelBtnStrokeColor() {
            return cancelBtnStrokeColor;
        }

        public Drawable getContentIcon() {
            return contentIcon;
        }

        public Builder setShowLoading(boolean showLoading) {
            this.showLoading = showLoading;
            return this;
        }

        public Builder setDeviceId(String deviceId) {
            this.deviceId = deviceId;
            return this;
        }

        public Builder setProvider(String provider) {
            this.provider = provider;
            return this;
        }

        public Builder setContentGravity(int gravity) {
            this.contentGravity = gravity;
            return this;
        }

        public Builder setContentLayoutMarginTop(int contentLayoutMarginTop) {
            this.contentLayoutMarginTop = contentLayoutMarginTop;
            return this;
        }

        public int getContentLayoutMarginTop() {
            return contentLayoutMarginTop;
        }

        public Builder setContentLayoutMarginBottom(int contentLayoutMarginBottom) {
            this.contentLayoutMarginBottom = contentLayoutMarginBottom;
            return this;
        }

        public int getContentLayoutMarginBottom() {
            return contentLayoutMarginBottom;
        }

        public Builder setContentPaddingBottom(int paddingBottom) {
            this.contentTextPaddingBottom = paddingBottom;
            return this;
        }

        public int getContentTextPaddingBottom() {
            return contentTextPaddingBottom;
        }

        public Builder setContentLayoutMinHeight(int minHeight) {
            this.contentLayoutMinHeight = minHeight;
            return this;
        }

        public int getContentLayoutMinHeight() {
            return contentLayoutMinHeight;
        }

        public Builder setContentTextMinHeight(int minHeight) {
            this.contentTextMinHeight = minHeight;
            return this;
        }

        public int getContentTextMinHeight() {
            return contentTextMinHeight;
        }

        public AlertDialog preBuilder() {
            AlertDialog alertDialog = new AlertDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}