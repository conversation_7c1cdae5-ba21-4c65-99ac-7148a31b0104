package com.dinsafer.module.settting.adapter.ipc;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/26 12:00 PM
 */
public class IpcHeaderViewHolder extends RecyclerView.ViewHolder {
    private LocalTextView mHeader;
    private ImageView mRightIcon;

    public IpcHeaderViewHolder(@NonNull View itemView, boolean isShowHeader) {
        super(itemView);
        mHeader = itemView.findViewById(R.id.homarm_list_header_name);
        mRightIcon = itemView.findViewById(R.id.iv_right_icon);
        mHeader.setVisibility(isShowHeader ? View.VISIBLE : View.GONE);
    }

    public void setHeader(String header) {
        mHeader.setLocalText(header);
    }

    public void setRightIconVisible(boolean visible) {
        mRightIcon.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setRightIconRes(@DrawableRes int iconResId) {
        mRightIcon.setVisibility(View.VISIBLE);
        mRightIcon.setImageResource(iconResId);
    }

    public void setRightIconOnClickListener(View.OnClickListener l) {
        mRightIcon.setOnClickListener(l);
    }
}
