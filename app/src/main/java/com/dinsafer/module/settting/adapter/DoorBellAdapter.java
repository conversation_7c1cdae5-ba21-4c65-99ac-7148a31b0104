package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DoorBellItemBinding;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DoorBell;
import com.dinsafer.util.DBUtil;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.FailReason;
import com.nostra13.universalimageloader.core.listener.ImageLoadingListener;

import java.text.SimpleDateFormat;
import java.util.ArrayList;

import androidx.databinding.DataBindingUtil;

/**
 * Created by rinfon on 2018/3/28.
 */

public class DoorBellAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<DoorBell.ResultBean> mData;

    private SimpleDateFormat myFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    public DoorBellAdapter(Activity mActivity, ArrayList<DoorBell.ResultBean> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    public ArrayList<DoorBell.ResultBean> getData() {
        return mData;
    }

    public void setData(ArrayList<DoorBell.ResultBean> mData) {
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {

        ViewHolder itemHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.door_bell_item, null);
            itemHolder = new ViewHolder(convertView);
            convertView.setTag(itemHolder);
        } else {
            try {
                itemHolder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!TextUtils.isEmpty(mData.get(position).getImg())) {
            if (DBUtil.Exists(mData.get(position).getImg())) {
                ImageLoader.getInstance().displayImage(DBUtil.Str(mData.get(position).getImg()), itemHolder.binding.doorBellItemIcon, new ImageLoadingListener() {
                    @Override
                    public void onLoadingStarted(String imageUri, View view) {

                    }

                    @Override
                    public void onLoadingFailed(String imageUri, View view, FailReason failReason) {
                        String url = DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.DOOR_BELL_SERVER_IP + mData.get(position).getImg());
                        DBUtil.Put(mData.get(position).getImg(), url);
                    }

                    @Override
                    public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage) {

                    }

                    @Override
                    public void onLoadingCancelled(String imageUri, View view) {

                    }
                });
            } else {
                String url = DDSecretUtil.privateDownloadUrlWithDeadline(
                        APIKey.DOOR_BELL_SERVER_IP + mData.get(position).getImg());
                DBUtil.Put(mData.get(position).getImg(), url);
                ImageLoader.getInstance().displayImage(url, itemHolder.binding.doorBellItemIcon);
            }
        }

        itemHolder.binding.doorBellItemName.setText(mData.get(position).getDoorbellname());
        itemHolder.binding.doorBellItemTime.setText(myFmt.format(mData.get(position).getRecordtime() / 1000000));

        return convertView;
    }

    static class ViewHolder {
        DoorBellItemBinding binding;

        ViewHolder(View view) {
            binding = DataBindingUtil.bind(view);
        }
    }
}
