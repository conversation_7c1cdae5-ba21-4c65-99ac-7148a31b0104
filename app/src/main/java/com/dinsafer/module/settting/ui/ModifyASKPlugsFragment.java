package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.TextView;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.common.PluginManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.IPCKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ModifyPlugsLayoutBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.model.event.NeedLoadInfoPluginsEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.family.view.CreateFamilyFragment;
import com.dinsafer.module.ipc.heartlai.event.NeedGetAllHeartLaiEvent;
import com.dinsafer.module.settting.ui.dialog.ReadyToArmTipDialog;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.activator.PluginActivatorManager;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;
import com.dinsafer.util.RandomStringUtils;
import com.dinsafer.util.RegxUtil;
import com.dinsafer.util.viewanimator.AnimationBuilder;
import com.dinsafer.util.viewanimator.AnimationListener;
import com.dinsafer.util.viewanimator.ViewAnimator;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by Rinfon on 16/7/12.
 */
public class ModifyASKPlugsFragment extends MyBaseFragment<ModifyPlugsLayoutBinding>
        implements IDeviceCallBack, IPluginBindCallBack, ActionSheet.ActionSheetListener {
    private static final String KEY_PLUGIN_INFO = "PLUGIN_INFO";

    private ICallBack callBack;

    private Builder builder;
    private String messageId;

    private Device mPluginDevice, mPanelDevice;
    private boolean isSelfOperate;
    private Plugin mAddPlugin;

    public static ModifyASKPlugsFragment newInstance(Builder builder) {
        ModifyASKPlugsFragment modifyPlugsFragment = new ModifyASKPlugsFragment();
        Bundle args = new Bundle();
        args.putParcelable("data", builder);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    public static ModifyASKPlugsFragment newInstance(Builder builder, Plugin plugin) {
        ModifyASKPlugsFragment modifyPlugsFragment = new ModifyASKPlugsFragment();
        Bundle args = new Bundle();
        args.putParcelable("data", builder);
        args.putSerializable(KEY_PLUGIN_INFO, plugin);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.modify_plugs_layout;
    }

    private void findDevice() {
        final String pluginId = builder.getId();
        if (!TextUtils.isEmpty(pluginId)) {
            mPluginDevice = DinHome.getInstance().getDevice(pluginId);
            if (null != mPluginDevice) {
                mPluginDevice.registerDeviceCallBack(this);
            }
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mAddPlugin = (Plugin) getArguments().getSerializable(KEY_PLUGIN_INFO);
        mBinding.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.commonBarRightIcon.setOnClickListener(v -> showMoreActionDialog());
        //初始化先设置为不可用
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.btnSave.setEnabled(false);
        mBinding.btnSave.setOnClickListener(v -> toSave());

        mBinding.modifyPlugsInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mBinding.btnSave.setEnabled(s.length() > 0);
            }
        });

        mBinding.modifyPlugsMoreSetting.setOnClickListener(v -> toMoreSetting());

        mBinding.sirenSetting.setOnClickListener(v -> toSirenSetting());

//        mBinding.sirenDelete.setOnClickListener(v -> toDeleteSiren());

//        mBinding.modifyNameDelete.setOnClickListener(v -> toDeleteItem());

        mBinding.sirenTest.setOnClickListener(v -> toTestSiren());

        mBinding.sirenHelp.setOnClickListener(v -> toShowSirenTestHelp());

        mBinding.modifyPlugsNetwork.setOnClickListener(v -> toNetWork());

        mBinding.llFamily.setOnClickListener(v -> showSelectHomeDialog());

    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.modify_plugs_title));
        mBinding.modifyPlugsInput.setHint(Local.s(getResources().getString(R.string.modifyaccessoryhint)));
//        mBinding.modifyNameDelete.setLocalText(getResources().getString(R.string.change_permission_delete));
        builder = (Builder) getArguments().getParcelable("data");
        if (builder.isAdd()) {
            DinSDK.getPluginActivtor().addBindCallBack(this);
        }
        mBinding.sirenHelp.setLocalText(getResources().getString(R.string.siren_help));
        mBinding.sirenTest.setLocalText(getResources().getString(R.string.siren_test));
        if (builder == null) {
            removeSelf();
        }
        String id = builder.getId();
        boolean isOffical = builder.isOffical();

        if (builder.isShowDelete()) {
            mBinding.commonBarRightIcon.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBarRightIcon.setVisibility(View.GONE);
        }

        if (builder.isAdd()) {
            mBinding.btnSave.setVisibility(View.VISIBLE);
        } else {
            mBinding.btnSave.setVisibility(View.GONE);
            mBinding.modifyPlugsInput.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icon_nav_edit, 0);
            mBinding.modifyPlugsInput.setFocusable(false);
            mBinding.modifyPlugsInput.setOnClickListener(v -> showChangeNameDialog());
        }

        if (builder.isShowwave()) {
//            IPCDevice model = HeartLaiServiceManager.getInstance().getDeviceById(builder.getId());
//            if (model != null) {
//                try {
//                    JSONObject jsonObject = new JSONObject(model.getSourceData());
//                    boolean isAp = DDJSONUtil.getBoolean(jsonObject, "ap");
//                    if (isAp) {
//                        mBinding.modifyPlugsNetwork.setLocalText(getResources().getString(R.string.modify_plugs_network_2));
//                    } else {
//                        mBinding.modifyPlugsNetwork.setLocalText(getResources().getString(R.string.modify_plugs_network));
//                    }
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
//
//            }
//            mBinding.modifyPlugsHint.setVisibility(View.GONE);
        } else {
            mBinding.modifyPlugsNetwork.setVisibility(View.GONE);
//            TODO IPC才显示
//            modifyPlugsHint.setLocalText(getResources().getString(R.string.modify_plugs_hint));
//            modifyPlugsHint.setVisibility(View.VISIBLE);
        }

        if (builder.isShowSiren()) {
            mBinding.sirenSetting.setLocalText(getResources().getString(R.string.siren_setting));
            mBinding.sirenSetting.setVisibility(View.VISIBLE);
        } else {
            mBinding.sirenSetting.setVisibility(View.GONE);
        }

        if (builder.isShowSirenTest()) {
            mBinding.sirenTestLayout.setVisibility(View.VISIBLE);
            mBinding.commonBarRightIcon.setVisibility(builder.isShowDelete() ? View.VISIBLE : View.GONE);
//            mBinding.sirenDelete.setLocalText(getString(R.string.change_permission_delete));
        } else {
            mBinding.sirenTestLayout.setVisibility(View.GONE);
        }

        mBinding.modifyPlugsMoreSetting.setLocalText(getResources().getString(R.string.modify_plugs_more_setting));


        if (builder.isShowMoreSetting()) {
            mBinding.modifyPlugsMoreSetting.setVisibility(View.VISIBLE);
        } else {
            mBinding.modifyPlugsMoreSetting.setVisibility(View.GONE);
        }


        try {
            JSONObject jsonData = new JSONObject(builder.getData());
            DDLog.d(TAG, jsonData.toString());
            String sType = "";
            if (IPCKey.NEW_QR_TYPE_IPC.equals(id.charAt(1))) {
                sType = CommonDataUtil.getInstance().getASKNameByBSType(CommonDataUtil.IPC_KEY);
            } else {
                sType = CommonDataUtil.getInstance().getASKNameByBSType(DDJSONUtil.getString(jsonData, "stype"));
            }
            mBinding.modifyPlugsType.setLocalText(sType);
            if (!TextUtils.isEmpty(builder.getName())) {
                mBinding.modifyPlugsInput.setText(builder.getName());
            } else {
                if (!builder.isAdd())
                    mBinding.modifyPlugsInput.setText(Local.s(sType) + "_" + builder.getId());
            }

            if ((!TextUtils.isEmpty(sType) && (sType.equals("1C") || sType.equals("11")))) {
                mBinding.modifyPlugsInput.setText(getMainActivity().getResources().getString(R.string.door_sensor_modify_view));
            }

        } catch (JSONException e) {

        }


        if (builder.isOffline() || builder.isLowPower()) {
            mBinding.pluginStatusHint.setVisibility(View.VISIBLE);
            if (builder.isOffline() && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE) {
                mBinding.pluginStatusHint.setLocalText(getResources().getString(R.string.plugin_offline_hint));
            } else {
                mBinding.pluginStatusHint.setLocalText(getResources().getString(R.string.plugin_lowpower_hint));
            }
        } else {
            mBinding.pluginStatusHint.setVisibility(View.GONE);
        }

        mBinding.modifyPlugsId.setText("ID:" + id);

        mBinding.llFamily.setVisibility(View.GONE);
        mBinding.tvFamily.setLocalText(getString(R.string.family));
        mBinding.tvFamilySelected.setText(HomeManager.getInstance().getCurrentHome().getHomeName());

        findDevice();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(NeedLoadInfoPluginsEvent event) {
        // APP前后台切换, 导致重新获取PanelConstant.DeviceType.HOME_PLUGIN类型的device；从后台回到前台，此
        // 页面的IDeviceCallBack监听仍然是在旧的device上，因此在该页面上的操作无法得知操作结果；
        if (null != mPluginDevice) {
            Device device = PluginManager.getInstance().getPluginByID(mPluginDevice.getId());
            DDLog.e(TAG, "NeedLoadInfoPluginsEvent. is ask plugin ？ " + (device != null));
            if (null != device) {
                mPluginDevice.unregisterDeviceCallBack(this);
                mPluginDevice = device;
                mPluginDevice.registerDeviceCallBack(this);
                mPluginDevice.submit(PanelParamsHelper.getPluginDetail());
            }
        }
    }


    public void toMoreSetting() {

//        IPCDevice model = HeartLaiServiceManager.getInstance().getDeviceById(builder.getId());
//        if (model == null)
//            return;
//        if (model.getConnectStatus() == HeartLaiConstants.CAMERA_STATUS_WRONG_PASSWORD) {
//            EventBus.getDefault().post(new IPCWrongPassword(model));
//            return;
//        }
////        if (!model.isConnect()) {
////            showToast(getDelegateActivity().getResources().getString(R.string.ipc_offline_toast));
////            return;
////        }
//        if (model.getProvider().equals(Constants.PROVIDER_HEARTLAI)) {
////            getDelegateActivity().addCommonFragment(HeartLaiIPCSettingFragment.newBuilder()
////                    .devID(model.getCameraPID())
////                    .isConnect(model.isConnected())
////                    .pwd(model.getPwd())
////                    .id(builder.getId())
////                    .build());
//            getDelegateActivity().addCommonFragment(IPCSettingFragment.newInstance(builder.getId()));
//        }

    }


    public void toClose() {
        removeSelf();
    }

    public void toSave() {
        final String inputName = mBinding.modifyPlugsInput.getText().toString().trim();
        if (TextUtils.isEmpty(inputName) || !RegxUtil.isLegalName(inputName)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }
        if (IPCKey.NEW_QR_TYPE_IPC.equals(String.valueOf(builder.getId().charAt(1)))) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            mAddPlugin.setPluginName(inputName);
            PluginActivatorManager.getInstance().bindDevice(mAddPlugin);
        } else {
            if (builder.isAdd()) {
                if (null == mAddPlugin) {
                    showErrorToast();
                    DDLog.e(TAG, "Error!!! Empty plugin info.");
                    return;
                }

                showTimeOutLoadinFramgmentWithErrorAlert();
                mAddPlugin.setPluginName(inputName);
                DinSDK.getPluginActivtor().bindDevice(mAddPlugin);
            } else {
//            修改名字
                showTimeOutLoadinFramgmentWithErrorAlert();
                DDLog.i(TAG, "toChangePluginName");
                if (null != mPluginDevice) {
                    DDLog.i(TAG, "修改名字");
                    isSelfOperate = true;
                    mPluginDevice.submit(PanelParamsHelper.setPluginName(inputName));
                } else {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showErrorToast();
                }
            }
        }

    }

    private void checkReadyToArmStatus() {
        DDLog.i(TAG, "checkReadyToArmStatus");
        if (null == mPanelDevice) {
            mPanelDevice = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID());
            if (null != mPanelDevice) {
                mPanelDevice.registerDeviceCallBack(this);
            }
        }

        if (null == mPanelDevice) {
            DDLog.e(TAG, "Error on null panel device.");
            closeTimeOutLoadinFramgmentWithErrorAlert();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.getReadyToArmStatus());
    }

    private void showReadyToArmNotEnableDialog() {
        ReadyToArmTipDialog dialog = new ReadyToArmTipDialog(getDelegateActivity());
        dialog.show();
    }

    private void toGetSirentData(JSONObject data) {
        int disArmTone = DDJSONUtil.getBoolean(data, "disarm_tone") ? 1 : 0;
        int homeTone = DDJSONUtil.getBoolean(data, "homearm_tone") ? 1 : 0;
        int armTone = DDJSONUtil.getBoolean(data, "arm_tone") ? 1 : 0;
        int sosTime = DDJSONUtil.getInt(data, "sos_time");
        int disArmLight = DDJSONUtil.getInt(data, "disarm_light");
        int sosLight = DDJSONUtil.getInt(data, "sos_light");
        int homeArmTime = DDJSONUtil.getInt(data, "homearm_light");
        int armTime = DDJSONUtil.getInt(data, "arm_light");
        int promptVolume = DDJSONUtil.getInt(data, "prompt_volume");
        int alarmVolume = DDJSONUtil.getInt(data, "alarm_volume");

        // 默认int类型数据是-1时重置为0，否则二进制拼接后可能出现长度大于32bit导致错误
        if (-1 == sosTime) {
            sosTime = 0;
        }
        if (-1 == disArmLight) {
            disArmLight = 0;
        }
        if (-1 == sosLight) {
            sosLight = 0;
        }
        if (-1 == homeArmTime) {
            homeArmTime = 0;
        }
        if (-1 == armTime) {
            armTime = 0;
        }
        if (-1 == promptVolume) {
            promptVolume = 0;
        }
        if (-1 == alarmVolume) {
            alarmVolume = 0;
        }
//                    第一项


        String sosTimeString = Integer.toBinaryString(sosTime);
        if (sosTimeString.length() < 5) {
            int length = 5 - sosTimeString.length();
            for (int i = 0; i < length; i++) {
                sosTimeString = "0" + sosTimeString;
            }
        }

        String firstByte = Integer.toBinaryString(disArmTone) +
                Integer.toBinaryString(homeTone) +
                Integer.toBinaryString(armTone) +
                sosTimeString;

        String firstHex = Integer.toHexString(Integer.valueOf(firstByte, 2));
        if (firstHex.length() < 2) {
//                        前面补0
            firstHex = "0" + firstHex;
        }

        String twoByte = DDSecretUtil.intToByte(disArmLight) +
                DDSecretUtil.intToByte(sosLight) +
                DDSecretUtil.intToByte(homeArmTime) +
                DDSecretUtil.intToByte(armTime);

        String twoHex = Integer.toHexString(Integer.valueOf(twoByte, 2));
        if (twoHex.length() < 2) {
//                        前面补0
            twoHex = "0" + twoHex;
        }

//                    4位0为扩展位
        String threeByte = DDSecretUtil.intToByte(promptVolume) +
                DDSecretUtil.intToByte(alarmVolume) + "0000";

        String threeHex = Integer.toHexString(Integer.valueOf(threeByte, 2));
        if (threeHex.length() < 2) {
//                        前面补0
            threeHex = "0" + threeHex;
        }

        try {
            data.put("advancesetting", firstHex + "," + twoHex + "," + threeHex);
            data.remove("disarm_tone");
            data.remove("homearm_tone");
            data.remove("arm_tone");
            data.remove("sos_time");
            data.remove("disarm_light");
            data.remove("sos_light");
            data.remove("homearm_light");
            data.remove("arm_light");
            data.remove("prompt_volume");
            data.remove("alarm_volume");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void toSirenSetting() {
        try {

            JSONObject object = new JSONObject(builder.getData());
            if ("21".equals(DDJSONUtil.getString(object, "stype"))
                    || "22".equals(DDJSONUtil.getString(object, "stype"))
                    || "34".equals(DDJSONUtil.getString(object, "stype"))
                    || "35".equals(DDJSONUtil.getString(object, "stype"))) {
                getDelegateActivity().addCommonFragment(SirenSettingFragment.newInstance(
                        DDJSONUtil.getString(object, "advancesetting"),
                        builder.getId(),
                        DDJSONUtil.getString(object, "sendid"),
                        DDJSONUtil.getString(object, "stype"),
                        true));
            } else {
                getDelegateActivity().addCommonFragment(SirenSettingFragment.newInstance(
                        DDJSONUtil.getString(object, "advancesetting"),
                        builder.getId(),
                        DDJSONUtil.getString(object, "sendid"),
                        DDJSONUtil.getString(object, "stype"),
                        DDJSONUtil.getBoolean(object, "lighteffect")));
            }


        } catch (JSONException e) {

        }
    }

    public void toDeleteSiren() {
        toDeleteItem();
    }

    public void toDeleteItem() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        messageId = RandomStringUtils.getMessageId();
                        if (!builder.isAskPlugin()) {
                            //小黑 ipc
                            DinsafeAPI.getApi().getDeleteIpcCall(builder.getId(), messageId,
                                            DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getToken()).
                                    enqueue(new Callback<StringResponseEntry>() {
                                        @Override
                                        public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                                            closeTimeOutLoadinFramgmentWithErrorAlert();
                                            StringResponseEntry stringResponseEntry = response.body();
                                            if (stringResponseEntry.getStatus() == 1) {
                                                removeSelf();
                                                if (callBack != null) {
                                                    callBack.onDeletePlug(builder.getId());
                                                }
                                                EventBus.getDefault().post(new PluginDeleteEvent(builder.getId()));
                                            } else {
                                                showErrorToast();
                                            }
                                        }

                                        @Override
                                        public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                                            closeTimeOutLoadinFramgmentWithErrorAlert();
                                            showErrorToast();
                                        }
                                    });
                        } else {
                            //继电器
                            if (null != mPluginDevice) {
                                isSelfOperate = true;
                                mPluginDevice.submit(PanelParamsHelper.deletePlugin());
                            } else {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                            }
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        DinSDK.getPluginActivtor().removeBindCallBack(this);
        EventBus.getDefault().unregister(this);
        if (mCountAnim != null) {
            mCountAnim.cancel();
        }
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
//        unbinder.unbind();
        if (callBack != null) {
            callBack = null;
        }
    }

    AnimationBuilder mCountAnim;

    public void toTestSiren() {

        mBinding.sirenTest.setClickable(false);

        float count[] = new float[10];

        for (int i = 1; i <= 10; i++) {
            count[i - 1] = i;
        }

        if (null == mPluginDevice) {
            DDLog.e(TAG, "No device");
            showErrorToast();
            return;
        }
        mPluginDevice.submit(PanelParamsHelper.testSiren(-1, -1));

        if (mCountAnim == null) {
            mCountAnim = ViewAnimator
                    .animate(mBinding.sirenTest)
                    .duration(10 * 1000)
                    .interpolator(new LinearInterpolator())
                    .custom(new AnimationListener.Update<TextView>() {
                        @Override
                        public void update(TextView view, float value) {
                            view.setText(Local.s(getResources().getString(R.string.siren_testing))
                                    + "(" + (10 - (int) value) + ")");
                        }
                    }, count)
                    .onStart(new AnimationListener.Start() {
                        @Override
                        public void onStart() {
                            mBinding.sirenTest.setAlpha(0.5f);
                            mBinding.sirenTest.setClickable(false);
                        }
                    })
                    .onStop(new AnimationListener.Stop() {
                        @Override
                        public void onStop() {
                            mBinding.sirenTest.setAlpha(1f);
                            mBinding.sirenTest.setLocalText(getResources().getString(R.string.siren_test));
                            mBinding.sirenTest.setVisibility(View.VISIBLE);
                            mBinding.sirenTest.setClickable(true);
                        }
                    });
        }
        mCountAnim.start();
    }

    public void toShowSirenTestHelp() {
        AlertDialog.createBuilder(getMainActivity())
                .setOk(getResources().getString(R.string.know_it))
                .setContent(getResources().getString(R.string.siren_help_msg))
                .preBuilder()
                .show();
    }


    public void toNetWork() {
//        IPCDevice model = HeartLaiServiceManager.getInstance().getDeviceById(builder.getId());
//        if (model == null)
//            return;
//
//        if (model.getProvider().equals(Constants.PROVIDER_HEARTLAI)) {
//            try {
//                JSONObject jsonObject = new JSONObject(model.getSourceData());
//                boolean isAp = DDJSONUtil.getBoolean(jsonObject, "ap");
//                int ipcType = DDJSONUtil.getInt(jsonObject, "ipc_type");
//                if (isAp) {
////                    TODO
//                    getDelegateActivity().addCommonFragment(ApStepHeartLaiIpcFragment.newInstance(model.getSourceData(), false, ipcType));
//                }
//            } catch (JSONException e) {
//                e.printStackTrace();
//            }
//        }


    }

    public void showChangeNameDialog() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultName(mBinding.modifyPlugsInput.getText().toString())
                .setContent(getResources().getString(R.string.rename_accessory))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
//                                            change ipc password
                        if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                            dialog.dismiss();
                            return;
                        }
                        dialog.dismiss();
                        mBinding.modifyPlugsInput.setText(string);
                        toSave();
                    }
                })
                .preBuilder()
                .show();
    }


    private void showSelectHomeDialog() {
        ArrayList<String> item = new ArrayList<>();
        if (HomeManager.getInstance().getCurrentHome() != null) {
            item.add(HomeManager.getInstance().getCurrentHome().getHomeName());
        }

        item.add(Local.s(getString(R.string.create_a_family)));

        for (int i = 0; i < item.size(); i++) {
            Log.d(TAG, "showSelectHomeDialog: " + i + " /" + item.get(i));
        }

        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(item.toArray(new String[]{}))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);
                        if (index == item.size() - 1) {
                            List<Home> homeList = HomeManager.getInstance().getHomeList();
                            ArrayList<String> homes = new ArrayList<>();
                            if (homeList != null && homeList.size() > 0) {
                                for (Home home : homeList) {
                                    homes.add(home.getHomeName());
                                }
                            }
                            getDelegateActivity().addCommonFragment(CreateFamilyFragment.newInstance(homes));
                        } else {

                        }
                    }
                }).show();

    }

    public interface ICallBack {
        void onDeletePlug(String id);

        void onChangeName(int index, String name);
    }

    public ICallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(ICallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, cmd: " + cmd + "   map: " + map);

        // 主机回调
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (null != mPanelDevice
                && deviceId.equals(mPanelDevice.getId())
                && PanelCmd.GET_READYTOARM_STATUS.equals(cmd)) {
            onGetReadyToArmInfo(status, map);
            return;
        }

        if (null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())
                || 1 != DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1)) {
            return;
        }

        // 配件回调
        if (isSelfOperate) {
            isSelfOperate = false;
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                showErrorToast();
                return;
            }

            if (PluginCmd.PLUGIN_SETNAME.equals(cmd)) {
                removeSelf();
                if (callBack != null)
                    callBack.onChangeName(builder.getMessageIndex(), mBinding.modifyPlugsInput.getText().toString());
                EventBus.getDefault().post(new PlugsNameChangeEvent(mBinding.modifyPlugsInput.getText().toString()));
            } else if (PluginCmd.PLUGIN_DELETE.equals(cmd)) {
                removeSelf();
                if (callBack != null)
                    callBack.onDeletePlug(builder.getId());
                EventBus.getDefault().post(new PluginDeleteEvent(builder.getId()));
            }
        } else {
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                return;
            }

            String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
            if (PluginCmd.PLUGIN_ONLINE_CHANGE.equals(cmd)) {
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    JSONObject dataObject = new JSONObject(builder.getData());
                    final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                    String sendid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID);

                    if (DDJSONUtil.getString(dataObject, "sendid").equals(sendid)) {
                        if (LocalKey.PLUGIN_OFFLINE.equals(operateCmd)) {
                            // 配件离线
                            mBinding.pluginStatusHint.setLocalText(getResources().getString(R.string.plugin_offline_hint));
                            if (!APIKey.IS_SHOW_PLUGIN_NO_RESPONSE) {
                                mBinding.pluginStatusHint.setVisibility(View.INVISIBLE);
                            } else {
                                mBinding.pluginStatusHint.setVisibility(View.VISIBLE);
                            }
                            if (!TextUtils.isEmpty(DDJSONUtil.getString(dataObject, "stype"))
                                    && DDJSONUtil.getString(dataObject, "stype").equals("1C")) {
                                mBinding.pluginStatusHint.setVisibility(View.INVISIBLE);
                            }
                        } else {
                            // 配件在线
                            mBinding.pluginStatusHint.setVisibility(View.INVISIBLE);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (PluginCmd.PLUGIN_POWER_CHANGE.equals(cmd)) {
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    JSONObject dataObject = new JSONObject(builder.getData());

                    final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                    String pluginid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);

                    if (DDJSONUtil.getString(dataObject, "pluginid").equals(pluginid)) {
                        if (LocalKey.EVENT_LOWERPOWER.equals(operateCmd)) {
                            // 配件低电
                            mBinding.pluginStatusHint.setLocalText(getResources().getString(R.string.plugin_lowpower_hint));
                            mBinding.pluginStatusHint.setVisibility(View.VISIBLE);
                        } else {
                            // 配件有电
                            mBinding.pluginStatusHint.setVisibility(View.INVISIBLE);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                showSuccess();
            } else if (PluginCmd.CHANGE_SIREN_SETTING.equals(cmd)) {
                DDLog.i(TAG, "CHANGE_SIREN_SETTING");
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                    if (LocalKey.SET_NEWASKSIRENDATA.equals(operateCmd)) {
                        String sendid = jsonObject.getString("sendid");
                        if (TextUtils.isEmpty(sendid)) {
                            DDLog.e(TAG, "null sendid");
                            return;
                        }
                        JSONObject jsonData = new JSONObject(builder.getData());
                        String mSendId = DDJSONUtil.getString(jsonData, "sendid");
                        if (sendid.equals(mSendId)) {
                            jsonData.put("advancesetting", DDJSONUtil.getString(jsonObject, "advancesetting"));
                        }
                        builder.setData(jsonData);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (PluginCmd.PLUGIN_DELETE.equals(cmd)) {
                removeSelf();
                if (callBack != null)
                    callBack.onDeletePlug(builder.getId());
                EventBus.getDefault().post(new PluginDeleteEvent(builder.getId()));
            }
        }
    }

    @Override
    public void onBindResult(int code, String msg) {
        DDLog.i(TAG, "onBindResult, code: " + code + ", msg: " + msg);
        if (code == 1) {
            if (PluginConstants.TYPE_1F.equals(mAddPlugin.getPluginTypeName())) {
                // 先断开全部的心赖，减少出现不断重连或之前添加的心赖很难连上的概率
                DinSDK.getHomeInstance().releaseDeviceByType(DinConst.TYPE_HEARTLAI);
                EventBus.getDefault().post(new NeedGetAllHeartLaiEvent(true));
                closeTimeOutLoadinFramgmentWithErrorAlert();
                getDelegateActivity().removeAllCommonFragment();
                return;
            }

            JSONObject jsonObject = null;
            try {
                JSONObject event = new JSONObject(msg);
                String result = DDJSONUtil.getString(event, "Result");
                jsonObject = new JSONObject(result);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            //TODO 2.1.6 针对ready to arm门磁，添加后增加提示界面：如需使用Ready to arm功能请到系统设置里确认启用。
            if (DeviceInfoHelper.getInstance().isAdmin() && !ReadyToArmTipDialog.checkState()
                    && ("1C".equals(DDJSONUtil.getString(jsonObject, "stype"))
                    || "11".equals(DDJSONUtil.getString(jsonObject, "stype"))
                    || "25".equals(DDJSONUtil.getString(jsonObject, "stype"))
                    || "38".equals(DDJSONUtil.getString(jsonObject, "stype"))
                    || "3D".equals(DDJSONUtil.getString(jsonObject, "stype")))) {
                checkReadyToArmStatus();
            } else {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                removeSelf();
            }
        } else if (ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL == code) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        } else if (ErrorCode.ACTIVTOR_ALREAD_HAS_PLUGIN == code) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            removeSelf();
            showToast(getResources().getString(R.string.tiggle_has_plug));
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    /**
     * 获取之前设置的ReadyToArm状态
     */
    private void onGetReadyToArmInfo(int status, Map map) {
        DDLog.i(TAG, "onGetSosMessageInfo, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            removeSelf();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        boolean enable = DeviceHelper.getBoolean(resultMap, PanelDataKey.ReadyToArm.ENABLE, false);
        if (!enable) {
            showReadyToArmNotEnableDialog();
        }
        removeSelf();
    }

    public void showMoreActionDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.delete)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setCancelableOnTouchOutside(true)
                .setListener(this).show();
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        if (0 == index) {
            toDeleteItem();
        }
    }

}

