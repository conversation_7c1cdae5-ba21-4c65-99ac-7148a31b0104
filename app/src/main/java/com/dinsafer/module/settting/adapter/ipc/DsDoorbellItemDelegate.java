package com.dinsafer.module.settting.adapter.ipc;

import android.view.View;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.doorbell.play.DsDoorbellPlayFocusModeActivity;
import com.dinsafer.module.doorbell.setting.DsDoorbellSettingFragment;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.main.view.MainActivity;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/6/28
 */
public class DsDoorbellItemDelegate extends DsCamIpcItemDelegate {
    private String TAG = getClass().getSimpleName();

    public DsDoorbellItemDelegate(MainActivity mMainActivity, boolean showStateIcon, String cacheName) {
        super(mMainActivity, showStateIcon, cacheName);
    }

    @Override
    public void onBindItemViewHolder(MainPanelIpcItemViewHolder itemHolder, int position, Device device) {
        super.onBindItemViewHolder(itemHolder, position, device);

        itemHolder.mIvMore.setOnClickListener(v ->
                mMainActivity.addCommonFragment(DsDoorbellSettingFragment.newInstance(device.getId())));

    }

    @Override
    public void onPlayIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {
        if (holder.isEditMode()) {
            return;
        }

        mMainActivity.setNotNeedToLogin(false);
        DsDoorbellPlayFocusModeActivity.startActivity(mMainActivity, device.getId(), false, false, false, null);
    }

    @Override
    public void onErrorIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {
        if (holder.isEditMode()) {
            return;
        }

        mMainActivity.setNotNeedToLogin(false);
        DsDoorbellPlayFocusModeActivity.startActivity(mMainActivity, device.getId(), false, false, false, null);
    }

}
