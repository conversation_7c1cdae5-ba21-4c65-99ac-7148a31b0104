package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.bluetooth.BluetoothGatt;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.BleCloseTimerEvent;
import com.dinsafer.model.WindowFocusChangedEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.add.ui.BleCheckBluetoothDialog;
import com.dinsafer.module.add.ui.BleStepCheckPasswordFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.ui.model.FourGSettingFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.add.PanelBinder;
import com.dinsafer.panel.add.callback.IPanelConnectListener;
import com.dinsafer.panel.add.callback.IPanelScanListener;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import rx.Observable;
import rx.Observer;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * Created by Rinfon on 16/7/7.
 */
public class AdvancedSettingFragment extends BaseFragment
        implements IDeviceCallBack, IPanelConnectListener {

    TextView advancedSettingVersionNumber;
    LocalTextView commonBarTitle;
    LocalTextView advancedSettingLabel;
    LocalTextView advancedSettingChangeNetwork;
    LocalTextView advancedSettingChangePassword;
    LocalTextView advancedSettingReset;
    LocalTextView advancedSettingLanguage;
    LocalTextView advancedSettingVersion;
    LocalTextView deviceManagementTimezoneSetting;
    ImageView deviceManagementTimezoneSettingNor;
    LocalTextView advancedSettingNet;
    TextView advancedSettingNetName;
    View advancedSettingNetLine;
    LocalTextView advancedSettingDeviceidLabel;
    TextView advancedSettingDeviceid;
    LocalTextView tvRestrictMode;
    IOSSwitch switchRestrictMode;
    RelativeLayout rlRestrictMode;
    LocalTextView advancedSetting4g;
    ImageView advancedSetting4gNor;
    View advancedSetting4gLine;

    String TAG = "AdvancedSettingFragment bletest";

    private Device mPanelDevice;
    private boolean isSelfOperate;
    private PanelBinder mPanelBinder;
    private boolean isPanelWithFamily = true;
    private boolean isSupport4G;
    private String settleDeviceName;

    private final Handler mTimeoutHandler = new Handler(Looper.getMainLooper());
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private ChangePasswordDialog oldPsdDialog, newPsdDialog;

    public static AdvancedSettingFragment newInstance() {
        return new AdvancedSettingFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        showLoadingFragment(LoadingFragment.BLACK, "");
        View rootView = inflater.inflate(R.layout.advanced_setting_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        updateRestrictVisibilityState();
        update4gNetworkSettingVisibilityState();
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.advanced_setting_4g).setOnClickListener( v -> to4GSettingFragment());
        rootView.findViewById(R.id.device_management_timezone_setting).setOnClickListener( v -> toSetTimeZone());
        rootView.findViewById(R.id.device_management_timezone_setting_nor).setOnClickListener( v -> toSetTimeZone());
        rootView.findViewById(R.id.advanced_setting_reset_nor).setOnClickListener( v -> toResetDevice());
        rootView.findViewById(R.id.advanced_setting_reset).setOnClickListener( v -> toResetDevice());
        rootView.findViewById(R.id.advanced_setting_change_network_nor).setOnClickListener( v -> toChangeNetwork());
        rootView.findViewById(R.id.advanced_setting_change_network).setOnClickListener( v -> toChangeNetwork());
        rootView.findViewById(R.id.advanced_setting_change_password_nor).setOnClickListener( v -> toChangeDevicePassword());
        rootView.findViewById(R.id.advanced_setting_change_password).setOnClickListener( v -> toChangeDevicePassword());
    }

    private void __bindViews(View rootView) {
        advancedSettingVersionNumber = rootView.findViewById(R.id.advanced_setting_version_number);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        advancedSettingLabel = rootView.findViewById(R.id.advanced_setting_label);
        advancedSettingChangeNetwork = rootView.findViewById(R.id.advanced_setting_change_network);
        advancedSettingChangePassword = rootView.findViewById(R.id.advanced_setting_change_password);
        advancedSettingReset = rootView.findViewById(R.id.advanced_setting_reset);
        advancedSettingLanguage = rootView.findViewById(R.id.advanced_setting_language);
        advancedSettingVersion = rootView.findViewById(R.id.advanced_setting_version);
        deviceManagementTimezoneSetting = rootView.findViewById(R.id.device_management_timezone_setting);
        deviceManagementTimezoneSettingNor = rootView.findViewById(R.id.device_management_timezone_setting_nor);
        advancedSettingNet = rootView.findViewById(R.id.advanced_setting_net);
        advancedSettingNetName = rootView.findViewById(R.id.advanced_setting_net_name);
        advancedSettingNetLine = rootView.findViewById(R.id.advanced_setting_net_line);
        advancedSettingDeviceidLabel = rootView.findViewById(R.id.advanced_setting_deviceid_label);
        advancedSettingDeviceid = rootView.findViewById(R.id.advanced_setting_deviceid);
        tvRestrictMode = rootView.findViewById(R.id.tv_restrict_mode);
        switchRestrictMode = rootView.findViewById(R.id.switch_restrict_mode);
        rlRestrictMode = rootView.findViewById(R.id.rl_restrict_mode);
        advancedSetting4g = rootView.findViewById(R.id.advanced_setting_4g);
        advancedSetting4gNor = rootView.findViewById(R.id.advanced_setting_4g_nor);
        advancedSetting4gLine = rootView.findViewById(R.id.advanced_setting_4g_line);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.getAdvancedSetting());
    }

    /**
     * 根据配置更新限制模式入口显示状态
     */
    private void updateRestrictVisibilityState() {
        rlRestrictMode.setVisibility(AppConfig.Functions.SUPPORT_RESTRICT_MODE
                ? View.VISIBLE
                : View.GONE);
    }

    /**
     * 更新4g网络设置入口显示状态
     */
    private void update4gNetworkSettingVisibilityState() {
        String simNetwork = DeviceHelper.getString(mPanelDevice, PanelDataKey.Panel.SIM_NETWORK, "2g");
        boolean is4G = "4G".equalsIgnoreCase(simNetwork);
        int visibility = (AppConfig.Functions.SUPPORT_4G_NETWORK_SETTING && is4G) ? View.VISIBLE : View.GONE;
        DDLog.i(TAG, "update4gNetworkSettingVisibilityState, visibility: " + visibility);

        advancedSetting4g.setVisibility(visibility);
        advancedSetting4gNor.setVisibility(visibility);
        advancedSetting4gLine.setVisibility(visibility);
    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.device_management_advanced_label));
        advancedSettingLabel.setLocalText(getResources().getString(R.string.advanced_setting_label));
        advancedSetting4g.setLocalText(getResources().getString(R.string.advanced_setting_4g));
        advancedSettingChangeNetwork.setLocalText(getResources().getString(R.string.advanced_setting_change_network));
        advancedSettingChangePassword.setLocalText(getResources().getString(R.string.advanced_setting_change_password));
        advancedSettingReset.setLocalText(getResources().getString(R.string.advanced_setting_reset));
        advancedSettingVersion.setLocalText(getResources().getString(R.string.advanced_setting_version));
        advancedSettingNet.setLocalText(getResources().getString(R.string.advanced_setting_current_net));
        advancedSettingDeviceidLabel.setLocalText(getResources().getString(R.string.advanced_setting_deviceid));
        advancedSettingDeviceid.setText(TextUtils.isEmpty(mPanelDevice.getId()) ? "" : mPanelDevice.getId());
        tvRestrictMode.setLocalText(getResources().getString(R.string.advanced_setting_restrict_mode));
        if (APIKey.IS_SHOW_DEVICE_NET) {
            checkNetStatus();
            advancedSettingNet.setVisibility(View.VISIBLE);
            advancedSettingNetName.setVisibility(View.VISIBLE);
            advancedSettingNetLine.setVisibility(View.VISIBLE);
        } else {
            advancedSettingNet.setVisibility(View.GONE);
            advancedSettingNetName.setVisibility(View.GONE);
            advancedSettingNetLine.setVisibility(View.GONE);
        }
        changeVersonNumber();
        deviceManagementTimezoneSettingNor.setVisibility(View.GONE);
        deviceManagementTimezoneSetting.setVisibility(View.GONE);
        deviceManagementTimezoneSetting.setLocalText(getResources().getString(R.string.select_time_zone));
        String deviceVersionCode = DeviceHelper.getString(mPanelDevice, PanelDataKey.Panel.FIRMWARE_VERSION, null);
        if (APIKey.IS_OPEN_PLUGIN
                && !TextUtils.isEmpty(deviceVersionCode)) {
//                设置时区
            String[] currentVersions = deviceVersionCode.split("/");
            if (currentVersions.length >= 1 && CommonDataUtil.getInstance().checkShowPlugin(currentVersions[0])) {
                deviceManagementTimezoneSetting.setLocalText(getResources().getString(R.string.select_time_zone));
                deviceManagementTimezoneSettingNor.setVisibility(View.VISIBLE);
                deviceManagementTimezoneSetting.setVisibility(View.VISIBLE);
            }
        }

        switchRestrictMode.setOnTouchListener(new View.OnTouchListener() {
            int flag = 0;

            @Override
            public boolean onTouch(View v, MotionEvent event) {

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        flag = 0;
                        return true;
//                    case MotionEvent.ACTION_MOVE:
//                        flag = 1;
//                        return true;
                    case MotionEvent.ACTION_UP:
                        if (flag == 0) {
                            changeRestrictModeSmsStatus();
                        }
                        return true;
                }

                return false;
            }
        });
    }

    private void changeRestrictModeSmsStatus() {
        showLoadingFragment(LoadingFragment.BLACK, "");
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.setRestrictMode(!switchRestrictMode.isOn()));
    }

    public void changeVersonNumber() {
        String currenthardwareversion = DeviceHelper.getString(mPanelDevice,
                PanelDataKey.Panel.FIRMWARE_VERSION, null);
        if (TextUtils.isEmpty(currenthardwareversion)) {
            return;
        }
        advancedSettingVersionNumber.setText(currenthardwareversion);
    }

    public void to4GSettingFragment() {
        getDelegateActivity().addCommonFragment(FourGSettingFragment.newInstance());
    }

    public void toSetTimeZone() {
        getMainActivity().addCommonFragment(TimeZoneFragment.newInstance(true));
    }

    public void toResetDevice() {
        getDelegateActivity().addCommonFragment(ResetPasswordFragment.newInstance());
    }

    public void toChangeNetwork() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            if (PermissionUtil.isLocationPermissionsDeny(getContext())) {
                PermissionDialogUtil.showNeedBleLocationPermissionDialog(getMainActivity());
                return;
            }
            if (!DDSystemUtil.isOpenGPS(getContext())) {
                toOpenGPS(0);
                return;
            }
        } else {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), null);
                return;
            }
        }

        callDeviceOpenBle();
    }

    private String oldPassword;

    public void toChangeDevicePassword() {
        oldPsdDialog = ChangePasswordDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setContent(getResources().getString(R.string.advance_change_panel_input_old_pwd_title))
                .setAutoDismiss(false)
                .setOKListener(new ChangePasswordDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(ChangePasswordDialog passwordDialog, String password) {
                        if (!TextUtils.isEmpty(password) && password.length() == 4) {
                            // passwordDialog.dismiss();
                            oldPassword = password;
                            inputNewPassword();
                        }
                    }

                    @Override
                    public void onCancel(ChangePasswordDialog dialog) {

                    }
                })
                .preBuilder();
        oldPsdDialog.show();
    }

    private void inputNewPassword() {
        newPsdDialog = ChangePasswordDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Next))
                .setCancel(getResources().getString(R.string.Back))
                .setContent(getResources().getString(R.string.re_password_panel_dialog_tittle))
                .setAutoDismiss(false)
                .setOKListener(new ChangePasswordDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(ChangePasswordDialog passwordDialog, String password) {
                        if (!TextUtils.isEmpty(password) && password.length() == 4) {
                            // passwordDialog.dismiss();
                            toConfirmPassword(password);
                        }
                    }

                    @Override
                    public void onCancel(ChangePasswordDialog dialog) {

                    }
                })
                .preBuilder();
        newPsdDialog.show();
    }

    private void toConfirmPassword(final String firstPassword) {
        ChangePasswordDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Back))
                .setContent(getResources().getString(R.string.re_password_panel_dialog_tittle2))
                .setAutoDismiss(false)
                .setOKListener(new ChangePasswordDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(ChangePasswordDialog dialog, String password) {
                        if (!TextUtils.isEmpty(password) && password.length() == 4) {
                            if (checkPassword(firstPassword, password)) {
                                dialog.dismiss();
                                if (null != newPsdDialog && newPsdDialog.isShowing()) {
                                    newPsdDialog.dismiss();
                                }
                                if (null != oldPsdDialog && oldPsdDialog.isShowing()) {
                                    oldPsdDialog.dismiss();
                                }
                                toDoChangePassword(password);
                            } else {
                                showToast(Local.s(getResources().getString(R.string.password_not_match)));
                            }
                        }
                    }

                    @Override
                    public void onCancel(ChangePasswordDialog dialog) {

                    }
                })
                .preBuilder()
                .show();
    }

    private void toDoChangePassword(String password) {
        showLoadingFragment(LoadingFragment.BLACK);
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.setPanelPassword(oldPassword, password));
        mTimeoutHandler.postDelayed(() -> {
            if (AdvancedSettingFragment.this.isAdded()) {
                closeLoadingFragment();
                isSelfOperate = false;
                showErrorToast();
            }
        }, LocalKey.TIMEOUT);
    }

    private boolean checkPassword(String first, String second) {
        return first.equals(second);
    }

    @Override
    public void onDestroyView() {
        mHandler.removeCallbacksAndMessages(null);
        super.onDestroyView();
        if (subscribe != null && !subscribe.isUnsubscribed()) {
            subscribe.unsubscribe();
        }
        if (null != mPanelBinder) {
            mPanelBinder.stopScanPanel();
            mPanelBinder.disconnectAllBle();
            mPanelBinder.destroyBinder();
            mPanelBinder.destroyAdder();
        }
        EventBus.getDefault().unregister(this);
        mTimeoutHandler.removeCallbacksAndMessages(null);
        if (mPanelDevice != null) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    private void checkNetStatus() {
        if (null == DeviceInfoHelper.getInstance().getCurrentDeviceInfo()) {
            return;
        }

        if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getNetwork() == 0) {
            //有线连接
            advancedSettingNetName.setText(Local.s(getResources().getString(R.string.advanced_setting_net_cable)));
        } else {
            //这里是用蓝牙初次添加主机，成功后，登录时，没有获取到ssid的情况下需要用到。
            if ("".equals(DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getSsid())
                    && DBUtil.Exists(DBKey.BLE_SSID)) {
                DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setSsid(DBUtil.SGet(DBKey.BLE_SSID));
                DBUtil.Delete(DBKey.BLE_SSID);
            }
            advancedSettingNetName.setText(DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getSsid());
        }
    }

    private Subscription subscribe;

    private void callDeviceOpenBle() {
        if (!BleManager.getInstance().isBlueEnable()) {
            showOpenPhoneBle();
            return;
        }
        DDLog.d(TAG, "callDeviceOpenBle");
        showLoadingFragment(LoadingFragment.BLUE);
        isCanFail = true;
        if (subscribe != null && !subscribe.isUnsubscribed()) {
            subscribe.unsubscribe();
        }
        subscribe = Observable.interval(APIKey.BLE_ADVANCE_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                .take(1)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Long aLong) {
                        fail();
                    }
                });

        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.openPanelBluetooth(true));
    }

    private void toConnectBleDevice() {
        DinSDK.getPluginActivtor().setup(DinSaferApplication.getAppContext());
        DinSDK.getPluginActivtor().createPanelBinder();
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof PanelBinder)) {
            DDLog.e(TAG, "Error panel binder.");
            showErrorToast();
            return;
        }
        mPanelBinder = (PanelBinder) pluginBinder;
        mPanelBinder.init(DinSaferApplication.getInstance(),
                null != DinSDK.getUserInstance().getUser()
                        ? DinSDK.getUserInstance().getUser().getUid()
                        : "",
                null != DinSDK.getUserInstance().getUser()
                        ? DinSDK.getUserInstance().getUser().getUser_id()
                        : "", HomeManager.getInstance().getCurrentHome().getHomeID());
        mPanelBinder.initScanRule(0, new String[]{APIKey.UUID_SERVICE},
                APIKey.UUID_CHRA_WRITE, APIKey.UUID_CHRA_NOTIFY);

        mHandler.removeCallbacksAndMessages(null);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                mPanelBinder.startScanPanel(bleScanCallback);
            }
        }, 500);
    }

    private final IPanelScanListener bleScanCallback = new IPanelScanListener() {
        @Override
        public void onScanStarted(boolean success) {
            // 开始扫描（主线程）
            DDLog.d(TAG, "开始扫描");
        }

        @Override
        public void onScanning(BleDevice bleDevice) {
            DDLog.d(TAG, "扫描中:" + bleDevice.getName());
            // 扫描到一个符合扫描规则的BLE设备（主线程）
            if (bleDevice.getName() == null) {
                return;
            }

            if (!bleDevice.getName().equals(CommonDataUtil.getInstance().getCurrentPanelID())) {
                return;
            }
            if (null != mPanelBinder) {
                isPanelWithFamily = PanelBinder.isDeviceWithFamily(bleDevice);
                isSupport4G = PanelBinder.isDeviceSupport4G(bleDevice);
                mPanelBinder.connect(bleDevice, AdvancedSettingFragment.this);
            }
        }

        @Override
        public void onScanFinished(List<BleDevice> scanResultList) {
            // 扫描结束，列出所有扫描到的符合扫描规则的BLE设备（主线程）
            if (scanResultList.isEmpty()) {
            }
        }
    };

    /**
     * 如果弹窗选择了退出当前页，那么久不再在onWindowFacous做判断。是否弹出弹窗
     */
    private boolean isQuit = false;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(WindowFocusChangedEvent ev) {
        DDLog.d(TAG, "onWindowFocusChanged:");
        if (!getDelegateActivity().isCommonFragmentExist(BleStepCheckPasswordFragment.class.getName())) {
            return;
        }
        if (!BleManager.getInstance().isBlueEnable() && !isQuit) {
            showOpenPhoneBle();
        }
    }

    public void showOpenDeviceBle() {
        /**
         * 打开弹窗，提示检查主机蓝牙
         */
        if (dialog != null && dialog.isShowing()) {
            DDLog.d(TAG, "dialog != null && dialog.isShowing()");
            return;
        } else {
            DDLog.d(TAG, "dialog == null ||  dialog.isNotShowing()");
            dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_DEVICE);
            dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    if (null != mPanelBinder && mPanelBinder.isOpenedBluetooth() && null != mPanelDevice) {
                        dialog.dismiss();
                        getDelegateActivity().removeToFragment(AdvancedSettingFragment.class.getName());
                        callDeviceOpenBle();
                    }
                }

                @Override
                public void clickCanal() {
                    dialog.dismiss();
                    getDelegateActivity().removeToFragment(AdvancedSettingFragment.class.getName());
                }
            });
            dialog.show();
        }
    }


    BleCheckBluetoothDialog dialog = null;

    public void showOpenPhoneBle() {
        if (dialog != null && dialog.isShowing()) {
            return;
        } else {
            isQuit = false;
            dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_PHONE);
            dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    if (null != mPanelBinder && mPanelBinder.isOpenedBluetooth() && null != mPanelDevice) {
                        dialog.dismiss();
                        getDelegateActivity().removeToFragment(AdvancedSettingFragment.class.getName());
                        callDeviceOpenBle();
                    }
                }

                @Override
                public void clickCanal() {
                    isQuit = true;
                    dialog.dismiss();
                    getDelegateActivity().removeToFragment(AdvancedSettingFragment.class.getName());
                }
            });
            dialog.show();
        }
    }

    /**
     * 如果已经失败了，只能出现失败一次的处理
     */
    boolean isCanFail = true;

    /**
     * 失败处理
     */
    private void fail() {
        if (!isCanFail) {
            return;
        }
        DDLog.d(TAG, "onFail");
        showErrorToast();
        if (null != mPanelBinder) {
            mPanelBinder.disconnectAllBle();
        }
        clean();
        isCanFail = false;
    }

    /**
     * 清空处理：定时器、loading
     */
    private void clean() {
        closeLoadingFragment();
        DDLog.d(TAG, "clean");
        if (subscribe != null && !subscribe.isUnsubscribed()) {
            subscribe.unsubscribe();
        }
        BleManager.getInstance().cancelScan();
    }


    /**
     * 关闭定时器，用于其他弹窗出现的时候————ble断开的时候
     *
     * @param ev
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleCloseTimerEvent ev) {
        clean();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        DDLog.i(TAG, "On advance setting cmd result: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (PanelCmd.GET_ADVANCED_SETTING.equals(cmd)) {
            // 获取高级设置信息
            onGetAdvanceSettingInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_RESTRICT_MODE.equals(cmd)) {
                // 设置限制模式开关
                onSetRestrictMode(status, map);
            } else if (PanelCmd.SET_PANEL_PASSWORD.equals(cmd)) {
                // 设置主机密码
                onSetPanelPassword(status, map);
            } else if (PanelCmd.OPEN_PANEL_BLUETOOTH.equals(cmd)) {
                // 打开主机蓝牙
                onPanelOpenBluetooth(status, map);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置高级设置信息
     */
    private void onGetAdvanceSettingInfo(int status, Map map) {
        DDLog.i(TAG, "onGetAdvanceSettingInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        boolean isOn = DeviceHelper.getBoolean(resultMap, PanelDataKey.AdvancedSetting.IS_ON, false);
        boolean offlineSms = DeviceHelper.getBoolean(resultMap, PanelDataKey.AdvancedSetting.OFFLINE_SMS, false);
        String panelName = DeviceHelper.getString(resultMap, PanelDataKey.AdvancedSetting.PANEL_NAME, "");
        switchRestrictMode.setOn(offlineSms);
    }

    /**
     * 设置限制模式开关
     */
    private void onSetRestrictMode(int status, Map map) {
        DDLog.i(TAG, "onSetArmSound, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            switchRestrictMode.setOn(!switchRestrictMode.isOn());
        } else {
            showErrorToast();
        }
    }

    /**
     * 设置主机密码
     */
    private void onSetPanelPassword(int status, Map map) {
        DDLog.i(TAG, "onSetPanelPassword, status: " + status + ", result: " + map);
        mTimeoutHandler.removeCallbacksAndMessages(null);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            showSuccess();
        } else {
            int originStatus = (int) map.get(PanelDataKey.CmdResult.ORIGIN_STATUS);
            if (originStatus == -75) {
                //旧密码错误
                showToast(getString(R.string.change_uid_unbind_wrong_password));
            } else {
                showErrorToast();
            }
        }
    }

    /**
     * 请求主机打开蓝牙
     */
    private void onPanelOpenBluetooth(int status, Map map) {
        DDLog.i(TAG, "onSetPanelOpenBluetooth, status: " + status + ", result: " + map);
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            toConnectBleDevice();
        } else {
            fail();
        }
    }

    @Override
    public void onStartConnect() {

    }

    @Override
    public void onConnectFail(BleDevice bleDevice, BleException e) {
        fail();
    }

    @Override
    public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt bluetoothGatt, int i) {

    }

    @Override
    public void onDisConnected(BleDevice bleDevice, boolean b, BluetoothGatt bluetoothGatt, int i) {
        closeTimeOutLoadinFramgmentWithErrorAlert();
        getMainActivity().removeToFragment(AdvancedSettingFragment.class.getName());
        showOpenDeviceBle();
    }

    @Override
    public void onNotifySuccess() {
        clean();
        getDelegateActivity().addCommonFragment(BleStepCheckPasswordFragment.newInstance(LocalKey.BLE_CONFIG_DEVICE_NET_MODE,
                isPanelWithFamily ? LocalKey.BLE_PANEL_WITH_FAMILY
                        : LocalKey.BLE_PANEL_NO_FAMILY, isSupport4G));
    }

    @Override
    public void onNotifyFailure(BleDevice bleDevice, BleException e) {
        fail();
    }
}
