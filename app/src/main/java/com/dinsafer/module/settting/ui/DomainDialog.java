package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;


/**
 * Created by rinfon on 15/6/26.
 */
public class DomainDialog extends Dialog {

    int layoutRes;//布局文件

    Context mContext;

    LocalCustomButton mOk, mCancel;

    EditText etServer;
    EditText etStatServer;

    LocalTextView mContent;

    public DomainDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.domain_dialog;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mOk = (LocalCustomButton) view.findViewById(R.id.alert_dialog_ok);
        mCancel = (LocalCustomButton) view.findViewById(R.id.alert_dialog_cancel);
        mContent = (LocalTextView) view.findViewById(R.id.server_title);
        etServer = (EditText) view.findViewById(R.id.et_server);
        etStatServer = (EditText) view.findViewById(R.id.et_stat_server);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onOkClick(DomainDialog.this, etServer.getText().toString(), etStatServer.getText().toString());
                }
            }
        });

        mContent.setLocalText(builder.mContent);

        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOk.setVisibility(View.VISIBLE);
        } else {
            mOk.setVisibility(View.GONE);
        }
        if (builder.isShowCancel) {
            mCancel.setLocalText(builder.mCancel);
            mCancel.setVisibility(View.VISIBLE);
        } else {
            mCancel.setVisibility(View.GONE);
        }

        if (builder.getOkColor() != 0) {
            mOk.setTextColor(builder.getOkColor());
        }

        if (builder.getCancelColor() != 0) {
            mCancel.setTextColor(builder.getCancelColor());
        }

        if (!TextUtils.isEmpty(builder.defaultServer)) {
            etServer.setText(builder.defaultServer);
        }

        if (!TextUtils.isEmpty(builder.defaultStatServer)) {
            etStatServer.setText(builder.defaultStatServer);
        }

    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        super.cancel();
    }

    public interface AlertOkClickCallback {

        void onOkClick(DomainDialog dialog, String server, String statServer);
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mOK;

        private boolean isShowOK = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isAutoDismiss = true;

        private AlertOkClickCallback okClick;

        private String defaultServer;
        private String defaultStatServer;

        private int OkColor = 0;

        private int CancelColor = 0;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }


        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            isShowCancel = true;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setDefaultServer(String defaultServer) {
            this.defaultServer = defaultServer;
            return this;
        }

        public Builder setDefaultStatServer(String defaultStatServer) {
            this.defaultStatServer = defaultStatServer;
            return this;
        }

        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public int getOkColor() {
            return OkColor;
        }

        public Builder setOkColor(int okColor) {
            OkColor = okColor;
            return this;
        }

        public int getCancelColor() {
            return CancelColor;
        }

        public Builder setCancelColor(int CancelColor) {
            this.CancelColor = CancelColor;
            return this;
        }

        public DomainDialog preBuilder() {
            DomainDialog alertDialog = new DomainDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
