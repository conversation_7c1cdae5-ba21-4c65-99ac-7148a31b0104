package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.AdapterView;

import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SwitchBotListLayoutBinding;
import com.dinsafer.model.SwitchBotEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.adapter.SwitchBotAdapter;
import com.dinsafer.module.settting.ui.delegate.SwitchBotDelegate;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * <AUTHOR>
 * @date 2019/3/22
 */
public class SwitchBotListFragment extends MyBaseFragment<SwitchBotListLayoutBinding> {

    private SwitchBotDelegate switchBotDelegate;
    private SwitchBotAdapter switchBotAdapter;
    private ArrayList<SwitchBotEntry> mData;
    private Observable<Void> sortObservable = Observable.create(new Observable.OnSubscribe<Void>() {
        @Override
        public void call(Subscriber<? super Void> subscriber) {
            subscriber.onNext(sortData());
            subscriber.onCompleted();
        }
    }).subscribeOn(Schedulers.newThread()).observeOn(AndroidSchedulers.mainThread()).compose(this.<Void>bindToLifecycle());


    public static SwitchBotListFragment newInstance() {
        SwitchBotListFragment switchBotListFragment = new SwitchBotListFragment();
        return switchBotListFragment;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.switch_bot_list_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBarRight.setOnClickListener(v -> toEdit());
        mBinding.tvEditDone.setOnClickListener(v -> toEditDone());
    }

    @Override
    public void initData() {
        super.initData();

        mBinding.tvCollectTip.setLocalText(getResources().getString(R.string.switch_bot_collect_tip));
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.switch_bot));
        mBinding.listviewEmpty.setLocalText("");
        mBinding.tvEditDone.setLocalText(getResources().getString(R.string.smart_plugin_edit_finish));
        mData = new ArrayList<>();
        switchBotAdapter = new SwitchBotAdapter(mData);
        mBinding.switchBotList.setAdapter(switchBotAdapter);
        switchBotAdapter.setCollectListener(new SwitchBotAdapter.CollectListener() {
            @Override
            public void onCollectChange(int pos) {

                if (!mData.get(pos).isCollected()) {
                    toCollect(mData.get(pos).getId(), mData.get(pos).getName());
                    mData.get(pos).setCollected(true);
                    updateView();
                    switchBotAdapter.notifyDataSetChanged();
                } else {
                    toDelete(mData.get(pos).getId(), mData.get(pos).getName(), pos);
                }
            }
        });
        switchBotAdapter.setControlListener(new SwitchBotAdapter.ControlListener() {
            @Override
            public void onControl(int pos, boolean isOn) {
                mData.get(pos).setLoading(true);
                mData.get(pos).setLoadingOn(isOn);
                startLoading(mData.get(pos).getId());
                String action = mData.get(pos).isOneBtn() ? "press" : (isOn ? "on" : "off");
                switchBotDelegate.controlSwitchBot(mData.get(pos).getId(), action, new SwitchBotDelegate.ControlListener() {
                    @Override
                    public void onSuccess(String id) {
                        int i = mData.indexOf(getTmpSwitchBotEntry(id));
                        if (i >= 0 && mData.get(i).isLoading()) {
                            mData.get(i).setLoading(false)
                                    .setOn(isOn);
                            switchBotAdapter.notifyDataSetChanged();
                            timerMap.get(id).unsubscribe();
                        }
                    }

                    @Override
                    public void onFail(String id) {
                        int i = mData.indexOf(getTmpSwitchBotEntry(id));
                        if (i >= 0 && mData.get(i).isLoading()) {
                            mData.get(i).setLoading(false);
                            switchBotAdapter.notifyDataSetChanged();
                            timerMap.get(id).unsubscribe();
                        }
                    }
                });
                switchBotAdapter.notifyDataSetChanged();
            }
        });

        mBinding.switchBotList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (switchBotAdapter.isEditMode() && mData.get(position).isCollected()) {
                    DDLog.d(TAG, "点击了编辑");
                    Builder builder = new Builder();
                    builder.setId(mData.get(position).getId())
                            .setName(mData.get(position).getName())
                            .setBtnOne(mData.get(position).isOneBtn())
                            .setOffline(mData.get(position).isOffline())
                            .setShowwave(true);

                    SwitchBotSettingFragment modifyASKPlugsFragment = SwitchBotSettingFragment.newInstance(builder);
                    modifyASKPlugsFragment.setSettingListener(new SwitchBotSettingFragment.SettingListener() {
                        @Override
                        public void onChangeName(String id, String name) {
                            if (mData.contains(getTmpSwitchBotEntry(id))) {
                                mData.get(mData.indexOf(new SwitchBotEntry().setId(id))).setName(name);
                                switchBotAdapter.notifyDataSetChanged();
                            }
                        }

                        @Override
                        public void onChangeMode(String id, boolean isOneBtn) {
                            if (mData.contains(getTmpSwitchBotEntry(id))) {
                                mData.get(mData.indexOf(new SwitchBotEntry().setId(id))).setOneBtn(isOneBtn);
                                switchBotAdapter.notifyDataSetChanged();
                            }
                        }
                    });
                    getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);

                    toEditDone();

                }
            }
        });
        // LT-OS: 这句话必须加
        mBinding.switchBotList.setEmptyView(mBinding.listviewEmpty);

        mBinding.tvEditDone.setVisibility(View.GONE);
        mBinding.commonBarRight.setVisibility(View.GONE);
        mBinding.listviewEmpty.setVisibility(View.GONE);

        switchBotDelegate = new SwitchBotDelegate();
        switchBotDelegate.setStatusChangeListener(statusChangeListener);
        dealCollectTip();


        Animation operatingAnim = AnimationUtils.loadAnimation(getContext(), R.anim.rotation);
        LinearInterpolator lin = new LinearInterpolator();
        operatingAnim.setInterpolator(lin);
        mBinding.imgLoading.startAnimation(operatingAnim);


        Observable.interval(LocalKey.TIMEOUT, TimeUnit.MILLISECONDS)
                .take(1)
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "bot test timeout");
                        if (mBinding.imgLoading.getVisibility() != View.GONE) {
                            mBinding.imgLoading.setVisibility(View.GONE);
                            mBinding.imgLoading.clearAnimation();
                            if (mData.size() <= 0) {

                                mBinding.listviewEmpty.setVisibility(View.VISIBLE);
                                mBinding.listviewEmpty.setLocalText(getResources().getString(R.string.switch_bot_list_not_data));
                            }
                        }
                    }
                });

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (switchBotDelegate != null) {
            switchBotDelegate.clear();
        }

        if (statusChangeListener != null) {
            statusChangeListener = null;
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
//        getVirtualData();
        getData();
    }

    private SwitchBotDelegate.StatusChangeListener statusChangeListener = new SwitchBotDelegate.StatusChangeListener() {
        @Override
        public void onStatusChanged(ArrayList<SwitchBotEntry> switchBotList) {
            for (int i = 0; i < switchBotList.size(); i++) {
                if (mData.contains(switchBotList.get(i))) {
                    SwitchBotEntry switchBotEntry = mData.get(mData.indexOf(switchBotList.get(i)));
                    switchBotEntry.setOneBtn(switchBotList.get(i).isOneBtn())
                            .setNoStatus(false)
                            .setLoading(false)
                            .setOffline(false)
                            .setOn(switchBotList.get(i).isOn());
                } else {
                    switchBotList.get(i).setCollected(false);
                    mData.add(switchBotList.get(i));
                }
            }

            for (int i = 0; i < mData.size(); i++) {
                mData.get(i).setNoStatus(false);
                if (!switchBotList.contains(mData.get(i))) {
                    if (mData.get(i).isCollected()) {
                        mData.get(i).setOffline(true);
                    } else {
                        mData.remove(mData.get(i));
                        i--;
                    }
                }
            }

            updateView();
            if (mData.size() > 0) {
                mBinding.imgLoading.clearAnimation();
                mBinding.imgLoading.setVisibility(View.GONE);
            }
            // FIXME: 2019/4/18 这里从有到无要出现空的提示符
        }

        @Override
        public void onCollectedSwitchBot(String id) {

            mBinding.commonBarRight.setVisibility(View.VISIBLE);
            if (mData.contains(getTmpSwitchBotEntry(id))) {
                int i = mData.indexOf(getTmpSwitchBotEntry(id));
                if (mData.get(i).isCollected()) {
                    return;
                }
                mData.get(mData.indexOf(i)).setCollected(true);
                updateView();
            } else {
                SwitchBotEntry switchBotEntry = new SwitchBotEntry();
                switchBotEntry.setNoStatus(false)
                        .setCollected(true);
                mData.add(switchBotEntry);
                updateView();
            }
        }

        @Override
        public void onDeleteSwitchBot(String id) {
            if (mData.contains(getTmpSwitchBotEntry(id))) {
                int i = mData.indexOf(getTmpSwitchBotEntry(id));
                if (mData.get(i).isCollected()) {
                    mData.get(mData.indexOf(i)).setCollected(false);
                    updateView();
                }
            }

            boolean isNotCollect = true;
            for (int i = 0; i < mData.size(); i++) {
                if (mData.get(i).isCollected()) {
                    isNotCollect = false;
                    break;
                }
            }
            if (isNotCollect) {
                mBinding.commonBarRight.setVisibility(View.GONE);
            }
        }

        @Override
        public void onChangeSwitchBotName(String id, String name) {
            if (mData.contains(getTmpSwitchBotEntry(id))) {
                int i = mData.indexOf(getTmpSwitchBotEntry(id));
                if (!mData.get(i).getName().equals(name)) {
                    return;
                }
                mData.get(mData.indexOf(i)).setName(name);
                switchBotAdapter.notifyDataSetChanged();

            }
        }

        @Override
        public void onChangeSwitchBotStatus(String id, boolean isOneBtn, boolean isOn) {
            if (mData.contains(getTmpSwitchBotEntry(id))) {
                int i = mData.indexOf(getTmpSwitchBotEntry(id));
                if (mData.get(i).isLoading()) {
                    timerMap.get(id).unsubscribe();
                }
                mData.get(i).setOn(isOn)
                        .setLoading(false)
                        .setNoStatus(false)
                        .setOffline(false)
                        .setOneBtn(isOneBtn);
                switchBotAdapter.notifyDataSetChanged();

            } else {
                SwitchBotEntry switchBotEntry = new SwitchBotEntry();
                switchBotEntry.setNoStatus(false)
                        .setOneBtn(isOneBtn)
                        .setOn(isOn)
                        .setId(id)
                        .setOffline(false)
                        .setCollected(false);
                mData.add(switchBotEntry);
                updateView();
            }
            if (mBinding.imgLoading.getVisibility() != View.GONE) {
                mBinding.imgLoading.setVisibility(View.GONE);
                mBinding.imgLoading.clearAnimation();
            }
        }
    };

    private boolean isEditing = false;

    public void toEdit() {
        isEditing = true;
        switchBotAdapter.setEditMode(isEditing);
        switchBotAdapter.notifyDataSetChanged();
        mBinding.commonBarRight.setVisibility(View.GONE);
        mBinding.tvEditDone.setVisibility(View.VISIBLE);
    }

    public void toEditDone() {
        isEditing = false;
        switchBotAdapter.setEditMode(isEditing);
        switchBotAdapter.notifyDataSetChanged();
        mBinding.commonBarRight.setVisibility(View.VISIBLE);
        mBinding.tvEditDone.setVisibility(View.GONE);
    }

    private void dealCollectTip() {
        if (switchBotDelegate.isShowCollectTip()) {
            mBinding.lyCollectTip.setVisibility(View.VISIBLE);
            mBinding.lyCollectTip.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    switchBotDelegate.disappearCollectTip();
                    mBinding.lyCollectTip.setVisibility(View.GONE);
                }
            });
        } else {
            mBinding.lyCollectTip.setVisibility(View.GONE);
        }
    }

    private void getData() {
        switchBotDelegate.getCollectedList(new SwitchBotDelegate.GetCollectedListListener() {
            @Override
            public void onSuccess(ArrayList<SwitchBotEntry> list) {
                if (list.size() > 0) {
                    mBinding.commonBarRight.setVisibility(View.VISIBLE);
                    mData.addAll(list);
                    switchBotAdapter.notifyDataSetChanged();
                } else {
                    mBinding.listviewEmpty.setVisibility(View.GONE);
                }
            }

            @Override
            public void fail() {

            }
        });
    }

    private void getVirtualData() {
        for (int i = 0; i < 6; i++) {
            SwitchBotEntry switchBotEntry = new SwitchBotEntry();
            switchBotEntry.setCollected(true)
                    .setName("collected" + i)
                    .setId(i + "");
            mData.add(switchBotEntry);
        }
        switchBotAdapter.notifyDataSetChanged();

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                boolean isOneBtn[] = {true, true, false, false};
                boolean isOn[] = {true, false, true, false};

                for (int i = 0; i < 4; i++) {
                    SwitchBotEntry switchBotEntry = new SwitchBotEntry();
                    switchBotEntry.setCollected(false)
                            .setName("uncollected" + i)
                            .setOneBtn(isOneBtn[i])
                            .setOn(isOn[i])
                            .setId((i + 10) + "");
                    mData.add(switchBotEntry);
                }

                boolean isOneBtn1[] = {true, true, true, false, false, false};
                boolean isOn1[] = {true, false, true, false, true, false};
                boolean isOffline1[] = {true, false, false, true, false, false};
                boolean isCollect1[] = {false, true, true, true, false, true};
                for (int i = 0; i < 6; i++) {
                    SwitchBotEntry switchBotEntry = mData.get(i);
                    switchBotEntry.setOn(isOn1[i])
                            .setOffline(isOffline1[i])
                            .setCollected(isCollect1[i])
                            .setOneBtn(isOneBtn1[i]);
                }


                getDelegateActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {


                        Collections.sort(mData, new Comparator<SwitchBotEntry>() {
                            @Override
                            public int compare(SwitchBotEntry o1, SwitchBotEntry o2) {
                                int i = o1.isCollected() ? 0 : 1;
                                int j = o2.isCollected() ? 0 : 1;

                                return i - j;
                            }
                        });

                        switchBotAdapter.notifyDataSetChanged();
                        mBinding.imgLoading.clearAnimation();
                        mBinding.imgLoading.setVisibility(View.GONE);
                        mBinding.commonBarRight.setVisibility(View.VISIBLE);
                    }
                });
            }
        }, 5 * 1000);
    }

    private Void sortData() {
        if (mData.isEmpty()) {
            return null;
        }
        /**
         * 排序，收藏的在前
         */
        Collections.sort(mData, new Comparator<SwitchBotEntry>() {
            @Override
            public int compare(SwitchBotEntry o1, SwitchBotEntry o2) {
                int i = o1.isCollected() ? 0 : 1;
                int j = o2.isCollected() ? 0 : 1;

                return i - j;
            }
        });
        return null;
    }

    private void updateView() {
        sortObservable.subscribe(new Subscriber<Void>() {
            @Override
            public void onCompleted() {

                getMainActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        switchBotAdapter.notifyDataSetChanged();
                    }
                });
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Void aVoid) {

            }
        });
    }

    private SwitchBotEntry tmp = new SwitchBotEntry();

    private SwitchBotEntry getTmpSwitchBotEntry(String id) {
        tmp.setId(id);
        return tmp;
    }

    private void toCollect(String id, String name) {

        mBinding.commonBarRight.setVisibility(View.VISIBLE);
        switchBotDelegate.collectSwitchBot(id, name, new SwitchBotDelegate.SwitchBotCallback() {
            @Override
            public void onSuccess() {
                DDLog.d(TAG, "收藏成功");
            }

            @Override
            public void onFail() {
                DDLog.d(TAG, "收藏失败");
            }

        });


    }


    private void toDelete(String id, String name, int pos) {
        AlertDialog.createBuilder(getMainActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setContent(getResources().getString(R.string.switch_bot_delete_tip))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {

                        if (mData.get(pos).getId().equals(id)) {

                            mData.get(pos).setCollected(false);
                            updateView();
                            switchBotAdapter.notifyDataSetChanged();

                            switchBotDelegate.deleteSwitchBot(id, name, new SwitchBotDelegate.SwitchBotCallback() {
                                @Override
                                public void onSuccess() {
                                    DDLog.d(TAG, "删除成功");
                                }

                                @Override
                                public void onFail() {
                                    DDLog.d(TAG, "删除失败");
                                }
                            });


                            boolean isNotCollect = true;
                            for (int i = 0; i < mData.size(); i++) {
                                if (mData.get(i).isCollected()) {
                                    isNotCollect = false;
                                    break;
                                }
                            }
                            if (isNotCollect) {
                                mBinding.commonBarRight.setVisibility(View.GONE);
                            }
                        } else if (mData.contains(getTmpSwitchBotEntry(id))) {
                            mData.get(mData.indexOf(getTmpSwitchBotEntry(id))).setCollected(false);

                            updateView();
                            switchBotAdapter.notifyDataSetChanged();
                        }
                    }
                })
                .preBuilder()
                .show();
        return;

    }

    public void startLoading(String id) {

        Subscription timer = Observable.interval(LocalKey.TIMEOUT, TimeUnit.MILLISECONDS)
                .take(1)
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "bot test timeout");
                        if (mData.contains(getTmpSwitchBotEntry(id))) {
                            int i = mData.indexOf(getTmpSwitchBotEntry(id));
                            if (mData.get(i).isLoading()) {
                                mData.get(i).setLoading(false);
                                switchBotAdapter.notifyDataSetChanged();
                            }
                            this.unsubscribe();
                        }
                    }
                });

        timerMap.put(id, timer);

    }

    private Map<String, Subscription> timerMap = new HashMap<String, Subscription>();

    @Override
    public void onPauseFragment() {
        super.onPauseFragment();
        if (!APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE && switchBotDelegate != null) {
            switchBotDelegate.closeScanTimer();
        }
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        if (!APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE && switchBotDelegate != null) {
            switchBotDelegate.startScanTimerAndDisconnect();
        }
    }
}
