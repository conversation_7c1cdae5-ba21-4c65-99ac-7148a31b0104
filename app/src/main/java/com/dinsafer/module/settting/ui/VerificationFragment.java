package com.dinsafer.module.settting.ui;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.VerificationLayoutBinding;
import com.dinsafer.module.MyBaseFragment;
import com.github.sahasbhop.apngview.ApngDrawable;
import com.github.sahasbhop.apngview.ApngImageLoader;

/**
 * Created by Rinfon on 16/7/8.
 */
public class VerificationFragment extends MyBaseFragment<VerificationLayoutBinding> {

    public static VerificationFragment newInstance() {
        return new VerificationFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.verification_layout;
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.verification_title));
        mBinding.verificationContent.setLocalText(getResources().getString(R.string.verification_content));
        mBinding.verificationEmail.setLocalText(getResources().getString(R.string.verification_email_text));
        mBinding.verificationPhone.setLocalText(getResources().getString(R.string.verification_phone_text));
        mBinding.verificationCancel.setLocalText(getResources().getString(R.string.bind_account_hint_layout_cancel_text));
        String uri = "assets://apng/animation_verification.png";
        ApngImageLoader.getInstance()
                .displayApng(uri, mBinding.verificationIcon,
                        new ApngImageLoader.ApngConfig(1, true));

        mBinding.verificationEmail.setOnClickListener(v->toEmailVerification());
        mBinding.verificationPhone.setOnClickListener(v->toPhoneVerification());
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v->close());
        mBinding.verificationCancel.setOnClickListener(v->close());

    }

    public void toEmailVerification() {
        getDelegateActivity().addCommonFragment(UserEmailFragment.newInstance());
    }

    public void toPhoneVerification() {
        getDelegateActivity().addCommonFragment(ChangePhoneFragment.newInstance());
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onEnterFragment() {
        toStartAnim();
    }

    private void toStartAnim() {
        if (mBinding.verificationIcon == null) {
            return;
        }

        ApngDrawable apngDrawable = ApngDrawable.getFromView(mBinding.verificationIcon);
        if (apngDrawable == null) return;

        if (!apngDrawable.isRunning()) {
            apngDrawable.setNumPlays(1);
            apngDrawable.start(); // Stop animation
        }
    }

    @Override
    public void onPauseFragment() {
        toStopAnim();
    }

    private void toStopAnim() {
        if (mBinding.verificationIcon == null) {
            return;
        }

        ApngDrawable apngDrawable = ApngDrawable.getFromView(mBinding.verificationIcon);
        if (apngDrawable == null) return;

        if (apngDrawable.isRunning()) {
            apngDrawable.stop(); // Stop animation
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        toStartAnim();
    }

    @Override
    public void onFinishAnim() {
        toStartAnim();
    }

    @Override
    public void onExitFragment() {
        toStopAnim();
    }
}

