package com.dinsafer.module.settting.ui.model;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/7.
 */
public class FourGSettingFragment extends BaseFragment
        implements IDeviceCallBack {

    IOSSwitch switch4g;
    LocalTextView tvTitle;
    RelativeLayout rlInfo;
    EditText etPsd;
    EditText etUserName;
    EditText etApn;
    LocalCustomButton btnSave;
    LocalTextView mTv4gAuto;
    LocalTextView mTvApn;
    LocalTextView mTvUserName;
    LocalTextView mTvPsd;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static FourGSettingFragment newInstance() {
        return new FourGSettingFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        showLoadingFragment(LoadingFragment.BLUE);
        View rootView = inflater.inflate(R.layout.fragment_4g, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initView(rootView, savedInstanceState);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.btn_save).setOnClickListener( v -> toSave());
    }

    private void __bindViews(View rootView) {
        switch4g = rootView.findViewById(R.id.switch4G);
        tvTitle = rootView.findViewById(R.id.common_bar_title);
        rlInfo = rootView.findViewById(R.id.rlInfo);
        etPsd = rootView.findViewById((R.id.etPsd));
        etUserName = rootView.findViewById((R.id.etUserName));
        etApn = rootView.findViewById((R.id.etApn));
        btnSave = rootView.findViewById(R.id.btn_save);
        mTv4gAuto = rootView.findViewById(R.id.tv_4g_auto);
        mTvApn = rootView.findViewById(R.id.tvApn);
        mTvUserName = rootView.findViewById(R.id.tvUserName);
        mTvPsd = rootView.findViewById(R.id.tvPsd);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.get4gInfo());
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        tvTitle.setLocalText(getString(R.string.advance_4g_title));
        mTv4gAuto.setLocalText(getString(R.string.advanced_setting_4g_auto));
        mTvApn.setLocalText(getString(R.string.advanced_setting_4g_apn));
        mTvUserName.setLocalText(getString(R.string.advance_4g_user_name));
        mTvPsd.setLocalText(getString(R.string.advance_4g_user_password));
        etApn.setHint(Local.s(getString(R.string.advanced_setting_4g_hint_type)));
        etUserName.setHint(Local.s(getString(R.string.advanced_setting_4g_hint_type)));
        etPsd.setHint(Local.s(getString(R.string.advanced_setting_4g_hint_type)));
        btnSave.setLocalText(getString(R.string.save));

        switch4g.setOnSwitchStateChangeListener(isOn -> {
            updateSaveBtnEnable();
            if (isOn) {
                rlInfo.setVisibility(View.INVISIBLE);
            } else {
                rlInfo.setVisibility(View.VISIBLE);
            }
        });
        switch4g.setOn(true);

        etApn.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                updateSaveBtnEnable();
            }
        });
    }

    private void updateSaveBtnEnable(){
        if(switch4g.isOn()){
            btnSave.setEnabled(true);
            btnSave.setAlpha(MainPanelHelper.VIEW_ENABLE_ALPHA);
            return;
        }

        if(TextUtils.isEmpty(etApn.getText().toString())){
            btnSave.setEnabled(false);
            btnSave.setAlpha(MainPanelHelper.VIEW_DISABLE_ALPHA);
        }else{
            btnSave.setEnabled(true);
            btnSave.setAlpha(MainPanelHelper.VIEW_ENABLE_ALPHA);
        }
    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    public void toSave() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.set4gInfo(switch4g.isOn(), etApn.getText().toString(),
                etPsd.getText().toString(), etUserName.getText().toString()));
    }

    private void showData(boolean isAutomatic, String nodeName, String password, String userName) {
        if (isAutomatic) {
            rlInfo.setVisibility(View.INVISIBLE);
        } else {
            rlInfo.setVisibility(View.VISIBLE);
        }
        switch4g.setOn(isAutomatic);
        etApn.setText(nodeName);
        etPsd.setText(password);
        etUserName.setText(userName);
    }

    @Override
    public void onDestroyView() {
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        super.onDestroyView();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (PanelCmd.GET_4G_INFO.equals(cmd)) {
            onGet4GInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_4G_INFO.equals(cmd)) {
                onSet4GInfo(status, map);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置4G信息
     */
    private void onGet4GInfo(int status, Map map) {
        DDLog.i(TAG, "onGet4GInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        boolean isAutomatic = DeviceHelper.getBoolean(resultMap, PanelDataKey.Panel4GInfo.AUTOMATIC, false);
        String nodeName = DeviceHelper.getString(resultMap, PanelDataKey.Panel4GInfo.NODE_NAME, "");
        String password = DeviceHelper.getString(resultMap, PanelDataKey.Panel4GInfo.PASSWORD, "");
        String userName = DeviceHelper.getString(resultMap, PanelDataKey.Panel4GInfo.USER_NAME, "");
        showData(isAutomatic, nodeName, password, userName);
    }

    /**
     * 设置4G信息
     */
    private void onSet4GInfo(int status, Map map) {
        DDLog.i(TAG, "onSet4GInfo, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            removeSelf();
        } else {
            showErrorToast();
        }
    }
}
