package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;


/**
 * Created by Rinfon on 16/7/8.
 */
public class UserEmailFragment extends BaseFragment {

    LocalTextView commonBarTitle;
    LocalCustomButton changeEmailSend;
    LocalCustomButton changeEmailUnbind;

    EditText etAccount;

    public static float BACKGROUND_ALPHA = 0.5f;

    public static UserEmailFragment newInstance() {
        return new UserEmailFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.user_email_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.change_email_unbind).setOnClickListener( v -> toUnbind());
        rootView.findViewById(R.id.change_email_send).setOnClickListener( v -> toSend());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
        rootView.findViewById(R.id.common_background).setOnClickListener( v -> closeInput());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        changeEmailSend = rootView.findViewById(R.id.change_email_send);
        changeEmailUnbind = rootView.findViewById(R.id.change_email_unbind);
        etAccount = rootView.findViewById(R.id.et_account);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.mail_setting));
        changeEmailUnbind.setLocalText(getResources().getString(R.string.change_email_unbind));
        etAccount.setHint(Local.s(getString(R.string.email_address)));
        if (!TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail())) {
            etAccount.setText(DinSDK.getUserInstance().getUser().getEmail());
            etAccount.setEnabled(false);
            if (!TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone())) {
                changeEmailUnbind.setVisibility(View.VISIBLE);
            } else {
                changeEmailUnbind.setVisibility(View.GONE);
            }
        } else {
//            changeEmailDescription.setLocalText(getResources().getString(R.string.change_email_description));
            changeEmailUnbind.setVisibility(View.GONE);
        }
        changeEmailSend.setLocalText(getResources().getString(R.string.change_binding_text));
//        changeEmailSend.setAlpha(BACKGROUND_ALPHA);
//        changeEmailSend.setEnabled(false);
        etAccount.addTextChangedListener(new TextWatcher() {
//            boolean hasValue = false;

            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(etAccount.getText())
                        && DDDateUtil.isEmail(etAccount.getText().toString())) {
                    changeEmailSend.setBackground(getResources().getDrawable(R.drawable.blue_rectangle));
                    changeEmailSend.setAlpha(1f);
                    changeEmailSend.setEnabled(true);
                } else {
                    changeEmailSend.setBackground(getResources().getDrawable(R.drawable.blue_rectangle));
                    changeEmailSend.setAlpha(BACKGROUND_ALPHA);
                    changeEmailSend.setEnabled(false);
                }
            }
        });
    }

    public void toUnbind() {
        if (TextUtils.isEmpty(etAccount.getText().toString())) {
            return;
        }

        getDelegateActivity().addCommonFragment(UnbindPhoneEmailVerifyCodeFragment.newInstance(DinSDK.getUserInstance().getUser().getEmail()));

//        showTimeOutLoadinFramgmentWithErrorAlert();
////         解绑email
//        DinSDK.getUserInstance().unbindEmail(changeEmail.getText().toString(), new IResultCallback() {
//            @Override
//            public void onError(int i, String s) {
//                closeTimeOutLoadinFramgmentWithErrorAlert();
//                showErrorToast();
//            }
//
//            @Override
//            public void onSuccess() {
//                closeTimeOutLoadinFramgmentWithErrorAlert();
//                showToast(getResources().getString(R.string.change_email_unbind_hint));
//                removeSelf();
//            }
//        });

    }

    public void toSend() {
        getDelegateActivity().addCommonFragment(ChangeEmailFragment.newInstance());
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    public void closeInput() {
        toCloseInput();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

}

