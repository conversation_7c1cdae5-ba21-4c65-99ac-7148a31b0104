package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SettingWebviewLayoutBinding;
import com.dinsafer.module.MyBaseFragment;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ThirdPartServiceFragment extends MyBaseFragment<SettingWebviewLayoutBinding> {
    public static ThirdPartServiceFragment newInstance() {
        return new ThirdPartServiceFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.setting_webview_layout;
    }

    @Override
    public void initData() {
        super.initData();
        showTimeOutLoadinFramgmentWithBack();
        mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_third_part_service));
        mBinding.settingWebview.getSettings().setJavaScriptEnabled(true);
        mBinding.settingWebview.getSettings().setAllowFileAccess(true);
        mBinding.settingWebview.setBackgroundColor(Color.TRANSPARENT);
        mBinding.settingWebview.getSettings()
                .setPluginState(WebSettings.PluginState.ON);
        mBinding.settingWebview.setWebViewClient(new WebViewClient() {

            public void onPageFinished(WebView view, String url) {
                closeLoadingFragment();
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                return super.shouldOverrideUrlLoading(view, url);
            }
        });
        mBinding.settingWebview.loadUrl(APIKey.THIRD_PART_SERVICE);
    }

}

