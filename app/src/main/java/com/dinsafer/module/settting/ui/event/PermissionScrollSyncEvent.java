package com.dinsafer.module.settting.ui.event;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/14 6:29 下午
 */
public class PermissionScrollSyncEvent {
    private final int hashcode;
    private final int permissionType;
    private int lastPosition = 0;
    private int lastOffset = 0;

    public PermissionScrollSyncEvent(int hashcode, int permissionType, int lastPosition, int lastOffset) {
        this.hashcode = hashcode;
        this.permissionType = permissionType;
        this.lastPosition = lastPosition;
        this.lastOffset = lastOffset;
    }

    public int getPermissionType() {
        return permissionType;
    }

    public int getLastOffset() {
        return lastOffset;
    }

    public int getLastPosition() {
        return lastPosition;
    }

    public int getHashcode() {
        return hashcode;
    }

    @Override
    public String toString() {
        return "PermissionScrollSyncEvent{" +
                "hashcode=" + hashcode +
                ", permissionType=" + permissionType +
                ", lastPosition=" + lastPosition +
                ", lastOffset=" + lastOffset +
                '}';
    }
}
