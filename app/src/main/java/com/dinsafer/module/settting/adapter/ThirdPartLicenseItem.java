package com.dinsafer.module.settting.adapter;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/12 4:44 下午
 */
public class ThirdPartLicenseItem {
    private final String name;
    private final String copyright;
    private final String url;

    public ThirdPartLicenseItem(String name, String copyright, String url) {
        this.name = name;
        this.copyright = copyright;
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public String getCopyright() {
        return copyright;
    }

    public String getUrl() {
        return url;
    }

    @Override
    public String toString() {
        return "ThirdPartLicenseItem{" +
                "name='" + name + '\'' +
                ", copyright='" + copyright + '\'' +
                ", url='" + url + '\'' +
                '}';
    }

    public static class Builder {
        private String name;
        private String copyright;
        private String url;

        public Builder setName(String name) {
            this.name = name;
            return this;
        }

        public Builder setCopyright(String copyright) {
            this.copyright = copyright;
            return this;
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public ThirdPartLicenseItem create() {
            return new ThirdPartLicenseItem(name, copyright, url);
        }
    }
}
