package com.dinsafer.module.settting.ui;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.model.PlugsData;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;

import androidx.databinding.ViewDataBinding;

/**
 * 包含倒计时的配件列表页面基类
 *
 * <AUTHOR>
 * @date 2020/9/3 5:51 PM
 */
public abstract class BaseTimeoutPluginFragment<T extends ViewDataBinding> extends MyBaseFragment<T>
        implements IDeviceCallBack {


    private Handler mTimeOutHandler = new Handler(Looper.getMainLooper());

    protected Device mPanelDevice;

    protected void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onDestroyView() {
        cancelTimeoutCountDown();
        if (null != mPanelDevice) {
            mPanelDevice.submit(PanelParamsHelper.stopRoundRobinPluginState());
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        super.onDestroyView();
    }

    /**
     * 取消超时倒计时
     */
    protected void cancelTimeoutCountDown() {
        DDLog.i(TAG, "cancelTimeoutCountDown");
        mTimeOutHandler.removeCallbacksAndMessages(null);
    }

    /**
     * 开启超时倒计时
     */
    protected void startTimeoutCountDown() {
        DDLog.i(TAG, "startTimeoutCountDown");
        mTimeOutHandler.postDelayed(() -> {
            DDLog.i(TAG, "Loading timeout after " + LocalKey.TIMEOUT);
            onLoadStatusTimeOut();
        }, LocalKey.TIMEOUT);
    }

    @Override
    public void onPause() {
        super.onPause();
        stopQueryPluginStatusRoundRobin();
    }

    @Override
    public void onResume() {
        super.onResume();
        startQueryPluginStatusRoundRobin();
    }

    @Override
    public void onPauseFragment() {
        super.onPauseFragment();
        DDLog.i(TAG, "onPauseFragment");
        stopQueryPluginStatusRoundRobin();

    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        DDLog.i(TAG, "onEnterFragment");
        startQueryPluginStatusRoundRobin();
    }

    /**
     * 更新配件的电量值
     *
     * @param result "plugins":[
     *               {
     *               "id":"",  // string,配件id
     *               "enable":true,   // bool,门磁开关状态, 可能没有该字段
     *               "battery_level": 100 // 电量百分比
     *               },
     */
    protected void updatePluginBattery(String result) {
        DDLog.i(TAG, "updatePluginBattery, RESULT: " + result);

        cancelTimeoutCountDown();

        ArrayList<PlugsData> sourceData = getCurrentPluginList();
        if (null == sourceData
                || 0 >= sourceData.size()
                || TextUtils.isEmpty(result)) {
            return;
        }

        Iterator it = sourceData.iterator();
        while (it.hasNext()) {
            PlugsData plugsData = (PlugsData) it.next();
            boolean batteryLeverError = false;
            try {
                JSONArray js;
                try {
                    JSONObject jObj = new JSONObject(result);
                    js = DDJSONUtil.getJSONarray(jObj, "plugins");
                } catch (Exception e) {
                    js = new JSONArray(result);
                }

                if (null != js && 0 < js.length()) {
                    for (int i = 0; i < js.length(); i++) {
                        JSONObject object = (JSONObject) js.get(i);
                        if (object.get(NetKeyConstants.NET_KEY_ID).equals(plugsData.getPlugId())) {
                            plugsData.setLoadStatusError(false);
                            plugsData.setLoadingView(false);
                            if (object.has(NetKeyConstants.NET_KEY_ENABLE)) {
                                plugsData.setEnable(object.getBoolean(NetKeyConstants.NET_KEY_ENABLE));
                            }
                            if (object.has(NetKeyConstants.NET_KEY_BATTERY__LEVEL)) {
                                plugsData.setBatteryLevel(object.getInt(NetKeyConstants.NET_KEY_BATTERY__LEVEL));
                                batteryLeverError = 255 == plugsData.getBatteryLevel();
                            }
                            if (object.has(NetKeyConstants.NET_KEY_RSSI)) {
                                plugsData.setSignalValue(object.getInt(NetKeyConstants.NET_KEY_RSSI));
                            }
                            if (object.has(NetKeyConstants.NET_KEY_TAMPER)) {
                                plugsData.setTamper(object.getBoolean(NetKeyConstants.NET_KEY_TAMPER));
                            }
                            if (object.has(NetKeyConstants.NET_KEY_CHARGING)) {
                                plugsData.setCharging(object.getBoolean(NetKeyConstants.NET_KEY_CHARGING));
                            }
                            if (object.has(NetKeyConstants.NET_KEY_SENSITIVITY)) {
                                plugsData.setSensitivity(object.getInt(NetKeyConstants.NET_KEY_SENSITIVITY));
                            }
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (batteryLeverError) {
                // 电量值为255，一直显示loading
                plugsData.setLoadingView(true);
            } else if (plugsData.isLoadingView()) {
                plugsData.setLoadStatusError(true)
                        .setLoadingView(false);
            }
        }

        onPluginStatusUpdate();

        // 请求结束后，开始定时轮询
        startQueryPluginStatusRoundRobin();
    }

    /**
     * 超时情况处理
     */
    protected void onLoadStatusTimeOut() {
        DDLog.i(TAG, "onLoadStatusTimeOut");

        ArrayList<PlugsData> sourceData = getCurrentPluginList();
        if (null == sourceData
                || 0 >= sourceData.size()) {
            return;
        }

        for (PlugsData plugin :
                sourceData) {
            if (plugin.isLoadingView()) {
                plugin.setLoadStatusError(true)
                        .setLoadingView(false);
            }
        }

        onPluginStatusUpdate();

        // 超时也开始轮询
        startQueryPluginStatusRoundRobin();
    }

    /**
     * 更新配件状态后回调
     * 通常需要进行页面数据刷新
     */
    protected abstract void onPluginStatusUpdate();

    /**
     * 获取当前列表数据
     */
    protected abstract ArrayList<PlugsData> getCurrentPluginList();


    /**
     * 开启轮询
     */
    protected void startQueryPluginStatusRoundRobin() {
        if (null != mPanelDevice) {
            mPanelDevice.submit(PanelParamsHelper.startRoundRobinPluginState());
        } else {
            DDLog.e(TAG, "Error on startQueryPluginStatusRoundRobin because panel device is null");
        }
    }

    /**
     * 停止定时轮询
     */
    protected void stopQueryPluginStatusRoundRobin() {
        if (null != mPanelDevice) {
            mPanelDevice.submit(PanelParamsHelper.stopRoundRobinPluginState());
        } else {
            DDLog.e(TAG, "Error on stopQueryPluginStatusRoundRobin because panel device is null");
        }
    }
}
