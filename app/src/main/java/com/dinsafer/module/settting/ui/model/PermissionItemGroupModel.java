package com.dinsafer.module.settting.ui.model;

import android.content.Context;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPermissionGroupBinding;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DisplayUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/14 3:03 下午
 */
public class PermissionItemGroupModel extends PermissionItemModel<ItemPermissionGroupBinding> {

    public static final int GROUP_TYPE_TOP = 0;
    public static final int GROUP_TYPE_MID = 1;
    public static final int GROUP_TYPE_BOTTOM = 2;
    public static final int GROUP_TYPE_SINGLE = 3;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({GROUP_TYPE_TOP, GROUP_TYPE_MID, GROUP_TYPE_BOTTOM, GROUP_TYPE_SINGLE})
    public @interface GroupType {
    }

    private static final float CONTENT_PADDING = 17.5f;
    private static final float TITTLE_PADDING = 10f;

    private final PermissionItemGroupData groupData;

    public PermissionItemGroupModel(@NonNull PermissionItemGroupData groupData) {
        this.groupData = groupData;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_permission_group;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPermissionGroupBinding binding) {
        final LinearLayout parentView = binding.llPermissionGroup;
        parentView.setBackgroundResource(getBackgroundResId(groupData.getGroupType()));
        parentView.removeAllViews();
        parentView.addView(getGroupTittleView(parentView, groupData.getGroupTittle()));
        final List<String> functions = groupData.getFunctions();
        if (null != functions && functions.size() > 0) {
            for (String function : functions) {
                parentView.addView(getFunctionItemView(parentView, function));
            }
        }
    }

    private int getBackgroundResId(@GroupType int groupType) {
        if (GROUP_TYPE_TOP == groupType) {
            return R.drawable.shape_item_permission_top;
        } else if (GROUP_TYPE_MID == groupType) {
            return R.drawable.shape_item_permission_mid;
        } else if (GROUP_TYPE_SINGLE == groupType) {
            return R.drawable.shape_item_permission_single;
        } else {
            return R.drawable.shape_item_permission_bottom;
        }
    }

    private LocalTextView getFunctionItemView(@NonNull ViewGroup parent, @NonNull String functionName) {
        final Context context = parent.getContext();
        final int padding = DisplayUtil.dip2px(context, CONTENT_PADDING);
        LocalTextView functionView = new LocalTextView(parent.getContext());
        functionView.setTextColor(context.getResources().getColor(R.color.color_white_01));
        functionView.setTextAppearance(context, R.style.TextFamilyBodyM);
        functionView.setPadding(0, padding, 0, padding);
        functionView.setLocalText(functionName);
        return functionView;
    }

    private LocalTextView getGroupTittleView(@NonNull ViewGroup parent, @NonNull String groupTittle) {
        final Context context = parent.getContext();
        final int padding = DisplayUtil.dip2px(context, TITTLE_PADDING);
        LocalTextView functionView = new LocalTextView(parent.getContext());
        functionView.setTextColor(context.getResources().getColor(R.color.color_white_03));
        functionView.setTextAppearance(context, R.style.TextFamilyCaptionL);
        functionView.setPadding(0, padding, 0, padding);
        functionView.setLocalText(groupTittle);
        return functionView;
    }
}
