package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.common.PluginManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ModifyDoorSensorLayoutBinding;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.model.CloseAllDeviceEvent;
import com.dinsafer.model.SmartButtonActionChangeEvent;
import com.dinsafer.model.SmartButtonSceneData;
import com.dinsafer.model.SmartButtonTargetData;
import com.dinsafer.model.event.NeedLoadInfoPluginsEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.pirSensitivity.PirSensitivitySettingFragment;
import com.dinsafer.module.pirSensitivity.PirSensitivitySettingGuideFragment;
import com.dinsafer.module.pirSensitivity.event.PirSensitivityUpdateEvent;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;
import com.dinsafer.util.SmartButtonUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

import static com.dinsafer.util.SmartButtonUtil.SERVICE_ACTION_SINGLE_PRESS;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_MUSIC;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_NAME;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_PLUGIN_ID;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_SCENE;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_SEND_ID;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_STYPE;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_VOLUME;

/**
 * 用于门磁和红外的改名，只用于改名，不用于配件添加。并且只用于helio smart新类型配件
 * 增加了block功能设置
 * Created by Rinfon on 16/7/12.
 */
public class ModifyDoorSensorAndPirPlugsFragment extends MyBaseFragment<ModifyDoorSensorLayoutBinding>
        implements ActionSheet.ActionSheetListener, IDeviceCallBack {
    private ICallBack callBack;
    private Builder builder;
    private String sendID;
    private String sType;
    private int mSensitivity;
    private int mCurrentMode = 0;
    private boolean mAlwaysPush;
    private JSONObject mData;

    private Device mPluginDevice;
    private boolean isSelfOperate;
    private Dialog sensitivityDialog;

    public static ModifyDoorSensorAndPirPlugsFragment newInstance(Builder builder) {
        ModifyDoorSensorAndPirPlugsFragment modifyPlugsFragment = new ModifyDoorSensorAndPirPlugsFragment();
        Bundle args = new Bundle();
        args.putParcelable("data", builder);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.modify_door_sensor_layout;
    }

    private void findDevice() {
        String pluginId = builder.getId();
        if (!TextUtils.isEmpty(pluginId)) {
            mPluginDevice = DinHome.getInstance().getDevice(pluginId);
            if (null != mPluginDevice) {
                mPluginDevice.registerDeviceCallBack(this);
            }
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBarRightIcon.setOnClickListener(v -> clickRightIcon());
        //初始化先设置为不可用
        mBinding.btnSave.setEnabled(false);
        mBinding.btnSave.setOnClickListener(v -> toSave());
        mBinding.modifyPlugsInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mBinding.btnSave.setEnabled(s.length() > 0);
            }
        });
//        modifyNameDelete.setOnClickListener(v -> toDeleteItem(0));
        mBinding.rlPirSensitivity.setOnClickListener(v -> toChangePirSensitivity());
        mBinding.modifyPlugsChimeSettingL.setOnClickListener(v -> toSelectChime());
        mBinding.rlAlwaysPushStatus.setOnClickListener(v -> toChangeAlwaysPushStatus());
        mBinding.modifyPlugsModeSettingL.setOnClickListener(v -> toModeSelect());
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.modify_plugs_title));
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.modifyPlugsInput.setHint(Local.s(getResources().getString(R.string.modifyaccessoryhint)));
//        modifyNameDelete.setLocalText(getResources().getString(R.string.change_permission_delete));
        mBinding.modifyPlugsChimeSetting.setLocalText(getString(R.string.select_chime));
        mBinding.modifyPlugsAlwaysPushStatus.setLocalText(getString(R.string.modify_plugs_always_push_status));
        builder = (Builder) getArguments().getParcelable("data");
        if (builder == null) {
            removeSelf();
        }
        String id = builder.getId();
        boolean isOffical = builder.isOffical();
        mSensitivity = builder.getSensitivity();

        if (builder.isShowDelete()) {
            mBinding.commonBarRightIcon.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBarRightIcon.setVisibility(View.GONE);
        }
        if (builder.isAdd()) {
            mBinding.btnSave.setVisibility(View.VISIBLE);
        } else {
            mBinding.btnSave.setVisibility(View.GONE);
            mBinding.modifyPlugsInput.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icon_nav_edit, 0);
            mBinding.modifyPlugsInput.setFocusable(false);
            mBinding.modifyPlugsInput.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showChangeNameDialog();
                }
            });
        }
        mBinding.modifyPlugsModeSetting.setLocalText(getResources().getString(R.string.modify_plugs_mode_setting));

        mBinding.modifyPlugsModeSettingL.setVisibility(View.GONE);

        try {
            mData = new JSONObject(builder.getData());
            DDLog.d(TAG, mData.toString());
            String name = "";
            if (!TextUtils.isEmpty(DDJSONUtil.getString(mData, "decodeid"))) {
                name = CommonDataUtil.getInstance().getSTypeByDecodeid(DDJSONUtil.getString(mData, "decodeid"));
            } else if (builder.getId().startsWith("!")) {
                sType = DDJSONUtil.getString(mData, "stype");
                sendID = DDJSONUtil.getString(mData, "sendid");
                name = CommonDataUtil.getInstance().getASKNameByBSType(sType);
                if ((!TextUtils.isEmpty(sType) && (sType.equals("1C") || sType.equals("11")))) {
                    mBinding.modifyPlugsInput.setText(getMainActivity().getResources().getString(R.string.door_sensor_modify_view));
                }
                if (PluginConstants.TYPE_36.equals(sType)
                        || PluginConstants.TYPE_4A.equals(sType)
                        || PluginConstants.TYPE_38.equals(sType)
                        || PluginConstants.TYPE_3D.equals(sType)
                        || PluginConstants.TYPE_2C.equals(sType)
                        || PluginConstants.TYPE_3F.equals(sType)) {
                    mBinding.modifyPlugsModeSettingL.setVisibility(View.VISIBLE);
                    mCurrentMode = DDJSONUtil.getInt(mData, "block", 0);
                    updataMode();
                }
            } else {
                name = CommonDataUtil.getInstance().getSTypeByID(builder.getId());
            }
            mBinding.modifyPlugsType.setLocalText(name);
            if (!TextUtils.isEmpty(builder.getName())) {
                mBinding.modifyPlugsInput.setText(builder.getName());
            } else {
                mBinding.modifyPlugsInput.setText(Local.s(name) + "_" + builder.getId());
            }


        } catch (JSONException e) {

        }

        if (builder.isOffline() || builder.isLowPower()) {
            mBinding.pluginStatusHint.setVisibility(View.VISIBLE);
            if (builder.isOffline() && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE) {
                mBinding.pluginStatusHint.setLocalText(getResources().getString(R.string.plugin_offline_hint));
            } else {
                mBinding.pluginStatusHint.setLocalText(getResources().getString(R.string.plugin_lowpower_hint));
            }
        } else {
            mBinding.pluginStatusHint.setVisibility(View.GONE);
        }

        mBinding.modifyPlugsId.setText("ID:" + id);
        findDevice();
    }

    private void updataMode() {
        switch (mCurrentMode) {
            case 1:
                mBinding.modifyPlugsCurrentMode.setLocalText(getResources().getString(R.string.block_mode_tamper_alarm));
                mBinding.modifyPlugsHint.setLocalText(getResources().getString(R.string.block_mode_tamper_alarm_hint));
                mBinding.modifyPlugsChimeSettingL.setVisibility(View.GONE);
                mBinding.modifyPlugsHint.setVisibility(View.VISIBLE);
                updateAlwaysPushSettingVisible();
                updatePirSensitivityVisible();
                break;
            case 2:
                mBinding.modifyPlugsCurrentMode.setLocalText(getResources().getString(R.string.block_mode_plugin));
                mBinding.modifyPlugsHint.setLocalText(getResources().getString(R.string.block_mode_block_plugin_hint));
                mBinding.modifyPlugsChimeSettingL.setVisibility(View.GONE);
                mBinding.modifyPlugsHint.setVisibility(View.VISIBLE);
                updateAlwaysPushSettingVisible();
                updatePirSensitivityVisible();
                break;
            case 3:
                mBinding.modifyPlugsCurrentMode.setLocalText(getResources().getString(R.string.block_mode_chime));
                mBinding.modifyPlugsHint.setLocalText(getResources().getString(R.string.block_mode_chime_hint));
                mBinding.modifyPlugsHint.setVisibility(View.VISIBLE);
                JSONObject chimePlugin = DDJSONUtil.getJSONObject(mData, "0");
                updateAlwaysPushSettingVisible();
                updatePirSensitivityVisible();
                if (chimePlugin == null || 0 >= chimePlugin.length()) {
                    return;
                }
                String name = DDJSONUtil.getString(chimePlugin, "name");
                String pluginId = DDJSONUtil.getString(chimePlugin, "pluginid");
                if (TextUtils.isEmpty(name)) {
                    if (!TextUtils.isEmpty(DDJSONUtil.getString(chimePlugin, "decodeid"))) {
                        name = CommonDataUtil.getInstance().getSTypeByDecodeid(DDJSONUtil.getString(chimePlugin, "decodeid"));
                    }
                    if (!TextUtils.isEmpty(pluginId)) {
                        if (pluginId.startsWith("!")) {
                            String localsType = DDJSONUtil.getString(chimePlugin, "stype");
                            if (!TextUtils.isEmpty(localsType)) {
                                name = CommonDataUtil.getInstance().getASKNameByBSType(localsType);
                            }
                        } else {
                            name = CommonDataUtil.getInstance().getSTypeByID(pluginId);
                        }
                    }
                    name = Local.s(name) + "_" + pluginId;
                }
                mBinding.modifyPlugsChimeSettingCurrentMode.setText(name);
                mBinding.modifyPlugsChimeSettingL.setVisibility(View.VISIBLE);
                break;
            case 0:
            default:
                mBinding.modifyPlugsCurrentMode.setLocalText(getResources().getString(R.string.block_mode_normal));
                mBinding.modifyPlugsChimeSettingL.setVisibility(View.GONE);
                mBinding.modifyPlugsHint.setVisibility(View.GONE);
                updateAlwaysPushSettingVisible();
                updatePirSensitivityVisible();
                break;
        }
    }

    private void updateAlwaysPushSettingVisible() {
        // 包含门磁开合推送设置的门磁
        if ((PluginConstants.TYPE_38.equals(sType) || PluginConstants.TYPE_3D.equals(sType))
                && (PanelConstant.PluginBlockType.BLOCK_TYPE_NULL == mCurrentMode || PanelConstant.PluginBlockType.BLOCK_TYPE_TAMPER == mCurrentMode)) {
            mBinding.rlAlwaysPushStatus.setVisibility(View.VISIBLE);
            mAlwaysPush = DDJSONUtil.getBoolean(mData, NetKeyConstants.NET_KEY_PUSH_STATUS);
            mBinding.modifyPlugsAlwaysPushStatusValue.setLocalText(mAlwaysPush ?
                    Local.s(getString(R.string.on)) : Local.s(getString(R.string.off)));
        } else {
            mBinding.rlAlwaysPushStatus.setVisibility(View.GONE);
        }
    }

    private void updatePirSensitivityVisible() {
        // 可调节灵敏度红外 sType = "4A"
        if (PluginConstants.TYPE_4A.equals(sType)) {
            mBinding.rlPirSensitivity.setVisibility(View.VISIBLE);
            updateSensitivity(mSensitivity);
        } else {
            mBinding.rlPirSensitivity.setVisibility(View.GONE);
        }
    }

    private void updateSensitivity(int sensitivity) {
        String sensitivityStr = "";
        if (PanelConstant.PirSensitivityType.PIR_SENSITIVITY_LOW == sensitivity) {
            sensitivityStr = getString(R.string.pir_sensitivity_low);
        } else if (PanelConstant.PirSensitivityType.PIR_SENSITIVITY_MIDDLE == sensitivity) {
            sensitivityStr = getString(R.string.pir_sensitivity_middle);
        } else if (PanelConstant.PirSensitivityType.PIR_SENSITIVITY_HIGH == sensitivity) {
            sensitivityStr = getString(R.string.pir_sensitivity_high);
        }
        mBinding.modifyPlugsPirSensitivityValue.setLocalText(sensitivityStr);
    }

    public void showChangeNameDialog() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultName(mBinding.modifyPlugsInput.getText().toString())
                .setContent(getResources().getString(R.string.rename_accessory))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
//                                            change ipc password
                        if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                            dialog.dismiss();
                            return;
                        }
                        dialog.dismiss();
                        mBinding.modifyPlugsInput.setText(string);
                        toSave();
                    }
                })
                .preBuilder()
                .show();
    }

    public void toSave() {
        final String inputName = mBinding.modifyPlugsInput.getText().toString().trim();
        if (TextUtils.isEmpty(inputName) || !RegxUtil.isLegalName(inputName)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }
        DDLog.i(TAG, "toChangePluginName");
        if (null != mPluginDevice) {
            DDLog.i(TAG, "修改名字");
            showTimeOutLoadinFramgmentWithErrorAlert();
            isSelfOperate = true;
            mPluginDevice.submit(PanelParamsHelper.setPluginName(inputName));
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    public void toChangePirSensitivity() {
        if (null != sensitivityDialog) {
            sensitivityDialog.dismiss();
            sensitivityDialog = null;
        }

        sensitivityDialog = new Dialog(getContext(), R.style.BottomDialog);
        View contentView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_bottom, null);

        ((LocalTextView) contentView.findViewById(R.id.tv_content)).setLocalText(getContext().getString(R.string.dialog_sensitivity_context));

        ((LocalCustomButton) contentView.findViewById(R.id.btn_ok)).setLocalText(getContext().getString(R.string.dialog_sensitivity_set_up));
        contentView.findViewById(R.id.btn_ok).setOnClickListener(v -> {
            if (sensitivityDialog.isShowing())
                sensitivityDialog.dismiss();
            toSetPirSettingEnabledStatue();
        });

        ((LocalCustomButton) contentView.findViewById(R.id.btn_cancel)).setLocalText(getContext().getString(R.string.cancel));
        contentView.findViewById(R.id.btn_cancel).setOnClickListener(v -> {
            if (sensitivityDialog.isShowing())
                sensitivityDialog.dismiss();
        });

        sensitivityDialog.setContentView(contentView);
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) contentView.getLayoutParams();
        params.width = getResources().getDisplayMetrics().widthPixels - DensityUtil.dp2px(getContext(), 16f);
        contentView.setLayoutParams(params);
        sensitivityDialog.getWindow().setGravity(Gravity.BOTTOM);
        sensitivityDialog.getWindow().setWindowAnimations(R.style.BottomDialog_Animation);
        sensitivityDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_BLUR_BEHIND | WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        sensitivityDialog.show();
    }

    public void toSelectChime() {
        toChime();
    }

    public void toChangeAlwaysPushStatus() {
        getMainActivity().addCommonFragment(DoorSensorAlwaysPushSettingFragment.newInstance(builder));
    }


    public void toModeSelect() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.block_mode_chime)),
                        Local.s(getResources().getString(R.string.block_mode_plugin)),
                        Local.s(getResources().getString(R.string.block_mode_tamper_alarm)),
                        Local.s(getResources().getString(R.string.block_mode_normal)))
                .setCancelableOnTouchOutside(true)
                .setListener(this).show();
    }


    public void clickRightIcon() {
        showMoreActionDialog();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
        if (null != sensitivityDialog) {
            sensitivityDialog.dismiss();
            sensitivityDialog = null;
        }
        EventBus.getDefault().unregister(this);
        if (callBack != null) {
            callBack = null;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(CloseAllDeviceEvent ev) {
        DDLog.d(TAG, "onEventMainThread(CloseAllDeviceEvent ev): maybe 有人报警");
        if (null != sensitivityDialog) {
            sensitivityDialog.dismiss();
            sensitivityDialog = null;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(SmartButtonActionChangeEvent ev) {
        closeTimeOutLoadinFramgmentWithErrorAlert();
        getDelegateActivity().removeToFragment(this.getClass().getName());
        try {
            mData = new JSONObject(builder.getData());
            JSONObject configData = DDJSONUtil.getJSONObject(mData, SERVICE_ACTION_SINGLE_PRESS);
            if (null == configData) {
                configData = new JSONObject();
                mData.put(SERVICE_ACTION_SINGLE_PRESS, configData);
            }
            configData.put(SERVICE_KEY_MUSIC, ev.getNewAction().getActionData().getMusicIndex());
            configData.put(SERVICE_KEY_VOLUME, ev.getNewAction().getActionData().getVolumeIndex());
            configData.put(SERVICE_KEY_NAME, ev.getNewAction().getTargetData().getTargetName());
            configData.put(SERVICE_KEY_PLUGIN_ID, ev.getNewAction().getTargetData().getTargetId());
            configData.put(SERVICE_KEY_SCENE, SmartButtonUtil.getSceneStringByType(ev.getNewAction().getSceneType()));
            configData.put(SERVICE_KEY_SEND_ID, ev.getNewAction().getTargetData().getSendid());
            configData.put(SERVICE_KEY_STYPE, ev.getNewAction().getTargetData().getStype());
            builder.setData(mData);
        } catch (JSONException e) {
            DDLog.e(TAG, "ERROR");
            e.printStackTrace();
            return;
        }

        toSaveChime();
    }

    private void toSaveChime() {
        if (null != mPluginDevice) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            isSelfOperate = true;
            mCurrentMode = PanelConstant.PluginBlockType.BLOCK_TYPE_CHIME;
            mPluginDevice.submit(PanelParamsHelper.configPluginBlock(PanelConstant.PluginBlockType.BLOCK_TYPE_CHIME));
        } else {
            showErrorToast();
            DDLog.e(TAG, "No plugin device.");
        }
    }

    private void toSetPirSettingEnabledStatue() {
        if (null != mPluginDevice) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            isSelfOperate = true;
            mPluginDevice.submit(PanelParamsHelper.setPirSettingEnabledStatue());
        } else {
            showErrorToast();
            DDLog.e(TAG, "No plugin device.");
        }
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        if (index == 0) {
            toChime();
        } else if (index == 1) {
            toBlockPlugin();
        } else if (index == 2) {
            toBlockTamperAlarm();
        } else {
            toBlockNormal();
        }
    }

    private void toChime() {
        SmartButtonTargetData mSmartButtonData;
        try {
            JSONObject mAskData = new JSONObject(builder.getData());
            mSmartButtonData = new SmartButtonTargetData.Builder()
                    .setTargetName(DDJSONUtil.getString(mAskData, SmartButtonUtil.SERVICE_KEY_NAME))
                    .setTargetId(DDJSONUtil.getString(mAskData, "id"))
                    .setSendid(DDJSONUtil.getString(mAskData, SmartButtonUtil.SERVICE_KEY_SEND_ID))
                    .setStype(DDJSONUtil.getString(mAskData, SmartButtonUtil.SERVICE_KEY_STYPE))
                    .setDtype(DDJSONUtil.getInt(mAskData, SmartButtonUtil.SERVICE_KEY_DTYPE))
                    .createSmartButtonTargetData();
            SmartButtonSceneData data = new SmartButtonSceneData.Builder()
                    .setItemType(SmartButtonUtil.ITEM_TYPE_ACTION)
                    .setSceneType(SmartButtonUtil.SCENE_TYPE_RING_BELL)
                    .createSmartButtonSceneData();
            JSONObject configData = DDJSONUtil.getJSONObject(mAskData, SERVICE_ACTION_SINGLE_PRESS);
            data.getActionData().setMusicIndex(DDJSONUtil.getInt(configData, SERVICE_KEY_MUSIC, 0));
            data.getActionData().setVolumeIndex(DDJSONUtil.getInt(configData, SERVICE_KEY_VOLUME, 0));
            getDelegateActivity().addCommonFragment(
                    SmartButtonSelectTargetFragment.newInstance(data, mSmartButtonData));
        } catch (JSONException e) {
            DDLog.e(TAG, "ERROR");
            e.printStackTrace();
            return;
        }
    }

    private void toBlockNormal() {
        if (null != mPluginDevice) {
            DDLog.i(TAG, "toBlockNormal");
            showTimeOutLoadinFramgmentWithErrorAlert();
            isSelfOperate = true;
            mCurrentMode = PanelConstant.PluginBlockType.BLOCK_TYPE_NULL;
            mPluginDevice.submit(PanelParamsHelper.configPluginBlock(PanelConstant.PluginBlockType.BLOCK_TYPE_NULL));
        } else {
            showErrorToast();
            DDLog.e(TAG, "No plugin device.");
        }
    }

    private void toBlockPlugin() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(Local.s(getResources().getString(R.string.sure)))
                .setCancel(Local.s(getResources().getString(R.string.cancel)))
                .setContent(Local.s(getResources().getString(R.string.block_plugin_content)))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (null != mPluginDevice) {
                            DDLog.i(TAG, "toBlockPlugin");
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            isSelfOperate = true;
                            mCurrentMode = PanelConstant.PluginBlockType.BLOCK_TYPE_PLUGIN;
                            mPluginDevice.submit(PanelParamsHelper.configPluginBlock(PanelConstant.PluginBlockType.BLOCK_TYPE_PLUGIN));
                        } else {
                            showErrorToast();
                            DDLog.e(TAG, "No plugin device.");
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    private void toBlockTamperAlarm() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(Local.s(getResources().getString(R.string.sure)))
                .setCancel(Local.s(getResources().getString(R.string.cancel)))
                .setContent(Local.s(getResources().getString(R.string.block_tamper_content)))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (null != mPluginDevice) {
                            DDLog.i(TAG, "toBlockTamperAlarm");
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            isSelfOperate = true;
                            mCurrentMode = PanelConstant.PluginBlockType.BLOCK_TYPE_TAMPER;
                            mPluginDevice.submit(PanelParamsHelper.configPluginBlock(PanelConstant.PluginBlockType.BLOCK_TYPE_TAMPER));
                        } else {
                            showErrorToast();
                            DDLog.e(TAG, "No plugin device.");
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    private void toDeleteItem() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (null != mPluginDevice) {
                            DDLog.i(TAG, "toDeleteItem");
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            isSelfOperate = true;
                            mPluginDevice.submit(PanelParamsHelper.deletePlugin());
                        } else {
                            showErrorToast();
                            DDLog.e(TAG, "No plugin device.");
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    public interface ICallBack {

        void onChangeName(int index, String name);

        void onBlockModeChange(String pluginID, int mode);

        void onPirSensitivityChange(String pluginID, int sensitivity);

        void onDelete(String id);
    }

    public ICallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(ICallBack callBack) {
        this.callBack = callBack;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        final boolean operateSelf = DeviceHelper.getBoolean(map, PanelDataKey.CmdResult.OWNER, false);

        if (!isSelfOperate
                && PanelDataKey.CmdResult.SUCCESS == status
                && PluginCmd.SET_DOOR_WINDOW_PUSH_STATUS.equals(cmd)) {
            String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
            tryUpdateAlwaysPushStatus(result);
            return;
        }

        isSelfOperate = false;
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        switch (cmd) {
            case PluginCmd.PLUGIN_SETNAME:
                removeSelf();
                if (callBack != null)
                    callBack.onChangeName(builder.getMessageIndex(), mBinding.modifyPlugsInput.getText().toString());
                break;
            case PluginCmd.PLUGIN_DELETE:
                if (callBack != null) {
                    callBack.onDelete(builder.getId());
                }
                EventBus.getDefault().post(new PluginDeleteEvent(builder.getId()));
                removeSelf();
                break;
            case PluginCmd.PLUGIN_CONFIG_BLOCK:
                updataMode();
                showSuccess();
                if (callBack != null)
                    callBack.onBlockModeChange(builder.getId(), mCurrentMode);
                break;
            case PluginCmd.BYPASS_PLUGIN_5_MIN:
                if (!operateSelf) {
                    return;
                }
                String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    boolean pirSettingEnabled = DDJSONUtil.getBoolean(jsonObject, PanelDataKey.PirSensitivityInfo.PIR_SETTING_ENABLED);
                    if (pirSettingEnabled) {
                        // 直接进入灵敏度设置页
                        getDelegateActivity().addCommonFragment(PirSensitivitySettingFragment.newInstance(builder));
                    } else {
                        getDelegateActivity().addCommonFragment(PirSensitivitySettingGuideFragment.newInstance(builder));
                    }
                    getMainActivity().sendExitPirSettingMsg();
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;

        }
    }

    private void tryUpdateAlwaysPushStatus(String result) {
        DDLog.i(TAG, "tryUpdateAlwaysPushStatus, result: " + result);
        try {
            JSONObject jsonObject = new JSONObject(result);
            String plugin_id = DDJSONUtil.getString(jsonObject, "plugin_id");
            boolean push_status = DDJSONUtil.getBoolean(jsonObject, NetKeyConstants.NET_KEY_PUSH_STATUS);
            if (!TextUtils.isEmpty(plugin_id)
                    && plugin_id.equals(builder.getId())
                    && null != mData) {
                mData.put(NetKeyConstants.NET_KEY_PUSH_STATUS, push_status);
                builder.setData(mData);
                updateAlwaysPushSettingVisible();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showMoreActionDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.delete)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (0 == index) {
                            toDeleteItem();
                        }
                    }
                }).show();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PirSensitivityUpdateEvent event) {
        DDLog.i(TAG, "PirSensitivityUpdateEvent. pluginId: " + event.getPluginId() + "  sensitivity: " + event.getSensitivity());
        if (TextUtils.isEmpty(event.getPluginId())
                || !mPluginDevice.getId().equals(event.getPluginId())) {
            return;
        }
        mSensitivity = event.getSensitivity();
        updateSensitivity(mSensitivity);
        builder.setSensitivity(mSensitivity);
        if (callBack != null)
            callBack.onPirSensitivityChange(builder.getId(), mSensitivity);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(NeedLoadInfoPluginsEvent event) {
        // APP前后台切换, 导致重新获取PanelConstant.DeviceType.HOME_PLUGIN类型的device；从后台回到前台，此
        // 页面的IDeviceCallBack监听仍然是在旧的device上，因此在该页面上的操作无法得知操作结果；
        if (null != mPluginDevice) {
            Device device = PluginManager.getInstance().getPluginByID(mPluginDevice.getId());
            DDLog.e(TAG, "NeedLoadInfoPluginsEvent. is ask plugin ？ " + (device != null));
            if (null != device) {
                mPluginDevice.unregisterDeviceCallBack(this);
                mPluginDevice = device;
                mPluginDevice.registerDeviceCallBack(this);
                mPluginDevice.submit(PanelParamsHelper.getPluginDetail());
            }
        }
    }

}

