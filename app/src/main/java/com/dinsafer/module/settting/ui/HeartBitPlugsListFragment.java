package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.BounceInterpolator;
import android.widget.AdapterView;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.PlugsData;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.adapter.MyFamilyListAdapter;
import com.dinsafer.module.settting.adapter.SecurityPlugsItem;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RandomStringUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


/**
 * "
 * Created by Rinfon on 16/7/12.
 */
public class HeartBitPlugsListFragment extends BaseFragment implements ModifyASKPlugsFragment.ICallBack {


    LocalTextView commonBarTitle;
    SwipeMenuListView securityListview;
    LocalTextView listviewEmpty;

    private ArrayList<PlugsData> mData;

    private SecurityPlugsItem plugsItem;

    private Call<ResponseBody> mCall;

    private String messageId;

    private int messageIndex;


    public static HeartBitPlugsListFragment newInstance() {
        HeartBitPlugsListFragment simplePlugsListFragment = new HeartBitPlugsListFragment();
        return simplePlugsListFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.security_plugs_list_layout, container, false);
        showBlueTimeOutLoadinFramgment();
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        securityListview = rootView.findViewById(R.id.security_listview);
        listviewEmpty = rootView.findViewById(R.id.listview_empty);
        securityListview.setOnItemClickListener((parent, view, position, id) -> toChangePlugName(position));
    }

    @Override
    public void initData() {
        super.initData();
        listviewEmpty.setLocalText(getResources().getString(R.string.listview_empty));
        commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_heart_bit));

        SwipeMenuCreator creator = new SwipeMenuCreator() {

            @Override
            public void create(SwipeMenu menu) {
                if (menu.getViewType() == MyFamilyListAdapter.TYPE_ITEM) {
                    // create "delete" item
                    SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                    // set item background
                    deleteItem.setBackground(R.color.colorDelete);
                    // set item width
                    deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                    deleteItem.setTitleSize(13);

                    deleteItem.setTitleColor(Color.WHITE);
                    // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                    deleteItem.setTitle(Local.s(getResources().getString(R.string.smart_plugs_list_delete)));
                    // add to menu
                    menu.addMenuItem(deleteItem);
                }
            }
        };

//        set creator
        securityListview.setMenuCreator(creator);
        securityListview.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
        securityListview.setCloseInterpolator(new BounceInterpolator());
        securityListview.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                switch (i1) {
                    case 0:
                        // delete
                        toDeleteItem(i);

                        break;
                }
                // false : close the menu; true : not close the menu
                return false;
            }
        });
        securityListview.setEmptyView(listviewEmpty);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        createPlugsList();
    }

    private void toDeleteItem(final int i) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        final String uid = CommonDataUtil.getInstance().getUserUid();
                        if(TextUtils.isEmpty(uid)){
                            showErrorToast();
                            return;
                        }
                        showLoadingFragment(LoadingFragment.BLACK, "");
                        messageId = RandomStringUtils.getMessageId();
                        messageIndex = i;
                        DinsafeAPI.getApi().getDeletePlugsCmdCall(uid, messageId
                                , DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getToken()
                                , mData.get(i).getPlugId()).enqueue(new Callback<StringResponseEntry>() {
                            @Override
                            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                                closeLoadingFragment();
                                showTimeOutLoadinFramgmentWithErrorAlert();
                            }

                            @Override
                            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                                closeLoadingFragment();

                            }
                        });
                    }
                })
                .preBuilder()
                .show();
    }

    /**
     * 初始化plugg数据列表
     */
    private void createPlugsList() {
        mData = new ArrayList<PlugsData>();
        plugsItem = new SecurityPlugsItem(getActivity(), mData);
        securityListview.setAdapter(plugsItem);

        mCall = DinsafeAPI.getApi().getHeartBitListCall(CommonDataUtil.getInstance().getCurrentDeviceId());
        mCall.enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                String json = null;

                try {
                    json = response.body().string();
                    JSONObject jsonObject = new JSONObject(json);
                    if (DDJSONUtil.getInt(jsonObject, "Status") == 1) {
                        String result = DDSecretUtil.getReverSC(DDJSONUtil.getString(jsonObject, "Result"));
                        JSONObject resultJson = new JSONObject(result);
                        mData = getData(DDJSONUtil.getJSONObject(resultJson, "datas"));
                        plugsItem.setData(mData);
                        plugsItem.notifyDataSetChanged();
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

                closeLoadingFragment();
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                closeLoadingFragment();
            }
        });
    }

    private ArrayList<PlugsData> getData(JSONObject json) {
        if (json == null)
            return null;
        ArrayList<PlugsData> list = new ArrayList<PlugsData>();

        Iterator it = json.keys();
        String key;
        while (it.hasNext()) {
            key = (String) it.next();
            JSONArray datas = DDJSONUtil.getJSONarray(json, key);
            ArrayList<PlugsData> plugsDatas = new ArrayList<PlugsData>();
            for (int i = 0; i < datas.length(); i++) {
                PlugsData plugsData = new PlugsData();
                try {
                    plugsData.setName(DDJSONUtil.getString((JSONObject) datas.get(i), "name"))
                            .setDescription(DDJSONUtil.getString((JSONObject) datas.get(i), "name"))
                            .setPlugId(DDJSONUtil.getString((JSONObject) datas.get(i), "id"))
                            .setAskData((JSONObject) datas.get(i));
                    plugsDatas.add(plugsData);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            String name = CommonDataUtil.getInstance().getASKNameByBSType(key);
            PlugsData plugsData = new PlugsData();
            plugsData.setName("")
                    .setDescription(name);
            list.add(plugsData);
            list.addAll(plugsDatas);
        }

        return list;
    }

    public void toClose() {
        removeSelf();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (mCall != null) {
            mCall.cancel();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOfflineEvent event) {
        DDLog.d(TAG, "DeviceOfflineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOnlineEvent event) {
        DDLog.d(TAG, "DeviceOnlineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceResultEvent ev) {
        if (ev.getMessageId().equals(messageId)) {
            if (LocalKey.DELETE_PLUGIN.equals(ev.getCmdType()) && ev.getMessageId().equals(messageId)) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (ev.getStatus() == 1) {
                    plugsItem.remove(messageIndex);
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PlugsNameChangeEvent ev) {
        plugsItem.changeName(messageIndex, ev.getName());
    }

    public void toChangePlugName(int index) {
        if (!CommonDataUtil.getInstance().isPanelOnline()) {
            DDLog.e(TAG, "当前主机离线，Item不能被点击哦");
            return;
        }
        if (!TextUtils.isEmpty(mData.get(index).getName())) {
            messageIndex = index;
            Builder builder = new Builder();
            builder.setId(DDJSONUtil.getString(mData.get(index).getAskData(), "id"))
                    .setAdd(false)
                    .setOffical(true)
                    .setMessageIndex(messageIndex)
                    .setShowDelete(true)
                    .setName(mData.get(index).getDescription())
                    .setShowwave(false)
                    .setData(mData.get(index).getAskData());
            ModifyASKPlugsFragment modifyASKPlugsFragment = ModifyASKPlugsFragment.newInstance(builder);
            modifyASKPlugsFragment.setCallBack(this);
            getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);
        }
    }

    @Override
    public void onDeletePlug(String id) {

    }

    @Override
    public void onChangeName(int index, String name) {
        plugsItem.changeName(index, name);
    }
}
