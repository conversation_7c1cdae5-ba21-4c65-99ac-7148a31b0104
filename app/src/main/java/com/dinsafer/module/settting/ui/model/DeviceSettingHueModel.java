package com.dinsafer.module.settting.ui.model;

import android.view.View;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.HueEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.hue.BridgeModule;
import com.dinsafer.module.hue.HueLightListFragment;
import com.dinsafer.module.hue.add.HueScanFragment;
import com.dinsafer.util.CommonDataUtil;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2019/4/6
 */
public class DeviceSettingHueModel extends BaseDeviceSettingPlugModel {

    public DeviceSettingHueModel(BaseFragment baseFragment, boolean haveLoading, boolean isLoading) {
        super(baseFragment, baseFragment.getResources().getString(R.string.philips_hue),
                R.drawable.icon_device_setting_hue, -1, haveLoading, isLoading);
    }

    @Override
    public void onDo(View view) {
        super.onDo(view);

        BridgeModule.getInstance().init(baseFragment.getContext());

        baseFragment.showLoadingFragment(1);
        DinsafeAPI.getApi().getPhilipsHue(CommonDataUtil.getInstance().getCurrentDeviceId())
                .enqueue(new Callback<HueEntry>() {
                    @Override
                    public void onResponse(Call<HueEntry> call, Response<HueEntry> response) {
                        baseFragment.closeLoadingFragment();
                        if (response.body().getResult() != null && response.body().getResult().size() > 0) {
                            HueEntry.ResultBean result = response.body().getResult().get(0);
                            BridgeModule.getInstance().setBridgeRemoteInfo(result);
                            if (BridgeModule.getInstance().checkBridgeWhetherKnow(result.getPid())) {
                                baseFragment.getDelegateActivity().addCommonFragment(
                                        HueLightListFragment.newInstance(
                                                BridgeModule.getInstance().getKnowBridge(result.getPid()).getIpAddress()));
                            } else {
                                jumpToSearchBridgeFragment(result.getPid());
                            }
                        } else {
                            jumpToSearchBridgeFragment("");
                        }
                    }

                    @Override
                    public void onFailure(Call<HueEntry> call, Throwable t) {
                        baseFragment.closeLoadingFragment();
                        jumpToSearchBridgeFragment("");
                    }
                });

    }

    private void jumpToSearchBridgeFragment(String s) {
        baseFragment.getDelegateActivity().addCommonFragment(HueScanFragment.newInstance(s));
    }
}
