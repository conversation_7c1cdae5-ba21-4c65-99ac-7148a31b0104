package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.jungly.gridpasswordview.GridPasswordView;


/**
 * Created by rinfon on 15/6/26.
 */
public class ChangePasswordDialog extends Dialog {
    private static final int PWD_LEN = 4;

    int layoutRes;//布局文件

    Context mContext;


    LocalCustomButton mOk, mCancel;

    GridPasswordView mInput;

    LocalTextView mContent;

    public ChangePasswordDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.change_password_dialog;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mOk = (LocalCustomButton) view.findViewById(R.id.alert_dialog_ok);
        mCancel = (LocalCustomButton) view.findViewById(R.id.alert_dialog_cancel);
        mContent = (LocalTextView) view.findViewById(R.id.password_dialog_title);
        mInput = (GridPasswordView) view.findViewById(R.id.password_dialog_input);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (builder.okClick != null) {
                    builder.okClick.onCancel(ChangePasswordDialog.this);
                }
                if (builder.cancelAutoDismiss) {
                    dismiss();
                }
            }
        });

        mOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onOkClick(ChangePasswordDialog.this, mInput.getPassWord());
                }
            }
        });

        mContent.setLocalText(builder.mContent);

        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOk.setVisibility(View.VISIBLE);
            mInput.setOnPasswordChangedListener(new GridPasswordView.OnPasswordChangedListener() {
                @Override
                public void onTextChanged(String psw) {
                    setOkEnable(!TextUtils.isEmpty(psw) && psw.length() == PWD_LEN);
                }

                @Override
                public void onInputFinish(String psw) {

                }
            });
            setOkEnable(!TextUtils.isEmpty(mInput.getPassWord()) && mInput.getPassWord().length() == PWD_LEN);
        } else {
            mOk.setVisibility(View.GONE);
        }
        if (builder.isShowCancel) {
            mCancel.setLocalText(builder.mCancel);
            mCancel.setVisibility(View.VISIBLE);
        } else {
            mCancel.setVisibility(View.GONE);
        }
    }

    private void setOkEnable(final boolean enble) {
        if (enble) {
            mOk.setEnabled(true);
            mOk.setAlpha(1.0f);
        } else {
            mOk.setEnabled(false);
            mOk.setAlpha(0.5f);
        }
    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
//        super.cancel();
    }

    public interface AlertOkClickCallback {

        void onOkClick(ChangePasswordDialog dialog, String password);

        void onCancel(ChangePasswordDialog dialog);
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mOK;

        private boolean isShowOK = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isAutoDismiss = true;

        private AlertOkClickCallback okClick;

        private boolean cancelAutoDismiss = true;

        public Builder(Context context) {
            mContext = context;
        }

        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }


        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            isShowCancel = true;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public Builder setCancelAutoDismiss(boolean dismiss) {
            this.cancelAutoDismiss = dismiss;
            return this;
        }

        public ChangePasswordDialog preBuilder() {
            ChangePasswordDialog alertDialog = new ChangePasswordDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
