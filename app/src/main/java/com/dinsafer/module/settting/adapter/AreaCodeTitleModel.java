//package com.dinsafer.module.settting.adapter;
//
//import android.view.View;
//
//import com.chad.library.adapter.base.BaseViewHolder;
//import com.dinsafer.carego.module_login.R;
//import com.dinsafer.carego.module_login.databinding.LoginItemAreCodeTitleBinding;
//import com.dinsafer.common.widget.rv.BaseBindModel;
//
///**
// * 区号设置标题Item数据封装类
// *
// * <AUTHOR>
// * @date 2020-03-29 14:34
// */
//public class AreaCodeTitleModel implements BaseBindModel<LoginItemAreCodeTitleBinding> {
//    private String title;
//
//    public AreaCodeTitleModel(String title) {
//        this.title = title;
//    }
//
//    public String getTitle() {
//        return title;
//    }
//
//    public void setTitle(String title) {
//        this.title = title;
//    }
//
//
//    @Override
//    public int getLayoutID() {
//        return R.layout.login_item_are_code_title;
//    }
//
//    @Override
//    public boolean onDo(View v) {
//        return false;
//    }
//
//    @Override
//    public void convert(BaseViewHolder holder, LoginItemAreCodeTitleBinding binding) {
//        binding.tvItemTitle.setLocalText(title);
//    }
//}
