package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.IPCData;

import java.io.File;
import java.util.ArrayList;


/**
 * Created by chenyufeng on 2017/5/19.
 */
public class IPCThumbnailAdapter extends BaseAdapter {


    private Activity mActivity;

    private ArrayList<IPCData> mData;

    public Activity getActivity() {
        return mActivity;
    }

    public void setActivity(Activity mActivity) {
        this.mActivity = mActivity;
    }

    public ArrayList<IPCData> getData() {
        return mData;
    }

    public void setData(ArrayList<IPCData> mData) {
        this.mData = mData;
    }

    public IPCThumbnailAdapter(Activity mActivity, ArrayList<IPCData> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData == null)
            return 0;
        return mData.size();
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        ViewHolder itemHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.ipc_thumbnail_item, null);
            itemHolder = new ViewHolder(convertView);
            convertView.setTag(itemHolder);
        } else {
            try {
                itemHolder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!TextUtils.isEmpty(mData.get(position).getSnapshot())) {
            File file = new File(mData.get(position).getSnapshot());
            if (file.exists()) {
                itemHolder.ipcThumbnailImageview.setImageURI(Uri.fromFile(file));
            } else {
                itemHolder.ipcThumbnailImageview.setImageResource(R.drawable.icon_define_setting_camera);
            }
        }

        itemHolder.ipcThumbnailName.setText(mData.get(position).getName());


        return convertView;
    }

    static class ViewHolder {
        ImageView ipcThumbnailImageview;
        TextView ipcThumbnailName;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            ipcThumbnailImageview = view.findViewById(R.id.ipc_thumbnail_imageview);
            ipcThumbnailName = view.findViewById(R.id.ipc_thumbnail_namw);
        }
    }
}
