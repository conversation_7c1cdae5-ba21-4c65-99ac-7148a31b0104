package com.dinsafer.module.settting.ui;

import android.app.Activity;
import android.os.Bundle;
import android.text.InputType;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.dialog.BaseDialog;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.TiggleDialogBinding;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;

/**
 * Created by rinfon on 15/6/26.
 */
public class TiggleDialog extends BaseDialog<TiggleDialogBinding> {

    private Builder builder;

    public TiggleDialog(Activity context, final Builder builder) {
        super(context, R.style.SosDialogStyle);
        mContext = context;
        this.builder = builder;
    }


    @Override
    protected int layoutRes() {
        return R.layout.tiggle_dialog;
    }

    @Override
    protected void initView() {
        Window window = this.getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        Display display = window.getWindowManager().getDefaultDisplay();
        windowParams.height = display.getHeight() - DisplayUtil.dip2px(getContext(), 140);
        window.setAttributes(windowParams);
        mBinding.titleRightIcon.setVisibility(View.GONE);
        mBinding.layoutWifi.setVisibility(View.GONE);
        mBinding.tiggleDialogIcon2.setVisibility(View.GONE);

        mBinding.tiggleDialogTitle.setLocalText(builder.title);
//        String uri = "assets://apng/animation_open_device_ap.png";
//        ApngImageLoader.getInstance()
//                .displayApng(builder.iconUrl, mIcon,
//                        new ApngImageLoader.ApngConfig(Integer.MAX_VALUE - 1, true));
        mBinding.tiggleDialogIcon.setAnimation(builder.iconUrl);

        mBinding.tiggleHint.setLocalText(builder.tiggleHint);
        if (!TextUtils.isEmpty(builder.typeString)) {
            mBinding.tiggleDialogType.setLocalText(builder.typeString);
            mBinding.tiggleDialogType.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogType.setVisibility(View.INVISIBLE);
        }
        if (!TextUtils.isEmpty(builder.id)) {
            mBinding.tiggleDialogId.setLocalText(builder.id);
            mBinding.tiggleDialogId.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogId.setVisibility(View.INVISIBLE);
        }

        mBinding.tiggleDialogCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!builder.isTuya) {
                    //涂鸦多种情况，不直接dismiss
                    dismiss();
                }
                if (builder.okClick != null) {
                    builder.okClick.onCancel(TiggleDialog.this);
                }
            }
        });

        mBinding.tiggleDialogNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss) {
                    dismiss();
                }
                if (builder.okClick != null) {
                    builder.okClick.onOkClick(TiggleDialog.this);
                }
            }
        });

        if (builder.isShowOK) {
            mBinding.tiggleDialogNext.setLocalText(builder.mOK);
            mBinding.tiggleDialogNext.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogNext.setVisibility(View.GONE);
        }
        if (builder.isShowCancel) {
            mBinding.tiggleDialogCancel.setLocalText(builder.mCancel);
            mBinding.tiggleDialogCancel.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogCancel.setVisibility(View.GONE);
        }

        switch (builder.titleIconType) {
            case 0:
                mBinding.titleRightIcon.setVisibility(View.GONE);
                break;
            case 1:
                mBinding.titleRightIcon.setVisibility(View.VISIBLE);
                mBinding.titleRightIcon.setImageResource(R.drawable.icon_trigger_tuya_help);
                break;
            case 2:
//                mBinding.titleRightIcon.setVisibility(View.VISIBLE);
//                mBinding.titleRightIcon.setImageResource(R.drawable.btn_define_setting_select);
                break;
            default:
                mBinding.titleRightIcon.setVisibility(View.GONE);
                break;
        }

        setOkTextAlpha(builder.okTextAlpha);

        setApngSize(builder.apngW, builder.apngH, builder.apngMarginTop, builder.apngMarginBottom);

        mBinding.titleRightIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (builder.titleIconClickCallback != null) {
                    builder.titleIconClickCallback.onClick(TiggleDialog.this);
                }
            }
        });

        mBinding.tuyaEditWifiSsid.setOnClickListener(v -> toSsid());
        mBinding.tuyaEditWifiSsidNor.setOnClickListener(v -> toSsid());
        mBinding.tuyaEditWifiPasswordIcon.setOnClickListener(v -> toClickEye());
        mBinding.tuyaEditRemember.setOnClickListener(v -> toRemember());

        mBinding.tuyaEditRemember.setChecked(true);
        mBinding.tuyaEditRemember.setText(Local.s(getContext().getResources().getString(R.string.remember_password)));
        mBinding.tuyaEditWifiHint.setLocalText(getContext().getResources().getString(R.string.tuya_edit_wifi_hint));
        mBinding.tuyaEditWifiPassword.setHint(Local.s(getContext().getResources().getString(R.string.tuya_edit_wifi_password_hint)));
        mBinding.tiggleDialogIcon.playAnimation();
    }


    @Override
    public void show() {
        super.show();
    }

    @Override
    public void dismiss() {
        Log.d("TiggleDialog", "dismiss, " + DDLog.getStackTrace());
        /**
         * 多次dimiss会导致unbinder已经unbind的情况下，再次unbind会闪退
         */
        super.dismiss();
        if (mBinding.tiggleDialogIcon.isAnimating()) {
            mBinding.tiggleDialogIcon.cancelAnimation();
        }
//        ApngDrawable apngDrawable = ApngDrawable.getFromView(mIcon);
//        if (apngDrawable == null) return;
//
//        if (apngDrawable.isRunning()) {
//            apngDrawable.stop(); // Stop animation
//        }
    }

    public static Builder createBuilder(Activity context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    public void setOkText(String okText) {
        if (TextUtils.isEmpty(okText)) {
            mBinding.tiggleDialogNext.setVisibility(View.GONE);
        } else {
            mBinding.tiggleDialogNext.setVisibility(View.VISIBLE);
            mBinding.tiggleDialogNext.setLocalText(okText);
        }
    }

    public void setOkTextAlpha(float alpha) {
        if (alpha < 1.0f) {
            mBinding.tiggleDialogNext.setEnabled(false);
        } else {
            mBinding.tiggleDialogNext.setEnabled(true);
        }
        mBinding.tiggleDialogNext.setAlpha(alpha);
    }

    public void setTitleString(String okText) {
        mBinding.tiggleDialogTitle.setLocalText(okText);
    }

    public void setApng(String assetPath, int numplays) {
        mBinding.tiggleDialogIcon.setRepeatCount(numplays);
        mBinding.tiggleDialogIcon.setAnimation(assetPath);
        mBinding.tiggleDialogIcon.playAnimation();
//        ApngDrawable apngDrawable = ApngDrawable.getFromView(mIcon);
//        if (apngDrawable == null) return;
//
//        if (apngDrawable.isRunning()) {
//            apngDrawable.stop(); // Stop animation
//        }
//
//        ApngImageLoader.getInstance()
//                .displayApng(apng, mIcon,
//                        new ApngImageLoader.ApngConfig(numplays, true));
    }

    public void setApngSize(int w, int h, int marginTop, int marginBottom) {
        mBinding.tiggleDialogIcon.getLayoutParams().width = w;
        mBinding.tiggleDialogIcon.getLayoutParams().height = h;
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mBinding.tiggleDialogIcon.getLayoutParams();
        lp.setMargins(0, marginTop, 0, marginBottom);
        mBinding.tiggleDialogIcon.setLayoutParams(lp);
    }

    public void setContent(String content) {
        mBinding.tiggleHint.setLocalText(content);
        if (getContext().getResources().getString(R.string.tiggle_success).equals(content)) {
            mBinding.tiggleHint.setGravity(Gravity.CENTER_HORIZONTAL);
        } else {
            mBinding.tiggleHint.setGravity(Gravity.LEFT);
        }
    }

    public void setTypeName(String name) {
        String names = Local.s("Plugin Type") + ":" + Local.s(name);
        mBinding.tiggleDialogType.setText(names);
        mBinding.tiggleDialogType.setVisibility(View.VISIBLE);
    }

    public void setTypeID(String id) {
        String ids = Local.s("Plugin ID") + ":" + id;
        if (TextUtils.isEmpty(id)) {
            mBinding.tiggleDialogId.setVisibility(View.GONE);
        } else {
            mBinding.tiggleDialogId.setText(ids);
            mBinding.tiggleDialogId.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
//        super.cancel();
    }

    public interface AlertOkClickCallback {

        void onOkClick(TiggleDialog dialog);

        void onCancel(TiggleDialog dialog);
    }

    public static class Builder {

        private Activity mContext;

        private String mOK;

        private boolean isShowOK = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isAutoDismiss = true;

        private AlertOkClickCallback okClick;

        private float okTextAlpha;

        private String iconUrl;

        private String title;

        private String tiggleHint;

        private String typeString;

        private String id;

        private boolean isTuya = false;

        private int apngW;
        private int apngH;
        private int apngMarginTop;
        private int apngMarginBottom;

        /**
         * titleIconType 右上角的图标：0为没有，1为help，2为ok
         */
        private int titleIconType = 0;

        private TitleIconClickCallback titleIconClickCallback;

        public Builder(Activity context) {
            mContext = context;
        }


        public Builder setApngSize(int w, int h, int marginTop, int marginBottom) {
            this.apngW = w;
            this.apngH = h;
            this.apngMarginTop = marginTop;
            this.apngMarginBottom = marginBottom;
            return this;
        }


        public Builder setOkTextAlpha(float alpha) {
            this.okTextAlpha = alpha;
            return this;
        }

        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setIconUrl(String iconUrl) {
            this.iconUrl = iconUrl;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setTiggleHint(String tiggleHint) {
            this.tiggleHint = tiggleHint;
            return this;
        }

        public Builder setTypeString(String typeString) {
            this.typeString = typeString;
            return this;
        }

        public Builder setId(String id) {
            this.id = id;
            return this;
        }


        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            isShowCancel = true;
            return this;
        }


        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public TiggleDialog preBuilder() {
            TiggleDialog alertDialog = new TiggleDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

        public Builder setTitleIconType(int titleIconType) {
            this.titleIconType = titleIconType;
            return this;
        }

        public Builder setTitleIconListener(TitleIconClickCallback titleIconClickCallback) {
            this.titleIconClickCallback = titleIconClickCallback;
            return this;
        }

        public Builder isTuya(boolean isTuya) {
            this.isTuya = isTuya;
            return this;
        }
    }

    public interface TitleIconClickCallback {
        void onClick(TiggleDialog dialog);
    }

    public void setMiddleIcon(int res) {
//        ApngDrawable apngDrawable = ApngDrawable.getFromView(mIcon);
//        if (apngDrawable == null) return;
//
//        if (apngDrawable.isRunning()) {
//            apngDrawable.stop(); // Stop animation
//        }
        mBinding.tiggleDialogIcon.setAnimation(res);
    }

    public void setCancelVisibility(boolean isVisible) {
        if (isVisible) {
            mBinding.tiggleDialogCancel.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogCancel.setVisibility(View.GONE);
        }
    }

    public void setOKVisibility(boolean isVisible) {
        if (isVisible) {
            mBinding.tiggleDialogNext.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogNext.setVisibility(View.GONE);
        }
    }

    public void setTitleIconVisibility(boolean isVisible) {
        if (isVisible) {
            mBinding.titleRightIcon.setVisibility(View.VISIBLE);
        } else {
            mBinding.titleRightIcon.setVisibility(View.GONE);
        }
    }

    public void setTitleIconRes(int res) {
        mBinding.titleRightIcon.setImageResource(res);
    }


    public void setLayoutWifiVisibility(boolean isVisible) {
        if (isVisible) {
            mBinding.layoutWifi.setVisibility(View.VISIBLE);
        } else {
            mBinding.layoutWifi.setVisibility(View.GONE);
        }
    }

    public void setCanCelText(String msg) {
        mBinding.tiggleDialogCancel.setLocalText(msg);
    }

    public void setStaticIcon(int res) {
        mBinding.tiggleDialogIcon2.setImageResource(res);
    }

    public void setStaticIconVisibility(boolean isVisible) {
        if (isVisible) {
            mBinding.tiggleDialogIcon2.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogIcon2.setVisibility(View.GONE);
        }
    }

    public void setMIconVisibility(boolean isVisible) {
        if (isVisible) {
            mBinding.tiggleDialogIcon.setVisibility(View.VISIBLE);
        } else {
            mBinding.tiggleDialogIcon.setVisibility(View.GONE);
        }
    }

    public void setTuyaEditWifiSsid(String str) {
        mBinding.tuyaEditWifiSsid.setText(str);
    }

    public String getTuyaEditWifiSsid() {
        return mBinding.tuyaEditWifiSsid.getText().toString();
    }

    public void setTuyaEditWifiPassword(String str) {
        mBinding.tuyaEditWifiPassword.setText(str);
    }

    public String getTuyaEditWifiPassword() {
        return mBinding.tuyaEditWifiPassword.getText().toString();
    }

    public Boolean getTuyaWifiIsRemember() {
        return mBinding.tuyaEditRemember.isChecked();
    }

    public void setConnectionWifi(String ssid) {
        //ssid单独颜色。
        String startStr = Local.s(getContext().getResources().getString(R.string.add_tuya_has_wifi));
        SpannableString spannableString = new SpannableString(startStr + ssid);
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(DinSaferApplication.getAppContext().getResources().getColor(R.color.colorLogout));
        spannableString.setSpan(colorSpan, startStr.length(), spannableString.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
        mBinding.tiggleHint.setText(spannableString);
    }

    public void toSsid() {
        hasNoPwdSSIDClickCallback.onClick();
    }

    private static final int SHOW_PASSWORD_CODE = 129;

    public void toClickEye() {
        //密码是否可见
        if (mBinding.tuyaEditWifiPassword.getInputType() == SHOW_PASSWORD_CODE) {
            mBinding.tuyaEditWifiPasswordIcon.setImageResource(R.drawable.icon_trigger_tuya_password_show);
            mBinding.tuyaEditWifiPassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            mBinding.tuyaEditWifiPasswordIcon.setImageResource(R.drawable.icon_trigger_tuya_password_notshow);
            mBinding.tuyaEditWifiPassword.setInputType(SHOW_PASSWORD_CODE);
        }
        mBinding.tuyaEditWifiPassword.setSelection(mBinding.tuyaEditWifiPassword.getText().length());
    }

    public void toRemember() {
    }

    public interface SSIDClickCallback {
        void onClick();
    }

    public interface HasNoPwdSSIDClickCallback {
        void onClick();
    }

    private HasNoPwdSSIDClickCallback hasNoPwdSSIDClickCallback;

    public void setHasNoPwdSSIDClickListener(final HasNoPwdSSIDClickCallback hasNoPwdSSIDClickCallback) {
        this.hasNoPwdSSIDClickCallback = hasNoPwdSSIDClickCallback;
    }

    public void resetPwdInvisible() {
        mBinding.tuyaEditWifiPasswordIcon.setImageResource(R.drawable.icon_trigger_tuya_password_notshow);
        mBinding.tuyaEditWifiPassword.setInputType(SHOW_PASSWORD_CODE);
    }

    public interface OnWindowFocusChangedListener {
        void onWindowFocusChanged();
    }

    private OnWindowFocusChangedListener onWindowFocusChangedListener;

    public void setOnWindowFocusChangedListener(OnWindowFocusChangedListener onWindowFocusChangedListener) {
        this.onWindowFocusChangedListener = onWindowFocusChangedListener;
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (onWindowFocusChangedListener != null) {
            onWindowFocusChangedListener.onWindowFocusChanged();
        }
    }
}
