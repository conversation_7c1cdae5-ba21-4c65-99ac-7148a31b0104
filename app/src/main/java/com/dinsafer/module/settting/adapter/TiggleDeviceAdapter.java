package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.TiggleListItemBinding;
import com.dinsafer.util.Local;

import java.util.ArrayList;

import androidx.databinding.DataBindingUtil;

/**
 * Created by Rinfon on 16/6/29.
 */
public class TiggleDeviceAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<String> mData;

    public TiggleDeviceAdapter(Activity mActivity, ArrayList<String> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    public ArrayList<String> getData() {
        return mData;
    }

    public void setData(ArrayList mData) {
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int postion, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.tiggle_list_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        holder.binding.tiggleListTitle.setText(Local.s(mData.get(postion)));

        if (postion == mData.size() - 1) {
            holder.binding.tiggleListLine.setVisibility(View.GONE);
        } else {
            holder.binding.tiggleListLine.setVisibility(View.VISIBLE);
        }


        return convertView;
    }

    static class ViewHolder {
        TiggleListItemBinding binding;

        ViewHolder(View view) {
            binding = DataBindingUtil.bind(view);
        }
    }
}
