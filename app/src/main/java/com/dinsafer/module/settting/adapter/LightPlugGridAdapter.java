package com.dinsafer.module.settting.adapter;

import static com.dinsafer.model.TuyaItem.LIGHT_LOADING;
import static com.dinsafer.model.TuyaItem.LIGHT_OFF;
import static com.dinsafer.model.TuyaItem.LIGHT_ON;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_LOADING;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_OFF;
import static com.dinsafer.model.TuyaItem.SMARTPLUGIN_ON;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_LOADING;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_OFF;
import static com.dinsafer.model.TuyaItem.TUYA_SMARTPLUGIN_ON;

import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.List;

/**
 * 自家插座、涂鸦灯泡、涂鸦插座的数据适配器
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/1/12 11:06 AM
 */
public class LightPlugGridAdapter extends BaseQuickAdapter<TuyaItemPlus, LightPlugGridAdapter.Holder> {
    private final static String TAG = LightPlugGridAdapter.class.getSimpleName();

    public LightPlugGridAdapter(@Nullable List<TuyaItemPlus> data) {
        super(R.layout.main_section_panel_item_shortcut, data);
    }

    @Override
    protected void convert(LightPlugGridAdapter.Holder itemHolder, TuyaItemPlus itemData) {
        itemHolder.setEditMode(false);
        if (itemData.isEmptyLoadingView()) {
            itemHolder.setStatusEmpty();
            return;
        }

        int pluginType = itemData.getType();
        itemHolder.setPluginName(itemData.getName());
        itemHolder.setQuickStartClickListener((View v) -> {
                    if (!MainPanelHelper.getInstance().isPanelEditMode()
                            && !MainPanelHelper.getInstance().isFunctionEnable()
                            && isTuyaLight(pluginType)
                            && isTuyaPlug(pluginType)) {
                        DDLog.e(TAG, "当前Item不能点击2");
                        return;
                    }

                    //  点击Item QuickStart icon
                    if (isOfficialSmartPlug(pluginType)) {
                        // 自研插座
                        if (requestChangeSmartPlugStatus(itemData, itemHolder)) {
                            itemHolder.setQuickStartEnable(false);
                            itemHolder.setQuickStartLoading(true);
                            itemData.setType(SMARTPLUGIN_LOADING);
                        }
                    } else {
                        DDLog.e(TAG, "Unhandle plugin type, pluginType: " + pluginType);
                    }
                }
        );

        if (itemData.isNeedLoading()
                && itemData.isLoading()) {
            // 还在loading
            itemHolder.setSelected(itemData.isShow());
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusLoading();
            return;
        }

        if (isOfficialSmartPlug(pluginType)) {
            boolean panelDeviceState = !CommonDataUtil.getInstance().isPanelOnline();
            changeItemStateByPanelDevice(itemData, itemHolder, panelDeviceState);
        } else {
            changeItemState(itemData, itemHolder);
        }
    }

    private void changeItemStateByPanelDevice(TuyaItemPlus itemData, LightPlugGridAdapter.Holder itemHolder, boolean panelDeviceState) {
        if (panelDeviceState) {
            itemHolder.setStatusFinish(mContext, true, false);
            itemHolder.setStateTextByPanelDevice(mContext, panelDeviceState);
            // 置灰，不可点击进入
            itemHolder.changeItemEnable(false);
            return;
        }
        itemHolder.changeItemEnable(true);
        changeItemState(itemData, itemHolder);
    }

    private void changeItemState(TuyaItemPlus itemData, LightPlugGridAdapter.Holder itemHolder) {
        if (itemData.isNeedOnlineState()
                && !itemData.isOnline()) {
            // 离线
            itemHolder.setSelected(itemData.isShow());
            itemHolder.setEditMode(MainPanelHelper.getInstance().isPanelEditMode());
            itemHolder.setStatusFinish(mContext, itemData.isNeedOnlineState(), itemData.isOnline());
            return;
        }

        // 在线
        updateStateFinished(itemData, itemHolder);
    }

    /**
     * 修改为完成的状态
     *
     * @param itemData
     * @param itemHolder
     */
    private void updateStateFinished(TuyaItemPlus itemData
            , Holder itemHolder) {
        itemHolder.setStatusFinish(mContext, itemData.isNeedOnlineState(), itemData.isOnline());

        if (MainPanelHelper.getInstance().isPanelEditMode()) {
            // 编辑模式
            itemHolder.setEditMode(true);
            itemHolder.setSelected(itemData.isShow());
        } else {
            // 非编辑模式
            itemHolder.setEditMode(false);
            if (SMARTPLUGIN_LOADING == itemData.getType()
                    || TUYA_SMARTPLUGIN_LOADING == itemData.getType()
                    || LIGHT_LOADING == itemData.getType()) {
                itemHolder.setQuickStartEnable(false);
                itemHolder.setQuickStartLoading(true);
            } else {
                itemHolder.setQuickStartEnable(true);
                itemHolder.setQuickStartLoading(false);
                itemHolder.setQuickStartIconRes(
                        CommonDataUtil.getInstance().getMainIconByType(itemData.getType()));
            }
        }
    }

    /**
     * 请求修改自家插座的开关状态
     * <p>
     * status 0:关; 1:开
     */
    private boolean requestChangeSmartPlugStatus(TuyaItemPlus data,
                                                 Holder itemHolder) {
        DDLog.d(TAG, "requestChangeSmartPlugStatus");
        if (data.getType() != SMARTPLUGIN_ON
                && data.getType() != SMARTPLUGIN_OFF) {
            DDLog.e(TAG, "Error type.");
            return false;
        }

        // 修改开关状态
        final int lastStatus = data.getType();
        int status = SMARTPLUGIN_ON == lastStatus ? 0 : 1;

        Device device = DinHome.getInstance().getDevice(data.getId());
        if (null != device) {
            device.submit(PanelParamsHelper.changePlugOn(1 == status));

            final Handler handler = new Handler();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        DDLog.e(TAG, "update smart plug state timeout.");
                        if (data.getType() == SMARTPLUGIN_LOADING) {
                            data.setType(lastStatus);
                            updateStateFinished(data, itemHolder);
                        }
                    } catch (Exception ex) {
                        DDLog.e(TAG, "update light state error.");
                    }
                }
            }, LocalKey.TIMEOUT);
            return true;
        } else {
            DDLog.e(TAG, "No plugin device");
            return false;
        }
    }

    /**
     * 是否自家插座
     *
     * @param type
     * @return true: 是自家插座
     */
    private boolean isOfficialSmartPlug(int type) {
        return type == SMARTPLUGIN_ON || type == SMARTPLUGIN_OFF
                || type == SMARTPLUGIN_LOADING;
    }

    /**
     * 是否涂鸦灯泡
     *
     * @param type
     * @return true: 是涂鸦灯泡
     */
    private boolean isTuyaLight(int type) {
        return LIGHT_ON == type
                || LIGHT_OFF == type
                || LIGHT_LOADING == type;
    }


    /**
     * 是否涂鸦插座
     *
     * @param type
     * @return true: 是涂鸦插座
     */
    private boolean isTuyaPlug(int type) {
        return TUYA_SMARTPLUGIN_ON == type
                || TUYA_SMARTPLUGIN_OFF == type
                || TUYA_SMARTPLUGIN_LOADING == type;
    }


    static class Holder extends BaseViewHolder {
        private ConstraintLayout clShortcutItemRoot;
        private TextView tvShortcutName;
        private CheckBox cbShortcutSelect;
        private ImageView ivShortcutQuickStart;
        private TextView tvShortcutStatus;
        private ProgressBar pbShortcutLoading;
        private ProgressBar pbShortcutQuickStartLoading;

        public Holder(View view) {
            super(view);
            clShortcutItemRoot = itemView.findViewById(R.id.cl_shortcut_item_root);
            tvShortcutName = itemView.findViewById(R.id.tv_shortcut_name);
            cbShortcutSelect = itemView.findViewById(R.id.cb_shortcut_select);
            ivShortcutQuickStart = itemView.findViewById(R.id.iv_shortcut_quick_start);
            tvShortcutStatus = itemView.findViewById(R.id.tv_shortcut_status);
            pbShortcutLoading = itemView.findViewById(R.id.pb_shortcut_loading);
            pbShortcutQuickStartLoading = itemView.findViewById(R.id.pb_shortcut_quick_start_loading);
            cbShortcutSelect.setEnabled(false);
        }

        /**
         * 修改loading的显示状态
         *
         * @param visible true: 显示; false:隐藏
         */
        public void setLoadingGone(boolean visible) {
            DDLog.d(TAG, "setLoadingVisible, visible: " + visible);
            pbShortcutLoading.setVisibility(visible
                    ? View.VISIBLE
                    : View.GONE);
        }

        /**
         * 修改状态文本的显示状态
         *
         * @param visible true: 显示; false:隐藏
         */
        public void setStatusTextGone(boolean visible) {
            DDLog.d(TAG, "setStatusTextGone, visible: " + visible);
            tvShortcutStatus.setVisibility(visible
                    ? View.VISIBLE
                    : View.GONE);
        }

        /**
         * 修改状态文本
         *
         * @param online true: 在线; false:离线
         */
        public void setStatusText(Context context, boolean online) {
            DDLog.d(TAG, "setStatusText, online: " + online);

            tvShortcutStatus.setBackgroundResource(
                    online ? R.drawable.shape_main_panel_shortcut_item_status_border_online
                            : R.drawable.shape_main_panel_shortcut_item_status_border);
            tvShortcutStatus.setTextColor(context.getResources().getColor(
                    online ? R.color.panel_plugin_item_status_online_text
                            : R.color.panel_plugin_item_status_offline_text));
            String statusText = context.getResources().getString(online
                    ? R.string.Online
                    : R.string.Offline);
            tvShortcutStatus.setText(Local.s(statusText));
        }

        /**
         * 修改编辑模式
         *
         * @param isEditMode true: 处于编辑模式
         */
        public void setEditMode(boolean isEditMode) {
            DDLog.d(TAG, "setEditMode, isEditMode: " + isEditMode);
            cbShortcutSelect.setVisibility(isEditMode ? View.VISIBLE : View.GONE);
            ivShortcutQuickStart.setVisibility(isEditMode ? View.GONE : View.VISIBLE);
            pbShortcutQuickStartLoading.setVisibility(isEditMode ? View.GONE : View.VISIBLE);
        }

        /**
         * 修改是否选中的状态
         *
         * @param isSelected true: 当前已选中
         */
        public void setSelected(boolean isSelected) {
            DDLog.d(TAG, "setSelected, isSelected: " + isSelected);
            cbShortcutSelect.setChecked(isSelected);
        }

        /**
         * 设置状态为loading
         */
        public void setStatusLoading() {
            DDLog.d(TAG, "setStatusLoading");
            setLoadingGone(true);
            setStatusTextGone(false);
            setQuickStartGone(false);
            setQuickStartLoadingGone(false);
        }

        /**
         * 主机离线状态时，修改自研智能插座的状态
         *
         * @param context
         * @param panelDeviceOffline
         */
        public void setStateTextByPanelDevice(Context context, boolean panelDeviceOffline) {
            DDLog.d(TAG, "setStateTextByPanelDevice. panelDeviceOffline: " + panelDeviceOffline);
            if (panelDeviceOffline) {
                tvShortcutStatus.setBackgroundResource(R.drawable.shape_main_panel_shortcut_item_status_border);
                tvShortcutStatus.setTextColor(context.getResources().getColor(R.color.panel_plugin_item_status_offline_text));
                String statusText = context.getResources().getString(R.string.Unknown);
                tvShortcutStatus.setText(Local.s(statusText));
            }
        }

        public void changeItemEnable(boolean enable) {
            clShortcutItemRoot.setAlpha(enable ? MainPanelHelper.VIEW_ENABLE_ALPHA : MainPanelHelper.VIEW_DISABLE_ALPHA);
        }

        /**
         * 设置状态为加载完成
         *
         * @param context
         * @param online
         */
        public void setStatusFinish(Context context, boolean needOnlineState, boolean online) {
            DDLog.d(TAG, "setStatusFinish, online: " + online);
            setLoadingGone(false);
            if (needOnlineState) {
                setStatusTextGone(true);
                setStatusText(context, online);
            } else {
                setStatusTextGone(false);
            }
            setQuickStartGone(online);
            setQuickStartLoadingGone(false);
        }

        /**
         * 空白填充
         */
        public void setStatusEmpty() {
            DDLog.d(TAG, "setStatusEmpty");
            tvShortcutName.setVisibility(View.GONE);
            tvShortcutStatus.setVisibility(View.GONE);
            pbShortcutLoading.setVisibility(View.GONE);
            cbShortcutSelect.setVisibility(View.GONE);
            ivShortcutQuickStart.setVisibility(View.GONE);
            pbShortcutQuickStartLoading.setVisibility(View.GONE);
        }

        public void setPluginName(String name) {
            tvShortcutName.setVisibility(View.VISIBLE);
            tvShortcutName.setText(TextUtils.isEmpty(name) ? "" : name);
        }

        /**
         * 修改快捷菜单的可见状态
         *
         * @param visible true: 可见
         */
        public void setQuickStartGone(boolean visible) {
            DDLog.d(TAG, "setQuickStartGone, visible: " + visible);
            ivShortcutQuickStart.setVisibility(visible ? View.VISIBLE : View.GONE);
        }

        /**
         * 修改快捷菜单的Loading可见状态
         *
         * @param visible true: 可见
         */
        public void setQuickStartLoadingGone(boolean visible) {
            DDLog.d(TAG, "setQuickStartLoadingGone, visible: " + visible);
            pbShortcutQuickStartLoading.setVisibility(visible ? View.VISIBLE : View.GONE);
        }

        /**
         * 设置快捷菜单是否真正loading
         *
         * @param isLoading true: 快捷菜单正在loading
         */
        public void setQuickStartLoading(boolean isLoading) {
            DDLog.d(TAG, "setQuickStartLoading, isLoading: " + isLoading);
            if (isLoading) {
                setQuickStartGone(false);
                setQuickStartLoadingGone(true);
            } else {
                setQuickStartGone(true);
                setQuickStartLoadingGone(false);
            }
        }

        /**
         * 设置快捷菜单图标
         *
         * @param iconRes 快捷菜单图片ID
         */
        public void setQuickStartIconRes(int iconRes) {
            DDLog.d(TAG, "setQuickStartIconRes");
            setQuickStartLoading(false);
            ivShortcutQuickStart.setImageResource(iconRes);
        }

        /**
         * 设置快捷菜单是否可点击
         *
         * @param enable true: 可以被点击
         */
        public void setQuickStartEnable(boolean enable) {
            DDLog.d(TAG, "setQuickStartEnable, enable: " + enable);
            ivShortcutQuickStart.setEnabled(enable);
        }

        /**
         * 设置整个Item的点击监听
         *
         * @param l
         */
        public void setRootViewClickListener(View.OnClickListener l) {
            clShortcutItemRoot.setOnClickListener(l);
        }

        /**
         * 设置快捷菜单的点击监听
         *
         * @param l
         */
        public void setQuickStartClickListener(View.OnClickListener l) {
            ivShortcutQuickStart.setOnClickListener(l);
        }
    }
}
