package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.animation.ValueAnimator;
import android.bluetooth.BluetoothGatt;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import androidx.annotation.IntDef;
import androidx.annotation.Keep;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.View;

import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPanelDebugModeBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.BleCheckBluetoothDialog;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.add.PanelBinder;
import com.dinsafer.panel.add.bean.PanelCmdResult;
import com.dinsafer.panel.add.callback.IPanelCmdCallback;
import com.dinsafer.panel.add.callback.IPanelConnectListener;
import com.dinsafer.panel.add.callback.IPanelScanListener;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.aop.annotations.SingleClick;
import com.yanzhenjie.permission.AndPermission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Observer;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * 主机Debug模式页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/7/5 5:49 下午
 */
public class PanelDebugModeFragment extends MyBaseFragment<FragmentPanelDebugModeBinding>
        implements IPanelConnectListener, IPanelCmdCallback {

    private static final int REQ_PERMISSION_LOCATION = 1123;
    private static final int DURATION_SCAN_TIMEOUT = 20 * 1000;
    private static final String KEY_DEVICE_ID = "device_id";

    @IntDef({DebugStep.STEP_DEFAULT, DebugStep.STEP_LOADING, DebugStep.STEP_SUCCESS, DebugStep.STEP_FAILED})
    @Retention(RetentionPolicy.SOURCE)
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    public @interface DebugStep {
        int STEP_DEFAULT = 0;
        int STEP_LOADING = 1;
        int STEP_SUCCESS = 2;
        int STEP_FAILED = 3;
    }

    @DebugStep
    private int currentStep = DebugStep.STEP_DEFAULT;

    private final Handler taskHandler = new Handler(Looper.getMainLooper());
    private final Runnable timeoutTask = () -> {
        if (DebugStep.STEP_LOADING == currentStep) {
            fail();
            changeViewToStep(DebugStep.STEP_FAILED);
        }
    };

    @Nullable
    private String deviceId;
    private boolean isPanelWithFamily = true;
    private boolean isSupport4G;
    private PanelBinder mPanelBinder;
    BleCheckBluetoothDialog checkBluetoothDialog = null;
    private final Handler mScanHandler = new Handler(Looper.getMainLooper());

    public static PanelDebugModeFragment newInstance(@Nullable final String deviceId) {
        final PanelDebugModeFragment fragment = new PanelDebugModeFragment();
        final Bundle args = new Bundle();
        args.putString(KEY_DEVICE_ID, deviceId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        deviceId = getArguments().getString(KEY_DEVICE_ID);

        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.debug_mode));
        mBinding.commonBar.commonBarBack.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                removeSelf();
            }
        });
        mBinding.btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                onConfirmClick();
            }
        });
        mBinding.cancel.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                onCancelClick();
            }
        });
        changeViewToStep(DebugStep.STEP_DEFAULT);
    }

    private void onCancelClick() {
        removeSelf();
    }

    private void onConfirmClick() {
        if (DebugStep.STEP_DEFAULT == currentStep) {
            if (!BleManager.getInstance().isBlueEnable()) {
                showOpenPhoneBle();
                return;
            }

            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                if (PermissionUtil.isLocationPermissionsDeny(getContext())) {
                    showNeedLocationPermissionDialog();
                    return;
                }
                if (!DDSystemUtil.isOpenGPS(getContext())) {
                    toOpenGPS(0);
                    return;
                }
            } else {
                if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                        || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), null);
                    return;
                }
            }

            changeViewToStep(DebugStep.STEP_LOADING);
        } else if (DebugStep.STEP_SUCCESS == currentStep) {
            removeSelf();
        } else if (DebugStep.STEP_FAILED == currentStep) {
            changeViewToStep(DebugStep.STEP_LOADING);
        }
    }

    @Override
    public boolean onBackPressed() {
        return DebugStep.STEP_DEFAULT != currentStep;
    }

    @Override
    public void onDestroyView() {
        taskHandler.removeCallbacksAndMessages(null);
        cancelLoadingAnim();
        mScanHandler.removeCallbacksAndMessages(null);
        clean();
        if (null != mPanelBinder) {
            mPanelBinder.stopBle();
            mPanelBinder.removePanelCmdResultListener(this);
            mPanelBinder.stopScanPanel();
            mPanelBinder.disconnectAllBle();
            mPanelBinder.destroyBinder();
            mPanelBinder.destroyAdder();
        }
        super.onDestroyView();
    }

    private void changeViewToStep(@DebugStep final int nextStep) {
        taskHandler.removeCallbacks(timeoutTask);
        switch (nextStep) {
            case DebugStep.STEP_DEFAULT:
                mBinding.commonBar.commonBarBack.setVisibility(View.VISIBLE);
                mBinding.ivDebugging.setVisibility(View.VISIBLE);
                String uri = "json/animation_debug_progress.json";
                mBinding.ivDebugging.setRepeatCount(ValueAnimator.INFINITE);
                mBinding.ivDebugging.setAnimation(uri);
                cancelLoadingAnim();
                mBinding.ivDebugResult.setVisibility(View.GONE);
                mBinding.tvCopy.setVisibility(View.GONE);
                mBinding.tvDebugHint.setTextColor(getResources().getColor(R.color.color_white_01));
                mBinding.tvDebugHint.setTextAppearance(getContext(), R.style.TextFamilyBodyL);
                mBinding.tvDebugHint.setLocalText(R.string.panel_debug_hint_before);
                mBinding.btnConfirm.setVisibility(View.VISIBLE);
                mBinding.btnConfirm.setLocalText(R.string.start);
                mBinding.cancel.setVisibility(View.GONE);
                break;
            case DebugStep.STEP_FAILED:
                cancelLoadingAnim();
                mBinding.commonBar.commonBarBack.setVisibility(View.INVISIBLE);
                mBinding.ivDebugging.setVisibility(View.GONE);
                mBinding.ivDebugResult.setVisibility(View.VISIBLE);
                mBinding.ivDebugResult.setImageResource(R.drawable.img_debug_fail);
                mBinding.tvCopy.setVisibility(View.GONE);
                mBinding.tvDebugHint.setTextColor(getResources().getColor(R.color.color_white_01));
                mBinding.tvDebugHint.setTextAppearance(getContext(), R.style.TextFamilyTittleM);
                mBinding.tvDebugHint.setLocalText(R.string.panel_debug_failed);
                mBinding.btnConfirm.setVisibility(View.VISIBLE);
                mBinding.btnConfirm.setLocalText(R.string.Retry);
                mBinding.cancel.setVisibility(View.VISIBLE);
                mBinding.cancel.setLocalText(R.string.quit);
                break;
            case DebugStep.STEP_LOADING:
                startLoadingAnim();
                mBinding.commonBar.commonBarBack.setVisibility(View.VISIBLE);
                mBinding.ivDebugging.setVisibility(View.VISIBLE);
                mBinding.ivDebugResult.setVisibility(View.GONE);
                mBinding.tvCopy.setVisibility(View.GONE);
                mBinding.tvDebugHint.setTextColor(getResources().getColor(R.color.color_white_01));
                mBinding.tvDebugHint.setTextAppearance(getContext(), R.style.TextFamilyBodyL);
                mBinding.tvDebugHint.setLocalText(R.string.panel_debug_hint_debugging);
                mBinding.btnConfirm.setVisibility(View.GONE);
                mBinding.cancel.setVisibility(View.GONE);

                taskHandler.postDelayed(timeoutTask, DURATION_SCAN_TIMEOUT);
                doRealScan();
                break;
            case DebugStep.STEP_SUCCESS:
                cancelLoadingAnim();
                mBinding.commonBar.commonBarBack.setVisibility(View.INVISIBLE);
                mBinding.ivDebugging.setVisibility(View.GONE);
                mBinding.ivDebugResult.setVisibility(View.VISIBLE);
                mBinding.ivDebugResult.setImageResource(R.drawable.img_debug_completion);
                mBinding.tvCopy.setVisibility(View.VISIBLE);
                mBinding.tvDebugHint.setTextColor(getResources().getColor(R.color.color_white_02));
                mBinding.tvDebugHint.setTextAppearance(getContext(), R.style.TextFamilyCaptionL);
                mBinding.tvDebugHint.setLocalText(R.string.panel_debug_long_press_hint);
                mBinding.btnConfirm.setVisibility(View.VISIBLE);
                mBinding.btnConfirm.setLocalText(R.string.done);
                mBinding.cancel.setVisibility(View.GONE);
                break;
        }
        currentStep = nextStep;
    }

    // 开启扫描动画
    private void startLoadingAnim() {
        mBinding.ivDebugging.playAnimation();
    }

    // 停止扫描动画
    private void cancelLoadingAnim() {
        mBinding.ivDebugging.pauseAnimation();
        mBinding.ivDebugging.cancelAnimation();
    }

    private void doRealScan() {
        toConnectBleDevice();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_panel_debug_mode;
    }

    private void showNoPhoneBleToast() {
        final BleCheckBluetoothDialog dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_PHONE);
        dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
            @Override
            public void clickOk() {
                if (BleManager.getInstance().isBlueEnable()) {
                    dialog.dismiss();
                }
            }

            @Override
            public void clickCanal() {
                dialog.dismiss();
                removeSelf();
            }
        });
        dialog.show();
    }

    private Subscription subscribe;

    private void toConnectBleDevice() {
        if (!BleManager.getInstance().isBlueEnable()) {
            showNoPhoneBleToast();
            return;
        }

        if (null == mPanelBinder) {
            DinSDK.getPluginActivtor().setup(DinSaferApplication.getAppContext());
            DinSDK.getPluginActivtor().createPanelBinder();
            BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
            if (!(pluginBinder instanceof PanelBinder)) {
                DDLog.e(TAG, "Error panel binder.");
                showErrorToast();
                return;
            }
            mPanelBinder = (PanelBinder) pluginBinder;
            mPanelBinder.init(DinSaferApplication.getInstance(),
                    null != DinSDK.getUserInstance().getUser()
                            ? DinSDK.getUserInstance().getUser().getUid()
                            : "",
                    null != DinSDK.getUserInstance().getUser()
                            ? DinSDK.getUserInstance().getUser().getUser_id()
                            : "", HomeManager.getInstance().getCurrentHome().getHomeID());
            mPanelBinder.initScanRule(0, new String[]{APIKey.UUID_SERVICE},
                    APIKey.UUID_CHRA_WRITE, APIKey.UUID_CHRA_NOTIFY);
        }
        mPanelBinder.addPanelCmdResultListener(this);

        if (subscribe != null && !subscribe.isUnsubscribed()) {
            subscribe.unsubscribe();
        }
        subscribe = Observable.interval(APIKey.BLE_CONNECT_TIMEOUT + 1000, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .take(1)
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Long aLong) {
                        fail();
                    }
                });
        mScanHandler.postDelayed(() -> mPanelBinder.startScanPanel(bleScanCallback), 500);
    }

    /**
     * 失败处理
     */
    private void fail() {
        DDLog.d(TAG, "onFail");
        changeViewToStep(DebugStep.STEP_FAILED);
        if (null != mPanelBinder) {
            mPanelBinder.disconnectAllBle();
        }
        clean();
    }

    /**
     * 清空处理：定时器、loading
     */
    private void clean() {
        DDLog.d(TAG, "clean");
        if (subscribe != null && !subscribe.isUnsubscribed()) {
            subscribe.unsubscribe();
        }
        if (null != mPanelBinder) {
            mPanelBinder.stopScanPanel();
        }
    }

    private final IPanelScanListener bleScanCallback = new IPanelScanListener() {
        @Override
        public void onScanStarted(boolean success) {
            // 开始扫描（主线程）
            DDLog.d(TAG, "开始扫描");
        }

        @Override
        public void onScanning(BleDevice bleDevice) {
            DDLog.d(TAG, "扫描中:" + bleDevice.getName());
            // 扫描到一个符合扫描规则的BLE设备（主线程）
            if (bleDevice.getName() == null) {
                return;
            }

            if (!bleDevice.getName().equals(deviceId)) {
                return;
            }

            if (null != mPanelBinder) {
                taskHandler.removeCallbacks(timeoutTask);
                taskHandler.postDelayed(timeoutTask, DURATION_SCAN_TIMEOUT);

                isPanelWithFamily = PanelBinder.isDeviceWithFamily(bleDevice);
                isSupport4G = PanelBinder.isDeviceSupport4G(bleDevice);
                mPanelBinder.connect(bleDevice, PanelDebugModeFragment.this);
            }
        }

        @Override
        public void onScanFinished(List<BleDevice> scanResultList) {
            // 扫描结束，列出所有扫描到的符合扫描规则的BLE设备（主线程）
            if (scanResultList.isEmpty()) {
            }
        }
    };

    @Override
    public void onStartConnect() {

    }

    @Override
    public void onConnectFail(BleDevice bleDevice, BleException e) {
        fail();
    }

    @Override
    public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt bluetoothGatt, int i) {

    }

    @Override
    public void onDisConnected(BleDevice bleDevice, boolean b, BluetoothGatt bluetoothGatt, int i) {
        fail();
        showOpenDeviceBle();
    }

    @Override
    public void onNotifySuccess() {
        clean();
        if (null != mPanelBinder) {
            mPanelBinder.runZT();
        }
    }

    @Override
    public void onNotifyFailure(BleDevice bleDevice, BleException e) {
        fail();
    }

    public void showOpenDeviceBle() {
        /**
         * 打开弹窗，提示检查主机蓝牙
         */
        if (checkBluetoothDialog != null && checkBluetoothDialog.isShowing()) {
            DDLog.d(TAG, "dialog != null && dialog.isShowing()");
            return;
        } else {
            DDLog.d(TAG, "dialog == null ||  dialog.isNotShowing()");
            checkBluetoothDialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_DEVICE);
            checkBluetoothDialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    if (null != mPanelBinder && mPanelBinder.isOpenedBluetooth()) {
                        checkBluetoothDialog.dismiss();
                    }
                }

                @Override
                public void clickCanal() {
                    checkBluetoothDialog.dismiss();
                }
            });
            checkBluetoothDialog.show();
        }
    }

    @Override
    public void onPanelResult(PanelCmdResult panelCmdResult) {
        DDLog.i(TAG, "onPanelResult： " + panelCmdResult);
        final int status = panelCmdResult.getStatus();
        final String cmd = panelCmdResult.getCmd();
        if (TextUtils.isEmpty(cmd)) {
            DDLog.e(TAG, "Empty panel adder cmd.");
            return;
        }

        if (LocalKey.BLE_CMD_RUN_ZT.equals(cmd)) {
            // {cmd='RunZT', result='ee888315d4', status=1}
            if (1 == status) {
                String result = panelCmdResult.getResult();
                changeViewToStep(DebugStep.STEP_SUCCESS);
                mBinding.tvCopy.setText(null == result ? "" : result);

                taskHandler.postDelayed(() -> {
                    if (null != mPanelBinder) {
                        mPanelBinder.stopBle();
                        mPanelBinder.disconnectAllBle();
                    }
                }, 500);
            } else {
                changeViewToStep(DebugStep.STEP_FAILED);
            }
        }
    }

    public void showOpenPhoneBle() {
        BleCheckBluetoothDialog dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_PHONE);
        dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
            @Override
            public void clickOk() {
                dialog.dismiss();
            }

            @Override
            public void clickCanal() {
                dialog.dismiss();
                removeSelf();
            }
        });
        dialog.show();
    }

    /**
     * 提示需要申请定位权限
     */
    public void showNeedLocationPermissionDialog() {
        PermissionDialogUtil.showNeedBleLocationPermissionDialog(getMainActivity(), false, v -> {
            removeSelf();
        }, v -> {
            getMainActivity().setNotNeedToLogin(true);
            requestLocationPermission();
        });
    }

    private void requestLocationPermission() {
        final String[] permission = PermissionUtil.getLocationPermissions();
        boolean denied = AndPermission.hasAlwaysDeniedPermission(getMainActivity(), permission);

        AndPermission.with(this)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    PermissionDialogUtil.hide();

                })
                .onDenied(permissions -> {
                    DDLog.e(TAG, "Location permission deny!!!");
                    if (denied
                            && AndPermission.hasAlwaysDeniedPermission(PanelDebugModeFragment.this, permissions)) {
                        openSystemSetting();
                    }
                })
                .start();
    }

    /**
     * 开启系统设置页
     */
    protected void openSystemSetting() {
        DDLog.i(TAG, "openSystemSetting");
        try {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setData(Uri.parse("package:" + getContext().getPackageName()));
            startActivityForResult(intent, REQ_PERMISSION_LOCATION);
        } catch (Exception e) {
            DDLog.e(TAG, "Can't open system setting!!!");
            e.printStackTrace();
            showErrorToast();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (REQ_PERMISSION_LOCATION == requestCode) {
            if (ContextCompat.checkSelfPermission(getMainActivity(),
                    Manifest.permission.ACCESS_COARSE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED) {
                return;
            }

            PermissionDialogUtil.hide();
            return;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

}
