package com.dinsafer.module.settting.ui.delegate;

import android.bluetooth.BluetoothGatt;
import android.bluetooth.le.ScanRecord;
import android.os.Handler;
import android.text.TextUtils;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.scan.BleScanRuleConfig;
import com.clj.fastble.utils.HexUtil;
import com.dinsafer.bluetooth.BleSwitchBotController;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.model.SwitchBotEntry;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.RandomStringUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import rx.Observable;
import rx.Subscriber;
import rx.Subscription;

/**
 * Created by LT on 2019/3/22.
 */
public class SwitchBotDelegate implements BleSwitchBotController.BleDataCallback {
    static SwitchBotDelegate instance;
    private String messageId;
    private final String TAG = this.getClass().getSimpleName();
    private BleSwitchBotController bleSwitchBotController;
    public static final String ONE_BUTTON_TO_PRESS = "570100";
    public static final String TWO_BUTTON_TO_ON = "570101";
    public static final String TWO_BUTTON_TO_OFF = "570102";

    public SwitchBotDelegate() {
        EventBus.getDefault().register(this);
        if (!APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE) {
            bleSwitchBotController = new BleSwitchBotController(this);
            setScanRuleWithUUID();
        }
    }

    public interface StatusChangeListener {
        void onStatusChanged(ArrayList<SwitchBotEntry> switchBotList);

        void onCollectedSwitchBot(String id);

        void onDeleteSwitchBot(String id);

        void onChangeSwitchBotName(String id, String name);

        void onChangeSwitchBotStatus(String id, boolean isOneBtn, boolean isOn);
    }

    private StatusChangeListener statusChangeListener;

    public void setStatusChangeListener(StatusChangeListener statusChangeListener) {
        this.statusChangeListener = statusChangeListener;
    }

    public void clear() {
        this.statusChangeListener = null;
        EventBus.getDefault().unregister(this);
        closeScanTimer();
        stopScan();
        BleManager.getInstance().disconnectAllDevice();
    }

    /**
     * 获取收藏列表
     *
     * @param getCollectedListListener
     */
    public void getCollectedList(GetCollectedListListener getCollectedListListener) {
        if (!APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE) {
            startScanTimerAndDisconnect();
        }
        Call<ResponseBody> call = DinsafeAPI.getApi().getSwitchBotListDataCall(CommonDataUtil.getInstance().getCurrentDeviceId());
        call.enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                if (getCollectedListListener == null) {
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(response.body().string());
                    if (jsonObject.getInt("Status") != 1) {
                        getCollectedListListener.fail();
                        return;
                    }

                    String result = DDSecretUtil.getReverSC(jsonObject.getString("Result"));
                    JSONObject resultJson = new JSONObject(result);
                    JSONArray jsonArray = DDJSONUtil.getJSONarray(resultJson, "datas");
                    ArrayList<SwitchBotEntry> list = new ArrayList<>();
                    if (jsonArray == null) {
                        return;
                    }
                    for (int i = 0; i < jsonArray.length(); i++) {
                        SwitchBotEntry switchBotEntry = new SwitchBotEntry();
                        switchBotEntry.setId(DDJSONUtil.getString(jsonArray.getJSONObject(i), "id"))
                                .setName(DDJSONUtil.getString(jsonArray.getJSONObject(i), "name"))
                                .setNoStatus(true)
                                .setOneBtn(true)
                                .setCollected(true);
                        list.add(switchBotEntry);
                    }
                    getCollectedListListener.onSuccess(list);
                } catch (JSONException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                if (getCollectedListListener == null) {
                    return;
                }
                getCollectedListListener.fail();
            }
        });

    }

    /**
     * 控制switchbot
     *
     * @param id
     * @param listener
     */
    public void controlSwitchBot(String id, String action, ControlListener listener) {
        DDLog.d(TAG, "controlSwitchbot~~~" + action);
        if (APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE) {
            final String uid = CommonDataUtil.getInstance().getUserUid();
            final String panelToken = DeviceHelper.getString(DinSDK.getHomeInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID()),
                    PanelDataKey.Panel.DEVICE_TOKEN, null);
            if (TextUtils.isEmpty(uid) || TextUtils.isEmpty(panelToken)) {
                listener.onFail(id);
                return;
            }

            messageId = RandomStringUtils.getMessageId();

            String switchBotName = "";
            Call<StringResponseEntry> call = DinsafeAPI.getApi().controlSwitchBotCall(uid, panelToken, messageId, action, id);
            call.enqueue(new Callback<StringResponseEntry>() {
                @Override
                public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                    if (response.body().getStatus() != 1) {
                        listener.onFail(id);
                        return;
                    }
                    listener.onSuccess(id);
                }

                @Override
                public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                    listener.onFail(id);
                }
            });
        } else {
            if (!isConnecting) {
                isConnecting = true;
                closeScanTimer();
                controlByBle(id, action, listener);

            }
        }
    }

    private boolean isConnecting = false;

    public interface GetCollectedListListener {
        void onSuccess(ArrayList<SwitchBotEntry> list);

        void fail();
    }


    public interface ControlListener {
        void onSuccess(String id);

        void onFail(String id);
    }

    /**
     * 显示去收藏的tip
     *
     * @return
     */
    public boolean isShowCollectTip() {
        if (DBUtil.Exists(DBKey.SWITCH_BOT_COLLECT_TIP)) {
            return false;
        }
        return true;
    }

    /**
     * 不显示去收藏的tip
     *
     * @return
     */
    public void disappearCollectTip() {
        DBUtil.Put(DBKey.SWITCH_BOT_COLLECT_TIP, 1);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceResultEvent ev) {
        if (statusChangeListener == null) {
            return;
        }
        if (ev.getStatus() == 1) {
            if (ev.getCmdType().equals(LocalKey.CMD_COLLECT_SWITCHBOT)
                    || ev.getCmdType().equals(LocalKey.CMD_DELETE_SWITCHBOT)
                    || ev.getCmdType().equals(LocalKey.CMD_SET_SWITCH_BOT_NAME)
                    || ev.getCmdType().equals(LocalKey.CMD_SET_SWITCH_BOT)) {
                String result = ev.getReslut();
                DDLog.d(TAG, "bot websocket返回：" + ev.getCmdType() + ",数据：" + result);
                try {
                    JSONObject json = new JSONObject(result);
                    if (TextUtils.isEmpty(result) || statusChangeListener == null || !DDJSONUtil.has(json, "id")) {
                        return;
                    }
                    switch (ev.getCmdType()) {
                        case LocalKey.CMD_COLLECT_SWITCHBOT:
                            statusChangeListener.onCollectedSwitchBot(DDJSONUtil.getString(json, "id"));
                            break;
                        case LocalKey.CMD_DELETE_SWITCHBOT:
                            statusChangeListener.onDeleteSwitchBot(DDJSONUtil.getString(json, "id"));
                            break;
                        case LocalKey.CMD_SET_SWITCH_BOT_NAME:
                            statusChangeListener.onChangeSwitchBotName(DDJSONUtil.getString(json, "id"), DDJSONUtil.getString(json, "name"));
                            break;
                        case LocalKey.CMD_SET_SWITCH_BOT:
                            statusChangeListener.onChangeSwitchBotStatus(DDJSONUtil.getString(json, "id"), !DDJSONUtil.getBoolean(json, "actMode"), DDJSONUtil.getBoolean(json, "status"));
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (ev.getCmdType().equals(LocalKey.TASK_SWITCH_BOT_STATUS)) {
                if (!APIKey.IS_CONTROL_SWTICHBOT_BY_DEVICE) {
                    return;
                }
                DDLog.d(TAG, "bot websocket返回：" + ev.getCmdType() + ",数据：" + ev.getReslut());
                try {
                    JSONArray jsonArray = new JSONArray(ev.getReslut());
                    ArrayList<SwitchBotEntry> list = new ArrayList<>();
                    if (jsonArray.length() > 0 || statusChangeListener != null) {
                        for (int i = 0; i < jsonArray.length(); i++) {
                            SwitchBotEntry switchBotEntry = new SwitchBotEntry();
                            switchBotEntry.setId(DDJSONUtil.getString((JSONObject) jsonArray.get(i), "id"))
                                    .setOn(DDJSONUtil.getBoolean((JSONObject) jsonArray.get(i), "status"))
                                    .setOneBtn(!DDJSONUtil.getBoolean((JSONObject) jsonArray.get(i), "actMode"));
                            list.add(switchBotEntry);
                        }

                    }
                    statusChangeListener.onStatusChanged(list);
                    return;
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 收藏
     *
     * @param id
     */
    public void collectSwitchBot(String id, String name, SwitchBotCallback callback) {
        // FIXME: 2019/3/26 bot 超时处理

        messageId = RandomStringUtils.getMessageId();
        Call<StringResponseEntry> call = DinsafeAPI.getApi().collectSwitchBotCall(id,
                name, CommonDataUtil.getInstance().getCurrentDeviceId());
        call.enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (response.body().getStatus() != 1) {
                    callback.onFail();
                    return;
                }
                callback.onSuccess();
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                callback.onFail();
            }
        });

    }


    /**
     * 收藏
     *
     * @param id
     */
    public void deleteSwitchBot(String id, String name, SwitchBotCallback callback) {
        // FIXME: 2019/3/26 bot 超时处理

        Call<StringResponseEntry> call = DinsafeAPI.getApi().deleteSwitchBotCall(id,
                CommonDataUtil.getInstance().getCurrentDeviceId());
        call.enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (response.body().getStatus() != 1) {
                    callback.onFail();
                    return;
                }
                callback.onSuccess();
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                callback.onFail();
            }
        });

    }


    public interface SwitchBotCallback {
        void onSuccess();

        void onFail();
    }

    private void scanSwitchBot() {

    }


    @Override
    public void onScanFinished(List<BleDevice> scanResultList) {

    }


    private BleScanCallback bleScanCallback = new BleScanCallback() {
        @Override
        public void onScanStarted(boolean success) {
            DDLog.d(TAG, "onScanStarted");
        }

        @Override
        public void onScanning(BleDevice bleDevice) {
        }

        @Override
        public void onScanFinished(List<BleDevice> scanResultList) {
            DDLog.d(TAG, "onScanFinished");
            ArrayList<SwitchBotEntry> list = new ArrayList<>();
            for (int i = 0; i < scanResultList.size(); i++) {
                ScanRecord scanRecord = parseScanRecordFromBytes(scanResultList.get(i).getScanRecord());
                if (scanRecord.getServiceData().size() <= 0) {
                    continue;
                }
                byte[] data = scanRecord.getServiceData().entrySet().iterator().next().getValue();
                /**
                 * mode 按钮数量：mode == 0(为一个按钮)； mode == 1(为两个按钮)
                 * state 按钮状态：state == 0(为on)； state == 1(为off)
                 */
                if (data.length < 2) {
                    continue;
                }
                int mode = getBit(data[1], 7);
                int state = getBit(data[1], 6);

                try {
                    SwitchBotEntry switchBotEntry = new SwitchBotEntry();
                    switchBotEntry.setId(scanResultList.get(i).getMac())
                            .setOn(state == 0 ? true : false)
                            .setOneBtn(mode == 0 ? true : false);
                    list.add(switchBotEntry);
                } catch (Exception e) {

                }
            }
            DDLog.d(TAG, list.toString());
            if (statusChangeListener != null && isScaning) {
                statusChangeListener.onStatusChanged(list);
            }
        }
    };

    public static void setScanRuleWithUUID() {
        UUID[] uuids = new UUID[1];
        uuids[0] = UUID.fromString(APIKey.SWITCH_BOT_UUID_SERVICE);
        BleScanRuleConfig scanRuleConfig = new BleScanRuleConfig.Builder()
                .setServiceUuids(uuids)
                .setScanTimeOut(APIKey.SWITCH_BOT_SCAN_TIMEOUT)
                .build();
        BleManager.getInstance().initScanRule(scanRuleConfig);
    }


    public static ScanRecord parseScanRecordFromBytes(byte[] bytes) {
        try {
            Method parseFromBytes = ScanRecord.class.getMethod("parseFromBytes", byte[].class);
            return (ScanRecord) parseFromBytes.invoke(null, (Object) bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private int getBit(byte by, int i) {
        /**
         * 获取7-0位的bit。
         */
        return (byte) ((by >> i) & 0x1);
    }


    private Subscription scanTimer;

    /**
     * 启动定时器
     */
    public void startScanTimerAndDisconnect() {
        if (isScaning) {
            return;
        }
//        //扫描超时时间+1
        isConnecting = false;
        BleManager.getInstance().disconnectAllDevice();
        closeScanTimer();
        scanTimer = Observable.interval(0, APIKey.BLE_SCAN_TIMEOUT + 100, TimeUnit.MILLISECONDS)
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "onNext");
                        BleManager.getInstance().scan(bleScanCallback);
                    }
                });
        isScaning = true;
    }

    private boolean isScaning = false;

    public void closeScanTimer() {
        if (!isScaning) {
            return;
        }
        if (scanTimer != null && !scanTimer.isUnsubscribed()) {
            scanTimer.unsubscribe();
        }
        stopScan();
        isScaning = false;
    }


    private void stopScan() {
        try {
            BleManager.getInstance().cancelScan();
        } catch (Exception e) {

        }
    }

    private void controlByBle(String id, String action, ControlListener listener) {
        DDLog.d(TAG, "connecting~~~");
        BleManager.getInstance().connect(id, new BleGattCallback() {
            @Override
            public void onStartConnect() {

            }

            @Override
            public void onConnectFail(BleDevice bleDevice, BleException exception) {
                listener.onFail(id);
                startScanTimerAndDisconnect();
            }

            @Override
            public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {

                BleManager.getInstance().notify(bleDevice,
                        APIKey.SWITCH_BOT_UUID_SERVICE,
                        APIKey.SWITCH_BOT_UUID_CHRA_NOTIFY,
                        new BleNotifyCallback() {
                            @Override
                            public void onNotifySuccess() {
                                DDLog.d(TAG, "onNotifySuccess");
                            }

                            @Override
                            public void onNotifyFailure(BleException exception) {
                                DDLog.d(TAG, "onNotifyFailure");
                            }

                            @Override
                            public void onCharacteristicChanged(byte[] data) {
                                DDLog.d(TAG, "notify data is " + data[0]);
                                if (listener != null && data[0] == 1) {
                                    listener.onSuccess(id);
                                }
                                startScanTimerAndDisconnect();

                            }
                        });

                /**
                 * 开启通知成功失败，都去执行。
                 */

                DDLog.d(TAG, "connect success, to write");
                String data = action.equals("press") ? ONE_BUTTON_TO_PRESS : (action.equals("on") ? TWO_BUTTON_TO_ON : TWO_BUTTON_TO_OFF);

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        BleManager.getInstance().write(
                                bleDevice,
                                APIKey.SWITCH_BOT_UUID_SERVICE,
                                APIKey.SWITCH_BOT_UUID_CHRA_WRITE,
                                HexUtil.hexStringToBytes(data),
                                new BleWriteCallback() {
                                    @Override
                                    public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
                                        DDLog.d(TAG, "onWriteSuccess");

                                        new Handler().postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                startScanTimerAndDisconnect();
                                            }
                                        }, 1000);
                                    }

                                    @Override
                                    public void onWriteFailure(final BleException exception) {
                                        startScanTimerAndDisconnect();
                                    }
                                });
                    }
                }, 500);


            }

            @Override
            public void onDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {

            }
        });
    }
}
