package com.dinsafer.module.settting.ui;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.RecordFile;
import com.dinsafer.module.settting.adapter.RecordFileAdapter;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import hsl.p2pipcam.activity.BridgeService;
import hsl.p2pipcam.activity.SettingsListener;
import hsl.p2pipcam.nativecaller.DeviceSDK;

public class RecordSearchActivity extends Activity implements SettingsListener {

    Calendar beginCalendar, endCalendar;

//    ProgressDialog progressDialog;

    ListView listView;


    List<RecordFile> fileList;
    RecordFileAdapter recordFileAdapter;
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String[] dateItems = new String[]{Local.s("today"), Local.s("3 days"), Local.s("a week"), Local.s("long long ago")};
    private long mUserID;
    private TextView nav_bar_name_textObj;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_record_search);

        mUserID = getIntent().getExtras().getLong("userID");

        BridgeService.setSettingsListener(this);

        //后退按钮
        Button backBtn = (Button) findViewById(R.id.nav_bar_left_btn);
        backBtn.setText(Local.s("Back"));
        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        nav_bar_name_textObj = (TextView) findViewById(R.id.nav_bar_name_text);
        nav_bar_name_textObj.setText(Local.s("Play Record"));

        // 选择时间
        Button dateBtn = (Button) findViewById(R.id.nav_bar_right_btn);
        dateBtn.setText(Local.s("DateFilter"));
        dateBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlertDialog.Builder builder = new AlertDialog.Builder(RecordSearchActivity.this);
                builder.setTitle(Local.s("Record time"));
                builder.setItems(dateItems, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {

//                        progressDialog = ProgressDialog.show(RecordSearchActivity.this, null, Local.s("Please wait"), true, true);

                        Date date = new Date();
                        if (which == 0) { // 当天
                            beginCalendar = Calendar.getInstance();
                            endCalendar = beginCalendar;

                        } else if (which == 1) { // 3天
                            Calendar calendar = new GregorianCalendar();
                            calendar.setTime(date);
                            calendar.add(calendar.DATE, -2);
                            beginCalendar = calendar;

                        } else if (which == 2) { // 一周
                            Calendar calendar = new GregorianCalendar();
                            calendar.setTime(date);
                            calendar.add(calendar.DATE, -6);
                            beginCalendar = calendar;

                        } else if (which == 3) { // long long ago
                            endCalendar = Calendar.getInstance();
                            Calendar calendar = new GregorianCalendar();
                            calendar.setTime(date);
                            try {
                                calendar.setTime(sdf.parse("2015-1-1 0:0:0"));
                                beginCalendar = calendar;
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                        }

                        fileList.clear();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                recordFileAdapter.notifyDataSetChanged();
                            }
                        });
                        getReplayFiles();
                    }
                });
                builder.setNegativeButton(Local.s("Cancel"), null);
                AlertDialog dialog = builder.show();
                dialog.getButton(DialogInterface.BUTTON_NEGATIVE).setTextSize(TypedValue.COMPLEX_UNIT_DIP, 22);

            }
        });

//        progressDialog = ProgressDialog.show(this, null, Local.s("Please wait"), true, true);

        fileList = new ArrayList<>();
        recordFileAdapter = new RecordFileAdapter(RecordSearchActivity.this, android.R.layout.simple_list_item_1, fileList);

        listView = (ListView) findViewById(R.id.pull_refresh_list);
        listView.setAdapter(recordFileAdapter);
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                RecordFile file = fileList.get(position);
                Bundle bundle = new Bundle();
                bundle.putString("fileName", file.getName());
                bundle.putInt("fileSize", file.getSize());
                bundle.putLong("userID", mUserID);
                Intent intent = new Intent(RecordSearchActivity.this, RecordPlayActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
            }
        });

        //默认先获取当天的录像文件
        beginCalendar = Calendar.getInstance();
        endCalendar = beginCalendar;

        getReplayFiles();
    }

    /**
     * 请求录像文件
     */
    private void getReplayFiles() {
        int beginYear = beginCalendar.get(Calendar.YEAR);
        int beginMonth = beginCalendar.get(Calendar.MONTH) + 1;
        int beginDay = beginCalendar.get(Calendar.DAY_OF_MONTH);
        int endYear = endCalendar.get(Calendar.YEAR);
        int endMonth = endCalendar.get(Calendar.MONTH) + 1;
        int endDay = endCalendar.get(Calendar.DAY_OF_MONTH);
//        Log.e("","begin year:"+beginYear);
//        Log.e("","begin month:"+beginMonth);
//        Log.e("","begin day:"+beginDay);
//        Log.e("","end year:"+endYear);
//        Log.e("","end month:"+endMonth);
//        Log.e("","end day:"+endDay);
        DeviceSDK.getRecordVideo(mUserID, beginYear, beginMonth, beginDay, endYear, endMonth, endDay);
//        NativeCaller.SearchRecordFile(IPCMainActivity.userid , 2014, 2, 1, 0, 0, 0, 2014, 2, 1, 0, 0, 0);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().post(new CloseActivityEvent());
    }

    private void sort() {
        Collections.sort(fileList, new Comparator<RecordFile>() {
            @Override
            public int compare(RecordFile lhs, RecordFile rhs) {
                try {
                    Date date1 = sdf.parse(lhs.getDate());
                    Date date2 = sdf.parse(rhs.getDate());
                    return date2.compareTo(date1);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_record_search, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == R.id.action_settings) {
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    //  SettingsListener
    @Override
    public void callBack_getParam(long UserID, long nType, String param) {

    }

    @Override
    public void callBack_setParam(long UserID, long nType, int nResult) {

    }

    @Override
    public void recordFileList(long UserID, int filecount, String fname, String strDate, int size) {
        if (recordFileAdapter != null) {
            RecordFile file = new RecordFile(fname, strDate, size);
            fileList.add(file);
            if (filecount == fileList.size()) {
                sort();
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        recordFileAdapter.notifyDataSetChanged();
                    }
                });
            }
        }
    }
}
