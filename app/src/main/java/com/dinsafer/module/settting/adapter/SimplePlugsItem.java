package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.PlugsData;
import com.dinsafer.util.CommonDataUtil;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class SimplePlugsItem extends BasePluginItem {

    private Activity mActivity;

    private ArrayList<PlugsData> mData;

    public SimplePlugsItem(Activity mActivity, ArrayList<PlugsData> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    public void setData(ArrayList<PlugsData> mData) {
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        ItemPluginHolder itemHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.item_plugin_new_style, null);
            itemHolder = new ItemPluginHolder(convertView);
            convertView.setTag(itemHolder);
        } else {
            try {
                itemHolder = (ItemPluginHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 更新视图状态
        PlugsData plugsData = mData.get(position);
        boolean panelDeviceState = !CommonDataUtil.getInstance().isPanelOnline();
        updatePluginItemByPanelDevice(panelDeviceState, plugsData, plugsData.getName(), itemHolder);

        // 更新底部分割线显示状态
        if (position == mData.size() - 1) {
            itemHolder.changeBottomLineStatus(ItemPluginHolder.BOTTOM_LINE_HIDE);
        } else {
            itemHolder.changeBottomLineStatusPX((int) mActivity.getResources().getDimension(R.dimen.item_plugin_new_edge_left));
        }

        return convertView;
    }

    public void remove(int index) {
        if (mData != null && index >= 0 && index < mData.size()) {
            mData.remove(index);
            notifyDataSetChanged();
        }
    }

    public void changeName(int index, String name) {
        mData.get(index).setName(name);
        notifyDataSetChanged();
    }
}
