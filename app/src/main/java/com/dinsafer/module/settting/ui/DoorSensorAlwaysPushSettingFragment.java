package com.dinsafer.module.settting.ui;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDoorSensorAlwaysPushSettingBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;

import org.json.JSONObject;

import java.util.Map;

/**
 * 随时推送门磁状态变化开关设置
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/8/20 3:11 下午
 */
public class DoorSensorAlwaysPushSettingFragment extends BaseFragment
        implements IDeviceCallBack {

    private static final String DATA = "data";
    private static final String OPENED = "OPENED";

    private Builder builder;
    private JSONObject mData;
    private boolean mAlwaysPush;

    private Device mPluginDevice;
    private boolean isSelfOperate;

    private FragmentDoorSensorAlwaysPushSettingBinding mBinding;

    public static DoorSensorAlwaysPushSettingFragment newInstance(final Builder builder) {
        DoorSensorAlwaysPushSettingFragment fragment = new DoorSensorAlwaysPushSettingFragment();
        Bundle args = new Bundle();
        args.putParcelable("data", builder);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_door_sensor_always_push_setting, container, false);
        builder = (Builder) getArguments().getParcelable(DATA);
        try {
            mData = new JSONObject(builder.getData());
            mAlwaysPush = DDJSONUtil.getBoolean(mData, "push_status");
        } catch (Exception e) {
            e.printStackTrace();
        }
        findDevice();
        initView(mBinding.getRoot(), savedInstanceState);
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
    }

    private void findDevice() {
        if (null == builder) {
            return;
        }
        String pluginId = builder.getId();
        if (!TextUtils.isEmpty(pluginId)) {
            mPluginDevice = DinHome.getInstance().getDevice(pluginId);
            if (null != mPluginDevice) {
                mPluginDevice.registerDeviceCallBack(this);
            }
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.modify_plugs_always_push_status));
        mBinding.tvAlwaysPushHint.setLocalText(getString(R.string.modify_plugs_always_push_status_hint));
        mBinding.switchOpen.setOn(mAlwaysPush);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.switchOpen.setOnSwitchStateChangeListener(isOn -> {
            if (null != mPluginDevice) {
                showTimeOutLoadinFramgmentWithErrorAlert();
                isSelfOperate = true;
                mPluginDevice.submit(PanelParamsHelper.setDoorWindowPushStatus(isOn));
            } else {
                showErrorToast();
                DDLog.e(TAG, "No plugin device.");
            }
        });
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())
                || !isSelfOperate) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        isSelfOperate = false;
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);

        if (PluginCmd.SET_DOOR_WINDOW_PUSH_STATUS.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                showErrorToast();
            } else {
                showSuccess();
                removeSelf();
            }
        }
    }
}
