package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.SmartPlugsData;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.ui.ModifyPlugsFragment;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.NavigatorUtil;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class SmartPlugsGridItem extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<SmartPlugsData> mData;

    private boolean isEditMode = false;


    public SmartPlugsGridItem(Activity mActivity, ArrayList<SmartPlugsData> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    @Override
    public int getCount() {
        if (mData.size() % 2 != 0) {
            return mData.size() + (2 - mData.size() % 2);
        }
        return mData.size();
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.smart_plugin_grid_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (position >= mData.size()) {
            holder.mainSectionIcon.clearAnimation();
            holder.mainSectionName.setVisibility(View.INVISIBLE);
            holder.mainSectionIcon.setVisibility(View.INVISIBLE);
            holder.mainSectionStatus.setVisibility(View.INVISIBLE);
            holder.smartPluginEdit.setVisibility(View.GONE);
            return convertView;
        } else {
            holder.mainSectionName.setVisibility(View.VISIBLE);
            holder.mainSectionIcon.setVisibility(View.VISIBLE);
            holder.mainSectionStatus.setVisibility(View.VISIBLE);
            if (isEditMode) {
                holder.smartPluginEdit.setVisibility(View.VISIBLE);
                holder.mainSectionIcon.setAlpha(0.4f);
                holder.mainSectionName.setAlpha(0.4f);
            } else {
                holder.smartPluginEdit.setVisibility(View.GONE);
                holder.mainSectionIcon.setAlpha(1f);
                holder.mainSectionName.setAlpha(1f);
            }
        }

        holder.mainSectionName.setText(mData.get(position).getName());
        if (mData.get(position).isLoading()) {
            holder.mainSectionIcon.setImageResource(R.drawable.icon_main_btn_loading);
            Animation operatingAnim = AnimationUtils.loadAnimation(mActivity, R.anim.rotation);
            LinearInterpolator lin = new LinearInterpolator();
            operatingAnim.setInterpolator(lin);
            holder.mainSectionIcon.startAnimation(operatingAnim);
        } else if (mData.get(position).getIsOpen()) {
            holder.mainSectionIcon.clearAnimation();
            holder.mainSectionStatus.setLocalText(mActivity.getResources().getString(R.string.smart_plugin_on));
            holder.mainSectionIcon.setImageResource(R.drawable.icon_main_switch_on);
        } else {
            holder.mainSectionIcon.clearAnimation();
            holder.mainSectionStatus.setLocalText(mActivity.getResources().getString(R.string.smart_plugin_off));
            holder.mainSectionIcon.setImageResource(R.drawable.icon_main_switch_off);
        }

        return convertView;
    }

    public void remove(int index) {
        if (mData != null && index >= 0 && index < mData.size()) {
            mData.remove(index);
            notifyDataSetChanged();
        }
    }

    public void changeName(int index, String name) {
        mData.get(index).setName(name);
        notifyDataSetChanged();
    }

    public boolean isEditMode() {
        return isEditMode;
    }

    public void setEditMode(boolean editMode) {
        isEditMode = editMode;
    }

    static class ViewHolder {
        ImageView mainSectionIcon;
        ImageView smartPluginEdit;
        LocalTextView mainSectionStatus;
        TextView mainSectionName;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            mainSectionIcon = view.findViewById(R.id.main_section_icon);
            smartPluginEdit = view.findViewById(R.id.smart_plugin_edit);
            mainSectionStatus = view.findViewById(R.id.main_section_status);
            mainSectionName = view.findViewById(R.id.main_section_name);
        }
    }
}
