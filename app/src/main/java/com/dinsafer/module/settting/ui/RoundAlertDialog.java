package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalTextView;


/**
 * Created by rinfon on 15/6/26.
 */
public class RoundAlertDialog extends Dialog {

    int layoutRes;//布局文件

    Context mContext;

    LocalTextView mOk;

    RelativeLayout mOkLayout;

    private LocalTextView mContent;

    private boolean isCanCancel = true;

    public RoundAlertDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.round_alert_dialog;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mContent = (LocalTextView) view.findViewById(R.id.round_alert_dialog_content);
        mOk = (LocalTextView) view.findViewById(R.id.round_alert_dialog_ok);
        mOkLayout = (RelativeLayout) view.findViewById(R.id.round_btn_layout);
        if (!builder.isRound1) {
            mOkLayout.setBackground(mContext.getResources().getDrawable(R.drawable.round_alert_btn_rectangle_2));
            mOk.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        }
        mOkLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onOkClick();
                }
            }
        });

        mOkLayout.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()){
                    case MotionEvent.ACTION_DOWN:
                        mOkLayout.setAlpha(0.7f);
                        break;

                    case MotionEvent.ACTION_UP:
                        mOkLayout.setAlpha(1.0f);
                        break;

                    case MotionEvent.ACTION_CANCEL:
                        mOkLayout.setAlpha(1.0f);
                        break;
                }
                return false;
            }
        });


        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOkLayout.setVisibility(View.VISIBLE);
        } else {
            mOkLayout.setVisibility(View.GONE);
        }

        mContent.setLocalText(builder.mContent);
        isCanCancel = builder.isCanCancel;


    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }


    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        if (isCanCancel)
            super.cancel();
    }

    public void setOKText(String ok) {
        mOk.setLocalText(ok);
    }

    public void setOKClick(View.OnClickListener onclick) {
        mOk.setOnClickListener(onclick);
    }

    public void setContent(String content) {

        mContent.setLocalText(content);
    }

    public interface AlertOkClickCallback {

        void onOkClick();
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mOK;

        private boolean isShowOK = false;

        private boolean isCanCancel = true;

        private boolean isAutoDismiss = true;

        private boolean isRound1 = true;

        private AlertOkClickCallback okClick;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setAutoDissmiss(boolean isAutoDismiss) {
            this.isAutoDismiss = isAutoDismiss;
            return this;
        }

        public Builder setCanCancel(boolean isCanCancel) {
            this.isCanCancel = isCanCancel;
            return this;
        }

        public Builder setRound1(boolean isRound1) {
            this.isRound1 = isRound1;
            return this;
        }

        public RoundAlertDialog preBuilder() {
            RoundAlertDialog alertDialog = new RoundAlertDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
