package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SaferSettingLayoutBinding;
import com.dinsafer.model.KnockToSosChange;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DeviceInfoHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/7.
 */
public class SafeSettingFragment extends MyBaseFragment<SaferSettingLayoutBinding> implements IDeviceCallBack {
    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static SafeSettingFragment newInstance() {
        return new SafeSettingFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.safer_setting_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
        EventBus.getDefault().register(this);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.deviceManagementAddress.setOnClickListener(v -> toChooseContacts());
        mBinding.advancedSettingSosSetting.setOnClickListener(v -> toSOSSetting());
        mBinding.advancedSettingSosSettingNor.setOnClickListener(v -> toSOSSetting());
        mBinding.advancedSettingContactid.setOnClickListener(v -> toContactId());
        mBinding.advancedSettingContactidNor.setOnClickListener(v -> toContactId());
        mBinding.advancedSettingCms.setOnClickListener(v -> toCms());
        mBinding.advancedSettingCmsNor.setOnClickListener(v -> toCms());
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.device_management_safe_label));
        mBinding.deviceManagementKnockOver.setLocalText(getResources().getString(R.string.device_managent_knoce_over));
        mBinding.deviceManagementAddress.setLocalText(getResources().getString(R.string.device_managent_contacts));
        mBinding.deviceManagementSosMessage.setLocalText(getResources().getString(R.string.device_managent_sos_message));
        mBinding.advancedSettingSosSetting.setLocalText(getResources().getString(R.string.advanced_setting_sos_setting));
        mBinding.advancedSettingContactid.setLocalText(getResources().getString(R.string.advanced_setting_contactid));
        mBinding.advancedSettingCms.setLocalText(getResources().getString(R.string.advanced_setting_cms));

        String versionCode = DeviceHelper.getString(mPanelDevice, PanelDataKey.Panel.FIRMWARE_VERSION, null);
        updataSOSsetting(versionCode);
        updateContactId();
        updateCms();

        mBinding.deviceManagementSosMessage.setOnClickListener(view -> toSosMessage());

        boolean panelOnline = CommonDataUtil.getInstance().isPanelOnline();
        mBinding.deviceManagementSafeLayout.setVisibility(panelOnline ? View.VISIBLE : View.GONE);
    }

    /**
     * 控制CMS设置是否开启
     */
    private void updateCms() {
//        此分支CMS都是显示的，不需要根据固件版本号判断CMS入口显示状态
//        int visibility = (APIKey.IS_OPEN_CMS && shouldOpenCms()) ? View.VISIBLE : View.GONE;
        int visibility = AppConfig.Functions.SUPPORT_CMS ? View.VISIBLE : View.GONE;
        mBinding.advancedSettingCmsLine.setVisibility(visibility);
        mBinding.advancedSettingCms.setVisibility(visibility);
        mBinding.advancedSettingCmsNor.setVisibility(visibility);

    }

    /**
     * 是否需要开启CMS设置
     * 当硬件版本号大于1.1.0时开启
     */
    public boolean shouldOpenCms() {
        if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo() == null
                || DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getFirmware_version() == null) {
            return false;
        }
        String currenthardwareversion = DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getFirmware_version();
        String tmp[] = currenthardwareversion.split("/");

        // 数组的第一个是固件版本号
        if (TextUtils.isEmpty(tmp[0])) {
            return false;
        }

        // 固件版本号有三位数字组成
        String versionCode[] = tmp[0].split("\\.");
        if (3 != versionCode.length) {
            return false;
        }

        try {
            // 校验固件版本号是否大于1.1.0
            // 校验第一位
            int code;
            code = Integer.parseInt(versionCode[0]);
            if (code > 1) {
                return true;
            } else if (code < 1) {
                return false;
            }

            // 第一位为1，校验第二位
            code = Integer.parseInt(versionCode[1]);
            if (code > 1) {
                return true;
            } else if (code < 1) {
                return false;
            }

            // 第二位为1，校验第三位
            code = Integer.parseInt(versionCode[2]);
            return code > 0;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public void toSosMessage() {
        showLoadingFragment(LoadingFragment.BLUE);
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.getPanelMessage());
    }

    private void updateContactId() {
        if (!APIKey.IS_OPEN_CONTACTID) {
            mBinding.advancedSettingContactid.setVisibility(View.GONE);
            mBinding.advancedSettingContactidLine.setVisibility(View.GONE);
            mBinding.advancedSettingContactidNor.setVisibility(View.GONE);
        } else {
            mBinding.advancedSettingContactid.setVisibility(View.VISIBLE);
            mBinding.advancedSettingContactidLine.setVisibility(View.VISIBLE);
            mBinding.advancedSettingContactidNor.setVisibility(View.VISIBLE);
        }
    }


    private void updataSOSsetting(String hardwareVersion) {
        if (!APIKey.IS_DINSAFER) {
            mBinding.advancedSettingSosSetting.setVisibility(View.GONE);
            mBinding.advancedSettingSosSettingLine.setVisibility(View.GONE);
            mBinding.advancedSettingSosSettingNor.setVisibility(View.GONE);
        } else if (!TextUtils.isEmpty(hardwareVersion)) {
            String[] versions = hardwareVersion.split("/");
            if (DDSystemUtil.VersionComparison(APIKey.LOW_DEVICE_VERSION, versions[0]) == 1) {
                mBinding.advancedSettingSosSetting.setVisibility(View.GONE);
                mBinding.advancedSettingSosSettingLine.setVisibility(View.GONE);
                mBinding.advancedSettingSosSettingNor.setVisibility(View.GONE);
            } else {
                mBinding.advancedSettingSosSetting.setVisibility(View.VISIBLE);
                mBinding.advancedSettingSosSettingLine.setVisibility(View.VISIBLE);
                mBinding.advancedSettingSosSettingNor.setVisibility(View.VISIBLE);
            }
        } else {
            mBinding.advancedSettingSosSetting.setVisibility(View.VISIBLE);
            mBinding.advancedSettingSosSettingLine.setVisibility(View.VISIBLE);
            mBinding.advancedSettingSosSettingNor.setVisibility(View.VISIBLE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(KnockToSosChange ev) {
        DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setKnock_over_to_sos(ev.isKnock());
        mBinding.deviceManagementKnockSwitch.setOn(ev.isKnock());
    }

    public void toChooseContacts() {
        getDelegateActivity().addCommonFragment(ContactsListFragment.newInstance());
    }

    public void toSOSSetting() {
        getDelegateActivity().addCommonFragment(SosSettingFragment.newInstance());
    }

    public void toContactId() {
        getDelegateActivity().addCommonFragment(ContactIdFragment.newInstance());
    }

    public void toCms() {
        getDelegateActivity().addCommonFragment(CmsFragment.newInstance());
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelCmd.GET_PANEL_MESSAGE.equals(cmd) && isSelfOperate) {
            onGetPanelPushMessage(status, map);
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置的推送语言结果
     */
    private void onGetPanelPushMessage(int status, Map map) {
        DDLog.i(TAG, "onGetPanelPushMessage, status: " + status + ", result: " + map);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            closeLoadingFragment();
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        String lang = DeviceHelper.getString(resultMap, PanelDataKey.PushLanguage.LANG, "");
        closeLoadingFragment();
        getDelegateActivity().addCommonFragment(ChangeMessageFragment.newInstance(true, lang));
    }
}
