package com.dinsafer.module.settting.ui;

import android.app.AlertDialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityGenerateNewPwdBinding;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.module_base.base.MyBaseActivity;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.GenerateImageUtils;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import java.text.SimpleDateFormat;
import java.util.Date;

public class ReadCurrentPwdActivity extends MyBaseActivity<ActivityGenerateNewPwdBinding> {
    private Bundle readBundle;


    private Bitmap qrcodeBitmap;

    private String filepath = "";

    @Override
    protected int getContentViewLayoutID() {
        return R.layout.activity_generate_new_pwd;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        setTheme(R.style.ActionSheetStyleiOS7);
        readBundle = getIntent().getExtras();
        if (readBundle != null) {
            qrcodeBitmap = readBundle.getParcelable("bitmap");
            mBinding.shareQrName.setText(readBundle.getString("name"));
            mBinding.shareQrNote.setText(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));


            mBinding.shareQrImageview.setImageBitmap(qrcodeBitmap);


//            navBarLeftBtn.setText(Local.s("Back"));
            mBinding.navBarLeftBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });

//            navBarRightBtn.setText(Local.s("Save"));
            mBinding.navBarRightBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (readBundle.getString("pwd") == null || readBundle.getString("pwd").equals("")) {
                        AlertDialog alert = new AlertDialog.Builder(ReadCurrentPwdActivity.this)
                                .setMessage(Local.s("no password!"))
                                .setNegativeButton(Local.s("Yes"), null)
                                .show();
                        TextView messageText = (TextView) alert.findViewById(android.R.id.message);
                        messageText.setGravity(Gravity.CENTER);
                    } else {

                        ActionSheet.createBuilder(ReadCurrentPwdActivity.this.getApplicationContext(), ReadCurrentPwdActivity.this.getSupportFragmentManager())
                                .setTitle(false)
                                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                                .setOtherButtonTitles(Local.s(getResources().getString(R.string.save_to_album))
                                        , Local.s(getResources().getString(R.string.share_mms)))
                                .setCancelableOnTouchOutside(true)
                                .setListener(new ActionSheet.ActionSheetListener() {
                                    @Override
                                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                                    }

                                    @Override
                                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {

                                        if (index == 0) {
                                            saveQRcodeImage();
                                            AlertDialog alert = new AlertDialog.Builder(ReadCurrentPwdActivity.this)
                                                    .setMessage(Local.s("Saving QR code successed."))
                                                    .setPositiveButton(Local.s("Yes"), null)
                                                    .show();
                                            TextView messageText = (TextView) alert.findViewById(android.R.id.message);
                                            messageText.setGravity(Gravity.CENTER);
                                        } else {
                                            if (TextUtils.isEmpty(filepath)) {
                                                filepath = saveQRcodeImage();
                                            }


                                            Intent intent = new Intent(Intent.ACTION_SEND);
                                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                            intent.putExtra(Intent.EXTRA_STREAM, Uri.parse(filepath));// uri为你的附件的uri
                                            intent.putExtra("subject", ""); //彩信的主题
                                            intent.putExtra("address", ""); //彩信发送目的号码
                                            intent.putExtra("sms_body", ""); //彩信中文字内容
                                            intent.putExtra(Intent.EXTRA_TEXT, "");
                                            intent.setType("image/*");// 彩信附件类型
                                            startActivity(intent);
                                        }

                                    }
                                }).show();
                    }
                }
            });

        }
    }

    public String saveQRcodeImage() {
        RelativeLayout myReadCurrentPwdLinearLayout = (RelativeLayout) ReadCurrentPwdActivity.this.findViewById(R.id.qr_layout);
        // 设置可以截图（webview必须，但是linearlayout 却可以不用）；
        //myReadCurrentPwdLinearLayout.setDrawingCacheEnabled(true);

        //截取该view 的图像；
        Bitmap viewBitmap = GenerateImageUtils.captureView(myReadCurrentPwdLinearLayout);
        //保存图片；
        String filePath = GenerateImageUtils.saveImage(ReadCurrentPwdActivity.this, viewBitmap);
        return filePath;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().post(new CloseActivityEvent());
    }
}
