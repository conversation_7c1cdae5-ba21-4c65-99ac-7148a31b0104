package com.dinsafer.module.settting.ui;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.Local;


/**
 * Created by rinfon on 15/6/26.
 */
public class SosMessageSettingDialog extends Dialog {

    int layoutRes;//布局文件

    Activity mContext;

    LocalTextView mOk, mCancel;

    LocalTextView mTitle;

    LocalTextView mCount;

    LocalTextView mSimHint;

    EditText mInput;

    public SosMessageSettingDialog(Activity context, final Builder builder) {
        super(context, R.style.SosDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.sos_message_dialog;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mOk = (LocalTextView) view.findViewById(R.id.sos_message_dialog_save);
        mCancel = (LocalTextView) view.findViewById(R.id.sos_message_dialog_cancel);
        mCount = (LocalTextView) view.findViewById(R.id.sos_message_dialog_input_count);
        mTitle = (LocalTextView) view.findViewById(R.id.title);
        mSimHint = (LocalTextView) view.findViewById(R.id.sos_message_dialog_nosim_hint);
        mCount.setText(builder.maxLength + "");
        mInput = (EditText) view.findViewById(R.id.sos_message_input);
        mInput.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                return (null != event && event.getKeyCode() == KeyEvent.KEYCODE_ENTER);
            }
        });

        mInput.setText(builder.mInitMessage);
        if (TextUtils.isEmpty(mInput.getText())) {
            mOk.setEnabled(false);
            mOk.setAlpha(0.5f);
        } else {
            mOk.setEnabled(true);
            mOk.setAlpha(1f);
        }
        mInput.setFilters(new InputFilter[]{new InputFilter.LengthFilter(builder.maxLength)});
        mInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                mCount.setText(builder.maxLength - mInput.length() + "");
                if (TextUtils.isEmpty(mInput.getText())) {
                    mOk.setEnabled(false);
                    mOk.setAlpha(0.5f);
                } else {
                    mOk.setEnabled(true);
                    mOk.setAlpha(1f);
                }

            }
        });
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onCancel(SosMessageSettingDialog.this);
                }
            }
        });

        mOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onOkClick(SosMessageSettingDialog.this, mInput.getText().toString());
                }
            }
        });

        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOk.setVisibility(View.VISIBLE);
        } else {
            mOk.setVisibility(View.GONE);
        }

        if (TextUtils.isEmpty(builder.mSimHint)) {
            mSimHint.setVisibility(View.GONE);
        } else {
            mSimHint.setLocalText(builder.mSimHint);
            mSimHint.setVisibility(View.VISIBLE);
        }
        if (builder.isShowCancel) {
            mCancel.setLocalText(builder.mCancel);
            mCancel.setVisibility(View.VISIBLE);
        } else {
            mCancel.setVisibility(View.GONE);
        }

        mInput.setHint(Local.s(mCancel.getResources().getString(R.string.sos_message_dialog_hint)));
        mTitle.setLocalText(mContext.getResources().getString(R.string.sos_message_dialog_title));

    }

    public static Builder createBuilder(Activity context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
//        super.cancel();
    }

    public interface AlertOkClickCallback {

        void onOkClick(SosMessageSettingDialog dialog, String password);

        void onCancel(SosMessageSettingDialog dialog);
    }

    public static class Builder {

        private Activity mContext;

        private String mOK;

        private boolean isShowOK = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isAutoDismiss = true;

        private int maxLength = 140;

        private String mInitMessage;

        private AlertOkClickCallback okClick;

        private String mSimHint;

        public Builder(Activity context) {
            mContext = context;
        }


        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setMaxLength(int length) {
            this.maxLength = length;
            return this;
        }

        public Builder setSimHint(String stirng) {
            this.mSimHint = stirng;
            return this;
        }


        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            isShowCancel = true;
            return this;
        }


        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public Builder setInitMessage(String message) {
            mInitMessage = message;
            return this;
        }

        public SosMessageSettingDialog preBuilder() {
            SosMessageSettingDialog alertDialog = new SosMessageSettingDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
