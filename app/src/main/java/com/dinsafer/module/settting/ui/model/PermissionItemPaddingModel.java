package com.dinsafer.module.settting.ui.model;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPermissionPaddingBinding;

/**
 * 底部边距
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/14 3:03 下午
 */
public class PermissionItemPaddingModel extends PermissionItemModel<ItemPermissionPaddingBinding> {

    @Override
    public int getLayoutID() {
        return R.layout.item_permission_padding;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPermissionPaddingBinding binding) {

    }
}
