package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.text.InputType;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;
import com.jakewharton.rxbinding.view.RxView;
import com.jakewharton.rxbinding.widget.RxTextView;

import rx.Observable;
import rx.functions.Action1;
import rx.functions.Func2;

/**
 * Created by Rinfon on 16/7/12.
 */
public class ModifyPlugsNetWorkFragment extends BaseFragment {

//    static {
//        System.loadLibrary("voiceRecog");
//    }

    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    ImageView commonBarLeftIcon;
    LocalTextView modifyPlugsTitle;
    EditText modifyPlugsSsid;
    EditText modifyPlugsPassword;
    EditText modifyPlugsRePassword;
    LocalCustomButton modifyPlugsSendWave;
    ImageView ipcWifiPasswordIcon;
    ImageView ipcWifiRepasswordIcon;
    CheckBox wifiRemember;


    public static final int OPEN_GPS_SERVICE_CODE = 11;

//    private VoicePlayer player;//声波通讯播放器

    public static ModifyPlugsNetWorkFragment newInstance(String id, boolean isAdd, String data) {
        ModifyPlugsNetWorkFragment modifyPlugsFragment = new ModifyPlugsNetWorkFragment();
        Bundle bundle = new Bundle();
        bundle.putString("id", id);
        bundle.putString("data", data);
        bundle.putBoolean("isAdd", isAdd);
        modifyPlugsFragment.setArguments(bundle);
        return modifyPlugsFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.modify_plugs_network_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.common_bar_left_icon).setOnClickListener( v -> toSave());
        rootView.findViewById(R.id.ipc_wifi_password_icon).setOnClickListener( v -> toShowNextPassword());
        rootView.findViewById(R.id.ipc_wifi_repassword_icon).setOnClickListener( v -> toShowConfirmPassword());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarLeftIcon = rootView.findViewById(R.id.common_bar_left_icon);
        modifyPlugsTitle = rootView.findViewById(R.id.modify_plugs_title);
        modifyPlugsSsid = rootView.findViewById(R.id.modify_plugs_ssid);
        modifyPlugsPassword = rootView.findViewById(R.id.modify_plugs_password);
        modifyPlugsRePassword = rootView.findViewById(R.id.modify_plugs_re_password);
        modifyPlugsSendWave = rootView.findViewById(R.id.modify_plugs_send_wave);
        ipcWifiPasswordIcon = rootView.findViewById(R.id.ipc_wifi_password_icon);
        ipcWifiRepasswordIcon = rootView.findViewById(R.id.ipc_wifi_repassword_icon);
        wifiRemember = rootView.findViewById(R.id.wifi_remember);
    }

    @Override
    public void initData() {
        super.initData();
//        player = new VoicePlayer();
//        player.setFreqs(new int[]{4000, 4200, 4400, 4600, 4800, 5000, 5200, 5400, 5600, 5800, 6000, 6200, 6400, 6600, 6800, 7000, 7200, 7400, 7600});
        commonBarTitle.setLocalText(getResources().getString(R.string.modify_plugs_network_sonic));
        modifyPlugsTitle.setLocalText(getResources().getString(R.string.modify_plugs_network_hint));
        modifyPlugsSsid.setText(DDSystemUtil.getWIFISSID(getDelegateActivity()));
        modifyPlugsPassword.setHint(Local.s(getResources().getString(R.string.ap_step_wifi_pass_hint)));
        modifyPlugsRePassword.setHint(Local.s(getResources().getString(R.string.ap_step_wifi_pass_hint_confirm)));
        modifyPlugsSendWave.setLocalText(getResources().getString(R.string.start));


        modifyPlugsPassword.setInputType(129);
        modifyPlugsRePassword.setInputType(129);
        wifiRemember.setVisibility(View.INVISIBLE);
        wifiRemember.setChecked(true);
        wifiRemember.setText(Local.s(getResources().getString(R.string.remember_password)));

        Observable<CharSequence> ObservablePsw = RxTextView.textChanges(modifyPlugsPassword);
        Observable<CharSequence> ObservablePassword = RxTextView.textChanges(modifyPlugsPassword);

        Observable.combineLatest(ObservablePsw, ObservablePassword, new Func2<CharSequence, CharSequence, Boolean>() {
            @Override
            public Boolean call(CharSequence password, CharSequence password2) {
                return !TextUtils.isEmpty(password.toString())
                        && !TextUtils.isEmpty(password2.toString())
                        && password.toString().equals(password2.toString());
            }
        }).subscribe(new Action1<Boolean>() {
            @Override
            public void call(Boolean aBoolean) {
                if (aBoolean) {
                    modifyPlugsSendWave.setAlpha(1f);
                } else {
                    modifyPlugsSendWave.setAlpha(0.3f);
                }
                RxView.enabled(modifyPlugsSendWave).call(aBoolean);
            }
        });

        if (ContextCompat.checkSelfPermission(getActivity(),
                Manifest.permission.ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            requestLocationPermisiions();
        }
        if (!DDSystemUtil.isOpenGPS(getContext())) {
            toOpenGPS(OPEN_GPS_SERVICE_CODE);
        }
    }

    public void toClose() {
        removeSelf();
    }


    public void toSave() {
        if (TextUtils.isEmpty(modifyPlugsSsid.getText()) || TextUtils.isEmpty(modifyPlugsPassword.getText())
                || TextUtils.isEmpty(modifyPlugsRePassword.getText()) || !modifyPlugsRePassword.getText().toString().equals(modifyPlugsPassword.getText().toString())) {
            return;
        }
        final String ssid = modifyPlugsSsid.getText().toString();
        final String pwd = modifyPlugsPassword.getText().toString();

//        getMainActivity().addCommonFragment(WaveFragment.newInstance(getArguments().getString("id"),
//                ssid, pwd, getArguments().getBoolean("isAdd")));

//        player.play(DataEncoder.encodeSSIDWiFi(ssid, pwd), 1, 200);
    }



    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void toShowNextPassword() {
        if (modifyPlugsPassword.getInputType() == 129) {
            ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_show);
            modifyPlugsPassword.setInputType(InputType.TYPE_CLASS_TEXT);

            //因为现在是一个小眼睛决定两个/三个EditText的可视状态。同时修改光标在最右边
            modifyPlugsRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
            modifyPlugsPassword.setSelection(modifyPlugsPassword.getText().length());
            modifyPlugsRePassword.setSelection(modifyPlugsRePassword.getText().length());
        } else {
            ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_hide);
            modifyPlugsPassword.setInputType(129);
            modifyPlugsRePassword.setInputType(129);

            modifyPlugsPassword.setSelection(modifyPlugsPassword.getText().length());
            modifyPlugsRePassword.setSelection(modifyPlugsRePassword.getText().length());
        }
    }

    public void toShowConfirmPassword() {
        if (modifyPlugsRePassword.getInputType() == 129) {
            ipcWifiRepasswordIcon.setImageResource(R.drawable.icon_form_show);
            modifyPlugsRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            ipcWifiRepasswordIcon.setImageResource(R.drawable.icon_form_hide);
            modifyPlugsRePassword.setInputType(129);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == OPEN_GPS_SERVICE_CODE) {
            int grantResult = grantResults[0];
            boolean granted = grantResult == PackageManager.PERMISSION_GRANTED;
            if (granted) {
                modifyPlugsSsid.setText(DDSystemUtil.getWIFISSID(getDelegateActivity()));
            }
        }
    }
}
