package com.dinsafer.module.settting.ui;

import android.os.Bundle;

import androidx.annotation.Nullable;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.config.LocalKey;
import com.dinsafer.model.ContactItem;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.settting.ui.event.RequestUpdateMemberEvent;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ChoosePushFragment extends BaseFragment {

    LocalTextView commonBarTitle;
    ImageView commonBarBack;
    LocalTextView permissionPushSysText;
    IOSSwitch permissionPushSysSwitch;
    LocalTextView pushSysDescription;
    LocalTextView permissionPushInfoText;
    IOSSwitch permissionPushInfoSwitch;
    LocalTextView pushInfoDescription;
    LocalTextView permissionPushSosText;
    IOSSwitch permissionPushSosSwitch;
    LocalTextView pushSosDescription;

    private ContactItem data;

    public static final int PUSH = 1;
    public static final int SMS = 2;
    public static final int PHONE = 3;
    private int mType;


    public static ChoosePushFragment newInstance(ContactItem dataItem, int type) {
        ChoosePushFragment fragment = new ChoosePushFragment();
//        不能用这种方式传递接口，否则会crash
        Bundle args = new Bundle();
        args.putSerializable("data", dataItem);
        args.putInt("type", type);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        EventBus.getDefault().register(this);
        View rootView = inflater.inflate(R.layout.choose_push_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        permissionPushSysText = rootView.findViewById(R.id.permission_push_sys_text);
        permissionPushSysSwitch = rootView.findViewById(R.id.permission_push_sys_switch);
        pushSysDescription = rootView.findViewById(R.id.push_sys_description);
        permissionPushInfoText = rootView.findViewById(R.id.permission_push_info_text);
        permissionPushInfoSwitch = rootView.findViewById(R.id.permission_push_info_switch);
        pushInfoDescription = rootView.findViewById(R.id.push_info_description);
        permissionPushSosText = rootView.findViewById(R.id.permission_push_sos_text);
        permissionPushSosSwitch = rootView.findViewById(R.id.permission_push_sos_switch);
        pushSosDescription = rootView.findViewById(R.id.push_sos_description);
    }

    @Override
    public void initData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.choose_push_title));
        data = (ContactItem) getArguments().getSerializable("data");
        permissionPushSysText.setLocalText(getResources().getString(R.string.contact_push_call_sys));
        permissionPushSosText.setLocalText(getResources().getString(R.string.contact_push_call_sos));
        permissionPushInfoText.setLocalText(getResources().getString(R.string.contact_push_call_info));
        pushSysDescription.setLocalText(getResources().getString(R.string.contact_sys_description));
        pushSosDescription.setLocalText(getResources().getString(R.string.contact_sos_description));
        pushInfoDescription.setLocalText(getResources().getString(R.string.contact_info_description));
        mType = getArguments().getInt("type");
        refreshUi();
    }

    private void refreshUi() {
        if (mType == PHONE) {
            permissionPushInfoSwitch.setOn(data.isCall_info());
            permissionPushSosSwitch.setOn(data.isCall_sos());
            permissionPushSysSwitch.setOn(data.isCall_sys());
            permissionPushInfoSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setCall_info(isOn);
                    requestUpdateInfo();
                }
            });

            permissionPushSosSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setCall_sos(isOn);
                    requestUpdateInfo();
                }
            });

            permissionPushSysSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setCall_sys(isOn);
                    requestUpdateInfo();
                }
            });
        } else if (mType == SMS) {
            permissionPushInfoSwitch.setOn(data.isSms_info());
            permissionPushSosSwitch.setOn(data.isSms_sos());
            permissionPushSysSwitch.setOn(data.isSms_sys());
            permissionPushInfoSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setSms_info(isOn);
                    requestUpdateInfo();
                }
            });

            permissionPushSosSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setSms_sos(isOn);
                    requestUpdateInfo();
                }
            });

            permissionPushSysSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setSms_sys(isOn);
                    requestUpdateInfo();
                }
            });
        } else {
            permissionPushInfoSwitch.setOn(data.isPush_info());
            permissionPushSosSwitch.setOn(data.isPush_sos());
            permissionPushSysSwitch.setOn(data.isPush_sys());
            permissionPushInfoSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setPush_info(isOn);
                    requestUpdateInfo();
                }
            });

            permissionPushSosSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setPush_sos(isOn);
                    requestUpdateInfo();
                }
            });

            permissionPushSysSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
                @Override
                public void onStateSwitched(boolean isOn) {
                    data.setPush_sys(isOn);
                    requestUpdateInfo();
                }
            });
        }

        if (data.getPermission() == LocalKey.GUEST) {
            permissionPushSysText.setAlpha(0.5f);
            permissionPushSysSwitch.setAlpha(0.5f);
            permissionPushSysSwitch.setEnabled(false);
            permissionPushSysSwitch.setOn(false);
        }

        boolean hasPanelDevice = CommonDataUtil.getInstance().isHadPanel();
        pushSosDescription.setVisibility(hasPanelDevice ? View.VISIBLE : View.GONE);
        pushSysDescription.setVisibility(hasPanelDevice ? View.VISIBLE : View.GONE);
        pushInfoDescription.setVisibility(hasPanelDevice ? View.VISIBLE : View.GONE);
    }

    private void requestUpdateInfo() {
        EventBus.getDefault().post(new RequestUpdateMemberEvent());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(RequestUpdateMemberEvent event) {
        if (event.isRequest()) {
            return;
        }

        if (event.isOnResultSuccess()) {
            return;
        }

        // 失败后需要还原界面
        refreshUi();
    }

    public void close() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

}

