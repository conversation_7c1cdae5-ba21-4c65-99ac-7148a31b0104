package com.dinsafer.module.settting.adapter;

import android.graphics.drawable.Drawable;
import androidx.core.content.ContextCompat;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ChoosePhoneZoneItem2Binding;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * 区号设置Item数据封装类
 *
 * <AUTHOR>
 * @date 2020-03-25 10:37
 */
public class AreaCodeItemModel implements BaseBindModel<ChoosePhoneZoneItem2Binding> {

    private boolean isSelected = false;

    private String code;
    private String name;

    private String groupTittle;
    private boolean showGroupTittle = false;

    public AreaCodeItemModel(String code, String name, boolean isSelected) {
        this.code = code;
        this.name = name;
        this.isSelected = isSelected;
    }

    public void setGroupTittle(String groupTittle) {
        this.groupTittle = groupTittle;
    }

    public void setShowGroupTittle(boolean showGroupTittle) {
        this.showGroupTittle = showGroupTittle;
    }

    @Override
    public int getLayoutID() {
        return R.layout.choose_phone_zone_item2;
    }

    @Override
    public void onDo(View v) {
    }

    @Override
    public void convert(BaseViewHolder holder, ChoosePhoneZoneItem2Binding binding) {
        binding.choosePhoneZoneText.setText(code + " " + name);

        if (isSelected) {
            Drawable drawable = binding.getRoot().getContext().getResources()
                    .getDrawable(R.drawable.btn_define_setting_select);
            drawable.setTint(ContextCompat.getColor(binding.getRoot().getContext(), R.color.color_brand_primary));
            binding.choosePhoneZoneText.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null);
        } else {
            binding.choosePhoneZoneText.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        }

        if (showGroupTittle) {
            binding.tvGroupTittle.setVisibility(View.VISIBLE);
            binding.tvGroupTittle.setText(null == groupTittle ? "" : groupTittle.toUpperCase());
        } else {
            binding.tvGroupTittle.setVisibility(View.GONE);
        }
    }

    public boolean isSelected() {
        return isSelected;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public void setChecked(boolean isSelected) {
        this.isSelected = isSelected;
    }

}
