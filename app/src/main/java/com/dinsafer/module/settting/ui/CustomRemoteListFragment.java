package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.CustomRemoteLayoutBinding;
import com.dinsafer.model.CustomizeSmartPlugInfo;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.adapter.CustomRemoteAdapter;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by Rinfon on 16/7/8.
 */
public class CustomRemoteListFragment extends MyBaseFragment<CustomRemoteLayoutBinding>
        implements IDeviceCallBack {
    private CustomRemoteAdapter customRemoteAdapter;
    private ArrayList<CustomizeSmartPlugInfo> mDatas;

    private CustomizeSmartPlugInfo mCustomizePlugInfo;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    /**
     * 3A类型的五键遥控，需要在页面内请求数据
     */
    public static CustomRemoteListFragment newInstance() {
        return new CustomRemoteListFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.custom_remote_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBarLeft.setOnClickListener(v -> toSave());
        findPanel();
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            showErrorToast();
            removeSelf();
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        mPanelDevice.submit(PanelParamsHelper.getCustomizeSmartPlugs());
    }

    public void toSave() {
        if (null != mPanelDevice) {
            showTimeOutLoadinFramgment();

            String plugId = customRemoteAdapter.getSelectId();
            String sendid = customRemoteAdapter.getmSendId();
            isSelfOperate = true;
            mPanelDevice.submit(PanelParamsHelper.saveCustomizeSmartPlugs(plugId, sendid));
        } else {
            DDLog.e(TAG, "Empty panel for save customize remote control.");
            showErrorToast();
        }
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.custom_remote_title));
        mBinding.customRemoteHint.setLocalText(getResources().getString(R.string.custom_remote_hint));
        mDatas = new ArrayList<>();
        customRemoteAdapter = new CustomRemoteAdapter(getDelegateActivity(), mDatas);
        mBinding.customRemoteListview.setAdapter(customRemoteAdapter);
    }

    private void updateViewStatus() {
        DDLog.i(TAG, "updateViewStatus");
        if (mDatas == null || mDatas.size() <= 0) {
            mBinding.customRemoteHint.setLocalText(getResources().getString(R.string.custom_remote_hint_no));
            mBinding.commonBarLeft.setVisibility(View.INVISIBLE);
        } else {
            mBinding.customRemoteHint.setLocalText(getResources().getString(R.string.custom_remote_hint));
            if (null != customRemoteAdapter) {
                customRemoteAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    /**
     * 处理获取到的智能插座数据
     */
    private void processCustomizeSmartPlugResponse(String result) {
        DDLog.i(TAG, "processCustomizeSmartPlugResponse");
        try {
            JSONObject resultJson = new JSONObject(result);
            DDLog.d("NET", resultJson.toString());

            // 当前设置的插座信息
            JSONObject customizeRemoteControlData = DDJSONUtil.getJSONObject(resultJson, "customize_remote_control_data");
            if (null != customizeRemoteControlData) {
                mCustomizePlugInfo = CustomizeSmartPlugInfo.newBuilder()
                        .withId(DDJSONUtil.getString(customizeRemoteControlData, "pluginid"))
                        .withDtype(DDJSONUtil.getInt(customizeRemoteControlData, "dtype"))
                        .withSendid(DDJSONUtil.getString(customizeRemoteControlData, "sendid"))
                        .withStype(DDJSONUtil.getString(customizeRemoteControlData, "stype"))
                        .build();
            }

            mDatas.clear();

            // 旧的插座信息
            JSONArray datas = DDJSONUtil.getJSONarray(resultJson, "datas");
            if (null != datas && 0 < datas.length()) {
                JSONObject oldPlug;
                CustomizeSmartPlugInfo oldPlugInfo;
                for (int i = 0; i < datas.length(); i++) {
                    oldPlug = (JSONObject) datas.get(i);
                    oldPlugInfo = CustomizeSmartPlugInfo.newBuilder()
                            .withId(DDJSONUtil.getString(oldPlug, "id"))
                            .withName(DDJSONUtil.getString(oldPlug, "name"))
                            .build();
                    mDatas.add(oldPlugInfo);
                }
            }

            // 新的ASK插座信息
            JSONObject newaskdatas = DDJSONUtil.getJSONObject(resultJson, "newaskdatas");
            if (null != newaskdatas) {
                Iterator<String> newAskPlugStypeKeys = newaskdatas.keys();
                String stypeKey;
                JSONArray stypePlugs;
                JSONObject stypePlug;
                CustomizeSmartPlugInfo askPlugInfo;
                while (newAskPlugStypeKeys.hasNext()) {
                    stypeKey = newAskPlugStypeKeys.next();
                    stypePlugs = DDJSONUtil.getJSONarray(newaskdatas, stypeKey);
                    if (null != stypePlugs && 0 < stypePlugs.length()) {
                        for (int i = 0; i < stypePlugs.length(); i++) {
                            stypePlug = (JSONObject) stypePlugs.get(i);
                            askPlugInfo = CustomizeSmartPlugInfo.newBuilder()
                                    .withId(DDJSONUtil.getString(stypePlug, "id"))
                                    .withName(DDJSONUtil.getString(stypePlug, "name"))
                                    .withSendid(DDJSONUtil.getString(stypePlug, "sendid"))
                                    .build();
                            mDatas.add(askPlugInfo);
                        }
                    }
                }
            }

            // Adapter中初始化默认选中的插座
            if (null != mCustomizePlugInfo
                    && !TextUtils.isEmpty(mCustomizePlugInfo.getId())) {
                customRemoteAdapter.setSelectAndSendId(mCustomizePlugInfo.getId(), mCustomizePlugInfo.getSendid());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "processCustomizeSmartPlugResponse: ERROR.");
            e.printStackTrace();
        }

        // 更新界面
        updateViewStatus();
        closeTimeOutLoadinFramgmentWithErrorAlert();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice
                || !deviceId.equals(mPanelDevice.getId())) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (PanelCmd.GET_CUSTOMIZE_SMART_PLUGS.equals(cmd)) {
            onGetCustomizePlugs(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SAVE_CUSTOMIZE_SMART_PLUGS.equals(cmd)) {
                // 设置延时布防信息
                onSaveCustomizePlugs(status, map);
            }
            isSelfOperate = false;
        }
    }

    private void onSaveCustomizePlugs(int status, Map<String, Object> map) {
        DDLog.i(TAG, "onSaveCustomizePlugs, MAP: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            DDLog.i(TAG, "Send cmd: CUSTOMIZE_REMOTE_CONTROL, result: SUCCESS.");
            removeSelf();
        } else {
            DDLog.e(TAG, "Send cmd: CUSTOMIZE_REMOTE_CONTROL, result: Error.");
            showErrorToast();
        }
    }

    private void onGetCustomizePlugs(int status, Map<String, Object> map) {
        DDLog.i(TAG, "onGetCustomizePlugs, MAP: " + map);
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
            processCustomizeSmartPlugResponse(result);
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            updateViewStatus();
        }
    }
}

