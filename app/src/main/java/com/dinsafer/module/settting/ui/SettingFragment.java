package com.dinsafer.module.settting.ui;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.FamilyInfoUpdateEvent;
import com.dinsafer.model.FamilySwitchEvent;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.model.TabEntity;
import com.dinsafer.model.UserPermissonUpdata;
import com.dinsafer.model.event.DeviceDeletedStateChangedEvent;
import com.dinsafer.model.event.GetDeviceInfoEvent;
import com.dinsafer.model.event.UpdateFamilyPanelStateEvent;
import com.dinsafer.model.event.WidgetFlagDeletedEvent;
import com.dinsafer.model.family.FamilyListChangeEvent;
import com.dinsafer.model.family.FetchHomeListEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module.main.adapter.CommonNonSaveStatePagerAdapter;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.event.BmtListUpdateEvent;
import com.dinsafer.module.settting.ui.event.ShowDebugModeEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.MainFragmentViewPager;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.SettingInfoHelper;
import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.flyco.tablayout.listener.OnTabSelectListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class SettingFragment extends BaseFragment {
    private static final int COUNT_ENTER_DEBUG_MODE = 8; // 连续点击进入debug mode的次数
    private static final long DURATION_CLICK_TIME = 500; // 连续点击时间间隔

    ImageView settingBack;
    ImageView settingUser;
    CommonTabLayout settingTab;
    MainFragmentViewPager setttingViewpager;

    private ArrayList<CustomTabEntity> mTabEntities;

    private CommonPagerAdapter mAdapter;

    /**
     * 记录当前tab的下标，当按home键后再返回时，恢复之前的位置
     */
    private int mCurrentIndex = 0;

    private long lastClickTimeMillis = 0;
    private int clickCount = 0;

    public static SettingFragment newInstance() {
        return new SettingFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.settting_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.setting_back).setOnClickListener( v -> toBack());
        rootView.findViewById(R.id.setting_user).setOnClickListener( v -> toUser());
    }

    private void __bindViews(View rootView) {
        settingBack = rootView.findViewById(R.id.setting_back);
        settingUser = rootView.findViewById(R.id.setting_user);
        settingTab = rootView.findViewById(R.id.setting_tab);
        setttingViewpager = rootView.findViewById(R.id.settting_viewpager);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void initData() {
        super.initData();
        mTabEntities = new ArrayList<CustomTabEntity>();
        settingTab.setTextsize(14);
        updataUI();

        ArrayList<BaseFragment> fragmentList = new ArrayList<BaseFragment>();
        fragmentList.add(DeviceSettingFragment.newInstance());
        fragmentList.add(AppSettingFragment.newInstance());
        mAdapter = new CommonNonSaveStatePagerAdapter(getChildFragmentManager(), fragmentList);
        setttingViewpager.setAdapter(mAdapter);
        updateViewPager();

        settingTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                setttingViewpager.setCurrentItem(position);
                mCurrentIndex = position;
            }

            @Override
            public void onTabReselect(int position) {
                onTabReClick(position);
            }
        });

        setttingViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                i("null?:" + settingTab.getTabCount() + ",position:" + position);
                try {
                    if (position < 0 || position >= settingTab.getTabCount()) {
                        settingTab.setCurrentTab(0);
                    } else {
                        settingTab.setCurrentTab(position);
                        mCurrentIndex = position;
                    }
                } catch (Exception ex) {
                    DDLog.e(TAG, "Error....");
                    ex.printStackTrace();
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    private void onTabReClick(final int selectIndex) {
        final int totalCount = mTabEntities.size();
        if (selectIndex < 0 || selectIndex >= totalCount) {
            return;
        }

        // 点击最后一个，也就是AppSetting
        if (selectIndex == totalCount - 1) {
            final long currentTime = System.currentTimeMillis();
            if (currentTime - lastClickTimeMillis > DURATION_CLICK_TIME) {
                clickCount = 1;
            } else {
                clickCount++;
            }
            lastClickTimeMillis = currentTime;

            if (clickCount >= COUNT_ENTER_DEBUG_MODE) {
                clickCount = 0;
                EventBus.getDefault().post(new ShowDebugModeEvent());
            }
        }
    }


    public void toBack() {
        getMainActivity().smoothToHome();
    }

    public void toUser() {
        getMainActivity().smoothToUser();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        updataUI();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(UpdateFamilyPanelStateEvent ev) {
        updataTabData();
    }

    /**
     * 更新Tab
     */
    private void updataUI() {
        ArrayList<String> tabTittles = new ArrayList<>();
        i("SettingInfoHelper.getInstance().showDeviceAndAppSetting(): "
                + SettingInfoHelper.getInstance().showDeviceAndAppSetting());
        if (SettingInfoHelper.getInstance().showDeviceAndAppSetting()) {
            tabTittles.add(Local.s(getString(R.string.family_setting)));
        }
        tabTittles.add(Local.s(getString(R.string.app_setting)));

        mTabEntities.clear();
        for (String mTitle : tabTittles) {
            mTabEntities.add(new TabEntity(mTitle));
        }
        settingTab.setTabData(mTabEntities);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(GetDeviceInfoEvent ev) {
        updataTabData();
    }

    /**
     * 更新Tab和ViewPager
     */
    private synchronized void updataTabData() {
        updataUI();
        updateViewPager();
    }

    /**
     * 更新ViewPager页面和可滑动的状态
     */
    private synchronized void updateViewPager() {
        if (SettingInfoHelper.getInstance().showDeviceAndAppSetting()) {
            settingTab.setCurrentTab(mCurrentIndex);
            setttingViewpager.setCurrentItem(mCurrentIndex);
            setttingViewpager.setCanSlide(true);
        } else {
            if (settingTab.getTabCount() == 1) {
                settingTab.setCurrentTab(0);
            }
            setttingViewpager.setCurrentItem(1);
            setttingViewpager.setCanSlide(false);
            mCurrentIndex = 0;
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            Device device = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID());
            if (APIKey.IS_OPEN_PLUGIN
                    && null != device
                    && !DeviceHelper.getBoolean(device, PanelDataKey.Panel.UPGRADING, false)
                    && CommonDataUtil.getInstance().isPanelOnline()
                    && TextUtils.isEmpty(DeviceHelper.getString(device, PanelDataKey.Panel.TIMEZONE, null))) {
                //  设置时区
                // String[] currentVersions = DeviceHelper.getString(device, PanelDataKey.Panel.FIRMWARE_VERSION, "").split("/");
                // if (currentVersions.length >= 1 && CommonDataUtil.getInstance().checkShowPlugin(currentVersions[0])) {

                AlertDialog.createBuilder(getDelegateActivity())
                        .setOk(getResources().getString(R.string.timezone_setting_go))
                        .setContent(getResources().getString(R.string.timezone_setting_content))
                        .setCanCancel(false)
                        .setAutoDissmiss(true)
                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                getMainActivity().addCommonFragment(TimeZoneFragment.newInstance(false));
                            }
                        })
                        .preBuilder()
                        .show();
                // }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(UserPermissonUpdata ev) {
        updataTabData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(FamilyListChangeEvent ev) {
        i("FamilyListChangeEvent: updata");
        updataTabData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(FetchHomeListEvent ev) {
        i("FetchHomeListEvent: updata");
        updataTabData();
    }

    @Subscribe
    public void onEvent(FamilySwitchEvent event) {
        i("FamilySwitchEvent: updata");
        updataTabData();
    }

    @Subscribe
    public void onEvent(FamilyInfoUpdateEvent event) {
        i("FamilyInfoUpdateEvent: updata");
        updataTabData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIPCListUpdateEvent(IPCListUpdateEvent event) {
        updataTabData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onBmtListUpdateEvent(BmtListUpdateEvent event) {
        updataTabData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(DeviceDeletedStateChangedEvent event) {
        updataTabData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(WidgetFlagDeletedEvent event) {
        updataTabData();
    }
}
