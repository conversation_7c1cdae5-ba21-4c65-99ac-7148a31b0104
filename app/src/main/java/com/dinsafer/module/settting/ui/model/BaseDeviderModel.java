package com.dinsafer.module.settting.ui.model;

import android.view.View;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemDeviceSettingDevicerBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.rv.BindModel;

public class BaseDeviderModel extends BindModel<ItemDeviceSettingDevicerBinding> {

    private int leftMargin = 0;
    private int rightMargin = 0;
    private BaseFragment baseFragment;

    public BaseDeviderModel(BaseFragment baseFragment) {
        super(baseFragment.getContext());
        this.baseFragment = baseFragment;
    }

    public BaseDeviderModel(BaseFragment baseFragment, int leftMargin, int rightMargin) {
        super(baseFragment.getContext());
        this.baseFragment = baseFragment;
        this.leftMargin = leftMargin;
        this.rightMargin = rightMargin;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_device_setting_devicer;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemDeviceSettingDevicerBinding itemDeviceSettingDevicerBinding) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) itemDeviceSettingDevicerBinding.divider.getLayoutParams();
        layoutParams.leftMargin = leftMargin;
        layoutParams.rightMargin = rightMargin;
        itemDeviceSettingDevicerBinding.divider.setLayoutParams(layoutParams);
    }

}
