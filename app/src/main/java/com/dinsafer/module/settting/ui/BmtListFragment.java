package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.HomeManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.common.IDeviceStatusListener;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.BmtListLayoutBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DeviceRequestType;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.model.TuyaItemPlus;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.main.model.BaseMainItemModel;
import com.dinsafer.module.main.model.MainBmtV2Model;
import com.dinsafer.module.main.model.MainPowerPulseCurrentModel;
import com.dinsafer.module.main.model.MainPowerStoreCurrentModel;
import com.dinsafer.module.main.model.OnWidgetItemListener;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtListUpdateEvent;
import com.dinsafer.module.powerstation.event.HomePowerStationDefaultEvent;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PluginWidgetStyleUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @describe：
 * @date：2022/12/21
 * @author: create by Sydnee
 */
public class BmtListFragment extends MyBaseFragment<BmtListLayoutBinding> implements IDeviceCallBack, IDeviceStatusListener {

    private ArrayList<Device> mDeviceList;
    private ArrayList<BaseMainItemModel> mData;
    private BindMultiAdapter<BaseMainItemModel> mAdapter;
    private String mSubcategory;


    public static BmtListFragment newInstance(String deviceType) {
        BmtListFragment fragment = new BmtListFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_TYPE, deviceType);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.bmt_list_layout;
    }


    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mSubcategory = bundle.getString(PSKeyConstant.KEY_DEVICE_TYPE);
        }
        EventBus.getDefault().register(this);
        showBlueTimeOutLoadinFramgment();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.power_station));
        mBinding.listviewEmpty.setLocalText(getResources().getString(R.string.listview_empty));
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
    }


    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        createBmtList();
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        if (CollectionUtil.isListNotEmpty(mData)) {
            for (BaseMainItemModel model : mData) {
                if (model instanceof MainBmtV2Model) {
                    ((MainBmtV2Model) model).release();
                }
                if (model instanceof MainPowerStoreCurrentModel) {
                    ((MainPowerStoreCurrentModel) model).release();
                }
                if (model instanceof MainPowerPulseCurrentModel) {
                    ((MainPowerPulseCurrentModel) model).release();
                }
            }
        }
        super.onDestroyView();
        clearData();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(cmd)) {
            return;
        }

        runOnMainThread(() -> {
            switch (cmd) {
                case DinConst.CMD_SET_NAME:
                    for (Device device : mDeviceList) {
                        if (deviceId.equals(device.getId())
                                && subCategory.equals(device.getSubCategory())) {
                            mAdapter.notifyDataSetChanged();
                            break;
                        }
                    }
                    break;
            }
        });
    }

    @Override
    public void online(String s, String subCategory) {

    }

    @Override
    public void offline(String s, String subCategory, String s1) {

    }

    @Override
    public void onInfoUpdate(String id, String subCategory, int type) {

    }

    private void createBmtList() {
        initRecyclerView();
        reload();
    }

    private void initRecyclerView() {
        mDeviceList = new ArrayList<>();
        mData = new ArrayList<>();
        mAdapter = new BindMultiAdapter<>();
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        mBinding.bmtList.setLayoutManager(linearLayoutManager);
        mBinding.bmtList.setAdapter(mAdapter);
        mAdapter.setNewData(mData);
    }

    private void clearData() {
        if (mDeviceList != null && mDeviceList.size() > 0) {
            for (Device device : mDeviceList) {
                device.unregisterDeviceCallBack(this);
                device.unregisterDeviceStatusListener(this);
            }
            mDeviceList.clear();
        }
        if (mData != null) {
            mData.clear();
        }
    }

    private void reload() {
        clearData();
        BmtManager.getInstance().fetchBmtDevice(DeviceRequestType.ALL_DEVICE, new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                createPluginList();
                closeLoadingFragment();
            }

            @Override
            public void onError(int i, String s) {
                createPluginList();
                closeLoadingFragment();
            }
        });
    }

    private void createPluginList() {
        clearData();

        mDeviceList.clear();
        List<Device> allBmtDevice = BmtManager.getInstance().getAllBmtDevice();
        if (allBmtDevice.size() > 0) {
            for (Device device : allBmtDevice) {
                if (device.getFlagDeleted()) {
                    continue;
                }
                String devSubCategory = device.getSubCategory();
                if (!TextUtils.isEmpty(devSubCategory) && !TextUtils.isEmpty(mSubcategory)
                        && devSubCategory.equals(mSubcategory)) {
                    mDeviceList.add(device);
                }
            }
        }
        for (int i = 0; i < mDeviceList.size(); i++) {
            String devSubCategory = mDeviceList.get(i).getSubCategory();
            if (TextUtils.isEmpty(devSubCategory)) continue;
            OnWidgetItemListener widgetItemListener = new OnWidgetItemListener() {
                @Override
                public void onItemClick(boolean isEditMode, int sectionType, TuyaItemPlus tuyaItemPlus, @NonNull String modelId, @NonNull String modelType) {

                }

                @Override
                public void onBmtOrBatteryItemClick(boolean isEditMode, int sectionType, int bmtOrBattery, Device device, String name) {
                    if (HomeManager.getInstance().isAdmin()) {
                        getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstance(name, device.getId(), device.getSubCategory(), bmtOrBattery));
                    }
                }

                @Override
                public void onItemLongClick(boolean isEditMode, RecyclerView.ViewHolder viewHolder, int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType) {

                }

                @Override
                public void onDeleteIconClick(int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType) {

                }

                @Override
                public void onUnavailableStateViewClick(int position, @NonNull final MainWidgetBean widgetBean, @NonNull String modelId, @NonNull String modelType) {

                }

            };
            if (devSubCategory.equals(DinConst.TYPE_BMT_HP5000)
                    || devSubCategory.equals(DinConst.TYPE_BMT_POWERCORE30)
                    || devSubCategory.equals(DinConst.TYPE_BMT_POWERCORE20)) {
                MainBmtV2Model model = new MainBmtV2Model(BmtListFragment.this, mDeviceList.get(i)
                        , new MainWidgetBean(mDeviceList.get(i), PluginWidgetStyleUtil.BMT, MainPanelHelper.SECTION_TYPE_BMT)
                        , widgetItemListener, i);
                mData.add(model);
                mDeviceList.get(i).registerDeviceStatusListener(BmtListFragment.this);
                mDeviceList.get(i).registerDeviceCallBack(BmtListFragment.this);
            } else if (devSubCategory.equals(DinConst.TYPE_BMT_POWERSTORE)) {
                MainPowerStoreCurrentModel powerStoreCurrentModel = new MainPowerStoreCurrentModel(BmtListFragment.this, mDeviceList.get(i),
                        new MainWidgetBean(mDeviceList.get(i), PluginWidgetStyleUtil.POWER_STORE_CURRENT, MainPanelHelper.SECTION_TYPE_POWER_STORE),
                        widgetItemListener, i);
                mData.add(powerStoreCurrentModel);
                mDeviceList.get(i).registerDeviceStatusListener(BmtListFragment.this);
                mDeviceList.get(i).registerDeviceCallBack(BmtListFragment.this);
            } else if (devSubCategory.equals(DinConst.TYPE_BMT_POWERPULSE)) {
                MainPowerPulseCurrentModel powerPulseCurrentModel = new MainPowerPulseCurrentModel(BmtListFragment.this, mDeviceList.get(i),
                        new MainWidgetBean(mDeviceList.get(i), PluginWidgetStyleUtil.POWER_PULSE_CURRENT, MainPanelHelper.SECTION_TYPE_POWER_PULSE),
                        widgetItemListener, i);
                mData.add(powerPulseCurrentModel);
                mDeviceList.get(i).registerDeviceStatusListener(BmtListFragment.this);
                mDeviceList.get(i).registerDeviceCallBack(BmtListFragment.this);
            }
        }
        i("reload end");
        updateEmptyViewVisibleState();
        mAdapter.notifyDataSetChanged();

        for (Device device : mDeviceList) {
            if (BmtUtil.isDeviceConnected(device)) {
                final Map<String, Object> map = new HashMap<>();
                BmtManager.mIndexMap.put(device.getId(), 0);
                map.put(PSKeyConstant.CMD, DsCamCmd.GET_INVERTER_INFO);
                map.put(PSKeyConstant.INDEX, 0);
                device.submit(map);
                map.clear();

                int iotVersionStatus = BmtUtil.getIotVersionGETargetVersion(device, "1.8.0");
                if (iotVersionStatus == 0) {
                    map.put(PSKeyConstant.CMD, BmtCmd.GET_MODE);
                } else {
                    map.put(PSKeyConstant.CMD, BmtCmd.GET_MODE_V2);
                }
                map.put(PSKeyConstant.CMD, BmtCmd.GET_MODE_V2);
                device.submit(map);
                map.clear();
                map.put(PSKeyConstant.CMD, DsCamCmd.GET_INVERTER_INPUT_INFO);
                device.submit(map);
                map.clear();
                map.put(PSKeyConstant.CMD, DsCamCmd.GET_COMMUNICATE_SIGNAL);
                device.submit(map);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtListUpdateEvent ev) {
        if (BmtListUpdateEvent.OPERATION_ADD == ev.getOperationType()
                || BmtListUpdateEvent.OPERATION_DELETE == ev.getOperationType()) {
            createPluginList();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        DDLog.i(TAG, "BmtDeviceStatusChange-" + event.toString());
        String deviceID = event.getDeviceID();
        String sub = event.getSubcategory();
        if (TextUtils.isEmpty(deviceID) || TextUtils.isEmpty(sub)) {
            return;
        }
        for (int i = 0; i < mDeviceList.size(); i++) {
            if (deviceID.equals(mDeviceList.get(i).getId()) && sub.equals(mDeviceList.get(i).getSubCategory())) {
                BaseMainItemModel model = mData.get(i);
                if (model instanceof MainBmtV2Model) {
                    ((MainBmtV2Model) model).updateBmtStatus(event);
                }
                if (model instanceof MainPowerStoreCurrentModel) {
                    ((MainPowerStoreCurrentModel) model).updateBmtStatus(event);
                }
                if (model instanceof MainPowerPulseCurrentModel) {
                    ((MainPowerPulseCurrentModel) model).updateBmtStatus(event);
                }
                break;
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGraphicUpdateEvent event) {
        DDLog.i(TAG, "BmtCurrentUpdateEvent-" + event.toString());
        String deviceID = event.getDeviceId();
        if (TextUtils.isEmpty(deviceID)) {
            return;
        }
        for (int i = 0; i < mDeviceList.size(); i++) {
            if (deviceID.equals(mDeviceList.get(i).getId()) && event.getSubCategory().equals(mDeviceList.get(i).getSubCategory())) {
                BaseMainItemModel model = mData.get(i);
                if (model instanceof MainBmtV2Model) {
                    ((MainBmtV2Model) model).updateBmtCurrent(event);
                }
                if (model instanceof MainPowerStoreCurrentModel) {
                    ((MainPowerStoreCurrentModel) model).updateBmtCurrent(event);
                }
                if (model instanceof MainPowerPulseCurrentModel) {
                    ((MainPowerPulseCurrentModel) model).updateBmtCurrent(event);
                }
                break;
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMainHomePowerStation(HomePowerStationDefaultEvent event) {
        String deviceId = event.getDeviceId();
        String subcategory = event.getSubcategory();
        if (CollectionUtil.isListNotEmpty(mData) && !TextUtils.isEmpty(deviceId)) {
            for (BaseMainItemModel model : mData) {
                if (model instanceof MainBmtV2Model) {
                    String dId = ((MainBmtV2Model) model).getDeviceId();
                    String dSub = ((MainBmtV2Model) model).getWidgetBean().getSubCategory();
                    if (!TextUtils.isEmpty(dId) && deviceId.equals(dId) && subcategory.equals(dSub)) {
                        ((MainBmtV2Model) model).setPowerStationDefaultView();
                        break;
                    }
                }
                if (model instanceof MainPowerStoreCurrentModel) {
                    String dId = ((MainPowerStoreCurrentModel) model).getDeviceId();
                    String dSub = ((MainPowerStoreCurrentModel) model).getWidgetBean().getSubCategory();
                    if (!TextUtils.isEmpty(dId) && deviceId.equals(dId) && subcategory.equals(dSub)) {
                        ((MainPowerStoreCurrentModel) model).setPowerStationDefaultView();
                        break;
                    }
                }

                if (model instanceof MainPowerPulseCurrentModel) {
                    String dId = ((MainPowerPulseCurrentModel) model).getDeviceId();
                    String dSub = ((MainPowerPulseCurrentModel) model).getWidgetBean().getSubCategory();
                    if (!TextUtils.isEmpty(dId) && deviceId.equals(dId) && subcategory.equals(dSub)) {
                        ((MainPowerPulseCurrentModel) model).setPowerStationDefaultView();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 判断数据是否为空
     */
    private void updateEmptyViewVisibleState() {
        boolean isEmpty = null == mData || 0 >= mData.size();
        mBinding.listviewEmpty.setVisibility(isEmpty ? View.VISIBLE : View.GONE);
        mBinding.bmtList.setVisibility(isEmpty ? View.GONE : View.VISIBLE);
    }


}
