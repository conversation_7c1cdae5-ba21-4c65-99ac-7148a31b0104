package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SelfTestLayoutBinding;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.model.SelfTestEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.PingUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

/**
 * Created by Rinfon on 16/7/8.
 */
public class SelfTestFragment extends MyBaseFragment<SelfTestLayoutBinding> {

    public static SelfTestFragment newInstance() {
        return new SelfTestFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.self_test_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.selfStart.setOnClickListener(v -> toStartTest());
        mBinding.changeIp.setOnClickListener(v -> toChangeIp());
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        mBinding.commonTitleBar.commonBarTitle.setLocalText("自测试程序");
    }

    public void toStartTest() {
        toPing();
        toArm(SelfTestEvent.ARM_TASK);
    }

    public void toPing() {
        mBinding.selfTestPing.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        mBinding.selfTestPing.setVisibility(View.VISIBLE);
        int time = PingUtil.getAvgRTT(APIKey.SERVER_IP);
        if (time == -1 || time > 200) {
            mBinding.selfTestPing.setLocalText("网络不正常：" + time);
            mBinding.selfTestPing.setCompoundDrawablesWithIntrinsicBounds(null, null,
                    getResources().getDrawable(R.drawable.icon_user_setting_warning), null);
        } else {
            mBinding.selfTestPing.setLocalText("网络正常：" + time);
            mBinding.selfTestPing.setCompoundDrawablesWithIntrinsicBounds(null, null,
                    getResources().getDrawable(R.drawable.btn_user_setting_select), null);
        }

        mBinding.selfTestDns.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        mBinding.selfTestDns.setVisibility(View.VISIBLE);
        String ip = DinSaferApplication.getHttpdns().getIpByHostAsync(APIKey.DOMIAN);
        if (TextUtils.isEmpty(ip)) {
            mBinding.selfTestDns.setLocalText("dns不正常");
            mBinding.selfTestDns.setCompoundDrawablesWithIntrinsicBounds(null, null,
                    getResources().getDrawable(R.drawable.icon_user_setting_warning), null);
        } else {
            int dnsTime = PingUtil.getAvgRTT(APIKey.SERVER_IP);
            if (dnsTime == -1 || dnsTime > 200) {
                mBinding.selfTestDns.setLocalText("dns(" + ip + ")不正常：" + time);
                mBinding.selfTestDns.setCompoundDrawablesWithIntrinsicBounds(null, null,
                        getResources().getDrawable(R.drawable.icon_user_setting_warning), null);
            } else {
                mBinding.selfTestDns.setLocalText("dns(" + ip + ")正常：" + time);
                mBinding.selfTestDns.setCompoundDrawablesWithIntrinsicBounds(null, null,
                        getResources().getDrawable(R.drawable.btn_user_setting_select), null);
            }
        }
    }

    public void toArm(String code) {
        mBinding.selfTestArm.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        mBinding.selfTestArm.setVisibility(View.VISIBLE);
        if (getMainActivity().wsIsConnect()) {
            mBinding.selfTestArm.setLocalText("连接服务器成功");
            EventBus.getDefault().post(new SelfTestEvent(code));
        } else {
            mBinding.selfTestArm.setLocalText("连接服务器失败:" + getMainActivity().closeReason());
        }
    }

    public void toPush() {
        getMainActivity().toCloseWs(false);
        mBinding.selfTestPush.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
        mBinding.selfTestPush.setVisibility(View.VISIBLE);
        EventBus.getDefault().post(new SelfTestEvent(SelfTestEvent.ARM_TASK));
    }

    public void toChangeIp() {
        String defaultServer = APIKey.DOMIAN;
        String defaultStatServer = APIKey.STATISTICS_HTTP_BASE_URL;
        if (DBUtil.contain(DBKey.APIKEY)) {
            defaultServer = DBUtil.Str(DBKey.APIKEY);
        }
        if (DBUtil.contain(DBKey.API_STAT_KEY)) {
            defaultStatServer = DBUtil.Str(DBKey.API_STAT_KEY);
        }
        DomainDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultServer(defaultServer)
                .setDefaultStatServer(defaultStatServer)
                .setContent("请输入ip")
                .setAutoDismiss(true)
                .setOKListener(new DomainDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(DomainDialog dialog, String server, String statServer) {
                        DBUtil.Put(DBKey.APIKEY, server);
                        DBUtil.Put(DBKey.API_STAT_KEY, statServer);
                        getMainActivity().exitAppDirect();
                    }
                })
                .preBuilder()
                .show();
    }

    @Subscribe
    public void onEventMainThread(SelfTestEvent ev) {
        if (ev.getCode().equals(SelfTestEvent.PUSH_TASK)) {
            mBinding.selfTestPush.setLocalText("push成功");
            mBinding.selfTestPush.setCompoundDrawablesWithIntrinsicBounds(null, null,
                    getResources().getDrawable(R.drawable.btn_user_setting_select), null);
            getMainActivity().reConnectWebSocket(true);
        }

    }

    @Subscribe
    public void onEventMainThread(final DeviceResultEvent event) {
        getDelegateActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (LocalKey.ARM_KEY.equals(event.getCmdType())) {
                    if (event.getStatus() == 1) {
                        mBinding.selfTestArm.setLocalText("任务执行成功");
                        mBinding.selfTestArm.setCompoundDrawablesWithIntrinsicBounds(null, null,
                                getResources().getDrawable(R.drawable.btn_user_setting_select), null);
                    } else {
                        mBinding.selfTestArm.setLocalText("任务执行失败");
                        mBinding.selfTestArm.setCompoundDrawablesWithIntrinsicBounds(null, null,
                                getResources().getDrawable(R.drawable.icon_user_setting_warning), null);
                    }
                    toPush();
                }
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (!getMainActivity().wsIsConnect()) {
            getMainActivity().reConnectWebSocket(true);
        }
    }

}

