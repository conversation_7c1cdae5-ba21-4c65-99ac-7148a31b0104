package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.dinsafer.model.BaseToastClose;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;

import org.greenrobot.eventbus.EventBus;


/**
 * Created by Rinfon on 16/7/8.
 */
public class BaseToastFragment extends BaseFragment {


    LocalTextView commonBarTitle;
    RelativeLayout commonBar;
    LottieAnimationView baseToastIcon;
    LocalTextView baseToastContent;
    LocalCustomButton baseToastBtn;

    private static final String TITLE = "title";

    private static final String BTNTEXT = "btntext";

    private static final String CONTENT = "content";

    private static final String ISSUCCESS = "isSuccess";


    public static BaseToastFragment newInstance(String title, boolean isSuccess, String content, String btnText) {
        BaseToastFragment baseToastFragment = new BaseToastFragment();
        Bundle args = new Bundle();
        args.putString(TITLE, title);
        args.putString(BTNTEXT, btnText);
        args.putString(CONTENT, content);
        args.putBoolean(ISSUCCESS, isSuccess);
        baseToastFragment.setArguments(args);
        return baseToastFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.base_toast_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.base_toast_btn).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBar = rootView.findViewById(R.id.common_bar);
        baseToastIcon = rootView.findViewById(R.id.base_toast_icon);
        baseToastContent = rootView.findViewById(R.id.base_toast_content);
        baseToastBtn = rootView.findViewById(R.id.base_toast_btn);
    }

    @Override
    public void initData() {
        if (TextUtils.isEmpty(getArguments().getString(TITLE))) {
            commonBar.setVisibility(View.GONE);
        }
        commonBarTitle.setLocalText(getArguments().getString(TITLE));
        if (!TextUtils.isEmpty(getArguments().getString(BTNTEXT))) {
            baseToastBtn.setLocalText(getArguments().getString(BTNTEXT));
            baseToastBtn.setVisibility(View.VISIBLE);
        } else {
            baseToastBtn.setVisibility(View.GONE);
        }

        baseToastContent.setLocalText(getArguments().getString(CONTENT));

        String uri = "json/animation_succeed.json";
        if (!getArguments().getBoolean(ISSUCCESS)) {
            uri = "json/animation_failed.json";
        }
//        ApngImageLoader.getInstance()
//                .displayApng(uri, baseToastIcon,
//                        new ApngImageLoader.ApngConfig(1, true));
        baseToastIcon.setAnimation(uri);
        baseToastIcon.playAnimation();
    }


    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
        EventBus.getDefault().post(new BaseToastClose());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        baseToastIcon.cancelAnimation();
    }


    @Override
    public void onEnterFragment() {
        toStartAnim();
    }

    private void toStartAnim() {
        if (baseToastIcon == null) {
            return;
        }
        baseToastIcon.setRepeatCount(0);
        baseToastIcon.playAnimation();
    }

    @Override
    public void onPauseFragment() {
        toStopAnim();
    }

    private void toStopAnim() {
        if (baseToastIcon == null) {
            return;
        }

        baseToastIcon.pauseAnimation();

    }

    @Override
    public void onResume() {
        super.onResume();
        toStartAnim();
    }

    @Override
    public boolean onBackPressed() {
        return true;
    }

    @Override
    public void onFinishAnim() {
        toStartAnim();
    }

    @Override
    public void onExitFragment() {
        toStopAnim();
    }

}

