package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.config.AppConfig;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemUserPermissionDetailBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.ui.event.PermissionScrollSyncEvent;
import com.dinsafer.module.settting.ui.model.PermissionItemGroupData;
import com.dinsafer.module.settting.ui.model.PermissionItemGroupModel;
import com.dinsafer.module.settting.ui.model.PermissionItemModel;
import com.dinsafer.module.settting.ui.model.PermissionItemPaddingModel;
import com.dinsafer.module.settting.ui.model.PermissionItemTypeModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * 具体用户权限说明页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/13 2:19 下午
 */
public class UserPermissionDetailFragment extends BaseFragment {
    private static final String KEY_PERMISSION_TYPE = "permission_type";

    private ItemUserPermissionDetailBinding mBinding;
    private int mPermissionType;
    private BindMultiAdapter<PermissionItemModel<?>> adapter;
    private LinearLayoutManager mLayoutManager;
    private final RecyclerView.OnScrollListener mScrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            // 因为是模拟无限循环，所以需要同步RecyclerView的滚动状态
            if (RecyclerView.SCROLL_STATE_IDLE == newState
                    && isNeedSyncScroll()) {
                final View topView = mLayoutManager.getChildAt(0);  //获取可视的第一个view
                final int lastOffset = topView.getTop();  //获取与该view的顶部的偏移量
                final int lastPosition = mLayoutManager.getPosition(topView);  //得到该View的数组位置
                EventBus.getDefault().post(new PermissionScrollSyncEvent(
                        UserPermissionDetailFragment.this.hashCode(), mPermissionType, lastPosition, lastOffset));
            }
        }
    };

    public static UserPermissionDetailFragment newInstance(int permissionType) {
        UserPermissionDetailFragment fragment = new UserPermissionDetailFragment();
        Bundle args = new Bundle();
        args.putInt(KEY_PERMISSION_TYPE, permissionType);
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * 是否需要同步RecyclerView的滚动状态
     * <p>
     * 目前仅Admin权限页需要同步RecyclerView的滚动状态
     *
     * @return true 同步
     */
    private boolean isNeedSyncScroll() {
        return LocalKey.ADMIN == mPermissionType
                || LocalKey.GUEST == mPermissionType;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding = DataBindingUtil.inflate(inflater, R.layout.item_user_permission_detail,
                container, false);
        mPermissionType = getArguments().getInt(KEY_PERMISSION_TYPE);
        initView(mBinding.getRoot(), savedInstanceState);
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        List<PermissionItemGroupData> permissionItemGroupDataList = UserPermissionInfoHelper
                .getPermissionGroupByType(mPermissionType);
        List<PermissionItemModel<?>> list = new ArrayList<>();
        list.add(new PermissionItemTypeModel(mPermissionType));
        if (null != permissionItemGroupDataList && permissionItemGroupDataList.size() > 0) {
            for (PermissionItemGroupData permissionGroup : permissionItemGroupDataList) {
                list.add(new PermissionItemGroupModel(permissionGroup));
            }
        }
        list.add(new PermissionItemPaddingModel());
        adapter = new BindMultiAdapter<>();
        adapter.setNewData(list);
        mLayoutManager = new LinearLayoutManager(getContext());
        mBinding.rcvPermissions.setLayoutManager(mLayoutManager);
        mBinding.rcvPermissions.setAdapter(adapter);
        mBinding.rcvPermissions.addOnScrollListener(mScrollListener);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PermissionScrollSyncEvent ev) {
        if (ev.getPermissionType() == mPermissionType && ev.getHashcode() != this.hashCode()) {
            DDLog.i(TAG, "同步滚动状态");
            mLayoutManager.scrollToPositionWithOffset(ev.getLastPosition(), ev.getLastOffset());
        }
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        mBinding.rcvPermissions.removeOnScrollListener(mScrollListener);
        super.onDestroyView();
    }

    @Override
    public void initData() {
        super.initData();
    }

    /**
     * 用户权限信息管理类
     */
    private static class UserPermissionInfoHelper {
        /**
         * 权限功能组
         */
        public static final String GROUP_ALARM_SYSTEM = "Alarm System";
        public static final String GROUP_ACCESSORIES_MANAGEMENT = "Accessories Management";
        public static final String GROUP_SMART_CAMERA = "Smart Camera";
        public static final String GROUP_FAMILY = "Family";
        public static final String GROUP_POWER_STATION = "Emaldo power station";

        /**
         * 权限具体功能
         */
        // GROUP_ALARM_SYSTEM
        public static final String PERMISSION_PANEL_ARM = "Operate Arm, Disarm & Home Arm";
        public static final String PERMISSION_PANEL_SOS = "Call for help via SOS Button";
        public static final String PERMISSION_PANEL_SECURITY_RULES = "Set system security rules";
        public static final String PERMISSION_FAMILY_ASSIGN_KEYPAD_PASSWORD = "Assign Keypad Password";

        // GROUP_ACCESSORIES_MANAGEMENT
        public static final String PERMISSION_ACCESSORY_SWITCH = "Switch bulbs, plugs, ect.";
        public static final String PERMISSION_ACCESSORY_SETUP = "Setup bulbs, plugs, smart buttons, ect.";
        public static final String PERMISSION_ACCESSORY_ADD = "Add devices and accessories";

        // GROUP_SMART_CAMERA
        public static final String PERMISSION_CAMERA_LIVE = "View live video of smart cameras";
        public static final String PERMISSION_CAMERA_RECORD = "View history records of smart cameras";
        public static final String PERMISSION_CAMERA_SETUP = "Setup smart cameras";

        // GROUP_FAMILY
        public static final String PERMISSION_FAMILY_INVITE_USER = "Invite guests";
        public static final String PERMISSION_FAMILY_INVITE_ADMIN = "Invite administrators";

        // GROUP_POWER_STATION
        public static final String PERMISSION_POWER_STATION_VIEW_HOMEPAGE_WIDGET = "View homepage widget";
        public static final String PERMISSION_POWER_STATION_VIEW_MORE_DETAILS = "View more details of Power Station";
        public static final String PERMISSION_POWER_STATION_SETUP = "Setup Power Station";
        public static final String PERMISSION_POWER_STATION_RECEIVE_EVENT_PUSH = "Receive event pushes";
        public static final String VIEW_POWER_ENERGY_STATS_OF_POWER_STATION = "View Power/Energy stats of Power Station";
        public static final String SET_EV_CHARGE = "Set EV Charge";
        public static final String SET_EV_CHARGE_EXTRA = "(Upon activation of this feature)";

        /**
         * 获取权限组信息
         *
         * @param permissionType 具有用户权限
         */
        public static List<PermissionItemGroupData> getPermissionGroupByType(int permissionType) {
            List<PermissionItemGroupData> permissionGroups = null;
            if (LocalKey.ADMIN == permissionType) {
                permissionGroups = getPermissionGroupAdmin();
            } else if (LocalKey.USER == permissionType) {
                permissionGroups = getPermissionGroupUser();
            } else if (LocalKey.GUEST == permissionType) {
                permissionGroups = getPermissionGroupGuest();
            } else {
                throw new IllegalStateException("User's permission must be one of 10,20,30");
            }

            return permissionGroups;
        }

        /**
         * Admin权限组
         */
        private static List<PermissionItemGroupData> getPermissionGroupAdmin() {
            List<PermissionItemGroupData> permissionGroups = new ArrayList<>();
            if (AppConfig.Plugins.SUPPORT_BMT_HP5000
                    || AppConfig.Plugins.SUPPORT_BMT_HP5001
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE20
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE30
                    || AppConfig.Plugins.SUPPORT_BMT_POWERSTORE
                    || AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_POWER_STATION)
                        .addFunction(PERMISSION_POWER_STATION_VIEW_HOMEPAGE_WIDGET)
                        .addFunction(PERMISSION_POWER_STATION_VIEW_MORE_DETAILS)
                        .addFunction(PERMISSION_POWER_STATION_SETUP)
                        .addFunction(PERMISSION_POWER_STATION_RECEIVE_EVENT_PUSH)
                        .create());
            }
            if (AppConfig.Plugins.SUPPORT_IPC) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_SMART_CAMERA)
                        .addFunction(PERMISSION_CAMERA_LIVE)
                        .addFunction(PERMISSION_CAMERA_RECORD)
                        .addFunction(PERMISSION_CAMERA_SETUP)
                        .create());
            }

            if (CommonDataUtil.getInstance().isHadPanel()) {
                PermissionItemGroupData.Builder builder = new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_ALARM_SYSTEM)
                        .addFunction(PERMISSION_PANEL_ARM)
                        .addFunction(PERMISSION_PANEL_SOS)
                        .addFunction(PERMISSION_PANEL_SECURITY_RULES);
                if (AppConfig.Functions.SUPPORT_KEYPAD_PWD) {
                    builder.addFunction(PERMISSION_FAMILY_ASSIGN_KEYPAD_PASSWORD);
                }
                permissionGroups.add(builder.create());
            }

            if (AppConfig.Functions.SUPPORT_SMART_WIDGET) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_ACCESSORIES_MANAGEMENT)
                        .addFunction(PERMISSION_ACCESSORY_SWITCH)
                        .addFunction(PERMISSION_ACCESSORY_SETUP)
                        .addFunction(PERMISSION_ACCESSORY_ADD)
                        .create());
            }

            permissionGroups.add(new PermissionItemGroupData.Builder()
                    .setGroupTittle(GROUP_FAMILY)
                    .addFunction(PERMISSION_FAMILY_INVITE_USER)
                    .addFunction(PERMISSION_FAMILY_INVITE_ADMIN)
                    .create());
            updatePermissionGroupsItemType(permissionGroups);
            return permissionGroups;
        }

        /**
         * User权限组
         */
        private static List<PermissionItemGroupData> getPermissionGroupUser() {
            List<PermissionItemGroupData> permissionGroups = new ArrayList<>();
            if (AppConfig.Plugins.SUPPORT_BMT_HP5000
                    || AppConfig.Plugins.SUPPORT_BMT_HP5001
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE20
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE30
                    || AppConfig.Plugins.SUPPORT_BMT_POWERSTORE
                    || AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_POWER_STATION)
                        .addFunction(PERMISSION_POWER_STATION_VIEW_HOMEPAGE_WIDGET)
                        .addFunction(PERMISSION_POWER_STATION_RECEIVE_EVENT_PUSH)
                        .create());
            }
            if (AppConfig.Plugins.SUPPORT_IPC) {

                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_SMART_CAMERA)
                        .addFunction(PERMISSION_CAMERA_LIVE)
                        .addFunction(PERMISSION_CAMERA_RECORD)
                        .create());
            }

            if (CommonDataUtil.getInstance().isHadPanel()) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_ALARM_SYSTEM)
                        .addFunction(PERMISSION_PANEL_ARM)
                        .addFunction(PERMISSION_PANEL_SOS)
                        .create());
            }

            if (AppConfig.Functions.SUPPORT_SMART_WIDGET) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_ACCESSORIES_MANAGEMENT)
                        .addFunction(PERMISSION_ACCESSORY_SWITCH)
                        .addFunction(PERMISSION_ACCESSORY_SETUP)
                        .addFunction(PERMISSION_ACCESSORY_ADD)
                        .create());
            }
            permissionGroups.add(new PermissionItemGroupData.Builder()
                    .setGroupTittle(GROUP_FAMILY)
                    .addFunction(PERMISSION_FAMILY_INVITE_USER)
                    .create());
            updatePermissionGroupsItemType(permissionGroups);
            return permissionGroups;
        }

        /**
         * Guest权限组
         */
        private static List<PermissionItemGroupData> getPermissionGroupGuest() {
            List<PermissionItemGroupData> permissionGroups = new ArrayList<>();
            if (AppConfig.Plugins.SUPPORT_BMT_HP5000
                    || AppConfig.Plugins.SUPPORT_BMT_HP5001
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE20
                    || AppConfig.Plugins.SUPPORT_BMT_POWERCORE30
                    || AppConfig.Plugins.SUPPORT_BMT_POWERSTORE
                    || AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_POWER_STATION)
                        .addFunction(PERMISSION_POWER_STATION_VIEW_HOMEPAGE_WIDGET)
                        .addFunction(VIEW_POWER_ENERGY_STATS_OF_POWER_STATION)
                        .addFunction(Local.s(SET_EV_CHARGE) + Local.s(SET_EV_CHARGE_EXTRA))
                        .create());
            }


            if (CommonDataUtil.getInstance().isHadPanel()) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_ALARM_SYSTEM)
                        .addFunction(PERMISSION_PANEL_ARM)
                        .create());
            }

            if (AppConfig.Functions.SUPPORT_SMART_WIDGET) {
                permissionGroups.add(new PermissionItemGroupData.Builder()
                        .setGroupTittle(GROUP_ACCESSORIES_MANAGEMENT)
                        .addFunction(PERMISSION_ACCESSORY_SWITCH)
                        .create());
            }
            updatePermissionGroupsItemType(permissionGroups);
            return permissionGroups;
        }
    }

    private static void updatePermissionGroupsItemType(@NonNull final List<PermissionItemGroupData> permissionGroups) {
        final int totalSize = permissionGroups.size();
        if (1 == totalSize) {
            permissionGroups.get(0).setGroupType(PermissionItemGroupModel.GROUP_TYPE_SINGLE);
        } else if (totalSize >= 2) {
            permissionGroups.get(0).setGroupType(PermissionItemGroupModel.GROUP_TYPE_TOP);
            permissionGroups.get(totalSize - 1).setGroupType(PermissionItemGroupModel.GROUP_TYPE_BOTTOM);
        }
    }
}
