package com.dinsafer.module.settting.ui.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.settting.ui.ReadyToArmSettingFragment;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.FastBlurActivityUtil;
import com.dinsafer.util.Local;

public class ReadyToArmTipDialog extends Dialog {
    public static final String KEY_TIP_ENABLE = "TIP_ENABLE";

    private Context mContext;
    private View rootView;
    private CheckBox checkBox;
    private LocalTextView tvTip;
    private LocalCustomButton btnCancel;
    private LocalCustomButton btnOK;

    public ReadyToArmTipDialog(@NonNull Context context) {
        super(context, R.style.CustomDialogStyle);
        init(context);
    }

    private void init(Context context) {
        this.mContext = context;
        rootView = LayoutInflater.from(mContext).inflate(R.layout.dialog_ready_to_arm, null, false);
        setContentView(rootView);

        checkBox = rootView.findViewById(R.id.cb);
        checkBox.setText(Local.s(mContext.getResources().getString(R.string.do_not_see_again)));

        tvTip = rootView.findViewById(R.id.tv_tip);
        tvTip.setLocalText(mContext.getResources().getString(R.string.ready_to_arm_off_tip));

        btnCancel = rootView.findViewById(R.id.btn_cancel);
        btnCancel.setLocalText(mContext.getResources().getString(R.string.not_now));
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveState();
                dismiss();
            }
        });

        btnOK = rootView.findViewById(R.id.btn_ok);
        btnOK.setLocalText(mContext.getResources().getString(R.string.go_setting));
        btnOK.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveState();
                ((BaseMainActivity) mContext).addCommonFragment(ReadyToArmSettingFragment.newInstance());
                dismiss();
            }
        });

    }

    @Override
    public void show() {
        super.show();
        setCanceledOnTouchOutside(false);

        Bitmap blurBackgroundDrawer = FastBlurActivityUtil.getBlurBackgroundDrawer((Activity) mContext);
        //blurBackgroundDrawer为模糊后的背景图片
        Window window = getWindow();
        WindowManager.LayoutParams lp = window.getAttributes();
        window.setBackgroundDrawable(new BitmapDrawable(mContext.getResources(), blurBackgroundDrawer));
        window.setGravity(Gravity.CENTER);
        window.setAttributes(lp);

    }

    @Override
    public void cancel() {
        super.cancel();
        saveState();
    }

    private void saveState() {
        Log.d("TAG", "saveState: ");
        DBUtil.Put(KEY_TIP_ENABLE, checkBox.isChecked());

    }

    public static boolean checkState() {
        return DBUtil.Bool(KEY_TIP_ENABLE);
    }


}
