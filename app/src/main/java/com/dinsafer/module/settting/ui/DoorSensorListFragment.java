package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.animation.BounceInterpolator;
import android.widget.AdapterView;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SecurityPlugsListLayoutBinding;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.PlugsData;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.model.SmartButtonActionChangeEvent;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.module.settting.adapter.DoorSensorItem;
import com.dinsafer.module.settting.adapter.MyFamilyListAdapter;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.AlertDialogManager;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.NavigatorUtil;
import com.dinsafer.util.SettingInfoHelper;
import com.dinsafer.util.SmartButtonUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;

import static com.dinsafer.util.SmartButtonUtil.SERVICE_ACTION_SINGLE_PRESS;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_MUSIC;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_NAME;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_PLUGIN_ID;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_SCENE;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_SEND_ID;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_STYPE;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_VOLUME;

/**
 * Created by Rinfon on 16/7/12.
 */
public class DoorSensorListFragment extends BaseTimeoutPluginFragment<SecurityPlugsListLayoutBinding>
        implements ModifyDoorSensorAndPirPlugsFragment.ICallBack {


    LocalTextView commonBarTitle;
    SwipeMenuListView securityListview;
    LocalTextView listviewEmpty;

    private ArrayList<PlugsData> mData;
    private DoorSensorItem plugsItem;
    private int messageIndex;

    private static final String OLD_DOOR_SENSOR = "Common window/door sensor";
    private static final String NEW_DOOR_SENSOR = "Smart window/door sensor";

    private ArrayList<Device> mPluginDevices;
    private boolean selfOperate;

    public static DoorSensorListFragment newInstance() {
        DoorSensorListFragment simplePlugsListFragment = new DoorSensorListFragment();
        return simplePlugsListFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.security_plugs_list_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        showBlueTimeOutLoadinFramgment();
        __bindViews(inflateView);
        __bindClicks(inflateView);
        findPanel();
        EventBus.getDefault().register(this);
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener(v -> toClose());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        securityListview = rootView.findViewById(R.id.security_listview);
        listviewEmpty = rootView.findViewById(R.id.listview_empty);
    }

    @Override
    public void initData() {
        super.initData();
        listviewEmpty.setLocalText(getResources().getString(R.string.listview_empty));
        commonBarTitle.setLocalText(getResources().getString(R.string.device_management_door_sensor));

        if (SettingInfoHelper.getInstance().isRFPluginItemClickable()
                && CommonDataUtil.getInstance().isPanelOnline()) {
            SwipeMenuCreator creator = new SwipeMenuCreator() {

                @Override
                public void create(SwipeMenu menu) {
                    if (menu.getViewType() == MyFamilyListAdapter.TYPE_ITEM) {
                        // create "delete" item
                        SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                        // set item background
                        deleteItem.setBackground(R.color.colorDelete);
                        // set item width
                        deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                        deleteItem.setTitleSize(13);

                        deleteItem.setTitleColor(Color.WHITE);
                        // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                        deleteItem.setTitle(Local.s(getResources().getString(R.string.smart_plugs_list_delete)));
                        // add to menu
                        menu.addMenuItem(deleteItem);
                    }
                }
            };

//        set creator
            securityListview.setMenuCreator(creator);
            securityListview.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
            securityListview.setCloseInterpolator(new BounceInterpolator());
            securityListview.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
                @Override
                public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                    switch (i1) {
                        case 0:
                            // delete
                            toDeleteItem(i);

                            break;
                    }
                    // false : close the menu; true : not close the menu
                    return false;
                }
            });
            securityListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                    toChangePlugName(position);
                }
            });
        }
        securityListview.setEmptyView(listviewEmpty);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        createPlugsList();
    }

    private void toDeleteItem(final int i) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        Device device = DinHome.getInstance().getDevice(mData.get(i).getPlugId());
                        if (null != device) {
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            selfOperate = true;
                            messageIndex = i;
                            device.submit(PanelParamsHelper.deletePlugin());
                        } else {
                            DDLog.e(TAG, "No device!!!");
                            showErrorToast();
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    /**
     * 初始化plugg数据列表
     */
    private void createPlugsList() {
        mData = new ArrayList<>();
        plugsItem = new DoorSensorItem(getActivity(), mData);
        securityListview.setAdapter(plugsItem);
        mPluginDevices = new ArrayList<>();

        startTimeoutCountDown();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> devices = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.DOOR_WINDOW_SENSOR);
                if (null != devices && 0 < devices.size()) {
                    for (Device device : devices) {
                        device.registerDeviceCallBack(DoorSensorListFragment.this);
                    }
                    mPluginDevices.addAll(devices);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createPlugsList-getdata");
            LinkedHashMap<String, ArrayList<PlugsData>> dataMap = new LinkedHashMap<String, ArrayList<PlugsData>>();
            ArrayList<PlugsData> oldDoorSensorList = new ArrayList<>();
            ArrayList<PlugsData> newDoorSensorList = new ArrayList<>();
            dataMap.put(NEW_DOOR_SENSOR, newDoorSensorList);
            dataMap.put(OLD_DOOR_SENSOR, oldDoorSensorList);
            if (null != mPanelDevice && 0 < mPluginDevices.size()) {
                PlugsData plug;
                for (Device device : mPluginDevices) {
                    boolean isSmartDoorSensor = DeviceHelper.getBoolean(device, PanelDataKey.DoorSensor.IS_SMART_DOOR_SENSOR, false);
                    plug = new PlugsData();
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    if (isSmartDoorSensor) {
                        plug.setName(NEW_DOOR_SENSOR)
                                .setDescription(pluginName)
                                .setPlugId(device.getId())
                                .setHasSignalLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_SIGNAL_LEVEL, false))
                                .setCanTamper(DeviceHelper.getBoolean(device, PanelDataKey.CAN_TAMPER, false))
                                .setCanReadyToArm(DeviceHelper.getBoolean(device, PanelDataKey.DoorSensor.CAN_READY_TO_ARM, false))
                                .setCanCharging(DeviceHelper.getBoolean(device, PanelDataKey.CAN_CHARGING, false))
                                .setHasBatteryLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_BATTERY_LEVEL, false))
                                .setHasWebsocketLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_WEB_SOCKET_LOADING, false))
                                .setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                        newDoorSensorList.add(plug);
                    } else {
                        plug.setName(OLD_DOOR_SENSOR)
                                .setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""))
                                .setDescription(pluginName)
                                .setPlugId(device.getId());
                        if (null != DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA)) {
                            plug.setTime(DeviceHelper.getLong(device, PanelDataKey.TIME_COMMON, 0))
                                    .setCanReadyToArm(false)
                                    .setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                        }
                        oldDoorSensorList.add(plug);
                    }
                }
            }

            for (String key : dataMap.keySet()) {
                if (dataMap.get(key).size() > 0) {
                    // 副标题修改
                    PlugsData plugsData = new PlugsData();
                    plugsData.setName("")
                            .setDescription(Local.s(key));

                    // 加副标题
                    mData.add(plugsData);
                    // 副标题下的数据列表
                    mData.addAll(dataMap.get(key));
                }
            }
            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
            }
            plugsItem.setData(mData);
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (null != mPluginDevices && 0 < mPluginDevices.size()) {
            for (Device device : mPluginDevices) {
                device.unregisterDeviceCallBack(this);
            }
        }
        DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.DOOR_WINDOW_SENSOR);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PlugsNameChangeEvent ev) {
        plugsItem.changeName(messageIndex, ev.getName());
    }

    public void toChangePlugName(int index) {
        if (!SettingInfoHelper.getInstance().isRFPluginItemClickable()) {
            DDLog.e(TAG, "当前权限下Item不能被点击哦$_$");
            return;
        }

        if (!CommonDataUtil.getInstance().isPanelOnline()) {
            DDLog.e(TAG, "当前主机离线，Item不能被点击哦");
            return;
        }

        messageIndex = index;
        if (!TextUtils.isEmpty(mData.get(index).getName())) {
            if (mData.get(index).getAskData() != null) {
                messageIndex = index;
                Builder builder = new Builder();
                boolean isOffline = false;
                boolean isLowPower = false;

                if ((DDJSONUtil.has(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_KEEP_LIVE)
                        && !DDJSONUtil.getBoolean(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_KEEP_LIVE)
                        && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE
                        && !DDJSONUtil.getString(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_S_TYPE).equals("1C"))
                        || mData.get(index).isLoadStatusError()) {
                    isOffline = true;
                } else if (DDJSONUtil.has(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_POWER)
                        && !DDJSONUtil.getBoolean(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_POWER)) {
                    isLowPower = true;
                }

                // 如果是有电量值的配件，优先以电量值的电量转态为标准
                if (mData.get(index).isHasBatteryLevel()) {
                    isLowPower = mData.get(index).isBatteryLevelLowBattery();
                }

                builder.setId(DDJSONUtil.getString(mData.get(index).getAskData(), NetKeyConstants.NET_KEY_ID))
                        .setAdd(false)
                        .setOffical(true)
                        .setOffline(isOffline)
                        .setLowPower(isLowPower)
                        .setMessageIndex(messageIndex)
                        .setShowDelete(true)
                        .setName(mData.get(index).getDescription())
                        .setShowwave(false)
                        .setDecodeId(mData.get(index).getDecodeid())
                        .setData(mData.get(index).getAskData());
                ModifyDoorSensorAndPirPlugsFragment modifyASKPlugsFragment =
                        ModifyDoorSensorAndPirPlugsFragment.newInstance(builder);
                modifyASKPlugsFragment.setCallBack(this);
                getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);
            } else {
                if (!TextUtils.isEmpty(mData.get(index).getDecodeid())) {
                    String oldPluginId = DDSecretUtil.hexStrToStr64(mData.get(index).getDecodeid());
                    NavigatorUtil.getInstance().toModifyPlugsNameFragment(mData.get(index).getDescription(), mData.get(index).getPlugId(), oldPluginId, false, true);
                } else {
                    NavigatorUtil.getInstance().toModifyPlugsNameFragment(mData.get(index).getDescription(), mData.get(index).getPlugId(), false, true);
                }
            }
        }
    }

    //    心跳配件改名字
    @Override
    public void onChangeName(int index, String name) {
        plugsItem.changeName(messageIndex, name);
    }

    @Override
    public void onBlockModeChange(String pluginID, int mode) {
        DDLog.i(TAG, "onBlockModeChange:" + mode);
        for (int i = 0; i < mData.size(); i++) {
            if (pluginID.equals(mData.get(i).getPlugId())) {
                JSONObject askData = mData.get(i).getAskData();
                try {
                    askData.put(NetKeyConstants.NET_KEY_BLOCK, mode);
                    mData.get(i).setAskData(askData);
                    return;
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    @Override
    public void onPirSensitivityChange(String pluginID, int sensitivity) {

    }

    @Override
    public void onDelete(String id) {
        Log.e(TAG, "onDeletePlug: " + id);
        if (mData != null && mData.size() > 0) {
            int index = -1;
            for (int i = 0; i < mData.size(); i++) {
                if (!TextUtils.isEmpty(mData.get(i).getPlugId()) && mData.get(i).getPlugId().equals(id)) {
                    index = i;
                    break;
                }
            }
            Log.e(TAG, "onDeletePlug: " + index);
            if (index >= 0 && index < mData.size()) {
                mData.remove(index);
                plugsItem.setData(mData);
                plugsItem.notifyDataSetChanged();

                if (!hasPlugin()) {
                    listviewEmpty.setVisibility(View.VISIBLE);
                    securityListview.setVisibility(View.GONE);
                } else {
                    listviewEmpty.setVisibility(View.GONE);
                    securityListview.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    private boolean hasPlugin() {
        if (mData == null || mData.size() == 0) {
            return false;
        }
        for (PlugsData mDatum : mData) {
            if (!TextUtils.isEmpty(mDatum.getPlugId())) {
                return true;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOfflineEvent event) {
        DDLog.d(TAG, "DeviceOfflineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOnlineEvent event) {
        DDLog.d(TAG, "DeviceOnlineEvent. ");
        plugsItem.notifyDataSetChanged();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPluginDeleteEvent(PluginDeleteEvent pluginDeleteEvent) {
        try {
            onDelete(pluginDeleteEvent.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(SmartButtonActionChangeEvent ev) {
//        closeTimeOutLoadinFramgmentWithErrorAlert();
//        getDelegateActivity().removeToFragment(this.getClass().getName());
        for (PlugsData data : mData) {
            if (ev.getSmartButtonData().getTargetId().equals(data.getPlugId())) {
                JSONObject configData = DDJSONUtil.getJSONObject(data.getAskData(), SERVICE_ACTION_SINGLE_PRESS);
                try {
                    if (null == configData) {
                        configData = new JSONObject();
                        data.getAskData().put(SERVICE_ACTION_SINGLE_PRESS, configData);
                    }
                    configData.put(SERVICE_KEY_MUSIC, ev.getNewAction().getActionData().getMusicIndex());
                    configData.put(SERVICE_KEY_VOLUME, ev.getNewAction().getActionData().getVolumeIndex());
                    configData.put(SERVICE_KEY_NAME, ev.getNewAction().getTargetData().getTargetName());
                    configData.put(SERVICE_KEY_PLUGIN_ID, ev.getNewAction().getTargetData().getTargetId());
                    configData.put(SERVICE_KEY_SCENE, SmartButtonUtil.getSceneStringByType(ev.getNewAction().getSceneType()));
                    configData.put(SERVICE_KEY_SEND_ID, ev.getNewAction().getTargetData().getSendid());
                    configData.put(SERVICE_KEY_STYPE, ev.getNewAction().getTargetData().getStype());

                    plugsItem.setData(mData);
                    plugsItem.notifyDataSetChanged();
                    break;
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onPluginStatusUpdate() {
        plugsItem.setData(mData);
        plugsItem.notifyDataSetChanged();
    }

    @Override
    protected ArrayList<PlugsData> getCurrentPluginList() {
        return mData;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || 1 != resultType) {
            return;
        }

        if (null != mPanelDevice
                && deviceId.equals(mPanelDevice.getId())) {
            onPanelCmdCallback(deviceId, cmd, map);
            return;
        }

        onPluginCmdCallback(deviceId, cmd, map);
    }

    private void onPanelCmdCallback(String deviceId, String cmd, Map map) {
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            return;
        }
        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
        if (PluginCmd.PLUGIN_STATE_CHANGE.equals(cmd)) {
            updatePluginBattery(result);
        } else if (LocalKey.TASK_DS_STATUS.equals(cmd)) {
            updatePluginState(cmd, result);
        }
    }

    private void onPluginCmdCallback(String deviceId, String cmd, Map map) {
        DDLog.i(TAG, "onPluginCmdCallback");
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            if (selfOperate) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
                selfOperate = false;
            }
            return;
        }

        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
        if (selfOperate && PluginCmd.PLUGIN_DELETE.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            plugsItem.remove(messageIndex);
            selfOperate = false;
            return;
        }

        if (PluginCmd.PLUGIN_ONLINE_CHANGE.equals(cmd)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                String sendid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID);
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getAskData() != null) {
                        if (DDJSONUtil.getString(mData.get(i).getAskData(), NetKeyConstants.NET_KEY_SEND_ID).equals(sendid)) {
                            mData.get(i).getAskData().put(NetKeyConstants.NET_KEY_KEEP_LIVE,
                                    !LocalKey.PLUGIN_OFFLINE.equals(operateCmd));
                            break;
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            plugsItem.notifyDataSetChanged();
        } else if (PluginCmd.PLUGIN_POWER_CHANGE.equals(cmd)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                String pluginid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getAskData() != null) {
                        //  新配件才显示低电
                        if (mData.get(i).getPlugId().equals(pluginid)) {
                            mData.get(i).getAskData().put(NetKeyConstants.NET_KEY_POWER,
                                    !LocalKey.EVENT_LOWERPOWER.equals(operateCmd));
                            break;
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            plugsItem.notifyDataSetChanged();
            showSuccess();
        } else if (PluginCmd.PLUGIN_CONFIG_BLOCK.equals(cmd)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                String pluginid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ID);
                for (int i = 0; i < mData.size(); i++) {
                    if (mData.get(i).getAskData() != null) {
                        // 新配件才显示低电
                        if (mData.get(i).getPlugId().equals(pluginid)) {
                            mData.get(i).getAskData().put(NetKeyConstants.NET_KEY_BLOCK,
                                    DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_BLOCK));
                            break;
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            plugsItem.notifyDataSetChanged();
        } else if (PluginCmd.SET_DOOR_WINDOW_PUSH_STATUS.equals(cmd)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                String plugin_id = DDJSONUtil.getString(jsonObject, "plugin_id");
                boolean push_status = DDJSONUtil.getBoolean(jsonObject, NetKeyConstants.NET_KEY_PUSH_STATUS);
                if (!TextUtils.isEmpty(plugin_id)) {
                    for (int i = 0; i < mData.size(); i++) {
                        if (mData.get(i).getAskData() != null) {
                            // 新配件才显示低电
                            if (plugin_id.equals(mData.get(i).getPlugId())) {
                                mData.get(i).getAskData().put(NetKeyConstants.NET_KEY_PUSH_STATUS,
                                        push_status);
                                break;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                DDLog.e(TAG, "Error on update push_status");
                e.printStackTrace();
            }
        }
    }

    private void updatePluginState(@NonNull String cmd, String result) {
        DDLog.i(TAG, "updatePluginState, ev: " + result);
        try {
            JSONArray js;
            try {
                JSONObject jObj = new JSONObject(result);
                if (LocalKey.TASK_DS_STATUS.equals(cmd)) {
                    String message = DDJSONUtil.getString(jObj, "message");
                    String postfix = "";
                    try {
                        JSONArray plugins = DDJSONUtil.getJSONarray(jObj, "plugins");
                        if (null != plugins && plugins.length() > 0) {
                            postfix = DDJSONUtil.getString((JSONObject) plugins.get(0), "id");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (!TextUtils.isEmpty(message)) {
                        AlertDialog.createBuilder(getDelegateActivity())
                                .setAutoDissmiss(true)
                                .setContentColor(getResources().getColor(R.color.common_dialog_content))
                                .setType(AlertDialogManager.DialogType.DOOR_SENSOR_STATE_CHANGED)
                                .setPostfix(postfix)
                                .setBackgroundTint(Color.WHITE)
                                .setOk(getResources().getString(R.string.ok))
                                .setContent(message)
                                .preBuilder()
                                .show();
                    }
                }

                js = DDJSONUtil.getJSONarray(jObj, "plugins");
            } catch (Exception e) {
                js = new JSONArray(result);
            }

            if (null == js || 0 >= js.length()) {
                return;
            }

            JSONObject itemJson;
            String itemId;
            boolean enable;
            boolean hadUpdate = false;
            for (int i = 0; i < js.length(); i++) {
                itemJson = (JSONObject) js.get(i);
                if (DDJSONUtil.has(itemJson, "enable")) {
                    itemId = DDJSONUtil.getString(itemJson, "id");
                    enable = DDJSONUtil.getBoolean(itemJson, "enable");

                    if (!TextUtils.isEmpty(itemId) && null != mData && mData.size() > 0) {
                        for (int i1 = 0; i1 < mData.size(); i1++) {
                            if (itemId.equals(mData.get(i1).getPlugId())) {
                                mData.get(i1).setEnable(enable);
                                hadUpdate = true;
                            }
                        }
                    }
                }
            }
            if (hadUpdate) {
                plugsItem.notifyDataSetChanged();
            }
        } catch (JSONException e) {
            DDLog.e(TAG, "Error on updatePluginState");
            e.printStackTrace();
        }
    }
}
