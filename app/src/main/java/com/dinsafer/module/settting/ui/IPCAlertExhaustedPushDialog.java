package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.nostra13.universalimageloader.core.ImageLoader;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;


/**
 * Created by rinfon on 15/6/26.
 */
public class IPCAlertExhaustedPushDialog extends Dialog {

    int layoutRes;//布局文件

    Context mContext;

    private ViewDataBinding mBinding;

    public IPCAlertExhaustedPushDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        EventBus.getDefault().register(this);
        mContext = context;
        this.layoutRes = R.layout.dialog_ipc_alert_exhausted_push;
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(mContext), layoutRes, null, false);
        setContentView(mBinding.getRoot());
        try {
            ImageLoader.getInstance().displayImage(builder.imgUrl, (ImageView) mBinding.getRoot().findViewById(R.id.imageView));
        } catch (Exception e) {
        }

        mBinding.getRoot().findViewById(R.id.ok_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onClick(IPCAlertExhaustedPushDialog.this);
                }
            }
        });

        mBinding.getRoot().findViewById(R.id.cancel_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.cancelClick != null) {
                    builder.cancelClick.onClick(IPCAlertExhaustedPushDialog.this);
                }
            }
        });

        ((LocalTextView) mBinding.getRoot().findViewById(R.id.tv_title)).setLocalText(builder.mTitle);
        ((LocalCustomButton) mBinding.getRoot().findViewById(R.id.ok_btn)).setLocalText(builder.mOK);
        ((LocalCustomButton) mBinding.getRoot().findViewById(R.id.cancel_btn)).setLocalText(builder.mCancel);
        ((LocalTextView) mBinding.getRoot().findViewById(R.id.tv_content)).setLocalText(builder.mContent);

    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        super.cancel();
    }

    @Override
    public void dismiss() {
        super.dismiss();
        try {
            EventBus.getDefault().unregister(this);
        } catch (Exception e) {

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(HadLogoutEvent ev) {
        try {
            dismiss();
        } catch (Exception e) {

        }
    }

    public interface AlertClickCallback {
        void onClick(IPCAlertExhaustedPushDialog dialog);
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mTitle;

        private String mOK;

        private String mCancel;

        private String imgUrl;

        private boolean isAutoDismiss = true;

        private AlertClickCallback okClick;

        private AlertClickCallback cancelClick;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setCancelListener(AlertClickCallback listener) {
            this.cancelClick = listener;
            return this;
        }


        public Builder setOk(String ok) {
            mOK = ok;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            return this;
        }

        public Builder setImgUrl(String url) {
            imgUrl = url;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setTitle(String title) {
            mTitle = title;
            return this;
        }

        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public IPCAlertExhaustedPushDialog preBuilder() {
            IPCAlertExhaustedPushDialog alertDialog = new IPCAlertExhaustedPushDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
