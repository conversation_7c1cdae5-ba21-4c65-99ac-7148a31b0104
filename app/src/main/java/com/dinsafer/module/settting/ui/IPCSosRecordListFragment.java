package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.BounceInterpolator;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.AlertServicePlanUpdateEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.iap.CloudStorageServiceHelper;
import com.dinsafer.module.iap.IapRootActivity;
import com.dinsafer.module.iap.ServiceCardItemModel;
import com.dinsafer.module.ipc.common.video.IPCHeartLaiMotionRecordIJKPlayerActivity2;
import com.dinsafer.module.settting.adapter.IpcSosRecordListAdapter;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.ui.IPCSosRecordListView;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.PullToRefreshLayout;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringStyle;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Collections;
import java.util.List;

import androidx.annotation.Nullable;


/**
 * Created by Rinfon on 16/7/8.
 */
public class IPCSosRecordListFragment extends BaseFragment {


    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    ImageView commonBarLeft;
    RelativeLayout commonTitleBar;
    ImageView pullIcon;
    TextView stateTv;
    ImageView stateIv;
    RelativeLayout headView;
    IPCSosRecordListView ipcSosRecordListview;
    ImageView pullupIcon;
    ImageView loadingIcon;
    ImageView loadingIcon2;
    TextView loadstateTv;
    ImageView loadstateIv;
    RelativeLayout loadmoreView;
    PullToRefreshLayout ipcSosRecordPulllayout;
    LocalTextView listviewEmpty;
    LocalTextView tvVideoListHint;
    ViewGroup llAlertService;
    View llAlertServiceBeta;
    private IpcSosRecordListAdapter adapter;

    private long currentTime = -1;

    private int pagesize = 20;

    private final List<String> providers = Collections.singletonList(HeartLaiConstants.PROVIDER_HEARTLAI);

    public static IPCSosRecordListFragment newInstance() {
        return new IPCSosRecordListFragment();
    }

    private PullToRefreshLayout.OnRefreshListener refreshListen = new PullToRefreshLayout.OnRefreshListener() {
        @Override
        public void onRefresh(PullToRefreshLayout pullToRefreshLayout) {

        }

        @Override
        public void onLoadMore(final PullToRefreshLayout pullToRefreshLayout) {
            if (adapter.getData() != null && adapter.getData().size() > 0) {
                currentTime = adapter.getData().get(adapter.getData().size() - 1).getRecordtime();
            } else {
                currentTime = -1;
            }
            DinSDK.getHomeInstance().getMotionDetectionRecordList(HomeManager.getInstance().getCurrentHome().getHomeID(), providers, currentTime, pagesize, new IDefaultCallBack2<IPCMotionDetectionRecordResponse>() {

                @Override
                public void onSuccess(IPCMotionDetectionRecordResponse ipcSosRecord) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    if (ipcSosRecord.getStatus() == 1) {
                        adapter.getData().addAll(ipcSosRecord.getResult());
                        if (adapter.getData().size() > 0) {
                            listviewEmpty.setVisibility(View.GONE);
                            tvVideoListHint.setVisibility(View.VISIBLE);
                        } else {
                            listviewEmpty.setVisibility(View.VISIBLE);
                            tvVideoListHint.setVisibility(View.GONE);
                        }
                        adapter.notifyDataSetChanged();
                        pullToRefreshLayout.setDelayHide(false);
                        pullToRefreshLayout.loadmoreFinish(PullToRefreshLayout.SUCCEED);
                    }
                }

                @Override
                public void onError(int i, String s) {
                    pullToRefreshLayout.setDelayHide(true);
                    pullToRefreshLayout.loadmoreFinish(PullToRefreshLayout.FAIL);
                }
            });

        }
    };

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.ipc_sos_list_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        adapter = new IpcSosRecordListAdapter(getDelegateActivity(), null);
        ipcSosRecordListview.setAdapter(adapter);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener(v -> close());
        rootView.findViewById(R.id.layout_alert_service).setOnClickListener(v -> clickCurrentPlanCard());
        rootView.findViewById(R.id.btn_manage).setOnClickListener(v -> clickCurrentPlanCard());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarLeft = rootView.findViewById(R.id.common_bar_left);
        commonTitleBar = rootView.findViewById(R.id.common_title_bar);
        pullIcon = rootView.findViewById(R.id.pull_icon);
        stateTv = rootView.findViewById(R.id.state_tv);
        stateIv = rootView.findViewById(R.id.state_iv);
        headView = rootView.findViewById(R.id.head_view);
        ipcSosRecordListview = rootView.findViewById(R.id.ipc_sos_record_listview);
        pullupIcon = rootView.findViewById(R.id.pullup_icon);
        loadingIcon = rootView.findViewById(R.id.loading_icon);
        loadingIcon2 = rootView.findViewById(R.id.loading_icon_2);
        loadstateTv = rootView.findViewById(R.id.loadstate_tv);
        loadstateIv = rootView.findViewById(R.id.loadstate_iv);
        loadmoreView = rootView.findViewById(R.id.loadmore_view);
        ipcSosRecordPulllayout = rootView.findViewById(R.id.ipc_sos_record_pulllayout);
        listviewEmpty = rootView.findViewById(R.id.listview_empty);
        tvVideoListHint = rootView.findViewById(R.id.tv_video_list_hint);
        llAlertService = rootView.findViewById(R.id.layout_alert_service);
        llAlertServiceBeta = rootView.findViewById(R.id.layout_alert_service_beta);
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        commonBarTitle.setLocalText(getResources().getString(R.string.video_list));
        ipcSosRecordListview.setIsCanPullUp(true);
        ipcSosRecordPulllayout.setOnRefreshListener(refreshListen);
        ipcSosRecordListview.setOnScrollListener(new AbsListView.OnScrollListener() {

            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                if (ipcSosRecordListview != null && ipcSosRecordListview.getLastVisiblePosition() != -1) {
                    if (ipcSosRecordListview.getLastVisiblePosition() == ipcSosRecordListview.getAdapter().getCount() - 1 &&
                            ipcSosRecordListview.getChildAt(ipcSosRecordListview.getChildCount() - 1).getBottom() <= ipcSosRecordListview.getHeight()) {
                        ipcSosRecordListview.setIsCanPullUp(true);
                    } else {
                        ipcSosRecordListview.setIsCanPullUp(false);
                    }
                }
            }
        });


        SwipeMenuCreator creator = new SwipeMenuCreator() {

            @Override
            public void create(SwipeMenu menu) {

                // create "delete" item
                SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                // set item background
                deleteItem.setBackground(R.color.colorDelete);
                // set item width
                deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                deleteItem.setTitleSize(13);

                deleteItem.setTitleColor(Color.WHITE);
                // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                deleteItem.setTitle(Local.s(getResources().getString(R.string.smart_plugs_list_delete)));
                // add to menu
                menu.addMenuItem(deleteItem);
            }
        };

//        set creator
        ipcSosRecordListview.setMenuCreator(creator);
        ipcSosRecordListview.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
        ipcSosRecordListview.setCloseInterpolator(new BounceInterpolator());
        ipcSosRecordListview.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                switch (i1) {
                    case 0:
                        // delete
                        toDeleteItem(i);

                        break;
                }
                // false : close the menu; true : not close the menu
                return false;
            }
        });
        ipcSosRecordListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                toItemClick(parent, view, position, id);
            }
        });

        listviewEmpty.setLocalText(getResources().getString(R.string.ipc_sos_record_empty));
        listviewEmpty.setVisibility(View.VISIBLE);
        tvVideoListHint.setLocalText(getString(R.string.video_list_page_hint));
        tvVideoListHint.setVisibility(View.GONE);

        if (CloudStorageServiceHelper.getInstance().getProductSchedules().size() == 0) {
            CloudStorageServiceHelper.getInstance().registerProductSchedulesListener(listProductSchedulesListener);
            CloudStorageServiceHelper.getInstance().fetchProductSchedules("");
        } else {
            initAlertServiceInfo();
        }
    }

    private void initAlertServiceInfo() {
        if (!AppConfig.Functions.SUPPORT_CLOUD_SERVICE || !(HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.ADMIN)) {
            return;
        }

        if (IPCManager.getInstance().getNotDeletedHearLaiList().size() == 0) {
            return;
        }

        if (!CloudStorageServiceHelper.getInstance().isHeartLaiServiceOpen()) {
            llAlertServiceBeta.setVisibility(View.VISIBLE);
            SpannableStringBuilder stringBuilder = new SpannableStringBuilder()
                    .append(StringStyle.format(getContext(), Local.s(getResources().getString(R.string.iap_cloud_storage_service_is_on)), R.style.iAPCurrentPlanDes1))
                    .append(" ")
                    .append(StringStyle.format(getContext(), Local.s(getResources().getString(R.string.iap_free_trial)), R.style.iAPCurrentPlanDes2));
            ((LocalTextView) llAlertServiceBeta.findViewById(R.id.tv_current_subs_plan)).setText(stringBuilder);
            ((LocalCustomButton) llAlertServiceBeta.findViewById(R.id.btn_change_subs_plan)).setLocalText(getString(R.string.iap_what_is_cloud_storage));
            llAlertServiceBeta.findViewById(R.id.btn_change_subs_plan).setOnClickListener(v -> clickCurrentPlanCard());
        } else {
            int cardCount = CloudStorageServiceHelper.getInstance().getProductSchedules().size();
            if (cardCount == 0) {
                llAlertService.setVisibility(View.GONE);
            } else {
                llAlertService.setVisibility(View.VISIBLE);
                ServiceCardItemModel listBean = CloudStorageServiceHelper.getInstance().getProductSchedules("").get(0);
                //2.1 有家庭服务卡
                //2.2 无家庭服务卡，显示有效期最近的设备服务卡状态
                llAlertService.findViewById(R.id.tv_name).setVisibility(View.VISIBLE);
                ((LocalTextView) llAlertService.findViewById(R.id.tv_name)).setLocalText(listBean.isFamilyService() ? getContext().getString(R.string.family_service) : listBean.getName());
                ((TextView) llAlertService.findViewById(R.id.tv_des)).setText(listBean.getServiceDateText(getContext()));
                ((TextView) llAlertService.findViewById(R.id.tv_des)).setTextColor(listBean.getServiceDateTextColor(getContext()));
                ((ImageView) llAlertService.findViewById(R.id.iv_expired)).setVisibility(listBean.isExpired() ? View.VISIBLE : View.GONE);

                //若有多个服务卡，则显示折叠样式
                if (cardCount > 1) {
                    llAlertService.setPadding(0, DensityUtils.dp2px(getContext(), 10), 0, 0);
                }
            }
        }
    }

    private void toDeleteItem(final int i) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        DinSDK.getHomeInstance().deleteMotionDetectionRecord(HomeManager.getInstance().getCurrentHome().getHomeID(), Collections.singletonList(adapter.getData().get(i).getEvent_id()), new IDefaultCallBack() {
                            @Override
                            public void onSuccess() {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                adapter.getData().remove(i);
                                adapter.notifyDataSetChanged();
                            }

                            @Override
                            public void onError(int i, String s) {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                            }
                        });
                    }
                })
                .preBuilder()
                .show();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getHomeInstance().getMotionDetectionRecordList(HomeManager.getInstance().getCurrentHome().getHomeID(), providers, currentTime, pagesize, new IDefaultCallBack2<IPCMotionDetectionRecordResponse>() {
            @Override
            public void onSuccess(IPCMotionDetectionRecordResponse ipcSosRecord) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (ipcSosRecord.getStatus() == 1) {
                    adapter.setData(ipcSosRecord.getResult());
                    adapter.notifyDataSetChanged();
                    if (adapter.getData().size() > 0) {
                        listviewEmpty.setVisibility(View.GONE);
                        tvVideoListHint.setVisibility(View.VISIBLE);
                    } else {
                        listviewEmpty.setVisibility(View.VISIBLE);
                        tvVideoListHint.setVisibility(View.GONE);
                    }
                } else {
//                    showErrorToast();
                }
            }

            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }
        });

    }

    public void toItemClick(AdapterView<?> adapterView, View view, int i, long l) {
//        showTimeOutLoadinFramgmentWithErrorAlert();
//        DinSDK.getHomeInstance().getMotionDetectionRecordVideoUrl(HomeManager.getInstance().getCurrentHome().getHomeID(), adapter.getData().get(i).getId(), new IDefaultCallBack2<String>() {
//            @Override
//            public void onSuccess(String s) {
//                closeTimeOutLoadinFramgmentWithErrorAlert();
//                DBUtil.Put(adapter.getData().get(i).getId(), true);
//                adapter.notifyDataSetChanged();
//                IPCHeartLaiMotionRecordIJKPlayerActivity2.start(getDelegateActivity(), s, adapter.getData().get(i).getIpcId(), adapter.getData().get(i).getProvider(), adapter.getData().get(i).getIpcname());
//                getMainActivity().setNotNeedToLogin(true);
//            }
//
//            @Override
//            public void onError(int i, String s) {
//                closeTimeOutLoadinFramgmentWithErrorAlert();
//                showErrorToast();
//            }
//        });

        DBUtil.Put(adapter.getData().get(i).getEvent_id(), true);
        adapter.notifyDataSetChanged();
        IPCHeartLaiMotionRecordIJKPlayerActivity2.start(getDelegateActivity(), adapter.getData().get(i).getEvent_id(), adapter.getData().get(i).getIpcId(), adapter.getData().get(i).getProvider(), adapter.getData().get(i).getIpcname());
        getMainActivity().setNotNeedToLogin(true);
    }

    public void close() {
        removeSelf();
    }

    public void clickCurrentPlanCard() {
        if (IPCManager.getInstance().getNotDeletedHearLaiList().size() == 0) {
            IapRootActivity.start(getMainActivity(), IapRootActivity.START_FRAG_BUY_IPC);
            // getDelegateActivity().addCommonFragment(BuyIPCFragment.newInstance());
            return;
        }

        if (!CloudStorageServiceHelper.getInstance().isHeartLaiServiceOpen()) {
            // getDelegateActivity().addCommonFragment(CloudStorageFragment.newInstance());
            IapRootActivity.start(getMainActivity(), IapRootActivity.START_FRAG_CLOUD_STORAGE);
            return;
        }

        IapRootActivity.start(getMainActivity(), IapRootActivity.START_FRAG_CLOUD_STORAGE_SERVICE);
        // getDelegateActivity().addCommonFragment(CloudStorageServiceFragment.newInstance());
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AlertServicePlanUpdateEvent alertServicePlanUpdateEvent) {
        initAlertServiceInfo();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        CloudStorageServiceHelper.getInstance().unregisterProductSchedulesListener(listProductSchedulesListener);
    }

    private CloudStorageServiceHelper.OnListProductSchedulesListener listProductSchedulesListener = new CloudStorageServiceHelper.OnListProductSchedulesListener() {
        @Override
        public void onUpdate(List<ServiceCardItemModel> data) {
            initAlertServiceInfo();
        }
    };
}


