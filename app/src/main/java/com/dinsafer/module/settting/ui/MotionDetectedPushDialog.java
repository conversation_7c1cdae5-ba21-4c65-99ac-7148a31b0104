package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafer.common.IPCManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.AlertServicePlanUpdateEvent;
import com.dinsafer.model.DeviceAlertServicePlanResponse;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.module.iap.IPCSubscriptionFragment;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module_dscam.bean.DsCamConst;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.Local;
import com.nostra13.universalimageloader.core.ImageLoader;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;


/**
 * Created by rinfon on 15/6/26.
 */
public class MotionDetectedPushDialog extends Dialog {
    private String TAG = getClass().getSimpleName();

    private Builder builder;
    private int layoutRes;
    private Context mContext;
    private ViewDataBinding mBinding;
    private ImageView ivLoading;
    private LocalTextView tvLoading;
    private LocalCustomButton btnOk;
    private LocalCustomButton btnCancel;
    private LocalCustomButton btnUpgrade;


    private MotionDetectedPushDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        EventBus.getDefault().register(this);
        mContext = context;
        this.builder = builder;

        Configuration configuration = mContext.getResources().getConfiguration();
        if (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            this.layoutRes = R.layout.dialog_motion_detected_push_land;
        } else {
            this.layoutRes = R.layout.dialog_motion_detected_push;
        }

        mBinding = DataBindingUtil.inflate(LayoutInflater.from(mContext), layoutRes, null, false);
        setContentView(mBinding.getRoot());
        mBinding.getRoot().findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        try {
            ImageLoader.getInstance().displayImage(builder.imgUrl, (ImageView) mBinding.getRoot().findViewById(R.id.img_view));
        } catch (Exception e) {
        }

        ((LocalTextView) mBinding.getRoot().findViewById(R.id.dialog_hint)).setLocalText(builder.mTitle);
        ((LocalTextView) mBinding.getRoot().findViewById(R.id.dialog_content)).setLocalText(builder.mContent);

        btnOk = mBinding.getRoot().findViewById(R.id.ok_btn);
        btnOk.setLocalText(builder.mOK);
        btnOk.setVisibility(!TextUtils.isEmpty(builder.mOK) ? View.VISIBLE : View.GONE);
        btnOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onClick(MotionDetectedPushDialog.this);
                }
            }
        });

        btnCancel = mBinding.getRoot().findViewById(R.id.cancel_btn);
        btnCancel.setLocalText(builder.mCancel);
        btnCancel.setVisibility(!TextUtils.isEmpty(builder.mCancel) ? View.VISIBLE : View.GONE);
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.cancelClick != null) {
                    builder.cancelClick.onClick(MotionDetectedPushDialog.this);
                }
            }
        });

        ivLoading = mBinding.getRoot().findViewById(R.id.iv_loading);
        tvLoading = mBinding.getRoot().findViewById(R.id.tv_loading);
        if (builder.showLoading) {
            setToLoading();
        } else {
            ivLoading.clearAnimation();
            ivLoading.setVisibility(View.GONE);
            tvLoading.setVisibility(View.GONE);
        }

        initAlertServiceInfo();

        if (builder.onDialogInitListener != null) {
            builder.onDialogInitListener.onDialogInit(mBinding);
        }
    }

    private void initAlertServiceInfo() {
        if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE) {
            DeviceAlertServicePlanResponse.AlertServicePlanInfo alertServicePlanInfo = CommonDataUtil.getInstance().getAlertServicePlanInfo();
            if (alertServicePlanInfo == null) {
                return;
            }
//
            if (alertServicePlanInfo.getTotalRemain() >= 60) {
                return;
            }

            mBinding.getRoot().findViewById(R.id.cv_plan_info).setVisibility(View.VISIBLE);
            String countStr = String.valueOf(alertServicePlanInfo.getTotalRemain());
            countStr = " " + countStr + " ";
            BackgroundColorSpan backgroundColorSpan = new BackgroundColorSpan(ContextCompat.getColor(mContext, R.color.ipc_subscription_motion_push_dialog_alert_count_bg));
            ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.text_blue_1));
            SpannableString des = new SpannableString(Local.s(mContext.getString(R.string.ipc_subscription_alert_service_remain)).replace("#remainder", countStr));
            String desStr = des.toString();
            int startIndex = desStr.indexOf(countStr);
            int endIndex = startIndex + countStr.length();
            des.setSpan(backgroundColorSpan, startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            des.setSpan(foregroundColorSpan, startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            ((TextView) mBinding.getRoot().findViewById(R.id.tv_remain_des)).setText(des);

            btnUpgrade = mBinding.getRoot().findViewById(R.id.btn_upgrade);
            btnUpgrade.setLocalText(mContext.getString(R.string.ipc_subscription_upgrade_service));
            btnUpgrade.setOnClickListener(v -> {
                dismiss();
                EventBus.getDefault().post(new PlayMotionDetectedRecordEvent());
                if (mContext instanceof MainActivity) {
                    ((MainActivity) mContext).addCommonFragment(IPCSubscriptionFragment.newInstance());
                } else {
                    MainActivity activity = (MainActivity) ActivityController.getInstance().getActivity(MainActivity.class);
                    if (activity != null) {
                        activity.addCommonFragment(IPCSubscriptionFragment.newInstance());
                    }
                }
            });
        } else {
            mBinding.getRoot().findViewById(R.id.cv_plan_info).setVisibility(View.GONE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AlertServicePlanUpdateEvent event) {
        initAlertServiceInfo();
    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        super.cancel();
    }

    public interface OnDialogInitListener {
        void onDialogInit(ViewDataBinding binding);
    }

    public interface AlertClickCallback {
        void onClick(MotionDetectedPushDialog dialog);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(HadLogoutEvent ev) {
        try {
            dismiss();
        } catch (Exception e) {

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCListUpdateEvent ev) {
        if (!builder.showLoading || TextUtils.isEmpty(builder.provider)) {
            return;
        }

        Device device;
        if (builder.provider.toLowerCase().startsWith(DsCamConst.PROVIDER_DSCAM) && IPCListUpdateEvent.DEVICE_TYPE_DSCAM == ev.getDeviceType()) {
            device = IPCManager.getInstance().getDsCamDeviceByID(builder.deviceId);
        } else if (HeartLaiConstants.PROVIDER_HEARTLAI.equals(builder.provider) && IPCListUpdateEvent.DEVICE_TYPE_HEARTLAI == ev.getDeviceType()) {
            device = IPCManager.getInstance().getHeartLaiDeviceByPID(builder.deviceId);
        } else {
            return;
        }
        if (device != null) {
            setToConnectSuccess();
        } else {
            setToConnectFail();
        }

    }

    private void setToLoading() {
        ivLoading.setVisibility(View.VISIBLE);
        ivLoading.clearAnimation();
        ivLoading.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.rotating));
        tvLoading.setVisibility(View.VISIBLE);
        tvLoading.setLocalText(R.string.ipc_connecting);
        setButtonEnable(false);
    }

    private void setToConnectFail() {
        ivLoading.setVisibility(View.GONE);
        ivLoading.clearAnimation();
        tvLoading.setVisibility(View.VISIBLE);
        tvLoading.setLocalText(R.string.ds_config_failed_device);
        setButtonEnable(false);
    }

    private void setToConnectSuccess() {
        ivLoading.setVisibility(View.GONE);
        ivLoading.clearAnimation();
        tvLoading.setVisibility(View.GONE);
        setButtonEnable(true);
    }

    private void setButtonEnable(boolean enable) {
        if (enable) {
            btnOk.setEnabled(true);
            btnOk.setAlpha(1.0f);
            btnCancel.setEnabled(true);
            btnCancel.setAlpha(1.0f);
            if (btnUpgrade != null) {
                btnUpgrade.setEnabled(true);
                btnUpgrade.setAlpha(1.0f);
            }
        } else {
            btnOk.setEnabled(false);
            btnOk.setAlpha(0.5f);
            btnCancel.setEnabled(false);
            btnCancel.setAlpha(0.5f);
            if (btnUpgrade != null) {
                btnUpgrade.setEnabled(false);
                btnUpgrade.setAlpha(0.5f);
            }
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        EventBus.getDefault().unregister(this);
        ivLoading.clearAnimation();
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mTitle;

        private String mOK;

        private String mCancel;

        private String imgUrl;

        private boolean isAutoDismiss = true;

        /**
         * 是否显示loading,ipc的移动侦测弹窗用到
         */
        private boolean showLoading = false;

        private String provider;

        private String deviceId;

        private AlertClickCallback okClick;

        private AlertClickCallback cancelClick;

        private OnDialogInitListener onDialogInitListener;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setCancelListener(AlertClickCallback listener) {
            this.cancelClick = listener;
            return this;
        }

        public Builder setOnDialogInitListener(OnDialogInitListener listener) {
            this.onDialogInitListener = listener;
            return this;
        }


        public Builder setOk(String ok) {
            mOK = ok;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            return this;
        }

        public Builder setImgUrl(String url) {
            imgUrl = url;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setTitle(String title) {
            mTitle = title;
            return this;
        }

        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public Builder setShowLoading(boolean showLoading) {
            this.showLoading = showLoading;
            return this;
        }

        public Builder setDeviceId(String deviceId) {
            this.deviceId = deviceId;
            return this;
        }

        public Builder setProvider(String provider) {
            this.provider = provider;
            return this;
        }

        public MotionDetectedPushDialog preBuilder() {
            MotionDetectedPushDialog alertDialog = new MotionDetectedPushDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }
}
