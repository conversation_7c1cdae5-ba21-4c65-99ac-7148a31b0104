package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.BounceInterpolator;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.IPCKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.PlugsData;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.model.SmartButtonActionChangeEvent;
import com.dinsafer.model.SmartButtonActionDeleteEvent;
import com.dinsafer.model.SmartButtonSceneData;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.module.settting.adapter.SimplePlugsItem;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.NavigatorUtil;
import com.dinsafer.util.SettingInfoHelper;
import com.dinsafer.util.SmartButtonUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by Rinfon on 16/7/12.
 */
public class SimplePlugsListFragment extends BaseTimeoutPluginFragment
        implements ModifyASKPlugsFragment.ICallBack {

    LocalTextView commonBarTitle;

    SwipeMenuListView smartPlugsList;
    LocalTextView listviewEmpty;
    ImageView smartListBottomLeftIcon;
    LocalTextView smartListBottomText;
    ImageView smartListBottomLine;
    ImageView smartListBottomGoto;
    RelativeLayout smartListBottomBtn;

    private ArrayList<PlugsData> mData;
    private SimplePlugsItem plugsItem;
    private int messageIndex;

    private static final String ASK_REMOTE_CONTROL_3A = "3A";

    private ArrayList<Device> mPluginDevices;
    private boolean selfOperate;
    private int mPluginType;

    public static SimplePlugsListFragment newInstance(int type, String title) {
        SimplePlugsListFragment simplePlugsListFragment = new SimplePlugsListFragment();
        Bundle args = new Bundle();
        args.putInt("type", type);
        args.putString("title", title);
        simplePlugsListFragment.setArguments(args);
        return simplePlugsListFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.smart_list_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        __bindViews(inflateView);
        __bindClicks(inflateView);
        showBlueTimeOutLoadinFramgment();
        mPluginType = getArguments().getInt("type");
        findPanel();
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener(v -> toClose());
        rootView.findViewById(R.id.smart_list_bottom_btn).setOnClickListener(v -> toCustomRemote());
        smartPlugsList.setOnItemClickListener((parent, view, position, id) -> toChangePlugName(position));
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        smartPlugsList = rootView.findViewById(R.id.smart_plugs_list);
        listviewEmpty = rootView.findViewById(R.id.listview_empty);
        smartListBottomLeftIcon = rootView.findViewById(R.id.smart_list_bottom_left_icon);
        smartListBottomText = rootView.findViewById(R.id.smart_list_bottom_text);
        smartListBottomLine = rootView.findViewById(R.id.smart_list_bottom_line);
        smartListBottomGoto = rootView.findViewById(R.id.smart_list_bottom_goto);
        smartListBottomBtn = rootView.findViewById(R.id.smart_list_bottom_btn);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getArguments().getString("title"));
        listviewEmpty.setLocalText(getResources().getString(R.string.listview_empty));
        smartListBottomText.setLocalText(getResources().getString(R.string.setting_custom_btn));

        if ((SettingInfoHelper.getInstance().isRFPluginItemClickable()
                || IPCKey.SMART_BUTTON == mPluginType)
                && CommonDataUtil.getInstance().isPanelOnline()) {
            SwipeMenuCreator creator = new SwipeMenuCreator() {

                @Override
                public void create(SwipeMenu menu) {

                    // create "delete" item
                    SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                    // set item background
                    deleteItem.setBackground(R.color.colorDelete);
                    // set item width
                    deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                    deleteItem.setTitleSize(13);

                    deleteItem.setTitleColor(Color.WHITE);
                    // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                    deleteItem.setTitle(Local.s(getResources().getString(R.string.smart_plugs_list_delete)));
                    // add to menu
                    menu.addMenuItem(deleteItem);
                }
            };

//        set creator
            smartPlugsList.setMenuCreator(creator);
            smartPlugsList.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
            smartPlugsList.setCloseInterpolator(new BounceInterpolator());
            smartPlugsList.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
                @Override
                public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                    switch (i1) {
                        case 0:
                            // delete
                            toDeleteItem(i);

                            break;
                    }
                    // false : close the menu; true : not close the menu
                    return false;
                }
            });
        }
//        listView.setEmptyView(listviewEmpty);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();

        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        int type = getArguments().getInt("type");
        if (type == IPCKey.WIRELESS) {
            createWirelessPlugsList();
        } else if (type == IPCKey.DOOR_BELL) {
            createDoorListPluginData();
        } else if (type == IPCKey.RELAY) {
            createRelayListPluginData();
        } else if (type == IPCKey.SMART_BUTTON) {
            createSmartButtonPluginData();
        } else if (type == IPCKey.RC_KEY) {
            createRcPluginData();
        } else if (type == IPCKey.KEYPAD_KEY) {
            createKeypadPluginData();
        } else {
            DDLog.e(TAG, "unknow type!!!!!!! type: " + type);
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOfflineEvent event) {
        DDLog.d(TAG, "DeviceOfflineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOnlineEvent event) {
        DDLog.d(TAG, "DeviceOnlineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    private void toDeleteItem(final int i) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        final Device device = DinHome.getInstance().getDevice(mData.get(i).getPlugId());
                        if (null != device) {
                            messageIndex = i;
                            DDLog.i(TAG, "Delete plugin.");
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            selfOperate = true;
                            device.submit(PanelParamsHelper.deletePlugin());
                        } else {
                            DDLog.e(TAG, "No plugin Device.");
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    /**
     * 初始化plugg数据列表
     */
    private void createWirelessPlugsList() {
        mData = new ArrayList<PlugsData>();
        plugsItem = new SimplePlugsItem(getActivity(), mData);
        smartPlugsList.setAdapter(plugsItem);
        smartListBottomBtn.setVisibility(View.GONE);
        mPluginDevices = new ArrayList<>();

        startTimeoutCountDown();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.WIRELESS_SIREN);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(SimplePlugsListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createWirelessPlugsList-getdata");
            if (null != mPanelDevice && 0 < mPluginDevices.size()) {
                PlugsData plug;
                for (Device device : mPluginDevices) {
                    plug = new PlugsData();
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    plug.setName(pluginName)
                            .setPlugId(device.getId())
                            .setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""))
                            .setSirenData(DeviceHelper.getString(device, PanelDataKey.WirelessSiren.SIREN_DATA, ""));
                    if (null != DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA)) {
                        plug.setTime(DeviceHelper.getLong(device, PanelDataKey.TIME_COMMON, 0));
                        plug.setHasSignalLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_SIGNAL_LEVEL, false));
                        plug.setCanTamper(DeviceHelper.getBoolean(device, PanelDataKey.CAN_TAMPER, false));
                        plug.setCanCharging(DeviceHelper.getBoolean(device, PanelDataKey.CAN_CHARGING, false));
                        plug.setHasBatteryLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_BATTERY_LEVEL, false));
                        plug.setHasWebsocketLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_WEB_SOCKET_LOADING, false));
                        plug.setCanReadyToArm(DeviceHelper.getBoolean(device, PanelDataKey.DoorSensor.CAN_READY_TO_ARM, false));
                        plug.setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                    }

                    mData.add(plug);
                }
            }

            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
            }
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }

    /**
     * 初始化door list plug数据列表
     */
    private void createDoorListPluginData() {
        mData = new ArrayList<PlugsData>();
        plugsItem = new SimplePlugsItem(getActivity(), mData);
        smartPlugsList.setAdapter(plugsItem);
        smartListBottomBtn.setVisibility(View.GONE);
        mPluginDevices = new ArrayList<>();

        startTimeoutCountDown();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.DOORBELL);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(SimplePlugsListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createDoorListPluginData-getdata");
            if (null != mPanelDevice && 0 < mPluginDevices.size()) {
                PlugsData plug;
                for (Device device : mPluginDevices) {
                    plug = new PlugsData();
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    plug.setName(pluginName)
                            .setPlugId(device.getId())
                            .setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""));
                    mData.add(plug);
                }
            }

            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
            }
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }

    public void toChangePlugName(int index) {
        if (!SettingInfoHelper.getInstance().isRFPluginItemClickable()
                && IPCKey.SMART_BUTTON != mPluginType) {
            DDLog.e(TAG, "当前权限下Item不能被点击哦$_$");
            return;
        }

        if (!CommonDataUtil.getInstance().isPanelOnline()) {
            DDLog.e(TAG, "当前主机离线，Item不能被点击哦");
            return;
        }

        messageIndex = index;
        if (getArguments().getInt("type") == IPCKey.DOOR_BELL) {
            ModifyDoorBellFragment fragment =
                    ModifyDoorBellFragment.newInstance(index, mData.get(index).getName(),
                            mData.get(index).getPlugId(), false, mData.get(index).getAskData().toString());
            fragment.setCallBack(this);
            getMainActivity().addCommonFragment(fragment);
        } else if (getArguments().getInt("type") == IPCKey.RELAY) {
            //继电器调整进控制界面
            PlugsData plugsData = mData.get(index);
            RelayControlFragment fragment = RelayControlFragment.newInstance(
                    index,
                    plugsData.getName(),
                    DDJSONUtil.getString(plugsData.getAskData(), "id"),
                    plugsData.getSendid(),
                    DDJSONUtil.getString(plugsData.getAskData(), "stype"));
            fragment.setIRelayCallback(new RelayControlFragment.IRelayCallback() {
                @Override
                public void onDeleteRelay(int index) {
                    mData.remove(index);
                    plugsItem.notifyDataSetChanged();
                    if (mData.size() <= 0) {
                        listviewEmpty.setVisibility(View.VISIBLE);
                    } else {
                        listviewEmpty.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onChangeRelayName(int index, String name) {
                    onChangeName(index, name);
                }
            });
            getMainActivity().addCommonFragment(fragment);
        } else if (mData.get(index).getAskData() != null) {
            Builder builder = new Builder();
            if ("21".equals(DDJSONUtil.getString(mData.get(index).getAskData(), "stype"))
                    || "22".equals(DDJSONUtil.getString(mData.get(index).getAskData(), "stype"))) {
                JSONObject data = mData.get(index).getAskData();
                if (TextUtils.isEmpty(
                        DDJSONUtil.getString(mData.get(index).getAskData(), "advancesetting"))) {
                    toGetSirentData(data);
                }
                builder.setId(DDJSONUtil.getString(mData.get(index).getAskData(), "id"))
                        .setAdd(false)
                        .setOffical(true)
                        .setOffline(false)
                        .setLowPower(false)
                        .setMessageIndex(messageIndex)
                        .setShowDelete(true)
                        .setName(mData.get(index).getName())
                        .setShowwave(false)
                        .setShowSiren(true)
                        .setShowSirenTest(
                                DDJSONUtil.getBoolean(mData.get(index).getAskData(), "test"))
                        .setData(mData.get(index).getAskData());
            } else {
                boolean isOffline = false;
                boolean isLowPower = false;

                if ((DDJSONUtil.has(mData.get(index).getAskData(), "keeplive")
                        && !DDJSONUtil.getBoolean(mData.get(index).getAskData(), "keeplive")
                        && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE
                        && !DDJSONUtil.getString(mData.get(index).getAskData(), "stype").equals("1C"))
                        || mData.get(index).isLoadStatusError()) {
                    isOffline = true;
                } else if (DDJSONUtil.has(mData.get(index).getAskData(), "power")
                        && !DDJSONUtil.getBoolean(mData.get(index).getAskData(), "power")) {
                    isLowPower = true;
                }
                // 如果是有电量值的配件，优先以电量值的电量转态为标准
                if (mData.get(index).isHasBatteryLevel()) {
                    isLowPower = mData.get(index).isBatteryLevelLowBattery();
                }

                builder.setId(DDJSONUtil.getString(mData.get(index).getAskData(), "id"))
                        .setAdd(false)
                        .setOffical(true)
                        .setOffline(isOffline)
                        .setLowPower(isLowPower)
                        .setMessageIndex(messageIndex)
                        .setShowDelete(true)
                        .setName(mData.get(index).getName())
                        .setShowwave(false)
                        .setShowSiren(!TextUtils.isEmpty(
                                DDJSONUtil.getString(mData.get(index).getAskData(), "advancesetting")))
                        .setShowSirenTest(
                                DDJSONUtil.getBoolean(mData.get(index).getAskData(), "test"))
                        .setData(mData.get(index).getAskData());
            }
            if (getArguments().getInt("type") == IPCKey.SMART_BUTTON) {
                // SmartButton
                builder.setAskPlugin(true);
                SmartButtonSceneFragment fragment = SmartButtonSceneFragment.newInstance(builder, IPCKey.SMART_BUTTON);
                fragment.setCallBack(this);
                getMainActivity().addCommonFragment(fragment);
            } else if (getArguments().getInt("type") == IPCKey.RC_KEY
                    && ASK_REMOTE_CONTROL_3A.equals(DDJSONUtil.getString(mData.get(index).getAskData(),
                    NetKeyConstants.NET_KEY_S_TYPE))) {
                // 五键遥控
                builder.setAskPlugin(true);
                SmartButtonSceneFragment fragment = SmartButtonSceneFragment.newInstance(builder,
                        IPCKey.RC_KEY, mData.get(index).getSettledAction());
                fragment.setCallBack(this);
                getMainActivity().addCommonFragment(fragment);
            } else {
                if (mData.get(index).getAskData() != null) {
                    builder.setAskPlugin(true);
                }
                ModifyASKPlugsFragment modifyASKPlugsFragment = ModifyASKPlugsFragment.newInstance(builder);
                modifyASKPlugsFragment.setCallBack(this);
                getDelegateActivity().addCommonFragment(modifyASKPlugsFragment);
            }
        } else {
            String decodeId = mData.get(index).getDecodeid();
            if (!TextUtils.isEmpty(decodeId)) {
                String pluginId, oldPluginId;
                if (CommonDataUtil.getInstance().checkIsSirenByDecodeId(decodeId)) {
                    // 警笛
                    pluginId = DDSecretUtil.hexStrToStr64(decodeId);
                    oldPluginId = mData.get(index).getPlugId();
                } else {
                    // 非警笛
                    pluginId = mData.get(index).getPlugId();
                    oldPluginId = DDSecretUtil.hexStrToStr64(decodeId);
                }
                NavigatorUtil.getInstance().toModifyPlugsNameFragment(mData.get(index).getName(),
                        pluginId, oldPluginId, false, mData.get(index).getSirenData(), true);
            } else {
                NavigatorUtil.getInstance().toModifyPlugsNameFragment(mData.get(index).getName(),
                        mData.get(index).getPlugId(), false, mData.get(index).getSirenData(), true);
            }
        }
    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (null != mPluginDevices && 0 < mPluginDevices.size()) {
            for (Device device : mPluginDevices) {
                device.unregisterDeviceCallBack(this);
            }
        }
        int type = getArguments().getInt("type");
        if (type == IPCKey.WIRELESS) {
            DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.WIRELESS_SIREN);
        } else if (type == IPCKey.DOOR_BELL) {
            DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.DOORBELL);
        } else if (type == IPCKey.RELAY) {
            DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.ROLLER_SHUTTER);
        } else if (type == IPCKey.SMART_BUTTON) {
            DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.SMART_BUTTON);
        } else if (type == IPCKey.RC_KEY) {
            DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.REMOTE_CONTROL);
        } else if (type == IPCKey.KEYPAD_KEY) {
            DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.KEYBOARD_KEY_TAGS);
        } else {
            DDLog.e(TAG, "unknow type!!!!!!! type: " + type);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PlugsNameChangeEvent ev) {
        plugsItem.changeName(messageIndex, ev.getName());
    }

    public void toCustomRemote() {
        getDelegateActivity().addCommonFragment(CustomRemoteListFragment.newInstance());
    }

    @Override
    public void onDeletePlug(String id) {
        if (mData != null && mData.size() > 0) {
            int index = -1;
            for (int i = 0; i < mData.size(); i++) {
                if (!TextUtils.isEmpty(mData.get(i).getPlugId()) && mData.get(i).getPlugId().equals(id)) {
                    index = i;
                    break;
                }
            }
            if (index >= 0 && index < mData.size()) {
                mData.remove(index);
                plugsItem.setData(mData);
                plugsItem.notifyDataSetChanged();

                if (!hasPlugin()) {
                    listviewEmpty.setVisibility(View.VISIBLE);
                    smartListBottomBtn.setVisibility(View.GONE);
                } else {
                    listviewEmpty.setVisibility(View.GONE);
                    smartListBottomBtn.setVisibility(View.GONE);
                }
            }
        }
    }

    private boolean hasPlugin() {
        if (mData == null || mData.size() == 0) {
            return false;
        }
        for (PlugsData mDatum : mData) {
            if (!TextUtils.isEmpty(mDatum.getPlugId())) {
                return true;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPluginDeleteEvent(PluginDeleteEvent pluginDeleteEvent) {
        try {
            onDeletePlug(pluginDeleteEvent.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onChangeName(int index, String name) {
        plugsItem.changeName(index, name);
    }


    private void toGetSirentData(JSONObject data) {
        int disArmTone = DDJSONUtil.getBoolean(data, "disarm_tone") ? 1 : 0;
        int homeTone = DDJSONUtil.getBoolean(data, "homearm_tone") ? 1 : 0;
        int armTone = DDJSONUtil.getBoolean(data, "arm_tone") ? 1 : 0;
        int sosTime = DDJSONUtil.getInt(data, "sos_time");
        int disArmLight = DDJSONUtil.getInt(data, "disarm_light");
        int sosLight = DDJSONUtil.getInt(data, "sos_light");
        int homeArmTime = DDJSONUtil.getInt(data, "homearm_light");
        int armTime = DDJSONUtil.getInt(data, "arm_light");
        int promptVolume = DDJSONUtil.getInt(data, "prompt_volume");
        int alarmVolume = DDJSONUtil.getInt(data, "alarm_volume");
//                    第一项


        String sosTimeString = Integer.toBinaryString(sosTime);
        if (sosTimeString.length() < 5) {
            int length = 5 - sosTimeString.length();
            for (int i = 0; i < length; i++) {
                sosTimeString = "0" + sosTimeString;
            }
        }

        String firstByte = Integer.toBinaryString(disArmTone) +
                Integer.toBinaryString(homeTone) +
                Integer.toBinaryString(armTone) +
                sosTimeString;

        String firstHex = Integer.toHexString(Integer.valueOf(firstByte, 2));
        if (firstHex.length() < 2) {
//                        前面补0
            firstHex = "0" + firstHex;
        }

        String twoByte = DDSecretUtil.intToByte(disArmLight) +
                DDSecretUtil.intToByte(sosLight) +
                DDSecretUtil.intToByte(homeArmTime) +
                DDSecretUtil.intToByte(armTime);

        String twoHex = Integer.toHexString(Integer.valueOf(twoByte, 2));
        if (twoHex.length() < 2) {
//                        前面补0
            twoHex = "0" + twoHex;
        }

//                    4位0为扩展位
        String threeByte = DDSecretUtil.intToByte(promptVolume) +
                DDSecretUtil.intToByte(alarmVolume) + "0000";

        String threeHex = Integer.toHexString(Integer.valueOf(threeByte, 2));
        if (threeHex.length() < 2) {
//                        前面补0
            threeHex = "0" + threeHex;
        }

        try {
            data.put("advancesetting", firstHex + "," + twoHex + "," + threeHex);
            data.remove("disarm_tone");
            data.remove("homearm_tone");
            data.remove("arm_tone");
            data.remove("sos_time");
            data.remove("disarm_light");
            data.remove("sos_light");
            data.remove("homearm_light");
            data.remove("arm_light");
            data.remove("prompt_volume");
            data.remove("alarm_volume");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    /**
     * 初始化 继电器 数据列表
     */
    private void createRelayListPluginData() {
        mData = new ArrayList<PlugsData>();
        plugsItem = new SimplePlugsItem(getActivity(), mData);
        smartPlugsList.setAdapter(plugsItem);
        smartListBottomBtn.setVisibility(View.GONE);
        mPluginDevices = new ArrayList<>();

        startTimeoutCountDown();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.ROLLER_SHUTTER);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(SimplePlugsListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createRelayListPluginData-getdata");
            if (null != mPanelDevice && 0 < mPluginDevices.size()) {
                PlugsData plug;
                for (Device device : mPluginDevices) {
                    plug = new PlugsData();
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    plug.setName(pluginName)
                            .setPlugId(device.getId())
                            .setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA))
                            .setRelay(true)
                            .setSendid(DeviceHelper.getString(device, PanelDataKey.SEND_ID, ""));
                    mData.add(plug);
                }
            }

            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
            }
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }

    /**
     * 初始化SmartButton列表
     */
    private void createSmartButtonPluginData() {
        DDLog.i(TAG, "createSmartButtonPluginData");
        mData = new ArrayList<>();
        plugsItem = new SimplePlugsItem(getActivity(), mData);
        smartPlugsList.setAdapter(plugsItem);
        smartListBottomBtn.setVisibility(View.GONE);
        mPluginDevices = new ArrayList<>();

        startTimeoutCountDown();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.SMART_BUTTON);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(SimplePlugsListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
                    DDLog.i(TAG, "plugin size: " + mPluginDevices.size());
                    PlugsData plugData;
                    if (null != mPluginDevices && 0 < mPluginDevices.size()) {
                        for (Device device : mPluginDevices) {
                            String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                            if (TextUtils.isEmpty(pluginName)) {
                                pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                            }
                            plugData = new PlugsData();
                            plugData.setName(pluginName)
                                    .setPlugId(device.getId())
                                    .setHasSignalLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_SIGNAL_LEVEL, false))
                                    .setCanTamper(DeviceHelper.getBoolean(device, PanelDataKey.CAN_TAMPER, false))
                                    .setCanCharging(DeviceHelper.getBoolean(device, PanelDataKey.CAN_CHARGING, false))
                                    .setHasBatteryLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_BATTERY_LEVEL, false))
                                    .setHasWebsocketLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_WEB_SOCKET_LOADING, false))
                                    .setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                            mData.add(plugData);
                        }
                    }
                    if (mData.size() <= 0) {
                        listviewEmpty.setVisibility(View.VISIBLE);
                    } else {
                        listviewEmpty.setVisibility(View.GONE);
                    }
                    plugsItem.notifyDataSetChanged();
                    closeLoadingFragment();
                }
        );

    }


    @Override
    protected void onPluginStatusUpdate() {
        plugsItem.setData(mData);
        plugsItem.notifyDataSetChanged();
    }

    @Override
    protected ArrayList<PlugsData> getCurrentPluginList() {
        return mData;
    }

    /**
     * 初始化遥控器页面数据
     * 暂时和{@link #createKeypadPluginData()} ()} 请求同一个接口，代码区分数据
     */
    private void createRcPluginData() {
        DDLog.i(TAG, "createRcPluginData");
        mData = new ArrayList<>();
        plugsItem = new SimplePlugsItem(getActivity(), mData);
        smartPlugsList.setAdapter(plugsItem);
        mPluginDevices = new ArrayList<>();
        startTimeoutCountDown();
        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.REMOTE_CONTROL);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(SimplePlugsListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createRcPluginData-getdata");
            if (null != mPanelDevice && 0 < mPluginDevices.size()) {
                PlugsData plug;
                SmartButtonSceneData smartButtonSceneData;

                for (Device device : mPluginDevices) {
                    plug = new PlugsData();
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    plug.setName(pluginName)
                            .setPlugId(device.getId())
                            .setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""))
                            .setSirenData(DeviceHelper.getString(device, PanelDataKey.WirelessSiren.SIREN_DATA, ""));
                    if (null != DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA)) {
                        plug.setTime(DeviceHelper.getLong(device, PanelDataKey.TIME_COMMON, 0));
                        plug.setHasSignalLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_SIGNAL_LEVEL, false));
                        plug.setCanTamper(DeviceHelper.getBoolean(device, PanelDataKey.CAN_TAMPER, false));
                        plug.setCanCharging(DeviceHelper.getBoolean(device, PanelDataKey.CAN_CHARGING, false));
                        plug.setHasBatteryLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_BATTERY_LEVEL, false));
                        plug.setHasWebsocketLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_WEB_SOCKET_LOADING, false));
                        plug.setCanReadyToArm(DeviceHelper.getBoolean(device, PanelDataKey.DoorSensor.CAN_READY_TO_ARM, false));
                        plug.setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));

                        smartButtonSceneData = SmartButtonUtil.parseSceneDataForRcFromJson(
                                getContext(), DDJSONUtil.getJSONObject(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA),
                                        SmartButtonUtil.SERVICE_ACTION_SINGLE_PRESS));
                        plug.setSettledAction(smartButtonSceneData);
                    }

                    mData.add(plug);
                }
            }

            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
                smartListBottomBtn.setVisibility(View.GONE);
            }
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }

    /**
     * 初始化键盘页面数据
     * 暂时和{@link #createRcPluginData()} 请求同一个接口，代码区分数据
     */
    private void createKeypadPluginData() {
        DDLog.i(TAG, "createKeypadPluginData");
        mData = new ArrayList<>();
        plugsItem = new SimplePlugsItem(getActivity(), mData);
        smartPlugsList.setAdapter(plugsItem);
        mPluginDevices = new ArrayList<>();

        startTimeoutCountDown();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.KEYBOARD_KEY_TAGS);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(SimplePlugsListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createKeypadPluginData-getdata");
            if (null != mPanelDevice && 0 < mPluginDevices.size()) {
                PlugsData plug;
                for (Device device : mPluginDevices) {
                    plug = new PlugsData();
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    plug.setName(pluginName)
                            .setPlugId(device.getId())
                            .setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""))
                            .setSirenData(DeviceHelper.getString(device, PanelDataKey.WirelessSiren.SIREN_DATA, ""));
                    if (null != DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA)) {
                        plug.setTime(DeviceHelper.getLong(device, PanelDataKey.TIME_COMMON, 0));
                        plug.setHasSignalLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_SIGNAL_LEVEL, false));
                        plug.setCanTamper(DeviceHelper.getBoolean(device, PanelDataKey.CAN_TAMPER, false));
                        plug.setCanCharging(DeviceHelper.getBoolean(device, PanelDataKey.CAN_CHARGING, false));
                        plug.setHasBatteryLevel(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_BATTERY_LEVEL, false));
                        plug.setHasWebsocketLoading(DeviceHelper.getBoolean(device, PanelDataKey.HAVE_WEB_SOCKET_LOADING, false));
                        plug.setCanReadyToArm(DeviceHelper.getBoolean(device, PanelDataKey.DoorSensor.CAN_READY_TO_ARM, false));
                        plug.setAskData(DeviceHelper.getJsonObject(device, PanelDataKey.ASK_DATA));
                    }

                    mData.add(plug);
                }
            }

            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
                smartListBottomBtn.setVisibility(View.GONE);
            }
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onActionChangedEvent(SmartButtonActionChangeEvent event) {
        DDLog.i(TAG, "onActionChangedEvent");
        if (null == mData
                || 0 >= mData.size()
                || null == event.getSmartButtonData()
                || TextUtils.isEmpty(event.getSmartButtonData().getTargetId())
                || null == event.getNewAction()) {
            return;
        }


        String pluginId = event.getSmartButtonData().getTargetId();
        for (int i = 0; i < mData.size(); i++) {
            if (pluginId.equals(mData.get(i).getPlugId())) {
                mData.get(i).setSettledAction(event.getNewAction());
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onActionDeletedEvent(SmartButtonActionDeleteEvent event) {
        DDLog.i(TAG, "onActionDeletedEvent");
        if (TextUtils.isEmpty(event.getPluginId())) {
            return;
        }

        String pluginId = event.getPluginId();
        for (int i = 0; i < mData.size(); i++) {
            if (pluginId.equals(mData.get(i).getPlugId())) {
                mData.get(i).setSettledAction(null);
            }
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || 1 != resultType) {
            return;
        }

        if (null != mPanelDevice
                && deviceId.equals(mPanelDevice.getId())) {
            onPanelCmdCallback(deviceId, cmd, map);
            return;
        }

        onPluginCmdCallback(deviceId, cmd, map);
    }

    private void onPanelCmdCallback(String deviceId, String cmd, Map map) {
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            return;
        }
        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
        if (PluginCmd.PLUGIN_STATE_CHANGE.equals(cmd)) {
            updatePluginBattery(result);
        }
    }

    /**
     * 配件CMD
     */
    private void onPluginCmdCallback(String deviceId, String cmd, Map map) {
        DDLog.i(TAG, "onPluginCmdCallback");
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            if (selfOperate) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
                selfOperate = false;
            }
            return;
        }

        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
        if (selfOperate && PluginCmd.PLUGIN_DELETE.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            plugsItem.remove(messageIndex);

            boolean hasRemoteControl = false;
            PlugsData plugsData;
            for (int i = 0; i < mData.size(); i++) {
                plugsData = mData.get(i);
                if (plugsData.getPlugId().startsWith("!")
                        && null != plugsData.getAskData()) {
                    String stype = DDJSONUtil.getString(plugsData.getAskData(), "stype");
                    if (ASK_REMOTE_CONTROL_3A.equals(stype)) {
                        hasRemoteControl = true;
                        break;
                    }
                }
            }

            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
                smartListBottomBtn.setVisibility(View.GONE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
                smartListBottomBtn.setVisibility(View.GONE);
            }
            plugsItem.notifyDataSetChanged();
            selfOperate = false;
            return;
        }

        if (PluginCmd.CHANGE_SIREN_SETTING.equals(cmd)) {
            DDLog.i(TAG, "CHANGE_SIREN_SETTING");
            try {
                JSONObject jsonObject = new JSONObject(result);
                final String operateCmd = DDJSONUtil.getString(jsonObject, PanelDataKey.CmdResult.OPERATION_CMD);
                if (LocalKey.SET_WIRELESS_SIREN_ADVANCED_SETTING.equals(operateCmd)) {
                    String pluginid = jsonObject.getString("pluginid");
                    for (int i = 0; i < mData.size(); i++) {
                        if (mData.get(i).getPlugId().equals(pluginid)) {
                            String sirenData = jsonObject.getString("plugin_item_wireless_siren_advanced_setting");
                            mData.get(i).setSirenData(sirenData);
                            break;
                        }
                    }
                } else {
                    String sendid = jsonObject.getString("sendid");
                    if (TextUtils.isEmpty(sendid)) {
                        DDLog.e(TAG, "null sendid");
                        return;
                    }
                    for (int i = 0; i < mData.size(); i++) {
                        String id = DDJSONUtil.getString(mData.get(i).getAskData(), "sendid");
                        if (sendid.equals(id)) {
                            String sirenData = jsonObject.getString("advancesetting");
                            mData.get(i).getAskData().put("advancesetting", sirenData);
                            break;
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }
}
