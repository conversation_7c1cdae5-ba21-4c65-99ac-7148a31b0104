package com.dinsafer.module.settting.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import com.dinsafer.model.RecordFile;

import java.util.List;

/**
 * Created by Casten on 15/3/4.
 */
public class RecordFileAdapter extends ArrayAdapter<RecordFile> {

    private int resourceId;

    public RecordFileAdapter(Context context, int resource, List<RecordFile> objects) {
        super(context, resource, objects);
        resourceId = resource;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        RecordFile file = getItem(position);
        TextView textView;
        if (convertView == null) {
            textView = (TextView) LayoutInflater.from(getContext()).inflate(resourceId, null);
        } else {
            textView = (TextView)convertView;
        }
        textView.setText(file.getDate());
        return textView;
    }
}
