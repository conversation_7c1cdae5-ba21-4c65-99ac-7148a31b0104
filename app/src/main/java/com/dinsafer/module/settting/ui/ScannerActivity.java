package com.dinsafer.module.settting.ui;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.qrcode.MLKitDecoderFactory;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ScannerLayoutBinding;
import com.dinsafer.model.QRRequestResultEvent;
import com.dinsafer.model.ScanQREvent;
import com.dinsafer.model.event.GoAddMoreEvent;
import com.dinsafer.model.event.GoShareQREvent;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module_dinfix.DinFix;
import com.dinsafer.module_dinfix.IDinFixResponseCallback;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.ReadlocalImage;
import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;


/**
 * Created by Rinfon on 16/7/7.
 */
public class ScannerActivity extends Activity implements BarcodeCallback {
    public static final int REQUEST_CODE_PICK_IMAGE_FROM_MEDIA = 1101;
    private final static String TAG = "ScannerActivity";

    private ScannerLayoutBinding mBinding;
    private boolean is_add_device = false;
    boolean isOpenFlash;

    // 0.添加设备  1. 添加家庭  2. 添加设备/家庭
    private int mFrom = 0;
    public static final int FROM_ADD_DEVICE = 0;
    public static final int FROM_ADD_FAMILY = 1;
    public static final int FROM_ADD_DEVICE_FAMILY = 2;

    /**
     * 启动扫描页
     *
     * @param isAddDevice 是否是添加主机 true: 添加主机； false: 添加配件
     */
    public static void startScan(MainActivity mainActivity, boolean isAddDevice, int from) {
        startScan(mainActivity, isAddDevice, from, false);
    }

    /**
     * 启动扫描页
     *
     * @param isAddDevice 是否是添加主机 true: 添加主机； false: 添加配件
     */
    public static void startScan(MainActivity mainActivity, boolean isAddDevice, int from, boolean isHideNoDevice) {
        if (null == mainActivity) {
            DDLog.e(TAG, "Can't open scan activity, because mainActivity is null.");
            return;
        }

        mainActivity.setNotNeedToLogin(true);
        if (mainActivity.needCameraPermission()) {
            // 申请相机，不管是否获得权限都进入扫描页
            mainActivity.requestCameraPermission((requestCode, permissions, grantResults) -> {
                mainActivity.setNotNeedToLogin(true);
                Intent intent = new Intent(mainActivity, ScannerActivity.class);
                intent.putExtra(LocalKey.IS_ADD_DEVICE, isAddDevice);
                intent.putExtra(LocalKey.FROM, from);
                intent.putExtra(LocalKey.IS_HIDE_NO_DEVICE, isHideNoDevice);
                mainActivity.startActivity(intent);
            });
        } else {
            mainActivity.setNotNeedToLogin(true);
            Intent intent = new Intent(mainActivity, ScannerActivity.class);
            intent.putExtra(LocalKey.IS_ADD_DEVICE, isAddDevice);
            intent.putExtra(LocalKey.FROM, from);
            intent.putExtra(LocalKey.IS_HIDE_NO_DEVICE, isHideNoDevice);
            mainActivity.startActivity(intent);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
//        getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
//        getWindow().addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        EventBus.getDefault().register(this);
        mBinding = DataBindingUtil.setContentView(this, R.layout.scanner_layout);

        mBinding.tvHelp.setLocalText(getString(R.string.help_title));

        mFrom = getIntent().getIntExtra(LocalKey.FROM, 0);
        boolean isHideNoDevice = getIntent().getBooleanExtra(LocalKey.IS_HIDE_NO_DEVICE, false);
        switch (mFrom) {
            case FROM_ADD_FAMILY:
                mBinding.tvInfo.setLocalText(getString(R.string.scan_the_family_qr_code));
                break;

            case FROM_ADD_DEVICE_FAMILY:
                mBinding.tvInfo.setLocalText(getString(R.string.add_device_or_family));
                break;

            default:
                mBinding.tvInfo.setLocalText(getString(R.string.scan_the_device_qr_code));
                break;
        }
        mBinding.tvNoQr.setVisibility((mFrom != FROM_ADD_FAMILY && !isHideNoDevice) ? View.VISIBLE : View.GONE);
        mBinding.tvAddFromInstaller.setVisibility((mFrom != FROM_ADD_FAMILY) ? View.VISIBLE : View.GONE);

        mBinding.commonBarBack.setOnClickListener(v -> finish());
        mBinding.tvHelp.setOnClickListener(v -> {
            startActivity(WebViewActivity.getStartIntent(this, APIKey.ADD_MORE_HELP_URL_V2));
        });
        mBinding.tvNoQr.setOnClickListener(view -> {
            EventBus.getDefault().post(new GoAddMoreEvent());
            finish();
        });
        mBinding.ivToggle.setOnClickListener(v -> toggleFlashLight());
        mBinding.ivAlbum.setOnClickListener(v -> checkAndRequestPermission());

        mBinding.zxingScanner.getBarcodeView().setDecoderFactory(new MLKitDecoderFactory());
        mBinding.zxingScanner.decodeSingle(this);
        mBinding.zxingScanner.setStatusText("");
        is_add_device = getIntent().getBooleanExtra(LocalKey.IS_ADD_DEVICE, false);

        if (!PermissionUtil.hasPermission(this, Manifest.permission.CAMERA)) {
            showPermissionNotGrantTip(getString(R.string.permission_tip_camera_not_grant));
        }

        mBinding.tvAddFromInstaller.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mBinding.flLoading.setVisibility(View.VISIBLE);
                DinFix.getInstance().genHomeQRCode(HomeManager.getInstance().getCurrentHome().getHomeID(),
                        new IDinFixResponseCallback<String>() {
                            @Override
                            public void onSuccess(String s) {
                                mBinding.flLoading.setVisibility(View.GONE);
                                GoShareQREvent goShareQREvent = new GoShareQREvent(s);
                                EventBus.getDefault().post(goShareQREvent);
                                finish();
                            }

                            @Override
                            public void onError(int i, String s) {
                                mBinding.flLoading.setVisibility(View.GONE);
                                showErrorToast();
                            }
                        });
            }
        });
    }

    private void checkAndRequestPermission() {
        if (PermissionUtil.isStoragePermissionDeny(this)) {
            requestReadImagesPermission(this);
        } else {
            toDecodeQRfromMedia();
        }
    }

    /**
     * 通过媒体库识别二维码
     */
    private void toDecodeQRfromMedia() {
        Intent mAddIntent = new Intent(Intent.ACTION_PICK,
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(mAddIntent, REQUEST_CODE_PICK_IMAGE_FROM_MEDIA);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE_PICK_IMAGE_FROM_MEDIA) {
            if (data != null) {
                try {
                    Uri uri = data.getData();
                    ReadlocalImage readlocalImage = new ReadlocalImage(this);
                    readlocalImage.decodeWithML(uri, new ReadlocalImage.BarcodeScanCallback() {
                        @Override
                        public void onSuccess(String rawValue) {
                            DDLog.i(TAG, "ReadlocalImage result:" + rawValue);
                            EventBus.getDefault().post(new ScanQREvent(is_add_device, rawValue));
                        }

                        @Override
                        public void onFail(String error) {
                            DDLog.e(TAG, "Error on scan qrCode from album:" + error);
                        }
                    });
                } catch (Exception e) {
                    DDLog.e(TAG, "Error on decode image from album.");
                    e.printStackTrace();
                }
            }
        }
    }

    public void requestReadImagesPermission(@NonNull final Activity activity) {
        final String[] permission = PermissionUtil.getStoragePermissions();
        requestStoragePermission(activity, permission);
    }

    public void requestReadVideoPermission(@NonNull final Activity activity) {
        final String[] permission = PermissionUtil.getStoragePermissions();
        requestStoragePermission(activity, permission);
    }

    public void requestStoragePermission(@NonNull final Activity activity, String[] permission) {
        if (null == permission) {
            return;
        }

        boolean denied = AndPermission.hasAlwaysDeniedPermission(activity, permission);
        AndPermission.with(activity)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    toDecodeQRfromMedia();
                })
                .onDenied(permissions -> {
                    DDLog.e(TAG, "Storage permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(activity, permissions)) {
                        showPermissionNotGrantTip(getString(R.string.permission_tip_album_not_grant));
                    }
                })
                .start();
    }

    protected void showPermissionNotGrantTip(String tip) {
        AlertDialog.createBuilder(this)
                .setOk(getString(R.string.go_setting))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        goIntentSetting();
                    }
                })
                .setCancel(getString(R.string.cancel))
                .setContent(tip)
                .preBuilder()
                .show();
    }

    private void goIntentSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        try {
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 开启关闭闪光灯
     */
    private void toggleFlashLight() {
        if (!isOpenFlash) {
            mBinding.ivToggle.setImageResource(R.drawable.icon_scan_qrcode_flashlight_on);
            mBinding.zxingScanner.setTorchOn();
            isOpenFlash = true;
        } else {
            mBinding.ivToggle.setImageResource(R.drawable.icon_scan_qrcode_flashlight_off);
            mBinding.zxingScanner.setTorchOff();
            isOpenFlash = false;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        mBinding.zxingScanner.setTorchOff();
    }

    /**
     * 修改参考：https://blog.csdn.net/weixin_39702651/article/details/83419371
     *
     * @param outState
     */
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        // 解决TransactionTooLargeException异常
//        super.onSaveInstanceState(outState);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mBinding.zxingScanner.resume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mBinding.zxingScanner.pause();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    public void barcodeResult(BarcodeResult result) {
        if (result.getText() != null) {
            String resultText = result.getText();
            EventBus.getDefault().post(new ScanQREvent(is_add_device, resultText));
        }
    }

    @Override
    public void possibleResultPoints(List<ResultPoint> resultPoints) {

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(GoAddMoreEvent ev) {
//        finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(QRRequestResultEvent event) {
        boolean isSuccess = event.isSuccess();
        if (isSuccess) {
            finish();
        } else {
            String msg = event.getMsg();
            if (TextUtils.isEmpty(msg)) {
                showErrorToast();
            } else {
                showToast(msg);
            }
        }
    }

    /**
     * 显示Toast消息
     *
     * @param msg 消息文本
     */
    public final void showToast(@NonNull final String msg) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.createBuilder(ScannerActivity.this)
                        .setOk(getResources().getString(R.string.ok))
                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                mBinding.zxingScanner.decodeSingle(ScannerActivity.this);
                            }
                        })
                        .setContent(msg)
                        .preBuilder()
                        .show();
            }
        });
    }

    private AlertDialog errorDialog;

    public final void showErrorToast() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (errorDialog != null && errorDialog.isShowing()) {
                    errorDialog.dismiss();
                }
                errorDialog = AlertDialog.createBuilder(ScannerActivity.this)
                        .setOk(getResources().getString(R.string.ok))
                        .setOKListener(() -> {
                            mBinding.zxingScanner.decodeSingle(ScannerActivity.this);
                        })
                        .setContent(getResources().getString(R.string.failed_try_again))
                        .preBuilder();
                errorDialog.show();
            }
        });
    }
}
