package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dinsafer.config.APIKey;
import com.dinsafer.config.IPCKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.SmartButtonActionChangeEvent;
import com.dinsafer.model.SmartButtonActionData;
import com.dinsafer.model.SmartButtonSceneData;
import com.dinsafer.model.SmartButtonTargetData;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.CustomizeSegmentTabLayout;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.VolumeControlerView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.SmartButtonUtil;
import com.dinsafer.util.viewanimator.AnimationBuilder;
import com.dinsafer.util.viewanimator.AnimationListener;
import com.dinsafer.util.viewanimator.ViewAnimator;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.github.sahasbhop.apngview.ApngDrawable;
import com.github.sahasbhop.apngview.ApngImageLoader;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

import static com.dinsafer.model.SmartButtonTargetData.TARGET_TYPE_TUYA_BULB;
import static com.dinsafer.model.SmartButtonTargetData.TARGET_TYPE_TUYA_PLUG;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_ACTION;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_ACTION_CONF;
import static com.dinsafer.util.SmartButtonUtil.SERVICE_KEY_CODE;

/**
 * SmartButton 定义SmartButton功能页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/10/22 3:48 PM
 */
public class SmartButtonSceneEditFragment extends BaseFragment implements IDeviceCallBack, VolumeControlerView.OnSelectedChangedListener {
    private static final String DATA_KEY_SMART_BUTTON_DATA = "smart_button_data";
    private static final String DATA_KEY_STYPE = "stype";
    private static final String DATA_KEY_SEND_ID = "sendid";
    private static final String DATA_KEY_TARGET_INFO = "target_info";
    private static final String DATA_KEY_PLUGIN_TYPE = "src_plugin_type";

    // 动作类型文本Id及Apng动画文件名
    private final int[] ACTION_TYPE_DES_IDS = {R.string.scene_action_single_click,
            R.string.scene_action_double_click, R.string.scene_action_long_click};
    private final String[] ACTION_TYPE_APNG_NAMES = {"assets://apng/animation_smartbtn_single.png",
            "assets://apng/animation_smartbtn_double.png", "assets://apng/animation_smartbtn_long.png"};
    // 操作类型下标定义
    private final int ACTION_TYPE_INDEX_SINGLE_PRESS = 0;
    private final int ACTION_TYPE_INDEX_DOUBLE_PRESS = 1;
    private final int ACTION_TYPE_INDEX_LONG_PRESS = 2;

    // 切换操作方式后，多久开启对应的Apng动画
    private final int DURATION_START_ANIM_MILLIS = 300;

    /**
     * 当前ActionSheet选择的内容类型
     */
    private final int CMD_NULL = 0;
    private final int CMD_COMMAND = 1;
    private final int CMD_DOORBELL_MUSIC = 2;

    LocalTextView commonBarTitle;
    CustomizeSegmentTabLayout stlActionTypeTab;
    ImageView ivActionType;
    LocalTextView tvCommand;
    LocalTextView tvCommandValue;
    ImageView ivCommandNor;
    LinearLayout llCommand;
    LocalTextView tvCustomerSetting1;
    LocalTextView tvCustomerSettingValue1;
    ImageView ivCustomerSettingNor1;
    LinearLayout llCustomerSetting1;
    LocalTextView tvCustomerSetting2;
    VolumeControlerView vcvVolume;
    LinearLayout llCustomerSetting2;
    LinearLayout llSettingDetail;
    LocalCustomButton btnTop;
    LocalCustomButton btnBottom;
    ImageView commonBarBack;
    LinearLayout tabSelectorL;
    LinearLayout llTabSelectorTop;
    ImageView ivTopSingleImage;

    private int mInitIndex; // 默认的操作类型下标
    private int mActionTypeIndex; // 当前选择的操作类型下标，0:单击; 1:双击; 2:长按
    private boolean mIsDoorbell; // 当前配置的是否是警笛
    private int mCurrentCmdType = CMD_NULL; // 通过ActionSheet选择的类型，可以为指令、音乐和音乐声音
    private int mSelectedCmdIndex, mSelectedSetting1Index, mSelectedSetting2Index;
    private String[] mCommands, mDoorBellMusics, mDoorBellVolumes; // ActionSheet数据源
    private int mSceneType;
    private String messageId;
    private Call<StringResponseEntry> mSaveCall;
    // SmartButton信息
    private SmartButtonTargetData mSmartButtonData;
    // Scene Item信息
    private SmartButtonSceneData mTargetInfo;
    // 切换TAB时延时开始动画，否则会卡顿
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private AnimationBuilder mCountAnim;
    private StringBuilder mSb = new StringBuilder();
    /**
     * 配件类型-控制对象
     * 目前有以下的类型：
     * {@link com.dinsafer.config.IPCKey#SMART_BUTTON}
     * {@link com.dinsafer.config.IPCKey#RC_KEY}
     */
    private int mSourcePluginType;

    private Device mPluginDevice;
    private boolean isSelfOperate;

    /**
     * @param smartButtonSceneData Scene item信息
     * @param smartButtonData      SmartButton 信息
     * @return
     */
    public static SmartButtonSceneEditFragment newInstance(@NonNull SmartButtonSceneData smartButtonSceneData,
                                                           @NonNull SmartButtonTargetData smartButtonData) {
        return newInstance(smartButtonSceneData, smartButtonData, IPCKey.SMART_BUTTON);
    }

    /**
     * @param smartButtonSceneData Scene item信息
     * @param smartButtonData      SmartButton 信息
     * @return
     */
    public static SmartButtonSceneEditFragment newInstance(@NonNull SmartButtonSceneData smartButtonSceneData,
                                                           @NonNull SmartButtonTargetData smartButtonData,
                                                           int srcPluginType) {
        SmartButtonSceneEditFragment fragment = new SmartButtonSceneEditFragment();
        Bundle args = new Bundle();
        args.putParcelable(DATA_KEY_TARGET_INFO, smartButtonSceneData);
        args.putParcelable(DATA_KEY_SMART_BUTTON_DATA, smartButtonData);
        args.putInt(DATA_KEY_PLUGIN_TYPE, srcPluginType);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.smart_button_scene_edit_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> onViewClick(v));
        rootView.findViewById(R.id.ll_command).setOnClickListener( v -> onViewClick(v));
        rootView.findViewById(R.id.ll_customer_setting1).setOnClickListener( v -> onViewClick(v));
        rootView.findViewById(R.id.btn_top).setOnClickListener( v -> onViewClick(v));
        rootView.findViewById(R.id.btn_bottom).setOnClickListener( v -> onViewClick(v));
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        stlActionTypeTab = rootView.findViewById(R.id.stl_action_type_tab);
        ivActionType = rootView.findViewById(R.id.iv_action_type);
        tvCommand = rootView.findViewById(R.id.tv_command);
        tvCommandValue = rootView.findViewById(R.id.tv_command_value);
        ivCommandNor = rootView.findViewById(R.id.iv_command_nor);
        llCommand = rootView.findViewById(R.id.ll_command);
        tvCustomerSetting1 = rootView.findViewById(R.id.tv_customer_setting1);
        tvCustomerSettingValue1 = rootView.findViewById(R.id.tv_customer_setting_value1);
        ivCustomerSettingNor1 = rootView.findViewById(R.id.iv_customer_setting_nor1);
        llCustomerSetting1 = rootView.findViewById(R.id.ll_customer_setting1);
        tvCustomerSetting2 = rootView.findViewById(R.id.tv_customer_setting2);
        vcvVolume = rootView.findViewById(R.id.vcv_volume);
        llCustomerSetting2 = rootView.findViewById(R.id.ll_customer_setting2);
        llSettingDetail = rootView.findViewById(R.id.ll_setting_detail);
        btnTop = rootView.findViewById(R.id.btn_top);
        btnBottom = rootView.findViewById(R.id.btn_bottom);
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        tabSelectorL = rootView.findViewById(R.id.tab_selector_l);
        llTabSelectorTop = rootView.findViewById(R.id.ll_selector_tab);
        ivTopSingleImage = rootView.findViewById(R.id.iv_single_top_image);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!isTopSingleImage()
                && isShowTabSelector()) {
            ApngImageLoader.getInstance()
                    .displayApng(ACTION_TYPE_APNG_NAMES[mInitIndex], ivActionType,
                            new ApngImageLoader.ApngConfig(1, true));
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
        if (null != mCountAnim) {
            mCountAnim.cancel();
        }
        toStopAnim();
        mHandler.removeCallbacksAndMessages(null);
    }

    @Override
    public void initData() {
        super.initData();
        showLoadingFragment(LoadingFragment.BLUE);
        getArgumentsFromOther();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mTargetInfo || null == mSmartButtonData) {
            DDLog.e(TAG, "Error get param form last page.");
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        // 页面动画完成后再开启动画，减少卡顿
        initParams();
        initLocalText();
        initViewsVisibility(mIsDoorbell);
        initSavedState();
        if (!isTopSingleImage()
                && isShowTabSelector()) {
            initTab();
            ApngImageLoader.getInstance()
                    .displayApng(ACTION_TYPE_APNG_NAMES[mInitIndex], ivActionType,
                            new ApngImageLoader.ApngConfig(1, true));
        }
        closeLoadingFragment();

        String smartButtonId = mSmartButtonData.getTargetId();
        if (TextUtils.isEmpty(smartButtonId)) {
            showErrorToast();
            return;
        }
        mPluginDevice = DinHome.getInstance().getDevice(smartButtonId);
        if (null != mPluginDevice) {
            mPluginDevice.registerDeviceCallBack(this);
        }
    }

    /**
     * 初始化页面中使用的数据
     */
    private void initParams() {
        DDLog.i(TAG, "initParams");
        SmartButtonActionData actionData = mTargetInfo.getActionData();

        mSceneType = mTargetInfo.getSceneType();
        mInitIndex = getActionIndexByActionType(actionData.getClickAction());
        mActionTypeIndex = mInitIndex;

        if (SmartButtonUtil.SCENE_TYPE_RING_BELL == mSceneType) {
            // 门铃，需要获取门铃音乐和音量集合
            mIsDoorbell = true;
            mDoorBellMusics = SmartButtonUtil.getDoorbellMusics();
            mDoorBellVolumes = SmartButtonUtil.getDoorbellVolumes();

            if (isEditAction()) {
                // 修改之前的Action
                mSelectedSetting1Index = actionData.getMusicIndex();
                mSelectedSetting2Index = actionData.getVolumeIndex();
            }
        } else {
            // 非门铃，需要获取CMD集合
            boolean isTuyaPlugin = false;
            SmartButtonTargetData targetData = mTargetInfo.getTargetData();
            if (null != targetData
                    && (TARGET_TYPE_TUYA_PLUG == targetData.getTargetType()
                    || TARGET_TYPE_TUYA_BULB == targetData.getTargetType())) {
                isTuyaPlugin = true;
            }
            mCommands = SmartButtonUtil.getCmdsBySceneType(mSceneType, isTuyaPlugin);
            if (isEditAction()) {
                // 修改之前的Action
                mSelectedCmdIndex = actionData.getCmdIndex();
            }
        }
    }

    private boolean isShowTabSelector() {
        if ("3B".equals(mSmartButtonData.getStype())
                || IPCKey.RC_KEY == mSourcePluginType) {
            return true;
        }
        return false;
    }

    /**
     * 获取上个页面传递的参数
     */
    private void getArgumentsFromOther() {
        DDLog.i(TAG, "getArgumentsFromOther");
        mSmartButtonData = getArguments().getParcelable(DATA_KEY_SMART_BUTTON_DATA);
        mTargetInfo = getArguments().getParcelable(DATA_KEY_TARGET_INFO);
        mSourcePluginType = getArguments().getInt(DATA_KEY_PLUGIN_TYPE, IPCKey.SMART_BUTTON);
    }

    /**
     * 当前是修改Action
     *
     * @return true: 修改Action
     */
    private boolean isEditAction() {
        return null != mTargetInfo
                && SmartButtonUtil.ITEM_TYPE_ACTION == mTargetInfo.getItemType();
    }

    /**
     * 当前是点击场景添加Action
     *
     * @return true: 点击场景添加Action
     */
    private boolean isSceneAddAction() {
        return null != mTargetInfo
                && SmartButtonUtil.ITEM_TYPE_SCENE == mTargetInfo.getItemType();
    }

    /**
     * 显示之前设置的相关值
     */
    private void initSavedState() {
        DDLog.i(TAG, "initSavedState");
        if (mIsDoorbell) {
            tvCommandValue.setLocalText(getString(R.string.smart_button_ring_doorbell));
            // 显示默认或设置的音乐和声音
            tvCustomerSettingValue1.setLocalText(mDoorBellMusics[mSelectedSetting1Index]);
            vcvVolume.setCurrentSelectIndex(mSelectedSetting2Index);
        } else {
            // 显示默认或设置的指令
            tvCommandValue.setLocalText(mCommands[mSelectedCmdIndex]);
        }
    }

    /**
     * 顶部是否仅使用单张图片
     *
     * @return true: 仅使用单张图片
     */
    private boolean isTopSingleImage() {
        if (IPCKey.SMART_BUTTON == mSourcePluginType) {
            return false;
        }
        return true;
    }

    /**
     * 更新视图的可见转态
     *
     * @param isDoorBell 是否显示门铃相关的设置
     */
    private void initViewsVisibility(boolean isDoorBell) {
        DDLog.i(TAG, "initViewsVisibility");
        if (isDoorBell) {
            btnTop.setVisibility(View.VISIBLE);
            llSettingDetail.setVisibility(View.VISIBLE);
            ivCommandNor.setVisibility(View.GONE);
            vcvVolume.setSelectedChangedListener(this);
        } else {
            btnTop.setVisibility(View.GONE);
            llSettingDetail.setVisibility(View.GONE);
        }

        if (!isShowTabSelector()) {
            tabSelectorL.setVisibility(View.GONE);
            commonBarTitle.setLocalText(getString(R.string.modify_plugs_chime_setting));
            return;
        }

        if (isTopSingleImage()) {
            llTabSelectorTop.setVisibility(View.GONE);
            ivTopSingleImage.setVisibility(View.VISIBLE);
            return;
        }
    }

    private void initLocalText() {
        DDLog.i(TAG, "initLocalText");
        commonBarTitle.setLocalText(getString(R.string.smart_button_set_command));
        tvCommand.setLocalText(getString(R.string.smart_button_command));
        tvCommandValue.setLocalText("");
        tvCustomerSetting1.setLocalText(getString(R.string.smart_button_doorbell_music));
        tvCustomerSetting2.setLocalText(getString(R.string.smart_button_doorbell_volume));
        btnBottom.setLocalText(getString(R.string.Done));
        btnTop.setLocalText(getString(R.string.smart_button_test_doorbell));
    }

    private void toStopAnim() {
        DDLog.i(TAG, "toStopAnim");
        getMainActivity().runOnUiThread(() -> {
            if (ivActionType == null) {
                return;
            }

            ApngDrawable apngDrawable = ApngDrawable.getFromView(ivActionType);
            if (apngDrawable == null) return;

            if (apngDrawable.isRunning()) {
                apngDrawable.stop(); // Stop animation
            }
        });
    }

    private void initTab() {
        DDLog.i(TAG, "initTab");
        String[] localTitles = new String[ACTION_TYPE_DES_IDS.length];
        for (int i = 0; i < ACTION_TYPE_DES_IDS.length; i++) {
            localTitles[i] = Local.s(getResources().getString(ACTION_TYPE_DES_IDS[i]));
        }

        stlActionTypeTab.setTextsize(13);
        stlActionTypeTab.setTabData(localTitles);

        stlActionTypeTab.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                DDLog.i(TAG, "onTabSelect, position: " + position);
                if (mActionTypeIndex == position) {
                    DDLog.d(TAG, "Not change action type.");
                    return;
                }

                mActionTypeIndex = position;

                mHandler.removeCallbacksAndMessages(null);
                mHandler.postDelayed(() -> {
                    startApngAnim(mActionTypeIndex);
                }, DURATION_START_ANIM_MILLIS);
            }

            @Override
            public void onTabReselect(int position) {
                DDLog.i(TAG, "onTabReselect, position: " + position);
            }
        });
        stlActionTypeTab.setCurrentTab(mInitIndex);
    }

    public void onViewClick(View view) {
        int clickId = view.getId();
        switch (clickId) {
            case R.id.common_bar_back:
                removeSelf();
                break;
            case R.id.ll_command:
                showActionSheet(CMD_COMMAND);
                break;
            case R.id.ll_customer_setting1:
                showActionSheet(CMD_DOORBELL_MUSIC);
                break;
            case R.id.btn_bottom:
                onDoneClick();
                break;
            case R.id.btn_top:
                testDoorBeel();
                break;
            default:
                DDLog.e(TAG, "Haven't process this view click.");
                break;
        }
    }

    /**
     * 测试门铃
     */
    private void testDoorBeel() {
        DDLog.i(TAG, "testDoorBeel");
        SmartButtonTargetData targetData = mTargetInfo.getTargetData();
        if (null == targetData
                || TextUtils.isEmpty(targetData.getSendid())
                || TextUtils.isEmpty(targetData.getStype())
                || null == mPluginDevice) {
            DDLog.e(TAG, "Target data/sendid/stype is null.");
            showErrorToast();
            return;
        }

        btnTop.setClickable(false);

        float count[] = new float[10];
        for (int i = 1; i <= 10; i++) {
            count[i - 1] = i;
        }

        if (mCountAnim == null) {
            mCountAnim = ViewAnimator
                    .animate(btnTop)
                    .duration(10 * 1000)
                    .interpolator(new LinearInterpolator())
                    .custom(new AnimationListener.Update<TextView>() {
                        @Override
                        public void update(TextView view, float value) {
                            mSb.delete(0, mSb.length());
                            mSb.append(Local.s(getResources().getString(R.string.smart_button_test_doorbell)))
                                    .append(" (")
                                    .append((10 - (int) value))
                                    .append(")");
                            view.setText(mSb.toString());
                        }
                    }, count)
                    .onStart(() -> {
                        btnTop.setAlpha(0.5f);
                        btnTop.setClickable(false);
                    })
                    .onStop(() -> {
                        resetTestButton(false);
                    });
        }
        mCountAnim.start();

        mPluginDevice.submit(PanelParamsHelper.testSirenSmartButton(mTargetInfo.getTargetData().getSendid(),
                mTargetInfo.getTargetData().getStype(), mSelectedSetting1Index, mSelectedSetting2Index));
    }

    /**
     * 还原测试按钮样式
     *
     * @param stopBySelf 是否主动停止
     */
    private void resetTestButton(boolean stopBySelf) {
        if (stopBySelf
                && null != mCountAnim) {
            mCountAnim.cancel();
        }

        btnTop.setAlpha(1f);
        btnTop.setLocalText(getResources().getString(R.string.smart_button_test_doorbell));
        btnTop.setVisibility(View.VISIBLE);
        btnTop.setClickable(true);
    }

    /**
     * 处理完成按钮的点击事件
     */
    private void onDoneClick() {
        DDLog.i(TAG, "onDoneClick");
        if (checkActionIsUsed()) {
            // Action已经被使用
            showActionHasUsedDialog();
        } else {
            // Action未被使用
            requestChangeOrSaveAction();
        }
    }

    /**
     * Action已经被使用了，提示是否需要覆盖
     */
    private void showActionHasUsedDialog() {
        DDLog.i(TAG, "showActionHasUsedDialog");
        AlertDialog.createBuilder(getContext())
                .setCancel(getString(R.string.Cancel))
                .setContent(getString(R.string.smart_button_action_repeat))
                .setOk(getString(R.string.overwrite))
                .setOKListener(this::requestChangeOrSaveAction)
                .preBuilder()
                .show();
    }

    /**
     * 检查当前Action是否已使用
     *
     * @return true: 已经使用
     */
    private boolean checkActionIsUsed() {
        DDLog.i(TAG, "checkActionIsUsed");
        if (isEditAction()
                && getActionTypeByIndex() == mTargetInfo.getActionData().getClickAction()) {
            // 如果是修改且没有修改Action类型，不标记已使用
            return false;
        }

        return mSmartButtonData.isActionUse(getActionTypeByIndex());
    }


    /**
     * 通过EventBus将结果通知上一个页面
     */
    private void postResult() {
        DDLog.i(TAG, "postResult");
        String cmd;
        String targetDes = null;

        if (mIsDoorbell) {
            // 门铃，设置固定的CMD并另外设置提示信息为音乐名
            targetDes = Local.s(mDoorBellMusics[mSelectedSetting1Index]);
            cmd = getString(R.string.smart_button_ring_doorbell);
        } else {
            cmd = mCommands[mSelectedCmdIndex];
        }

        // 操作和指令信息
        SmartButtonActionData actionData = new SmartButtonActionData.Builder()
                .setCmdDesc(targetDes)
                .setCmd(cmd)
                .setClickAction(getActionTypeByIndex())
                .setCmdIndex(mSelectedCmdIndex)
                .createSmartButtonActionData();
        if (IPCKey.RC_KEY == mSourcePluginType) {
            actionData.setClickActionDes(getResources()
                    .getString(R.string.smart_button_custom_remote_controller));
        }

        if (mIsDoorbell) {
            // 门铃，需要记录选择的音乐和声音大小
            actionData.setMusicIndex(mSelectedSetting1Index);
            actionData.setVolumeIndex(mSelectedSetting2Index);
        }

        SmartButtonSceneData result = new SmartButtonSceneData.Builder()
                .setItemType(SmartButtonUtil.ITEM_TYPE_ACTION)
                .setSceneType(mSceneType)
                .setActionData(actionData)
                .createSmartButtonSceneData();

        // 非安防指令，需要添加target信息
        if (SmartButtonUtil.SCENE_TYPE_SECURITY_CMD != mSceneType) {
            result.setTargetData(new SmartButtonTargetData.Builder()
                    .setTargetType(mTargetInfo.getTargetData().getTargetType())
                    .setTargetId(mTargetInfo.getTargetData().getTargetId())
                    .setTargetName(mTargetInfo.getTargetData().getTargetName())
                    .setDtype(mTargetInfo.getTargetData().getDtype())
                    .setSendid(mTargetInfo.getTargetData().getSendid())
                    .setStype(mTargetInfo.getTargetData().getStype())
                    .createSmartButtonTargetData());
        }

        SmartButtonActionChangeEvent resultEvent = new SmartButtonActionChangeEvent.Builder()
                .withIsAdd(SmartButtonUtil.ITEM_TYPE_SCENE == mTargetInfo.getItemType())
                .smartButtonData(mSmartButtonData)
                .withNewAction(result)
                .build();
        if (isEditAction()
                && null != mTargetInfo.getActionData()) {
            resultEvent.setSrcActionType(mTargetInfo.getActionData().getClickAction());
        }
        EventBus.getDefault().post(resultEvent);
    }

    /**
     * 通过下标获取操作类型
     *
     * @return 操作类型
     */
    private int getActionTypeByIndex() {
        if (ACTION_TYPE_INDEX_DOUBLE_PRESS == mActionTypeIndex) {
            return SmartButtonUtil.ACTION_TYPE_DOUBLE_CLICK;
        } else if (ACTION_TYPE_INDEX_LONG_PRESS == mActionTypeIndex) {
            return SmartButtonUtil.ACTION_TYPE_LONG_CLICK;
        } else {
            return SmartButtonUtil.ACTION_TYPE_SINGLE_CLICK;
        }
    }

    /**
     * 通过操作类型获取操作类型坐标
     *
     * @return 操作类型下标
     */
    private int getActionIndexByActionType(@SmartButtonUtil.SmartButtonActionType int actionType) {
        if (SmartButtonUtil.ACTION_TYPE_DOUBLE_CLICK == actionType) {
            return ACTION_TYPE_INDEX_DOUBLE_PRESS;
        } else if (SmartButtonUtil.ACTION_TYPE_LONG_CLICK == actionType) {
            return ACTION_TYPE_INDEX_LONG_PRESS;
        } else {
            return ACTION_TYPE_INDEX_SINGLE_PRESS;
        }
    }

    private void startApngAnim(int position) {
        DDLog.i(TAG, "startApngAnim, position: " + position);
        ApngDrawable apngDrawable = ApngDrawable.getFromView(ivActionType);
        if (apngDrawable == null) return;

        if (apngDrawable.isRunning()) {
            apngDrawable.stop(); // Stop animation
        }

        ApngImageLoader.getInstance()
                .displayApng(ACTION_TYPE_APNG_NAMES[position], ivActionType,
                        new ApngImageLoader.ApngConfig(1, true));
    }

    /**
     * 显示底部选择对话框
     *
     * @param cmdType 当前选择的是什么，可取值为一下之一：
     *                {{@link #CMD_COMMAND}}
     *                {{@link #CMD_DOORBELL_MUSIC}}
     */
    private void showActionSheet(int cmdType) {
        DDLog.i(TAG, "showActionSheet, cmdType: " + cmdType);

        String[] srcCmds = null, cmds;
        if (CMD_COMMAND == cmdType
                && null == mCommands) {
            return;
        }

        mCurrentCmdType = cmdType;

        if (CMD_COMMAND == mCurrentCmdType) {
            srcCmds = mCommands;
        } else if (CMD_DOORBELL_MUSIC == mCurrentCmdType) {
            srcCmds = mDoorBellMusics;
        }

        if (null == srcCmds || 0 >= srcCmds.length) {
            DDLog.e(TAG, "CMD is null.");
            return;
        }

        cmds = new String[srcCmds.length];
        for (int i = 0; i < srcCmds.length; i++) {
            cmds[i] = Local.s(srcCmds[i]);
        }

        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setOtherButtonTitles(cmds)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.Cancel)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                        mCurrentCmdType = CMD_NULL;
                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        DDLog.i(TAG, "onOtherButtonClick, cmdtype: " + mCurrentCmdType
                                + ", index: " + index);
                        if (CMD_COMMAND == mCurrentCmdType) {
                            mSelectedCmdIndex = index;
                            tvCommandValue.setLocalText(mCommands[index]);
                        } else if (CMD_DOORBELL_MUSIC == mCurrentCmdType) {
                            mSelectedSetting1Index = index;
                            tvCustomerSettingValue1.setLocalText(mDoorBellMusics[index]);
                        }
                        mCurrentCmdType = CMD_NULL;
                    }
                }).show();
    }

    /**
     * 创建保存时提交的配件信息
     * <p>
     * // 安防状态
     * "action_conf":{
     * "scenary":"SecurityCMD",
     * "cmd": "TASK_ARM"
     * }
     * <p>
     * // 门铃
     * "action_conf":{
     * "scenary":"Doorbell",
     * "pluginid":"" // string, 门铃id
     * "sendid":"",  // string，门铃sendid
     * "stype":"",   // string，门铃stype
     * "name":"",    // string，门铃名称
     * "volume":,    // int 门铃音量
     * "music":,     // int 门铃歌曲
     * }
     * <p>
     * // 涂鸦灯泡
     * "action_conf":{
     * "scenary":"Bulb",
     * "pluginid":"" // string, 涂鸦id
     * "name":"",    // string，名称
     * "action":,    // int 0:开 1:关 2:反转
     * }
     * <p>
     * // 涂鸦插座
     * "action_conf":{
     * "scenary":"Plug",
     * "pluginid":"" // string, 涂鸦id
     * "name":"",    // string，名称
     * "action":,    // int 0:开 1:关 2:反转
     * }
     * <p>
     * // 自研插座
     * "action_conf":{
     * "scenary":"Plug",
     * "pluginid":"" // string, 配件id
     * "sendid":"",  // string，配件sendid
     * "stype":"",   // string，配件stype
     * "dtype":10,   // int，配件dtype
     * "name":"",    // string，配件名称
     * "action":,    // int 0:开 1:关 2:反转
     * }
     *
     * @return action_conf的内容封装对象
     */
    public JSONObject createControlTargetInfo() {
        DDLog.i(TAG, "createControlTargetInfo");

        JSONObject params = new JSONObject();
        try {
            params.put(SmartButtonUtil.SERVICE_KEY_SCENE, SmartButtonUtil.getSceneStringByType(mSceneType));
            SmartButtonTargetData targetData = mTargetInfo.getTargetData();
            switch (mSceneType) {
                case SmartButtonUtil.SCENE_TYPE_SECURITY_CMD:
                    // 安防指令
                    params.put(SmartButtonUtil.SERVICE_KEY_CMD,
                            SmartButtonUtil.getServiceCmdByItemCmd(mCommands[mSelectedCmdIndex]));
                    break;
                case SmartButtonUtil.SCENE_TYPE_RING_BELL:
                    // 警笛
                    params.put(SmartButtonUtil.SERVICE_KEY_PLUGIN_ID, targetData.getTargetId());
                    params.put(SmartButtonUtil.SERVICE_KEY_SEND_ID, targetData.getSendid());
                    params.put(SmartButtonUtil.SERVICE_KEY_STYPE, targetData.getStype());
                    params.put(SmartButtonUtil.SERVICE_KEY_NAME, targetData.getTargetName());
                    params.put(SmartButtonUtil.SERVICE_KEY_MUSIC, mSelectedSetting1Index);
                    params.put(SmartButtonUtil.SERVICE_KEY_VOLUME, mSelectedSetting2Index);
                    break;
                case SmartButtonUtil.SCENE_TYPE_SWITCH_BULB:
                    params = null;
                    break;
                case SmartButtonUtil.SCENE_TYPE_SWITCH_PLUG:
                    // 所有插座都需要
                    params.put(SmartButtonUtil.SERVICE_KEY_PLUGIN_ID, targetData.getTargetId());
                    params.put(SmartButtonUtil.SERVICE_KEY_NAME, targetData.getTargetName());
                    params.put(SERVICE_KEY_ACTION, mSelectedCmdIndex);
                    // 自研额外添加
                    if (SmartButtonTargetData.TARGET_TYPE_SMART_PLUG == targetData.getTargetType()) {
                        params.put(SmartButtonUtil.SERVICE_KEY_SEND_ID, targetData.getSendid());
                        params.put(SmartButtonUtil.SERVICE_KEY_STYPE, targetData.getStype());
                        params.put(SmartButtonUtil.SERVICE_KEY_DTYPE, targetData.getDtype());
                    }
                    // 涂鸦插座添加
                    if (TARGET_TYPE_TUYA_PLUG == targetData.getTargetType()) {
                       params = null;
                    }
                    break;
                case SmartButtonUtil.SCENE_TYPE_CONTROL_SHUTTER:
                    // TODO 继电器
                    params.put(SmartButtonUtil.SERVICE_KEY_PLUGIN_ID, targetData.getTargetId());
                    params.put(SmartButtonUtil.SERVICE_KEY_NAME, targetData.getTargetName());
                    params.put(SERVICE_KEY_ACTION, mSelectedCmdIndex);
                    break;
                default:
                    // 未定义
                    DDLog.e(TAG, "Undefine scene type.");
                    params = null;
                    break;
            }
        } catch (JSONException e) {
            DDLog.e(TAG, "ERROR on create control target info.");
            e.printStackTrace();
            return null;
        }

        if (null == params
                || TextUtils.isEmpty(DDJSONUtil.getString(params, SmartButtonUtil.SERVICE_KEY_SCENE))) {
            DDLog.i(TAG, "Params is empty or scene is empty.");
            return null;
        } else {
            JSONObject newConfig = new JSONObject();
            try {
                newConfig.put(SERVICE_KEY_ACTION,
                        SmartButtonUtil.getActionStringByActionType(getActionTypeByIndex()));
                newConfig.put(SERVICE_KEY_ACTION_CONF, params);
            } catch (JSONException e) {
                DDLog.e(TAG, "ERROR.");
                e.printStackTrace();
                return null;
            }
            return newConfig;
        }
    }

    private void requestChangeOrSaveAction() {
        DDLog.i(TAG, "requestChangeOrSaveAction");

        if (null == mSmartButtonData) {
            showErrorToast();
            DDLog.e(TAG, "The info of SmartButton is null.");
            return;
        }

        String smartButtonSendId = mSmartButtonData.getSendid();
        String smartButtonStype = mSmartButtonData.getStype();
        JSONObject actionConfig = createControlTargetInfo();

        if (TextUtils.isEmpty(smartButtonSendId)
                || TextUtils.isEmpty(smartButtonStype)
                || null == actionConfig) {
            showErrorToast();
            DDLog.e(TAG, "The info of SmartButton or control target is null.");
            return;
        }
        JSONObject oldConfig = null;
        if (isEditAction()
                && getActionTypeByIndex() != mTargetInfo.getActionData().getClickAction()) {
            // 修改了操作的类型才需要将之前的action移除
            oldConfig = new JSONObject();
            try {
                oldConfig.put(SERVICE_KEY_ACTION,
                        SmartButtonUtil.getActionStringByActionType(mTargetInfo.getActionData().getClickAction()));
                oldConfig.put(SERVICE_KEY_ACTION_CONF, new JSONObject());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        if (null == mPluginDevice) {
            showErrorToast();
            DDLog.e(TAG, "requestDeleteAction - ERROR, no device");
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        ArrayList<JSONObject> actionConfigs = new ArrayList<>();
        actionConfigs.add(oldConfig);
        actionConfigs.add(actionConfig);
        isSelfOperate = true;
        mPluginDevice.submit(PanelParamsHelper.updateSmartButtonConfig(actionConfigs));
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PluginCmd.UPDATE_PLUGIN_CONFIG.equals(cmd) && isSelfOperate) {
            onUpdateSmartButtonConfig(status, map);
            isSelfOperate = false;
        }
    }

    private void onUpdateSmartButtonConfig(int status, Map<String, Object> map) {
        DDLog.i(TAG, "onUpdateSmartButtonConfig");
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            DDLog.i(TAG, "Send cmd: UPDATE_SMART_BT_CONF, result: SUCCESS");
            postResult();
            closeTimeOutLoadinFramgmentWithErrorAlert();
            getMainActivity().removeToFragment(SmartButtonSceneFragment.class.getName());
        } else {
            DDLog.e(TAG, "Send cmd: UPDATE_SMART_BT_CONF, result: ERROR");
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    @Override
    public void onSelectedChanged(int selectedIndex, boolean isClick) {
        DDLog.i(TAG, "onSelectedChanged, selectedIndex: " + selectedIndex + ", isClick: " + isClick);
        mSelectedSetting2Index = selectedIndex;
    }
}
