package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.PlugsData;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.SectionedBaseAdapter;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by Rinfon on 16/7/1.
 */
public class  PlugsItem extends SectionedBaseAdapter {

    private Activity mActivity;

    private HashMap<Integer, ArrayList<PlugsData>> mData;

    private boolean isShowSectionTitle = false;

//    private ArrayList<PlugsData> mData;

    public PlugsItem(Activity mActivity, HashMap<Integer, ArrayList<PlugsData>> mData, boolean isShowSectionTitle) {
        this.mActivity = mActivity;
        this.mData = mData;
        this.isShowSectionTitle = isShowSectionTitle;
    }

//    @Override
//    public int getCount() {
//        if (mData != null)
//            return mData.size();
//        return 0;
//    }


//    @Override
//    public View getView(final int position, View convertView, ViewGroup viewGroup) {
//        ViewHolder holder = null;
//        if (convertView == null) {
//            convertView = LayoutInflater.from(mActivity).inflate(R.layout.plugitem, null);
//            holder = new ViewHolder(convertView);
//            convertView.setTag(holder);
//        } else {
//            try {
//                holder = (ViewHolder) convertView.getTag();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//
//        holder.plugitemName.setLocalText(mData.get(position).getName());
//        if (!TextUtils.isEmpty(mData.get(position).getDescription())) {
//            holder.plugitemDescription.setText(mData.get(position).getDescription());
//        } else {
//            holder.plugitemDescription.setText(Local.s(mData.get(position).getName()) + "_" + mData.get(position).getPlugId());
//        }
//        holder.plugitemSwitch.setOn(mData.get(position).getIsOpen());
//        holder.plugitemSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
//            @Override
//            public void onStateSwitched(boolean isEnable) {
//                mData.get(position).setIsOpen(isEnable);
//            }
//        });
//        holder.plugitemIcon.setImageResource(mData.get(position).getIcon());
//        return convertView;
//    }

    @Override
    public Object getItem(int section, int position) {
        return null;
    }

    @Override
    public long getItemId(int section, int position) {
        return 0;
    }

    @Override
    public int getSectionCount() {
        return mData.size();
    }

    @Override
    public int getCountForSection(int section) {
        return mData.get(section).size();
    }

    @Override
    public View getItemView(final int section, final int position, View convertView, ViewGroup parent) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.plugitem, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        holder.plugitemName.setLocalText(mData.get(section).get(position).getName());
        if (!TextUtils.isEmpty(mData.get(section).get(position).getDescription())) {
            holder.plugitemDescription.setText(mData.get(section).get(position).getDescription());
        } else {
            holder.plugitemDescription.setText(Local.s(mData.get(section).get(position).getName()) + "_" + mData.get(section).get(position).getPlugId());
        }
        holder.plugitemSwitch.setOn(mData.get(section).get(position).getIsOpen());
        holder.plugitemSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                mData.get(section).get(position).setIsOpen(isOn);
            }
        });
        holder.plugitemIcon.setImageResource(mData.get(section).get(position).getIcon());
        return convertView;
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        sectionHeaderHolder mHolder = null;
        if (convertView == null) {
            mHolder = new sectionHeaderHolder();
            LayoutInflater inflator = (LayoutInflater) parent.getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflator.inflate(R.layout.homearm_list_section_header, null);
            mHolder.homearmName = (LocalTextView) convertView.findViewById(R.id.homarm_list_header_name);
            convertView.setTag(mHolder);
        } else {
            try {
                mHolder = (sectionHeaderHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (isShowSectionTitle) {
            if (!mData.get(section).get(0).isThirdpartyData())
                mHolder.homearmName.setLocalText(mActivity.getResources().getString(R.string.offical_plugin));
            else {
                mHolder.homearmName.setLocalText(mActivity.getResources().getString(R.string.device_managent_other_plugin));
            }
            mHolder.homearmName.setVisibility(View.VISIBLE);
        } else {
            mHolder.homearmName.setVisibility(View.GONE);
        }
        return convertView;
    }

    static class ViewHolder {
        ImageView plugitemIcon;
        LocalTextView plugitemName;
        View plugitemLine;
        TextView plugitemDescription;
        IOSSwitch plugitemSwitch;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            plugitemIcon = view.findViewById(R.id.plugitem_icon);
            plugitemName = view.findViewById(R.id.plugitem_name);
            plugitemLine = view.findViewById(R.id.plugitem_line);
            plugitemDescription = view.findViewById(R.id.plugitem_description);
            plugitemSwitch = view.findViewById(R.id.plugs_switch);
        }
    }

    static class sectionHeaderHolder {
        LocalTextView homearmName;
    }
}
