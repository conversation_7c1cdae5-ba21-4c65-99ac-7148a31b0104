package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.nostra13.universalimageloader.core.ImageLoader;


/**
 * Created by rinfon on 15/6/26.
 */
public class ImageDialog extends Dialog {

    int layoutRes;//布局文件

    Context mContext;

    LocalCustomButton mOk, mCancel;

    ImageView imageView;

    LocalTextView mTitle;

    LocalTextView mContent;


    public ImageDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.image_dialog;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mOk = (LocalCustomButton) view.findViewById(R.id.ok_btn);
        mCancel = (LocalCustomButton) view.findViewById(R.id.cancel_btn);
        mTitle = (LocalTextView) view.findViewById(R.id.dialog_title);
        mContent = (LocalTextView) view.findViewById(R.id.dialog_hint);
        imageView = (ImageView) view.findViewById(R.id.img_view);

        ImageLoader.getInstance().displayImage(builder.imgUrl, imageView);

        mOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onClick(ImageDialog.this);
                }
            }
        });

        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.cancelClick != null) {
                    builder.cancelClick.onClick(ImageDialog.this);
                }
            }
        });

        mTitle.setLocalText(builder.mTitle);
        mOk.setLocalText(builder.mOK);
        mCancel.setLocalText(builder.mCancel);
        mContent.setLocalText(builder.mContent);
    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        super.cancel();
    }

    public interface AlertClickCallback {
        void onClick(ImageDialog dialog);
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mTitle;

        private String mOK;

        private String mCancel;

        private String imgUrl;

        private boolean isAutoDismiss = true;

        private AlertClickCallback okClick;

        private AlertClickCallback cancelClick;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setCancelListener(AlertClickCallback listener) {
            this.cancelClick = listener;
            return this;
        }


        public Builder setOk(String ok) {
            mOK = ok;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            return this;
        }

        public Builder setImgUrl(String url) {
            imgUrl = url;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setTitle(String title) {
            mTitle = title;
            return this;
        }

        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public ImageDialog preBuilder() {
            ImageDialog alertDialog = new ImageDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
