package com.dinsafer.module.settting.adapter.ipc;

import androidx.recyclerview.widget.RecyclerView;

import android.view.View;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamMultiFullPlayActivity;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.ipc.player.IVideoViewListener;
import com.dinsafer.module.main.adapter.MainPanelBaseSection;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.List;

import io.github.luizgrp.sectionedrecyclerviewadapter.SectionParameters;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/26 11:56 AM
 */
public class IpcItemSection extends MainPanelBaseSection<Device> implements IVideoViewListener {
    private static final String TAG = IpcItemSection.class.getSimpleName();

    protected List<Device> mData;
    protected java.lang.String mTitle;
    protected MainActivity mMainActivity;
    private boolean isCanEnterEditMode = false;
    private boolean isShowHeader = true;

    private IPCItemDelegate heartLaiIpcItemDelegate;
    private IPCItemDelegate dsCamIpcItemDelegate;
    private IPCItemDelegate dsDoorbellItemDelegate;

    public IpcItemSection(MainActivity activity, String tittle, ArrayList<Device> datas, boolean isCanEnterEditMode, boolean isShowHeader) {
        super(activity, SectionParameters.builder()
                .headerResourceId(R.layout.homearm_list_section_header)
                .itemResourceId(R.layout.main_section_panel_item_ipc)
                .build(), tittle, datas);
        this.mMainActivity = activity;
        this.mTitle = tittle;
        this.mData = datas;
        this.isCanEnterEditMode = isCanEnterEditMode;
        this.isShowHeader = isShowHeader;

        heartLaiIpcItemDelegate = new HeartLaiIpcItemDelegate(mMainActivity, true, "");
        dsCamIpcItemDelegate = new DsCamIpcItemDelegate(mMainActivity, true, "");
        dsDoorbellItemDelegate = new DsDoorbellItemDelegate(mMainActivity, true, "");
    }

    @Override
    public int getContentItemsTotal() {
        return mData.size();
    }

    @Override
    public RecyclerView.ViewHolder getHeaderViewHolder(View view) {
        return new IpcHeaderViewHolder(view, isShowHeader);
    }

    @Override
    public void onBindHeaderViewHolder(RecyclerView.ViewHolder holder) {
        IpcHeaderViewHolder headerHolder = (IpcHeaderViewHolder) holder;
        headerHolder.setHeader(mTitle);
        // 1.2.5-首页-自研ipc的数量大于1个的时候显示多屏入口
        if (!isCanEnterEditMode) {
            // 不是首页的
            headerHolder.setRightIconVisible(false);
            return;
        }

        if (!DsCamUtils.supportStaticMultiPlay()) {
            headerHolder.setRightIconVisible(false);
            return;
        }

        // 需要显示多屏入口
        headerHolder.setRightIconVisible(true);
        headerHolder.setRightIconRes(R.drawable.icon_home_screen_multiple);
        headerHolder.setRightIconOnClickListener(v -> {
            final ArrayList<String> dsCamIds = DsCamUtils.getMultiPlayDsCamIds();
            if (dsCamIds.size() == 0) {
                mMainActivity.showErrorToast();
                return;
            }
            DsCamMultiFullPlayActivity.start(mMainActivity, dsCamIds);
        });
    }

    @Override
    public int getRowItemCount() {
        return 1;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(View view) {
        MainPanelIpcItemViewHolder viewHolder = new MainPanelIpcItemViewHolder(view, -1);
        viewHolder.setVideoViewListener(this);
        return viewHolder;
    }

    private IPCItemDelegate getIpcItemDelegate(Device device) {
        if (HeartLaiUtils.isHeartLaiDevice(device)) {
            return heartLaiIpcItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSCAM)) {
            return dsCamIpcItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSDOORBELL)) {
            return dsDoorbellItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSCAM_VOO6)) {
            return dsCamIpcItemDelegate;
        } else if (device.getCategory() == 2
                && device.getSubCategory().equals(DinConst.TYPE_DSCAM_VO15)) {
            return dsCamIpcItemDelegate;
        }


        return heartLaiIpcItemDelegate;
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, int position) {
        MainPanelIpcItemViewHolder itemHolder = (MainPanelIpcItemViewHolder) holder;
        itemHolder.bindCurrentIndex(position);
        Device device = mData.get(position);

        itemHolder.setRootViewEnable(true);
        itemHolder.setRootViewClickListener((View v) -> {
            if (!isCanEnterEditMode) {
                return;
            }
            if (!MainPanelHelper.getInstance().isPanelEditMode()
                    && !MainPanelHelper.getInstance().isFunctionEnable()) {
                DDLog.e(TAG, "当前Item不能点击");
                return;
            }

            if (MainPanelHelper.getInstance().isPanelEditMode()) {
                // 编辑模式下
                device.getInfo().put("isSelected", !DeviceHelper.getBoolean(device, "isSelected", true));
                itemHolder.setSelected(DeviceHelper.getBoolean(device, "isSelected", true));
                if (null != mOnItemClickListener) {
                    mOnItemClickListener.onItemClick(mTitle, true, v, position);
                }
            } else {
                // 非编辑模式下
                if (null != mOnItemClickListener) {
                    mOnItemClickListener.onItemClick(mTitle, false, v, position);
                }
            }
        });
        itemHolder.setIsEditMode(isCanEnterEditMode && MainPanelHelper.getInstance().isPanelEditMode());
        itemHolder.setSelected(DeviceHelper.getBoolean(device, "isSelected", true));
        getIpcItemDelegate(device).onBindItemViewHolder(itemHolder, position, device);
    }


    @Override
    public void onPlayIconClick(int position, CameraVideoView videoViewRoot, View parent) {
        getIpcItemDelegate(mData.get(position)).onPlayIconClick(mData.get(position), position, videoViewRoot, parent);
    }

    @Override
    public void onErrorIconClick(int position, CameraVideoView videoViewRoot, View parent) {
        getIpcItemDelegate(mData.get(position)).onErrorIconClick(mData.get(position), position, videoViewRoot, parent);
    }

    @Override
    public void onFullscreenIconClick(int position, CameraVideoView videoView, View parent) {
        getIpcItemDelegate(mData.get(position)).onFullscreenIconClick(mData.get(position), position, videoView, parent);
    }


}
