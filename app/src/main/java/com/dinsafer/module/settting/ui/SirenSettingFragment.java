package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafe.Dinsafe;
import com.dinsafer.aop.annotations.Safer;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SirenSettingLayoutBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/8.
 */
public class SirenSettingFragment extends MyBaseFragment<SirenSettingLayoutBinding> implements IDeviceCallBack {

    private static final String DATA = "data";

    private static final String PLUGINID = "pluginid";
    private static final String SEND_ID = "sendid";

    private static final String ON = "1";

    private static final String OFF = "0";

    private static final String ALWAYS = "1";

    private static final String FLASH = "2";

    private static final String ISASK = "ISASK";

    private static final String STYPE = "stype";

    private static final String ISCANSETLIGHT = "ISCANSETLIGHT";

    private String mData;

    private String mDisarmRemind, mHomeArmRemind, mArmRemind;

    private String mHomeMode, mDisArmMode, mArmMode, mAlartMode, mSirenTime, mSirenRemindVol, mSirenAlarmVol;

    private Device mPluginDevice;
    private boolean selfOperate;

    public static SirenSettingFragment newInstance(String data, String pluginId, String sendid,
                                                   String stype, boolean isCanSetLight) {
        Bundle bundle = new Bundle();
        if (data != null) {
            bundle.putSerializable(DATA, data);
            bundle.putString(PLUGINID, pluginId);
            bundle.putString(SEND_ID, sendid);
            bundle.putString(STYPE, stype);
            bundle.putBoolean(ISCANSETLIGHT, isCanSetLight);
            bundle.putBoolean(ISASK, true);
        }
        SirenSettingFragment sirenSettingFragment = new SirenSettingFragment();
        sirenSettingFragment.setArguments(bundle);

        return sirenSettingFragment;
    }

    public static SirenSettingFragment newInstance(String data, String pluginid) {
        Bundle bundle = new Bundle();
        if (data != null) {
            bundle.putSerializable(DATA, data);
            bundle.putString(PLUGINID, pluginid);
            bundle.putBoolean(ISASK, false);
        }
        SirenSettingFragment sirenSettingFragment = new SirenSettingFragment();
        sirenSettingFragment.setArguments(bundle);

        return sirenSettingFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.siren_setting_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        findDevice();
        __bindClicks(inflateView);
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.siren_setting_homearm_light_mode).setOnClickListener(v -> toHomeArmLightMode());
        rootView.findViewById(R.id.siren_setting_homearm_light_mode_nor).setOnClickListener(v -> toHomeArmLightMode());
        rootView.findViewById(R.id.siren_setting_homearm_light_current_mode).setOnClickListener(v -> toHomeArmLightMode());
        rootView.findViewById(R.id.siren_setting_arm_light_mode).setOnClickListener(v -> toArmLightMode());
        rootView.findViewById(R.id.siren_setting_arm_light_mode_nor).setOnClickListener(v -> toArmLightMode());
        rootView.findViewById(R.id.siren_setting_arm_light_current_mode).setOnClickListener(v -> toArmLightMode());
        rootView.findViewById(R.id.siren_setting_disarm_light_mode).setOnClickListener(v -> toDisArmLightMode());
        rootView.findViewById(R.id.siren_setting_disarm_light_mode_nor).setOnClickListener(v -> toDisArmLightMode());
        rootView.findViewById(R.id.siren_setting_disarm_light_current_mode).setOnClickListener(v -> toDisArmLightMode());
        rootView.findViewById(R.id.siren_setting_sos_mode).setOnClickListener(v -> toSOSLightMode());
        rootView.findViewById(R.id.siren_setting_sos_current_mode).setOnClickListener(v -> toSOSLightMode());
        rootView.findViewById(R.id.siren_setting_sos_mode_nor).setOnClickListener(v -> toSOSLightMode());
        rootView.findViewById(R.id.siren_setting_alert_time).setOnClickListener(v -> toSelectAlertTime());
        rootView.findViewById(R.id.siren_setting_alert_time_current_number).setOnClickListener(v -> toSelectAlertTime());
        rootView.findViewById(R.id.siren_setting_alert_time_nor).setOnClickListener(v -> toSelectAlertTime());
        rootView.findViewById(R.id.siren_setting_remind_volume).setOnClickListener(v -> toRemindVol());
        rootView.findViewById(R.id.siren_setting_remind_volume_current_number).setOnClickListener(v -> toRemindVol());
        rootView.findViewById(R.id.siren_setting_remind_volume_nor).setOnClickListener(v -> toRemindVol());
        rootView.findViewById(R.id.siren_setting_alarm_volume_mode).setOnClickListener(v -> toAlarmVol());
        rootView.findViewById(R.id.siren_setting_alarm_volume_current_number).setOnClickListener(v -> toAlarmVol());
        rootView.findViewById(R.id.siren_setting_alarm_volume_nor).setOnClickListener(v -> toAlarmVol());
        rootView.findViewById(R.id.btn_save).setOnClickListener(v -> toSave());
    }

    private void findDevice() {
        String pluginId = getArguments().getString(PLUGINID);
        if (!TextUtils.isEmpty(pluginId)) {
            mPluginDevice = DinHome.getInstance().getDevice(pluginId);
            if (null != mPluginDevice) {
                mPluginDevice.registerDeviceCallBack(this);
            }
        }
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.siren_setting));
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mData = (String) getArguments().getSerializable(DATA);
        mBinding.sirenSettingDisarmRemind.setLocalText(getResources().getString(R.string.siren_disarm));
        mBinding.sirenSettingHomearmRemind.setLocalText(getResources().getString(R.string.siren_homearm));
        mBinding.sirenSettingArmRemind.setLocalText(getResources().getString(R.string.siren_arm));
        mBinding.sirenSettingDisarmLightMode.setLocalText(getResources().getString(R.string.siren_disarm_light_mode));
        mBinding.sirenSettingHomearmLightMode.setLocalText(getResources().getString(R.string.siren_homearm_light_mode));
        mBinding.sirenSettingArmLightMode.setLocalText(getResources().getString(R.string.siren_arm_light_mode));
        mBinding.sirenSettingSosMode.setLocalText(getResources().getString(R.string.siren_sos));
        mBinding.sirenSettingRemindVolume.setLocalText(getResources().getString(R.string.siren_remind_volume));
        mBinding.sirenSettingAlarmVolumeMode.setLocalText(getResources().getString(R.string.siren_setting_alarm_volume));
        mBinding.sirenSettingAlertTime.setLocalText(getResources().getString(R.string.siren_alert_time));

        mBinding.commonBarBack.setOnClickListener(v -> close());
        initWithData();
    }

    @Safer
    private void initWithData() {
        if (mData == null) {
            mData = "0,0,0,1,0,2,1,1,10,10";
        }

        if ("21".equals(getArguments().getString(STYPE))
                || "22".equals(getArguments().getString(STYPE))
                || "34".equals(getArguments().getString(STYPE))
                || "35".equals(getArguments().getString(STYPE))) {
            i("mdata:" + mData);
            String tempData[] = mData.split(",");
            String mTData = "";


            String dd = Integer.toBinaryString(Integer.valueOf(tempData[0], 16));
            if (dd.length() < 8) {
                int length = dd.length();
                for (int j = 0; j < 8 - length; j++) {
                    dd = "0" + dd;
                }
            }

            mTData = Integer.valueOf(dd.substring(0, 1), 2).toString();
            mTData = mTData + "," + Integer.valueOf(dd.substring(1, 2), 2).toString();
            mTData = mTData + "," + Integer.valueOf(dd.substring(2, 3), 2).toString();
            mTData = mTData + "," + Integer.valueOf(dd.substring(3, 8), 2).toString() + ",";

            for (int i = 1; i < tempData.length; i++) {
                String d = Integer.toBinaryString(Integer.valueOf(tempData[i], 16));
                if (d.length() < 8) {
                    int length = d.length();
                    for (int j = 0; j < 8 - length; j++) {
                        d = "0" + d;
                    }
                }

                for (int k = 0; k < d.length(); ) {
                    mTData = mTData + Integer.valueOf(d.substring(k, k + 2), 2).toString() + ",";
                    k = k + 2;
                }
            }
//            去除最后两个0
            mTData = mTData.substring(0, 19);
//            转换最后两位音量
            tempData = mTData.split(",");
            mTData = mTData.substring(0, 15);
            mTData = mTData + "," + getValueWithIndex(Integer.parseInt(tempData[8]));
            mTData = mTData + "," + getValueWithIndex(Integer.parseInt(tempData[9]));

            mData = mTData;
            i(mData);
        }

        String[] values = mData.split(",");
        mBinding.sirenSettingDisarmSwitch.setOn(ON.equals(values[0]));
        mDisarmRemind = values[0];
        mBinding.sirenSettingHomearmSwitch.setOn(ON.equals(values[1]));
        mHomeArmRemind = values[1];
        mBinding.sirenSettingArmSwitch.setOn(ON.equals(values[2]));
        mArmRemind = values[2];

        mBinding.sirenSettingAlertTimeCurrentNumber.setLocalText(values[3]);
        mSirenTime = values[3];

        mDisArmMode = values[4];
        //        新警笛默认值不是off或者flash的话，则变成OFF
        if (!isShowLightAlwaysOn() && (!mDisArmMode.equals(OFF) && !mDisArmMode.equals(FLASH))) {
            mDisArmMode = OFF;
        }
        mBinding.sirenSettingDisarmLightCurrentMode.setLocalText(
                getModeWithValue(mDisArmMode));

        mAlartMode = values[5];
        i("mAlartMode:" + mAlartMode +
                " !mAlartMode.equals(OFF):" + !mAlartMode.equals(OFF)
                + " !mAlartMode.equals(FLASH):" + !mAlartMode.equals(FLASH));
        if (getArguments().getBoolean(ISASK) && (!mAlartMode.equals(OFF) && !mAlartMode.equals(FLASH))) {
            mAlartMode = OFF;
        }
        mBinding.sirenSettingSosCurrentMode.setLocalText(
                getModeWithValue(mAlartMode));

        mHomeMode = values[6];
        if (!isShowLightAlwaysOn() && (!mHomeMode.equals(OFF) && !mHomeMode.equals(FLASH))) {
            mHomeMode = OFF;
        }
        mBinding.sirenSettingHomearmLightCurrentMode.setLocalText(
                getModeWithValue(mHomeMode));

        mArmMode = values[7];
        if (!isShowLightAlwaysOn() && (!mArmMode.equals(OFF) && !mArmMode.equals(FLASH))) {
            mArmMode = OFF;
        }
        mBinding.sirenSettingArmLightCurrentMode.setLocalText(
                getModeWithValue(mArmMode));

        mBinding.sirenSettingRemindVolumeCurrentNumber.setLocalText(

                getNameWithValue(values[8]));
        mSirenRemindVol = values[8];
        mBinding.sirenSettingAlarmVolumeCurrentNumber.setLocalText(

                getNameWithValue(values[9]));
        mSirenAlarmVol = values[9];

        if (getArguments().getBoolean(ISASK)) {
            if (getArguments().getBoolean(ISCANSETLIGHT)) {
                mBinding.sirenSettingLightLayout.setVisibility(View.VISIBLE);
            } else {
                mBinding.sirenSettingLightLayout.setVisibility(View.GONE);
            }
        } else {
            String dinID = Dinsafe.str64ToHexStr(getArguments().getString(PLUGINID));
//        String sTypeID = dinID.substring(1, 3);

            String hasLight = dinID.substring(11, 13);

            if (hasLight.equals("02")) {
//            02
                mBinding.sirenSettingLightLayout.setVisibility(View.VISIBLE);
            } else {
//            03
                mBinding.sirenSettingLightLayout.setVisibility(View.GONE);

            }
        }


    }

    public void toHomeArmLightMode() {

        toSelectMode(isShowLightAlwaysOn(), new ActionSheet.ActionSheetListener() {
            @Override
            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

            }

            @Override
            public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                //                新警笛取消指示灯的常亮选项，旧警笛保留，2019/2/25/ 22:23 对接人，何毅进
                if (!isShowLightAlwaysOn() && index == 1) {
                    mHomeMode = index + 1 + "";
                } else {
                    mHomeMode = index + "";
                }

                mBinding.sirenSettingHomearmLightCurrentMode.setLocalText(getModeWithValue(mHomeMode));
            }
        });
    }

    public void toArmLightMode() {

        toSelectMode(isShowLightAlwaysOn(), new ActionSheet.ActionSheetListener() {
            @Override
            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

            }

            @Override
            public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                //                新警笛取消指示灯的常亮选项，旧警笛保留，2019/2/25/ 22:23 对接人，何毅进
                if (!isShowLightAlwaysOn() && index == 1) {
                    mArmMode = index + 1 + "";
                } else {
                    mArmMode = index + "";
                }
                mBinding.sirenSettingArmLightCurrentMode.setLocalText(getModeWithValue(mArmMode));
            }
        });
    }

    public void toDisArmLightMode() {

        toSelectMode(isShowLightAlwaysOn(), new ActionSheet.ActionSheetListener() {
            @Override
            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

            }

            @Override
            public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                //                新警笛取消指示灯的常亮选项，旧警笛保留，2019/2/25/ 22:23 对接人，何毅进
                if (!isShowLightAlwaysOn() && index == 1) {
                    mDisArmMode = index + 1 + "";
                } else {
                    mDisArmMode = index + "";
                }
                mBinding.sirenSettingDisarmLightCurrentMode.setLocalText(getModeWithValue(mDisArmMode));
            }
        });
    }

    public void toSOSLightMode() {

        toSelectMode(!getArguments().getBoolean(ISASK), new ActionSheet.ActionSheetListener() {
            @Override
            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

            }

            @Override
            public void onOtherButtonClick(ActionSheet actionSheet, int index) {

                if (getArguments().getBoolean(ISASK) && index == 1) {
                    mAlartMode = index + 1 + "";
                } else {
                    mAlartMode = index + "";
                }

                mBinding.sirenSettingSosCurrentMode.setLocalText(getModeWithValue(mAlartMode));
            }
        });
    }

    private void toSelectAlarmMode(ActionSheet.ActionSheetListener listener) {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.off)),
                        Local.s(getResources().getString(R.string.flash)))
                .setCancelableOnTouchOutside(true)
                .setListener(listener).show();
    }

    private void toSelectMode(boolean showLightAlwaysOn, ActionSheet.ActionSheetListener listener) {
        if (showLightAlwaysOn) {
            ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(getResources().getString(R.string.off)),
                            Local.s(getResources().getString(R.string.always)),
                            Local.s(getResources().getString(R.string.flash)))
                    .setCancelableOnTouchOutside(true)
                    .setListener(listener).show();
        } else {
            ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(getResources().getString(R.string.off)),
                            Local.s(getResources().getString(R.string.flash)))
                    .setCancelableOnTouchOutside(true)
                    .setListener(listener).show();
        }
    }


    public void toSelectAlertTime() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles("1", "2", "3", "4", "5")
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        mSirenTime = index + 1 + "";
                        mBinding.sirenSettingAlertTimeCurrentNumber.setLocalText(mSirenTime);
                    }
                }).show();
    }


    public void toRemindVol() {

        toSelectVol(new ActionSheet.ActionSheetListener() {
            @Override
            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

            }

            @Override
            public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                mSirenRemindVol = index + "";
                if (index == 2) {
                    mSirenRemindVol = 5 + "";
                }

                if (index == 3) {
                    mSirenRemindVol = 10 + "";
                }
                mBinding.sirenSettingRemindVolumeCurrentNumber.setLocalText(getNameWithValue(mSirenRemindVol));
            }
        });
    }


    public void toAlarmVol() {
        toSelectVol(new ActionSheet.ActionSheetListener() {
            @Override
            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

            }

            @Override
            public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                mSirenAlarmVol = index + "";

                if (index == 2) {
                    mSirenAlarmVol = 5 + "";
                }

                if (index == 3) {
                    mSirenAlarmVol = 10 + "";
                }

                mBinding.sirenSettingAlarmVolumeCurrentNumber.setLocalText(getNameWithValue(mSirenAlarmVol));

            }
        });
    }


    private void toSelectVol(ActionSheet.ActionSheetListener listener) {
        String[] vol = {Local.s(getResources().getString(R.string.siren_mute)),
                Local.s(getResources().getString(R.string.siren_low)),
                Local.s(getResources().getString(R.string.siren_middle)),
                Local.s(getResources().getString(R.string.siren_high))};
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(vol)
                .setCancelableOnTouchOutside(true)
                .setListener(listener).show();
    }

    private String getNameWithValue(String value) {
        String volString = getDelegateActivity().getString(R.string.siren_mute);
        if ("0".equals(value))
            volString = getDelegateActivity().getString(R.string.siren_mute);

        if ("1".equals(value))
            volString = getDelegateActivity().getString(R.string.siren_low);

        if ("5".equals(value)) {
            volString = getDelegateActivity().getString(R.string.siren_middle);
        }

        if ("10".equals(value)) {
            volString = getDelegateActivity().getString(R.string.siren_high);
        }
        return volString;
    }

    private String getValueWithIndex(int index) {
        if (index == 0)
            return "0";
        if (index == 1)
            return "1";

        if (index == 2)
            return "5";

        if (index == 3)
            return "10";
        return "10";
    }

    private String getModeWithValue(String value) {
        if (OFF.equals(value)) {
            return getResources().getString(R.string.off);
        } else if (ALWAYS.equals(value)) {
            return getResources().getString(R.string.always);
        } else {
            return getResources().getString(R.string.flash);
        }
    }

    public void toSave() {
        if (mBinding.sirenSettingDisarmSwitch.isOn()
                || mBinding.sirenSettingHomearmSwitch.isOn()
                || mBinding.sirenSettingArmSwitch.isOn()
                || !mDisArmMode.equals(OFF)
                || !mArmMode.equals(OFF)
                || !mHomeMode.equals(OFF)) {
            AlertDialog.createBuilder(getMainActivity())
                    .setContent(getResources().getString(R.string.siren_content))
                    .setOk(getResources().getString(R.string.know_it))
                    .setOKListener(new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            toDoSave();
                        }
                    })
                    .setCancel(getResources().getString(R.string.reset_siren))
                    .preBuilder()
                    .show();

        } else {
            toDoSave();
        }
    }

    private void toDoSave() {
        StringBuilder sirenSetting = new StringBuilder();
        if (mBinding.sirenSettingDisarmSwitch.isOn()) {
            sirenSetting.append("1").append(",");
        } else {
            sirenSetting.append("0").append(",");
        }

        if (mBinding.sirenSettingHomearmSwitch.isOn()) {
            sirenSetting.append("1").append(",");
        } else {
            sirenSetting.append("0").append(",");
        }
        if (mBinding.sirenSettingArmSwitch.isOn()) {
            sirenSetting.append("1").append(",");
        } else {
            sirenSetting.append("0").append(",");
        }

        sirenSetting.append(mSirenTime).append(",");
        sirenSetting.append(mDisArmMode).append(",");
        sirenSetting.append(mAlartMode).append(",");
        sirenSetting.append(mHomeMode).append(",");
        sirenSetting.append(mArmMode).append(",");
        sirenSetting.append(mSirenRemindVol).append(",");
        sirenSetting.append(mSirenAlarmVol);

        if (null != mPluginDevice) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            selfOperate = true;
            mPluginDevice.submit(PanelParamsHelper.changeSirenSetting(sirenSetting.toString()));
        } else {
            DDLog.e(TAG, "No siren device");
            showErrorToast();
        }
    }

    public void close() {
        removeSelf();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
    }

    /**
     * 设置选择框是否可选择常亮
     *
     * @return 旧的和34、35类型的显示，其他不显示
     */
    private boolean isShowLightAlwaysOn() {
        return false;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPluginDevice
                || !deviceId.equals(mPluginDevice.getId())) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, relay control: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);

        if (selfOperate && PluginCmd.CHANGE_SIREN_SETTING.equals(cmd)) {
            selfOperate = false;
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS == status) {
                removeSelf();
            } else {
                showErrorToast();
            }
        }
    }
}

