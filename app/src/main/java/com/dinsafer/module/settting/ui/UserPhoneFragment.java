package com.dinsafer.module.settting.ui;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Patterns;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.dinsafer.common.utils.BitmapUtil;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.LottieLoadingView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;


/**
 * Created by Rinfon on 16/7/8.
 */
public class UserPhoneFragment extends BaseFragment {

    LocalTextView commonBarTitle;
    LocalCustomButton changePhoneSend;
    LocalCustomButton changePhoneUnbind;
    LocalCustomButton changePhoneNext;

    LinearLayout llInputPhone;
    TextView tvPhoneZone;
    View vPhoneLine;
    EditText etAccount;
    EditText etCode;
    ConstraintLayout clInputVerificationCode;
    LottieLoadingView viewLoading;
    ImageView ivCode;


    public static float BACKGROUND_ALPHA = 0.5f;
    private String verifyId;

    private static final long TOW_SECONDS = 2 * 1000;

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };


    public static UserPhoneFragment newInstance() {
        return new UserPhoneFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.user_phone_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.change_phone_unbind).setOnClickListener(v -> loadCode());
        rootView.findViewById(R.id.change_phone_send).setOnClickListener(v -> toSend());
        rootView.findViewById(R.id.change_phone_next).setOnClickListener(v -> {
            setNextBtnEnable(false);
            toUnbind();
        });
        rootView.findViewById(R.id.common_bar_back).setOnClickListener(v -> close());
        rootView.findViewById(R.id.common_background).setOnClickListener(v -> closeInput());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        changePhoneSend = rootView.findViewById(R.id.change_phone_send);
        changePhoneUnbind = rootView.findViewById(R.id.change_phone_unbind);
        changePhoneNext = rootView.findViewById(R.id.change_phone_next);
        llInputPhone = rootView.findViewById(R.id.ll_input_phone);
        tvPhoneZone = rootView.findViewById(R.id.tv_phone_zone);
        vPhoneLine = rootView.findViewById(R.id.v_phone_line);
        etAccount = rootView.findViewById(R.id.et_account);
        etCode = rootView.findViewById(R.id.et_code);
        clInputVerificationCode = rootView.findViewById(R.id.cl_input_verification_code);
        viewLoading = rootView.findViewById(R.id.view_loading);
        ivCode = rootView.findViewById(R.id.iv_code);
        etCode.addTextChangedListener(mWatcher);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.phone_setting));
        changePhoneUnbind.setLocalText(getResources().getString(R.string.change_email_unbind));
        changePhoneNext.setLocalText(getResources().getString(R.string.Next));
        changePhoneNext.setVisibility(View.GONE);
        etAccount.setHint(Local.s(getString(R.string.phone)));
        if (!TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone())) {
            refreshPhone();
            if (!TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail())) {
                changePhoneUnbind.setVisibility(View.VISIBLE);
            } else {
                changePhoneUnbind.setVisibility(View.GONE);
            }
        } else {
            changePhoneUnbind.setVisibility(View.GONE);
        }
        changePhoneSend.setLocalText(getResources().getString(R.string.change_binding_text));
        etAccount.addTextChangedListener(new TextWatcher() {

            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(etAccount.getText())
                        && Patterns.PHONE.matcher(etAccount.getText().toString()).matches()) {
                    changePhoneSend.setBackground(getResources().getDrawable(R.drawable.blue_rectangle));
                    changePhoneSend.setAlpha(1f);
                    changePhoneSend.setEnabled(true);
                } else {
                    changePhoneSend.setBackground(getResources().getDrawable(R.drawable.blue_rectangle));
                    changePhoneSend.setAlpha(BACKGROUND_ALPHA);
                    changePhoneSend.setEnabled(false);
                }
            }
        });

        ivCode.setOnClickListener(v -> {
            setIvCodeEnable(false);
            getVerificationCode();
        });
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        getVerificationCode();
    }

    public void loadCode() {
        if (TextUtils.isEmpty(etAccount.getText().toString())) {
            return;
        }

        clInputVerificationCode.setVisibility(View.VISIBLE);
        changePhoneNext.setVisibility(View.VISIBLE);
        changePhoneSend.setVisibility(View.GONE);
        changePhoneUnbind.setVisibility(View.INVISIBLE);
        getVerificationCode();
        updateBtnStateEnable();

    }

    public void toUnbind() {
        final String verifyCode = etCode.getText().toString().trim();

        if (TextUtils.isEmpty(verifyCode) || TextUtils.isEmpty(verifyId)) {
            showErrorToast();
            setNextBtnEnable(true);
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getUserInstance().unbindPhone(DinSDK.getUserInstance().getUser().getPhone()
                , DDSystemUtil.getWidevineId(), verifyCode, verifyId, new IResultCallback() {

                    @Override
                    public void onError(int i, String s) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (i == ErrorCode.ERROR_WRONG_VERIFICATION_CODE_ENTERED) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_wrong_verifyCode)));
                            getVerificationCode();
                        } else if (i == ErrorCode.ERROR_VERIFICATION_CODE_ENTERED_TIMEOUT) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_verifyCode_timeout)));
                            getVerificationCode();
                        } else {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));
                        }
                        setNextBtnEnable(false);
                    }

                    @Override
                    public void onSuccess() {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        getDelegateActivity().addCommonFragment(UnbindPhoneEmailVerifyCodeFragment.newInstance(DinSDK.getUserInstance().getUser().getPhone()));

                    }
                });

    }

    public void toSend() {
        getDelegateActivity().addCommonFragment(ChangePhoneFragment.newInstance());
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    public void closeInput() {
        toCloseInput();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    private void refreshPhone() {
        String[] phone = DinSDK.getUserInstance().getUser().getPhone().split(" ");
        if (phone.length > 0) {
            for (int i = 0; i < ChoosePhoneZoneFragment.countryCodes.length; i++) {
                if (phone[0].equals(ChoosePhoneZoneFragment.countryCodes[i])) {
                    tvPhoneZone.setText(phone[0]);
                    break;
                }
            }
            etAccount.setText(phone[1]);
        }

        tvPhoneZone.setEnabled(false);
        etAccount.setEnabled(false);
    }

    private void getVerificationCode() {
        viewLoading.setVisibility(View.VISIBLE);
        DinSDK.getUserInstance().refreshVerifyCode(DDSystemUtil.getWidevineId(), new IResultCallback2<RefreshVerifyCodeResponse.ResultBean>() {
            @Override
            public void onSuccess(RefreshVerifyCodeResponse.ResultBean resultBean) {
                DDLog.d(TAG, "refreshVerifyCode. onSuccess");
                viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);
                if (resultBean != null) {
                    verifyId = resultBean.getVerify_id();
                    String base64Image = resultBean.getBase64();
                    Bitmap bitmap = BitmapUtil.convertStringToBitmap(base64Image);
                    Bitmap roundedBitmap = BitmapUtil.getRoundedCornerBitmap(getContext(), bitmap, 6);
                    ivCode.setImageBitmap(roundedBitmap);
                }
            }

            @Override
            public void onError(int code, String msg) {
                DDLog.e(TAG, "refreshVerifyCode. onError: " + code + " " + msg);
                viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);
                if (ErrorCode.ERROR_TOO_MANY_VERIFICATION == code) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_too_many_verifyCode)));
                    return;
                }
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));

            }
        });
    }

    private void setIvCodeEnable(boolean enable) {
        if (enable) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    ivCode.setEnabled(true);
                    ivCode.setAlpha(1.0f);
                }
            }, TOW_SECONDS);
            return;
        }
        ivCode.setEnabled(false);
        ivCode.setAlpha(0.5f);
    }


    private void updateBtnStateEnable() {
        final String account = etAccount.getText().toString().trim();
        final String verificationCode = etCode.getText().toString().trim();

        boolean enable = !TextUtils.isEmpty(account)
                && !TextUtils.isEmpty(verificationCode);
        setNextBtnEnable(enable);
    }

    private void setNextBtnEnable(final boolean enable) {
        changePhoneNext.setAlpha(enable ? 1.0f : 0.5f);
        changePhoneNext.setEnabled(enable);
    }
}

