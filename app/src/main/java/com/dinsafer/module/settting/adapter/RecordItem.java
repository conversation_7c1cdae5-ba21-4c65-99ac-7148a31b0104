package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.RecordData;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.PinnedSectionedHeaderAdapter;
import com.dinsafer.ui.SectionedBaseAdapter;
import com.dinsafer.util.Local;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;

/**
 * Created by Rinfon on 16/7/1.
 */
public class RecordItem extends SectionedBaseAdapter {

    private Activity mActivity;

    private HashMap<Integer, ArrayList<RecordData>> list;

//    private ArrayList<RecordData> mData;

    private int[] backgroundColor = {R.color.colorMainFragmentListViewItemBG_1,
            R.color.colorMainFragmentListViewItemBG_2};

    public RecordItem(Activity mActivity, HashMap<Integer, ArrayList<RecordData>> mData) {
        this.mActivity = mActivity;
        this.list = mData;
    }

    @Override
    public Object getItem(int section, int position) {
        return null;
    }

    @Override
    public long getItemId(int section, int position) {
        return 0;
    }

    @Override
    public int getSectionCount() {
        return list.size();
    }

    @Override
    public int getCountForSection(int section) {
        if (list == null)
            return 0;
        return list.get(section).size();
    }

    public HashMap<Integer, ArrayList<RecordData>> getList() {
        return list;
    }

    public void setList(HashMap<Integer, ArrayList<RecordData>> list) {
        this.list = list;
    }

    @Override
    public View getItemView(int section, int position, View convertView, ViewGroup parent) {
        ViewHolder itemHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.record_item, null);
            itemHolder = new ViewHolder(convertView);
            convertView.setTag(itemHolder);
        } else {
            try {
                itemHolder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (list.get(section).get(position).getFile() != null) {

            itemHolder.itemBackground.setBackgroundColor(mActivity.getResources().getColor(backgroundColor[position % backgroundColor.length]));
            itemHolder.recordItemText.setText(Local.s(mActivity.getResources().getString(R.string.record_motionhint)));
            long time = list.get(section).get(position).getTime();
            SimpleDateFormat myFmt = new SimpleDateFormat("HH:mm:ss");
            SimpleDateFormat myTime = new SimpleDateFormat("yyyy-MM-dd");
            itemHolder.recordDay.setText(myTime.format(time));
            itemHolder.recordTime.setText(myFmt.format(time));
            itemHolder.recordTime.setVisibility(View.VISIBLE);
            itemHolder.recordDay.setVisibility(View.VISIBLE);
            itemHolder.recordNor.setVisibility(View.VISIBLE);
            if (!list.get(section).get(position).isClick()) {
                itemHolder.recordIcon.setImageResource(R.drawable.icon_ipc_video_list_unseen);
                itemHolder.recordTime.setTextColor(mActivity.getResources().getColor(R.color.text_blue_1));
                itemHolder.recordTime.setAlpha(1f);
            } else {
                itemHolder.recordIcon.setImageResource(R.drawable.icon_ipc_video_list_seen);
                itemHolder.recordTime.setTextColor(mActivity.getResources().getColor(R.color.white));
                itemHolder.recordTime.setAlpha(0.6f);

            }
        } else {
            itemHolder.recordItemText.setText(Local.s(mActivity.getResources().getString(R.string.record_empty)));
            itemHolder.recordTime.setVisibility(View.GONE);
            itemHolder.recordDay.setVisibility(View.GONE);
            itemHolder.recordNor.setVisibility(View.GONE);
            itemHolder.recordIcon.setImageResource(R.drawable.icon_ipc_video_list_norecord);
        }

        return convertView;
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        HeaderViewHolder mHolder = null;
        if (convertView == null) {
            mHolder = new HeaderViewHolder();
            LayoutInflater inflator = (LayoutInflater) parent.getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflator.inflate(R.layout.record_item_header, null);
            mHolder.header = (LocalTextView) convertView.findViewById(R.id.record_header);
            convertView.setTag(mHolder);
        } else {
            try {
                mHolder = (HeaderViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (section == 0) {
            mHolder.header.setLocalText(mActivity.getResources().getString(R.string.record_one_hour));
        } else if (section == 1) {
            mHolder.header.setLocalText(mActivity.getResources().getString(R.string.record_three_hour));
        } else if (section == 2) {
            mHolder.header.setLocalText(mActivity.getResources().getString(R.string.record_six_hour));
        } else if (section == 3) {
            mHolder.header.setLocalText(mActivity.getResources().getString(R.string.record_one_day));
        } else if (section == 4) {
            mHolder.header.setLocalText(mActivity.getResources().getString(R.string.record_three_day));
        } else if (section == 5) {
            mHolder.header.setLocalText(mActivity.getResources().getString(R.string.record_more));
        }

        return convertView;

    }


    static class HeaderViewHolder {
        LocalTextView header;
    }

    static class ViewHolder {
        RelativeLayout itemBackground;
        ImageView recordIcon;
        TextView recordItemText;
        ImageView recordNor;
        TextView recordTime;
        TextView recordDay;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            itemBackground = view.findViewById(R.id.record_item_background);
            recordIcon = view.findViewById(R.id.record_icon);
            recordItemText = view.findViewById(R.id.record_item_text);
            recordNor = view.findViewById(R.id.record_nor);
            recordTime = view.findViewById(R.id.record_time);
            recordDay = view.findViewById(R.id.record_day);
        }
    }
}
