package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.CustomRemoteItemBinding;
import com.dinsafer.model.CustomizeSmartPlugInfo;

import java.util.ArrayList;

import androidx.databinding.DataBindingUtil;

/**
 * Created by Rinfon on 16/7/1.
 */
public class CustomRemoteAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<CustomizeSmartPlugInfo> list;

    private String mSelectId, mSendId;

    public CustomRemoteAdapter(Activity mActivity, ArrayList<CustomizeSmartPlugInfo> mData) {
        this.mActivity = mActivity;
        this.list = mData;
    }

    @Override
    public int getCount() {
        if (list != null)
            return list.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.custom_remote_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        holder.binding.customRemoteName.setText(list.get(position).getName());
        if (list.get(position).getId().equals(mSelectId)) {
            holder.binding.customRemoteCheckbox.setChecked(true);
        } else {
            holder.binding.customRemoteCheckbox.setChecked(false);
        }

        holder.binding.customRemoteCheckbox.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (list.get(position).getId().equals(mSelectId)) {
                    mSelectId = "";
                    mSendId = "";
                } else {
                    mSelectId = list.get(position).getId();
                    mSendId = list.get(position).getSendid();
                }
                notifyDataSetChanged();
            }
        });

        holder.binding.customRemoteName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (list.get(position).getId().equals(mSelectId)) {
                    mSelectId = "";
                    mSendId = "";
                } else {
                    mSelectId = list.get(position).getId();
                    mSendId = list.get(position).getSendid();
                }
                notifyDataSetChanged();
            }
        });

        return convertView;
    }


    public String getSelectId() {
        return mSelectId;
    }

    public void setmSelectId(String selectId) {
        this.mSelectId = selectId;
    }

    public String getmSendId() {
        return mSendId;
    }

    public void setmSendId(String mSendId) {
        this.mSendId = mSendId;
    }

    public void setSelectAndSendId(String pluginId, String sendid) {
        setmSelectId(pluginId);
        setmSendId(sendid);
    }

    static class ViewHolder {
        CustomRemoteItemBinding binding;
        ViewHolder(View view) {
           binding = DataBindingUtil.bind(view);
        }
    }
}
