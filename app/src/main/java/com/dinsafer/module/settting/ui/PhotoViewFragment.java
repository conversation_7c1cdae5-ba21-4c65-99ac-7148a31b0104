package com.dinsafer.module.settting.ui;

import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.PhotoviewLayoutBinding;
import com.dinsafer.module.MyBaseFragment;
import com.nostra13.universalimageloader.core.ImageLoader;

public class PhotoViewFragment extends MyBaseFragment<PhotoviewLayoutBinding> {

    private String url;

    public static PhotoViewFragment newInstance(String url) {
        Bundle bundle = new Bundle();
        bundle.putString("url", url);
        PhotoViewFragment fragment = new PhotoViewFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    public static PhotoViewFragment newInstance(Uri uri) {
        Bundle bundle = new Bundle();
        bundle.putParcelable("uri", uri);
        PhotoViewFragment fragment = new PhotoViewFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.photoview_layout;
    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.photoview.setOnClickListener(v -> toClose());
        Uri uri = getArguments().getParcelable("uri");
        if (!TextUtils.isEmpty(getArguments().getString("url"))) {
//            ImageLoader.getInstance().displayImage(DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.SOS_RECORD_SERVER_IP + getArguments().getString("url")), photoview);
            ImageLoader.getInstance().displayImage(getArguments().getString("url"), mBinding.photoview);
        } else if (null != uri) {
            mBinding.photoview.setImageURI(uri);
        }

    }

}