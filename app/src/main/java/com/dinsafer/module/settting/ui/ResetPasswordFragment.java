package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.jungly.gridpasswordview.GridPasswordView;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/12.
 */
public class ResetPasswordFragment extends BaseFragment
        implements ActionSheet.ActionSheetListener, IDeviceCallBack {

    LocalTextView commonBarTitle;

    LocalTextView resetPasswordHint;

    LocalCustomButton resetPasswordReset;

    LocalCustomButton resetPasswordCancel;

    RelativeLayout resetPasswordInputLayout;

    LocalTextView resetPasswordConfirm;

    LocalTextView resetPasswordTitle;

    GridPasswordView resetPasswordInput;

    public static String messageId;
    private boolean isResetAll = false;
    private Handler mTimeoutHandler = new Handler(Looper.getMainLooper());

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static ResetPasswordFragment newInstance() {
        return new ResetPasswordFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.reset_password_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.reset_password_reset).setOnClickListener( v -> toSave());
        rootView.findViewById(R.id.reset_password_confirm).setOnClickListener( v -> toConfirm());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.reset_password_cancel).setOnClickListener( v -> toClose());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        resetPasswordHint = rootView.findViewById(R.id.reset_password_hint);
        resetPasswordReset = rootView.findViewById(R.id.reset_password_reset);
        resetPasswordCancel = rootView.findViewById(R.id.reset_password_cancel);
        resetPasswordInputLayout = rootView.findViewById(R.id.reset_password_input_layout);
        resetPasswordConfirm = rootView.findViewById(R.id.reset_password_confirm);
        resetPasswordTitle = rootView.findViewById(R.id.reset_password_title);
        resetPasswordInput = rootView.findViewById(R.id.reset_password_input);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            showErrorToast();
            removeSelf();
        }
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.advanced_setting_reset));
        resetPasswordTitle.setLocalText(getResources().getString(R.string.password_dialog_title));
        resetPasswordReset.setLocalText(getResources().getString(R.string.Reset));
        resetPasswordCancel.setLocalText(getResources().getString(R.string.Cancel));
        resetPasswordConfirm.setLocalText(getResources().getString(R.string.Confirm));
        resetPasswordHint.setLocalText(getResources().getString(R.string.reset_password_hint));
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
    }

    public void toSave() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.reset_device_all)),
                        Local.s(getResources().getString(R.string.reset_device_content)))
                .setCancelableOnTouchOutside(true)
                .setListener(this).show();
    }

    public void toConfirm() {
        if (!TextUtils.isEmpty(resetPasswordInput.getPassWord()) && resetPasswordInput.getPassWord().length() >= 4) {
            AlertDialog.createBuilder(getDelegateActivity())
                    .setOKListener(new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            toCloseInput();
                            doReset(resetPasswordInput.getPassWord());
                        }
                    })
                    .setAutoDissmiss(true)
                    .setContent(getResources().getString(R.string.reset_device_content_hint))
                    .setOk(getResources().getString(R.string.reset_device_content_confirm))
                    .setCancel(getResources().getString(R.string.Cancel))
                    .preBuilder()
                    .show();
        }
    }

    private void doReset(String password) {
        showLoadingFragment(LoadingFragment.BLACK, "");
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.resetPanel(password, isResetAll));
        mTimeoutHandler.postDelayed(() -> {
            closeLoadingFragment();
            showErrorToast();
            isSelfOperate = false;
        }, LocalKey.TIMEOUT);
    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mTimeoutHandler.removeCallbacksAndMessages(null);
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        isResetAll = index == 1;
        resetPasswordHint.setVisibility(View.GONE);
        resetPasswordCancel.setVisibility(View.GONE);
        resetPasswordReset.setVisibility(View.GONE);
        resetPasswordInput.requestFocus();
        toOpenInput();
        resetPasswordConfirm.setVisibility(View.VISIBLE);
        resetPasswordInputLayout.setVisibility(View.VISIBLE);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.RESET_PANEL.equals(cmd)) {
                // 重置主机
                onResetPanel(status, map);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 重置主机
     */
    private void onResetPanel(int status, Map map) {
        DDLog.i(TAG, "onGetSosMessageInfo, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
        }
    }
}
