package com.dinsafer.module.settting.ui;


import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.dinsafer.common.utils.BitmapUtil;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ChangePhoneLayoutBinding;
import com.dinsafer.model.BindPhoneSuccessEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.user.modify.ChangePhoneVerifyCodeFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ChangePhoneFragment extends MyBaseFragment<ChangePhoneLayoutBinding> implements ChoosePhoneZoneFragment.IChoosePhoneZoneCallBack {
    private String phoneZone;
    private String verifyId;
    private static final long TOW_SECONDS = 2 * 1000;

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    public static ChangePhoneFragment newInstance() {
        return new ChangePhoneFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.change_phone_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.phone_setting));
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBackground.setOnClickListener(v -> toCloseInput());
        mBinding.etAccount.setHint(Local.s(getString(R.string.enter_phone_number)));
        mBinding.etCode.setHint(Local.s(getString(R.string.change_phone_code)));
        mBinding.btnNext.setLocalText(getResources().getString(R.string.next));
        mBinding.etAccount.addTextChangedListener(mWatcher);
        mBinding.etCode.addTextChangedListener(mWatcher);
        mBinding.tvPhoneZone.setOnClickListener(v -> toChoosePhoneZone());
        mBinding.btnNext.setOnClickListener(v -> {
            setNextBtnEnable(false);
            onNextClick();
        });
        mBinding.ivCode.setOnClickListener(v -> {
            setIvCodeEnable(false);
            getVerificationCode();
        });
        initDefaultPhoneZone();
        updateBtnStateEnable();
        getVerificationCode();
    }

    private void updateBtnStateEnable() {
        final String account = mBinding.etAccount.getText().toString().trim();
        final String verificationCode = mBinding.etCode.getText().toString().trim();

        boolean enable = !TextUtils.isEmpty(account)
                && !TextUtils.isEmpty(verificationCode)
                && !TextUtils.isEmpty(phoneZone);
        setNextBtnEnable(enable);
    }

    private void setNextBtnEnable(final boolean enable) {
        mBinding.btnNext.setAlpha(enable ? 1.0f : 0.5f);
        mBinding.btnNext.setEnabled(enable);
    }

    private void onNextClick() {
        final String account = mBinding.etAccount.getText().toString().trim();
        final String verifyCode = mBinding.etCode.getText().toString().trim();

        if (TextUtils.isEmpty(phoneZone) || phoneZone.split(" ").length < 1 || TextUtils.isEmpty(verifyCode)) {
            showErrorToast();
            setNextBtnEnable(true);
            return;
        }

        final String phoneZoneCode = phoneZone.split(" ")[0];

        if (TextUtils.isEmpty(account)
                || !RegxUtil.isPhoneNumber(phoneZoneCode, account)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.phone_format_illegal));
            setNextBtnEnable(true);
            return;
        }

        toGetMessage(phoneZoneCode, account, verifyCode);
    }

    private void toGetMessage(@NonNull final String phoneZoneCode, @NonNull final String phone, final String verifyCode) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setNextBtnEnable(true);
        });
        DinSDK.getUserInstance().bindPhone(phoneZoneCode, phone, DDSystemUtil.getWidevineId()
                , verifyCode, verifyId, new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragmentWithCallBack();
                        if (!ChangePhoneFragment.this.isAdded()) {
                            return;
                        }

                        if (i == ErrorCode.ERROR_PHONE_EXIST) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail, getResources().getString(R.string.error_phone_exist));
                        } else if (i == ErrorCode.ERROR_WRONG_VERIFICATION_CODE_ENTERED) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_wrong_verifyCode)));
                            getVerificationCode();
                        } else if (i == ErrorCode.ERROR_VERIFICATION_CODE_ENTERED_TIMEOUT) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_verifyCode_timeout)));
                            getVerificationCode();
                        } else {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));
                        }
                        setNextBtnEnable(true);
                    }

                    @Override
                    public void onSuccess() {
                        closeLoadingFragmentWithCallBack();
                        if (!ChangePhoneFragment.this.isAdded()) {
                            return;
                        }
                        getMainActivity().addCommonFragment(ChangePhoneVerifyCodeFragment.newInstance(phoneZoneCode, phone));
                        setNextBtnEnable(true);
                    }
                });
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    public void closeInput() {
        toCloseInput();
    }

    /**
     * 初始化手机号注册的默认区号
     */
    private void initDefaultPhoneZone() {
        String defaultPhoneZone = null;
        if (null != DinSDK.getUserInstance().getUser()
                && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone())) {
            mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.change_the_binding));
            final String[] phones = DinSDK.getUserInstance().getUser().getPhone().split(" ");
            if (phones.length > 1) {
                final String phoneZoneCode = phones[0];
                for (int i = 0; i < ChoosePhoneZoneFragment.countryCodes.length; i++) {
                    if (phoneZoneCode.equals(ChoosePhoneZoneFragment.countryCodes[i])) {
                        defaultPhoneZone = phoneZoneCode + " " + ChoosePhoneZoneFragment.countryNames[i];
                        break;
                    }
                }
            }
        } else {
            defaultPhoneZone = ChoosePhoneZoneFragment.getCachePhoneZone();
        }

        if (TextUtils.isEmpty(defaultPhoneZone)) {
            phoneZone = APIKey.DEFAULT_PHONE_TEXT;
        } else {
            phoneZone = defaultPhoneZone;
        }
        onPhoneZoneUpdate();
    }

    public void toChoosePhoneZone() {
        String currentPhoneZone = "";
        if (!TextUtils.isEmpty(phoneZone)) {
            currentPhoneZone = phoneZone;
        }
        ChoosePhoneZoneFragment fragment = ChoosePhoneZoneFragment.newInstance(currentPhoneZone);
        fragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(fragment);
    }

    // 选择时区回调
    @Override
    public void onResult(String code, String name) {
        phoneZone = code + " " + name;
        onPhoneZoneUpdate();
    }

    private void onPhoneZoneUpdate() {
        String phoneCode = "";
        if (!TextUtils.isEmpty(phoneZone) && phoneZone.split(" ").length > 1) {
            phoneCode = phoneZone.split(" ")[0];
        }
        mBinding.tvPhoneZone.setText(phoneCode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BindPhoneSuccessEvent event) {
        removeSelf();
    }

    private void getVerificationCode() {
        mBinding.viewLoading.setVisibility(View.VISIBLE);
        DinSDK.getUserInstance().refreshVerifyCode(DDSystemUtil.getWidevineId(), new IResultCallback2<RefreshVerifyCodeResponse.ResultBean>() {
            @Override
            public void onSuccess(RefreshVerifyCodeResponse.ResultBean resultBean) {
                DDLog.d(TAG, "refreshVerifyCode. onSuccess");
                mBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);

                if (resultBean != null) {
                    verifyId = resultBean.getVerify_id();
                    String base64Image = resultBean.getBase64();
                    Bitmap bitmap = BitmapUtil.convertStringToBitmap(base64Image);
                    Bitmap roundedBitmap = BitmapUtil.getRoundedCornerBitmap(getContext(), bitmap, 6);
                    mBinding.ivCode.setImageBitmap(roundedBitmap);
                }
            }

            @Override
            public void onError(int code, String msg) {
                DDLog.e(TAG, "refreshVerifyCode. onError: " + code + " " + msg);
                mBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);

                if (ErrorCode.ERROR_TOO_MANY_VERIFICATION == code) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_too_many_verifyCode)));
                    return;
                }
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));

            }
        });
    }

    private void setIvCodeEnable(boolean enable) {
        if (enable) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mBinding.ivCode.setEnabled(true);
                    mBinding.ivCode.setAlpha(1.0f);
                }
            }, TOW_SECONDS);
            return;
        }
        mBinding.ivCode.setEnabled(false);
        mBinding.ivCode.setAlpha(0.5f);
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        getVerificationCode();
    }
}

