package com.dinsafer.module.settting.ui;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.common.NetKeyConstants;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.IPCKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SmartButtonSelectTargetLayoutBinding;
import com.dinsafer.model.CategoryPlugsEntry;
import com.dinsafer.model.SmartButtonSceneData;
import com.dinsafer.model.SmartButtonTargetData;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.adapter.SmartButtonSelectTargetItem;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.SmartButtonUtil;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;

/**
 * SmartButton选择控制对象页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/10/27 6:14 PM
 */
public class SmartButtonSelectTargetFragment extends MyBaseFragment<SmartButtonSelectTargetLayoutBinding>
        implements BaseQuickAdapter.OnItemClickListener, IDeviceCallBack {

    private static final String DATA_KEY_SMART_BUTTON_DATA = "smart_button_data";
    private static final String DATA_KEY_TARGET_INFO = "target_info";
    private static final String DATA_KEY_PLUGIN_TYPE = "src_plugin_type";
    private static final String DATA_KEY_PAGE_TITTLE = "page_tittle";

    /**
     * 当前页面显示的配件类型
     */
    public static final int TYPE_UNDEFINE = 0; // 未定义
    public static final int TYPE_BELL = IPCKey.WIRELESS; // 警笛
    public static final int TYPE_BULB = 2; // 灯泡
    public static final int TYPE_PLUG = IPCKey.SMART_PLUGS; // 插座
    public static final int TYPE_SHUTTER = IPCKey.RELAY; // 继电器

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({TYPE_BELL, TYPE_BULB, TYPE_PLUG, TYPE_SHUTTER})
    @interface PluginType {
    }

    private static final String ASK_WIRELESS_34 = "34";
    private static final String ASK_PLUGIN_3E = "3E";
    private static final String ASK_PLUGIN_4E = "4E";

    private static final String[] ASK_PLUGIN_IDS = {ASK_PLUGIN_3E, ASK_PLUGIN_4E};

    // 旧配件默认信息
    private static final int DTYPE_DEFAULT_WIRELESS = 4; // 警笛默认dtype
    private static final int DTYPE_DEFAULT_PLUG = 3;  // 旧插座默认dtype
    private static final String STYPE_DEFAULT_SMART_PLUG = "15";// 旧插座默认stype
    private static final String SEND_ID_DEFAULT = ""; // 旧配件默认sendid

    /**
     * 新型无线警笛ID
     */
    private static final String[] ASK_WIRELESS_IDS = {ASK_WIRELESS_34};

    private static final int ITEM_DECORATION_HEIGHT_DP = 1;
    private static final int ITEM_DECORATION_START_MARGIN_DP = 15;

    private int mSceneType;
    private List<SmartButtonTargetData> mDatas;
    private SmartButtonSelectTargetItem mAdapter;
    private SmartButtonTargetData mSmartButtonData; // SmartButton信息
    private SmartButtonSceneData mTargetInfo;
    private Call<ResponseBody> mPluginCall;

    private @PluginType
    int mPluginType; // 当前页面显示的配件类型，用于判断列表数据来源
    private @SmartButtonTargetData.SmartButtonTargetType
    int mDefaultTargetType; // 默认配件的类型，用于根据服务器返回的配件信息创建item
    int mDefaultDtype; // 旧配件默认dtype，用于根据服务器返回的配件信息创建item
    String mDefaultStype = ""; // 旧配件默认dtype，用于根据服务器返回的配件信息创建item
    String mEnptyTargetHint = "";

    /**
     * 配件类型-控制对象
     * 目前有以下的类型：
     * {@link com.dinsafer.config.IPCKey#SMART_BUTTON}
     * {@link com.dinsafer.config.IPCKey#RC_KEY}
     */
    private int mSourcePluginType;

    private Device mPluginDevice;

    private Device mPanelDevice;

    private int mTittleResId;

    /**
     * @param smartButtonSceneData Scene item信息
     * @param smartButtonData      SmartButton 信息
     * @return
     */
    public static SmartButtonSelectTargetFragment newInstance(@NonNull SmartButtonSceneData smartButtonSceneData,
                                                              @NonNull SmartButtonTargetData smartButtonData) {
        return newInstance(smartButtonSceneData, smartButtonData, IPCKey.SMART_BUTTON, R.string.select_chime);
    }

    public static SmartButtonSelectTargetFragment newInstance(@NonNull SmartButtonSceneData smartButtonSceneData,
                                                              @NonNull SmartButtonTargetData smartButtonData,
                                                              int srcPluginType) {
        return newInstance(smartButtonSceneData, smartButtonData, srcPluginType, R.string.smart_button_select_control_target);
    }

    /**
     * @param smartButtonSceneData Scene item信息
     * @param smartButtonData      SmartButton 信息
     * @param srcPluginType
     * @return
     */
    public static SmartButtonSelectTargetFragment newInstance(@NonNull SmartButtonSceneData smartButtonSceneData,
                                                              @NonNull SmartButtonTargetData smartButtonData,
                                                              int srcPluginType, int tittleResId) {
        SmartButtonSelectTargetFragment fragment = new SmartButtonSelectTargetFragment();
        Bundle args = new Bundle();
        args.putParcelable(DATA_KEY_TARGET_INFO, smartButtonSceneData);
        args.putParcelable(DATA_KEY_SMART_BUTTON_DATA, smartButtonData);
        args.putInt(DATA_KEY_PLUGIN_TYPE, srcPluginType);
        args.putInt(DATA_KEY_PAGE_TITTLE, tittleResId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.smart_button_select_target_layout;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPluginCall) {
            mPluginCall.cancel();
        }
        if (null != mPluginDevice) {
            mPluginDevice.unregisterDeviceCallBack(this);
            mPluginDevice = null;
        }
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.btnBuy.setOnClickListener(v -> onBuyClick());
    }

    @Override
    public void initData() {
        super.initData();
        showTimeOutLoadinFramgmentWithErrorAlert();
        initPageArguments();
        initPluginType();

        if (mTittleResId <= 0) {
            mTittleResId = R.string.smart_button_select_control_target;
        }
        mBinding.commonBar.commonBarTitle.setLocalText(getString(mTittleResId));
        String emptyHint = Local.s(getResources().getString(R.string.smart_button_empty_target_hint))
                .replace("#plugin", mEnptyTargetHint);
        mBinding.tvTargetEmptyHint.setText(emptyHint);
        mBinding.btnBuy.setLocalText(getString(R.string.rta_setting_btn_buy));
        initBuyVisible();

        initRecyclerView();
    }

    private void initBuyVisible() {
        boolean enableBuy = AppConfig.Functions.SUPPORT_SMART_BUTTON_BUY;
        if (TYPE_BELL == mPluginType) {
            enableBuy = enableBuy && !TextUtils.isEmpty(APIKey.WEB_PRODUCT_CHIME_URL);
        } else if (TYPE_BULB == mPluginType) {
            enableBuy = enableBuy && !TextUtils.isEmpty(APIKey.WEB_PRODUCT_BULB_URL);
        } else {
            enableBuy = enableBuy && !TextUtils.isEmpty(APIKey.SMART_BUTTON_BUY_URL);
        }
        mBinding.btnBuy.setVisibility(enableBuy ? View.VISIBLE : View.INVISIBLE);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();

        if (null == mSmartButtonData || TextUtils.isEmpty(mSmartButtonData.getTargetId())) {
            showErrorToast();
            return;
        }

        mPluginDevice = DinHome.getInstance().getDevice(mSmartButtonData.getTargetId());
        if (null != mPluginDevice) {
            mPluginDevice.registerDeviceCallBack(this);
        }

        mPanelDevice = DinHome.getInstance().getDevice(CommonDataUtil.getInstance().getCurrentPanelID());
        if (null != mPanelDevice) {
            mPanelDevice.registerDeviceCallBack(this);
        }

        switch (mSceneType) {
            case SmartButtonUtil.SCENE_TYPE_RING_BELL:
                requestPluginData();
                break;
            case SmartButtonUtil.SCENE_TYPE_SWITCH_BULB:
                break;
            case SmartButtonUtil.SCENE_TYPE_SWITCH_PLUG:
                requestPluginData();
                break;
            case SmartButtonUtil.SCENE_TYPE_CONTROL_SHUTTER:
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
                break;
            default:
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
                break;
        }

    }

    /**
     * 初始化RecyclerView
     */
    private void initRecyclerView() {
        DDLog.i(TAG, "initRecyclerView");
        mDatas = new ArrayList<>();

        mAdapter = new SmartButtonSelectTargetItem(R.layout.smart_button_select_target_item, mDatas);
        mAdapter.setOnItemClickListener(this);
        mBinding.lvTarget.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.lvTarget.setAdapter(mAdapter);
    }

    /**
     * 获取从上个页面传递过来的参数
     */
    private void initPageArguments() {
        DDLog.i(TAG, "initPageArguments");
        mSmartButtonData = getArguments().getParcelable(DATA_KEY_SMART_BUTTON_DATA);
        mTargetInfo = getArguments().getParcelable(DATA_KEY_TARGET_INFO);
        mSourcePluginType = getArguments().getInt(DATA_KEY_PLUGIN_TYPE, IPCKey.SMART_BUTTON);
        mTittleResId = getArguments().getInt(DATA_KEY_PAGE_TITTLE, 0);
    }

    /**
     * 初始化页面配件类型
     * 根据场景，觉得当前的列表数据配件类型
     */
    private void initPluginType() {
        DDLog.i(TAG, "initPluginType");
        mPluginType = TYPE_UNDEFINE;
        if (null != mTargetInfo) {
            mSceneType = mTargetInfo.getSceneType();

            switch (mSceneType) {
                case SmartButtonUtil.SCENE_TYPE_RING_BELL:
                    mPluginType = TYPE_BELL;
                    mDefaultTargetType = SmartButtonTargetData.TARGET_TYPE_BELL;
                    mDefaultDtype = DTYPE_DEFAULT_WIRELESS;
                    mEnptyTargetHint = Local.s(getString(R.string.doorbell));
                    break;
                case SmartButtonUtil.SCENE_TYPE_SWITCH_BULB:
                    mPluginType = TYPE_BULB;
                    mDefaultTargetType = SmartButtonTargetData.TARGET_TYPE_TUYA_BULB;
                    mEnptyTargetHint = Local.s(getString(R.string.bulb));
                    break;
                case SmartButtonUtil.SCENE_TYPE_SWITCH_PLUG:
                    mPluginType = TYPE_PLUG;
                    mDefaultTargetType = SmartButtonTargetData.TARGET_TYPE_SMART_PLUG;
                    mDefaultDtype = DTYPE_DEFAULT_PLUG;
                    mDefaultStype = STYPE_DEFAULT_SMART_PLUG;
                    mEnptyTargetHint = Local.s(getString(R.string.plug));
                    break;
                case SmartButtonUtil.SCENE_TYPE_CONTROL_SHUTTER:
                    mPluginType = TYPE_SHUTTER;
                    mDefaultTargetType = SmartButtonTargetData.TARGET_TYPE_SHUTTER;
                    break;
                default:
                    break;
            }
        }
        DDLog.i(TAG, "Current plugin type is: " + mPluginType);
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        DDLog.i(TAG, "onItemClick, position: " + position);

        mTargetInfo.setTargetData(mDatas.get(position));
        getDelegateActivity().addCommonFragment(SmartButtonSceneEditFragment.newInstance(
                mTargetInfo, mSmartButtonData, mSourcePluginType));
    }

    private void requestPluginData() {
        DDLog.i(TAG, "requestPluginData");

        if (TYPE_PLUG != mPluginType
                && TYPE_BELL != mPluginType
                && TYPE_SHUTTER != mPluginType) {
            DDLog.i(TAG, "无需从服务器请求配件信息");
            return;
        }

        if (null == mPluginDevice) {
            DDLog.e(TAG, "No device");
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            return;
        }

        if (TYPE_PLUG == mPluginType && null == mPanelDevice) {
            DDLog.e(TAG, "No panelDevice");
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            return;
        }

        if (TYPE_PLUG == mPluginType) {
            mPanelDevice.submit(PanelParamsHelper.getPlugsInfo());
        } else {
            mPluginDevice.submit(PanelParamsHelper.getSmartButtonTargetList(mPluginType));
        }
    }

    /**
     * 当前配件列表是否支持就配件
     * 目前仅支持旧插座
     *
     * @return true: 支持旧配件
     */
    private boolean isSupportOldPlugin() {
        DDLog.i(TAG, "isSupportOldPlugin");
        return TYPE_PLUG == mPluginType;
    }

    /**
     * 刷新列表或显示空提示页
     */
    private void updateListOrShowEmptyView() {
        DDLog.i(TAG, "updateListOrShowEmptyView");

        if (null == mDatas || 0 >= mDatas.size()) {
            mBinding.lvTarget.setVisibility(View.GONE);
            mBinding.rlEmptyView.setVisibility(View.VISIBLE);
        } else {
            mBinding.lvTarget.setVisibility(View.VISIBLE);
            mBinding.rlEmptyView.setVisibility(View.GONE);
            mAdapter.notifyDataSetChanged();
        }
    }

    public void onBuyClick() {
        DDLog.i(TAG, "onBuyClick");

        Uri uri;
        if (TYPE_BELL == mPluginType) {
            uri = Uri.parse(APIKey.WEB_PRODUCT_CHIME_URL);
        } else if (TYPE_BULB == mPluginType) {
            uri = Uri.parse(APIKey.WEB_PRODUCT_BULB_URL);
        } else {
            uri = Uri.parse(APIKey.SMART_BUTTON_BUY_URL);
        }
        DDLog.i(TAG, "onBuyClick: " + uri.toString());
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.setData(uri);
        startActivity(intent);
    }


    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)) {
            return;
        }

        DDLog.i(TAG, "onCmdCallBack, map: " + map);
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PanelCmd.GET_PLUGS_INFO.equals(cmd) || PluginCmd.GET_TARGET_LIST.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                showErrorToast();
            } else {
                switch (mSceneType) {
                    case SmartButtonUtil.SCENE_TYPE_RING_BELL:
                        if (null == mPluginDevice
                                || !deviceId.equals(mPluginDevice.getId())) {
                            return;
                        }
                        onRequestPluginData(map);
                        break;
                    case SmartButtonUtil.SCENE_TYPE_SWITCH_PLUG:
                        if (!deviceId.equals(mPanelDevice.getId())) {
                            return;
                        }
                        String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, null);
                        if (TextUtils.isEmpty(result)) {
                            showErrorToast();
                        } else {
                            onRequestPluginInfoData(result);
                        }
                        break;
                }

            }
        }
    }

    private void onRequestPluginData(Map<String, Object> map) {
        final String result = DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, "");
        try {
            JSONObject jsonObject = new JSONObject();
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put("Result", resultJson);
            Gson gson = new Gson();
            CategoryPlugsEntry categoryPlugsEntry = gson.fromJson(jsonObject.toString(), CategoryPlugsEntry.class);
            String pluginName;
            String pluginId;
            if (SmartButtonSelectTargetFragment.this.isAdded() && categoryPlugsEntry.getResult() != null) {
                // 旧配件——目前仅支持旧插座
                if (isSupportOldPlugin()
                        && null != categoryPlugsEntry.getResult().getDatas()
                        && 0 < categoryPlugsEntry.getResult().getDatas().size()) {
                    for (int i = 0; i < categoryPlugsEntry.getResult().getDatas().size(); i++) {
                        pluginName = categoryPlugsEntry.getResult().getDatas().get(i).getName();
                        pluginId = categoryPlugsEntry.getResult().getDatas().get(i).getId();

                        if (TextUtils.isEmpty(pluginId)) {
                            DDLog.e(TAG, "Find a plugin with empty id.");
                            continue;
                        }

                        if (TextUtils.isEmpty(pluginName)) {
                            // 没有名字，设置默认名字
                            if (pluginId.startsWith("!")) {
                                pluginName = CommonDataUtil.getInstance().getASKNameByBSType(
                                        categoryPlugsEntry.getResult().getDatas().get(i).getSubcategory());
                            } else {
                                pluginName = CommonDataUtil.getInstance().getSTypeByID(
                                        categoryPlugsEntry.getResult().getDatas().get(i).getId());
                            }
                            pluginName = Local.s(pluginName) + "_" + pluginId;
                        }

                        mDatas.add(new SmartButtonTargetData.Builder()
                                .setTargetType(mDefaultTargetType)
                                .setTargetId(pluginId)
                                .setDtype(mDefaultDtype)
                                .setSendid(SEND_ID_DEFAULT)
                                .setStype(mDefaultStype)
                                .setTargetName(pluginName)
                                .createSmartButtonTargetData());
                    }
                }

                // ASK配件
                JSONObject object = DDJSONUtil.getJSONObject(resultJson, "newaskdatas");
                String[] newAskIds = null;
                if (IPCKey.WIRELESS == mPluginType) {
                    newAskIds = ASK_WIRELESS_IDS;
                } else if (IPCKey.SMART_PLUGS == mPluginType) {
                    newAskIds = ASK_PLUGIN_IDS;
                }

                if (null != object && null != newAskIds) {
                    SmartButtonTargetData targetData;
                    JSONObject askJsonObject;
                    for (String wirelessId : newAskIds) {
                        if (object.has(wirelessId)) {
                            JSONArray plugins = (JSONArray) object.get(wirelessId);
                            if (plugins.length() > 0) {
                                for (int i = 0; i < plugins.length(); i++) {
                                    askJsonObject = (JSONObject) plugins.get(i);
                                    pluginName = DDJSONUtil.getString(askJsonObject, "name");
                                    pluginId = DDJSONUtil.getString(askJsonObject, "id");

                                    if (TextUtils.isEmpty(pluginName)) {
                                        // 名字为空，设置默认名字
                                        pluginName = CommonDataUtil.getInstance().getASKNameByBSType(wirelessId);
                                        pluginName = Local.s(pluginName) + "_" + pluginId;
                                    }

                                    // 初始化其他数据，如sendid等
                                    targetData = new SmartButtonTargetData.Builder()
                                            .setTargetType(mDefaultTargetType)
                                            .setTargetName(pluginName)
                                            .setTargetId(pluginId)
                                            .setStype(DDJSONUtil.getString(askJsonObject, SmartButtonUtil.SERVICE_KEY_STYPE))
                                            .setSendid(DDJSONUtil.getString(askJsonObject, SmartButtonUtil.SERVICE_KEY_SEND_ID))
                                            .setDtype(DDJSONUtil.getInt(askJsonObject, SmartButtonUtil.SERVICE_KEY_DTYPE))
                                            .createSmartButtonTargetData();
                                    mDatas.add(targetData);
                                }
                            }
                        }
                    }
                }

                updateListOrShowEmptyView();
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }
        } catch (Exception e) {
            e.printStackTrace();
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    private void onRequestPluginInfoData(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            JSONObject askdatas = DDJSONUtil.getJSONObject(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__INFO);
            final int dtyp = 10;

            if (null != askdatas) {
                JSONArray smartArray = DDJSONUtil.getJSONarray(askdatas, NetKeyConstants.NET_KEY_SMART__PLUG);
                dealPlugins(smartArray, dtyp);
                JSONArray repeaterArray = DDJSONUtil.getJSONarray(askdatas, "signal_repeater_plug");
                dealPlugins(repeaterArray, dtyp);
            }
            updateListOrShowEmptyView();
            closeTimeOutLoadinFramgmentWithErrorAlert();
        } catch (JSONException e) {
            e.printStackTrace();
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }

    }

    private void dealPlugins(JSONArray plugins, int dtype) {
        if (plugins.length() > 0) {
            try {
                SmartButtonTargetData targetData;
                JSONObject askJsonObject;
                String pluginName;
                String pluginId;
                String stype;
                for (int i = 0; i < plugins.length(); i++) {
                    askJsonObject = (JSONObject) plugins.get(i);
                    pluginName = DDJSONUtil.getString(askJsonObject, NetKeyConstants.NET_KEY_NAME);
                    pluginId = DDJSONUtil.getString(askJsonObject, NetKeyConstants.NET_KEY_ID);
                    stype = DDJSONUtil.getString(askJsonObject, NetKeyConstants.NET_KEY_S_TYPE);

                    if (TextUtils.isEmpty(pluginName)) {
                        // 名字为空，设置默认名字
                        pluginName = CommonDataUtil.getInstance().getASKNameByBSType(stype);
                        pluginName = Local.s(pluginName) + "_" + pluginId;
                    }

                    // 初始化其他数据，如sendid等
                    targetData = new SmartButtonTargetData.Builder()
                            .setTargetType(mDefaultTargetType)
                            .setTargetName(pluginName)
                            .setTargetId(pluginId)
                            .setStype(DDJSONUtil.getString(askJsonObject, SmartButtonUtil.SERVICE_KEY_STYPE))
                            .setSendid(DDJSONUtil.getString(askJsonObject, SmartButtonUtil.SERVICE_KEY_SEND_ID))
                            .setDtype(dtype)
                            .createSmartButtonTargetData();
                    mDatas.add(targetData);
                }
            } catch (JSONException e) {
                e.printStackTrace();
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
            }
        }
    }
}
