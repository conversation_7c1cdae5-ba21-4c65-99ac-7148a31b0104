package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.content.res.ColorStateList;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;


/**
 * Created by rinfon on 15/6/26.
 */
public class AlertDialogDelete extends Dialog {

    int layoutRes;//布局文件

    Context mContext;

    LocalCustomButton mOk, mCancel;

    private LocalTextView mContent;

    private boolean isCanCancel = true;

    private ImageView mContentImageView;

    public AlertDialogDelete(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.alert_dialog_delete;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mContent = (LocalTextView) view.findViewById(R.id.alert_dialog_content);
        mOk = (LocalCustomButton) view.findViewById(R.id.alert_dialog_ok);
        mCancel = (LocalCustomButton) view.findViewById(R.id.alert_dialog_cancel);
        mContentImageView = (ImageView) view.findViewById(R.id.alert_dialog_content_imageview);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onOkClick();
                }
            }
        });

        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOk.setVisibility(View.VISIBLE);
        } else {
            mOk.setVisibility(View.GONE);
        }

        if (builder.isShowSuccessView) {
            mContentImageView.setVisibility(View.VISIBLE);
            if (builder.isSuccess) {
                mContentImageView.setImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.color_brand_primary)));
                mContentImageView.setImageResource(R.drawable.icon_dialogue_succeed);
            } else {
                mContentImageView.setImageResource(R.drawable.icon_dialogue_failed);
            }
        } else {
            mContentImageView.setVisibility(View.GONE);
        }

        if (builder.getOkColor() != 0) {
            mOk.setTextColor(builder.getOkColor());
        }

        if (builder.getCancelColor() != 0) {
            mCancel.setTextColor(builder.getCancelColor());
        }
        if (builder.isShowCancel) {
            mCancel.setLocalText(builder.mCancel);
            mCancel.setVisibility(View.VISIBLE);
            if (builder.cancelClick != null) {
                mCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (builder.isAutoDismiss) {
                            dismiss();
                        }
                        builder.cancelClick.onClick();
                    }
                });
            }
        } else {
            mCancel.setVisibility(View.GONE);
        }


        mContent.setLocalText(builder.mContent);
        isCanCancel = builder.isCanCancel;

        if (builder.getBackgroundTint() != 0) {
            ((RelativeLayout) view.findViewById(R.id.rl_bg)).setBackgroundTintList(ColorStateList.valueOf(builder.getBackgroundTint()));
        }

        if (builder.getContentColor() != 0) {
            mContent.setTextColor(builder.getContentColor());
        }

        if (builder.getOkBtnSolidColor() != 0) {
            mOk.setPressedColor(builder.getOkBtnSolidColor());
            mOk.setNormalColor(builder.getOkBtnSolidColor());
        }

        if (builder.getOkBtnStrokeColor() != 0) {
            mOk.setStrokeColor(builder.getOkBtnStrokeColor());
        }

    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }


    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        if (isCanCancel)
            super.cancel();
    }

    public void setOKText(String ok) {
        mOk.setLocalText(ok);
    }

    public void setCancelText(String cancel) {
        mCancel.setLocalText(cancel);
    }

    public void setOKClick(View.OnClickListener onclick) {
        mOk.setOnClickListener(onclick);
    }

    public void setCancel(View.OnClickListener onclick) {
        mCancel.setOnClickListener(onclick);
    }

    public void setContent(String content) {

        mContent.setLocalText(content);
    }

    public interface AlertOkClickCallback {

        void onOkClick();
    }

    public interface AlertCancelClickCallback {

        void onClick();
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mOK;

        private boolean isShowOK = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isCanCancel = true;

        private boolean isAutoDismiss = true;

        private int OkColor = 0;

        private int CancelColor = 0;

        private int contentColor = 0;

        private boolean isShowSuccessView;

        private boolean isSuccess = true;

        private int backgroundTint = 0;

        private int okBtnSolidColor = 0;

        private int okBtnStrokeColor = 0;

        private AlertOkClickCallback okClick;

        private AlertCancelClickCallback cancelClick;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setCancelListener(AlertCancelClickCallback cancelClick) {
            this.cancelClick = cancelClick;
            return this;
        }

        public Builder setCancel(String cancel) {
            if (TextUtils.isEmpty(cancel)) {
                isShowCancel = false;
            } else {
                mCancel = cancel;
                isShowCancel = true;
            }
            return this;
        }

        public Builder setAutoDissmiss(boolean isAutoDismiss) {
            this.isAutoDismiss = isAutoDismiss;
            return this;
        }

        public Builder setCanCancel(boolean isCanCancel) {
            this.isCanCancel = isCanCancel;
            return this;
        }

        public Builder setIsShowSuccessView(boolean isShow) {
            this.isShowSuccessView = isShow;
            return this;
        }

        public Builder setIsSuccess(boolean success) {
            this.isSuccess = success;
            return this;
        }

        public int getOkColor() {
            return OkColor;
        }

        public Builder setOkColor(int okColor) {
            OkColor = okColor;
            return this;
        }

        public int getCancelColor() {
            return CancelColor;
        }

        public Builder setCancelColor(int CancelColor) {
            this.CancelColor = CancelColor;
            return this;
        }

        public Builder setBackgroundTint(int backgroundTint) {
            this.backgroundTint = backgroundTint;
            return this;
        }

        public int getBackgroundTint() {
            return backgroundTint;
        }

        public Builder setContentColor(int contentColor) {
            this.contentColor = contentColor;
            return this;
        }

        public int getContentColor() {
            return contentColor;
        }

        public Builder setOkBtnSolidColor(int okBtnSolidColor) {
            this.okBtnSolidColor = okBtnSolidColor;
            return this;
        }

        public Builder setOkBtnStrokeColor(int okBtnStrokeColor) {
            this.okBtnStrokeColor = okBtnStrokeColor;
            return this;
        }

        public int getOkBtnSolidColor() {
            return okBtnSolidColor;
        }

        public int getOkBtnStrokeColor() {
            return okBtnStrokeColor;
        }

        public AlertDialogDelete preBuilder() {
            AlertDialogDelete alertDialog = new AlertDialogDelete(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
