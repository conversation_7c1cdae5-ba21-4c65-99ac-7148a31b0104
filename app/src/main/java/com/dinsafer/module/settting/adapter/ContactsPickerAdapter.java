package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.CheckBox;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.dinnet.R;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by Rinfon on 16/7/1.
 */
@Deprecated
public class ContactsPickerAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<HashMap<String, String>> list;

    private ArrayList<HashMap<String, String>> selectList;

    private final String KEY_CHECK = "isCheck";
    private final String obj = "";

    public ContactsPickerAdapter(Activity mActivity, ArrayList<HashMap<String, String>> mData) {
        this.mActivity = mActivity;
        this.list = mData;
        selectList = new ArrayList<HashMap<String, String>>();
    }

    @Override
    public int getCount() {
        if (list != null)
            return list.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.contacts_picker_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        HashMap<String, String> dataMap = list.get(position);
        assert holder != null;
        holder.contactPickerName.setText(dataMap.get("name"));
        holder.contactPickerPhone.setText(list.get(position).get("phone"));
        holder.contactPickerCheckbox.setChecked(dataMap.get(KEY_CHECK) != null);

//        final ViewHolder finalHolder = holder;
//        holder.contactPickLayout.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                boolean isCheck = dataMap.get(KEY_CHECK) == null;
//                if (isCheck){
//                    dataMap.remove(KEY_CHECK);
//                }else{
//                    dataMap.put(KEY_CHECK,obj);
//                }
//                finalHolder.contactPickerCheckbox.setChecked(!isCheck);
//            }
//        });
//        holder.contactPickerCheckbox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
//            @Override
//            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
//                if (b)
//                    selectList.add(list.get(position));
//                else
//                    selectList.remove(list.get(position));
//
//            }
//        });
        return convertView;
    }

    public ArrayList<HashMap<String, String>> getSelectList() {
        return selectList;
    }

    public void setSelectList(ArrayList<HashMap<String, String>> selectList) {
        this.selectList = selectList;
    }

    public ArrayList<HashMap<String, String>> getList() {
        return list;
    }

    public void setList(ArrayList<HashMap<String, String>> list) {
        this.list = list;
    }

    static class ViewHolder {
        TextView contactPickerName;
        TextView contactPickerPhone;
        CheckBox contactPickerCheckbox;
        RelativeLayout contactPickLayout;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            contactPickerName = view.findViewById(R.id.contact_picker_name);
            contactPickerPhone = view.findViewById(R.id.contact_picker_phone);
            contactPickerCheckbox = view.findViewById(R.id.contact_picker_checkbox);
            contactPickLayout = view.findViewById(R.id.contact_picker_layout);
        }
    }

}
