package com.dinsafer.module.settting.ui;

import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.RelativeLayout;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityIpcSosRecordBinding;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.module_base.base.MyBaseActivity;
import com.githang.statusbar.StatusBarCompat;

import java.text.SimpleDateFormat;

public class IPCSosRecordActivity extends MyBaseActivity<ActivityIpcSosRecordBinding> {

    private String url;

    private String ipcName;

    private boolean isFullScreen = false;

    private SimpleDateFormat sdf = new SimpleDateFormat("mm:ss");

    private boolean isAnimation = true;

    @Override
    protected int getContentViewLayoutID() {
        return R.layout.activity_ipc_sos_record;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        super.initView(savedInstanceState);
        StatusBarCompat.setStatusBarColor(this, getResources().getColor(R.color.black), false);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> finish());
        mBinding.glviewFullscreen.setOnClickListener(v -> toFullScreen());
        mBinding.resumePause.setOnClickListener(v -> clickPlay());
    }

    public void toFullScreen() {
        if (!isFullScreen)
            makeVideoFullScreen();
        else
            exitVideoFullScreen();
        isFullScreen = !isFullScreen;
    }

    public void initData() {
        super.initData();
        url = getIntent().getStringExtra("url");
        ipcName = getIntent().getStringExtra("ipcname");
        mBinding.commonBar.commonBarTitle.setLocalText(ipcName);
        url = DDSecretUtil.privateDownloadUrlWithDeadline(url);
        mBinding.videoView.setVideoPath(url);
        mBinding.videoView.start();
        mBinding.videoView.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                if (isAnimation) {
                    isAnimation = false;
                    mBinding.imgLoading.setVisibility(View.GONE);
                    mBinding.imgLoading.clearAnimation();
                    mBinding.glviewFullscreen.setVisibility(View.VISIBLE);
                }
                handler.post(run);
//                mp.setOnBufferingUpdateListener(new MediaPlayer.OnBufferingUpdateListener() {
//                    @Override
//                    public void onBufferingUpdate(MediaPlayer mp, int percent) {
//                        // 获得当前播放时间和当前视频的长度
//                        int currentPosition = videoView.getCurrentPosition();
//                        int duration = videoView.getDuration();
////                        int time = ((currentPosition * 100) / duration);
//                        // 设置进度条的主要进度，表示当前的播放时间
//                        seekBar.setProgress(currentPosition);
//                        seekBar.setMax(duration);
//                        seekBarFullscreen.setProgress(currentPosition);
//                        seekbarTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
//                        seekBarFullscreenTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
//                        seekBarFullscreen.setMax(duration);
//                    }
//                });
                mp.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mp) {
                        mBinding.seekBar.setProgress(0);
                        mBinding.seekBarFullscreen.setProgress(0);
                        mBinding.resumePause.setVisibility(View.VISIBLE);
                        mBinding.videoView.seekTo(0);
                        mBinding.videoView.pause();
                    }
                });

            }
        });

        mBinding.videoView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_UP)
                    clickPlay();
                return true;
            }
        });

        Animation operatingAnim = AnimationUtils.loadAnimation(this, R.anim.rotation);
        LinearInterpolator lin = new LinearInterpolator();
        operatingAnim.setInterpolator(lin);
        mBinding.imgLoading.startAnimation(operatingAnim);
        mBinding.glviewFullscreen.setVisibility(View.GONE);

    }

    private RelativeLayout.LayoutParams defaultVideoViewParams;
    private int defaultScreenOrientationMode;

    // play video in fullscreen mode
    private void makeVideoFullScreen() {

        defaultScreenOrientationMode = getResources().getConfiguration().orientation;
        defaultVideoViewParams = (RelativeLayout.LayoutParams) mBinding.videoView.getLayoutParams();
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        mBinding.controlView.setVisibility(View.GONE);
        mBinding.fullscreenControl.setVisibility(View.VISIBLE);
        mBinding.commonBar.getRoot().setVisibility(View.GONE);
        mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_small_screen);
        mBinding.videoView.postDelayed(new Runnable() {

            @Override
            public void run() {
                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) new RelativeLayout.LayoutParams(
                        RelativeLayout.LayoutParams.MATCH_PARENT,
                        RelativeLayout.LayoutParams.MATCH_PARENT);

                mBinding.videoView.setLayoutParams(params);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);
    }

    public void clickPlay() {
        if (mBinding.videoView.isPlaying()) {
            mBinding.resumePause.setVisibility(View.VISIBLE);
            mBinding.videoView.pause();
        } else {
            mBinding.resumePause.setVisibility(View.GONE);
            mBinding.videoView.start();
        }
    }

    // close fullscreen mode
    private void exitVideoFullScreen() {
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        mBinding.controlView.setVisibility(View.VISIBLE);
        mBinding.fullscreenControl.setVisibility(View.GONE);
        mBinding.commonBar.getRoot().setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarTitle.setLocalText(ipcName);
        mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_full_screen);
        mBinding.videoView.postDelayed(new Runnable() {

            @Override
            public void run() {
                mBinding.videoView.setLayoutParams(defaultVideoViewParams);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);
    }


    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // Checks the orientation of the screen
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            isFullScreen = true;
            defaultScreenOrientationMode = getResources().getConfiguration().orientation;
            defaultVideoViewParams = (RelativeLayout.LayoutParams) mBinding.videoView.getLayoutParams();
            mBinding.controlView.setVisibility(View.GONE);
            mBinding.fullscreenControl.setVisibility(View.VISIBLE);
            mBinding.commonBar.getRoot().setVisibility(View.GONE);
            mBinding.videoView.postDelayed(new Runnable() {

                @Override
                public void run() {
                    RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) new RelativeLayout.LayoutParams(
                            RelativeLayout.LayoutParams.MATCH_PARENT,
                            RelativeLayout.LayoutParams.MATCH_PARENT);

                    mBinding.videoView.setLayoutParams(params);
                    mBinding.videoView.layout(10, 10, 10, 10);
                }
            }, 100);
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            isFullScreen = false;
            setRequestedOrientation(defaultScreenOrientationMode);
            mBinding.controlView.setVisibility(View.VISIBLE);
            mBinding.fullscreenControl.setVisibility(View.GONE);
            mBinding.commonBar.getRoot().setVisibility(View.VISIBLE);
            mBinding.videoView.postDelayed(new Runnable() {

                @Override
                public void run() {
                    mBinding.videoView.setLayoutParams(defaultVideoViewParams);
                    mBinding.videoView.layout(10, 10, 10, 10);
                }
            }, 100);
        }
    }

    private Handler handler = new Handler();
    private Runnable run = new Runnable() {
        public void run() {
            // 获得当前播放时间和当前视频的长度
            // 获得当前播放时间和当前视频的长度
            int currentPosition = mBinding.videoView.getCurrentPosition();
            int duration = mBinding.videoView.getDuration();
//                        int time = ((currentPosition * 100) / duration);
            // 设置进度条的主要进度，表示当前的播放时间
            mBinding.seekBar.setProgress(currentPosition);
            mBinding.seekBar.setMax(duration);
            mBinding.seekBarFullscreen.setProgress(currentPosition);
            mBinding.seekbarTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
            mBinding.seekBarFullscreenTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
            mBinding.seekBarFullscreen.setMax(duration);

            handler.postDelayed(run, 1000);
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mBinding.videoView.setOnPreparedListener(null);
        handler.removeCallbacks(run);
    }

}

