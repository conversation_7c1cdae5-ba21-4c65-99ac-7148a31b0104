package com.dinsafer.module.settting.adapter;

import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPluginNewStyleBinding;
import com.dinsafer.model.panel.MainPanelHelper;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import androidx.annotation.IntDef;
import androidx.databinding.DataBindingUtil;

/**
 * 配件列表页Item的ViewHolder
 *
 * <AUTHOR>
 * @date 2020/8/17 3:22 PM
 */
public class ItemPluginHolder {
    public static final int BOTTOM_LINE_SHORT = 0;
    public static final int BOTTOM_LINE_LONG = 1;
    public static final int BOTTOM_LINE_HIDE = 2;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({BOTTOM_LINE_SHORT, BOTTOM_LINE_LONG, BOTTOM_LINE_HIDE})
    public @interface BottomLineStatus {
    }

    Animation mOperatingAnim;

    ItemPluginNewStyleBinding binding;

    ItemPluginHolder(View view) {
        binding = DataBindingUtil.bind(view);
        mOperatingAnim = AnimationUtils.loadAnimation(DinSaferApplication.getAppContext(), R.anim.rotation);
        mOperatingAnim.setInterpolator(new LinearInterpolator());
    }

    /**
     * 显示加载中的布局
     */
    public void showStatusLoading(String title) {
        binding.tvPluginName.setVisibility(View.VISIBLE);
        binding.ivPluginNameSignal.setVisibility(View.GONE);
        binding.tvPluginNameCenter.setVisibility(View.GONE);
        binding.ivPluginNameCenterSignal.setVisibility(View.GONE);
        binding.tvPluginStatusFirst.setVisibility(View.GONE);
        binding.tvPluginStatusSecond.setVisibility(View.GONE);
        binding.tvPluginStatusThird.setVisibility(View.GONE);

        binding.tvPluginName.setText(TextUtils.isEmpty(title) ? "" : title);
        showAndStartLoadingAnim();
    }

    public void showStatusFinished(String title) {
        showStatusFinished(title, -1, null, 0, null,
                0, null, 0);
    }

    public void showStatusFinished(String title, int signalLevel) {
        showStatusFinished(title, signalLevel, null, 0, null,
                0, null, 0);
    }

    public void showStatusFinished(String title, int signalLevel, String firstContent, int firstIconId) {
        showStatusFinished(title, signalLevel, firstContent, firstIconId, null,
                0, null, 0);
    }

    public void showStatusFinished(String title, int signalLevel, String firstContent, int firstIconId,
                                   String secondContent, int secondIconId) {
        showStatusFinished(title, signalLevel, firstContent, firstIconId, secondContent, secondIconId,
                null, 0);
    }

    /**
     * 显示加载完成的布局
     *
     * @param signalLevel  0-3 表示有信号图标，其他值不显示信号图标
     * @param firstIconId  0 表示没有任何状态图标
     * @param secondIconId 0 表示只有一个状态图标
     * @param thirdIconId  0 表示只有两个状态图标
     */
    public void showStatusFinished(String title, int signalLevel, String firstContent, int firstIconId,
                                   String secondContent, int secondIconId, String thirdContent, int thirdIconId) {
        // 隐藏所有内容
        stopAndHindLoading();
        binding.tvPluginName.setVisibility(View.GONE);
        binding.ivPluginNameSignal.setVisibility(View.GONE);
        binding.tvPluginNameCenter.setVisibility(View.GONE);
        binding.ivPluginNameCenterSignal.setVisibility(View.GONE);
        binding.tvPluginStatusFirst.setVisibility(View.GONE);
        binding.tvPluginStatusSecond.setVisibility(View.GONE);
        binding.tvPluginStatusThird.setVisibility(View.GONE);

        // 设置信号级别图标
        boolean hasSignalIcon = setSignalLevel(signalLevel);

        // 1、只显示配件名不显示状态图标
        if (0 == firstIconId && 0 == secondIconId && 0 == thirdIconId) {
            binding.tvPluginName.setVisibility(View.GONE);
            binding.tvPluginNameCenter.setVisibility(View.VISIBLE);
            if (hasSignalIcon) {
                binding.ivPluginNameCenterSignal.setVisibility(View.VISIBLE);
            }
            binding.tvPluginNameCenter.setText(TextUtils.isEmpty(title) ? "" : title);
            return;
        }

        // 2、显示顶部配件名
        binding.tvPluginName.setVisibility(View.VISIBLE);
        binding.tvPluginNameCenter.setVisibility(View.GONE);
        if (hasSignalIcon) {
            binding.ivPluginNameSignal.setVisibility(View.VISIBLE);
        }
        binding.tvPluginName.setText(TextUtils.isEmpty(title) ? "" : title);

        // 显示第一个状态图标
        if(0 != firstIconId) {
            binding.tvPluginStatusFirst.setVisibility(View.VISIBLE);
            binding.tvPluginStatusFirst.setText(TextUtils.isEmpty(firstContent) ? "" : firstContent);
            binding.tvPluginStatusFirst.setCompoundDrawablesWithIntrinsicBounds(
                    DinSaferApplication.getAppContext().getResources().getDrawable(firstIconId),
                    null, null, null);
        }

        // 显示第二个状态图标
        if (0 != secondIconId) {
            binding.tvPluginStatusSecond.setVisibility(View.VISIBLE);
            binding.tvPluginStatusSecond.setText(TextUtils.isEmpty(secondContent) ? "" : secondContent);
            binding.tvPluginStatusSecond.setCompoundDrawablesWithIntrinsicBounds(
                    DinSaferApplication.getAppContext().getResources().getDrawable(secondIconId),
                    null, null, null);
        }

        // 显示第三个状态图标
        if (0 != thirdIconId) {
            binding.tvPluginStatusThird.setVisibility(View.VISIBLE);
            binding.tvPluginStatusThird.setText(TextUtils.isEmpty(thirdContent) ? "" : thirdContent);
            binding.tvPluginStatusThird.setCompoundDrawablesWithIntrinsicBounds(
                    DinSaferApplication.getAppContext().getResources().getDrawable(thirdIconId),
                    null, null, null);
        }
    }

    private void showAndStartLoadingAnim() {
        binding.ivStateLoading.setVisibility(View.VISIBLE);
        binding.ivStateLoading.startAnimation(mOperatingAnim);
    }

    private void stopAndHindLoading() {
        binding.ivStateLoading.clearAnimation();
        binding.ivStateLoading.setVisibility(View.GONE);
    }

    public void changeBottomLineStatus(@BottomLineStatus int bottomLineStatus) {
        changeBottomLineStatus(bottomLineStatus, 0);
    }

    public void changeBottomLineStatusPX(int leftPaddingDP) {
        changeBottomLineStatus(BOTTOM_LINE_SHORT, leftPaddingDP);
    }

    public void changeItemEnable(boolean enable) {
        binding.clContent.setAlpha(enable ? MainPanelHelper.VIEW_ENABLE_ALPHA : MainPanelHelper.VIEW_DISABLE_ALPHA);
    }

    /**
     * 修改底部分割线的显示状态
     *
     * @param bottomLineStatus 显示状态
     * @param leftPaddingDP    底部分割线的左边padding值
     */
    private void changeBottomLineStatus(@BottomLineStatus int bottomLineStatus, int leftPaddingDP) {
        switch (bottomLineStatus) {
            case BOTTOM_LINE_SHORT:
                binding.glStartBottomLine.setGuidelineBegin(leftPaddingDP);
                binding.vLineBottom.setVisibility(View.VISIBLE);
                break;
            case BOTTOM_LINE_LONG:
                binding.vLineBottom.setVisibility(View.VISIBLE);
                break;
            default:
                binding.vLineBottom.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 设置信号级别图标
     * 0-3 为ImageView设置对应的信号图标，其他值隐藏信号图标
     *
     * @return true: 有信号图标
     */
    private boolean setSignalLevel(int level) {
        int signalIconId;
        switch (level) {
            case 0:
                signalIconId = R.drawable.icon_plugin_list_signal_0;
                break;
            case 1:
                signalIconId = R.drawable.icon_plugin_list_signal_1;
                break;
            case 2:
                signalIconId = R.drawable.icon_plugin_list_signal_2;
                break;
            case 3:
                signalIconId = R.drawable.icon_plugin_list_signal_3;
                break;
            default:
                signalIconId = 0;
                break;
        }

        if (0 == signalIconId) {
            binding.ivPluginNameSignal.setVisibility(View.GONE);
            binding.ivPluginNameCenterSignal.setVisibility(View.GONE);
            return false;
        } else {
            binding.ivPluginNameSignal.setImageResource(signalIconId);
            binding.ivPluginNameCenterSignal.setImageResource(signalIconId);
            return true;
        }
    }
}
