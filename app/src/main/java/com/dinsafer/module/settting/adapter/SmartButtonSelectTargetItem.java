package com.dinsafer.module.settting.adapter;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.SmartButtonTargetData;

import java.util.List;

/**
 * SmartButton选择控制对象列表的数据适配器
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/10/27 6:28 PM
 */
public class SmartButtonSelectTargetItem extends BaseQuickAdapter<SmartButtonTargetData, BaseViewHolder> {

    public SmartButtonSelectTargetItem(int layoutResId, @Nullable List<SmartButtonTargetData> data) {
        super(layoutResId, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, SmartButtonTargetData item) {
        helper.setText(R.id.tv_target_name, item.getTargetName());

        // 分割线显示状态，最后一个不显示
        helper.setGone(R.id.v_divider, getData().get(getData().size() - 1) != item);

    }
}
