package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.add.ui.bean.WifiInfo;
import com.dinsafer.util.WifiUtil;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class WifiListAdapter extends BaseAdapter {

    private Activity mActivity;

    private ArrayList<WifiInfo> mData;

    private boolean showOtherItem;

    public WifiListAdapter(Activity mActivity, ArrayList<WifiInfo> mData, boolean showOtherItem) {
        this.mActivity = mActivity;
        this.mData = mData;
        this.showOtherItem = showOtherItem;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.wifi_select_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        holder.apStepThreeWifiItemText.setText(mData.get(position).getSsid());
        holder.apStepThreeWifiItemLock.setImageResource(R.drawable.icon_lock);
        holder.apStepThreeWifiItemSignal.setImageResource(WifiUtil.getBleWifiRssiIconResId(mData.get(position).getRssi()));
        holder.apStepThreeWifiItemRight.setImageResource(R.drawable.btn_device_setting_arrow);
//        最后一个去掉横线还有icon
        if (showOtherItem && (position == mData.size() - 1)) {
            holder.apStepThreeWifiItemText.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            holder.apStepThreeWifiItemRight.setVisibility(View.VISIBLE);
            holder.apStepThreeWifiLine.setVisibility(View.GONE);
            holder.apStepThreeWifiItemSignal.setVisibility(View.GONE);
            holder.apStepThreeWifiItemLock.setVisibility(View.GONE);
        } else {
            holder.apStepThreeWifiItemText.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null);
            holder.apStepThreeWifiLine.setVisibility(View.VISIBLE);
            holder.apStepThreeWifiItemRight.setVisibility(View.GONE);
            holder.apStepThreeWifiItemSignal.setVisibility(View.VISIBLE);
            holder.apStepThreeWifiItemLock.setVisibility(mData.get(position).isAuth() ? View.VISIBLE : View.GONE);
        }


        return convertView;
    }

    public void remove(int index) {
        if (mData != null && index >= 0 && index < mData.size()) {
            mData.remove(index);
            notifyDataSetChanged();
        }
    }


    static class ViewHolder {
        TextView apStepThreeWifiItemText;
        View apStepThreeWifiLine;
        ImageView apStepThreeWifiItemRight;
        ImageView apStepThreeWifiItemLock;
        ImageView apStepThreeWifiItemSignal;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            apStepThreeWifiItemText = view.findViewById(R.id.ap_step_three_wifi_item_text);
            apStepThreeWifiLine = view.findViewById(R.id.ap_step_three_wifi_line);
            apStepThreeWifiItemRight = view.findViewById(R.id.ap_step_three_wifi_item_right);
            apStepThreeWifiItemLock = view.findViewById(R.id.ap_step_three_wifi_item_lock);
            apStepThreeWifiItemSignal = view.findViewById(R.id.ap_step_three_wifi_item_signal);
        }
    }
}
