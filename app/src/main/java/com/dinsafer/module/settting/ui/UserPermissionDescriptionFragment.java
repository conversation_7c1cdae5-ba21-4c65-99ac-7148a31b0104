package com.dinsafer.module.settting.ui;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentUserPermissionDescriptionBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.util.DensityUtils;

import java.util.ArrayList;

/**
 * 用户权限区别说明页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/13 2:19 下午
 */
public class UserPermissionDescriptionFragment extends BaseFragment {

    private FragmentUserPermissionDescriptionBinding mBinding;
    private boolean mIsChanged = false;
    private int mCurrentPagePosition = FIRST_ITEM_INDEX;
    private static final int POINT_LENGTH = 2;
    private static final int FIRST_ITEM_INDEX = 1;

    private CommonPagerAdapter mAdapter;
    private ArrayList<BaseFragment> mFragments;

    public static UserPermissionDescriptionFragment newInstance() {
        return new UserPermissionDescriptionFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_user_permission_description, container, false);
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonTitleBar.commonBarTitle.setLocalText(getResources().getString(R.string.what_is_the_difference));

        // 左右多添加一个Fragment实现模拟无限循环滚动
        // 需要同步滚动状态
        mFragments = new ArrayList<>();
        mFragments.add(UserPermissionDetailFragment.newInstance(LocalKey.GUEST));
        mFragments.add(UserPermissionDetailFragment.newInstance(LocalKey.ADMIN));
        mFragments.add(UserPermissionDetailFragment.newInstance(LocalKey.GUEST));
//        mFragments.add(UserPermissionDetailFragment.newInstance(LocalKey.GUEST));
        mFragments.add(UserPermissionDetailFragment.newInstance(LocalKey.ADMIN));
        mAdapter = new CommonPagerAdapter(getChildFragmentManager(), mFragments);
        mBinding.vpPermission.setPageMargin(DensityUtils.dp2px(getContext(), 20));
        mBinding.vpPermission.setAdapter(mAdapter);
        mBinding.vpPermission.setOffscreenPageLimit(4);
        mBinding.vpPermission.setCurrentItem(FIRST_ITEM_INDEX, false);

        mBinding.vpPermission.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageSelected(int pPosition) {
                mIsChanged = true;
                if (pPosition > POINT_LENGTH) {// 末位之后，跳转到首位（1）
                    mCurrentPagePosition = FIRST_ITEM_INDEX;
                } else if (pPosition < FIRST_ITEM_INDEX) {// 首位之前，跳转到末尾（N）
                    mCurrentPagePosition = POINT_LENGTH;
                } else {
                    mCurrentPagePosition = pPosition;
                }
            }


            @Override
            public void onPageScrolled(int arg0, float arg1, int arg2) {
            }


            @Override
            public void onPageScrollStateChanged(int pState) {
                if (ViewPager.SCROLL_STATE_IDLE == pState) {
                    if (mIsChanged) {
                        mIsChanged = false;
                        mBinding.vpPermission.setCurrentItem(mCurrentPagePosition, false);
                    }
                }
            }
        });

    }
}
