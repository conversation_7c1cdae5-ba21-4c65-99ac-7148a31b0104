package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SosSettingFragmentBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.Map;

/**
 * Created by Rinfon on 16/7/7.
 */
public class SosSettingFragment extends MyBaseFragment<SosSettingFragmentBinding> implements IDeviceCallBack {

    private static final int SOS_MAX_LENGTH = 99999;

    private String mInitMessage;
    private String mMessage;
    private boolean isFistSet;
    private boolean isSetting;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static SosSettingFragment newInstance() {
        return new SosSettingFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.sos_setting_fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.sosSettingPassword.setOnClickListener(v -> toSetPassword());
        mBinding.sosSettingSosMessage.setOnClickListener(v -> toSetMessage());
        showTimeOutLoadinFramgmentWithErrorAlert();
        findPanel();
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    public void toClose() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    public void toFirstOpenSos() {
        isSetting = true;
        ChangePasswordDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Next))
                .setCancel(getResources().getString(R.string.Cancel))
                .setContent(getResources().getString(R.string.set_sos_password_title))
                .setAutoDismiss(false)
                .setOKListener(new ChangePasswordDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(ChangePasswordDialog passwordDialog, String password) {
                        if (!TextUtils.isEmpty(password) && password.length() == 4) {
                            passwordDialog.dismiss();
                            toFirstConfirmPassword(password);
                        }
                    }

                    @Override
                    public void onCancel(ChangePasswordDialog dialog) {
                        mBinding.sosSettingDescriptionSwitch.setOn(false);
                        isSetting = false;
                    }
                })
                .preBuilder()
                .show();
    }

    public void toSetMessage() {
        SosMessageSettingDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.sos_message_dialog_save))
                .setCancel(getResources().getString(R.string.Cancel))
                .setInitMessage(mInitMessage)
                .setAutoDismiss(false)
                .setMaxLength(SOS_MAX_LENGTH)
                .setOKListener(new SosMessageSettingDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(SosMessageSettingDialog dialog, String message) {
                        if (!TextUtils.isEmpty(message)) {
                            mMessage = message;
                            dialog.dismiss();
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            isSelfOperate = true;
                            mPanelDevice.submit(PanelParamsHelper.setDuressSms(message));
                        }
                    }

                    @Override
                    public void onCancel(SosMessageSettingDialog dialog) {
                    }
                })
                .preBuilder()
                .show();
    }

    public void toSetPassword() {
        ChangePasswordDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Next))
                .setCancel(getResources().getString(R.string.Cancel))
                .setContent(getResources().getString(R.string.set_sos_password_title))
                .setAutoDismiss(false)
                .setOKListener(new ChangePasswordDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(ChangePasswordDialog passwordDialog, String password) {
                        if (!TextUtils.isEmpty(password) && password.length() == 4) {
                            passwordDialog.dismiss();
                            toConfirmPassword(password);
                        }
                    }

                    @Override
                    public void onCancel(ChangePasswordDialog dialog) {

                    }
                })
                .preBuilder()
                .show();
    }

    private void toFirstConfirmPassword(final String firstPassword) {
        ChangePasswordDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setContent(getResources().getString(R.string.re_password_dialog_title))
                .setAutoDismiss(false)
                .setOKListener(new ChangePasswordDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(ChangePasswordDialog dialog, String password) {
                        if (!TextUtils.isEmpty(password) && password.length() == 4) {
                            dialog.dismiss();
                            if (checkPassword(firstPassword, password)) {
                                toFirstSetMessage(password);
                            } else {
                                showToast(Local.s(getResources().getString(R.string.password_not_match)));
                                mBinding.sosSettingDescriptionSwitch.setOn(false);
                                isSetting = false;
                            }
                        }
                    }

                    @Override
                    public void onCancel(ChangePasswordDialog dialog) {
                        mBinding.sosSettingDescriptionSwitch.setOn(false);
                        isSetting = false;
                    }
                })
                .preBuilder()
                .show();
    }

    public void toFirstSetMessage(final String password) {
        SosMessageSettingDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.sos_message_dialog_save))
                .setCancel(getResources().getString(R.string.Cancel))
                .setAutoDismiss(false)
                .setInitMessage(mInitMessage)
                .setMaxLength(SOS_MAX_LENGTH)
                .setOKListener(new SosMessageSettingDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(SosMessageSettingDialog dialog, String message) {
                        if (!TextUtils.isEmpty(message)) {
                            mMessage = message;
                            dialog.dismiss();
                            isSetting = false;
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            isSelfOperate = true;
                            mPanelDevice.submit(PanelParamsHelper.initDuressInfo(password, message));
                        }
                    }

                    @Override
                    public void onCancel(SosMessageSettingDialog dialog) {
                        mBinding.sosSettingDescriptionSwitch.setOn(false);
                        isSetting = false;
                    }
                })
                .preBuilder()
                .show();
    }

    private boolean isSuccess;

    private void toConfirmPassword(final String firstPassword) {
        ChangePasswordDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setContent(getResources().getString(R.string.re_password_dialog_title))
                .setAutoDismiss(false)
                .setOKListener(new ChangePasswordDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(ChangePasswordDialog dialog, String password) {
                        if (!TextUtils.isEmpty(password) && password.length() == 4) {
                            dialog.dismiss();
                            if (checkPassword(firstPassword, password)) {
                                showTimeOutLoadinFramgmentWithErrorAlert();
                                isSelfOperate = true;
                                mPanelDevice.submit(PanelParamsHelper.setDuressPassword(password));
                            } else {
                                showToast(Local.s(getResources().getString(R.string.password_not_match)));
                            }
                        }
                    }

                    @Override
                    public void onCancel(ChangePasswordDialog dialog) {

                    }
                })
                .preBuilder()
                .show();
    }

    private boolean checkPassword(String first, String second) {

        return first.equals(second);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();

        if (null == mPanelDevice) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.getDuressInfo());
    }

    public void toChangeSosEnable(boolean isOn) {
        showTimeOutLoadinFramgment();
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.enableDuress(isOn));
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.advanced_setting_sos_setting));
        mBinding.sosSettingDescription.setLocalText(getResources().getString(R.string.sos_setting_description));
        mBinding.sosSettingPasswordTitle.setLocalText(getResources().getString(R.string.sos_setting_password_title));
        mBinding.sosSettingPasswordDescription.setLocalText(getResources().getString(R.string.sos_setting_password_content));
        mBinding.sosSettingSosMessageTitle.setLocalText(getResources().getString(R.string.sos_setting_sos_message_title));
        mBinding.sosSettingSosMessageDescription.setLocalText(getResources().getString(R.string.sos_setting_sos_message_content));
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mPanelDevice != null) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (PanelCmd.GET_DURESS_INFO.equals(cmd)) {
            onGetSosMessageInfo(status, map);
        } else if (resultType == 1 && isSelfOperate) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelCmd.ENABLE_DURESS.equals(cmd)
                    || PanelCmd.SET_DURESS_PASSWORD.equals(cmd)) {
                DDLog.i(TAG, "ENABLE_DURESS, SET_DURESS_PASSWORD");
            } else if (PanelCmd.SET_DURESS_SMS.equals(cmd)) {
                mInitMessage = mMessage;
            } else if (PanelCmd.INIT_DURESS_INFO.equals(cmd)
                    && PanelDataKey.CmdResult.SUCCESS == status) {
                isFistSet = false;
                mInitMessage = mMessage;
                mBinding.sosSettingPassword.setVisibility(View.VISIBLE);
                mBinding.sosSettingSosMessage.setVisibility(View.VISIBLE);
            }
            isSelfOperate = false;
        }
    }

    /**
     * 获取之前设置的胁迫报警信息
     */
    private void onGetSosMessageInfo(int status, Map map) {
        DDLog.i(TAG, "onGetSosMessageInfo, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        Map<String, Object> resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
        mBinding.sosSettingDescriptionSwitch.setVisibility(View.VISIBLE);
        mBinding.sosSettingDescriptionSwitch.setOn(DeviceHelper.getBoolean(resultMap, PanelDataKey.IntimidateSos.ENABLE, false));
        isFistSet = !DeviceHelper.getBoolean(resultMap, PanelDataKey.IntimidateSos.HAD_SET_PASSWORD, false);
        mInitMessage = DeviceHelper.getString(resultMap, PanelDataKey.IntimidateSos.SMS, "");
        mBinding.sosSettingDescriptionSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                if (!isSetting) {
                    if (isFistSet) {
                        toFirstOpenSos();
                    } else {
                        toChangeSosEnable(isOn);
                    }
                }
            }
        });
        if (!isFistSet) {
            mBinding.sosSettingPassword.setVisibility(View.VISIBLE);
            mBinding.sosSettingSosMessage.setVisibility(View.VISIBLE);
        } else {
            mBinding.sosSettingPassword.setVisibility(View.GONE);
            mBinding.sosSettingSosMessage.setVisibility(View.GONE);
        }
    }
}
