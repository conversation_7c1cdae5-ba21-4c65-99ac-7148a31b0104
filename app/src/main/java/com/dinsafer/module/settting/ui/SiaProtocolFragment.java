package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.ReplacementTransformationMethod;
import android.view.View;
import android.view.WindowManager;

import com.dinsafer.config.AppConfig;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SiaProtocolLayoutBinding;
import com.dinsafer.model.CmsProtocolModel;
import com.dinsafer.model.DeviceCmsSaveEnableEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

/**
 * CMS设置SIA设置页面
 *
 * <AUTHOR>
 * @date 2019-11-28 11:59
 */
public class SiaProtocolFragment extends MyBaseFragment<SiaProtocolLayoutBinding> implements TextWatcher {

    public static final String NETWORK_TYPE_TCP = "tcp";
    public static final String NETWORK_TYPE_UDP = "udp";

    private CmsProtocolModel mProtocolModel;
    private CmsProtocolModel mUpdateProtocolModel;
    private DeviceCmsSaveEnableEvent mCanSaveEvent;
    private String mCurrentNetworktype;

    public static SiaProtocolFragment newInstance(CmsProtocolModel protocolModel) {
        Bundle args = new Bundle();
        args.putSerializable("data", protocolModel);
        SiaProtocolFragment fragment = new SiaProtocolFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.sia_protocol_layout;
    }

    @Override
    public void onResume() {
        super.onResume();
        postVerifyState();
    }

    /**
     * 初始化页面改变监听
     */
    private void initChangeListener() {
        // IP地址改变监听
        mBinding.primaryIpAddress.addTextChangedListener(this);
        mBinding.primaryIpAddress.addTextChangedListener(this);
        mBinding.secondaryIpAddress.addTextChangedListener(this);
        mBinding.secondaryPort.addTextChangedListener(this);
        mBinding.accountNumber.addTextChangedListener(this);
        mBinding.switchEncrypt.setOnSwitchStateChangeListener(isOn -> {
            postVerifyState();
        });
        mBinding.encryptionKey.addTextChangedListener(this);
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.accountNumber.setTransformationMethod(new UpperCaseTransform());
        initChangeListener();
        mBinding.tvNetworkType.setOnClickListener(v -> onChangeNetworkTypeClick());
        mBinding.ivNetworkType.setOnClickListener(v -> onChangeNetworkTypeClick());
    }

    @Override
    public void initData() {
        super.initData();

        mCanSaveEvent = new DeviceCmsSaveEnableEvent(false);
        mUpdateProtocolModel = new CmsProtocolModel();

        changeNetworkTypeVisible(AppConfig.Functions.SUPPORT_SELECT_CMS_NETWORK_TYPE);

        // 修改键盘弹出时布局的调整方式，在onDestroyView中还原设置。
        // 修改布局调整方式后，可解决页面标题和tab被顶出屏幕外的问题，
        // 但同时会导致其他页面进行刷新调整
        // getMainActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);

        mBinding.primaryIpAddressLabel.setLocalText(getResources().getString(R.string.advanced_setting_primary_ip_address));
        mBinding.primaryIpAddressTitle.setLocalText(getResources().getString(R.string.advanced_setting_ip_address));
        mBinding.primaryIpAddress.setHint(Local.s(getResources().getString(R.string.advanced_setting_cms_hint)));
        mBinding.primaryPortTitle.setLocalText(getResources().getString(R.string.advanced_setting_port));
        mBinding.primaryPort.setHint(Local.s(getResources().getString(R.string.advanced_setting_cms_hint)));

        mBinding.secondaryIpAddressLabel.setLocalText(getResources().getString(R.string.advanced_setting_secondary_ip_address));
        mBinding.secondaryIpAddressTitle.setLocalText(getResources().getString(R.string.advanced_setting_ip_address));
        mBinding.secondaryIpAddress.setHint(Local.s(getResources().getString(R.string.advanced_setting_cms_hint)));
        mBinding.secondaryPortTitle.setLocalText(getResources().getString(R.string.advanced_setting_port));
        mBinding.secondaryPort.setHint(Local.s(getResources().getString(R.string.advanced_setting_cms_hint)));

        mBinding.accountNumberTitle.setLocalText(getResources().getString(R.string.advanced_setting_account_number));
        mBinding.accountNumber.setHint(Local.s(getResources().getString(R.string.advanced_setting_cms_hint)));

        mBinding.encryptionLabel.setLocalText(getResources().getString(R.string.advanced_setting_encryption_label));
        mBinding.encryptionTitle.setLocalText(getResources().getString(R.string.advanced_setting_encryption));
        mBinding.encryptionKeyTitle.setLocalText(getResources().getString(R.string.advanced_setting_encryption_key));
        mBinding.encryptionKey.setHint(Local.s(getResources().getString(R.string.advanced_setting_cms_hint)));
        mBinding.tvNetworkType.setLocalText(getResources().getString(R.string.advanced_setting_cms_network_tcp));

        // 初始化数据
        mProtocolModel = (CmsProtocolModel) getArguments().getSerializable("data");
        mUpdateProtocolModel.setProtocolName(mProtocolModel.getProtocolName());
        mCurrentNetworktype = NETWORK_TYPE_TCP;
        if (null == mProtocolModel.getInfo()) {
            // 没有初始数据时，保存按钮不可点击
            mCanSaveEvent.setCanSave(false);
            EventBus.getDefault().post(mCanSaveEvent);
            return;
        }

        if (!TextUtils.isEmpty(mProtocolModel.getInfo().getPrimary_ip())) {
            mBinding.primaryIpAddress.setText(mProtocolModel.getInfo().getPrimary_ip());
        }
        if (0 != mProtocolModel.getInfo().getPrimary_port()) {
            mBinding.primaryPort.setText(String.valueOf(mProtocolModel.getInfo().getPrimary_port()));
        }
        if (!TextUtils.isEmpty(mProtocolModel.getInfo().getSecondary_ip())) {
            mBinding.secondaryIpAddress.setText(mProtocolModel.getInfo().getSecondary_ip());
        }
        if (0 != mProtocolModel.getInfo().getSecondary_port()) {
            mBinding.secondaryPort.setText(String.valueOf(mProtocolModel.getInfo().getSecondary_port()));
        }
        if (!TextUtils.isEmpty(mProtocolModel.getInfo().getAccount_number())) {
            mBinding.accountNumber.setText(String.valueOf(mProtocolModel.getInfo().getAccount_number()));
        }
        mBinding.switchEncrypt.setOn(mProtocolModel.getInfo().isEncryption());
        if (!TextUtils.isEmpty(mProtocolModel.getInfo().getEncryption_key())) {
            mBinding.encryptionKey.setText(mProtocolModel.getInfo().getEncryption_key());
        }
        if (!TextUtils.isEmpty(mProtocolModel.getInfo().getNetwork())) {
            if (!getString(R.string.advanced_setting_cms_network_udp)
                    .equalsIgnoreCase(mProtocolModel.getInfo().getNetwork())) {
                // TCP
                mBinding.tvNetworkType.setLocalText(getResources().getString(R.string.advanced_setting_cms_network_tcp));
                mCurrentNetworktype = NETWORK_TYPE_TCP;
            } else {
                mBinding.tvNetworkType.setLocalText(getResources().getString(R.string.advanced_setting_cms_network_udp));
                mCurrentNetworktype = NETWORK_TYPE_UDP;
            }
        }

        // 回显数据后，更新保存按钮可点击状态
        postVerifyState();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 还原键盘弹出时布局调整方式
        getMainActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);

        EventBus.getDefault().unregister(this);
    }

    public void onChangeNetworkTypeClick() {
        showChangeNetworkTypeDialog();
    }

    /**
     * 修改修改网络类型入口状态是否可见
     *
     * @param visible 修改网络设置入口是否可见，true:可见
     */
    private void changeNetworkTypeVisible(boolean visible) {
        if (visible) {
            mBinding.rlSelectNetworkType.setVisibility(View.VISIBLE);
            mBinding.vNetworkTypeTopLine.setVisibility(View.VISIBLE);
        } else {
            mBinding.rlSelectNetworkType.setVisibility(View.GONE);
            mBinding.vNetworkTypeTopLine.setVisibility(View.GONE);
        }
    }

    /**
     * 显示修改网络类型的对话框
     */
    private void showChangeNetworkTypeDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.advanced_setting_cms_network_tcp)),
                        Local.s(getResources().getString(R.string.advanced_setting_cms_network_udp)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            // 网络状态为tcp
                            mBinding.tvNetworkType.setLocalText(getResources().getString(R.string.advanced_setting_cms_network_tcp));
                            mCurrentNetworktype = NETWORK_TYPE_TCP;
                        } else if (index == 1) {
                            // 网络状态为udp
                            mBinding.tvNetworkType.setLocalText(getResources().getString(R.string.advanced_setting_cms_network_udp));
                            mCurrentNetworktype = NETWORK_TYPE_UDP;
                        }
                    }
                }).show();
    }

    /**
     * 校验输入的协议信息是否符合规则
     *
     * @return 输入的协议信息是否符合指定规则
     */
    private boolean verifyProtocolInfo() {
        mUpdateProtocolModel.setInfo(null);
        CmsProtocolModel.CmsProtocolInfo protocolInfo = new CmsProtocolModel.CmsProtocolInfo();
        String inputText;
        int inputNum;
        // 1、主IP地址
        inputText = mBinding.primaryIpAddress.getText().toString();
        if (TextUtils.isEmpty(inputText)) {
            return false;
        }
        if (!DDDateUtil.isIPv4(inputText)) {
            return false;
        }
        protocolInfo.setPrimary_ip(inputText);

        // 2、主IP端口
        inputText = mBinding.primaryPort.getText().toString();
        if (TextUtils.isEmpty(inputText)) {
            return false;
        }
        try {
            inputNum = Integer.parseInt(inputText);
        } catch (Exception e) {
            return false;
        }
        if (!DDDateUtil.isIpPort(inputNum)) {
            return false;
        }
        protocolInfo.setPrimary_port(inputNum);

        // 3、副IP地址
        inputText = mBinding.secondaryIpAddress.getText().toString();
        if (!TextUtils.isEmpty(inputText) && !DDDateUtil.isIPv4(inputText)) {
            // 设置了副IP地址，校验副IP地址
            return false;
        }
        protocolInfo.setSecondary_ip(inputText);

        // 4、副IP端口
        inputText = mBinding.secondaryPort.getText().toString();
        // 是否未设置副端口号
        boolean noSetSecondaryPort = true;
        if (!TextUtils.isEmpty(inputText)) {
            try {
                inputNum = Integer.parseInt(inputText);
            } catch (Exception e) {
                return false;
            }
            // 设置了端口号，需要校验端口号
            if (!DDDateUtil.isIpPort(inputNum)) {
                return false;
            }
            noSetSecondaryPort = false;
            protocolInfo.setSecondary_port(inputNum);
        }

        // 设置副IP地址后，副IP端口不能为空
        if (!TextUtils.isEmpty(protocolInfo.getSecondary_ip())
                && noSetSecondaryPort) {
            return false;
        }
        // 设置副IP端口后，副IP地址不能为空
        if (TextUtils.isEmpty(protocolInfo.getSecondary_ip())
                && !noSetSecondaryPort) {
            return false;
        }

        // 5、AccountNumber
        inputText = mBinding.accountNumber.getText().toString();
        if (TextUtils.isEmpty(inputText)) {
            return false;
        }
        if (!DDDateUtil.isAccountNumber(inputText)) {
            return false;
        }
        protocolInfo.setAccount_number(inputText);

        // 6、是否加密
        protocolInfo.setEncryption(mBinding.switchEncrypt.isOn());
        // 7、加密KEY
        inputText = mBinding.encryptionKey.getText().toString();
        if (protocolInfo.isEncryption() && TextUtils.isEmpty(inputText)) {
            return false;
        }
        protocolInfo.setEncryption_key(inputText);

        mUpdateProtocolModel.setInfo(protocolInfo);
        return true;
    }

    /**
     * 获取用户输入的协议信息
     *
     * @return 用户输入或修改后的协议信息
     */
    public CmsProtocolModel getUpdateProtocolInfo() {
        if (!verifyProtocolInfo()) {
            return null;
        }
        mUpdateProtocolModel.getInfo().setNetwork(mCurrentNetworktype);

        return mUpdateProtocolModel;
    }

    /**
     * 校验输入的内容并发送可保存状态的事件
     */
    public void postVerifyState() {
        mCanSaveEvent.setCanSave(verifyProtocolInfo());
        EventBus.getDefault().post(mCanSaveEvent);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
    }

    @Override
    public void afterTextChanged(Editable s) {
        postVerifyState();
    }

    /**
     * EditText字母自动大写转换类
     */
    private static class UpperCaseTransform extends ReplacementTransformationMethod {
        @Override
        protected char[] getOriginal() {
            char[] aa = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
            return aa;
        }

        @Override
        protected char[] getReplacement() {
            char[] cc = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
            return cc;
        }
    }
}
