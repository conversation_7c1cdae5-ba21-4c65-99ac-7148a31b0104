package com.dinsafer.module.settting.camera;

import android.text.TextUtils;

import com.dinsafer.module_heartlai.play.player.DinsaferPlayer;

import java.util.HashMap;
import java.util.Map;

/**
 * 心赖播放器管理器类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/28 2:36 PM
 */
public class HeartLaiPlayerManager {

    private final Map<String, DinsaferPlayer> mHeartLaiPlayers = new HashMap<>();

    private HeartLaiPlayerManager() {
    }

    public static class Holder {
        private static final HeartLaiPlayerManager instance = new HeartLaiPlayerManager();
    }

    public static HeartLaiPlayerManager getInstance() {
        return Holder.instance;
    }


    public void addPlayer(String cameraId, DinsaferPlayer player) {
        if (TextUtils.isEmpty(cameraId) || null == player) {
            return;
        }

        DinsaferPlayer lastPlayer = getPlayer(cameraId);
        if(lastPlayer != null){
            lastPlayer.releasePlay();
        }

        mHeartLaiPlayers.put(cameraId, player);
    }

    public void removePlayer(String cameraId) {
        if (TextUtils.isEmpty(cameraId)) {
            return;
        }
        mHeartLaiPlayers.remove(cameraId);
    }

    public DinsaferPlayer getPlayer(String cameraId) {
        if (TextUtils.isEmpty(cameraId)) {
            return null;
        }
        return mHeartLaiPlayers.get(cameraId);
    }

}
