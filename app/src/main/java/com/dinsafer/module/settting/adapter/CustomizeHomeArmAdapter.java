package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.SectionedBaseAdapter;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 定制在家布防和延时布防配件列表Adapter
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/27 10:58 AM
 */
public class CustomizeHomeArmAdapter extends SectionedBaseAdapter {

    private Activity mActivity;

    private HashMap<Integer, ArrayList<Map<String, Object>>> mData;

    private boolean isShowSectionTitle = false;

//    private ArrayList<PlugsData> mData;

    public CustomizeHomeArmAdapter(Activity mActivity, HashMap<Integer, ArrayList<Map<String, Object>>> mData, boolean isShowSectionTitle) {
        this.mActivity = mActivity;
        this.mData = mData;
        this.isShowSectionTitle = isShowSectionTitle;
    }

    @Override
    public Object getItem(int section, int position) {
        return null;
    }

    @Override
    public long getItemId(int section, int position) {
        return 0;
    }

    @Override
    public int getSectionCount() {
        return mData.size();
    }

    @Override
    public int getCountForSection(int section) {
        return mData.get(section).size();
    }

    @Override
    public View getItemView(final int section, final int position, View convertView, ViewGroup parent) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.plugitem, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        final Map<String, Object> pluginMap = mData.get(section).get(position);
        String name = DeviceHelper.getString(pluginMap, PanelDataKey.NAME, "");
        String subCategory = DeviceHelper.getString(pluginMap, PanelDataKey.SUBCATEGORY, "");
        String id = DeviceHelper.getString(pluginMap, PanelDataKey.ID, "");
        boolean isOn = PanelConstant.PluginSwitchState.OPENED
                == DeviceHelper.getInt(pluginMap, PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);

        holder.plugitemName.setLocalText(DisplayUtil.getNameBySubCategoryID(subCategory));
        if (!TextUtils.isEmpty(name)) {
            holder.plugitemDescription.setText(name);
        } else {
            holder.plugitemDescription.setText(Local.s(DisplayUtil.getNameBySubCategoryID(subCategory)) + "_" + id);
        }
        holder.plugitemSwitch.setOn(isOn);
        holder.plugitemSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                pluginMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, isOn
                        ? PanelConstant.PluginSwitchState.OPENED
                        : PanelConstant.PluginSwitchState.CLOSED);
            }
        });
        holder.plugitemIcon.setImageResource(DisplayUtil.getIconBySubCategoryID(subCategory));
        return convertView;
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        sectionHeaderHolder mHolder = null;
        if (convertView == null) {
            mHolder = new sectionHeaderHolder();
            LayoutInflater inflator = (LayoutInflater) parent.getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflator.inflate(R.layout.homearm_list_section_header, null);
            mHolder.homearmName = (LocalTextView) convertView.findViewById(R.id.homarm_list_header_name);
            convertView.setTag(mHolder);
        } else {
            try {
                mHolder = (sectionHeaderHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (isShowSectionTitle) {
            if (!DeviceHelper.getBoolean(mData.get(section).get(0), PanelDataKey.IS_THIRD_PART_PLUGIN, true))
                mHolder.homearmName.setLocalText(mActivity.getResources().getString(R.string.offical_plugin));
            else {
                mHolder.homearmName.setLocalText(mActivity.getResources().getString(R.string.device_managent_other_plugin));
            }
            mHolder.homearmName.setVisibility(View.VISIBLE);
        } else {
            mHolder.homearmName.setVisibility(View.GONE);
        }
        return convertView;
    }

    static class ViewHolder {
        ImageView plugitemIcon;
        LocalTextView plugitemName;
        View plugitemLine;
        TextView plugitemDescription;
        IOSSwitch plugitemSwitch;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            plugitemIcon = view.findViewById(R.id.plugitem_icon);
            plugitemName = view.findViewById(R.id.plugitem_name);
            plugitemLine = view.findViewById(R.id.plugitem_line);
            plugitemDescription = view.findViewById(R.id.plugitem_description);
            plugitemSwitch = view.findViewById(R.id.plugs_switch);
        }
    }

    static class sectionHeaderHolder {
        LocalTextView homearmName;
    }
}
