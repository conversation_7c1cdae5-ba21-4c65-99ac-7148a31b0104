package com.dinsafer.module.settting.ui.model;

import androidx.databinding.ViewDataBinding;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.DensityUtils;

public abstract class BaseDeviceSettingPlugModel<V extends ViewDataBinding> extends BindModel<V> {

    protected BaseFragment baseFragment;

    private String name;
    private int iconRes;
    private int plugCount;
    private boolean haveLoading;
    private boolean isLoading;
    private boolean customize = false;

    public BaseDeviceSettingPlugModel(BaseFragment baseFragment, String name, int iconRes,
                                      int plugCount, boolean haveLoading, boolean isLoading) {
        super(baseFragment.getContext());
        this.baseFragment = baseFragment;
        this.name = name;
        this.iconRes = iconRes;
        this.plugCount = plugCount;
        this.haveLoading = haveLoading;
        this.isLoading = isLoading;
    }

    public BaseDeviceSettingPlugModel(BaseFragment baseFragment, String name, int iconRes,
                                      int plugCount, boolean haveLoading, boolean isLoading, boolean customize) {
        super(baseFragment.getContext());
        this.baseFragment = baseFragment;
        this.name = name;
        this.iconRes = iconRes;
        this.plugCount = plugCount;
        this.haveLoading = haveLoading;
        this.isLoading = isLoading;
        this.customize = true;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_device_setting_plug;
    }

    @Override
    public void convert(BaseViewHolder holder, V v) {
//        DDLog.d(TAG, this.getName());

        try {
            View view = v.getRoot();
            ((LocalTextView) view.findViewById(R.id.device_management_plug_name)).setLocalText(getName());

            if (getIconRes() != -1) {
                ((LocalTextView) view.findViewById(R.id.device_management_plug_name)).setCompoundDrawablesWithIntrinsicBounds(iconRes, 0, 0, 0);
                ((LocalTextView) view.findViewById(R.id.device_management_plug_name)).setCompoundDrawablePadding(DensityUtils.dp2px(baseFragment.getContext(), 15));
            } else {
                ((LocalTextView) view.findViewById(R.id.device_management_plug_name)).setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            }
            if (customize) {
                ((LocalTextView) view.findViewById(R.id.device_management_plug_name))
                        .setTextColor(baseFragment.getResources().getColor(R.color.colorReset));
                ((ImageView) view.findViewById(R.id.device_management_smart_nor))
                        .setImageResource(R.drawable.btn_define_setting_cell_pink);
            }

            TextView tvPlugCount = view.findViewById(R.id.device_management_plug_number);
            if (haveLoading && isLoading) {
                view.findViewById(R.id.pb_state_loading).setVisibility(View.VISIBLE);
                tvPlugCount.setVisibility(View.GONE);
            } else {
                view.findViewById(R.id.pb_state_loading).setVisibility(View.GONE);
                if (getPlugCount() > 0) {
                    tvPlugCount.setVisibility(View.VISIBLE);
                    tvPlugCount.setText(String.valueOf(getPlugCount()));
                } else {
                    tvPlugCount.setVisibility(View.GONE);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public String getName() {
        return name;
    }

    public int getIconRes() {
        return iconRes;
    }

    public int getPlugCount() {
        return plugCount;
    }


    public void setName(String name) {
        this.name = name;
    }

    public void setIconRes(int iconRes) {
        this.iconRes = iconRes;
    }

    public void setPlugCount(int plugCount) {
        this.plugCount = plugCount;
    }

    public boolean isHaveLoading() {
        return haveLoading;
    }

    public void setHaveLoading(boolean haveLoading) {
        this.haveLoading = haveLoading;
    }

    public boolean isLoading() {
        return isLoading;
    }

    public void setLoading(boolean loading) {
        isLoading = loading;
    }
}
