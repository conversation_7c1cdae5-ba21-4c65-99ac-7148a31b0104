package com.dinsafer.module.settting.adapter.ipc;

import android.Manifest;
import android.content.pm.PackageManager;

import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;

import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.DDSystemUtil;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/7/7
 */
class BaseIPCItemDelegate implements IPCItemDelegate {

    protected MainActivity mMainActivity;
    protected MainPanelIpcItemViewHolder holder;

    public BaseIPCItemDelegate(MainActivity mMainActivity) {
        this.mMainActivity = mMainActivity;
    }

    @Override
    public void onBindItemViewHolder(MainPanelIpcItemViewHolder holder, int position, Device device) {
        this.holder = holder;
        if (!holder.isEditMode()) {
            if (LocalKey.ADMIN == HomeManager.getInstance().getCurrentHome().getLevel()) {
                holder.mIvMore.setVisibility(View.VISIBLE);
            } else {
                holder.mIvMore.setVisibility(View.GONE);
            }
        }

        if (!holder.isEditMode()) {
            holder.getCameraVideoView().setOnClickListener(v -> onErrorIconClick(device, position, holder.getCameraVideoView(), holder.itemView));
        }
    }

    @Override
    public void onPlayIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {
    }

    @Override
    public void onErrorIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent) {

    }

    @Override
    public void onFullscreenIconClick(Device device, int position, CameraVideoView videoView, View parent) {

    }

    protected boolean hasAudioPermission() {
        if (DDSystemUtil.isMarshmallow() && ContextCompat.checkSelfPermission(mMainActivity,
                Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
            Fragment fragment = ActivityController.getInstance().currentFragment();
            if (fragment != null && fragment instanceof BaseFragment) {
                ((BaseFragment) fragment).requestAudioPermisiions();
            }
            return false;
        }
        return true;
    }
}
