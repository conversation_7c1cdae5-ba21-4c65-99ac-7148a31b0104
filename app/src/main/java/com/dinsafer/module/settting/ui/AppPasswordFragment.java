package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DBUtil;
import com.jungly.gridpasswordview.GridPasswordView;


/**
 * Created by Rinfon on 16/7/8.
 */
public class AppPasswordFragment extends BaseFragment implements GridPasswordView.OnPasswordChangedListener {


    LocalTextView commonBarTitle;
    GridPasswordView appPasswordInput;
    View appPasswordOne;
    View appPasswordTwo;
    View appPasswordThree;
    View appPasswordFour;
    View appPasswordFive;
    View appPasswordSix;
    LocalTextView appPasswordTitle;

    private String firstPassword;

    private String confirmPassword;

    public static AppPasswordFragment newInstance() {
        return new AppPasswordFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.app_password_setting_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        appPasswordInput = rootView.findViewById(R.id.app_password_input);
        appPasswordOne = rootView.findViewById(R.id.app_password_one);
        appPasswordTwo = rootView.findViewById(R.id.app_password_two);
        appPasswordThree = rootView.findViewById(R.id.app_password_three);
        appPasswordFour = rootView.findViewById(R.id.app_password_four);
        appPasswordFive = rootView.findViewById(R.id.app_password_five);
        appPasswordSix = rootView.findViewById(R.id.app_password_six);
        appPasswordTitle = rootView.findViewById(R.id.app_password_title);
    }

    @Override
    public void initData() {
        commonBarTitle.setLocalText(getResources().getString(R.string.app_password_setting));
        appPasswordTitle.setLocalText(getResources().getString(R.string.app_password_setting_one));
        appPasswordInput.setOnPasswordChangedListener(this);
        appPasswordInput.requestFocus();
        toOpenInput();
    }


    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }

    @Override
    public void onDestroyView() {
        appPasswordInput.clearFocus();
        super.onDestroyView();
    }

    /**
     * Invoked when the password changed.
     *
     * @param psw new text
     */
    @Override
    public void onTextChanged(String psw) {
        if (psw.length() > 0) {
            appPasswordOne.setVisibility(View.INVISIBLE);
        } else {
            appPasswordOne.setVisibility(View.VISIBLE);
        }

        if (psw.length() > 1) {
            appPasswordTwo.setVisibility(View.INVISIBLE);
        } else {
            appPasswordTwo.setVisibility(View.VISIBLE);
        }

        if (psw.length() > 2) {
            appPasswordThree.setVisibility(View.INVISIBLE);
        } else {
            appPasswordThree.setVisibility(View.VISIBLE);
        }

        if (psw.length() > 3) {
            appPasswordFour.setVisibility(View.INVISIBLE);
        } else {
            appPasswordFour.setVisibility(View.VISIBLE);
        }

        if (psw.length() > 4) {
            appPasswordFive.setVisibility(View.INVISIBLE);
        } else {
            appPasswordFive.setVisibility(View.VISIBLE);
        }

        if (psw.length() > 5) {
            appPasswordSix.setVisibility(View.INVISIBLE);
        } else {
            appPasswordSix.setVisibility(View.VISIBLE);
        }

    }

    /**
     * Invoked when the password is at the maximum length.
     *
     * @param psw complete text
     */
    @Override
    public void onInputFinish(String psw) {
        appPasswordOne.setVisibility(View.VISIBLE);
        appPasswordTwo.setVisibility(View.VISIBLE);
        appPasswordThree.setVisibility(View.VISIBLE);
        appPasswordFour.setVisibility(View.VISIBLE);
        appPasswordFive.setVisibility(View.VISIBLE);
        appPasswordSix.setVisibility(View.VISIBLE);
        if (TextUtils.isEmpty(firstPassword)) {
            firstPassword = psw;
            appPasswordTitle.setLocalText(getResources().getString(R.string.app_password_setting_confirm));
            appPasswordInput.clearPassword();
        } else {
            confirmPassword = psw;
            if (firstPassword.equals(confirmPassword)) {
                DBUtil.SPut(DBKey.APP_PASSWORD, firstPassword);
                removeSelf();
            } else {
                appPasswordTitle.setLocalText(getResources().getString(R.string.app_password_setting_one));
                appPasswordInput.clearPassword();
                firstPassword = "";
                confirmPassword = "";
                showToast("Passcodes did not match. Try again.");
            }
        }
    }
}

