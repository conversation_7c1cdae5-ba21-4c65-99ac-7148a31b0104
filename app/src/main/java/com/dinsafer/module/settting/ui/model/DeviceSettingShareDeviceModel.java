package com.dinsafer.module.settting.ui.model;

import android.view.View;

import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.hue.HueLightListFragment;
import com.dinsafer.module.hue.add.HueScanFragment;
import com.dinsafer.module.settting.ui.ShareQR;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;

public class DeviceSettingShareDeviceModel extends BaseDeviceSettingPlugModel {

    public DeviceSettingShareDeviceModel(BaseFragment baseFragment) {
        super(baseFragment, baseFragment.getResources().getString(R.string.device_managent_share_device),
                -1, -1, false, false);
    }

    @Override
    public void onDo(View v) {
        toShareDevice();
    }

    public void toShareDevice() {

        if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getPermission() == LocalKey.ADMIN) {
            ActionSheet.createBuilder(baseFragment.getDelegateActivity().getApplicationContext(), baseFragment.getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(baseFragment.getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(baseFragment.getResources().getString(R.string.change_permission_admin)),
                            Local.s(baseFragment.getResources().getString(R.string.change_permission_user)),
                            Local.s(baseFragment.getResources().getString(R.string.change_permission_guest)))
                    .setCancelableOnTouchOutside(true)
                    .setListener(new ActionSheet.ActionSheetListener() {
                        @Override
                        public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                        }

                        @Override
                        public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                            int type = LocalKey.GUEST;
                            if (index == 0) {
                                type = LocalKey.ADMIN;
                            } else if (index == 1) {
                                type = LocalKey.USER;
                            } else {
                                type = LocalKey.GUEST;
                            }
//                            TODO 分享二维码？
//                            baseFragment.getDelegateActivity().addCommonFragment(ShareQR.newInstance(type));

                        }
                    }).show();
        } else if (DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getPermission() == LocalKey.USER) {
            ActionSheet.createBuilder(baseFragment.getDelegateActivity().getApplicationContext(), baseFragment.getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(baseFragment.getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(baseFragment.getResources().getString(R.string.change_permission_user)),
                            Local.s(baseFragment.getResources().getString(R.string.change_permission_guest)))
                    .setCancelableOnTouchOutside(true)
                    .setListener(new ActionSheet.ActionSheetListener() {
                        @Override
                        public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                        }

                        @Override
                        public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                            int type = LocalKey.GUEST;
                            if (index == 0) {
                                type = LocalKey.USER;
                            } else if (index == 1) {
                                type = LocalKey.GUEST;
                            }
//                            TODO 分享二维码？
//                            baseFragment.getDelegateActivity().addCommonFragment(ShareQR.newInstance(type));

                        }
                    }).show();
        }
    }
}
