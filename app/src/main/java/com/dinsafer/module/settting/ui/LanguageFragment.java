package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListView;

import com.dinsafer.dinnet.R;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.ChoosePhoneZoneAdapter;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import androidx.annotation.Nullable;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by Rinfon on 16/7/8.
 */
public class LanguageFragment extends BaseFragment {

    LocalTextView commonBarTitle;

    ListView languageListview;

    ChoosePhoneZoneAdapter choosePhoneZoneAdapter;

    private ArrayList<String> keys;
    private String[] vals;
    private String currentValue;

    private Observable<String> readObservable = Observable.create(new Observable.OnSubscribe<String>() {
        @Override
        public void call(Subscriber<? super String> subscriber) {
            subscriber.onNext(createData());
            subscriber.onCompleted();
        }
    }).subscribeOn(Schedulers.newThread()).observeOn(AndroidSchedulers.mainThread());

    public static LanguageFragment newInstance() {
        return new LanguageFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.setting_language_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        EventBus.getDefault().register(this);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        languageListview = rootView.findViewById(R.id.language_listview);
    }

    @Override
    public void initData() {
        readObservable.compose(this.<String>bindToLifecycle());
        commonBarTitle.setLocalText(getResources().getString(R.string.app_setting_language));
        readObservable.subscribe(new Subscriber<String>() {
            @Override
            public void onCompleted() {
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(String message) {
                updataUI();
            }
        });

    }

    public String createData() {
        Map<String, Object> languageList = LocalManager.getInstance().getCacheLanguageList();
        Map<String, Object> dataMap = new HashMap<>();
        if (languageList != null) {
            dataMap = (Map<String, Object>) languageList.get("Data");
        }
        Iterator iter = dataMap.entrySet().iterator();

        ArrayList<String> keys = new ArrayList<String>();
        final String[] vals = new String[dataMap.size()];
        int idx = 0;
        String currentKey = LocalManager.getInstance().getCurrentLanguage();
        String currentValue = "";
        while (iter.hasNext()) {
            Map.Entry entry = (Map.Entry) iter.next();

            String key = (String) entry.getKey();
            Map<String, Object> val = (Map) entry.getValue();
            if (key.toLowerCase().equals("default")) {
                key = Local.s("Auto detect");
            }
            keys.add(key);
            String l = (String) val.get("Language");
            vals[idx] = l;

            if (vals[idx].equals(currentKey)) {
                if (currentKey.toLowerCase().equals("default")) {
                    currentValue = Local.s("Auto detect");
                } else {
                    currentValue = (String) val.get("LanguageValue");
                }
            }

            idx++;
        }

        this.keys = keys;
        this.vals = vals;
        this.currentValue = currentValue;

        return "";

    }

    public void updataUI() {
        choosePhoneZoneAdapter = new ChoosePhoneZoneAdapter(getDelegateActivity(), keys);

        choosePhoneZoneAdapter.setIndex(Local.s(currentValue));
        languageListview.setAdapter(choosePhoneZoneAdapter);
//        languageListview.setAdapter(new ArrayAdapter<String>(getDelegateActivity(), R.layout.language_list, keys));
        languageListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                String key = vals[i];
                showTimeOutLoadinFramgmentWithErrorAlert();
                // 需要判断是否选择default
                if (key.equals("default")) {
                    LocalManager.getInstance().autoDetectLanguage();
                } else {
                    LocalManager.getInstance().changeLanguage(key);
                }
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        DDLog.i("language", "finish");
        closeTimeOutLoadinFramgmentWithErrorAlert();
        close();
    }

    public void close() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

}

