package com.dinsafer.module.settting.ui;

import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ChangeMessageLayoutBinding;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.ui.event.NotificationLanguageChangeEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;


/**
 * Created by Rinfon on 16/7/8.
 */
public class ChangeMessageFragment extends MyBaseFragment<ChangeMessageLayoutBinding> implements IDeviceCallBack {

    private final ArrayList<String> languageData = new ArrayList<>();
    private final ArrayList<String> languageValue = new ArrayList<>();

    private static String KEY = "key";
    private static String IS_CAN_BACK = "isCanBack";

    private boolean isCanBack;
    private String currentKey;
    private boolean isHasLoadData;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static ChangeMessageFragment newInstance(boolean isCanBack, String key) {
        ChangeMessageFragment fragment = new ChangeMessageFragment();
        Bundle bundle = new Bundle();
        bundle.putString(KEY, key);
        bundle.putBoolean(IS_CAN_BACK, isCanBack);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.change_message_layout;
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
    }

    @Override
    public void initData() {
        super.initData();
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_sos_message));
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.changeMessageChooessHint.setLocalText(getResources().getString(R.string.change_message_chooess_hint_text));
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.btnSave.setOnClickListener(v -> toSave());
        mBinding.llContent.setOnClickListener(v -> toChangeLanguage());
        String[] data = null;
        languageData.clear();
        EventBus.getDefault().register(this);
        Map<String, Object> cacheLanguageList = LocalManager.getInstance().getCacheLanguageList();
        if (cacheLanguageList != null && cacheLanguageList.size() > 0) {
            i("not null lazyload");
            lazyLoadData();
        } else {
            i("null watting");
//            等待读取语言文件
            LocalManager.getInstance().readLocalCache();
            showTimeOutLoadinFramgment();
        }
    }

    public void lazyLoadData() {
        isHasLoadData = true;
        Map<String, Object> languageList = LocalManager.getInstance().getCacheLanguageList();
        Map<String, Object> dataMap = new HashMap<>();
        if (languageList != null) {
            dataMap = (Map<String, Object>) languageList.get("Data");
        }
        isCanBack = getArguments().getBoolean(IS_CAN_BACK);
        mBinding.changeMessageHint.setVisibility(View.GONE);
        if (!isCanBack) {
            mBinding.commonBarBack.setVisibility(View.INVISIBLE);
            mBinding.changeMessageHint.setLocalText(getResources().getString(R.string.change_message_hint));
            mBinding.changeMessageHint.setVisibility(View.VISIBLE);
        }
        Iterator iter = dataMap.entrySet().iterator();
        currentKey = getArguments().getString(KEY);
        if (TextUtils.isEmpty(currentKey)) {
            currentKey = "default";
        }
        boolean hasKey = false;
        boolean hasSystemKey = false;
        int systemKeyIndex = 0;
        while (iter.hasNext()) {
            Map.Entry entry = (Map.Entry) iter.next();

            String key = (String) entry.getKey();
            Map<String, Object> val = (Map) entry.getValue();

            languageValue.add(key);
            String l = (String) val.get("Language");
            languageData.add(l);

            if (currentKey.equals(l)) {
                hasKey = true;
                mBinding.changeMessageType.setText(key);
            } else if (l.equals(LocalManager.getInstance().getCurrentLanguage())) {
                hasSystemKey = true;
                systemKeyIndex = languageData.size() - 1;
            }
        }
        if (!hasKey) {
            if (hasSystemKey) {
//                如果当前systemkey为default,则表示在我们的语言列表里面找不到
//                则把第一个当作为默认
                currentKey = languageData.get(systemKeyIndex);
                mBinding.changeMessageType.setText(languageValue.get(systemKeyIndex));
            } else {
                currentKey = languageData.get(0);
                mBinding.changeMessageType.setText(languageValue.get(0));

            }
        }
        closeTimeOutLoadinFramgmentWithErrorAlert();
    }

    public void toSave() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getHomeInstance().setHomeNotificationLanguage(
                HomeManager.getInstance().getCurrentHome().getHomeID(),
                currentKey, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        EventBus.getDefault().post(new NotificationLanguageChangeEvent());
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showSuccess();
                        removeSelf();
                    }

                    @Override
                    public void onError(int i, String s) {
                        DDLog.e(TAG, "Error, i: " + i + ", s: " + s);
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                    }
                });
        //  设置时区
//        String[] currentVersions = DeviceHelper.getString(mPanelDevice, PanelDataKey.Panel.FIRMWARE_VERSION, "")
//                .split("/");
//        if (currentVersions.length >= 1
//                && CommonDataUtil.getInstance().checkChangeMessage(currentVersions[0])) {
//            isSelfOperate = true;
//            mPanelDevice.submit(PanelParamsHelper.setMessageLanguage(currentKey));
//        } else {
//            Map<String, String> local = LocalHelper.getLangsMapWithArray(currentKey);
//            if (local == null) {
//                closeTimeOutLoadinFramgmentWithErrorAlert();
//                showErrorToast();
//                return;
//            }
//
//            JSONObject jsonObject = new JSONObject();
//            GetSaveKeyJsonList(local, jsonObject);
//            // 获取翻译后的值
//            isSelfOperate = true;
//            mPanelDevice.submit(PanelParamsHelper.setMessageTemplate(currentKey, jsonObject.toString()));
//        }
    }

    public void GetSaveKeyJsonList(Map<String, String> local, JSONObject jsonObject) {
        try {
            jsonObject.put(getResources().getString(R.string.device_text_alarm), getValue(local, getResources().getString(R.string.device_text_alarm)));

            jsonObject.put(getResources().getString(R.string.device_text_status), getValue(local, getResources().getString(R.string.device_text_status)));

            jsonObject.put(getResources().getString(R.string.device_text_password), getValue(local, getResources().getString(R.string.device_text_password)));

            jsonObject.put(getResources().getString(R.string.device_text_lowbettery), getValue(local, getResources().getString(R.string.device_text_lowbettery)));

            jsonObject.put(getResources().getString(R.string.device_text_lowpower), getValue(local, getResources().getString(R.string.device_text_lowpower)));

            jsonObject.put(getResources().getString(R.string.device_text_auth_change), getValue(local, getResources().getString(R.string.device_text_auth_change)));

            jsonObject.put(getResources().getString(R.string.device_text_plug), getValue(local, getResources().getString(R.string.device_text_plug)));

            jsonObject.put(getResources().getString(R.string.device_text_plug_user), getValue(local, getResources().getString(R.string.device_text_plug_user)));

            jsonObject.put(getResources().getString(R.string.toolbar_arm_text), getValue(local, getResources().getString(R.string.toolbar_arm_text)));

            jsonObject.put(getResources().getString(R.string.toolbar_disarm_text), getValue(local, getResources().getString(R.string.toolbar_disarm_text)));

            jsonObject.put(getResources().getString(R.string.toolbar_homearm_text), getValue(local, getResources().getString(R.string.toolbar_homearm_text)));

            jsonObject.put(getResources().getString(R.string.change_permission_admin), getValue(local, getResources().getString(R.string.change_permission_admin)));

            jsonObject.put(getResources().getString(R.string.change_permission_user), getValue(local, getResources().getString(R.string.change_permission_user)));

            jsonObject.put(getResources().getString(R.string.change_permission_guest), getValue(local, getResources().getString(R.string.change_permission_guest)));

            jsonObject.put(getResources().getString(R.string.had_removed_device), getValue(local, getResources().getString(R.string.had_removed_device)));

            jsonObject.put(getResources().getString(R.string.device_pluged_in), getValue(local, getResources().getString(R.string.device_pluged_in)));

            jsonObject.put(getResources().getString(R.string.device_power_disconnect), getValue(local, getResources().getString(R.string.device_power_disconnect)));

            String pluglowKey = getResources().getString(R.string.device_plug_power_low);
            jsonObject.put(pluglowKey, getValue(local, pluglowKey));

            jsonObject.put(getResources().getString(R.string.device_text_reset), getValue(local, getResources().getString(R.string.device_text_reset)));

            jsonObject.put(getResources().getString(R.string.device_text_alarm), getValue(local, getResources().getString(R.string.device_text_alarm)));

            jsonObject.put(getResources().getString(R.string.device_status), getValue(local, getResources().getString(R.string.device_status)));

            jsonObject.put(getResources().getString(R.string.device_offline), getValue(local, getResources().getString(R.string.device_offline)));

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private String getValue(Map<String, String> local, String key) {
        if (local.get(key) == null) {
            return key;
        }
        return local.get(key);
    }

    @Override
    public boolean onBackPressed() {
        if (!isCanBack)
            return true;
        return super.onBackPressed();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        if (LanguageUpdataEvent.EVENT_FINISHED.equals(ev.getEventType()) && !isHasLoadData) {
            i("finish lazyload");
            lazyLoadData();
        } else {

        }
    }


    public void toChangeLanguage() {
        String[] data = null;
        data = languageValue.toArray(new String[languageValue.size()]);

        android.app.AlertDialog.Builder listDialog =
                new android.app.AlertDialog.Builder(getDelegateActivity());
//        listDialog.setTitle("我是一个列表Dialog");
        listDialog.setItems(data, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                mBinding.changeMessageType.setText(languageValue.get(which));
                currentKey = languageData.get(which);
            }
        });
        listDialog.show();
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onCmdCallBack(String deviceId, String sub, String cmd, Map map) {

        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if ((PanelCmd.SET_MESSAGE_TEMPLATE.equals(cmd)
                || PanelCmd.SET_MESSAGE_LANGUAGE.equals(cmd)) && isSelfOperate) {
            onSetPanelPushMessage(status, map);
            isSelfOperate = false;
        }
    }

    /**
     * 设置推送语言结果
     */
    private void onSetPanelPushMessage(int status, Map map) {
        DDLog.i(TAG, "onSetPanelPushMessage, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();

        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        if (null != mPanelDevice.getInfo()) {
            mPanelDevice.getInfo().put(PanelDataKey.Panel.IS_MESSAGE_SET, true);
        }
        removeSelf();
    }
}

