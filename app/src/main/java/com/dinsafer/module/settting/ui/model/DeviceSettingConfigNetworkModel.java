package com.dinsafer.module.settting.ui.model;

import android.view.View;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemDeviceSettingPlugBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.ui.DeviceSettingFragment;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.DDLog;

public class DeviceSettingConfigNetworkModel extends BindModel<ItemDeviceSettingPlugBinding> {

    protected DeviceSettingFragment deviceSettingFragment;

    public DeviceSettingConfigNetworkModel(DeviceSettingFragment baseFragment) {
        super(baseFragment.getContext());
        this.deviceSettingFragment = baseFragment;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_device_setting_plug;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemDeviceSettingPlugBinding v) {
        try {
            View view = v.getRoot();
            ((LocalTextView) view.findViewById(R.id.device_management_plug_name))
                    .setLocalText(deviceSettingFragment.getString(R.string.advanced_setting_change_network));
            ((LocalTextView) view.findViewById(R.id.device_management_plug_name))
                    .setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
            TextView tvPlugCount = view.findViewById(R.id.device_management_plug_number);
            view.findViewById(R.id.pb_state_loading).setVisibility(View.GONE);
            tvPlugCount.setVisibility(View.GONE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDo(View view) {
        super.onDo(view);
        DDLog.i(TAG, "onDo, DeviceSettingConfigNetworkModel");
        deviceSettingFragment.toChangeNetwork();
    }

}
