package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.common.IPCManager;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentIpcListNewBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dscam.DsCamMultiFullPlayActivity;
import com.dinsafer.dscam.DsCamPlayerManager;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.event.DeviceLoadedStateChangedEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.ipc.heartlai.event.IPCInfoChangeEvent;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module.settting.adapter.ipc.IpcItemSection;
import com.dinsafer.module.settting.adapter.ipc.IpcSectionAdapter;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/26 11:44 AM
 */
public class IPCListNewFragment extends MyBaseFragment<FragmentIpcListNewBinding> implements IDeviceCallBack {

    private List<Device> heartLaiIpcList = new ArrayList<>();
    private List<Device> dscamList = new ArrayList<>();
    private IpcSectionAdapter mAdapter;
    private LinearLayoutManager mLayoutManager;
    private String SECTION_HEART_LAI;
    private String SECTION_DSCAM;
    private String TAG = IPCListNewFragment.class.getSimpleName();

    public static IPCListNewFragment newInstance() {
        return new IPCListNewFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ipc_list_new;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.smart_camera));
        mBinding.tvEmptyHint.setLocalText(getResources().getString(R.string.listview_empty));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        updateTittleRightIconVisible();
        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> {
            final ArrayList<String> dsCamIds = DsCamUtils.getMultiPlayDsCamIds();
            if (dsCamIds.size() == 0) {
                getMainActivity().showErrorToast();
                return;
            }
            DsCamMultiFullPlayActivity.start(getMainActivity(), dsCamIds);
        });
    }

    private void updateTittleRightIconVisible() {
        if (DsCamUtils.supportStaticMultiPlay()) {
            mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_home_screen_multiple);
            mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBar.commonBarRightIcon.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void initData() {
        super.initData();
        SECTION_HEART_LAI = Local.s("Heart Lai IPC");
        SECTION_DSCAM = Local.s("DS Camera");
        EventBus.getDefault().register(this);
        showBlueTimeOutLoadinFramgment();
        mLayoutManager = new LinearLayoutManager(getContext());
        mAdapter = new IpcSectionAdapter();
        mBinding.rvIpc.setLayoutManager(mLayoutManager);
        mBinding.rvIpc.setAdapter(mAdapter);
        mBinding.rvIpc.addOnChildAttachStateChangeListener(new RecyclerView.OnChildAttachStateChangeListener() {
            @Override
            public void onChildViewAttachedToWindow(@NonNull View view) {
                DDLog.i(TAG, "onChildViewAttachedToWindow");
            }

            @Override
            public void onChildViewDetachedFromWindow(@NonNull View view) {

            }
        });
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        showTimeOutLoadinFramgment();
        IPCManager.getInstance().fetchIPC(new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                // 统一在IPCListUpdateEvent中处理
                // createPlugsList();
                // mAdapter.notifyDataSetChanged();
            }

            @Override
            public void onError(int i, String s) {
                showErrorToast();
                // 统一在IPCListUpdateEvent中处理
                // createPlugsList();
            }
        });
    }

    private void createPlugsList() {
        dscamList.clear();
        final List<Device> tempDscam = IPCManager.getInstance().getNotDeletedDsCamList();
        if (null != tempDscam && tempDscam.size() > 0) {
            for (Device device : tempDscam) {
                if (device.getFlagDeleted()) {
                    continue;
                }
                dscamList.add(device);
            }
        }
        heartLaiIpcList.clear();
        final List<Device> tempHeartLai = IPCManager.getInstance().getNotDeletedHearLaiList();
        if (null != tempHeartLai && tempHeartLai.size() > 0) {
            for (Device device : tempHeartLai) {
                if (device.getFlagDeleted()) {
                    continue;
                }
                heartLaiIpcList.add(device);
            }
        }
        try {
            mAdapter.removeAllSections();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (heartLaiIpcList.size() <= 0 && dscamList.size() <= 0) {
            mBinding.tvEmptyHint.setVisibility(View.VISIBLE);

        } else {
            mBinding.tvEmptyHint.setVisibility(View.GONE);
            for (Device device : dscamList) {
                device.registerDeviceCallBack(IPCListNewFragment.this);
            }

            for (Device device : heartLaiIpcList) {
                device.registerDeviceCallBack(IPCListNewFragment.this);
            }
            boolean showHeader = dscamList.size() > 0 && heartLaiIpcList.size() > 0;
            if (dscamList.size() > 0)
                mAdapter.addSection(SECTION_DSCAM,
                        new IpcItemSection(getMainActivity(), SECTION_DSCAM, (ArrayList<Device>) dscamList, false, showHeader));
            if (heartLaiIpcList.size() > 0) {
                mAdapter.addSection(SECTION_HEART_LAI, new IpcItemSection(getMainActivity(), SECTION_HEART_LAI, (ArrayList<Device>) heartLaiIpcList, false, showHeader));
            }

        }
        closeLoadingFragment();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        for (Device device : dscamList) {
            device.unregisterDeviceCallBack(this);
        }
        for (Device device : heartLaiIpcList) {
            device.unregisterDeviceCallBack(this);
        }
        DsCamPlayerManager.getInstance().stopAllCam();
        IPCManager.getInstance().disconnectNotOnlineCamera();
    }

    @Override
    public void onCmdCallBack(String deviceID, String subCategory, String cmd, Map map) {

        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (Device device : dscamList) {
                    if (deviceID.equals(device.getId())) {
                        if (DinConst.CMD_SET_NAME.equals(cmd)) {
//            Dscam change name
                            mAdapter.notifyDataSetChanged();
                        } else if (DsCamCmd.CMD_CONNECT.equals(cmd)) {
                            mAdapter.notifyDataSetChanged();
                        } else if (DsCamCmd.GET_PARAMS.equals(cmd)) {
                            mAdapter.notifyDataSetChanged();
                        }
                        return;
                    }
                }
                for (Device device : heartLaiIpcList) {
                    if (deviceID.equals(device.getId())) {
                        if (HeartLaiCmd.CMD_STATUS_CHANGE.equals(cmd)) {
                            mAdapter.notifyDataSetChanged();
                        } else if (HeartLaiCmd.CMD_CHANGE_NAME.equals(cmd)) {
                            createPlugsList();
                            mAdapter.notifyDataSetChanged();
                        } else if (HeartLaiCmd.CMD_CHANGE_PASSWORD.equals(cmd)) {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            if (((int) map.get("status")) != 1) {
                                showErrorToast();
                            } else {
                                mAdapter.notifyDataSetChanged();
                            }
                        } else if (HeartLaiCmd.CMD_DEL.equals(cmd)) {
                            IPCManager.getInstance().fetchIPC(new IDefaultCallBack() {
                                @Override
                                public void onSuccess() {
                                    // 统一在IPCListUpdateEvent中处理
                                    // createPlugsList();
                                    // mAdapter.notifyDataSetChanged();
                                }

                                @Override
                                public void onError(int i, String s) {

                                }
                            });

                        }
                    }
                }
            }
        });

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCListUpdateEvent ev) {
        createPlugsList();
        mAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCInfoChangeEvent ev) {
        mAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceLoadedStateChangedEvent ev) {
        if (PluginConstants.TYPE_DSCAM.equals(ev.getDeviceType())
                || PluginConstants.TYPE_HEART_LAI.equals(ev.getDeviceType())) {
            mAdapter.notifyDataSetChanged();
        }
    }
}
