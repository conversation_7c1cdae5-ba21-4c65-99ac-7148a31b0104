package com.dinsafer.module.settting.ui;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.InputType;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ChangePremissonLayoutBinding;
import com.dinsafer.model.ContactItem;
import com.dinsafer.model.UserPermissonUpdata;
import com.dinsafer.model.event.KeypadMemberPwdUpdatedEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.interfaces.IUpdataList;
import com.dinsafer.module.settting.ui.event.RequestUpdateMemberEvent;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdResetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdUpdateResponse;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.BaseTextDrawable;
import com.dinsafer.ui.FlowLayout;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.nostra13.universalimageloader.core.ImageLoader;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by Rinfon on 16/7/8.
 */
public class ChangePermissionFragment extends MyBaseFragment<ChangePremissonLayoutBinding>
        implements ActionSheet.ActionSheetListener {
    private int mPermission;

    private ContactItem member, mSourceData;

    private IUpdataList callback;

    private int mTabSize;

    private int adminCount = 0;

    public static ChangePermissionFragment newInstance(ContactItem member, int adminCount) {
        ChangePermissionFragment fragment = new ChangePermissionFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("data", member);
        bundle.putInt("admincount", adminCount);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.change_premisson_layout;
    }

    public IUpdataList getCallback() {
        return callback;
    }

    public void setCallback(IUpdataList callback) {
        this.callback = callback;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarLeft.setOnClickListener(v -> showMoreActionDialog());
        mBinding.commonBarBack.setOnClickListener(v -> close());
        mBinding.changePermissionOption.setOnClickListener(v -> toChangePremission());
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.change_permission_title));
        this.adminCount = getArguments().getInt("admincount");
        mSourceData = (ContactItem) getArguments().getSerializable("data");
        mTabSize = getMainActivity().getResources().getDimensionPixelSize(R.dimen.textSmallSize);
        member = new ContactItem();
        resetData();
        mBinding.changePermissionName.setText(member.getName());
        mBinding.permissionNoSimHint.setLocalText(getResources().getString(R.string.permission_no_sim_hint));

        if (TextUtils.isEmpty(member.getPhoto())) {
            BaseTextDrawable drawable = DDImageUtil.getBaseTextDrawable(getDelegateActivity(), member.getUid()
                    , DinSDK.getUserInstance().getUser().getUid().equals(member.getUid()));

            int w = getResources().getDimensionPixelOffset(R.dimen.user_zone_avatar_size);
//     只能通过这种方式，把basttextdrawabel转换成bitmap，才能显示出来，不能直接调用setdrawable方法
            mBinding.changePermissionAvatar.setBaseTextDrawable(drawable, w, w);
        } else {
            ImageLoader.getInstance().displayImage(APIKey.UPLOAD_SERVER_IP + member.getPhoto(), mBinding.changePermissionAvatar);
        }
        if (HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.ADMIN
                && !DinSDK.getUserInstance().getUser().getUid().equals(member.getUid())) {
            mBinding.commonBarLeft.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBarLeft.setVisibility(View.GONE);
        }
        mBinding.changePermissionOption.setLocalText(getPermissionText(member.getPermission()));
//        changePermissionOption.setCompoundDrawablesWithIntrinsicBounds(getPermissionDrawabel(member.getPermission()),
//                null, getResources().getDrawable(R.drawable.icon_member_setting_edit_dropdown), null);

        mBinding.permissionPushText.setLocalText(getResources().getString(R.string.contact_push_noti));
        mBinding.permissionSmsText.setLocalText(getResources().getString(R.string.contact_push_sms));
        mBinding.permissionPhoneText.setLocalText(getResources().getString(R.string.contact_push_phone));
        mBinding.permissionPushSwitch.setOn(member.isPush());
        mBinding.permissionSmsSwitch.setOn(member.isSms());
        mBinding.permissionPhoneSwitch.setOn(member.isCall());
        setPushDetailMenuVisible(member.isPush());
        setSmsDetailMenuVisible(member.isSms());
        setPhoneDetailMenuVisible(member.isCall());
        setPushDetailMenuEnable(member.canUsePush());

        mBinding.permissionPushDetailText.setLocalText(getResources().getString(R.string.contact_detail_text));
        mBinding.permissionSmsDetailText.setLocalText(getResources().getString(R.string.contact_detail_text));
        mBinding.permissionPhoneDetailText.setLocalText(getResources().getString(R.string.contact_detail_text));

        mBinding.permissionPushSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                member.setPush(isOn);
                setPushDetailMenuVisible(member.isPush());
                toDoSave();
            }
        });

        mBinding.permissionPhoneSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                member.setCall(isOn);
                setPhoneDetailMenuVisible(member.isCall());
                toDoSave();
            }
        });

        mBinding.permissionSmsSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                member.setSms(isOn);
                setSmsDetailMenuVisible(member.isSms());
                toDoSave();
            }
        });

        updateTabTextUI();

        mBinding.permissionPhoneDetailText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getDelegateActivity().addCommonFragment(ChoosePushFragment.newInstance(member, ChoosePushFragment.PHONE));
            }
        });

        mBinding.permissionPushDetailText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getDelegateActivity().addCommonFragment(ChoosePushFragment.newInstance(member, ChoosePushFragment.PUSH));
            }
        });

        mBinding.permissionSmsDetailText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getDelegateActivity().addCommonFragment(ChoosePushFragment.newInstance(member, ChoosePushFragment.SMS));
            }
        });

        if ((TextUtils.isEmpty(member.getPhone()) && !member.isBindPhone()) || !member.canUsePush()) {
            mBinding.permissionNoSimHint.setVisibility(View.VISIBLE);
            mBinding.permissionSmsText.setAlpha(0.5f);
            mBinding.permissionSmsSwitch.setOn(false);
            mBinding.permissionSmsSwitch.setEnabled(false);
            mBinding.permissionSmsSwitch.setAlpha(0.5f);
            mBinding.permissionSmsMenu.setVisibility(View.GONE);


            mBinding.permissionPhoneText.setAlpha(0.5f);
            mBinding.permissionPhoneSwitch.setEnabled(false);
            mBinding.permissionPhoneSwitch.setOn(false);
            mBinding.permissionPhoneSwitch.setAlpha(0.5f);
            mBinding.permissionPhoneMenu.setVisibility(View.GONE);
        } else {
            mBinding.permissionNoSimHint.setVisibility(View.GONE);
            mBinding.permissionSmsText.setAlpha(1f);
            mBinding.permissionSmsSwitch.setEnabled(true);
            mBinding.permissionSmsSwitch.setAlpha(1f);

            mBinding.permissionPhoneText.setAlpha(1f);
            mBinding.permissionPhoneSwitch.setEnabled(true);
            mBinding.permissionPhoneSwitch.setAlpha(1f);

        }
        updateHint();
        initKeypadPasswordView();

        if (HomeManager.getInstance().getCurrentHome().getLevel() < member.getPermission()) {
            // 权限小于修改者,不能修改
        }
    }


    private void updateTabTextUI() {

        mBinding.permissionFlowlayout.removeAllViews();
        mBinding.permissionSmsFlowlayout.removeAllViews();
        mBinding.permissionPhoneFlowlayout.removeAllViews();
//        if (member.isPush_info() && member.isPush_sos() && member.isPush_sys() && mPermission != LocalKey.GUEST) {
        if (member.isPush_sos() && member.isPush_sys() && mPermission != LocalKey.GUEST) {
            mBinding.permissionFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_all), true));
//        } else if (!member.isPush_info() && !member.isPush_sos() && !member.isPush_sys()) {
        } else if (!member.isPush_sos() && !member.isPush_sys()) {
            mBinding.permissionFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_empty), false));
        } else {
//            if (member.isPush_info()) {
//                mBinding.permissionFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_info), false));
//            }

            if (member.isPush_sys() && mPermission != LocalKey.GUEST) {
                mBinding.permissionFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sys), false));
            }

            if (member.isPush_sos()) {
                mBinding.permissionFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sos), false));
            }
        }

//        if (member.isSms_info() && member.isSms_sos() && member.isSms_sys() && mPermission != LocalKey.GUEST) {
        if (member.isSms_sos() && member.isSms_sys() && mPermission != LocalKey.GUEST) {
            mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_all), true));
//        } else if (!member.isSms_info() && !member.isSms_sos() && !member.isSms_sys()) {
        } else if (!member.isSms_sos() && !member.isSms_sys()) {
            mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_empty), false));
        } else {
//            if (member.isSms_info()) {
//                mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_info), false));
//            }

            if (member.isSms_sys() && mPermission != LocalKey.GUEST) {
                mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sys), false));
            }

            if (member.isSms_sos()) {
                mBinding.permissionSmsFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sos), false));
            }
        }

//        if (member.isCall_info() && member.isCall_sos() && member.isCall_sys()) {
        if (member.isCall_sos() && member.isCall_sys()) {
            mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_all), true));
//        } else if (!member.isCall_info() && !member.isCall_sos() && !member.isCall_sys()) {
        } else if (!member.isCall_sos() && !member.isCall_sys()) {
            mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_empty), false));
        } else {
//            if (member.isCall_info()) {
//                mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_info), false));
//            }

            if (member.isCall_sys()) {
                mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sys), false));
            }

            if (member.isCall_sos()) {
                mBinding.permissionPhoneFlowlayout.addView(createTabView(getResources().getString(R.string.contact_push_call_sos), false));
            }
        }
    }

    private void setPushDetailMenuVisible(boolean isShow) {
        if (isShow) {
            mBinding.permissionPushMenu.setVisibility(View.VISIBLE);
        } else {
            mBinding.permissionPushMenu.setVisibility(View.GONE);
        }
    }

    private void setSmsDetailMenuVisible(boolean isShow) {
        if (isShow) {
            mBinding.permissionSmsMenu.setVisibility(View.VISIBLE);
        } else {
            mBinding.permissionSmsMenu.setVisibility(View.GONE);
        }
    }

    private void setPhoneDetailMenuVisible(boolean isShow) {
        if (isShow) {
            mBinding.permissionPhoneMenu.setVisibility(View.VISIBLE);
        } else {
            mBinding.permissionPhoneMenu.setVisibility(View.GONE);
        }
    }

    private Drawable getPermissionDrawabel(int i) {
        if (i == LocalKey.ADMIN) {
            return getResources().getDrawable(R.drawable.icon_member_profile_jurisdiction_admin);
        } else if (i == LocalKey.USER) {
            return getResources().getDrawable(R.drawable.icon_member_profile_jurisdiction_user);
        } else {
            return getResources().getDrawable(R.drawable.icon_member_profile_jurisdiction_guest);
        }
    }

    public void toChangePremission() {
        if (HomeManager.getInstance().getCurrentHome().getLevel() < member.getPermission()) {
//            权限小于修改者,不能修改
            return;
        }
        if (HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.ADMIN) {
            ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(getResources().getString(R.string.change_permission_admin)),
                            Local.s(getResources().getString(R.string.change_permission_guest)),
                            Local.s(getString(R.string.what_is_the_difference)))
                    .setLastButtonTextColor(getContext().getResources().getColor(R.color.permission_description_text_color))
                    .setCancelableOnTouchOutside(true)
                    .setListener(new ActionSheet.ActionSheetListener() {
                        @Override
                        public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                        }

                        @Override
                        public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                            if (2 == index) {
                                getMainActivity().addCommonFragment(UserPermissionDescriptionFragment.newInstance());
                                return;
                            }

                            if (index == 0) {
                                if (LocalKey.ADMIN == mPermission) {
                                    return;
                                }
                                mBinding.changePermissionOption.setLocalText(getPermissionText(LocalKey.ADMIN));
//                                changePermissionOption.setCompoundDrawablesWithIntrinsicBounds(getPermissionDrawabel(LocalKey.ADMIN),
//                                        null, getResources().getDrawable(R.drawable.icon_member_setting_edit_dropdown), null);
                                mPermission = LocalKey.ADMIN;
                                member.setSms_sys(mSourceData.isSms_sys());
                                member.setPush_sys(mSourceData.isPush_sys());
                            } else {
                                if (LocalKey.GUEST == mPermission) {
                                    return;
                                }
                                mBinding.changePermissionOption.setLocalText(getPermissionText(LocalKey.GUEST));
//                                changePermissionOption.setCompoundDrawablesWithIntrinsicBounds(getPermissionDrawabel(LocalKey.GUEST),
//                                        null, getResources().getDrawable(R.drawable.icon_member_setting_edit_dropdown), null);
                                mPermission = LocalKey.GUEST;
                                member.setSms_sys(false);
                                member.setPush_sys(false);
                            }
                            member.setPermission(mPermission);
                            mBinding.permissionPushSwitch.setOn(member.isPush());
                            mBinding.permissionSmsSwitch.setOn(member.isSms());

                            setPushDetailMenuVisible(member.isPush());
                            setPushDetailMenuEnable(member.canUsePush());

                            setSmsDetailMenuVisible(TextUtils.isEmpty(member.getPhone()) && !member.isBindPhone() ? false : member.isSms());
                            setSmsDetailMenuEnable(!TextUtils.isEmpty(member.getPhone()) || member.isBindPhone());
                            updateHint();

                            updateTabTextUI();

                            toDoSave();
                        }
                    }).show();
        } else {
//            user
            ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                    .setTitle(false)
                    .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                    .setOtherButtonTitles(Local.s(getResources().getString(R.string.change_permission_user)),
                            Local.s(getResources().getString(R.string.change_permission_guest)),
                            Local.s(getString(R.string.what_is_the_difference)))
                    .setLastButtonTextColor(getContext().getResources().getColor(R.color.permission_description_text_color))
                    .setCancelableOnTouchOutside(true)
                    .setListener(new ActionSheet.ActionSheetListener() {
                        @Override
                        public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                        }

                        @Override
                        public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                            if (2 == index) {
                                getMainActivity().addCommonFragment(UserPermissionDescriptionFragment.newInstance());
                                return;
                            }

                            if (index == 0) {
                                if (LocalKey.USER == mPermission) {
                                    return;
                                }
                                mBinding.changePermissionOption.setLocalText(getPermissionText(LocalKey.USER));
//                                changePermissionOption.setCompoundDrawablesWithIntrinsicBounds(getPermissionDrawabel(LocalKey.USER),
//                                        null, getResources().getDrawable(R.drawable.icon_member_setting_edit_dropdown), null);
                                mPermission = LocalKey.USER;
                                member.setSms_sys(mSourceData.isSms_sys());
                                member.setPush_sys(mSourceData.isPush_sys());
                            } else {
                                if (LocalKey.GUEST == mPermission) {
                                    return;
                                }
                                mBinding.changePermissionOption.setLocalText(getPermissionText(LocalKey.GUEST));
//                                changePermissionOption.setCompoundDrawablesWithIntrinsicBounds(getPermissionDrawabel(LocalKey.GUEST),
//                                        null, getResources().getDrawable(R.drawable.icon_member_setting_edit_dropdown), null);
                                mPermission = LocalKey.GUEST;
                                member.setSms_sys(false);
                                member.setPush_sys(false);
                            }
                            member.setPermission(mPermission);
                            mBinding.permissionPushSwitch.setOn(member.isPush());
                            mBinding.permissionSmsSwitch.setOn(member.isSms());

                            setPushDetailMenuVisible(member.isPush());
                            setPushDetailMenuEnable(member.canUsePush());

                            setSmsDetailMenuVisible(TextUtils.isEmpty(member.getPhone()) && !member.isBindPhone() ? false : member.isSms());
                            setSmsDetailMenuEnable(!TextUtils.isEmpty(member.getPhone()));
                            updateHint();

                            updateTabTextUI();

                            toDoSave();
                        }
                    }).show();
        }
    }

    private void setPushDetailMenuEnable(boolean push) {
        if (push) {
            mBinding.permissionPushSwitch.setEnabled(true);
            mBinding.permissionPushSwitch.setAlpha(1.0f);
            mBinding.permissionPushText.setAlpha(1.0f);
        } else {
            mBinding.permissionPushSwitch.setEnabled(false);
            mBinding.permissionPushSwitch.setAlpha(0.5f);
            mBinding.permissionPushText.setAlpha(0.5f);
        }
    }

    private void setSmsDetailMenuEnable(boolean push) {
        if (push) {
            mBinding.permissionSmsSwitch.setEnabled(true);
            mBinding.permissionSmsSwitch.setAlpha(1.0f);
            mBinding.permissionSmsText.setAlpha(1.0f);
        } else {
            mBinding.permissionSmsSwitch.setEnabled(false);
            mBinding.permissionSmsSwitch.setAlpha(0.5f);
            mBinding.permissionSmsText.setAlpha(0.5f);
        }
    }

    public void close() {
        if (null != member && !member.equals(mSourceData)) {
            showUnSaveChangeDialog();
        } else {
            removeSelf();
        }
    }

    private void toDoSave() {
        member.setPermission(mPermission);
        showTimeOutLoadinFramgment();

        HomeMember homeMember = new HomeMember();
        homeMember.setPush(member.isPush());
        homeMember.setSms(member.isSms());
        homeMember.setPush_sos(member.isPush_sos());
        homeMember.setPush_info(member.isPush_info());
        homeMember.setPush_sys(member.isPush_sys());
        homeMember.setSms_sos(member.isSms_sos());
        homeMember.setSms_info(member.isSms_info());
        homeMember.setSms_sys(member.isSms_sys());

        DinSDK.getHomeInstance().updateHomeMember(HomeManager.getInstance().getCurrentHome().getHomeID(),
                member.getUserId(), member.getPermission(), homeMember, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        closeLoadingFragment();
                        if (null != DinSDK.getUserInstance().getUser()
                                && !TextUtils.isEmpty(member.getUserId())
                                && member.getUserId().equals(DinSDK.getUserInstance().getUser().getUser_id())
                                && mSourceData.getPermission() != member.getPermission()) {
                            HomeManager.getInstance().getCurrentHome().setLevel(member.getPermission());
                            EventBus.getDefault().post(new UserPermissonUpdata());
                            getMainActivity().removeAllCommonFragment();
                            getMainActivity().smoothToHome();
                        } else {
                            toSetData();
                            updateTabTextUI();
                        }

                        EventBus.getDefault().post(new RequestUpdateMemberEvent(true));
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                        if (ErrorCode.ERROR_ONLY_CURRENT_ADMIN == i) {
                            showToast(getString(R.string.only_current_admin_msg));
                        } else {
                            showErrorToast();
                        }

                        resetData();

                        mBinding.changePermissionOption.setLocalText(getPermissionText(mPermission));
                        mBinding.permissionPushSwitch.setOn(member.isPush());
                        mBinding.permissionSmsSwitch.setOn(member.isSms());

                        setPushDetailMenuVisible(member.isPush());
                        setPushDetailMenuEnable(member.canUsePush());

                        setSmsDetailMenuVisible(TextUtils.isEmpty(member.getPhone()) && !member.isBindPhone() ? false : member.isSms());
                        setSmsDetailMenuEnable(!TextUtils.isEmpty(member.getPhone()) || member.isBindPhone());
                        updateHint();

                        updateTabTextUI();

                        EventBus.getDefault().post(new RequestUpdateMemberEvent(false));
                    }
                });
    }

    private void resetData() {
        member.setCall(mSourceData.isCall());
        member.setCall_info(mSourceData.isCall_info());
        member.setCall_sys(mSourceData.isCall_sys());
        member.setCall_sos(mSourceData.isCall_sos());
        member.setContactid(mSourceData.getContactid());
        member.setSms(mSourceData.isSms());
        member.setSms_sys(mSourceData.isSms_sys());
        member.setSms_sos(mSourceData.isSms_sos());
        member.setSms_info(mSourceData.isSms_info());
        member.setPush(mSourceData.isPush());
        member.setPush_sos(mSourceData.isPush_sos());
        member.setPush_info(mSourceData.isPush_info());
        member.setPush_sys(mSourceData.isPush_sys());
        member.setName(mSourceData.getName());
        member.setPermission(mSourceData.getPermission());
        member.setPhone(mSourceData.getPhone());
        member.setBindPhone(mSourceData.isBindPhone());
        member.setPhoto(mSourceData.getPhoto());
        member.setTime(mSourceData.getTime());
        member.setType(mSourceData.getType());
        member.setUid(mSourceData.getUid());
        member.setUserId(mSourceData.getUserId());
        mPermission = member.getPermission();
    }

    private void toSetData() {
        mSourceData.setCall(member.isCall());
        mSourceData.setCall_info(member.isCall_info());
        mSourceData.setCall_sys(member.isCall_sys());
        mSourceData.setCall_sos(member.isCall_sos());
        mSourceData.setContactid(member.getContactid());
        mSourceData.setSms(member.isSms());
        mSourceData.setSms_sys(member.isSms_sys());
        mSourceData.setSms_sos(member.isSms_sos());
        mSourceData.setSms_info(member.isSms_info());
        mSourceData.setPush(member.isPush());
        mSourceData.setPush_sos(member.isPush_sos());
        mSourceData.setPush_info(member.isPush_info());
        mSourceData.setPush_sys(member.isPush_sys());
        mSourceData.setName(member.getName());
        mSourceData.setPermission(member.getPermission());
        mSourceData.setPhone(member.getPhone());
        mSourceData.setBindPhone(member.isBindPhone());
        mSourceData.setPhoto(member.getPhoto());
        mSourceData.setTime(member.getTime());
        mSourceData.setType(member.getType());
        mSourceData.setUid(member.getUid());
        mSourceData.setUserId(member.getUserId());
        if (callback != null)
            callback.updata();
    }


    private String getPermissionText(int i) {
        if (i == LocalKey.ADMIN) {
            return getResources().getString(R.string.change_permission_admin);
        } else {
            return getResources().getString(R.string.change_permission_guest);
        }
    }

    public TextView createTabView(final String text, boolean isBlue) {
        TextView tv = new TextView(getMainActivity());
        tv.setText(Local.s(text));
        tv.setTextAppearance(getContext(), R.style.TextFamilyCaptionM);
        if (isBlue) {
            tv.setBackgroundResource(R.drawable.dd_tab_corner_blue_bg);
            tv.setTextColor(getResources().getColor(R.color.colorContacTabText));
        } else {
            tv.setBackgroundResource(R.drawable.dd_tab_corner_gray_bg);
            tv.setTextColor(getResources().getColor(R.color.colorContacTabText));
        }
        FlowLayout.LayoutParams lap = new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT,
                FlowLayout.LayoutParams.WRAP_CONTENT);
        final int margin = 30;
        lap.setMargins(0, 10, 0, 10);
        tv.setPadding(margin, margin, margin, margin);
        lap.bottomMargin = margin;
        lap.topMargin = margin;
        lap.leftMargin = margin + 10;
        lap.rightMargin = margin + 10;
        tv.setLayoutParams(lap);
        return tv;
    }

    private void initKeypadPasswordView() {
        mBinding.switchKeyboardPassword.setOnSwitchStateChangeListener(isOn -> {
            if (isOn) {
                showEnableKeypadPwdDialog();
            } else {
                showDisableKeypadPwdDialog();
            }
        });
        mBinding.ivKeyboardPasswordVisible.setOnClickListener(v -> {
            final boolean hidden = mBinding.tvKeyboardPassword.getInputType() == 129;
            changeKeypadPasswordVisible(hidden);
        });
        mBinding.ivKeyboardPasswordVisibleTittle.setOnClickListener(v -> {
            final boolean hidden = mBinding.tvKeyboardPasswordTittle.getInputType() == 129;
            changeKeypadPasswordVisible(hidden);
        });
        mBinding.tvResetKeypadPassword.setOnClickListener(v -> showResetKeypadPwdDialog());

        // 默认不显示键盘密码的相关设置
        changeKeypadPasswordGroupVisible(false, false, false);
        if (AppConfig.Functions.SUPPORT_KEYPAD_PWD) {
            final boolean hadPanelNotDeleted = CommonDataUtil.getInstance().isHadPanelNotDeleted();
            if (hadPanelNotDeleted) {
                showTimeOutLoadinFramgmentWithErrorAlert();
                requestMemberKeypadPwdInfo();
            }
        }
    }

    /**
     * 获取键盘密码的相关配置信息
     */
    private void requestMemberKeypadPwdInfo() {
        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final String userId = mSourceData.getUserId();

        DinSDK.getHomeInstance().getKeypadMemberPwdInfo(homeId, panelId, userId, new IDefaultCallBack2<KeypadMemberPwdInfoGetResponse>() {
            @Override
            public void onSuccess(KeypadMemberPwdInfoGetResponse keypadMemberPwdInfoGetResponse) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (null == keypadMemberPwdInfoGetResponse || null == keypadMemberPwdInfoGetResponse.getResult()) {
                    showErrorToast();
                    return;
                }

                final KeypadMemberPwdInfoGetResponse.ResultBean result = keypadMemberPwdInfoGetResponse.getResult();
                final Boolean hasDevice = result.getHasDevice();
                final Boolean hasKeyBoard = result.getHasKeyBoard();
                final Boolean pwdEnable = result.getPwdEnable();
                final String pwd = result.getPwd();

                final int myPermission = DinSDK.getHomeInstance().getCurrentHomeInfo().getLevel();
                final boolean isMeAdmin = LocalKey.ADMIN == myPermission;
                final boolean isMeUser = LocalKey.USER == myPermission;
                // 有主机并且有键盘并且自己是管理员或用户的时候才显示相关设置
                final boolean visible = null != hasDevice && hasDevice
                        && null != hasKeyBoard && hasKeyBoard;
                final boolean enabled = null != pwdEnable && pwdEnable;

                changeKeypadPasswordGroupVisible((visible && isMeAdmin) || (visible && isMeUser && enabled), isMeAdmin, enabled);
                setKeypadPassword(pwd);
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error on requestMemberKeypadPwdInfo, i: " + i + ", s: " + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
            }
        });
    }

    /**
     * 修改键盘密码的开关状态
     *
     * @param enabled true: 开启键盘密码；false: 关闭键盘密码
     */
    private void requestUpdateMemberKeypadPwdInfo(final boolean enabled) {
        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final String userId = mSourceData.getUserId();

        DinSDK.getHomeInstance().updateKeypadMemberPwdInfo(homeId, panelId, userId, enabled, new IDefaultCallBack2<KeypadMemberPwdUpdateResponse>() {
            @Override
            public void onSuccess(KeypadMemberPwdUpdateResponse keypadMemberPwdInfoUpdateResponse) {
                closeLoadingFragmentWithCallBack();
                if (null == keypadMemberPwdInfoUpdateResponse || null == keypadMemberPwdInfoUpdateResponse.getResult()) {
                    mBinding.switchKeyboardPassword.setOn(!enabled);
                    showErrorToast();
                    return;
                }

                // 修改成功
                final KeypadMemberPwdUpdateResponse.ResultBean result = keypadMemberPwdInfoUpdateResponse.getResult();
                final String pwd = result.getPwd();
                changeKeypadPasswordDetailVisible(enabled);
                setKeypadPassword(pwd);
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error on requestUpdateMemberKeypadPwdInfo, i: " + i + ", s: " + s);
                closeLoadingFragmentWithCallBack();
                mBinding.switchKeyboardPassword.setOn(!enabled);
                showErrorToast();
            }
        });
    }

    /**
     * 重置键盘密码
     */
    private void requestResetMemberKeypadPwdInfo() {
        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final String userId = mSourceData.getUserId();

        DinSDK.getHomeInstance().resetKeypadMemberPwdInfo(homeId, panelId, userId, new IDefaultCallBack2<KeypadMemberPwdResetResponse>() {
            @Override
            public void onSuccess(KeypadMemberPwdResetResponse keypadMemberPwdInfoResetResponse) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (null == keypadMemberPwdInfoResetResponse || null == keypadMemberPwdInfoResetResponse.getResult()) {
                    showErrorToast();
                    return;
                }

                // 修改成功
                final KeypadMemberPwdResetResponse.ResultBean result = keypadMemberPwdInfoResetResponse.getResult();
                final String pwd = result.getPwd();
                setKeypadPassword(pwd);
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error on requestResetMemberKeypadPwdInfo, i: " + i + ", s: " + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
            }
        });
    }

    /**
     * @param visible  是否显示相关设置
     * @param editable 是否可以修改
     * @param opened   当前是否已开启
     */
    private void changeKeypadPasswordGroupVisible(final boolean visible, final boolean editable, final boolean opened) {
        if (visible) {
            mBinding.llKeypadPasswordGroup.setVisibility(View.VISIBLE);
            if (editable) {
                changeKeypadPasswordEditable(true);
                changeKeypadPasswordDetailVisible(opened);
            } else {
                changeKeypadPasswordDetailVisible(false);
                changeKeypadPasswordEditable(false);
            }
        } else {
            mBinding.llKeypadPasswordGroup.setVisibility(View.GONE);
        }
    }

    private void changeKeypadPasswordEditable(final boolean editable) {
        if (editable) {
            mBinding.switchKeyboardPassword.setVisibility(View.VISIBLE);
            mBinding.llKeyboardPasswordTittle.setVisibility(View.GONE);
        } else {
            mBinding.switchKeyboardPassword.setVisibility(View.GONE);
            mBinding.llKeyboardPasswordTittle.setVisibility(View.VISIBLE);
            mBinding.tvKeypadPasswordTittle.setLocalText(R.string.keypad_password);
        }
    }

    private void changeKeypadPasswordDetailVisible(final boolean opened) {
        if (opened) {
            mBinding.switchKeyboardPassword.setOn(true);
            mBinding.llKeyboardPassword.setVisibility(View.VISIBLE);
            mBinding.vLineKeypadPasswordMiddle.setVisibility(View.VISIBLE);
            mBinding.tvKeypadPasswordTittle.setLocalText(R.string.keypad_password);
        } else {
            mBinding.switchKeyboardPassword.setOn(false);
            mBinding.llKeyboardPassword.setVisibility(View.GONE);
            mBinding.vLineKeypadPasswordMiddle.setVisibility(View.GONE);
            mBinding.tvKeypadPasswordTittle.setLocalText(R.string.assign_keypad_password);
        }
    }

    /**
     * 修改是否明文密码
     *
     * @param visible true: 显示明文密码
     */
    private void changeKeypadPasswordVisible(final boolean visible) {
        if (visible) {
            mBinding.ivKeyboardPasswordVisible.setImageResource(R.drawable.icon_form_show);
            mBinding.tvKeyboardPassword.setInputType(InputType.TYPE_CLASS_TEXT);
            mBinding.ivKeyboardPasswordVisibleTittle.setImageResource(R.drawable.icon_form_show);
            mBinding.tvKeyboardPasswordTittle.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            mBinding.ivKeyboardPasswordVisible.setImageResource(R.drawable.icon_form_hide);
            mBinding.tvKeyboardPassword.setInputType(129);
            mBinding.ivKeyboardPasswordVisibleTittle.setImageResource(R.drawable.icon_form_hide);
            mBinding.tvKeyboardPasswordTittle.setInputType(129);
        }
    }

    private void setKeypadPassword(final String pwd) {
        final String displayPwd = null == pwd ? "" : pwd;
        mBinding.tvKeyboardPassword.setText(displayPwd);
        mBinding.tvKeyboardPasswordTittle.setText(displayPwd);
    }

    private void showEnableKeypadPwdDialog() {
        AlertDialog.createBuilder(getContext()).setContent(getString(R.string.keypad_password_enable_hint))
                .setOk(getString(R.string.Confirm))
                .setCanCancel(false)
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    showTimeOutLoadinFramgmentWithCallBack(0, () -> mBinding.switchKeyboardPassword.setOn(false));
                    requestUpdateMemberKeypadPwdInfo(true);
                })
                .setCancelListener(() -> {
                    mBinding.switchKeyboardPassword.setOn(false);
                })
                .preBuilder()
                .show();
    }

    private void showDisableKeypadPwdDialog() {
        AlertDialog.createBuilder(getContext()).setContent(getString(R.string.keypad_password_disable_hint))
                .setOk(getString(R.string.Confirm))
                .setCanCancel(false)
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    showTimeOutLoadinFramgmentWithCallBack(0, () -> mBinding.switchKeyboardPassword.setOn(true));
                    requestUpdateMemberKeypadPwdInfo(false);
                })
                .setCancelListener(() -> {
                    mBinding.switchKeyboardPassword.setOn(true);
                })
                .preBuilder()
                .show();
    }

    private void showResetKeypadPwdDialog() {
        AlertDialog.createBuilder(getContext()).setContent(getString(R.string.keypad_password_reset_hint))
                .setOk(getString(R.string.Confirm))
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    showTimeOutLoadinFramgmentWithErrorAlert();
                    requestResetMemberKeypadPwdInfo();
                })
                .preBuilder()
                .show();
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        updateTabTextUI();
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    /**
     * 更新权限提示
     */
    private void updateHint() {
        String hint = "";
        if (TextUtils.isEmpty(member.getPhone())
                && !member.isBindPhone()) {
            hint = getResources().getString(R.string.permission_no_sim_hint);
        }

        if (TextUtils.isEmpty(hint)) {
            mBinding.permissionNoSimHint.setVisibility(View.GONE);
        } else {
            mBinding.permissionNoSimHint.setVisibility(View.VISIBLE);
            mBinding.permissionNoSimHint.setLocalText(hint);
        }

    }

    @Override
    public boolean onBackPressed() {
        if (null != member && !member.equals(mSourceData)) {
            showUnSaveChangeDialog();
            return true;
        }
        return super.onBackPressed();
    }

    private void showUnSaveChangeDialog() {
        AlertDialog.createBuilder(getContext())
                .setOk(getString(R.string.save))
                .setCancel(getString(R.string.cancel))
                .setOKListener(this::toDoSave)
                .setCancelListener(this::removeSelf)
                .setContent(getString(R.string.un_save_member_info_change))
                .preBuilder()
                .show();
    }

    public void showMoreActionDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.delete)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setCancelableOnTouchOutside(true)
                .setListener(this).show();
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        if (0 == index) {
            AlertDialog.createBuilder(getDelegateActivity())
                    .setOk(Local.s(getResources().getString(R.string.smart_plugs_list_delete_yes)))
                    .setCancel(Local.s(getResources().getString(R.string.smart_plugs_list_delete_no)))
                    .setContent(Local.s(getResources().getString(R.string.smart_plugs_list_delete_confirm)))
                    .setOKListener(new AlertDialog.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            showTimeOutLoadinFramgment();

                            DinSDK.getHomeInstance().removeFamilyMember(
                                    HomeManager.getInstance().getCurrentHome().getHomeID(),
                                    mSourceData.getUserId(), new IDefaultCallBack() {
                                        @Override
                                        public void onSuccess() {
                                            closeLoadingFragment();
                                            if (callback != null) {
                                                callback.deletItem(mSourceData);
                                            }
                                            removeSelf();
                                        }

                                        @Override
                                        public void onError(int i, String s) {
                                            closeLoadingFragment();
                                            showErrorToast();
                                        }
                                    }
                            );
                        }
                    })
                    .preBuilder()
                    .show();
        }
    }

    @Subscribe()
    public void onEvent(RequestUpdateMemberEvent event) {
        if (event.isResult()) {
            return;
        }

        toDoSave();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(KeypadMemberPwdUpdatedEvent event) {
        final String editUid = mSourceData.getUid();
        final String myUid = null != DinSDK.getUserInstance().getUser() ? DinSDK.getUserInstance().getUser().getUid() : null;
        if (editUid != null && editUid.equals(myUid)) {
            requestMemberKeypadPwdInfo();
        }
    }
}

