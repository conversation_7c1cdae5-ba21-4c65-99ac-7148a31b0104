package com.dinsafer.module.settting.adapter;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.MyDeviceItemHeaderBinding;
import com.dinsafer.model.PlugsData;
import com.dinsafer.util.CommonDataUtil;

import java.util.ArrayList;

import androidx.databinding.DataBindingUtil;


/**
 * Created by Rinfon on 16/7/1.
 */
public class SecurityPlugsItem extends BasePluginItem {

    private Activity mActivity;

    private ArrayList<PlugsData> mData;

    public static final int TYPE_ITEM = 1;

    public static final int TYPE_HEADER = 0;

    public SecurityPlugsItem(Activity mActivity, ArrayList<PlugsData> mData) {
        this.mActivity = mActivity;
        this.mData = mData;
    }

    @Override
    public int getItemViewType(int position) {
        if (TextUtils.isEmpty(mData.get(position).getName())) {
            return 0;
        } else {
            return 1;
        }
    }

    public ArrayList<PlugsData> getData() {
        return mData;
    }

    public void setData(ArrayList<PlugsData> mData) {
        this.mData = mData;
    }

    @Override
    public int getViewTypeCount() {
        return 2;
    }

    @Override
    public int getCount() {
        if (mData != null)
            return mData.size();
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        int type = getItemViewType(position);
        if (type == TYPE_HEADER) {
            HeaderViewHolder holder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.my_device_item_header, null);
                holder = new HeaderViewHolder(convertView);
                convertView.setTag(holder);
            } else {
                try {
                    holder = (HeaderViewHolder) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            holder.binding.myDeviceItemHeader.setLocalText(mData.get(position).getDescription());
        } else {
            ItemPluginHolder itemHolder = null;
            if (convertView == null) {
                convertView = LayoutInflater.from(mActivity).inflate(R.layout.item_plugin_new_style, null);
                itemHolder = new ItemPluginHolder(convertView);
                convertView.setTag(itemHolder);
            } else {
                try {
                    itemHolder = (ItemPluginHolder) convertView.getTag();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            // 初始化数据
            PlugsData plugsData = mData.get(position);
            boolean panelDeviceState = !CommonDataUtil.getInstance().isPanelOnline();
            updatePluginItemByPanelDevice(panelDeviceState, plugsData, plugsData.getDescription(), itemHolder);
        }

        return convertView;
    }

    public void remove(int index) {
        if (mData != null && index > 0 && index < mData.size()) {
            if (TextUtils.isEmpty(mData.get(index - 1).getName())
                    && (index + 1) == mData.size()) {
//                已经是最后一组，且只有一个item，删除本组
//                删除item
                mData.remove(index);
//                删除标题
                mData.remove(index - 1);
            } else if (TextUtils.isEmpty(mData.get(index - 1).getName())
                    && TextUtils.isEmpty(mData.get(index + 1).getName())) {
//                前一个为header，后一个也是header的话，删除本组
//                删除item
                mData.remove(index);
//                删除标题
                mData.remove(index - 1);
            } else {
                mData.remove(index);
            }
            notifyDataSetChanged();
        }
    }

    public void changeName(int index, String name) {
        mData.get(index).setDescription(name);
        notifyDataSetChanged();
    }

    class HeaderViewHolder {
        MyDeviceItemHeaderBinding binding;
        HeaderViewHolder(View view) {
           binding = DataBindingUtil.bind(view);
        }
    }
}
