package com.dinsafer.module.settting.ui;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.HomeManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by Rinfon on 16/7/8.
 */
public class FeedBackFragment extends BaseFragment {

    LocalTextView commonBarTitle;

    ImageView ivRightIcon;

    EditText feedbackEdittext;

    EditText feedbackEmailEdittext;

    LocalTextView feedbackSendBtn;

    LocalTextView feedbackTitle;

    LocalTextView feedbackEmail;

    RelativeLayout feedbackSendBtnLayout;

    private Drawable mDrawableEnvelope, mDrawablePencil;

    public static FeedBackFragment newInstance() {
        return new FeedBackFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.feedback_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
        rootView.findViewById(R.id.feedback_send_btn_layout).setOnClickListener( v -> toSend());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        ivRightIcon = rootView.findViewById(R.id.common_bar_right_icon);
        feedbackEdittext = rootView.findViewById(R.id.feedback_edittext);
        feedbackEmailEdittext = rootView.findViewById(R.id.feedback_email_edittext);
        feedbackSendBtn = rootView.findViewById(R.id.feedback_send_btn);
        feedbackTitle = rootView.findViewById(R.id.feedback_title);
        feedbackEmail = rootView.findViewById(R.id.feedback_email);
        feedbackSendBtnLayout = rootView.findViewById(R.id.feedback_send_btn_layout);
    }

    @Override
    public void initData() {
        mDrawableEnvelope = getResources().getDrawable(R.drawable.icon_define_setting_envelope);
        mDrawableEnvelope.setBounds(0, 0, mDrawableEnvelope.getMinimumWidth(), mDrawableEnvelope.getMinimumHeight());
        mDrawablePencil = getResources().getDrawable(R.drawable.icon_define_setting_content_edit);
        mDrawablePencil.setBounds(0, 0, mDrawablePencil.getMinimumWidth(), mDrawablePencil.getMinimumHeight());

        commonBarTitle.setLocalText(getResources().getString(R.string.app_setting_feedback));
        ivRightIcon.setImageResource(R.drawable.icon_navbar_phonecall);
        ivRightIcon.setVisibility(View.VISIBLE);
        feedbackTitle.setLocalText(getResources().getString(R.string.feedback_note));
        feedbackEmail.setLocalText(getResources().getString(R.string.feedback_email));
        feedbackSendBtn.setLocalText(getResources().getString(R.string.feedback_send));
        feedbackSendBtn.setAlpha(0.3f);
        ivRightIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showPhoneCall("45 78759563");
            }
        });
        feedbackEdittext.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (null == feedbackEdittext) {
                    return;
                }
//                if (hasFocus || !TextUtils.isEmpty(feedbackEdittext.getText().toString())) {
//                    feedbackEdittext.setCompoundDrawables(null, null, null, null);
//                } else {
//                    feedbackEdittext.setCompoundDrawables(mDrawablePencil, null, null, null);
//                }
            }
        });
        feedbackEmailEdittext.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (null == feedbackEmailEdittext) {
                    return;
                }
//                if (hasFocus || !TextUtils.isEmpty(feedbackEmailEdittext.getText().toString())) {
//                    feedbackEmailEdittext.setCompoundDrawables(null, null, null, null);
//                } else {
//                    feedbackEmailEdittext.setCompoundDrawables(mDrawableEnvelope, null, null, null);
//                }
            }
        });

        feedbackEdittext.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                if (TextUtils.isEmpty(feedbackEdittext.getText())) {
                    feedbackSendBtn.setAlpha(0.3f);
                } else {
                    feedbackSendBtn.setAlpha(1f);
                    feedbackSendBtnLayout.setOnTouchListener(new View.OnTouchListener() {
                        @Override
                        public boolean onTouch(View v, MotionEvent event) {
                            switch (event.getAction()) {
                                case MotionEvent.ACTION_DOWN:
                                    feedbackSendBtnLayout.setAlpha(0.7f);
                                    break;

                                case MotionEvent.ACTION_UP:
                                    feedbackSendBtnLayout.setAlpha(1.0f);
                                    break;

                                case MotionEvent.ACTION_CANCEL:
                                    feedbackSendBtnLayout.setAlpha(1.0f);
                                    break;
                            }
                            return false;
                        }
                    });

                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
        feedbackEmailEdittext.setText(DinSDK.getUserInstance().getUser().getEmail());

    }

    private void showPhoneCall(String phoneNum) {

        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.ps_advanced_settings_cancel)))
                .setOtherButtonTitles(getString(R.string.ps_customer_contact_customer_call_space) + phoneNum)
                .setCancelableOnTouchOutside(true)
                .setLastButtonTextColor(getResources().getColor(R.color.color_FF6497FD))
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);
                        Intent intent = new Intent(Intent.ACTION_DIAL);
                        Uri data = Uri.parse("tel:"+phoneNum);
                        intent.setData(data);
                        startActivity(intent);
                    }
                }).show();
    }

    public void close() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }


    public void toSend() {

        if (TextUtils.isEmpty(feedbackEdittext.getText())) {
//            showToast("请输入内容");
            return;
        }

        if (TextUtils.isEmpty(feedbackEmailEdittext.getText().toString()) || !DisplayUtil.validate(feedbackEmailEdittext.getText().toString())) {
            showToast(getResources().getString(R.string.invalid_email_address));
            return;
        }

        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.ok))
                .setContent(getResources().getString(R.string.feedback_send_success))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        removeSelf();
                    }
                }).preBuilder().show();

        DinsafeAPI.getApi().getFeedBackCall(
                HomeManager.getInstance().getCurrentHome().getHomeID(),
                CommonDataUtil.getInstance().getCurrentDeviceId(),
                feedbackEdittext.getText().toString() + getVersionInfo(), feedbackEmailEdittext.getText().toString())
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {

                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {

                    }
                });
    }

    private String getVersionInfo() {
        return " - (Android - " + DDSystemUtil.getVersion(getDelegateActivity()) + ")";
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

}

