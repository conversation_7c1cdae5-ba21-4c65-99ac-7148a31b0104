package com.dinsafer.module.settting.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ContactsPickerItemBinding;
import com.dinsafer.model.ContactBean;

import java.util.ArrayList;

import androidx.databinding.DataBindingUtil;

/**
 * 通讯录列表
 *
 * <AUTHOR>
 */
public class ContactsPickerAdapterNew extends BaseAdapter {

    private ArrayList<ContactBean> list;

    public ContactsPickerAdapterNew(ArrayList<ContactBean> mData) {
        this.list = mData;
    }

    @Override
    public int getCount() {
        if (list != null) {
            return list.size();
        }
        return 0;
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup viewGroup) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.contacts_picker_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        ContactBean contactBean = list.get(position);
        assert holder != null;

        holder.binding.contactPickerCheckbox.setChecked(contactBean.isCheck);
        holder.binding.contactPickerPhone.setText(contactBean.getPhone());
        holder.binding.contactPickerName.setText(contactBean.getName());

        final ViewHolder finalHolder = holder;
        holder.binding.contactPickerLayout.setOnClickListener(view -> {
            for (ContactBean cb : list) {
                cb.isCheck = false;
            }
            contactBean.isCheck = true;
            notifyDataSetChanged();
        });
        return convertView;
    }

    public void refreshList(ArrayList<ContactBean> list) {
        this.list = list;
        notifyDataSetChanged();
    }

    static class ViewHolder {
        ContactsPickerItemBinding binding;

        ViewHolder(View view) {
            binding = DataBindingUtil.bind(view);
        }
    }

}
