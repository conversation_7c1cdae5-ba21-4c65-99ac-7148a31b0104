package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.BounceInterpolator;
import android.widget.AdapterView;

import androidx.annotation.Nullable;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.BmtManager;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.FamilyListData;
import com.dinsafer.model.FamilySwitchEvent;
import com.dinsafer.model.family.FamilyListChangeEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.family.view.CreateFamilyFragment;
import com.dinsafer.module.iap.CloudStorageServiceHelper;
import com.dinsafer.module.iap.GetServiceExpirationResponse;
import com.dinsafer.module.iap.powercare.TrafficPackageProductSchedulesResponse;
import com.dinsafer.module.iap.powercare.TrafficPackageServiceCardItemModel;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.adapter.MyFamilyListAdapter;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


/**
 * Created by Rinfon on 16/7/8.
 */
public class MyFamilyFragment extends BaseFragment implements MyFamilyListAdapter.ICallBack {

    LocalTextView commonBarTitle;

    SwipeMenuListView myDeviceListview;

    private ArrayList<FamilyListData> mData;

    private MyFamilyListAdapter listAdapter;

    public static MyFamilyFragment newInstance() {
        return new MyFamilyFragment();
    }

    public static MyFamilyFragment newInstance(boolean isNotShowCurrentDevice) {
        Bundle bundle = new Bundle();
        bundle.putBoolean("isNotShowCurrentDevice", isNotShowCurrentDevice);
        MyFamilyFragment myFamilyFragment = new MyFamilyFragment();
        myFamilyFragment.setArguments(bundle);
        return myFamilyFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.my_family_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        getDelegateActivity().setTheme(R.style.ActionSheetStyleiOS7);
        EventBus.getDefault().register(this);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
        rootView.findViewById(R.id.common_bar_add).setOnClickListener( v -> {
            ArrayList<String> familyNameList = new ArrayList<>();
            for (FamilyListData family : mData) {
                if (family.isHeader() || family.isBottom()) {
                    continue;
                }
                familyNameList.add(family.getName());
            }
            getMainActivity().addCommonFragment(CreateFamilyFragment.newInstance(familyNameList));
        });
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        myDeviceListview = rootView.findViewById(R.id.my_device_listview);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.my_family_text));

        SwipeMenuCreator creator = new SwipeMenuCreator() {

            @Override
            public void create(SwipeMenu menu) {
                if (menu.getViewType() != MyFamilyListAdapter.TYPE_HEADER
                        &&  menu.getViewType() != MyFamilyListAdapter.TYPE_BOTTOM) {
                    // create "delete" item
                    SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                    // set item background
                    deleteItem.setBackground(R.color.colorDelete);
                    // set item width
                    deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                    deleteItem.setTitleSize(16);

                    deleteItem.setTitleColor(Color.WHITE);
                    // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                    deleteItem.setTitle(Local.s(getResources().getString(R.string.quit)));
                    // add to menu
                    menu.addMenuItem(deleteItem);
                }
            }
        };

//        set creator
        myDeviceListview.setMenuCreator(creator);
        myDeviceListview.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
        myDeviceListview.setCloseInterpolator(new BounceInterpolator());
        myDeviceListview.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                if (0 == i1) {
                    requestCheckIsOnlyAdmin(i);
                }
                // false : close the menu; true : not close the menu
                return false;
            }
        });
        myDeviceListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                toAction(parent,view,position,id);
            }
        });
        mData = new ArrayList<FamilyListData>();
        createHomeList();
    }

    /**
     * 检查是否是Family唯一的管理员
     *
     * @param index
     */
    private void requestCheckIsOnlyAdmin(final int index) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getHomeInstance().isOnlyAdmin(mData.get(index).getId(), new IDefaultCallBack2<Boolean>() {
            @Override
            public void onSuccess(Boolean onlyAdminInfo) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (null == onlyAdminInfo) {
                    DDLog.e(TAG, "Error on requestCheckIsOnlyAdmin, empty result");
                    showErrorToast();
                    return;
                }

                if (onlyAdminInfo) {
                    // 唯一管理员-强制退出
                    showConfirmForceDeleteFaimilyDialog(index);
                } else {
                    // 还有其他管理员-退出家庭
                    showConfirmQuitFamilyDialog(index);
                }
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error on requestCheckIsOnlyAdmin, i: " + i + ", s: " + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
            }
        });
    }

    private void showConfirmQuitFamilyDialog(final int index) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.quit))
                .setCancel(getResources().getString(R.string.cancel))
                .setContent(getResources().getString(R.string.confirm_quit_family))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        HomeManager.getInstance().quiteFamily(mData.get(index).getId(), new IDefaultCallBack() {
                            @Override
                            public void onSuccess() {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                            }

                            @Override
                            public void onError(int i, String s) {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                            }
                        });
                    }
                })
                .preBuilder()
                .show();
    }

    private void showConfirmForceDeleteFaimilyDialog(final int index) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.quit_unbind_device))
                .setCancel(getResources().getString(R.string.cancel))
                .setContent(getResources().getString(R.string.force_delete_home_hint))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE) {
                            if (!CloudStorageServiceHelper.getInstance().isCloudServiceOpen()) {
                                forceDeleteDevice(index);
                            } else {
                                checkCloudStorageServiceExpiration(index);
                            }
                        }

                        if (AppConfig.Functions.SUPPORT_TRAFFIC_PACKAGE_SERVICE) {
                            if (CollectionUtil.isListNotEmpty(BmtManager.getInstance().getNotDeletedBmtDeviceList())) {
                                checkTrafficPackageServiceExpiration(index);
                            } else {
                                forceDeleteDevice(index);
                            }
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    private void createHomeList() {
        getHomeListData();

        // Join a family
//        FamilyListData joinFamily = new FamilyListData();
//        joinFamily.setBottom(true)
//                .setName(getString(R.string.join_a_family));
//        mData.add(joinFamily);

        listAdapter = new MyFamilyListAdapter(getActivity(), mData);
        listAdapter.setCallBack(this);
        myDeviceListview.setAdapter(listAdapter);
    }

    private void getHomeListData() {
        mData.clear();
        if (HomeManager.getInstance().getHomeList().size() <= 0) {
            DDLog.e(TAG, "Have no home now.");
            return;
        }

        if (getArguments() != null && getArguments().getBoolean("isNotShowCurrentDevice")) {
            FamilyListData three = new FamilyListData();
            three.setName(getResources().getString(R.string.my_family_other))
                    .setSelect(false)
                    .setHeader(true);
            mData.add(three);
            for (Home home : HomeManager.getInstance().getHomeList()) {
                FamilyListData four = new FamilyListData();
                four.setName(home == null ? "" : home.getHomeName())
                        .setId(home.getHomeID())
                        .setLevel(home.getLevel())
                        .setSelect(false)
                        .setHeader(false);
                mData.add(four);
            }
        } else {
            FamilyListData one = new FamilyListData();
            one.setName(getResources().getString(R.string.my_family_current))
                    .setSelect(false)
                    .setHeader(true);


            FamilyListData two = new FamilyListData();
            Home home = HomeManager.getInstance().getCurrentHome();

            two.setName(home == null ? "" : home.getHomeName())
                    .setId(home.getHomeID())
                    .setLevel(home.getLevel())
                    .setSelect(true)
                    .setHeader(false);
            mData.add(one);
            mData.add(two);
            if (HomeManager.getInstance().getHomeList().size() > 1) {
                FamilyListData three = new FamilyListData();
                three.setName(getResources().getString(R.string.my_family_other))
                        .setSelect(false)
                        .setHeader(true);
                mData.add(three);

                for (int i = 0; i < HomeManager.getInstance().getHomeList().size(); i++) {
                    if (!HomeManager.getInstance().getHomeList().get(i).getHomeID()
                            .equals(HomeManager.getInstance().getCurrentHome().getHomeID())) {
                        FamilyListData four = new FamilyListData();

                        Home tempHome =
                                HomeManager.getInstance().getHomeList().get(i);
                        four.setName(tempHome == null ? "" : tempHome.getHomeName())
                                .setId(tempHome.getHomeID())
                                .setLevel(tempHome.getLevel())
                                .setSelect(false)
                                .setHeader(false);
                        mData.add(four);
                    }
                }
            }
        }
    }

    public void close() {
        if (getArguments() != null && getArguments().getBoolean("isNotShowCurrentDevice")) {
            return;
        }
        getDelegateActivity().removeCommonFragmentAndData(this, true);
    }


    public void toAction(AdapterView<?> adapterView, View view, final int i, long l) {
        if (!mData.get(i).isHeader()
                && !mData.get(i).isBottom()) {
            if (!mData.get(i).isSelect()) {
                if (mData.get(i).getLevel() == LocalKey.ADMIN) {
                    adminOtherFamilyAction(i);
                } else {
                    otherFamilyAction(i);
                }
            } else {
                if (mData.get(i).getLevel() == LocalKey.ADMIN) {
                    adminCurrentFamilyAction(i);
                } else if (mData.get(i).getLevel() == LocalKey.USER) {
                    userCurrentFamilyAction(i);
                } else {
                    currentFamilyAction(i);
                }
            }
        }
    }

    //   当前用户在当前选中的房间是不是管理者
    private void currentFamilyAction(int i) {
        final String[] localTittles;
        localTittles = new String[]{Local.s(getResources().getString(R.string.quit))};

        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(localTittles)
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.colorDelete))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            requestCheckIsOnlyAdmin(i);
                        }
                    }
                }).show();
    }

    /**
     * 当前用户在当前选中的房间是USER
     *
     * @param i
     */
    private void userCurrentFamilyAction(int i) {
        final String[] localTittles;
        localTittles = new String[]{
                Local.s(getString(R.string.device_managent_member_setting)),
                Local.s(getResources().getString(R.string.quit))};

        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(localTittles)
                .setCancelableOnTouchOutside(true)
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.colorDelete))
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (0 == index) {
                            getDelegateActivity().addCommonFragment(ContactsListFragment.newInstance());
                        } else if (index == 1) {
                            requestCheckIsOnlyAdmin(i);
                        }
                    }
                }).show();
    }

    //    当前用户在当前选中的房间是管理者
    private void adminCurrentFamilyAction(int i) {
        final String[] localTittles;
        localTittles = new String[]{
                Local.s(getString(R.string.device_managent_member_setting)),
                Local.s(getResources().getString(R.string.rename))
                , Local.s(getResources().getString(R.string.quit))};

        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(localTittles)
                .setCancelableOnTouchOutside(true)
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.colorDelete))
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (0 == index) {
                            getDelegateActivity().addCommonFragment(ContactsListFragment.newInstance());
                        } else if (index == 1) {
                            //                                修改名字
                            EdittextDialog.createBuilder(getDelegateActivity())
                                    .setOk(getResources().getString(R.string.Confirm))
                                    .setCancel(getResources().getString(R.string.Cancel))
                                    .setDefaultName(mData.get(i).getName())
                                    .setContent(getResources().getString(R.string.rename_family))
                                    .setAutoDismiss(false)
                                    .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                                        @Override
                                        public void onOkClick(EdittextDialog dialog, String string) {
                                            if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                                                getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                                                dialog.dismiss();
                                                return;
                                            }

                                            changeHomeName(i, string);
                                            dialog.dismiss();
                                            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loading));
                                        }
                                    })
                                    .preBuilder()
                                    .show();
                        } else if (index == 2) {
                            requestCheckIsOnlyAdmin(i);
                        }
                    }
                }).show();
    }

    //    当前用户在其他家庭是管理员的actionsheet
    private void adminOtherFamilyAction(int i) {
        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.switch_a_family))
                        , Local.s(getResources().getString(R.string.rename))
                        , Local.s(getResources().getString(R.string.quit)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.colorDelete))
                .setCancelableOnTouchOutside(true)

                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
//                                改变房间
                            if (getArguments() != null && getArguments().getBoolean("isNotShowCurrentDevice")) {
                                getDelegateActivity().removeAllCommonFragment();
                                HomeManager.getInstance().changeFamily(mData.get(i).getId());
                            } else {
                                getDelegateActivity().removeAllCommonFragment();
                                HomeManager.getInstance().changeFamily(mData.get(i).getId());
                            }
                        } else if (index == 1) {
//                                修改名字
                            EdittextDialog.createBuilder(getDelegateActivity())
                                    .setOk(getResources().getString(R.string.Confirm))
                                    .setCancel(getResources().getString(R.string.Cancel))
                                    .setDefaultName(mData.get(i).getName())
                                    .setContent(getResources().getString(R.string.rename_family))
                                    .setAutoDismiss(false)
                                    .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                                        @Override
                                        public void onOkClick(EdittextDialog dialog, String string) {
                                            if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                                                getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                                                dialog.dismiss();
                                                return;
                                            }

                                            changeHomeName(i, string);
                                            dialog.dismiss();
                                            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loading));
                                        }
                                    })
                                    .preBuilder()
                                    .show();
                        } else if (index == 2) {
                            requestCheckIsOnlyAdmin(i);
                        }
                    }
                }).show();
    }


    //    当前用户在其他家庭是不是管理员的actionsheet
    private void otherFamilyAction(int i) {
        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.switch_a_family)),
                        Local.s(getResources().getString(R.string.quit)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.colorDelete))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
//                                改变房间
                            if (getArguments() != null && getArguments().getBoolean("isNotShowCurrentDevice")) {
                                getDelegateActivity().removeAllCommonFragment();
                                HomeManager.getInstance().changeFamily(mData.get(i).getId());
                            } else {
                                getDelegateActivity().removeAllCommonFragment();
                                HomeManager.getInstance().changeFamily(mData.get(i).getId());

                            }
                        } else {
                            requestCheckIsOnlyAdmin(i);
                        }
                    }
                }).show();
    }

    public void toAddHome() {
        final boolean hadFamily = null != HomeManager.getInstance().getHomeList()
                && 0 < HomeManager.getInstance().getHomeList().size();
        final String[] menus;
        if (hadFamily) {
            menus = new String[2];
            menus[0] = getResources().getString(R.string.join_a_family);
            menus[1] = getResources().getString(R.string.create_a_family);
        } else {
            menus = new String[1];
            menus[0] = getResources().getString(R.string.create_a_family);
        }

        final String[] localMenus = new String[menus.length];
        for (int i = 0; i < menus.length; i++) {
            localMenus[i] = Local.s(menus[i]);
        }

        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(localMenus)
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        final String currentItem = menus[index];
                        if (currentItem.equals(getResources().getString(R.string.join_a_family))) {
                            ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_FAMILY);
                        } else if (currentItem.equals(getResources().getString(R.string.create_a_family))) {
                            ArrayList<String> familyNameList = new ArrayList<>();
                            for (FamilyListData family : mData) {
                                if (family.isHeader() || family.isBottom()) {
                                    continue;
                                }
                                familyNameList.add(family.getName());
                            }
                            getMainActivity().addCommonFragment(CreateFamilyFragment.newInstance(familyNameList));
                        }
                    }
                }).show();
    }


    private void changeHomeName(final int index, final String name) {
        DinSDK.getHomeInstance().reNameHome(mData.get(index).getId(),
                name, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        mData.get(index).setName(name);
                        listAdapter.notifyDataSetChanged();
//                    更新主页的设备列表
                        EventBus.getDefault().post(new FamilyListChangeEvent());
                        closeLoadingFragment();
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                    }
                });
    }


    private void forceDeleteDevice(final int index) {
        showTimeOutLoadinFramgment();
        HomeManager.getInstance().forceDeleteFamily(mData.get(index).getId(), new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }

            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (i == ErrorCode.ERROR_DELETE_FAMILY_OTHER_IN_FAMILY) {
                    DDLog.e(TAG, "还有其他用户，不能删除家庭");
                    showToast(getString(R.string.has_another_family_member));
                    return;
                } else if (i == ErrorCode.ERROR_DELETE_FAMILY_ONLY_FAMILY) {
                    DDLog.e(TAG, "只有一个家庭，不能删除家庭");
                    showToast(getString(R.string.only_one_family_hint));
                    return;
                }
                showErrorToast();
            }
        });
    }

    @Override
    public boolean onBackPressed() {
        if (getArguments() != null && getArguments().getBoolean("isNotShowCurrentDevice")) {
            return true;
        }
        return super.onBackPressed();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }


    @Override
    public void onCurrentDeviceClick(final int i) {
        ActionSheet.createBuilder(getDelegateActivity(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.change_device_name_title))
                        , Local.s(getResources().getString(R.string.quit)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.colorDelete))
                .setCancelableOnTouchOutside(true)

                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            //                                修改名字
                            EdittextDialog.createBuilder(getDelegateActivity())
                                    .setOk(getResources().getString(R.string.Confirm))
                                    .setCancel(getResources().getString(R.string.Cancel))
                                    .setDefaultName(mData.get(i).getName())
                                    .setContent(getResources().getString(R.string.change_device_name_title))
                                    .setAutoDismiss(false)
                                    .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                                        @Override
                                        public void onOkClick(EdittextDialog dialog, String string) {
                                            if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                                                getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                                                dialog.dismiss();
                                                return;
                                            }

                                            changeHomeName(i, string);
                                            dialog.dismiss();
                                            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loading));
                                        }
                                    })
                                    .preBuilder()
                                    .show();
                        } else if (index == 1) {
                            requestCheckIsOnlyAdmin(i);
                        }
                    }
                }).show();
    }

    @Override
    public void onJoinButtonClick() {
        // TODO 处理加入Family按钮的点击事件
        DDLog.i(TAG, "onJoinButtonClick");
    }

    @Subscribe
    public void onEvent(FamilyListChangeEvent event) {
        DDLog.i(TAG, "onEvent-FamilyListChangeEvent");
        getHomeListData();
        listAdapter.notifyDataSetChanged();
    }

    @Subscribe
    public void onEvent(FamilySwitchEvent event) {
        DDLog.i(TAG, "onEvent-FamilySwitchEvent");
        getHomeListData();
        listAdapter.notifyDataSetChanged();
    }

    private void checkCloudStorageServiceExpiration(int index) {
        showTimeOutLoadinFramgment();
        DinsafeAPI.getApi().checkHomeServiceExpired()
                .enqueue(new Callback<GetServiceExpirationResponse>() {
                    @Override
                    public void onResponse(Call<GetServiceExpirationResponse> call, Response<GetServiceExpirationResponse> response) {
                        closeLoadingFragment();
                        if (response != null && response.body() != null && response.body().getStatus() == 1) {
                            if (!response.body().isService_expiration()) {
                                showAbandonmentCloudStorageServiceDialog(index);
                            } else {
                                forceDeleteDevice(index);
                            }
                        } else {
                            showErrorToast();
                        }
                    }

                    @Override
                    public void onFailure(Call<GetServiceExpirationResponse> call, Throwable t) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

    private void showAbandonmentCloudStorageServiceDialog(int index) {
        AlertDialogV2 alertDialogV2 = AlertDialogV2.createBuilder(getActivity())
                .setContent(this.getResources().getString(R.string.delete_family_with_service))
                .setOk(this.getResources().getString(R.string.cancel))
                .setOkV2(this.getResources().getString(R.string.transfer))
                .setCancel(this.getResources().getString(R.string.quit_and_discard))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(Dialog dialog) {
                        dialog.dismiss();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        //transfer
                        getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
                    }
                })
                .preBuilder();
        alertDialogV2.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialogV2.dismiss();
                showTimeOutLoadinFramgmentWithErrorAlert();
                //quit and discard
                forceDeleteDevice(index);
            }
        });
        alertDialogV2.show();
    }

    private void checkTrafficPackageServiceExpiration(int index) {
        showTimeOutLoadinFramgment();
        DinsafeAPI.getApi().bmtListProductSchedules("").enqueue(new Callback<TrafficPackageProductSchedulesResponse>() {
            @Override
            public void onResponse(Call<TrafficPackageProductSchedulesResponse> call, Response<TrafficPackageProductSchedulesResponse> response) {
                closeLoadingFragment();
                if (response != null && response.body() != null) {
                    List<TrafficPackageServiceCardItemModel> productSchedules = response.body().getResult().getList();
                    if (CollectionUtil.isListNotEmpty(productSchedules)) {
                        boolean hasNotExpired = false;
                        for (TrafficPackageServiceCardItemModel itemModel : productSchedules) {
                            int status = itemModel.getStatus();
                            if (status == 0 || status == 1) {
                                hasNotExpired = true;
                                break;
                            }
                        }
                        if (hasNotExpired) {
                            showAbandonmentTrafficPackageServiceDialog(index);
                        } else {
                            forceDeleteDevice(index);
                        }
                    } else {
                        forceDeleteDevice(index);
                    }
                } else {
                    showErrorToast();;
                }
            }

            @Override
            public void onFailure(Call<TrafficPackageProductSchedulesResponse> call, Throwable t) {
                closeLoadingFragment();
                showErrorToast();
            }
        });
    }

    private void showAbandonmentTrafficPackageServiceDialog(int index) {
        String content = Local.s(this.getResources().getString(R.string.delete_family_with_traffic_package_service))
                .replace(this.getResources().getString(R.string.hashtag_service),
                        Local.s(getString(R.string.iap_4g_traffic_package)));
        AlertDialogV2 alertDialogV2 = AlertDialogV2.createBuilder(getActivity())
                .setContent(content)
                .setOk(this.getResources().getString(R.string.cancel))
                .setCancel(this.getResources().getString(R.string.delete))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(Dialog dialog) {
                        dialog.dismiss();
                    }
                })
                .preBuilder();
        alertDialogV2.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialogV2.dismiss();
                showTimeOutLoadinFramgmentWithErrorAlert();
                //quit and discard
                forceDeleteDevice(index);
            }
        });
        alertDialogV2.show();
    }
}

