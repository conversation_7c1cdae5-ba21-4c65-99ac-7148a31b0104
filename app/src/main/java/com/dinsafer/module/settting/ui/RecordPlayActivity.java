package com.dinsafer.module.settting.ui;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.opengl.GLSurfaceView;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.daimajia.numberprogressbar.NumberProgressBar;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.IPCPlayPos;
import com.dinsafer.model.IPCStatueEvent;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import hsl.p2pipcam.activity.BridgeService;
import hsl.p2pipcam.activity.MyRender;
import hsl.p2pipcam.activity.RecorderListener;
import hsl.p2pipcam.marco.IPCMarco;
import hsl.p2pipcam.nativecaller.DeviceSDK;
import hsl.p2pipcam.util.AudioPlayer;
import hsl.p2pipcam.util.CustomBuffer;
import hsl.p2pipcam.util.CustomBufferData;
import hsl.p2pipcam.util.CustomBufferHead;

public class RecordPlayActivity extends Activity implements RecorderListener {

    GLSurfaceView glView;
    MyRender myRender;
    String fileName;
    int fileSize;

    AudioPlayer audioPlayer;
    CustomBuffer AudioBuffer;

    NumberProgressBar seekBar;
    NumberProgressBar seekBarFullScreen;
    int progress = 0;

    ProgressDialog progressDialog;


    ImageView volumeBtn;

    ImageView resume_pause;

    ImageView mFullScreen;

    ImageView mVolumeFullScreen;

    LinearLayout mFullSrceenControl;

    private long mUserID;

    private TextView nav_bar_name_textObj;

    boolean isSound = false;

    int currentVolume;

    boolean isPlaying = true;

    boolean isFullScreen = false;

    RelativeLayout commonBar;

    LinearLayout controlView;


    @Override
    protected void onDestroy() {
        super.onDestroy();
//        这个放在这里太慢了,所以选在提前
//        EventBus.getDefault().post(new CloseActivityEvent());
//        mUserID = getIntent().getExtras().getLong("userID");
//        glView = null;
//        myRender = null;
//        DeviceSDK.stopPlayRecord(mUserID, fileName);
//        audioPlayer.AudioPlayStop();
//        AudioBuffer.ClearAll();
//
//        BridgeService.resetSettingListener();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);//隐藏标题
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);//设置全屏
        setContentView(R.layout.activity_record_play);
        BridgeService.setRecorderListener(this);

        commonBar = (RelativeLayout) findViewById(R.id.common_bar);
        controlView = (LinearLayout) findViewById(R.id.control_view);
        mFullSrceenControl = (LinearLayout) findViewById(R.id.fullscreen_control);

        nav_bar_name_textObj = (TextView) findViewById(R.id.common_bar_title);
        nav_bar_name_textObj.setText(Local.s("Play Record"));

        glView = (GLSurfaceView) findViewById(R.id.glview);
        myRender = new MyRender(glView);
        glView.setRenderer(myRender);

        Bundle bundle = getIntent().getExtras();
        fileName = bundle.getString("fileName");
        fileSize = bundle.getInt("fileSize");
        mUserID = bundle.getLong("userID");
//        mAudioManager = (AudioManager) this.getSystemService(Context.AUDIO_SERVICE);
//        currentVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
//        mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 0, 0);


        //后退按钮
        ImageView backBtn = (ImageView) findViewById(R.id.common_bar_back);
        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EventBus.getDefault().post(new CloseActivityEvent());
                finish();
            }
        });

        resume_pause = (ImageView) findViewById(R.id.resume_pause);
        mVolumeFullScreen = (ImageView) findViewById(R.id.volume_fullscreen);
        mVolumeFullScreen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isSound) {
                    audioPlayer.AudioPlayStop();
//                    mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 0, 0);
                    mVolumeFullScreen.setImageResource(R.drawable.icon_ipc_video_sounds);
                    volumeBtn.setImageResource(R.drawable.icon_ipc_video_sounds);
                } else {
                    audioPlayer.AudioPlayStart();
//                    mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, currentVolume, 0);
                    mVolumeFullScreen.setImageResource(R.drawable.icon_ipc_video_mute);
                    volumeBtn.setImageResource(R.drawable.icon_ipc_video_mute);
                }

                isSound = !isSound;
            }
        });
        resume_pause.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!isPlaying) {
                    isPlaying = true;
                    DeviceSDK.startPlayRecord(mUserID, fileName, progress);
//                    audioPlayer.AudioPlayStart();
                    resume_pause.setVisibility(View.GONE);
                }
            }
        });


        mFullScreen = (ImageView) findViewById(R.id.glview_fullscreen);
        mFullScreen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                if (!isFullScreen) {
                    if (getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
                        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                    }
                    isFullScreen = true;

                    RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mFullScreen.getLayoutParams();
                    layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE);
                    layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, 0);
                    commonBar.setVisibility(View.GONE);
                    seekBar.setVisibility(View.GONE);
                    volumeBtn.setVisibility(View.GONE);
                    controlView.setVisibility(View.GONE);
                    resume_pause.setImageResource(R.drawable.icon_ipc_play_fullscreen);
                    mFullSrceenControl.setVisibility(View.VISIBLE);
                    mFullScreen.setImageResource(R.drawable.icon_ipc_small_screen);
                } else {
                    isFullScreen = false;
                    if (getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT) {
                        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
                    }
                    RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mFullScreen.getLayoutParams();
                    layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
                    layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP, 0);
                    commonBar.setVisibility(View.VISIBLE);
                    seekBar.setVisibility(View.VISIBLE);
                    volumeBtn.setVisibility(View.VISIBLE);
                    controlView.setVisibility(View.VISIBLE);
                    mFullSrceenControl.setVisibility(View.GONE);
                    resume_pause.setImageResource(R.drawable.icon_ipc_static);
                    mFullScreen.setImageResource(R.drawable.icon_ipc_full_screen);
                }
            }
        });


        glView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isPlaying) {
                    isPlaying = false;
                    int pauseInt = DeviceSDK.stopPlayRecord(mUserID, fileName);
                    audioPlayer.AudioPlayStop();
                    AudioBuffer.ClearAll();
                    resume_pause.setVisibility(View.VISIBLE);
                }
            }
        });


        //暂停/继续 播放按钮
        volumeBtn = (ImageView) findViewById(R.id.resume_pause_btn);
        volumeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                if (isSound) {
                    audioPlayer.AudioPlayStop();
//                    mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 0, 0);
                    volumeBtn.setImageResource(R.drawable.icon_ipc_video_mute);
                    mVolumeFullScreen.setImageResource(R.drawable.icon_ipc_video_mute);
                } else {
                    audioPlayer.AudioPlayStart();
//                    mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, currentVolume, 0);
                    volumeBtn.setImageResource(R.drawable.icon_ipc_video_sounds);
                    mVolumeFullScreen.setImageResource(R.drawable.icon_ipc_video_sounds);
                }

                isSound = !isSound;
            }
        });

        volumeBtn.setImageResource(R.drawable.icon_ipc_video_mute);
        mVolumeFullScreen.setImageResource(R.drawable.icon_ipc_video_mute);

        seekBar = (NumberProgressBar) findViewById(R.id.seekBar);
        seekBarFullScreen = (NumberProgressBar) findViewById(R.id.seekBar_fullscreen);
        seekBar.setMax(fileSize);
        seekBar.setReachedBarColor(getResources().getColor(R.color.text_blue_1));
        seekBarFullScreen.setMax(fileSize);
        seekBar.setProgressTextVisibility(NumberProgressBar.ProgressTextVisibility.Invisible);
        seekBarFullScreen.setProgressTextVisibility(NumberProgressBar.ProgressTextVisibility.Invisible);
        seekBarFullScreen.setReachedBarColor(getResources().getColor(R.color.text_blue_1));
        //开始播放
        AudioBuffer = new CustomBuffer();
        audioPlayer = new AudioPlayer(AudioBuffer);

        DeviceSDK.setRecordRender(mUserID, myRender);
        int startInt = DeviceSDK.startPlayRecord(mUserID, fileName, 0);

//        audioPlayer.AudioPlayStart();


    }

    @Override
    public void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        EventBus.getDefault().post(new CloseActivityEvent());
        mUserID = getIntent().getExtras().getLong("userID");
        glView = null;
        myRender = null;
        DeviceSDK.stopPlayRecord(mUserID, fileName);
        audioPlayer.AudioPlayStop();
        AudioBuffer.ClearAll();


//        BridgeService.resetSettingListener();
        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCStatueEvent event) {
        int type = event.getType();
        if (type == IPCMarco.DeviceStatus.PPPP_STATUS_DISCONNECT) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    progressDialog = ProgressDialog.show(RecordPlayActivity.this, null, Local.s(getResources().getString(R.string.loading)), true, false);
                }
            });
        } else if (type == IPCMarco.DeviceStatus.PPPP_STATUS_ON_LINE) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (progressDialog != null) {
                        progressDialog.dismiss();
                        progressDialog = null;
                    }
//                    NativeCaller.StopPlayRecord(mUserID, fileName);
                    DeviceSDK.stopPlayRecord(mUserID, fileName);
                    AudioBuffer.ClearAll();
                    audioPlayer.AudioPlayStop();
                    progress = 0;
                    seekBar.setProgress(progress);
                    seekBarFullScreen.setProgress(progress);
                    resume_pause.setVisibility(View.VISIBLE);
                    resume_pause.setClickable(true);
                    isPlaying = false;
                    DeviceSDK.stopPlayStream(mUserID);
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCPlayPos event) {
        progress = event.getPos();
        Log.e("", "pos:=" + event.getPos() + ",size:" + fileSize);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                seekBar.setProgress(progress);
                seekBarFullScreen.setProgress(progress);
            }
        });
        if (progress * 1f / fileSize >= 0.85) {
//            Log.e("","CallBack_RecordPlayPos done");
//            timer.cancel();
//            NativeCaller.StopPlayRecord(mUserID, fileName);
//            AudioBuffer.ClearAll();
//            audioPlayer.AudioPlayStop();
//            progress = 0;
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    DeviceSDK.stopPlayRecord(mUserID, fileName);
                    AudioBuffer.ClearAll();
                    audioPlayer.AudioPlayStop();
                    progress = 0;
                    seekBar.setProgress(progress);
                    seekBarFullScreen.setProgress(progress);
                    resume_pause.setVisibility(View.VISIBLE);
                    resume_pause.setClickable(true);
                    isPlaying = false;
                    DeviceSDK.stopPlayStream(mUserID);
                }
            });
        }
    }


    @Override
    public void callBackAudioData(long nUserID, byte[] pcm, int size) {
        CustomBufferHead head = new CustomBufferHead();
        CustomBufferData data = new CustomBufferData();
        head.length = size;
        head.startcode = 0xff00ff;
        data.head = head;
        data.data = pcm;
        AudioBuffer.addData(data);
    }

    @Override
    public void callBack_RecordPlayPos(long userid, int pos) {
//        Log.i("123","callBack_RecordPlayPos");
        progress = pos;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                seekBar.setProgress(progress);
                seekBarFullScreen.setProgress(progress);
            }
        });
        if (progress * 1f / fileSize >= 0.85) {
//            Log.e("","CallBack_RecordPlayPos done");
//            timer.cancel();
//            NativeCaller.StopPlayRecord(mUserID, fileName);
//            AudioBuffer.ClearAll();
//            audioPlayer.AudioPlayStop();
//            progress = 0;
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    DeviceSDK.stopPlayRecord(mUserID, fileName);
                    AudioBuffer.ClearAll();
                    audioPlayer.AudioPlayStop();
                    progress = 0;
                    seekBar.setProgress(progress);
                    seekBarFullScreen.setProgress(progress);
                    resume_pause.setVisibility(View.VISIBLE);
                    resume_pause.setClickable(true);
                    isPlaying = false;
                    DeviceSDK.stopPlayStream(mUserID);
                }
            });
        }
    }


    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
// Nothing need to be done here
        } else {
// Nothing need to be done here
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_record_play, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == R.id.action_settings) {
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

}