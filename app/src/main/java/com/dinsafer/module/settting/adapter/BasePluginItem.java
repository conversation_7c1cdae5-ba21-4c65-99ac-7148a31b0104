package com.dinsafer.module.settting.adapter;

import android.widget.BaseAdapter;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.APIKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.PlugsData;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.Local;

import org.json.JSONObject;

/**
 * 配件列表适配器基类
 * 提供获取电量状态图标ID和描述和判断配件是否离线的方法
 *
 * <AUTHOR>
 * @date 2020/8/19 3:33 PM
 */
public abstract class BasePluginItem extends BaseAdapter {
    /**
     * 获取电量百分比的展示图标
     *
     * @param batteryLevel 电量百分比
     * @return 电量百分比图标id
     */
    protected int getBatteryLevelIconId(int batteryLevel) {
        int iconResId;
        if (batteryLevel > 60) {
            iconResId = R.drawable.icon_plugin_list_battery_full;
        } else if (batteryLevel > 20) {
            iconResId = R.drawable.icon_plugin_list_battery_half;
        } else {
            iconResId = R.drawable.icon_plugin_list_battery_low;
        }
        return iconResId;
    }

    /**
     * 设备是否是离线
     *
     * @return true 设备离线
     */
    protected boolean isPluginOffline(JSONObject askData) {
        return (DDJSONUtil.has(askData, "keeplive")
                && !DDJSONUtil.getBoolean(askData, "keeplive")
                && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE
                && !DDJSONUtil.getString(askData, "stype").equals("1C"));
    }

    protected int getBlock(JSONObject askData) {
        return DDJSONUtil.getInt(askData, "block", 0);
    }

    /**
     * 获取低电状态的icon id
     *
     * @return 0 表示正常，不是低电状态
     */
    protected int getLowBatteryIconId(JSONObject askData) {
        int iconId = 0;
        if (askData != null) {
            // 1.1 包含低电信息
            if (DDJSONUtil.has(askData, "keeplive")
                    && !DDJSONUtil.getBoolean(askData, "keeplive")
                    && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE
                    && !DDJSONUtil.getString(askData, "stype").equals("1C")) {
                // 1C为C106产品，没有心跳提示的。
                // 离线
                iconId = R.drawable.icon_plugin_list_offline;
            } else if (DDJSONUtil.has(askData, "power")
                    && !DDJSONUtil.getBoolean(askData, "power")) {
                // 低电
                iconId = R.drawable.icon_plugin_list_battery_low;
            }
        }
        return iconId;
    }

    /**
     * 获取低电状态描述文本的id
     *
     * @return 0 表示正常，不是低电状态
     */
    protected int getLowBatterContentId(JSONObject askData) {
        int contentId = 0;
        if (askData != null) {
            if (DDJSONUtil.has(askData, "keeplive")
                    && !DDJSONUtil.getBoolean(askData, "keeplive")
                    && APIKey.IS_SHOW_PLUGIN_NO_RESPONSE
                    && !DDJSONUtil.getString(askData, "stype").equals("1C")) {
                // 1C为C106产品，没有心跳提示的。
                // 离线
                contentId = R.string.Offline;
            } else if (DDJSONUtil.has(askData, "power")
                    && !DDJSONUtil.getBoolean(askData, "power")) {
                // 低电
                contentId = R.string.item_plugin_low_battary;
            }
        }
        return contentId;
    }

    /**
     * 主机离线时，更新受影响的Item的视图内容
     *
     * @param panelDeviceOffline 主机状态
     */
    protected void updatePluginItemByPanelDevice(boolean panelDeviceOffline, PlugsData plugsData
            , String pluginName, ItemPluginHolder itemHolder) {
        if (panelDeviceOffline) {
            String firstContent = Local.s(DinSaferApplication.getAppContext().getResources()
                    .getString(R.string.Unknown));
            int firstIconId = R.drawable.icon_plugin_list_offline;
            // 仅显示配件名和离线图标，不显示其他的状态图标
            itemHolder.showStatusFinished(pluginName, -1, firstContent, firstIconId,
                    null, 0, null, 0);
            // 置灰，不可点击进入
            itemHolder.changeItemEnable(false);
            return;
        }
        itemHolder.changeItemEnable(true);
        updatePluginItem(plugsData, pluginName, itemHolder);
    }

    /**
     * 更新Item的视图内容
     *
     * @param pluginName 可能是name可能是description
     */
    protected void updatePluginItem(PlugsData plugsData, String pluginName, ItemPluginHolder itemHolder) {
        int pluginSignalLevel = plugsData.isHasSignalLevel() ? plugsData.getSignalLevel() : -1;

        // 1、离线直接显示离线状态
        if (isPluginOffline(plugsData.getAskData())) {
            String firstContent = Local.s(DinSaferApplication.getAppContext().getResources()
                    .getString(getLowBatterContentId(plugsData.getAskData())));
            int firstIconId = getLowBatteryIconId(plugsData.getAskData());
            // 仅显示配件名和离线图标，不显示其他的状态图标
            itemHolder.showStatusFinished(pluginName, -1, firstContent, firstIconId,
                    null, 0, null, 0);
            return;
        }

        // 2、没有通过websocket返回的数据，直接显示文本内容
        if (!plugsData.isHasWebsocketLoading()) {
            int firstIconId = getLowBatteryIconId(plugsData.getAskData());
            int firstContentId = getLowBatterContentId(plugsData.getAskData());

            if (0 == firstContentId) {
                // 正常
                itemHolder.showStatusFinished(pluginName, pluginSignalLevel);
            } else {
                // 没有具体电量值的低电标记
                itemHolder.showStatusFinished(pluginName, pluginSignalLevel,
                        Local.s(DinSaferApplication.getAppContext().getResources().getString(firstContentId))
                        , firstIconId);
            }
        } else {

            // 3、有通过websocket返回的状态，分加载中和加载完成
            // 如果配件离线，不显示其他状态图标，仅显示离线。
            // 在线有电量值再显示电量值，没有电量值有低电状态显示低电状态
            if (plugsData.isLoadingView()) {
                // 3.1 加载中
                itemHolder.showStatusLoading(pluginName);
            } else {
                // 3.2 加载出错，直接显示加载出错的提示内容
                if (plugsData.isLoadStatusError()) {
                    if (DDJSONUtil.has(plugsData.getAskData(), "keeplive")
                            && DDJSONUtil.getBoolean(plugsData.getAskData(), "keeplive")) {
                        // 如果服务器返回的数据是在线的，仅显示名字
                        itemHolder.showStatusFinished(pluginName, -1);
                    } else {
                        String firstContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.Offline));
                        int firstIconId = R.drawable.icon_plugin_list_offline;
                        // 仅显示配件名和离线图标，不显示其他的状态图标
                        itemHolder.showStatusFinished(pluginName, -1, firstContent, firstIconId,
                                null, 0, null, 0);
                    }
                    return;
                }

                // 3.3 加载成功
                String firstContent = null, secondContent = null, thirdContent = null;
                int firstIconId = 0, secondIconId = 0, thirdIconId = 0;

                // 第一个状态图标及文本:优先级别 离线>充电>电量百分比>低电>正常
                if (plugsData.isCanCharging()
                        && plugsData.isCharging()) {
                    // a 包含充电状态
                    firstIconId = R.drawable.icon_plugin_list_battery_ac;
                    firstContent = plugsData.isCanCharging()
                            ? Local.s(DinSaferApplication.getAppContext().getResources()
                            .getString(R.string.item_plugin_status_charging))
                            : "100%";
                } else {
                    // b 不包含充电状态
                    if (plugsData.isHasBatteryLevel()) {
                        // b.a 有电量百分比
                        int battery = plugsData.getBatteryLevel();
                        firstIconId = getBatteryLevelIconId(battery);
                        firstContent = battery + "%";
                    } else {
                        // b.b 无电量百分比
                        firstIconId = getLowBatteryIconId(plugsData.getAskData());
                        firstContent =
                                0 == firstIconId
                                        ? null
                                        : Local.s(DinSaferApplication.getAppContext().getResources()
                                        .getString(getLowBatterContentId(plugsData.getAskData())));
                    }
                }


                int block = getBlock(plugsData.getAskData());
                // 第二个状态图标及文本
                boolean haveThreeIcon = false; // 是否有第三个图标
                if (block == 2) {
                    secondIconId = R.drawable.icon_plugin_list_blocked;
                    secondContent = Local.s(DinSaferApplication.getAppContext().getResources()
                            .getString(R.string.door_sensor_item_block));
                } else if (plugsData.isCanReadyToArm()) {
                    if (plugsData.isEnable()) {
                        secondIconId = R.drawable.icon_plugin_list_apart;
                        secondContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.door_sensor_item_open));
                    } else {
                        secondIconId = R.drawable.icon_plugin_list_close;
                        secondContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.door_sensor_close));
                    }
                    haveThreeIcon = true;
                } else if (block == 1) {
                    secondIconId = R.drawable.icon_plugin_list_tamper_enabled;
                    secondContent = Local.s(DinSaferApplication.getAppContext().getResources()
                            .getString(R.string.item_plugin_status_tamper_enabled));
                } else if (plugsData.isCanTamper()) {
                    if (plugsData.isTamper()) {
                        secondIconId = R.drawable.icon_plugin_list_released;
                        secondContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.item_plugin_status_released));
                    } else {
                        secondIconId = R.drawable.icon_plugin_list_pressed;
                        secondContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.item_plugin_status_pressed));
                    }
                }
                //第三个状态图标及文本
                if (haveThreeIcon
                        && plugsData.isCanTamper()) {
                    if (block == 1) {
                        thirdIconId = R.drawable.icon_plugin_list_tamper_enabled;
                        thirdContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.item_plugin_status_tamper_enabled));
                    } else if (plugsData.isTamper()) {
                        thirdIconId = R.drawable.icon_plugin_list_released;
                        thirdContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.item_plugin_status_released));
                    } else {
                        thirdIconId = R.drawable.icon_plugin_list_pressed;
                        thirdContent = Local.s(DinSaferApplication.getAppContext().getResources()
                                .getString(R.string.item_plugin_status_pressed));
                    }
                }


                itemHolder.showStatusFinished(pluginName, pluginSignalLevel, firstContent, firstIconId,
                        secondContent, secondIconId, thirdContent, thirdIconId);
            }
        }
    }

}
