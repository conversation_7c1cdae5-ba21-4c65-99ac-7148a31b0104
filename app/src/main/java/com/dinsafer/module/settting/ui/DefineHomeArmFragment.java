package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.config.APIKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.AddAccessoryEvent;
import com.dinsafer.model.DeviceResultEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.adapter.CustomizeHomeArmAdapter;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.PinnedHeaderListView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rinfon on 16/6/30.
 */
public class DefineHomeArmFragment extends BaseFragment implements IDeviceCallBack {

    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    LocalTextView definehomearmDescription;
    PinnedHeaderListView definehomearListview;
    LocalCustomButton definehomearmConfirm;
    TextView entryDelayText;
    RelativeLayout entryDelayTime;
    LocalTextView definehomearmHint;

    private CustomizeHomeArmAdapter mAdapter;

    private HashMap<Integer, ArrayList<Map<String, Object>>> mAllData;
    private final ArrayList<Map<String, Object>> mOfficalData = new ArrayList<>();
    private final ArrayList<Map<String, Object>> mThirdPartyData = new ArrayList<>();

    private boolean isShowEntryDelay;
    private int mEntryDelayTime = -1;
    private boolean entryDelaySoundEnable;

    private Device mPanelDevice;
    private final Handler mTimeoutHandler = new Handler(Looper.getMainLooper());
    private boolean isSelfOperate;

    public static DefineHomeArmFragment newInstance(boolean isShowEntryDelay) {
        DefineHomeArmFragment defineHomeArmFragment = new DefineHomeArmFragment();
        Bundle args = new Bundle();
        args.putBoolean("isShowEntryDelay", isShowEntryDelay);
        defineHomeArmFragment.setArguments(args);
        return defineHomeArmFragment;
    }

    @Override
    public void initData() {
        super.initData();
        definehomearListview.setPinHeaders(false);
        if (isShowEntryDelay) {
            commonBarTitle.setLocalText(getResources().getString(R.string.advanced_setting_entry_delay));
            definehomearmDescription.setLocalText(getResources().getString(R.string.definehomearm_description));
            definehomearmHint.setLocalText(getResources().getString(R.string.homearm_hint_2));
            definehomearmHint.setVisibility(View.VISIBLE);
        } else {
            commonBarTitle.setLocalText(getResources().getString(R.string.device_managent_home_arm));
            definehomearmDescription.setLocalText(getResources().getString(R.string.homearm_empty_hint));
            definehomearmHint.setVisibility(View.GONE);
        }
        definehomearmConfirm.setLocalText(getResources().getString(R.string.definehomearm_confirm));
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        showLoadingFragment(LoadingFragment.BLUE);
        View rootView = inflater.inflate(R.layout.definehomearm_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        isShowEntryDelay = getArguments().getBoolean("isShowEntryDelay");
        findPanel();
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toBack());
        rootView.findViewById(R.id.entry_delay_time).setOnClickListener( v -> toTimerPicker());
        rootView.findViewById(R.id.definehomearm_confirm).setOnClickListener( v -> toSave());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        definehomearmDescription = rootView.findViewById(R.id.definehomearm_description);
        definehomearListview = rootView.findViewById(R.id.definehomear_listview);
        definehomearmConfirm = rootView.findViewById(R.id.definehomearm_confirm);
        entryDelayText = rootView.findViewById(R.id.entry_delay_text);
        entryDelayTime = rootView.findViewById(R.id.entry_delay_time);
        definehomearmHint = rootView.findViewById(R.id.definehomearm_hint);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeLoadingFragment();
            showErrorToast();
            removeSelf();
            return;
        }

        createPlugsList();
    }

    /**
     * 初始化plugg数据列表
     */
    private void createPlugsList() {
        mAllData = new HashMap<>();
        mAdapter = new CustomizeHomeArmAdapter(getMainActivity(), mAllData, APIKey.IS_OPEN_OTHER_PLUGIN);
        definehomearListview.setAdapter(mAdapter);
        View footer = new View(this.getActivity());
        AbsListView.LayoutParams params = new AbsListView.LayoutParams(1, getResources().getDimensionPixelOffset(R.dimen.definehomearm_confirm_layout_height));
        footer.setLayoutParams(params);
        definehomearListview.addFooterView(footer);

        if (isShowEntryDelay) {
            getEntryDelayData();
        } else {
            getHomeArmData();
        }
    }

    private void getHomeArmData() {
        entryDelayTime.setVisibility(View.GONE);
        mPanelDevice.submit(PanelParamsHelper.getHomeArmInfo());
    }

    private void getEntryDelayData() {
        entryDelayTime.setVisibility(View.VISIBLE);
        mPanelDevice.submit(PanelParamsHelper.getEntryDelay());
    }

    private void setDelayTime(int seconds) {
        if (seconds <= 0) {
            entryDelayText.setText(Local.s(getResources().getString(R.string.no_delay)));
        } else if (seconds > 0 && seconds <= 60) {
            entryDelayText.setText(seconds + "s");
        } else {
            int min = seconds / 60;
            int second = seconds % 60;
            entryDelayText.setText(min + "min " + second + "s");
        }
    }

    public void toBack() {
        removeSelf();
    }

    private int defaultIndex = 0;

    public void toTimerPicker() {
        TimePicker2 picker2 = TimePicker2.newInstance(getResources().getString(R.string.advanced_setting_entry_delay),
                mEntryDelayTime, entryDelaySoundEnable);
        picker2.setCallBack(new TimePicker2.ITimePickerCallBack() {
            @Override
            public void getSelect(int seconds, boolean isSoundEnable) {
                setDelayTime(seconds);
                mEntryDelayTime = seconds;
                entryDelaySoundEnable = isSoundEnable;
            }
        });

        getDelegateActivity().addCommonFragment(picker2);
    }

    public void toSave() {
        if (mAllData.size() <= 0 && !isShowEntryDelay && !APIKey.IS_DINSAFER) {
            EventBus.getDefault().post(new AddAccessoryEvent());
            return;
        }
        try {
            showLoadingFragment(LoadingFragment.BLACK);
            JSONArray jsonArray = new JSONArray();
            JSONArray thirdPartyjsonArray = new JSONArray();
            JSONArray askjsonArray = new JSONArray();
            String key = "homearmenable";
            if (isShowEntryDelay) {
                key = "entrydelayenable";
            }
            String pluginId;
            boolean isOpened;
            JSONObject askData;
            for (int i = 0; i < mOfficalData.size(); i++) {
                pluginId = DeviceHelper.getString(mOfficalData.get(i), PanelDataKey.ID, "");
                isOpened = PanelConstant.PluginSwitchState.OPENED
                        == DeviceHelper.getInt(mOfficalData.get(i), PanelDataKey.PLUGIN_SWITCH_STATE, 0);
                askData = DeviceHelper.getJsonObject(mOfficalData.get(i), PanelDataKey.ASK_DATA);
                JSONObject jsonObject = new JSONObject();
                if (askData == null) {
                    jsonObject.put("id", pluginId);
                    jsonObject.put(key, isOpened);
                    jsonArray.put(jsonObject);
                } else {
                    jsonObject.put("sendid", DDJSONUtil.getString(askData, "sendid"));
                    jsonObject.put("stype", DDJSONUtil.getString(askData, "stype"));
                    jsonObject.put(key, isOpened);
                    askjsonArray.put(jsonObject);
                }
            }

            for (int i = 0; i < mThirdPartyData.size(); i++) {
                pluginId = DeviceHelper.getString(mThirdPartyData.get(i), PanelDataKey.ID, "");
                isOpened = PanelConstant.PluginSwitchState.OPENED
                        == DeviceHelper.getInt(mThirdPartyData.get(i), PanelDataKey.PLUGIN_SWITCH_STATE, 0);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", pluginId);
                jsonObject.put(key, isOpened);
                thirdPartyjsonArray.put(jsonObject);
            }

            isSelfOperate = true;
            if (isShowEntryDelay) {
                mPanelDevice.submit(PanelParamsHelper.setEntryDelay(mEntryDelayTime, entryDelaySoundEnable,
                        jsonArray.toString(), askjsonArray.toString(), thirdPartyjsonArray.toString()));
            } else {
                mPanelDevice.submit(PanelParamsHelper.setHomeArmInfo(jsonArray.toString(),
                        askjsonArray.toString(), thirdPartyjsonArray.toString()));
            }
            mTimeoutHandler.postDelayed(() -> {
                if (DefineHomeArmFragment.this.isAdded()) {
                    closeLoadingFragment();
                    showErrorToast();
                }
            }, LocalKey.TIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
            closeLoadingFragment();
            showErrorToast();
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceResultEvent ev) {
        if (LocalKey.ADD_PLUGIN.equals(ev.getCmdType())) {
            if (ev.getStatus() == 1) {
                showLoadingFragment(LoadingFragment.BLUE);
                getHomeArmData();
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mTimeoutHandler.removeCallbacksAndMessages(null);
        EventBus.getDefault().unregister(this);
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        Map<String, Object> resultMap;

        switch (cmd) {
            case PanelCmd.GET_HOMEARM_INFO:
                DDLog.i(TAG, "Customize home arm");
                resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
                onGetHomeArmInfo(status, resultMap);
                closeLoadingFragment();
                break;
            case PanelCmd.GET_ENTRYDELAY:
                DDLog.i(TAG, "Entry Delay");
                resultMap = DeviceHelper.getMap(map, PanelDataKey.CmdResult.RESULT);
                onGetEntryDelay(status, resultMap);
                closeLoadingFragment();
                break;
            case PanelCmd.SET_ENTRYDELAY:
            case PanelCmd.SET_HOMEARM_INFO:
                DDLog.i(TAG, "SET Customize home arm or entry delay.");
                onSetHomeArmInfo(status, resultType);
                break;
        }
    }

    /**
     * 延时报警
     *
     * @param status
     * @param result
     */
    private void onGetEntryDelay(int status, Map<String, Object> result) {
        DDLog.i(TAG, "onGetEntryDelay, status: " + status + ", result: " + result);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            definehomearmDescription.setLocalText(getResources().getString(R.string.definehomearm_description));
            if (mAllData == null || mAllData.size() <= 0) {
                definehomearmConfirm.setVisibility(View.VISIBLE);
                definehomearmDescription.setLocalText(getResources().getString(R.string.definehomearm_description));
            } else {
                definehomearmConfirm.setVisibility(View.VISIBLE);
            }
            showErrorToast();
            return;
        }

        initListData(result);
        mEntryDelayTime = DeviceHelper.getInt(result, PanelDataKey.TIME_COMMON, 0);
        entryDelaySoundEnable = DeviceHelper.getBoolean(result, PanelDataKey.ENTRY_DELAY_SOUND, false);
        setDelayTime(mEntryDelayTime);

        definehomearmConfirm.setVisibility(View.VISIBLE);
        definehomearmDescription.setLocalText(getResources().getString(R.string.definehomearm_description));
    }


    /**
     * 获取定制在家布防的数据
     */
    private void onSetHomeArmInfo(int status, int resultType) {
        DDLog.i(TAG, "onSetHomeArmInfo, status: " + status + ", resultType: " + resultType);
        if (1 != resultType || !isSelfOperate) {
            return;
        }

        // result
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            isSelfOperate = false;
            closeLoadingFragment();
            showErrorToast();
            return;
        }

        isSelfOperate = false;
        closeLoadingFragment();
        showSuccess();
        removeSelf();

//        DeviceInfoHelper.getInstance().getCurrentDeviceInfo().setHas_homearm_set(true);
//        CommonDataUtil.getInstance().saveMultiData();
    }

    /**
     * 获取定制在家布防的数据
     */
    private void onGetHomeArmInfo(int status, Map<String, Object> result) {
        DDLog.i(TAG, "onGetHomeArmInfo, status: " + status + ", result: " + result);
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            if (mAllData == null || mAllData.size() <= 0) {
                if (APIKey.IS_DINSAFER)
                    definehomearmConfirm.setVisibility(View.GONE);
                else
                    definehomearmConfirm.setLocalText(getResources().getString(R.string.home_arm_add_plugs));
                definehomearmDescription.setLocalText(getResources().getString(R.string.homearm_empty_hint));
            } else {
                definehomearmConfirm.setVisibility(View.VISIBLE);
                definehomearmConfirm.setLocalText(getResources().getString(R.string.definehomearm_confirm));
                if (isShowEntryDelay) {
                    definehomearmDescription.setLocalText(getResources().getString(R.string.definehomearm_description));
                } else {
                    definehomearmDescription.setLocalText(getResources().getString(R.string.customize_home_arem_description));
                }
            }
            showErrorToast();
            return;
        }

        initListData(result);

        if (mAllData == null || mAllData.size() <= 0) {
            if (APIKey.IS_DINSAFER)
                definehomearmConfirm.setVisibility(View.GONE);
            else
                definehomearmConfirm.setLocalText(getResources().getString(R.string.home_arm_add_plugs));
            definehomearmDescription.setLocalText(getResources().getString(R.string.homearm_empty_hint));
        } else {
            definehomearmConfirm.setVisibility(View.VISIBLE);
            definehomearmConfirm.setLocalText(getResources().getString(R.string.definehomearm_confirm));
            if (isShowEntryDelay) {
                definehomearmDescription.setLocalText(getResources().getString(R.string.definehomearm_description));
            } else {
                definehomearmDescription.setLocalText(getResources().getString(R.string.customize_home_arem_description));
            }
        }
    }

    /**
     * 初始化列表数据
     */
    private void initListData(Map<String, Object> result) {
        mAllData.clear();
        mOfficalData.clear();
        mThirdPartyData.clear();
        if (DefineHomeArmFragment.this.isAdded()) {
            Map<String, Object> pluginMap;
            int pirIndex = 0;
            List<Map<String, Object>> pluginList;
            pluginList = DeviceHelper.getList(result, PanelDataKey.PLUGINS);
            if (pluginList.size() > 0) {
                for (int i = 0; i < pluginList.size(); i++) {
                    pluginMap = pluginList.get(i);
                    if ("09".equals(DeviceHelper.getString(pluginMap, PanelDataKey.SUBCATEGORY, ""))) {
                        //   确定有多少个红外，方便插入心跳红外
                        pirIndex++;
                    }
                }
                mOfficalData.addAll(pluginList);
            }

            //     add askplugin
            pluginList = DeviceHelper.getList(result, PanelDataKey.NEW_ASK_PLUGIN);
            if (pluginList.size() > 0) {
                for (int i = 0; i < pluginList.size(); i++) {
                    pluginMap = pluginList.get(i);
                    if ("09".equals(DeviceHelper.getString(pluginMap, PanelDataKey.SUBCATEGORY, ""))) {
                        mOfficalData.add(pirIndex, pluginMap);
                        pirIndex++;
                    } else {
                        mOfficalData.add(pluginMap);
                    }
                }
            }

            if (mOfficalData.size() > 0)
                mAllData.put(0, mOfficalData);

            pluginList = DeviceHelper.getList(result, PanelDataKey.THIRD_PARTY_PLUGINS);
            if (pluginList.size() > 0) {
                mThirdPartyData.addAll(pluginList);
            }

            if (mThirdPartyData.size() > 0) {
                if (mAllData.get(0) == null) {
                    mAllData.put(0, mThirdPartyData);
                } else {
                    mAllData.put(1, mThirdPartyData);
                }
            }
            mAdapter.notifyDataSetChanged();
        }
    }
}
