package com.dinsafer.module.settting.ui.model;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPermissionTypeBinding;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/10/14 3:03 下午
 */
public class PermissionItemTypeModel extends PermissionItemModel<ItemPermissionTypeBinding> {
    private final int type;

    public PermissionItemTypeModel(int type) {
        this.type = type;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_permission_type;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPermissionTypeBinding binding) {
        binding.ivPermissionType.setImageResource(getImageResId(type));
        binding.tvPermissionType.setLocalText(getPermissionTypeText(type));
    }

    private String getPermissionTypeText(int type) {
        int textId = R.string.change_permission_admin;
        if (LocalKey.GUEST == type) {
            textId = R.string.change_permission_guest;
        } else if (LocalKey.USER == type) {
            textId = R.string.change_permission_user;
        }

        return DinSaferApplication.getInstance().getApplicationContext().getString(textId);
    }

    private int getImageResId(int type) {
        if (LocalKey.GUEST == type) {
            return R.drawable.img_access_guest;
        } else if (LocalKey.USER == type) {
            return R.drawable.img_access_user;
        } else {
            return R.drawable.img_access_admin;
        }
    }
}
