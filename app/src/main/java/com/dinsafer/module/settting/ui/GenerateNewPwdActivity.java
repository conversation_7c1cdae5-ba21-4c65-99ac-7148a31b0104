package com.dinsafer.module.settting.ui;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.IPCEvent;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.GenerateImageUtils;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

import hsl.p2pipcam.marco.IPCMarco;
import hsl.p2pipcam.nativecaller.NativeCaller;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class GenerateNewPwdActivity extends Activity {

    Button navBarLeftBtn;
    TextView navBarNameText;
    LocalCustomButton navBarRightBtn;
    RelativeLayout navBar;
    ImageView shareQrImageview;
    TextView shareQrName;
    TextView shareQrNote;

    private Bundle generateBundle;

    private String newPwd, devID;
    private Long mUserId;

    private String newQR;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_generate_new_pwd);
        __bindViews();


        generateBundle = getIntent().getExtras();
        try {
            newQR = generateBundle.getString("newQR");
            Bitmap newPwdBitmap = GenerateImageUtils.createQRImage(newQR);
            shareQrImageview.setImageBitmap(newPwdBitmap);

        } catch (Exception e) {
        }
        try {
            newPwd = generateBundle.getString("pwd");
        } catch (Exception e) {
            newPwd = null;
        }
        try {
            devID = generateBundle.getString("devID");
        } catch (Exception e) {
            devID = null;
        }

        try {
            mUserId = generateBundle.getLong("userID");
        } catch (Exception e) {
            mUserId = null;
        }

        navBarRightBtn.setLocalText(getString(R.string.save));
        shareQrName.setText(generateBundle.getString("name"));
        shareQrNote.setText(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

//        navBarLeftBtn.setText(Local.s("Back"));
        navBarLeftBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

//        navBarRightBtn.setText(Local.s("Save"));
        navBarRightBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlertDialog alert = new AlertDialog.Builder(GenerateNewPwdActivity.this)
                        .setMessage(Local.s("Generate new QR code now?"))
                        .setNegativeButton("No", null)
                        .setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                if (generateBundle.getString("pwd") == null || "".equals(generateBundle.getString("pwd"))) {
                                    Toast.makeText(GenerateNewPwdActivity.this, Local.s("无法生成新密码!"), Toast.LENGTH_SHORT).show();
                                } else {
                                    RelativeLayout myReadCurrentPwdLinearLayout = (RelativeLayout) GenerateNewPwdActivity.this.findViewById(R.id.qr_layout);
                                    //              截取该view 的图像；
                                    Bitmap viewBitmap = GenerateImageUtils.captureView(myReadCurrentPwdLinearLayout);
                                    //              保存图片；
                                    GenerateImageUtils.saveImage(GenerateNewPwdActivity.this, viewBitmap);
                                }


                                /**
                                 * 修改hsl密码；
                                 */
                                try {
                                    JSONObject userObj = new JSONObject();
                                    userObj.put("user3", "admin");
                                    userObj.put("pwd3", newPwd);
                                    NativeCaller.SetParam(mUserId, IPCMarco.Param.SET_PARAM_USERINFO, userObj.toString());
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                                Log.i("123", "yes1");
                                AlertDialog alert = new AlertDialog.Builder(GenerateNewPwdActivity.this)
                                        .setMessage(Local.s("Your smart camera's QRCode has been regenerated and saved into your album. smart camera will restart right now. Please visit it about 1 minute later."))
                                        .setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                                            @Override
                                            public void onClick(DialogInterface dialog, int which) {
//                                                Eventbus 退出IPCFragment;
                                                finish();
                                                IPCEvent ipcEvent = new IPCEvent();
                                                ipcEvent.setKey(newQR);
                                                EventBus.getDefault().post(ipcEvent);
                                                changeIPCPassword();
//                                                to tell server change password;

//                                                ((MainApplication) GenerateNewPwdActivity.this.getApplication()).exitIPCActivitys();
                                            }
                                        })
                                        .show();
                                TextView messageText = (TextView) alert.findViewById(android.R.id.message);
                                messageText.setGravity(Gravity.CENTER);
                            }
                        })
                        .show();
                TextView messageText = (TextView) alert.findViewById(android.R.id.message);
                messageText.setGravity(Gravity.CENTER);
            }
        });
    }

    private void __bindViews() {
        navBarLeftBtn = findViewById(R.id.nav_bar_left_btn);
        navBarNameText = findViewById(R.id.nav_bar_name_text);
        navBarRightBtn = findViewById(R.id.btn_save);
        navBar = findViewById(R.id.nav_bar);
        shareQrImageview = findViewById(R.id.share_qr_imageview);
        shareQrName = findViewById(R.id.share_qr_name);
        shareQrNote = findViewById(R.id.share_qr_note);
    }

    private void changeIPCPassword() {
        try {
            JSONObject jsonObject = new JSONObject(newQR);
            String id = jsonObject.getString("id");

            DinsafeAPI.getApi().getChangeIPCDataCall(id, CommonDataUtil.getInstance().getCurrentDeviceId(), newQR)
                    .enqueue(new Callback<StringResponseEntry>() {
                        @Override
                        public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {

                        }

                        @Override
                        public void onFailure(Call<StringResponseEntry> call, Throwable t) {

                        }
                    });
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().post(new CloseActivityEvent());
    }
}
