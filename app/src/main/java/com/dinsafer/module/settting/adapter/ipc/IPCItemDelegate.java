package com.dinsafer.module.settting.adapter.ipc;

import android.view.View;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.model.panel.MainPanelIpcItemViewHolder;
import com.dinsafer.module.ipc.player.CameraVideoView;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/6/28
 */
public interface IPCItemDelegate {
    void onBindItemViewHolder(MainPanelIpcItemViewHolder holder, int position, Device device);

    void onPlayIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent);

    void onErrorIconClick(Device device, int position, CameraVideoView videoViewRoot, View parent);

    void onFullscreenIconClick(Device device, int position, CameraVideoView videoView, View parent);
}
