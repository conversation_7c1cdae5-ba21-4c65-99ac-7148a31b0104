package com.dinsafer.module.settting.ui;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;


/**
 * Created by rinfon on 15/6/26.
 */
public class PhoneZoneDialog extends Dialog {

    int layoutRes;//布局文件

    Context mContext;

    LocalCustomButton mOk, mCancel;

    LocalTextView mInput;

    LocalTextView mContent;

    public PhoneZoneDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.phonezone_dialog;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mOk = (LocalCustomButton) view.findViewById(R.id.alert_dialog_ok);
        mCancel = (LocalCustomButton) view.findViewById(R.id.alert_dialog_cancel);
        mContent = (LocalTextView) view.findViewById(R.id.dialog_title);
        mInput = (LocalTextView) view.findViewById(R.id.phone_zone_input);
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.isAutoDismiss)
                    dismiss();
                if (builder.okClick != null) {
                    builder.okClick.onOkClick(PhoneZoneDialog.this, mInput.getText().toString().split(" ")[0]);
                }
            }
        });

        mInput.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (builder.okClick != null) {
                    builder.okClick.onPhoneZoneClick(PhoneZoneDialog.this);
                }
            }
        });


        mContent.setLocalText(builder.mContent);

        mInput.setLocalText(builder.mPhoneZoneValue);

        if (builder.isShowOK) {
            mOk.setLocalText(builder.mOK);
            mOk.setVisibility(View.VISIBLE);
        } else {
            mOk.setVisibility(View.GONE);
        }
        if (builder.isShowCancel) {
            mCancel.setLocalText(builder.mCancel);
            mCancel.setVisibility(View.VISIBLE);
        } else {
            mCancel.setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(builder.defaultName)) {
            mInput.setText(builder.defaultName);
        }


    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        super.cancel();
    }

    public interface AlertOkClickCallback {

        void onOkClick(PhoneZoneDialog dialog, String phoneZone);

        void onPhoneZoneClick(PhoneZoneDialog dialog);
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private String mOK;

        private boolean isShowOK = false;

        private String mCancel;

        private boolean isShowCancel = false;

        private boolean isAutoDismiss = true;

        private AlertOkClickCallback okClick;

        private String defaultName;

        private String mPhoneZoneValue;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setOk(String ok) {
            mOK = ok;
            isShowOK = true;
            return this;
        }

        public Builder setCancel(String cancel) {
            mCancel = cancel;
            isShowCancel = true;
            return this;
        }

        public Builder setPhoneZoneValue(String mPhoneZoneValue) {
            this.mPhoneZoneValue = mPhoneZoneValue;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setDefaultName(String defaultName) {
            this.defaultName = defaultName;
            return this;
        }

        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public PhoneZoneDialog preBuilder() {
            PhoneZoneDialog alertDialog = new PhoneZoneDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
