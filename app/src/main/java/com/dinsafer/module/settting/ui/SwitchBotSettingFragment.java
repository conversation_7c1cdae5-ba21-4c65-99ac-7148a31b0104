package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.RandomStringUtils;
import com.dinsafer.util.RegxUtil;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by LT on 2019/3/28.
 */
public class SwitchBotSettingFragment extends BaseFragment implements SwitchBotAdvanceSettingFragment.AdvanceSettingListener{

    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    ImageView commonBarLeftIcon;
    LocalTextView pluginStatusHint;
    LocalTextView modifyPlugsType;
    TextView modifyPlugsId;
    EditText modifyPlugsInput;
    LocalTextView modifyPlugsHint;
    LocalTextView modifyPlugsNetwork;
    LocalTextView sirenSetting;
    LocalCustomButton sirenTest;
    LocalTextView sirenHelp;
    RelativeLayout sirenTestLayout;

    private Builder builder;

    public static SwitchBotSettingFragment newInstance(Builder builder) {
        SwitchBotSettingFragment switchBotSettingFragment = new SwitchBotSettingFragment();
        Bundle args = new Bundle();
        args.putParcelable("data", builder);
        switchBotSettingFragment.setArguments(args);
        return switchBotSettingFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.modify_plugs_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
//        EventBus.getDefault().register(this);
        initData();
        return rootView;

    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toBack());
        rootView.findViewById(R.id.common_bar_left_icon).setOnClickListener( v -> toChangeName());
        rootView.findViewById(R.id.modify_plugs_network).setOnClickListener( v -> toAdvanceSetting());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarLeftIcon = rootView.findViewById(R.id.common_bar_left_icon);
        pluginStatusHint = rootView.findViewById(R.id.plugin_status_hint);
        modifyPlugsType = rootView.findViewById(R.id.modify_plugs_type);
        modifyPlugsId = rootView.findViewById(R.id.modify_plugs_id);
        modifyPlugsInput = rootView.findViewById(R.id.modify_plugs_input);
        modifyPlugsHint = rootView.findViewById(R.id.modify_plugs_hint);
        modifyPlugsNetwork = rootView.findViewById(R.id.modify_plugs_network);
        sirenSetting = rootView.findViewById(R.id.siren_setting);
        sirenTest = rootView.findViewById(R.id.siren_test);
        sirenHelp = rootView.findViewById(R.id.siren_help);
        sirenTestLayout = rootView.findViewById(R.id.siren_test_layout);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.tuya_color_light_setting));
        modifyPlugsInput.setHint(Local.s(getResources().getString(R.string.modifyaccessoryhint)));
        modifyPlugsInput.setText(Local.s(getResources().getString(R.string.switch_bot)));
        modifyPlugsType.setLocalText(getResources().getString(R.string.switch_bot));
        modifyPlugsNetwork.setLocalText(getResources().getString(R.string.ipc_setting_advance_setting));

        modifyPlugsId.setVisibility(View.GONE);

        builder = (Builder) getArguments().getParcelable("data");

        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)modifyPlugsType.getLayoutParams();
        layoutParams.topMargin = DensityUtils.dp2px(getContext(), 30);
        modifyPlugsType.setLayoutParams(layoutParams);

        LinearLayout.LayoutParams layoutParams1 = (LinearLayout.LayoutParams)modifyPlugsInput.getLayoutParams();
        layoutParams1.topMargin = DensityUtils.dp2px(getContext(), 30);
        modifyPlugsInput.setLayoutParams(layoutParams1);

        if (!TextUtils.isEmpty(builder.getName())) {
            modifyPlugsInput.setText(builder.getName());
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void toBack() {
        removeSelf();
    }

    public void toChangeName() {
        final String inputName = modifyPlugsInput.getText().toString().trim();
        if (TextUtils.isEmpty(inputName) || !RegxUtil.isLegalName(inputName)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail,  Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }

        showLoadingFragment(LoadingFragment.BLACK, "");
        String messageId = RandomStringUtils.getMessageId();
        Call<StringResponseEntry> call = DinsafeAPI.getApi().setSwitchBotNameCall(builder.getId()
                , CommonDataUtil.getInstance().getCurrentDeviceId(),
                inputName);
        if (call != null) {
            call.enqueue(new Callback<StringResponseEntry>() {
                @Override
                public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                    if (response.body().getStatus() == 1) {
                        settingListener.onChangeName(builder.getId(), inputName);
                        closeLoadingFragment();
                        removeSelf();
                    } else {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                }

                @Override
                public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                    closeLoadingFragment();
                    showErrorToast();
                }
            });

        }
    }


    public void toAdvanceSetting() {
//        if (builder.isOffline()) {
//            showToast(Local.s(getResources().getString(R.string.switch_bot_offline_tip)));
//            return;
//        }
        SwitchBotAdvanceSettingFragment switchBotAdvanceSettingFragment = SwitchBotAdvanceSettingFragment.newInstance(builder.getId(), builder.isBtnOne());
        switchBotAdvanceSettingFragment.setAdvanceSettingListener(this);
        getDelegateActivity().addCommonFragment(switchBotAdvanceSettingFragment);
    }

    public interface SettingListener {
        void onChangeName(String id, String name);
        void onChangeMode(String id, boolean isOneBtn);
    }
    private SettingListener settingListener;

    public void setSettingListener(SettingListener settingListener) {
        this.settingListener = settingListener;
    }

    @Override
    public void onChangeMode(String id, boolean isOneBtn) {
        builder.setBtnOne(isOneBtn);
        if (settingListener != null) {
            settingListener.onChangeMode(id, isOneBtn);
        }
    }


}
