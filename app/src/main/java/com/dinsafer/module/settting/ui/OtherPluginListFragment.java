package com.dinsafer.module.settting.ui;

import android.graphics.Color;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.BounceInterpolator;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.PlugsData;
import com.dinsafer.model.PlugsNameChangeEvent;
import com.dinsafer.model.event.DeviceOfflineEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.SimplePlugsItem;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.NavigatorUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by Rinfon on 16/7/12.
 */
public class OtherPluginListFragment extends BaseFragment
        implements IDeviceCallBack {

    LocalTextView commonBarTitle;

    SwipeMenuListView smartPlugsList;
    LocalTextView listviewEmpty;
    ImageView smartListBottomLeftIcon;
    LocalTextView smartListBottomText;
    ImageView smartListBottomLine;
    ImageView smartListBottomGoto;
    RelativeLayout smartListBottomBtn;

    private ArrayList<PlugsData> mData;
    private SimplePlugsItem plugsItem;
    private int messageIndex;

    private ArrayList<Device> mPluginDevices;
    private boolean selfOperate;


    public static OtherPluginListFragment newInstance(String title) {
        OtherPluginListFragment simplePlugsListFragment = new OtherPluginListFragment();
        Bundle args = new Bundle();
        args.putString("title", title);
        simplePlugsListFragment.setArguments(args);
        return simplePlugsListFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.smart_list_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        showBlueTimeOutLoadinFramgment();
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener(v -> toClose());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        smartPlugsList = rootView.findViewById(R.id.smart_plugs_list);
        listviewEmpty = rootView.findViewById(R.id.listview_empty);
        smartListBottomLeftIcon = rootView.findViewById(R.id.smart_list_bottom_left_icon);
        smartListBottomText = rootView.findViewById(R.id.smart_list_bottom_text);
        smartListBottomLine = rootView.findViewById(R.id.smart_list_bottom_line);
        smartListBottomGoto = rootView.findViewById(R.id.smart_list_bottom_goto);
        smartListBottomBtn = rootView.findViewById(R.id.smart_list_bottom_btn);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getArguments().getString("title"));
        listviewEmpty.setLocalText(getResources().getString(R.string.listview_empty));

        SwipeMenuCreator creator = new SwipeMenuCreator() {

            @Override
            public void create(SwipeMenu menu) {

                // create "delete" item
                SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                // set item background
                deleteItem.setBackground(R.color.colorDelete);
                // set item width
                deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 90));

                deleteItem.setTitleSize(13);

                deleteItem.setTitleColor(Color.WHITE);
                // set a icon
//                deleteItem.setIcon(R.drawable.ic_delete);
                deleteItem.setTitle(Local.s(getResources().getString(R.string.smart_plugs_list_delete)));
                // add to menu
                menu.addMenuItem(deleteItem);
            }
        };

//        set creator
        smartPlugsList.setMenuCreator(creator);
        smartPlugsList.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
        smartPlugsList.setCloseInterpolator(new BounceInterpolator());
        smartPlugsList.setOnMenuItemClickListener(new SwipeMenuListView.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(final int i, SwipeMenu swipeMenu, int i1) {
                switch (i1) {
                    case 0:
                        // delete
                        toDeleteItem(i);

                        break;
                }
                // false : close the menu; true : not close the menu
                return false;
            }
        });
//        listView.setEmptyView(listviewEmpty);
        smartPlugsList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                toChangePlugName(position);
            }
        });
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        createPlugsList();
    }

    private void toDeleteItem(final int i) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        final Device device = DinHome.getInstance().getDevice(mData.get(i).getPlugId());
                        if (null != device) {
                            DDLog.i(TAG, "Delete other plugin.");
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            selfOperate = true;
                            device.submit(PanelParamsHelper.deletePlugin());
                        } else {
                            DDLog.e(TAG, "No other plugin Device.");
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    /**
     * 初始化plugg数据列表
     */
    private void createPlugsList() {
        mData = new ArrayList<PlugsData>();
        plugsItem = new SimplePlugsItem(getActivity(), mData);
        smartPlugsList.setAdapter(plugsItem);
        mPluginDevices = new ArrayList<>();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                List<Device> deviceList = DinHome.getInstance().getDeviceByType(PanelConstant.DeviceType.OTHER_PLUGIN);
                if (null != deviceList && 0 < deviceList.size()) {
                    for (Device device : deviceList) {
                        device.registerDeviceCallBack(OtherPluginListFragment.this);
                    }
                    mPluginDevices.addAll(deviceList);
                }
                return mPluginDevices;
            }
        }).thenUI(o -> {
            DDLog.i(TAG, "createDoorListPluginData-getdata");
            if (null != mPluginDevices && 0 < mPluginDevices.size()) {
                PlugsData plug;
                for (Device device : mPluginDevices) {
                    plug = new PlugsData();
                    String pluginName = DeviceHelper.getString(device, PanelDataKey.NAME, "");
                    if (TextUtils.isEmpty(pluginName)) {
                        pluginName = CommonDataUtil.getInstance().getDefaultRFDeviceName(device);
                    }
                    plug.setName(pluginName)
                            .setPlugId(device.getId())
                            .setSirenData(DeviceHelper.getString(device, PanelDataKey.WirelessSiren.SIREN_DATA, ""))
                            .setPlugin_item_sub_category(device.getSubCategory())
                            .setDecodeid(DeviceHelper.getString(device, PanelDataKey.DECODE_ID, ""));
                    mData.add(plug);
                }
            }

            if (mData.size() <= 0) {
                listviewEmpty.setVisibility(View.VISIBLE);
            } else {
                listviewEmpty.setVisibility(View.GONE);
            }
            plugsItem.notifyDataSetChanged();
            closeLoadingFragment();
        });
    }

    public void toChangePlugName(int index) {
        if (!CommonDataUtil.getInstance().isPanelOnline()) {
            DDLog.e(TAG, "当前主机离线，Item不能被点击哦");
            return;
        }

        messageIndex = index;
        NavigatorUtil.getInstance().toModifyNotOfficalPlugsNameFragment(mData.get(index).getName(),
                mData.get(index).getPlugId(), mData.get(index).getPlugin_item_sub_category());
    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (null != mPluginDevices && 0 < mPluginDevices.size()) {
            for (Device device : mPluginDevices) {
                device.unregisterDeviceCallBack(this);
            }
        }
        DinHome.getInstance().releaseDeviceByType(PanelConstant.DeviceType.OTHER_PLUGIN);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOfflineEvent event) {
        DDLog.d(TAG, "DeviceOfflineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(DeviceOnlineEvent event) {
        DDLog.d(TAG, "DeviceOnlineEvent. ");
        plugsItem.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PlugsNameChangeEvent ev) {
        plugsItem.changeName(messageIndex, ev.getName());
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || 1 != resultType
                || !selfOperate) {
            return;
        }

        selfOperate = false;
        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        if (PluginCmd.PLUGIN_DELETE.equals(cmd)) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS != status) {
                showErrorToast();
            } else {
                plugsItem.remove(messageIndex);
                if (mData.size() <= 0) {
                    listviewEmpty.setVisibility(View.VISIBLE);
                } else {
                    listviewEmpty.setVisibility(View.GONE);
                }
                plugsItem.notifyDataSetChanged();
            }
        }
    }
}
