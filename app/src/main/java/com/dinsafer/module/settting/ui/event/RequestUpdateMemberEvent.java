package com.dinsafer.module.settting.ui.event;

import androidx.annotation.Keep;

/**
 * 请求修改用户信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/4/10 11:39
 */
@Keep
public class RequestUpdateMemberEvent {
    /**
     * true: 请求修改；false:结果
     */
    private final boolean request;

    /**
     * 是否修改成功
     */
    private final boolean onResultSuccess;

    public RequestUpdateMemberEvent() {
        this.request = true;
        this.onResultSuccess = false;
    }

    public RequestUpdateMemberEvent(boolean onResultSuccess) {
        this.request = false;
        this.onResultSuccess = onResultSuccess;
    }

    public boolean isRequest() {
        return request;
    }

    public boolean isOnResultSuccess() {
        return onResultSuccess;
    }

    public boolean isResult() {
        return !request;
    }
}
