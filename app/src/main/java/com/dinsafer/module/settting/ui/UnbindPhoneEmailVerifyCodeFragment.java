package com.dinsafer.module.settting.ui;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Patterns;
import android.view.View;

import androidx.annotation.NonNull;

import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.UnBindEmail;
import com.dinsafer.model.UnBindPhone;
import com.dinsafer.module.user.BaseVerifyCodeFragment;
import com.dinsafer.module.user.UserType;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

/**
 * 解绑手机或邮箱页面
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/8/2 11:10 上午
 */
public class UnbindPhoneEmailVerifyCodeFragment extends BaseVerifyCodeFragment {

    private static final String KEY_ACCOUNT = "account";

    private String account;

    public static UnbindPhoneEmailVerifyCodeFragment newInstance(String account) {
        final UnbindPhoneEmailVerifyCodeFragment fragment = new UnbindPhoneEmailVerifyCodeFragment();
        final Bundle args = new Bundle();
        args.putString(KEY_ACCOUNT, account);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onFinishAnim() {
        // super.onFinishAnim();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        account = getArguments().getString(KEY_ACCOUNT);
        if (TextUtils.isEmpty(account)) {
            getDelegateActivity().showErrorToast();
            return;
        }
        userType = Patterns.EMAIL_ADDRESS.matcher(account).matches() ? UserType.EMAIL : UserType.PHONE;
        final String topHint = Local.s(getResources().getString(R.string.send_verification_key)) + " " + account;
        mBinding.tvVerifyHint.setText(topHint);

        changeCountDownViewType(TYPE_DEFAULT, true);

        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setRefreshEnable(true);
            changeCountDownViewType(TYPE_TIME_OUT, true);
        });
        getVerifyCode(null, null);
    }

    public void getVerifyCode(String imgVerifyCode, String imgVerifyId) {
        final IResultCallback callback = new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                if (!UnbindPhoneEmailVerifyCodeFragment.this.isAdded()) {
                    return;
                }
                closeLoadingFragmentWithCallBack();
                if (i == ErrorCode.ERROR_WRONG_VERIFICATION_CODE_ENTERED) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_wrong_verifyCode)));
                    getVerificationCode();
                } else if (i == ErrorCode.ERROR_VERIFICATION_CODE_ENTERED_TIMEOUT) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_verifyCode_timeout)));
                    getVerificationCode();
                } else {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));
                }
                setRefreshEnable(true);
            }

            @Override
            public void onSuccess() {
                if (!UnbindPhoneEmailVerifyCodeFragment.this.isAdded()) {
                    return;
                }

                closeLoadingFragmentWithCallBack();
                changeCountDownViewType(TYPE_COUNTING_DOWN, true);
                requestFocusAndOpenInput();
            }
        };
        if (Patterns.EMAIL_ADDRESS.matcher(account).matches()) {
            DinSDK.getUserInstance().unbindEmail(account, callback);
        } else {
            if (TextUtils.isEmpty(imgVerifyCode) && TextUtils.isEmpty(imgVerifyId)) {
                closeLoadingFragmentWithCallBack();
                changeCountDownViewType(TYPE_COUNTING_DOWN, true);
                requestFocusAndOpenInput();
                return;
            }
            DinSDK.getUserInstance().unbindPhone(account, DDSystemUtil.getWidevineId(), imgVerifyCode, imgVerifyId, callback);
        }
    }

    public void requestUnbind(final @NonNull String inputCode) {
        final IResultCallback callback = new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                if (!UnbindPhoneEmailVerifyCodeFragment.this.isAdded()) {
                    return;
                }
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (i == -1 && !TextUtils.isEmpty(s)) {
                    showErrorToast();
                } else {
                    getMainActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.forget_password_error_code));
                }
                clearPassword();
                requestFocusAndOpenInput();
            }

            @Override
            public void onSuccess() {
                if (!UnbindPhoneEmailVerifyCodeFragment.this.isAdded()) {
                    return;
                }
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (Patterns.EMAIL_ADDRESS.matcher(account).matches()) {
                    EventBus.getDefault().post(new UnBindEmail());
                } else {
                    EventBus.getDefault().post(new UnBindPhone());
                }
                removeSelf();
                getDelegateActivity().removeAllCommonFragment();
                showSuccess();
            }
        };

        showBlueTimeOutLoadinFramgmentWithBack();
        if (Patterns.EMAIL_ADDRESS.matcher(account).matches()) {
            DinSDK.getUserInstance().verifyUnbindEmailCode(account, inputCode, callback);
        } else {
            DinSDK.getUserInstance().verifyUnbindPhone(account, inputCode, callback);
        }
    }

    @Override
    protected void onInputFished(@NonNull String inputCode) {
        requestUnbind(inputCode);
    }

    @Override
    protected void onRefreshClick(String vc, String vi) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setRefreshEnable(true);
        });
        getVerifyCode(vc, vi);
    }
}
