package com.dinsafer.module.interfaces;


import android.os.Bundle;
import android.view.View;

/**
 * 基础的Fragment接口
 * Created by Rinfon on 16/6/15.
 */
public interface IBaseFragment {


    /**
     */
    void initData();

//    /**
//     * 此方法用于初始化布局中所有的View，如果使用了View注入框架则不需要调用
//     */
//    void findView(View inflateView, Bundle savedInstanceState);

    /**
     * 此方法用于设置View显示数据
     */
    void initView(View inflateView, Bundle savedInstanceState);

    void initListener();

    /**
     * 此方法用于初始化对话框
     */
    void initDialog();

    void onEnterFragment();

    void onPauseFragment();

    void onExitFragment();

    void onFinishAnim();
}