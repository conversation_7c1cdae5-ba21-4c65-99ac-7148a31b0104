package com.dinsafer.module.interfaces;

import com.dinsafer.config.APIKey;
import com.dinsafer.model.AdEntry;
import com.dinsafer.model.AddDeviceEntry;
import com.dinsafer.model.BaseHttpEntry;
import com.dinsafer.model.BindPhoneEntry;
import com.dinsafer.model.CategoryPlugsEntry;
import com.dinsafer.model.CheckWifiWorkEntry;
import com.dinsafer.model.ContactListData;
import com.dinsafer.model.ContactsDataV2;
import com.dinsafer.model.DeviceAlertServicePlanResponse;
import com.dinsafer.model.DeviceSettingEntry;
import com.dinsafer.model.DeviceStatusEntry;
import com.dinsafer.model.DeviceTokenEntry;
import com.dinsafer.model.DeviceWifiList;
import com.dinsafer.model.DoorBell;
import com.dinsafer.model.ForgetPasswordEntry;
import com.dinsafer.model.HueEntry;
import com.dinsafer.model.IPCAlertServicePlanListResponse;
import com.dinsafer.model.IPCAlertServiceSettingResponse;
import com.dinsafer.model.IPCDataResponse;
import com.dinsafer.model.LoginResponse;
import com.dinsafer.model.MainSectionPlugin;
import com.dinsafer.model.MemberListEntry;
import com.dinsafer.model.ModifyUidPassword;
import com.dinsafer.model.MultiDataEntry;
import com.dinsafer.model.NewAskPlugInfo;
import com.dinsafer.model.PluginResponseEntry;
import com.dinsafer.model.PopupEntry;
import com.dinsafer.model.ReadyToArmSwitchStatusEntry;
import com.dinsafer.model.RefreshDeviceWifiList;
import com.dinsafer.model.RegisterAccount;
import com.dinsafer.model.SosStatusEntry;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.model.SystemResponseEntry;
import com.dinsafer.model.UserMainDevice;
import com.dinsafer.model.home.HomeDeviceInfoEntry;
import com.dinsafer.model.home.HomePluginQuantityEntry;
import com.dinsafer.module.iap.GetDeviceExpirationDateResponse;
import com.dinsafer.module.iap.GetDeviceExpirationDateResponseV2;
import com.dinsafer.module.iap.GetLastTriggeredTimeResponse;
import com.dinsafer.module.iap.GetServiceConfigResponse;
import com.dinsafer.module.iap.GetServiceExpirationResponse;
import com.dinsafer.module.iap.GetServiceOpenResponse;
import com.dinsafer.module.iap.GetTaskDetailResponse;
import com.dinsafer.module.iap.ListBigBannerResponse;
import com.dinsafer.module.iap.ListCloudStorageServiceResponse;
import com.dinsafer.module.iap.ListOrdersResponse;
import com.dinsafer.module.iap.ListSmallBannerResponse;
import com.dinsafer.module.iap.ProductSchedulesResponse;
import com.dinsafer.module.iap.StaticCloudServiceResponse;
import com.dinsafer.module.iap.StaticPowerCareResponse;
import com.dinsafer.module.iap.powercare.ListTrafficPackageServiceResponse;
import com.dinsafer.module.iap.powercare.TrafficPackageOrdersResponse;
import com.dinsafer.module.iap.powercare.TrafficPackageProductSchedulesResponse;
import com.dinsafer.module.iap.powercare.TrafficPackageServiceCardItemModel;
import com.dinsafer.module.iap.powercare.bean.BmtIAPOpenListBean;

import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Url;
import rx.Observable;

/**
 * Created by Rinfon on 16/6/16.
 */
public interface IApi {

    @POST("/auth/getusersetting/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<LoginResponse> Login(@FieldMap Map<String, Object> map);

    @POST("/device/listmydevices/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<UserMainDevice> getUserMainDeviceList(@FieldMap Map<String, Object> map);

    @POST("/auth/modifyuidpsw/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ModifyUidPassword> modifyUidPassword(@FieldMap Map<String, Object> map);

    @POST("/auth/modifyuid/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> modifyUid(@FieldMap Map<String, Object> map);

    @POST("/auth/bindphone/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BindPhoneEntry> bindPhone(@FieldMap Map<String, Object> map);

    @POST("/auth/unbindphone/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BindPhoneEntry> unbindPhone(@FieldMap Map<String, Object> map);

    @POST("/auth/verifybindphone/v3/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BindPhoneEntry> verifyBindPhone(@FieldMap Map<String, Object> map);

    @POST("/auth/verifyunbindphone/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> verifyUnBindPhone(@FieldMap Map<String, Object> map);

    @POST("/auth/bindmail/v3/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> bindEmail(@FieldMap Map<String, Object> map);

    @POST("/auth/unbindmail/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> unbindEmail(@FieldMap Map<String, Object> map);

    @POST("/device/userverifydevicepwd/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> verifyMainDevicePassword(@FieldMap Map<String, Object> map);

    @POST("/auth/logout/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> logout(@FieldMap Map<String, Object> map);

    @POST("/auth/changepwd/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> changePassword(@FieldMap Map<String, Object> map);

    @POST("/device/listmembersetting/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<MemberListEntry> getMemberList(@FieldMap Map<String, Object> map);

    @POST("/uploader/getuptoken/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getUploadToken(@FieldMap Map<String, Object> map);

    @POST("/auth/modifyuserinfos/v3/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getUploadImage(@FieldMap Map<String, Object> map);

    @POST("/device/listdevicesetting/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<DeviceSettingEntry> getDeviceSettingCall(@FieldMap Map<String, Object> map);

    @POST("/device/getdevicetoken/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<DeviceTokenEntry> getDeviceToken(@FieldMap Map<String, Object> map);

    @POST("/device/listspecifypluginsetting/v3/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<CategoryPlugsEntry> getCategoryPlugsCallV3(@FieldMap Map<String, Object> map);

    @POST("/device/listspecifypluginsetting/v4/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getSpecifyPlugin(@FieldMap Map<String, Object> map);

    @POST("/device/listaccessorysetting/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getCategoryPlugsCallV4(@FieldMap Map<String, Object> map);

    @POST("/device/listpluginsettingbytype/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getCategoryPlugsByTypeCall(@FieldMap Map<String, Object> map);

    @POST("/device/removeuserdevice/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteDeviceCall(@FieldMap Map<String, Object> map);

    @POST("/device/modifydevicename/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getChangeDeviceNameCall(@FieldMap Map<String, Object> map);


    @POST("/device/threeinone/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<MultiDataEntry> getAllData(@FieldMap Map<String, Object> map);

    @POST("/device/sendcmd/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getDeviceCmdCall(@FieldMap Map<String, Object> map);

    @POST("/device/sendcmd/" + APIKey.APP_ID)
    @FormUrlEncoded
    Observable<StringResponseEntry> getAddNewASKPluginObservable(@FieldMap Map<String, Object> map);


    @POST("/device/modifyipcjson/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getChangeIPCPassword(@FieldMap Map<String, Object> map);

    @POST("/auth/getuserphonekey/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ForgetPasswordEntry> getForgetPWDMessageCall(@FieldMap Map<String, Object> map);

    @POST("/auth/forgetchangepwd/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<LoginResponse> getComfirmForgetPWDCall(@FieldMap Map<String, Object> map);

    @POST("/device/generatesharecode/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getShareDeviceQR(@FieldMap Map<String, Object> map);

    @POST("/device/adddevicebysharecode/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<AddDeviceEntry> getAddDeviceCall(@FieldMap Map<String, Object> map);

    @POST("/device/getsosstate/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<SosStatusEntry> getSosStatusCall(@FieldMap Map<String, Object> map);

    @GET
    Call<ResponseBody> getLanguageList(@Url String url);

    @GET
    Call<ResponseBody> getLanguageFile(@Url String url);

    @POST("/feedback/new/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getFeedBackCall(@FieldMap Map<String, Object> map);

    @POST("/device/unbinduser/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteMemberCall(@FieldMap Map<String, Object> map);

    @POST("/device/listemergencycontacts/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ContactListData> getContactsListCall(@FieldMap Map<String, Object> map);

    @POST("/device/modifydevicecontact/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getContactsPushTypeCall(@FieldMap Map<String, Object> map);

    @POST("/device/addmulticontactmanually/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getAddContactsCall(@FieldMap Map<String, Object> map);

    @POST("/device/modifycontactmanually/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getEditContactsCall(@FieldMap Map<String, Object> map);

    @POST("/device/deletecontactmanually/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteContactsCall(@FieldMap Map<String, Object> map);

    @POST(APIKey.DEVICE_WIFI_KEY)
    @FormUrlEncoded
    Call<DeviceWifiList> getDeviceWifiListCall(@FieldMap Map<String, Object> map);

    @POST(APIKey.DEVICE_REFRESH_WIFI_KEY)
    Call<RefreshDeviceWifiList> getRefreshWifiListCall();

    @POST(APIKey.DEVICE_WIFI_CHECK_KEY)
    @FormUrlEncoded
    Call<StringResponseEntry> getCheckDeviceCall(@FieldMap Map<String, Object> map);

    @POST("/device/verifywithtmpdevice/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<CheckWifiWorkEntry> getCheckDeviceWifiWorkCall(@FieldMap Map<String, Object> map);

    @POST("/auth/autoregister/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<RegisterAccount> getAutoRegisterAccount(@FieldMap Map<String, Object> map);

    @POST("/device/modifyuserpermission/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getModifyUserPermissionCall(@FieldMap Map<String, Object> map);

    @POST("/domain/getappversionstate/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<SystemResponseEntry> getAppUpdataCall(@FieldMap Map<String, Object> map);

    @POST(APIKey.SET_NETWORK_IP)
    @FormUrlEncoded
    Call<StringResponseEntry> getSetStaticIpCall(@FieldMap Map<String, Object> map);

    @POST(APIKey.SET_NETWORK_DHCP_IP)
    @FormUrlEncoded
    Call<StringResponseEntry> getSetDHCPIpCall(@FieldMap Map<String, Object> map);

    @POST("/news/list/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<AdEntry> getAdListCall(@FieldMap Map<String, Object> map);

    @POST("/home/<USER>/get-image-popup/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<PopupEntry> getPopupList(@FieldMap Map<String, Object> map);

    @POST(APIKey.CHECK_PASSWORD_ON_DEVICE)
    @FormUrlEncoded
    Call<StringResponseEntry> verifyMainDevicePasswordOnDevice(@FieldMap Map<String, Object> map);

    @POST(APIKey.CHECK_PASSWORD_ON_DEVICE_2)
    @FormUrlEncoded
    Call<StringResponseEntry> verifyMainDevicePasswordOnDeviceNotExitAP(@FieldMap Map<String, Object> map);

    @POST("/device/getsostext/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getSosMessageCall(@FieldMap Map<String, Object> map);

    @GET
    Call<StringResponseEntry> getExitApCall(@Url String url);

    @POST("/device/listemergencycontacts/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ContactsDataV2> getListmemberContactsV2(@FieldMap Map<String, Object> map);

    @POST("/device/modifydevicecontact/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getModifydevicecontact(@FieldMap Map<String, Object> map);

    @POST("/device/modifycontactmanually/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getModifycontactmanually(@FieldMap Map<String, Object> map);

    @POST("/device/addmulticontactmanually/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getAddmulticontactmanually(@FieldMap Map<String, Object> map);

    @POST("/auth/getuserdata/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<LoginResponse> getUserDataCall(@FieldMap Map<String, Object> map);

    @POST("/device/addclientlog/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getLogCall(@FieldMap Map<String, Object> map);


    @POST("/logs/parser/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getLogUploadCall(@FieldMap Map<String, Object> map);

    @POST("/deviceaddon/listaddonverbyappver/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<PluginResponseEntry> getPluginCall(@FieldMap Map<String, Object> map);

    @POST("/pluginqrcode/scan/" + APIKey.APP_ID + "/{shortId}/1")
    @FormUrlEncoded
    Call<ResponseBody> getNewQRCodeScan(@Path("shortId") String shortId, @FieldMap Map<String, Object> map);

    @POST("/device/listnewackplugindata/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getHeartBitListCall(@FieldMap Map<String, Object> map);

    @POST("/device/addipc/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getAddIPCCall(@FieldMap Map<String, Object> map);

    @POST("/device/listdoorbelldata/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getListDoorBellCall(@FieldMap Map<String, Object> map);

    @POST("/device/modifydoorbell/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getModifyDoorBellCall(@FieldMap Map<String, Object> map);

    @POST("/device/adddoorbell/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getAddDoorBellCall(@FieldMap Map<String, Object> map);

    @POST("/device/listdoorbellcapture/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<DoorBell> getDoorBellCapCall(@FieldMap Map<String, Object> map);

    @POST("/device/deletedoorbellcapture/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteDoorBellCapCall(@FieldMap Map<String, Object> map);

    @POST("/device/deleteipc/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteIPCCall(@FieldMap Map<String, Object> map);

    @POST("/device/modifyipc/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> modifyXiaoHeiCall(@FieldMap Map<String, Object> map);

    @POST("/device/settuyaaccount/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getTuyaAccount(@FieldMap Map<String, Object> map);

    @POST("/device/getplugindatas/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<MainSectionPlugin> getPluginDatas(@FieldMap Map<String, Object> map);

    @POST("/device/homepage/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<MultiDataEntry> getHomePageCall(@FieldMap Map<String, Object> map);

    @POST("/device/getdevicestatus/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<DeviceStatusEntry> getDeviceStatusForWidget(@FieldMap Map<String, Object> map);

    @POST("/device/getrdasetting/" + APIKey.APP_ID)
    @FormUrlEncoded
    Observable<ReadyToArmSwitchStatusEntry> getReadyToArmSwitchStatus(@FieldMap Map<String, Object> map);


    @POST("/device/listdoorsensorsetting/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getDoorSensorListData(@FieldMap Map<String, Object> map);

    @POST("/device/listrelaysetting/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getRelayListData(@FieldMap Map<String, Object> map);

    @POST("/pluginqrcode/getnewaskdata/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<NewAskPlugInfo> getNewAskPlugInfo(@FieldMap Map<String, Object> map);

    @POST("/device/listswitchbotsetting/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getSwitchBotListData(@FieldMap Map<String, Object> map);

    @POST("/device/collectswitchbot/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> collectSwitchBotCall(@FieldMap Map<String, Object> map);


    @POST("/device/deleteswitchbot/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> deleteSwitchBotCall(@FieldMap Map<String, Object> map);


    @POST("/device/setswitchbot/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> setSwitchBotNameCall(@FieldMap Map<String, Object> map);

    @POST("/tuya/setuid/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> setTuyaUid(@FieldMap Map<String, Object> map);

    @POST("/philips/addhub/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> addPhilipsHue(@FieldMap Map<String, Object> map);

    @POST("/philips/sethubname/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> setPhilipsHueName(@FieldMap Map<String, Object> map);

    @POST("/philips/delhub/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> delPhilipsHue(@FieldMap Map<String, Object> map);

    @POST("/philips/gethubs/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<HueEntry> getPhilipsHue(@FieldMap Map<String, Object> map);

    //    上传字符串的日志
    @POST("/device/addclientlog/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getLogCallV2(@FieldMap Map<String, Object> map);

    /**
     * 获取设备状态详情页异常配件信息
     */
    @POST("/device/sendcmd/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getHomeExceptionAccessory(@FieldMap Map<String, Object> map);

    /**
     * 获取配件转态
     */
    @POST("/device/sendcmd/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> getPluginStatus(@FieldMap Map<String, Object> map);

    /**
     * 获取自定义五键遥控器控制的智能插座配件列表
     */
    @POST("/device/list-customize-smartplugs/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getCustomizeSmartPlugs(@FieldMap Map<String, Object> map);

    /**
     * 设置五键遥控控制的自定义插座
     */
    @POST("/device/sendcmd/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> setCustomizeSmartPlugs(@FieldMap Map<String, Object> map);


    @POST("/device/listipcdata/v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getIPCList(@FieldMap Map<String, Object> map);

    /**
     * 上传涂鸦操作eventlist
     *
     * @param map
     * @return
     */
    @POST("/device/c-save-event-list/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> uploadTuyaEventList(@FieldMap Map<String, Object> map);

    /**
     * 修改或保存SmartButton的控制对象配置信息
     */
    @POST("/device/sendcmd/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> updateAndSaveSmartButtonConfig(@FieldMap Map<String, Object> map);

    /**
     * 获取care mode数据
     */
    @POST("/device/get-care-mode-data/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getCareModeData(@FieldMap Map<String, Object> map);


    /**
     * 获取指定ipc的推送服务配置
     *
     * @param map
     * @return
     */
    @POST("/device/get-ipc-service-settings/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<IPCAlertServiceSettingResponse> getIPCAlertServiceSetting(@FieldMap Map<String, Object> map);


    /**
     * 修改指定ipc的推送服务配置
     *
     * @param map
     * @return
     */
    @POST("/device/update-ipc-service-settings/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> modifyIPCAlertServiceSetting(@FieldMap Map<String, Object> map);

    /**
     * 获取主机当前的推送服务套餐信息
     *
     * @param map
     * @return
     */
    @POST("/device/get-device-current-service/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<DeviceAlertServicePlanResponse> getCurrentIPCAlertServicePlan(@FieldMap Map<String, Object> map);


    /**
     * 获取指定ipc的数据(移动侦测推送看直播)
     *
     * @param map
     * @return
     */
    @POST("/ipc/get-ipc-data/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<IPCDataResponse> getIPCData(@FieldMap Map<String, Object> map);


    /**
     * 获取ipc推送服务的产品列表
     *
     * @param map
     * @return
     */
    @POST("/device/get-ipc-service-product-list/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<IPCAlertServicePlanListResponse> getIPCAlertServicePlanList(@FieldMap Map<String, Object> map);

    @POST("/device/get-device-info/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<HomeDeviceInfoEntry> getDeviceInfo(@FieldMap Map<String, Object> map);

    @POST("/device/get-plugin-info/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getPluginInfo(@FieldMap Map<String, Object> map);

    @POST("/device/get-plugin-details/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ResponseBody> getPluginDetails(@FieldMap Map<String, Object> map);

    @POST("/device/get-plugin-quantity-info/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<HomePluginQuantityEntry> getPluginQuantityInfo(@FieldMap Map<String, Object> map);

    /**
     * 校验购买的一次性ipc推送服务商品
     *
     * @param map
     * @return
     */
    @POST("/bh/verify_ot_product_and/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> verifyInAppProduct(@FieldMap Map<String, Object> map);

    /**
     * 校验订阅的ipc推送服务商品
     *
     * @param map
     * @return
     */
    @POST("/bh/verify_s_product_and/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> verifySubscriptionProduct(@FieldMap Map<String, Object> map);


    @POST("/iap/list-product-schedules/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ProductSchedulesResponse> listProductSchedules(@FieldMap Map<String, Object> map);

    @POST("/iap/list-country-products/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ListCloudStorageServiceResponse> listCloudStorageServicePlan(@FieldMap Map<String, Object> map);

    @POST("/bh/verify-allowed-to-buy/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BaseHttpEntry> verifyAllowedToBuy(@FieldMap Map<String, Object> map);

    @POST("/bh/verify-cloud-product-and/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BaseHttpEntry> verifyCloudProduct(@FieldMap Map<String, Object> map);

    @POST("/iap/list-orders/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ListOrdersResponse> listOrders(@FieldMap Map<String, Object> map);

    @POST("/bh/verify-recharge-code/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BaseHttpEntry> verifyRechargeCode(@FieldMap Map<String, Object> map);

    @POST("/iap/get-expiration-date/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetDeviceExpirationDateResponse> getDeviceServiceExpirationData(@FieldMap Map<String, Object> map);

    @POST("/iap/get-expiration-date-v2/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetDeviceExpirationDateResponseV2> getDeviceServiceExpirationDataV2(@FieldMap Map<String, Object> map);

    @POST("/ipc/check-service-remaining/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetServiceExpirationResponse> checkDeviceServiceRemaining(@FieldMap Map<String, Object> map);

    @POST("/ipc/md/get-last-triggered-time-by-ipc-id/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetLastTriggeredTimeResponse> getLastTriggeredTimeByIpcId(@FieldMap Map<String, Object> map);

    @POST("/home/<USER>/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetServiceExpirationResponse> checkHomeServiceExpired(@FieldMap Map<String, Object> map);

    @POST("/iap/get-service-conf/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetServiceConfigResponse> getServiceConfig(@FieldMap Map<String, Object> map);

    @POST("/iap/get-service-open/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetServiceOpenResponse> getServiceOpen(@FieldMap Map<String, Object> map);

    @POST("/task/submit-join-email/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BaseHttpEntry> submitJoinEmail(@FieldMap Map<String, Object> map);

    @POST("/task/list-big-banner/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ListBigBannerResponse> listBigBanner(@FieldMap Map<String, Object> map);

    @POST("/task/list-small-banner/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ListSmallBannerResponse> listSmallBanner(@FieldMap Map<String, Object> map);

    @POST("/task/get-task-detail/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetTaskDetailResponse> getTaskDetail(@FieldMap Map<String, Object> map);

    @GET(APIKey.IAP_STATIC_URL)
    Call<StaticCloudServiceResponse> getIapStaticInfo();

    @GET(APIKey.POWER_CARE_STATIC_URL)
    Call<StaticPowerCareResponse> getPowerCareStaticInfo();

    @POST("/iap/list-country-products/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<ListTrafficPackageServiceResponse> listTrafficPackageServicePlan(@FieldMap Map<String, Object> map);

    @POST("/iap/emaldo/list-orders/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<TrafficPackageOrdersResponse> trafficPackageOrders(@FieldMap Map<String, Object> map);

    @POST("/iap/emaldo/list-bmt-iap-open/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<BmtIAPOpenListBean> getBmtIAPOpen(@FieldMap Map<String, Object> map);

    @POST("/iap/emaldo/list-product-schedules/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<TrafficPackageProductSchedulesResponse> bmtListProductSchedules(@FieldMap Map<String, Object> map);

    @POST("/iap/emaldo/get-expiration-date/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<GetDeviceExpirationDateResponseV2> getBmtIAPExpirationDate(@FieldMap Map<String, Object> map);

    @POST("/bmt/bmt-feedback/" + APIKey.APP_ID)
    @FormUrlEncoded
    Call<StringResponseEntry> bmtFeedback(@FieldMap Map<String, Object> map);
}
