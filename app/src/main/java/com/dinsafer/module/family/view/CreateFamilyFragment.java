package com.dinsafer.module.family.view;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentCreateFamilyBinding;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.model.family.FamilyListChangeEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;

/**
 * 创建家庭页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/23 4:25 PM
 */
public class CreateFamilyFragment extends BaseFragment {
    private static final String KEY_FAMILY_LIST = "family_list";

    private FragmentCreateFamilyBinding mBinding;
    private ArrayList<String> mCurrentFamilyList;
    private String defaultFamilyName;

    public static CreateFamilyFragment newInstance(ArrayList<String> familyList) {
        Bundle args = new Bundle();
        args.putStringArrayList(KEY_FAMILY_LIST, familyList);
        CreateFamilyFragment fragment = new CreateFamilyFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_create_family, container, false);
        initParams();
        initView(mBinding.getRoot(), savedInstanceState);
        return mBinding.getRoot();
    }

    private void initParams() {
        mCurrentFamilyList = getArguments().getStringArrayList(KEY_FAMILY_LIST);
        if (null == mCurrentFamilyList) {
            mCurrentFamilyList = new ArrayList<>();
        }

        String familyNamePrefix = Local.s(getString(R.string.default_family_name));
        // 当前没有家庭或没有默认名字命名的家庭
        if (mCurrentFamilyList.size() <= 0
                || !mCurrentFamilyList.contains(familyNamePrefix)) {
            defaultFamilyName = familyNamePrefix;
            return;
        }

        StringBuilder sb = new StringBuilder();
        String tempName;
        for (int i = 0; i < mCurrentFamilyList.size(); i++) {
            sb.delete(0, sb.length());
            sb.append(familyNamePrefix)
                    .append(" ");
            if (i < 9) {
                sb.append("0");
            }
            sb.append(i + 1);
            tempName = sb.toString();
            if (mCurrentFamilyList.contains(tempName)) {
                continue;
            }

            defaultFamilyName = tempName;
            break;
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.tvCreateFamilyHint.setLocalText(getString(R.string.create_a_family));
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.etFamilyName.setHint(defaultFamilyName);
        mBinding.ivBack.setOnClickListener(v -> removeSelf());
        mBinding.btnSave.setOnClickListener(v -> requestCreateFamily());
    }

    /**
     * 请求创建Family
     */
    private void requestCreateFamily() {
        final String inputFamilyName = mBinding.etFamilyName.getText().toString();
        final String familyName = TextUtils.isEmpty(inputFamilyName)
                ? defaultFamilyName
                : inputFamilyName;

        if (TextUtils.isEmpty(familyName) || !RegxUtil.isLegalName(familyName)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getHomeInstance().createHome(familyName, LocalManager.getInstance().getCurrentLanguage(), new IDefaultCallBack2<Home>() {
            @Override
            public void onSuccess(Home home) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                removeSelf();
                notifyCreateFamilySuccess(home.getHomeID());
            }

            @Override
            public void onError(int i, String s) {
                i("create  home fail:" + i + " s:" + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (ErrorCode.ERROR_REACH_MAX_AMOUNT_OF_FAMILY == i) {
                    showToast(getString(R.string.reach_max_amount_of_family));
                } else {
                    showErrorToast();
                }
            }
        });
    }

    /**
     * 通知创建家庭成功
     */
    private void notifyCreateFamilySuccess(String familyId) {
        EventBus.getDefault().post(new FamilyListChangeEvent());
        HomeManager.getInstance().changeFamily(familyId);
        getMainActivity().removeAllCommonFragment();
        getMainActivity().smoothToHome();
    }
}
