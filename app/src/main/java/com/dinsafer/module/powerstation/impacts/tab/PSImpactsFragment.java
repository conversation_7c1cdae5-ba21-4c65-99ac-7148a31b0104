package com.dinsafer.module.powerstation.impacts.tab;

import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.view.View;

import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsImpactsBinding;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.impacts.ImpactStrategiesFragment;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.OnFragmentCreatedListener;
import com.dinsafer.util.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PSImpactsFragment extends MyBaseFragment<FragmentPsImpactsBinding> implements IDeviceCallBack {

    private static OnFragmentCreatedListener mCreatedListener;

    public static PSImpactsFragment newInstance(int position, OnFragmentCreatedListener createdListener) {
        PSImpactsFragment fragment = new PSImpactsFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_POSITION, position);
        fragment.setArguments(bundle);
        mCreatedListener = createdListener;
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_impacts;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        Bundle bundle = getArguments();
        int position = bundle.getInt(PSKeyConstant.KEY_POSITION);
        if (mCreatedListener != null) {
            mCreatedListener.onCreated(inflateView, position);
        }
        ImpactStrategiesFragment.sPSDevice.registerDeviceCallBack(this);
        getStatRevenue();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            getStatRevenue();
        }
    }

    @Override
    public void onDestroyView() {
        if (ImpactStrategiesFragment.sPSDevice != null)
            ImpactStrategiesFragment.sPSDevice.unregisterDeviceCallBack(this);
        super.onDestroyView();
    }


    private void setEmptyViewVisible(boolean visible) {
        mBinding.viewEmpty.getRoot().setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    /**
     * 今日收益
     */
    private void getStatRevenue() {
        Map<String, Object> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, DsCamCmd.GET_STATS_REVENUE);
        params.put(BmtDataKey.INTERVAL, ChartDataUtil.DAY);
        params.put(BmtDataKey.OFFSET, 0);
        showTimeOutLoadinFramgment();
        ImpactStrategiesFragment.sPSDevice.submit(params);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (ImpactStrategiesFragment.sPSDevice != null
                && StringUtil.isNotEmpty(ImpactStrategiesFragment.sPSDevice.getId())
                && StringUtil.isNotEmpty(deviceId)
                && deviceId.equals(ImpactStrategiesFragment.sPSDevice.getId())
                && subCategory.equals(ImpactStrategiesFragment.sPSDevice.getSubCategory())) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            if (!(isInit && getUserVisibleHint())) return;
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        switch (cmd) {
                            case DsCamCmd.GET_STATS_REVENUE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                List<List<Float>> revenueDayData = (List<List<Float>>) result.get(BmtDataKey.DATA);
                                float sumVal = 0;
                                if (CollectionUtil.isListNotEmpty(revenueDayData)) {
                                    for (List<Float> sonData : revenueDayData) {
                                        if (sonData.size() > 1) {
                                            sumVal = sumVal + sonData.get(1);
//                                            if (PowerStationRootFragment.isAllBSensorInstalled && sonData.size() > 2) {
                                            if (sonData.size() > 2) {
                                                sumVal = sumVal + sonData.get(2);
                                            }
                                        }
                                    }
                                    int interval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);
                                    sumVal = sumVal * interval / 60;
                                    float inverter = ChartDataUtil.getValInNote(PowerStationRoot2Fragment.random, sumVal / 1000);
                                    String inverterStr = ChartDataUtil.savePointStr(inverter, 1);
                                    String value = ChartDataUtil.getRevenueNote(getContext(), inverterStr, PowerStationRoot2Fragment.random);
                                    mBinding.tvValue.setMovementMethod(LinkMovementMethod.getInstance());
                                    mBinding.tvValue.setText(StringUtil.getLargeBoldText(value, inverterStr));
                                    String note = Local.s(getString(R.string.impact_strategies_impacts_note));
                                    String key = getString(R.string.impact_strategies_hashtag_product_consumption);
                                    if (PowerStationRoot2Fragment.random == 0) {
                                        mBinding.tvNote.setText("* " + note.replace(key, "1.25"));
                                    } else if (PowerStationRoot2Fragment.random == 1) {
                                        mBinding.tvNote.setText("* " + note.replace(key, "13"));
                                    } else {
                                        mBinding.tvNote.setLocalText("");
                                    }
                                    int img = ChartDataUtil.getRevenueImgDrawable(PowerStationRoot2Fragment.random);
                                    if (img != 0) {
                                        mBinding.ivIndicator.setImageResource(img);
                                    }
                                }
                                setEmptyViewVisible(CollectionUtil.isListEmpty(revenueDayData));
                                break;
                        }
                    }
                });
            } else {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        switch (cmd) {
                            case DsCamCmd.GET_STATS_REVENUE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                                setEmptyViewVisible(true);
                                break;
                        }
                    }
                });
            }
        }
    }
}
