package com.dinsafer.module.powerstation.adapter;

import androidx.constraintlayout.widget.ConstraintLayout;
import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.bean.ElectricityTabBean;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/10 17:29
 * @description :
 */
public class ElectricityTabAdapter extends BaseQuickAdapter<ElectricityTabBean, BaseViewHolder> {

    public ElectricityTabAdapter() {
        super(R.layout.item_electricity_tab);
    }

    @Override
    protected void convert(BaseViewHolder helper, ElectricityTabBean item) {
        ConstraintLayout clParent = helper.getView(R.id.cl_parent);
        View viewBg = helper.getView(R.id.view_bg);
        ImageView ivIcon = helper.getView(R.id.iv_icon);
        if (item.isSelected()) {
            clParent.setBackgroundResource(R.drawable.bg_electricity_tab_sel);
            viewBg.setVisibility(View.VISIBLE);
            viewBg.setBackgroundResource(item.getSelectedBgColor());
            ivIcon.setImageResource(item.getIconSel());
        } else {
            clParent.setBackgroundResource(R.color.electricity_tab_bg_color);
            viewBg.setVisibility(View.INVISIBLE);
            viewBg.setBackgroundResource(item.getSelectedBgColor());
            ivIcon.setImageResource(item.getIconNor());
        }
    }
}
