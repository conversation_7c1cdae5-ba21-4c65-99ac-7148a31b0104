package com.dinsafer.module.powerstation.widget.reserve_mode_indicator;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.databinding.DataBindingUtil;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutReserveModeIndicatorBinding;
import com.dinsafer.module.powerstation.widget.WrapHeightViewPager;
import com.dinsafer.common.utils.DensityUtil;

import java.util.ArrayList;
import java.util.List;

public class ReserveModeIndicator extends FrameLayout {

    private Context mContext;
    private LayoutReserveModeIndicatorBinding mBinding;
    private int mLastPosition = 0;
    private int mCurrentPosition = 0;
    private List<FrameLayout> flIndicators;
    private List<View> viewIndicators;
    private List<LinearLayout> llIndicators;
    private List<String> indicatorTxtList;
    private int[] viewRes = {R.drawable.shape_bg_reserve_mode_indicator_1,
            R.drawable.shape_bg_reserve_mode_indicator_2,
            R.drawable.shape_bg_reserve_mode_indicator_3,
            R.drawable.shape_bg_reserve_mode_indicator_4,
            R.drawable.shape_bg_reserve_mode_indicator_5};
    private float mTouchX;
    private float mReferenceVal;
    private boolean isEnd = true;
    private ViewPager mViewpager;

    public ReserveModeIndicator(Context context) {
        this(context, null);
    }

    public ReserveModeIndicator(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ReserveModeIndicator(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_reserve_mode_indicator, this, true);
        flIndicators = new ArrayList<>();
        viewIndicators = new ArrayList<>();
        llIndicators = new ArrayList<>();
        indicatorTxtList = new ArrayList<>();

        flIndicators.add(mBinding.flBp);
        flIndicators.add(mBinding.flLpa);
        flIndicators.add(mBinding.flEr);
        flIndicators.add(mBinding.flSr);
        flIndicators.add(mBinding.flPe);

        viewIndicators.add(mBinding.viewBp);
        viewIndicators.add(mBinding.viewLpa);
        viewIndicators.add(mBinding.viewEr);
        viewIndicators.add(mBinding.viewSr);
        viewIndicators.add(mBinding.viewPe);

        llIndicators.add(mBinding.llBp);
        llIndicators.add(mBinding.llLpa);
        llIndicators.add(mBinding.llEr);
        llIndicators.add(mBinding.llSr);
        llIndicators.add(mBinding.llPe);

        indicatorTxtList.add(mContext.getString(R.string.ps_is_battery_protection));
        indicatorTxtList.add(mContext.getString(R.string.power_battery_bar_status_text_4));
        indicatorTxtList.add(mContext.getString(R.string.power_battery_bar_status_text_3));
        indicatorTxtList.add(mContext.getString(R.string.power_battery_bar_status_text_2));
        indicatorTxtList.add(mContext.getString(R.string.power_battery_bar_status_text_1));

        for (int i = 0; i < flIndicators.size(); i++) {
            FrameLayout flIndicator = flIndicators.get(i);
            int finalI = i;
            flIndicator.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    setIndicatorSelected(finalI, true);
                }
            });
        }
        mReferenceVal = DensityUtil.dp2px(mContext, 28);
//        mBinding.flThumb.setOnTouchListener(new OnTouchListener() {
//            @Override
//            public boolean onTouch(View view, MotionEvent motionEvent) {
//                int action = motionEvent.getAction();
//                switch (action) {
//                    case MotionEvent.ACTION_DOWN:
//                        mTouchX = motionEvent.getX();
//                        break;
//
//                    case MotionEvent.ACTION_MOVE:
//                        float moveX = motionEvent.getX();
//                        float distanceX = moveX - mTouchX;
//                        DDLog.i("XXx", "mTouchX==="+mTouchX);
//                        DDLog.i("XXx", "moveX==="+moveX);
//                        DDLog.i("XXx", "distanceX==="+distanceX);
//                        if (moveX > mTouchX) {
//                            if (distanceX > mReferenceVal && mCurrentPosition < 4 && isEnd) {
//                                setIndicatorSelected(mCurrentPosition + 1, true);
//                                isEnd = false;
//                                mTouchX = moveX;
//                            }
//                        } else {
//                            if (distanceX < -mReferenceVal && mCurrentPosition > 0  && isEnd) {
//                                setIndicatorSelected(mCurrentPosition - 1, true);
//                                isEnd = false;
//                                mTouchX = moveX;
//                            }
//                        }
//                        break;
//                }
//                return true;
//            }
//        });
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            mTouchX = event.getX();
        }
        long downTime = event.getDownTime();
        long eventTime = event.getEventTime();
        long interval = eventTime - downTime;
        return interval > 100;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                performClick();
                getParent().requestDisallowInterceptTouchEvent(true);
                mTouchX = event.getX();
                break;

            case MotionEvent.ACTION_MOVE:
                float moveX = event.getX();
                float distanceX = moveX - mTouchX;

                if (distanceX > 0) {
                    if (mCurrentPosition > 0 && isEnd) {
                        setIndicatorSelected(mCurrentPosition - 1, true);
                        isEnd = false;
                        mTouchX = moveX;
                    }


                } else if (distanceX < 0) {
                    if (mCurrentPosition < 4 && isEnd) {
                        setIndicatorSelected(mCurrentPosition + 1, true);
                        isEnd = false;
                        mTouchX = moveX;
                    }
                }

            case MotionEvent.ACTION_UP:
                break;
        }
        return true;
    }

    private void setIndicatorSelected(int index, boolean isSelf) {
        if (index == mCurrentPosition) return;
        mLastPosition = mCurrentPosition;
        mCurrentPosition = index;
        float lastSelectedPosition = flIndicators.get(mLastPosition).getX();
        float tempPosition = lastSelectedPosition + (viewIndicators.get(mCurrentPosition).getWidth()
                + DensityUtil.dp2px(mContext, 24)) * (mCurrentPosition - mLastPosition);
        float nowSelectedPosition = mLastPosition < mCurrentPosition ?
                tempPosition :
                flIndicators.get(mCurrentPosition).getX();

        float[] x = {lastSelectedPosition, nowSelectedPosition};
        ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(
                mBinding.flThumb,
                "translationX",
                x
        );
        objectAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
                mBinding.viewThumb.setBackgroundResource(viewRes[mCurrentPosition]);
                mBinding.tvThumb.setLocalText(indicatorTxtList.get(mCurrentPosition));
                if (mViewpager != null && isSelf) {
                    mViewpager.setCurrentItem(mCurrentPosition);
                }
                if (isSelf && selectedListener != null) {
                    selectedListener.onSelected(index);
                }
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                isEnd = true;
                llIndicators.get(mLastPosition).setVisibility(GONE);
                llIndicators.get(mCurrentPosition).setVisibility(INVISIBLE);
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        objectAnimator.setDuration(200);
        objectAnimator.start();
    }

    public void setupViewpager(ViewPager viewPager) {
        mViewpager = viewPager;
        mViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if (viewPager instanceof WrapHeightViewPager) {
                    ((WrapHeightViewPager) viewPager).updateHeight(position);
                }
                setIndicatorSelected(position, false);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    private OnSelectedListener selectedListener;

    public void setSelectedListener(OnSelectedListener selectedListener) {
        this.selectedListener = selectedListener;
    }

    public interface OnSelectedListener {
        void onSelected(int position);
    }
}
