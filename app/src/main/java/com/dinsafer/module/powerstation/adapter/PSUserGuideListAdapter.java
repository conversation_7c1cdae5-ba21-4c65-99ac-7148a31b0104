package com.dinsafer.module.powerstation.adapter;

import android.view.View;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.PSUserGuideListFragment;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 18:20
 * @description :
 */
public class PSUserGuideListAdapter extends BaseQuickAdapter<PSUserGuideListFragment.PSUserGuideEnum, BaseViewHolder> {

    public PSUserGuideListAdapter() {
        super(R.layout.item_ps_user_guide);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, PSUserGuideListFragment.PSUserGuideEnum item) {
        View viewLine = helper.getView(R.id.view_line);
        viewLine.setVisibility(helper.getAdapterPosition() == getData().size() - 1 ? View.GONE : View.VISIBLE);
        LocalTextView tvName = helper.getView(R.id.tv_name);
        tvName.setLocalText(item.getStringResourcesId());
    }

}
