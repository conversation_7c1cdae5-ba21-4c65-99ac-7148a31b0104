package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.content.res.Resources;
import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.OverScroller;

import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.module.powerstation.event.RecyclerViewDragOnTouchEvent;

import org.greenrobot.eventbus.EventBus;

public class DragSelectTouchListener implements RecyclerView.OnItemTouchListener {

    private boolean needSwitch;
    private boolean isActive;
    private boolean isReverse;
    private int start, end;
    private int certainStartX = -1, certainEndX = -1;

    private onSelectListener selectListener;

    private RecyclerView recyclerView;

    private static final int DELAY = 25;

    private int autoScrollDistance = (int) (Resources.getSystem().getDisplayMetrics().density * 56);

    private int mTopBound, mBottomBound;

    private boolean inTopSpot, inBottomSpot;

    private Handler autoScrollHandler = new Handler(Looper.getMainLooper());

    private int scrollDistance;

    private float lastX, lastY;

    private static final int MAX_SCROLL_DISTANCE = 16;

    //这个数越大，滚动的速度增加越慢
    private static final int SCROLL_FECTOR = 6;

    private int lastStart, lastEnd;

    private OverScroller scroller;

    // 是否可触摸操作
    private boolean canTouch = true;
    private int canTouchStartPosition = -1;
    private int canTouchEndPosition = -1;

    private Runnable scrollRunnable = new Runnable() {
        @Override
        public void run() {
            if (!inTopSpot && !inBottomSpot) {
                return;
            }
            scrollBy(scrollDistance);
            autoScrollHandler.postDelayed(this, DELAY);
        }
    };

    public void setSelectListener(onSelectListener selectListener) {
        this.selectListener = selectListener;
    }

    public interface onSelectListener {

        void onUp();

        void onStartChange(int position);

        /**
         * 选择结果的回调
         *
         * @param start 开始的位置
         * @param end   结束的位置
         */
        void onSelectChange(int start, int end, boolean isSelected);
    }

    public DragSelectTouchListener() {
        reset();
    }

    public DragSelectTouchListener(boolean needSwitch) {
        this.needSwitch = needSwitch;
        this.isActive = !needSwitch;
        reset();
    }

    public DragSelectTouchListener(boolean needSwitch, int certainStartX, int certainEndX, boolean isReverse) {
        this.needSwitch = needSwitch;
        this.certainStartX = certainStartX;
        this.certainEndX = certainEndX;
        this.isReverse = isReverse;
        this.isActive = !needSwitch;
        reset();
    }

    public DragSelectTouchListener(boolean needSwitch, int certainStartX, int certainEndX,
                                   int canTouchStartPosition, int canTouchEndPosition, boolean isReverse) {
        this.needSwitch = needSwitch;
        this.certainStartX = certainStartX;
        this.certainEndX = certainEndX;
        this.canTouchStartPosition = canTouchStartPosition;
        this.canTouchEndPosition = canTouchEndPosition;
        this.isReverse = isReverse;
        this.isActive = !needSwitch;
        reset();
    }

    private float mDownX, mDownY, mUpX, mUpY;
    private long mLastTime, differentTime;

    float touchX = -1f;
    private boolean isTouched;

    private void dealNoIntercept(int action) {
        if (action == MotionEvent.ACTION_DOWN ||
                action == MotionEvent.ACTION_POINTER_DOWN) {
            isTouched = true;
        } else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL
                || action == MotionEvent.ACTION_POINTER_UP) {
            isTouched = false;
        }
    }

    @Override
    public boolean onInterceptTouchEvent(RecyclerView rv, MotionEvent e) {
        int action = e.getAction();
        if (!canTouch) {
            dealNoIntercept(action);
            return false;
        }
        int touchPosition = getTouchPosition(rv, e.getX(), e.getY());
        if (canTouchStartPosition > -1) {
            if (touchPosition < canTouchStartPosition) {
                dealNoIntercept(action);
                return false;
            }
        }
        if (canTouchEndPosition > -1) {
            if (touchPosition > canTouchEndPosition) {
                dealNoIntercept(action);
                return false;
            }
        }

        if (action == MotionEvent.ACTION_DOWN ||
                action == MotionEvent.ACTION_POINTER_DOWN) {
            touchX = e.getX();
            EventBus.getDefault().post(new RecyclerViewDragOnTouchEvent());
            isTouched = true;
        } else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL
                || action == MotionEvent.ACTION_POINTER_UP) {
            isTouched = false;
        }

        if (touchX > -1f && certainStartX > -1f && certainEndX > -1f && !(touchX >= certainStartX && touchX < certainEndX)) {
            return false;
        }
        if (!isActive || rv.getAdapter().getItemCount() == 0) {
            return false;
        }

        switch (action) {
            case MotionEvent.ACTION_POINTER_DOWN:

            case MotionEvent.ACTION_DOWN:
                mDownX = e.getX();
                mDownY = e.getY();
                mUpX = e.getX();
                mUpY = e.getY();
                mLastTime = System.currentTimeMillis();
                if (needSwitch) {
                    reset();
                } else {
                    View child = rv.findChildViewUnder(e.getX(), e.getY());
                    if (child != null) {
                        int position = rv.getChildAdapterPosition(child);
                        setStartSelectPosition(position);
                    }
                }
                break;

            case MotionEvent.ACTION_MOVE:
                mUpX = e.getX();
                mUpY = e.getY();
                break;

            case MotionEvent.ACTION_UP:
                touchX = -1;
                if (selectListener != null) {
                    selectListener.onUp();
                }
                break;
        }
        recyclerView = rv;
        int height = rv.getHeight();
        mTopBound = -20;
        mBottomBound = height - autoScrollDistance;
        differentTime = System.currentTimeMillis() - mLastTime;

        if (differentTime < 200 && mDownX == mUpX && mDownY == mUpY) {
            return false;
        } else {
            return true;
        }
    }

    public void startAutoScroll() {
        if (recyclerView == null) {
            return;
        }
        initScroller(recyclerView.getContext());
        if (scroller.isFinished()) {
            recyclerView.removeCallbacks(scrollRun);
            scroller.startScroll(0, scroller.getCurrY(), 0, 5000, 100000);
            ViewCompat.postOnAnimation(recyclerView, scrollRun);
        }
    }

    private void initScroller(Context context) {
        if (scroller == null) {
            scroller = new OverScroller(context, new LinearInterpolator());
        }
    }

    public void stopAutoScroll() {
        if (scroller != null && !scroller.isFinished()) {
            recyclerView.removeCallbacks(scrollRun);
            scroller.abortAnimation();
        }
    }

    private Runnable scrollRun = new Runnable() {
        @Override
        public void run() {
            if (scroller != null && scroller.computeScrollOffset()) {
                scrollBy(scrollDistance);
                ViewCompat.postOnAnimation(recyclerView, scrollRun);
            }
        }
    };

    @Override
    public void onTouchEvent(RecyclerView rv, MotionEvent e) {
        int action = e.getActionMasked();
        if (!isActive) {
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    isTouched = true;
                    break;

                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_POINTER_UP:
                case MotionEvent.ACTION_UP:
                    isTouched = false;
                    break;
            }
            return;
        }

        switch (action) {

            case MotionEvent.ACTION_MOVE:
//                if (!inTopSpot && !inBottomSpot) {
                //更新滑动选择区域
//                    updateSelectedRange(rv, e);
//                }
                //在顶部或者底部触发自动滑动
                processAutoScroll(e);
                updateSelectedRange(rv, e);
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_POINTER_UP:
                //结束滑动选择，初始化各状态值
                reset();
                if (selectListener != null) {
                    selectListener.onUp();
                }
                isTouched = false;
                break;
        }
    }

    private void updateSelectedRange(RecyclerView rv, MotionEvent e) {
        updateSelectedRange(rv, e.getX(), e.getY());
    }

    private void updateSelectedRange(RecyclerView rv, float x, float y) {
        View child = rv.findChildViewUnder(x, y);
        if (child == null) {
            if (rv.getChildCount() > 0) {
                if (inTopSpot) {
                    child = rv.getChildAt(0);
                } else if (inBottomSpot) {
                    child = rv.getChildAt(rv.getChildCount() - 1);
                }
            }
        }
        if (child != null) {
            int position = rv.getChildAdapterPosition(child);
            if (canTouchStartPosition > -1 && position < canTouchStartPosition) return;
            if (canTouchEndPosition > -1 && position > canTouchEndPosition) return;
            if (position != RecyclerView.NO_POSITION && end != position) {
                end = position;
                if (isReverse) {
                    notifySelectRangeChangeReverse();
                } else {
                    notifySelectRangeChange();
                }
            }
        }
    }

    private int getTouchPosition(RecyclerView rv, float x, float y) {
        View child = rv.findChildViewUnder(x, y);
        if (child != null) {
            int position = rv.getChildAdapterPosition(child);
            return position;
        }
        return -1;
    }

    private void notifySelectRangeChangeReverse() {
        if (selectListener == null) {
            return;
        }
        if (start == RecyclerView.NO_POSITION || end == RecyclerView.NO_POSITION) {
            return;
        }

        int newStart, newEnd;
        newStart = Math.min(start, end);
        newEnd = Math.max(start, end);
        if (lastStart == RecyclerView.NO_POSITION || lastEnd == RecyclerView.NO_POSITION) {
            if (newEnd - newStart == 1) {
                selectListener.onSelectChange(newStart, newStart, true);
            } else {
                selectListener.onSelectChange(newStart, newEnd, true);
            }
        } else {
            if (newStart > lastStart) {
                selectListener.onSelectChange(lastStart, newStart - 1, false);
            } else if (newStart < lastStart) {
                selectListener.onSelectChange(newStart, lastStart - 1, true);
            }

            if (newEnd > lastEnd) {
                selectListener.onSelectChange(lastEnd + 1, newEnd, true);
            } else if (newEnd < lastEnd) {
                selectListener.onSelectChange(newEnd + 1, lastEnd, false);
            }
        }

        lastStart = newStart;
        lastEnd = newEnd;
    }


    private void processAutoScroll(MotionEvent event) {
        int y = (int) event.getY();
        if (y < mTopBound) {
            lastX = event.getX();
            lastY = event.getY();
            scrollDistance = -(mTopBound - y) / SCROLL_FECTOR;
            if (!inTopSpot) {
                inTopSpot = true;
                startAutoScroll();
            }
        } else if (y > mBottomBound) {
            lastX = event.getX();
            lastY = event.getY();
            scrollDistance = (y - mBottomBound) / SCROLL_FECTOR;
            if (!inBottomSpot) {
                inBottomSpot = true;
                startAutoScroll();
            }
        } else {
            inBottomSpot = false;
            inTopSpot = false;
            lastX = Float.MIN_VALUE;
            lastY = Float.MIN_VALUE;
            stopAutoScroll();
        }
    }

    private void notifySelectRangeChange() {
        if (selectListener == null) {
            return;
        }
        if (start == RecyclerView.NO_POSITION || end == RecyclerView.NO_POSITION) {
            return;
        }
        selectListener.onSelectChange(start, end, true);
    }

    private void reset() {
        if (needSwitch) {
            setIsActive(false);
        }
        start = RecyclerView.NO_POSITION;
        end = RecyclerView.NO_POSITION;
        lastStart = RecyclerView.NO_POSITION;
        lastEnd = RecyclerView.NO_POSITION;
        autoScrollHandler.removeCallbacks(scrollRunnable);
        inTopSpot = false;
        inBottomSpot = false;
        lastX = Float.MIN_VALUE;
        lastY = Float.MIN_VALUE;
        stopAutoScroll();
    }

    @Override
    public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {

    }

    private void scrollBy(int distance) {
        int scrollDistance;
        if (distance > 0) {
            scrollDistance = Math.min(distance, MAX_SCROLL_DISTANCE);
        } else {
            scrollDistance = Math.max(distance, -MAX_SCROLL_DISTANCE);
        }
        recyclerView.scrollBy(0, scrollDistance);
        if (lastX != Float.MIN_VALUE && lastY != Float.MIN_VALUE) {
            updateSelectedRange(recyclerView, lastX, lastY);
        }
    }


    public void setIsActive(boolean isActive) {
        this.isActive = isActive;
    }

    public void setStartSelectPosition(int position) {
        if (needSwitch) {
            setIsActive(true);
        }
        start = position;
        end = position;
        lastStart = position;
        lastEnd = position;
        if (selectListener != null) {
            selectListener.onStartChange(position);
        }
    }

    public void setCanTouch(boolean canTouch) {
        this.canTouch = canTouch;
    }

    public boolean isTouched() {
        return isTouched;
    }

    public int getCanTouchStartPosition() {
        return canTouchStartPosition;
    }

    public void setCanTouchStartPosition(int canTouchStartPosition) {
        this.canTouchStartPosition = canTouchStartPosition;
    }

    public int getCanTouchEndPosition() {
        return canTouchEndPosition;
    }

    public void setCanTouchEndPosition(int canTouchEndPosition) {
        this.canTouchEndPosition = canTouchEndPosition;
    }
}
