package com.dinsafer.module.powerstation.electricity.helper;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;

import java.util.ArrayList;
import java.util.List;

public class UsageChartHelper {

    private final Context mContext;
    private final int bmtType;

    public UsageChartHelper(Context mContext, int bmtType) {
        this.mContext = mContext;
        this.bmtType = bmtType;
    }

    public ArrayList<PSElectricityTypeBean> getFilterData() {
        ArrayList<PSElectricityTypeBean> data = new ArrayList<>();
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            data.add(new PSElectricityTypeBean(R.drawable.shape_circle_4485e8, R.drawable.shape_electricity_keep_on_normal, mContext.getString(R.string.power_station_keep_on_load), true));
            data.add(new PSElectricityTypeBean(R.drawable.shape_circle_679ef1, R.drawable.shape_electricity_additional_normal,  mContext.getString(R.string.power_station_additional_load), true));
            data.add(new PSElectricityTypeBean(R.drawable.shape_circle_94bbf8, R.drawable.shape_electricity_vehicle_normal,  mContext.getString(R.string.power_station_vehicle), true));
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            data.add(new PSElectricityTypeBean(R.drawable.shape_circle_4485e8, R.drawable.shape_electricity_keep_on_normal, mContext.getString(R.string.power_station_keep_on_load), true));
            data.add(new PSElectricityTypeBean(R.drawable.shape_circle_679ef1, R.drawable.shape_electricity_additional_normal,  mContext.getString(R.string.power_station_additional_load), true));
        }
        return data;
    }

    public ArrayList<Entry> getUsageEntries(List<List<Float>> chartData, FlipCombinedChartView flipCombinedChartView) {
        ArrayList<Entry> entries = new ArrayList<Entry>();
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            for (int i = 0; i < chartData.size(); i++) {
                List<Float> sonData = chartData.get(i);
                float val = 0;
                if (flipCombinedChartView.isFilterSelected(0)) {
                    val = val + sonData.get(1);
                }
                if (flipCombinedChartView.isFilterSelected(1)) {
//                if (isBSensorInstalled) {
//                    val = val + sonData.get(2);
//                } else {
//                    val = val + sonData.get(3);
//                }
                    val = val + sonData.get(2);
                }
//            if (isBSensorInstalled) {
//                if (mFlipCombinedChartView.isFilterSelected(2)) {
//                    val = val + sonData.get(3);
//                }
//            }
                if (flipCombinedChartView.isFilterSelected(2)) {
                    val = val + sonData.get(3);
                }
                entries.add(new Entry(val, i));
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            for (int i = 0; i < chartData.size(); i++) {
                List<Float> sonData = chartData.get(i);
                float val = 0;
                if (flipCombinedChartView.isFilterSelected(0)) {
                    val = val + sonData.get(1);
                }
                if (flipCombinedChartView.isFilterSelected(1)) {
                    val = val + sonData.get(2);
                }
                entries.add(new Entry(val, i));
            }
        }
        return entries;
    }

    public BarEntry getBarEntry(FlipCombinedChartView flipCombinedChartView,List<Float> sonData, float val2, int xIndex) {
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
           return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
                    flipCombinedChartView.isFilterSelected(1) ? val2 : 0,
                    flipCombinedChartView.isFilterSelected(2) ? sonData.get(3) : 0}, xIndex);
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
                    flipCombinedChartView.isFilterSelected(1) ? val2 : 0}, xIndex);
        } else {
            return new BarEntry(new float[]{}, xIndex);
        }
    }

    public int[] getBarColors(boolean isSuccess) {
        int[] barColors;
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            if (isSuccess) {
                barColors = new int[]{mContext.getResources().getColor(R.color.color_tip_05),
                        mContext.getResources().getColor(R.color.color_tip_05_2),
                        mContext.getResources().getColor(R.color.color_tip_05_3)};
            } else {
                barColors = new int[]{mContext.getResources().getColor(R.color.transparent),
                        mContext.getResources().getColor(R.color.transparent),
                        mContext.getResources().getColor(R.color.transparent)};
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            if (isSuccess) {
                barColors = new int[]{mContext.getResources().getColor(R.color.color_tip_05),
                        mContext.getResources().getColor(R.color.color_tip_05_2)};
            } else {
                barColors = new int[]{mContext.getResources().getColor(R.color.transparent),
                        mContext.getResources().getColor(R.color.transparent)};
            }
        } else {
            barColors = new int[]{};
        }
       return barColors;
    }
}
