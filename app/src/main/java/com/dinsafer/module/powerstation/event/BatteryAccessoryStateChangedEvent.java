package com.dinsafer.module.powerstation.event;

public class BatteryAccessoryStateChangedEvent {

    private String deviceId;
    private String subcategory;
    private int index;
    private boolean heating;
    private boolean heatAvailable;
    private int cabinetIndex;
    private int cabinetPositionIndex;

    public BatteryAccessoryStateChangedEvent(String deviceId, String subcategory, int index, boolean heating) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.index = index;
        this.heating = heating;
    }

    public BatteryAccessoryStateChangedEvent(String deviceId, String subcategory, boolean heating, int cabinetIndex, int cabinetPositionIndex) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.heating = heating;
        this.cabinetIndex = cabinetIndex;
        this.cabinetPositionIndex = cabinetPositionIndex;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public boolean isHeating() {
        return heating;
    }

    public void setHeating(boolean heating) {
        this.heating = heating;
    }

    public boolean isHeatAvailable() {
        return heatAvailable;
    }

    public void setHeatAvailable(boolean heatAvailable) {
        this.heatAvailable = heatAvailable;
    }

    public int getCabinetIndex() {
        return cabinetIndex;
    }

    public void setCabinetIndex(int cabinetIndex) {
        this.cabinetIndex = cabinetIndex;
    }

    public int getCabinetPositionIndex() {
        return cabinetPositionIndex;
    }

    public void setCabinetPositionIndex(int cabinetPositionIndex) {
        this.cabinetPositionIndex = cabinetPositionIndex;
    }
}
