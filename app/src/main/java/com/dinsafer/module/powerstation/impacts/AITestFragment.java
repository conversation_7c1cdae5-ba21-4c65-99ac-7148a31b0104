package com.dinsafer.module.powerstation.impacts;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAiTestBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class AITestFragment extends MyBaseFragment<FragmentAiTestBinding> {

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ai_test;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
//        AIDataManager aiDataManager = new AIDataManager(mBinding.viewChart);
//        aiDataManager.setHighlightListener(index -> DDLog.i("XXX", "index==="+index));

        Random random = new Random();
        List<Float> forecastSolars = new ArrayList<>();
        for (int i = 0; i < 36; i++) {
            float num = random.nextInt(100);
            forecastSolars.add(num);
        }
        List<Float> relativePriceNorms = new ArrayList<>();
        List<Float> mHelpData = new ArrayList<>();
        for (int i = 0; i < 36; i++) {
            float price = (random.nextInt(200) - 100) / 100f;
            relativePriceNorms.add(price);
            mHelpData.add(0f);
        }
        Integer[] weekdays = new Integer[24];
        for (int i = 0; i < 24; i++) {
            weekdays[i] = -128;
        }
        List<Integer> hopeChargeDischarges = new ArrayList<>();
        for (int i = 0; i < 36; i++) {
            int num = random.nextInt(200) - 100;
            hopeChargeDischarges.add(num);
        }
        long startTime = System.currentTimeMillis() / 1000;
        String timezone = "Asia/Shanghai";
        List<Long> sunriseSunsetDates = new ArrayList<>();
        sunriseSunsetDates.add(1745317825L);
        sunriseSunsetDates.add(1745361025L);
        sunriseSunsetDates.add(1745404225L);
        float absZeroEcPrices = -0.5f;
        int c1 = -60;
        int c2 = -40;
        int c3 = -20;
        int s1 = 20;
        int s2 = 40;
        AIDataManager aiDataManager = new AIDataManager.Builder(mBinding.viewChart)
                .setC1(c1)
                .setC2(c2)
                .setC3(c3)
                .setS1(s1)
                .setS1(s2)
                .setStartTime(startTime)
                .setTimezone(timezone)
                .setAbsZeroEcPrices(absZeroEcPrices)
                .setConditions(new ArrayList<>())
//                .setForecastSolars(forecastSolars)
                .setHopeChargeDischarges(hopeChargeDischarges)
                .setMarketPrices(new ArrayList<>())
                .setPlans(new ArrayList<>())
                .setRelativePriceNorms(relativePriceNorms)
                .setSunriseSunsetDates(sunriseSunsetDates)
                .setUserPrices(new ArrayList<>())
                .setWeekdays(weekdays)
                .setHighlightListener(new AIDataManager.OnHighlightListener() {
                    @Override
                    public void highlight(int index) {
                        // 这里只给索引, 要显示出来的数据根据索引从对应列表获取数据
                        DDLog.i("XXX", "index==="+index);
                    }
                }).build();

        // 这只是做个设置默认高亮演示
        mBinding.viewChart.postDelayed(() -> aiDataManager.setDefaultHighlight(), 5000);
    }
    private int getColor(int color) {
        return getContext().getResources().getColor(color);
    }
}
