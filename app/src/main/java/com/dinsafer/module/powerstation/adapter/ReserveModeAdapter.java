package com.dinsafer.module.powerstation.adapter;


import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ReserveModeBean;
import com.dinsafer.ui.LocalTextView;

import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/25 14:07
 * @description :
 */
public class ReserveModeAdapter extends BaseMultiItemQuickAdapter<ReserveModeBean, BaseViewHolder> {

    private OnEditClickListener mEditClickListener;

    /**
     * Same as QuickAdapter#QuickAdapter(Context,int) but with
     * some initialization data.
     *
     * @param data A new list is created out of this one to avoid mutable list
     */
    public ReserveModeAdapter(List<ReserveModeBean> data) {
        super(data);
        addItemType(ReserveModeBean.ITEM_MODE, R.layout.item_reserve_mode);
        addItemType(ReserveModeBean.ITEM_TIP, R.layout.item_reserve_tip);
    }

    public void setEditClickListener(OnEditClickListener editClickListener) {
        this.mEditClickListener = editClickListener;
    }

    @Override
    protected void convert(BaseViewHolder helper, ReserveModeBean item) {
        if (helper.getItemViewType() == ReserveModeBean.ITEM_MODE) {
            helper.addOnClickListener(R.id.tv_edit);
            ConstraintLayout clParent = helper.getView(R.id.cl_parent);
            clParent.setSelected(item.isSelected() && item.isSelected());
            clParent.setAlpha(item.isEnabled() ? 1f : 0.5f);
            helper.setImageResource(R.id.iv_logo, item.getLogo());
            LocalTextView tvName = helper.getView(R.id.tv_name);
            LocalTextView tvDesc = helper.getView(R.id.tv_desc);
            tvName.setLocalText(item.getName());
            tvDesc.setLocalText(item.getDesc());
            List<ReserveModeBean.PriceBean> data = item.getPrices();
            RecyclerView recyclerView = helper.getView(R.id.rv_price);
            recyclerView.setVisibility(data.size() > 0 ? View.VISIBLE : View.GONE);
            LocalTextView tvEdit = helper.getView(R.id.tv_edit);
            tvEdit.setVisibility(item.isEnabled() && item.isSelected() ? View.VISIBLE : View.GONE);
            if (item.isEnabled() && data.size() > 0) {
                recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
                ReserveModePriceAdapter reserveModePriceAdapter = new ReserveModePriceAdapter();
                if (item.isSelected()) {
                    reserveModePriceAdapter.setNewData(data);
                } else {
                    reserveModePriceAdapter.setNewData(null);
                }
                reserveModePriceAdapter.setOnItemClickListener(new OnItemClickListener() {
                    @Override
                    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                        if (mEditClickListener != null) {
                            mEditClickListener.onItem(reserveModePriceAdapter.getItem(position), helper.getAdapterPosition(), position);
                        }
                    }
                });
                reserveModePriceAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
                    @Override
                    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                        int viewId = view.getId();
                        if (viewId == R.id.tv_edit) {
                            if (mEditClickListener != null) {
                                mEditClickListener.onEdit(reserveModePriceAdapter.getItem(position), helper.getAdapterPosition(), position);
                            }
                        }
                    }
                });
                recyclerView.setAdapter(reserveModePriceAdapter);
            }
        }
    }

    public interface OnEditClickListener {
        void onItem(ReserveModeBean.PriceBean item, int parentPosition, int position);

        void onEdit(ReserveModeBean.PriceBean item, int parentPosition, int position);
    }
}
