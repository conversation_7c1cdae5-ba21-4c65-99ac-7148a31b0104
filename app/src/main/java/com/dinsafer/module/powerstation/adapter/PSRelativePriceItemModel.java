package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsRelativePriceBinding;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.utils.RelativePriceChartHelper;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

public class PSRelativePriceItemModel extends BasePowerStationItemModel<ItemPsRelativePriceBinding> {

    private List<List<Float>> data = new ArrayList<>();
    private int interval;
    private long startTime;
    private String timezone = "";
    private String unit;
    private String showPriceUnit;
    private boolean isSuccess;

    private boolean isShow = true;

    private RelativePriceChartHelper mRelativePriceChartHelper;
    private ItemPsRelativePriceBinding mBinding;

    public PSRelativePriceItemModel(Context context, String deviceId, String subcategory) {
        super(context, deviceId, subcategory);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_relative_price;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsRelativePriceBinding binding) {
        mRelativePriceChartHelper = new RelativePriceChartHelper(mContext, binding.ccPrice);
        mBinding = binding;
        refresh();
    }

    public void refresh() {
        if (mBinding != null && mRelativePriceChartHelper != null) {
            String relativePriceUnit = "%";
            float peakPrice = mRelativePriceChartHelper.getRelativePriceMax(data);
            String peakPriceStr = isSuccess ? ChartDataUtil.savePointStr(Math.abs(peakPrice), 2) : "-";
            mBinding.tvPriceValue.setText(peakPriceStr);
            mBinding.tvPriceUnit.setText(relativePriceUnit);
            float troughPrice = mRelativePriceChartHelper.getRelativePriceMin(data);
            String troughPriceStr = isSuccess ? ChartDataUtil.savePointStr(Math.abs(troughPrice), 2) : "-";
            String finalTroughPriceStr = Local.s(mContext.getString(R.string.trough)) + " "
                    + troughPriceStr + relativePriceUnit;
            mBinding.tvTrough.setText(finalTroughPriceStr);
            mRelativePriceChartHelper.setPriceData(data, startTime, timezone);
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) mBinding.clPrice.getLayoutParams();
            params.height = DensityUtils.dp2px(mContext, isShow ? ViewGroup.LayoutParams.WRAP_CONTENT : 0);
            params.topMargin = isShow ? (int) mContext.getResources().getDimension(R.dimen.power_station_today_usage_margin_top) : 0;
            mBinding.clPrice.setLayoutParams(params);
            mBinding.clPrice.setVisibility(isShow ? View.VISIBLE : View.GONE);
        }
    }

    public void setData(List<List<Float>> data) {
        this.data = data;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setShowPriceUnit(String showPriceUnit) {
        this.showPriceUnit = showPriceUnit;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    public void setShow(boolean show) {
        isShow = show;
    }
}
