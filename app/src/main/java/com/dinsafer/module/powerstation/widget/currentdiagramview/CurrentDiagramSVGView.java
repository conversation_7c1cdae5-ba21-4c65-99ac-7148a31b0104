package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.SweepGradient;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.LinearInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.DDLog;

import java.util.HashMap;
import java.util.Map;

public class CurrentDiagramSVGView extends View {

    private final String TAG = CurrentDiagramSVGView.class.getSimpleName();
    private static final int CURRENT_POWER_CORE = 0;
    private static final int CURRENT_POWER_STORE = 1;
    private static final int CURRENT_POWER_PULSE = 2;

    private final Matrix mCanvasMatrix = new Matrix();

    private Context mContext;
    private Paint mPaint;

    private Paint mCirclePaint;
    private float mX = 104;
    private float mY = 66;
    private float mRadius = 20f;
    private int[] mCircleColors;
    private float[] mCirclePositions;
    private RectF mRectF;
    private ValueAnimator mRotateAnimator;
    private float mRotateValue;
    private boolean isDrawCircle = true;

    private ValueAnimator mValueAnimator;
    private BaseCurrentView mCurrentView;
    private int mTopoColor = Color.BLACK;
    private float mBaseWidth = 230f;
    private float mBaseHeight = 147f;
    private int mCurrentType;
    private float mScale = 1.0f;
    private int mStartColor = Color.parseColor("#104485E8");
    private int mEndColor = Color.parseColor("#4485E8");
    private HashMap<String, CurrentPathBean> mPathMap = new HashMap<>();

    private boolean isStop;
    private final int REFRESH_CURRENT = 0X01;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mAnimRunnable = new Runnable() {
        @Override
        public void run() {
            if (mValueAnimator != null) {
                mValueAnimator.start();
            }
        }
    };

    public CurrentDiagramSVGView(Context context) {
        this(context, null);
    }

    public CurrentDiagramSVGView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CurrentDiagramSVGView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CurrentDiagramSVGView);
        mTopoColor = typedArray.getColor(R.styleable.CurrentDiagramSVGView_topo_color, mTopoColor);
        mBaseWidth = typedArray.getFloat(R.styleable.CurrentDiagramSVGView_topo_base_width, 230f);
        mBaseHeight = typedArray.getFloat(R.styleable.CurrentDiagramSVGView_topo_base_height, 147f);
        mX = typedArray.getFloat(R.styleable.CurrentDiagramSVGView_topo_circle_x, 104f);
        mY = typedArray.getFloat(R.styleable.CurrentDiagramSVGView_topo_circle_y, 66f);
        mCurrentType = typedArray.getInt(R.styleable.CurrentDiagramSVGView_current_type, CURRENT_POWER_CORE);
        typedArray.recycle();
        mContext = context;
        if (mCurrentType == CURRENT_POWER_STORE) {
            mCurrentView = new PowerStoreCurrentView();
        } else if (mCurrentType == CURRENT_POWER_PULSE) {
            mCurrentView = new PowerPulseCurrentView();
        } else {
            mCurrentView = new PowerCoreCurrentView();
        }
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mCirclePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mCirclePaint.setStyle(Paint.Style.STROKE);
        mCirclePaint.setStrokeCap(Paint.Cap.ROUND);
        mRectF = new RectF(mX - mRadius, mY - mRadius, mX + mRadius, mY + mRadius);
        mValueAnimator = ValueAnimator.ofFloat(0, 1);
        mValueAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!isStop) {
                    mHandler.postDelayed(mAnimRunnable, 500);
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                float value = (float) valueAnimator.getAnimatedValue();
                for (Map.Entry<String, CurrentPathBean> map : mPathMap.entrySet()) {
                    map.getValue().setAnimatorValue(value);
                }
                invalidate();
            }
        });
        mValueAnimator.setDuration(2000);
        mValueAnimator.setInterpolator(new AccelerateDecelerateInterpolator());


        mRotateAnimator = ValueAnimator.ofFloat(360f, 0f);
        mRotateAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(@NonNull ValueAnimator valueAnimator) {
                mRotateValue = (float) valueAnimator.getAnimatedValue();
                invalidate();
            }
        });
        mRotateAnimator.setDuration(2000);
        mRotateAnimator.setRepeatCount(ValueAnimator.INFINITE);
        mRotateAnimator.setInterpolator(new LinearInterpolator());
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mScale = Math.min(getWidth() / mBaseWidth, getHeight() / mBaseHeight);
        mCirclePaint.setStrokeWidth(0.5f * mScale);
        mCanvasMatrix.preScale(mScale, mScale);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.setMatrix(mCanvasMatrix);
        drawTB(canvas);
        for (Map.Entry<String, CurrentPathBean> map : mPathMap.entrySet()) {
            drawCurrent(canvas, map.getValue());
        }
        drawCircle(canvas);
    }

    /**
     * 画圆
     *
     * @param canvas
     */
    private void drawCircle(Canvas canvas) {
        if (hasPathThroughInverter()) {
            if (mCircleColors == null || mCircleColors.length < 1
                    || mCirclePositions == null || mCirclePositions.length < 1
                    || mCircleColors.length != mCirclePositions.length) {
                DDLog.i(TAG, "请设置正确颜色渐变属性");
                return;
            }
            canvas.save();
            canvas.rotate(mRotateValue, mX, mY);
            SweepGradient gradient = new SweepGradient(mX, mY, mCircleColors, mCirclePositions);
            mCirclePaint.setShader(gradient);
            canvas.drawCircle(mX, mY, mRadius, mCirclePaint);
            canvas.drawArc(mRectF, 0, 315, false, mCirclePaint);
            canvas.restore();
        }
    }

    /**
     * 车充到逆变器
     *
     * @param canvas
     */
    private void drawCurrent(Canvas canvas, CurrentPathBean pathBean) {
        if (pathBean != null && pathBean.isDrawPath()) {
            Path currentPath = pathBean.getCurrentPath();
            if (currentPath != null) {
                PathMeasure pathMeasure = pathBean.getPathMeasure();
                pathMeasure.setPath(currentPath, false);
                pathBean.setLength(pathMeasure.getLength());
                Path dst = pathBean.getDst();
                dst.reset();
                float stop = pathBean.getLength() * pathBean.getAnimatorValue();
                float start = (float) (stop - ((0.5 - Math.abs(pathBean.getAnimatorValue() - 0.5)) * pathBean.getLength()));
                pathMeasure.getSegment(start, stop + 15, dst, true);

                pathMeasure.getPosTan(start, pathBean.getStartPoint(), null);
                pathMeasure.getPosTan(stop, pathBean.getEndPoint(), null);

                RectF rectF = pathBean.getRectF();
                dst.computeBounds(rectF, true);
                float xDistance = rectF.right - rectF.left;
                float yDistance = rectF.bottom - rectF.top;
                Paint paint = pathBean.getPaint();
                paint.setStrokeWidth(0.5f * mScale);
                int startColor = pathBean.getStartColor();
                int endColor = pathBean.getEndColor();
                if (yDistance > xDistance) {
                    paint.setShader(new LinearGradient(0, rectF.top, 0, rectF.bottom,
                            pathBean.startGreaterEndY() ? endColor : startColor, pathBean.startGreaterEndY() ? startColor : endColor, Shader.TileMode.CLAMP));
                } else {
                    paint.setShader(new LinearGradient(rectF.left, 0,
                            rectF.right, 0,
                            pathBean.startGreaterEndX() ? endColor : startColor, pathBean.startGreaterEndX() ? startColor : endColor, Shader.TileMode.CLAMP));
                }
                canvas.drawPath(dst, paint);
            }
        }
    }


    /**
     * 画topo
     *
     * @param canvas
     */
    private void drawTB(Canvas canvas) {
        if (mCurrentView != null) {
            mPaint.setColor(mTopoColor);
            mCurrentView.drawTB(canvas, mPaint);
        }
    }

    /**
     * 开始动画
     */
    public void startAnim() {
        isStop = false;
        if (mValueAnimator != null) {
            if (mValueAnimator != null && !mValueAnimator.isRunning()) {
                mValueAnimator.start();
            }
        }
        if (mRotateAnimator != null && !mRotateAnimator.isRunning()) {
            mRotateAnimator.start();
        }
    }

    /**
     * 停止动画
     */
    public void stopAnim() {
        stopAnim(false);
    }

    public void stopAnim(boolean drawPath) {
        isStop = true;
        if (mHandler != null) {
            if (mAnimRunnable != null) {
                mHandler.removeCallbacks(mAnimRunnable);
            }
            mHandler.removeMessages(REFRESH_CURRENT);
        }
        if (mValueAnimator != null) {
            mHandler.removeCallbacks(mAnimRunnable);
            mValueAnimator.cancel();
        }
        for (Map.Entry<String, CurrentPathBean> map : mPathMap.entrySet()) {
            CurrentPathBean pathBean = map.getValue();
            pathBean.setDrawPath(drawPath);
        }
        if (mRotateAnimator != null) {
            isDrawCircle = false;
            mRotateAnimator.cancel();
        }
        invalidate();
    }

    /**
     * 设置动画路径
     *
     * @param key
     * @param pathBean
     */
    public void setPathMap(String key, CurrentPathBean pathBean) {
        if (!mPathMap.containsKey(key)) {
            mPathMap.put(key, pathBean);
        }
    }

    public CurrentPathBean getPathBeanByKey(String key) {
        return mPathMap.get(key);
    }

    public int[] getCircleColors() {
        return mCircleColors;
    }

    public void setCircleColors(int[] circleColors) {
        this.mCircleColors = circleColors;
    }

    public float[] getCirclePositions() {
        return mCirclePositions;
    }

    public void setCirclePositions(float[] circlePositions) {
        this.mCirclePositions = circlePositions;
    }

    public boolean isDrawCircle() {
        return isDrawCircle;
    }

    public void setDrawCircle(boolean drawCircle) {
        isDrawCircle = drawCircle;
    }

    public void setSameColor(int startColor, int endColor) {
        for (Map.Entry<String, CurrentPathBean> map : mPathMap.entrySet()) {
            CurrentPathBean pathBean = map.getValue();
            pathBean.setStartColor(startColor);
            pathBean.setEndColor(endColor);
        }
    }

    public boolean hasPathThroughInverter() {
        if (mPathMap == null || mPathMap.size() == 0) {
            isDrawCircle = false;
        } else {
            CurrentPathBean batteryInverter = mPathMap.get(CurrentPathBean.BATTERY_INVERTER_KEY);
            CurrentPathBean inverter2Battery = mPathMap.get(CurrentPathBean.INVERTER_BATTERY_KEY);
            CurrentPathBean solarPathBean = mPathMap.get(CurrentPathBean.SOLAR_INVERTER_KEY);
            CurrentPathBean gridInverter = mPathMap.get(CurrentPathBean.GRID_INVERTER_KEY);
            CurrentPathBean inverter2Grid = mPathMap.get(CurrentPathBean.INVERTER_GRID_KEY);
            CurrentPathBean additionalPathBean = mPathMap.get(CurrentPathBean.INVERTER_ADDITIONAL_KEY);
            CurrentPathBean otherPathBean = mPathMap.get(CurrentPathBean.INVERTER_KEEP_ON_KEY);
            CurrentPathBean vehicleInverter = mPathMap.get(CurrentPathBean.VEHICLE_INVERTER_KEY);
            CurrentPathBean inverterVehicle = mPathMap.get(CurrentPathBean.INVERTER_VEHICLE_KEY);

            CurrentPathBean inverterDualPower = mPathMap.get(CurrentPathBean.INVERTER_DUALPOWER_KEY);
            CurrentPathBean dualPowerInverter = mPathMap.get(CurrentPathBean.DUALPOWER_INVERTER_KEY);

            CurrentPathBean inverterBalancingPower = mPathMap.get(CurrentPathBean.INVERTER_BALANCING_POWER_KEY);
            CurrentPathBean balancingPowerInverter = mPathMap.get(CurrentPathBean.BALANCING_POWER_INVERTER_KEY);
            isDrawCircle = (batteryInverter != null && batteryInverter.isDrawPath())
                    || (inverter2Battery != null && inverter2Battery.isDrawPath())
                    || (solarPathBean != null && solarPathBean.isDrawPath())
                    || (gridInverter != null && gridInverter.isDrawPath())
                    || (inverter2Grid != null && inverter2Grid.isDrawPath())
                    || (additionalPathBean != null && additionalPathBean.isDrawPath())
                    || (otherPathBean != null && otherPathBean.isDrawPath())
                    || (vehicleInverter != null && vehicleInverter.isDrawPath())
                    || (inverterVehicle != null && inverterVehicle.isDrawPath()
                    || (inverterDualPower != null && inverterDualPower.isDrawPath())
                    || (dualPowerInverter != null && dualPowerInverter.isDrawPath())
                    || (inverterBalancingPower != null && inverterBalancingPower.isDrawPath())
                    || (balancingPowerInverter != null && balancingPowerInverter.isDrawPath()));
        }
        return isDrawCircle;
    }
}
