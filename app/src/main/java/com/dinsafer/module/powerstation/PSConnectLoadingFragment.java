package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.settings.network.PSNetworkBleScanFragment;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public abstract class PSConnectLoadingFragment<V extends ViewDataBinding> extends MyBaseFragment<V> {

    protected static final int PARAM_FROM_SETTING = 0;
    protected static final int PARAM_FROM_STEP_ADD = 1;
    protected int mFrom = PARAM_FROM_SETTING;
    private boolean isLoading;
    protected String mDeviceId;
    protected String mSubcategory;
    protected Device mPSDevice;

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    public void showDeviceOfflineDialog(Device device) {
        AlertDialogV2.Builder builder = AlertDialogV2.createBuilder(getContext());
        builder.setContent(getResources().getString(R.string.ipc_failed_to_connect_the_network));
        builder.setOk(getResources().getString(R.string.ipc_reconnect_the_network));

        builder.setCancel(getResources().getString(R.string.cancel));
        builder.setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        BmtManager.getInstance().connectDevice(device, true);
                        showTimeOutLoadinFramgmentWithErrorAlert();
                    }
                });
        if (mFrom == PARAM_FROM_SETTING) {
            builder.setOkV2(getResources().getString(R.string.ipc_reconfigure_the_network));
            builder.setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                @Override
                public void onOkClick() {
                    getDelegateActivity().addCommonFragment(PSNetworkBleScanFragment.newInstance(mDeviceId, mSubcategory));
                }
            });
        }
        AlertDialogV2 offlineDialog = builder.preBuilder();
        offlineDialog.show();
    }

    protected void initParams() {
        Bundle bundle = getArguments();
        mFrom = bundle.getInt(PSKeyConstant.KEY_FROM);
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
    }

    /**
     * BMT状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        final String deviceId = event.getDeviceID();
        if (deviceId != null && deviceId.equals(mDeviceId) && event.getSubcategory().equals(mSubcategory)) {
            int status = event.getConnect_status();
            if (status == BmtDeviceStatusChange.ONLINE) {
                if (isLoading) {
                    isLoading = false;
                    getDelegateActivity().closeLoadingTextFragment();
                }
            } else if (status == BmtDeviceStatusChange.LOADING) {
                if (!getDelegateActivity().isFragmentInTopExcludeLoading(this)
                        || event.getPreviousStatus() != -1) return;
                isLoading = true;
                closeLoadingFragment();
                getDelegateActivity().showLoadingTextFragment();
            } else if (status == BmtDeviceStatusChange.OFFLINE) {
                if (isLoading) {
                    isLoading = false;
                    getDelegateActivity().closeLoadingTextFragment();
                    showDeviceOfflineDialog(mPSDevice);
                }
            }
        }
    }
}
