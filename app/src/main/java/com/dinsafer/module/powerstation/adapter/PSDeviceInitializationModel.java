package com.dinsafer.module.powerstation.adapter;

import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsDeviceInitializationBinding;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/2 18:50
 * @description :
 */
public class PSDeviceInitializationModel implements BaseBindModel<ItemPsDeviceInitializationBinding> {

    public static final int FAILED = -1;
    public static final int LOADING = 0;
    public static final int SUCCEED = 1;
    // -1. 失败; 0. loading; 1. 成功
    private int status;
    private String step;
    private Animation operatingAnim;

    public PSDeviceInitializationModel(int status, String step) {
        this.status = status;
        this.step = step;
    }

    public void updateStatus(int newStatus){
        this.status = newStatus;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_device_initialization;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsDeviceInitializationBinding itemPsDeviceInitializationBinding) {
        int drawable = R.drawable.icon_ps_accessories_device_loading;
        itemPsDeviceInitializationBinding.lavLoading.setVisibility(View.GONE);
        itemPsDeviceInitializationBinding.ivLogo.setVisibility(View.GONE);
        switch (status) {
            case SUCCEED:
                drawable = R.drawable.radiobox_sel;
                itemPsDeviceInitializationBinding.ivLogo.setVisibility(View.VISIBLE);
                break;

            case FAILED:
                drawable = R.drawable.radiobox_sel_failed;
                itemPsDeviceInitializationBinding.ivLogo.setVisibility(View.VISIBLE);
                break;

            default:
                itemPsDeviceInitializationBinding.lavLoading.setVisibility(View.VISIBLE);
                drawable = R.drawable.icon_ps_accessories_device_loading;
        }
        itemPsDeviceInitializationBinding.ivLogo.setImageResource(drawable);
        itemPsDeviceInitializationBinding.tvStep.setLocalText(step);
    }
}
