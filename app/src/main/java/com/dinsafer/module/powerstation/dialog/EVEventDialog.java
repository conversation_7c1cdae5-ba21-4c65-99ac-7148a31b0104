package com.dinsafer.module.powerstation.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.dinsafer.dialog.BaseDialog;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogEvEventBinding;
import com.dinsafer.util.ScreenUtils;

public class EVEventDialog extends BaseDialog<DialogEvEventBinding> {

    private Builder mBuilder;
    private int coverRes;
    private String title;
    private String subtitle;
    private String btnText;
    private String subBtnText;
    private OnCheckListener checkListener;
    private boolean isCenter =  false;

    public EVEventDialog(@NonNull Context context) {
        super(context, R.style.CommonDialogStyle);
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = ScreenUtils.getScreenWidth(mContext)/5*4;
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        layoutParams.gravity = Gravity.CENTER;
        getWindow().setAttributes(layoutParams);
    }

    @Override
    protected int layoutRes() {
        return R.layout.dialog_ev_event;
    }

    @Override
    protected void initView() {
        setData();
    }

    public void setData() {
        mBinding.ivCover.setImageResource(coverRes);
        mBinding.tvTitle.setText(title);
        mBinding.tvSubtitle.setText(subtitle);
        if (!TextUtils.isEmpty(btnText)) {
            mBinding.tvCheck.setText(btnText);
        }
        if (!TextUtils.isEmpty(subBtnText)) {
            mBinding.tvGotIt.setText(subBtnText);
        }

        mBinding.tvSubtitle.post(new Runnable() {
            @Override
            public void run() {
                if (isCenter) {
                    mBinding.tvSubtitle.setGravity(Gravity.CENTER_HORIZONTAL);
                } else {
                    mBinding.tvSubtitle.setGravity(mBinding.tvSubtitle.getLineCount() <= 1 ?
                            Gravity.CENTER_HORIZONTAL : Gravity.LEFT);
                }
            }
        });

        mBinding.tvGotIt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (checkListener != null) {
                    checkListener.onSubBtnClick(EVEventDialog.this);
                }
            }
        });

        mBinding.tvCheck.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (checkListener != null) {
                    checkListener.onCheck(EVEventDialog.this);
                }
            }
        });
    }

    public void resetRes(int coverRes, String title, String subtitle, String btnText, String subBntText, OnCheckListener checkListener,boolean isCenter) {
        this.coverRes = coverRes;
        this.title = title;
        this.subtitle = subtitle;
        this.checkListener = checkListener;
        this.btnText = btnText;
        this.subBtnText = subBntText;
        this.isCenter = isCenter;
    }

    public void resetRes(int coverRes, String title, String subtitle,  OnCheckListener checkListener) {
        resetRes(coverRes, title, subtitle, btnText, subBtnText, checkListener,false);
    }

    public interface OnCheckListener {
        void onCheck(EVEventDialog dialog);
        void onSubBtnClick(EVEventDialog dialog);
    }

    public static class Builder {
        private Context context;
        private int coverRes;
        private String title;
        private String subtitle;
        private String btnText;
        private String subBtnText;
        private OnCheckListener checkListener;
        private boolean isCenter;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setCoverRes(int coverRes) {
            this.coverRes = coverRes;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setCenter(boolean isCenter) {
            this.isCenter = isCenter;
            return this;
        }

        public Builder setSubtitle(String subtitle) {
            this.subtitle = subtitle;
            return this;
        }

        public Builder setBtnText(String btnText) {
            this.btnText = btnText;
            return this;
        }

        public Builder setSubBtnText(String subBtnText) {
            this.subBtnText = subBtnText;
            return this;
        }

        public Builder setCheckListener(OnCheckListener checkListener) {
            this.checkListener = checkListener;
            return this;
        }

        public EVEventDialog build() {
            EVEventDialog evEventDialog = new EVEventDialog(context);
            evEventDialog.resetRes(this.coverRes, this.title, this.subtitle, this.btnText, this.subBtnText, this.checkListener,this.isCenter);
            return evEventDialog;
        }
    }
}
