package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.dinnet.R;
import com.dinsafer.common.utils.DensityUtil;

public class BatteryChargeView extends View {

    private Context mContext;
    private Resources mResources;
    private Paint mBitPaint;
    private Bitmap mBitmap;
    private Rect mSrcRect, mDestRect;

    private int mBitWidth;
    private int mBitHeight;
    private float mScale = 1f;
    private float mWTimes = 1f;

    private Path mClipPath;

    private Path mForeWavePath, mBackWavePath;
    private Paint mForeWavePaint, mBackWavePaint;
    private int mForeWaveColor, mBackWaveColor;

    // 显示百分比
    private float mProgress = -1f;
    private float mAnimProgress = 0f;
    private TextPaint mProgressPaint;//进度画笔
    private TextPaint mUnitPaint;//进度画笔
    private int mProgressColor = Color.WHITE; // 颜色
    private float mProgressTextSize = 48f;
    private float mUnitTextSize = 16f;
    private boolean mShowProgress = true;
    private Rect mProgressBounds = new Rect();
    private Rect mUnitBounds = new Rect();
    private boolean isOnline = false;
    private boolean isSuccess;

    private int mWidth;
    private int mHeight;
    private float mLeft;
    private float mTop;
    private float mRight;
    private float mBottom;

    private float A = 10f;
    private float φ;
    private float mAmplitude = 10f;
    private float mSpeed = 0.1f;

    private int chargeStatus = NORMAL;
    public static final int DISCHARGING = -1;  // 放电中
    public static final int NORMAL = 0;  // 补充不放
    public static final int CHARGING = 1;  // 充电中

    private float smartReserve = 0.9f;
    private float emergencyReserve = 0.5f;

    private boolean isAnim = true;

    public BatteryChargeView(Context context) {
        this(context, null);
    }

    public BatteryChargeView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BatteryChargeView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        mResources = getResources();
        initAttrs(context, attrs);
        initPaint();
        initBitmap();
        resetColor();
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.BatteryChargeView);
        mForeWaveColor = typedArray.getColor(R.styleable.BatteryChargeView_battery_fore_wave_color, Color.WHITE);
        mBackWaveColor = typedArray.getColor(R.styleable.BatteryChargeView_battery_back_wave_color, Color.WHITE);
        mProgressColor = typedArray.getColor(R.styleable.BatteryChargeView_battery_charge_progress_color, Color.WHITE);
        mProgressTextSize = typedArray.getDimension(R.styleable.BatteryChargeView_battery_charge_progress_text_size, DensityUtil.sp2px(context, 48));
        mUnitTextSize = typedArray.getDimension(R.styleable.BatteryChargeView_battery_charge_unit_text_size, DensityUtil.sp2px(context, 14));
        mShowProgress = typedArray.getBoolean(R.styleable.BatteryChargeView_battery_charge_show_progress, false);
        mScale = typedArray.getFloat(R.styleable.BatteryChargeView_battery_charge_scale, 1.0f);
        mWTimes = typedArray.getFloat(R.styleable.BatteryChargeView_battery_charge_times, 1.0f);
        mAmplitude = typedArray.getFloat(R.styleable.BatteryChargeView_battery_charge_amplitude, 10f);
        mSpeed = typedArray.getFloat(R.styleable.BatteryChargeView_battery_charge_speed, 0.1f);
        A = 0;
        typedArray.recycle();
    }

    private void initPaint() {
        mBitPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mBitPaint.setFilterBitmap(true);
        mBitPaint.setDither(true);
        mProgressPaint = new TextPaint();
        mProgressPaint.setAntiAlias(true);
        mProgressPaint.setColor(mProgressColor);
        mProgressPaint.setTextSize(mProgressTextSize);
        mProgressPaint.setFakeBoldText(true);
        Typeface progressTypeface = ResourcesCompat.getFont(getContext(), R.font.poppins);
        mProgressPaint.setTypeface(progressTypeface);
        mUnitPaint = new TextPaint();
        mUnitPaint.setAntiAlias(true);
        mUnitPaint.setColor(mProgressColor);
        mUnitPaint.setTextSize(mUnitTextSize);
        Typeface unitTypeface = ResourcesCompat.getFont(getContext(), R.font.palanquin);
        mUnitPaint.setTypeface(unitTypeface);
        mClipPath = new Path();

        //初始化路径
        mForeWavePath = new Path();
        mBackWavePath = new Path();
        //初始化画笔
        mForeWavePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mForeWavePaint.setAntiAlias(true);
        mForeWavePaint.setStyle(Paint.Style.FILL);
        mForeWavePaint.setColor(mForeWaveColor);

        mBackWavePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mBackWavePaint.setAntiAlias(true);
        mBackWavePaint.setStyle(Paint.Style.FILL);
        mBackWavePaint.setColor(mBackWaveColor);
    }

    private void initBitmap() {
        mBitmap = ((BitmapDrawable) mResources.getDrawable(R.drawable.img_power_battery_background)).getBitmap();
        mBitWidth = mBitmap.getWidth();
        mBitHeight = mBitmap.getHeight();
        mSrcRect = new Rect(0, 0, mBitWidth, mBitHeight);
        mDestRect = new Rect(0, 0, (int) (mBitWidth * mScale), (int) (mBitHeight * mScale));
        mLeft = mDestRect.left + 15 * mScale;
        mTop = mDestRect.top + 52 * mScale;
        mRight = mDestRect.right - 15 * mScale;
        mBottom = mDestRect.bottom - 15 * mScale;
        mClipPath.addRoundRect(mLeft, mTop, mRight, mBottom, 10 * mScale, 10 * mScale, Path.Direction.CW);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension((int) (mBitWidth * mScale), (int) (mBitHeight * mScale));
        mWidth = getMeasuredWidth();
        mHeight = getMeasuredHeight();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawBitmap(mBitmap, mSrcRect, mDestRect, mBitPaint);
        if (isOnline) {
            drawWavePath(canvas);
        }
        drawProgress(canvas);
        if (chargeStatus != NORMAL && isAnim) {
            if (mProgress > 0) {
                if (chargeStatus == CHARGING) {
                    mAnimProgress = mAnimProgress == mProgress ? 0 : mAnimProgress + 0.01f;
                }
                if (chargeStatus == DISCHARGING) {
                    mAnimProgress = mAnimProgress == 0 ? mProgress : mAnimProgress - 0.01f;
                }
                if (mAnimProgress < 0f) mAnimProgress = 0f;
                if (mAnimProgress > mProgress) mAnimProgress = mProgress;
                int alpha = (int) ((mProgress - mAnimProgress) / mProgress * 100);
                mForeWavePaint.setAlpha(alpha);
            }
            postInvalidateDelayed(30);
        }
    }

    private void drawWavePath(Canvas canvas) {
        if (mProgress > 0) {
            canvas.save();
            canvas.clipPath(mClipPath);
            mForeWavePath.reset();
            mBackWavePath.reset();

            φ -= mSpeed;

            float y, y2;

            double ω = mWTimes * Math.PI / getWidth();

            mForeWavePath.moveTo(mLeft, mBottom);
            mBackWavePath.moveTo(mLeft, mBottom);
            for (float x = 0; x <= getWidth(); x += 20) {
                /**
                 *  y=Asin(ωx+φ)+k
                 *  A—振幅越大，波形在y轴上最大与最小值的差值越大
                 *  ω—角速度， 控制正弦周期(单位角度内震动的次数)
                 *  φ—初相，反映在坐标系上则为图像的左右移动。这里通过不断改变φ,达到波浪移动效果
                 *  k—偏距，反映在坐标系上则为图像的上移或下移。
                 */
                y = (float) (A * Math.cos(ω * x + φ) - (A == 0 && mProgress < 100 ? (mBottom - mTop) * 0.05 : 0)) + mTop + (mBottom - mTop) * (1 - mAnimProgress);
                y2 = (float) (A * Math.sin(ω * x + φ)) + mTop + (mBottom - mTop) * (1 - mProgress);
                mForeWavePath.lineTo(x, y);
                mBackWavePath.lineTo(x, y2);
            }
            mForeWavePath.lineTo(mRight, mBottom);
            mBackWavePath.lineTo(mRight, mBottom);

            canvas.drawPath(mForeWavePath, mForeWavePaint);
            canvas.drawPath(mBackWavePath, mBackWavePaint);
        }
    }

    /**
     * 画百分比
     *
     * @param canvas
     */
    private void drawProgress(Canvas canvas) {
        if (mShowProgress) {
            String progress = isOnline && isSuccess ? String.valueOf((int) (mProgress * 1000 / 10)) : mContext.getString(R.string.power_station_cdv_offline_val);
            mProgressPaint.getTextBounds(progress, 0, progress.length(), mProgressBounds);
            String percent = "%";
            mUnitPaint.getTextBounds(percent, 0, percent.length(), mUnitBounds);
            int sumWidth = mProgressBounds.width() + mUnitBounds.width();
            canvas.drawText(progress, mWidth / 2f - sumWidth / 2f, (mTop + mBottom) / 2 + (isOnline && isSuccess ? mProgressBounds.height() / 2 : mUnitBounds.height()), mProgressPaint);
            canvas.drawText(percent, mWidth / 2f - sumWidth / 2f + mProgressBounds.width() + DensityUtil.dp2px(mContext, 4), (mTop + mBottom) / 2 + mUnitBounds.height() / 2, mUnitPaint);
        }
    }

    public int getForeWaveColor() {
        return mForeWaveColor;
    }

    public void setForeWaveColor(int foreWaveColor) {
        this.mForeWaveColor = foreWaveColor;
    }

    public int getBackWaveColor() {
        return mBackWaveColor;
    }

    public void setBackWaveColor(int backWaveColor) {
        this.mBackWaveColor = backWaveColor;
    }

    public void setOnline(boolean online) {
        isOnline = online;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    public void setChargeStatus(int status, boolean needRefresh) {
        if (status == this.chargeStatus) return;
        this.chargeStatus = status;
        A = chargeStatus == NORMAL ? 0 : mAmplitude;
        mAnimProgress = chargeStatus == CHARGING ? 0 : mProgress;
        if (needRefresh) {
            invalidate();
        }
    }

    public void setProgress(float progress, boolean needRefresh) {
        this.mProgress = progress;
        this.mAnimProgress = chargeStatus == CHARGING ? 0 : mProgress;
        resetColor();
        if (needRefresh) {
            invalidate();
        }
    }

    public void setProgressChargeStatus(float progress, int status) {
        this.mProgress = progress;
        this.chargeStatus = status;
        A = chargeStatus == NORMAL ? 0 : mAmplitude;
        mAnimProgress = chargeStatus == CHARGING ? 0 : mProgress;
        resetColor();
        invalidate();
    }

    public void setSmartReserve(float smartReserve) {
        this.smartReserve = smartReserve;
    }

    public void setEmergencyReserve(float emergencyReserve) {
        this.emergencyReserve = emergencyReserve;
    }

    public void resetColor() {
        if (mProgress <= 0.02f) {
            mForeWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DE34D43));
            mBackWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DE34D43));
        } else if (mProgress > 0.02f && mProgress <= 0.12f) {
            mForeWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DE9A632));
            mBackWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DE9A632));
        } else if (mProgress > 0.12f && mProgress <= emergencyReserve) {
            mForeWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DFFD12E));
            mBackWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DFFD12E));
        } else if (mProgress > emergencyReserve && mProgress <= smartReserve) {
            mForeWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4D35C38A));
            mBackWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4D35C38A));
        } else if (mProgress > smartReserve && mProgress <= 1f) {
            mForeWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4D00C2B7));
            mBackWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4D00C2B7));
        }
    }

    public boolean isAnim() {
        return isAnim;
    }

    public void setAnim(boolean anim) {
        isAnim = anim;
        invalidate();
    }
}
