package com.dinsafer.module.powerstation.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.dinsafer.module_bmt.cmd.Mcu;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 16:12
 * @description :
 */
public class CabinetBatteryBean implements Parcelable {
    private int state;
    private Double bmsTemp;
    private Double electrodeATemp;
    private Double electrodeBTemp;
    private int ampere;
    private int voltage;
    private int soc;
    private double curPower;
    private double fullPower;
    private int releaseTimes;
    private int recycleTimes;
    private int healthy;
    private String idInfo;
    private String version;
    private String barcode;
    private int cabinetIndex;  //所属的机柜index 接口返回
    private int index = -1;  // 电池包引索  接口返回
    private Boolean heating;
    private Boolean heatAvailable;
    private boolean isEmpty;  // 自己定义
    private int indexInCabinet; // 在机柜的哪个位置, 自己定义
    private String key;
    private boolean dischargeSwitchOn;
    private boolean chargeSwitchOn;
    private Integer cabinetPositionIndex;
    private boolean isCurrentIndex;
    private int capacity;
    private List<Integer> exceptions;
    private boolean isCache;
    private boolean isUnknown;

    public CabinetBatteryBean() {
    }

    public CabinetBatteryBean(boolean isEmpty, int indexInCabinet) {
        this.isEmpty = isEmpty;
        this.indexInCabinet = indexInCabinet;
    }

    public CabinetBatteryBean(boolean isEmpty, int indexInCabinet, String key) {
        this.isEmpty = isEmpty;
        this.indexInCabinet = indexInCabinet;
        this.key = key;
    }

    public CabinetBatteryBean(int state, Double bmsTemp, Double electrodeATemp, Double electrodeBTemp, int ampere, int voltage, int soc, double curPower, double fullPower, int releaseTimes, int recycleTimes, int healthy, String idInfo, String version, String barcode, int cabinetIndex, int index) {
        this.state = state;
        this.bmsTemp = bmsTemp;
        this.electrodeATemp = electrodeATemp;
        this.electrodeBTemp = electrodeBTemp;
        this.ampere = ampere;
        this.voltage = voltage;
        this.soc = soc;
        this.curPower = curPower;
        this.fullPower = fullPower;
        this.releaseTimes = releaseTimes;
        this.recycleTimes = recycleTimes;
        this.healthy = healthy;
        this.idInfo = idInfo;
        this.version = version;
        this.barcode = barcode;
        this.cabinetIndex = cabinetIndex;
        this.index = index;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public Double getBmsTemp() {
        return bmsTemp;
    }

    public void setBmsTemp(Double bmsTemp) {
        this.bmsTemp = bmsTemp;
    }

    public Double getElectrodeATemp() {
        return electrodeATemp;
    }

    public void setElectrodeATemp(Double electrodeATemp) {
        this.electrodeATemp = electrodeATemp;
    }

    public Double getElectrodeBTemp() {
        return electrodeBTemp;
    }

    public void setElectrodeBTemp(Double electrodeBTemp) {
        this.electrodeBTemp = electrodeBTemp;
    }

    public int getAmpere() {
        return ampere;
    }

    public void setAmpere(int ampere) {
        this.ampere = ampere;
    }

    public int getVoltage() {
        return voltage;
    }

    public void setVoltage(int voltage) {
        this.voltage = voltage;
    }

    public int getSoc() {
        return soc;
    }

    public void setSoc(int soc) {
        this.soc = soc;
    }

    public double getCurPower() {
        return curPower;
    }

    public void setCurPower(double curPower) {
        this.curPower = curPower;
    }

    public double getFullPower() {
        return fullPower;
    }

    public void setFullPower(double fullPower) {
        this.fullPower = fullPower;
    }

    public int getReleaseTimes() {
        return releaseTimes;
    }

    public void setReleaseTimes(int releaseTimes) {
        this.releaseTimes = releaseTimes;
    }

    public int getRecycleTimes() {
        return recycleTimes;
    }

    public void setRecycleTimes(int recycleTimes) {
        this.recycleTimes = recycleTimes;
    }

    public int getHealthy() {
        return healthy;
    }

    public void setHealthy(int healthy) {
        this.healthy = healthy;
    }

    public String getIdInfo() {
        return idInfo;
    }

    public void setIdInfo(String idInfo) {
        this.idInfo = idInfo;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public int getCabinetIndex() {
        return cabinetIndex;
    }

    public void setCabinetIndex(int cabinetIndex) {
        this.cabinetIndex = cabinetIndex;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public Boolean getHeating() {
        return heating;
    }

    public void setHeating(Boolean heating) {
        this.heating = heating;
    }

    public Boolean getHeatAvailable() {
        return heatAvailable;
    }

    public void setHeatAvailable(Boolean heatAvailable) {
        this.heatAvailable = heatAvailable;
    }

    public boolean isEmpty() {
        return isEmpty;
    }

    public void setEmpty(boolean empty) {
        isEmpty = empty;
    }

    public int getIndexInCabinet() {
        return indexInCabinet;
    }

    public void setIndexInCabinet(int indexInCabinet) {
        this.indexInCabinet = indexInCabinet;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public boolean isDischargeSwitchOn() {
        return dischargeSwitchOn;
    }

    public void setDischargeSwitchOn(boolean dischargeSwitchOn) {
        this.dischargeSwitchOn = dischargeSwitchOn;
    }

    public boolean isChargeSwitchOn() {
        return chargeSwitchOn;
    }

    public void setChargeSwitchOn(boolean chargeSwitchOn) {
        this.chargeSwitchOn = chargeSwitchOn;
    }

    public Integer getCabinetPositionIndex() {
        return cabinetPositionIndex;
    }

    public void setCabinetPositionIndex(Integer cabinetPositionIndex) {
        this.cabinetPositionIndex = cabinetPositionIndex;
    }

    public boolean isCurrentIndex() {
        return isCurrentIndex;
    }

    public void setCurrentIndex(boolean currentIndex) {
        isCurrentIndex = currentIndex;
    }

    public int getCapacity() {
        return capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }

    public List<Integer> getExceptions() {
        return exceptions;
    }

    public void setExceptions(List<Integer> exceptions) {
        this.exceptions = exceptions;
    }

    public boolean isCache() {
        return isCache;
    }

    public void setCache(boolean cache) {
        isCache = cache;
    }

    public boolean isUnknown() {
        return isUnknown;
    }

    public void setUnknown(boolean unknown) {
        isUnknown = unknown;
    }

    /**
     * 高温告警/保护
     *
     * @return
     */
    public boolean isHighTemp() {
        if (exceptions != null && exceptions.size() > 0) {
            for (Integer exception : exceptions) {
                if (exception == Mcu.Battery.BatteryException.ReleaseHighTempAlert.getCode() ||
                        exception == Mcu.Battery.BatteryException.ChargeHighTempProtect.getCode() ||
                        exception == Mcu.Battery.BatteryException.ReleaseHighTempProtect.getCode()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isLowTemp() {
        if (exceptions != null && exceptions.size() > 0) {
            for (Integer exception : exceptions) {
                if (exception == Mcu.Battery.BatteryException.ChargeLowTempProtect.getCode() ||
                        exception == Mcu.Battery.BatteryException.ReleaseLowTempProtect.getCode()) {
                    return true;
                }
            }
        }
        return false;
    }

    public void reset() {
        this.state = -1;
        this.bmsTemp = null;
        this.electrodeATemp = null;
        this.electrodeBTemp = null;
        this.ampere = -1;
        this.voltage = -1;
        this.soc = 0;
        this.curPower = -1;
        this.fullPower = -1;
        this.releaseTimes = -1;
        this.recycleTimes = -1;
        this.healthy = -1;
        this.idInfo = null;
        this.version = null;
        this.barcode = null;
        this.capacity = 0;
        this.index = -1;
        this.exceptions = null;
        this.isCache = false;
        this.isUnknown = false;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.state);
        dest.writeValue(this.bmsTemp);
        dest.writeValue(this.electrodeATemp);
        dest.writeValue(this.electrodeBTemp);
        dest.writeInt(this.ampere);
        dest.writeInt(this.voltage);
        dest.writeInt(this.soc);
        dest.writeDouble(this.curPower);
        dest.writeDouble(this.fullPower);
        dest.writeInt(this.releaseTimes);
        dest.writeInt(this.recycleTimes);
        dest.writeInt(this.healthy);
        dest.writeString(this.idInfo);
        dest.writeString(this.version);
        dest.writeString(this.barcode);
        dest.writeInt(this.cabinetIndex);
        dest.writeInt(this.index);
        dest.writeValue(this.heating);
        dest.writeValue(this.heatAvailable);
        dest.writeByte(this.isEmpty ? (byte) 1 : (byte) 0);
        dest.writeInt(this.indexInCabinet);
        dest.writeString(this.key);
        dest.writeByte(this.dischargeSwitchOn ? (byte) 1 : (byte) 0);
        dest.writeByte(this.chargeSwitchOn ? (byte) 1 : (byte) 0);
        dest.writeValue(this.cabinetPositionIndex);
        dest.writeByte(this.isCurrentIndex ? (byte) 1 : (byte) 0);
        dest.writeInt(this.capacity);
        dest.writeList(this.exceptions);
        dest.writeByte(this.isCache ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isUnknown ? (byte) 1 : (byte) 0);
    }

    public void readFromParcel(Parcel source) {
        this.state = source.readInt();
        this.bmsTemp = (Double) source.readValue(Double.class.getClassLoader());
        this.electrodeATemp = (Double) source.readValue(Double.class.getClassLoader());
        this.electrodeBTemp = (Double) source.readValue(Double.class.getClassLoader());
        this.ampere = source.readInt();
        this.voltage = source.readInt();
        this.soc = source.readInt();
        this.curPower = source.readDouble();
        this.fullPower = source.readDouble();
        this.releaseTimes = source.readInt();
        this.recycleTimes = source.readInt();
        this.healthy = source.readInt();
        this.idInfo = source.readString();
        this.version = source.readString();
        this.barcode = source.readString();
        this.cabinetIndex = source.readInt();
        this.index = source.readInt();
        this.heating = (Boolean) source.readValue(Boolean.class.getClassLoader());
        this.heatAvailable = (Boolean) source.readValue(Boolean.class.getClassLoader());
        this.isEmpty = source.readByte() != 0;
        this.indexInCabinet = source.readInt();
        this.key = source.readString();
        this.dischargeSwitchOn = source.readByte() != 0;
        this.chargeSwitchOn = source.readByte() != 0;
        this.cabinetPositionIndex = (Integer) source.readValue(Integer.class.getClassLoader());
        this.isCurrentIndex = source.readByte() != 0;
        this.capacity = source.readInt();
        this.exceptions = new ArrayList<Integer>();
        source.readList(this.exceptions, Integer.class.getClassLoader());
        this.isCache = source.readByte() != 0;
        this.isUnknown = source.readByte() != 0;
    }

    protected CabinetBatteryBean(Parcel in) {
        this.state = in.readInt();
        this.bmsTemp = (Double) in.readValue(Double.class.getClassLoader());
        this.electrodeATemp = (Double) in.readValue(Double.class.getClassLoader());
        this.electrodeBTemp = (Double) in.readValue(Double.class.getClassLoader());
        this.ampere = in.readInt();
        this.voltage = in.readInt();
        this.soc = in.readInt();
        this.curPower = in.readDouble();
        this.fullPower = in.readDouble();
        this.releaseTimes = in.readInt();
        this.recycleTimes = in.readInt();
        this.healthy = in.readInt();
        this.idInfo = in.readString();
        this.version = in.readString();
        this.barcode = in.readString();
        this.cabinetIndex = in.readInt();
        this.index = in.readInt();
        this.heating = (Boolean) in.readValue(Boolean.class.getClassLoader());
        this.heatAvailable = (Boolean) in.readValue(Boolean.class.getClassLoader());
        this.isEmpty = in.readByte() != 0;
        this.indexInCabinet = in.readInt();
        this.key = in.readString();
        this.dischargeSwitchOn = in.readByte() != 0;
        this.chargeSwitchOn = in.readByte() != 0;
        this.cabinetPositionIndex = (Integer) in.readValue(Integer.class.getClassLoader());
        this.isCurrentIndex = in.readByte() != 0;
        this.capacity = in.readInt();
        this.exceptions = new ArrayList<Integer>();
        in.readList(this.exceptions, Integer.class.getClassLoader());
        this.isCache = in.readByte() != 0;
        this.isUnknown = in.readByte() != 0;
    }

    public static final Creator<CabinetBatteryBean> CREATOR = new Creator<CabinetBatteryBean>() {
        @Override
        public CabinetBatteryBean createFromParcel(Parcel source) {
            return new CabinetBatteryBean(source);
        }

        @Override
        public CabinetBatteryBean[] newArray(int size) {
            return new CabinetBatteryBean[size];
        }
    };
}


