package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.Constants;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.PsVersion1EventCode;
import com.dinsafer.config.PsVersion2EventCode;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPowerStationRootBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.adapter.BasePowerStationItemModel;
import com.dinsafer.module.powerstation.adapter.PSBmtInfoItemModel;
import com.dinsafer.module.powerstation.adapter.PSDirectionItemModel;
import com.dinsafer.module.powerstation.adapter.PSImpactStrategiesItemModel;
import com.dinsafer.module.powerstation.adapter.PSOnlineStatusItemModel;
import com.dinsafer.module.powerstation.adapter.PSRelativePriceItemModel;
import com.dinsafer.module.powerstation.adapter.PSTodayUsageItemModel;
import com.dinsafer.module.powerstation.adapter.PSWarningItemModel;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.module.powerstation.dialog.WarningDialog;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.ElectricityStatisticsFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.factory.ChartDataFactory;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtListUpdateEvent;
import com.dinsafer.module.powerstation.event.HomePowerStationDefaultEvent;
import com.dinsafer.module.powerstation.event.ShowBmtGuideTipEvent;
import com.dinsafer.module.powerstation.event.ShowHomeGuideEvent;
import com.dinsafer.module.powerstation.guide.PSGuideFragment;
import com.dinsafer.module.powerstation.impacts.ImpactStrategiesFragment;
import com.dinsafer.module.powerstation.settings.PowerSettingsFragment;
import com.dinsafer.module.powerstation.settings.network.NetworkDiagnosisFragment;
import com.dinsafer.module.powerstation.settings.network.PSNetworkBleScanFragment;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.FeedBackFragment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.SettingInfoHelper;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PowerStationRoot2Fragment extends MyBaseFragment<FragmentPowerStationRootBinding> implements IDeviceCallBack {

    private static final int PARAM_FROM_DEFAULT = 0;
    private static final int PARAM_FROM_STEP_ADD = 1;
    public static final int SHOW_BMT = 0;
    public static final int SHOW_BATTERY = 1;
    public static final int SHOW_EV = 11;
    public static final int SHOW_EMERGENCY = 21;
    public static final int SHOW_STATS = 31;
    private static final String SHOW_PAGER_ITEM = "show_pager_item";
    public static boolean isBSenorOutputOn;
    public static boolean isAllBSensorInstalled;

    private String mDeviceId;
    private String mSubcategory;
    private int mFrom;
    private Device mPSDevice;
    private int mInitPagerItem;
    private String subCategory;
    private BindMultiAdapter<BasePowerStationItemModel> mAdapter;
    private List<BasePowerStationItemModel> mContents;
    private ArrayList<WarningBean> mWarningList = new ArrayList<>();

    private PSWarningItemModel mPSWarningItemModel;
    private PSOnlineStatusItemModel mPSOnlineStatusItemModel;
    private PSBmtInfoItemModel mPSBmtInfoItemModel;
    private PSTodayUsageItemModel mPSTodayUsageItemModel;
    private PSRelativePriceItemModel mPSRelativePriceItemModel;
    private PSImpactStrategiesItemModel mPSImpactStrategiesItemModel;
    private WarningDialog mWarningDialog;
    private boolean isShowWarmingDialog;
    private int mWifiSignal = 1;
    private int mCellularSignal = 1;
    private int mEthernetSignal = 0;
    public static int random = -1;

    public static PowerStationRoot2Fragment newInstanceForStepAddPS(String title, String deviceId, String subCategory) {
        return newInstance(title, deviceId, subCategory, PARAM_FROM_STEP_ADD, SHOW_BMT);
    }

    public static PowerStationRoot2Fragment newInstance(String title, String deviceId, String subCategory, int initPagerItem) {
        return newInstance(title, deviceId, subCategory, PARAM_FROM_DEFAULT, initPagerItem);
    }

    public static PowerStationRoot2Fragment newInstance(String title, String deviceId, String subCategory, int from, int initPagerItem) {
        PowerStationRoot2Fragment fragment = new PowerStationRoot2Fragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_TITLE, title);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putInt(SHOW_PAGER_ITEM, initPagerItem);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_power_station_root;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        EventBus.getDefault().register(this);
        mBinding.commonBar.commonBarTitle.setMaxLines(1);
        mBinding.commonBar.commonBarTitle.setEllipsize(TextUtils.TruncateAt.END);
        if (SettingInfoHelper.getInstance().isAdmin()) {
            mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);
            mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
            mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PowerSettingsFragment.newInstance(mDeviceId, mSubcategory)));
        }
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        initRV();
        //        loadCacheException();
        if (mFrom >= PARAM_FROM_STEP_ADD) {
            // 检查固件版本
            checkNotifyShowGuideTip();
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.clWarning.setOnClickListener(v -> {
            if (mWarningList.size() > 0) {
                showWarningDialog();
            }
        });
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        showTimeOutLoadinFramgment();
        HomePowerStationDefaultEvent event = new HomePowerStationDefaultEvent(mDeviceId, mSubcategory);
        EventBus.getDefault().post(event);
        BmtManager.getInstance().removeDeviceGraphicCacheInfoByKey(mDeviceId, mSubcategory);
        submitCmd(BmtCmd.GET_BSENSOR_STATUS);
        submitCmd(BmtCmd.GET_AIMODE_SETTINGS);
        getStatisticData();
        getStatRevenue();
        getFeature();
        getPriceInfo();
        updateByDeviceOnlineStatus();
//                submitCmd(DsCamCmd.GET_GLOBAL_EXCEPTIONS);
//                submitCmd(DsCamCmd.GET_COMMUNICATE_SIGNAL);
        if (BmtUtil.isDeviceOffline(mPSDevice)) {
            showDeviceOfflineDialog(mPSDevice);
            return;
        }
        BmtManager.getInstance().stopPolling();
        BmtManager.getInstance().getBmtInfo(mPSDevice);
        BmtManager.getInstance().startPolling();

        // 这个功能是emaldo有, HP没有的
        // VB1设备不适用于现有的设备详情页有引导
        boolean isDeviceGuideShowed = DBUtil.Bool(DBKey.KEY_DEVICE_GUIDE_SHOWED);
        if (!isDeviceGuideShowed && !BmtUtil.isBmtDevicePowerPulse(mPSDevice)) {
            getDelegateActivity().addCommonFragment(PSGuideFragment.newInstance(PSGuideFragment.DEVICE_GUIDE));
            DBUtil.Put(DBKey.KEY_DEVICE_GUIDE_SHOWED, true);
        }
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        random = -1;
        super.onDestroyView();
        if (mFrom == PARAM_FROM_STEP_ADD) {
            EventBus.getDefault().post(new ShowHomeGuideEvent());
        }
    }

    private void initParams() {
        Bundle bundle = getArguments();
        String title = bundle.getString(PSKeyConstant.KEY_TITLE);
        mInitPagerItem = bundle.getInt(SHOW_PAGER_ITEM);
        mFrom = bundle.getInt(PSKeyConstant.KEY_FROM);
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
            subCategory = mPSDevice.getSubCategory();
            ExceptionWarning.deviceName = BmtManager.getDeviceName(mPSDevice);
        }
        if (TextUtils.isEmpty(title)) {
            mBinding.commonBar.commonBarTitle.setText("");
        } else {
            mBinding.commonBar.commonBarTitle.setText(title.equals(Constants.POWER_STATION) ? Local.s(title) : title);
        }
    }

    private void initRV() {
        mAdapter = new BindMultiAdapter<>();
        mBinding.rvContent.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvContent.setAdapter(mAdapter);
        mContents = new ArrayList<>();
        mPSOnlineStatusItemModel = new PSOnlineStatusItemModel(getContext(), mDeviceId, mSubcategory);
        mContents.add(mPSOnlineStatusItemModel);
        mPSBmtInfoItemModel = new PSBmtInfoItemModel(this, mDeviceId, mSubcategory, mInitPagerItem);
        mContents.add(mPSBmtInfoItemModel);

        boolean isAdmin = SettingInfoHelper.getInstance().isAdmin();
        if (!BmtUtil.isBmtDevicePowerPulse(mPSDevice)) {
            mPSTodayUsageItemModel = new PSTodayUsageItemModel(getContext(), mDeviceId, mSubcategory);
            mContents.add(mPSTodayUsageItemModel);
//            mPSRelativePriceItemModel = new PSRelativePriceItemModel(getContext(), mDeviceId, mSubcategory);
//            mContents.add(mPSRelativePriceItemModel);
            if (isAdmin) {
                mPSImpactStrategiesItemModel = new PSImpactStrategiesItemModel(getContext(), mDeviceId, mSubcategory);
                mContents.add(mPSImpactStrategiesItemModel);
            }
        }
        if (isAdmin) {
            mContents.add(new PSDirectionItemModel(getContext(), mDeviceId, mSubcategory));
        }

        mAdapter.setNewData(mContents);
        mAdapter.setOnBindItemClickListener((OnBindItemClickListener<BasePowerStationItemModel>) (v, position, model) -> {
            if (model instanceof PSWarningItemModel) {
                if (mWarningList.size() > 0) {
                    showWarningDialog();
                }
            } else if (model instanceof PSTodayUsageItemModel) {
                getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory));
            } else if (model instanceof PSRelativePriceItemModel) {
                getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory, BaseChartFragment.CHART_ELECTRICITY_PRICE));
            } else if (model instanceof PSImpactStrategiesItemModel) {
                getDelegateActivity().addCommonFragment(ImpactStrategiesFragment.newInstance(mDeviceId, mSubcategory));
            } else if (model instanceof PSDirectionItemModel) {
                getDelegateActivity().addCommonFragment(PSUserGuideListFragment.newInstance(mDeviceId, mSubcategory));
            }
        });

        mAdapter.setOnBindItemChildClickListener((OnBindItemChildClickListener<BasePowerStationItemModel>) (view, position, model) -> {
            if (model instanceof PSOnlineStatusItemModel) {
                int id = view.getId();
                if (id == R.id.tv_diagnostic_network) {
                    getDelegateActivity().addCommonFragment(NetworkDiagnosisFragment.newInstance(mDeviceId, mSubcategory));
                }
            } else if (model instanceof PSTodayUsageItemModel) {
                getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory));
            } else if (model instanceof PSRelativePriceItemModel) {
                getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory, BaseChartFragment.CHART_ELECTRICITY_PRICE));
            }
        });
    }

    public boolean isShowWarmingDialog() {
        return isShowWarmingDialog;
    }

    private void loadCacheException() {
        mWarningList.clear();
        List<Integer> vertBatteryExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_BATTERY_EXCEPTIONS);
        List<Integer> vertExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_EXCEPTIONS);
        List<Integer> vertGridExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_GRID_EXCEPTIONS);
        List<Integer> vertSystemExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_SYSTEM_EXCEPTIONS);
        List<Integer> vertmpptExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_MPPT_EXCEPTIONS);
        List<Integer> vertDCExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_DC_EXCEPTIONS);
        setVertBatteryException(vertBatteryExceptions);
        setInverterException(vertExceptions);
        setVertGridException(vertGridExceptions);
        setVertSystemException(vertSystemExceptions);
        setVertMpptException(vertmpptExceptions);
//        setVertPresentException(vertPresentExceptions);
        setVertDcException(vertDCExceptions);
        List<Integer> evExceptions = DeviceHelper.getList(mPSDevice, "evExceptions");
        setEvException(evExceptions);
        List<Integer> mpptExceptions = DeviceHelper.getList(mPSDevice, "mpptExceptions");
        setMpptException(mpptExceptions);
        List<Integer> cabinetExceptions = DeviceHelper.getList(mPSDevice, "cabinetExceptions");
        setCabinetException(cabinetExceptions);
        List<Integer> batteryExceptions = DeviceHelper.getList(mPSDevice, "batteryExceptions");
        setBatteryException(batteryExceptions);
        List<Integer> systemExceptions = DeviceHelper.getList(mPSDevice, "systemExceptions");
        setSystemException(systemExceptions);
        List<Integer> communicationExceptions = DeviceHelper.getList(mPSDevice, "communicationExceptions");
        setCommunicationException(communicationExceptions);
        showException();
    }

    private void setVertBatteryException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertBatteryWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Inverter 异常
     *
     * @param exceptions
     */
    private void setInverterException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getInvertWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertGridException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertGridWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertSystemException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertSystemWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertMpptException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertMpptWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }

    }

    private void setVertPresentException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertPresentWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertDcException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertDcWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    public void showDeviceOfflineDialog(Device device) {
        AlertDialogV2 offlineDialog = AlertDialogV2.createBuilder(getContext())
                .setContent(getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        BmtManager.getInstance().connectDevice(device, true);
                        showTimeOutLoadinFramgmentWithErrorAlert();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        getDelegateActivity().addCommonFragment(PSNetworkBleScanFragment.newInstance(mDeviceId, mSubcategory));
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    /**
     * 检查是否需要显示用户指引
     * 首次添加HP5000和HP5001时，都需要通知显示用户指引
     */
    private void checkNotifyShowGuideTip() {
        final Device d = DinHome.getInstance().getDevice(mDeviceId);
        if (null == d) {
            return;
        }

        if (BmtUtil.isBmtDeviceHP5001(mPSDevice)) {
            // BMT HP5001
            if (!DBUtil.contain(DBKey.KEY_FIRST_ADD_BMT_HP5001)) {
                DBUtil.Put(DBKey.KEY_FIRST_ADD_BMT_HP5001, true);
                EventBus.getDefault().post(new ShowBmtGuideTipEvent(mDeviceId, mPSDevice.getSubCategory()));
            }
            return;
        }

        // BMT HP5000
        if (!DBUtil.contain(DBKey.KEY_FIRST_ADD_BMT_HP5000)) {
            DBUtil.Put(DBKey.KEY_FIRST_ADD_BMT_HP5000, true);
            EventBus.getDefault().post(new ShowBmtGuideTipEvent(mDeviceId, mPSDevice.getSubCategory()));
        }
    }

    /**
     * 今日负载
     */
    protected void getStatisticData() {
        if (null != mPSDevice) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_STATS_LOADUSAGE_V2);
            params.put(BmtDataKey.INTERVAL, ElectricityStatisticsFragment.mInterval);
            params.put(BmtDataKey.OFFSET, 0);
            mPSDevice.submit(params);
        }
    }


    /**
     * 支持地区
     */
    private void getFeature() {
        if (null != mPSDevice) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_FEATURE);
            mPSDevice.submit(params);
        }
    }

    /**
     * 今日价格
     */
    private void getPriceInfo() {
        if (null != mPSDevice) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_ELEC_PRICE_INFO);
            params.put(BmtDataKey.OFFSET, 0);
            mPSDevice.submit(params);
        }
    }

    /**
     * 今日收益
     */
    private void getStatRevenue() {
        if (null != mPSDevice) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.GET_STATS_REVENUE);
            params.put(BmtDataKey.INTERVAL, ChartDataUtil.DAY);
            params.put(BmtDataKey.OFFSET, 0);
            mPSDevice.submit(params);
        }
    }

    /**
     * 发送cmd
     *
     * @param cmd
     */
    private void submitCmd(String cmd) {
        if (mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, cmd);
            mPSDevice.submit(params);
        }
    }

    /**
     * 设置忽略异常状态
     */
    private void submitCmdExceptionIgnore(int ignoreException) {
        if (mPSDevice != null && ignoreException > -1) {
            showTimeOutLoadinFramgment();
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_EXCEPTION_IGNORE);
            params.put(PSKeyConstant.IGNORE_EXCEPTION, ignoreException);
            mPSDevice.submit(params);
        }
    }

    private void showWarningDialog() {

        if (mWarningDialog == null) {
            mWarningDialog = WarningDialog.newInstance(mWarningList);
            mWarningDialog.setWarningListener(new WarningDialog.WarningListener() {
                @Override
                public void onClose() {
                    isShowWarmingDialog = false;
                }

                @Override
                public void onRestart(String type) {
                    if (TextUtils.isEmpty(type)) {
                        return;
                    }

                    if (BmtUtil.isBmtDeviceV2(mPSDevice) && PsVersion2EventCode.canResetInverter(type)) {
                        BmtManager.getInstance().resetInverter(mPSDevice, true);
                    } else if (PsVersion1EventCode.canResetInverter(type)) {
                        //                            submitCmdInverterClose();
                        BmtManager.getInstance().resetInverter(mPSDevice, true);
                    }
                }

                @Override
                public void onWithout(String type) {
                    if (TextUtils.isEmpty(type)) {
                        return;
                    }
                    if (BmtUtil.isBmtDeviceV2(mPSDevice)) {
                        return;
                    }
                    int ignoreException;
                    switch (type) {
                        case PsVersion1EventCode.EVENT_PS_7001:
                            ignoreException = 0;
                            break;
                        case PsVersion1EventCode.EVENT_PS_1203:
                            ignoreException = 1;
                            break;
                        case PsVersion1EventCode.EVENT_PS_1204:
                            ignoreException = 2;
                            break;
                        case PsVersion1EventCode.EVENT_PS_1205:
                            ignoreException = 3;
                            break;
                        default:
                            ignoreException = -1;
                    }
                    submitCmdExceptionIgnore(ignoreException);
                }

                @Override
                public void onContactCustomerSupport() {
                    getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
                }
            });
        }
        if (mWarningList.size() > 0) {
            isShowWarmingDialog = true;
            mWarningDialog.show(getChildFragmentManager(), WarningDialog.TAG);
        }
    }

    /**
     * 处理 EV 异常
     *
     * @param exceptions
     */
    private void setEvException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getEVWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 MPPT 异常
     *
     * @param exceptions
     */
    private void setMpptException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getMPPTWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Cabinet 异常
     *
     * @param exceptions
     */
    private void setCabinetException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getCabinetWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Battery 异常
     *
     * @param exceptions
     */
    private void setBatteryException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getBatteryWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 System 异常
     *
     * @param exceptions
     */
    private void setSystemException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getSystemWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Communication 异常
     *
     * @param exceptions
     */
    private void setCommunicationException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getCommunicationWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void showException() {
        mBinding.tvCount.setLocalText(mWarningList.size() > 1 ? (mWarningList.size() + "") : "");
        if (mWarningList.size() > 0) {
            WarningBean warningBean = mWarningList.get(0);
            String eventKey = warningBean.getEventKey();
            if (TextUtils.isEmpty(eventKey)) {
                mBinding.tvWarning.setLocalText(warningBean.getTitle());
            } else {
                String title = Local.s(eventKey) + "-" + Local.s(warningBean.getTitle());
                mBinding.tvWarning.setText(title);
            }
        }
        mBinding.tvCount.setVisibility(mWarningList.size() > 0 ? View.VISIBLE : View.INVISIBLE);
        mBinding.clWarning.setVisibility(mWarningList.size() > 0 ? View.VISIBLE : View.GONE);
    }

    private void setWifiSignal(int wifiSignal) {
        mWifiSignal = wifiSignal;
        if (mPSOnlineStatusItemModel != null) {
            mPSOnlineStatusItemModel.setWifiSignal(wifiSignal - 1);
            mAdapter.notifyItemChanged(mContents.indexOf(mPSOnlineStatusItemModel));
        }
    }

    private void setCellularSignal(int cellular) {
        if (mPSOnlineStatusItemModel != null) {
            mCellularSignal = cellular;
            mPSOnlineStatusItemModel.setCellularSignal(cellular - 1);
            mAdapter.notifyItemChanged(mContents.indexOf(mPSOnlineStatusItemModel));
        }
    }

    private void setEthernetSignal(int ethernet) {
        if (mPSOnlineStatusItemModel != null) {
            mEthernetSignal = ethernet;
            mPSOnlineStatusItemModel.setEthernetSignal(ethernet);
            mAdapter.notifyItemChanged(mContents.indexOf(mPSOnlineStatusItemModel));
        }
    }

    private void setRegulateFrequencyState(int state) {
        if (mPSOnlineStatusItemModel != null) {
            mPSOnlineStatusItemModel.setBalancingState(state);
            mAdapter.notifyItemChanged(mContents.indexOf(mPSOnlineStatusItemModel));
        }
    }

    private void updateUI() {
        if (null == mPSDevice) {
            return;
        }
        String name = DeviceHelper.getString(mPSDevice, DinConst.INFO_NAME, "");
        if (TextUtils.isEmpty(name)) {
            mBinding.commonBar.commonBarTitle.setText("");
        } else {
            mBinding.commonBar.commonBarTitle.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);

            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    long startTime;
                    String timezone;
                    int interval = 1;
                    int offset;
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case DsCamCmd.GET_GLOBAL_EXCEPTIONS:
//                                closeLoadingFragmentWithCallBack();
//                                mWarningList.clear();
//                                final List<WarningBean> warningBeanForInverter = ExceptionWarning.createWarningBeanForInverter(getContext(), result);
//                                if (warningBeanForInverter.size() > 0) {
//                                    mWarningList.addAll(warningBeanForInverter);
//                                }
//
//                                List<Integer> evs = (List<Integer>) MapUtils.get(result, PSKeyConstant.EV, null);
//                                setEvException(evs);
//                                List<Integer> mppts = (List<Integer>) MapUtils.get(result, PSKeyConstant.MPPT, null);
//                                setMpptException(mppts);
//                                List<Integer> cabinets = (List<Integer>) MapUtils.get(result, PSKeyConstant.CABINET, null);
//                                setCabinetException(cabinets);
//                                List<Integer> batteries = (List<Integer>) MapUtils.get(result, PSKeyConstant.BATTERY, null);
//                                setBatteryException(batteries);
//                                List<Integer> systems = (List<Integer>) MapUtils.get(result, PSKeyConstant.SYSTEM, null);
//                                setSystemException(systems);
//                                List<Integer> communications = (List<Integer>) MapUtils.get(result, PSKeyConstant.COMMUNICATION, null);
//                                setCommunicationException(communications);
//                                showException();
                                break;

                            case DsCamCmd.GET_COMMUNICATE_SIGNAL:
                                int wifi = DeviceHelper.getInt(result, PSKeyConstant.WIFI, 0);
                                int cellular = DeviceHelper.getInt(result, BmtDataKey.CELLULAR, 0);
                                int ethernet = DeviceHelper.getInt(result, BmtDataKey.ETHERNET, 0);
                                if (wifi > 0) {
                                    setWifiSignal(wifi);
                                }
                                if (cellular > 0) {
                                    setCellularSignal(cellular);
                                }
                                setEthernetSignal(ethernet);
                                break;
                            case BmtCmd.GET_REGULATE_FREQUENCY_STATE:
                                int state = DeviceHelper.getInt(result, BmtDataKey.STATE, 0);
                                setRegulateFrequencyState(state);
                                break;

                            case DsCamCmd.INVERTER_EXCEPTION:
                            case DsCamCmd.BATTERY_EXCEPTION:
                            case DsCamCmd.MPPT_EXCEPTION:
                            case DsCamCmd.EV_EXCEPTION:
                            case DsCamCmd.COMMUNICATION_EXCEPTION:
                            case DsCamCmd.CABINET_EXCEPTION:
                            case DsCamCmd.SYSTEM_EXCEPTION:
//                                loadCacheException();
                                break;
                            case DinConst.CMD_SET_NAME:
                                updateUI();
                                break;

                            case BmtCmd.GET_STATS_LOADUSAGE:
                            case BmtCmd.GET_STATS_LOADUSAGE_V2:
                                closeLoadingFragment();
                                startTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0);
                                timezone = DeviceHelper.getString(result, BmtDataKey.TIMEZONE, "");
                                interval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);
                                offset = DeviceHelper.getInt(result, BmtDataKey.OFFSET, 0);
                                String cycleType = DeviceHelper.getString(result, BmtDataKey.CYCLE_TYPE, "");
                                if (!(cycleType.equals(ChartDataUtil.DAY) && offset == 0)) {
                                    return;
                                }
                                List<List<Float>> data = (List<List<Float>>) MapUtils.get(result, BmtDataKey.DATA, new ArrayList<>());
                                if (mPSTodayUsageItemModel != null) {
                                    mPSTodayUsageItemModel.setData(data);
                                    mPSTodayUsageItemModel.setInterval(interval);
                                    mPSTodayUsageItemModel.setStartTime(startTime);
                                    mPSTodayUsageItemModel.setTimezone(timezone);
                                    mPSTodayUsageItemModel.setSuccess(true);
                                    mAdapter.notifyItemChanged(mContents.indexOf(mPSTodayUsageItemModel));
                                }
                                break;

                            case DsCamCmd.GET_STATS_REVENUE:
                                if (random == -1) {
                                    random = ChartDataUtil.getRandom(8, 0);
                                }
                                interval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);
                                List<List<Float>> revenueDayData = (List<List<Float>>) result.get(BmtDataKey.DATA);
                                if (mPSImpactStrategiesItemModel != null) {
                                    mPSImpactStrategiesItemModel.setRevenueDayData(revenueDayData);
                                    mPSImpactStrategiesItemModel.setInterval(interval);
                                    mPSImpactStrategiesItemModel.setRandom(random);
                                    mAdapter.notifyItemChanged(mContents.indexOf(mPSImpactStrategiesItemModel));
                                }
                                break;

                            case BmtCmd.GET_BSENSOR_STATUS:
                                isAllBSensorInstalled = DeviceHelper.getBoolean(result, BmtDataKey.B_SENSOR_INSTALLED, false);
//                                getStatisticData();
//                                getStatRevenue();
                                break;

                            case DsCamCmd.SET_EXCEPTION_IGNORE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        // 重新获取exceptions
                                        submitCmd(DsCamCmd.GET_GLOBAL_EXCEPTIONS);
                                    }
                                }, 500);
                                break;

                            case BmtCmd.GET_VIEW_EXCEPTIONS:
                                mWarningList.clear();
                                final List<WarningBean> warningBeanForInverter = ExceptionWarning.createWarningBeanForInverter(getContext()
                                        , result, BmtUtil.isBmtDeviceV2(mPSDevice));
                                if (warningBeanForInverter.size() > 0) {
                                    mWarningList.addAll(warningBeanForInverter);
                                }
                                List<Integer> evs = DeviceHelper.getList(result, PSKeyConstant.EV);
                                setEvException(evs);
                                List<Integer> mppts = DeviceHelper.getList(result, PSKeyConstant.MPPT);
                                setMpptException(mppts);
                                List<Integer> cabinets = DeviceHelper.getList(result, PSKeyConstant.CABINET);
                                setCabinetException(cabinets);
                                List<Integer> batteries = DeviceHelper.getList(result, PSKeyConstant.BATTERY);
                                setBatteryException(batteries);
                                List<Integer> systems = DeviceHelper.getList(result, PSKeyConstant.SYSTEM);
                                setSystemException(systems);
                                List<Integer> communications = DeviceHelper.getList(result, PSKeyConstant.COMMUNICATION);
                                setCommunicationException(communications);
                                showException();
                                break;

                            case BmtCmd.GET_FEATURE:
                                boolean elecSupport = (boolean) MapUtils.get(result, BmtDataKey.ELEC_SUPPORT, false);
                                boolean gridToBattery = (boolean) MapUtils.get(result, BmtDataKey.GRID_TO_BATTERY, false);
                                // 选择不支持“价格追踪模式”，则不显示【今日电价】卡片
                                if (mPSRelativePriceItemModel != null) {
                                    mPSRelativePriceItemModel.setShow(elecSupport && gridToBattery);
                                    mPSRelativePriceItemModel.refresh();
                                }
                                break;

                            case BmtCmd.GET_ELEC_PRICE_INFO:
                                offset = DeviceHelper.getInt(result, BmtDataKey.OFFSET, 0);
                                if (offset != 0) return;
                                startTime = (long) MapUtils.get(result, BmtDataKey.START_TIME, 0);
                                timezone = (String) MapUtils.get(result, BmtDataKey.TIMEZONE, "");
                                String unit = DeviceHelper.getString(result, BmtDataKey.UNIT, "").split("/")[0];
                                String showPriceUnit = DeviceHelper.getString(result, BmtDataKey.UNIT_APP, "");
                                List<List<Float>> chartDataFromServer = DeviceHelper.getList(result, BmtDataKey.DATA);
                                if (mPSRelativePriceItemModel != null) {
                                    mPSRelativePriceItemModel.setData(chartDataFromServer);
                                    mPSRelativePriceItemModel.setInterval(interval);
                                    mPSRelativePriceItemModel.setStartTime(startTime);
                                    mPSRelativePriceItemModel.setTimezone(timezone);
                                    mPSRelativePriceItemModel.setUnit(unit);
                                    mPSRelativePriceItemModel.setShowPriceUnit(showPriceUnit);
                                    mPSRelativePriceItemModel.setSuccess(true);
                                    mPSRelativePriceItemModel.refresh();
                                }
                                break;

                        }
                    } else {

                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();

                            case DsCamCmd.GET_STATS_REVENUE:
                                if (mPSImpactStrategiesItemModel != null) {
                                    mPSImpactStrategiesItemModel.setRevenueDayData(null);
                                    mAdapter.notifyItemChanged(mContents.indexOf(mPSImpactStrategiesItemModel));
                                }
                                break;

                            case DsCamCmd.SET_INVERTER_OPEN:

                            case DsCamCmd.SET_EXCEPTION_IGNORE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                                break;

                            case BmtCmd.GET_STATS_LOADUSAGE:
                            case BmtCmd.GET_STATS_LOADUSAGE_V2:
                                closeLoadingFragment();
                                if (!getMainActivity().isFragmentInTopExcludeLoading(PowerStationRoot2Fragment.this))
                                    return;
                                int hourCount = 1440;
                                interval = 5;
                                List<List<Float>> chartDataFromServer = new ArrayList<>();
                                List<List<Float>> data = ChartDataFactory.createBuilder()
                                        .setIndex(BaseChartFragment.CHART_ELECTRICITY_USAGE)
                                        .setSize(hourCount / interval + 1)
                                        .setInterval(interval)
                                        .setCycType(CycleType.DAY)
                                        .setPendingData(chartDataFromServer)
                                        .build()
                                        .createChartData();

                                if (mPSTodayUsageItemModel != null) {
                                    mPSTodayUsageItemModel.setData(data);
                                    mPSTodayUsageItemModel.setInterval(interval);
                                    mPSTodayUsageItemModel.setStartTime(0);
                                    mPSTodayUsageItemModel.setTimezone("");
                                    mPSTodayUsageItemModel.setSuccess(false);
                                    mAdapter.notifyItemChanged(mContents.indexOf(mPSTodayUsageItemModel));
                                }


                                break;

                            case BmtCmd.GET_ELEC_PRICE_INFO:
                                if (!getMainActivity().isFragmentInTop(PowerStationRoot2Fragment.this))
                                    return;
                                List<List<Float>> failData = new ArrayList<>();
                                List<List<Float>> priceFailData = ChartDataFactory.createBuilder()
                                        .setIndex(BaseChartFragment.CHART_ELECTRICITY_PRICE)
                                        .setSize(24)
                                        .setInterval(1)
                                        .setCycType(CycleType.DAY)
                                        .setPendingData(failData)
                                        .build()
                                        .createChartData();

                                if (mPSRelativePriceItemModel != null) {
                                    mPSRelativePriceItemModel.setData(priceFailData);
                                    mPSRelativePriceItemModel.setInterval(interval);
                                    mPSRelativePriceItemModel.setStartTime(0);
                                    mPSRelativePriceItemModel.setTimezone("");
                                    mPSRelativePriceItemModel.setUnit("");
                                    mPSRelativePriceItemModel.setShowPriceUnit("");
                                    mPSRelativePriceItemModel.setSuccess(false);
                                    mPSRelativePriceItemModel.refresh();
                                }
                                break;
                        }
                    }
                }
            });

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtListUpdateEvent event) {
        final String deviceId = event.getDeviceId();
        final String subcategory = event.getSubcategory();
        final int operateType = event.getOperationType();

        if (TextUtils.isEmpty(deviceId) || !deviceId.equals(mDeviceId) || !subcategory.equals(mSubcategory)) {
            return;
        }

        if (BmtListUpdateEvent.OPERATION_DELETE == operateType) {
            getDelegateActivity().removeAllCommonFragment();
        }
    }

    private void updateByDeviceOnlineStatus() {
        final boolean online = BmtUtil.isDeviceConnected(mPSDevice);
        if (online) {
            if (mPSOnlineStatusItemModel != null) {
                mPSOnlineStatusItemModel.setOnline(true);
                mPSOnlineStatusItemModel.setWifiSignal(mWifiSignal - 1);
                mPSOnlineStatusItemModel.setCellularSignal(mCellularSignal - 1);
                mPSOnlineStatusItemModel.setEthernetSignal(mEthernetSignal);
                mAdapter.notifyItemChanged(mContents.indexOf(mPSOnlineStatusItemModel));
            }
        } else {
            if (mPSOnlineStatusItemModel != null) {
                mPSOnlineStatusItemModel.setOnline(false);
                mAdapter.notifyItemChanged(mContents.indexOf(mPSOnlineStatusItemModel));
            }
            if (mPSBmtInfoItemModel != null) {
                mPSBmtInfoItemModel.setUpdate(false);
//                mAdapter.notifyItemChanged(mContents.indexOf(mPSBmtInfoItemModel));
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGraphicUpdateEvent event) {
        final String deviceId = event.getDeviceId();
        final String cmd = event.getCmd();
        if (TextUtils.isEmpty(deviceId) || !deviceId.equals(mDeviceId) || TextUtils.isEmpty(cmd) || !event.getSubCategory().equals(mSubcategory)) {
            return;
        }

        final Map map = event.getData();

        final Map<String, Object> result = DeviceHelper.getMap(map, PSKeyConstant.RESULT);
        int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
        if (status == StatusConstant.STATUS_SUCCESS && BmtCmd.GET_CHIPS_STATUS.equals(cmd)) {
            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
            if (-1 != chipsStatus) {
                final boolean showMarker = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//                final boolean waitForUpdate = BmtUtil.isWaitForUpdate(chipsStatus);
//                final boolean ignore = BmtManager.getInstance().isIgnoredUpgrade(mDeviceId);
//                final boolean needUpgrade = waitForUpdate ? !ignore : showMarker;
                if (mPSBmtInfoItemModel != null) {
                    mPSBmtInfoItemModel.setUpdate(showMarker);
//                    mAdapter.notifyItemChanged(mContents.indexOf(mPSBmtInfoItemModel));
                }
            }
        }
    }
}
