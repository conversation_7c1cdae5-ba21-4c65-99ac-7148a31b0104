package com.dinsafer.module.powerstation.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.viewpager.widget.ViewPager;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsBmtInfoModelBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.BatteryFragment;
import com.dinsafer.module.powerstation.CurrentDiagramPowerPulseFragment;
import com.dinsafer.module.powerstation.CurrentDiagramPowerStoreFragment;
import com.dinsafer.module.powerstation.CurrentDiagramV2Fragment;

import java.util.ArrayList;

public class PSBmtInfoItemModel extends BasePowerStationItemModel<ItemPsBmtInfoModelBinding> {

    private final BaseFragment mBaseFragment;
    private boolean isUpdate;
    private boolean isCanSlide = true;
    private int curViewPagerSelectedIndex;
    private ItemPsBmtInfoModelBinding mBinding;

    public PSBmtInfoItemModel(BaseFragment baseFragment, String deviceId, String subcategory, int curViewPagerSelectedIndex) {
        super(baseFragment.getContext(), deviceId, subcategory);
        mBaseFragment = baseFragment;
        this.curViewPagerSelectedIndex = curViewPagerSelectedIndex;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_bmt_info_model;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsBmtInfoModelBinding binding) {
        mBinding = binding;
        binding.viewDisable.setVisibility(isUpdate ? View.VISIBLE : View.GONE);
        ArrayList<BaseFragment> fragments = new ArrayList<>();
        Device device = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (BmtUtil.isBmtDevicePowerStore(device)) {
            fragments.add(CurrentDiagramPowerStoreFragment.newInstance(mDeviceId, mSubcategory));
            fragments.add(BatteryFragment.newInstance(mDeviceId, mSubcategory));
        } else if (BmtUtil.isBmtDevicePowerPulse(device)) {
            fragments.add(CurrentDiagramPowerPulseFragment.newInstance(mDeviceId, mSubcategory));
        } else {
            fragments.add(CurrentDiagramV2Fragment.newInstance(mDeviceId, mSubcategory));
            fragments.add(BatteryFragment.newInstance(mDeviceId, mSubcategory));
        }
        CommonPagerAdapter adapter = new CommonPagerAdapter(mBaseFragment.getChildFragmentManager(), fragments);
        binding.vpCurrent.setAdapter(adapter);
        binding.vpCurrent.setCanSlide(isCanSlide);
        ViewGroup.LayoutParams vpLayoutParams = binding.vpCurrent.getLayoutParams();
        vpLayoutParams.height = BmtUtil.isBmtDevicePowerPulse(device) ? binding.vpCurrent.getContext().getResources().getDimensionPixelOffset(R.dimen.power_station_pulse_vp_current_height)
                : binding.vpCurrent.getContext().getResources().getDimensionPixelOffset(R.dimen.power_station_vp_current_height);
        binding.vpCurrent.setLayoutParams(vpLayoutParams);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        //设置小圆点左右之间的间隔
        params.setMargins(8, 0, 8, 0);
        ImageView[] dotViews = new ImageView[fragments.size()];
        if (fragments.size() > 1) {
            for (int i = 0; i < fragments.size(); i++) {
                ImageView imageView = new ImageView(mBaseFragment.getContext());
                imageView.setLayoutParams(params);
                imageView.setImageResource(R.drawable.shape_bg_power_vp_nor);
                //默认启动时，选中第一个小圆点
                imageView.setSelected(i == curViewPagerSelectedIndex);
                dotViews[i] = imageView;
                if (i == 0) {
                    dotViews[i].setImageResource(R.drawable.shape_bg_power_vp_sel); // 设置第一个页面已被选择
                }
                binding.llIndicator.addView(imageView);
            }
        }

        binding.vpCurrent.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                curViewPagerSelectedIndex = position;
                if (fragments.size() > 1) {
                    for (int i = 0; i < dotViews.length; i++) {
                        dotViews[i].setSelected(curViewPagerSelectedIndex == i);
                        dotViews[i].setImageResource(curViewPagerSelectedIndex == i
                                ? R.drawable.shape_bg_power_vp_sel : R.drawable.shape_bg_power_vp_nor);
                    }
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        binding.vpCurrent.setCurrentItem(curViewPagerSelectedIndex);

    }

    public void setUpdate(boolean update) {
        isUpdate = update;
        isCanSlide = !update;
        if (mBinding != null) {
            mBinding.viewDisable.setVisibility(isUpdate ? View.VISIBLE : View.GONE);
            mBinding.vpCurrent.setCanSlide(isCanSlide);
        }
    }

    public void setCanSlide(boolean canSlide) {
        isCanSlide = canSlide;
    }
}
