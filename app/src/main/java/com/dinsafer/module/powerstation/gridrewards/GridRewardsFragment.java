package com.dinsafer.module.powerstation.gridrewards;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.DDLog;
import com.dinsafer.common.utils.ScreenUtils;
import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentGridRewardsBinding;
import com.dinsafer.model.BleDeviceSimpleEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.adapter.BleScanDeviceAdapter;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;
import com.google.gson.Gson;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/23
 * @author: create by Sydnee
 */
public class GridRewardsFragment extends MyBaseFragment<FragmentGridRewardsBinding> {

    private BindMultiAdapter<TitleContentModel> mAdapter;
    private ArrayList<BleDeviceSimpleEntry> simpleObjectList;
    private BleScanDeviceAdapter objectAdapter;
    private List<CountryBean> mCountryList;
    private FamilyBalanceContractInfoResponse.ResultBean mContractInfo;


    public static GridRewardsFragment newInstance(List<CountryBean> countryList, FamilyBalanceContractInfoResponse.ResultBean contractInfo) {
        GridRewardsFragment fragment = new GridRewardsFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PSKeyConstant.KEY_COUNTRIES, (Serializable) countryList);
        bundle.putSerializable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, contractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static GridRewardsFragment newInstanceOnlyShow() {
        return new GridRewardsFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_grid_rewards;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.prime_service_grid_reward));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.tvTitle.setLocalText(getString(R.string.grid_title));
        mBinding.tvContent.setLocalText(getString(R.string.grid_content));

        mBinding.btnJoin.setLocalText(getString(R.string.sign_up));
        mBinding.btnJoin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toAppear();
            }
        });

        mBinding.imgBtnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toDisappear();
            }
        });
        initRv();
        String key = DBKey.KEY_OLD_CONTRACT_DATA + HomeManager.getInstance().getCurrentHome().getHomeID();
        if (!DBUtil.contain(key)) {
            DBUtil.Put(key, true);
        }
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mCountryList = (List<CountryBean>) bundle.getSerializable(PSKeyConstant.KEY_COUNTRIES);
            mContractInfo = (FamilyBalanceContractInfoResponse.ResultBean) bundle.getSerializable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
        } else {
            mBinding.btnJoin.setVisibility(View.GONE);
        }
    }

    private void initRv() {
        mAdapter = new BindMultiAdapter<>();
        List<TitleContentModel> modelList = new ArrayList<>();
        modelList.add(new TitleContentModel(getString(R.string.grid_title2), getString(R.string.grid_content2_1), ""));
        modelList.add(new TitleContentModel(getString(R.string.grid_title3), getString(R.string.grid_content3_1), ""));
        modelList.add(new TitleContentModel(getString(R.string.grid_title4), getString(R.string.grid_content4_1), getString(R.string.grid_content4_2)));
        mAdapter.setNewData(modelList);
        mBinding.rcvContent.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rcvContent.setAdapter(mAdapter);
        mBinding.rcvContent.setNestedScrollingEnabled(false);

        simpleObjectList = new ArrayList<>();
        BleDeviceSimpleEntry company = new BleDeviceSimpleEntry(BleDeviceSimpleEntry.BLE_DEVICE_STATUS_NORMAL, Local.s(getString(R.string.grid_sign_as_company)), false);
        company.setIconID(R.drawable.icon_company_organization);
        simpleObjectList.add(company);
        BleDeviceSimpleEntry person = new BleDeviceSimpleEntry(BleDeviceSimpleEntry.BLE_DEVICE_STATUS_NORMAL, Local.s(getString(R.string.grid_sign_as_person)), false);
        person.setIconID(R.drawable.icon_private_person);
        simpleObjectList.add(person);
        objectAdapter = new BleScanDeviceAdapter(simpleObjectList);
        mBinding.rvSignObjectList.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvSignObjectList.setAdapter(objectAdapter);

        objectAdapter.setOnItemClick(new BleScanDeviceAdapter.OnItemClick() {
            @Override
            public void connect(BleDeviceSimpleEntry bleDeviceSimpleEntry, int position) {
                String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
                String cacheStr = DBUtil.Str(DBKey.KEY_FAMILY_BALANCE_CONTRACT_INFO + "_" + position + "_" + homeId);
                FamilyBalanceContractInfo familyBalanceContractInfo;
                // 如果有对应缓存信息用缓存信息
                if (!TextUtils.isEmpty(cacheStr)) {
                    familyBalanceContractInfo = new Gson().fromJson(cacheStr, FamilyBalanceContractInfo.class);
                    familyBalanceContractInfo.setType(position);
                    if (mContractInfo != null) {
                        familyBalanceContractInfo.setFirst_sign(mContractInfo.isFirst_sign());
                        familyBalanceContractInfo.setFamilySigning(mContractInfo.isSigning());
                    }
                } else {
                    // 没有缓存信息新建一个
                    familyBalanceContractInfo = new FamilyBalanceContractInfo();
                    familyBalanceContractInfo.setType(position);
                    // 再把预填信息塞进对应字段
                    if (mContractInfo != null) {
                        FamilyBalanceContractInfoResponse.ResultBean.Data data = mContractInfo.getData();
                        if (data != null) {
                            familyBalanceContractInfo.setName(data.getName());
                            familyBalanceContractInfo.setCompany_name(data.getCompany_name());
                            familyBalanceContractInfo.setEuVatNumber(data.getEu_vat_number());
                            familyBalanceContractInfo.setEmailAddress(data.getEmail_address());
                            familyBalanceContractInfo.setCountry_code(data.getCountry_code());
                            familyBalanceContractInfo.setCountryNameDisplay(data.getCountry_name_display());
                            familyBalanceContractInfo.setCity(data.getCity());
                            familyBalanceContractInfo.setStreetNameAndNumber(data.getStreet_name_and_number());
                            familyBalanceContractInfo.setZipCode(data.getZip_code());
                            familyBalanceContractInfo.setElectricitySupplier(data.getElectricity_supplier());
                            familyBalanceContractInfo.setElectricitySupplierId(data.getElectricity_supplier_id());
                            familyBalanceContractInfo.setCardholder(data.getCardholder());
                            familyBalanceContractInfo.setIBAN(data.IBAN);
                        }
                        familyBalanceContractInfo.setFirst_sign(mContractInfo.isFirst_sign());
                        familyBalanceContractInfo.setFamilySigning(mContractInfo.isSigning());
                    }

                }
                familyBalanceContractInfo.setHome_id(HomeManager.getInstance().getCurrentHome().getHomeID());
                familyBalanceContractInfo.setHome_name(HomeManager.getInstance().getCurrentHome().getHomeName());
                getDelegateActivity().addCommonFragment(PSUserInformationFragment.newInstance(mCountryList, familyBalanceContractInfo));
            }
        });
    }

    private void toAppear() {
        DDLog.d(TAG, "toAppear.");
        ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.rlSignAs, "translationY"
                , ScreenUtils.getScreenHeight(getDelegateActivity()), 0);
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {
                mBinding.clSignAs.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        animator.setDuration(300);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.start();
    }

    private void toDisappear() {
        DDLog.d(TAG, "toDisappear.");
        ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.rlSignAs, "translationY"
                , 0, ScreenUtils.getScreenHeight(getDelegateActivity()));
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                mBinding.clSignAs.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        animator.setDuration(300);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.start();
    }
}
