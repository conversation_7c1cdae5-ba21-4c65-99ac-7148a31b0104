package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;

import android.text.TextUtils;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBatteryDetailBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.BatteryDetailsAdapter;
import com.dinsafer.module.powerstation.bean.BatteryDetailsItemBean;
import com.dinsafer.module.powerstation.bean.CabinetBatteryBean;
import com.dinsafer.util.Local;
import com.dinsafer.util.UnitUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 16:44
 * @description :
 */
public class BatteryDetailFragment extends MyBaseFragment<FragmentBatteryDetailBinding> {

    private BatteryDetailsAdapter mBaseInfoAdapter;
    private BatteryDetailsAdapter mTemperatureInfoAdapter;
    private CabinetBatteryBean mCabinetBatteryBean;
    private String mUnit;

    public static BatteryDetailFragment newInstance(String unit, CabinetBatteryBean cabinetBatteryBean) {
        BatteryDetailFragment fragment = new BatteryDetailFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_UNIT, unit);
        bundle.putParcelable(PSKeyConstant.KEY_BEAN, cabinetBatteryBean);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_battery_detail;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_battery_details));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        initRvBaseInfo();
        initRvTemperatureInfo();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mUnit = bundle.getString(PSKeyConstant.KEY_UNIT);
        if (TextUtils.isEmpty(mUnit)) {
            mUnit = getString(R.string.ps_advanced_settings_celsius_unit);
        }
        mCabinetBatteryBean = bundle.getParcelable(PSKeyConstant.KEY_BEAN);
    }

    /**
     * 基础信息
     */
    private void initRvBaseInfo() {
        mBinding.rvBase.setLayoutManager(new LinearLayoutManager(getContext()));
        mBaseInfoAdapter = new BatteryDetailsAdapter();
        mBinding.rvBase.setAdapter(mBaseInfoAdapter);
        if (mCabinetBatteryBean != null) {
            int cabinetIndex = mCabinetBatteryBean.getCabinetIndex() + 1;
            int indexInCabinet = mCabinetBatteryBean.getIndexInCabinet();
            String indexStr = cabinetIndex < 10 ? "0" + cabinetIndex : "" + cabinetIndex;
            indexStr += "_" + (indexInCabinet + 1);
            List<BatteryDetailsItemBean> data = new ArrayList<>();
            data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_id), mCabinetBatteryBean.getBarcode()));
            String cabinetIndexKey = getString(R.string.ps_battery_overview_cabinet_battery);
            String cabinetIndexSuffix = getString(R.string.ps_hashtag_cabinet_battery_index);
            data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_seat), Local.s(cabinetIndexKey).replace(cabinetIndexSuffix, indexStr)));
            data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_charge_switch),
                    getString(mCabinetBatteryBean.isChargeSwitchOn() ? R.string.on : R.string.off)));
            data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_discharge_switch),
                    getString(mCabinetBatteryBean.isDischargeSwitchOn() ? R.string.on : R.string.off)));
            int state = mCabinetBatteryBean.getState();
            int capacity = mCabinetBatteryBean.getCapacity();
//            double capacityDou = capacity/1000.0;
//            double finalCapacity = UnitUtil.savePoint(capacityDou, 1, BigDecimal.ROUND_FLOOR);
            data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_capacity),  capacity + "Wh"));
            int soc = mCabinetBatteryBean.getSoc();
            if (soc < 0 || soc > 100) {
                soc = 0;
            }
            data.add(new BatteryDetailsItemBean(getString(R.string.power_battery_balance), soc + "%"));
            data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_cycle_times), mCabinetBatteryBean.getReleaseTimes() + ""));
            mBaseInfoAdapter.setNewData(data);
        }
    }

    /**
     * 温度信息
     */
    private void initRvTemperatureInfo() {
        mBinding.rvTemperature.setLayoutManager(new LinearLayoutManager(getContext()));
        mTemperatureInfoAdapter = new BatteryDetailsAdapter();
        mBinding.rvTemperature.setAdapter(mTemperatureInfoAdapter);
        List<BatteryDetailsItemBean> data = new ArrayList<>();
        Double bmsTemp = mCabinetBatteryBean.getBmsTemp();

//        int temperatureLogo = R.drawable.icon_accessories_temperture_null;
        if (bmsTemp != null) {
//            temperatureLogo = R.drawable.icon_accessories_temperture_nor;
            bmsTemp = UnitUtil.getTemperature(getContext(), mUnit, bmsTemp);
        }
        String bmsTempStr = bmsTemp != null ? UnitUtil.covertTemperatureStr(bmsTemp) + mUnit : getString(R.string.ps_battery_overview_unknown);
//        if (mCabinetBatteryBean.isHighTemp()) {
//            temperatureLogo = R.drawable.icon_accessories_temperture_high;
//        }
//        if (mCabinetBatteryBean.isLowTemp()) {
//            temperatureLogo = R.drawable.icon_accessories_temperture_low;
//        }
        data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_temperature_of_bms), bmsTempStr));

//        int temperatureALogo = R.drawable.icon_accessories_temperture_null;
        Double electrodeATemp = mCabinetBatteryBean.getElectrodeATemp();
        String aTempStr = getString(R.string.ps_battery_overview_unknown);
        if (electrodeATemp != null) {
//            temperatureALogo = R.drawable.icon_accessories_temperture_nor;
            electrodeATemp = UnitUtil.getTemperature(getContext(), mUnit, electrodeATemp);
            aTempStr = UnitUtil.covertTemperatureStr(electrodeATemp) + mUnit;
        }
//        if (mCabinetBatteryBean.isHighTemp()) {
//            temperatureALogo = R.drawable.icon_accessories_temperture_high;
//        }
//        if (mCabinetBatteryBean.isLowTemp()) {
//            temperatureALogo = R.drawable.icon_accessories_temperture_low;
//        }
        String temperatureCell = getString(R.string.ps_battery_temperature_of_cell);
        String hashTagIndex = getString(R.string.ps_hashtag_cell_index);
        data.add(new BatteryDetailsItemBean(Local.s(temperatureCell).replace(hashTagIndex, getString(R.string.ps_battery_core_a)),
                aTempStr));

        Double electrodeBTemp = mCabinetBatteryBean.getElectrodeBTemp();
//        int temperatureBLogo = R.drawable.icon_accessories_temperture_null;
        String bTempStr = getString(R.string.ps_battery_overview_unknown);
        if (electrodeATemp != null) {
//            temperatureBLogo = R.drawable.icon_accessories_temperture_nor;
            electrodeBTemp = UnitUtil.getTemperature(getContext(), mUnit, electrodeBTemp);
            bTempStr = UnitUtil.covertTemperatureStr(electrodeBTemp) + mUnit;
        }
//        if (mCabinetBatteryBean.isHighTemp()) {
//            temperatureBLogo = R.drawable.icon_accessories_temperture_high;
//        }
//        if (mCabinetBatteryBean.isLowTemp()) {
//            temperatureBLogo = R.drawable.icon_accessories_temperture_low;
//        }
        data.add(new BatteryDetailsItemBean(Local.s(temperatureCell).replace(hashTagIndex, getString(R.string.ps_battery_core_b)),
                bTempStr));

        Boolean heatAvailable = mCabinetBatteryBean.getHeatAvailable();
        String heatStr = getString(R.string.ps_battery_overview_unknown);
        int heatLogo = R.drawable.icon_accessories_heating_null;
        if (heatAvailable != null) {
            if (heatAvailable == true) {
                heatStr = getString(R.string.Online);
                heatLogo = R.drawable.icon_accessories_heating_nor;
                Boolean heating = mCabinetBatteryBean.getHeating();
                if (heating != null && heating == true) {
                    heatLogo = R.drawable.icon_accessories_heating_on;
                    heatStr = getString(R.string.ps_heating);
                }
            } else {
                heatStr = getString(R.string.ps_battery_overview_unavailable);
            }
        }
        data.add(new BatteryDetailsItemBean(getString(R.string.ps_battery_heating_film), heatStr, heatLogo));
        mTemperatureInfoAdapter.setNewData(data);
    }
}
