package com.dinsafer.module.powerstation.event;

public class ChargeModeEvent {
    private final String deviceId;
    private final String subcategory;
    /**
     * 是否仅仅更新了模式
     */
    private final boolean onlyMode;
    private int mode;
    private int smartReserve;
    private int emergencyReserve;
    private boolean isManual;

    public ChargeModeEvent(final String deviceId, final String subcategory, int mode) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.onlyMode = true;
        this.mode = mode;
    }

    public ChargeModeEvent(final String deviceId, final String subcategory, int mode, int smartReserve, int emergencyReserve) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.onlyMode = false;
        this.mode = mode;
        this.smartReserve = smartReserve;
        this.emergencyReserve = emergencyReserve;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public boolean isOnlyMode() {
        return onlyMode;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public int getSmartReserve() {
        return smartReserve;
    }

    public void setSmartReserve(int smartReserve) {
        this.smartReserve = smartReserve;
    }

    public int getEmergencyReserve() {
        return emergencyReserve;
    }

    public void setEmergencyReserve(int emergencyReserve) {
        this.emergencyReserve = emergencyReserve;
    }

    public boolean isManual() {
        return isManual;
    }

    public void setManual(boolean manual) {
        isManual = manual;
    }
}
