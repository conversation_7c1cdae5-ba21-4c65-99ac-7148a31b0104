
package com.dinsafer.module.powerstation.dialog;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import com.aigestudio.wheelpicker.WheelPicker;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogPsTimePickerBinding;
import com.dinsafer.permission.BaseBottomSheetDialog;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.TimeUtil;

import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/27 17:22
 * @description :
 */
public class PSTimePickerDialog extends BaseBottomSheetDialog<DialogPsTimePickerBinding> {

    private Context mContext;
    public static final String TAG = PSTimePickerDialog.class.getSimpleName();
    public static final String KEY_FROM_AT = "key_from_at";
    public static final String KEY_START_TIME = "key_start_time";
    public static final String KEY_END_TIME = "key_end_time";
    public static final String KEY_START_TIME_STR = "key_start_time_str";
    public static final String KEY_END_TIME_STR = "key_end_time_str";
    public static int TV_START_SELECTED = 1;
    public static int TV_END_SELECTED = 2;
    private final ArrayList<Date> mDayList = new ArrayList<>();
    private final ArrayList<String> mDayStrList = new ArrayList<>();
    private final ArrayList<String> mTimeList = new ArrayList<>();

    private int mFromAt; // 1 start 2 end
    private long mStartTime;
    private long mEndTime;
    private String mStartTimeStr;
    private String mEndTimeStr;

    public PSTimePickerDialog(Context context) {
        this.mContext = context;
    }

    public static PSTimePickerDialog newInstance(Context context, Builder builder) {
        PSTimePickerDialog dialog = new PSTimePickerDialog(context);
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_FROM_AT, builder.fromAt);
        bundle.putLong(KEY_START_TIME, builder.startTime);
        bundle.putLong(KEY_END_TIME, builder.endTime);
        bundle.putString(KEY_START_TIME_STR, builder.startTimeStr);
        bundle.putString(KEY_END_TIME_STR, builder.endTimeStr);
        dialog.setArguments(bundle);
        dialog.setConfirmListener(builder.confirmListener);
        return dialog;
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_ps_time_picker;
    }

    @Override
    protected int provideDialogHeight() {
        return dip2px(342);
    }

    @Override
    protected void initView() {
        super.initView();
        mBinding.tvCancel.setOnClickListener(v -> dismiss());
        mBinding.tvConfirm.setOnClickListener(v -> {
            if (confirmListener != null) {
                long tempTime = 0;
                String tempTimeStr = "";
                if (mStartTime > mEndTime) {
                    tempTime = mStartTime;
                    mStartTime = mEndTime;
                    mEndTime = tempTime;
                    tempTimeStr = mStartTimeStr;
                    mStartTimeStr = mEndTimeStr;
                    mEndTimeStr = tempTimeStr;
                }
                confirmListener.onConfirm(mStartTime, mEndTime, mStartTimeStr, mEndTimeStr);
            }
            dismiss();
        });
        mBinding.tvStart.setOnClickListener(v -> setTimeSelected(TV_START_SELECTED));
        mBinding.tvEnd.setOnClickListener(v -> setTimeSelected(TV_END_SELECTED));
        initParam();
        initWheelData();
        setTimeSelected(mFromAt);
    }

    private void initParam() {
        Bundle bundle = getArguments();
        mFromAt = bundle.getInt(KEY_FROM_AT, 1);
        mStartTime = bundle.getLong(KEY_START_TIME);
        mEndTime = bundle.getLong(KEY_END_TIME);
        mStartTimeStr = bundle.getString(KEY_START_TIME_STR);
        mEndTimeStr = bundle.getString(KEY_END_TIME_STR);
        mBinding.tvStart.setText(mStartTimeStr);
        mBinding.tvEnd.setText(mEndTimeStr);
    }

    private void setTimeSelected(int fromAt) {
        mBinding.tvStart.setSelected(fromAt == TV_START_SELECTED);
        mBinding.tvEnd.setSelected(fromAt == TV_END_SELECTED);
        long compareTime = fromAt == 1 ? mStartTime : mEndTime;
        String compareText = fromAt == 1 ? mBinding.tvStart.getText().toString() : mBinding.tvEnd.getText().toString();
        for (int i = 0; i < mDayList.size(); i++) {
            Date date = mDayList.get(i);
            if (TimeUtil.isSameDay(compareTime, date.getTime())) {
                mBinding.wpDay.setSelectedItemPosition(i);
                setTimeData();
                break;
            }
        }
        int hour = TimeUtil.getDateHour(new Date(compareTime));
        for (int i = 0; i < mTimeList.size(); i++) {
            String timeStr = mTimeList.get(i);
            if (timeStr.equals(Local.s(mContext.getString(R.string.impact_strategies_now)))) {
                if (compareText.equals(Local.s(mContext.getString(R.string.impact_strategies_now)))) {
                    mBinding.wpTime.setSelectedItemPosition(i);
                    setTimeData();
                    break;
                }
            } else {
                String[] time = timeStr.split(":");
                if (time[0].equals(hour < 10 ? ("0" + hour) : hour + "")) {
                    mBinding.wpTime.setSelectedItemPosition(i);
                    setTimeData();
                    break;
                }
            }
        }
    }

    // 初始化时间列表数据
    private void initWheelData() {
        mDayStrList.addAll(DDDateUtil.getDaysRecentStr(100, DDDateUtil.LOCAL_MONTH_DATE_FORMAT));
        mDayList.addAll(DDDateUtil.getDaysRecent(100));
        mBinding.wpDay.setData(mDayStrList);
        mTimeList.addAll(TimeUtil.getHourSection(24));
        setTimeData();
        mBinding.wpDay.setOnItemSelectedListener(new WheelPicker.OnItemSelectedListener() {
            @Override
            public void onItemSelected(WheelPicker picker, Object data, int position) {
                setTimeData();
                setText();
            }
        });
        mBinding.wpTime.setOnItemSelectedListener(new WheelPicker.OnItemSelectedListener() {
            @Override
            public void onItemSelected(WheelPicker picker, Object data, int position) {
                setText();
            }
        });
    }

    private void setTimeData() {
        mTimeList.clear();
        int hour = TimeUtil.getDateHour(new Date());
        int index = mBinding.wpDay.getCurrentItemPosition();
//        if (index < mDayList.size()-14) {
//            mTimeList.addAll(TimeUtil.getHourSection(hour+1));
//        }else if (index == mDayList.size()-1) {
//            mTimeList.addAll(TimeUtil.getHourSection(hour+2));
//        } else {
//            mTimeList.addAll(TimeUtil.getHourSection(24));
//        }
        if (index == 0) {
            mTimeList.addAll(TimeUtil.getHourSection(getContext(), hour, 24));
        } else {
            mTimeList.addAll(TimeUtil.getHourSection(24));
        }
        mBinding.wpTime.setData(mTimeList);
    }

    private void setText() {
        String text = mDayStrList.get(mBinding.wpDay.getCurrentItemPosition()) + " " + mTimeList.get(mBinding.wpTime.getCurrentItemPosition());
        if (text.contains(Local.s(mContext.getString(R.string.impact_strategies_now)))) {
            text = Local.s(mContext.getString(R.string.impact_strategies_now));
        }
        Date date = mDayList.get(mBinding.wpDay.getCurrentItemPosition());
        int selectHour = TimeUtil.getDateHour(new Date());
        String hourStr = mTimeList.get(mBinding.wpTime.getCurrentItemPosition());
        if (!hourStr.equals(Local.s(mContext.getString(R.string.impact_strategies_reserve_mode_now)))) {
            String[] hourArray = hourStr.split(":");
            String hourOne = hourArray[0];
            if (hourOne.startsWith("0")) {
                hourOne = hourOne.substring(1);
            }
            selectHour = Integer.parseInt(hourOne);
        }

        date = DDDateUtil.getHourTime(date, selectHour, 1);
        if (mBinding.tvStart.isSelected()) {
            mStartTime = date.getTime();
            mStartTimeStr = text;
            mBinding.tvStart.setText(text);
        }
        if (mBinding.tvEnd.isSelected()) {
            mEndTime = date.getTime();
            mEndTimeStr = text;
            mBinding.tvEnd.setText(text);
        }
    }

    public static class Builder {
        private int fromAt; // 1 start 2 end
        private long startTime;
        private long endTime;
        private String startTimeStr;
        private String endTimeStr;
        private OnConfirmListener confirmListener;

        public Builder setFromAt(int fromAt) {
            this.fromAt = fromAt;
            return this;
        }

        public Builder setStartTime(long startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder setEndTime(long endTime) {
            this.endTime = endTime;
            return this;
        }

        public Builder setStartTimeStr(String startTimeStr) {
            this.startTimeStr = startTimeStr;
            return this;
        }

        public Builder setEndTimeStr(String endTimeStr) {
            this.endTimeStr = endTimeStr;
            return this;
        }

        public Builder setConfirmListener(OnConfirmListener confirmListener) {
            this.confirmListener = confirmListener;
            return this;
        }

        public PSTimePickerDialog build(Context context) {
            return newInstance(context,this);
        }

    }

    private OnConfirmListener confirmListener;

    public void setConfirmListener(OnConfirmListener confirmListener) {
        this.confirmListener = confirmListener;
    }

    public interface OnConfirmListener {
        void onConfirm(long startTime, long endTime, String startTimeStr, String endTimeStr);
    }
}
