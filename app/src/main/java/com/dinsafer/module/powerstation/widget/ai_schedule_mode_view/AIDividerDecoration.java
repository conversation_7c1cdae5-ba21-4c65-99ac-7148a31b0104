package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.DensityUtils;

import java.util.Objects;

public class AIDividerDecoration extends RecyclerView.ItemDecoration {

    private final Context mContext;
    private final RectF rectF;
    private final Paint mPaint;
    private final int mStrokeWidth;
    private int enabledSize;

    public AIDividerDecoration(Context context) {
        mContext = context;
        rectF = new RectF();
        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.STROKE);
        mStrokeWidth = DensityUtils.dp2px(context, 1);
        mPaint.setStrokeWidth(mStrokeWidth);
        mPaint.setAntiAlias(true);
    }

    @Override
    public void onDrawOver(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = parent.getChildAt(i);
            if (child != null) {
                int position = parent.getChildAdapterPosition(child);
                if (position == enabledSize - 1 || position == enabledSize
                        || position == Objects.requireNonNull(parent.getAdapter()).getItemCount() - 1)
                    continue;
                View viewData = child.findViewById(R.id.view_data);
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) child.getLayoutParams();
                int left = viewData.getLeft();
                int right = viewData.getRight();
                int bottom = child.getBottom() + params.bottomMargin;
                int top = bottom - viewData.getHeight();
                mPaint.setColor(mContext.getResources().getColor(R.color.color_white_04));
                c.drawLine(left, bottom + mStrokeWidth / 2f, right, bottom + mStrokeWidth / 2f, mPaint);
            }
        }
    }

    public void setEnabledSize(int enabledSize) {
        this.enabledSize = enabledSize;
    }
}
