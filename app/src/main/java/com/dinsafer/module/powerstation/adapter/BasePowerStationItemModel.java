package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import androidx.databinding.ViewDataBinding;

import com.dinsafer.ui.rv.BindModel;

public abstract class BasePowerStationItemModel <V extends ViewDataBinding> extends BindModel<V> {

    protected Context mContext;
    protected String mDeviceId;
    protected String mSubcategory;

    public BasePowerStationItemModel(Context context, String deviceId, String subcategory) {
        super(context);
        this.mContext = context;
        mDeviceId = deviceId;
        mSubcategory = subcategory;
    }
}
