package com.dinsafer.module.powerstation.adapter;

import androidx.annotation.DrawableRes;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsWelcomeInfoBinding;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/2 17:50
 * @description :
 */
public class PSWelcomeInfoModel implements BaseBindModel<ItemPsWelcomeInfoBinding> {

    @DrawableRes
    private int logo;
    private String title;
    private String content;

    public PSWelcomeInfoModel(int logo, String title, String content) {
        this.logo = logo;
        this.title = title;
        this.content = content;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_welcome_info;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsWelcomeInfoBinding itemPsWelcomeInfoBinding) {
        itemPsWelcomeInfoBinding.ivLogo.setImageResource(logo);
        itemPsWelcomeInfoBinding.tvTitle.setLocalText(title);
        itemPsWelcomeInfoBinding.tvContent.setLocalText(content);
    }
}
