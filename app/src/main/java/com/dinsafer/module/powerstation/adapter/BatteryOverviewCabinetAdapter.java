package com.dinsafer.module.powerstation.adapter;

import androidx.databinding.DataBindingUtil;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutCabinetBatteryBinding;
import com.dinsafer.common.utils.DensityUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/28 23:42
 * @description :
 */
public class BatteryOverviewCabinetAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    public BatteryOverviewCabinetAdapter() {
        super(R.layout.item_battery_overview_cabinet);
    }

    @Override
    protected void convert(BaseViewHolder helper, String item) {
        LinearLayout linearLayout = helper.getView(R.id.ll_battery);
        List<String> data = new ArrayList<>();
        int size = helper.getAdapterPosition()==0 ? 3 : 5;
        for (int i=0; i<size; i++) {
            data.add("aa");
        }
        createCellView(linearLayout, data);
    }

    private void createCellView(LinearLayout llLayout, List<String> item) {
        for (int i=0; i<item.size(); i++) {
            View view = View.inflate(mContext, R.layout.layout_cabinet_battery, null);
            LayoutCabinetBatteryBinding binding = DataBindingUtil.bind(view);
            LinearLayout.LayoutParams llParam = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtil.dp2px(mContext, 72));
            llParam.leftMargin =  DensityUtil.dp2px(mContext, 6);
            llParam.rightMargin =  DensityUtil.dp2px(mContext, 6);
            llParam.bottomMargin =  DensityUtil.dp2px(mContext, 6);
            view.setLayoutParams(llParam);
            llLayout.addView(view);
        }
    }
}
