package com.dinsafer.module.powerstation.settings.network;

import android.Manifest;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.BmtManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.utils.BleHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.PsNetworkBleScanDeviceLayoutBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.model.BleCloseTimerEvent;
import com.dinsafer.model.BleDeviceSimpleEntry;
import com.dinsafer.model.BleStartScanEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.BleCheckBluetoothDialog;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module_bmt.add.BaseBmtNetworkManager;
import com.dinsafer.module_bmt.add.BmtHP5000NetworkManager;
import com.dinsafer.module_bmt.add.BmtHP5001NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerCore20NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerCore30NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerPulseNetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerStoreNetworkManager;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.yanzhenjie.permission.Action;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * BMT 网络修改扫描页面
 */
public class PSNetworkBleScanFragment extends MyBaseFragment<PsNetworkBleScanDeviceLayoutBinding> implements BleHelper.ConnectCallback {

    private Subscription connectTimer;

    private boolean isUp = true;
    private ArrayList<BleDevice> bleDeviceArrayList;
    private ArrayList<BleDeviceSimpleEntry> simpleBleList;
    private boolean isScan = false;

    private String TAG = "PSNetworkBleScanFragment bletest";
    public final static int ANIM_UP_TIME = 300;
    private BaseBmtNetworkManager mBinder;
    private String id;
    private String subcategory;
    private Device mPSDevice;
    private String provider;
    private ConnectHandler connectHandler = new ConnectHandler();
    private boolean isScanError;

    public static PSNetworkBleScanFragment newInstance(String deviceID, String subcategory) {
        PSNetworkBleScanFragment bmtBleScanFragment = new PSNetworkBleScanFragment();
        Bundle bundle = new Bundle();
        bundle.putString("deviceID", deviceID);
        bundle.putString("subcategory", subcategory);
        bmtBleScanFragment.setArguments(bundle);
        return bmtBleScanFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.ps_network_ble_scan_device_layout;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        clean();
        if (null != mBinder) {
            mBinder.destroyBinder();
        }
        if (connectHandler != null) {
            connectHandler.removeCallbacksAndMessages(null);
            connectHandler = null;
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> toBack());
        mBinding.commonBarRight.setOnClickListener(v -> toHelp(BleCheckBluetoothDialog.BLE_DIALOG_CHANGE_NETWORK_CHECK_BMT_DEVICE));
        mBinding.btnConnect.setOnClickListener(v -> realStartScan());

        String uri = "json/animation_loading.json";

        mBinding.lodingImage.setRepeatCount(ValueAnimator.INFINITE);
        mBinding.lodingImage.setAnimation(uri);
        mBinding.lodingImage.playAnimation();
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        bleDeviceArrayList = new ArrayList<BleDevice>();
        simpleBleList = new ArrayList<BleDeviceSimpleEntry>();
        id = getArguments().getString("deviceID");
        subcategory = getArguments().getString("subcategory");
        mPSDevice = BmtManager.getInstance().getDeviceById(id, subcategory);
        provider = mPSDevice.getSubCategory();
        tmpBleDeviceArrayList = new ArrayList<BleDevice>();
        tmpSimpleBleList = new ArrayList<BleDeviceSimpleEntry>();


        DinSDK.getPluginActivtor().setup(DinSaferApplication.getAppContext());
        if (DinConst.TYPE_BMT_HP5001.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtHP501NetworkManager();
        } else if (DinConst.TYPE_BMT_POWERCORE20.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerCore20NetworkManager();
        } else if (DinConst.TYPE_BMT_POWERSTORE.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerStoreNetworkManager();
        } else if (DinConst.TYPE_BMT_POWERPULSE.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerPulseNetworkManager();
        } else if (DinConst.TYPE_BMT_POWERCORE30.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerCore30NetworkManager();
        } else {
            DinSDK.getPluginActivtor().createBmtHP500NetworkManager();
        }
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof BmtHP5000NetworkManager) && !(pluginBinder instanceof BmtHP5001NetworkManager)
                && !(pluginBinder instanceof BmtPowerCore20NetworkManager)
                && !(pluginBinder instanceof BmtPowerCore30NetworkManager)
                && !(pluginBinder instanceof BmtPowerStoreNetworkManager)
                && !(pluginBinder instanceof BmtPowerPulseNetworkManager)) {
            DDLog.e(TAG, "Error BmtNetworkManager binder.");
            showErrorToast();
            return;
        }

        mBinder = (BaseBmtNetworkManager) pluginBinder;
    }

    private void connectDevice(int position) {
        closeScanTimer();
        connectHandler.removeCallbacksAndMessages(null);
        connectHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "connectDevice: " + bleDeviceArrayList.get(position).getName());
                mBinder.connectDevice(bleDeviceArrayList.get(position),
                        PSNetworkBleScanFragment.this);
            }
        }, 500);

//        connectTimer = Observable.interval(APIKey.BLE_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
//                .take(1)
//                .observeOn(AndroidSchedulers.mainThread())
//                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
//                .subscribe(new Subscriber<Object>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//
//                    }
//
//                    @Override
//                    public void onNext(Object o) {
//                        DDLog.d(TAG, "subscribe里");
//                        fail();
//                    }
//                });
    }


    public void toHelp(int type) {
        showOpenDeviceBle(type);
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        bleDeviceArrayList.clear();
        simpleBleList.clear();
    }

    @Override
    public void onPauseFragment() {
        DDLog.d(TAG, "onPauseFragment");
        super.onPauseFragment();

    }

    @Override
    public void onExitFragment() {
        DDLog.d(TAG, "onExitFragment");
        super.onExitFragment();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
//        realStartScan();
    }

    public void toBack() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
//        removeSelf();
    }

    private void setLoadingVisible(boolean visible) {
        mBinding.flLoading.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    private ArrayList<BleDevice> mdata;


    private ArrayList<BleDevice> tmpBleDeviceArrayList;
    private ArrayList<BleDeviceSimpleEntry> tmpSimpleBleList;

    private final BleScanCallback bleScanCallback = new BleScanCallback() {
        @Override
        public void onScanStarted(boolean success) {

            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    DDLog.d(TAG, "开始扫描");
                    setLoadingVisible(true);
                    isScanError = true;
                    tmpBleDeviceArrayList.clear();
                    tmpSimpleBleList.clear();
                    if (bleDeviceArrayList.size() <= 0) {
                        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
                            return;
                        }
//                        startShowTipTimer();
                    }
                }
            });
        }

        @Override
        public void onScanning(BleDevice bleDevice) {
            // 扫描到一个符合扫描规则的BLE设备（主线程）
            if (bleDevice.getName() == null) {
                return;
            }
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    DDLog.d(TAG, "扫描中");

                    if (!getDelegateActivity().isFragmentInTopExcludeLoading(PSNetworkBleScanFragment.this)) {
                        return;
                    }

                    /**
                     * 名字：ydEqumoVNyhPiKfg
                     * 显示：device id 的后四位
                     */
                    String name = bleDevice.getName();
                    String bleId = id;
                    if (!TextUtils.isEmpty(bleId) && bleId.contains("_")) {
                        bleId = bleId.split("_")[0];
                    }
                    Log.d(TAG, "onScanning-->id:" + bleId + " /name:" + name);
                    if (bleId.equalsIgnoreCase(name)) {
                        bleDeviceArrayList.add(bleDevice);
                        connectDevice(0);
                    }

                    /**
                     * 如果扫描到，就取消扫描不到出现打开主机蓝牙提示的定时器
                     */
                    closeShowTipTimer();

                }
            });
        }

        @Override
        public void onScanFinished(List<BleDevice> scanResultList) {
            if (!getDelegateActivity().isFragmentInTopExcludeLoading(PSNetworkBleScanFragment.this)) {
                return;
            }

            getDelegateActivity().runOnUiThread(
                    new Runnable() {
                        @Override
                        public void run() {
// 扫描结束，列出所有扫描到的符合扫描规则的BLE设备（主线程）
                            DDLog.d(TAG, "扫描结束");
                            if (CollectionUtil.isListEmpty(bleDeviceArrayList) && isScanError) {
                                setLoadingVisible(false);
                                toHelp(BleCheckBluetoothDialog.BLE_DIALOG_CHANGE_NETWORK_CHECK_BMT_DEVICE);
                            }
                            isScanError = false;
                            isScan = false;
//
//                            DDLog.d(TAG, "tmpBleDeviceArrayList size is " + tmpBleDeviceArrayList.size());
//                            DDLog.d(TAG, "simpleBleList size is " + simpleBleList.size());
//                            bleDeviceArrayList.clear();
//                            bleDeviceArrayList.addAll(tmpBleDeviceArrayList);
//                            simpleBleList.clear();
//                            simpleBleList.addAll(tmpSimpleBleList);
//                            bleScanDeviceAdapter.notifyDataSetChanged();

                        }
                    });
        }
    };


    BleCheckBluetoothDialog dialog = null;

    /**
     * 如果弹窗选择了退出当前页，那么久不再在onWindowFacous做判断。是否弹出弹窗
     */
    private boolean isQuit = false;

    public void showOpenPhoneBle() {
        if (dialog != null && dialog.isShowing()) {
            return;
        } else {
            isQuit = false;
            dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_PHONE);
            dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    if (null != mBinder) {
                        dialog.dismiss();
                        realStartScan();
                    } else {
                        dialog.dismiss();
                    }
                }

                @Override
                public void clickCanal() {
                    isQuit = true;
                    removeSelf();
                    dialog.dismiss();
                }
            });
            dialog.show();
        }
    }

    private void stopScan() {
        try {
            if (isScan) {
                BleManager.getInstance().cancelScan();
                isScan = false;
            }

        } catch (Exception e) {

        }
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void onEventMainThread(WindowFocusChangedEvent ev) {
//        if (!BleManager.getInstance().isBlueEnable() && !isQuit) {
//            showOpenPhoneBle();
//        }
//    }

    public void showOpenDeviceBle(int type) {
        /**
         * 打开弹窗，提示检查主机蓝牙
         */
        if (dialog != null && dialog.isShowing()) {
            DDLog.d(TAG, "dialog != null && dialog.isShowing()");
            return;
        } else {
            DDLog.d(TAG, "dialog == null ||  dialog.isNotShowing()");
            dialog = new BleCheckBluetoothDialog(getDelegateActivity(), type);
            dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    dialog.dismiss();
                    if (null != mBinder) {
                        if (BleManager.getInstance().isBlueEnable()) {
                            realStartScan();
                        }
                    }
                }

                @Override
                public void clickCanal() {
                    dialog.dismiss();
                    removeSelf();
                }
            });
            dialog.show();
        }

    }

    /**
     * 开启扫描动画和真正开始扫描
     */
    private void realStartScan() {
        DDLog.i(TAG, "realStartScan");
        if (null != mBinder && !BleManager.getInstance().isBlueEnable()) {
            showOpenPhoneBle();
            return;
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                showNeedLocationPermissionDialog();
                return;
            }
            if (!DDSystemUtil.isOpenGPS(getContext())) {
                toOpenGPS(0);
                return;
            }
        } else {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), null);
                return;
            }
        }

        startScanTimer();
    }

    private void requestLocationPermission() {
        AndPermission.with(this)
                .runtime()
                .permission(Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.ACCESS_COARSE_LOCATION,
                        Manifest.permission.ACCESS_LOCATION_EXTRA_COMMANDS)
                .onGranted(new Action<List<String>>() {
                    @Override
                    public void onAction(List<String> permissions) {
                        if (null != dialog && dialog.isShowing()) {
                            dialog.dismiss();
                        }
//                        realStartScan();
                    }
                })
                .onDenied(new Action<List<String>>() {
                    @Override
                    public void onAction(List<String> permissions) {
                        DDLog.e(TAG, "Location permission deny!!!");
                        if (AndPermission.hasAlwaysDeniedPermission(PSNetworkBleScanFragment.this, permissions)) {
                            openSystemSetting();
                        }
                    }
                })
                .start();
    }

    /**
     * 提示需要申请定位权限
     */
    public void showNeedLocationPermissionDialog() {
        if (dialog != null && dialog.isShowing()) {
            DDLog.d(TAG, "dialog != null && dialog.isShowing()");
            return;
        }

        dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_LOCATION_PERMISSION_MODIFY_NETWORK);
        dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
            @Override
            public void clickOk() {

                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                    if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
                            || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                        getMainActivity().setNotNeedToLogin(true);
                        requestLocationPermission();
                        return;
                    }
                    if (!DDSystemUtil.isOpenGPS(getContext())) {
                        toOpenGPS(0);
                        return;
                    }
                } else {
                    if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                            || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), null);
                        return;
                    }
                }

                dialog.dismiss();
//                realStartScan();

            }

            @Override
            public void clickCanal() {
                isQuit = true;
                dialog.dismiss();
                removeSelf();
            }
        });
        dialog.show();
    }

    /**
     * 开启系统设置页
     */
    protected void openSystemSetting() {
        DDLog.i(TAG, "openSystemSetting");
        try {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setData(Uri.parse("package:" + getContext().getPackageName()));
            getMainActivity().setNotNeedToLogin(true);
            startActivity(intent);
        } catch (Exception e) {
            DDLog.e(TAG, "Can't open system setting!!!");
            e.printStackTrace();
            showErrorToast();
        }
    }

    private Subscription scanTimer;

    /**
     * 启动定时器
     */
    public void startScanTimer() {
//        //扫描超时时间+1

        closeScanTimer();
//        scanTimer = Observable.interval(0, APIKey.BLE_SCAN_TIMEOUT + 500, TimeUnit.MILLISECONDS)
//                .subscribe(new Subscriber<Object>() {
//                    @Override
//                    public void onCompleted() {
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//
//                    }
//
//                    @Override
//                    public void onNext(Object o) {
//                        DDLog.d(TAG, "onNext");
//                        mBinder.discoveryDevice(APIKey.BLE_SCAN_TIMEOUT, bleScanCallback);
//                    }
//                });
        mBinder.discoveryDevice(30000, bleScanCallback);

    }

    public void closeScanTimer() {
        if (scanTimer != null && !scanTimer.isUnsubscribed()) {
            scanTimer.unsubscribe();
        }
        try {
            mBinder.stopDiscoveryDevice();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleStartScanEvent ev) {
//        startTimer();
//        toStartAnim();
        DDLog.d(TAG, "BleStartScanEvent");
    }

    /**
     * 失败处理
     */
    private void fail() {
        DDLog.d(TAG, "onFail");
        simpleBleList.clear();
        bleDeviceArrayList.clear();
        showOpenDeviceBle(BleCheckBluetoothDialog.BLE_DIALOG_CHANGE_NETWORK_CHECK_BMT_DEVICE);
        mBinder.stop();
        if (connectTimer != null && !connectTimer.isUnsubscribed()) {
            connectTimer.unsubscribe();
        }
        // realStartScan();
    }

    /**
     * 清空处理：定时器、loading
     */
    private void clean() {
        DDLog.d(TAG, "clean");
        closeScanTimer();
        if (connectTimer != null && !connectTimer.isUnsubscribed()) {
            connectTimer.unsubscribe();
        }
        closeShowTipTimer();
    }


    /**
     * 关闭定时器，用于其他弹窗出现的时候————ble断开的时候
     *
     * @param ev
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleCloseTimerEvent ev) {
        clean();
    }

    private Subscription showTipTimer;

    /**
     * 在没有数据的情况下，每隔三分钟弹出一次help框
     */
    private void startShowTipTimer() {
        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
            showTipTimer.unsubscribe();
        }

        showTipTimer = Observable.interval(APIKey.BLE_SCAN_NO_DEVICE_TIP_TIMEOUT, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        toHelp(BleCheckBluetoothDialog.BLE_DIALOG_CHANGE_NETWORK_CHECK_BMT_DEVICE);
                    }
                });

    }

    private void closeShowTipTimer() {
        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
            showTipTimer.unsubscribe();
        }
    }

    @Override
    public boolean onBackPressed() {
        toBack();
        return true;

    }

    @Override
    public void onStartConnect() {

    }

    @Override
    public void onConnectFail(String s) {
        setLoadingVisible(false);
        fail();
    }

    @Override
    public void onConnectSuccess() {
        setLoadingVisible(false);
        DDLog.i(TAG, "onConnectSuccess");
        clean();
        getDelegateActivity().addCommonFragment(PSNetworkSettingsFragment.newInstanceForChangeNetwork(id, provider, true));
    }

    @Override
    public void onDisConnected() {
        DDLog.i(TAG, "onDisConnected");
        showOpenDeviceBle(BleCheckBluetoothDialog.BLE_DIALOG_CHANGE_NETWORK_CHECK_BMT_DEVICE);
    }

    private static class ConnectHandler extends Handler {

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(FinishAddBmtEvent event) {
        removeSelf();
    }
}
