package com.dinsafer.module.powerstation.adapter;

import android.view.View;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ScheduledChargeFgBean;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.LocalTextView;

import java.util.ArrayList;
import java.util.List;

public class ScheduledChargeFgAdapter extends BaseQuickAdapter<ScheduledChargeFgBean, BaseViewHolder> {

    private int mItemHeight;

    public ScheduledChargeFgAdapter(int itemHeight) {
        super(R.layout.item_scheduled_charge_fg);
        mItemHeight = itemHeight;
    }

    @Override
    protected void convert(BaseViewHolder helper, ScheduledChargeFgBean item) {
        int position = helper.getAdapterPosition();
        ConstraintLayout clParent = helper.getView(R.id.cl_parent);
        RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) clParent.getLayoutParams();
        layoutParams.height = mItemHeight;
        layoutParams.rightMargin = item.getType() == 0 ? DensityUtil.dp2px(mContext, 1) : 0;
        layoutParams.leftMargin = item.getType() == 1 ? DensityUtil.dp2px(mContext, 1) : 0;
        clParent.setLayoutParams(layoutParams);
        View viewData = helper.getView(R.id.view_data);
        LocalTextView tvTime = helper.getView(R.id.tv_time);
        int hour = item.getHour();
        String startTime = (hour - 1) < 10 ? ("0" + (hour - 1) + ":00") : ((hour - 1) + ":00");
        String endTime = hour < 10 ? ("0" + hour + ":00") : (hour + ":00");
        if (item.isApplied() && !item.isSelected()) {
            for (int i = position + 2; i < getData().size(); i = i + 2) {
                ScheduledChargeFgBean nextChargeBean = getData().get(i);
                if (nextChargeBean.isApplied()) {
                    endTime = nextChargeBean.getHour() < 10 ? ("0" + nextChargeBean.getHour() + ":00") : (nextChargeBean.getHour() + ":00");
                } else {
                    break;
                }
            }
            tvTime.setLocalText(startTime + "-" + endTime);
            if (position == 0 || position == 1) {
                tvTime.setVisibility(View.VISIBLE);
            } else {
                ScheduledChargeFgBean lastChargeBean = getData().get(position - 2);
                tvTime.setVisibility(lastChargeBean.isApplied() ? View.GONE : View.VISIBLE);
            }
        } else {
            tvTime.setVisibility(View.GONE);
        }
        if (item.isSelected()) {
            viewData.setBackgroundResource(R.color.color_brand_light_03);
        } else if (item.isApplied()) {
            viewData.setBackgroundResource(R.color.color_brand_light_02);
        } else {
            viewData.setBackgroundResource(0);
        }
    }

    /**
     * 所有应用数据
     * @return
     */
    public List<ScheduledChargeFgBean> getAppliedData() {
        List<ScheduledChargeFgBean> appliedData = new ArrayList<>();
        for (ScheduledChargeFgBean scheduledChargeFgBean : getData()) {
            if (scheduledChargeFgBean.isApplied()) {
                appliedData.add(scheduledChargeFgBean);
            }
        }
        return appliedData;
    }

    /**
     * 所有weekdays应用数据
     * @return
     */
    public List<ScheduledChargeFgBean> getWeekdaysAppliedData() {
        List<ScheduledChargeFgBean> appliedData = new ArrayList<>();
        for (int i = 0; i < getData().size(); i++) {
            if (i % 2 == 0) {
                ScheduledChargeFgBean scheduledChargeFgBean = getData().get(i);
                if (scheduledChargeFgBean.isApplied()) {
                    appliedData.add(scheduledChargeFgBean);
                }
            }
        }
        return appliedData;
    }

    /**
     * 所有weekdays(用 1 和 0 标识)
     * @return
     */
    public ArrayList<Integer> getWeekdaysData() {
        ArrayList<Integer> weekdaysData = new ArrayList<>();
        for (int i = 0; i < getData().size(); i++) {
            if (i % 2 == 0) {
                ScheduledChargeFgBean scheduledChargeFgBean = getData().get(i);
                weekdaysData.add(scheduledChargeFgBean.isApplied() ? 1 : 0);
            }
        }
        return weekdaysData;
    }

    /**
     * 所有weekends应用数据
     * @return
     */
    public List<ScheduledChargeFgBean> getWeekendsAppliedData() {
        List<ScheduledChargeFgBean> appliedData = new ArrayList<>();
        for (int i = 0; i < getData().size(); i++) {
            if (i % 2 == 1) {
                ScheduledChargeFgBean scheduledChargeFgBean = getData().get(i);
                if (scheduledChargeFgBean.isApplied()) {
                    appliedData.add(scheduledChargeFgBean);
                }
            }
        }
        return appliedData;
    }

    /**
     * 所有weekends(用 1 和 0 标识)
     * @return
     */
    public ArrayList<Integer> getWeekendsData() {
        ArrayList<Integer> weekendsData = new ArrayList<>();
        for (int i = 0; i < getData().size(); i++) {
            if (i % 2 == 1) {
                ScheduledChargeFgBean scheduledChargeFgBean = getData().get(i);
                weekendsData.add(scheduledChargeFgBean.isApplied() ? 1 : 0);
            }
        }
        return weekendsData;
    }

}
