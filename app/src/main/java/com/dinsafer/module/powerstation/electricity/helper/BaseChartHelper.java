package com.dinsafer.module.powerstation.electricity.helper;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.FillFormatter;

import java.util.ArrayList;

public class BaseChartHelper {

    protected final Context mContext;
    protected FillFormatter fillFormatter = (dataSet, dataProvider) -> 0;

    public BaseChartHelper(Context context) {
        this.mContext = context;
    }

    protected SectionLineDataSet getSectionLineDataSet(ArrayList<Entry> entries, int color,
                                                       YAxis.AxisDependency dependency,
                                                       int[] gradientColor, float[] pos,
                                                       boolean isSection, boolean isFilled) {
        SectionLineDataSet lineDataSet = new SectionLineDataSet(entries, "");
        lineDataSet.setLineWidth(0.5f);
        lineDataSet.setDrawCircles(false);
        lineDataSet.setCircleRadius(5f);
        lineDataSet.setDrawCubic(false);
        lineDataSet.setDrawValues(false);
        lineDataSet.setValueTextSize(10f);
        lineDataSet.setValueTextColor(Color.rgb(240, 238, 70));
        lineDataSet.setFillFormatter(fillFormatter);
        lineDataSet.setDrawHorizontalHighlightIndicator(false);
        lineDataSet.setHighLightColor(mContext.getResources().getColor(R.color.color_white_03));
        lineDataSet.setHighlightLineWidth(1f);
        lineDataSet.setDrawCircleHole(false);
        lineDataSet.setFillAlpha(60);
        if (!isSection) {
            lineDataSet.setColor(color);
            lineDataSet.setFillColor(color);
        }
        lineDataSet.setAxisDependency(dependency);
        lineDataSet.setMode(LineDataSet.Mode.CUBIC_BEZIER);
        if (isSection) {
            lineDataSet.setGradientColors(gradientColor);
            lineDataSet.setGradientPosition(pos);
        }
        lineDataSet.setSection(isSection);
        lineDataSet.setDrawFilled(isFilled);
        return lineDataSet;
    }

    protected SectionLineDataSet getSectionLineDataSet(ArrayList<Entry> entries, int color, Drawable drawable,
                                                       YAxis.AxisDependency dependency,
                                                       boolean isSection, boolean isFilled) {
        SectionLineDataSet lineDataSet = new SectionLineDataSet(entries, "Label");
        lineDataSet.setLineWidth(0.5f);
        lineDataSet.setDrawCircles(false);
        lineDataSet.setCircleRadius(5f);
        lineDataSet.setDrawCubic(false);
        lineDataSet.setDrawValues(false);
        lineDataSet.setValueTextSize(10f);
        lineDataSet.setValueTextColor(Color.rgb(240, 238, 70));
        lineDataSet.setFillFormatter(fillFormatter);
        lineDataSet.setDrawHorizontalHighlightIndicator(false);
        lineDataSet.setHighLightColor(mContext.getResources().getColor(R.color.color_white_03));
        lineDataSet.setHighlightLineWidth(1f);
        lineDataSet.setDrawCircleHole(false);
        lineDataSet.setFillAlpha(255);
        if (!isSection) {
            lineDataSet.setColor(color);
            lineDataSet.setFillDrawable(drawable);
        }
        lineDataSet.setAxisDependency(dependency);
        lineDataSet.setMode(LineDataSet.Mode.LINEAR);
        lineDataSet.setSection(isSection);
        lineDataSet.setDrawFilled(isFilled);
        return lineDataSet;
    }

    protected int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }
}
