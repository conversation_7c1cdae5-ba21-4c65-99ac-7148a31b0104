package com.dinsafer.module.powerstation.electricity;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentElectricityRelativePriceBinding;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.electricity.bean.CustomBarDataSet;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.marker.RelativePriceMarkerView;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.RelativePriceChartModelController;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.module.powerstation.event.StatInfoEvent;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.ScreenUtils;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class ElectricityRelativePriceFragment extends BaseChartFragment<RelativePriceChartModelController,
        FragmentElectricityRelativePriceBinding> {

    private String mNullVal = "-";
    private CustomCombinedMarkerView mMarkerView;
    private int mHighLightIndex = 12;
    private boolean isFirst = true;

    private XAxisValueFormatter mPriceFormatter = (original, index, viewPortHandler) -> {

        if (mHourCount == DST_TIME_MINUTE) {
            if (index == 0) {
                return "00:00";
            } else if (index == 24) {
                return "24:00";
            }
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            if (index == 0) {
                return "00:00";
            } else if (index == 22) {
                return "24:00";
            }
        } else {
            if (index == 0) {
                return "00:00";
            } else if (index == 12) {
                return "12:00";
            } else if (index == 23) {
                return "24:00";
            }
        }
        return "";
    };

    private YAxisValueFormatter yFormatter = (value, yAxis) -> {
        float valueAsb = Math.abs(value);
        if (valueAsb == yAxis.getAxisMaximum()) {
            return (int) value + "%";
        }
        if (value == 0) {
            return "0%";
        }
        return "";
    };

    public static ElectricityRelativePriceFragment newInstance(int fromIndex, String deviceId) {
        ElectricityRelativePriceFragment fragment = new ElectricityRelativePriceFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.INDEX, fromIndex);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_electricity_relative_price;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mMarkerView = new RelativePriceMarkerView(getContext());
        String relativePriceUnit = "%";
        mMarkerView.setHighLightListener(new CustomCombinedMarkerView.HighLightListener() {
            @Override
            public void highLight(List<Entry> entries, List<Highlight> highlights) {
                if (CollectionUtil.isListNotEmpty(entries)) {
                    BarEntry barEntry = (BarEntry) entries.get(entries.size() - 1);
                    float[] values = barEntry.getVals();
                    float value1 = values[0];
                    String val1Str = ChartDataUtil.savePointStr(value1, 0, 2);
                    mBinding.esvVal.setLeftVal(val1Str, relativePriceUnit);
                    mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.price)) + " " + Local.s(getString(R.string.ps_peak)),
                            ChartDataUtil.savePointStr(Math.abs(getRelativePriceMax()), 0, 2),
                            relativePriceUnit);
                    mBinding.esvVal.setRightVal(Local.s(getString(R.string.price)) + " " + Local.s(getString(R.string.trough)),
                            ChartDataUtil.savePointStr(Math.abs(getRelativePriceMin()), 0, 2),
                            relativePriceUnit);
                } else {
                    setDefaultVal();
                }
                mBinding.esvVal.refreshText();
            }
        });
    }

    @Override
    protected void initChartView() {
        super.initChartView();
        initChart(mFlipCombinedChartView, mMarkerView);
        setIvRightEnabled(mOffSet != 0);
        initRefreshLayout(mBinding.refreshLayout);
        mFlipCombinedChartView.setFlipChangeListener(new FlipCombinedChartView.OnFlipChangeListener() {
            @Override
            public void flipChange(int index, OperateOrientation orientation) {
                mIndex = index;
                if (orientation == OperateOrientation.LEFT) {
                    mOffSet = mOffSet - 1;
                } else if (orientation == OperateOrientation.RIGHT) {
                    mOffSet = mOffSet + 1;
                }
                mHighLightIndex = 12;
                setIvRightEnabled(mOffSet != 0);
                getStatisticData(true);
            }
        });
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            if (mCycleType != CycleType.LIFETIME) {
                if (isChartViewNotNull()) {
                    setIvRightEnabled(mOffSet != 0);
                }
            }
            if (mFromIndex == BaseChartFragment.CHART_ELECTRICITY_PRICE) {
                getStatisticData(!isFirst);
                isFirst = false;
            } else {
                getStatisticData(true);
            }
        }
    }

    @Override
    protected void setChartData(int index, Map<String, Object> result) {
        checkChartViewNull();
        if (refreshLayout != null)
            refreshLayout.finishRefresh();
        isSuccess = true;
        timezone = (String) MapUtils.get(result, BmtDataKey.TIMEZONE, "");
        long startTime = (long) MapUtils.get(result, BmtDataKey.START_TIME, 0);
        Boolean bSensorInstalled = (Boolean) MapUtils.get(result, BmtDataKey.B_SENSOR_INSTALLED, null);
        if (bSensorInstalled != null) {
            isBSensorInstalled = bSensorInstalled;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(startTime * 1000);
        if (DDDateUtil.isDSTTransitionDay(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1500;
        } else if (DDDateUtil.isWT2ST(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1380;
        } else {
            mHourCount = 1440;
        }
        mMarkerView.setHourCount(mHourCount);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        mStartIndex = hour * 60 + minute;

        mResult = result;
        mInterval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);

        if (mResult != null) {
            mStartTime = DeviceHelper.getLong(mResult, BmtDataKey.START_TIME, 0) * 1000;
            timezone = DeviceHelper.getString(mResult, BmtDataKey.TIMEZONE, "");
        }

        mChartData.clear();
        mChartData.addAll((List<List<Float>>) result.get(BmtDataKey.DATA));
        chartModelController.initXAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, 0, mPriceFormatter);
        chartModelController.initYAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, yFormatter, false);
        float maxVal = getRelativePriceYMax();
        resetYAxisLeft(maxVal, index);

        mFlipCombinedChartView.resetHighValues();
        resetChart();
    }

    @Override
    protected void setFailChartData(int index) {
        checkChartViewNull();
        if (refreshLayout != null)
            refreshLayout.finishRefresh();
        timezone = "";
        mHourCount = 1440;
        mInterval = 1;

        mChartData.clear();
        List<List<Float>> data = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            List<Float> sonData = new ArrayList<>();
            sonData.add(0f);
            sonData.add(0f);
            sonData.add(0f);
            data.add(sonData);
        }
        mChartData.addAll(data);
        chartModelController.initXAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, 0, mPriceFormatter);
        chartModelController.initYAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, yFormatter, false);
        resetYAxisLeft(100, index);
        mFlipCombinedChartView.resetHighValues();
        resetFailChart();
    }

    @Override
    protected void resetYAxisLeft(float maxVal, int index) {
        if (CollectionUtil.isListNotEmpty(chartManagers)) {
            for (CustomCombinedChartManager chartManager : chartManagers) {
                chartManager.setYAxisMaxMin(maxVal, -maxVal, 5);
                chartManager.showDrawLeftYAxisLabel(true);
                chartManager.setDrawLefAxisYDesc(true);
                chartManager.setLefAxisYDesc(Local.s(getString(R.string.relative_price_multiLine)));
            }
        }
    }

    @Override
    public void createChartModelController() {
        chartModelController = new RelativePriceChartModelController();
    }

    @Override
    protected void getStatisticData(boolean showLoading) {
        if (ElectricityStatisticsFragment.mPSDevice != null) {
            if (showLoading) {
                showTimeOutLoadinFramgmentWithMarginTop(marginTop);
            }
            Map<String, Object> result = mDayCache.get(mOffSet);
            if (result == null) {
                Map<String, Object> params = new HashMap<>();
                params.put(PSKeyConstant.CMD, BmtCmd.GET_ELEC_PRICE_INFO);
                params.put(BmtDataKey.OFFSET, mOffSet);
                ElectricityStatisticsFragment.isPriceReq = true;
                ElectricityStatisticsFragment.mPSDevice.submit(params);
            } else {
                EventBus.getDefault().post(new StatInfoEvent(result));
                setChartData(BaseChartFragment.CHART_ELECTRICITY_PRICE, result);
                closeLoadingFragment();
            }
        }
    }

    @Override
    protected void setDayChart() {
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
            List<Float> sonData = mChartData.get(i);
            if (sonData.size() > 1) {
                yVals.add(new BarEntry(new float[]{sonData.get(1)}, i));
            }

        }
        CombinedData data = new CombinedData(xVals);
        CustomBarDataSet barDataSet = new CustomBarDataSet(yVals, "");
        int colorsBar[] = new int[]{getColor(R.color.color_tip_warning)};
        int negativeColorsBar[] = new int[]{getColor(R.color.color_brand_primary)};
        barDataSet.setColors(colorsBar);
        barDataSet.setNegativeColors(negativeColorsBar);
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(true);
        barDataSet.setHighLightColor(getColor(R.color.color_white_03));
        barDataSet.setBarSpacePercent((ScreenUtils.getScreenWidth(getContext()) - DensityUtil.dp2px(getContext(), 65)) * 1f / count * 1.2f);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
        mFlipCombinedChartView.setDefaultHighLight(mIndex, mHighLightIndex);
    }

    @Override
    protected void setWeekChart() {

    }

    @Override
    protected void setMonthChart() {

    }

    @Override
    protected void setYearChart() {

    }

    @Override
    protected void setLifetimeChart() {

    }

    @Override
    protected void setFailDayChart() {
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
            List<Float> sonData = mChartData.get(i);
            if (sonData.size() > 1) {
                yVals.add(new BarEntry(new float[]{0}, i));
            }
        }
        CombinedData data = new CombinedData(xVals);
        BarDataSet barDataSet = new BarDataSet(yVals, "");
        int colorsBar[] = new int[]{getColor(R.color.transparent)};
        barDataSet.setColors(colorsBar);
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(true);
        barDataSet.setHighLightColor(getColor(R.color.color_white_03));
        barDataSet.setBarSpacePercent((ScreenUtils.getScreenWidth(getContext()) - DensityUtil.dp2px(getContext(), 65)) * 1f / count * 1.2f);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
        setDefaultVal();
        mBinding.esvVal.refreshText();
    }

    @Override
    protected void setFailWeekChart() {

    }

    @Override
    protected void setFailMonthChart() {

    }

    @Override
    protected void setFailYearChart() {

    }

    @Override
    protected void setFailLifetimeChart() {

    }

    private int getRelativePriceYMax() {
        if (CollectionUtil.isListEmpty(mChartData)) return 100;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < mChartData.size(); i++) {
            List<Float> sonData = mChartData.get(i);
            sumData.add(Math.abs(ChartDataUtil.saveFloatPoint(sonData.get(1), 2)));
        }
        Collections.sort(sumData);
        float maxVal = sumData.get(sumData.size() - 1);
        return ChartDataUtil.findUpperRelativePrice(maxVal);
    }

    private float getRelativePriceMin() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < mChartData.size(); i++) {
            List<Float> sonData = mChartData.get(i);
            sumData.add(ChartDataUtil.saveFloatPoint(sonData.get(1), 2));
        }
        Collections.sort(sumData);
        float minVal = sumData.get(0);
        return minVal;
    }

    private float getRelativePriceMax() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < mChartData.size(); i++) {
            List<Float> sonData = mChartData.get(i);
            sumData.add(ChartDataUtil.saveFloatPoint(sonData.get(1), 2));
        }
        Collections.sort(sumData);
        float maxVal = sumData.get(sumData.size() - 1);
        return maxVal;
    }

    private void setDefaultVal() {
        String relativePriceUnit = "%";
        mBinding.esvVal.setLeftVal(mNullVal, relativePriceUnit);
        mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.price)) + " " + Local.s(getString(R.string.ps_peak)),
                mNullVal, relativePriceUnit);
        mBinding.esvVal.setRightVal(Local.s(getString(R.string.price)) + " " + Local.s(getString(R.string.trough)),
                mNullVal, relativePriceUnit);
    }
}
