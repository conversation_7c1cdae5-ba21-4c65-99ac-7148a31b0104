package com.dinsafer.module.powerstation.event;

/**
 * 通知显示Bmt引导对话框
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/6/14 2:39 下午
 */
public class ShowBmtGuideTipEvent {
    private final String deviceId;
    private final String subcategory;

    public ShowBmtGuideTipEvent(String deviceId, String subcategory) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    @Override
    public String toString() {
        return "ShowBmtGuideTipEvent{" +
                "deviceId='" + deviceId + '\'' +
                ", subcategory='" + subcategory + '\'' +
                '}';
    }
}
