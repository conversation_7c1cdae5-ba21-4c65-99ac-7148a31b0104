package com.dinsafer.module.powerstation.electricity.manager;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ChartNoteTextAttrBean;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.chart.CustomCombinedChart;
import com.dinsafer.module.powerstation.electricity.chart.SectionLineChart;
import com.dinsafer.module.powerstation.electricity.chart.marker.UsageDayMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomBarChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomCombinedChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.SectionLineChartRender;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.components.MarkerView;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.BubbleData;
import com.github.mikephil.charting.data.BubbleDataSet;
import com.github.mikephil.charting.data.BubbleEntry;
import com.github.mikephil.charting.data.CandleData;
import com.github.mikephil.charting.data.CandleDataSet;
import com.github.mikephil.charting.data.CandleEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.data.ScatterData;
import com.github.mikephil.charting.data.ScatterDataSet;
import com.github.mikephil.charting.formatter.FillFormatter;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.interfaces.dataprovider.LineDataProvider;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.renderer.DataRenderer;
import com.github.mikephil.charting.utils.ColorTemplate;

import java.util.ArrayList;
import java.util.List;

public class CustomCombinedChartManager {

    private Context mContext;
    private CustomCombinedChart mCombinedChart;

    public CustomCombinedChartManager(Context context, CustomCombinedChart combinedChart) {
        this.mContext = context;
        this.mCombinedChart = combinedChart;
    }

    public void initChart(boolean isDrawBorders, float borderWidth, int borderColor,
                          boolean isHighlightPerDragEnabled, boolean isHighlightFullBarEnabled,
                          boolean isScaleEnabled, float leftOffsets, float topOffsets,
                          float rightOffsets, float bottomOffsets, float minOffset, int backgroundColor,
                          boolean isLegendEnabled, String description, int animateXDurationMillis,
                          boolean isDrawDefaultXGridLines) {
        if (mCombinedChart == null) return;
        mCombinedChart.setDrawBorders(isDrawBorders);
        mCombinedChart.setBorderWidth(borderWidth);
        mCombinedChart.setBorderColor(borderColor);
        mCombinedChart.setHighlightPerDragEnabled(isHighlightPerDragEnabled);
        mCombinedChart.setHighlightFullBarEnabled(isHighlightFullBarEnabled);
        mCombinedChart.setScaleEnabled(isScaleEnabled);
        mCombinedChart.setDrawDefaultXGridLines(isDrawDefaultXGridLines);
        mCombinedChart.setNeedNoDataGrid(false);
        //设置图表距离上下左右的距离
        mCombinedChart.setExtraOffsets(leftOffsets, topOffsets, rightOffsets, bottomOffsets);
        mCombinedChart.setMinOffset(minOffset);
        if (backgroundColor != 0)
            mCombinedChart.setBackgroundColor(backgroundColor);
        //图例
        mCombinedChart.getLegend().setEnabled(isLegendEnabled);
        mCombinedChart.setDescription(description);
        mCombinedChart.animateX(animateXDurationMillis);

    }

    public void setExtrasTopOffset(int topOffset) {
        mCombinedChart.setExtraTopOffset(topOffset);
    }

    public void initXAxis(boolean isDrawXGridLines, boolean isDrawXAxisLine, int xGridColor,
                          float xLabelTextSize, boolean isAvoidFirstLastClipping, int xLabelTextColor,
                          float xAxisLineWidth, XAxis.XAxisPosition xAxisPosition, int labelsToSkip,
                          XAxisValueFormatter xFormatter) {
        if (mCombinedChart == null) return;
        //获取X轴
        XAxis xAxis = mCombinedChart.getXAxis();
        //将垂直于X轴的网格线隐藏，将X轴显示
        xAxis.setDrawGridLines(isDrawXGridLines);
        xAxis.setDrawAxisLine(isDrawXAxisLine);
        xAxis.setGridColor(xGridColor);
        //设置X轴上label颜色和大小
        xAxis.setTextSize(xLabelTextSize);
        xAxis.setAvoidFirstLastClipping(true);
        xAxis.setTextColor(xLabelTextColor);
        //设置X轴高度
        xAxis.setAxisLineWidth(xAxisLineWidth);
        //x轴刻度值的位置
        xAxis.setPosition(xAxisPosition);
//        设置在”绘制下一个标签”时，要忽略的标签数。
        xAxis.setLabelsToSkip(labelsToSkip);
        xAxis.setValueFormatter(xFormatter);
        mCombinedChart.animateX(1000);
    }

    public void initYAxis(boolean isDrawYAxisLabel, float yLabelTextSize, int yLabelTextColor,
                          float axisLineWidth, boolean isDrawAxisLine, boolean isDrawYGridLines,
                          int yGridColor, YAxisValueFormatter yFormatter,
                          boolean isAxisRightEnabled, int labelCount) {
        if (mCombinedChart == null) return;
        //获取左侧侧坐标轴
        YAxis yAxis = mCombinedChart.getAxisLeft();
        //设置是否显示Y轴的值
        yAxis.setDrawLabels(isDrawYAxisLabel);
        yAxis.setTextSize(yLabelTextSize);
        yAxis.setTextColor(yLabelTextColor);
        yAxis.setAxisLineWidth(axisLineWidth);
        //是否绘制坐标轴
        yAxis.setDrawAxisLine(isDrawAxisLine);
        //设置所有垂直Y轴的的网格线是否显示
        yAxis.setDrawGridLines(isDrawYGridLines);
        yAxis.setGridColor(yGridColor);

        yAxis.setValueFormatter(yFormatter);
        yAxis.setLabelCount(labelCount, true);
        mCombinedChart.getAxisRight().setEnabled(isAxisRightEnabled);
    }

    public void setYAxisMaxMin(float yAxisMaxValue, float yAxisMinValue, int labelCount) {
        if (mCombinedChart == null) return;
        //获取左侧侧坐标轴
        YAxis yAxis = mCombinedChart.getAxisLeft();
        yAxis.setAxisMaxValue(yAxisMaxValue);
        yAxis.setAxisMinValue(yAxisMinValue);
        yAxis.setLabelCount(labelCount, true);
    }

    public void showDrawLeftYAxisLabel(boolean isDrawYAxisLabel) {
        if (mCombinedChart == null) return;
        //获取左侧侧坐标轴
        YAxis yAxis = mCombinedChart.getAxisLeft();
        yAxis.setDrawLabels(isDrawYAxisLabel);
    }

    public void initYAxisRight(boolean isDrawYAxisLabel, boolean isDrawAxisLine, boolean isDrawYGridLines, int yLabelTextColor) {
        if (mCombinedChart == null) return;
        //获取左侧侧坐标轴
        YAxis yAxis = mCombinedChart.getAxisRight();
        //设置是否显示Y轴的值
        yAxis.setDrawLabels(isDrawYAxisLabel);
        //是否绘制坐标轴
        yAxis.setDrawAxisLine(isDrawAxisLine);
        //设置所有垂直Y轴的的网格线是否显示
        yAxis.setDrawGridLines(isDrawYGridLines);
        yAxis.setTextColor(yLabelTextColor);
    }

    public void initYAxisRight(boolean isDrawYAxisLabel, float yLabelTextSize, int yLabelTextColor,
                               float axisLineWidth, boolean isDrawAxisLine, boolean isDrawYGridLines,
                               int yGridColor, YAxisValueFormatter yFormatter, int labelCount) {
        if (mCombinedChart == null) return;
        //获取左侧侧坐标轴
        YAxis yAxis = mCombinedChart.getAxisRight();
        //设置是否显示Y轴的值
        yAxis.setDrawLabels(isDrawYAxisLabel);
        yAxis.setTextSize(yLabelTextSize);
        yAxis.setTextColor(yLabelTextColor);
        yAxis.setAxisLineWidth(axisLineWidth);
        //是否绘制坐标轴
        yAxis.setDrawAxisLine(isDrawAxisLine);
        //设置所有垂直Y轴的的网格线是否显示
        yAxis.setDrawGridLines(isDrawYGridLines);
        yAxis.setGridColor(yGridColor);

        yAxis.setValueFormatter(yFormatter);
        yAxis.setLabelCount(labelCount, true);
    }

    public void setYAxisRightMaxMin(float yAxisMaxValue, float yAxisMinValue) {
        if (mCombinedChart == null) return;
        //获取左侧侧坐标轴
        YAxis yAxis = mCombinedChart.getAxisRight();
        yAxis.setAxisMaxValue(yAxisMaxValue);
        yAxis.setAxisMinValue(yAxisMinValue);
    }

    public void initLineChartRender(boolean isMultiply) {
        if (mCombinedChart == null) return;
        List<DataRenderer> mRenderers = ((CustomCombinedChartRenderer) mCombinedChart.getRenderer()).getSubRenderers();
        if (CollectionUtil.isListEmpty(mRenderers)) return;
        for (DataRenderer dataRenderer : mRenderers) {
            if (dataRenderer instanceof SectionLineChartRender) {
                SectionLineChartRender renderer = (SectionLineChartRender) dataRenderer;
                renderer.setMultiply(isMultiply);
                renderer.setInCircleColor(mContext.getResources().getColor(R.color.color_white_00));
                renderer.setOutCircleColor(mContext.getResources().getColor(R.color.color_white_03));
                break;
            }
        }
    }

    public void initBarChartRender(int radius, int outCircleColor, float highLightWidth, float highLightBorderWidth) {
        if (mCombinedChart == null) return;
        List<DataRenderer> mRenderers = ((CustomCombinedChartRenderer) mCombinedChart.getRenderer()).getSubRenderers();
        if (CollectionUtil.isListEmpty(mRenderers)) return;
        for (DataRenderer dataRenderer : mRenderers) {
            if (dataRenderer instanceof CustomBarChartRenderer) {
                ((CustomBarChartRenderer) dataRenderer).setRadius(radius);
                ((CustomBarChartRenderer) dataRenderer).setOutCircleColor(outCircleColor);
                ((CustomBarChartRenderer) dataRenderer).setHighlightLineWidth(highLightWidth);
                ((CustomBarChartRenderer) dataRenderer).setHighlightBoarderWidth(highLightBorderWidth);
                break;
            }
        }
    }

    public void setMarkerView(MarkerView markerView) {
        if (mCombinedChart == null) return;
        mCombinedChart.setMarkerView(markerView);
    }

    public void setDrawDefaultXGridLines(boolean isNeed) {
        if (mCombinedChart != null) {
            mCombinedChart.setDrawDefaultXGridLines(isNeed);
        }
    }

    public void setShowAboveChartText(boolean showAboveChartText) {
        mCombinedChart.setShowAboveChartText(showAboveChartText);
    }

    public void setAboveChartTexts(List<ChartNoteTextAttrBean> aboveChartTexts) {
        mCombinedChart.setAboveChartTexts(aboveChartTexts);
    }

    public void setDrawLefAxisYDesc(boolean drawLefAxisYDesc) {
        if (mCombinedChart != null) {
            mCombinedChart.setDrawLefAxisYDesc(drawLefAxisYDesc);
        }
    }

    public void setDrawRightAxisYDesc(boolean drawRightAxisYDesc) {
        if (mCombinedChart != null) {
            mCombinedChart.setDrawRightAxisYDesc(drawRightAxisYDesc);
        }
    }

    public void setLefAxisYDesc(String lefAxisYDesc) {
        if (mCombinedChart != null) {
            mCombinedChart.setLefAxisYDesc(lefAxisYDesc);
        }
    }

    public void setRightAxisYDesc(String rightAxisYDesc) {
        if (mCombinedChart != null) {
            mCombinedChart.setRightAxisYDesc(rightAxisYDesc);
        }
    }
}
