package com.dinsafer.module.powerstation.utils;

import android.content.Context;

import com.dinsafer.dinnet.R;

public class AIColorUtil {

    public static int[] getAIColor(Context context) {
        int[] colors = new int[]{
                context.getResources().getColor(R.color.color_ai_stroke_1),
                context.getResources().getColor(R.color.color_ai_stroke_2),
                context.getResources().getColor(R.color.color_ai_stroke_3),
                context.getResources().getColor(R.color.color_ai_stroke_4)
        };
        return colors;
    }

    public static float[] getAIColorPosition() {
        return new float[]{0f, 0.33f, 0.7f, 1f};
    }
}
