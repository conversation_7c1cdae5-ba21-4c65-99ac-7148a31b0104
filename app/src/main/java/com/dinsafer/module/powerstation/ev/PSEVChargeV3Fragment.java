package com.dinsafer.module.powerstation.ev;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dialog.SeekBarDialog;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsEvChargeV3Binding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.powerstation.LottieManager;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PSEVChargeV3Model;
import com.dinsafer.module.powerstation.bean.PSEVChargeV3Bean;
import com.dinsafer.module.powerstation.dialog.EVEventDialog;
import com.dinsafer.module.powerstation.dialog.EVPopup;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;
import com.dinsafer.util.UnitUtil;
import com.google.android.material.bottomsheet.BottomSheetBehavior;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

public class PSEVChargeV3Fragment extends PSConnectLoadingFragment<FragmentPsEvChargeV3Binding> implements IDeviceCallBack {

    private BottomSheetBehavior mBottomSheetBehavior;
    private LottieManager mLottieManager;

    private BindMultiAdapter<PSEVChargeV3Model> mEVChargeAdapter;
    private List<PSEVChargeV3Model> mSmartChargeData;
    private List<PSEVChargeV3Model> mInstantChargeData;
    private PSEVChargeV3Bean mLowerUtilityRate;
    private PSEVChargeV3Bean mScheduledCharge;
    private PSEVChargeV3Bean mInstantChargeFull;
    private PSEVChargeV3Bean mInstantChargeFixed;
    private List<PSEVChargeV3Bean> mEVChargeModeList = new ArrayList<>();
    private PSEVChargeV3Model mSmartFirstModel;
    private PSEVChargeV3Model mSmartSecondModel;
    private PSEVChargeV3Model mInstantFirstModel;
    private PSEVChargeV3Model mInstantSecondModel;
    private SeekBarDialog mSeekBarDialog;
    private int mEvChargeMode;
    private int mCurrentItem;

    private Subscription mPollTimer;

    private EVPopup evPopup;
    // 默认开启ev平衡
    private int smartEvStatus = 2;
    private boolean shouldRestart;
    private boolean requestRestart;

    private boolean isShowEvTip;


    public static PSEVChargeV3Fragment newInstance(String deviceId, String subcategory) {
        PSEVChargeV3Fragment fragment = new PSEVChargeV3Fragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_ev_charge_v3;
    }

    private int status = 0;

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        BmtManager.getInstance().stopPolling();
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.ps_ev_charge));
        mBinding.commonBar.vDivider.setVisibility(View.GONE);
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);
        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> {
            showEVPopup();
        });
        initParams();
        mLottieManager = new LottieManager();
        mBinding.lavEv.setProgress(870 / 993f);
        setEvStatus(status);
        initModeData();
        initRV();
        mBottomSheetBehavior = BottomSheetBehavior.from(mBinding.rlBottom);
        mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.tvMode.setOnClickListener(view -> mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED));
        mBinding.viewHelper.setOnClickListener(view -> mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED));
        mBottomSheetBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                mBinding.viewHelper.setVisibility(newState == BottomSheetBehavior.STATE_EXPANDED ? View.VISIBLE : View.GONE);
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {

            }
        });
        mBinding.csvInstant.setStateChangeListener((isOn, isFromUser) -> {
            if (isFromUser) {
                setInstantCharge(isOn);
            }
        });

        mBinding.bntRestart.setOnClickListener(v -> {
            setSmartEvStatus(0, true);
        });
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (BmtUtil.isDeviceOffline(mPSDevice)) {
            showDeviceOfflineDialog(mPSDevice);
            return;
        }
        mBinding.lavEv.postDelayed(new Runnable() {
            @Override
            public void run() {
                getEVAdvanceStatus();
                getEVChargingInfo();
                getSmartEvStatus(true);
                getEvChargeMode(true);
            }
        }, 200);
    }

    private void initModeData() {
        mInstantChargeFull = new PSEVChargeV3Bean(getString(R.string.ps_ev_instant_charge_title_1),
                getString(R.string.ps_ev_instant_charge_content_1), PSEVChargeV3Bean.INSTANT_CHARGE_FULL);
        mEVChargeModeList.add(mInstantChargeFull);
        mInstantChargeFixed = new PSEVChargeV3Bean(getString(R.string.ps_ev_instant_charge_title_2),
                getString(R.string.ps_ev_instant_charge_content_2), PSEVChargeV3Bean.INSTANT_CHARGE_FIXED);
        mEVChargeModeList.add(mInstantChargeFixed);

        mLowerUtilityRate = new PSEVChargeV3Bean(getString(R.string.ps_ev_smart_charge_title_1),
                getString(R.string.ps_ev_smart_charge_content_1), PSEVChargeV3Bean.LOWER_UTILITY_RATE);
        mEVChargeModeList.add(mLowerUtilityRate);
        mScheduledCharge = new PSEVChargeV3Bean(getString(R.string.ps_ev_smart_charge_title_3),
                getString(R.string.ps_ev_smart_charge_content_3), PSEVChargeV3Bean.SCHEDULE_CHARGE);
        mEVChargeModeList.add(mScheduledCharge);
    }

    private void initRV() {
        mEVChargeAdapter = new BindMultiAdapter<>();
        mSmartChargeData = new ArrayList<>();
        mInstantChargeData = new ArrayList<>();
        mBinding.rvMode.setLayoutManager(new LinearLayoutManager(getContext()));
        mInstantFirstModel = new PSEVChargeV3Model(getContext(), mInstantChargeFull);
        mInstantSecondModel = new PSEVChargeV3Model(getContext(), mInstantChargeFixed);
        mInstantChargeData.add(mInstantFirstModel);
        mInstantChargeData.add(mInstantSecondModel);
        mSmartFirstModel = new PSEVChargeV3Model(getContext(), mLowerUtilityRate);
        mSmartSecondModel = new PSEVChargeV3Model(getContext(), mScheduledCharge);
        mSmartChargeData.add(mSmartFirstModel);
        mSmartChargeData.add(mSmartSecondModel);

        mEVChargeAdapter.setOnBindItemClickListener(new OnBindItemClickListener<PSEVChargeV3Model>() {
            @Override
            public void onItemClick(View v, int position, PSEVChargeV3Model model) {
//                if (mBottomSheetBehaviorState == BottomSheetBehavior.STATE_COLLAPSED) return;
                PSEVChargeV3Bean evChargeBean = model.getEvChargeBean();
                int type = evChargeBean.getType();
                if (evChargeBean.isSelected()) return;
                for (PSEVChargeV3Model psevChargeV2Model : mEVChargeAdapter.getData()) {
                    PSEVChargeV3Bean chargeV2Bean = psevChargeV2Model.getEvChargeBean();
                    chargeV2Bean.setSelected(false);
                    chargeV2Bean.setStatus(chargeV2Bean.getStatusHelper());
                }
                evChargeBean.setSelected(true);
                mEVChargeAdapter.notifyDataSetChanged();
            }
        });

        mEVChargeAdapter.setOnBindItemChildClickListener(new OnBindItemChildClickListener<PSEVChargeV3Model>() {
            @Override
            public void onItemChildClick(View view, int position, PSEVChargeV3Model model) {
                int viewId = view.getId();
                PSEVChargeV3Bean evChargeBean = model.getEvChargeBean();
                int type = evChargeBean.getType();
                switch (viewId) {
                    case R.id.tv_operate:
                        if (type == PSEVChargeV3Bean.SCHEDULE_CHARGE) { //  计划充电
//                            mAppliedPosition = 2;
                            getDelegateActivity().addCommonFragment(PSScheduledChargeFragment.newInstance(mDeviceId, mSubcategory));
                        }
                        break;

                    case R.id.tv_edit:
                        evChargeBean.setStatus(-1);
                        evChargeBean.setSameSelected(false);
                        mEVChargeAdapter.notifyDataSetChanged();
                        break;

                    case R.id.ll_status:
                        if (evChargeBean.getStatus() == -1) {
                            applyCharge(evChargeBean, position, false);
                        }
                        break;
                }
            }
        });
        mBinding.rvMode.setAdapter(mEVChargeAdapter);
    }

    /**
     * 设置车充状态
     *
     * @param status
     */
    private void setEvStatus(int status) {
        mBinding.clValue.setVisibility(status == LottieManager.EV_CHARGING ?
                View.VISIBLE : View.GONE);

        mLottieManager.controlEV(mBinding.lavEv, status);
        switch (status) {
            case LottieManager.EV_OFF:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_off));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_02));
                break;

            case LottieManager.EV_CHARGING:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_charging));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_01));
                break;

            case LottieManager.EV_WAITING:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_waiting_for_charge));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_01));
                break;

            case LottieManager.EV_ERROR:
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_tip_warning));
                break;

            case LottieManager.EV_UNAUTHORIZED:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_unauthorized));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_tip_warning));
                break;
        }
    }

    @Override
    public void onDestroyView() {
        BmtManager.getInstance().startPolling();
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        closePollTimer();
        super.onDestroyView();
    }

    /**
     * 获取EV充电状态信息(0xa027)
     */
    private void getEVAdvanceStatus() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
            mPSDevice.submit(map);
        }
    }

    /**
     * 获取EV充电状态信息(0xa027)
     */
    private void getEVChargingInfo() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_EVCHARGING_INFO);
            mPSDevice.submit(map);
        }
    }


    /**
     * 获取EV充电模式(0xa020)
     */
    private void getEvChargeMode(boolean showLoading) {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
            if (showLoading) {
                showTimeOutLoadinFramgment();
            }
            mPSDevice.submit(map);
        }
    }

    /***
     * 获取智能 EV 调控状态(0xa050)
     */
    private void getSmartEvStatus(boolean isShow) {
        if (mPSDevice != null) {
            isShowEvTip = isShow;
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_SMART_EV_STATUS);
            mPSDevice.submit(map);
        }
    }

    public void setInstantCharge(boolean open) {
        if (mPSDevice != null) {
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
            params.put(BmtDataKey.OPEN, open);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    private void setSmartEvStatus(int status, boolean restart) {
        if (mPSDevice != null) {
            requestRestart = restart;
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_SMART_EV_STATUS);
            params.put(BmtDataKey.TOGGLE_STATUS, status);
            params.put(BmtDataKey.REQUEST_RESTART, restart);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    /**
     * 应用
     *
     * @param evChargeBean
     * @param position
     */
    private void applyCharge(PSEVChargeV3Bean evChargeBean, int position, boolean isLoading) {
        mCurrentItem = position;
        if (!isLoading) {
            evChargeBean.setStatus(0);
            mEVChargeAdapter.notifyDataSetChanged();
        }
        int mode = evChargeBean.getType();
        Map<String, Object> map = new HashMap<>();
        map.put(PSKeyConstant.CMD, mode < 4 ? BmtCmd.SET_EV_CHARGING_MODE : BmtCmd.SET_EVCHARGINGMODE_INSTANT);
        map.put(BmtDataKey.EV_CHARGING_MODE, mode);
        int fixed = evChargeBean.getTempValue();
        if (mode >= 4) {
            map.put(BmtDataKey.FIXED, evChargeBean.getType() == PSEVChargeV3Bean.INSTANT_CHARGE_FIXED ? fixed : 0);
        }
        if (isLoading) {
            showTimeOutLoadinFramgment();
        }
        mPSDevice.submit(map);
    }

    /**
     * 初始化轮询
     */
    private void startPollTimer() {
        closePollTimer();
        mPollTimer = Observable.interval(0, 15, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "subscribe里");
                        getEVChargingInfo();
                    }
                });
    }

    public void closePollTimer() {
        if (mPollTimer != null && !mPollTimer.isUnsubscribed()) {
            mPollTimer.unsubscribe();
        }
    }

    private void setChargeModeText() {
        switch (mEvChargeMode) {
            case PSEVChargeV3Bean.LOWER_UTILITY_RATE:
                mBinding.tvMode.setLocalText(mLowerUtilityRate.getTitle());
                break;

            case PSEVChargeV3Bean.SCHEDULE_CHARGE:
                mBinding.tvMode.setLocalText(mScheduledCharge.getTitle());
                break;

            case PSEVChargeV3Bean.INSTANT_CHARGE_FULL:
                mBinding.tvMode.setLocalText(mInstantChargeFull.getTitle());
                break;

            case PSEVChargeV3Bean.INSTANT_CHARGE_FIXED:
                mBinding.tvMode.setLocalText(mInstantChargeFixed.getTitle());
                break;
        }
    }

    private void showEVPopup() {
        if (evPopup == null) {
            evPopup = new EVPopup(getContext(), getString(R.string.smart_ev_balancing)
                    , getString(R.string.smart_ev_balancing_content), smartEvStatus == 2);
            evPopup.setOnDismissListener(() -> backgroundAlpha(1.0f));
            evPopup.setListener(isOn -> {
                DDLog.d(TAG, "onSwitchStateChange: " + isOn);
                if (!isOn) {
                    showWarning();
                    return;
                }
                evPopup.dismiss();
                setSmartEvStatus(1, false);
            });
        }

        if (!evPopup.isShowing()) {
            evPopup.showAsDropDown(mBinding.commonBar.commonBarRightIcon, -(ScreenUtils.getScreenWidth(getContext()) - DensityUtils.dp2px(getContext(), 49)), 0, Gravity.END);
            backgroundAlpha(0.5f);
        }
    }

    private void showWarning() {
        AlertDialog builder = AlertDialog.createBuilder(getContext())
                .setAutoDissmiss(true)
                .setIsShowContentImageView(true)
                .setContentGravityCenter(true)
                .setContentIcon(getResources().getDrawable(R.drawable.slice_2))
                .setContent(getResources().getString(R.string.smart_ev_balancing_close_tip))
                .setOk(getResources().getString(R.string.Continue))
                .setOKListener(() -> {
                    evPopup.dismiss();
                    setSmartEvStatus(-1, false);
                })
                .setCancel(getResources().getString(R.string.cancel))
                .setCancelListener(() -> {
                    evPopup.setIsOn(true);
                    evPopup.dismiss();
                })
                .preBuilder();
        builder.show();
    }

    private void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp = getDelegateActivity().getWindow().getAttributes();
        lp.alpha = f;
        getDelegateActivity().getWindow().setAttributes(lp);
    }

    private void showEVChargeTip() {
        EVEventDialog mEVEventDialog = new EVEventDialog.Builder(getContext())
                .setCoverRes(R.drawable.img_ev_live_waiting)
                .setTitle(Local.s(getString(R.string.smart_ev_notification)))
                .setBtnText(Local.s(getString(R.string.Confirm)))
                .setSubBtnText(Local.s(getString(R.string.not_now)))
                .setCheckListener(new EVEventDialog.OnCheckListener() {
                    @Override
                    public void onCheck(EVEventDialog dialog) {
                        setSmartEvStatus(0, true);
                        dialog.dismiss();
                    }

                    @Override
                    public void onSubBtnClick(EVEventDialog dialog) {

                    }
                }).build();
        mEVEventDialog.show();
    }

    private void refreshChargeView() {
        mBinding.bntRestart.setVisibility(shouldRestart ? View.VISIBLE : View.GONE);
        mBinding.csvInstant.setVisibility(shouldRestart ? View.GONE : View.VISIBLE);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case BmtCmd.GET_CURRENT_EVADVANCESTATUS:
                            case BmtCmd.EV_ADVANCESTATUS_CHANGED:
                                int status = DeviceHelper.getInt(result, BmtDataKey.ADVANCE_STATUS, 0);
                                if (status <= 0 || status > 4) return;
                                setEvStatus(status - 1);
                                if (status == 3) {
                                    startPollTimer();
                                } else {
                                    closePollTimer();
                                }
                                if (cmd.equals(BmtCmd.EV_ADVANCESTATUS_CHANGED)) {
                                    getSmartEvStatus(false);
                                    getEvChargeMode(false);
                                }
                                break;

                            case BmtCmd.GET_EVCHARGING_INFO:
                                int batteryCharged = DeviceHelper.getInt(result, BmtDataKey.BATTERY_CHARGED, 0);
                                int chargeTime = DeviceHelper.getInt(result, BmtDataKey.CHARGE_TIME, 0);
                                double batteryChargedDou = batteryCharged / 10.0d;
                                mBinding.tvValue.setLocalText(UnitUtil.savePointStr(batteryChargedDou, 1) + getString(R.string.power_station_kWh));
                                String hmStr = TimeUtil.minute2HourMinute(chargeTime);
                                String[] hmArr = hmStr.split(":");
                                String timeStr = hmArr[0] + Local.s(getString(R.string.power_h)) + hmArr[1] + Local.s(getString(R.string.power_min));
                                mBinding.tvTime.setText(timeStr);
                                break;


                            case BmtCmd.GET_CURRENT_EV_CHARGING_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mEvChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                int fixed = DeviceHelper.getInt(result, BmtDataKey.FIXED, 0);
                                int fixedFull = DeviceHelper.getInt(result, BmtDataKey.FIXED_FULL, 0);
                                int pricePercent = DeviceHelper.getInt(result, BmtDataKey.PRICE_PERCENT, 0);
                                DDLog.i(TAG, "pricePercent===" + pricePercent);
                                if (mEvChargeMode <= 0 || mEvChargeMode == 2 || mEvChargeMode > 5)
                                    return;
                                setChargeModeText();
                                if (cmd.equals(BmtCmd.GET_CURRENT_EV_CHARGING_MODE)) {
                                    mBinding.csvInstant.animateToState(mEvChargeMode > 3, false);
                                }
                                for (PSEVChargeV3Bean chargeV2Bean : mEVChargeModeList) {
                                    int type = chargeV2Bean.getType();
                                    chargeV2Bean.setStatus(type == mEvChargeMode ? 1 : -1);
                                    chargeV2Bean.setStatusHelper(type == mEvChargeMode ? 1 : -1);
                                    chargeV2Bean.setValue(type == PSEVChargeV3Bean.LOWER_UTILITY_RATE ? pricePercent : fixed);
                                    chargeV2Bean.setTempValue(fixed);
                                    chargeV2Bean.setMaxValue(fixedFull);
                                    chargeV2Bean.setSelected(chargeV2Bean.getType() == mEvChargeMode);
                                }
                                mEVChargeAdapter.setNewData(mEvChargeMode < 4 ? mSmartChargeData : mInstantChargeData);
                                break;

                            case BmtCmd.GET_SMART_EV_STATUS:
                                smartEvStatus = DeviceHelper.getInt(result, BmtDataKey.TOGGLE_STATUS, 2);
                                shouldRestart = DeviceHelper.getBoolean(result, BmtDataKey.SHOULD_RESTART, false);
                                refreshChargeView();
                                if (shouldRestart && isShowEvTip) {
                                    showEVChargeTip();
                                }
                                break;

                            case BmtCmd.SET_SMART_EV_STATUS:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                if (requestRestart) {
                                    shouldRestart = false;
                                    refreshChargeView();
                                }
                                break;

                            case BmtCmd.SET_EV_CHARGING_MODE:
                                // 此处不能合并: 解决EV充电页面-设置计划充电后点击保存应该返回到上一页，不是返回到上上页。
                                DDLog.i("DSQ","SET_EV_CHARGING_MODE");
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mEvChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                if (mEvChargeMode <= 0 || mEvChargeMode == 2 || mEvChargeMode > 5)
                                    return;
                                setChargeModeText();
                                for (PSEVChargeV3Bean chargeV3Bean : mEVChargeModeList) {
                                    chargeV3Bean.setStatus(chargeV3Bean.getType() == mEvChargeMode ? 1 : -1);
                                    chargeV3Bean.setStatusHelper(chargeV3Bean.getType() == mEvChargeMode ? 1 : -1);
                                    if (chargeV3Bean.getType() != PSEVChargeV3Bean.LOWER_UTILITY_RATE) {
                                        chargeV3Bean.setValue(chargeV3Bean.getTempValue());
                                    }
                                    chargeV3Bean.setSelected(false);
                                    chargeV3Bean.setSelected(chargeV3Bean.getType() == mEvChargeMode);
                                }
                                mEVChargeAdapter.notifyDataSetChanged();
                                if (getDelegateActivity().isFragmentInTop(PSEVChargeV3Fragment.this))
                                    mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
                                break;
                            case BmtCmd.SET_EVCHARGINGMODE_INSTANT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mEvChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                if (mEvChargeMode <= 0 || mEvChargeMode == 2 || mEvChargeMode > 5)
                                    return;
                                setChargeModeText();
                                for (PSEVChargeV3Bean chargeV3Bean : mEVChargeModeList) {
                                    chargeV3Bean.setStatus(chargeV3Bean.getType() == mEvChargeMode ? 1 : -1);
                                    chargeV3Bean.setStatusHelper(chargeV3Bean.getType() == mEvChargeMode ? 1 : -1);
                                    if (chargeV3Bean.getType() != PSEVChargeV3Bean.LOWER_UTILITY_RATE) {
                                        chargeV3Bean.setValue(chargeV3Bean.getTempValue());
                                    }
                                    chargeV3Bean.setSelected(false);
                                    chargeV3Bean.setSelected(chargeV3Bean.getType() == mEvChargeMode);
                                }
                                mEVChargeAdapter.notifyDataSetChanged();
                                mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
                                break;

                            case BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mEvChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                if (mEvChargeMode <= 0 || mEvChargeMode == 2 || mEvChargeMode > 5)
                                    return;
                                setChargeModeText();
                                for (PSEVChargeV3Bean chargeV3Bean : mEVChargeModeList) {
                                    chargeV3Bean.setStatus(chargeV3Bean.getType() == mEvChargeMode ? 1 : -1);
                                    chargeV3Bean.setStatusHelper(chargeV3Bean.getType() == mEvChargeMode ? 1 : -1);
                                    if (chargeV3Bean.getType() != PSEVChargeV3Bean.LOWER_UTILITY_RATE) {
                                        chargeV3Bean.setValue(chargeV3Bean.getTempValue());
                                    }
                                    chargeV3Bean.setReset(true);
                                    chargeV3Bean.setSelected(false);
                                    chargeV3Bean.setSelected(chargeV3Bean.getType() == mEvChargeMode);
                                }
                                mEVChargeAdapter.setNewData(mEvChargeMode < 4 ? mSmartChargeData : mInstantChargeData);
                                break;

                        }
                    } else {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                            case BmtCmd.GET_CURRENT_EV_CHARGING_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case BmtCmd.SET_SMART_EV_STATUS:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                                break;

                            case BmtCmd.SET_EV_CHARGING_MODE:
                            case BmtCmd.SET_EVCHARGINGMODE_INSTANT:
                                mEVChargeAdapter.getData().get(mCurrentItem).getEvChargeBean().setStatus(-1);
                                mEVChargeAdapter.notifyDataSetChanged();
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                                break;

                            case BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE:
//                                closeLoadingFragment();
//                                showErrorToast();
//                                mBinding.csInstantCharge.setSwitch(isInstantSelected, false);
                                break;
                        }
                    }
                }
            });
        }
    }
}
