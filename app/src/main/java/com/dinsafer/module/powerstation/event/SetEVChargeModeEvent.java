package com.dinsafer.module.powerstation.event;

import com.dinsafer.module.powerstation.bean.PSEVChargeV2Bean;

public class SetEVChargeModeEvent {

    private int chargeType;
    private PSEVChargeV2Bean psEVChargeV2Bean;
    private boolean isLoading;

    public SetEVChargeModeEvent(int chargeType, PSEVChargeV2Bean psEVChargeV2Bean) {
        this.chargeType = chargeType;
        this.psEVChargeV2Bean = psEVChargeV2Bean;
    }

    public SetEVChargeModeEvent(int chargeType, PSEVChargeV2Bean psEVChargeV2Bean, boolean isLoading) {
        this.chargeType = chargeType;
        this.psEVChargeV2Bean = psEVChargeV2Bean;
        this.isLoading = isLoading;
    }

    public int getChargeType() {
        return chargeType;
    }

    public void setChargeType(int chargeType) {
        this.chargeType = chargeType;
    }

    public PSEVChargeV2Bean getPsEVChargeV2Bean() {
        return psEVChargeV2Bean;
    }

    public void setPsEVChargeV2Bean(PSEVChargeV2Bean psEVChargeV2Bean) {
        this.psEVChargeV2Bean = psEVChargeV2Bean;
    }

    public boolean isLoading() {
        return isLoading;
    }

    public void setLoading(boolean loading) {
        isLoading = loading;
    }
}
