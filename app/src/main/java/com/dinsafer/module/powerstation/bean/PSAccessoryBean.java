package com.dinsafer.module.powerstation.bean;

public class PSAccessoryBean {

    public static final int WATER_TYPE = 1;
    public static final int SMOKE_TYPE = 2;
    public static final int FANS_TYPE = 3;

    public static final int WATER_STATE_UNKNOWN = -1;
    public static final int WATER_STATE_LOADING = 0;
    public static final int WATER_STATE_VALID = 1;
    public static final int WATER_STATE_ALERT = 2;

    public static final int SMOKE_STATE_INVALID = 0;
    public static final int SMOKE_STATE_VALID = 1;
    public static final int SMOKE_STATE_ALERT = 2;

    public static final int FAN_STATE_UNKNOWN = -1;
    public static final int FAN_STATE_LOADING = 0;
    public static final int FAN_STATE_STOP = 1;
    public static final int FAN_STATE_RUNNING = 2;
    public static final int FAN_STATE_EXCEPTION = 3;

    private String name;
    private int type;
    private int status;
    private int cabinetIndex;

    public PSAccessoryBean(String name, int type, int status) {
        this.name = name;
        this.type = type;
        this.status = status;
    }



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
