package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;

import androidx.databinding.DataBindingUtil;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentUserGuideBinding;
import com.dinsafer.dinnet.databinding.LayoutUserGuideBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.settings.EnergySettingFragment;
import com.dinsafer.ui.scroller.CustomScroller;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import io.reactivex.annotations.NonNull;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/25 16:28
 * @description :
 */
public class UserGuideFragment extends MyBaseFragment<FragmentUserGuideBinding> {

    private static final String KEY_POSITION = "key_position";
    private ArrayList<View> mGuideView = new ArrayList<View>(); // 将要显示的布局存放到list数组
    private ViewPagerAdapter mAdapter;
    private int mCurrentIndex;
    private int mPosition;
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;

    public static UserGuideFragment newInstance(int position, String deviceId, String subcategory) {
        UserGuideFragment fragment = new UserGuideFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_POSITION, position);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    private int[] imgs;
    private List<String> guideTitles;
    private List<String> guideStep;
    private List<String> guideDetail;

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_user_guide;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_manual);
//        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarTitle.setText("");
        getDataParams();
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> {
            if (mCurrentIndex == 0) {
                removeSelf();
            } else {
                mBinding.vpGuide.setCurrentItem(mCurrentIndex - 1);
            }
        });
        mBinding.tvNext.setOnClickListener(v -> {
            if (mCurrentIndex == mGuideView.size() - 1) {
                removeSelf();
            } else {
                mBinding.vpGuide.setCurrentItem(mCurrentIndex + 1);
            }
        });
        List<String> data = Arrays.asList(getResources().getStringArray(R.array.ps_user_guide_list));
        mBinding.tvName.setText(Local.s(data.get(mPosition)));
        initVp();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
    }

    private void getDataParams() {
        Bundle bundle = getArguments();
        mPosition = bundle.getInt(KEY_POSITION);
        switch (mPosition) {
            case 0:
                imgs = new int[]{R.drawable.img_power_user_guide_solarpanel1, R.drawable.img_power_user_guide_solarpanel2, R.drawable.img_power_user_guide_solarpanel3};
                guideTitles = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_solar_title));
                guideStep = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_solar_step));
                guideDetail = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_solar_detail));
                break;

            case 1:
                imgs = new int[]{R.drawable.img_power_user_guide_battery1, R.drawable.img_power_user_guide_battery2, R.drawable.img_power_user_guide_battery3};
                guideTitles = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_battery_title));
                guideStep = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_battery_step));
                guideDetail = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_battery_detail));
                break;

            case 2:
                imgs = new int[]{R.drawable.img_power_user_guide_contracts1, R.drawable.img_power_user_guide_contracts2,
                        R.drawable.img_power_user_guide_contracts3, R.drawable.img_power_user_guide_contracts4};
                guideTitles = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_contract_title));
                guideStep = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_contract_step));
                guideDetail = Arrays.asList(getContext().getResources().getStringArray(R.array.ps_user_guide_contract_detail));
                break;
        }
    }

    private void initVp() {
        for (int i = 0; i < imgs.length; i++) {
            View view = View.inflate(getContext(), R.layout.layout_user_guide, null);
            LayoutUserGuideBinding binding = DataBindingUtil.bind(view);
            binding.tvTitle.setLocalText(guideTitles.get(i));
            binding.ivLogo.setImageResource(imgs[i]);
            if (mPosition == 2 && i == 2) {
                String text = Local.s(guideStep.get(i)) + Local.s("(Optional) ");
                binding.tvStep.setText(text);
            } else {
                binding.tvStep.setLocalText(guideStep.get(i));
            }
            binding.tvStep.setText((i + 1) + ". " + binding.tvStep.getText());
            if (BmtUtil.isBmtDevicePowerStore(mPSDevice)) {
                binding.tvGo.setVisibility(View.GONE);
            } else {
                binding.tvGo.setVisibility(i == imgs.length - 1 && mPosition == 2 ? View.VISIBLE : View.GONE);
                boolean isOnline = BmtUtil.isDeviceConnected(mPSDevice);
                binding.tvGo.setEnabled(isOnline);
                binding.tvGo.setAlpha(isOnline ? 1f : 0.5f);
                binding.tvGo.setOnClickListener(v -> {
                    getDelegateActivity().addCommonFragment(EnergySettingFragment.newInstanceFromSetting(mDeviceId, mSubcategory));
                });
            }
            binding.tvDetail.setLocalText(guideDetail.get(i));
            if (i == imgs.length - 1) {
                setLastPageHelp(binding);
            }
            mGuideView.add(view);
        }
        mAdapter = new ViewPagerAdapter(mGuideView);
        mBinding.vpGuide.setAdapter(mAdapter);
        mBinding.vpGuide.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                mCurrentIndex = i;
                mBinding.tvNext.setText(i == mGuideView.size() - 1 ? Local.s(StringUtil.getStringFromRes(getContext(), R.string.user_guide_quit))
                        : Local.s(StringUtil.getStringFromRes(getContext(), R.string.user_guide_next)));
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });

        try {
            Field mScroller = ViewPager.class.getDeclaredField("mScroller");
            mScroller.setAccessible(true);
            CustomScroller scroller = new CustomScroller(getContext(), new AccelerateInterpolator());
            scroller.setDuration(500);
            mScroller.set(mBinding.vpGuide, scroller);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 最后一页其它操作
     *
     * @param binding
     */
    private void setLastPageHelp(LayoutUserGuideBinding binding) {
        String help1Text = "";
        String help2Text = "";
        switch (mPosition) {
            case 0:
                help1Text = getString(R.string.ps_user_guide_solar_help_1);
                help2Text = getString(R.string.ps_user_guide_solar_help_2);
                binding.tvHelp1.setOnClickListener(v -> {
                    jumpTo(UserGuideFragment.newInstance(1, mDeviceId, mSubcategory));
                });
                binding.tvHelp2.setOnClickListener(v -> {
                    jumpTo(UserGuideFragment.newInstance(2, mDeviceId, mSubcategory));
                });
                break;

            case 1:
                help1Text = getString(R.string.ps_user_guide_battery_help_1);
                help2Text = getString(R.string.ps_user_guide_battery_help_2);
                binding.tvHelp1.setOnClickListener(v -> {
                    jumpTo(UserGuideFragment.newInstance(0, mDeviceId, mSubcategory));
                });
                binding.tvHelp2.setOnClickListener(v -> {
                    jumpTo(UserGuideFragment.newInstance(2, mDeviceId, mSubcategory));
                });
                break;

            case 2:
                help1Text = getString(R.string.ps_user_guide_contract_help_1);
                help2Text = getString(R.string.ps_user_guide_contract_help_2);
                binding.tvHelp1.setOnClickListener(v -> {
                    jumpTo(UserGuideFragment.newInstance(0, mDeviceId, mSubcategory));
                });
                binding.tvHelp2.setOnClickListener(v -> {
                    jumpTo(UserGuideFragment.newInstance(1, mDeviceId, mSubcategory));
                });
                break;
        }

        binding.tvHelp1.setLocalText(help1Text);
        binding.tvHelp2.setLocalText(help2Text);
        boolean isPowerPulse = BmtUtil.isBmtDevicePowerPulse(mPSDevice);
        binding.tvHelp1.setVisibility(isPowerPulse ? View.GONE : View.VISIBLE);
        binding.tvHelp2.setVisibility(isPowerPulse ? View.GONE : View.VISIBLE);
    }

    private void jumpTo(final BaseFragment mFragment) {
        removeSelf();
        getDelegateActivity().addCommonFragment(mFragment);
    }

    class ViewPagerAdapter extends PagerAdapter {


        private ArrayList<View> mViewList;

        public ViewPagerAdapter(ArrayList<View> viewList) {
            mViewList = viewList;
        }

        @Override
        public int getCount() {
            return mViewList.size();
        }


        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return view == object;
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            container.addView(mViewList.get(position));
            return mViewList.get(position);
        }

        @Override
        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            container.removeView(mViewList.get(position));
        }
    }
}
