package com.dinsafer.module.powerstation.guide.home_guide;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentHomeGuide2Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class HomeGuide2Fragment extends MyBaseFragment<FragmentHomeGuide2Binding> {

    public static final String TAG = HomeGuide2Fragment.class.getSimpleName();

    public static HomeGuide2Fragment newInstance() {
        return new HomeGuide2Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_home_guide_2;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
