package com.dinsafer.module.powerstation.widget.segmentbar;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.CornerPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.RectF;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import android.graphics.Shader;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.View;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/23 16:46
 * @description :
 */
public class SegmentSlideBar extends View {

    private Context mContext;
    // 分段bar
    private Map<Integer, RectF> rectFMap = new HashMap<>();
    private List<Segment> segments;
    private int barWidth;
    private int gapWidth;
    private Paint fillPaint;
    private int radius;

    // 三角
    private Path trianglePath;
    private Paint trianglePaint;
    private int triangleColor;
    private int triangleSize;
    private Point point1;
    private Point point2;
    private Point point3;

    // 文本
    private Map<Segment, StaticLayout> staticLayoutMap = new HashMap<>();
    private TextPaint textPaint;
    private float textSize;
    private int textColor;
    private int textWidth;
    private int index;

    private RectF mTrackRect = new RectF();
    private float mTrackLength;
    private float mBottom;

    private float mDelta = 100f;
    private float mProgressY;
    private float mRemainSpace;

    public SegmentSlideBar(Context context) {
        this(context, null);
    }

    public SegmentSlideBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SegmentSlideBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initAttrs(context, attrs);
        init();
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        TypedArray a = context.getTheme().obtainStyledAttributes(
                attrs,
                R.styleable.SegmentSlideBar,
                0, 0);
        gapWidth = a.getDimensionPixelSize(R.styleable.SegmentSlideBar_ssb_segment_gap_width, dp2px(2));
        barWidth = a.getDimensionPixelSize(R.styleable.SegmentSlideBar_ssb_bar_width, dp2px(10));
        radius = a.getDimensionPixelSize(R.styleable.SegmentSlideBar_ssb_bar_radius, dp2px(3));
        triangleSize = a.getDimensionPixelSize(R.styleable.SegmentSlideBar_ssb_triangle_size, dp2px(8));
        triangleColor = a.getColor(R.styleable.SegmentSlideBar_ssb_triangle_color, Color.GRAY);
        textSize = a.getDimensionPixelSize(R.styleable.SegmentSlideBar_ssb_text_size, sp2px(10));
        textWidth = a.getDimensionPixelSize(R.styleable.SegmentSlideBar_ssb_text_width, dp2px(70));
        textColor = a.getColor(R.styleable.SegmentSlideBar_ssb_text_color, Color.GRAY);
        a.recycle();
    }

    private void init() {
        fillPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        fillPaint.setStyle(Paint.Style.FILL);

        trianglePath = new Path();
        trianglePath.setFillType(Path.FillType.EVEN_ODD);
        trianglePaint = new Paint();
        trianglePaint.setAntiAlias(true);
        trianglePaint.setStyle(Paint.Style.FILL);
        trianglePaint.setPathEffect(new CornerPathEffect(dp2px(3)));
        trianglePaint.setColor(triangleColor);
        point1 = new Point();
        point2 = new Point();
        point3 = new Point();

        textPaint = new TextPaint();
        textPaint.setAntiAlias(true);
        textPaint.setColor(textColor);
        textPaint.setTextSize(textSize);
        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.palanquin);
        textPaint.setTypeface(typeface);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        setMeasuredDimension(widthMeasureSpec, heightMeasureSpec);

    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mTrackRect = new RectF(getPaddingLeft(), getPaddingTop() + triangleSize / 2,
                barWidth + getPaddingLeft(), getHeight() - triangleSize / 2 - getPaddingBottom());
        mTrackLength = mTrackRect.bottom - mTrackRect.top;
        mBottom = mTrackRect.bottom;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int segmentsSize = segments == null ? 0 : segments.size();
        if (segmentsSize > 0) {
            for (int segmentIndex = 0; segmentIndex < segmentsSize; segmentIndex++) {
                Segment segment = segments.get(segmentIndex);
                drawSegment(canvas, segment, segmentIndex, segmentsSize);
            }
            drawBar(canvas);
            drawTriangle(canvas);
            drawText(canvas);
        }
    }

    private int getContentWidth() {
        return getWidth() - getPaddingLeft() - getPaddingRight();
    }

    private int getContentHeight() {
        return getHeight() - getPaddingTop() - getPaddingBottom();
    }

    /**
     * 画分段
     *
     * @param canvas
     * @param segment
     * @param segmentIndex
     * @param segmentsSize
     */
    private void drawSegment(Canvas canvas, Segment segment, int segmentIndex, int segmentsSize) {

        float singleSegmentHeight = getContentHeight() * (segment.getMaxValue() - segment.getMinValue()) - gapWidth;
        float segmentTop = getPaddingTop() + getContentHeight() * (1 - segment.getMaxValue());
        float segmentBottom = segmentTop + singleSegmentHeight;
        // Segment bounds
        RectF rectBounds = rectFMap.get(segmentIndex);
        if (rectBounds == null) {
            rectBounds = new RectF();
            rectFMap.put(segmentIndex, rectBounds);
        }
        rectBounds.set(getPaddingLeft(), (int) (segmentTop), barWidth + getPaddingLeft(), (int) (segmentBottom));
        fillPaint.setColor(segment.getColor());
    }

    private void drawBar(Canvas canvas) {
        float space = 0.01f;
        float maxValue1 = segments.get(1).getMaxValue();
        float[] pos = {segments.get(4).getMinValue(), segments.get(4).getMaxValue() - 0.001f,
                segments.get(4).getMaxValue() - 0.001f, segments.get(4).getMaxValue(),
                segments.get(3).getMinValue(), segments.get(3).getMaxValue() - space,
                segments.get(3).getMaxValue() - space, segments.get(3).getMaxValue(),
                segments.get(2).getMinValue(), segments.get(2).getMaxValue() - space,
                segments.get(2).getMaxValue() - space, segments.get(2).getMaxValue(),
                segments.get(1).getMinValue(), segments.get(1).getMaxValue() - space,
                segments.get(1).getMaxValue() - space, segments.get(1).getMaxValue(),
                segments.get(0).getMinValue(), segments.get(0).getMaxValue()};

        int transparentColor = mContext.getResources().getColor(R.color.transparent);
        int[] colors = {segments.get(4).getColor(), segments.get(4).getColor(),
                transparentColor, transparentColor,
                segments.get(3).getColor(), segments.get(3).getColor(),
                transparentColor, transparentColor,
                segments.get(2).getColor(), segments.get(2).getColor(),
                transparentColor, transparentColor,
                segments.get(1).getColor(), segments.get(1).getColor(),
                maxValue1 >= 1f ? segments.get(1).getColor() : transparentColor, maxValue1 >= 1f ? segments.get(1).getColor() : transparentColor,
                segments.get(0).getColor(), segments.get(0).getColor()};
        fillPaint.setShader(new LinearGradient(0, mTrackRect.bottom,
                0, mTrackRect.top, colors, pos, Shader.TileMode.CLAMP));
        canvas.drawRoundRect(
                mTrackRect,
                radius,
                radius,
                fillPaint
        );
    }

    /**
     * 画三角
     *
     * @param canvas
     */
    private void drawTriangle(Canvas canvas) {

        canvas.drawPath(trianglePath, trianglePaint);
    }

    /**
     * 画文本
     *
     * @param canvas
     */
    private void drawText(Canvas canvas) {
        canvas.save();
        RectF rectF = rectFMap.get(index);
//        StaticLayout staticLayout = staticLayoutMap.get(segments.get(index));
        textWidth = (int) (mRemainSpace - rectF.width() - triangleSize - dp2px(19));
        if (textWidth <= 0) {
            textWidth = dp2px(65);
        }
        StaticLayout staticLayout = new StaticLayout(Local.s(segments.get(index).getDescriptionText()), textPaint, textWidth, Layout.Alignment.ALIGN_NORMAL, 1, 0, false);
        float half = staticLayout.getHeight() / 2f;
        float offset = mProgressY - half;
        if (mProgressY - half < mTrackRect.top) {
            offset = mTrackRect.top - triangleSize / 2 - staticLayout.getHeight() / 10f;
        }
        if (mProgressY + half > mTrackRect.bottom) {
            offset = getHeight() - staticLayout.getHeight();
        }
        canvas.translate(rectF.right + triangleSize + dp2px(10), offset);
        staticLayout.draw(canvas);
        canvas.restore();
    }

    /**
     * 分段数据
     *
     * @param segments 分段数据
     * @param index    文本说明位置
     */
    public void setSegments(List<Segment> segments, int index) {
        this.segments = segments;
        this.index = index;
        createStaticLayout();
        invalidate();
        requestLayout();
    }

    /**
     * 文本
     */
    private void createStaticLayout() {
        staticLayoutMap.clear();
        for (Segment segment : segments) {
            StaticLayout staticLayout = new StaticLayout(Local.s(segment.getDescriptionText()), textPaint, textWidth, Layout.Alignment.ALIGN_NORMAL, 1, 0, false);
            staticLayoutMap.put(segment, staticLayout);
        }
    }

    /**
     * 间隔
     *
     * @param gapWidth
     */
    public void setGapWidth(int gapWidth) {
        this.gapWidth = gapWidth;
        invalidate();
        requestLayout();
    }

    /**
     * 文本说明位置
     *
     * @param index
     */
    public void setIndex(int index) {
        this.index = index;
        invalidate();
    }

    public void setProgress(float progress, boolean needRefresh) {
        mProgressY = mBottom - mTrackLength * progress;
        if (mProgressY >= mBottom - triangleSize / 2) mProgressY = mBottom - triangleSize / 2;
//        if (mProgressY <= mTrackRect.top + triangleSize / 2)
//            mProgressY = mTrackRect.top + triangleSize / 2;
        point1.x = (int) (mTrackRect.right + dp2px(5));
        if (progress <= 0) {
            point1.y = (int) mProgressY + triangleSize / 2;
        } else if (progress >= 100) {
            point1.y = (int) mProgressY - triangleSize / 2;
        } else {
            point1.y = (int) mProgressY;
        }

        point2.x = point1.x + triangleSize;
        point2.y = point1.y - triangleSize / 2;
        point3.x = point1.x + triangleSize;
        point3.y = point1.y + triangleSize / 2;
        trianglePath.reset();
        trianglePath.moveTo(point1.x, point1.y);
        trianglePath.lineTo(point2.x, point2.y);
        trianglePath.lineTo(point3.x, point3.y);
        trianglePath.lineTo(point1.x, point1.y);
        trianglePath.close();
        if (progress <= segments.get(4).getMaxValue()) {
            index = 4;
        } else if (progress > segments.get(3).getMinValue() && progress <= segments.get(3).getMaxValue()) {
            index = 3;
        } else if (progress > segments.get(2).getMinValue() && progress <= segments.get(2).getMaxValue()) {
            index = 2;
        } else if (progress > segments.get(1).getMinValue() && progress <= segments.get(1).getMaxValue()) {
            index = 1;
        } else if (progress > segments.get(0).getMinValue() && progress <= segments.get(0).getMaxValue()) {
            index = 0;
        }
        if (needRefresh) {
            invalidate();
        }
    }

    public void setTextWidth(int width) {
        textWidth = width;
    }

    public void setRemainSpace(float remainSpace) {
        mRemainSpace = remainSpace;
    }

    private static int dp2px(final float dp) {
        return DisplayUtil.dip2px(DinSaferApplication.getAppContext(), dp);
    }

    private static int sp2px(final float sp) {
        return DisplayUtil.sp2px(DinSaferApplication.getAppContext(), sp);
    }
}
