package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemTotalLoadChildrenBinding;
import com.dinsafer.module.powerstation.bean.TotalLoadSize;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.UnitUtil;

/**
 * @describe：
 * @date：2025/5/12
 * @author: create by Sydnee
 */

public class PSTotalSizeModel extends BindModel<ItemTotalLoadChildrenBinding> {

    private TotalLoadSize totalLoadSize;
    private boolean isSelected;
    private boolean showDivider;

    public PSTotalSizeModel(Context context, TotalLoadSize totalLoadSize, boolean isSelected, boolean showDivider) {
        super(context);
        this.totalLoadSize = totalLoadSize;
        this.isSelected = isSelected;
        this.showDivider = showDivider;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_total_load_children;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemTotalLoadChildrenBinding itemTotalLoadChildrenBinding) {
        holder.addOnClickListener(R.id.cl_layout);

        String name = totalLoadSize.getSpec()
                + " - " + totalLoadSize.getPowerCap() / 10 + "kW";
        itemTotalLoadChildrenBinding.tvName.setText(name);
        itemTotalLoadChildrenBinding.ivSelected.setVisibility(isSelected ? View.VISIBLE : View.INVISIBLE);
        itemTotalLoadChildrenBinding.viewDivider.setVisibility(showDivider ? View.VISIBLE : View.GONE);
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public TotalLoadSize getTotalLoadSize() {
        return totalLoadSize;
    }
}