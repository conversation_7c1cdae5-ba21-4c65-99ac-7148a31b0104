package com.dinsafer.module.powerstation;

import com.dinsafer.module_home.bean.CountryBean;

import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;

public class CountryComparator implements Comparator<CountryBean> {
    @Override
    public int compare(CountryBean s, CountryBean t1) {
        String country = s.getCountry_name_display().toUpperCase(Locale.getDefault()) + "," + s.getCountry_name_display().toUpperCase(Locale.getDefault());
        String country2 = t1.getCountry_name_display().toUpperCase(Locale.getDefault()) + "," + t1.getCountry_name_display().toUpperCase(Locale.getDefault());
        if (!(country.charAt(0) >= 'A' && country.charAt(0) <= 'Z')
                && (country2.charAt(0) >= 'A' && country2.charAt(0) <= 'Z')) {
            return 1;
        }
        if ((country.charAt(0) >= 'A' && country.charAt(0) <= 'Z')
                && !(country2.charAt(0) >= 'A' && country2.charAt(0) <= 'Z')) {
            return -1;
        }
        return Collator.getInstance(Locale.getDefault()).compare(country, country2);
    }
}
