package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dialog.StatusDialog;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentEnergySettingBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.EnergySettingModel;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EnergySettingFragment extends PSConnectLoadingFragment<FragmentEnergySettingBinding> implements IDeviceCallBack {

    private BindMultiAdapter<EnergySettingModel> mAdapter;
    private List<EnergySettingModel> mData;
    private EnergySettingModel mThirdPartyPVModel;
    private EnergySettingModel mVirtualPlantModel;
    private String mNormalSubtitle;
    private String mOpenTPPSubtitle;

    public static EnergySettingFragment newInstanceFromAddPS(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_STEP_ADD, deviceId, subcategory);
    }

    public static EnergySettingFragment newInstanceFromSetting(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_SETTING, deviceId, subcategory);
    }

    public static EnergySettingFragment newInstance(int from, String deviceId, String subcategory) {
        EnergySettingFragment fragment = new EnergySettingFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_energy_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mNormalSubtitle = Local.s(getString(R.string.virtual_power_plant_desc));
        mOpenTPPSubtitle = mNormalSubtitle + Local.s(getString(R.string.third_party_pv_virtual_plant_tips));
        mBinding.lcbDone.setVisibility(mFrom == PARAM_FROM_STEP_ADD ? View.VISIBLE : View.GONE);
        initTitle();
        initRv();
        if (!BmtUtil.isThirdPartyDefaultOpen(mPSDevice)) {
            getThirdPartyPV();
        }
        getVirtualPlant();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.lcbDone.setOnClickListener(v -> {
            getDelegateActivity().removeAllCommonFragment();
            getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstanceForStepAddPS(
                    DeviceHelper.getString(BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory), DinConst.INFO_NAME, "")
                    , mDeviceId, mSubcategory));
        });
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    private void initTitle() {
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.energy_settings));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
    }

    private void initRv() {
        mAdapter = new BindMultiAdapter<>();
        mData = new ArrayList<>();
        boolean isDefaultOpen = BmtUtil.isThirdPartyDefaultOpen(mPSDevice);
        if (mPSDevice != null) {
            if (!isDefaultOpen) {
                int iotVersionStatus = BmtUtil.getIotVersionGETargetVersion(mPSDevice, "1.8.0");
                if (iotVersionStatus == 1) {
                    String thirdPartySubTitle = Local.s(getString(R.string.requirements_colon)) +
                            Local.s(getString(R.string.smartmeter_operates_normally_period)) +
                            Local.s(getString(R.string.utility_is_inputted_normally_period));
                    mThirdPartyPVModel = new EnergySettingModel(getContext(), getString(R.string.third_party_pv),
                            thirdPartySubTitle, isOn -> openThirdPartyPV(isOn));
                    mData.add(0, mThirdPartyPVModel);
                }
            }
        }
        mVirtualPlantModel = new EnergySettingModel(getContext(), getString(R.string.sell_back_to_grid),
                isDefaultOpen ? mOpenTPPSubtitle : mNormalSubtitle, isOn -> openVirtualPP(isOn));
        mData.add(mVirtualPlantModel);
        mBinding.rvData.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvData.setAdapter(mAdapter);
        mAdapter.setNewData(mData);
    }

    /**
     * 获取第三方PV配置(0xa040)
     */
    private void getThirdPartyPV() {
        if (mPSDevice == null) {
            showErrorToast();
            return;
        }
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, String> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, BmtCmd.GET_THIRDPARTYPV_INFO);
        mPSDevice.submit(params);
    }

    private void getVirtualPlant() {
        if (mPSDevice == null) {
            showErrorToast();
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, BmtCmd.GET_VIRTUAL_POWER_PLANT);
        mPSDevice.submit(params);
    }

    /**
     * 打开虚拟电厂
     */
    private void openVirtualPP(boolean isOn) {
        Map<String, Object> params = new HashMap<>();
        params.clear();
        params.put(PSKeyConstant.CMD, BmtCmd.SET_VIRTUAL_POWER_PLANT);
        params.put(PSKeyConstant.ON, isOn);
        showTimeOutLoadinFramgmentWithErrorAlert();
        mPSDevice.submit(params);
    }

    private void openThirdPartyPV(boolean isOn) {
        Map<String, Object> params = new HashMap<>();
        params.clear();
        params.put(PSKeyConstant.CMD, BmtCmd.SET_THIRD_PARTYPV_ON);
        params.put(PSKeyConstant.ON, isOn);
        showTimeOutLoadinFramgmentWithErrorAlert();
        mPSDevice.submit(params);
    }

    private void showTPPSuccess() {
        StatusDialog.createBuilder(getDelegateActivity())
                .setAutoDissmiss(true)
                .setCanCancel(false)
                .setIsShowSuccessView(true)
                .setIsSuccess(true)
                .setOk(Local.s(getResources().getString(R.string.done)))
                .setContent(Local.s(getString(R.string.third_party_pv_successfully)))
                .preBuilder()
                .show();
    }

    private void showTPPFail(String content) {
        StatusDialog.createBuilder(getDelegateActivity())
                .setAutoDissmiss(true)
                .setCanCancel(false)
                .setCancel(Local.s(getString(R.string.cancel)))
                .setIsShowSuccessView(true)
                .setIsSuccess(false)
                .setOk(Local.s(getResources().getString(R.string.try_again)))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (mThirdPartyPVModel != null) {
                            boolean isTPOn = mThirdPartyPVModel.isOpen();
                            mThirdPartyPVModel.setOpen(!isTPOn);
                            mAdapter.notifyDataSetChanged();
                            openThirdPartyPV(!isTPOn);
                        }

                    }
                })
                .setContent(content)
                .preBuilder()
                .show();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case BmtCmd.GET_VIRTUAL_POWER_PLANT:
                                boolean powerPlantOn = (boolean) MapUtils.get(result, PSKeyConstant.ON, false);
                                mVirtualPlantModel.setOpen(powerPlantOn);
                                mAdapter.notifyDataSetChanged();
                                break;

                            case BmtCmd.GET_THIRDPARTYPV_INFO:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                boolean isOn = DeviceHelper.getBoolean(result, BmtDataKey.ON, false);
                                if (mThirdPartyPVModel != null) {
                                    mThirdPartyPVModel.setOpen(isOn);
                                }
                                mVirtualPlantModel.setSubtitle(isOn ? mOpenTPPSubtitle : mNormalSubtitle);
                                mAdapter.notifyDataSetChanged();
                                break;

                            case BmtCmd.SET_VIRTUAL_POWER_PLANT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;
                            case BmtCmd.SET_THIRD_PARTYPV_ON:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                if (mThirdPartyPVModel != null) {
                                    boolean on = DeviceHelper.getBoolean(result, BmtDataKey.ON, false);
                                    mThirdPartyPVModel.setOpen(on);
                                    mVirtualPlantModel.setSubtitle(on ? mOpenTPPSubtitle : mNormalSubtitle);
                                    mAdapter.notifyDataSetChanged();
                                    List<Integer> errors = DeviceHelper.getList(result, BmtDataKey.ERROR);
                                    if (CollectionUtil.isListNotEmpty(errors)) {
                                        StringBuilder stringBuilder = new StringBuilder();
                                        boolean hasBSenorError = false;
                                        boolean hasGridError = false;
                                        for (Integer error : errors) {
                                            if (error == Mcu.ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType.BSensorNotDetect.getThirdPartyPVActiveErrorType()) {
                                                hasBSenorError = true;
                                            }

                                            if (error == Mcu.ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType.GridUnavailable.getThirdPartyPVActiveErrorType()) {
                                                hasGridError = true;
                                            }
                                        }
                                        if (!hasBSenorError && !hasGridError) {
                                            showTPPFail(Local.s(getString(R.string.failed_try_again)));
                                        } else {
                                            stringBuilder.append(Local.s(getString(R.string.third_party_pv_failed)));
                                            stringBuilder.append(Local.s(getString(R.string.please_check_the_requirements_colon)));
                                            if (hasBSenorError) {
                                                stringBuilder.append("\n");
                                                stringBuilder.append(Local.s(getString(R.string.smartmeter_operates_normally_period)));
                                            }
                                            if (hasGridError) {
                                                stringBuilder.append("\n");
                                                stringBuilder.append(Local.s(getString(R.string.utility_is_inputted_normally_period)));
                                            }
                                            showTPPFail(stringBuilder.toString());
                                        }
                                    } else {
                                        if (on) {
                                            showTPPSuccess();
                                        }
                                    }
                                }
                                break;

                        }
                    } else {
                        switch (cmd) {
                            case BmtCmd.GET_VIRTUAL_POWER_PLANT:
                            case BmtCmd.GET_THIRDPARTYPV_INFO:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case BmtCmd.SET_VIRTUAL_POWER_PLANT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mVirtualPlantModel.setOpen(!mVirtualPlantModel.isOpen());
                                mAdapter.notifyDataSetChanged();
                                showErrorToast();
                                break;

                            case BmtCmd.SET_THIRD_PARTYPV_ON:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                if (mThirdPartyPVModel != null) {
                                    mThirdPartyPVModel.setOpen(!mThirdPartyPVModel.isOpen());
                                    mAdapter.notifyDataSetChanged();
                                }
                                showErrorToast();
                                break;
                        }
                    }
                }
            });
        }
    }
}
