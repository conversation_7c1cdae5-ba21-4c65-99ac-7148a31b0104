package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemParticipationHoursTipsBinding;
import com.dinsafer.ui.rv.BindModel;

public class ParticipationHoursTipsModel extends BindModel<ItemParticipationHoursTipsBinding> {

    private String num;
    private String content;

    public ParticipationHoursTipsModel(Context context, String num, String content) {
        super(context);
        this.num = num;
        this.content = content;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_participation_hours_tips;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemParticipationHoursTipsBinding binding) {
        binding.tvNum.setText(num);
        binding.tvContent.setLocalText(content);
    }
}
