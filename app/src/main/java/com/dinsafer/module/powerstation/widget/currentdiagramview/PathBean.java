package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.RectF;

import com.dinsafer.util.DDLog;

public class PathBean {

    public static final int SOLAR_SUPPLY = 1;
    public static final int GRID_SUPPLY = 2;
    public static final int VEHICLE_SUPPLY = 3;
    public static final int BATTERY_SUPPLY = 4;

    public static final String SOLAR_KEY = "1";
    public static final String GRID_KEY = "2";
    public static final String VEHICLE_KEY = "3";
    public static final String BATTERY_KEY = "4";

    private int type;
    private Paint animPaint1;
    private Paint animPaint2;
    private Paint animPaint3;
    private Paint animPaint4;
    private Paint animPaint5;

    // 第一条动画路径
    private PathMeasure pathMeasure1;
    private Path path1;
    private Path dst1;
    private float length1;
    private float animatorValue1;

    // 第二条动画路径
    private PathMeasure pathMeasure2;
    private Path path2;
    private Path dst2;
    private float length2;
    private float animatorValue2;

    // 第三条动画路径
    private PathMeasure pathMeasure3;
    private Path path3;
    private Path dst3;
    private float length3;
    private float animatorValue3;

    // 第四条动画路径
    private PathMeasure pathMeasure4;
    private Path path4;
    private Path dst4;
    private float length4;
    private float animatorValue4;

    // 第四条动画路径
    private PathMeasure pathMeasure5;
    private Path path5;
    private Path dst5;
    private float length5;
    private float animatorValue5;

    // 动画路径的矩形
    private RectF rectF1;
    private RectF rectF2;
    private RectF rectF3;
    private RectF rectF4;
    private RectF rectF5;

    // 是否绘制路径
    private boolean isDrawPath1 = false;
    private boolean isDrawPath2 = false;
    private boolean isDrawPath3 = false;
    private boolean isDrawPath4 = false;
    private boolean isDrawPath5 = false;

    // 渐变颜色
    private int startColor1;
    private int endColor1;
    private int startColor2;
    private int endColor2;
    private int startColor3;
    private int endColor3;
    private int startColor4;
    private int endColor4;
    private int startColor5;
    private int endColor5;

    private float[] pointStart1;
    private float[] pointStart2;
    private float[] pointStart3;
    private float[] pointStart4;
    private float[] pointStart5;

    private float[] pointEnd1;
    private float[] pointEnd2;
    private float[] pointEnd3;
    private float[] pointEnd4;
    private float[] pointEnd5;

    public PathBean(int type) {
        if (type<1 || type>4) {
            return;
        }
        this.type = type;

        animPaint1 = new Paint();
        animPaint2 = new Paint();
        animPaint3 = new Paint();
        animPaint4 = new Paint();


        rectF1 = new RectF();
        rectF2 = new RectF();
        rectF3 = new RectF();
        rectF4 = new RectF();


        initPath1();
        initPath2();
        initPath3();
        initPath4();

        if (type == SOLAR_SUPPLY) {
            rectF5 = new RectF();
            animPaint5 = new Paint();
            initPath5();
        }
    }


    /**
     * 第一条路径
     */
    private void initPath1() {
        pathMeasure1 = new PathMeasure();
        path1 = new Path();
        dst1 = new Path();
        pointStart1 = new float[2];
        pointEnd1 = new float[2];
    }

    /**
     * 第二条路径
     */
    private void initPath2() {
        pathMeasure2 = new PathMeasure();
        path2 = new Path();
        dst2 = new Path();
        pointStart2 = new float[2];
        pointEnd2 = new float[2];
    }

    /**
     * 第三条路径
     */
    private void initPath3() {
        pathMeasure3 = new PathMeasure();
        path3 = new Path();
        dst3 = new Path();
        pointStart3 = new float[2];
        pointEnd3 = new float[2];
    }

    /**
     * 第四条路径
     */
    private void initPath4() {
        pathMeasure4 = new PathMeasure();
        path4 = new Path();
        dst4 = new Path();
        pointStart4 = new float[2];
        pointEnd4 = new float[2];
    }

    /**
     * 第五条路径
     */
    private void initPath5() {
        pathMeasure5 = new PathMeasure();
        path5 = new Path();
        dst5 = new Path();
        pointStart5 = new float[2];
        pointEnd5 = new float[2];
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Paint getAnimPaint1() {
        return animPaint1;
    }

    public void setAnimPaint1(Paint animPaint1) {
        this.animPaint1 = animPaint1;
    }

    public Paint getAnimPaint2() {
        return animPaint2;
    }

    public void setAnimPaint2(Paint animPaint2) {
        this.animPaint2 = animPaint2;
    }

    public Paint getAnimPaint3() {
        return animPaint3;
    }

    public void setAnimPaint3(Paint animPaint3) {
        this.animPaint3 = animPaint3;
    }

    public Paint getAnimPaint4() {
        return animPaint4;
    }

    public void setAnimPaint4(Paint animPaint4) {
        this.animPaint4 = animPaint4;
    }

    public Paint getAnimPaint5() {
        return animPaint5;
    }

    public void setAnimPaint5(Paint animPaint5) {
        this.animPaint5 = animPaint5;
    }

    public PathMeasure getPathMeasure1() {
        return pathMeasure1;
    }

    public void setPathMeasure1(PathMeasure pathMeasure1) {
        this.pathMeasure1 = pathMeasure1;
    }

    public Path getPath1() {
        return path1;
    }

    public void setPath1(Path path1) {
        this.path1 = path1;
    }

    public Path getDst1() {
        return dst1;
    }

    public void setDst1(Path dst1) {
        this.dst1 = dst1;
    }

    public float getLength1() {
        return length1;
    }

    public void setLength1(float length1) {
        this.length1 = length1;
    }

    public float getAnimatorValue1() {
        return animatorValue1;
    }

    public void setAnimatorValue1(float animatorValue1) {
        this.animatorValue1 = animatorValue1;
    }

    public PathMeasure getPathMeasure2() {
        return pathMeasure2;
    }

    public void setPathMeasure2(PathMeasure pathMeasure2) {
        this.pathMeasure2 = pathMeasure2;
    }

    public Path getPath2() {
        return path2;
    }

    public void setPath2(Path path2) {
        this.path2 = path2;
    }

    public Path getDst2() {
        return dst2;
    }

    public void setDst2(Path dst2) {
        this.dst2 = dst2;
    }

    public float getLength2() {
        return length2;
    }

    public void setLength2(float length2) {
        this.length2 = length2;
    }

    public float getAnimatorValue2() {
        return animatorValue2;
    }

    public void setAnimatorValue2(float animatorValue2) {
        this.animatorValue2 = animatorValue2;
    }

    public PathMeasure getPathMeasure3() {
        return pathMeasure3;
    }

    public void setPathMeasure3(PathMeasure pathMeasure3) {
        this.pathMeasure3 = pathMeasure3;
    }

    public Path getPath3() {
        return path3;
    }

    public void setPath3(Path path3) {
        this.path3 = path3;
    }

    public Path getDst3() {
        return dst3;
    }

    public void setDst3(Path dst3) {
        this.dst3 = dst3;
    }

    public float getLength3() {
        return length3;
    }

    public void setLength3(float length3) {
        this.length3 = length3;
    }

    public float getAnimatorValue3() {
        return animatorValue3;
    }

    public void setAnimatorValue3(float animatorValue3) {
        this.animatorValue3 = animatorValue3;
    }

    public PathMeasure getPathMeasure4() {
        return pathMeasure4;
    }

    public void setPathMeasure4(PathMeasure pathMeasure4) {
        this.pathMeasure4 = pathMeasure4;
    }

    public Path getPath4() {
        return path4;
    }

    public void setPath4(Path path4) {
        this.path4 = path4;
    }

    public Path getDst4() {
        return dst4;
    }

    public void setDst4(Path dst4) {
        this.dst4 = dst4;
    }

    public float getLength4() {
        return length4;
    }

    public void setLength4(float length4) {
        this.length4 = length4;
    }

    public float getAnimatorValue4() {
        return animatorValue4;
    }

    public void setAnimatorValue4(float animatorValue4) {
        this.animatorValue4 = animatorValue4;
    }

    public PathMeasure getPathMeasure5() {
        return pathMeasure5;
    }

    public void setPathMeasure5(PathMeasure pathMeasure5) {
        this.pathMeasure5 = pathMeasure5;
    }

    public Path getPath5() {
        return path5;
    }

    public void setPath5(Path path5) {
        this.path5 = path5;
    }

    public Path getDst5() {
        return dst5;
    }

    public void setDst5(Path dst5) {
        this.dst5 = dst5;
    }

    public float getLength5() {
        return length5;
    }

    public void setLength5(float length5) {
        this.length5 = length5;
    }

    public float getAnimatorValue5() {
        return animatorValue5;
    }

    public void setAnimatorValue5(float animatorValue5) {
        this.animatorValue5 = animatorValue5;
    }

    public RectF getRectF1() {
        return rectF1;
    }

    public void setRectF1(RectF rectF1) {
        this.rectF1 = rectF1;
    }

    public RectF getRectF2() {
        return rectF2;
    }

    public void setRectF2(RectF rectF2) {
        this.rectF2 = rectF2;
    }

    public RectF getRectF3() {
        return rectF3;
    }

    public void setRectF3(RectF rectF3) {
        this.rectF3 = rectF3;
    }

    public RectF getRectF4() {
        return rectF4;
    }

    public void setRectF4(RectF rectF4) {
        this.rectF4 = rectF4;
    }

    public RectF getRectF5() {
        return rectF5;
    }

    public void setRectF5(RectF rectF5) {
        this.rectF5 = rectF5;
    }

    public boolean isDrawPath1() {
        return isDrawPath1;
    }

    public void setDrawPath1(boolean drawPath1) {
        isDrawPath1 = drawPath1;
    }

    public boolean isDrawPath2() {
        return isDrawPath2;
    }

    public void setDrawPath2(boolean drawPath2) {
        isDrawPath2 = drawPath2;
    }

    public boolean isDrawPath3() {
        return isDrawPath3;
    }

    public void setDrawPath3(boolean drawPath3) {
        isDrawPath3 = drawPath3;
    }

    public boolean isDrawPath4() {
        return isDrawPath4;
    }

    public void setDrawPath4(boolean drawPath4) {
        isDrawPath4 = drawPath4;
    }

    public boolean isDrawPath5() {
        return isDrawPath5;
    }

    public void setDrawPath5(boolean drawPath5) {
        isDrawPath5 = drawPath5;
    }

    public int getStartColor1() {
        return startColor1;
    }

    public void setStartColor1(int startColor1) {
        this.startColor1 = startColor1;
    }

    public int getEndColor1() {
        return endColor1;
    }

    public void setEndColor1(int endColor1) {
        this.endColor1 = endColor1;
    }

    public int getStartColor2() {
        return startColor2;
    }

    public void setStartColor2(int startColor2) {
        this.startColor2 = startColor2;
    }

    public int getEndColor2() {
        return endColor2;
    }

    public void setEndColor2(int endColor2) {
        this.endColor2 = endColor2;
    }

    public int getStartColor3() {
        return startColor3;
    }

    public void setStartColor3(int startColor3) {
        this.startColor3 = startColor3;
    }

    public int getEndColor3() {
        return endColor3;
    }

    public void setEndColor3(int endColor3) {
        this.endColor3 = endColor3;
    }

    public int getStartColor4() {
        return startColor4;
    }

    public void setStartColor4(int startColor4) {
        this.startColor4 = startColor4;
    }

    public int getEndColor4() {
        return endColor4;
    }

    public void setEndColor4(int endColor4) {
        this.endColor4 = endColor4;
    }

    public int getStartColor5() {
        return startColor5;
    }

    public void setStartColor5(int startColor5) {
        this.startColor5 = startColor5;
    }

    public int getEndColor5() {
        return endColor5;
    }

    public void setEndColor5(int endColor5) {
        this.endColor5 = endColor5;
    }

    public float[] getPointStart1() {
        return pointStart1;
    }

    public void setPointStart1(float[] pointStart1) {
        this.pointStart1 = pointStart1;
    }

    public float[] getPointStart2() {
        return pointStart2;
    }

    public void setPointStart2(float[] pointStart2) {
        this.pointStart2 = pointStart2;
    }

    public float[] getPointStart3() {
        return pointStart3;
    }

    public void setPointStart3(float[] pointStart3) {
        this.pointStart3 = pointStart3;
    }

    public float[] getPointStart4() {
        return pointStart4;
    }

    public void setPointStart4(float[] pointStart4) {
        this.pointStart4 = pointStart4;
    }

    public float[] getPointStart5() {
        return pointStart5;
    }

    public void setPointStart5(float[] pointStart5) {
        this.pointStart5 = pointStart5;
    }

    public float[] getPointEnd1() {
        return pointEnd1;
    }

    public void setPointEnd1(float[] pointEnd1) {
        this.pointEnd1 = pointEnd1;
    }

    public float[] getPointEnd2() {
        return pointEnd2;
    }

    public void setPointEnd2(float[] pointEnd2) {
        this.pointEnd2 = pointEnd2;
    }

    public float[] getPointEnd3() {
        return pointEnd3;
    }

    public void setPointEnd3(float[] pointEnd3) {
        this.pointEnd3 = pointEnd3;
    }

    public float[] getPointEnd4() {
        return pointEnd4;
    }

    public void setPointEnd4(float[] pointEnd4) {
        this.pointEnd4 = pointEnd4;
    }

    public float[] getPointEnd5() {
        return pointEnd5;
    }

    public void setPointEnd5(float[] pointEnd5) {
        this.pointEnd5 = pointEnd5;
    }

    public boolean startGreaterEndX1 () {
        return pointStart1[0] > pointEnd1[0];
    }

    public boolean startGreaterEndY1 () {
        return pointStart1[1] > pointEnd1[1];
    }

    public boolean startGreaterEndX2 () {
        return pointStart2[0] > pointEnd2[0];
    }

    public boolean startGreaterEndY2 () {
        return pointStart2[1] > pointEnd2[1];
    }

    public boolean startGreaterEndX3 () {
        return pointStart3[0] > pointEnd3[0];
    }

    public boolean startGreaterEndY3 () {
        return pointStart3[1] > pointEnd3[1];
    }

    public boolean startGreaterEndX4 () {
        return pointStart4[0] > pointEnd4[0];
    }

    public boolean startGreaterEndY4 () {
        return pointStart4[1] > pointEnd4[1];
    }

    public boolean startGreaterEndX5 () {
        return pointStart5[0] > pointEnd5[0];
    }

    public boolean startGreaterEndY5 () {
        return pointStart5[1] > pointEnd5[1];
    }

    public void setAllPathFalse() {
        isDrawPath1 = false;
        isDrawPath2 = false;
        isDrawPath3 = false;
        isDrawPath4 = false;
        isDrawPath5 = false;
    }
}
