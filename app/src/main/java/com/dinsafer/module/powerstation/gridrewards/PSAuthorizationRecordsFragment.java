package com.dinsafer.module.powerstation.gridrewards;

import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.BounceInterpolator;
import android.widget.AdapterView;

import com.baoyz.swipemenulistview.SwipeMenu;
import com.baoyz.swipemenulistview.SwipeMenuCreator;
import com.baoyz.swipemenulistview.SwipeMenuItem;
import com.baoyz.swipemenulistview.SwipeMenuListView;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.common.utils.FileUtil;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsAuthorizationRecordsBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.powerstation.OnlinePdfFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.utils.download.DownloadAndZipResultEvent;
import com.dinsafer.module.powerstation.utils.download.DownloadAndZipUtil;
import com.dinsafer.module.powerstation.utils.download.DownloadBean;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;
import com.scwang.smart.refresh.footer.ClassicsFooter;
import com.scwang.smart.refresh.header.ClassicsHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/30
 * @author: create by Sydnee
 */
public class PSAuthorizationRecordsFragment extends MyBaseFragment<FragmentPsAuthorizationRecordsBinding> {

    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;

    private AuthorizationRecordsAdapter mAdapter;
    private List<BalanceContractRecordsResponse.Record> recordData = new ArrayList<>();

    private String downloadPath;
    private long requestTime = 0;
    private static int RECORD_LISTVIEW_MAX_H = 500;

    private BroadcastReceiver downloadReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (DownloadManager.ACTION_DOWNLOAD_COMPLETE.equals(intent.getAction())) {
                long downloadId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);
                // 查询下载状态并处理完成逻辑
                DownloadManager downloadManager = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
                DownloadManager.Query query = new DownloadManager.Query().setFilterById(downloadId);
                Cursor cursor = downloadManager.query(query);
                if (cursor.moveToFirst()) {
                    int state = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS);
                    if (state != -1) {
                        switch (cursor.getInt(state)) {
                            case DownloadManager.STATUS_SUCCESSFUL:
                                // 下载成功
                                DDLog.i(TAG, "Downloaded success！");
                                int columnIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI);
                                if (columnIndex >= 0) {
                                    String downloadedFileUri = cursor.getString(columnIndex);
                                    String filePath = FileUtil.filePathFromUri(getContext(), downloadedFileUri);
                                    DDLog.d(TAG, "Downloaded file path: " + filePath);
                                    closeLoadingFragment();
                                    getMainActivity().showTopToast("Downloaded file path: " + filePath);
                                    break;
                                }
                                closeLoadingFragment();
                                showErrorToast();
                                break;
                            case DownloadManager.STATUS_FAILED:
                                DDLog.e(TAG, "Downloaded failed！！！");
                                int reason = cursor.getColumnIndex(DownloadManager.COLUMN_REASON);
                                if (reason != -1) {
                                    DDLog.e(TAG, "Downloaded failed reason: " + cursor.getInt(reason));
                                }
                                closeLoadingFragment();
                                showErrorToast();
                                break;
                        }
                    }
                }
                cursor.close();
            }
        }
    };

    public static PSAuthorizationRecordsFragment newInstance(FamilyBalanceContractInfo familyBalanceContractInfo) {
        PSAuthorizationRecordsFragment fragment = new PSAuthorizationRecordsFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static PSAuthorizationRecordsFragment newInstance() {
        return newInstance(null);
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_authorization_records;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.authorization_records));

        mBinding.btnTerminate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDelegateActivity().addCommonFragment(PSTerminateFragment.newInstanceFromContractDetail(
                        HomeManager.getInstance().getCurrentHome().getHomeName()
                        , HomeManager.getInstance().getCurrentHome().getHomeID()));
            }
        });
        initListView();
        createMenu();
        initRefreshLayout();
        showTimeOutLoadinFramgmentWithErrorAlert();
        getRecordData(false);
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        mBinding.btnTerminate.setVisibility(View.GONE);
        if (bundle != null) {
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
            if (mFamilyBalanceContractInfo != null) {
                refreshRecordListView();
            }
        }

        if (DDSystemUtil.aboveAndroid11()) {
            downloadPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath()
                    + "/" + getContext().getApplicationContext().getPackageName() + "/File/";
        } else {
            downloadPath = getContext().getExternalFilesDir(null).getAbsolutePath()
                    + "/" + getContext().getApplicationContext().getPackageName() + "/File/";
        }

        if (DDSystemUtil.aboveAndroid14()) {
            getDelegateActivity().registerReceiver(downloadReceiver
                    , new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), Context.RECEIVER_EXPORTED);
        } else {
            getDelegateActivity().registerReceiver(downloadReceiver
                    , new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        getDelegateActivity().unregisterReceiver(downloadReceiver);
    }

    private void refreshRecordListView() {
        mBinding.btnTerminate.setVisibility(mFamilyBalanceContractInfo.isFamilySigning() ? View.VISIBLE : View.GONE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(outMetrics);
        mBinding.btnTerminate.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                mBinding.btnTerminate.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int topHeight = mBinding.commonBar.rlParent.getHeight() + DensityUtil.dp2px(getContext(), 30);
                int screenHeight = outMetrics.heightPixels - topHeight - mBinding.btnTerminate.getHeight() - DensityUtil.dp2px(getContext(), 80);
                ViewGroup.LayoutParams params = mBinding.recordListview.getLayoutParams();
                params.height = screenHeight;
                mBinding.recordListview.setLayoutParams(params);
            }
        });
    }

    private void createMenu() {
        SwipeMenuCreator creator = new SwipeMenuCreator() {
            @Override
            public void create(SwipeMenu menu) {
                SwipeMenuItem deleteItem = new SwipeMenuItem(getActivity());
                deleteItem.setBackground(R.color.color_brand_primary);
                deleteItem.setWidth((int) DisplayUtil.dip2px(getActivity(), 130));
                deleteItem.setTitleSize(13);
                deleteItem.setTitleColor(Color.WHITE);
                deleteItem.setTitle(Local.s(getResources().getString(R.string.download)));
                menu.addMenuItem(deleteItem);
            }
        };

        mBinding.recordListview.setMenuCreator(creator);
        mBinding.recordListview.setSwipeDirection(SwipeMenuListView.DIRECTION_LEFT);
        mBinding.recordListview.setCloseInterpolator(new BounceInterpolator());
        mBinding.recordListview.setOnMenuItemClickListener((SwipeMenuListView.OnMenuItemClickListener) (position, menu, index) -> {
            DDLog.e(TAG, "onMenuItemClick:" + position);
            BalanceContractRecordsResponse.Record record = recordData.get(position);
            int status = record.getStatus();
            if (status == 2) {
                return false;
            }

            StringBuilder sbName = new StringBuilder();
            String time = DDDateUtil.formatLong(record.getCreate_time() / 1000L / 1000L, "yyyyMMdd");
            sbName.append(time);
            sbName.append(record.getSeq());
            sbName.append(" ");

            if (status == 0) {
                if (PermissionUtil.isStoragePermissionDeny(getContext())) {
                    requestReadImagePermission();
                    return false;
                }
                String attorneyUrl = record.getContract_file();
                String termUrl = record.getAttorney_auth();
                List<DownloadBean> downloadBeans = new ArrayList<>();
                downloadBeans.add(new DownloadBean(Local.s(getString(R.string.power_of_attorney)), attorneyUrl, ".pdf"));
                downloadBeans.add(new DownloadBean(Local.s(getString(R.string.emaldo_terms)), termUrl, ".pdf"));
                sbName.append(Local.s(getString(R.string.power_of_attorney)));
                showLoadingFragment(LoadingFragment.BLACK);
                DownloadAndZipUtil.downloadAndZipFiles(downloadBeans, downloadPath, sbName.toString() + ".zip");

            } else if (status == 1) {
                sbName.append(Local.s(getString(R.string.terminate_statement)));
                String url = record.getContract_file();
                sbName.append(".pdf");
                downloadFile(url, downloadPath, sbName.toString());
            }


            // false : close the menu; true : not close the menu
            return false;
        });
        mBinding.recordListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                DDLog.e(TAG, "onItemClick:" + position);
                if (position >= recordData.size()) {
                    return;
                }
                BalanceContractRecordsResponse.Record record = recordData.get(position);
                if (record.getStatus() == 2) {
                    return;
                }
                if (record.getStatus() == 0) {
                    showTerms(record);
                    return;
                }
                if (record.getContract_file().equals("")) {
                    showNoDataDialog();
                } else {
                    getDelegateActivity().addCommonFragment(OnlinePdfFragment.newInstance(record.getContract_file()));
                }
            }
        });
    }

    private void showNoDataDialog() {
        AlertDialog.createBuilder(getContext())
                .setContent(getString(R.string.AI_Mode_No_data))
                .setOk(getString(R.string.ok))
                .setContentGravity(Gravity.CENTER)
                .setContentLayoutMarginBottom(0)
                .setContentLayoutMarginTop(DensityUtils.dp2px(getContext(), 30))
                .setContentLayoutMinHeight(0)
                .setContentTextMinHeight(DensityUtils.dp2px(getContext(), 60))
                .setIsSuccess(false)
                .setIsShowContentImageView(true)
                .setOKListener(() -> {
                    getRecordData(false);
                    mBinding.refreshLayout.setNoMoreData(false);
                })
                .preBuilder().show();
    }


    private void initListView() {
        mAdapter = new AuthorizationRecordsAdapter(getActivity(), recordData);
        mBinding.recordListview.setAdapter(mAdapter);
    }

    private void initRefreshLayout() {
//        ClassicsHeader header = new CommonRefreshHeader(getContext());
//        header.setEnableLastTime(false);
//        header.setAccentColor(getResources().getColor(R.color.white));
//        mBinding.refreshLayout.setRefreshHeader(header);
//        ClassicsFooter footer = new CommonRefreshFooter(getContext());
//        footer.setAccentColor(getResources().getColor(R.color.white));
//        mBinding.refreshLayout.setRefreshFooter(footer);
        // 隐藏 Header（下拉刷新样式）
        mBinding.refreshLayout.setRefreshHeader(new ClassicsHeader(getContext()).setEnableLastTime(false)
                .setArrowBitmap(null).setFinishDuration(0).setProgressBitmap(null).setTextSizeTitle(0f));
        // 隐藏 Footer（上拉加载样式）
        mBinding.refreshLayout.setRefreshFooter(new ClassicsFooter(getContext()).setProgressBitmap(null)
                .setFinishDuration(0).setTextSizeTitle(0f));
        mBinding.refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshlayout) {
                Log.d(TAG, "onRefresh: ");
                mBinding.refreshLayout.finishRefresh(0);
                getRecordData(false);
            }
        });
        mBinding.refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(RefreshLayout refreshlayout) {
                Log.d(TAG, "onLoadMore: ");
                mBinding.refreshLayout.finishLoadMore(0);
                getRecordData(true);
            }
        });
    }

    private void showTerms(BalanceContractRecordsResponse.Record record) {
        ActionSheet.createBuilder(getContext(), getFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getString(R.string.cancel)))
                .setOtherButtonTitles(Local.s(getString(R.string.contract_preview)), Local.s(getString(R.string.emaldo_terms)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        DDLog.d(TAG, "onOtherButtonClick: " + index);
                        if (index == 0) {
                            if (record.getContract_file().equals("")) {
                                showNoDataDialog();
                            } else {
                                getDelegateActivity().addCommonFragment(OnlinePdfFragment.newInstance(record.getContract_file()));
                            }
                        } else if (index == 1) {
                            getDelegateActivity().addCommonFragment(OnlinePdfFragment.newInstance(record.getAttorney_auth()));
                        }
                    }
                })
                .show();
    }

    private void downloadFile(String url, String filePath, String fileName) {
        if (PermissionUtil.isStoragePermissionDeny(getContext())) {
            requestReadImagePermission();
            return;
        }

        showLoadingFragment(LoadingFragment.BLACK);
        File dir = new File(filePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
        request.setDestinationUri(Uri.parse("file://" + filePath + fileName));

        DownloadManager dm = (DownloadManager) getContext().getSystemService(Context.DOWNLOAD_SERVICE);
        if (dm != null) {
            dm.enqueue(request);
        }
    }

    private void getRecordData(boolean isLoadMore) {
        long lastTime = 0;
        if (isLoadMore && recordData.size() > 0) {
            lastTime = recordData.get(recordData.size() - 1).getCreate_time();
        }

        DinHome.getInstance().getBalanceContractRecords(lastTime,
                HomeManager.getInstance().getCurrentHome().getHomeID(), 20,
                new IDefaultCallBack2<BalanceContractRecordsResponse.ResultBean>() {
                    @Override
                    public void onSuccess(BalanceContractRecordsResponse.ResultBean resultBean) {
                        if (resultBean != null) {
                            List<BalanceContractRecordsResponse.Record> records = resultBean.getRecords();
                            if (isLoadMore) {
                                if (CollectionUtil.isListEmpty(records)) {
                                    mBinding.refreshLayout.finishLoadMore(0, true, true);
                                    return;
                                }
                                recordData.addAll(records);
//                                mBinding.refreshLayout.finishLoadMore(false);
                                mAdapter.notifyDataSetChanged();
                            } else {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                if (CollectionUtil.isListEmpty(records)) {
//                                    mBinding.refreshLayout.finishRefresh(true);
                                    showEmptyView();
                                    return;
                                }
                                mBinding.flEmpty.setVisibility(View.GONE);
//                                mBinding.refreshLayout.setVisibility(View.VISIBLE);
                                recordData.clear();
                                recordData.addAll(records);
                                mAdapter.notifyDataSetChanged();
//                                mBinding.refreshLayout.finishRefresh(true);
                            }
                        } else {
                            if (isLoadMore) {
                                mBinding.refreshLayout.finishLoadMore(0, true, true);
                            } else {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
//                                mBinding.refreshLayout.finishRefresh(false);
                            }
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        showErrorToast();
                        if (isLoadMore) {
//                            mBinding.refreshLayout.finishLoadMore(false);
                        } else {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
//                            mBinding.refreshLayout.finishRefresh(false);
                        }
                    }
                });
    }

    /**
     * 显示空视图
     */
    private void showEmptyView() {
        DDLog.i(TAG, "showEmptyView");
        mBinding.flEmpty.setVisibility(View.VISIBLE);
        mBinding.refreshLayout.setVisibility(View.GONE);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(DownloadAndZipResultEvent event) {
        DDLog.i(TAG,"DownloadAndZipResultEvent");
        boolean isSuccess = event.isSuccess();
        closeLoadingFragment();
        if (isSuccess) {
            getMainActivity().showTopToast("Downloaded file path: " + event.getPath());
        } else {
            showErrorToast();
        }
    }

}
