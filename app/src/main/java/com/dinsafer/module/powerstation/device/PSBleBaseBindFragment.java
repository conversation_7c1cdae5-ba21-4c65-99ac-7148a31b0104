package com.dinsafer.module.powerstation.device;

import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.IPluginEthernetCallback;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtBinderWrapper;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module.powerstation.settings.PSAdvancedSettingsFragment;
import com.dinsafer.module.powerstation.settings.network.PSConfigureNetworkResultFragment;
import com.dinsafer.module.powerstation.settings.network.PSNetworkBleScanFragment;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.BmtListFragment;
import com.dinsafer.module_bmt.add.BaseBmtBinder;
import com.dinsafer.module_bmt.add.BaseBmtNetworkManager;
import com.dinsafer.module_bmt.add.BmtHP5000Binder;
import com.dinsafer.module_bmt.add.BmtHP5000NetworkManager;
import com.dinsafer.module_bmt.add.BmtHP5001Binder;
import com.dinsafer.module_bmt.add.BmtHP5001NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerCore20Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore20NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerCore30Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore30NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerPulseBinder;
import com.dinsafer.module_bmt.add.BmtPowerPulseNetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerStoreBinder;
import com.dinsafer.module_bmt.add.BmtPowerStoreNetworkManager;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import io.reactivex.annotations.NonNull;

/**
 * 统一处理Bmt的修改网络或添加时蓝牙返回的结果
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/2/10 14:57
 */
public abstract class PSBleBaseBindFragment<V extends ViewDataBinding> extends MyBaseFragment<V>
        implements IPluginBindCallBack, BaseMainActivity.ILoadingCallBack, IPluginEthernetCallback {
    protected static final String KEY_FROM = "key_from";
    protected static final String KEY_DEVICE_ID = "deviceId";
    protected static final String KEY_IGNORE_DISCONNECT = "ignore_disconnect";
    protected static final int KEY_FROM_SCAN_ADD = 1;
    protected static final int KEY_FROM_CHANGE_NETWORK = 2;
    public final static int ERROR_CODE_ETHERNET = -4;
    public final static int SUCCEED_CODE_ETHERNET = 1;


    protected int mFrom;
    protected BmtBinderWrapper mBinder;
    protected String deviceID;
    protected String wifiSSid;
    protected String wifiPassword;
    protected String mModel;
    protected long mConfigTimeout = (30 + 10) * 1000L;

    protected boolean isEthernetConnect = false;

    @Override
    public void initData() {
        super.initData();
        // 2.0.4bmt会在添加时检查升级，所以添加的时候register超时时间改为4m30s-Android是很多指令封装在dincore执行的，再增加10s
        mConfigTimeout = KEY_FROM_SCAN_ADD == mFrom ? (270 + 10) * 1000L : (30 + 10) * 1000L;
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (KEY_FROM_SCAN_ADD != mFrom && KEY_FROM_CHANGE_NETWORK != mFrom) {
            DDLog.e(TAG, "Unknown from type: " + mFrom);
            showErrorToast();
            return;
        } else if (KEY_FROM_SCAN_ADD == mFrom
                && !(pluginBinder instanceof BmtHP5000Binder)
                && !(pluginBinder instanceof BmtHP5001Binder)
                && !(pluginBinder instanceof BmtPowerCore20Binder)
                && !(pluginBinder instanceof BmtPowerCore30Binder)
                && !(pluginBinder instanceof BmtPowerStoreBinder)
                && !(pluginBinder instanceof BmtPowerPulseBinder)) {
            DDLog.e(TAG, "Error BMTBinder binder.");
            showErrorToast();
            return;
        } else if (KEY_FROM_CHANGE_NETWORK == mFrom
                && !(pluginBinder instanceof BmtHP5000NetworkManager)
                && !(pluginBinder instanceof BmtHP5001NetworkManager)
                && !(pluginBinder instanceof BmtPowerCore20NetworkManager)
                && !(pluginBinder instanceof BmtPowerCore30NetworkManager)
                && !(pluginBinder instanceof BmtPowerStoreNetworkManager)
                && !(pluginBinder instanceof BmtPowerPulseNetworkManager)) {
            DDLog.e(TAG, "Error bmtNetworkManager binder.");
            showErrorToast();
            return;
        }

        if (KEY_FROM_CHANGE_NETWORK == mFrom) {
            mBinder = new BmtBinderWrapper((BaseBmtNetworkManager) pluginBinder);
        } else {
            mBinder = new BmtBinderWrapper((BaseBmtBinder) pluginBinder);
        }
        onBinderReady(mBinder);
    }

    @Override
    public void onEthernetCallback(int status, int eState) {
        DDLog.d(TAG, "onEthernetCallback. status: " + status + "  ethernetState: " + eState);
        if (!isEthernetConnect) {
            return;
        }
        if (status == 1 && eState == 1) {
            if (KEY_FROM_CHANGE_NETWORK == mFrom) {
                onBindResult(SUCCEED_CODE_ETHERNET, null);
            } else {
                mBinder.ethernetConnect();
            }
        } else {
            onBindResult(ERROR_CODE_ETHERNET, null);
        }
        isEthernetConnect = false;
    }

    @Override
    public void onBindResult(int i, String s) {
        if (!getDelegateActivity().isFragmentInTopExcludeLoading(PSBleBaseBindFragment.this)) {
            return;
        }
        if (i == 1) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (!PSBleBaseBindFragment.this.isAdded()) {
                        return;
                    }
                    closeLoadingFragmentWithCallBack();
                    onPreBindSuccess();
                    if (KEY_FROM_CHANGE_NETWORK == mFrom) {
                        BmtManager.getInstance().connectDevice(BmtManager.getInstance().getDeviceById(deviceID, mModel));
                        closeLoadingFragmentWithCallBack();
                        if (ActivityController.getInstance().getFragment(BmtListFragment.class) != null) {
                            getDelegateActivity().removeToFragment(BmtListFragment.class.getName());
                        } else {
                            getDelegateActivity().removeAllCommonFragment();
                        }
                    } else {
                        final String bmtId;
                        if (!TextUtils.isEmpty(s)) {
                            bmtId = s;
                        } else {
                            bmtId = deviceID;
                        }
                        DDLog.i(TAG, "添加BMT成功：" + bmtId);
                        getDelegateActivity().addCommonFragment(PSSetNameFragment.newInstance(bmtId, mModel));
                        EventBus.getDefault().post(new FinishAddBmtEvent());
                    }
                }
            });
        } else if (i == -70) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.tiggle_has_plug));
            if (KEY_FROM_CHANGE_NETWORK == mFrom) {
                getDelegateActivity().removeToFragment(PSNetworkBleScanFragment.class.getName());
            } else {
                getDelegateActivity().removeToFragment(PSBleScanFragment.class.getName());
            }
        } else if (i == -73) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.add_ipc_limit_error));
            if (KEY_FROM_CHANGE_NETWORK == mFrom) {
                getDelegateActivity().removeToFragment(PSNetworkBleScanFragment.class.getName());
            } else {
                getDelegateActivity().removeToFragment(PSBleScanFragment.class.getName());
            }
        } else if (i == 21 || i == 22) {

        } else if (i == 3) {
            DDLog.d(TAG, "固件恢复成功");
            closeLoadingFragmentWithCallBack();
            EventBus.getDefault().post(new FinishAddBmtEvent());
        } else {
            closeLoadingFragmentWithCallBack();
            onResultError(i);
        }
    }

    protected abstract void onBinderReady(@NonNull BmtBinderWrapper pluginBinder);

    protected void onPreBindSuccess() {
    }

    protected void onResultError(final int code) {
        DDLog.e("onResultError", "code = " + code + ", frome: " + mFrom + "， this: " + this);
        getDelegateActivity().addCommonFragment(PSConfigureNetworkResultFragment.newInstance(mFrom
                , code, deviceID, mModel, wifiSSid, wifiPassword
                , code == ERROR_CODE_ETHERNET ? PSConfigureNetworkResultFragment.TYPE_NETWORK_WIRED : PSConfigureNetworkResultFragment.TYPE_NETWORK_WIFI));

    }

    private AlertDialogV2 errorDialog;

    private void showErrorConnectDialog(int code) {
        if (errorDialog != null && errorDialog.isShowing()) {
            return;
        }
        final String msg = Local.s(BmtUtil.getConfigErrorMsgByCode(code));
        errorDialog = AlertDialogV2.createBuilder(getActivity())
                .setContent(msg)
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithCallBack(PSBleBaseBindFragment.this);
                        mBinder.setSsid(wifiSSid);
                        mBinder.setSsidPassword(wifiPassword);
                        mBinder.bindDevice(null);
                    }
                })
                .setCanCancel(true)
                .setCancel(getString(R.string.Cancel))
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        errorDialog.dismiss();
                    }
                })
                .preBuilder();
        errorDialog.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                errorDialog.dismiss();
                if (KEY_FROM_CHANGE_NETWORK == mFrom) {
                    getDelegateActivity().removeToFragment(PSAdvancedSettingsFragment.class.getName());
                } else {
                    getDelegateActivity().removeAllCommonFragment();
                }
            }
        });
        errorDialog.show();
    }

    @Override
    public void onTimeout() {
        onResultError(-999);
    }
}
