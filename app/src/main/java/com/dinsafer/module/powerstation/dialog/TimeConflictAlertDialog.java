package com.dinsafer.module.powerstation.dialog;

import android.content.Context;
import android.content.res.ColorStateList;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.dinsafer.dialog.BaseDialog;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogTimeConflictAlertBinding;
import com.dinsafer.util.ColorUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.ScreenUtils;

public class TimeConflictAlertDialog extends BaseDialog<DialogTimeConflictAlertBinding> {

    private Builder builder;

    public TimeConflictAlertDialog(@NonNull Context context, Builder builder) {
        super(context, R.style.CommonDialogStyle);
        this.builder = builder;
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = ScreenUtils.getScreenWidth(mContext) / 5 * 4;
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        layoutParams.gravity = Gravity.CENTER;
        getWindow().setAttributes(layoutParams);
    }

    @Override
    protected int layoutRes() {
        return R.layout.dialog_time_conflict_alert;
    }

    @Override
    protected void initView() {
        mBinding.tvTips.setLocalText(builder.tips);
        mBinding.tvNote.setText(builder.note);
        mBinding.tvNote.setVisibility(builder.showNote ? View.VISIBLE : View.GONE);
        mBinding.lcbConfirm.setLocalText(builder.confirmText);
        mBinding.lcbCancel.setLocalText(builder.cancelText);
        mBinding.lcbCancel.setTextColor(ColorUtil.adjustAlpha(mContext.getResources().getColor(R.color.color_white_01), 0.6f));
        mBinding.lcbConfirm.setOnClickListener(v -> builder.clickCallback.onConfirm(v, TimeConflictAlertDialog.this));
        mBinding.lcbCancel.setOnClickListener(v -> builder.clickCallback.onCancel(v, TimeConflictAlertDialog.this));
    }

    public static class Builder {

        private Context mContext;
        private String tips;
        private String note;
        private boolean showNote;
        private String confirmText;
        private String cancelText;
        private OnClickCallback clickCallback;

        public Builder(Context context) {
            mContext = context;
        }

        public Builder setTips(String tips) {
            this.tips = tips;
            return this;
        }

        public Builder setNote(String note) {
            this.note = note;
            return this;
        }

        public Builder setShowNote(boolean showNote) {
            this.showNote = showNote;
            return this;
        }

        public Builder setConfirmText(String confirmText) {
            this.confirmText = confirmText;
            return this;
        }

        public Builder setCancelText(String cancelText) {
            this.cancelText = cancelText;
            return this;
        }

        public Builder setClickCallback(OnClickCallback clickCallback) {
            this.clickCallback = clickCallback;
            return this;
        }

        public TimeConflictAlertDialog builder() {
            TimeConflictAlertDialog timeConflictAlertDialog = new TimeConflictAlertDialog(mContext, this);
            return timeConflictAlertDialog;
        }

        public interface OnClickCallback {
            void onConfirm(View view, TimeConflictAlertDialog dialog);

            void onCancel(View view, TimeConflictAlertDialog dialog);
        }
    }
}
