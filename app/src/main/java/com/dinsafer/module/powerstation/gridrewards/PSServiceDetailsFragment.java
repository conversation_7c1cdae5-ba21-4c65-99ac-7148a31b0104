package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsServiceDetailsBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSAdvancedSettingsAdapter;
import com.dinsafer.module.powerstation.adapter.PSContractedDeviceDetailsModel;
import com.dinsafer.module.powerstation.bean.PSAdvancedSettingsItemBean;
import com.dinsafer.util.DDDateUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/29
 * @author: create by Sydnee
 */
public class PSServiceDetailsFragment extends MyBaseFragment<FragmentPsServiceDetailsBinding> {

    private PSAdvancedSettingsAdapter mDeviceInfoAdapter;
    private Device mPSDevice;
    private String deviceId;
    private String mSubcategory;
    private String mcuId;
    private int deviceState;
    private long effectiveTime;
    private String authorizationCompanyName;

    public static PSServiceDetailsFragment newInstance(String deviceID, String subcategory, String mcuId, int deviceState, long effectiveTime, String authorizationCompanyName) {
        PSServiceDetailsFragment fragment = new PSServiceDetailsFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceID);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putString(PSKeyConstant.KEY_MCU_ID, mcuId);
        bundle.putInt(PSKeyConstant.KEY_STATUS, deviceState);
        bundle.putLong(PSKeyConstant.KEY_EFFECTIVE_TIME, effectiveTime);
        bundle.putString(PSKeyConstant.KEY_AUTHORIZATION_COMPANY_NAME, authorizationCompanyName);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_service_details;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.service_details));
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (null != bundle) {
            deviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
            mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
            mcuId = bundle.getString(PSKeyConstant.KEY_MCU_ID);
            effectiveTime = bundle.getLong(PSKeyConstant.KEY_EFFECTIVE_TIME);
            authorizationCompanyName = bundle.getString(PSKeyConstant.KEY_AUTHORIZATION_COMPANY_NAME);
            deviceState = bundle.getInt(PSKeyConstant.KEY_STATUS);
            mPSDevice = BmtManager.getInstance().getDeviceById(deviceId, mSubcategory);
            if (mPSDevice != null) {
                initRv();
            }
        }
    }

    private void initRv() {
        mBinding.rvDetails.setLayoutManager(new LinearLayoutManager(getContext()));
        mDeviceInfoAdapter = new PSAdvancedSettingsAdapter();
        mBinding.rvDetails.setAdapter(mDeviceInfoAdapter);

        List<PSAdvancedSettingsItemBean> data = new ArrayList<>();
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.device), BmtManager.getDeviceName(mPSDevice)));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_device_id), BmtUtil.isBmtDeviceHP5000(mPSDevice) ? mcuId : deviceId, true));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.product_name), BmtUtil.getDeviceDefaultName(mPSDevice)));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.power_of_attorney_company), authorizationCompanyName));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.effective_time), DDDateUtil.formatLong((effectiveTime / 1000000), DDDateUtil.LOCAL_YEAR_MONTH_DATE_FORMAT)));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.device_state), getStatusStr(deviceState)));
        mDeviceInfoAdapter.setNewData(data);
    }

    private String getStatusStr(int status) {
        String deviceStatusStr;
        switch (status) {
            case PSContractedDeviceDetailsModel.OFFLINE_STATUS:
                deviceStatusStr = getString(R.string.Offline);
                break;

            case PSContractedDeviceDetailsModel.OFF_GRID_STATUS:
                deviceStatusStr = getString(R.string.Offgrid);
                break;

            case PSContractedDeviceDetailsModel.ENABLING_STATUS:
                deviceStatusStr = getString(R.string.Enabling);
                break;

            default:
                deviceStatusStr = "";
                break;
        }
        return deviceStatusStr;
    }

}
