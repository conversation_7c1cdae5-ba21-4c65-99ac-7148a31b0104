package com.dinsafer.module.powerstation.impacts.tab;

import static com.dinsafer.module.powerstation.ChartDataUtil.DAY;
import static com.dinsafer.module.powerstation.ChartDataUtil.LIFETIME;
import static com.dinsafer.module.powerstation.ChartDataUtil.MONTH;
import static com.dinsafer.module.powerstation.ChartDataUtil.WEEK;
import static com.dinsafer.module.powerstation.ChartDataUtil.YEAR;

import android.os.Bundle;
import android.util.LruCache;
import android.view.View;
import android.view.WindowManager;

import androidx.databinding.DataBindingUtil;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsEconBinding;
import com.dinsafer.dinnet.databinding.FragmentPsEconSonBinding;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PSEcoAdapter;
import com.dinsafer.module.powerstation.adapter.PSEcoRateAdapter;
import com.dinsafer.module.powerstation.bean.PsEcoRateBean;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypeDialog;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypePopup;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.impacts.ImpactStrategiesFragment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.timeruler.TimePartUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.OnFragmentCreatedListener;
import com.dinsafer.util.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PSEcoFragment extends MyBaseFragment<FragmentPsEconBinding> implements IDeviceCallBack {

    private ElectricityCircleTypeDialog mCircleTypeDialog;
    private ElectricityCircleTypePopup mElectricityCircleTypePopup;
    private PSEcoAdapter mPSEcoAdapter;
    private String mInterval = WEEK;
    private int mOffSet = 0;
    private CycleType mCycleType = CycleType.WEEK;
    private int maxItem = 999;
    private int mCurrentItem = maxItem;
    private boolean canLoadData;
    private static OnFragmentCreatedListener mCreatedListener;
    private LruCache<Integer, Map<String, Object>> mDayCache;
    private LruCache<Integer, Map<String, Object>> mWeekCache;
    private LruCache<Integer, Map<String, Object>> mMonthCache;
    private LruCache<Integer, Map<String, Object>> mYearCache;

    public static PSEcoFragment newInstance(int position, OnFragmentCreatedListener createdListener) {
        PSEcoFragment fragment = new PSEcoFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_POSITION, position);
        fragment.setArguments(bundle);
        mCreatedListener = createdListener;
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_econ;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {

        mDayCache = new LruCache<>(20);
        mWeekCache = new LruCache<>(20);
        mMonthCache = new LruCache<>(20);
        mYearCache = new LruCache<>(20);

        super.initView(inflateView, savedInstanceState);
        ImpactStrategiesFragment.sPSDevice.registerDeviceCallBack(this);
        mPSEcoAdapter = new PSEcoAdapter(getContext());
        mBinding.infiniteViewPager.setAdapter(mPSEcoAdapter);
        mBinding.infiniteViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                mOffSet = position - maxItem;
                mCurrentItem = position;
                if (canLoadData) {
                    getData();
                }
                canLoadData = true;
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        mBinding.infiniteViewPager.setCurrentItem(999);
        Bundle bundle = getArguments();
        int position = bundle.getInt(PSKeyConstant.KEY_POSITION);
        if (mCreatedListener != null) {
            mCreatedListener.onCreated(inflateView, position);
        }
    }

    @Override
    public void onDestroyView() {
        if (ImpactStrategiesFragment.sPSDevice != null)
            ImpactStrategiesFragment.sPSDevice.unregisterDeviceCallBack(this);
        super.onDestroyView();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            getData();
        }
    }

    private void getData() {
        if (ImpactStrategiesFragment.sPSDevice != null) {
            Map<String, Object> result = null;
            switch (mCycleType) {
                case DAY:
                    result = mDayCache.get(mOffSet);
                    break;

                case WEEK:
                    result = mWeekCache.get(mOffSet);
                    break;

                case MONTH:
                    result = mMonthCache.get(mOffSet);
                    break;

                case YEAR:
                    result = mYearCache.get(mOffSet);
                    break;
            }
            if (result == null) {
                Map<String, Object> params = new HashMap<>();
                params.put(PSKeyConstant.CMD, BmtCmd.GET_STATS_ECO_V2);
                params.put(BmtDataKey.INTERVAL, mInterval);
                params.put(BmtDataKey.OFFSET, mOffSet);
                showTimeOutLoadinFramgment();
                ImpactStrategiesFragment.sPSDevice.submit(params);
            } else {
                setResultData(result);
            }
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvOption.setOnClickListener(v -> {
            showElectricityCircleTypePopup();
        });
    }

    /**
     * 显示周期类型弹窗
     */
    private void showElectricityCircleTypePopup() {
        if (mElectricityCircleTypePopup == null) {
            mElectricityCircleTypePopup = new ElectricityCircleTypePopup(getContext(), ElectricityCircleTypePopup.IMPACTS_STRATEGIES_ECO_TYPE);
            mElectricityCircleTypePopup.setTypeSelectedListener((type, cycleType) -> {
                mBinding.tvOption.setLocalText(type);
                mCycleType = cycleType;
                mOffSet = 0;
                switch (mCycleType) {

                    case DAY:
                        mInterval = DAY;
                        break;

                    case WEEK:
                        mInterval = WEEK;
                        break;

                    case MONTH:
                        mInterval = MONTH;
                        break;

                    case YEAR:
                        mInterval = YEAR;
                        break;

                    case LIFETIME:
                        mInterval = LIFETIME;
                        break;
                }
                mBinding.infiniteViewPager.setCurrentItem(maxItem);
                mBinding.infiniteViewPager.setCanSlide(mCycleType != CycleType.LIFETIME);
                getData();
            });
            mElectricityCircleTypePopup.setOnDismissListener(() -> backgroundAlpha(1f));
        }
        if (mElectricityCircleTypePopup != null && !mElectricityCircleTypePopup.isShowing()) {
            mElectricityCircleTypePopup.showAtLocation(mBinding.tvOption,
                    mBinding.tvOption.getWidth() -
                            DensityUtil.dp2px(getContext(), 136), 0);
            backgroundAlpha(0.5f);
        }
    }

    private void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp = getDelegateActivity().getWindow().getAttributes();
        lp.alpha = f;
        getDelegateActivity().getWindow().setAttributes(lp);
    }


    private void setEmptyView(boolean isEmpty) {
        mBinding.layoutEmpty.getRoot().setVisibility(isEmpty ? View.VISIBLE : View.GONE);
    }

    private void setResultData(Map<String, Object> result) {
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (result == null) return;
        int percentage = DeviceHelper.getInt(result, BmtDataKey.DATA, 0);
        long gmtTime = (long) result.get(BmtDataKey.START_TIME) * 1000;
        String timezone = (String) result.get(BmtDataKey.TIMEZONE);
        String time = ChartDataUtil.getResetTime(gmtTime, timezone, mCycleType);
        if (mCycleType == CycleType.DAY) {
            if (mOffSet == 0) {
                mBinding.tvTime.setLocalText(getString(R.string.electricity_today));
            } else if (mOffSet == -1) {
                mBinding.tvTime.setLocalText(getString(R.string.electricity_yesterday));
            } else {
                mBinding.tvTime.setLocalText(time);
            }
        } else {
            mBinding.tvTime.setLocalText(time);
        }
        View view = mPSEcoAdapter.getCurrentView(mBinding.infiniteViewPager);
        if (view == null) {
            DDLog.e(TAG, "获取视图出错");
            return;
        }
        FragmentPsEconSonBinding binding = DataBindingUtil.bind(view);
        binding.epvPercentage.setPercentage(percentage);
        int leafCount = ChartDataUtil.getLeafCount(percentage);
        PSEcoRateAdapter rateAdapter = (PSEcoRateAdapter) binding.rvRate.getAdapter();
        List<PsEcoRateBean> data = rateAdapter.getData();
        for (int i = 0; i < data.size(); i++) {
            data.get(i).setRated(i < leafCount);
        }
        rateAdapter.notifyDataSetChanged();
        String note = Local.s(getString(R.string.electricity_stats_note)).replace(getString(R.string.impact_strategies_hashtag_timezone), TimePartUtil.getTimezoneDisplayName(timezone));
        binding.tvNote.setText(note);
        binding.layoutEmpty.getRoot().setVisibility(View.GONE);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (ImpactStrategiesFragment.sPSDevice != null
                && StringUtil.isNotEmpty(ImpactStrategiesFragment.sPSDevice.getId())
                && StringUtil.isNotEmpty(deviceId)
                && deviceId.equals(ImpactStrategiesFragment.sPSDevice.getId())
                && subCategory.equals(ImpactStrategiesFragment.sPSDevice.getSubCategory())) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        switch (cmd) {
                            case DsCamCmd.GET_STATS_ECO:
                                switch (mCycleType) {
                                    case DAY:
                                        Map<String, Object> resultCache = mDayCache.get(-1);
                                        if (resultCache != null) {
                                            long cacheStartTime = DeviceHelper.getLong(resultCache, BmtDataKey.START_TIME, 0);
                                            long startTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0);
                                            // offset(0) 开始时间减缓存offset(-1)开始时间大于一天要清除全部缓存
                                            if (startTime - cacheStartTime > 86400) {
                                                mDayCache.evictAll();
                                            }
                                        }
                                        if (mOffSet < 0) {
                                            mDayCache.put(mOffSet, result);
                                        }
                                        break;

                                    case WEEK:
                                        mWeekCache.put(mOffSet, result);
                                        break;

                                    case MONTH:
                                        mMonthCache.put(mOffSet, result);
                                        break;

                                    case YEAR:
                                        mYearCache.put(mOffSet, result);
                                        break;
                                }
                                setResultData(result);
                                break;
                        }
                    }
                });
            } else {
                switch (cmd) {
                    case DsCamCmd.GET_STATS_ECO:
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                        View view = mPSEcoAdapter.getCurrentView(mBinding.infiniteViewPager);
                        if (view == null) {
                            DDLog.e(TAG, "获取视图出错");
                            return;
                        }
                        FragmentPsEconSonBinding binding = DataBindingUtil.bind(view);
                        binding.layoutEmpty.getRoot().setVisibility(View.VISIBLE);
                        break;
                }
            }
        }
    }
}
