package com.dinsafer.module.powerstation.ev;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsScheduledChargeBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class PSScheduledChargeFragment extends MyBaseFragment<FragmentPsScheduledChargeBinding>
        implements IDeviceCallBack {

    private static final String KEY_WEEKDAYS_DATA = "key_weekdays_data";
    private static final String KEY_WEEKEND_DATA = "key_weekend_data";

    private ArrayList<Integer> mWeekdays;
    private ArrayList<Integer> mWeekend;

    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;

    public static PSScheduledChargeFragment newInstance(String deviceId, String subcategory) {
        PSScheduledChargeFragment fragment = new PSScheduledChargeFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_scheduled_charge;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        initTitle();
        getChargeScheduled();
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    private void initTitle() {
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.ps_ev_smart_charge_title_3));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarRightText.setLocalText(getString(R.string.ps_ev_save));
        mBinding.commonBar.commonBarRightText.setTextColor(getResources().getColor(R.color.color_brand_text));
        mBinding.commonBar.commonBarRightText.setBackgroundResource(0);
        mBinding.commonBar.commonBarRightText.setVisibility(View.VISIBLE);
        // 保存
        mBinding.commonBar.commonBarRightText.setOnClickListener(v -> {
            setEvChargingMode();
        });
    }

    /**
     * 获取EV充电模式-预设时间设置(0xa021)
     */
    private void getChargeScheduled() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE);
            mPSDevice.submit(map);
        }
    }

    /**
     * 设置EV充电模式(0xa022)
     */
    private void setEvChargingMode() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.SET_EV_CHARGING_MODE);
            map.put(BmtDataKey.EV_CHARGING_MODE, 3);
            map.put(BmtDataKey.WEEKDAYS, mBinding.scheduleChargeView.getWeekdaysData());
            map.put(BmtDataKey.WEEKEND, mBinding.scheduleChargeView.getWeekendsData());
            showTimeOutLoadinFramgment();
            mPSDevice.submit(map);
        }
    }

    private void setChargeData() {
        List<Integer> indexList = new ArrayList<>();
        if (CollectionUtil.isListNotEmpty(mWeekdays)) {
            for (int i = 0; i < mWeekdays.size(); i++) {
                int applied = mWeekdays.get(i);
                if (applied == 1) {
                    indexList.add(2 * i);
                }
            }
        }

        if (CollectionUtil.isListNotEmpty(mWeekend)) {
            for (int i = 0; i < mWeekend.size(); i++) {
                int applied = mWeekend.get(i);
                if (applied == 1) {
                    indexList.add(2 * i + 1);
                }
            }
        }
        mBinding.scheduleChargeView.setScheduledApplied(indexList);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE:
                                mWeekdays = (ArrayList) MapUtils.get(result, BmtDataKey.WEEKDAYS, new ArrayList<>());
                                mWeekend = (ArrayList) MapUtils.get(result, BmtDataKey.WEEKEND, new ArrayList<>());
                                setChargeData();
                                break;

                            case BmtCmd.SET_EV_CHARGING_MODE:
                                removeSelf();
                                break;
                        }
                    } else {

                    }
                }
            });
        }
    }
}
