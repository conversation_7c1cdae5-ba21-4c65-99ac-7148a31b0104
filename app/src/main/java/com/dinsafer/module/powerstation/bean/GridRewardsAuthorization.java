package com.dinsafer.module.powerstation.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class GridRewardsAuthorization implements Parcelable {

    private String companyName;
    private String organizationNumber;
    private String address;
    private String emailAddress;

    public GridRewardsAuthorization(String companyName, String organizationNumber, String address, String emailAddress) {
        this.companyName = companyName;
        this.organizationNumber = organizationNumber;
        this.address = address;
        this.emailAddress = emailAddress;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getOrganizationNumber() {
        return organizationNumber;
    }

    public void setOrganizationNumber(String organizationNumber) {
        this.organizationNumber = organizationNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.companyName);
        dest.writeString(this.organizationNumber);
        dest.writeString(this.address);
        dest.writeString(this.emailAddress);
    }

    public void readFromParcel(Parcel source) {
        this.companyName = source.readString();
        this.organizationNumber = source.readString();
        this.address = source.readString();
        this.emailAddress = source.readString();
    }

    protected GridRewardsAuthorization(Parcel in) {
        this.companyName = in.readString();
        this.organizationNumber = in.readString();
        this.address = in.readString();
        this.emailAddress = in.readString();
    }

    public static final Creator<GridRewardsAuthorization> CREATOR = new Creator<GridRewardsAuthorization>() {
        @Override
        public GridRewardsAuthorization createFromParcel(Parcel source) {
            return new GridRewardsAuthorization(source);
        }

        @Override
        public GridRewardsAuthorization[] newArray(int size) {
            return new GridRewardsAuthorization[size];
        }
    };
}
