package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.RectF;
import android.graphics.Shader;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.Nullable;

import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

import com.dinsafer.dinnet.R;

/**
 * 电流图
 */
public class CurrentDiagramView extends View {

    // 左边坐标
    float leftPointX;
    float leftPointY;
    // 上边坐标
    float topPointX;
    float topPointY;
    // 右边坐标
    float rightPointX;
    float rightPointY;
    // 下边坐标
    float bottomPointX;
    float bottomPointY;
    // 左下汇合点坐标
    float leftBottomX;
    float leftBottomY;
    // 左下第一条坐标
    float leftBottomX1;
    float leftBottomY1;
    // 左下第二条坐标
    float leftBottomX2;
    float leftBottomY2;
    // 左下控制点坐标
    float leftBottomQuadX;
    float leftBottomQuadY;

    private float mRightX;

    private RectF mRect;
    private int mColor = Color.BLACK;
    private int mRadius = 60;
    private int mActualRadius;
    private Paint mPaint;
    private Path mCurrentPath;

    private Paint mAnimPaint;
    private Paint mAnimPaint1;
    private Paint mAnimPaint2;
    private Paint mAnimPaint3;
    private Paint mAnimPaint4;
    private Paint mAnimPaint5;

    private Path firsPath; // 用于截取path
    private Path secondPath; // 用于截取path
    private Path thirdPath; // 用于截取path
    private Path forthPath; // 用于截取path

    // 第一条动画路径
    private PathMeasure mPathMeasure1;
    private Path mPath1;
    private Path mDst1;
    private float mLength1;
    private float mAnimatorValue1;

    // 第二条动画路径
    private PathMeasure mPathMeasure2;
    private Path mPath2;
    private Path mDst2;
    private float mLength2;
    private float mAnimatorValue2;

    // 第三条动画路径
    private PathMeasure mPathMeasure3;
    private Path mPath3;
    private Path mDst3;
    private float mLength3;
    private float mAnimatorValue3;

    // 第四条动画路径
    private PathMeasure mPathMeasure4;
    private Path mPath4;
    private Path mDst4;
    private float mLength4;
    private float mAnimatorValue4;

    // 第四条动画路径
    private PathMeasure mPathMeasure5;
    private Path mPath5;
    private Path mDst5;
    private float mLength5;
    private float mAnimatorValue5;

    // 第一限项控制点
    private float arcQuadX1;
    private float arcQuadY1;
    private float quadX1;
    private float quadY1;

    // 第二限项控制点
    private float arcQuadX2;
    private float arcQuadY2;
    private float quadX2;
    private float quadY2;

    // 第三限项控制点
    private float arcQuadX3;
    private float arcQuadY3;
    private float quadX3;
    private float quadY3;

    // 第四限项控制点
    private float arcQuadX4;
    private float arcQuadY4;
    private float quadX4;
    private float quadY4;

    private int mPathColor = Color.BLACK; // 路径颜色
    private int mAnimPathColor = Color.RED;  // 动画路径颜色
    private int mPathWidth = 3;  // 路径宽度
    private int offsetX = 0; // 中心X偏移量
    private int offsetY = 0; // 中心Y偏移量

    private ValueAnimator mValueAnimator;

    private PowerType mPowerType = PowerType.SUN_SUPPLY;

    private final int REFRESH_CURRENT = 0X01;

    // 动画路径的矩形
    private RectF rectF1;
    private RectF rectF2;
    private RectF rectF3;
    private RectF rectF4;
    private RectF rectF5;

    // 是否绘制路径
    private boolean isDrawPath1 = false;
    private boolean isDrawPath2 = false;
    private boolean isDrawPath3 = false;
    private boolean isDrawPath4 = false;
    private boolean isDrawPath5 = false;

    private boolean hasSetMRect;

    private int startColor = Color.parseColor("#104485E8");
    private int endColor = Color.parseColor("#4485E8");

    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mAnimRunnable = new Runnable() {
        @Override
        public void run() {
            if (mValueAnimator != null) {
                mValueAnimator.start();
            }
        }
    };

    public CurrentDiagramView(Context context) {
        this(context, null);
    }

    public CurrentDiagramView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CurrentDiagramView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CurrentDiagramView);
        mActualRadius = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_radius, 50);
        mPathColor = typedArray.getColor(R.styleable.CurrentDiagramView_path_color, mPathColor);
        mAnimPathColor = typedArray.getColor(R.styleable.CurrentDiagramView_anim_path_color, mAnimPathColor);
        mPathWidth = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_path_width, dp2px(context, 3));
        offsetX = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_x, 0);
        offsetY = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_y, 0);
        typedArray.recycle();
        init();
    }

    private void init() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStrokeWidth(mPathWidth);
        mPaint.setStyle(Paint.Style.STROKE);

        mPaint.setColor(mPathColor);
        mCurrentPath = new Path();

        mAnimPaint = new Paint();
        mAnimPaint.setAntiAlias(true);
        mAnimPaint.setStyle(Paint.Style.STROKE);
        mAnimPaint.setColor(mAnimPathColor);
        mAnimPaint.setStrokeWidth(mPathWidth);
        mAnimPaint.setStrokeCap(Paint.Cap.ROUND);

        mAnimPaint1 = new Paint();
        mAnimPaint1.setAntiAlias(true);
        mAnimPaint1.setStyle(Paint.Style.STROKE);
        mAnimPaint1.setColor(mAnimPathColor);
        mAnimPaint1.setStrokeWidth(mPathWidth);
        mAnimPaint1.setStrokeCap(Paint.Cap.ROUND);


        mAnimPaint2 = new Paint();
        mAnimPaint2.setAntiAlias(true);
        mAnimPaint2.setStyle(Paint.Style.STROKE);
        mAnimPaint2.setColor(mAnimPathColor);
        mAnimPaint2.setStrokeWidth(mPathWidth);
        mAnimPaint2.setStrokeCap(Paint.Cap.ROUND);

        mAnimPaint3 = new Paint();
        mAnimPaint3.setAntiAlias(true);
        mAnimPaint3.setStyle(Paint.Style.STROKE);
        mAnimPaint3.setColor(mAnimPathColor);
        mAnimPaint3.setStrokeWidth(mPathWidth);
        mAnimPaint3.setStrokeCap(Paint.Cap.ROUND);

        mAnimPaint4 = new Paint();
        mAnimPaint4.setAntiAlias(true);
        mAnimPaint4.setStyle(Paint.Style.STROKE);
        mAnimPaint4.setColor(mAnimPathColor);
        mAnimPaint4.setStrokeWidth(mPathWidth);
        mAnimPaint4.setStrokeCap(Paint.Cap.ROUND);

        mAnimPaint5 = new Paint();
        mAnimPaint5.setAntiAlias(true);
        mAnimPaint5.setStyle(Paint.Style.STROKE);
        mAnimPaint5.setColor(mAnimPathColor);
        mAnimPaint5.setStrokeWidth(mPathWidth);
        mAnimPaint5.setStrokeCap(Paint.Cap.ROUND);

        rectF1 = new RectF();
        rectF2 = new RectF();
        rectF3 = new RectF();
        rectF4 = new RectF();
        rectF5 = new RectF();
        mRect = new RectF();

        firsPath = new Path();
        secondPath = new Path();
        thirdPath = new Path();
        forthPath = new Path();

        initPath1();
        initPath2();
        initPath3();
        initPath4();
        initPath5();

        mValueAnimator = ValueAnimator.ofFloat(0, 1);
        mValueAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mHandler.postDelayed(mAnimRunnable, 500);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                float value = (float) valueAnimator.getAnimatedValue();
                mAnimatorValue1 = value;
                mAnimatorValue2 = value;
                mAnimatorValue3 = value;
                mAnimatorValue4 = value;
                mAnimatorValue5 = value;
                invalidate();
            }
        });
        mValueAnimator.setDuration(3000);
        mValueAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        mValueAnimator.start();
    }

    /**
     * 第一条路径
     */
    private void initPath1() {
        mPathMeasure1 = new PathMeasure();
        mPath1 = new Path();
        mDst1 = new Path();
    }

    /**
     * 第二条路径
     */
    private void initPath2() {
        mPathMeasure2 = new PathMeasure();
        mPath2 = new Path();
        mDst2 = new Path();
    }

    /**
     * 第三条路径
     */
    private void initPath3() {
        mPathMeasure3 = new PathMeasure();
        mPath3 = new Path();
        mDst3 = new Path();
    }

    /**
     * 第四条路径
     */
    private void initPath4() {
        mPathMeasure4 = new PathMeasure();
        mPath4 = new Path();
        mDst4 = new Path();
    }

    /**
     * 第五条路径
     */
    private void initPath5() {
        mPathMeasure5 = new PathMeasure();
        mPath5 = new Path();
        mDst5 = new Path();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        //设置宽高,默认200dp
        int defaultSize = dp2px(getContext(), 200);
        setMeasuredDimension(measureWidth(widthMeasureSpec, defaultSize),
                measureHeight(heightMeasureSpec, defaultSize));
    }

    /**
     * 测量宽
     *
     * @param measureSpec
     * @param defaultSize
     * @return
     */
    private int measureWidth(int measureSpec, int defaultSize) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = defaultSize + getPaddingLeft() + getPaddingRight();
            if (specMode == MeasureSpec.AT_MOST) {
                result = Math.min(result, specSize);
            }
        }
        result = Math.max(result, getSuggestedMinimumWidth());
        return result;
    }

    /**
     * 测量高
     *
     * @param measureSpec
     * @param defaultSize
     * @return
     */
    private int measureHeight(int measureSpec, int defaultSize) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = defaultSize + getPaddingTop() + getPaddingBottom();
            if (specMode == MeasureSpec.AT_MOST) {
                result = Math.min(result, specSize);
            }
        }
        result = Math.max(result, getSuggestedMinimumHeight());
        return result;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //计算圆的圆心
        int cx = getPaddingLeft() + (getWidth() - getPaddingLeft() - getPaddingRight()) / 2 - offsetX;
        int cy = getPaddingTop() + (getHeight() - getPaddingTop() - getPaddingBottom()) / 2 - offsetY;
        mRightX = getWidth() - getPaddingLeft() - 3;
//        if (mRect == null) {
//            mRect = new RectF(cx - mActualRadius, cy - mActualRadius, cx + mActualRadius, cy + mActualRadius);
//        }
        if (!hasSetMRect) {
            mRect.left = cx - mActualRadius;
            mRect.top = cy - mActualRadius;
            mRect.right = cx + mActualRadius;
            mRect.bottom = cy + mActualRadius;
            hasSetMRect = true;
        }

        drawPathTB(canvas, cx, cy);
        if (isDrawPath1)
            drawPath1(canvas, cx, cy);
        if (isDrawPath2)
            drawPath2(canvas, cx, cy);
        if (isDrawPath3)
            drawPath3(canvas, cx, cy);
        if (isDrawPath4)
            drawPath4(canvas, cx, cy);
        if (isDrawPath5)
            drawPath5(canvas, cx, cy);
    }

    /**
     * 第一条路径动画
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath1(Canvas canvas, int cx, int cy) {
        if (mPath1.isEmpty()) {
            if (mPowerType == PowerType.SUN_SUPPLY) { // 太阳能
                mPath1.moveTo(cx, 0);
                mPath1.lineTo(topPointX, topPointY);
                mPath1.quadTo(quadX4, quadY4, leftPointX, leftPointY);
                mPath1.lineTo(0, cy);

            } else {  // 城市
                mPath1.moveTo(mRightX, 0);
                mPath1.lineTo(mRightX, cy);
                mPath1.lineTo(rightPointX, rightPointY);
                float qx = (quadX1 + rightPointX) / 2;
                float qy = (quadY1 + rightPointY) / 2;
                mPath1.quadTo(qx, qy, arcQuadX1, arcQuadY1);
                mPath1.arcTo(mRect, 315, -90, false);
                float qx2 = (quadX4 + leftPointX) / 2;
                float qy2 = (quadY4 + leftPointY) / 2;
                mPath1.quadTo(qx2, qy2, leftPointX, leftPointY);
                mPath1.lineTo(0, leftPointY);
            }
            mPathMeasure1.setPath(mPath1, false);
            mLength1 = mPathMeasure1.getLength();
        }
        mDst1.reset();
        float stop = mLength1 * mAnimatorValue1;
        float start = (float) (stop - ((0.5 - Math.abs(mAnimatorValue1 - 0.5)) * mLength1));
        mPathMeasure1.getSegment(start, stop + 15, mDst1, true);
        mDst1.computeBounds(rectF1, true);
        float xDistance = rectF1.right - rectF1.left;
        float yDistance = rectF1.bottom - rectF1.top;
        if (yDistance > xDistance) {
            mAnimPaint1.setShader(new LinearGradient(0, rectF1.top, 0, rectF1.bottom,
                    startColor, endColor, Shader.TileMode.CLAMP));
        } else {
            boolean isLeft = rectF1.right > cx;
            mAnimPaint1.setShader(new LinearGradient(isLeft ? rectF1.left : rectF1.right, 0,
                    isLeft ? rectF1.right : rectF1.left, 0,
                    startColor, endColor, Shader.TileMode.CLAMP));
        }
        canvas.drawPath(mDst1, mAnimPaint1);
    }

    /**
     * 第二条动画路径
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath2(Canvas canvas, int cx, int cy) {
        if (mPath2.isEmpty()) {
            if (mPowerType == PowerType.SUN_SUPPLY) {  // 太阳能
                mPath2.moveTo(cx, 0);
                mPath2.lineTo(topPointX, topPointY);
                float qx = (quadX4 + topPointX) / 2;
                float qy = (quadY4 + topPointY) / 2;
                mPath2.quadTo(qx, qy, arcQuadX4, arcQuadY4);
                mPath2.arcTo(mRect, 225, -65, false);
            } else {  // 城市供电
                mPath2.moveTo(mRightX, 0);
                mPath2.lineTo(mRightX, cy);
                mPath2.lineTo(rightPointX, rightPointY);
                float qx = (quadX2 + rightPointX) / 2;
                float qy = (quadY2 + rightPointY) / 2;
                mPath2.quadTo(qx, qy, arcQuadX2, arcQuadY2);
                mPath2.arcTo(mRect, 45, 65, false);
            }
            mPath2.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX, leftBottomY);
            float endY = getHeight();
            float endX = (endY - cy) / (leftBottomY - cy) * (leftBottomX - cx) + cx;
            mPath2.lineTo(endX, endY);
            mPathMeasure2.setPath(mPath2, false);
            mLength2 = mPathMeasure2.getLength();
        }
        mDst2.reset();
        float stop = mLength2 * mAnimatorValue2;
        float start = (float) (stop - ((0.5 - Math.abs(mAnimatorValue2 - 0.5)) * mLength2));
        mPathMeasure2.getSegment(start, stop + 15, mDst2, true);
        mDst2.computeBounds(rectF2, true);
        float xDistance = rectF2.right - rectF2.left;
        float yDistance = rectF2.bottom - rectF2.top;
        if (yDistance > xDistance) {
            mAnimPaint2.setShader(new LinearGradient(0, rectF2.top, 0, rectF2.bottom,
                    startColor, endColor, Shader.TileMode.CLAMP));
        } else {
            boolean isLeft = rectF2.right > cx;
            mAnimPaint2.setShader(new LinearGradient(isLeft ? rectF2.left : rectF2.right, 0,
                    isLeft ? rectF2.right : rectF2.left, 0,
                    startColor, endColor, Shader.TileMode.CLAMP));
        }
        canvas.drawPath(mDst2, mAnimPaint2);
    }

    /**
     * 第三条动画路径
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath3(Canvas canvas, int cx, int cy) {
        if (mPath3.isEmpty()) {
            if (mPowerType == PowerType.SUN_SUPPLY) {  // 太阳能
                mPath3.moveTo(cx, 0);
                mPath3.lineTo(topPointX, topPointY);
                float qx = (quadX4 + topPointX) / 2;
                float qy = (quadY4 + topPointY) / 2;
                mPath3.quadTo(qx, qy, arcQuadX4, arcQuadY4);
                mPath3.arcTo(mRect, 225, -90, false);
                float qx1 = (quadX3 + bottomPointX) / 2;
                float qy2 = (quadY3 + bottomPointY) / 2;
                mPath3.quadTo(qx1, qy2, bottomPointX, bottomPointY);
            } else {  // 城市供电
                mPath3.moveTo(mRightX, 0);
                mPath3.lineTo(mRightX, cy);
                mPath3.lineTo(rightPointX, rightPointY);
                mPath3.quadTo(quadX2, quadY2, bottomPointX, bottomPointY);
            }
            mPath3.lineTo(bottomPointX, getHeight() - 20);
            mPathMeasure3.setPath(mPath3, false);
            mLength3 = mPathMeasure3.getLength();
        }
        mDst3.reset();
        float stop = mLength3 * mAnimatorValue3;
        float start = (float) (stop - ((0.5 - Math.abs(mAnimatorValue3 - 0.5)) * mLength3));
        mPathMeasure3.getSegment(start, stop + 15, mDst3, true);

        mDst3.computeBounds(rectF3, true);
        float xDistance = rectF3.right - rectF3.left;
        float yDistance = rectF3.bottom - rectF3.top;
        if (yDistance > xDistance) {
            mAnimPaint3.setShader(new LinearGradient(0, rectF3.top, 0, rectF3.bottom,
                    startColor, endColor, Shader.TileMode.CLAMP));
        } else {
            boolean isLeft = rectF3.right > cx;
            mAnimPaint3.setShader(new LinearGradient(isLeft ? rectF3.left : rectF3.right, 0,
                    isLeft ? rectF3.right : rectF3.left, 0,
                    startColor, endColor, Shader.TileMode.CLAMP));
        }
        canvas.drawPath(mDst3, mAnimPaint3);
    }

    /**
     * 第四条路径动画
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath4(Canvas canvas, int cx, int cy) {
        if (mPath4.isEmpty()) {
            if (mPowerType == PowerType.SUN_SUPPLY) { // 太阳能
                mPath4.moveTo(cx, 0);
                mPath4.lineTo(topPointX, topPointY);
                mPath4.quadTo(quadX1, quadY1, rightPointX, rightPointY);
                mPath4.lineTo(mRightX, cy);
            } else {  // 城市
                mPath4.moveTo(mRightX, 0);
            }
            mPath4.lineTo(mRightX, getHeight() - 20);
            mPathMeasure4.setPath(mPath4, false);
            mLength4 = mPathMeasure4.getLength();
        }
        mDst4.reset();
        float stop = mLength4 * mAnimatorValue4;
        float start = (float) (stop - ((0.5 - Math.abs(mAnimatorValue4 - 0.5)) * mLength4));
        mPathMeasure4.getSegment(start, stop + 15, mDst4, true);
        mDst4.computeBounds(rectF4, true);
        float xDistance = rectF4.right - rectF4.left;
        float yDistance = rectF4.bottom - rectF4.top;
        if (yDistance > xDistance) {
            mAnimPaint4.setShader(new LinearGradient(0, rectF4.top, 0, rectF4.bottom,
                    startColor, endColor, Shader.TileMode.CLAMP));
        } else {
            boolean isLeft = rectF4.right > cx;
            mAnimPaint4.setShader(new LinearGradient(isLeft ? rectF4.left : rectF4.right, 0,
                    isLeft ? rectF4.right : rectF4.left, 0,
                    startColor, endColor, Shader.TileMode.CLAMP));
        }
        canvas.drawPath(mDst4, mAnimPaint4);
    }

    /**
     * 第四条路径动画
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath5(Canvas canvas, int cx, int cy) {
        if (mPath5.isEmpty()) {
            if (mPowerType == PowerType.SUN_SUPPLY) { // 太阳能
                mPath5.moveTo(cx, 0);
                mPath5.lineTo(topPointX, topPointY);
                mPath5.quadTo(quadX1, quadY1, rightPointX, rightPointY);
                mPath5.lineTo(mRightX, cy);
                mPath5.lineTo(mRightX, 0);
                mPathMeasure5.setPath(mPath5, false);
                mLength5 = mPathMeasure5.getLength();
            } else {  // 城市

            }

        }
        if (!mPath5.isEmpty()) {
            mDst5.reset();
            float stop = mLength5 * mAnimatorValue5;
            float start = (float) (stop - ((0.5 - Math.abs(mAnimatorValue5 - 0.5)) * mLength5));
            mPathMeasure5.getSegment(start, stop + 15, mDst5, true);
            mDst5.computeBounds(rectF5, true);
            float xDistance = rectF5.right - rectF5.left;
            float yDistance = rectF5.bottom - rectF5.top;
            if (yDistance > xDistance) {
                mAnimPaint5.setShader(new LinearGradient(0, rectF5.top, 0, rectF5.bottom,
                        startColor, endColor, Shader.TileMode.CLAMP));
            } else {
                boolean isLeft = rectF5.right > cx;
                mAnimPaint5.setShader(new LinearGradient(isLeft ? rectF5.left : rectF5.right, 0,
                        isLeft ? rectF5.right : rectF5.left, 0,
                        startColor, endColor, Shader.TileMode.CLAMP));
            }
            canvas.drawPath(mDst5, mAnimPaint5);
        }
    }

    /**
     * 画拓补图
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPathTB(Canvas canvas, int cx, int cy) {
        if (mCurrentPath.isEmpty()) {
            mCurrentPath.addCircle(cx, cy, mActualRadius, Path.Direction.CCW);
            // 左边坐标
            leftPointX = (float) (cx - (2 * mActualRadius));
            leftPointY = cy;
            // 上边坐标
            topPointX = cx;
            topPointY = (float) (cy - (2 * mActualRadius));
            // 右边坐标
            rightPointX = (float) (cx + (2 * mActualRadius));
            rightPointY = cy;
            // 下边坐标
            bottomPointX = cx;
            bottomPointY = (float) (cy + (2 * mActualRadius));

            // 绘制路径
            mCurrentPath.moveTo(topPointX, 0);
            mCurrentPath.lineTo(topPointX, topPointY);
            // 第一限项控制点
            double arcQuad1 = (2 * Math.PI / 360) * 45;
            arcQuadX1 = (float) (cx + Math.sin(arcQuad1) * (mActualRadius));
            arcQuadY1 = (float) (cy - Math.cos(arcQuad1) * (mActualRadius));
            // 贝塞尔公式算得
            quadX1 = (float) ((arcQuadX1 - 0.25 * topPointX - 0.25 * rightPointX) / 0.5);
            quadY1 = (float) ((arcQuadY1 - 0.25 * topPointY - 0.25 * rightPointY) / 0.5);
            mCurrentPath.quadTo(quadX1, quadY1, rightPointX, rightPointY);
            if (firsPath.isEmpty()) {
                firsPath.moveTo(topPointX, topPointY);
                firsPath.quadTo(quadX1, quadY1, rightPointX, rightPointY);
            }
            mCurrentPath.lineTo(getWidth() - 1, rightPointY);
            mCurrentPath.moveTo(rightPointX, rightPointY);
            // 第二限项控制点
            double arcQuad2 = (2 * Math.PI / 360) * 135;
            arcQuadX2 = (float) (cx + Math.sin(arcQuad2) * (mActualRadius));
            arcQuadY2 = (float) (cy - Math.cos(arcQuad2) * (mActualRadius));
            quadX2 = (float) ((arcQuadX2 - 0.25 * rightPointX - 0.25 * bottomPointX) / 0.5);
            quadY2 = (float) ((arcQuadY2 - 0.25 * rightPointY - 0.25 * bottomPointY) / 0.5);
            mCurrentPath.quadTo(quadX2, quadY2, bottomPointX, bottomPointY);
            if (secondPath.isEmpty()) {
                secondPath.moveTo(rightPointX, rightPointY);
                secondPath.quadTo(quadX2, quadY2, bottomPointX, bottomPointY);
            }
            mCurrentPath.lineTo(bottomPointX, getHeight() - 20);
            mCurrentPath.moveTo(bottomPointX, bottomPointY);
            // 第三限项控制点
            double arcQuad3 = (2 * Math.PI / 360) * 225;
            arcQuadX3 = (float) (cx + Math.sin(arcQuad3) * (mActualRadius));
            arcQuadY3 = (float) (cy - Math.cos(arcQuad3) * (mActualRadius));
            quadX3 = calculateBezierX(arcQuadX3, bottomPointX, leftPointX, 0.5f);
            quadY3 = calculateBezierY(arcQuadY3, bottomPointY, leftPointY, 0.5f);
            mCurrentPath.quadTo(quadX3, quadY3, leftPointX, leftPointY);
            if (thirdPath.isEmpty()) {
                thirdPath.moveTo(leftPointX, leftPointY);
                thirdPath.quadTo(quadX3, quadY3, bottomPointX, bottomPointY);
            }
            mCurrentPath.lineTo(0, leftPointY);
            mCurrentPath.moveTo(leftPointX, leftPointY);
            // 第四限项控制点
            double arcQuad4 = (2 * Math.PI / 360) * 315;
            arcQuadX4 = (float) (cx + Math.sin(arcQuad4) * (mActualRadius));
            arcQuadY4 = (float) (cy - Math.cos(arcQuad4) * (mActualRadius));
            quadX4 = calculateBezierX(arcQuadX4, leftPointX, topPointX, 0.5f);
            quadY4 = calculateBezierY(arcQuadY4, leftPointY, topPointY, 0.5f);
            mCurrentPath.quadTo(quadX4, quadY4, topPointX, topPointY);
            if (forthPath.isEmpty()) {
                forthPath.moveTo(topPointX, topPointY);
                forthPath.quadTo(quadX4, quadY4, leftPointX, leftPointY);
            }

            // 左下路径
            double arc1 = (2 * Math.PI / 360) * 250;
            double arc2 = (2 * Math.PI / 360) * 200;
            leftBottomX1 = (float) (cx + Math.sin(arc1) * (mActualRadius));
            leftBottomY1 = (float) (cy - Math.cos(arc1) * (mActualRadius));
            leftBottomX2 = (float) (cx + Math.sin(arc2) * (mActualRadius));
            leftBottomY2 = (float) (cy - Math.cos(arc2) * (mActualRadius));
            leftBottomX = (leftPointX + cy) / 2 + mActualRadius / 2;
            leftBottomY = (leftBottomX - cx) / (quadX3 - cx) * (quadY3 - cy) + cy;
            leftBottomQuadX = (leftBottomX1 + leftBottomX2) / 2;
            leftBottomQuadY = (leftBottomY1 + leftBottomY2) / 2;
            mCurrentPath.moveTo(leftBottomX, leftBottomY);
            mCurrentPath.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX1, leftBottomY1);
            mCurrentPath.moveTo(leftBottomX2, leftBottomY2);
            mCurrentPath.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX, leftBottomY);
            float lbArcX = (leftPointX + cy) / 2;
            float lbArxY = (lbArcX - leftBottomX) / (quadX3 - leftBottomX) * (quadY3 - leftBottomY) + leftBottomY;
            float endY = getHeight();
            float endX = (endY - lbArxY) / (leftBottomY - lbArxY) * (leftBottomX - lbArcX) + lbArcX;
            mCurrentPath.lineTo(endX, endY);
            mCurrentPath.moveTo(mRightX, 0);
            mCurrentPath.lineTo(mRightX, getHeight() - 20);
            mCurrentPath.moveTo(leftPointX, leftPointY);
            mCurrentPath.quadTo(leftBottomX1, leftBottomX1, lbArcX, lbArxY);
        }
        canvas.drawPath(mCurrentPath, mPaint);
    }

    /**
     * 贝塞尔控制点X坐标
     *
     * @param x1
     * @param x2
     * @param x3
     * @param t
     * @return
     */
    private float calculateBezierX(float x1, float x2, float x3, float t) {
        float x = (x1 - (1 - t) * (1 - t) * x2 - t * t * x3) / (2 * t * (1 - t));
        return x;
    }

    /**
     * 贝塞尔控制点Y坐标
     *
     * @param y1
     * @param y2
     * @param y3
     * @param t
     * @return
     */
    private float calculateBezierY(float y1, float y2, float y3, float t) {
        float y = (y1 - (1 - t) * (1 - t) * y2 - t * t * y3) / (2 * t * (1 - t));
        return y;
    }

    public void setDrawPath1(boolean drawPath1) {
        isDrawPath1 = drawPath1;
    }

    public void setDrawPath2(boolean drawPath2) {
        isDrawPath2 = drawPath2;
    }

    public void setDrawPath3(boolean drawPath3) {
        isDrawPath3 = drawPath3;
    }

    public void setDrawPath4(boolean drawPath4) {
        isDrawPath4 = drawPath4;
    }

    /**
     * 开始动画
     */
    public void startAnim() {
        if (mValueAnimator != null) {
            isDrawPath1 = true;
            isDrawPath2 = true;
            isDrawPath3 = true;
            isDrawPath4 = true;
            isDrawPath5 = true;
            if (mValueAnimator != null) {
                mValueAnimator.start();
            }
        }
    }

    /**
     * 停止动画
     */
    public void stopAnim() {
        isDrawPath1 = false;
        isDrawPath2 = false;
        isDrawPath3 = false;
        isDrawPath4 = false;
        isDrawPath5 = false;
        if (mHandler != null) {
            if (mAnimRunnable != null) {
                mHandler.removeCallbacks(mAnimRunnable);
            }
            mHandler.removeMessages(REFRESH_CURRENT);
        }
        if (mValueAnimator != null) {
            mHandler.removeCallbacks(mAnimRunnable);
            mValueAnimator.cancel();
        }
    }

    public void resetPath() {
        mCurrentPath.reset();
        mPath1.reset();
        mPath2.reset();
        mPath3.reset();
        mPath4.reset();
        firsPath.reset();
        secondPath.reset();
        thirdPath.reset();
        forthPath.reset();
        hasSetMRect = false;
        invalidate();
    }

    /**
     * dp转px
     */
    private static int dp2px(Context context, float dpVal) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                dpVal, context.getResources().getDisplayMetrics());
    }


    @Override
    protected void onDetachedFromWindow() {
//        stopAnim();
        super.onDetachedFromWindow();
    }


    public enum PowerType {
        SUN_SUPPLY, CITY_SUPPLY, VEHICLE_SUPPLY, BATTERY_SUPPLY
    }

}
