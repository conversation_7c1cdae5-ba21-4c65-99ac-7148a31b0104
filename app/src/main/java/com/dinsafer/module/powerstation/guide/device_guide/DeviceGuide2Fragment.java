package com.dinsafer.module.powerstation.guide.device_guide;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDeviceGuide2Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class DeviceGuide2Fragment extends MyBaseFragment<FragmentDeviceGuide2Binding> {

    public static DeviceGuide2Fragment newInstance() {
        return new DeviceGuide2Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_device_guide_2;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
