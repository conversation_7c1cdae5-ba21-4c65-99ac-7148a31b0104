package com.dinsafer.module.powerstation.widget.schedule_mode_view;

public class ScheduledModeBean {

    public static final int ONE_SECTION = 1;
    public static final int TWO_SECTION = 2;
    public static final int THREE_SECTION = 3;

    //-1. 放电 0. 不充不放  1. 充电
    private int mode;
    private int hour;
    private int percentage;
    private int sectionType = 1; // 1. 一段  2. 两段  3. 三段
    private boolean selected;

    public ScheduledModeBean() {

    }

    public ScheduledModeBean(int hour) {
        this.hour = hour;
    }

    public ScheduledModeBean(int mode, int hour, int percentage) {
        this.mode = mode;
        this.hour = hour;
        this.percentage = percentage;
    }

    public ScheduledModeBean(int mode, int hour, int percentage, int sectionType, boolean selected) {
        this.mode = mode;
        this.hour = hour;
        this.percentage = percentage;
        this.sectionType = sectionType;
        this.selected = selected;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int type) {
        this.mode = type;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getPercentage() {
        return percentage;
    }

    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    public int getSectionType() {
        return sectionType;
    }

    public void setSectionType(int sectionType) {
        this.sectionType = sectionType;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
