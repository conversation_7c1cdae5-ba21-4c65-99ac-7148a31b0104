package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.RectF;

import com.dinsafer.util.viewanimator.SvgPathParser;


public class CurrentPathBean {

    private final String INVERTER_ADDITIONAL_PATH = "M118.145 80.1202C126.875 71.3957 138.933 66 152.251 66H229V146";
    private final String INVERTER_BATTERY_PATH = "M89.8864 51.8496C81.1534 60.5922 69.0831 66.0005 55.7496 66.0005L1 66";
    private final String INVERTER_GRID_PATH = "M118.145 80.1202C126.875 71.3957 138.933 66 152.251 66H229V1";
    private final String INVERTER_KEEP_ON_PATH = "M89.8887 80.1523C98.6084 88.8823 104.001 100.937 104.001 114.25V146";
    private final String INVERTER_VEHICLE_PATH = "M84.3618 69.8267C85.5524 75.9527 83.343 82.5117 78.0881 86.6173L3.01848 145.003";
    private final String INVERTER_SOLAR_PATH = "M118.154 51.8897C109.41 43.1564 104 31.0849 104 17.75V1";

    private final String GRID_ADDITIONAL_PATH = "M229 1V146";
    private final String BATTERY_INVERTER_PATH = "M1.00063 66L55.7506 66C69.0679 66 81.125 71.3952 89.8555 80.119";
    private final String GRID_INVERTER_PATH = "M229 1L229 66L152.25 66C138.933 66 126.877 60.6056 118.146 51.8828";
    private final String SOLAR_INVERTER_PATH = "M104 0.999999L104 17.75C104 31.3198 98.398 43.5812 89.3809 52.3479";
    private final String VEHICLE_INVERTER_PATH = "M3 144.999L78.0689 86.6123C83.2317 82.5787 90.3176 81.7522 95.8949 84.2904";

    private final String INVERTER_BALANCING_POWER_PATH = "M94.1451 36.119C102.876 27.3952 114.933 22 128.25 22H145";
    private final String BALANCING_POWER_INVERTER_PATH = "M144.887 21.9502C128.137 21.9507 114.804 21.9507 102.733 16.5424L94.0003 7.7998";
    private final String BALANCING_POWER_INVERTER_BATTERY_PATH = "M65.8864 7.7998C57.1534 16.5424 45.0831 21.9507 31.7496 21.9507L2 21.9502";
    private final String BALANCING_POWER_BATTERY_INVERTER_PATH = "M2 22H31.75C45.0673 22 57.1244 27.3952 65.8549 36.119";

    private final String INVERTER_DUALPOWER_PATH = "M118.145 80.1202C126.875 71.3957 138.933 66 152.251 66H229V146";
    private final String DUALPOWER_INVERTER_PATH = "M229 146L229 66.0005L152.25 66.0005C138.917 66.0005 126.846 60.5922 118.113 51.8496";
    private final String GRID_DUALPOWER_PATH = "M229 1V146";
    private final String DUALPOWER_GRID_PATH = "M229 146.001V1";

    public static final String INVERTER_ADDITIONAL_KEY = "inverter_additional_key";
    public static final String INVERTER_BATTERY_KEY = "inverter_battery_key";
    public static final String INVERTER_GRID_KEY = "inverter_grid_key";
    public static final String INVERTER_KEEP_ON_KEY = "inverter_keep_on_key";
    public static final String INVERTER_VEHICLE_KEY = "inverter_vehicle_key";
    public static final String INVERTER_SOLAR_KEY = "inverter_solar_key";

    public static final String GRID_ADDITIONAL_KEY = "grid_additional_key";
    public static final String BATTERY_INVERTER_KEY = "battery_inverter_key";
    public static final String GRID_INVERTER_KEY = "grid_inverter_key";
    public static final String SOLAR_INVERTER_KEY = "solar_inverter_key";
    public static final String VEHICLE_INVERTER_KEY = "vehicle_inverter_key";

    public static final String BALANCING_POWER_INVERTER_KEY = "balancing_power_inverter_key";
    public static final String INVERTER_BALANCING_POWER_KEY = "inverter_balancing_power_key";
    public static final String BALANCING_POWER_INVERTER_BATTERY_KEY = "balancing_power_inverter_battery_key";
    public static final String BALANCING_POWER_BATTERY_INVERTER_KEY = "balancing_power_battery_inverter_key";

    public static final String INVERTER_DUALPOWER_KEY = "inverter_dualpower_key";
    public static final String DUALPOWER_INVERTER_KEY = "dualpower_inverter_key";
    public static final String GRID_DUALPOWER_KEY = "grid_dualpower_key";
    public static final String DUALPOWER_GRID_KEY = "dualpower_grid_key";

    private Paint paint;
    private Path currentPath;
    private PathMeasure pathMeasure;
    private Path dst;
    private RectF rectF;
    private float length;
    private float animatorValue;
    private int startColor;
    private int endColor;
    private float[] startPoint;
    private float[] endPoint;
    private boolean isDrawPath;

    public CurrentPathBean(String key) {
        paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeCap(Paint.Cap.ROUND);
        initPath(key);
        pathMeasure = new PathMeasure();
        dst = new Path();
        startPoint = new float[2];
        endPoint = new float[2];
        rectF = new RectF();
    }

    private void initPath(String key) {
        switch (key) {
            case INVERTER_ADDITIONAL_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_ADDITIONAL_PATH);
                break;

            case INVERTER_BATTERY_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_BATTERY_PATH);
                break;

            case INVERTER_GRID_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_GRID_PATH);
                break;

            case INVERTER_KEEP_ON_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_KEEP_ON_PATH);
                break;

            case INVERTER_VEHICLE_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_VEHICLE_PATH);
                break;

            case GRID_ADDITIONAL_KEY:
                currentPath = SvgPathParser.tryParsePath(GRID_ADDITIONAL_PATH);
                break;

            case INVERTER_SOLAR_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_SOLAR_PATH);
                break;

            case BATTERY_INVERTER_KEY:
                currentPath = SvgPathParser.tryParsePath(BATTERY_INVERTER_PATH);
                break;

            case GRID_INVERTER_KEY:
                currentPath = SvgPathParser.tryParsePath(GRID_INVERTER_PATH);
                break;

            case SOLAR_INVERTER_KEY:
                currentPath = SvgPathParser.tryParsePath(SOLAR_INVERTER_PATH);
                break;

            case VEHICLE_INVERTER_KEY:
                currentPath = SvgPathParser.tryParsePath(VEHICLE_INVERTER_PATH);
                break;

            case INVERTER_DUALPOWER_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_DUALPOWER_PATH);
                break;

            case DUALPOWER_INVERTER_KEY:
                currentPath = SvgPathParser.tryParsePath(DUALPOWER_INVERTER_PATH);
                break;

            case GRID_DUALPOWER_KEY:
                currentPath = SvgPathParser.tryParsePath(GRID_DUALPOWER_PATH);
                break;

            case DUALPOWER_GRID_KEY:
                currentPath = SvgPathParser.tryParsePath(DUALPOWER_GRID_PATH);
                break;

            case INVERTER_BALANCING_POWER_KEY:
                currentPath = SvgPathParser.tryParsePath(INVERTER_BALANCING_POWER_PATH);
                break;

            case BALANCING_POWER_INVERTER_KEY:
                currentPath = SvgPathParser.tryParsePath(BALANCING_POWER_INVERTER_PATH);
                break;

            case BALANCING_POWER_INVERTER_BATTERY_KEY:
                currentPath = SvgPathParser.tryParsePath(BALANCING_POWER_INVERTER_BATTERY_PATH);
                break;

            case BALANCING_POWER_BATTERY_INVERTER_KEY:
                currentPath = SvgPathParser.tryParsePath(BALANCING_POWER_BATTERY_INVERTER_PATH);
                break;
        }
    }

    public Paint getPaint() {
        return paint;
    }

    public void setPaint(Paint paint) {
        this.paint = paint;
    }

    public Path getCurrentPath() {
        return currentPath;
    }

    public void setCurrentPath(Path currentPath) {
        this.currentPath = currentPath;
    }

    public PathMeasure getPathMeasure() {
        return pathMeasure;
    }

    public void setPathMeasure(PathMeasure pathMeasure) {
        this.pathMeasure = pathMeasure;
    }

    public Path getDst() {
        return dst;
    }

    public void setDst(Path dst) {
        this.dst = dst;
    }

    public RectF getRectF() {
        return rectF;
    }

    public void setRectF(RectF rectF) {
        this.rectF = rectF;
    }

    public float getLength() {
        return length;
    }

    public void setLength(float length) {
        this.length = length;
    }

    public float getAnimatorValue() {
        return animatorValue;
    }

    public void setAnimatorValue(float animatorValue) {
        this.animatorValue = animatorValue;
    }

    public int getStartColor() {
        return startColor;
    }

    public void setStartColor(int startColor) {
        this.startColor = startColor;
    }

    public int getEndColor() {
        return endColor;
    }

    public void setEndColor(int endColor) {
        this.endColor = endColor;
    }

    public float[] getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(float[] startPoint) {
        this.startPoint = startPoint;
    }

    public float[] getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(float[] endPoint) {
        this.endPoint = endPoint;
    }

    public boolean isDrawPath() {
        return isDrawPath;
    }

    public void setDrawPath(boolean drawPath) {
        isDrawPath = drawPath;
    }

    public boolean startGreaterEndX () {
        return startPoint[0] > endPoint[0];
    }

    public boolean startGreaterEndY () {
        return startPoint[1] > endPoint[1];
    }
}
