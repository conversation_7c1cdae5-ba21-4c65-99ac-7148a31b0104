package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;

public class EleStatValView extends View {

    private final Context mContext;
    private int mKeyTextColor;
    private int mValTextColor;
    private float mLeftValSize;
    private float mLeftUnitSize;
    private float mMiddleKeySize;
    private float mMiddleValSize;
    private float mMiddleUnitSize;
    private float mRightKeySize;
    private float mRightValSize;
    private float mRightUnitSize;

    private TextPaint mTextPaint;
    private Rect mLeftValRect;
    private Rect mLeftUnitRect;
    private Rect mMiddleKeyRect;
    private Rect mMiddleValRect;
    private Rect mMiddleUnitRect;
    private Rect mRightKeyRect;
    private Rect mRightValRect;
    private Rect mRightUnitRect;

    private String mLeftVal = "";
    private String mLeftUnit = "";
    private String mMiddleKey = "";
    private String mMiddleVal = "";
    private String mMiddleUnit = "";
    private String mRightKey = "";
    private String mRightVal = "";
    private String mRightUnit = "";

    private float mValUnitSpace;
    private float mKeyValSpace;
    private float mValSpace;
    private boolean isLeftRTL;
    private boolean isMiddleRTL;
    private boolean isRightRTL;

    public EleStatValView(Context context) {
        this(context, null);
    }

    public EleStatValView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EleStatValView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        TypedArray typedArray = mContext.obtainStyledAttributes(attrs, R.styleable.EleStatValView);
        mKeyTextColor = typedArray.getColor(R.styleable.EleStatValView_esvv_key_text_color, mContext.getResources().getColor(R.color.color_white_03));
        mValTextColor = typedArray.getColor(R.styleable.EleStatValView_esvv_val_text_color, mContext.getResources().getColor(R.color.color_white_01));
        mLeftValSize = typedArray.getDimension(R.styleable.EleStatValView_esvv_left_val_text_size, DensityUtil.sp2px(mContext, 32));
        mLeftUnitSize = typedArray.getDimension(R.styleable.EleStatValView_esvv_left_unit_text_size, DensityUtil.sp2px(mContext, 20));
        mMiddleKeySize = typedArray.getDimension(R.styleable.EleStatValView_esvv_middle_key_text_size, DensityUtil.sp2px(mContext, 12));
        mMiddleValSize = typedArray.getDimension(R.styleable.EleStatValView_esvv_middle_val_text_size, DensityUtil.sp2px(mContext, 20));
        mMiddleUnitSize = typedArray.getDimension(R.styleable.EleStatValView_esvv_middle_unit_text_size, DensityUtil.sp2px(mContext, 16));
        mRightKeySize = typedArray.getDimension(R.styleable.EleStatValView_esvv_right_key_text_size, DensityUtil.sp2px(mContext, 12));
        mRightValSize = typedArray.getDimension(R.styleable.EleStatValView_esvv_right_val_text_size, DensityUtil.sp2px(mContext, 20));
        mRightUnitSize = typedArray.getDimension(R.styleable.EleStatValView_esvv_right_unit_text_size, DensityUtil.sp2px(mContext, 16));
        mValUnitSpace = typedArray.getDimension(R.styleable.EleStatValView_esvv_val_unit_space, DensityUtil.dp2px(mContext, 3));
        mKeyValSpace = typedArray.getDimension(R.styleable.EleStatValView_esvv_key_val_space, DensityUtil.dp2px(mContext, 6));

        mTextPaint = new TextPaint();
        mTextPaint.setAntiAlias(true);
        mTextPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.poppins);
        mTextPaint.setTypeface(typeface);

        mLeftValRect = new Rect();
        mLeftUnitRect = new Rect();
        mMiddleKeyRect = new Rect();
        mMiddleValRect = new Rect();
        mMiddleUnitRect = new Rect();
        mRightKeyRect = new Rect();
        mRightValRect = new Rect();
        mRightUnitRect = new Rect();
        mValSpace = DensityUtil.dp2px(mContext, 10);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        mTextPaint.setTextSize(mLeftValSize);
        mTextPaint.setColor(mValTextColor);

        mTextPaint.setFakeBoldText(true);
        mTextPaint.getTextBounds(mLeftVal, 0, mLeftVal.length(), mLeftValRect);
        mTextPaint.setTextSize(mLeftUnitSize);
        mTextPaint.getTextBounds(mLeftUnit, 0, mLeftUnit.length(), mLeftUnitRect);

        mTextPaint.setFakeBoldText(false);
        mTextPaint.setTextSize(mMiddleKeySize);
        mTextPaint.getTextBounds(mMiddleKey, 0, mMiddleKey.length(), mMiddleKeyRect);
        mTextPaint.setTextSize(mMiddleValSize);
        mTextPaint.getTextBounds(mMiddleVal, 0, mMiddleVal.length(), mMiddleValRect);
        mTextPaint.setTextSize(mMiddleUnitSize);
        mTextPaint.getTextBounds(mMiddleUnit, 0, mMiddleUnit.length(), mMiddleUnitRect);
        mTextPaint.setTextSize(mRightKeySize);
        mTextPaint.getTextBounds(mRightKey, 0, mRightKey.length(), mRightKeyRect);
        mTextPaint.setTextSize(mRightValSize);
        mTextPaint.getTextBounds(mRightVal, 0, mRightVal.length(), mRightValRect);
        mTextPaint.setTextSize(mRightUnitSize);
        mTextPaint.getTextBounds(mRightUnit, 0, mRightUnit.length(), mRightUnitRect);
        float leftWidth = mLeftValRect.width() + mLeftUnitRect.width() + mValUnitSpace;
        float middleValWidth = mMiddleValRect.width() + mMiddleUnitRect.width() + mValUnitSpace;
        float middleWidth = Math.max(mMiddleKeyRect.width(), middleValWidth);
        float rightValWidth = mRightValRect.width() + mRightUnitRect.width() + mValUnitSpace;
        float rightWidth = Math.max(mRightKeyRect.width(), rightValWidth);
        float space = (getMeasuredWidth() - leftWidth - middleWidth - rightWidth) / 2f;

        mTextPaint.setFakeBoldText(true);
        mTextPaint.setTextSize(mLeftValSize);
        float centY = getHeight() / 2f;
        float leftY = centY + (mLeftValRect.height()) / 2f;
        if (isLeftRTL) {
            mTextPaint.setTextSize(mLeftUnitSize);
            canvas.drawText(mLeftUnit, 0, leftY, mTextPaint);
            mTextPaint.setTextSize(mLeftValSize);
            canvas.drawText(mLeftVal, mLeftUnitRect.width() + mValUnitSpace, leftY, mTextPaint);
        } else {
            canvas.drawText(mLeftVal, 0, leftY, mTextPaint);
            mTextPaint.setTextSize(mLeftUnitSize);
            canvas.drawText(mLeftUnit, mLeftValRect.width() + mValUnitSpace, leftY, mTextPaint);
        }

        mTextPaint.setFakeBoldText(false);
        mTextPaint.setTextSize(mMiddleKeySize);
        mTextPaint.setColor(mKeyTextColor);
        float keyY = getHeight() / 2.6f - mKeyValSpace / 2f;
        float middleX = leftWidth + space;
        if (TextUtils.isEmpty(mRightVal)) {
             middleX = leftWidth < getMeasuredWidth() / 2f - middleWidth / 2f - mValSpace ?
                    getMeasuredWidth() / 2f - middleWidth / 2f : leftWidth + space;
        }
        canvas.drawText(mMiddleKey, middleX, keyY, mTextPaint);
        mTextPaint.setTextSize(mMiddleValSize);
        mTextPaint.setColor(mValTextColor);
        float middleValRectHeight = Math.max(mMiddleValRect.height(), 42f);
        float valY = getHeight() / 2.6f + mKeyValSpace / 2f + middleValRectHeight;
        if (isMiddleRTL) {
            mTextPaint.setTextSize(mMiddleUnitSize);
            canvas.drawText(mMiddleUnit, middleX , valY, mTextPaint);
            mTextPaint.setTextSize(mMiddleValSize);
            canvas.drawText(mMiddleVal, middleX + mMiddleUnitRect.width() + mValUnitSpace, valY, mTextPaint);
        } else {
            canvas.drawText(mMiddleVal, middleX, valY, mTextPaint);
            mTextPaint.setTextSize(mMiddleUnitSize);
            canvas.drawText(mMiddleUnit, middleX + mMiddleValRect.width() + mValUnitSpace,
                    valY, mTextPaint);
        }

        mTextPaint.setTextSize(mRightKeySize);
        mTextPaint.setColor(mKeyTextColor);
        float rightX = leftWidth + space * 2f + middleWidth - mValUnitSpace;
        canvas.drawText(mRightKey, rightX, keyY, mTextPaint);
        mTextPaint.setTextSize(mRightValSize);
        mTextPaint.setColor(mValTextColor);
        if (isRightRTL) {
            mTextPaint.setTextSize(mRightUnitSize);
            canvas.drawText(mRightUnit, rightX , valY, mTextPaint);
            mTextPaint.setTextSize(mRightValSize);
            canvas.drawText(mRightVal, rightX + mRightUnitRect.width() + mValUnitSpace, valY, mTextPaint);
        } else {
            canvas.drawText(mRightVal, rightX, valY, mTextPaint);
            mTextPaint.setTextSize(mRightUnitSize);
            canvas.drawText(mRightUnit, rightX + mRightValRect.width() + mValUnitSpace,
                    valY, mTextPaint);
        }
    }

    public void setLeftVal(String val, String unit) {
        mLeftVal = val;
        mLeftUnit = unit;
    }

    public void setMiddleVal(String key, String val, String unit) {
        mMiddleKey = key;
        mMiddleVal = val;
        mMiddleUnit = unit;
    }

    public void setRightVal(String key, String val, String unit) {
        mRightKey = key;
        mRightVal = val;
        mRightUnit = unit;
    }

    public void setLeftRTL(boolean leftRTL) {
        isLeftRTL = leftRTL;
    }

    public void setMiddleRTL(boolean middleRTL) {
        isMiddleRTL = middleRTL;
    }

    public void setRightRTL(boolean rightRTL) {
        isRightRTL = rightRTL;
    }

    public void refreshText() {
        invalidate();
    }
}
