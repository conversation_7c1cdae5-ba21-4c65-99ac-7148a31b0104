package com.dinsafer.module.powerstation.electricity.bean;

import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;

public class ElectricityCircleTypeBean {

    private String type;
    private boolean selected;
    private CycleType cycleType;
    private PlusMinusType plusMinusType;

    public ElectricityCircleTypeBean(String type) {
        this.type = type;
    }

    public ElectricityCircleTypeBean(String type, CycleType cycleType) {
        this.type = type;
        this.cycleType = cycleType;
    }

    public ElectricityCircleTypeBean(String type, PlusMinusType plusMinusType) {
        this.type = type;
        this.plusMinusType = plusMinusType;
    }

    public ElectricityCircleTypeBean(String type, boolean selected) {
        this.type = type;
        this.selected = selected;
    }

    public ElectricityCircleTypeBean(String type, boolean selected, CycleType cycleType) {
        this.type = type;
        this.selected = selected;
        this.cycleType = cycleType;
    }

    public String getType() {
        return type == null ? "" : type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public CycleType getCycleType() {
        return cycleType;
    }

    public void setCycleType(CycleType cycleType) {
        this.cycleType = cycleType;
    }

    public PlusMinusType getPlusMinusType() {
        return plusMinusType;
    }

    public void setPlusMinusType(PlusMinusType plusMinusType) {
        this.plusMinusType = plusMinusType;
    }
}
