package com.dinsafer.module.powerstation;

import com.dinsafer.module.powerstation.bean.PSRegionBean;

import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;

public class CityComparator implements Comparator<PSRegionBean> {
    @Override
    public int compare(PSRegionBean s, PSRegionBean t1) {
        String country = s.getCountryName().toUpperCase(Locale.getDefault()) + "," + s.getCountryName().toUpperCase(Locale.getDefault());
        String country2 = t1.getCountryName().toUpperCase(Locale.getDefault()) + "," + t1.getCountryName().toUpperCase(Locale.getDefault());
        if (!(country.charAt(0) >= 'A' && country.charAt(0) <= 'Z')
                && (country2.charAt(0) >= 'A' && country2.charAt(0) <= 'Z')) {
            return 1;
        }
        if ((country.charAt(0) >= 'A' && country.charAt(0) <= 'Z')
                && !(country2.charAt(0) >= 'A' && country2.charAt(0) <= 'Z')) {
            return -1;
        }
        return Collator.getInstance(Locale.getDefault()).compare(country, country2);
    }
}
