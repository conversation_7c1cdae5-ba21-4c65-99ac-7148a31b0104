package com.dinsafer.module.powerstation.impacts;

import static com.dinsafer.module.powerstation.impacts.AIModeFragment.AI_MODE;
import static com.dinsafer.module.powerstation.impacts.AIModeFragment.MANUAL_MODE;
import static com.dinsafer.module.powerstation.impacts.AIModeFragment.NEGATIVE_ELECTRICITY_PRICE_MODEL_LIGHT;
import static com.dinsafer.module.powerstation.impacts.AIModeFragment.NEGATIVE_ELECTRICITY_PRICE_MODEL_NIGHT;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAiLandscapeModeBinding;
import com.dinsafer.dinnet.databinding.LayoutAiModeDetailsLandscapePageBinding;
import com.dinsafer.dinnet.databinding.LayoutAiModeDetailsPageBinding;
import com.dinsafer.dinnet.databinding.LayoutEmaldoAiRetrievingBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.TimeUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AILandscapeModeFragment extends MyBaseFragment<FragmentAiLandscapeModeBinding> {

    private static final String TAG = AILandscapeModeFragment.class.getSimpleName();

    private Map<String, Object> mHTTPDataMap = new HashMap<>();
    private Map<String, Object> mIotScheduleDataMap = new HashMap<>();

    private SerializableMap mHTTPData;
    private SerializableMap mIotScheduleData;

    private AIDataManager aiDataManager;
    private String unitPrice;
    private String timeZone;
    private long gmTime;

    private int chartIndex = -1;
    private int startTime;
    private int endTime;
    private int mPVPreference;

    private boolean isChartControlSolar = true;
    private boolean isChartControlPrice = true;
    private boolean isChartControlPriceLevel = true;
    private boolean isChartControlCharge = true;

    private int statusBarHeightPortrait;
    private boolean isSupportPVAndLocation;

    private LayoutEmaldoAiRetrievingBinding mRetrievingBinding;

    public static AILandscapeModeFragment newInstance() {
        AILandscapeModeFragment fragment = new AILandscapeModeFragment();
        return fragment;
    }

    public static AILandscapeModeFragment newInstance(Map<String, Object> mHTTPDataMap, Map<String, Object> mIotScheduleDataMap,
                                                      int statusBarHeightPortrait,boolean isSupportPVAndLocation,int mPVPreference) {
        AILandscapeModeFragment fragment = new AILandscapeModeFragment();
        Bundle bundle = new Bundle();
        SerializableMap mHTTPData = new SerializableMap(mHTTPDataMap);
        bundle.putSerializable(PSKeyConstant.HTTP_DATA_MAP, mHTTPData);
        SerializableMap mIotScheduleData = new SerializableMap(mIotScheduleDataMap);
        bundle.putSerializable(PSKeyConstant.IOT_SCHEDULE_DATA_MAP, mIotScheduleData);
        bundle.putInt(PSKeyConstant.STATUS_BAR_HEIGHT_PORTRAIT, statusBarHeightPortrait);
        bundle.putBoolean(PSKeyConstant.IS_SUPPORT_PV_AND_LOCATION, isSupportPVAndLocation);
        bundle.putInt(PSKeyConstant.PV_PREFERENCE, mPVPreference);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ai_landscape_mode;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        Bundle bundle = getArguments();
        mHTTPData = (SerializableMap) bundle.get(PSKeyConstant.HTTP_DATA_MAP);
        mHTTPDataMap = mHTTPData.getObjectMap();
        mIotScheduleData = (SerializableMap) bundle.get(PSKeyConstant.IOT_SCHEDULE_DATA_MAP);
        mIotScheduleDataMap = mIotScheduleData.getObjectMap();
        statusBarHeightPortrait = bundle.getInt(PSKeyConstant.STATUS_BAR_HEIGHT_PORTRAIT);
        isSupportPVAndLocation = bundle.getBoolean(PSKeyConstant.IS_SUPPORT_PV_AND_LOCATION);
        mPVPreference = bundle.getInt(PSKeyConstant.PV_PREFERENCE);
        ConstraintLayout rootLayout = mBinding.rootLayout;
        rootLayout.setPadding(
                statusBarHeightPortrait,
                rootLayout.getPaddingTop(),
                statusBarHeightPortrait,
                rootLayout.getPaddingBottom()
        );
        initChart();
    }

    @Override
    public void onResume() {
        if (activity.getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
            // 当前状态为竖屏
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }
        super.onResume();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.closeToLandscapeMode.setOnClickListener(v -> {
            removeSelf();
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
        });
        mBinding.hideDetailsPage.setOnClickListener(v -> aiDataManager.clearHighlight());
    }

    @Override
    public boolean onBackPressed() {
        activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
        activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
        return super.onBackPressed();
    }

    private void setPvTag(){
        if (mPVPreference == Mcu.PVPreference.PVPreferenceType.followEmaldoAI.getPreference()) {
            detailsBinding.aiDetailsLoadFirst.setLocalText(getString(R.string.Follow_Emaldo_AI));
        } else if (mPVPreference == Mcu.PVPreference.PVPreferenceType.load.getPreference()) {
            detailsBinding.aiDetailsLoadFirst.setLocalText(getString(R.string.Load_First));
        } else if (mPVPreference == Mcu.PVPreference.PVPreferenceType.batteryCharge.getPreference()) {
            detailsBinding.aiDetailsLoadFirst.setLocalText(getString(R.string.Battery_Charge_First));
        }
    }

    private int dpToPx(int dp) {
        return (int) (dp * getResources().getDisplayMetrics().density);
    }

    /****************************************************** 展示详情 ********************************************************/

    private void showAiModeDetails(int index) {
        if (detailsBinding.aiDetailsPageLayout.getVisibility() == View.INVISIBLE) {
            startDetailsAnimation();
        }
        int DetailsType ,chargeType;
        detailsBinding.currencyUnit.setLocalText(unitPrice);
        // 获取当前显示的时间
        startTime = DDDateUtil.getHourByTimestamps(gmTime * 1000 + index * 3600000L, timeZone);
        endTime = DDDateUtil.getHourByTimestamps(gmTime * 1000 + (index + 1) * 3600000L, timeZone);
        detailsBinding.aiDetailsTime.setLocalText(TimeUtil.formatHour(startTime)
                + ":00 - " + TimeUtil.formatHour(endTime) + ":00");
        Integer[] weekdays = (Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null);
        if (weekdays[startTime] == -128) {
            chargeType = aiDataManager.getHopeChargeDischarges().get(index);
            if (aiDataManager.getMarketPrices().get(index) < 0f)
                DetailsType = NEGATIVE_ELECTRICITY_PRICE_MODEL_LIGHT;
            else
                DetailsType = AI_MODE;
        } else {
            chargeType = weekdays[startTime];
            if (aiDataManager.getMarketPrices().get(index) < 0f)
                DetailsType = NEGATIVE_ELECTRICITY_PRICE_MODEL_NIGHT;
            else
                DetailsType = MANUAL_MODE;
        }
        DDLog.i(TAG,"getHopeChargeDischarges: " + aiDataManager.getHopeChargeDischarges().get(index) + "    getUserPrices: " + aiDataManager.getUserPrices().get(index) +
                "   weekdays: " + weekdays[startTime]);
        showAiModeDetailsType(DetailsType, aiDataManager.getPlans().get(index), chargeType);
        totalForecastSolar(aiDataManager.getForecastSolars(),aiDataManager.getForecastSolars().get(index),false);
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        BigDecimal bd = new BigDecimal(String.valueOf(aiDataManager.getMarketPrices().get(index)));
        String result = bd.stripTrailingZeros().toPlainString();
        detailsBinding.marketPricesValue.setLocalText(result);
        DDLog.i(TAG, "showAiModeDetails： startTime: " + startTime + "    endTime: " + endTime +
                "\nDetailsType: " + DetailsType + "    plans: " + aiDataManager.getPlans().get(index) +
                "\nIndex: " + index +
                "\ngetForecastSolars: " + String.valueOf(aiDataManager.getForecastSolars().get(index)));
    }

    private void startDetailsAnimation() {
        isAnimationFinish = false;
        detailsBinding.aiDetailsPageLayout.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                detailsBinding.aiDetailsPageLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                // 获取标题布局高度
                final int aiDetailsWidth = detailsBinding.aiDetailsPageLayout.getWidth();
                detailsBinding.aiDetailsPageLayout.setVisibility(View.VISIBLE);
                // ChartView
                ValueAnimator marginAnimator = ValueAnimator.ofInt(0, aiDetailsWidth + dpToPx(10)).setDuration(300);
                marginAnimator.addUpdateListener(animation -> {
                    int marginValue = (Integer) animation.getAnimatedValue();
                    ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
                    params.leftMargin = marginValue;
                    mBinding.aiModeChartView.setLayoutParams(params);
                });
                // 同步执行动画
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(marginAnimator,
                        ObjectAnimator.ofFloat(detailsBinding.aiDetailsPageLayout, "alpha", 0f, 1f).setDuration(300));
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        isAnimationFinish = true;
                    }
                });
                animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
                animatorSet.start();
            }
        });
    }

    private boolean isAnimationFinish = true;

    private void exitDetailsAnimation() {
        isAnimationFinish = false;
        // 获取标题布局高度
        final int aiDetailsWidth = detailsBinding.aiDetailsPageLayout.getWidth();
        // ChartView
        ValueAnimator marginAnimator = ValueAnimator.ofInt(aiDetailsWidth + dpToPx(10) ,0).setDuration(300);
        marginAnimator.addUpdateListener(animation -> {
            int marginValue = (Integer) animation.getAnimatedValue();
            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
            params.leftMargin = marginValue;
            mBinding.aiModeChartView.setLayoutParams(params);
        });
        // 同步执行动画
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(marginAnimator,
                ObjectAnimator.ofFloat(detailsBinding.aiDetailsPageLayout, "alpha", 1f, 0f).setDuration(300));
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
                chartViewParams.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                chartViewParams.leftMargin = 0;
                mBinding.aiModeChartView.setLayoutParams(chartViewParams);
                detailsBinding.aiDetailsPageLayout.setVisibility(View.INVISIBLE);
                isAnimationFinish = true;
            }
        });
        animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
        animatorSet.start();
    }

    private void showAiModeDetailsType(int type, int plans, int chargeType) {
        int[] colors = AIColorUtil.getAIColor(getContext());
        float[] positions = AIColorUtil.getAIColorPosition();
        detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_mode_details_charge_discharge));
        DDLog.i(TAG,"showAiModeDetailsType -->  type: " + type + "  plans: " + plans +" chargeType: " + chargeType +
                "   chartIndex: " + chartIndex + "  C1: " + (float)aiDataManager.getC1() / 100 +
                "  RelativePriceNorms: " + aiDataManager.getRelativePriceNorms().get(chartIndex));
        switch (type) {
            case AI_MODE:
            case NEGATIVE_ELECTRICITY_PRICE_MODEL_LIGHT:
                detailsBinding.aiDetailsBatteryMode.setAIColorShader(colors, positions);
                detailsBinding.aiDetailsLoadFirst.setAIColorShader(colors, positions);
                detailsBinding.aiDetailsLoadFirst.setSelected(true);
                detailsBinding.aiDetailsTimeTip.setVisibility(View.GONE);
                detailsBinding.aiDetailsInstructions.setVisibility(View.VISIBLE);
                switch (plans) {
                    case 0:
                        if (chargeType == 0) {
                            detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.No_Charge_Nor_Discharge));
                            detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_mode_details_discharge));
                        } else if (chargeType < 0) {
                            detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Battery_Discharge));
                            if ((float)aiDataManager.getS2() / 100 < aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_8));
                            else if ((float)aiDataManager.getS1() / 100 < aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_4));
                        } else {
                            detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Charge_with_grid));
                            if ((float)aiDataManager.getC1() / 100 > aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_1));
                            else if ((float)aiDataManager.getC2() / 100 > aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_6));
                            else if ((float)aiDataManager.getC3() / 100 > aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_7));
                        }
                        break;
                    case 1:
                        detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Await_Sun_Charge));
                        detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_2));
                        break;
                    case 2:
                        detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Await_Lower_Utility_Charge));
                        detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_3));
                        break;
                }
                if (type == AI_MODE) {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.GONE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.GONE);
                } else {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.VISIBLE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.VISIBLE);
                }
                break;
            case MANUAL_MODE:
            case NEGATIVE_ELECTRICITY_PRICE_MODEL_NIGHT:
                detailsBinding.aiDetailsBatteryMode.removeAIShader(getColor(R.color.color_white_01));
                detailsBinding.aiDetailsLoadFirst.removeAIShader(getColor(R.color.color_white_01));
                detailsBinding.aiDetailsLoadFirst.setSelected(false);
                detailsBinding.aiDetailsTimeTip.setVisibility(View.VISIBLE);
                if (chargeType == 0) {
                    detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.ps_is_no_charge_nor_discharge));
                    detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_mode_details_discharge));
                } else if (chargeType < 0) {
                    detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Battery_Discharge));
                } else {
                    detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Charge_with_grid));
                }
                if (type == MANUAL_MODE) {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.GONE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.GONE);
                } else {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.VISIBLE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.VISIBLE);
                }
                break;
        }
    }

    private int getColor(int color) {
        return getResources().getColor(color);
    }

    /****************************************************** 图表初始化 ********************************************************/

    private void initChart() {
        Integer[] weekdays = (Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null);
        DDLog.i(TAG, "weekdays===" + Arrays.toString(weekdays));
        // 初始化数据
        unitPrice = DeviceHelper.getString(mHTTPDataMap, BmtDataKey.UNIT_PRICE, "");
        timeZone = DeviceHelper.getString(mHTTPDataMap, BmtDataKey.TIMEZONE, "");
        // 截取时间得到 StartIndex
        long startTimeNow = DeviceHelper.getLong(mHTTPDataMap, BmtDataKey.START_TIME, 0);
        gmTime = DeviceHelper.getLong(mHTTPDataMap, BmtDataKey.GMTTIME, 0) / 1000000000;
        startTime = DDDateUtil.getHourByTimestamps(gmTime * 1000, timeZone);
        endTime = DDDateUtil.getHourByTimestamps(gmTime * 1000 + 3600000L, timeZone);
//        currentIotChargeDischarges = weekdays[startTime];
        // 计算 ForecastSolars 总和 (根据 relative_price_norms 剔除)
        List<Float> relativePriceNorms = getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.RELATIVE_PRICE_NORMS));
        List<Integer> forecastSolars = getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.FORECAST_SOLARS));
        aiDataManager = new AIDataManager.Builder(mBinding.aiModeChartView)
                .setC1(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.C_1, 0))
                .setC2(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.C_2, 0))
                .setC3(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.C_3, 0))
                .setS1(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.S_1, 0))
                .setS2(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.S_2, 0))
                .setStartTime(gmTime)
                .setTimezone(DeviceHelper.getString(mHTTPDataMap, BmtDataKey.TIMEZONE, ""))
                // 绝对零电价绝对零电价(归一化相对电价)
                .setAbsZeroEcPrices(DeviceHelper.getFloat(mHTTPDataMap, BmtDataKey.ABS_ZERO_EC_PRICES, 0))
                // 每小时的天气编码。0：部分多云，1：晴天，2：阴天，3：雾，4：下雨，5：下雪，6：雨夹雪
                .setConditions(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.CONDITIONS)))
                // 每小时的预测PV，单位：wh
                .setForecastSolars(forecastSolars.subList(0, relativePriceNorms.size()))
                // 每小时的期望充放电量，单位：百分比
                .setHopeChargeDischarges(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.HOPE_CHARGE_DISCHARGES)))
                // 每小时的分时批发价
                .setMarketPrices(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.MARKET_PRICES)))
                // 每小时的充放电量计划。0：A计划，1：B计划，2：C计划
                .setPlans(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.PLANS)))
                // 归一化后每小时的相对电价
                .setRelativePriceNorms(relativePriceNorms)
                // 每小时的用户侧分时价格
                .setUserPrices(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.USER_PRICES)))
                // 每天的日出日落时间戳
                .setSunrise(DeviceHelper.getList(mHTTPDataMap, BmtDataKey.SUNRISES))
                .setSunset(DeviceHelper.getList(mHTTPDataMap, BmtDataKey.SUNSETS))
                // IOT手动充放电数据
                .setWeekdays(weekdays)
                .setHighlightListener(index -> {
                    // 这里只给索引, 要显示出来的数据根据索引从对应列表获取数据
                    DDLog.i(TAG, "图表回调： index" + index + "   chartIndex: " + chartIndex);
                    if (index == chartIndex) return;
                    chartIndex = index;
                    initDetails(index);
                })
                .setRetrievingViewListener((width, height, retrievingSize) -> dealRetrievingView((int) width, (int) Math.ceil(height), retrievingSize)).build();
        // 计算 ForecastSolars 总和 (根据 relative_price_norms 剔除)
        totalForecastSolar(aiDataManager.getForecastSolars(),0,true);
        initControl();
        DDLog.i(TAG, "aiDataManager.getRelativePriceNorms().size(): " + (aiDataManager.getRelativePriceNorms().size()) + "  ForecastSolars: " + aiDataManager.getForecastSolars());
        DDLog.d(TAG, "截断数据： conditions: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.CONDITIONS)));
        DDLog.d(TAG, "截断数据： forecast_solars: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.FORECAST_SOLARS)));
        DDLog.d(TAG, "截断数据： hope_charge_discharges: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.HOPE_CHARGE_DISCHARGES)));
        DDLog.d(TAG, "截断数据： market_prices: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.MARKET_PRICES)));
        DDLog.d(TAG, "截断数据： plans: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.PLANS)));
        DDLog.d(TAG, "截断数据： relative_price_norms: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.RELATIVE_PRICE_NORMS)));
        DDLog.d(TAG, "截断数据： user_prices: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.USER_PRICES)));
    }

    private void dealRetrievingView(int width, int height, int retrievingSize) {
        if (mRetrievingBinding == null) {
            ViewStub viewStub = mBinding.vsRetrieving.getViewStub();
            if (viewStub != null) {
                View view = viewStub.inflate();
                mRetrievingBinding = DataBindingUtil.bind(view);
                if (mRetrievingBinding != null) {
                    mRetrievingBinding.llRetrieving.setOnClickListener(v -> {
                        if (aiDataManager != null) {
                            if (chartIndex == aiDataManager.getRelativePriceNorms().size() - 1) {
                                aiDataManager.clearHighlight();
                            } else {
                                aiDataManager.setLastHighlightByIndex();
                            }
                        }
                    });
                }

            }
        }
        if (mRetrievingBinding != null) {
            ConstraintLayout.LayoutParams clParams = (ConstraintLayout.LayoutParams) mRetrievingBinding.llRetrieving.getLayoutParams();
            if (width != clParams.width || height != clParams.height) {
                clParams.width = width;
                clParams.height = height;
                mRetrievingBinding.llRetrieving.setLayoutParams(clParams);
                ViewGroup.LayoutParams logoLLParams = mRetrievingBinding.ivLogo.getLayoutParams();
                if (retrievingSize >= 12) {
                    logoLLParams.width = DensityUtil.dp2px(getContext(), 36);
                    logoLLParams.height = DensityUtil.dp2px(getContext(), 36);
                    mRetrievingBinding.ivLogo.setLayoutParams(logoLLParams);
                    mRetrievingBinding.ivLogo.setVisibility(View.VISIBLE);
                    mRetrievingBinding.tvNote.setVisibility(View.VISIBLE);
                } else if (retrievingSize >= 6) {
                    logoLLParams.width = DensityUtil.dp2px(getContext(), 24);
                    logoLLParams.height = DensityUtil.dp2px(getContext(), 24);
                    mRetrievingBinding.ivLogo.setLayoutParams(logoLLParams);
                    mRetrievingBinding.ivLogo.setVisibility(View.VISIBLE);
                    mRetrievingBinding.tvNote.setVisibility(View.GONE);
                } else {
                    mRetrievingBinding.ivLogo.setVisibility(View.GONE);
                    mRetrievingBinding.tvNote.setVisibility(View.GONE);
                }
            }
        }
    }

    private LayoutAiModeDetailsLandscapePageBinding detailsBinding;

    private void initDetails(int index) {
        ViewStub viewStub = mBinding.aiDetailsPageViewStub.getViewStub();
        if (null == detailsBinding) {
            View view = viewStub.inflate();
            detailsBinding = DataBindingUtil.bind(view);
            detailsBinding.aiDetailsTimeTip.setLocalText("(" + Local.s(getString(R.string.Edited)) + ")");
            detailsBinding.aiDetailsLoadFirst.setVisibility(isSupportPVAndLocation ? View.VISIBLE : View.GONE);
            setPvTag();
        }
        if (!isAnimationFinish) return;
        if (index == -1) {
            if (detailsBinding.aiDetailsPageLayout.getVisibility() == View.VISIBLE) exitDetailsAnimation();
            return;
        }
        showAiModeDetails(index);
    }

    private void initControl() {
        mBinding.chartControlChargeText.setLocalText(Local.s(getString(R.string.Charge)) + " / " + Local.s(getString(R.string.Discharge)));
        // 图表控制
        mBinding.chartControlSolar.setOnClickListener(v -> {
            if (!isChartControlPrice && !isChartControlPriceLevel && !isChartControlCharge) return;
            isChartControlSolar = !isChartControlSolar;
            mBinding.chartControlSolar.setAlpha(isChartControlSolar ? 1f : 0.5f);
            aiDataManager.setDrawSolar(isChartControlSolar);
        });
        mBinding.chartControlPrice.setOnClickListener(v -> {
            if (!isChartControlSolar && !isChartControlPriceLevel && !isChartControlCharge) return;
            isChartControlPrice = !isChartControlPrice;
            mBinding.chartControlPrice.setAlpha(isChartControlPrice ? 1f : 0.5f);
            aiDataManager.setDrawPrice(isChartControlPrice);
        });
        mBinding.chartControlPriceLevel.setOnClickListener(v -> {
            if (!isChartControlSolar && !isChartControlPrice && !isChartControlCharge) return;
            isChartControlPriceLevel = !isChartControlPriceLevel;
            mBinding.chartControlPriceLevel.setAlpha(isChartControlPriceLevel ? 1f : 0.5f);
            aiDataManager.setDrawPriceLevel(isChartControlPriceLevel);
        });
        mBinding.chartControlCharge.setOnClickListener(v -> {
            if (!isChartControlSolar && !isChartControlPrice && !isChartControlPriceLevel) return;
            isChartControlCharge = !isChartControlCharge;
            mBinding.chartControlCharge.setAlpha(isChartControlCharge ? 1f : 0.5f);
            aiDataManager.setDrawChargeStatus(isChartControlCharge);
        });
    }

    private void totalForecastSolar(List<Integer> forecastSolar, int forecastSolarValue, boolean isList) {
        if (isList) {
            if (forecastSolar == null || forecastSolar.isEmpty()) return;
            long sum = 0;
            double converted;
            for (Integer num : forecastSolar) {
                if (num != null) sum += num;
            }
            if (sum < 1000) {
                converted = sum;
                mBinding.forecastSolarTotalW.setLocalText("Wh");
            } else if (sum < 1_000_000) {
                converted = sum / 1000.0;
                mBinding.forecastSolarTotalW.setLocalText("kWh");
            } else {
                converted = sum / 1_000_000.0;
                mBinding.forecastSolarTotalW.setLocalText("MWh");
            }
            DecimalFormat df = new DecimalFormat("#.#");
            String formattedNumber = df.format(converted);
            mBinding.forecastSolarTotalValue.setLocalText(formattedNumber);
        } else {
            double converted;
            if (forecastSolarValue < 1000) {
                converted = forecastSolarValue;
                detailsBinding.forecastSolarsValueW.setLocalText("Wh");
            } else if (forecastSolarValue < 1_000_000) {
                converted = forecastSolarValue / 1000.0;
                detailsBinding.forecastSolarsValueW.setLocalText("kWh");
            } else {
                converted = forecastSolarValue / 1_000_000.0;
                detailsBinding.forecastSolarsValueW.setLocalText("MWh");
            }
            DecimalFormat df = new DecimalFormat("#.#");
            String formattedNumber = df.format(converted);
            detailsBinding .forecastSolarsValue.setLocalText(formattedNumber);
        }
    }

    /****************************************************** 处理数据 ********************************************************/

    public <T> List<T> getDataTruncation(long startTime, long gmTime, List<T> hourlyData) {
        DDLog.i(TAG, "startTime===" + startTime + "   gmTime===" + gmTime + "    " + hourlyData.size());
        List<T> result = new ArrayList<>();
        if (hourlyData == null) return result;
        // 计算相差小时数（整小时）
        long diffSeconds = gmTime - startTime;
        if (diffSeconds < 0) return result;
        int hourDiff = (int) (diffSeconds / 3600);
        int startIndex = hourDiff;
        int endIndex;
        if (hourlyData.size() < 36) {
            endIndex = hourlyData.size() ;
        } else {
            endIndex = Math.min(startIndex + 36, hourlyData.size());
        }
        DDLog.i(TAG, "分割操作： hourDiff===" + hourDiff + "  startIndex===" + startIndex +
                "  endIndex===" + endIndex + "  startTime===" + startTime + "   gmTime===" + gmTime +
                "  hourlyData.size(): " + hourlyData.size() + "  result.size(): " + result.size());
        result = new ArrayList<T>(hourlyData.subList(startIndex, endIndex));
        return result;
    }

    public static class SerializableMap implements Serializable {
        private Map<String, Object> objectMap;

        public SerializableMap(Map<String, Object> objectMap) {
            this.objectMap = objectMap;
        }

        public Map<String, Object> getObjectMap() {
            return objectMap;
        }

        public void setObjectMap(Map<String, Object> objectMap) {
            this.objectMap = objectMap;
        }
    }

}
