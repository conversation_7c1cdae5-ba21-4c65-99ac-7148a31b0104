package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.Constants;
import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmenntPsDataModeBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;


public class PSDataModeFragment extends MyBaseFragment<FragmenntPsDataModeBinding> {

    private String mDeviceId;

    public static PSDataModeFragment newInstance(String deviceId) {
        PSDataModeFragment fragment = new PSDataModeFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        fragment.setArguments(bundle);
        return fragment;
    }
    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragmennt_ps_data_mode;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.data_mode));
        mBinding.commonBar.commonBarBack.setOnClickListener(view -> removeSelf());
        mDeviceId = getArguments().getString(PSKeyConstant.KEY_DEVICE_ID);
        int dataMode = DBUtil.Num(DBKey.KEY_CURRENT_INFO_DATA_MODE + mDeviceId);
        mBinding.tvDataModeType.setLocalText(dataMode == Constants.TRUE_DATA_MODE ?
                getString(R.string.true_data) : getString(R.string.fake_data));
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.llDatatMode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                                getDelegateActivity().getSupportFragmentManager())
                        .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                        .setOtherButtonTitles(
                                Local.s(getString(R.string.true_data)),
                                Local.s(getString(R.string.fake_data)))
                        .setCancelableOnTouchOutside(true)
                        .setListener(new ActionSheet.ActionSheetListener() {

                            @Override
                            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                            }

                            @Override
                            public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                                if (index == 0) {
                                    DBUtil.Put(DBKey.KEY_CURRENT_INFO_DATA_MODE + mDeviceId, Constants.TRUE_DATA_MODE);
                                    mBinding.tvDataModeType.setLocalText(getString(R.string.true_data));
                                } else if (index == 1) {
                                    mBinding.tvDataModeType.setLocalText(getString(R.string.fake_data));
                                    DBUtil.Put(DBKey.KEY_CURRENT_INFO_DATA_MODE + mDeviceId, 0);
                                }
                            }
                        }).show();
            }
        });
    }
}
