package com.dinsafer.module.powerstation.electricity.bean;

import com.github.mikephil.charting.data.BarEntry;

public class ThresholdBarEntry extends BarEntry {
    
    // 阈值相关信息
    private float thresholdValue = 0f;  // 阈值数值
    private boolean hasThreshold = false;  // 是否存在阈值
    
    public ThresholdBarEntry(int xIndex, float val) {
        super(val, xIndex);
    }
    
    public ThresholdBarEntry(int xIndex, float val, Object data) {
        super(val, xIndex, data);
    }
    
    public ThresholdBarEntry(int xIndex, float[] vals) {
        super(vals, xIndex);
    }
    
    public ThresholdBarEntry(int xIndex, float[] vals, String data) {
        super(vals, xIndex, data);
    }
    
    /**
     * 设置阈值信息
     * @param thresholdValue 阈值数值
     */
    public void setThreshold(float thresholdValue) {
        this.thresholdValue = thresholdValue;
        this.hasThreshold = thresholdValue > 0;
    }
    
    /**
     * 获取阈值数值
     */
    public float getThresholdValue() {
        return thresholdValue;
    }
    
    /**
     * 是否存在有效阈值（阈值>0）
     */
    public boolean hasThreshold() {
        return hasThreshold && thresholdValue > 0;
    }
    
    /**
     * 当前数值是否超过阈值
     */
    public boolean exceedsThreshold() {
        return hasThreshold() && getVal() > thresholdValue;
    }

}
