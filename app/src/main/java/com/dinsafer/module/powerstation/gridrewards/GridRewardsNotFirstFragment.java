package com.dinsafer.module.powerstation.gridrewards;

import android.graphics.Typeface;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.StyleSpan;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.StyleSpan;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.dialog.RichTextAlertDialog;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentGridRewardsNotFirstBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSAdvancedSettingsAdapter;
import com.dinsafer.module.powerstation.adapter.ParticipationHourModel;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.bean.PSAdvancedSettingsItemBean;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/28
 * @author: create by Sydnee
 */
public class GridRewardsNotFirstFragment extends MyBaseFragment<FragmentGridRewardsNotFirstBinding> {


    private List<CountryBean> mCountryList;
    private FamilyBalanceContractInfoResponse.ResultBean mContractInfo;
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;

    private PSAdvancedSettingsAdapter infoAdapter;

    private final String KEY = DBKey.KEY_OLD_CONTRACT_DATA + HomeManager.getInstance().getCurrentHome().getHomeID();

    public static GridRewardsNotFirstFragment newInstance(List<CountryBean> countryList, FamilyBalanceContractInfoResponse.ResultBean contractInfo) {
        GridRewardsNotFirstFragment fragment = new GridRewardsNotFirstFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PSKeyConstant.KEY_COUNTRIES, (Serializable) countryList);
        bundle.putSerializable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, contractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_grid_rewards_not_first;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.prime_service_grid_reward));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.lyImg.setOnClickListener(v -> getDelegateActivity().addCommonFragment(GridRewardsFragment.newInstanceOnlyShow()));
        mBinding.tvTitle.setLocalText(getString(R.string.grid_title));
        mBinding.tvMore.setLocalText(getString(R.string.ps_is_learn_more));
        mBinding.commonBar.vDivider.setVisibility(View.GONE);
        initRv();
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mCountryList = (List<CountryBean>) bundle.getSerializable(PSKeyConstant.KEY_COUNTRIES);
            mContractInfo = (FamilyBalanceContractInfoResponse.ResultBean) bundle.getSerializable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
            mFamilyBalanceContractInfo = new FamilyBalanceContractInfo();
            if (mContractInfo != null) {
                FamilyBalanceContractInfoResponse.ResultBean.Data data = mContractInfo.getData();
                if (data != null) {
                    mFamilyBalanceContractInfo.setName(data.getName());
                    mFamilyBalanceContractInfo.setCompany_name(data.getCompany_name());
                    mFamilyBalanceContractInfo.setEuVatNumber(data.getEu_vat_number());
                    mFamilyBalanceContractInfo.setEmailAddress(data.getEmail_address());
                    mFamilyBalanceContractInfo.setCountry_code(data.getCountry_code());
                    mFamilyBalanceContractInfo.setCountryNameDisplay(data.getCountry_name_display());
                    mFamilyBalanceContractInfo.setCity(data.getCity());
                    mFamilyBalanceContractInfo.setStreetNameAndNumber(data.getStreet_name_and_number());
                    mFamilyBalanceContractInfo.setZipCode(data.getZip_code());
                    mFamilyBalanceContractInfo.setElectricitySupplier(data.getElectricity_supplier());
                    mFamilyBalanceContractInfo.setElectricitySupplierId(data.getElectricity_supplier_id());
                    mFamilyBalanceContractInfo.setCardholder(data.getCardholder());
                    mFamilyBalanceContractInfo.setIBAN(data.IBAN);
                }
                mFamilyBalanceContractInfo.setFirst_sign(mContractInfo.isFirst_sign());
                mFamilyBalanceContractInfo.setFamilySigning(mContractInfo.isSigning());
            }
            mFamilyBalanceContractInfo.setHome_name(HomeManager.getInstance().getCurrentHome().getHomeName());
            mFamilyBalanceContractInfo.setHome_id(HomeManager.getInstance().getCurrentHome().getHomeID());
        }

    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (!DBUtil.contain(KEY)) {
            getParticipationHoursData();
        }
    }

    private void initRv() {
        mBinding.rvInfo.setLayoutManager(new LinearLayoutManager(getContext()));
        infoAdapter = new PSAdvancedSettingsAdapter();
        mBinding.rvInfo.setAdapter(infoAdapter);

        List<PSAdvancedSettingsItemBean> data = new ArrayList<>();
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.participation_hours), R.drawable.btn_device_setting_arrow));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.bank_account), R.drawable.btn_device_setting_arrow));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.authorization_records), R.drawable.btn_device_setting_arrow));
        infoAdapter.setNewData(data);

        infoAdapter.setOnItemClickListener((adapter, view, position) -> {
            PSAdvancedSettingsItemBean bean = infoAdapter.getItem(position);
            if (bean.getKey().equals(getString(R.string.participation_hours))) {
                getDelegateActivity().addCommonFragment(ParticipationHoursFragment.newInstance(0));
            } else if (bean.getKey().equals(getString(R.string.bank_account))) {
                getDelegateActivity().addCommonFragment(PSBankAccountFragment.newInstance(mFamilyBalanceContractInfo));
            } else if (bean.getKey().equals(getString(R.string.authorization_records))) {
                getDelegateActivity().addCommonFragment(PSAuthorizationRecordsFragment.newInstance(mFamilyBalanceContractInfo));
            }
        });
    }

    private void getParticipationHoursData() {
        DinHome.getInstance().bmtBalanceContractParticipationHours(0, HomeManager.getInstance().getCurrentHome().getHomeID(), 10, new IDefaultCallBack2<BmtBalanceContractParticipationHoursResponse.ResultBean>() {
            @Override
            public void onSuccess(BmtBalanceContractParticipationHoursResponse.ResultBean resultBean) {
                if (resultBean != null) {
                    List<ParticipationHour> participationHours = resultBean.getDatas();
                    if (participationHours != null && participationHours.size() == 1) {
                        ParticipationHour participationHour = participationHours.get(0);
                        String start = participationHour.getStart();
                        String end = participationHour.getEnd();
                        List<Integer> repeat = participationHour.getRepeat();
                        if (participationHour.getAll_day() && repeat.size() == 7) {
                            runOnMainThread(new Runnable() {
                                @Override
                                public void run() {
                                    show247Dialog();
                                }
                            });
                        }
                    }
                }
            }

            @Override
            public void onError(int i, String s) {

            }
        });
    }

    private void show247Dialog() {
        String fullText = Local.s(getString(R.string.participate_in_balancing_24_7_tips));
        SpannableString spannableString = new SpannableString(fullText);
        String boldText = Local.s(getString(R.string.Participation_Hours));
        int startIndex = fullText.indexOf(boldText);
        int endIndex = startIndex + boldText.length();
        spannableString.setSpan(new StyleSpan(Typeface.BOLD), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        DBUtil.Put(KEY, true);
        RichTextAlertDialog.createBuilder(getDelegateActivity())
                .showTitle(false)
                .setContentTxt(spannableString)
                .setContentTextPaddingTop(DensityUtils.dp2px(getContext(), 29))
                .setContentTextPaddingBottom(DensityUtils.dp2px(getContext(), 29))
                .setConfirmTxt(getString(R.string.Confirm))
                .setCancelTxt(getString(R.string.Go_Settings))
                .setAutoDismiss(true)
                .setShowCancel(true)
                .setConfirmCallback(new RichTextAlertDialog.OnConfirmCallback() {
                    @Override
                    public void onConfirm(RichTextAlertDialog dialog) {

                    }

                    @Override
                    public void onCancel(RichTextAlertDialog dialog) {
                        getDelegateActivity().addCommonFragment(ParticipationHoursFragment.newInstance(0));
                    }
                })
                .builder().show();
    }
}
