package com.dinsafer.module.powerstation.device;

import android.Manifest;
import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.DecelerateInterpolator;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.HomeManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.utils.BleHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.BleStepScanDeviceLayoutBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.model.BleCloseTimerEvent;
import com.dinsafer.model.BleDeviceSimpleEntry;
import com.dinsafer.model.BleStartScanEvent;
import com.dinsafer.model.WindowFocusChangedEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.BleCheckBluetoothDialog;
import com.dinsafer.module.add.ui.adapter.BleScanDeviceAdapter;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.powerstation.PSProductNameFragment;
import com.dinsafer.module.powerstation.adapter.PSAddDeviceHintAdapter;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module.powerstation.settings.network.PSNetworkSettingsFragment;
import com.dinsafer.module_bmt.add.BaseBmtBinder;
import com.dinsafer.module_bmt.add.BmtHP5000Binder;
import com.dinsafer.module_bmt.add.BmtHP5001Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore20Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore30Binder;
import com.dinsafer.module_bmt.add.BmtPowerPulseBinder;
import com.dinsafer.module_bmt.add.BmtPowerStoreBinder;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.add.PanelBinder;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.ScreenUtils;
import com.github.sahasbhop.apngview.ApngDrawable;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 *
 */
public class PSBleScanFragment extends MyBaseFragment<BleStepScanDeviceLayoutBinding> implements BleHelper.ConnectCallback {
    private static final int REQ_PERMISSION_LOCATION = 1124;

    private String TAG = "PSBleScanFragment bletest";
    private Subscription connectTimer;
    private BleScanDeviceAdapter bleScanDeviceAdapter;
    private int listMarginTopPx;
    private boolean isUp = true;
    private ArrayList<BleDevice> bleDeviceArrayList;
    private ArrayList<BleDeviceSimpleEntry> simpleBleList;
    private boolean isScan = false;

    public final static int ANIM_UP_TIME = 300;
    private BaseBmtBinder mBinder;

    private String currentID;

    private String provider;
    private String deviceCode;

    private PSAddDeviceHintAdapter mAddDeviceHintAdapter;
    private List<String> mValidProvider = new ArrayList<>();

    private boolean isFirst = true;

    public static PSBleScanFragment newInstance(String deviceCode) {
        Bundle args = new Bundle();
        args.putString("deviceCode", deviceCode);
        PSBleScanFragment fragment = new PSBleScanFragment();
        fragment.setArguments(args);
        return fragment;
    }

    private BaseBmtBinder.IModelCallback mModelCallback = new BaseBmtBinder.IModelCallback() {
        @Override
        public void onModelCallback(String cmd, int status, String result) {
            runOnMainThread(() -> {
                if (cmd != null) {
                    if (cmd.equals("get_model")) {
                        closeLoadingFragment();
                        if (status == 1) {
                            if (!TextUtils.isEmpty(result)
                                    && !result.equals(DinConst.TYPE_BMT_POWERSTORE2)) {
                                provider = result;
                                mBinder.setModel(result);
                            }
                        }
                        mBinder.setModel(provider);
                        if (BmtUtil.isSupportProvider(mBinder.getHardwareVersion(), provider)) {
                            getDelegateActivity().addCommonFragment(PSNetworkSettingsFragment.newInstanceForScanAdd(currentID, provider, true, false));
                        } else {
                            getDelegateActivity().addCommonFragment(PSProductNameFragment.newInstance(currentID));
                        }
                    } else if (cmd.equals("get_valid_models")) {
                        if (status == 1) {
                            closeLoadingFragment();
                            if (!TextUtils.isEmpty(result)) {
                                mValidProvider.add(result);
                            }
                            if (mValidProvider.contains(provider)) {
                                getDelegateActivity().addCommonFragment(PSNetworkSettingsFragment.newInstanceForScanAdd(currentID, provider, true, false));
                            } else {
                                getDelegateActivity().addCommonFragment(PSProductNameFragment.newInstance(currentID));
                            }
                        } else if (status == 2) {
                            if (!TextUtils.isEmpty(result)) {
                                mValidProvider.add(result);
                            }
                        } else {
                            closeLoadingFragment();
                            showErrorToast();
                        }
                    }
                }
            });
        }
    };


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.ble_step_scan_device_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonBarRight.setOnClickListener(v -> toHelp());
        mBinding.commonBarBack.setOnClickListener(v -> toBack());
        mBinding.imgBtnUp.setOnClickListener(v -> toUp());
        mBinding.title.setOnClickListener(v -> toUp());
        mBinding.centerIcon.setVisibility(View.GONE);
        mBinding.lavIcon.setVisibility(View.VISIBLE);
        initRv();
    }

    @Override
    public void initData() {
        super.initData();
        deviceCode = getArguments().getString("deviceCode");
        provider = deviceCode;
        bleDeviceArrayList = new ArrayList<BleDevice>();
        simpleBleList = new ArrayList<BleDeviceSimpleEntry>();

        tmpBleDeviceArrayList = new ArrayList<BleDevice>();
        tmpSimpleBleList = new ArrayList<BleDeviceSimpleEntry>();
        mBinding.hintToCloseToPhone.setLocalText(getResources().getString(R.string.ble_ipc_scan_close_to_phone));
        mBinding.commonBarRight.setLocalText(getResources().getString(R.string.ble_scan_right_top_word));
        mBinding.title.setLocalText(getResources().getString(R.string.choose_a_device));

        /**
         * listMarginTopPx 为layout离顶部的距离
         */
        listMarginTopPx = ScreenUtils.getScreenHeight(getDelegateActivity()) * 3 / 5;

        mBinding.root.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                // listMarginTopPx = mBinding.lySearch.getMeasuredHeight();
                mBinding.root.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });

        mBinding.rvDeviceList.setLayoutManager(new LinearLayoutManager(getDelegateActivity(), LinearLayoutManager.VERTICAL, false));
        bleScanDeviceAdapter = new BleScanDeviceAdapter(simpleBleList);
        mBinding.rvDeviceList.setAdapter(bleScanDeviceAdapter);

        bleScanDeviceAdapter.setOnItemClick(new BleScanDeviceAdapter.OnItemClick() {
            @Override
            public void connect(BleDeviceSimpleEntry bleDeviceSimpleEntry, int position) {
                closeScanTimer();

                showLoadingFragment(LoadingFragment.BLUE);

                currentID = bleDeviceArrayList.get(position).getName();
                mBinder.connectDevice(bleDeviceArrayList.get(position),
                        PSBleScanFragment.this);

                connectTimer = Observable.interval(APIKey.BLE_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                        .take(1)
                        .observeOn(AndroidSchedulers.mainThread())
                        .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                        .subscribe(new Subscriber<Object>() {
                            @Override
                            public void onCompleted() {

                            }

                            @Override
                            public void onError(Throwable e) {

                            }

                            @Override
                            public void onNext(Object o) {
                                DDLog.d(TAG, "subscribe里");
                                fail();
                            }
                        });
            }
        });


        mBinding.lyDeviceList.setVisibility(View.GONE);

        mBinding.centerIcon.setImageResource(DinConst.TYPE_BMT_HP5001.equals(provider) ? R.drawable.img_ps_ipc_wireless_power_on : R.drawable.img_ps_ipc_wireless_power_on);

        DinSDK.getPluginActivtor().setup(DinSaferApplication.getAppContext());
        if (DinConst.TYPE_BMT_HP5001.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtHP5001Binder();
        } else if (DinConst.TYPE_BMT_POWERCORE20.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerCore20Binder();
        } else if (DinConst.TYPE_BMT_POWERSTORE.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerStoreBinder();
        } else if (DinConst.TYPE_BMT_POWERPULSE.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerPulseBinder();
        } else if (DinConst.TYPE_BMT_POWERCORE30.equals(provider)) {
            DinSDK.getPluginActivtor().createBmtPowerCore30Binder();
        } else {
            DinSDK.getPluginActivtor().createBmtHP5000Binder();
        }
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof BmtHP5000Binder) && !(pluginBinder instanceof BmtHP5001Binder)
                && !(pluginBinder instanceof BmtPowerCore20Binder)
                && !(pluginBinder instanceof BmtPowerCore30Binder)
                && !(pluginBinder instanceof BmtPowerStoreBinder)
                && !(pluginBinder instanceof BmtPowerPulseBinder)) {
            DDLog.e(TAG, "Error bmtBinder binder.");
            showErrorToast();
            return;
        }
        mBinder = (BaseBmtBinder) pluginBinder;
        mBinder.setBindHomeID(HomeManager.getInstance().getCurrentHome().getHomeID());
        mBinder.setDataHost(APIKey.STATISTICS_HTTP_BASE_URL);
        mBinder.setVersionCallback(new BaseBmtBinder.IVersionCallback() {
            @Override
            public void onVersion(String cmd, int status, String version) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!TextUtils.isEmpty(cmd) && cmd.equals("get_version")) {
                            if (status == 1) {
                                if (mBinder.compareVersion() < 0) {
                                    closeLoadingFragment();
                                    mBinder.setModel(DinConst.TYPE_BMT_HP5000);
                                    getDelegateActivity().addCommonFragment(PSNetworkSettingsFragment.newInstanceForScanAdd(currentID, DinConst.TYPE_BMT_HP5000, true, false));
                                } else {
                                    getModel();
                                }
                            } else {
                                closeLoadingFragment();
                                showErrorToast();
                            }
                        }
                    }
                });
            }
        });
    }


    private void initRv() {
        mBinding.hintToCloseToPhone.setVisibility(View.GONE);
        mBinding.rvHint.setVisibility(View.VISIBLE);
        mAddDeviceHintAdapter = new PSAddDeviceHintAdapter();
        mBinding.rvHint.setLayoutManager(new LinearLayoutManager(getContext()));
        List<String> hints = new ArrayList<>();
        hints.add(getContext().getString(R.string.ps_add_device_hint));
        hints.add(getContext().getString(R.string.ble_scan_close_to_phone));
        hints.add(getContext().getString(R.string.ps_add_device_hint_3));
        mAddDeviceHintAdapter.setNewData(hints);
        mBinding.rvHint.setAdapter(mAddDeviceHintAdapter);
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinding.rvDeviceList.removeCallbacks(scanRunnable);
        clean();
        if (null != mBinder) {
            mBinder.setModelCallback(null);
            mBinder.destroyBinder();
        }
        EventBus.getDefault().unregister(this);
    }

    public void toHelp() {
        showOpenDeviceBle();
    }

    private void toStartAnim() {
        if (mBinding.centerIcon == null) {
            return;
        }

        ApngDrawable apngDrawable = ApngDrawable.getFromView(mBinding.centerIcon);
        if (apngDrawable == null) return;

        if (!apngDrawable.isRunning()) {
            apngDrawable.start(); // Stop animation
        }
    }


    private void toStopAnim() {
        DDLog.d(TAG, "toStopAnim");
        if (mBinding.centerIcon == null) {
            return;
        }

        ApngDrawable apngDrawable = ApngDrawable.getFromView(mBinding.centerIcon);
        if (apngDrawable == null) return;

        if (apngDrawable.isRunning()) {
            apngDrawable.stop(); // Stop animation
        }
    }

    private Runnable scanRunnable = () -> realStartScan();

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        bleDeviceArrayList.clear();
        simpleBleList.clear();
        bleScanDeviceAdapter.notifyDataSetChanged();
        mBinding.rvDeviceList.postDelayed(scanRunnable, 500);
    }

    @Override
    public void onPauseFragment() {
        DDLog.d(TAG, "onPauseFragment");
        super.onPauseFragment();
        mBinding.rvDeviceList.removeCallbacks(scanRunnable);
        toStopAnim();

    }

    @Override
    public void onExitFragment() {
        DDLog.d(TAG, "onExitFragment");
        super.onExitFragment();
        toStopAnim();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        realStartScan();
    }

    public void toBack() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
//        removeSelf();
    }

    private ArrayList<BleDevice> mdata;

    public void toUp() {
        ObjectAnimator animator;
        if (isUp) {

            animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", listMarginTopPx, 0);
            mBinding.imgBtnUp.setImageDrawable(DinSaferApplication.getAppContext().getResources().getDrawable(R.drawable.btn_cell_device_down));
        } else {
            animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", 0, listMarginTopPx);
            mBinding.imgBtnUp.setImageDrawable(DinSaferApplication.getAppContext().getResources().getDrawable(R.drawable.btn_cell_device_up));
        }

        animator.setDuration(ANIM_UP_TIME);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.start();
        isUp = !isUp;
    }

    private boolean isNowAppear = false;

    public void toAppear(boolean isAppear) {
        DDLog.d(TAG, "toAppear");
        if (this.isNowAppear == isAppear) {
            return;
        }
        if (isAppear) {

            ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", ScreenUtils.getScreenHeight(getDelegateActivity()), listMarginTopPx);
            animator.setDuration(300);
            animator.setInterpolator(new DecelerateInterpolator());
            animator.start();

        } else {

            ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", listMarginTopPx, ScreenUtils.getScreenHeight(getDelegateActivity()));
            animator.setDuration(300);
            animator.setInterpolator(new DecelerateInterpolator());
            animator.start();
        }

        this.isNowAppear = isAppear;
    }

    private void toDisappear() {
        if (!isNowAppear) {
            return;
        }
        mBinding.imgBtnUp.setImageDrawable(DinSaferApplication.getAppContext().getResources().getDrawable(R.drawable.btn_cell_device_up));
        Log.d(TAG, "toDisappear: " + mBinding.lyDeviceList.getTranslationY());
        ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", mBinding.lyDeviceList.getTranslationY(), ScreenUtils.getScreenHeight(getDelegateActivity()));
        animator.setDuration(300);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mBinding.lyDeviceList.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animator.start();
        isNowAppear = false;
        isUp = true;
    }

    private ArrayList<BleDevice> tmpBleDeviceArrayList;
    private ArrayList<BleDeviceSimpleEntry> tmpSimpleBleList;

    private final BleScanCallback bleScanCallback = new BleScanCallback() {
        @Override
        public void onScanStarted(boolean success) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    DDLog.d(TAG, "开始扫描");
                    tmpBleDeviceArrayList.clear();
                    tmpSimpleBleList.clear();
                    if (bleDeviceArrayList.size() <= 0) {
                        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
                            return;
                        }
                        startShowTipTimer();
                    }
                }
            });
        }

        @Override
        public void onScanning(BleDevice bleDevice) {
            // 扫描到一个符合扫描规则的BLE设备（主线程）
            if (bleDevice.getName() == null) {
                return;
            }
            getDelegateActivity().runOnUiThread(new Runnable() {
                @SuppressLint("MissingPermission")
                @Override
                public void run() {
                    DDLog.d(TAG, "扫描中");

                    if (!getDelegateActivity().isFragmentInTop(PSBleScanFragment.class.getSimpleName())) {
                        return;
                    }

                    /**
                     * 名字：ydEqumoVNyhPiKfg
                     * 显示：device id 的后四位
                     */
                    String name = bleDevice.getName();
                    BleDeviceSimpleEntry bleDeviceSimpleEntry = new BleDeviceSimpleEntry(
                            PanelBinder.isNewDevice(bleDevice)
                                    ? BleDeviceSimpleEntry.BLE_DEVICE_STATUS_NORMAL
                                    : BleDeviceSimpleEntry.BLE_DEVICE_STATUS_DISABLE,
                            name.substring(name.length() - 4),
                            false);
                    bleDeviceSimpleEntry.setIconID(R.drawable.icon_ps_cell_device);
                    Log.d(TAG, "run: " + name + " /type:" + bleDeviceSimpleEntry.getType());
                    if (bleDeviceSimpleEntry.getType() != BleDeviceSimpleEntry.BLE_DEVICE_STATUS_DISABLE) {
                        DDLog.d(TAG, "tmpBleDeviceArrayList 增加了");
                        tmpBleDeviceArrayList.add(bleDevice);
                        tmpSimpleBleList.add(bleDeviceSimpleEntry);
                    }

                    DDLog.d(TAG, "simpleBleList is " + simpleBleList.toString());
                    if (bleDeviceSimpleEntry.getType() != BleDeviceSimpleEntry.BLE_DEVICE_STATUS_DISABLE && !simpleBleList.contains(bleDeviceSimpleEntry)) {
                        DDLog.d(TAG, "bleDeviceArrayList 增加了 !simpleBleList.contains(bleDeviceSimpleEntry");
                        bleDeviceArrayList.add(bleDevice);
                        simpleBleList.add(bleDeviceSimpleEntry);
                        bleScanDeviceAdapter.notifyDataSetChanged();
                    }


                    if (mBinding.lyDeviceList != null && mBinding.lyDeviceList.getVisibility() != View.VISIBLE && simpleBleList.size() != 0) {
                        mBinding.lyDeviceList.setVisibility(View.VISIBLE);
                        toAppear(true);
                    }

                    /**
                     * 如果扫描到，就取消扫描不到出现打开主机蓝牙提示的定时器
                     */
                    closeShowTipTimer();

                }
            });
        }

        @Override
        public void onScanFinished(List<BleDevice> scanResultList) {
            if (!getDelegateActivity().isFragmentInTop(PSBleScanFragment.class.getSimpleName())) {
                return;
            }

            getDelegateActivity().runOnUiThread(
                    new Runnable() {
                        @Override
                        public void run() {
// 扫描结束，列出所有扫描到的符合扫描规则的BLE设备（主线程）
                            DDLog.d(TAG, "扫描结束");
                            isScan = false;

                            DDLog.d(TAG, "tmpBleDeviceArrayList size is " + tmpBleDeviceArrayList.size());
                            DDLog.d(TAG, "simpleBleList size is " + simpleBleList.size());
                            bleDeviceArrayList.clear();
                            bleDeviceArrayList.addAll(tmpBleDeviceArrayList);
                            simpleBleList.clear();
                            simpleBleList.addAll(tmpSimpleBleList);
                            bleScanDeviceAdapter.notifyDataSetChanged();

                            if (mBinding.lyDeviceList != null && mBinding.lyDeviceList.getVisibility() == View.VISIBLE && simpleBleList.size() == 0) {
                                toAppear(false);
                                mBinding.lyDeviceList.setVisibility(View.GONE);
                            }
                        }
                    });
        }
    };


    BleCheckBluetoothDialog dialog = null;

    /**
     * 如果弹窗选择了退出当前页，那么久不再在onWindowFacous做判断。是否弹出弹窗
     */
    private boolean isQuit = false;

    public void showOpenPhoneBle() {
        if (dialog != null && dialog.isShowing()) {
            return;
        } else {
            isQuit = false;
            dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_PHONE);
            dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    if (null != mBinder) {
                        dialog.dismiss();
                        realStartScan();
                    } else {
                        dialog.dismiss();
                    }
                }

                @Override
                public void clickCanal() {
                    isQuit = true;
                    dialog.dismiss();
                }
            });
            dialog.show();
        }
    }

    private void stopScan() {
        try {
            if (isScan) {
                BleManager.getInstance().cancelScan();
                isScan = false;
            }

        } catch (Exception e) {

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(WindowFocusChangedEvent ev) {
        if (!BleManager.getInstance().isBlueEnable() && !isQuit) {
            toDisappear();
            showOpenPhoneBle();
            getDelegateActivity().removeToFragment(getClass().getName());
        }
    }

    public void showOpenDeviceBle() {
        /**
         * 打开弹窗，提示检查主机蓝牙
         */
        if (dialog != null && dialog.isShowing()) {
            DDLog.d(TAG, "dialog != null && dialog.isShowing()");
            return;
        }

        DDLog.d(TAG, "dialog == null ||  dialog.isNotShowing()");
        dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_BMT_DEVICE);
        dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
            @Override
            public void clickOk() {
                dialog.dismiss();
                if (getDelegateActivity().isCommonFragmentExist(PSBleScanFragment.class.getName())) {
                    getDelegateActivity().removeToFragment(PSBleScanFragment.class.getName());

                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                        if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
                                || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                            showNeedLocationPermissionDialog();
                            return;
                        }
                        if (!DDSystemUtil.isOpenGPS(getContext())) {
                            toOpenGPS(0);
                            return;
                        }
                    } else {
                        if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                                || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                            PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), null);
                            return;
                        }
                    }

                    realStartScan();
                }
            }

            @Override
            public void clickCanal() {
                dialog.dismiss();
                removeSelf();
            }
        });
        dialog.show();
    }

    /**
     * 开启扫描动画和真正开始扫描
     */
    private void realStartScan() {
        DDLog.i(TAG, "realStartScan");
        if (null != mBinder && !BleManager.getInstance().isBlueEnable()) {
            showOpenPhoneBle();
            return;
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                showNeedLocationPermissionDialog();
                return;
            }
            if (!DDSystemUtil.isOpenGPS(getContext())) {
                toOpenGPS(0);
                return;
            }
        } else {
            if (ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                PermissionDialogUtil.requestBluetoothPermission(getMainActivity(), () -> {
                    DDLog.i(TAG, "bluetooth permission granted!!!");
                    realStartScan();
                });
                return;
            }
        }


        toStartAnim();
        startScanTimer();
    }

    private void requestLocationPermission() {
        final String[] permission = new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION};
        boolean denied = AndPermission.hasAlwaysDeniedPermission(getMainActivity(), permission);

        AndPermission.with(this)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    PermissionDialogUtil.hide();
                    if (null != dialog && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                    realStartScan();
                })
                .onDenied(permissions -> {
                    DDLog.e(TAG, "Location permission deny!!!");
                    if (denied
                            && AndPermission.hasAlwaysDeniedPermission(PSBleScanFragment.this, permissions)) {
                        openSystemSetting();
                    }
                })
                .start();
    }

    /**
     * 提示需要申请定位权限
     */
    public void showNeedLocationPermissionDialog() {
        if (dialog != null && dialog.isShowing()) {
            DDLog.d(TAG, "dialog != null && dialog.isShowing()");
            return;
        }

        toStopAnim();
        clean();

        PermissionDialogUtil.showNeedBleLocationPermissionDialog(getMainActivity(), false, v -> {
            isQuit = true;
            if (null != dialog) {
                dialog.dismiss();
            }
            removeSelf();
        }, v -> {
            getMainActivity().setNotNeedToLogin(true);
            requestLocationPermission();
        });
    }

    /**
     * 开启系统设置页
     */
    protected void openSystemSetting() {
        DDLog.i(TAG, "openSystemSetting");
        try {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setData(Uri.parse("package:" + getContext().getPackageName()));
            getMainActivity().setNotNeedToLogin(true);
            startActivityForResult(intent, REQ_PERMISSION_LOCATION);
        } catch (Exception e) {
            DDLog.e(TAG, "Can't open system setting!!!");
            e.printStackTrace();
            showErrorToast();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (REQ_PERMISSION_LOCATION == requestCode) {
            if (ContextCompat.checkSelfPermission(getMainActivity(),
                    Manifest.permission.ACCESS_COARSE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED) {
                return;
            }

            PermissionDialogUtil.hide();
            realStartScan();
            return;
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    private Subscription scanTimer;

    /**
     * 启动定时器
     */
    public void startScanTimer() {
//        //扫描超时时间+1

        closeScanTimer();
        scanTimer = Observable.interval(0, APIKey.BLE_SCAN_TIMEOUT + 500, TimeUnit.MILLISECONDS)
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "onNext");
                        mBinder.discoveryDevice(APIKey.BLE_SCAN_TIMEOUT, bleScanCallback);
                    }
                });

    }

    public void closeScanTimer() {
        if (scanTimer != null && !scanTimer.isUnsubscribed()) {
            scanTimer.unsubscribe();
        }
        mBinder.stopDiscoveryDevice();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleStartScanEvent ev) {
//        startTimer();
//        toStartAnim();
        DDLog.d(TAG, "BleStartScanEvent");
    }

    /**
     * 失败处理
     */
    private void fail() {
        DDLog.d(TAG, "onFail");
        simpleBleList.clear();
        bleDeviceArrayList.clear();
        bleScanDeviceAdapter.notifyDataSetChanged();
        showErrorToast();
        mBinder.stop();
        closeLoadingFragment();
        if (connectTimer != null && !connectTimer.isUnsubscribed()) {
            connectTimer.unsubscribe();
        }
        realStartScan();
    }

    /**
     * 清空处理：定时器、loading
     */
    private void clean() {
        DDLog.d(TAG, "clean");
        closeScanTimer();
        if (connectTimer != null && !connectTimer.isUnsubscribed()) {
            connectTimer.unsubscribe();
        }
        closeShowTipTimer();
        closeLoadingFragment();
    }


    /**
     * 关闭定时器，用于其他弹窗出现的时候————ble断开的时候
     *
     * @param ev
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleCloseTimerEvent ev) {
        clean();
    }

    private Subscription showTipTimer;

    /**
     * 在没有数据的情况下，每隔三分钟弹出一次help框
     */
    private void startShowTipTimer() {
        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
            showTipTimer.unsubscribe();
        }

        showTipTimer = Observable.interval(APIKey.BLE_SCAN_NO_DEVICE_TIP_TIMEOUT, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        toHelp();
                    }
                });

    }

    private void closeShowTipTimer() {
        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
            showTipTimer.unsubscribe();
        }
    }

    @Override
    public boolean onBackPressed() {
        toBack();
        return true;

    }

    @Override
    public void onStartConnect() {

    }

    @Override
    public void onConnectFail(String s) {
        fail();
    }

    @Override
    public void onConnectSuccess() {
        DDLog.i(TAG, "onConnectSuccess==uuid=" + mBinder.getConnectedUUID());
        cleanSuccess();
        if (TextUtils.isEmpty(mBinder.getConnectedUUID())) {
            showErrorToast();
            closeLoadingFragment();
            return;
        }
        /**
         * Power core 3.0和Power core2.0 共用一个二维码；添加到3.0 uuid时需修改provider
         * */
        if (mBinder.isThreePointZero()) {
            provider = DinConst.TYPE_BMT_POWERCORE30;
        }
        mBinder.setModel(provider);
        DDLog.i(TAG, "setModel provider:  " + provider);

        if (mBinder.isOldUUID()) {
            mBinder.getVersion();
        } else {
            getModel();
        }
    }

    /**
     * 清空处理：定时器、loading
     */
    private void cleanSuccess() {
        DDLog.d(TAG, "clean");
        closeScanTimer();
        if (connectTimer != null && !connectTimer.isUnsubscribed()) {
            connectTimer.unsubscribe();
        }
        closeShowTipTimer();
    }

    private void getModel() {
        mBinder.setModelCallback(mModelCallback);
        mBinder.getModel();
    }

    @Override
    public void onDisConnected() {
        DDLog.i(TAG, "onDisConnected");
        getDelegateActivity().removeToFragment(getClass().getName());
        showOpenDeviceBle();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(FinishAddBmtEvent event) {
        removeSelf();
    }
}
