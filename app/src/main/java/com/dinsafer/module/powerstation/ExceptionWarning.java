package com.dinsafer.module.powerstation;

import android.content.Context;

import androidx.annotation.NonNull;

import com.dinsafer.config.PsVersion1EventCode;
import com.dinsafer.dinnet.R;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ExceptionWarning {

    private static final String TAG = ExceptionWarning.class.getSimpleName();
    public static String currentFamily = "";
    public static String deviceName = "";


    public static WarningBean getVertBatteryWarningBean(Context context, int code, boolean isBmtDeviceV2) {
        String[] titles = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_battery_exception_titles) : context.getResources().getStringArray(R.array.ps_vert_battery_exception_titles);
        String[] contents = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_battery_exception_contents) : context.getResources().getStringArray(R.array.ps_vert_battery_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_1000) + (code - 1));

        WarningBean warningBean = new WarningBean(titles[code - 1], contents[code - 1], true, eventBmt);
        return warningBean;
    }

    public static WarningBean getInvertWarningBean(Context context, int code, boolean isBmtDeviceV2) {
        String[] titles = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_inverter_exception_titles) : context.getResources().getStringArray(R.array.ps_inverter_exception_titles);
        String[] contents = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_battery_exception_contents) : context.getResources().getStringArray(R.array.ps_inverter_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_1100) + (code - 1));
        return new WarningBean(titles[code - 1], contents[code - 1], true, eventBmt);
    }

    public static WarningBean getVertGridWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_vert_grid_exception_titles);
        String[] contents = context.getResources().getStringArray(R.array.ps_vert_grid_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_1200) + (code - 1));

        WarningBean bean = new WarningBean(titles[code - 1], contents[code - 1], true, eventBmt);
        if (PsVersion1EventCode.EVENT_PS_1203.equals(eventBmt)
                || PsVersion1EventCode.EVENT_PS_1204.equals(eventBmt)
                || PsVersion1EventCode.EVENT_PS_1205.equals(eventBmt)) {
            bean.setWithout(true);
            bean.setWithoutText(context.getResources().getString(R.string.bmt_exception_without_1203));
        }
        return bean;
    }

    public static WarningBean getVertSystemWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_vert_system_exception_titles);
        String[] contents = context.getResources().getStringArray(R.array.ps_vert_system_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_1300) + (code - 1));

        return new WarningBean(titles[code - 1], contents[code - 1], true, eventBmt);
    }

    public static WarningBean getVertMpptWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_vert_mppt_exception_titles);
        String[] contents = context.getResources().getStringArray(R.array.ps_vert_mppt_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_1400) + (code - 1));

        return new WarningBean(titles[code - 1], contents[code - 1], true, eventBmt);
    }

    public static WarningBean getVertPresentWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_vert_present_exception_titles);
        String[] contents = context.getResources().getStringArray(R.array.ps_vert_present_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        return new WarningBean(titles[code - 1], contents[code - 1]);
    }

    public static WarningBean getVertDcWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_vert_dc_exception_titles);
        String[] contents = context.getResources().getStringArray(R.array.ps_vert_dc_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }

        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_1600) + (code - 1));
        return new WarningBean(titles[code - 1], contents[code - 1], true, eventBmt);
    }

    public static WarningBean getBatteryWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_battery_exception_titles);
        String[] contents = context.getResources().getStringArray(R.array.ps_battery_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        WarningBean warningBean = new WarningBean(titles[code - 1], contents[code - 1]);
        warningBean.setEventKey(context.getResources().getString(R.string.bmt_battery_exception_key));
        return warningBean;
    }

    public static WarningBean getMPPTWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_mppt_exception_titles);
        String[] contents = context.getResources().getStringArray(R.array.ps_mppt_exception_contents);
        if (code < 1 || code > titles.length || code == 5) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        WarningBean warningBean = new WarningBean(titles[code - 1], contents[code - 1]);
        return warningBean;
    }

    public static WarningBean getEVWarningBean(Context context, int code, boolean isBmtDeviceV2) {
        String[] titles = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_ev_exception_titles) : context.getResources().getStringArray(R.array.ps_ev_exception_titles);
        String[] contents = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_ev_exception_contents) : context.getResources().getStringArray(R.array.ps_ev_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }

        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_2000) + (code - 1));
        WarningBean warningBean = new WarningBean(titles[code - 1], contents[code - 1], true, eventBmt);
        return warningBean;
    }

    public static WarningBean getCabinetWarningBean(Context context, int code, boolean isBmtDeviceV2) {
        if (code == 2) return null;
        String[] titles = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_cabinet_exception_titles) : context.getResources().getStringArray(R.array.ps_cabinet_exception_titles);
        String[] contents = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_battery_exception_contents) : context.getResources().getStringArray(R.array.ps_cabinet_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        WarningBean warningBean = new WarningBean(titles[code - 1], contents[code - 1]);
        return warningBean;
    }

    public static WarningBean getSystemWarningBean(Context context, int code) {
        String[] titles = context.getResources().getStringArray(R.array.ps_system_exception_tittles);
        String[] contents = context.getResources().getStringArray(R.array.ps_system_exception_contents);
        if (code < 1 || code == 10 || code > titles.length) {
            DDLog.i(TAG, "数组越界, 在数组找不到对应code的值");
            return null;
        }
        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_6000) + (code - 1));
        boolean restart = !PsVersion1EventCode.EVENT_PS_6002.equals(eventBmt)
                && !PsVersion1EventCode.EVENT_PS_6005.equals(eventBmt)
                && !PsVersion1EventCode.EVENT_PS_6006.equals(eventBmt)
                && !PsVersion1EventCode.EVENT_PS_6008.equals(eventBmt);
        return new WarningBean(titles[code - 1], contents[code - 1], restart, eventBmt);

    }

    public static WarningBean getCommunicationWarningBean(Context context, int code, boolean isBmtDeviceV2) {
        String[] titles = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_communication_exception_titles) : context.getResources().getStringArray(R.array.ps_communication_exception_titles);
        String[] contents = isBmtDeviceV2 ? context.getResources().getStringArray(R.array.ps2_communication_exception_contents) : context.getResources().getStringArray(R.array.ps_communication_exception_contents);
        if (code < 1 || code > titles.length) {
            DDLog.e(TAG, "数组越界, 在数组找不到对应" + code + "的值");
            return null;
        }
        WarningBean warningBean;
        String eventBmt = String.valueOf(Integer.parseInt(PsVersion1EventCode.EVENT_PS_7000) + (code - 1));
        if (eventBmt.equals(PsVersion1EventCode.EVENT_PS_7001)) {
            warningBean = new WarningBean(titles[code - 1], contents[code - 1]
                    , context.getResources().getString(R.string.bmt_exception_without_7001)
                    , true, true, PsVersion1EventCode.EVENT_PS_7001);
        } else {
            warningBean = new WarningBean(titles[code - 1], contents[code - 1]);
        }
        return warningBean;
    }

    @NonNull
    public static List<WarningBean> createWarningBeanForInverter(Context context, Map<String, Object> inverterInfoResult, boolean isBmtDeviceV2) {
        List<WarningBean> result = new ArrayList<>();
        if (null != inverterInfoResult) {
            List<Integer> vertBatteryExceptions = (List<Integer>) MapUtils.get(inverterInfoResult, BmtDataKey.VERT_BATTERY_EXCEPTIONS, null);
            List<Integer> vertExceptions = (List<Integer>) MapUtils.get(inverterInfoResult, BmtDataKey.VERT_EXCEPTIONS, null);
            List<Integer> vertGridExceptions = (List<Integer>) MapUtils.get(inverterInfoResult, BmtDataKey.VERT_GRID_EXCEPTIONS, null);
            List<Integer> vertSystemExceptions = (List<Integer>) MapUtils.get(inverterInfoResult, BmtDataKey.VERT_SYSTEM_EXCEPTIONS, null);
            List<Integer> vertmpptExceptions = (List<Integer>) MapUtils.get(inverterInfoResult, BmtDataKey.VERT_MPPT_EXCEPTIONS, null);
            List<Integer> vertDCExceptions = (List<Integer>) MapUtils.get(inverterInfoResult, BmtDataKey.VERT_DC_EXCEPTIONS, null);

            WarningBean warningBean;
            if (null != vertBatteryExceptions) {
                for (Integer code : vertBatteryExceptions) {
                    warningBean = getVertBatteryWarningBean(context, code, isBmtDeviceV2);
                    if (null != warningBean) {
                        result.add(warningBean);
                    }
                }
            }
            if (null != vertExceptions) {
                for (Integer code : vertExceptions) {
                    warningBean = getInvertWarningBean(context, code, isBmtDeviceV2);
                    if (null != warningBean) {
                        result.add(warningBean);
                    }
                }
            }
            if (null != vertGridExceptions) {
                for (Integer code : vertGridExceptions) {
                    warningBean = getVertGridWarningBean(context, code);
                    if (null != warningBean) {
                        result.add(warningBean);
                    }
                }
            }
            if (null != vertSystemExceptions) {
                for (Integer code : vertSystemExceptions) {
                    warningBean = getVertSystemWarningBean(context, code);
                    if (null != warningBean) {
                        result.add(warningBean);
                    }
                }
            }
            if (null != vertmpptExceptions) {
                for (Integer code : vertmpptExceptions) {
                    warningBean = getVertMpptWarningBean(context, code);
                    if (null != warningBean) {
                        result.add(warningBean);
                    }
                }
            }

            if (null != vertDCExceptions) {
                for (Integer code : vertDCExceptions) {
                    warningBean = getVertDcWarningBean(context, code);
                    if (null != warningBean) {
                        result.add(warningBean);
                    }
                }
            }
        }
        return result;
    }

    public static WarningBean createWarningBean(Context context, String cmd, int code, boolean isBmtDeviceV2) {
        WarningBean warningBean = null;
        switch (cmd) {
            case DsCamCmd.INVERTER_EXCEPTION:
                warningBean = getInvertWarningBean(context, code, isBmtDeviceV2);
                break;

            case DsCamCmd.BATTERY_EXCEPTION:
                warningBean = getBatteryWarningBean(context, code);
                break;

            case DsCamCmd.MPPT_EXCEPTION:
                warningBean = getMPPTWarningBean(context, code);
                break;

            case DsCamCmd.EV_EXCEPTION:
                warningBean = getEVWarningBean(context, code, isBmtDeviceV2);
                break;

            case DsCamCmd.COMMUNICATION_EXCEPTION:
                warningBean = getCommunicationWarningBean(context, code, isBmtDeviceV2);
                break;

            case DsCamCmd.CABINET_EXCEPTION:
                warningBean = getCabinetWarningBean(context, code, isBmtDeviceV2);
                break;

            case DsCamCmd.SYSTEM_EXCEPTION:
                warningBean = getSystemWarningBean(context, code);
                break;
        }
        return warningBean;
    }
}
