package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.utils.KeyBoardUtil;
import com.dinsafer.config.DBKey;
import com.dinsafer.dialog.CommonAlertDialog;
import com.dinsafer.dialog.InputPasswordDialog;
import com.dinsafer.dialog.KeyboardHeightHelper;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentOnGridConfigurationBinding;
import com.dinsafer.model.event.EditTextFocusEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.OGCProgressModel;
import com.dinsafer.module.powerstation.adapter.OGCSwitchModel;
import com.dinsafer.module.powerstation.adapter.OGCTwoProgressModel;
import com.dinsafer.module.powerstation.event.OnGridConfigurationEditEvent;
import com.dinsafer.module.powerstation.event.OnGridConfigurationProgressEvent;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OnGridConfigurationFragment extends MyBaseFragment<FragmentOnGridConfigurationBinding> implements IDeviceCallBack {

    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private BindMultiAdapter<BindModel> mGCSettingAdapter;
    private List<BindModel> mGCSettingModelData;
    private boolean isEdit;
    private View mFocusView;
    private int mPosition;
    private KeyboardHeightHelper mKeyboardHeightHelper;
    private CommonAlertDialog mConfirmDialog;

    private OGCSwitchModel mFastPowerDown;
    private OGCSwitchModel mAntiIslandingSwitch;
    private OGCSwitchModel mActiveHighLowWear;
    private OGCSwitchModel mQUMode;
    private OGCSwitchModel mCosPhiPMode;
    private OGCTwoProgressModel mPowerFactor;
    private OGCProgressModel mPowerSetting;
    private OGCProgressModel mPowerSoftStartRate;
    private OGCProgressModel mUnderfrequencyLoading;
    private OGCProgressModel mUnderfrequencyLoadingSlope;
    private OGCProgressModel mOverfrequencyLoadShedding;
    private OGCProgressModel mOverfrequencyLoadSheddingSlopeSetting;
    private OGCProgressModel mOverfrequencyProtectionTime;
    private OGCProgressModel mOverloadProtectionTime;
    private OGCProgressModel mClass1GridFrequencyUnderfrequency;
    private OGCProgressModel mClass2GridFrequencyUnderfrequency;
    private OGCProgressModel mLevel1GridFrequencyOverfrequency;
    private OGCProgressModel mLevel2GridFrequencyOverfrequency;
    private OGCProgressModel mLevel1GridVoltageUndervoltage;
    private OGCProgressModel mLevel2GridVoltageUndervoltage;
    private OGCProgressModel mLevel1GridVoltageOvervoltage;
    private OGCProgressModel mLevel2GridVoltageOvervoltage;
    private OGCProgressModel mLevel1GridUndervoltageProtectionTime;
    private OGCProgressModel mLevel2GridUndervoltageProtectionTime;
    private OGCProgressModel mLevel1GridOvervoltageProtectionTime;
    private OGCProgressModel mLevel2GridOvervoltageProtectionTime;
    private OGCProgressModel mBootFailureReconnectTime;
    private OGCProgressModel mBootFailureReconnectionSlope;
    private OGCProgressModel mUndervoltageOnBootAndReconnect;
    private OGCProgressModel mOvervoltageOnBootAndReconnect;
    private OGCProgressModel mUnderfrequencyOnBootAndReconnect;
    private OGCProgressModel mOverfrequencyOnBootAndReconnect;
    private boolean isCosPModeOn;
    private float mPowerSettingProgress;

    public static OnGridConfigurationFragment newInstance(String deviceId, String subcategory) {
        OnGridConfigurationFragment fragment = new OnGridConfigurationFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_on_grid_configuration;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        getDelegateActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        EventBus.getDefault().register(this);
        initTitleBar();
        mKeyboardHeightHelper = new KeyboardHeightHelper(getDelegateActivity());
        mKeyboardHeightHelper.setHeightChangeListener((height, y) -> dealKeyBoard(height, y));
        mKeyboardHeightHelper.create();
        initRv();
        initParams();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.lcbResume.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                KeyBoardUtil.closeKeybord(getContext(), mBinding.lcbResume);
                EventBus.getDefault().post(new EditTextFocusEvent(null, -1));
                showConfirmDialog();
            }
        });
        mBinding.tvTryAgain.setOnClickListener(view -> getData());
    }

    private void initRv() {
        mBinding.rvSetting.setLayoutManager(new LinearLayoutManager(getContext()));
        ((SimpleItemAnimator) mBinding.rvSetting.getItemAnimator()).setSupportsChangeAnimations(false);
        mGCSettingAdapter = new BindMultiAdapter<>();
        mGCSettingModelData = new ArrayList<>();
        initGridData();
        mBinding.rvSetting.setAdapter(mGCSettingAdapter);
        mGCSettingAdapter.setNewData(mGCSettingModelData);
        mGCSettingAdapter.setOnBindItemClickListener(new OnBindItemClickListener() {
            @Override
            public void onItemClick(View v, int position, Object model) {
                if (model instanceof OGCSwitchModel) {
                    EventBus.getDefault().post(new OnGridConfigurationEditEvent(position));
                }
            }
        });
    }

    private void initGridData() {
        mFastPowerDown = new OGCSwitchModel(getContext(), getString(R.string.app_fast_power_down), false);
        mGCSettingModelData.add(mFastPowerDown);

        mAntiIslandingSwitch = new OGCSwitchModel(getContext(), getString(R.string.anti_islanding_switch), false);
        mGCSettingModelData.add(mAntiIslandingSwitch);

        mActiveHighLowWear = new OGCSwitchModel(getContext(), getString(R.string.active_high_and_low_wear_enables_computing), false);
        mGCSettingModelData.add(mActiveHighLowWear);

        mQUMode = new OGCSwitchModel(getContext(), getString(R.string.qu_mode), false);
        mGCSettingModelData.add(mQUMode);

        mCosPhiPMode = new OGCSwitchModel(getContext(), getString(R.string.cos_phi_p_mode), false);
        mGCSettingModelData.add(mCosPhiPMode);

        mPowerFactor = new OGCTwoProgressModel(getContext(), getString(R.string.power_factor),
                "", -0.90f, -1.00f, 0.90f, 1.00f,
                1.00f, 100f);
        mGCSettingModelData.add(mPowerFactor);

        mPowerSetting = new OGCProgressModel(getContext(), getString(R.string.power_setting),
                "", 0f, 100f, 0f, 1f);
        mGCSettingModelData.add(mPowerSetting);

        mPowerSoftStartRate = new OGCProgressModel(getContext(), getString(R.string.power_soft_start_rate),
                "", 0.33f, 0.66f, 0.33f, 100f);
        mGCSettingModelData.add(mPowerSoftStartRate);

        mUnderfrequencyLoading = new OGCProgressModel(getContext(), getString(R.string.underfrequency_loading),
                "Hz", 47.5f, 50.0f, 49.8f, 100f);
        mGCSettingModelData.add(mUnderfrequencyLoading);

        mUnderfrequencyLoadingSlope = new OGCProgressModel(getContext(), getString(R.string.underfrequency_loading_slope),
                "Hz", 0f, 1f, 0.005f, 1000f);
        mGCSettingModelData.add(mUnderfrequencyLoadingSlope);

        mOverfrequencyLoadShedding = new OGCProgressModel(getContext(), getString(R.string.app_overfrequency_load_shedding),
                "Hz", 50.0f, 51.5f, 50.2f, 100f);
        mGCSettingModelData.add(mOverfrequencyLoadShedding);

        mOverfrequencyLoadSheddingSlopeSetting = new OGCProgressModel(getContext(), getString(R.string.overfrequency_load_shedding_slope_setting),
                "", 0.02f, 0.12f, 0.05f, 100f);
        mGCSettingModelData.add(mOverfrequencyLoadSheddingSlopeSetting);

        mOverfrequencyProtectionTime = new OGCProgressModel(getContext(), getString(R.string.overfrequency_protection_time),
                "ms", 0f, 10000f, 200f, 1f);
        mGCSettingModelData.add(mOverfrequencyProtectionTime);

        mOverloadProtectionTime = new OGCProgressModel(getContext(), getString(R.string.overload_protection_time),
                "ms", 0f, 10000f, 200f, 1f);
        mGCSettingModelData.add(mOverloadProtectionTime);

        mClass1GridFrequencyUnderfrequency = new OGCProgressModel(getContext(), getString(R.string.class_1_grid_frequency_underfrequency),
                "Hz", 47.0f, 47.5f, 47.5f, 100f);
        mGCSettingModelData.add(mClass1GridFrequencyUnderfrequency);

        mClass2GridFrequencyUnderfrequency = new OGCProgressModel(getContext(), getString(R.string.class_2_grid_frequency_underfrequency),
                "Hz", 44.0f, 45.0f, 45.0f, 100f);
        mGCSettingModelData.add(mClass2GridFrequencyUnderfrequency);

        mLevel1GridFrequencyOverfrequency = new OGCProgressModel(getContext(), getString(R.string.level_1_grid_frequency_overfrequency),
                "Hz", 51.5f, 52.0f, 51.5f, 100f);
        mGCSettingModelData.add(mLevel1GridFrequencyOverfrequency);

        mLevel2GridFrequencyOverfrequency = new OGCProgressModel(getContext(), getString(R.string.level_2_grid_frequency_overfrequency),
                "Hz", 56.0f, 57.0f, 56.0f, 100f);
        mGCSettingModelData.add(mLevel2GridFrequencyOverfrequency);

        mLevel1GridVoltageUndervoltage = new OGCProgressModel(getContext(), getString(R.string.level_1_grid_voltage_undervoltage),
                "V", 184.0f, 195.5f, 185f, 10f);
        mGCSettingModelData.add(mLevel1GridVoltageUndervoltage);

        mLevel2GridVoltageUndervoltage = new OGCProgressModel(getContext(), getString(R.string.level_2_grid_voltage_undervoltage),
                "V", 103.5f, 172.5f, 172.5f, 10f);
        mGCSettingModelData.add(mLevel2GridVoltageUndervoltage);

        mLevel1GridVoltageOvervoltage = new OGCProgressModel(getContext(), getString(R.string.level_1_grid_voltage_overvoltage),
                "V", 252.0f, 253.0f, 252.0f, 10f);
        mGCSettingModelData.add(mLevel1GridVoltageOvervoltage);

        mLevel2GridVoltageOvervoltage = new OGCProgressModel(getContext(), getString(R.string.level_2_grid_voltage_overvoltage),
                "V", 265.0f, 287.5f, 265.0f, 10f);
        mGCSettingModelData.add(mLevel2GridVoltageOvervoltage);

        mLevel1GridUndervoltageProtectionTime = new OGCProgressModel(getContext(), getString(R.string.level_1_grid_undervoltage_protection_time),
                "s", 0.0f, 300.0f, 3.0f, 10f);
        mGCSettingModelData.add(mLevel1GridUndervoltageProtectionTime);

        mLevel2GridUndervoltageProtectionTime = new OGCProgressModel(getContext(), getString(R.string.level_2_grid_undervoltage_protection_time),
                "s", 0.0f, 300.0f, 0.3f, 10f);
        mGCSettingModelData.add(mLevel2GridUndervoltageProtectionTime);

        mLevel1GridOvervoltageProtectionTime = new OGCProgressModel(getContext(), getString(R.string.level_1_grid_overvoltage_protection_time),
                "s", 0.0f, 300.0f, 0.1f, 10f);
        mGCSettingModelData.add(mLevel1GridOvervoltageProtectionTime);

        mLevel2GridOvervoltageProtectionTime = new OGCProgressModel(getContext(), getString(R.string.level_2_grid_overvoltage_protection_time),
                "s", 0.0f, 300.0f, 0.1f, 10f);
        mGCSettingModelData.add(mLevel2GridOvervoltageProtectionTime);

        mBootFailureReconnectTime  = new OGCProgressModel(getContext(), getString(R.string.boot_failure_reconnect_time),
                "s", 10.0f, 600.0f, 20f, 10f);
        mGCSettingModelData.add(mBootFailureReconnectTime);

        mBootFailureReconnectionSlope  = new OGCProgressModel(getContext(), getString(R.string.boot_failure_reconnect_slope),
                "", 0.06f, 30.00f, 10.00f, 100f);
        mGCSettingModelData.add(mBootFailureReconnectionSlope);

        mUndervoltageOnBootAndReconnect =  new OGCProgressModel(getContext(), getString(R.string.overfrequency_on_boot_and_reconnect),
                "Hz", 47.50f, 50.00f, 47.50f, 100f);
        mGCSettingModelData.add(mUndervoltageOnBootAndReconnect);
        mOvervoltageOnBootAndReconnect =  new OGCProgressModel(getContext(), getString(R.string.underfrequency_on_boot_and_reconnect),
                "V", 184.0f, 195.5f, 192.0f, 10f);
        mGCSettingModelData.add(mOvervoltageOnBootAndReconnect);

        mUnderfrequencyOnBootAndReconnect =  new OGCProgressModel(getContext(), getString(R.string.underfrequency_on_boot_and_reconnect),
                "V", 252.0f, 256.0f, 256.0f, 10f);
        mGCSettingModelData.add(mUnderfrequencyOnBootAndReconnect);

        mOverfrequencyOnBootAndReconnect =  new OGCProgressModel(getContext(), getString(R.string.overfrequency_on_boot_and_reconnect),
                "Hz", 50.00f, 52.50f, 51.40f, 100f);
        mGCSettingModelData.add(mOverfrequencyOnBootAndReconnect);
    }

    private void initParams() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
            mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
            mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
            if (mPSDevice != null) {
                mPSDevice.registerDeviceCallBack(this);
                getData();
            }
        }
    }

    /**
     * 恢复默认配置弹窗
     */
    private void showConfirmDialog() {
        if (mConfirmDialog == null) {
            mConfirmDialog = CommonAlertDialog.createBuilder(getDelegateActivity())
                    .setContentTxt(Local.s(getString(R.string.confirm_to_resume)))
                    .setConfirmTxt(getString(R.string.Cancel))
                    .setCancelTxt(getString(R.string.Confirm))
                    .setAutoDismiss(true)
                    .setShowCancel(true)
                    .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                        @Override
                        public void onConfirm(CommonAlertDialog dialog) {

                        }

                        @Override
                        public void onCancel(CommonAlertDialog dialog) {
                            showTimeOutLoadinFramgment();
                            BmtManager.getInstance().resumeGridconnectionConfig(mPSDevice);
                        }
                    })
                    .builder();
        }
        if (!mConfirmDialog.isShowing()) {
            mConfirmDialog.show();
        }
    }

    private void getData() {
//        showTimeOutLoadinFramgment();
//        BmtManager.getInstance().getGridconnectionConfig(mPSDevice);
       setFailedView(false);
    }

    private void initTitleBar() {
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.on_grid_configuration));
        mBinding.commonBar.commonBarBack.setOnClickListener(view -> removeSelf());
        mBinding.commonBar.commonBarRightText.setLocalText(getString(R.string.edit));
        RelativeLayout.LayoutParams rlLayoutParams = (RelativeLayout.LayoutParams) mBinding.commonBar.commonBarRightText.getLayoutParams();
        rlLayoutParams.rightMargin = 0;
        mBinding.commonBar.commonBarRightText.setLayoutParams(rlLayoutParams);
        mBinding.commonBar.commonBarRightText.setBackgroundResource(0);
        mBinding.commonBar.commonBarRightText.setTextColor(getResources().getColor(R.color.color_brand_text));
        mBinding.commonBar.commonBarRightText.setOnClickListener(view -> {
            if (isEdit) {
                KeyBoardUtil.closeKeybord(getContext(), mBinding.commonBar.commonBarRightText);
                EventBus.getDefault().post(new EditTextFocusEvent(null, -1));
                save();
            } else {
                new InputPasswordDialog.Builder(getContext())
                        .setAutoDismiss(true)
                        .setConfirmListener(new InputPasswordDialog.OnConfirmListener() {
                            @Override
                            public void onConfirm(InputPasswordDialog dialog, String password) {
                                if (TextUtils.isEmpty(password)) {
                                    showErrorToast();
                                    return;
                                }
                                if (!password.equals(DBUtil.SGet(DBKey.CHECK_USER_PASSWORD))) {
                                    showErrorToast();
                                    return;
                                }
                                setDataEditable(true);
                            }
                        }).build().show();
            }
        });
    }

    private void setDataEditable(boolean isEditable) {
        if (CollectionUtil.isListNotEmpty(mGCSettingModelData)) {
            for (BindModel model : mGCSettingModelData) {
                if (model instanceof OGCProgressModel) {
                    ((OGCProgressModel) model).setEditable(isEditable);
                } else if (model instanceof OGCSwitchModel) {
                    ((OGCSwitchModel) model).setEditable(isEditable);
                } else if (model instanceof OGCTwoProgressModel) {
                    ((OGCTwoProgressModel) model).setEditable(isEditable);
                }
            }
            mGCSettingAdapter.notifyDataSetChanged();
        }
        isEdit = isEditable;
        mBinding.commonBar.commonBarRightText.setLocalText(isEdit ? getString(R.string.save) : getString(R.string.edit));
        mBinding.lcbResume.setVisibility(isEdit ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mKeyboardHeightHelper != null) {
            mKeyboardHeightHelper.release();
        }
        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(OnGridConfigurationEditEvent event) {
        if (isEdit) {
            KeyBoardUtil.closeKeybord(getContext(), mBinding.commonBar.commonBarRightText);
            EventBus.getDefault().post(new EditTextFocusEvent(null, -1));
        }
        int position = event.getPosition();
        if (CollectionUtil.isListNotEmpty(mGCSettingModelData)) {
            for (int i = 0; i < mGCSettingModelData.size(); i++) {
                if (position != i) {
                    BindModel model = mGCSettingModelData.get(i);
                    if (model instanceof OGCProgressModel) {
                        ((OGCProgressModel) model).setEdit(false);
                    } else if (model instanceof OGCSwitchModel) {
                        ((OGCSwitchModel) model).setEdit(false);
                    } else if (model instanceof OGCTwoProgressModel) {
                        ((OGCTwoProgressModel) model).setEdit(false);
                    }
                }
            }
            if (position == 4) {
                isCosPModeOn = event.isOn();
                mPowerFactor.setEditable(!isCosPModeOn);
                if (isCosPModeOn) {
                    resetPowerFactorProgress();
                }
            }
            mGCSettingAdapter.notifyDataSetChanged();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(EditTextFocusEvent event) {
        mFocusView = event.getView();
        mPosition = event.getPosition();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(OnGridConfigurationProgressEvent event){
        int position = event.getPosition();
        if (position == 6 && mPowerFactor != null && isCosPModeOn) {
            mPowerSettingProgress = event.getProgress();
            resetPowerFactorProgress();
            mGCSettingAdapter.notifyItemChanged(5);
        }
    }

    private void resetPowerFactorProgress(){
        if (mPowerSettingProgress >= 50f) {
            float percent = (mPowerSettingProgress - 50f) / (100f - 50f);
            float powerFactorProgress = 1.00f - percent * (1.00f - 0.90f);
            mPowerFactor.setProgress(powerFactorProgress);
        } else {
            mPowerFactor.setProgress(1.00f);
        }
    }

    /**
     * 根据键盘高度处理页面显示
     *
     * @param height
     * @param y
     */
    private void dealKeyBoard(int height, int y) {
        if (height == 0) {
            mBinding.clParent.setPadding(0, 0, 0, 0);
        } else {
            if (mFocusView != null) {
                int[] locations = new int[2];
                mFocusView.getLocationInWindow(locations);
                if ((locations[1] + mFocusView.getHeight()) >= y) {
                    if (mPosition > mGCSettingModelData.size() - 3) {
                        mBinding.clParent.setPadding(0, 0, 0, DensityUtils.dp2px(getContext(), 50) + mFocusView.getHeight());
                        mBinding.nsvParent.postDelayed(() -> mBinding.nsvParent.scrollTo(0, (int) (mFocusView.getY() + DensityUtils.dp2px(getContext(), 18)) - height), 100);
                    } else {
                        mBinding.nsvParent.scrollTo(0, (int) (mFocusView.getY() + DensityUtils.dp2px(getContext(), 18)) - height);
                    }
                }
            }
        }
    }

    /**
     * 装载并网配置数据
     *
     * @param result
     */
    private void setGridConfigData(Map<String, Object> result) {
//        float activeSoftStartRate = DeviceHelper.getFloat(result, BmtDataKey.ACTIVE_SOFT_START_RATE, 0f);
//        boolean appFastPowerDown = DeviceHelper.getBoolean(result, BmtDataKey.APP_FAST_POWER_DOWN, false);
//        float overfrequencyLoadShedding = DeviceHelper.getFloat(result, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING, 0f);
//        float overfrequencyLoadSheddingSlope = DeviceHelper.getFloat(result, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING_SLOPE, 0f);
//        float underfrequencyLoadShedding = DeviceHelper.getFloat(result, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING, 0f);
//        boolean quMode = DeviceHelper.getBoolean(result, BmtDataKey.QU_MODE, false);
//        boolean cosMode = DeviceHelper.getBoolean(result, BmtDataKey.COS_MODE, false);
//        isCosPModeOn = cosMode;
//        boolean antiislandingMode = DeviceHelper.getBoolean(result, BmtDataKey.ANTI_ISLANDING_MODE, false);
//        boolean highlowMode = DeviceHelper.getBoolean(result, BmtDataKey.HIGH_LOW_MODE, false);
//        float powerFactor = DeviceHelper.getFloat(result, BmtDataKey.POWER_FACTOR, 0f);
//        int reconnectTime = DeviceHelper.getInt(result, BmtDataKey.RECONNECT_TIME, 0);
//        float reconnectSlope = DeviceHelper.getFloat(result, BmtDataKey.RECONNECT_SLOPE, 0f);
//        float gridOvervoltage1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_VOLTAGE_1, 0f);
//        float gridOvervoltage2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_VOLTAGE_2, 0f);
//        float gridUndervoltage1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_VOLTAGE_1, 0f);
//        float gridUndervoltage2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_VOLTAGE_2, 0f);
//        float gridOverfrequency1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_FREQUENCY_1, 0f);
//        float gridOverfrequency2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_FREQUENCY_2, 0f);
//        float gridUnderfrequency1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_FREQUENCY_1, 0f);
//        float gridUnderfrequency2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_FREQUENCY_2, 0f);
//
//        mGCSettingModelData.clear();
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.power_soft_start_rate),
//                "", 0.33f, 0.66f, activeSoftStartRate, 100f));
//        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.app_fast_power_down), appFastPowerDown));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.app_overfrequency_load_shedding),
//                "Hz", 50.2f, 51.5f, overfrequencyLoadShedding, 100f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.overfrequency_load_shedding_slope_setting),
//                "", 0.02f, 0.12f, overfrequencyLoadSheddingSlope, 100f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.underfrequency_loading),
//                "Hz", 47.5f, 49.8f, underfrequencyLoadShedding, 100f));
//
//        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.qu_mode), quMode));
//        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.cos_phi_p_mode), cosMode));
//        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.anti_islanding_switch), antiislandingMode));
//        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.active_high_and_low_wear_enables_computing), highlowMode));
//
//        mGCSettingModelData.add(new OGCTwoProgressModel(getContext(), getString(R.string.power_factor),
//                "", -0.90f, -1.00f, 0.90f, 1.00f,
//                powerFactor, 100f));
//
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.boot_failure_reconnect_time),
//                "s", 10f, 600f, reconnectTime, 1f));
//
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.boot_failure_reconnect_slope),
//                "", 0.06f, 30f, reconnectSlope, 100f));
//
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_1_grid_voltage_overvoltage),
//                "V", 252.0f, 253.0f, gridOvervoltage1, 10f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_2_grid_voltage_overvoltage),
//                "V", 265.0f, 287.5f, gridOvervoltage2, 10f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_1_grid_voltage_undervoltage),
//                "V", 184.0f, 195.5f, gridUndervoltage1, 10f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_2_grid_voltage_undervoltage),
//                "V", 103.5f, 172.5f, gridUndervoltage2, 10f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_1_grid_frequency_overfrequency),
//                "Hz", 51.5f, 52.0f, gridOverfrequency1, 100f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_2_grid_frequency_overfrequency),
//                "Hz", 56.0f, 57.0f, gridOverfrequency2, 100f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.class_1_grid_frequency_underfrequency),
//                "Hz", 47.0f, 47.5f, gridUnderfrequency1, 100f));
//        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.class_2_grid_frequency_underfrequency),
//                "Hz", 44.0f, 45.0f, gridUnderfrequency2, 100f));
//        mGCSettingAdapter.setNewData(mGCSettingModelData);

        boolean appFastPowerDown = DeviceHelper.getBoolean(result, BmtDataKey.APP_FAST_POWER_DOWN, false);
        mFastPowerDown.setOpen(appFastPowerDown);
        boolean antiislandingMode = DeviceHelper.getBoolean(result, BmtDataKey.ANTI_ISLANDING_MODE, false);
        mAntiIslandingSwitch.setOpen(antiislandingMode);
        boolean highlowMode = DeviceHelper.getBoolean(result, BmtDataKey.HIGH_LOW_MODE, false);
        mActiveHighLowWear.setOpen(highlowMode);
        boolean quMode = DeviceHelper.getBoolean(result, BmtDataKey.QU_MODE, false);
        mQUMode.setOpen(quMode);
        boolean cosMode = DeviceHelper.getBoolean(result, BmtDataKey.COS_MODE, false);
        isCosPModeOn = cosMode;
        mCosPhiPMode.setOpen(cosMode);
        float powerFactor = DeviceHelper.getFloat(result, BmtDataKey.POWER_FACTOR, 0f);
        mPowerFactor.setProgress(powerFactor);
        float powerSetting = DeviceHelper.getFloat(result, BmtDataKey.POWER_PERCENT, 0f);
        mPowerSetting.setProgress(powerSetting);
        float activeSoftStartRate = DeviceHelper.getFloat(result, BmtDataKey.ACTIVE_SOFT_START_RATE, 0f);
        mPowerSoftStartRate.setProgress(activeSoftStartRate);
        float underfrequencyLoadShedding = DeviceHelper.getFloat(result, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING, 0f);
        mUnderfrequencyLoading.setProgress(underfrequencyLoadShedding);
        float underfrequencyLoadSheddingSlope = DeviceHelper.getFloat(result, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING_SLOPE, 0f);
        mUnderfrequencyLoadingSlope.setProgress(underfrequencyLoadSheddingSlope);
        float overfrequencyLoadShedding = DeviceHelper.getFloat(result, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING, 0f);
        mOverfrequencyLoadShedding.setProgress(overfrequencyLoadShedding);
        float overfrequencyLoadSheddingSlope = DeviceHelper.getFloat(result, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING_SLOPE, 0f);
        mOverfrequencyLoadSheddingSlopeSetting.setProgress(overfrequencyLoadSheddingSlope);
        int gridOverfrequencyProtectTime = DeviceHelper.getInt(result, BmtDataKey.GRID_OVER_FREQUENCY_PROTECT_TIME, 0);
        mOverfrequencyProtectionTime.setProgress(gridOverfrequencyProtectTime);
        int gridUnderfrequencyProtectTime = DeviceHelper.getInt(result, BmtDataKey.GRID_UNDER_FREQUENCY_PROTECT_TIME, 0);
        mOverloadProtectionTime.setProgress(gridUnderfrequencyProtectTime);
        float gridUnderfrequency1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_FREQUENCY_1, 0f);
        mClass1GridFrequencyUnderfrequency.setProgress(gridUnderfrequency1);
        float gridUnderfrequency2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_FREQUENCY_2, 0f);
        mClass2GridFrequencyUnderfrequency.setProgress(gridUnderfrequency2);
        float gridOverfrequency1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_FREQUENCY_1, 0f);
        mLevel1GridFrequencyOverfrequency.setProgress(gridOverfrequency1);
        float gridOverfrequency2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_FREQUENCY_2, 0f);
        mLevel2GridFrequencyOverfrequency.setProgress(gridOverfrequency2);
        float gridUndervoltage1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_VOLTAGE_1, 0f);
        mLevel1GridVoltageUndervoltage.setProgress(gridUndervoltage1);
        float gridUndervoltage2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_VOLTAGE_2, 0f);
        mLevel2GridVoltageUndervoltage.setProgress(gridUndervoltage2);
        float gridOvervoltage1 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_VOLTAGE_1, 0f);
        mLevel1GridVoltageOvervoltage.setProgress(gridOvervoltage1);
        float gridOvervoltage2 = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_VOLTAGE_2, 0f);
        mLevel2GridVoltageOvervoltage.setProgress(gridOvervoltage2);
        float gridUndervoltage1ProtectTime = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_VOLTAGE_1_PROTECT_TIME, 0f);
        mLevel1GridUndervoltageProtectionTime.setProgress(gridUndervoltage1ProtectTime);
        float gridUndervoltage2ProtectTime = DeviceHelper.getFloat(result, BmtDataKey.GRID_UNDER_VOLTAGE_2_PROTECT_TIME, 0f);
        mLevel2GridUndervoltageProtectionTime.setProgress(gridUndervoltage2ProtectTime);
        float gridOvervoltage1ProtectTime = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_VOLTAGE_1_PROTECT_TIME, 0f);
        mLevel1GridOvervoltageProtectionTime.setProgress(gridOvervoltage1ProtectTime);
        float gridOvervoltage2ProtectTime = DeviceHelper.getFloat(result, BmtDataKey.GRID_OVER_VOLTAGE_2_PROTECT_TIME, 0f);
        mLevel2GridOvervoltageProtectionTime.setProgress(gridOvervoltage2ProtectTime);
        float reconnectTime = DeviceHelper.getFloat(result, BmtDataKey.RECONNECT_TIME, 0f);
        mBootFailureReconnectTime.setProgress(reconnectTime);
        float reconnectSlope = DeviceHelper.getFloat(result, BmtDataKey.RECONNECT_SLOPE, 0f);
        mBootFailureReconnectionSlope.setProgress(reconnectSlope);
        float rebootGridUnderfrequency = DeviceHelper.getFloat(result, BmtDataKey.REBOOT_GRID_UNDER_FREQUENCY, 0f);
        mUnderfrequencyOnBootAndReconnect.setProgress(rebootGridUnderfrequency);
        float rebootGridOvervoltage = DeviceHelper.getFloat(result, BmtDataKey.REBOOT_GRID_0VER_VOLTAGE, 0f);
        mOvervoltageOnBootAndReconnect.setProgress(rebootGridOvervoltage);
        float rebootGridUndervoltage = DeviceHelper.getFloat(result, BmtDataKey.REBOOT_GRID_UNDER_VOLTAGE, 0f);
        mUndervoltageOnBootAndReconnect.setProgress(rebootGridUndervoltage);
        float rebootGridOverfrequency = DeviceHelper.getFloat(result, BmtDataKey.REBOOT_GRID_OVER_FREQUENCY, 0f);
        mOverfrequencyOnBootAndReconnect.setProgress(rebootGridOverfrequency);
        setFailedView(false);
    }

    /**
     * 获取并网配置结果显示
     *
     * @param isFailed
     */
    private void setFailedView(boolean isFailed) {
        mBinding.nsvParent.setVisibility(!isFailed ? View.VISIBLE : View.GONE);
        mBinding.commonBar.commonBarRightText.setVisibility(!isFailed ? View.VISIBLE : View.GONE);
        mBinding.clFailed.setVisibility(isFailed ? View.VISIBLE : View.GONE);
    }

    /**
     * 保存修改
     */
    private void save() {
        if (mPSDevice != null && CollectionUtil.isListNotEmpty(mGCSettingModelData)) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.UPDATE_GRIDCONNECTION_CONFIG);
            params.put(BmtDataKey.POWER_PERCENT, mPowerSetting.getProgress());
            params.put(BmtDataKey.APP_FAST_POWER_DOWN, mFastPowerDown.isOpen());
            params.put(BmtDataKey.QU_MODE, mQUMode.isOpen());
            params.put(BmtDataKey.COS_MODE, mCosPhiPMode.isOpen());
            params.put(BmtDataKey.ANTI_ISLANDING_MODE, mAntiIslandingSwitch.isOpen());
            params.put(BmtDataKey.HIGH_LOW_MODE, mActiveHighLowWear.isOpen());
            params.put(BmtDataKey.ACTIVE_SOFT_START_RATE, mPowerSoftStartRate.getProgress());
            params.put(BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING, mOverfrequencyLoadShedding.getProgress());
            params.put(BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING_SLOPE, mOverfrequencyLoadSheddingSlopeSetting.getProgress());
            params.put(BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING, mUnderfrequencyLoading.getProgress());
            params.put(BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING_SLOPE, mUnderfrequencyLoadingSlope.getProgress());
            params.put(BmtDataKey.POWER_FACTOR, mPowerFactor.getProgress());
            params.put(BmtDataKey.RECONNECT_TIME, mBootFailureReconnectTime.getProgress());
            params.put(BmtDataKey.GRID_OVER_VOLTAGE_1, mLevel1GridVoltageOvervoltage.getProgress());
            params.put(BmtDataKey.GRID_OVER_VOLTAGE_1_PROTECT_TIME, mLevel1GridOvervoltageProtectionTime.getProgress());
            params.put(BmtDataKey.GRID_OVER_VOLTAGE_2, mLevel2GridVoltageOvervoltage.getProgress());
            params.put(BmtDataKey.GRID_OVER_VOLTAGE_2_PROTECT_TIME, mLevel2GridOvervoltageProtectionTime.getProgress());
            params.put(BmtDataKey.GRID_UNDER_VOLTAGE_1, mLevel1GridVoltageUndervoltage.getProgress());
            params.put(BmtDataKey.GRID_UNDER_VOLTAGE_1_PROTECT_TIME, mLevel1GridUndervoltageProtectionTime.getProgress());
            params.put(BmtDataKey.GRID_UNDER_VOLTAGE_2, mLevel2GridVoltageUndervoltage.getProgress());
            params.put(BmtDataKey.GRID_UNDER_VOLTAGE_2_PROTECT_TIME, mLevel2GridUndervoltageProtectionTime.getProgress());
            params.put(BmtDataKey.GRID_OVER_FREQUENCY_1, mLevel1GridFrequencyOverfrequency.getProgress());
            params.put(BmtDataKey.GRID_OVER_FREQUENCY_PROTECT_TIME, (int) mOverfrequencyProtectionTime.getProgress());
            params.put(BmtDataKey.GRID_OVER_FREQUENCY_2, mLevel2GridFrequencyOverfrequency.getProgress());
            params.put(BmtDataKey.GRID_UNDER_FREQUENCY_1, mClass1GridFrequencyUnderfrequency.getProgress());
            params.put(BmtDataKey.GRID_UNDER_FREQUENCY_PROTECT_TIME, (int) mOverloadProtectionTime.getProgress());
            params.put(BmtDataKey.GRID_UNDER_FREQUENCY_2, mClass2GridFrequencyUnderfrequency.getProgress());
            params.put(BmtDataKey.RECONNECT_SLOPE, mBootFailureReconnectionSlope.getProgress());
            params.put(BmtDataKey.REBOOT_GRID_0VER_VOLTAGE, mOvervoltageOnBootAndReconnect.getProgress());
            params.put(BmtDataKey.REBOOT_GRID_UNDER_VOLTAGE, mUndervoltageOnBootAndReconnect.getProgress());
            params.put(BmtDataKey.REBOOT_GRID_OVER_FREQUENCY, mOverfrequencyOnBootAndReconnect.getProgress());
            params.put(BmtDataKey.REBOOT_GRID_UNDER_FREQUENCY, mUnderfrequencyOnBootAndReconnect.getProgress());
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            runOnMainThread(() -> {
                if (status == StatusConstant.STATUS_SUCCESS) {
                    switch (cmd) {
                        case BmtCmd.GET_GRIDCONNECTION_CONFIG:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            if (result == null) return;
                            setGridConfigData(result);
                            mBinding.nsvParent.setVisibility(View.VISIBLE);
                            break;

                        case BmtCmd.UPDATE_GRIDCONNECTION_CONFIG:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            getMainActivity().showTopToast(R.drawable.icon_toast_succeed, getString(R.string.success));
                            setDataEditable(false);
                            break;

                        case BmtCmd.RESUME_GRIDCONNECTION_CONFIG:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            setDataEditable(false);
                            getData();
                            break;
                    }
                } else {
                    switch (cmd) {
                        case BmtCmd.GET_GRIDCONNECTION_CONFIG:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            setFailedView(true);
                            break;

                        case BmtCmd.UPDATE_GRIDCONNECTION_CONFIG:
                        case BmtCmd.RESUME_GRIDCONNECTION_CONFIG:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showErrorToast();
                            break;
                    }
                }
            });
        }
    }
}
