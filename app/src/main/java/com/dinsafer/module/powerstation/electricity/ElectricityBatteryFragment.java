package com.dinsafer.module.powerstation.electricity;

import android.os.Bundle;
import android.util.LruCache;
import android.view.View;
import android.view.ViewStub;


import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentElectricityBatteryBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.ChartNoteTextAttrBean;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypeDialog;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.chart.marker.BatteryMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.controller.BatteryChartModelController;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.module.powerstation.electricity.helper.BatteryChartHelper;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.module.powerstation.event.StatInfoEvent;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.utils.ViewPortHandler;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class ElectricityBatteryFragment extends BaseChartFragment<BatteryChartModelController,
        FragmentElectricityBatteryBinding> {

    private ElectricityCircleTypeDialog mElectricityCircleTypeDialog;
    private CustomCombinedMarkerView mMarkerView;
    private CustomCombinedChartManager mPLManager1;
    private CustomCombinedChartManager mPLManager2;
    private List<CustomCombinedChartManager> mPLManagers;
    private int mPLInterval;
    private int mGridChargeIndex = 1;
    private ViewStub mVSPowerLeve;
    private FlipCombinedChartView mPowerLevelChart;
    private XAxisValueFormatter xPLFormatter = new XAxisValueFormatter() {
        @Override
        public String getXValue(String original, int index, ViewPortHandler viewPortHandler) {
            if ((index * mPLInterval) % 720 == 0) {
                int hour = (index * mPLInterval) / 60;
                String text = hour < 10 ? ("0" + hour) + ":00" : hour + ":00";
                return text;
            }
            return "";
        }
    };

    private YAxisValueFormatter yPLFormatter = new YAxisValueFormatter() {
        @Override
        public String getFormattedValue(float value, YAxis yAxis) {
            return (int) value + "%";
        }
    };

    private LruCache<Integer, Map<String, Object>> mPowerLevelCache;

    private int  mBmtType = -1;
    private BatteryChartHelper mBatteryChartHelper;

    public static ElectricityBatteryFragment newInstance(int fromIndex, String deviceId, String subCategory) {
        ElectricityBatteryFragment fragment = new ElectricityBatteryFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.INDEX, fromIndex);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_electricity_battery;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        mPowerLevelCache = new LruCache<>(20);
        super.initView(inflateView, savedInstanceState);
        String subCategory = getArguments().getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mBmtType = BmtUtil.getBmtType(subCategory);
        mBatteryChartHelper = new BatteryChartHelper(getContext(), mBmtType);
        mVSPowerLeve = inflateView.findViewById(R.id.vs_power_level);
        if (mBmtType == BmtUtil.BMT_POWER_STORE) {
            mGridChargeIndex = 1;
        } else {
            mGridChargeIndex = isDualPowerOpen ? 2 : 1;
        }
        mType = BaseChartFragment.CHART_ELECTRICITY_BATTERY;
        mPlusMinusType = PlusMinusType.ALL;
        mBinding.tvChargeType.setText(Local.s(getString(R.string.electricity_battery)) + "-" + Local.s(getString(R.string.electricity_net_stats)));
        initRefreshLayout(mBinding.refreshLayout);
        mMarkerView = new BatteryMarkerView(getContext(), mBmtType);
        mMarkerView.setDualPowerOpen(isDualPowerOpen);
        mBinding.tvChargeType.setOnClickListener(v -> {
            showElectricityCircleTypeDialog();
        });
        mBinding.tvUnit.setOnClickListener(v -> {
            showElectricityCircleTypeDialog();
        });
//        initPowerLevel();
//        getStatisticData();
    }

    @Override
    protected void initChartView() {
        super.initChartView();
        initChart(mFlipCombinedChartView, mMarkerView);
        setIvRightEnabled(mOffSet != 0);
        initElectricityType();
        List<ChartNoteTextAttrBean> noteTexts = getNoteTexts(Local.s(getString(R.string.electricity_discharged)),
                Local.s(getString(R.string.electricity_charged)));
        mFlipCombinedChartView.getCustomCombinedChart1().setNoteTexts(noteTexts);
        mFlipCombinedChartView.getCustomCombinedChart2().setNoteTexts(noteTexts);
        mFlipCombinedChartView.setStartFlipChangeListener((index, orientation) -> {
            if (mCycleType == CycleType.DAY) {
                if (mPowerLevelChart != null) {
                    mPowerLevelChart.dealClick(orientation);
                }
            }
        });
        mFlipCombinedChartView.setFlipChangeListener((index, orientation) -> {
            mIndex = index;
            if (orientation == OperateOrientation.LEFT) {
                mOffSet = mOffSet - 1;
            } else if (orientation == OperateOrientation.RIGHT) {
                mOffSet = mOffSet + 1;
            }
            setIvRightEnabled(mOffSet != 0);
            getStatisticData(true);
        });
        mFlipCombinedChartView.setTvNoteVisible(false);
        showNote(mPlusMinusType == PlusMinusType.ALL);
        mFlipCombinedChartView.setRvFilterVisible(false);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            if (mCycleType != CycleType.LIFETIME) {
                if (isChartViewNotNull()) {
                    setIvRightEnabled(mOffSet != 0);
                }
            }
            getStatisticData(true);
        }
    }

    private void initPowerLevel() {
        if (mPowerLevelChart == null) return;
        mPowerLevelChart.setPowerLevelVisible();
        mPLManager1 = new CustomCombinedChartManager(getContext(), mPowerLevelChart.getCustomCombinedChart1());
        mPLManager1.initChart(true, 0.5f, getContext().getResources().getColor(R.color.color_white_04),
                true, true, false, 10f,
                30f, 20f, 10f, 10f, 0, false,
                "", 800, false);
        mPLManager2 = new CustomCombinedChartManager(getContext(), mPowerLevelChart.getCustomCombinedChart2());
        mPLManager2.initChart(true, 0.5f, getContext().getResources().getColor(R.color.color_white_04),
                true, true, false, 10f,
                30f, 20f, 10f, 10f, 0, false,
                "", 800, false);
        mPLManagers = new ArrayList<>();
        mPLManagers.add(mPLManager1);
        mPLManagers.add(mPLManager2);
    }

    private void showElectricityCircleTypeDialog() {
        if (mElectricityCircleTypeDialog == null) {
            mElectricityCircleTypeDialog = new ElectricityCircleTypeDialog(ElectricityCircleTypeDialog.BATTER_CHARGE_TYPE);
            mElectricityCircleTypeDialog.setPlusMinusTypeListener(new ElectricityCircleTypeDialog.OnPlusMinusTypeListener() {
                @Override
                public void plusMinus(String type, PlusMinusType plusMinusType) {

                    mFlipCombinedChartView.resetHighValues();
                    mPlusMinusType = plusMinusType;
                    mBinding.tvChargeType.setText(Local.s(getString(R.string.electricity_battery)) + "-" + Local.s(type));
                    isMultiply = StringUtil.isNotEmpty(type) && type.equals(getString(R.string.electricity_charged));
                    showNote(mPlusMinusType == PlusMinusType.ALL);
                    mFlipCombinedChartView.setRvFilterVisible(type.equals(getString(R.string.electricity_charged)));
                    float maxVal = getYMax(CHART_ELECTRICITY_BATTERY, mChartData);
                    resetLeftAboveText(CHART_ELECTRICITY_BATTERY, maxVal);
                    resetYAxisLeft(maxVal, CHART_ELECTRICITY_BATTERY);
                    if (isSuccess) {
                        resetChart();
                    } else {
                        resetFailChart();
                    }
                }
            });
        }
        if (mElectricityCircleTypeDialog != null && !mElectricityCircleTypeDialog.isAdded()) {
            mElectricityCircleTypeDialog.show(getChildFragmentManager(), ElectricityCircleTypeDialog.CHARGE_TAG);
        }
    }

    private void initElectricityType() {
        if (mFlipCombinedChartView != null) {
            mData = mBatteryChartHelper.getFilterData(isDualPowerOpen);
            mFlipCombinedChartView.setElectricityTypeData(mData);
        }
    }

    @Override
    protected void resetChart() {
        float leftYMaxVal = getYMax(CHART_ELECTRICITY_BATTERY, mChartData);
        String powerUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, false);
        String powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        mMarkerView.setPowerUnit(powerUnit);
        mMarkerView.setPowerHourUnit(powerHourUnit);
        mMarkerView.setLeftYMaxVal(leftYMaxVal);
        mMarkerView.setDSTTransitionDay(mHourCount == DST_TIME_MINUTE);
        if (mHourCount == DST_TIME_MINUTE) {
            mMarkerView.setTimeType(1);
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            mMarkerView.setTimeType(-1);
        } else {
            mMarkerView.setTimeType(0);
        }
        mMarkerView.setTimestamp(mStartTime);
        mMarkerView.setTimezone(timezone);
        if (mPlusMinusType == PlusMinusType.ALL) {
            String dischargeVal = ChartDataUtil.getPowerTransferVal(getDischargedVal(), getDischargedVal(), false);
            float solarChargeVal = getSolarChargedVal(false);
            if (isDualPowerOpen  || mBmtType == BmtUtil.BMT_POWER_STORE) {
                solarChargeVal += getSolarDualVal(false);
            }
            String solarEnergy = ChartDataUtil.getPowerTransferVal(solarChargeVal, solarChargeVal, false);
            String gridEnergy = ChartDataUtil.getPowerTransferVal(getGridChargedVal(false), getGridChargedVal(false), false);
            String solarEnergyUnit = ChartDataUtil.getPowerUnit(solarChargeVal, true);
            String gridEnergyUnit = ChartDataUtil.getPowerUnit(getGridChargedVal(false), true);

            float totalEnergy = 0f;
            float solarEnergyFl = ChartDataUtil.getBigDecimal(solarEnergy).floatValue();
            float gridEnergyFl = ChartDataUtil.getBigDecimal(gridEnergy).floatValue();
            if (solarEnergyUnit.equals("kWh")) {
                solarEnergyFl = solarEnergyFl * 1000f;
            } else if (solarEnergyUnit.equals("MWh")) {
                solarEnergyFl = solarEnergyFl * 1000000f;
            }

            if (gridEnergyUnit.equals("kWh")) {
                gridEnergyFl = gridEnergyFl * 1000f;
            } else if (gridEnergyUnit.equals("MWh")) {
                gridEnergyFl = gridEnergyFl * 1000000f;
            }
            totalEnergy = solarEnergyFl + gridEnergyFl;

            String chargeVal = ChartDataUtil.getPowerTransferVal(totalEnergy, totalEnergy, false);
            String dischargeUnit = ChartDataUtil.getPowerUnit(getDischargedVal(), true);
            String chargeUnit = ChartDataUtil.getPowerUnit(totalEnergy, true);
//            BigDecimal netStatistics = ChartDataUtil.getBigDecimalSub(dischargeVal, chargeVal);
            float netStatisticsVal = 0f;
            float dischargeFl = ChartDataUtil.getBigDecimal(dischargeVal).floatValue();
            float chargeFl = ChartDataUtil.getBigDecimal(chargeVal).floatValue();
            if (dischargeUnit.equals("kWh")) {
                dischargeFl = dischargeFl * 1000f;
            } else if (dischargeUnit.equals("MWh")) {
                dischargeFl = dischargeFl * 1000000f;
            }

            if (chargeUnit.equals("kWh")) {
                chargeFl = chargeFl * 1000f;
            } else if (chargeUnit.equals("MWh")) {
                chargeFl = chargeFl * 1000000f;
            }
            netStatisticsVal = dischargeFl - chargeFl;
//
            String leftUnit = ChartDataUtil.getPowerUnit(netStatisticsVal, true);
            mBinding.esvVal.setLeftVal(ChartDataUtil.getPowerTransferVal(netStatisticsVal, netStatisticsVal, false), leftUnit);
            mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_discharged)), dischargeVal, dischargeUnit);
            mBinding.esvVal.setRightVal(Local.s(getString(R.string.electricity_charged)), chargeVal, chargeUnit);
        } else {
            String leftUnit;
            if (isMultiply) {
                float solarChargeVal = getSolarChargedVal(true);
                if (isDualPowerOpen || mBmtType == BmtUtil.BMT_POWER_STORE) {
                    solarChargeVal += getSolarDualVal(true);
                }
                String solarEnergy = ChartDataUtil.getPowerTransferVal(solarChargeVal, solarChargeVal, false);
                String gridEnergy = ChartDataUtil.getPowerTransferVal(getGridChargedVal(true), getGridChargedVal(true), false);
                String solarEnergyUnit = ChartDataUtil.getPowerUnit(solarChargeVal, true);
                String gridEnergyUnit = ChartDataUtil.getPowerUnit(getGridChargedVal(true), true);

                float totalEnergy = 0f;
                float solarEnergyFl = ChartDataUtil.getBigDecimal(solarEnergy).floatValue();
                float gridEnergyFl = ChartDataUtil.getBigDecimal(gridEnergy).floatValue();
                if (solarEnergyUnit.equals("kWh")) {
                    solarEnergyFl = solarEnergyFl * 1000f;
                } else if (solarEnergyUnit.equals("MWh")) {
                    solarEnergyFl = solarEnergyFl * 1000000f;
                }

                if (gridEnergyUnit.equals("kWh")) {
                    gridEnergyFl = gridEnergyFl * 1000f;
                } else if (gridEnergyUnit.equals("MWh")) {
                    gridEnergyFl = gridEnergyFl * 1000000f;
                }
                totalEnergy = solarEnergyFl + gridEnergyFl;
                String totalEnergyUnit = ChartDataUtil.getPowerUnit(totalEnergy, true);
                mBinding.esvVal.setLeftVal(ChartDataUtil.getPowerTransferVal(totalEnergy, totalEnergy, false), totalEnergyUnit);
                mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_solar_energy)),
                        solarEnergy, solarEnergyUnit);
                mBinding.esvVal.setRightVal(Local.s(getString(R.string.electricity_grid_energy)),
                        gridEnergy, gridEnergyUnit);
            } else {
                leftUnit = ChartDataUtil.getPowerUnit(getDischargedVal(), true);
                mBinding.esvVal.setLeftVal(ChartDataUtil.getPowerTransferVal(getDischargedVal(), getDischargedVal(), false), leftUnit);
                mBinding.esvVal.setMiddleVal("", "", "");
                mBinding.esvVal.setRightVal("", "", "");
            }
//            mBinding.tvUnit.setText("(" + leftUnit + ")");
        }
        mBinding.esvVal.refreshText();
//        mBinding.esvVal.setMiddleVisible(mPlusMinusType == PlusMinusType.ALL || isMultiply);
//        mBinding.esvVal.setRightVisible(mPlusMinusType == PlusMinusType.ALL || isMultiply);
        mFlipCombinedChartView.setTvNotePreBalancingVisible(mCycleType == CycleType.DAY);
        super.resetChart();
    }

    @Override
    protected void resetFailChart() {
        float leftYMaxVal = 0;
        String powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        if (mPlusMinusType == PlusMinusType.ALL) {
            mBinding.esvVal.setLeftVal(mFailVal, powerHourUnit);
            mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_discharged)), mFailVal, powerHourUnit);
            mBinding.esvVal.setRightVal(Local.s(getString(R.string.electricity_charged)), mFailVal, powerHourUnit);
        } else {
            String leftUnit;
            if (isMultiply) {
                leftUnit = ChartDataUtil.getPowerUnit(getChargedVal(true), true);
                mBinding.esvVal.setLeftVal(mFailVal, leftUnit);
                mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_solar_energy)), mFailVal, powerHourUnit);
                mBinding.esvVal.setRightVal(Local.s(getString(R.string.electricity_grid_energy)), mFailVal, powerHourUnit);
            } else {
                leftUnit = ChartDataUtil.getPowerUnit(getDischargedVal(), true);
                mBinding.esvVal.setLeftVal(mFailVal, leftUnit);
                mBinding.esvVal.setMiddleVal("", "", "");
                mBinding.esvVal.setRightVal("", "", "");
            }
        }
        mBinding.esvVal.refreshText();
        super.resetFailChart();
    }

    @Override
    public void createChartModelController() {
        if (chartModelController == null) {
            chartModelController = new BatteryChartModelController();
        }
    }

    @Override
    protected void getStatisticData(boolean showLoading) {
        if (ElectricityStatisticsFragment.mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_STATS_BATTERY_V2);
            params.put(BmtDataKey.INTERVAL, ElectricityStatisticsFragment.mInterval);
            params.put(BmtDataKey.OFFSET, mOffSet);
            if (showLoading) {
                showTimeOutLoadinFramgmentWithMarginTop(marginTop);
            }
            Map<String, Object> result = null;
            Map<String, Object> powerLevelResult = null;
            switch (mCycleType) {
                case DAY:
                    result = mDayCache.get(mOffSet);
                    powerLevelResult = mPowerLevelCache.get(mOffSet);
                    break;

                case WEEK:
                    result = mWeekCache.get(mOffSet);
                    break;

                case MONTH:
                    result = mMonthCache.get(mOffSet);
                    break;

                case YEAR:
                    result = mYearCache.get(mOffSet);
                    break;
            }
            if (result == null) {
                ElectricityStatisticsFragment.mPSDevice.submit(params);
            } else {
                EventBus.getDefault().post(new StatInfoEvent(result));
                setChartData(BaseChartFragment.CHART_ELECTRICITY_BATTERY, result);
                closeLoadingFragment();
            }


            if (mCycleType == CycleType.DAY) {
                if (powerLevelResult == null) {
                    params.put(PSKeyConstant.CMD, DsCamCmd.GET_STATS_BATTERY_POWERLEVEL);
                    ElectricityStatisticsFragment.mPSDevice.submit(params);
                } else {
                    setPowerLevelData(powerLevelResult);
                }
            }
        }
    }


    @Override
    protected void setDayChart() {
        setPowerLevelVisible(true);
        mMarkerView.setInterval(mInterval);
        mMarkerView.setPlusMinusType(mPlusMinusType);
        mMarkerView.setMultiply(isMultiply);
        mMarkerView.setSelectedPositions(mFlipCombinedChartView.getSelectPositions());
        int count = mHourCount / mInterval + 1;
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        LineData lineData = new LineData(xVals, mBatteryChartHelper.getLineDataSets(mChartData, mFlipCombinedChartView,
                mPlusMinusType, isDualPowerOpen, isMultiply));
        data.setData(lineData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    @Override
    protected void setWeekChart() {
        setPowerLevelVisible(false);
        refreshWeek2Lifetime(mWeeks);
    }

    @Override
    protected void setMonthChart() {
        setPowerLevelVisible(false);
        refreshWeek2Lifetime(months);
    }

    @Override
    protected void setYearChart() {
        setPowerLevelVisible(false);
        refreshWeek2Lifetime(mYears);
    }

    @Override
    protected void setLifetimeChart() {
        setPowerLevelVisible(false);
        refreshWeek2Lifetime(lifetimes);
    }

    @Override
    protected void setFailDayChart() {
        setPowerLevelVisible(true);
        int count = mHourCount / mInterval + 1;
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        LineData lineData = new LineData(xVals, mBatteryChartHelper.getLineDataSets(mChartData, mFlipCombinedChartView,
                mPlusMinusType, isDualPowerOpen, isMultiply));
        data.setData(lineData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    @Override
    protected void setFailWeekChart() {
        setPowerLevelVisible(false);
        refreshFailWeek2Lifetime(mWeeks);
    }

    @Override
    protected void setFailMonthChart() {
        setPowerLevelVisible(false);
        refreshFailWeek2Lifetime(months);
    }

    @Override
    protected void setFailYearChart() {
        setPowerLevelVisible(false);
        refreshFailWeek2Lifetime(mYears);
    }

    @Override
    protected void setFailLifetimeChart() {
        setPowerLevelVisible(false);
        refreshFailWeek2Lifetime(lifetimes);
    }

    private void refreshWeek2Lifetime(String[] xLabels) {
        mMarkerView.setPlusMinusType(mPlusMinusType);
        mMarkerView.setMultiply(isMultiply);
        mMarkerView.setSelectedPositions(mFlipCombinedChartView.getSelectPositions());
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();

//        for (int i=0; i<xLabels.length; i++) {
//            if (isMultiply) {
//                yVals.add(new BarEntry(new float[]{0, 0}, i));
//            } else {
//                yVals.add(new BarEntry(new float[]{0}, i));
//            }
//        }
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            yVals.add(mBatteryChartHelper.getBarEntry(mFlipCombinedChartView, sonData, mPlusMinusType,
                    isDualPowerOpen, isMultiply, xIndex));
        }

        BarDataSet barDataSetWeek = new BarDataSet(yVals, "");
        int[] colorsBar = mBatteryChartHelper.getBarColors(true, isDualPowerOpen, isMultiply);
        barDataSetWeek.setColors(colorsBar);
        barDataSetWeek.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSetWeek.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSetWeek.setHighLightColor(getColor(R.color.color_white_03));
        barDataSetWeek.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSetWeek);
        BarData barData = new BarData(xVals, dataSets);
        CombinedData data = new CombinedData(xLabels);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    private void refreshFailWeek2Lifetime(String[] xLabels) {
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();

        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            yVals.add(mBatteryChartHelper.getBarEntry(mFlipCombinedChartView, sonData, mPlusMinusType,
                    isDualPowerOpen, isMultiply, xIndex));
        }

        BarDataSet barDataSetWeek = new BarDataSet(yVals, "");
        int[] colorsBar = mBatteryChartHelper.getBarColors(true, isDualPowerOpen, isMultiply);
        barDataSetWeek.setColors(colorsBar);
        barDataSetWeek.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSetWeek.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSetWeek.setHighLightColor(getColor(R.color.color_white_03));
        barDataSetWeek.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSetWeek);
        BarData barData = new BarData(xVals, dataSets);
        CombinedData data = new CombinedData(xLabels);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    public void setPowerLevelVisible(boolean visible) {
        if (mPowerLevelChart != null) {
            mPowerLevelChart.setVisibility(visible ? View.VISIBLE : View.GONE);
        }
    }

    private float getDischargedVal() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float dischargedVal = 0f;
        for (List<Float> sonData : mChartData) {
            dischargedVal = dischargedVal + sonData.get(1);
        }
        return mCycleType == CycleType.DAY ? dischargedVal * mInterval / 60 : dischargedVal;
    }

    private float getChargedVal(boolean isFilter) {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float chargedVal = 0f;
        for (List<Float> sonData : mChartData) {
            if (isFilter && mFlipCombinedChartView != null) {
                if (mFlipCombinedChartView.isFilterSelected(0)) {
                    chargedVal = chargedVal + sonData.get(2);
                }
                if (mFlipCombinedChartView.isFilterSelected(1)) {
                    chargedVal = chargedVal + sonData.get(3);
                }
            } else {
                chargedVal = chargedVal + (sonData.get(2) + sonData.get(3));
            }
//            chargedVal = chargedVal + (sonData.get(2) + sonData.get(3));
        }
        return mCycleType == CycleType.DAY ? chargedVal * mInterval / 60 : chargedVal;
    }

    private float getNetVal() {
        return getDischargedVal() - getChargedVal(false);
    }

    private float getSolarChargedVal(boolean isFilter) {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float solarChargedVal = 0f;
        if (isFilter) {
            if (mFlipCombinedChartView != null && mFlipCombinedChartView.isFilterSelected(0)) {
                for (List<Float> sonData : mChartData) {
                    solarChargedVal = solarChargedVal + sonData.get(2);
                }
            }
        } else {
            for (List<Float> sonData : mChartData) {
                solarChargedVal = solarChargedVal + sonData.get(2);
            }
        }
        if (mBmtType == BmtUtil.BMT_POWER_CORE) {
            return mCycleType == CycleType.DAY ? solarChargedVal * mInterval / 60 : solarChargedVal;
        } else {
            return 0f;
        }
    }

    private float getSolarDualVal(boolean isFilter) {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float solarDualVal = 0f;
        if (isFilter) {
            int filterIndex = (mBmtType == BmtUtil.BMT_POWER_STORE)
                    ? 0 : 1;
            if (mFlipCombinedChartView != null && mFlipCombinedChartView.isFilterSelected(filterIndex)) {
                for (List<Float> sonData : mChartData) {
                    if (sonData.size() > 4) {
                        solarDualVal = solarDualVal + sonData.get(4);
                    }
                }
            }
        } else {
            for (List<Float> sonData : mChartData) {
                if (sonData.size() > 4) {
                    solarDualVal = solarDualVal + sonData.get(4);
                }
            }
        }
        return mCycleType == CycleType.DAY ? solarDualVal * mInterval / 60 : solarDualVal;
    }

    private float getGridChargedVal(boolean isFilter) {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float gridChargedVal = 0f;
        if (isFilter) {
            if (mFlipCombinedChartView != null && mFlipCombinedChartView.isFilterSelected(mGridChargeIndex)) {
                for (List<Float> sonData : mChartData) {
                    gridChargedVal = gridChargedVal + sonData.get(3);
                }
            }
        } else {
            for (List<Float> sonData : mChartData) {
                gridChargedVal = gridChargedVal + sonData.get(3);
            }
        }
        return mCycleType == CycleType.DAY ? gridChargedVal * mInterval / 60 : gridChargedVal;
    }

    public void setPowerLevelDataFromServer(Map<String, Object> result) {
        if (mOffSet < 0) {
            mPowerLevelCache.put(mOffSet, result);
        }
        setPowerLevelData(result);
    }

    private void checkPowerLevelChart() {
        if (mVSPowerLeve != null) {
            if (mPowerLevelChart == null) {
                View view = mVSPowerLeve.inflate();
                mPowerLevelChart = view.findViewById(R.id.fc_power_level);
                initPowerLevel();
            }
        }
    }
    /**
     * 电池百分比数据
     *
     * @param result
     */
    public void setPowerLevelData(Map<String, Object> result) {
        checkPowerLevelChart();
        long time = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0);
        String zone = DeviceHelper.getString(result, BmtDataKey.TIMEZONE, "");
        if (DDDateUtil.isDSTTransitionDay(time * 1000, TimeZone.getTimeZone(zone))) {
            mHourCount = 1500;
        } else if (DDDateUtil.isWT2ST(time * 1000, TimeZone.getTimeZone(zone))) {
            mHourCount = 1380;
        } else {
            mHourCount = 1440;
        }
        mPLInterval = DeviceHelper.getInt(result, BmtDataKey.INTERVAL, 1);
        List<List<Float>> plData = (List<List<Float>>) result.get(BmtDataKey.DATA);
        int labelsToSkip = mHourCount / mPLInterval / 4 - 1;

        for (CustomCombinedChartManager chartManager : mPLManagers) {
            chartManager.initXAxis(false, false, getColor(R.color.color_white_04), 10f,
                    false, getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                    labelsToSkip, xPLFormatter);

            chartManager.initYAxis(true, 10f, getColor(R.color.color_white_03),
                    0.5f, false, false, getColor(R.color.color_white_04),
                    yPLFormatter, false, 3);
            chartManager.setYAxisMaxMin(100f, 0f, 2);
        }
        int count = mHourCount / mPLInterval + 1;
        ArrayList<Entry> entries = new ArrayList<Entry>();
        for (int i = 0; i < count; i++) {
            entries.add(new Entry(0, i));
        }

        for (List<Float> sonData : plData) {
            int offset = Math.round(Math.abs(sonData.get(0))) / mPLInterval;
            float val = 0;
            val = val + sonData.get(1);
            if (offset >= 0) {
//                        entries.add(new Entry(val, offset));
                entries.get(offset).setVal(val);
            }
        }
        SectionLineDataSet dayLineData = getSectionLineDataSet(entries, getColor(R.color.color_tip_03), YAxis.AxisDependency.LEFT,
                null, null, false, true);
        dayLineData.setHighlightEnabled(false);
        dayLineData.setMode(LineDataSet.Mode.LINEAR);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        lineDataSets.add(dayLineData);
        LineData lineData = new LineData(xVals, lineDataSets);
        data.setData(lineData);
        mPowerLevelChart.getDisplayCombinedChart(mIndex).setData(data);
        mPowerLevelChart.getDisplayCombinedChart(mIndex).invalidate();
        if (chartModelController == null) {
            createChartModelController();
        }
        chartModelController.initLineChartRender(getContext(), mPLManagers.get(mIndex), CycleType.DAY,
                PlusMinusType.PLUS);
        chartModelController.initBarChartRender(getContext(), mPLManagers.get(mIndex), CycleType.DAY,
                PlusMinusType.PLUS);
    }

    public void getPowerLevelFailed() {
        checkPowerLevelChart();
        mHourCount = 1440;
        mPLInterval = 5;
        int labelsToSkip = mHourCount / mPLInterval / 4 - 1;
        for (CustomCombinedChartManager chartManager : mPLManagers) {
            chartManager.initXAxis(false, false, getColor(R.color.color_white_04), 10f,
                    false, getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                    labelsToSkip, xPLFormatter);

            chartManager.initYAxis(true, 10f, getColor(R.color.color_white_03),
                    0.5f, false, false, getColor(R.color.color_white_04),
                    yPLFormatter, false, 3);
            chartManager.setYAxisMaxMin(100f, 0f, 2);
        }
        int count = mHourCount / mPLInterval + 1;
        ArrayList<Entry> entries = new ArrayList<Entry>();
        for (int i = 0; i < count; i++) {
            entries.add(new Entry(0, i));
        }
        SectionLineDataSet dayLineData = getSectionLineDataSet(entries, getColor(R.color.transparent), YAxis.AxisDependency.LEFT,
                null, null, false, true);
        dayLineData.setHighlightEnabled(false);
        dayLineData.setMode(LineDataSet.Mode.LINEAR);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        lineDataSets.add(dayLineData);
        LineData lineData = new LineData(xVals, lineDataSets);
        data.setData(lineData);
        mPowerLevelChart.getDisplayCombinedChart(mIndex).setData(data);
        mPowerLevelChart.getDisplayCombinedChart(mIndex).invalidate();
        chartModelController.initLineChartRender(getContext(), mPLManagers.get(mIndex), CycleType.DAY,
                PlusMinusType.PLUS);
        chartModelController.initBarChartRender(getContext(), mPLManagers.get(mIndex), CycleType.DAY,
                PlusMinusType.PLUS);
    }
}
