package com.dinsafer.module.powerstation;

import android.animation.ArgbEvaluator;
import android.content.Context;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class ChartDataUtil {

    private static String TAG = ChartDataUtil.class.getSimpleName();

    public static String DAY = "day";
    public static String WEEK = "week";
    public static String MONTH = "month";
    public static String YEAR = "year";
    public static String LIFETIME = "lifetime";

    public static final String ONE_POINT = "#.0";

    public static int findUpperRelativePrice(float num) {
        boolean isNegative = num < 0;
        float absN = Math.abs(num);
        int upperLimit = 50;
        if (absN >= 50f) {
            if (((int)saveFloatPoint(num, 0)) % 50 == 0) {
                upperLimit = (int) (saveFloatPoint(num, 0) + 50);
            } else {
                upperLimit = (int) (Math.ceil(absN / 50.0) * 50);
            }
        }
        return isNegative ? -upperLimit : upperLimit;
    }

    public static boolean isDivisibleByHalf(float value) {
        return value * 2 == Math.floor(value * 2);
    }

    public static float findUpperPriceLimit(float num) {
        float result = 0.2f;
        if (num >= 0.2f) {
            float dividedNumber = num / 0.2f;
            float roundedUp = (float) Math.ceil(dividedNumber);
            result = roundedUp * 0.2f;
        }
        return result;
    }

    public static float findUpperLimit(float n, boolean isW) {
        boolean isNegative = n < 0;
        float absN = Math.abs(n);
        float upperLimit = 0f;

        if (absN < 100.0f) {
            upperLimit = 100.0f;

        } else if (absN < 1000) {
            upperLimit = (float) ((Math.floor(absN / 100.0f) + 1.0f) * 100.0f);
        } else if (absN < 1000000.0f) {
            upperLimit = (float) ((Math.floor(absN / 1000.0f) + 1.0f) * 1000.0f);

        } else {
            upperLimit = (float) ((Math.floor(absN / 1000000.0f) + 1.0f) * 1000000.0f);
        }
        return isNegative ? -upperLimit : upperLimit;
    }

    public static float getActMax(float maxVal, boolean isW) {
        if (isW) {
            if (maxVal < 500f) {
                return 500f;
            } else if (maxVal < 1000f) {
                return 1000f;
            } else if (maxVal < 5000f) {
                return 5000f;
            } else if (maxVal < 10000f) {
                return 10000f;
            } else if (maxVal < 15000f) {
                return 15000f;
            } else if (maxVal < 20000f) {
                return 20000f;
            } else if (maxVal < 25000f) {
                return 25000f;
            } else if (maxVal < 30000f) {
                return 30000f;
            } else if (maxVal < 35000f) {
                return 35000f;
            } else if (maxVal < 40000f) {
                return 40000f;
            } else if (maxVal < 45000f) {
                return 45000f;
            } else if (maxVal < 50000f) {
                return 50000f;
            } else if (maxVal < 100000f) {
                return 100000f;
            } else if (maxVal < 500000f) {
                return 500000f;
            } else if (maxVal < 1000000f) {
                return 1000000f;
            } else if (maxVal < 5000000f) {
                return 5000000f;
            } else if (maxVal < 10000000f) {
                return 10000000f;
            } else if (maxVal < 50000000f) {
                return 50000000f;
            } else if (maxVal < 100000000f) {
                return 100000000f;
            } else if (maxVal < 500000000f) {
                return 500000000f;
            } else {
                return 1000000000f;
            }
        } else {
            if (maxVal < 5000f) {
                return 5000f;
            } else if (maxVal < 10000f) {
                return 10000f;
            } else if (maxVal < 15000f) {
                return 15000f;
            } else if (maxVal < 20000f) {
                return 20000f;
            } else if (maxVal < 25000f) {
                return 25000f;
            } else if (maxVal < 30000f) {
                return 30000f;
            } else if (maxVal < 35000f) {
                return 35000f;
            } else if (maxVal < 40000f) {
                return 40000f;
            } else if (maxVal < 45000f) {
                return 45000f;
            } else if (maxVal < 50000f) {
                return 50000f;
            } else if (maxVal < 100000f) {
                return 100000f;
            } else if (maxVal < 500000f) {
                return 500000f;
            } else if (maxVal < 1000000f) {
                return 1000000f;
            } else if (maxVal < 5000000f) {
                return 5000000f;
            } else if (maxVal < 10000000f) {
                return 10000000f;
            } else if (maxVal < 50000000f) {
                return 50000000f;
            } else if (maxVal < 100000000f) {
                return 100000000f;
            } else if (maxVal < 500000000f) {
                return 500000000f;
            } else {
                return 1000000000f;
            }
        }
    }

    public static int getLeafCount(int percentage) {
        if (percentage >= 60) {
            return 4;
        } else if (percentage >= 30) {
            return 3;
        } else if (percentage >= 10) {
            return 2;
        } else if (percentage > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 获取今日收益说明文案 (与getRevenueImgDrawable关联, 文案和图片位置要保持一致)
     *
     * @param context
     * @param value
     * @param index
     * @return
     */
    public static String getRevenueNote(Context context, String value, int index) {
        String[] noteArr = context.getResources().getStringArray(R.array.ps_today_revenue_note_array);
        String[] valKey = context.getResources().getStringArray(R.array.ps_today_revenue_value_key_array);
        if (index < 0 || index >= noteArr.length) {
            DDLog.e(TAG, "数组越界");
            return "";
        }
        return Local.s(noteArr[index]).replace(valKey[index], value);
    }

    /**
     * 获取今日收益图片 (与getRevenueNote关联, 文案和图片位置要保持一致)
     *
     * @param index
     * @return
     */
    public static int getRevenueImgDrawable(int index) {
        int[] imgArr = {R.drawable.img_impacts_e_bike, R.drawable.img_impacts_car, R.drawable.img_impacts_lamp,
                R.drawable.img_impacts_refrigerator, R.drawable.img_impacts_ac, R.drawable.img_impacts_fan,
                R.drawable.img_impacts_tv, R.drawable.img_impacts_beer};
        if (index < 0 || index >= imgArr.length) {
            DDLog.e(TAG, "数组越界");
            return 0;
        }
        return imgArr[index];
    }

    /**
     * 获取今日收益功耗换算
     *
     * @param index
     * @param value
     * @return
     */
    public static float getValInNote(int index, float value) {
        float result = 0f;
        switch (index) {
            case 0:
                result = 80 * value;
                break;

            case 1:
                result = 7 * value;
                break;

            case 2:
                result = 100 * value;
                break;

            case 3:
                result = 15 * value;
                break;
            case 4:
                result = 1.4f * value;
                break;

            case 5:
                result = 20 * value;
                break;

            case 6:
                result = 10 * value;
                break;

            case 7:
                result = 15 * value;
                break;
        }
        return result;
    }

    /**
     * 时间戳和时区统计算时间标题
     *
     * @param time
     * @param timezone
     * @param cycleType
     * @return
     */
    public static String getResetTime(long time, String timezone, CycleType cycleType) {
        String title = DDDateUtil.formatWithTimezone(time, timezone, "yyyy.MM.dd");
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(time);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        Date startDate = DDDateUtil.getCurrentWeekDayStartOrEndTime(calendar, true);
        Date endDate = DDDateUtil.getCurrentWeekDayStartOrEndTime(calendar, false);
        switch (cycleType) {
            case WEEK:
                title = DDDateUtil.formatDate(startDate, "MM.dd") + "-" + DDDateUtil.formatDate(endDate, "MM.dd");
                break;

            case MONTH:
                int days = DDDateUtil.getDaysByYearMonth(year, month);
                BaseChartFragment.months = new String[days];
                for (int i = 0; i < days; i++) {
                    String d = (i + 1) < 10 ? ("0" + (i + 1)) : "" + (i + 1);
                    BaseChartFragment.months[i] = (month) + "." + d;
                }
                title = year + "." + (month < 10 ? ("0" + month) : ("" + month));
                break;

            case YEAR:
                title = year + "";
                break;

            case LIFETIME:
                title = DinSaferApplication.getAppContext().getString(R.string.electricity_lifetime);
//                for (int i = 0; i < BaseChartFragment.lifetimes.length; i++) {
//                    BaseChartFragment.lifetimes[i] = (year + i) + "";
//                }
                break;
        }
        return title;
    }

    public static int getYear(long time, String timezone) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(time);
        int year = calendar.get(Calendar.YEAR);
        return year;
    }

    public static float[] getPositions(float maxY) {
        float limit = 10800f * 0.45f;
        float[] pos = new float[3];
        pos[0] = 0f;
        if (maxY < limit) {
            pos[1] = 0f;
        } else {
            pos[1] = (maxY - limit) / maxY;
        }
        pos[2] = 1f;
        return pos;
    }

    public static int[] getLineGradientColor(Context context, float maxY, boolean alpha) {
        int[] colors = new int[3];
        float limit = 10800 * 0.45f;
        float fraction = maxY / limit;
        if (fraction > 1.0f) {
            fraction = 1.0f;
        }
        fraction = 1.0f - fraction;
        if (alpha) {
            colors[0] = context.getResources().getColor(R.color.chart_line_gradient_color_alpha_2);
            colors[1] = getInterpolatedColor(context.getResources().getColor(R.color.chart_line_gradient_color_alpha_2),
                    context.getResources().getColor(R.color.chart_line_gradient_color_alpha_3), fraction);
            colors[2] = context.getResources().getColor(R.color.chart_line_gradient_color_alpha_3);
        } else {
            colors[0] = context.getResources().getColor(R.color.chart_line_gradient_color_2);
            colors[1] = getInterpolatedColor(context.getResources().getColor(R.color.chart_line_gradient_color_2),
                    context.getResources().getColor(R.color.chart_line_gradient_color_3), fraction);
            colors[2] = context.getResources().getColor(R.color.chart_line_gradient_color_3);
        }
        return colors;
    }

    public static int getInterpolatedColor(int color1, int color2, float fraction) {
        ArgbEvaluator evaluator = new ArgbEvaluator();
        return (int) evaluator.evaluate(fraction, color1, color2);
    }

    public static String getPowerUnit(float maxVal, boolean isHour) {
        if (isHour) {
            if (Math.abs(maxVal) < 1000) {
                return "Wh";
            } else if (Math.abs(maxVal) < 1000000) {
                return "kWh";
            } else {
                return "MWh";
            }
        } else {
            if (Math.abs(maxVal) < 1000) {
                return "w";
            } else if (Math.abs(maxVal) < 1000000) {
                return "kw";
            } else {
                return "MW";
            }
        }
    }

    public static String getWStr(float w) {
//        double val = savePoint(w, 1);
        return savePointStr(w, 1);
    }

    public static String getW2KWStr(float w) {
        return savePointStr(w / 1000d, 1);
    }

    public static String getW2MWStr(float w) {
        return savePointStr(w / 1000000d, 1);
    }

    public static String getPowerTransferVal(float val, float maxVal, boolean isW) {
        String result = "";
        if (Math.abs(maxVal) < 1000) {
            result = getWStr(val);
        } else if (Math.abs(maxVal) < 1000000) {
            result = getW2KWStr(val);
        } else {
            result = getW2MWStr(val);
        }
        return result;
    }

    public static String trimPointZero(String str) {
        if (str.startsWith(".")) str = "0" + str;
        if (str.startsWith("-.")) str = str.replace("-.", "-0.");
        if (str.endsWith(".0")) {
            str = str.replace(".0", "");
        }
        if (str.equals("-0")) str = "0";
        return str;
    }

    public static int getRandom(int range, int startFrom) {
        return (int) (Math.random() * range) + startFrom;
    }

    public static BigDecimal getBigDecimal(String numStr) {
        BigDecimal num = new BigDecimal(str2DoubleByDecimalFormat(numStr, 1));
        return num;
    }

    public static BigDecimal getBigDecimalSub(String numStr1, String numStr2) {
        BigDecimal num1 = new BigDecimal(str2DoubleByDecimalFormat(numStr1, 1));
        BigDecimal num2 = new BigDecimal(str2DoubleByDecimalFormat(numStr2, 1));
        BigDecimal result = num1.subtract(num2);
        return result;
    }

    public static float saveFloatPoint(float num, int scale) {
        float result = new BigDecimal(num).setScale(scale, BigDecimal.ROUND_HALF_EVEN).floatValue();
        return result;
    }

    public static double savePoint(float num, int scale) {
        double result = new BigDecimal(num).setScale(scale, BigDecimal.ROUND_HALF_EVEN).doubleValue();
        return result;
    }

    public static String savePointStr(double num, int count) {
        DecimalFormat df = new DecimalFormat();
        df.setMinimumFractionDigits(0);
        df.setMaximumFractionDigits(count);
        df.setMinimumIntegerDigits(1);
        df.setRoundingMode(RoundingMode.HALF_EVEN);
        String valStr = df.format(num);
        return valStr;
    }

    public static String savePointStr(double num, int minimumFractionDigits, int count) {
        DecimalFormat df = new DecimalFormat();
        df.setMinimumFractionDigits(minimumFractionDigits);
        df.setMaximumFractionDigits(count);
        df.setMinimumIntegerDigits(1);
        df.setRoundingMode(RoundingMode.HALF_EVEN);
        String valStr = df.format(num);
        return valStr;
    }

    public static double str2DoubleByDecimalFormat(String str, int count) {
        DecimalFormat df = new DecimalFormat();
        df.setMinimumFractionDigits(0);
        df.setMaximumFractionDigits(count);
        df.setMinimumIntegerDigits(1);
        df.setRoundingMode(RoundingMode.HALF_EVEN);
        double val = 0;
        try {
            val = df.parse(str).doubleValue();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return val;
    }

    public static String unitFormat(float maxVal, boolean isW) {
        String maxValStr = "";
        if (maxVal < 1000) {
            maxValStr = ChartDataUtil.savePointStr(maxVal, 1) + (isW ? "W" : "Wh");
        } else if (maxVal < 1000000) {
            maxValStr = ChartDataUtil.savePointStr(maxVal / 1000, 1) + (isW ? "kW" : "kWh");
        } else {
            maxValStr = ChartDataUtil.savePointStr(maxVal / 1000000, 1) + (isW ? "MW" : "kWh");
        }
        return maxValStr;
    }
}
