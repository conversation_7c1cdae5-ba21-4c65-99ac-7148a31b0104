package com.dinsafer.module.powerstation.dialog;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogReserveModePriceTypeBinding;
import com.dinsafer.permission.BaseBottomSheetDialog;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/27 11:01
 * @description :
 */
public class ReserveModePriceTypeDialog extends BaseBottomSheetDialog<DialogReserveModePriceTypeBinding> {

    public static final String TAG = ReserveModePriceTypeDialog.class.getSimpleName();

    public static final String KEY_PRICE_TYPE = "key_price_type";
    public static final String KEY_PERCENT = "key_percent";
    private int mType;  // 1 ignore 2accepted price
    private int mPercent;

    public static ReserveModePriceTypeDialog newInstance(final Builder builder) {
        ReserveModePriceTypeDialog dialog = new ReserveModePriceTypeDialog();
        Bundle args = new Bundle();
        args.putInt(KEY_PRICE_TYPE, builder.type);
        args.putInt(KEY_PERCENT, builder.percent);
        dialog.setPriceTypeListener(builder.priceTypeListener);
        dialog.setArguments(args);
        return dialog;
    }
    @Override
    protected int provideResId() {
        return R.layout.dialog_reserve_mode_price_type;
    }

    @Override
    protected void initView() {
        super.initView();
        setEnableDrag(false);
        mBinding.getRoot().setBackgroundResource(R.drawable.shape_bg_cascading_reserve_mode);
        mBinding.ivClose.setOnClickListener(v -> dismiss());
        mBinding.tvConfirm.setOnClickListener(v -> {
            if (mPriceTypeListener!=null) {
                if (mType == 1) {
                    mPercent = -1;
                }
                mPriceTypeListener.onPriceType(mType, mPercent);
            }
            dismiss();
        });
        mBinding.tvPercent.setOnClickListener(v -> {
           showPercentDialog();
        });
        Bundle bundle = getArguments();
        mType = bundle.getInt(KEY_PRICE_TYPE);
        mPercent = bundle.getInt(KEY_PERCENT);
        updateState();
        setPercentEnable(mType==2);
        if (mType == 2) {
            mBinding.tvPercent.setText(mPercent+"%");
        }
        mBinding.llIgnore.setOnClickListener( v -> {
            setType(1);
            setPercentEnable(false);
        });

        mBinding.llAcceptablePrice.setOnClickListener( v -> {
            if (mPercent < 0 || mPercent>1000) {
                mPercent = 100;
            }
            setType(2);
            setPercentEnable(true);
        });
    }

    private void setPercentEnable(boolean enabled) {
        mBinding.tvPercent.setAlpha(enabled ? 1 : 0.5f);
        mBinding.tvPercent.setEnabled(enabled);
    }

    /**
     * 百分比弹窗
     */
    private void showPercentDialog() {
        int percent = 0;
        try {
            String text = mBinding.tvPercent.getText().toString();
            percent = Integer.parseInt(text.substring(0, text.length()-1));
        }catch (NumberFormatException e) {
            e.printStackTrace();
        }

        new PercentDialog.Builder()
                .setPercent(percent)
                .setSelectPercentListener(new PercentDialog.OnSelectPercentListener() {
                    @Override
                    public void onSelect(int percent) {
                        mPercent = percent;
                        mBinding.tvPercent.setText(percent+"%");
                    }
                }).build().show(getFragmentManager(), PercentDialog.TAG);
    }

    /**
     * 选中类型
     * @param type
     */
    private void setType(int type) {
        mType = type;
        updateState();
    }

    private void updateState() {
        mBinding.ivIgnoreChecked.setVisibility(mType == 1 ? View.VISIBLE : View.INVISIBLE);
        mBinding.ivAcceptedChecked.setVisibility(mType == 2 ? View.VISIBLE : View.INVISIBLE);
    }

    private OnPriceTypeListener mPriceTypeListener;

    public void setPriceTypeListener(OnPriceTypeListener priceTypeListener) {
        this.mPriceTypeListener = priceTypeListener;
    }

    public interface OnPriceTypeListener {
        void onPriceType(int type, int percent);
    }

    public static final class Builder {
        private int type;
        private int percent;
        private OnPriceTypeListener priceTypeListener;

        public Builder setType(int type) {
            this.type = type;
            return this;
        }

        public Builder setPercent(int percent) {
            this.percent = percent;
            return this;
        }

        public Builder setPriceTypeListener(OnPriceTypeListener priceTypeListener) {
            this.priceTypeListener = priceTypeListener;
            return this;
        }

        public ReserveModePriceTypeDialog build() {
            return newInstance(this);
        }
    }
}
