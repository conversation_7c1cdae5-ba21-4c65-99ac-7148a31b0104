package com.dinsafer.module.powerstation;

public class PSReserveModeConstant {

    public static final int PRICE_INSENSITIVE = 1;
    public static final int SMART_CHARGE = 2;
    public static final int EXTREME_SAVING = 3;

    public static final int PRICE_INSENSITIVE_SMART = 100;
    public static final int PRICE_INSENSITIVE_EMERGENCY = 50;
    public static final int PRICE_INSENSITIVE_GOOD_PRICE = -1;
    public static final int PRICE_INSENSITIVE_ACCEPTABLE_PRICE = -1;

    public static final int SMART_CHARGE_SMART = 90;
    public static final int SMART_CHARGE_EMERGENCY = 50;
    public static final int SMART_CHARGE_GOOD_PRICE = 80;
    public static final int SMART_CHARGE_ACCEPTABLE_PRICE = 500;

    public static final int EXTREME_SAVING_SMART = 80;
    public static final int EXTREME_SAVING_EMERGENCY = 30;
    public static final int EXTREME_SAVING_GOOD_PRICE = 50;
    public static final int EXTREME_SAVING_ACCEPTABLE_PRICE = 200;
}
