package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutAiScheduleModeViewBinding;
import com.dinsafer.module.powerstation.widget.AIScheduledCardView;
import com.dinsafer.module.powerstation.widget.DragSelectTouchListener;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.ScreenUtils;

import java.util.ArrayList;
import java.util.List;


public class AIScheduledModeView extends ConstraintLayout {

    private Context mContext;
    private LayoutAiScheduleModeViewBinding mBinding;

    private BindMultiAdapter<BindModel> mScheduledModeAdapter;
    private List<BindModel> mScheduledModeData;
    private List<AIScheduleModeModel> mSelectedModelData;

    private LinearLayoutManager mLayoutManager;
    private DragSelectTouchListener mTouchListener;
    private int startX;
    private int endX;
    private boolean canTouch = true;
    private boolean isCharWithGrid = true;
    private AISelectedDecoration mAISelectedDecoration;
    private int mRVPaddingTop;
    private int mItemHeight;
    private int mPVForecastHeight;
    private Rect mRect;
    private boolean isIgnoreScrollListener;
    private boolean isEdit;
    private View mExpandView;
    private AIScheduleModeModel mExpandModeModel;
    private int mLastItemHour = -1;
    private int enabledSize;
    private int clickPosition;
    private AIDividerDecoration mAIDividerDecoration;
    private AIBounderDecoration mAIBounderDecoration;
    private AIExpressDecoration mAIExpressDecoration;
    private boolean isExpanding;
    private AIScheduledCardView mAIScheduledCardView;
    private final int mTopBottomOffset;
    private boolean isAnimating;

    public AIScheduledModeView(@NonNull Context context) {
        this(context, null);
    }

    public AIScheduledModeView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AIScheduledModeView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mTopBottomOffset = DensityUtil.dp2px(context, 5);
        init(context);
    }

    protected void init(Context context) {
        mContext = context;
        mRect = new Rect();
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_ai_schedule_mode_view, this, true);
        mLayoutManager = new LinearLayoutManager(getContext());
        mBinding.rvData.setLayoutManager(mLayoutManager);
        ((SimpleItemAnimator) mBinding.rvData.getItemAnimator()).setSupportsChangeAnimations(false);
        mBinding.rvData.getItemAnimator().setChangeDuration(0); // 设置动画持续时间为0
        mScheduledModeAdapter = new BindMultiAdapter<>();
        mBinding.rvData.setAdapter(mScheduledModeAdapter);
        mScheduledModeData = new ArrayList<>();
        mSelectedModelData = new ArrayList<>();
        mAIDividerDecoration = new AIDividerDecoration(mContext);
        mBinding.rvData.addItemDecoration(mAIDividerDecoration);
        mAIBounderDecoration = new AIBounderDecoration(mContext);
        mBinding.rvData.addItemDecoration(mAIBounderDecoration);
        mAIExpressDecoration = new AIExpressDecoration(mContext, mScheduledModeAdapter);
        mBinding.rvData.addItemDecoration(mAIExpressDecoration);
        mAISelectedDecoration = new AISelectedDecoration(mContext, mScheduledModeAdapter);
        mBinding.rvData.addItemDecoration(mAISelectedDecoration);
        mRVPaddingTop = mBinding.rvData.getPaddingTop();
        mItemHeight = DensityUtil.dp2px(context, 24);
        mPVForecastHeight = DensityUtil.dp2px(context, 58);
        mScheduledModeAdapter.notifyDataSetChanged();

        mScheduledModeAdapter.setOnBindItemChildClickListener((OnBindItemChildClickListener<BindModel>) (view, position, model) -> {
            int viewId = view.getId();
            if (model instanceof AIScheduleModeModel) {
                if (viewId == R.id.view_data) {
                    if (!isEdit) {
                        if (isExpanding) return;
                        isExpanding = true;
                        mExpandView = view;
                        mLastItemHour = position == 0 ? -1 : ((AIScheduleModeModel) mScheduledModeData.get(position - 1)).getHour();
                        mExpandModeModel = (AIScheduleModeModel) model;
                        if (mExpandModeModel.isCustom()) {
                            mExpandModeModel = null;
                            isExpanding = false;
                            return;
                        }
                        clickPosition = position;
                        int[] viewLocation = new int[2];
                        view.getLocationOnScreen(viewLocation);
                        if (expandViewDataListener != null) {
                            isAnimating = true;
                            expandViewDataListener.onPrepare(mLastItemHour, mExpandModeModel);
                            ((AIScheduleModeModel) mScheduledModeData.get(clickPosition)).setExpand(true);
                            mBinding.rvData.postDelayed(() -> {
                                int expandHeight = mAIScheduledCardView.getContentHeight() + mTopBottomOffset * 2;
                                int heightSum = viewLocation[1] + expandHeight + expandHeight / 3;
                                int screenHeight = ScreenUtils.getScreenHeight(getContext());
                                mBinding.rvData.setScrollable(false);
                                if (heightSum > screenHeight) {
                                    mBinding.rvData.smoothScrollBy(0, heightSum - screenHeight);
                                } else {
                                    scaleSelectedViewHeightDelay();
                                }
                            }, 200);

                        }

                    }
                }
            }
        });
    }

    public void scaleSelectedViewHeightDelay() {
        if (mExpandView == null || mExpandModeModel == null) return;
        mBinding.rvData.postDelayed(() -> {
            scaleSelectedViewHeight();
            mBinding.rvData.setScrollable(true);
        }, 300);
    }

    public void scaleSelectedViewHeight() {
        if (mExpandView == null || mExpandModeModel == null) return;
        int startHeight = mExpandView.getHeight();
        boolean isExpand = startHeight == mItemHeight;
        if (expandViewDataListener != null) {
            expandViewDataListener.onStart(mExpandView, isExpand, mLastItemHour, mExpandModeModel);
        }
        int expandHeight = mAIScheduledCardView.getContentHeight() + mTopBottomOffset * 2;
        int endHeight = isExpand ? expandHeight : mItemHeight;

        int[] viewLocation = new int[2];
        mExpandView.getLocationOnScreen(viewLocation);
        ViewGroup.LayoutParams params = mExpandView.getLayoutParams();
        ValueAnimator animator = ValueAnimator.ofInt(startHeight, endHeight);
        animator.addUpdateListener(animation -> {
            if (mExpandView != null) {
                int val = (Integer) animation.getAnimatedValue();
                params.height = val;
                mExpandView.setLayoutParams(params);
                if (expandViewDataListener != null) {
                    expandViewDataListener.onLayoutParamsChange(mExpandView, viewLocation, mExpandView.getWidth(), val, expandHeight);
                }
            }
        });

        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                if (expandViewDataListener != null) {
                    expandViewDataListener.onEnd(isExpand);
                }
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        animator.setDuration(300); // 动画持续时间300毫秒
        animator.start(); // 开始动画
    }

    /**
     * 初始化触摸事件
     */
    public void initTouchListener() {
        mBinding.rvData.post(new Runnable() {
            @Override
            public void run() {
                startX = isCharWithGrid ? mBinding.rvData.getWidth() / 2 - DensityUtil.dp2px(mContext, 21)
                        : DensityUtil.dp2px(mContext, 60);
                endX = startX + DensityUtil.dp2px(mContext, 105);
                mTouchListener = new DragSelectTouchListener(false, startX, endX, 0, enabledSize - 1, true);
                mBinding.rvData.addOnItemTouchListener(mTouchListener);
                mTouchListener.setSelectListener(new DragSelectTouchListener.onSelectListener() {
                    @Override
                    public void onUp() {
                        mSelectedModelData.clear();
                        for (int i = 0; i < enabledSize; i++) {
                            AIScheduleModeModel aiScheduleModeModel = (AIScheduleModeModel) mScheduledModeAdapter.getItem(i);
                            if (aiScheduleModeModel.isSelected()) {
                                mSelectedModelData.add(aiScheduleModeModel);
                            }
                        }
                        mScheduledModeAdapter.notifyDataSetChanged();
                        if (mSelectedListener != null && CollectionUtil.isListNotEmpty(mSelectedModelData)) {
                            mSelectedListener.onSelected(mSelectedModelData);
                        }
                    }

                    @Override
                    public void onStartChange(int position) {
                        if (position < 0 || position >= enabledSize) return;
                        AIScheduleModeModel aiScheduleModeModel = (AIScheduleModeModel) mScheduledModeAdapter.getItem(position);
                        aiScheduleModeModel.setSelected(true);
                        mScheduledModeAdapter.notifyItemChanged(position);

                    }

                    @Override
                    public void onSelectChange(int start, int end, boolean isSelected) {
                        if (start < 0 || end >= enabledSize) {
                            return;
                        }

                        for (int i = start; i <= end; i++) {
                            AIScheduleModeModel aiScheduleModeModel = (AIScheduleModeModel) mScheduledModeAdapter.getItem(i);
                            aiScheduleModeModel.setSelected(isSelected);
                        }
                        mScheduledModeAdapter.notifyDataSetChanged();
                    }
                });
                setCanTouch(false);

                mBinding.rvData.addOnScrollListener(new RecyclerView.OnScrollListener() {
                    private int firstVisibleItem = 0;

                    @Override
                    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                        super.onScrollStateChanged(recyclerView, newState);
                        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                            if (!isIgnoreScrollListener) {
                                dealScroll(recyclerView);
                            }
                            scaleSelectedViewHeightDelay();
                        }
                    }

                    @Override
                    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                        super.onScrolled(recyclerView, dx, dy);
                        if (mAIBounderDecoration != null) {
                            mAIBounderDecoration.setScroll(dy);
                        }
                        if (mScrollListener != null) {
                            mScrollListener.onScrolled(recyclerView, dx, dy);
                        }
                        if (isIgnoreScrollListener) return;
                        if (mTouchListener.isTouched()) {
                            dealScroll(recyclerView);
                        }
                    }

                    private void dealScroll(RecyclerView recyclerView) {
                        // 获取当前RecyclerView的第一个可见item的位置
                        int itemCount = recyclerView.getChildCount();
                        View firstChild = recyclerView.getChildAt(0);
                        View endChild = recyclerView.getChildAt(itemCount - 1);
                        int firstPosition = recyclerView.getChildAdapterPosition(firstChild) + 1;
                        int endPosition = recyclerView.getChildAdapterPosition(endChild);
                        if (firstVisibleItem == firstPosition) return;
                        if (mScrollPositionListener != null) {
                            mScrollPositionListener.onScrollPosition(firstPosition, endPosition, firstPosition < firstVisibleItem);
                        }
                        firstVisibleItem = firstPosition;
                    }
                });
            }
        });
    }


    /**
     * 设置 RecyclerView 是否可用
     *
     * @param isEnabled
     */
    public void setCanTouch(boolean isEnabled) {
        canTouch = isEnabled;
        if (mTouchListener != null) {
            mTouchListener.setCanTouch(canTouch);
        }
    }

    public void scrollToPosition(int position, boolean isIgnoreScrollListener) {
        if (mBinding.rvData.getChildCount() <= 0) return;
        this.isIgnoreScrollListener = isIgnoreScrollListener;
        if (mScheduledModeAdapter == null || mLayoutManager == null) return;
        if (position < 0) position = 0;
        if (position > mScheduledModeAdapter.getItemCount() - 1)
            position = mScheduledModeAdapter.getItemCount() - 1;
        View firstItemView = mBinding.rvData.getChildAt(0);
        firstItemView.getGlobalVisibleRect(mRect);
        int firstItem = mBinding.rvData.getChildAdapterPosition(firstItemView);
        int diffPosition = position - firstItem;


        int firstItemHeight = mRect.height();
        int firstItemPosition = mBinding.rvData.getChildAdapterPosition(firstItemView);
        int offset = 0;
        int firstItemY = (int) firstItemView.getY();
        if (firstItemPosition == 0) {
            if (firstItemY > 0) {
                offset = firstItemY;
            }
        }
        if (position == 0) {
            offset = offset - mRVPaddingTop;
        }
        int extraOffset = 0;
        if (firstItemPosition < enabledSize) {
            if (position > enabledSize) {
                extraOffset = mPVForecastHeight;
            }
            int itemOffset = position == enabledSize ? 0 : mItemHeight;
            if (diffPosition < 0) {
                mBinding.rvData.smoothScrollBy(0, diffPosition * mItemHeight + offset - (mItemHeight - firstItemHeight) - itemOffset + extraOffset);
            } else if (diffPosition > 0) {
                mBinding.rvData.smoothScrollBy(0, (diffPosition - 1) * mItemHeight + offset + firstItemHeight - itemOffset + extraOffset);
            } else {
                mBinding.rvData.smoothScrollBy(0, offset - (mItemHeight - firstItemHeight) - itemOffset + extraOffset);
            }
        } else if (firstItemPosition == enabledSize) {
            if (firstItemView.getY() < 0) {
                if (diffPosition < 0) {
                    mBinding.rvData.smoothScrollBy(0, diffPosition * mItemHeight + offset - (mPVForecastHeight - firstItemHeight) - mItemHeight);
                } else if (diffPosition > 0) {
                    mBinding.rvData.smoothScrollBy(0, (diffPosition - 1) * mItemHeight + offset + firstItemHeight);
                } else {
                    mBinding.rvData.smoothScrollBy(0, offset - (mPVForecastHeight - firstItemHeight));
                }
            } else {
                if (diffPosition < 0) {
                    mBinding.rvData.smoothScrollBy(0, diffPosition * mItemHeight + offset - mItemHeight);
                } else if (diffPosition > 0) {
                    mBinding.rvData.smoothScrollBy(0, (diffPosition - 1) * mItemHeight + firstItemHeight + offset);
                }
            }
        } else {
            if (diffPosition < 0) {
                if (position < enabledSize) {
                    extraOffset = mItemHeight;
                }
                mBinding.rvData.smoothScrollBy(0, (diffPosition + 1) * mItemHeight + offset - (mItemHeight - firstItemHeight) - mPVForecastHeight - extraOffset);
            } else if (diffPosition > 0) {
                mBinding.rvData.smoothScrollBy(0, diffPosition * mItemHeight + offset + firstItemHeight - mItemHeight);
            } else {
                mBinding.rvData.smoothScrollBy(0, offset - (mItemHeight - firstItemHeight));
            }
        }
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
        if (CollectionUtil.isListNotEmpty(mScheduledModeData)) {
            for (int i = 0; i < enabledSize; i++) {
                BindModel bindModel = mScheduledModeData.get(i);
                if (bindModel instanceof AIScheduleModeModel) {
                    AIScheduleModeModel aiScheduleModeModel = (AIScheduleModeModel) bindModel;
                    aiScheduleModeModel.setEdit(edit);
                }
            }
            mScheduledModeAdapter.notifyItemRangeChanged(0, enabledSize);
        }
    }

    public void setIgnoreScrollListener(boolean ignoreScrollListener) {
        isIgnoreScrollListener = ignoreScrollListener;
    }

    public void resetExpandView() {
        mExpandView = null;
        mExpandModeModel = null;
        mLastItemHour = -1;
        mBinding.rvData.postDelayed(() -> {
            ((AIScheduleModeModel) mScheduledModeData.get(clickPosition)).setExpand(false);
            isExpanding = false;
            isAnimating = false;
        }, 200);
    }

    /**
     * 取消高亮
     */
    public void canCelHighLight() {
        if (CollectionUtil.isListNotEmpty(mScheduledModeData)) {
            for (BindModel modeModel : mScheduledModeData) {
                if (modeModel instanceof AIScheduleModeModel) {
                    ((AIScheduleModeModel) modeModel).setSelected(false);
                }
            }
            mScheduledModeAdapter.notifyDataSetChanged();
        }
    }

    public void setData(List<BindModel> data) {
        mScheduledModeData.addAll(data);
        mScheduledModeAdapter.setNewData(mScheduledModeData);
    }

    public List<BindModel> getScheduleData() {
        return mScheduledModeData;
    }

    public void setEmergencyChange(int emergency) {
        if (mScheduledModeAdapter == null) return;
        for (BindModel bindModel : mScheduledModeAdapter.getData()) {
            if (bindModel instanceof AIScheduleModeModel) {
                AIScheduleModeModel scheduleModeModel = (AIScheduleModeModel) bindModel;
                if (scheduleModeModel.isCustom()) {
                    int mode = scheduleModeModel.getMode();
                    int sectionType = scheduleModeModel.getSectionType();
                    if (mode == 1) {
                        if (sectionType == 1) {
                            scheduleModeModel.setCustomPercentage(emergency);
                        }
                    } else if (mode == -1) {
                        if (sectionType == 2) {
                            scheduleModeModel.setCustomPercentage(-emergency);
                        }
                    }
                    scheduleModeModel.setCustomEmergency(emergency);
                }
            }
        }
        mScheduledModeAdapter.notifyDataSetChanged();
    }


    public void setSmartChange(int smart) {
        if (mScheduledModeAdapter == null) return;
        for (BindModel bindModel : mScheduledModeAdapter.getData()) {
            if (bindModel instanceof AIScheduleModeModel) {
                AIScheduleModeModel scheduleModeModel = (AIScheduleModeModel) bindModel;
                if (scheduleModeModel.isCustom()) {
                    int mode = scheduleModeModel.getMode();
                    int sectionType = scheduleModeModel.getSectionType();
                    if (mode == 1) {
                        if (sectionType == 2) {
                            scheduleModeModel.setCustomPercentage(smart);
                        }
                    } else if (mode == -1) {
                        if (sectionType == 1) {
                            scheduleModeModel.setCustomPercentage(-smart);
                        }
                    }
                    scheduleModeModel.setCustomSmart(smart);
                }
            }
        }
        mScheduledModeAdapter.notifyDataSetChanged();
    }

    public void initializeData() {
        if (mScheduledModeAdapter == null) return;
        for (BindModel bindModel : mScheduledModeAdapter.getData()) {
            if (bindModel instanceof AIScheduleModeModel) {
                AIScheduleModeModel scheduleModeModel = (AIScheduleModeModel) bindModel;
                if (scheduleModeModel.isCustom()) {
                    scheduleModeModel.setCustom(false);
                    scheduleModeModel.setCustomPercentage(-128);
                }
            }
        }
        mScheduledModeAdapter.notifyDataSetChanged();
    }

    public void setEnabledSize(int size) {
        enabledSize = size;
        if (mTouchListener != null) {
            mTouchListener.setCanTouchEndPosition(size - 1);
        }

        if (mAIDividerDecoration != null) {
            mAIDividerDecoration.setEnabledSize(size);
        }
        if (mAIBounderDecoration != null) {
            mAIBounderDecoration.setEnabledSize(size);
        }

        if (mAISelectedDecoration != null) {
            mAISelectedDecoration.setEnabledSize(size);
        }

        if (mAIExpressDecoration != null) {
            mAIExpressDecoration.setEnabledSize(size);
        }
    }

    public boolean hasAIData() {
        if (CollectionUtil.isListNotEmpty(mScheduledModeData)) {
            for (int i = 0; i < enabledSize; i++) {
                AIScheduleModeModel scheduleModeModel = (AIScheduleModeModel) mScheduledModeData.get(i);
                if (!scheduleModeModel.isCustom()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isAnimating() {
        return isAnimating;
    }

    public void setAnimating(boolean animating) {
        isAnimating = animating;
    }

    public BindMultiAdapter<BindModel> getScheduledModeAdapter() {
        return mScheduledModeAdapter;
    }

    public void setAIScheduledCardView(AIScheduledCardView aIScheduledCardView) {
        this.mAIScheduledCardView = aIScheduledCardView;
    }

    // 监听事件
    private OnSelectedListener mSelectedListener;

    public void setSelectedListener(OnSelectedListener selectedListener) {
        this.mSelectedListener = selectedListener;
    }

    public interface OnSelectedListener {
        void onSelected(List<AIScheduleModeModel> selectedData);
    }

    private OnScrollPositionListener mScrollPositionListener;

    public void setScrollPositionListener(OnScrollPositionListener scrollPositionListener) {
        this.mScrollPositionListener = scrollPositionListener;
    }

    public interface OnScrollPositionListener {
        void onScrollPosition(int firstPosition, int endPosition, boolean isUp);
    }

    private ExpandViewDatListener expandViewDataListener;

    public void setExpandViewDataListener(ExpandViewDatListener expandViewDataListener) {
        this.expandViewDataListener = expandViewDataListener;
    }

    public interface ExpandViewDatListener {

        void onPrepare(int lastItemHour, AIScheduleModeModel scheduleModeModel);

        void onStart(View expandView, boolean isExpand, int lastItemHour, AIScheduleModeModel scheduleModeModel);

        void onLayoutParamsChange(View expandView, int[] viewLocation, int width, int height, int expandHeight);

        void onEnd(boolean isExpand);
    }

    private ScrollListener mScrollListener;

    public void setScrollListener(ScrollListener scrollListener) {
        mScrollListener = scrollListener;
    }

    public interface ScrollListener {
        void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy);
    }
}
