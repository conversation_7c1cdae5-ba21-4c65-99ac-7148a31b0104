package com.dinsafer.module.powerstation.widget;


import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;

import android.util.AttributeSet;
import android.view.MotionEvent;

public class ElcScrollView extends NestedScrollView {

    private float mXDistance;
    private float mYDistance;
    private float mXLast;
    private float mYLast;

    public ElcScrollView(@NonNull Context context) {
        super(context);
    }

    public ElcScrollView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public ElcScrollView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mXDistance = 0f;
                mYDistance = 0f;
                mXLast = ev.getX();
                mYLast = ev.getY();
                break;

            case MotionEvent.ACTION_MOVE:
                mXDistance += Math.abs(ev.getX() - mXLast);
                mYDistance += Math.abs(ev.getY() - mYLast);
                mXLast = ev.getX();
                mYLast = ev.getY();
                if (mXDistance>mYDistance) {
                    return false;
                }
                break;
        }
        return super.onInterceptTouchEvent(ev);
    }
}
