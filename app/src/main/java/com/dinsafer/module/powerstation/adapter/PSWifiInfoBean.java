package com.dinsafer.module.powerstation.adapter;

import androidx.annotation.NonNull;

import com.dinsafer.module_bmt.bean.BmtBleWifiInfo;

import java.util.Objects;

/**
 * Wifi信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/3/7 17:39
 */
public class PSWifiInfoBean {
    private final String ssid;
    private final int rssi;
    private final boolean auth;


    public PSWifiInfoBean(@NonNull BmtBleWifiInfo src) {
        this(src.getSsid(), src.getRssi(), src.isAuth());
    }

    public PSWifiInfoBean(String ssid, int rssi, boolean auth) {
        this.ssid = ssid;
        this.rssi = rssi;
        this.auth = auth;
    }

    public String getSsid() {
        return ssid;
    }

    public int getRssi() {
        return rssi;
    }

    public boolean isAuth() {
        return auth;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PSWifiInfoBean bean = (PSWifiInfoBean) o;
        return Objects.equals(ssid, bean.ssid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ssid);
    }

    @Override
    public String toString() {
        return "PSWifiInfoBean{" +
                "ssid='" + ssid + '\'' +
                ", rssi=" + rssi +
                ", auth=" + auth +
                '}';
    }
}
