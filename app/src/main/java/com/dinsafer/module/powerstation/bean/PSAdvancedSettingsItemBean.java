package com.dinsafer.module.powerstation.bean;

import androidx.annotation.DrawableRes;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 17:05
 * @description :
 */
public class PSAdvancedSettingsItemBean {

    private String key;
    private String value;
    @DrawableRes
    private int logo;
    private boolean showDot = false;
    private boolean visible = true;
    private boolean isCopied;
    private boolean online = true;


    public PSAdvancedSettingsItemBean(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public PSAdvancedSettingsItemBean(String key, String value, boolean isCopied) {
        this.key = key;
        this.value = value;
        this.isCopied = isCopied;
    }

    public PSAdvancedSettingsItemBean(String key, int logo) {
        this.key = key;
        this.logo = logo;
    }

    public PSAdvancedSettingsItemBean(String key, String value, int logo) {
        this.key = key;
        this.value = value;
        this.logo = logo;
    }

    public String getKey() {
        return key == null ? "" : key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value == null ? "" : value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getLogo() {
        return logo;
    }

    public void setLogo(int logo) {
        this.logo = logo;
    }

    public boolean isShowDot() {
        return showDot;
    }

    public void setShowDot(boolean showDot) {
        this.showDot = showDot;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public boolean isVisible() {
        return visible;
    }

    public boolean isCopied() {
        return isCopied;
    }

    public void setCopied(boolean copied) {
        isCopied = copied;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }

    public boolean isOnline() {
        return online;
    }

}
