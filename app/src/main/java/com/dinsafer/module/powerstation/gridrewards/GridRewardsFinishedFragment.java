package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.HomeManager;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentGridRewardsFinishBinding;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.ui.rv.BindMultiAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/25
 * @author: create by Sydnee
 */
public class GridRewardsFinishedFragment extends MyBaseFragment<FragmentGridRewardsFinishBinding> {
    private BindMultiAdapter<TitleContentModel> mAdapter;

    public static GridRewardsFinishedFragment newInstance() {
        return new GridRewardsFinishedFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_grid_rewards_finish;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarBack.setVisibility(View.INVISIBLE);
        mBinding.commonBar.commonBarBack.setEnabled(false);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.prime_service_grid_reward));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.tvTitle.setLocalText(getString(R.string.grid_done_title));
        mBinding.btnDone.setLocalText(getString(R.string.Done));
        mBinding.btnDone.setOnClickListener(v -> {
            getFamilyBalanceContractInfo();
        });
        initRv();
    }

    private void initRv() {
        mAdapter = new BindMultiAdapter<>();
        List<TitleContentModel> modelList = new ArrayList<>();
        TitleContentModel model1 = new TitleContentModel(getString(R.string.grid_done_contract_title1), getString(R.string.grid_done_contract_content1), "");
        model1.setLineSpace(getResources().getDimensionPixelSize(R.dimen.contract_content_lineSpace));
        TitleContentModel model2 = new TitleContentModel(getString(R.string.grid_done_contract_title2), getString(R.string.grid_done_contract_content2), "");
        model2.setLineSpace(getResources().getDimensionPixelSize(R.dimen.contract_content_lineSpace));
        modelList.add(model1);
        modelList.add(model2);
        mAdapter.setNewData(modelList);
        mBinding.rcvContent.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rcvContent.setAdapter(mAdapter);
        mBinding.rcvContent.setNestedScrollingEnabled(false);
    }

    private void getFamilyBalanceContractInfo() {
        showTimeOutLoadinFramgment();
        DinHome.getInstance().getFamilyBalanceContractInfo(HomeManager.getInstance().getCurrentHome().getHomeID(),
                new IDefaultCallBack2<>() {
                    @Override
                    public void onSuccess(FamilyBalanceContractInfoResponse.ResultBean resultBean) {
                        closeLoadingFragment();
                        getDelegateActivity().removeAllCommonFragment();
                        if (null != resultBean && resultBean.isSigning()) {
                            getDelegateActivity().addCommonFragment(GridRewardsNotFirstFragment
                                    .newInstance(MainPanelHelper.getInstance().getBmtRegionCountryList(), resultBean));
                        } else {
                            getDelegateActivity().addCommonFragment(GridRewardsFragment
                                    .newInstance(MainPanelHelper.getInstance().getBmtRegionCountryList(), resultBean));
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

}
