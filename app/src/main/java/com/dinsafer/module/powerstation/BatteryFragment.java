package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBatteryBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtShowUpdateDialogEvent;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module.powerstation.impacts.ReserveModeFragment;
import com.dinsafer.module.powerstation.settings.BatteryOverviewFragment;
import com.dinsafer.module.powerstation.widget.BatteryChargeView;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.plugin.widget.util.DensityUtil;
import com.dinsafer.util.MapUtils;

import com.dinsafer.util.SettingInfoHelper;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/23 11:55
 * @description :
 */
public class BatteryFragment extends MyBaseFragment<FragmentBatteryBinding> {

    private String mDeviceId;
    private String mSubCategory;
    private Device mPSDevice;
    private Map<String, Object> params = new HashMap<>();
    private int batteryPercent;

    List<Segment> segments = new ArrayList<>();

    private float mSmartReserve = 0.9f;
    private float mEmergencyReserve = 0.5f;
    private int mChipsStatus = 0;

    private boolean isEmergencyOn;
    private boolean isManualAIMode;
    private int mReserveMode;

    private final BehaviorSubject<Map<String, Object>> mBatteryStatusSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mBatteryInfoSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mReserveModeSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mPTReserveSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mScheduledReserveSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mEmergencySubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mCustomScheduleSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mChargeModeSubject = BehaviorSubject.create();
    private Subscriber<Map<String, Object>> mBatteryInfoSubscriber;
    private Subscriber<Map<String, Object>> mReserveModePTSubscriber;
    private Subscriber<Map<String, Object>> mReserveModeScheduledSubscriber;
    private Subscriber<Map<String, Object>> mReserveModeAISubscriber;
    private final Map<String, Object> mChargeModeMap = new HashMap<>();

    public static BatteryFragment newInstance(String deviceId, String subCategory) {
        BatteryFragment fragment = new BatteryFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_battery;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        if (SettingInfoHelper.getInstance().isAdmin()) {
            mBinding.ivUpdating.setOnClickListener(v -> {
                if (TextUtils.isEmpty(mDeviceId) || mChipsStatus == 0) {
                    return;
                }

                EventBus.getDefault().post(new BmtShowUpdateDialogEvent(mDeviceId, mSubCategory, mChipsStatus));
            });
            mBinding.ivBalanceArrow.setVisibility(View.VISIBLE);
        }

        initParams();
        setSegment();
        mBinding.ssb.postDelayed(new Runnable() {
            @Override
            public void run() {
                mBinding.ssb.post(() -> mBinding.ssb.setRemainSpace(mBinding.clParent.getWidth() / 2f - mBinding.powerBatteryView.getWidth() / 2f - DensityUtil.dp2px(getContext(), 9.5f)));
                mBinding.ssb.setProgress(0f, true);
            }
        }, 200);

        updateByDeviceOnlineStatus();
        initBatteryInfoSubscriber();
        initReserveModePTSubscriber();
        initReserveModeScheduledSubscriber();
        initReserveModeAISubscriber();
    }

    @Override
    public void initListener() {
        super.initListener();
        if (SettingInfoHelper.getInstance().isAdmin()) {
            mBinding.clPowerBatteryView.setOnClickListener(view -> {
                if (mPSDevice != null && BmtUtil.isDeviceConnected(mPSDevice)) {
                    getDelegateActivity().addCommonFragment(BatteryOverviewFragment.newInstance(mDeviceId, mSubCategory));
                }
            });

            mBinding.llBalance.setOnClickListener(view -> {
                if (mPSDevice != null && BmtUtil.isDeviceConnected(mPSDevice)) {
                    getDelegateActivity().addCommonFragment(BatteryOverviewFragment.newInstance(mDeviceId, mSubCategory));
                }
            });

            mBinding.llMode.setOnClickListener(view -> {
                if (mPSDevice != null && BmtUtil.isDeviceConnected(mPSDevice)) {
                    getDelegateActivity().addCommonFragment(ReserveModeFragment.newInstance(mDeviceId, mSubCategory));
                }
            });
        }
    }

    private void initBatteryInfoSubscriber() {
        mBatteryInfoSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int percent = DeviceHelper.getInt(resultMap, PSKeyConstant.SOC, 0);
                int totalVolt = DeviceHelper.getInt(resultMap, PSKeyConstant.TOTAL_VOLT, 0);
                int curPower = DeviceHelper.getInt(resultMap, PSKeyConstant.CUR_POWER, 0);
                int remainTime = DeviceHelper.getInt(resultMap, PSKeyConstant.REMAIN_TIME, 0);
                int chargeTime = DeviceHelper.getInt(resultMap, PSKeyConstant.CHARGE_TIME, 0);
                int batteryWat = DeviceHelper.getInt(resultMap, BmtDataKey.BATTERY_WAT, 0);
                int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                ;
                int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                ;
                mEmergencyReserve = emergencyReserve / 100f;
                mSmartReserve = smartReserve / 100f;
                if (percent > 100 || percent < 0) {
                    percent = 0;
                }

                batteryPercent = percent;
                double balance = totalVolt / 1000.0 * curPower / 1000.0;
                balance = new BigDecimal(balance).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                String balanceStr = String.valueOf(balance);
                if (balanceStr.endsWith(".0")) {
                    balanceStr = balanceStr.replace(".0", "");
                }
                int chargeStatus = BatteryChargeView.NORMAL;
                if (batteryWat > 0) {
                    chargeStatus = BatteryChargeView.DISCHARGING;
                }
                if (batteryWat < 0) {
                    chargeStatus = BatteryChargeView.CHARGING;
                }
                String timeKey = chargeStatus == BatteryChargeView.CHARGING ?
                        getString(R.string.power_battery_need_to_charge) : getString(R.string.power_battery_lasts_for);
                String hmStr = TimeUtil.minute2HourMinute(chargeStatus == BatteryChargeView.CHARGING ? chargeTime : remainTime);
                String[] hmArr = hmStr.split(":");

                mBinding.powerBatteryView.setSuccess(true);
                mBinding.powerBatteryView.setOnline(true);
                mBinding.powerBatteryView.setChargeStatus(chargeStatus, false);
                mBinding.powerBatteryView.setEmergencyReserve(mEmergencyReserve);
                mBinding.powerBatteryView.setSmartReserve(mSmartReserve);
                mBinding.powerBatteryView.setProgress(percent / 100f, true);
                mBinding.tvBalanceVal.setLocalText(balanceStr);
                mBinding.tvLastForKey.setLocalText(timeKey);
                mBinding.tvLastForHour.setLocalText(hmArr[0]);
                mBinding.tvLastForMinute.setLocalText(hmArr[1]);

                segments.get(0).setMinValue(smartReserve / 100f);
                segments.get(1).setMinValue(emergencyReserve / 100f);
                segments.get(1).setMaxValue(smartReserve / 100f);
                segments.get(2).setMaxValue(emergencyReserve / 100f);
                mBinding.ssb.setProgress(percent / 100f, false);
                mBinding.ssb.invalidate();
                mBinding.clParent.setAlpha(1f);
            }
        };
        Observable.combineLatest(mBatteryStatusSubject, mBatteryInfoSubject, mChargeModeSubject, (batteryStatusMap, batteryInfoMap, chargeModeMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(batteryStatusMap);
                    allMap.putAll(batteryInfoMap);
                    allMap.putAll(chargeModeMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mBatteryInfoSubscriber);
    }

    private void initReserveModePTSubscriber() {
        mReserveModePTSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                mReserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);
                isManualAIMode = false;
                if (!isEmergencyOn) {
                    setChargeMode(mReserveMode - 1);
                }
                if (mReserveMode == 1) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, mReserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
            }
        };
        Observable.combineLatest(mReserveModeSubject, mPTReserveSubject, (reserveModeMap, ptReserveMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(reserveModeMap);
                    allMap.putAll(ptReserveMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModePTSubscriber);
    }

    private void initReserveModeScheduledSubscriber() {
        mReserveModeScheduledSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                mReserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);
                isManualAIMode = false;
                if (!isEmergencyOn) {
                    setChargeMode(mReserveMode - 1);
                }
                if (mReserveMode == 2) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, mReserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
            }
        };

        Observable.combineLatest(mReserveModeSubject, mScheduledReserveSubject, (reserveModeMap, scheduledReserveMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(reserveModeMap);
                    allMap.putAll(scheduledReserveMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModeScheduledSubscriber);
    }

    private void initReserveModeAISubscriber() {
        mReserveModeAISubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                mReserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);

                isManualAIMode = false;
                if (mReserveMode == 3) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    Integer[] weekdays = (Integer[]) MapUtils.get(resultMap, BmtDataKey.WEEKDAYS, null);
                    if (weekdays != null) {
                        boolean hasAIData = false;
                        for (int weekday : weekdays) {
                            if (weekday == -128) {
                                hasAIData = true;
                                emergencyReserve = DeviceHelper.getInt(mPSDevice, BmtDataKey.EMERGENCY_RESERVE, 0);
                                smartReserve = DeviceHelper.getInt(mPSDevice, BmtDataKey.SMART_RESERVE, 0);
                                break;
                            }
                        }
                        isManualAIMode = !hasAIData;
                    }
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, mReserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
                if (!isEmergencyOn) {
                    if (mReserveMode == 3 && isManualAIMode) {
                        setAIManual();
                    } else {
                        setChargeMode(mReserveMode - 1);
                    }
                }
            }
        };

        Observable.combineLatest(mReserveModeSubject, mCustomScheduleSubject, (reserveModeMap, scheduledReserveMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(reserveModeMap);
                    allMap.putAll(scheduledReserveMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModeAISubscriber);
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubCategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubCategory);
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        if (mBatteryInfoSubscriber != null) {
            mBatteryInfoSubscriber.unsubscribe();
        }
        if (mReserveModePTSubscriber != null) {
            mReserveModePTSubscriber.unsubscribe();
        }
        if (mReserveModeScheduledSubscriber != null) {
            mReserveModeScheduledSubscriber.unsubscribe();
        }
        if (mReserveModeAISubscriber != null) {
            mReserveModeAISubscriber.unsubscribe();
        }
        super.onDestroyView();
    }


    private void setSegment() {
        segments.clear();
        segments.add(new Segment(0.9f, 1.0f, getString(R.string.power_battery_bar_status_text_1), getColor(R.color.power_station_battery_color_1)));
        segments.add(new Segment(0.6f, 0.9f, getString(R.string.power_battery_bar_status_text_2), getColor(R.color.power_station_battery_color_2)));
        segments.add(new Segment(0.12f, 0.6f, getString(R.string.power_battery_bar_status_text_3), getColor(R.color.power_station_battery_color_3)));
        segments.add(new Segment(0.02f, 0.12f, getString(R.string.power_battery_bar_status_text_4), getColor(R.color.power_station_battery_color_4)));
        segments.add(new Segment(0.0f, 0.02f, getString(R.string.power_battery_bar_status_text_5), getColor(R.color.power_station_battery_color_5)));
        mBinding.ssb.setSegments(segments, 4);
    }

    /**
     * 在更新有蒙版和更新图标
     */
    public void setUpdating(boolean isUpdating) {
        mBinding.viewDisable.setVisibility(isUpdating ? View.VISIBLE : View.GONE);
        mBinding.ivUpdating.setVisibility(isUpdating ? View.VISIBLE : View.GONE);
    }

    private int getColor(int color) {
        return getResources().getColor(color);
    }

    private void setChargeMode(int mode) {
        String[] modeList = getContext().getResources().getStringArray(R.array.ps_charge_mode);
        int[] icons = {R.drawable.icon_power_price_tracking_mode, R.drawable.icon_power_scheduled_mode,
                R.drawable.icon_power_mode_ai, R.drawable.icon_power_mode_emergency_charge,
                R.drawable.icon_power_mode_emergency_charge};
        mBinding.ivModeLogo.setImageResource(icons[mode]);
        mBinding.tvMode.setLocalText(modeList[mode]);
        mBinding.llMode.setVisibility(View.VISIBLE);
    }

    private void setAIManual() {
        mBinding.ivModeLogo.setImageResource(R.drawable.icon_power_mode_ai);
        mBinding.tvMode.setLocalText(getString(R.string.AI_Manual_Mode));
        mBinding.llMode.setVisibility(View.VISIBLE);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGraphicUpdateEvent event) {
        if (!event.isGraphicBattery()) {
            return;
        }
        final String deviceId = event.getDeviceId();
        final String deviceSub = event.getSubCategory();
        final String cmd = event.getCmd();
        final Map map = event.getData();
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && deviceSub.equals(mSubCategory)
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (status == StatusConstant.STATUS_SUCCESS) {
                if (result != null && result.size() > 0) {
                    switch (cmd) {

                        case BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO:
                            mBatteryStatusSubject.onNext(result);
                            break;

                        case DsCamCmd.GET_BATTERY_ALLINFO:
                            mBatteryInfoSubject.onNext(result);
                            break;
                        case DsCamCmd.GET_EMERGENCY_CHARGE:
                            isEmergencyOn = DeviceHelper.getBoolean(result, PSKeyConstant.ON, false);
                            if (isEmergencyOn) {
                                long currentMillis = System.currentTimeMillis();
                                long startTime = DeviceHelper.getLong(result, PSKeyConstant.START_TIME, 0) * 1000;
                                long endTime = DeviceHelper.getLong(result, PSKeyConstant.END_TIME, 0) * 1000;
                                if (currentMillis < startTime) {
                                    setChargeMode(4);
                                } else if (currentMillis <= endTime) {
                                    setChargeMode(3);
                                }
                            }
                            break;

                        case BmtCmd.GET_CHIPS_STATUS:
                            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
                            if (-1 != chipsStatus) {
                                mChipsStatus = chipsStatus;
                                final boolean showMarker = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//                                final boolean waitForUpdate = BmtUtil.isWaitForUpdate(chipsStatus);
//                                final boolean ignore = BmtManager.getInstance().isIgnoredUpgrade(mDeviceId);
//                                final boolean needUpgrade = waitForUpdate ? !ignore : showMarker;
                                changeViewStateByUpdateState(showMarker);
                            }
                            break;

                        case BmtCmd.GET_CURRENT_RESERVE_MODE:  // 获取模式
                            mReserveModeSubject.onNext(result);
                            break;

                        case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                            mPTReserveSubject.onNext(result);
                            break;
                        case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                            mScheduledReserveSubject.onNext(result);
                            break;

                        case BmtCmd.GET_CUSTOM_SCHEDULEMODE:
                            mCustomScheduleSubject.onNext(result);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    private void changeViewStateByUpdateState(final boolean showUpdate) {
        if (showUpdate) {
            mBinding.viewDisable.setVisibility(View.VISIBLE);
            mBinding.ivUpdating.setVisibility(View.VISIBLE);
        } else {
            mBinding.viewDisable.setVisibility(View.GONE);
            mBinding.ivUpdating.setVisibility(View.GONE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BatteryStatusEvent event) {
//        chargeStatus = event.getStatus();
//        DDLog.d(TAG, "状态====" + chargeStatus);
//        mBinding.powerBatteryView.setChargeStatus(chargeStatus);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ChargeModeEvent event) {
        int mode = event.getMode();
        if (mode == 4 || !isEmergencyOn) {
            if (event.isManual()) {
                setAIManual();
            } else {
                setChargeMode(mode - 1);
            }
        }
        if (event.isOnlyMode()) {
            return;
        }
        int smartReserve = event.getSmartReserve();
        int emergencyReserve = event.getEmergencyReserve();
        segments.get(0).setMinValue(smartReserve / 100f);
        segments.get(1).setMinValue(emergencyReserve / 100f);
        segments.get(1).setMaxValue(smartReserve / 100f);
        segments.get(2).setMaxValue(emergencyReserve / 100f);
        mBinding.ssb.invalidate();

        float er = emergencyReserve / 100f;
        float sr = smartReserve / 100f;
        mEmergencyReserve = er;
        mSmartReserve = sr;
        mBinding.powerBatteryView.setEmergencyReserve(mEmergencyReserve);
        mBinding.powerBatteryView.setSmartReserve(mSmartReserve);
        mBinding.powerBatteryView.resetColor();
        mBinding.powerBatteryView.invalidate();
    }

    /**
     * BMT状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        String deviceId = event.getDeviceID();
        if (StringUtil.isNotEmpty(deviceId) && StringUtil.isNotEmpty(mDeviceId) && deviceId.equals(mDeviceId) && event.getSubcategory().equals(mSubCategory)) {
            updateByDeviceOnlineStatus();
        }
    }

    private void updateByDeviceOnlineStatus() {
        final boolean online = BmtUtil.isDeviceConnected(mPSDevice);
        mBinding.clParent.setAlpha(online ? 1f : 0.5f);
        if (!online) {
            mBinding.tvBalanceVal.setText(getString(R.string.power_station_cdv_offline_val));
            mBinding.tvLastForHour.setText(getString(R.string.power_station_cdv_offline_val));
            mBinding.tvLastForMinute.setText(getString(R.string.power_station_cdv_offline_val));
            changeViewStateByUpdateState(false);
        }
        String modeTxt = mBinding.tvMode.getText().toString();
        mBinding.llMode.setVisibility((online && !TextUtils.isEmpty(modeTxt)) ? View.VISIBLE : View.INVISIBLE);
        mBinding.powerBatteryView.setOnline(online);
    }
}
