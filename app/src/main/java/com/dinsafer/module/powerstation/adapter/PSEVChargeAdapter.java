package com.dinsafer.module.powerstation.adapter;


import android.animation.Animator;
import android.animation.ValueAnimator;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.PSEVChargeBean;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.LocalTextView;


public class PSEVChargeAdapter extends BaseQuickAdapter<PSEVChargeBean, BaseViewHolder> {

    public PSEVChargeAdapter() {
        super(R.layout.item_ps_ev_charge);
    }

    @Override
    protected void convert(BaseViewHolder helper, PSEVChargeBean item) {
        helper.addOnClickListener(R.id.ll_operate);
        helper.addOnClickListener(R.id.tv_go_settings);
        helper.addOnClickListener(R.id.ll_status);
        boolean isSelected = item.isSelected();
        ConstraintLayout llParent = helper.getView(R.id.cl_parent);
        ConstraintLayout clContent = helper.getView(R.id.cl_content);
        int status = item.getStatus();
        float alpha = item.getAlpha();
//        llParent.setAlpha(alpha);
        View viewSelectedBg = helper.getView(R.id.view_selected_bg);
//        viewSelectedBg.setAlpha(alpha);
        viewSelectedBg.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        LocalTextView tvTitle = helper.getView(R.id.tv_title);
        tvTitle.setLocalText(item.getTitle());
        LocalTextView tvSubTitle = helper.getView(R.id.tv_subtitle);
        tvSubTitle.setLocalText(item.getSubTitle());


        LocalTextView tvGoSetting = helper.getView(R.id.tv_go_settings);
        ConstraintLayout llStatus = helper.getView(R.id.ll_status);

//        llStatus.setVisibility(isSelected && status != 1 ? View.VISIBLE : View.GONE);
        llStatus.setBackgroundResource(status == 0 ? R.drawable.shape_brand_light_03_right_r16 :
                R.drawable.shape_brand_primary_right_r16);
        int type = item.getType();
        if (item.isNeedAnim()) {
            if (!item.isSameSelected()) {
                boolean isEdit = isSelected && status != 1;
                if (status == 1) {
                    tvGoSetting.setVisibility(isSelected && type == PSEVChargeBean.SCHEDULE_CHARGE ? View.VISIBLE : View.GONE);
                }
                int startMargin = isEdit ? 5 : 0;
                int endMargin = isEdit ? 0 : 5;
                ValueAnimator marginAnimator = ValueAnimator.ofInt(startMargin, endMargin);
                marginAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        int val = (Integer) animation.getAnimatedValue();
                        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) clContent.getLayoutParams();
                        int marginRight = status == 1 ? 0 : DensityUtil.dp2px(mContext, 60) - DensityUtil.dp2px(mContext, val * 12);
                        layoutParams.setMargins(layoutParams.leftMargin,
                                layoutParams.topMargin,
                                marginRight,
                                layoutParams.bottomMargin);
                        clContent.requestLayout();
                    }
                });
                marginAnimator.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animator) {
                        if (isEdit && type == PSEVChargeBean.SCHEDULE_CHARGE) {
                            tvGoSetting.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onAnimationEnd(Animator animator) {
                        if (!isEdit && status == -1) {
                            tvGoSetting.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onAnimationCancel(Animator animator) {

                    }

                    @Override
                    public void onAnimationRepeat(Animator animator) {

                    }
                });
                item.setSameSelected(true);
                marginAnimator.setDuration(200);
                marginAnimator.start();
            }
        } else {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) clContent.getLayoutParams();
            int marginRight = (status != 1 && isSelected) ? DensityUtil.dp2px(mContext, 60) : 0;
            layoutParams.setMargins(layoutParams.leftMargin,
                    layoutParams.topMargin,
                    marginRight,
                    layoutParams.bottomMargin);
        }
        tvGoSetting.setVisibility(type == PSEVChargeBean.SCHEDULE_CHARGE && isSelected ?
                View.VISIBLE : View.GONE);
        LocalTextView tvApplied = helper.getView(R.id.tv_applied);
        tvApplied.setVisibility(status == 1 ? View.VISIBLE : View.GONE);
        ImageView ivStatus = helper.getView(R.id.iv_status);
        ivStatus.setVisibility(status == 0 ? View.GONE : View.VISIBLE);
        LottieAnimationView lavLoading = helper.getView(R.id.lav_loading);
        lavLoading.setVisibility(status == 0 ? View.VISIBLE : View.GONE);
    }

    private void setAnimResource(ImageView imageView, boolean loading) {
        if (loading) {
            imageView.setImageTintList(null);
            imageView.setImageResource(R.drawable.icon_ps_accessories_device_loading);
            imageView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.rotation));
        } else {
            imageView.clearAnimation();
            imageView.setImageResource(R.drawable.radiobox_sel_white);
        }
    }

    public void setExpand(boolean isExpand) {
        for (int i = 0; i < getData().size(); i++) {
            PSEVChargeBean psevChargeBean = getData().get(i);
            if (psevChargeBean.getStatus() == 1) {
                psevChargeBean.setSelected(isExpand);
                psevChargeBean.setNeedAnim(false);
                notifyItemChanged(i);
                break;
            }
        }
    }
}
