package com.dinsafer.module.powerstation.electricity.factory;

import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

public class UsageChartData implements IChartData {
    @Override
    public List<List<Float>> createChartData(int size, int interval, CycleType cycleType, List<List<Float>> pendingData) {
        List<List<Float>> data = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            List<Float> sonData = new ArrayList<>();
            sonData.add(i * 1.0f);
            sonData.add(0f);
            sonData.add(0f);
            sonData.add(0f);
            sonData.add(0f);
            data.add(sonData);
        }
        if (CollectionUtil.isListNotEmpty(pendingData)) {
            if (cycleType == CycleType.LIFETIME) {
                for (int i = 0; i < pendingData.size(); i++) {
                    data.set(i, pendingData.get(i));
                }
            } else {
                for (List<Float> sonData : pendingData) {
                    int offset = Math.round(Math.abs(sonData.get(0))) / interval;
                    if (offset < data.size()) {
                        data.set(offset, sonData);
                    }
                }
            }
        }
        return data;
    }
}
