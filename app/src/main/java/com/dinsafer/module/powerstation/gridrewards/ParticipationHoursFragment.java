package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentParticipationHoursBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.ParticipationHourModel;
import com.dinsafer.module.powerstation.adapter.ParticipationHoursTipsModel;
import com.dinsafer.module.powerstation.adapter.RepeatDayModel;
import com.dinsafer.module.powerstation.dialog.TimeConflictAlertDialog;
import com.dinsafer.module.powerstation.widget.SpaceItemDecoration;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.ScreenUtils;
import com.google.android.material.bottomsheet.BottomSheetBehavior;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

public class ParticipationHoursFragment extends MyBaseFragment<FragmentParticipationHoursBinding> {

    private final List<Integer> mTempRepeats;
    private BindMultiAdapter<ParticipationHourModel> mAdapter;
    private BindMultiAdapter<RepeatDayModel> mRepeatDayAdapter;
    private List<ParticipationHourModel> mData;
    private List<String> mTimeData;
    private BottomSheetBehavior mBSBSchedule;
    private BottomSheetBehavior mBSBWeek;
    private ParticipationHour mSelParticipationHour;
    private TimeConflictAlertDialog mTimeConflictAlertDialog;
    private int from;
    private View mStatusBar;

    public static ParticipationHoursFragment newInstance(int from) {
        ParticipationHoursFragment fragment = new ParticipationHoursFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        fragment.setArguments(bundle);
        return fragment;
    }

    public ParticipationHoursFragment() {
        mTempRepeats = new ArrayList<>();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_participation_hours;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mStatusBar = new View(getContext());
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ScreenUtils.getStatusHeightByResource(getContext()));
        mStatusBar.setLayoutParams(layoutParams);
        ((ViewGroup) getActivity().getWindow().getDecorView()).addView(mStatusBar);
        Bundle bundle = getArguments();
        if (bundle != null) {
            from = bundle.getInt(PSKeyConstant.KEY_FROM);
        }
        mBinding.commonBar.commonBarBack.setVisibility(from == 0 ? View.VISIBLE : View.INVISIBLE);
        mBinding.commonBar.commonBarBack.setEnabled(from == 0);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(from == 1 ?
                R.string.prime_service_grid_reward : R.string.Participation_Hours));
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_add_brand_primary);
        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> {
            if (mData.size() < 10) {
                showScheduleBottomSheet(null);
            } else {
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail,
                        Local.s(getString(R.string.maximum_of_10_schedules_tips)));
            }
        });
        mBinding.tvNext.setOnClickListener(v -> getDelegateActivity().addCommonFragment(GridRewardsFinishedFragment.newInstance()));
        mBinding.tvNext.setVisibility(from == 1 ? View.VISIBLE : View.GONE);
        RelativeLayout.LayoutParams rightIconParams = (RelativeLayout.LayoutParams) mBinding.commonBar.commonBarRightIcon.getLayoutParams();
        rightIconParams.rightMargin = DensityUtil.dp2px(getContext(), 8);
        mBinding.commonBar.commonBarRightIcon.setLayoutParams(rightIconParams);
        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.tvSettingGuide.setOnClickListener(v -> {
            boolean isSGSelected = mBinding.tvSettingGuide.isSelected();
            mBinding.ivCover.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.tvGridBalancing.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.tvActual.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.tvPreferred.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.ivTips.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.tvTips.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.rvTips.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.ivArrow.animate().rotation(isSGSelected ? 90 : 270).start();
            mBinding.viewLine.setVisibility(isSGSelected ? View.VISIBLE : View.GONE);
            mBinding.tvSettingGuide.setSelected(!isSGSelected);
        });
        initTop();
        initRV();
        initScheduleBottomSheet();
        initRepeatDayBottomSheet();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        getData(true, true);
    }

    @Override
    public boolean onBackPressed() {
        if (mBSBWeek != null && mBSBWeek.getState() == BottomSheetBehavior.STATE_EXPANDED) {
            repeatBack();
            return true;
        } else if (mBSBSchedule != null && mBSBSchedule.getState() == BottomSheetBehavior.STATE_EXPANDED) {
            checkModified();
            return true;
        } else {
            if (from == 1) {
                return true;
            } else {
                return super.onBackPressed();
            }
        }
    }

    private void initTop() {
        BindMultiAdapter<ParticipationHoursTipsModel> tipsAdapter = new BindMultiAdapter<>();
        List<ParticipationHoursTipsModel> tips = new ArrayList<>();
        tips.add(new ParticipationHoursTipsModel(getContext(), "1. ", getString(R.string.participation_hours_note_1)));
        tips.add(new ParticipationHoursTipsModel(getContext(), "2. ", getString(R.string.participation_hours_note_2)));
        tipsAdapter.setNewData(tips);
        if (from == 0) {
            mBinding.clDesc.setVisibility(View.GONE);
            mBinding.clSettingGuide.setVisibility(View.VISIBLE);
            mBinding.rvTips.setLayoutManager(new LinearLayoutManager(getContext()));
            mBinding.rvTips.setAdapter(tipsAdapter);
        } else {
            mBinding.clSettingGuide.setVisibility(View.GONE);
            mBinding.clDesc.setVisibility(View.VISIBLE);
            mBinding.rvDescTips.setLayoutManager(new LinearLayoutManager(getContext()));
            mBinding.rvDescTips.setAdapter(tipsAdapter);
        }

    }

    private void initRV() {
        mAdapter = new BindMultiAdapter<>();
        mBinding.rvHours.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvHours.addItemDecoration(new SpaceItemDecoration(getContext(), DensityUtil.dp2px(getContext(), 15), 0));
        mData = new ArrayList<>();
        mAdapter.setNewData(mData);
        mAdapter.setOnBindItemChildClickListener((OnBindItemChildClickListener<ParticipationHourModel>) (view, position, model) -> {
            int viewId = view.getId();
            if (viewId == R.id.iv_edit_help) {
                mSelParticipationHour = model.getParticipationHour();
                showScheduleBottomSheet(model.getParticipationHour());
            } else if (viewId == R.id.iv_delete_help) {
                isTaskTimeInUpdatedRange(1, model.getParticipationHour().getId());
            }
        });
        mBinding.rvHours.setAdapter(mAdapter);
    }

    private void initScheduleBottomSheet() {
        mTimeData = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            String time = i < 10 ? ("0" + i) + ":00" : i + ":00";
            mTimeData.add(time);
        }
        mBinding.viewSchedule.wpStart.setData(mTimeData);
        mBinding.viewSchedule.wpEnd.setData(mTimeData);
        mBinding.viewSchedule.wpStart.setSelectedItemPosition(0);
        mBinding.viewSchedule.wpEnd.setSelectedItemPosition(23);

        mBinding.viewSchedule.switchAllDay.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                mBinding.viewSchedule.wpStart.setVisibility(isOn ? View.GONE : View.VISIBLE);
                mBinding.viewSchedule.wpEnd.setVisibility(isOn ? View.GONE : View.VISIBLE);
                mBinding.viewSchedule.viewLine.setVisibility(isOn ? View.GONE : View.VISIBLE);
                mBinding.viewSchedule.viewHighlight.setVisibility(isOn ? View.GONE : View.VISIBLE);
            }
        });
        View.OnClickListener dayListener = v -> {
            for (int i = 0; i < mRepeatDayAdapter.getData().size(); i++) {
                mRepeatDayAdapter.getData().get(i).setSelected(mTempRepeats.contains(i));
            }
            mRepeatDayAdapter.notifyDataSetChanged();
            mBSBWeek.setState(BottomSheetBehavior.STATE_EXPANDED);
        };
        mBinding.viewSchedule.tvRepeatKey.setOnClickListener(dayListener);
        mBinding.viewSchedule.tvRepeatValue.setOnClickListener(dayListener);
        mBinding.viewSchedule.ivArrow.setOnClickListener(dayListener);
        mBinding.viewSchedule.tvCancel.setOnClickListener(v -> {
            checkModified();
        });

        mBinding.viewSchedule.tvConfirm.setOnClickListener(v -> {
            int startPosition = mBinding.viewSchedule.wpStart.getCurrentItemPosition();
            int endPosition = mBinding.viewSchedule.wpEnd.getCurrentItemPosition();
            String startStr = mTimeData.get(startPosition);
            String endStr = mTimeData.get(endPosition);
            String[] startArr = startStr.split(":");
            String[] endArr = endStr.split(":");
            int startVal = Integer.parseInt(startArr[0]);
            int endVal = Integer.parseInt(endArr[0]);
            if (endVal - startVal < 1) {
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.participation_time_1_hour_tips)));
                return;
            }

            if (mSelParticipationHour == null) {
                addBalanceContractParticipationHours();
            } else {
                isTaskTimeInUpdatedRange(0, mSelParticipationHour.getId());
            }
        });
        mBSBSchedule = BottomSheetBehavior.from(mBinding.viewSchedule.clParent);
        mBSBSchedule.setDraggable(false);
        mBSBSchedule.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {

            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                if (slideOffset == 0f) {
                    mBinding.viewMaskHead.setVisibility(View.GONE);
                    mBinding.viewMaskContent.setVisibility(View.GONE);
                    mStatusBar.setBackgroundResource(R.color.transparent);
                }
            }
        });
    }

    private void checkModified() {
        String start = mTimeData.get(mBinding.viewSchedule.wpStart.getCurrentItemPosition());
        String end = mTimeData.get(mBinding.viewSchedule.wpEnd.getCurrentItemPosition());
        boolean isOn = mBinding.viewSchedule.switchAllDay.isOn();
        if (mSelParticipationHour == null) {
            Calendar calendar = Calendar.getInstance();
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            if (!start.equals("00:00") || !end.equals("23:00") || !isOn || !(mTempRepeats.size() == 1 && mTempRepeats.get(0) + 1 == dayOfWeek)) {
                showDiscardDialog();
            } else {
                mBSBSchedule.setState(BottomSheetBehavior.STATE_COLLAPSED);
            }
        } else {
            if (isOn == mSelParticipationHour.getAll_day()
                    && start.equals(mSelParticipationHour.getStart())
                    && end.equals(mSelParticipationHour.getEnd())
                    && mSelParticipationHour.getRepeat().containsAll(mTempRepeats)
                    && mTempRepeats.containsAll(mSelParticipationHour.getRepeat())) {
                mBSBSchedule.setState(BottomSheetBehavior.STATE_COLLAPSED);
            } else {
                showDiscardDialog();
            }
        }
    }

    private void repeatBack() {
        mTempRepeats.clear();
        for (int i = 0; i < mRepeatDayAdapter.getData().size(); i++) {
            if (mRepeatDayAdapter.getData().get(i).isSelected()) {
                mTempRepeats.add(i);
            }
        }
        setRepeatStr();
        mBSBWeek.setState(BottomSheetBehavior.STATE_COLLAPSED);
    }

    private void initRepeatDayBottomSheet() {
        mBinding.viewRepeatDay.tvBack.setOnClickListener(v -> {
            repeatBack();
        });
        mBinding.viewRepeatDay.rvDay.setLayoutManager(new LinearLayoutManager(getContext()));
        mRepeatDayAdapter = new BindMultiAdapter<>();
        List<RepeatDayModel> data = new ArrayList<>();
        data.add(new RepeatDayModel(getContext(), getString(R.string.Every_Sunday), false));
        data.add(new RepeatDayModel(getContext(), getString(R.string.Every_Monday), false));
        data.add(new RepeatDayModel(getContext(), getString(R.string.Every_Tuesday), false));
        data.add(new RepeatDayModel(getContext(), getString(R.string.Every_Wednesday), false));
        data.add(new RepeatDayModel(getContext(), getString(R.string.Every_Thursday), false));
        data.add(new RepeatDayModel(getContext(), getString(R.string.Every_Friday), false));
        data.add(new RepeatDayModel(getContext(), getString(R.string.Every_Saturday), false));
        mRepeatDayAdapter.setNewData(data);
        mRepeatDayAdapter.setOnBindItemClickListener((OnBindItemClickListener<RepeatDayModel>) (v, position, model) -> {
            boolean noSelected = true;
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i).isSelected() && i != position) {
                    noSelected = false;
                    break;
                }
            }
            if (noSelected) {
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail,
                        Local.s(getString(R.string.Please_select_at_least_one_date)));
            } else {
                model.setSelected(!model.isSelected());
                mRepeatDayAdapter.notifyItemChanged(position);
            }
        });
        mBinding.viewRepeatDay.rvDay.setAdapter(mRepeatDayAdapter);
        mBSBWeek = BottomSheetBehavior.from(mBinding.viewRepeatDay.clParent);
        mBSBWeek.setDraggable(false);
    }

    private void showScheduleBottomSheet(ParticipationHour participationHour) {
        mSelParticipationHour = participationHour;
        mTempRepeats.clear();
        if (participationHour == null) {
            mBinding.viewSchedule.wpStart.setSelectedItemPosition(0);
            mBinding.viewSchedule.wpEnd.setSelectedItemPosition(23);
            mBinding.viewSchedule.wpStart.setVisibility(View.GONE);
            mBinding.viewSchedule.wpEnd.setVisibility(View.GONE);
            mBinding.viewSchedule.viewLine.setVisibility(View.GONE);
            mBinding.viewSchedule.viewHighlight.setVisibility(View.GONE);
            mBinding.viewSchedule.switchAllDay.setOn(true);
            Calendar calendar = Calendar.getInstance();
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            mTempRepeats.add(dayOfWeek - 1);
        } else {
            for (int i = 0; i < mTimeData.size(); i++) {
                String time = mTimeData.get(i);
                if (time.equals(participationHour.getStart())) {
                    mBinding.viewSchedule.wpStart.setSelectedItemPosition(i);
                    break;
                }
            }
            for (int i = 0; i < mTimeData.size(); i++) {
                String time = mTimeData.get(i);
                if (time.equals(participationHour.getEnd())) {
                    mBinding.viewSchedule.wpEnd.setSelectedItemPosition(i);
                    break;
                }
            }
            mTempRepeats.addAll(mSelParticipationHour.getRepeat());
            boolean isOn = mSelParticipationHour.getAll_day();
            mBinding.viewSchedule.switchAllDay.setOn(isOn);
            mBinding.viewSchedule.wpStart.setVisibility(isOn ? View.GONE : View.VISIBLE);
            mBinding.viewSchedule.wpEnd.setVisibility(isOn ? View.GONE : View.VISIBLE);
            mBinding.viewSchedule.viewLine.setVisibility(isOn ? View.GONE : View.VISIBLE);
            mBinding.viewSchedule.viewHighlight.setVisibility(isOn ? View.GONE : View.VISIBLE);
        }
        setRepeatStr();
        mStatusBar.setBackgroundResource(R.color.color_black_02);
        mBinding.viewMaskHead.setVisibility(View.VISIBLE);
        mBinding.viewMaskContent.setVisibility(View.VISIBLE);
        mBSBSchedule.setState(BottomSheetBehavior.STATE_EXPANDED);
    }

    private void setRepeatStr() {
        Collections.sort(mTempRepeats);
        mBinding.viewSchedule.tvRepeatValue.setText(DDDateUtil.sumWeekStr(mTempRepeats));
    }

    private void showDiscardDialog() {
        AlertDialog.createBuilder(getContext())
                .setContent(getString(R.string.Are_you_sure_you_want_to_discard_all_changes))
                .setOk(getString(R.string.Discard))
                .setCancel(getString(R.string.cancel))
                .setContentGravity(Gravity.CENTER)
                .setContentLayoutMarginBottom(0)
                .setAutoDissmiss(true)
                .setOKListener(() -> mBSBSchedule.setState(BottomSheetBehavior.STATE_COLLAPSED)).preBuilder().show();
    }

    private void showDeleteDialog(String id) {
        AlertDialog.createBuilder(getContext())
                .setContent(getString(R.string.delete_schedule_tips))
                .setOk(getString(R.string.delete))
                .setCancel(getString(R.string.cancel))
                .setOkColor(getResColor(R.color.color_white_01))
                .setCancelColor(getResColor(R.color.color_tip_warning))
                .setOkBtnSolidColor(R.color.color_tip_warning)
                .setOkBtnStrokeColor(R.color.color_tip_warning)
                .setCancelBtnStrokeColor(R.color.color_tip_warning)
                .setContentGravity(Gravity.CENTER)
                .setContentLayoutMarginBottom(0)
                .setAutoDissmiss(true)
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        deleteParticipationHour(id);
                    }
                }).preBuilder().show();
    }

    private void showTimeConflictDialog() {
        if (mTimeConflictAlertDialog == null) {
            String sb = Local.s(getString(R.string.Note)) +
                    ":\n" +
                    Local.s(getString(R.string.conflicts_with_existing_tasks_discard_note)) +
                    "\n" +
                    Local.s(getString(R.string.conflicts_with_existing_tasks_save_note));
            mTimeConflictAlertDialog = new TimeConflictAlertDialog.Builder(getContext())
                    .setTips(getString(R.string.conflicts_with_existing_tasks_tips))
                    .setNote(sb)
                    .setConfirmText(getString(R.string.Discard))
                    .setCancelText(getString(R.string.Modify_It))
                    .setShowNote(true)
                    .setClickCallback(new TimeConflictAlertDialog.Builder.OnClickCallback() {
                        @Override
                        public void onConfirm(View view, TimeConflictAlertDialog dialog) {
                            mTimeConflictAlertDialog.dismiss();
                            mBSBSchedule.setState(BottomSheetBehavior.STATE_COLLAPSED);
                        }

                        @Override
                        public void onCancel(View view, TimeConflictAlertDialog dialog) {
                            updateBalanceContractParticipationHours();
                            mTimeConflictAlertDialog.dismiss();
                        }
                    }).builder();
        }
        if (!mTimeConflictAlertDialog.isShowing()) {
            mTimeConflictAlertDialog.show();
        }
    }

    private void showDeleteConflictDialog(String id) {
        new TimeConflictAlertDialog.Builder(getContext())
                .setTips(getString(R.string.participation_hour_delete_current_task_tips))
                .setConfirmText(getString(R.string.Keep_Setting))
                .setCancelText(getString(R.string.delete))
                .setShowNote(false)
                .setClickCallback(new TimeConflictAlertDialog.Builder.OnClickCallback() {
                    @Override
                    public void onConfirm(View view, TimeConflictAlertDialog dialog) {
                        dialog.dismiss();
                    }

                    @Override
                    public void onCancel(View view, TimeConflictAlertDialog dialog) {
                        dialog.dismiss();
                        deleteParticipationHour(id);
                    }
                }).builder().show();
    }

    private void getData(boolean showLoading, boolean isToTop) {
        if (showLoading) {
            showTimeOutLoadinFramgment();
        }
        DinHome.getInstance().bmtBalanceContractParticipationHours(0, HomeManager.getInstance().getCurrentHome().getHomeID(), 10, new IDefaultCallBack2<BmtBalanceContractParticipationHoursResponse.ResultBean>() {
            @Override
            public void onSuccess(BmtBalanceContractParticipationHoursResponse.ResultBean resultBean) {
                if (resultBean != null) {
                    mData.clear();
                    List<ParticipationHour> participationHours = resultBean.getDatas();
                    if (CollectionUtil.isListNotEmpty(participationHours)) {
                        for (ParticipationHour participationHour : participationHours) {
                            if (participationHour.getAll_day()) {
                                participationHour.setStart("00:00");
                                participationHour.setEnd("23:00");
                            }
                            mData.add(new ParticipationHourModel(getContext(), participationHour, participationHours.size() == 1));
                        }
                        mAdapter.notifyDataSetChanged();
                    }
                    mBinding.commonBar.commonBarRightIcon.setAlpha(mData.size() < 10 ? 1f : 0.5f);
                    if (isToTop) {
                        mBinding.nsvParent.scrollTo(0, 0);
                    }
                }
                if (showLoading) {
                    closeLoadingFragment();
                }
            }

            @Override
            public void onError(int i, String s) {
                if (showLoading) {
                    closeLoadingFragment();
                    showErrorToast();
                }
            }
        });
    }

    private void addBalanceContractParticipationHours() {
        ParticipationHour participationHour = new ParticipationHour();
        participationHour.setStart(mTimeData.get(mBinding.viewSchedule.wpStart.getCurrentItemPosition()));
        participationHour.setEnd(mTimeData.get(mBinding.viewSchedule.wpEnd.getCurrentItemPosition()));
        participationHour.setAll_day(mBinding.viewSchedule.switchAllDay.isOn());
        participationHour.setRepeat(mTempRepeats);
        DinHome.getInstance().bmtAddBalanceContractParticipationHours(HomeManager.getInstance().getCurrentHome().getHomeID(),
                participationHour, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        getData(false, true);
                        mBSBSchedule.setState(BottomSheetBehavior.STATE_COLLAPSED);
                    }

                    @Override
                    public void onError(int i, String s) {
                        showErrorToast();
                    }
                });
    }

    private void isTaskTimeInUpdatedRange(int type, String id) {
        ParticipationHour participationHour = new ParticipationHour();
        if (type == 0) {
            participationHour.setId(mSelParticipationHour.getId());
            participationHour.setStart(mTimeData.get(mBinding.viewSchedule.wpStart.getCurrentItemPosition()));
            participationHour.setEnd(mTimeData.get(mBinding.viewSchedule.wpEnd.getCurrentItemPosition()));
            participationHour.setAll_day(mBinding.viewSchedule.switchAllDay.isOn());
            participationHour.setRepeat(mTempRepeats);
        } else if (type == 1) {
            participationHour.setId(id);
            participationHour.setAll_day(true);
            participationHour.setRepeat(new ArrayList<>());
        }
        showTimeOutLoadinFramgment();
        DinHome.getInstance().bmtIsTaskTimeInUpdatedRange(HomeManager.getInstance().getCurrentHome().getHomeID(),
                participationHour, new IDefaultCallBack2<BmtIsTaskTimeInUpdatedRangeResponse.ResultBean>() {
                    @Override
                    public void onSuccess(BmtIsTaskTimeInUpdatedRangeResponse.ResultBean resultBean) {
                        runOnMainThread(() -> {
                            closeLoadingFragment();
                            if (resultBean != null) {
                                boolean isTaskTimeInUpdatedRange = resultBean.isIs_task_time_in_updated_range();
                                if (type == 0) {
                                    if (isTaskTimeInUpdatedRange) {
                                        updateBalanceContractParticipationHours();
                                    } else {
                                        showTimeConflictDialog();
                                    }
                                } else if (type == 1) {
                                    if (isTaskTimeInUpdatedRange) {
                                        showDeleteDialog(id);
                                    } else {
                                        showDeleteConflictDialog(id);
                                    }
                                }
                            } else {
                                showErrorToast();
                            }
                        });

                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

    private void updateBalanceContractParticipationHours() {
        mSelParticipationHour.setStart(mTimeData.get(mBinding.viewSchedule.wpStart.getCurrentItemPosition()));
        mSelParticipationHour.setEnd(mTimeData.get(mBinding.viewSchedule.wpEnd.getCurrentItemPosition()));
        mSelParticipationHour.setAll_day(mBinding.viewSchedule.switchAllDay.isOn());
        mSelParticipationHour.setRepeat(mTempRepeats);
        showTimeOutLoadinFramgment();
        DinHome.getInstance().bmtUpdateBalanceContractParticipationHours(HomeManager.getInstance().getCurrentHome().getHomeID(),
                mSelParticipationHour, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        closeLoadingFragment();
                        getData(false, false);
                        mBSBSchedule.setState(BottomSheetBehavior.STATE_COLLAPSED);
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

    private void deleteParticipationHour(String id) {
        showTimeOutLoadinFramgment();
        DinHome.getInstance().bmtDeleteBalanceContractParticipationHours(HomeManager.getInstance().getCurrentHome().getHomeID(),
                id, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        closeLoadingFragment();
                        getData(false, false);
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }
}
