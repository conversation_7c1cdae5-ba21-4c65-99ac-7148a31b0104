package com.dinsafer.module.powerstation.dialog;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogScheduledModeBinding;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.ScheduledModeDialogModel;
import com.dinsafer.permission.BaseBottomSheetDialog;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CollectionUtil;

import java.util.ArrayList;

public class ScheduledModeDialog extends BaseBottomSheetDialog<DialogScheduledModeBinding> {

    public static final String TAG = ScheduledModeDialog.class.getSimpleName();
    private BindMultiAdapter<ScheduledModeDialogModel> mAdapter;
    private ArrayList<ScheduledModeDialogModel> mData;
    private static final String KEY_SELECTED_MODE = "key_selected_mode";
    private static final String KEY_SECTION_TYPE = "key_section_type";
    private static final String KEY_EMERGENCY_RESERVE = "key_emergency_reserve";
    private static final String KEY_SMART_RESERVE = "key_smart_reserve";
    private int selectedMode;
    private int sectionType;
    private int emergencyReserve = 30;
    private int smartReserve = 70;
    private boolean isGridToBattery;

    public static ScheduledModeDialog newInstance(final Builder builder) {
        ScheduledModeDialog dialog = new ScheduledModeDialog();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_SELECTED_MODE, builder.selectedMode);
        bundle.putInt(KEY_SECTION_TYPE, builder.sectionType);
        bundle.putInt(KEY_EMERGENCY_RESERVE, builder.emergencyReserve);
        bundle.putInt(KEY_SMART_RESERVE, builder.smartReserve);
        bundle.putBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY, builder.isGridToBattery);
        dialog.setArguments(bundle);
        dialog.setConfirmListener(builder.confirmListener);
        return dialog;
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_scheduled_mode;
    }

    @Override
    protected int provideDialogHeight() {
        return ViewGroup.LayoutParams.WRAP_CONTENT;
    }

    @Override
    protected void initView() {
        super.initView();
        setEnableDrag(false);
        mBinding.getRoot().setBackgroundResource(R.drawable.shape_bg_cascading_reserve_mode);
        mBinding.tvCancel.setOnClickListener(view -> {
            dismiss();
            if (confirmListener != null) {
                confirmListener.onCancel();
            }
        });
        mBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ScheduledModeDialogModel model = getSelectedMode();
                if (model != null) {
                    int mode = model.getMode();
                    int sectionType = 0;
                    if (mode > 0) {
                        sectionType = model.getSectionType();
                    } else if (mode < 0) {
                        sectionType = 3 - model.getSectionType();
                    }
                    if (confirmListener != null) {
                        confirmListener.confirm(model.getMode(), sectionType);
                    }
                }
                dismiss();
            }
        });
        Bundle bundle = getArguments();
        selectedMode = bundle.getInt(KEY_SELECTED_MODE);
        sectionType = bundle.getInt(KEY_SECTION_TYPE);
        emergencyReserve = bundle.getInt(KEY_EMERGENCY_RESERVE);
        smartReserve = bundle.getInt(KEY_SMART_RESERVE);
        isGridToBattery = bundle.getBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY);
        initRv();
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (confirmListener != null) {
            confirmListener.onCancel();
        }
    }

    private void initRv() {
        mBinding.rvMode.setLayoutManager(new LinearLayoutManager(getContext()));
        ((SimpleItemAnimator)mBinding.rvMode.getItemAnimator()).setSupportsChangeAnimations(false);
        mAdapter = new BindMultiAdapter<>();
        mBinding.rvMode.setAdapter(mAdapter);
        mAdapter.setOnBindItemClickListener(new OnBindItemClickListener<ScheduledModeDialogModel>() {
            @Override
            public void onItemClick(View v, int position, ScheduledModeDialogModel model) {
                if (model.isSelected()) return;
                for (ScheduledModeDialogModel scheduledModeDialogModel : mData) {
                    scheduledModeDialogModel.setSelected(false);
                }
                model.setSectionType(2);
                model.setSelected(true);
                mAdapter.notifyDataSetChanged();
            }
        });

        mAdapter.setOnBindItemChildClickListener(new OnBindItemChildClickListener<ScheduledModeDialogModel>() {
            @Override
            public void onItemChildClick(View view, int position, ScheduledModeDialogModel model) {
                int viewId = view.getId();
                if (viewId == R.id.tv_one) {
                    model.setSectionType(1);
                } else if (viewId == R.id.tv_two) {
                    model.setSectionType(2);
                } else if (viewId == R.id.tv_three) {
                    model.setSectionType(3);
                }
                mAdapter.notifyItemChanged(position);
            }
        });

        mData = new ArrayList<>();
        mData.add(new ScheduledModeDialogModel(getContext(), 1, sectionType, emergencyReserve, smartReserve, selectedMode == 1, isGridToBattery));
        mData.add(new ScheduledModeDialogModel(getContext(), 0, sectionType, emergencyReserve, smartReserve, selectedMode == 0, true));
        mData.add(new ScheduledModeDialogModel(getContext(), -1, sectionType, emergencyReserve, smartReserve, selectedMode == -1, true));
        mAdapter.setNewData(mData);
    }

    private ScheduledModeDialogModel getSelectedMode() {
        ScheduledModeDialogModel model = null;
        if (CollectionUtil.isListNotEmpty(mData)) {
            for (ScheduledModeDialogModel scheduledModeDialogModel : mData) {
                if (scheduledModeDialogModel.isSelected()) {
                    model = scheduledModeDialogModel;
                    break;
                }
            }
        }
        return model;
    }

    private OnConfirmListener confirmListener;

    public void setConfirmListener(OnConfirmListener confirmListener) {
        this.confirmListener = confirmListener;
    }

    public interface OnConfirmListener {
        void confirm(int mode, int sectionType);
        void onCancel();
    }

    public static class Builder {
        private int selectedMode;
        private int sectionType;
        private int emergencyReserve;
        private int smartReserve;
        private boolean isGridToBattery;
        private OnConfirmListener confirmListener;

        public Builder setSelectedMode(int selectedMode) {
            this.selectedMode = selectedMode;
            return this;
        }

        public Builder setSectionType(int  sectionType) {
            this.sectionType = sectionType;
            return this;
        }

        public Builder setEmergencyReserve(int emergencyReserve) {
            this.emergencyReserve = emergencyReserve;
            return this;
        }

        public Builder setSmartReserve(int smartReserve) {
            this.smartReserve = smartReserve;
            return this;
        }

        public Builder setGridToBattery(boolean gridToBattery) {
            isGridToBattery = gridToBattery;
            return this;
        }

        public Builder setConfirmListener(OnConfirmListener confirmListener) {
            this.confirmListener = confirmListener;
            return this;
        }

        public ScheduledModeDialog build() {
            return newInstance(this);
        }
    }
}
