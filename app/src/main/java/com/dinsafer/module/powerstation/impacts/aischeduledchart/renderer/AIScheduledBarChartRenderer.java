package com.dinsafer.module.powerstation.impacts.aischeduledchart.renderer;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;

import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.impacts.aischeduledchart.bean.AIScheduledBarEntry;
import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.buffer.BarBuffer;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.interfaces.dataprovider.BarDataProvider;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.renderer.BarChartRenderer;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.Utils;
import com.github.mikephil.charting.utils.ViewPortHandler;

import java.util.ArrayList;
import java.util.List;


public class AIScheduledBarChartRenderer extends BarChartRenderer {

    private Context mContext;
    private int mRadius = 10;
    private Path mDataPath;
    private Path mBorderPath;
    private RectF mRectF;
    private RectF mBorderRectF;
    private Rect mTextRect;
    private View mEmojiView;
    private int mNowPosition = -1;
    private int mClickPosition = -1;
    private List<AIBarEntryTopNoteCache> mTopNoteCacheList;
    private Typeface mPalanquinTypeface;

    public AIScheduledBarChartRenderer(Context context, BarDataProvider chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {
        super(chart, animator, viewPortHandler);
        mContext = context;
        mPalanquinTypeface = ResourcesCompat.getFont(context, R.font.palanquin);
        mDataPath = new Path();
        mBorderPath = new Path();
        mRectF = new RectF();
        mBorderRectF = new RectF();
        mTextRect = new Rect();
        mTopNoteCacheList = new ArrayList<>();
        mRenderPaint.setTypeface(mPalanquinTypeface);
    }

    @Override
    protected void drawDataSet(Canvas c, IBarDataSet dataSet, int index) {
        Transformer trans = mChart.getTransformer(dataSet.getAxisDependency());
        mBarBorderPaint.setColor(dataSet.getBarBorderColor());
        mBarBorderPaint.setStrokeWidth(Utils.convertDpToPixel(dataSet.getBarBorderWidth()));
        mShadowPaint.setColor(dataSet.getBarShadowColor());
        float phaseX = mAnimator.getPhaseX();
        float phaseY = mAnimator.getPhaseY();
        // initialize the buffer
        BarBuffer buffer = mBarBuffers[index];
        buffer.setPhases(phaseX, phaseY);
        buffer.setBarSpace(dataSet.getBarSpace());
        buffer.setDataSet(index);
        buffer.setInverted(mChart.isInverted(dataSet.getAxisDependency()));

        buffer.feed(dataSet);

        trans.pointValuesToPixel(buffer.buffer);

        // draw the bar shadow before the values
        if (mChart.isDrawBarShadowEnabled()) {
            for (int j = 0; j < buffer.size(); j += 4) {

                if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2]))
                    continue;

                if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
                    break;

                c.drawRect(buffer.buffer[j], mViewPortHandler.contentTop(),
                        buffer.buffer[j + 2],
                        mViewPortHandler.contentBottom(), mShadowPaint);
            }
        }

        int stackSize = dataSet.getStackSize();
        int buffersize = buffer.size();
        // if multiple colors
        int colorSize = dataSet.getColors().size();

        for (int j = 0; j < buffer.size(); j += 4 * stackSize) {
            BarEntry e = dataSet.getEntryForIndex(j / (4 * stackSize));
            if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2]))
                continue;
            if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
                break;
            boolean isStack = dataSet.getStackSize() > 1;
            final float y1;
            final float y2;

            if (isStack) {
                y1 = e.getPositiveSum();
                y2 = -e.getNegativeSum();
            } else {
                y1 = e.getVal();
                y2 = 0f;
            }

            boolean tl = y1 > 0;
            boolean tr = y1 > 0;
            boolean bl = y1 < 0;
            boolean br = y1 < 0;
            if (isStack) {
                tl = y1 > 0;
                tr = y1 > 0;
                bl = y2 < 0;
                br = y2 < 0;
            }

            float top = buffer.buffer[j + 4 * (stackSize - 1) + 1];
            float bottom = buffer.buffer[j + 3];
            mRectF.set(buffer.buffer[j], top, buffer.buffer[j + 2],
                    bottom);
            Path path = roundRect(mDataPath, mRectF, mRadius, mRadius, tl,
                    tr, bl, br);
            if (colorSize > 1) {
                float[] positions = new float[stackSize];
                for (int k = 0; k < stackSize; k++) {
                    positions[k] = (buffer.buffer[j + 4 * k + 1] - buffer.buffer[j + 4 * k + 3]) / (top - bottom);
                }
                float[] pos = new float[stackSize * 2];
                int[] colors = new int[stackSize * 2];
                int num = 0;
                for (int l = 0; l < positions.length; l++) {
                    colors[num] = dataSet.getColors().get(l % dataSet.getColors().size());
                    colors[num + 1] = dataSet.getColors().get(l % dataSet.getColors().size());
                    if (l == 0) {
                        pos[num] = 0f;
                        pos[num + 1] = positions[l];
                    } else {
                        pos[num] = positions[l - 1];
                        pos[num + 1] = positions[l - 1] + positions[l];
                    }
                    num = num + 2;
                }
                mRenderPaint.setShader(new LinearGradient(0, bottom,
                        0, top, colors,
                        pos, Shader.TileMode.CLAMP));
            } else {
                if (e instanceof AIScheduledBarEntry) {
                    mRenderPaint.setColor(((AIScheduledBarEntry) e).getCustomColor());
                } else {
                    mRenderPaint.setColor(dataSet.getColor());
                }
            }
            mRenderPaint.setStyle(Paint.Style.FILL);
            c.drawPath(path, mRenderPaint);
            if (e instanceof AIScheduledBarEntry) {
                AIScheduledBarEntry tempEntry = (AIScheduledBarEntry) e;
                if (tempEntry.isDrawBorder()) {
                    mBarBorderPaint.setStyle(Paint.Style.STROKE);
                    mBarBorderPaint.setColor(tempEntry.getBorderColor());
                    float borderWidth = tempEntry.getBorderWidth();
                    mBarBorderPaint.setStrokeWidth(borderWidth);
                    mBorderRectF.set(buffer.buffer[j] + borderWidth / 2f, top + borderWidth / 2f,
                            buffer.buffer[j + 2] - borderWidth / 2f,
                            bottom - borderWidth / 2f);
                    Path borderPath = roundRectBorder(mBorderPath, mBorderRectF, mRadius, mRadius, tl,
                            tr, bl, br);
                    c.drawPath(borderPath, mBarBorderPaint);
                }

                if (tempEntry.isDrawIcon()) {
                    Bitmap bitmap = BitmapFactory.decodeResource(mContext.getResources(), tempEntry.getIconRes());
                    c.drawBitmap(bitmap, (mRectF.left + mRectF.right) / 2f - bitmap.getWidth() / 2f,
                            mRectF.top + tempEntry.getIconTopOffset(), mRenderPaint);
                }
            }
        }

    }

    @Override
    public void drawExtras(Canvas canvas) {
        super.drawExtras(canvas);
        if (mEmojiView != null) {
            mEmojiView.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
            mEmojiView.layout(0, 0, mEmojiView.getMeasuredWidth(),
                    mEmojiView.getMeasuredHeight());
            float offsetX = (mViewPortHandler.contentLeft() + mViewPortHandler.contentRight()) / 2f
                    - (mEmojiView.getWidth()) / 2f;
            canvas.translate(offsetX, 0);
            mEmojiView.draw(canvas);
            canvas.translate(-offsetX, 0);
        }
        mTopNoteCacheList.clear();
        if (mNowPosition > -1) {
            getTopNote(mNowPosition);
        }
        if (mClickPosition > -1 && mClickPosition != mNowPosition) {
            getTopNote(mClickPosition);
        }
        drawTopNote(canvas);
    }

    private void getTopNote(int index) {
        BarData barData = mChart.getBarData();
        if (index > -1) {
            IBarDataSet dataSet = barData.getDataSetByIndex(0);
            if (dataSet != null && dataSet.isVisible() && dataSet.getEntryCount() > 0) {
                Transformer trans = mChart.getTransformer(dataSet.getAxisDependency());
                float phaseX = mAnimator.getPhaseX();
                float phaseY = mAnimator.getPhaseY();
                // initialize the buffer
                BarBuffer buffer = mBarBuffers[0];
                buffer.setPhases(phaseX, phaseY);
                buffer.setBarSpace(dataSet.getBarSpace());
                buffer.setDataSet(0);
                buffer.setInverted(mChart.isInverted(dataSet.getAxisDependency()));

                buffer.feed(dataSet);

                trans.pointValuesToPixel(buffer.buffer);

                int stackSize = dataSet.getStackSize();
                int buffersize = buffer.size();
                // if multiple colors
                int colorSize = dataSet.getColors().size();
                int j = 4 * index;
                BarEntry e = dataSet.getEntryForIndex(j / (4 * stackSize));
                if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2]))
                    return;
                if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
                    return;
                boolean isStack = dataSet.getStackSize() > 1;
                final float y1;
                final float y2;

                if (isStack) {
                    y1 = e.getPositiveSum();
                    y2 = -e.getNegativeSum();
                } else {
                    y1 = e.getVal();
                    y2 = 0f;
                }

                boolean tl = y1 > 0;
                boolean tr = y1 > 0;
                boolean bl = y1 < 0;
                boolean br = y1 < 0;
                if (isStack) {
                    tl = y1 > 0;
                    tr = y1 > 0;
                    bl = y2 < 0;
                    br = y2 < 0;
                }

                float top = buffer.buffer[j + 4 * (stackSize - 1) + 1];
                float bottom = buffer.buffer[j + 3];
                mRectF.set(buffer.buffer[j], top, buffer.buffer[j + 2],
                        bottom);
                if (e instanceof AIScheduledBarEntry) {
                    AIScheduledBarEntry tempEntry = (AIScheduledBarEntry) e;
                    if (tempEntry.isDrawTopNote()) {
                        mBarBorderPaint.setStyle(Paint.Style.STROKE);
                        mBarBorderPaint.setColor(tempEntry.getBorderColor());
                        float borderWidth = tempEntry.getBorderWidth();
                        mBarBorderPaint.setStrokeWidth(borderWidth);
                        mBorderRectF.set(buffer.buffer[j] + borderWidth / 2f, top + borderWidth / 2f,
                                buffer.buffer[j + 2] - borderWidth / 2f,
                                bottom - borderWidth / 2f);
                        AIBarEntryTopNoteCache topNoteCache = new AIBarEntryTopNoteCache(tempEntry, (mBorderRectF.left + mBorderRectF.right) / 2f, mBorderRectF.top);
                        mTopNoteCacheList.add(topNoteCache);
                    }
                }
            }

        }
    }

    private void drawTopNote(Canvas canvas) {
        if (mTopNoteCacheList == null || mTopNoteCacheList.size() == 0) return;
        AIBarEntryTopNoteCache aiBarEntryTopNoteCache = mTopNoteCacheList.get(0);
        if (mTopNoteCacheList.size() == 1) {
            drawTopText(canvas, aiBarEntryTopNoteCache, aiBarEntryTopNoteCache.getY() - 10);
        } else if (mTopNoteCacheList.size() == 2) {

            AIBarEntryTopNoteCache aiBarEntryTopNoteCache2 = mTopNoteCacheList.get(1);
            float x = aiBarEntryTopNoteCache.getX();
            float y = aiBarEntryTopNoteCache.getY();
            float x2 = aiBarEntryTopNoteCache2.getX();
            float y2 = aiBarEntryTopNoteCache2.getY();
            AIScheduledBarEntry barEntry = aiBarEntryTopNoteCache.getBarEntry();
            String text = barEntry.getTopNote();
            if (TextUtils.isEmpty(text)) return;
            mRenderPaint.setTextSize(barEntry.getNoteTextSize());
            mRenderPaint.getTextBounds(text, 0, text.length(), mTextRect);
            float lineEndY = y - 10;
            float top = lineEndY - mTextRect.height() - barEntry.getTextPaddingTop() - barEntry.getTextPaddingBottom();
            float left = x - mTextRect.width() / 2f - barEntry.getTextPaddingLeft();
            float right = x + mTextRect.width() / 2f + barEntry.getTextPaddingRight();

            AIScheduledBarEntry barEntry2 = aiBarEntryTopNoteCache2.getBarEntry();
            String text2 = barEntry2.getTopNote();
            if (TextUtils.isEmpty(text2)) return;
            mRenderPaint.setTextSize(barEntry2.getNoteTextSize());
            mRenderPaint.getTextBounds(text2, 0, text2.length(), mTextRect);
            float lineEndY2 = y2 - 10;
            float top2 = lineEndY2 - mTextRect.height() - barEntry2.getTextPaddingTop() - barEntry2.getTextPaddingBottom();
            float left2 = x2 - mTextRect.width() / 2f - barEntry2.getTextPaddingLeft();
            float right2 = x2 + mTextRect.width() / 2f + barEntry2.getTextPaddingRight();
            boolean widthCover = (left > left2 && left < right2) || (right > left2 && right < right2);
            float startY;
            if (y < y2) {
                startY = (top2 > top && top2 < lineEndY && widthCover) ? top2 - 10 : y - 10;
                drawTopText(canvas, aiBarEntryTopNoteCache, startY);
                drawTopText(canvas, aiBarEntryTopNoteCache2, y2 - 10);
            } else {
                startY = (top > top2 && top < lineEndY2 && widthCover) ? top - 10 : y2 - 10;
                drawTopText(canvas, aiBarEntryTopNoteCache, y - 10);
                drawTopText(canvas, aiBarEntryTopNoteCache2, startY);
            }
        }
        mTopNoteCacheList.clear();
    }

    private void drawTopText(Canvas canvas, AIBarEntryTopNoteCache topNoteCache, float lineEndY) {
        if (canvas == null) return;
        AIScheduledBarEntry barEntry = topNoteCache.getBarEntry();
        float x = topNoteCache.getX();
        float y = topNoteCache.getY();

        String text = barEntry.getTopNote();
        if (text == null) return;
        mRenderPaint.setStyle(Paint.Style.FILL);
        mRenderPaint.setColor(barEntry.getBorderColor());
        mRenderPaint.setStrokeWidth(barEntry.getBorderWidth());
        canvas.drawLine(x, y, x, lineEndY, mRenderPaint);
        mRenderPaint.setTextSize(barEntry.getNoteTextSize());
        mRenderPaint.getTextBounds(text, 0, text.length(), mTextRect);
        float left = x - mTextRect.width() / 2f - barEntry.getTextPaddingLeft();
        float top = lineEndY - mTextRect.height() - barEntry.getTextPaddingTop() - barEntry.getTextPaddingBottom();
        float right = x + mTextRect.width() / 2f + barEntry.getTextPaddingRight();
        float bottom = lineEndY;
        float radius = barEntry.getTextBackgroundRadius();
        canvas.drawRoundRect(left, top, right, bottom, radius, radius, mRenderPaint);
        mRenderPaint.setColor(barEntry.getNoteColor());
        mRenderPaint.setFakeBoldText(true);
        canvas.drawText(text, left + barEntry.getTextPaddingLeft(), bottom - barEntry.getTextPaddingBottom(), mRenderPaint);
    }

    private Path roundRect(Path path, RectF rect, float rx, float ry, boolean tl, boolean tr, boolean br, boolean bl) {
        float top = rect.top;
        float left = rect.left;
        float right = rect.right;
        float bottom = rect.bottom;
        path.reset();
        if (rx < 0) {
            rx = 0;
        }
        if (ry < 0) {
            ry = 0;
        }
        float width = right - left;
        float height = bottom - top;
        if (rx > width / 2) {
            rx = width / 2;
        }
        if (ry > height / 2) {
            ry = height / 2;
        }
        float widthMinusCorners = (width - (2 * rx));
        float heightMinusCorners = (height - (2 * ry));

        path.moveTo(right, top + ry);
        if (tr) {
            //top-right corner
            path.rQuadTo(0, -ry, -rx, -ry);
        } else {
            path.rLineTo(0, -ry);
            path.rLineTo(-rx, 0);
        }
        path.rLineTo(-widthMinusCorners, 0);
        if (tl) {
            //top-left corner
            path.rQuadTo(-rx, 0, -rx, ry);
        } else {
            path.rLineTo(-rx, 0);
            path.rLineTo(0, ry);
        }
        path.rLineTo(0, heightMinusCorners);

        if (bl) {
            //bottom-left corner
            path.rQuadTo(0, ry, rx, ry);
        } else {
            path.rLineTo(0, ry);
            path.rLineTo(rx, 0);
        }

        path.rLineTo(widthMinusCorners, 0);
        if (br) {
            //bottom-right corner
            path.rQuadTo(rx, 0, rx, -ry);
        } else {
            path.rLineTo(rx, 0);
            path.rLineTo(0, -ry);
        }

        path.rLineTo(0, -heightMinusCorners);
        path.close();//Given close, last lineto can be removed.
        return path;
    }

    private Path roundRectBorder(Path path, RectF rect, float rx, float ry, boolean tl, boolean tr, boolean br, boolean bl) {
        float top = rect.top;
        float left = rect.left;
        float right = rect.right;
        float bottom = rect.bottom;
        path.reset();
        if (rx < 0) {
            rx = 0;
        }
        if (ry < 0) {
            ry = 0;
        }
        float width = right - left;
        float height = bottom - top;
        if (rx > width / 2) {
            rx = width / 2;
        }
        if (ry > height / 2) {
            ry = height / 2;
        }
        float widthMinusCorners = (width - (2 * rx));
        float heightMinusCorners = (height - (2 * ry));

        path.moveTo(right, top + ry);
        if (tr) {
            //top-right corner
            path.rQuadTo(0, -ry, -rx, -ry);
        } else {
            path.rLineTo(0, -ry);
            path.rLineTo(-rx, 0);
        }
        path.rLineTo(-widthMinusCorners, 0);
        if (tl) {
            //top-left corner
            path.rQuadTo(-rx, 0, -rx, ry);
        } else {
            path.rLineTo(-rx, 0);
            path.rLineTo(0, ry);
        }
        path.rLineTo(0, heightMinusCorners);

        if (bl) {
            //bottom-left corner
            path.rQuadTo(0, ry, rx, ry);
        } else {
            path.rLineTo(0, ry);
            path.rLineTo(rx, 0);
        }

        path.rLineTo(widthMinusCorners, 0);
        if (br) {
            //bottom-right corner
            path.rQuadTo(rx, 0, rx, -ry);
        } else {
            path.rLineTo(rx, 0);
            path.rLineTo(0, -ry);
        }

        path.rLineTo(0, -heightMinusCorners);
        path.close();//Given close, last lineto can be removed.
        return path;
    }

    public void setRadius(int radius) {
        this.mRadius = radius;
    }

    public void setEmojiView(View view) {
        this.mEmojiView = view;
    }

    public void setHighLightPosition(int nowPosition, int clickPosition) {
        this.mNowPosition = nowPosition;
        this.mClickPosition = clickPosition;
    }

    public static class AIBarEntryTopNoteCache {
        private AIScheduledBarEntry barEntry;
        private float x;
        private float y;

        public AIBarEntryTopNoteCache() {
        }

        public AIBarEntryTopNoteCache(AIScheduledBarEntry barEntry, float x, float y) {
            this.barEntry = barEntry;
            this.x = x;
            this.y = y;
        }

        public AIScheduledBarEntry getBarEntry() {
            return barEntry;
        }

        public void setBarEntry(AIScheduledBarEntry barEntry) {
            this.barEntry = barEntry;
        }

        public float getX() {
            return x;
        }

        public void setX(float x) {
            this.x = x;
        }

        public float getY() {
            return y;
        }

        public void setY(float y) {
            this.y = y;
        }
    }
}
