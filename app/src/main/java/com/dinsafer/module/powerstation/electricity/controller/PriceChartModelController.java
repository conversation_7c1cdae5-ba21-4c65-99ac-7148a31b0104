package com.dinsafer.module.powerstation.electricity.controller;

import android.content.Context;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;

public class PriceChartModelController implements IChartModelController{
    @Override
    public void initXAxis(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType, int labelsToSkip, XAxisValueFormatter formatter) {
        customCombinedChartManager.initXAxis(false, false, context.getResources().getColor(R.color.color_white_04), 10f,
                true, context.getResources().getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                labelsToSkip, formatter);
    }

    @Override
    public void initYAxis(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType, YAxisValueFormatter yFormatter, boolean isAxisRightEnabled) {
        customCombinedChartManager.initYAxis(false, 10f, context.getResources().getColor(R.color.color_white_03),
                0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
                yFormatter, isAxisRightEnabled, 5);
    }

    @Override
    public void initLineChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType) {
        customCombinedChartManager.initLineChartRender(true);
    }

    @Override
    public void initBarChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType) {
        customCombinedChartManager.initBarChartRender(DensityUtil.dp2px(context, 5), context.getResources().getColor(R.color.color_white_03), 1.0f, 2.0f);
    }
}
