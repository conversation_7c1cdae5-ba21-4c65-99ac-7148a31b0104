package com.dinsafer.module.powerstation.event;

public class PushBmtExceptionEvent {

    private String deviceId;
    private String subcategory;
    private String cmd;
    private String homeId;
    private String message;
    private String title;

    public PushBmtExceptionEvent(String deviceId, String subcategory, String cmd, String homeId, String message, String title) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.cmd = cmd;
        this.homeId = homeId;
        this.message = message;
        this.title = title;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getHomeId() {
        return homeId;
    }

    public void setHomeId(String homeId) {
        this.homeId = homeId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
