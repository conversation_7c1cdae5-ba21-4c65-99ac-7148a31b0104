package com.dinsafer.module.powerstation;

import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_EV;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_EV_BN;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_IOT;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_IOT_HARDWARE;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_IOT_ID;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_MCU;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_MCU_ID;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_SIDECAR;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_VERSION_BATTERY;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_VERSION_CABINET;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_VERSION_COMMON;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_VERSION_INVERTER;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;

import androidx.annotation.NonNull;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsFirmwareVersionBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.model.AppStatePreEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.powerstation.bean.KeyValueBean;
import com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean;
import com.dinsafer.module.powerstation.dialog.PSFirmWareDetailDialog;
import com.dinsafer.module.powerstation.event.BmtChipsStatusReloadEvent;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.PSFirmWareVersionUpdateEvent;
import com.dinsafer.module.powerstation.event.PSUpdateSuccessEvent;
import com.dinsafer.module.powerstation.settings.PSDataModeFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.EdittextDialog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MultiClickCounter;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/1 14:08
 * @description :
 */
public class PSFirmWareVersionFragment extends MyBaseFragment<FragmentPsFirmwareVersionBinding> implements IDeviceCallBack {

    private static final long INTERVAL_UPDATE_PROGRESS_MILLIS = 10 * 1000L;
    private static final long INTERVAL_CONNECT_MILLIS = 50 * 1000L;

    private static final String NEW_EV_BN = "v1205203_2346";


    private FirmwareStatus mFirmwareStatus;
    private Animation operatingAnim; // loading动画
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private Subscription mUpdateProgressTimer;
    private Subscription mConnectTimer;
    private final Map<String, PSFirmWareVersionDetailBean> mVersionInfoMap = new HashMap<>();
    private boolean mLoadedVersion = false;
    private MultiClickCounter mMultiClickCounter;
    private MultiClickCounter mVersionClickCounter;
    private int mTimeoutCount;
    private boolean isDealChipStatus = true;
    private String mTimeoutCmd;
    private String mMcuId;

    public static PSFirmWareVersionFragment newInstance(String deviceId, String subcategory) {
        PSFirmWareVersionFragment fragment = new PSFirmWareVersionFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static PSFirmWareVersionFragment newInstance(String deviceId, String subcategory, String mcuId) {
        PSFirmWareVersionFragment fragment = new PSFirmWareVersionFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putString("mcuId", mcuId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_firmware_version;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        initParams();
        mBinding.setClick(new OnClickHandler());
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_firmware_version));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.tvVersion.setText("");
        mBinding.tvNext.setText("");
        changeViewByStatus(FirmwareStatus.NORMAL, null);
        mMultiClickCounter = new MultiClickCounter(10, 2000);
        mMultiClickCounter.setListener(() -> {
            showPwdDialog();
        });

        if (null == mPSDevice) {
            return;
        }

        mVersionClickCounter = new MultiClickCounter(5, 2000);
        mVersionClickCounter.setListener(() -> {
            if (!mLoadedVersion) {
                mLoadedVersion = true;
                requestOtherVersion();
            }
            PSFirmWareDetailDialog.newInstance((dialog) -> {
                postVersionInfoUpdateEvent();
            }).show(getActivity().getSupportFragmentManager(), PSFirmWareDetailDialog.TAG);
        });

        showLoadingFragment(LoadingFragment.BLUE);
        final String deviceName = DeviceHelper.getString(mPSDevice, DinConst.INFO_NAME, "");
        mBinding.tvNext.setText(deviceName);
        BmtManager.needGetOutputInfo = false;
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPSDevice) {
            showErrorToast();
            return;
        }

        requestUpdateStatusAndIotVersion();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        cancelScheduleQueryUpdateProgress();
        cancelScheduleConnect();
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
        }
        BmtManager.needGetOutputInfo = true;
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mMcuId = bundle.getString("mcuId");
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    /**
     * 获取芯片升级状态和IOT版本信息
     */
    private void requestUpdateStatusAndIotVersion() {
        submitCmd(BmtCmd.GET_ADVANCE_INFO);
        submitCmd(BmtCmd.GET_CHIPS_STATUS);
    }

    /**
     * 获取除IOT版本外的其他版本信息
     */
    private void requestOtherVersion() {
        if (TextUtils.isEmpty(mMcuId)) {
            submitCmd(BmtCmd.GET_MCU_INFO);
        }
        submitFirmwares(Mcu.Firmware.FirmwareType.MCU.getFirmwareType());
        submitFirmwares(Mcu.Firmware.FirmwareType.EV.getFirmwareType());
        submitFirmwares(Mcu.Firmware.FirmwareType.Sidecar.getFirmwareType());
        submitFirmwares(Mcu.Firmware.FirmwareType.Cabinet.getFirmwareType());
        submitFirmwares(Mcu.Firmware.FirmwareType.Invertor.getFirmwareType());
        submitFirmwares(Mcu.Firmware.FirmwareType.Battery.getFirmwareType());
//        final int inverterCount = BmtUtil.getTotalInverterCount(mPSDevice);
//        createVersionInfoMap(KEY_VERSION_INVERTER, inverterCount);
//        for (int i = 0; i < inverterCount; i++) {
//            submitCmdWithIndex(BmtCmd.GET_INVERTER_INFO, i);
//        }
//        submitCmd(BmtCmd.GET_BATTERY_ALLINFO);
//        submitCmd(BmtCmd.GET_CABINET_ALLINFO);
    }

    /**
     * 请求升级设备
     */
    private void requestUpdateChips() {
        changeViewByStatus(FirmwareStatus.LOADING, null);
        submitCmd(BmtCmd.UPDATE_CHIPS);
    }

    private boolean submitCmd(String cmd) {
        if (null == mPSDevice) {
            return false;
        }

        final Map<String, Object> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, cmd);
        mPSDevice.submit(params);
        return true;
    }

    private boolean submitCmdWithIndex(String cmd, int index) {
        if (null == mPSDevice) {
            return false;
        }

        final Map<String, Object> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, cmd);
        params.put(PSKeyConstant.INDEX, index);
        mPSDevice.submit(params);
        return true;
    }

    private boolean submitFirmwares(int type) {
        if (null == mPSDevice) {
            return false;
        }
        final Map<String, Object> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, BmtCmd.GET_FIRMWARES);
        params.put(BmtDataKey.TYPE, type);
        mPSDevice.submit(params);
        return true;
    }

    /**
     * 设置为正常
     */
    private void setFirmwareNormal() {
        mBinding.ivStatus.setImageResource(R.drawable.img_power_firmware_battery_nor);
        mBinding.tvInfo.setVisibility(View.VISIBLE);
        mBinding.llNote.setVisibility(View.GONE);
        mBinding.tvInfo.setLocalText(getString(R.string.ps_firm_info_normal));
        mFirmwareStatus = FirmwareStatus.NORMAL;
        setOperateVisible();
    }

    /**
     * 设置为可以更新
     */
    public void setFirmwareCanUpgrade() {
        mBinding.ivStatus.setImageResource(R.drawable.img_power_firmware_battery_upgrade);
        mBinding.tvInfo.setVisibility(View.VISIBLE);
        mBinding.llNote.setVisibility(View.GONE);
        String info;
        if (BmtUtil.isBmtDevicePowerStore(mPSDevice)) {
            info = getString(R.string.ps_firm_info_can_upgrade_ps);
        } else if (BmtUtil.isBmtDevicePowerPulse(mPSDevice)) {
            info = getString(R.string.ps_firm_info_can_upgrade_vb);
        } else {
            info = getString(R.string.ps_firm_info_can_upgrade);
        }
        mBinding.tvInfo.setLocalText(info);
        mFirmwareStatus = FirmwareStatus.CAN_UPGRADE;
        setOperateVisible();
    }

    /**
     * 设置为更新加载中
     */
    public void setFirmwareLoading() {
        mBinding.ivStatus.setImageResource(R.drawable.img_power_firmware_battery_upgrade);
        mBinding.tvInfo.setVisibility(View.GONE);
        mBinding.llNote.setVisibility(View.VISIBLE);
        mBinding.tvNote.setLocalText(getString(R.string.ps_firm_note_loading));
        mBinding.tvTips.setLocalText(getString(R.string.ps_firm_tips_loading));
        mFirmwareStatus = FirmwareStatus.LOADING;
        if (operatingAnim == null) {
            operatingAnim = AnimationUtils.loadAnimation(DinSaferApplication.getAppContext(), R.anim.rotation);
            LinearInterpolator lin = new LinearInterpolator();
            operatingAnim.setInterpolator(lin);
        }
        mBinding.ivLoading.startAnimation(operatingAnim);
        setOperateVisible();
    }

    /**
     * 设置为更新中
     */
    public void setFirmwareUpgrading() {
        mBinding.ivStatus.setImageResource(R.drawable.img_power_firmware_battery_upgrade);
        mBinding.tvInfo.setVisibility(View.GONE);
        mBinding.llNote.setVisibility(View.VISIBLE);
        mBinding.tvNote.setLocalText(getString(R.string.ps_firm_note_loading));
        mBinding.tvTips.setLocalText(getString(R.string.ps_firm_tips_loading));
        mFirmwareStatus = FirmwareStatus.UPGRADING;
        if (operatingAnim != null) {
            mBinding.ivLoading.clearAnimation();
        }
        setOperateVisible();
    }

    /**
     * 设置为其他人在更新中
     */
    public void setFirmwareOtherUpgrading() {
        mBinding.ivStatus.setImageResource(R.drawable.img_power_firmware_battery_upgrade);
        mBinding.tvInfo.setVisibility(View.VISIBLE);
        mBinding.llNote.setVisibility(View.GONE);
        mBinding.tvInfo.setLocalText(getString(R.string.ps_firm_info_other_upgrading));
        mFirmwareStatus = FirmwareStatus.OTHER_UPGRADING;
        setOperateVisible();
    }

    /**
     * 设置更新成功
     */
    public void setFirmwareUpgradeSucceed() {
        mBinding.ivStatus.setImageResource(R.drawable.icon_dialogue_succeed);
        mBinding.tvInfo.setVisibility(View.VISIBLE);
        mBinding.llNote.setVisibility(View.GONE);
        mBinding.tvInfo.setLocalText(getString(R.string.ps_firm_info_other_upgrading_succeed));
        mFirmwareStatus = FirmwareStatus.UPGRADE_SUCCEED;
        mBinding.lcbResult.setLocalText(getString(R.string.ps_firm_done));
        EventBus.getDefault().post(new PSUpdateSuccessEvent());
        BmtManager.getInstance().getBmtInfo(mPSDevice);
        setOperateVisible();
    }

    /**
     * 设置更新失败
     */
    public void setFirmwareUpgradeFailed(List<Integer> errorList) {
        mBinding.ivStatus.setImageResource(R.drawable.icon_dialogue_failed);
        mBinding.tvInfo.setVisibility(View.GONE);
        mBinding.llNote.setVisibility(View.VISIBLE);
        mBinding.tvInfo.setLocalText(getString(R.string.ps_firm_info_other_upgrading_failed));
        mBinding.tvNote.setLocalText(getString(R.string.ps_firm_update_failed));
        mBinding.tvNote.setTextColor(getResColor(R.color.color_white_01));
        mBinding.tvVersion.setVisibility(View.GONE);
        if (CollectionUtil.isListNotEmpty(errorList)) {
            String errors = "";
            if (errorList.contains(Mcu.Upgrade.UpgradeError.BatteryLevelLow.getError())) {
                errors = Local.s(getString(R.string.ps_firm_update_error_hint));
            }
            if (errorList.contains(Mcu.Upgrade.UpgradeError.Other.getError())) {
                errors = TextUtils.isEmpty(errors) ? Local.s(getString(R.string.ps_firm_update_unknown_error_hint))
                        : errors + "\n\n" + Local.s(getString(R.string.ps_firm_update_unknown_error_hint));
            }
            mBinding.tvTips.setLocalText(errors);
        }
        mFirmwareStatus = FirmwareStatus.UPGRADE_FAILED;
        mBinding.lcbResult.setLocalText(getString(R.string.ps_firm_retry));
        setOperateVisible();
    }

    /**
     * 更加当前状态切换界面
     *
     * @param newStatus
     */
    private void changeViewByStatus(FirmwareStatus newStatus, List<Integer> errorList) {
        if (mFirmwareStatus == newStatus) {
            return;
        }

        switch (newStatus) {
            case CAN_UPGRADE:
                setFirmwareCanUpgrade();
                break;
            case LOADING:
                setFirmwareLoading();
                break;
            case UPGRADING:
                setFirmwareUpgrading();
                break;
            case OTHER_UPGRADING:
                setFirmwareOtherUpgrading();
                break;
            case UPGRADE_SUCCEED:
                setFirmwareUpgradeSucceed();
                break;
            case UPGRADE_FAILED:
                setFirmwareUpgradeFailed(errorList);
                break;
            case NORMAL:
//            default:
                setFirmwareNormal();
                break;
        }
        mFirmwareStatus = newStatus;
    }

    /**
     * 根据状态显示隐藏控件
     */
    private void setOperateVisible() {
        mBinding.lcbStartNow.setVisibility(mFirmwareStatus == FirmwareStatus.CAN_UPGRADE ? View.VISIBLE : View.GONE);
        mBinding.lcbNotNow.setVisibility(mFirmwareStatus == FirmwareStatus.CAN_UPGRADE ? View.VISIBLE : View.GONE);
        boolean result = mFirmwareStatus == FirmwareStatus.UPGRADE_SUCCEED || mFirmwareStatus == FirmwareStatus.UPGRADE_FAILED;
        mBinding.lcbResult.setVisibility(result ? View.VISIBLE : View.GONE);
        if (mFirmwareStatus != FirmwareStatus.LOADING) {
            mBinding.ivLoading.clearAnimation();
        }
        mBinding.ivLoading.setVisibility(mFirmwareStatus == FirmwareStatus.LOADING ? View.VISIBLE : View.GONE);
        mBinding.tvProgress.setVisibility(mFirmwareStatus == FirmwareStatus.UPGRADING ? View.VISIBLE : View.GONE);
        mBinding.progressBar.setVisibility(mFirmwareStatus == FirmwareStatus.UPGRADING ? View.VISIBLE : View.GONE);
    }

    /**
     * 检查超时次数, 超时第三次重连
     *
     * @param cmd
     */
    private void checkTimeoutCount(String cmd) {
        cancelScheduleQueryUpdateProgress();
        if (mTimeoutCount < 3) {
            mTimeoutCount = mTimeoutCount + 1;
            submitCmd(cmd);
        } else {
            mTimeoutCmd = cmd;
            DDLog.i(TAG, "超时重连...");
            BmtManager.getInstance().stopPolling();
//                            BmtManager.getInstance().disconnectDevice(mPSDevice);
            scheduleConnect();
        }
    }

    @Override
    public void onCmdCallBack(final String deviceId, String subCategory, final String cmd, final Map map) {
        DDLog.v(TAG, "onCmdCallBack, " + deviceId + " /cmd:" + cmd
                + " /result:" + map.toString());
        if (TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(cmd) || !deviceId.equals(mDeviceId) || !subCategory.equals(mPSDevice.getSubCategory())) {
            return;
        }

        runOnMainThread(() -> {
            if (!getDelegateActivity().isCommonFragmentExist(PSFirmWareVersionFragment.class.getName())) {
                return;
            }

            final int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, 0);
            final Map<String, Object> result = DeviceHelper.getMap(map, PSKeyConstant.RESULT);
            switch (cmd) {
                case BmtCmd.GET_CHIPS_STATUS:
                    if (isDealChipStatus) {
                        if (status == StatusConstant.TIMEOUT
                                || status == StatusConstant.OFFLINE_FAIL) {
                            checkTimeoutCount(BmtCmd.GET_CHIPS_STATUS);
                        } else {
                            mTimeoutCount = 0;
                            closeLoadingFragment();
                            isDealChipStatus = false;
                            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
                            onGetChipsStatus(chipsStatus);
                        }
                    }
                    break;
                case BmtCmd.GET_CHIPS_UPDATE_PROGRESS:
                    if (status == StatusConstant.TIMEOUT
                            || status == StatusConstant.OFFLINE_FAIL) {
                        checkTimeoutCount(BmtCmd.GET_CHIPS_UPDATE_PROGRESS);
                    } else {
                        if (mUpdateProgressTimer == null) {
                            DDLog.i(TAG, "重新开启定时器");
                            scheduleQueryUpdateProgress();
                        }
                        mTimeoutCount = 0;
                        final boolean failed = DeviceHelper.getBoolean(result, BmtDataKey.FAILED, true);
                        final int progress = DeviceHelper.getInt(result, BmtDataKey.PROGRESS, 0);
                        List<Integer> errorList = DeviceHelper.getList(result, BmtDataKey.ERROR);
                        onUpdateProgressUpdate(failed, progress, errorList);
                    }
                    break;
                case BmtCmd.UPDATE_CHIPS:
                    if (status == StatusConstant.TIMEOUT
                            || status == StatusConstant.OFFLINE_FAIL) {
                        checkTimeoutCount(BmtCmd.UPDATE_CHIPS);
                    } else {
                        mTimeoutCount = 0;
                        final int updateError = DeviceHelper.getInt(result, BmtDataKey.ERROR, -1);
                        onUpdateChipResult(updateError);
                    }
                    break;
                case BmtCmd.GET_ADVANCE_INFO:
                    String iotVersion = DeviceHelper.getString(result, BmtDataKey.VERSION, "");
                    String iotHardwareVersion = DeviceHelper.getString(result, BmtDataKey.HARDWARE_VERSION, "");
                    mBinding.tvVersion.setText(iotVersion);
                    createVersionInfoMap(KEY_VERSION_COMMON, 8, null);
                    updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_IOT_ID, mDeviceId);
                    updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_IOT_HARDWARE, iotHardwareVersion);
                    updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_IOT, iotVersion);
                    if (!TextUtils.isEmpty(mMcuId)) {
                        updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_MCU_ID, mMcuId);
                    }
                    postVersionInfoUpdateEvent();
                    break;
                case BmtCmd.GET_MCU_INFO:
                    final String barcode = DeviceHelper.getString(result, BmtDataKey.BARCODE, "");
                    updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_MCU_ID, barcode);
                    postVersionInfoUpdateEvent();
                    break;
//                case BmtCmd.GET_INVERTER_INFO:
//                    final int inverterIndex = DeviceHelper.getInt(result, BmtDataKey.INDEX, -1);
//                    final String inverterVersion = DeviceHelper.getString(result, BmtDataKey.VERSION, "");
//                    updateVersionInfoMap(KEY_VERSION_INVERTER, inverterIndex, inverterVersion);
//                    postVersionInfoUpdateEvent();
//                    break;
//                case BmtCmd.GET_BATTERY_ALLINFO:
//                    if (StatusConstant.STATUS_SUCCESS != status) {
//                        postVersionInfoUpdateEvent();
//                        break;
//                    }
//                    final int batteryCount = DeviceHelper.getInt(result, BmtDataKey.COUNT, 0);
//                    createVersionInfoMap(KEY_VERSION_BATTERY, batteryCount);
//                    for (int i = 0; i < batteryCount; i++) {
//                        submitCmdWithIndex(BmtCmd.GET_BATTERY_INFO, i);
//                    }
//                    break;
//                case BmtCmd.GET_BATTERY_INFO:
//                    final int cabinet_index = DeviceHelper.getInt(result, BmtDataKey.CABINET_INDEX, -1);
//                    final int cabinetPositionIndex = DeviceHelper.getInt(result, PSKeyConstant.CABINET_POSITION_INDEX, -1);
//                    final int batteryIndex = DeviceHelper.getInt(result, BmtDataKey.INDEX, -1);
//                    final String batteryId = DeviceHelper.getString(result, BmtDataKey.ID_INFO, null);
//                    final String batteryVersion = DeviceHelper.getString(result, BmtDataKey.VERSION, "");
//
//                    final String displayName;
//                    if (!TextUtils.isEmpty(batteryId)) {
//                        displayName = batteryId;
//                    } else {
//                        String cabinetIndexKey = getString(R.string.ps_battery_overview_cabinet_battery);
//                        String cabinetIndexSuffix = getString(R.string.ps_hashtag_cabinet_battery_index);
//                        final int realIndex = cabinet_index + 1;
//                        final int realPosition = cabinetPositionIndex + 1;
//                        final String index = (realIndex < 10 ? ("0" + realIndex) : realIndex) + "_" + realPosition;
//                        displayName = Local.s(cabinetIndexKey).replace(cabinetIndexSuffix, index);
//                    }
//                    DDLog.d(TAG, "displayName=" + displayName);
//                    updateVersionInfoMap(KEY_VERSION_BATTERY, batteryIndex, displayName, batteryVersion);
//                    postVersionInfoUpdateEvent();
//                    break;
//                case BmtCmd.GET_CABINET_ALLINFO:
//                    if (StatusConstant.STATUS_SUCCESS != status) {
//                        postVersionInfoUpdateEvent();
//                        break;
//                    }
//                    final int cabinetCount = DeviceHelper.getInt(result, BmtDataKey.COUNT, 0);
//                    createVersionInfoMap(KEY_VERSION_CABINET, cabinetCount);
//                    for (int i = 0; i < cabinetCount; i++) {
//                        submitCmdWithIndex(BmtCmd.GET_CABINET_STATE, i);
//                    }
//                    break;
//                case BmtCmd.GET_CABINET_STATE:
//                    final int cabinetIndex = DeviceHelper.getInt(result, BmtDataKey.INDEX, -1);
//                    final String cabinetVersion = DeviceHelper.getString(result, BmtDataKey.VERSION, "");
//                    updateVersionInfoMap(KEY_VERSION_CABINET, cabinetIndex, cabinetVersion);
//                    postVersionInfoUpdateEvent();
//                    break;

                case BmtCmd.GET_FIRMWARES:
                    int type = DeviceHelper.getInt(result, BmtDataKey.TYPE, -1);
                    String versionStr = DeviceHelper.getString(result, BmtDataKey.VERSION, "");
                    DDLog.d(TAG, "type: " + type + "  versionStr： " + versionStr);
                    if (TextUtils.isEmpty(versionStr)) return;
                    String[] versionArr = versionStr.split(",");
                    int len = versionArr.length;
                    if (type == Mcu.Firmware.FirmwareType.MCU.getFirmwareType()) {
                        updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_MCU, versionStr);
                        postVersionInfoUpdateEvent();
                    } else if (type == Mcu.Firmware.FirmwareType.EV.getFirmwareType()) {
                        if (versionArr.length > 0) {
                            updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_EV, versionArr[0]);
                        }
                        if (versionArr.length > 1) {
                            String identifier = versionArr[1].contains(NEW_EV_BN) ? Local.s(getString(R.string.new_identifier)) : Local.s(getString(R.string.old_identifier));
                            updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_EV_BN, versionArr[1] + identifier);
                        }
                        postVersionInfoUpdateEvent();
                    } else if (type == Mcu.Firmware.FirmwareType.Sidecar.getFirmwareType()) {
                        updateVersionInfoMap(KEY_VERSION_COMMON, KEY_INDEX_COMMON_SIDECAR, versionStr);
                        postVersionInfoUpdateEvent();
                    } else if (type == Mcu.Firmware.FirmwareType.Cabinet.getFirmwareType()) {
                        int finalLen = len;
                        if (!TextUtils.isEmpty(versionArr[0]) && versionArr[0].contains(":")) {
                            finalLen = finalLen * 2;
                        }
                        createVersionInfoMap(KEY_VERSION_CABINET, finalLen, null);
                        int index = 0;
                        for (int i = 0; i < len; i++) {
                            String[] cabinetArr = versionArr[i].split(":");
                            if (cabinetArr.length > 1) {
                                updateVersionInfoMap(KEY_VERSION_CABINET, index, Local.s(getString(R.string.cabinet)) + " " + (i + 1) + " " + Local.s(getString(R.string.id)), cabinetArr[0]);
                                updateVersionInfoMap(KEY_VERSION_CABINET, index + 1, Local.s(getString(R.string.cabinet)) + " " + (i + 1), cabinetArr[1]);
                                index = index + 2;
                            } else {
                                updateVersionInfoMap(KEY_VERSION_CABINET, i, versionArr[i]);
                            }
                        }
                        postVersionInfoUpdateEvent();

                    } else if (type == Mcu.Firmware.FirmwareType.Invertor.getFirmwareType()) {
                        String supplier = versionArr[0].contains("@") ? versionArr[0].split("@")[0] : null;
                        createVersionInfoMap(KEY_VERSION_INVERTER, len, supplier);
                        for (int i = 0; i < len; i++) {
                            String realVersion = versionArr[i].contains("@") ? versionArr[i].split("@")[1] : versionArr[i];
                            updateVersionInfoMap(KEY_VERSION_INVERTER, i, realVersion);
                        }
                        postVersionInfoUpdateEvent();
                    } else if (type == Mcu.Firmware.FirmwareType.Battery.getFirmwareType()) {
                        createVersionInfoMap(KEY_VERSION_BATTERY, len, null);
                        for (int i = 0; i < len; i++) {
                            updateVersionInfoMap(KEY_VERSION_BATTERY, i, Local.s(getString(R.string.battery)) + " " + (i + 1), versionArr[i]);
                        }
                        postVersionInfoUpdateEvent();
                    }
                    break;
                default:
                    break;
            }
        });
    }

    private void postVersionInfoUpdateEvent() {
        EventBus.getDefault().post(new PSFirmWareVersionUpdateEvent(mVersionInfoMap, mPSDevice));
    }

    private void createVersionInfoMap(@NonNull final String versionType, final int count, final String supplier) {
        PSFirmWareVersionDetailBean psFirmWareVersionDetailBean = mVersionInfoMap.get(versionType);
        if (null == psFirmWareVersionDetailBean) {
            psFirmWareVersionDetailBean = new PSFirmWareVersionDetailBean(versionType, null, count);
            if (versionType.equals(KEY_VERSION_INVERTER) && !TextUtils.isEmpty(supplier)) {
                psFirmWareVersionDetailBean.setInverterSupplier(supplier);
            }
            mVersionInfoMap.put(versionType, psFirmWareVersionDetailBean);
        }
        List<KeyValueBean> items = psFirmWareVersionDetailBean.getItems();
        if (null == items) {
            items = new ArrayList<>();
            psFirmWareVersionDetailBean.setItems(items);
        }

        psFirmWareVersionDetailBean.setTotalItemCount(count);
        if (items.size() != count) {
            items.clear();
            KeyValueBean bean;
            for (int i = 0; i < count; i++) {
                bean = new KeyValueBean(String.valueOf(i), null);
                items.add(bean);
            }
        }
    }

    private void addVersionInfoMap(@NonNull final String versionType) {
        if (mVersionInfoMap != null) {
            PSFirmWareVersionDetailBean psFirmWareVersionDetailBean = mVersionInfoMap.get(versionType);
            if (psFirmWareVersionDetailBean != null) {
                List<KeyValueBean> items = psFirmWareVersionDetailBean.getItems();
                if (items != null) {
                    KeyValueBean bean = new KeyValueBean(String.valueOf(items.size()), null);
                    psFirmWareVersionDetailBean.addItem(bean);
                }
            }
        }
    }

    private void updateVersionInfoMap(@NonNull final String versionType, final int index, final String displayKey, final String versionInfo) {
        PSFirmWareVersionDetailBean psFirmWareVersionDetailBean = mVersionInfoMap.get(versionType);
        if (null == psFirmWareVersionDetailBean) {
            return;
        }

        List<KeyValueBean> items = psFirmWareVersionDetailBean.getItems();
        if (null == items) {
            return;
        }

        final int size = items.size();
        if (index < 0 || index >= size) {
            return;
        }

        items.get(index).setValue(versionInfo);
        items.get(index).setDisplayKey(displayKey);
        psFirmWareVersionDetailBean.markLoadItem();
    }

    private void updateVersionInfoMap(@NonNull final String versionType, final int index, final String versionInfo) {
        updateVersionInfoMap(versionType, index, null, versionInfo);
    }

    /**
     * 发送升级指令执行结果
     *
     * @param updateError 错误码
     */
    private void onUpdateChipResult(final int updateError) {
        if (Mcu.Chips.ChipsUpdateError.none.getCode() == updateError) {
//            changeViewByStatus(FirmwareStatus.LOADING, null);
            onUpdateProgressUpdate(false, 0, null);
            scheduleQueryUpdateProgress();
        } else {
            changeViewByStatus(FirmwareStatus.CAN_UPGRADE, null);
//            showUpdateChipsFailedDialog();
            showErrorToast();
        }
    }

    /**
     * 显示执行升级指令失败的对话框
     */
    private void showUpdateChipsFailedDialog() {
        AlertDialog.createBuilder(getContext())
                .setContent(getString(R.string.ps_firm_update_error_hint))
                .setOk(getString(R.string.know_it))
                .preBuilder().show();
    }

    /**
     * 开始轮询升级中的进度
     */
    private void scheduleQueryUpdateProgress() {
        cancelScheduleQueryUpdateProgress();
        mUpdateProgressTimer = Observable.interval(INTERVAL_UPDATE_PROGRESS_MILLIS, INTERVAL_UPDATE_PROGRESS_MILLIS, TimeUnit.MILLISECONDS)
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "onScheduleQueryUpdateProgress");
                        submitCmd(BmtCmd.GET_CHIPS_UPDATE_PROGRESS);
                    }
                });
    }

    /**
     * 取消轮询级中的进度
     */
    private void cancelScheduleQueryUpdateProgress() {
        if (mUpdateProgressTimer != null && !mUpdateProgressTimer.isUnsubscribed()) {
            mUpdateProgressTimer.unsubscribe();
            mUpdateProgressTimer = null;
        }
    }

    /**
     * 开始轮询连接设备
     */
    private void scheduleConnect() {
        cancelScheduleConnect();
        mConnectTimer = Observable.interval(0, INTERVAL_CONNECT_MILLIS, TimeUnit.MILLISECONDS)
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "onScheduleConnect");
                        BmtManager.getInstance().connectDevice(mPSDevice, true);
                    }
                });
    }

    /**
     * 取消轮询连接设备
     */
    private void cancelScheduleConnect() {
        if (mConnectTimer != null && !mConnectTimer.isUnsubscribed()) {
            mConnectTimer.unsubscribe();
            mConnectTimer = null;
        }
    }

    private void checkIfNeedPostReloadChipsStatusEvent() {
        final String deviceId = mDeviceId;
        if (TextUtils.isEmpty(deviceId)) {
            return;
        }

        if (FirmwareStatus.LOADING == mFirmwareStatus
                || FirmwareStatus.UPGRADING == mFirmwareStatus
                || FirmwareStatus.UPGRADE_FAILED == mFirmwareStatus
                || FirmwareStatus.UPGRADE_SUCCEED == mFirmwareStatus) {
            EventBus.getDefault().post(new BmtChipsStatusReloadEvent(deviceId, mSubcategory));
        }
    }

    @Override
    public boolean onBackPressed() {
        checkIfNeedPostReloadChipsStatusEvent();
        return super.onBackPressed();
    }

    /**
     * 升级中进度更新
     *
     * @param failed   是否失败
     * @param progress 当前进度
     */
    private void onUpdateProgressUpdate(boolean failed, int progress, List<Integer> errorList) {
        if (failed) {
            cancelScheduleQueryUpdateProgress();
            changeViewByStatus(FirmwareStatus.UPGRADE_FAILED, errorList);
            return;
        }

        if (FirmwareStatus.UPGRADING != mFirmwareStatus
                && FirmwareStatus.LOADING != mFirmwareStatus) {
            cancelScheduleQueryUpdateProgress();
            return;
        }

        final String progressStr = progress + " %";
        if (FirmwareStatus.LOADING == mFirmwareStatus) {
            changeViewByStatus(FirmwareStatus.UPGRADING, errorList);
            mBinding.progressBar.setMax(100);
            mBinding.tvProgress.setText(progressStr);
        }
        if (progress >= 0 && progress <= 100) {
            mBinding.tvProgress.setText(progressStr);
            mBinding.progressBar.setProgress(progress);

            if (100 == progress) {
                cancelScheduleQueryUpdateProgress();
                changeViewByStatus(FirmwareStatus.UPGRADE_SUCCEED, errorList);
            }
        }
    }

    /**
     * 查询到芯片升级状态
     *
     * @param chipsStatus
     */
    private void onGetChipsStatus(int chipsStatus) {
        if (Mcu.Chips.ChipsUpdateState.uptodate.getCode() == chipsStatus) {
            changeViewByStatus(FirmwareStatus.NORMAL, null);
        } else if (Mcu.Chips.ChipsUpdateState.updating.getCode() == chipsStatus) {
            changeViewByStatus(FirmwareStatus.LOADING, null);
            scheduleQueryUpdateProgress();
        } else if (Mcu.Chips.ChipsUpdateState.waitForUpdate.getCode() == chipsStatus) {
            changeViewByStatus(FirmwareStatus.CAN_UPGRADE, null);
        } else if (Mcu.Chips.ChipsUpdateState.forceUpdate.getCode() == chipsStatus) {
            changeViewByStatus(FirmwareStatus.CAN_UPGRADE, null);
        } else {
            changeViewByStatus(FirmwareStatus.UPGRADE_FAILED, null);
            showErrorToast();
        }
    }

    public void showPwdDialog() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(Local.s(getResources().getString(R.string.Confirm)))
                .setCancel(Local.s(getResources().getString(R.string.Cancel)))
                .setDefaultName("")
                .setContent(Local.s("Please input password"))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
                        if (TextUtils.isEmpty(string)) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s("Please input password"));
                            return;
                        }
                        if (!string.equals("32zhenbang")) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s("Please input correct password"));
                            return;
                        }
                        getDelegateActivity().addCommonFragment(PSDataModeFragment.newInstance(mDeviceId));
                        dialog.dismiss();
                    }
                })
                .preBuilder()
                .show();
    }

    /**
     * BMT状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        final String deviceId = event.getDeviceID();
        DDLog.i(TAG, "event.getConnect_status===" + event.getConnect_status());
        DDLog.i(TAG, "mTimeoutCount===" + mTimeoutCount);
        if (deviceId != null && deviceId.equals(mDeviceId) && event.getSubcategory().equals(mSubcategory)) {
            int status = event.getConnect_status();
            if (status == 1 && mTimeoutCount == 3) {
                cancelScheduleConnect();
                BmtManager.getInstance().startPolling();
//                mTimeoutCount = 0;
                DDLog.i(TAG, "mTimeoutCmd===" + mTimeoutCmd);
                if (!TextUtils.isEmpty(mTimeoutCmd)) {
                    switch (mTimeoutCmd) {
                        case BmtCmd.GET_CHIPS_STATUS:
                            DDLog.i(TAG, "重连成功, 调GET_CHIPS_STATUS");
                            submitCmd(BmtCmd.GET_CHIPS_STATUS);
                            break;
                        case BmtCmd.UPDATE_CHIPS:
                            DDLog.i(TAG, "重连成功, 调UPDATE_CHIPS");
                            submitCmd(BmtCmd.UPDATE_CHIPS);
                            break;
                        case BmtCmd.GET_CHIPS_UPDATE_PROGRESS:
                            DDLog.i(TAG, "重连成功, 重新轮询进度");
                            scheduleQueryUpdateProgress();
                            break;
                    }
//                    mTimeoutCmd = null;
                }
            }
        }
    }

    /**
     * 点击事件
     */
    public class OnClickHandler {

        public void onStatus() {
            if (mMultiClickCounter != null) {
                mMultiClickCounter.click();
            }
        }

        public void onVersion() {
            if (mVersionClickCounter != null) {
                mVersionClickCounter.click();
            }
        }

        public void onStartNow() {
            requestUpdateChips();
        }

        public void onResult() {
            if (FirmwareStatus.UPGRADE_FAILED == mFirmwareStatus) {
                mBinding.tvNote.setTextColor(getResColor(R.color.text_brand));
                mBinding.tvVersion.setVisibility(View.VISIBLE);
                requestUpdateChips();
            } else {
                removeSelf();
            }
        }

        public void onNotNow() {
            removeSelf();
        }
    }

    @Override
    public void removeSelf() {
        checkIfNeedPostReloadChipsStatusEvent();
        super.removeSelf();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppStateEvent(AppStatePreEvent event) {
        if (event.isBackground()) {
            getDelegateActivity().removeAllCommonFragment();
        }
    }

    /**
     * 页面状态
     */
    enum FirmwareStatus {
        NORMAL,  // 正常
        CAN_UPGRADE, // 可以更新
        LOADING,   // 更新加载中
        UPGRADING,  // 更新中
        OTHER_UPGRADING,  // 其他人在更新
        UPGRADE_SUCCEED,  // 更新成功
        UPGRADE_FAILED  // 更新失败
    }
}
