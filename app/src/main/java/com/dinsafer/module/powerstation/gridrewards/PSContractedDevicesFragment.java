package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.HomeManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsContractedDevicesBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSContractedDevicesModel;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.ErrorDeviceSignedEvent;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PSContractedDevicesFragment extends MyBaseFragment<FragmentPsContractedDevicesBinding> implements IDeviceCallBack {

    private List<CountryBean> mCountryList;
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;
    private BindMultiAdapter<PSContractedDevicesModel> mAdapter;
    private ArrayList<PSContractedDevicesModel> mData;

    private Map<String, String> mMcuIdMap = new HashMap<>();

    public static PSContractedDevicesFragment newInstance(List<CountryBean> countryBeans, FamilyBalanceContractInfo familyBalanceContractInfo) {
        PSContractedDevicesFragment fragment = new PSContractedDevicesFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PSKeyConstant.KEY_COUNTRIES, (Serializable) countryBeans);
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_contracted_devices;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.contracted_devices));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.btnNext.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(ContractSigningFragment.newInstance(mFamilyBalanceContractInfo));
        });
        setNextEnabled(false);
        initRv();
    }

    private void setNextEnabled(boolean enabled) {
        mBinding.btnNext.setEnabled(enabled);
        mBinding.btnNext.setAlpha(enabled ? 1f : 0.5f);
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mCountryList = (List<CountryBean>) bundle.getSerializable(PSKeyConstant.KEY_COUNTRIES);
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
        }
        getFamilyBalanceContractInfo();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mData != null && mData.size() > 0) {
            for (PSContractedDevicesModel devicesModel : mData) {
                Device device = devicesModel.getDevice();
                if (BmtUtil.isBmtDeviceHP5000(device)) {
                    device.unregisterDeviceCallBack(PSContractedDevicesFragment.this);
                }
            }
        }
        EventBus.getDefault().unregister(this);
    }

    private void initRv() {
        mBinding.rvDevice.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new BindMultiAdapter<>();
        mData = new ArrayList<>();
        mAdapter.setNewData(mData);
        mBinding.rvDevice.setAdapter(mAdapter);
    }

    private boolean hasSelected() {
        if (mData != null) {
            for (PSContractedDevicesModel devicesModel : mData) {
                if (devicesModel.isSelected()) {
                    return true;
                }
            }
        }
        return false;
    }

    private void getFamilyBalanceContractInfo() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinHome.getInstance().getFamilyBalanceContractInfo(HomeManager.getInstance().getCurrentHome().getHomeID(),
                new IDefaultCallBack2<FamilyBalanceContractInfoResponse.ResultBean>() {
                    @Override
                    public void onSuccess(FamilyBalanceContractInfoResponse.ResultBean resultBean) {
                        if (mData != null && mData.size() > 0) {
                            for (PSContractedDevicesModel devicesModel : mData) {
                                Device device = devicesModel.getDevice();
                                if (BmtUtil.isBmtDeviceHP5000(device)) {
                                    device.unregisterDeviceCallBack(PSContractedDevicesFragment.this);
                                }
                            }
                        }
                        mData.clear();
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        List<String> signedIDList = new ArrayList<>();
                        List<FamilyBalanceContractInfoResponse.ResultBean.SignedDevices> signedDevices = resultBean.getSigned_devices();
                        if (signedDevices != null) {
                            for (FamilyBalanceContractInfoResponse.ResultBean.SignedDevices signedDevice : signedDevices) {
                                signedIDList.add(signedDevice.getDevice_id());
                            }
                        }
                        List<Device> bmtDevices = BmtManager.getInstance().getNotDeletedBmtDeviceList();
                        int size = bmtDevices.size();
                        for (int i = 0; i < size; i++) {
                            Device device = bmtDevices.get(i);
                            String displayID = BmtUtil.isBmtDeviceHP5000(device) ? "" : (Local.s(getString(R.string.ID)) + ": " + device.getId());
                            PSContractedDevicesModel contractedDevicesModel = new PSContractedDevicesModel(displayID, device, true);
                            boolean isAreaMatch = isAreaMatch(DeviceHelper.getString(device, "countryCode", ""));
                            if (BmtUtil.isDeviceConnected(device)) {
                                if (BmtUtil.isDeviceConnected(device)) {  // 在线
                                    if (BmtUtil.isBmtDeviceHP5000(device)) {
                                        device.registerDeviceCallBack(PSContractedDevicesFragment.this);
                                        getMCUInfo(device);
                                    }
                                }
                            }
                            if (!isAreaMatch) {  // 国家不匹配
                                contractedDevicesModel.setStatus(PSContractedDevicesModel.MISMATCH_STATUS);
                            } else {  // 国家匹配
                                if (BmtUtil.isDeviceConnected(device)) {  // 在线
                                    int gridStatus = DeviceHelper.getInt(device, BmtDataKey.GRID_STATUS, 0);
                                    if (gridStatus == 2) { // 并网
                                        if (signedIDList.contains(device.getId())) { // 已签约
                                            contractedDevicesModel.setStatus(PSContractedDevicesModel.ENABLING_STATUS);
                                        } else {  // 可用
                                            contractedDevicesModel.setStatus(PSContractedDevicesModel.SELECTABLE_STATUS);
                                        }
                                    } else {  // 离网
                                        contractedDevicesModel.setStatus(PSContractedDevicesModel.OFF_GRID_STATUS);
                                    }
                                } else {  // 离线
                                    contractedDevicesModel.setStatus(PSContractedDevicesModel.OFFLINE_STATUS);
                                }
                            }
                            mData.add(contractedDevicesModel);
                        }
                        if (mData != null && mData.size() > 0) {
                            Collections.sort(mData, new ContractDeviceComparator());
                            setNextEnabled(true);
                            mData.get(mData.size() - 1).setShowLine(false);
                            mAdapter.notifyDataSetChanged();
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                    }
                });
    }

    private void getMCUInfo(Device device) {
        if (device == null) return;
        Map<String, Object> params = new HashMap<>();
        params.put(BmtDataKey.CMD, BmtCmd.GET_MCU_INFO);
        device.submit(params);
    }

    private boolean isAreaMatch(String countryCode) {
        if (TextUtils.isEmpty(countryCode) ||
                mCountryList == null || mCountryList.size() == 0) return false;
        String selectedCountryCode = mFamilyBalanceContractInfo.getCountry_code();
        if (TextUtils.isEmpty(selectedCountryCode) ||
                !selectedCountryCode.equals(countryCode)) return false;
        for (CountryBean countryBean : mCountryList) {
            String cbCode = countryBean.getCountry_code();
            if (!TextUtils.isEmpty(cbCode) && cbCode.equals(countryCode) && countryBean.isBalance_contract_support()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(cmd)) {
            return;
        }
        Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                if (cmd.equals(BmtCmd.GET_MCU_INFO) && result != null) {
                    String barcode = DeviceHelper.getString(result, PSKeyConstant.BAR_CODE, null);
                    if (!TextUtils.isEmpty(barcode)) {
                        mMcuIdMap.put(deviceId, barcode);
                        updateDeviceDisplayID(deviceId, barcode);
                    }
                }
            }
        });
    }


    private void updateDeviceDisplayID(String deviceId, String barcode) {
        if (mData != null) {
            for (PSContractedDevicesModel devicesModel : mData) {
                Device device = devicesModel.getDevice();
                if (device != null) {
                    if (device.getId().equals(deviceId)) {
                        if (mAdapter != null && TextUtils.isEmpty(devicesModel.getDisplayID())) {
                            devicesModel.setDisplayID(Local.s(getString(R.string.ID)) + ": " + barcode);
                            mAdapter.notifyDataSetChanged();
                        }
                        break;
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ErrorDeviceSignedEvent event) {
        getFamilyBalanceContractInfo();
    }

    public static class ContractDeviceComparator implements Comparator<PSContractedDevicesModel> {

        @Override
        public int compare(PSContractedDevicesModel o1, PSContractedDevicesModel o2) {
            int o1Status = o1.getStatus();
            int o2Status = o2.getStatus();
            if (o1Status == o2Status) {
                return 0;
            } else if (o1Status < o2Status) {
                return -1;
            } else {
                return 1;
            }
        }
    }
}
