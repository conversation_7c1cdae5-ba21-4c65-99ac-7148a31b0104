package com.dinsafer.module.powerstation.widget.schedule_mode_view;

import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemScheduledModeBinding;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;

public class ScheduledModeModel extends BindModel<ItemScheduledModeBinding> {

    private Context mContext;
    private int hour;
    private ScheduledModeBean mScheduledModeBean;
    private BindMultiAdapter<ScheduledModeModel> mScheduledModeAdapter;
    private int mEmergencyReserve = 30;
    private int mSmartReserve = 70;
    private boolean isChargeWithGrid;


    public ScheduledModeModel(Context context, int hour, ScheduledModeBean mScheduledModeBean, int mEmergencyReserve, int mSmartReserve, boolean isChargeWithGrid) {
        super(context);
        this.mContext = context;
        this.hour = hour;
        this.mScheduledModeBean = mScheduledModeBean;
        this.mEmergencyReserve = mEmergencyReserve;
        this.mSmartReserve = mSmartReserve;
        this.isChargeWithGrid = isChargeWithGrid;
    }

    public ScheduledModeModel(Context context, int hour, ScheduledModeBean scheduledModeBean,
                              BindMultiAdapter<ScheduledModeModel> scheduledModeAdapter, boolean isChargeWithGrid) {
        super(context);
        mContext = context;
        this.hour = hour;
        this.mScheduledModeBean = scheduledModeBean;
        this.mScheduledModeAdapter = scheduledModeAdapter;
        this.isChargeWithGrid = isChargeWithGrid;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_scheduled_mode;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemScheduledModeBinding binding) {
        if (mScheduledModeBean == null || mScheduledModeAdapter == null) return;
        binding.clParent.setBackgroundResource(mScheduledModeBean.isSelected() ? R.color.color_brand_light_01 : 0);
        binding.llCharge.setVisibility(isChargeWithGrid ? View.VISIBLE : View.GONE);
        int position = holder.getAdapterPosition();
        int finalPosition = mScheduledModeAdapter.getData().size() - 1;
        if (position == 0) {
            binding.viewSpaceTop.setVisibility(View.VISIBLE);
            binding.tvTime.setTranslationY(DensityUtil.dp2px(mContext, -2));
            binding.tvEndTime.setTranslationY(DensityUtil.dp2px(mContext, 8));
        } else if (position == finalPosition) {
            binding.tvTime.setTranslationY(DensityUtil.dp2px(mContext, -10));
            binding.viewSpaceBottom.setVisibility(View.VISIBLE);
            binding.tvEndTime.setVisibility(View.VISIBLE);
        } else {
            binding.tvTime.setTranslationY(DensityUtil.dp2px(mContext, -10));
            binding.tvEndTime.setTranslationY(DensityUtil.dp2px(mContext, 8));
        }
        int hour = mScheduledModeBean.getHour();
        String hourStr = hour < 10 ? ("0" + hour + ":00") : (hour + ":00");
        String endHourStr = (hour + 1) < 10 ? ("0" + (hour + 1) + ":00") : ((hour + 1) + ":00");
        binding.tvTime.setLocalText(hourStr);

        boolean normalTimeVisible = false;
        boolean isFirstSelected = false;
        int selectedStatus = -1;
        if (mScheduledModeBean.isSelected()) {
            selectedStatus = 0;
            if (position == 0) {
                normalTimeVisible = true;
                isFirstSelected = true;
            } else {
                ScheduledModeModel scheduledModeModel = mScheduledModeAdapter.getItem(position - 1);
                ScheduledModeBean lastModeBean = scheduledModeModel.getScheduledModeBean();
                normalTimeVisible = !lastModeBean.isSelected();
                isFirstSelected = !lastModeBean.isSelected();
            }
        }
        boolean endTimeVisible = false;
        boolean isFinalSelected = false;
        if (position == finalPosition) {
            endTimeVisible = true;
            isFinalSelected = mScheduledModeBean.isSelected();
        } else {
            ScheduledModeModel scheduledModeModel = mScheduledModeAdapter.getItem(position + 1);
            ScheduledModeBean nextModeBean = scheduledModeModel.getScheduledModeBean();
            endTimeVisible = mScheduledModeBean.isSelected() && !nextModeBean.isSelected();
            isFinalSelected = mScheduledModeBean.isSelected() && !nextModeBean.isSelected();
        }
        binding.tvTime.setVisibility(normalTimeVisible || position % 3 == 0 ? View.VISIBLE : View.INVISIBLE);
        binding.tvTime.setTextColor(normalTimeVisible ? getColor(R.color.color_brand_text) : getColor(R.color.color_white_03));
        binding.tvEndTime.setLocalText(endHourStr);
        binding.tvEndTime.setTextColor(mScheduledModeBean.isSelected() ? getColor(R.color.color_brand_text) : getColor(R.color.color_white_03));
        binding.tvEndTime.setVisibility(endTimeVisible ? View.VISIBLE : View.GONE);
        int mode = mScheduledModeBean.getMode();

        if (isFirstSelected && isFinalSelected) {
            selectedStatus = 3;
        } else if (isFirstSelected) {
            selectedStatus = 1;
        } else if (isFinalSelected) {
            selectedStatus = 2;
        }
        binding.viewData.resetStatus(mode, selectedStatus);
        binding.viewCharge.setBackground(getChargeGradientDrawable(mScheduledModeBean.getSectionType()));
        binding.viewDischarge.setBackground(getDisChargeGradientDrawable(mScheduledModeBean.getSectionType()));
        binding.viewCharge.post(new Runnable() {
            @Override
            public void run() {
                int viewChargeWidth = mode == 1 ? (int) (binding.llCharge.getMeasuredWidth() * mScheduledModeBean.getPercentage() / 100f) : 0;
                ValueAnimator animCharge = ValueAnimator.ofInt(binding.viewCharge.getMeasuredWidth(), viewChargeWidth);
                int viewDischargeWidth = mode == -1 ? (int) (binding.llDischarge.getMeasuredWidth() * mScheduledModeBean.getPercentage() / 100f) : 0;
                ValueAnimator animDischarge = ValueAnimator.ofInt(binding.viewDischarge.getMeasuredWidth(), viewDischargeWidth);
                animCharge.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator valueAnimator) {
                        int val = (int) valueAnimator.getAnimatedValue();
                        ViewGroup.LayoutParams layoutParams = binding.viewCharge.getLayoutParams();
                        layoutParams.width = val;
                        binding.viewCharge.setLayoutParams(layoutParams);
                    }
                });
                animDischarge.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator valueAnimator) {
                        int val = (int) valueAnimator.getAnimatedValue();
                        ViewGroup.LayoutParams layoutParams = binding.viewDischarge.getLayoutParams();
                        layoutParams.width = val;
                        binding.viewDischarge.setLayoutParams(layoutParams);
                    }
                });
                AnimatorSet set = new AnimatorSet();
                set.playTogether(animCharge, animDischarge);
                set.setInterpolator(new LinearInterpolator());
                set.setDuration(500);
                set.start();
            }
        });
    }

    private GradientDrawable getChargeGradientDrawable(int sectionType) {
        GradientDrawable gd = new GradientDrawable();
        gd.setOrientation(GradientDrawable.Orientation.RIGHT_LEFT);
        if (sectionType == ScheduledModeBean.ONE_SECTION) {
            gd.setColor(getColor(R.color.color_tip_01));
        } else if (sectionType == ScheduledModeBean.TWO_SECTION){
            gd.setColors(new int[]{getColor(R.color.color_tip_01),
                    getColor(R.color.price_scheduled_mode_second_color)});
        } else if (sectionType == ScheduledModeBean.THREE_SECTION){
            gd.setColors(new int[]{getColor(R.color.color_tip_01),
                    getColor(R.color.price_scheduled_mode_middle),
                    getColor(R.color.color_tip_03)});
        }
        gd.setCornerRadius(DensityUtil.dp2px(mContext, 6));
        gd.setAlpha(0x99);
        return gd;
    }

    private GradientDrawable getDisChargeGradientDrawable(int sectionType) {
        GradientDrawable gd = new GradientDrawable();
        gd.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT);
        if (sectionType == ScheduledModeBean.ONE_SECTION) {
            gd.setColor(getColor(R.color.color_tip_03));
        } else if (sectionType == ScheduledModeBean.TWO_SECTION){
            gd.setColors(new int[]{getColor(R.color.color_tip_03), getColor(R.color.price_scheduled_mode_discharge_second_color)});
        }
        gd.setCornerRadius(DensityUtil.dp2px(mContext, 6));
        gd.setAlpha(0x99);
        return gd;
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

    public Context getContext() {
        return mContext;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public ScheduledModeBean getScheduledModeBean() {
        return mScheduledModeBean;
    }

    public void setReserve(int emergencyReserve, int smartReserve) {
        this.mEmergencyReserve = emergencyReserve;
        this.mSmartReserve = smartReserve;
    }

    public void setScheduledModeBean(ScheduledModeBean scheduledModeBean) {
        this.mScheduledModeBean = scheduledModeBean;
    }

    public int getEmergencyReserve() {
        return mEmergencyReserve;
    }

    public void setEmergencyReserve(int emergencyReserve) {
        this.mEmergencyReserve = emergencyReserve;
    }

    public int getSmartReserve() {
        return mSmartReserve;
    }

    public void setSmartReserve(int smartReserve) {
        this.mSmartReserve = smartReserve;
    }

    public boolean isChargeWithGrid() {
        return isChargeWithGrid;
    }

    public void setChargeWithGrid(boolean chargeWithGrid) {
        isChargeWithGrid = chargeWithGrid;
    }
}
