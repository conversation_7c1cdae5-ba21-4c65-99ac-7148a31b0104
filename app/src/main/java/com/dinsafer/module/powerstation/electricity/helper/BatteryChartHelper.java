package com.dinsafer.module.powerstation.electricity.helper;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.util.BitMapUtil;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;

import java.util.ArrayList;
import java.util.List;

public class BatteryChartHelper extends BaseChartHelper {

    private final Context mContext;
    private final int bmtType;

    public BatteryChartHelper(Context context, int bmtType) {
        super(context);
        this.mContext = context;
        this.bmtType = bmtType;
    }

    public ArrayList<PSElectricityTypeBean> getFilterData(boolean isDualPowerOpen) {
        ArrayList<PSElectricityTypeBean> data = new ArrayList<>();
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_solar_energy_sel, mContext.getString(R.string.electricity_solar_energy), true));
            if (isDualPowerOpen) {
                data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_solar_dual_sel, mContext.getString(R.string.solar_dual), true));
            }
            data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_grid_energy_sel, mContext.getString(R.string.electricity_grid_energy), true));
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_solar_dual_sel, mContext.getString(R.string.solar_dual), true));
            data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_grid_energy_sel, mContext.getString(R.string.electricity_grid_energy), true));
        }
        return data;
    }

    public List<ILineDataSet> getLineDataSets(List<List<Float>> chartData, FlipCombinedChartView flipCombinedChartView,
                                              PlusMinusType plusMinusType, boolean isDualPowerOpen, boolean isMultiply) {
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            return getPowerCoreLineDataSets(chartData, flipCombinedChartView, plusMinusType, isDualPowerOpen, isMultiply);
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            return getPowerStoreLineDataSets(chartData, flipCombinedChartView, plusMinusType, isDualPowerOpen, isMultiply);
        } else {
            return new ArrayList<>();
        }
    }

    private List<ILineDataSet> getPowerCoreLineDataSets(List<List<Float>> chartData, FlipCombinedChartView flipCombinedChartView,
                                                        PlusMinusType plusMinusType, boolean isDualPowerOpen, boolean isMultiply) {
        ArrayList<Entry> entries = new ArrayList<Entry>();
        ArrayList<Entry> entries2 = new ArrayList<Entry>();
        ArrayList<Entry> entries3 = new ArrayList<Entry>();
        ArrayList<Entry> preBalEntries = new ArrayList<Entry>();
        ArrayList<Entry> preBalEntries2 = new ArrayList<Entry>();
        ArrayList<Entry> preBalEntries3 = new ArrayList<Entry>();

        for (int i = 0; i < chartData.size(); i++) {
            List<Float> sonData = chartData.get(i);

            float val = 0;
            if (plusMinusType == PlusMinusType.ALL) {
                if (isDualPowerOpen && sonData.size() > 4) {
                    val = val + (sonData.get(1) - (sonData.get(2) + sonData.get(3) + sonData.get(4)));
                } else {
                    val = val + (sonData.get(1) - (sonData.get(2) + sonData.get(3)));
                }
            } else {
                if (isMultiply) {
                    if (flipCombinedChartView.isFilterSelected(0)) {
                        val = val + sonData.get(2);
                    }
                    if (isDualPowerOpen && sonData.size() > 4) {
                        val = val + sonData.get(4);
                    }
                } else {
                    val = val + sonData.get(1);
                }
            }

            if (sonData.size() > 5 && sonData.get(5) == 14) {
                preBalEntries.add(new Entry(val, i));
                entries.add(new Entry(val, i, true));
            } else {
                preBalEntries.add(new Entry(0, i));
                entries.add(new Entry(val, i, false));
            }
        }

        if (isMultiply) {
            if (isDualPowerOpen) {
                for (int i = 0; i < chartData.size(); i++) {
                    List<Float> sonData = chartData.get(i);
                    float val = 0;
                    if (flipCombinedChartView.isFilterSelected(0)) {
                        val = val + sonData.get(2);
                    }
                    if (flipCombinedChartView.isFilterSelected(1) && sonData.size() > 4) {
                        val = val + sonData.get(4);
                    }
                    if (sonData.size() > 5 && sonData.get(5) == 14) {
                        preBalEntries2.add(new Entry(val, i));
                        entries2.add(new Entry(val, i, true));
                    } else {
                        preBalEntries2.add(new Entry(0, i));
                        entries2.add(new Entry(val, i, false));
                    }

                    if (flipCombinedChartView.isFilterSelected(2)) {
                        val = val + sonData.get(3);
                        if (sonData.size() > 5 && sonData.get(5) == 14) {
                            preBalEntries3.add(new Entry(val, i));
                            entries3.add(new Entry(val, i, true));
                        } else {
                            preBalEntries3.add(new Entry(0, i));
                            entries3.add(new Entry(val, i, false));
                        }
                    }
                }

            } else {
                for (int i = 0; i < chartData.size(); i++) {
                    List<Float> sonData = chartData.get(i);
                    float val = 0;
                    if (flipCombinedChartView.isFilterSelected(0)) {
                        val = val + sonData.get(2);
                    }
                    if (flipCombinedChartView.isFilterSelected(1)) {
                        val = val + sonData.get(3);
                    }
                    if (sonData.size() > 5 && sonData.get(5) == 14) {
                        preBalEntries2.add(new Entry(val, i));
                        entries2.add(new Entry(val, i, true));
                    } else {
                        preBalEntries2.add(new Entry(0, i));
                        entries2.add(new Entry(val, i, false));
                    }

                }
            }
        }
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        if (isMultiply) {
            SectionLineDataSet dayLineData1 = getSectionLineDataSet(entries, getColor(R.color.color_tip_04), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData1.setMode(LineDataSet.Mode.LINEAR);
            SectionLineDataSet dayLineData2;
            SectionLineDataSet dayLineData3 = null;
            if (isDualPowerOpen) {
                dayLineData2 = getSectionLineDataSet(entries2, getColor(R.color.color_tip_01), YAxis.AxisDependency.LEFT,
                        null, null, false, true);
                dayLineData2.setMode(LineDataSet.Mode.LINEAR);
                dayLineData3 = getSectionLineDataSet(entries3, getColor(R.color.color_tip_06), YAxis.AxisDependency.LEFT,
                        null, null, false, true);
                dayLineData3.setMode(LineDataSet.Mode.LINEAR);
            } else {
                dayLineData2 = getSectionLineDataSet(entries2, getColor(R.color.color_tip_06), YAxis.AxisDependency.LEFT,
                        null, null, false, true);
                dayLineData2.setMode(LineDataSet.Mode.LINEAR);
            }

            ArrayList<Entry> preBalData = new ArrayList<Entry>();
            if (flipCombinedChartView.isFilterSelected(0)) {
                lineDataSets.add(dayLineData1);
                preBalData = preBalEntries;
            }
            if (flipCombinedChartView.isFilterSelected(1)) {
                lineDataSets.add(dayLineData2);
                preBalData = preBalEntries2;
            }
            if (flipCombinedChartView.isFilterSelected(2) && dayLineData3 != null) {
                lineDataSets.add(dayLineData3);
                preBalData = preBalEntries3;
            }

            if (preBalData.size() > 0) {
                SectionLineDataSet preBalLineData2 = getSectionLineDataSet(preBalData, getColor(R.color.transparent)
                        , BitMapUtil.createBitmapWithDiagonalLines(getColor(R.color.color_tip_05), getColor(R.color.transparent))
                        , YAxis.AxisDependency.LEFT, false, true);
                preBalLineData2.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
                lineDataSets.add(preBalLineData2);
            }


        } else {
            SectionLineDataSet dayLineData = getSectionLineDataSet(entries, getColor(R.color.color_tip_03), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData.setMode(LineDataSet.Mode.LINEAR);
            lineDataSets.add(dayLineData);

            SectionLineDataSet preBalLineData = getSectionLineDataSet(preBalEntries, getColor(R.color.transparent)
                    , BitMapUtil.createBitmapWithDiagonalLines(getColor(R.color.color_tip_05), getColor(R.color.transparent))
                    , YAxis.AxisDependency.LEFT, false, true);
            preBalLineData.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
            lineDataSets.add(preBalLineData);
        }

        return lineDataSets;
    }

    private List<ILineDataSet> getPowerStoreLineDataSets(List<List<Float>> chartData, FlipCombinedChartView flipCombinedChartView,
                                                         PlusMinusType plusMinusType, boolean isDualPowerOpen, boolean isMultiply) {
        ArrayList<Entry> entries = new ArrayList<Entry>();
        ArrayList<Entry> entries2 = new ArrayList<Entry>();
        ArrayList<Entry> preBalEntries = new ArrayList<Entry>();
        ArrayList<Entry> preBalEntries2 = new ArrayList<Entry>();

        for (int i = 0; i < chartData.size(); i++) {
            List<Float> sonData = chartData.get(i);
            float val = 0;
            if (plusMinusType == PlusMinusType.ALL) {
                if (sonData.size() > 4) {
                    val = val + (sonData.get(1) - (sonData.get(3) + sonData.get(4)));
                } else {
                    val = val + (sonData.get(1) - (sonData.get(3)));
                }
            } else {
                if (isMultiply) {
                    if (flipCombinedChartView.isFilterSelected(0)) {
                        val = val + sonData.get(4);
                    }
                } else {
                    val = val + sonData.get(1);
                }
            }
            if (sonData.size() > 5 && sonData.get(5) == 14) {
                preBalEntries.add(new Entry(val, i));
                entries.add(new Entry(val, i, true));
            } else {
                preBalEntries.add(new Entry(0, i));
                entries.add(new Entry(val, i, false));
            }
        }

        if (isMultiply) {
            for (int i = 0; i < chartData.size(); i++) {
                List<Float> sonData = chartData.get(i);
                float val = 0;
                if (flipCombinedChartView.isFilterSelected(0) && sonData.size() > 4) {
                    val = val + sonData.get(4);
                }
                if (flipCombinedChartView.isFilterSelected(1)) {
                    val = val + sonData.get(3);
                    if (sonData.size() > 5 && sonData.get(5) == 14) {
                        preBalEntries2.add(new Entry(val, i));
                        entries2.add(new Entry(val, i, true));
                    } else {
                        preBalEntries2.add(new Entry(0, i));
                        entries2.add(new Entry(val, i, false));
                    }
                }
            }
        }
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        if (isMultiply) {
            SectionLineDataSet dayLineData1 = getSectionLineDataSet(entries, getColor(R.color.color_tip_01), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData1.setMode(LineDataSet.Mode.LINEAR);
            SectionLineDataSet dayLineData2 = getSectionLineDataSet(entries2, getColor(R.color.color_tip_06), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData2.setMode(LineDataSet.Mode.LINEAR);

            ArrayList<Entry> preBalData = new ArrayList<Entry>();
            if (flipCombinedChartView.isFilterSelected(0)) {
                lineDataSets.add(dayLineData1);
                preBalData = preBalEntries;
            }
            if (flipCombinedChartView.isFilterSelected(1)) {
                lineDataSets.add(dayLineData2);
                preBalData = preBalEntries2;
            }

            if (preBalData.size() > 0) {
                SectionLineDataSet preBalLineData = getSectionLineDataSet(preBalData, getColor(R.color.transparent)
                        , BitMapUtil.createBitmapWithDiagonalLines(getColor(R.color.color_tip_05), getColor(R.color.transparent))
                        , YAxis.AxisDependency.LEFT, false, true);
                preBalLineData.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
                lineDataSets.add(preBalLineData);
            }

        } else {
            SectionLineDataSet dayLineData = getSectionLineDataSet(entries, getColor(R.color.color_tip_03), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData.setMode(LineDataSet.Mode.LINEAR);
            lineDataSets.add(dayLineData);

            SectionLineDataSet preBalLineData = getSectionLineDataSet(preBalEntries, getColor(R.color.transparent)
                    , BitMapUtil.createBitmapWithDiagonalLines(getColor(R.color.color_tip_05), getColor(R.color.transparent))
                    , YAxis.AxisDependency.LEFT, false, true);
            preBalLineData.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
            lineDataSets.add(preBalLineData);
        }

        return lineDataSets;
    }

    public BarEntry getBarEntry(FlipCombinedChartView flipCombinedChartView, List<Float> sonData,
                                PlusMinusType plusMinusType, boolean isDualPowerOpen,
                                boolean isMultiply, int xIndex) {
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            if (plusMinusType == PlusMinusType.ALL) {
                float result = sonData.get(1) - (sonData.get(2) + sonData.get(3));
                if (isDualPowerOpen) {
                    if (sonData.size() > 4) {
                        result = result - sonData.get(4);
                    }
                }
                return new BarEntry(new float[]{result}, xIndex);
            } else {
                if (isMultiply) {
                    if (isDualPowerOpen) {
                        return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(2) : 0,
                                flipCombinedChartView.isFilterSelected(1) && sonData.size() > 4 ? sonData.get(4) : 0,
                                flipCombinedChartView.isFilterSelected(2) ? sonData.get(3) : 0}, xIndex);
                    } else {
                        return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(2) : 0,
                                flipCombinedChartView.isFilterSelected(1) ? sonData.get(3) : 0}, xIndex);
                    }
//                    yVals.get(xIndex).setVals(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(2) : 0,
//                            mFlipCombinedChartView.isFilterSelected(1) ? sonData.get(3) : 0});
                } else {
                    return new BarEntry(new float[]{sonData.get(1)}, xIndex);
//                    yVals.get(xIndex).setVals(new float[]{sonData.get(1)});
                }
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            if (plusMinusType == PlusMinusType.ALL) {
                float result = sonData.get(1) - (sonData.get(4) + sonData.get(3));
                return new BarEntry(new float[]{result}, xIndex);
//                yVals.get(xIndex).setVals(new float[]{sonData.get(1) - (sonData.get(2) + sonData.get(3))});
            } else {
                if (isMultiply) {
                    return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(4) : 0,
                            flipCombinedChartView.isFilterSelected(1) ? sonData.get(3) : 0}, xIndex);
//                    yVals.get(xIndex).setVals(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(2) : 0,
//                            mFlipCombinedChartView.isFilterSelected(1) ? sonData.get(3) : 0});
                } else {
                    return new BarEntry(new float[]{sonData.get(1)}, xIndex);
//                    yVals.get(xIndex).setVals(new float[]{sonData.get(1)});
                }
            }
        } else {
            return new BarEntry(new float[]{}, xIndex);
        }

    }

    public int[] getBarColors(boolean isSuccess, boolean isDualPowerOpen,
                              boolean isMultiply) {
        int[] barColors;
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            if (isSuccess) {
                if (isMultiply) {
                    if (isDualPowerOpen) {
                        barColors = new int[]{getColor(R.color.color_tip_04),
                                getColor(R.color.color_tip_01),
                                getColor(R.color.color_tip_06)};
                    } else {
                        barColors = new int[]{getColor(R.color.color_tip_04),
                                getColor(R.color.color_tip_06)};
                    }
                } else {
                    barColors = new int[]{getColor(R.color.color_tip_03)};
                }
            } else {
                if (isMultiply) {
                    if (isDualPowerOpen) {
                        barColors = new int[]{getColor(R.color.transparent),
                                getColor(R.color.transparent),
                                getColor(R.color.transparent)};
                    } else {
                        barColors = new int[]{getColor(R.color.transparent),
                                getColor(R.color.transparent)};
                    }
                } else {
                    barColors = new int[]{getColor(R.color.transparent)};
                }
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            if (isSuccess) {
                if (isMultiply) {
                    barColors = new int[]{getColor(R.color.color_tip_01),
                            getColor(R.color.color_tip_06)};
                } else {
                    barColors = new int[]{getColor(R.color.color_tip_03)};
                }
            } else {
                if (isMultiply) {
                    barColors = new int[]{getColor(R.color.transparent),
                            getColor(R.color.transparent)};
                } else {
                    barColors = new int[]{getColor(R.color.transparent)};
                }
            }
        } else {
            barColors = new int[]{};
        }
        return barColors;
    }
}
