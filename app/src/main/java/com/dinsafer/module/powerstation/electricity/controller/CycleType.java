package com.dinsafer.module.powerstation.electricity.controller;

import java.util.ArrayList;
import java.util.List;

public enum CycleType {
    DAY,
    WEEK,
    MONTH,
    YEAR,
    LIFETIME;

    public static List<CycleType> getTypeList() {
        List<CycleType> typeList = new ArrayList<>();
        typeList.add(DAY);
        typeList.add(WEEK);
        typeList.add(MONTH);
        typeList.add(YEAR);
        typeList.add(LIFETIME);
        return typeList;
    }

    public static List<CycleType> getTypeListExceptDay() {
        List<CycleType> typeList = new ArrayList<>();
        typeList.add(WEEK);
        typeList.add(MONTH);
        typeList.add(YEAR);
        typeList.add(LIFETIME);
        return typeList;
    }
}
