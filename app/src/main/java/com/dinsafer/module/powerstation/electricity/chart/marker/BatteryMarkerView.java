package com.dinsafer.module.powerstation.electricity.chart.marker;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.TimeUtil;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;

import java.util.ArrayList;
import java.util.List;

public class BatteryMarkerView extends CustomCombinedMarkerView {

    private int bmtType = -1;

    public BatteryMarkerView(Context context, int bmtType) {
        super(context);
        this.bmtType = bmtType;
    }

    @Override
    public void setMarker(List<Entry> entries, List<Highlight> highlights) {

        List<Entry> tempEntries = new ArrayList<>();
        tempEntries.addAll(entries);
        if (BaseChartFragment.mCycleType == CycleType.DAY) {
            // 去掉pre-balance的entry
            tempEntries.remove(tempEntries.size() - 1);
        }
        setBatteryMarker(tempEntries, highlights);
    }

    private void setBatteryMarker(List<Entry> entries, List<Highlight> highlights) {
        switch (BaseChartFragment.mCycleType) {
            case DAY:
                llValue.setVisibility(VISIBLE);
                if (plusMinusType == PlusMinusType.ALL) {
                    float value = entries.get(0).getVal();
                    float valAbs = Math.abs(value);
                    String valStr = ChartDataUtil.getPowerTransferVal(valAbs, valAbs, true) + getUnit(value, false);
                    tvKey.setLocalText(mContext.getString(R.string.power));
                    tvValue.setLocalText(valStr);
                    tvSubKey.setLocalText(mContext.getString(R.string.status));
                    tvSubValue.setLocalText(value < 0 ? mContext.getString(R.string.electricity_charged) :
                            mContext.getString(R.string.electricity_discharged));
                    llSubValue.setVisibility(!valStr.equals("0" + getUnit(valAbs, false)) ? VISIBLE : GONE);
                    llThirdValue.setVisibility(GONE);
                } else {
                    if (isMultiply) {
                        if (CollectionUtil.isListNotEmpty(selectedPositions)) {
                            if (bmtType == BmtUtil.BMT_POWER_CORE) {
                                setPowerCoreDayChargeMarker(entries);
                            } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
                                setPowerStoreDayChargeMarker(entries);
                            }
                        } else {
                            llSubValue.setVisibility(GONE);
                            llThirdValue.setVisibility(GONE);
                        }

                    } else {
                        llSubValue.setVisibility(GONE);
                        llThirdValue.setVisibility(GONE);
                        float value = entries.get(0).getVal();
                        String valStr = ChartDataUtil.getPowerTransferVal(value, value, true) + getUnit(value, false);
                        tvKey.setLocalText(mContext.getString(R.string.electricity_discharged));
                        tvValue.setLocalText(valStr);
                    }
                }
                int xIndex = entries.get(0).getXIndex();
                if (Math.abs(timeType) == 1) {
                    tvTime.setLocalText(TimeUtil.getHourMinuteStr(timestamp, timezone, xIndex * interval, timeType));
                } else {
                    tvTime.setLocalText(TimeUtil.minute2HourMinute(xIndex * interval));
                }

                if (null != entries.get(0).getData() && (boolean) entries.get(0).getData()) {
                    String preTime = tvTime.getText() + " (" + Local.s(mContext.getString(R.string.pre_balancing)) + ")";
                    tvTime.setText(preTime);
                }
                break;

            case WEEK:
            case MONTH:
            case YEAR:
            case LIFETIME:
                if (plusMinusType == PlusMinusType.ALL) {
                    float value = entries.get(0).getVal();
                    float valAbs = Math.abs(value);
                    String valStr = ChartDataUtil.getPowerTransferVal(valAbs, valAbs, false) + getUnit(valAbs, true);
                    tvKey.setLocalText(value < 0 ? mContext.getString(R.string.electricity_charged) :
                            mContext.getString(R.string.electricity_discharged));
                    tvValue.setLocalText(valStr);
                    tvTime.setLocalText(TimeUtil.minute2HourMinute(entries.get(0).getXIndex() * interval));
                    llSubValue.setVisibility(GONE);
                } else {
                    if (isMultiply) {
                        if (CollectionUtil.isListNotEmpty(selectedPositions)) {
                            if (bmtType == BmtUtil.BMT_POWER_CORE) {
                                setPowerCoreWee2LifeTimeChargeMarker(entries);
                            } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
                                setPowerStoreWee2LifeTimeChargeMarker(entries);
                            }
                        } else {
                            llValue.setVisibility(GONE);
                            llThirdValue.setVisibility(GONE);
                        }

                    } else {
                        llSubValue.setVisibility(GONE);
                        llThirdValue.setVisibility(GONE);
                        float value = entries.get(0).getVal();
                        String valStr = ChartDataUtil.getPowerTransferVal(value, value, false) + getUnit(value, true);
                        tvKey.setLocalText(mContext.getString(R.string.electricity_discharged));
                        tvValue.setLocalText(valStr);
                    }
                }
                int index = entries.get(0).getXIndex();
                if (BaseChartFragment.mCycleType == CycleType.WEEK) {
                    if (index >= BaseChartFragment.mWeeks.length) return;
                    tvTime.setLocalText(BaseChartFragment.mWeeks[index]);
                } else if (BaseChartFragment.mCycleType == CycleType.MONTH) {
                    if (index >= BaseChartFragment.months.length) return;
                    tvTime.setLocalText(BaseChartFragment.months[index]);
                } else if (BaseChartFragment.mCycleType == CycleType.YEAR) {
                    if (index >= BaseChartFragment.mYears.length) return;
                    tvTime.setLocalText(BaseChartFragment.mYears[index]);
                } else if (BaseChartFragment.mCycleType == CycleType.LIFETIME) {
                    if (index >= BaseChartFragment.lifetimes.length) return;
                    tvTime.setLocalText(BaseChartFragment.lifetimes[index]);
                }
                break;
        }
    }

    private void setPowerStoreWee2LifeTimeChargeMarker(List<Entry> entries) {
        BarEntry barEntry = (BarEntry) entries.get(0);
        float[] values = barEntry.getVals();
        float value1 = values[0];
        float value2 = values[1];
        llThirdValue.setVisibility(GONE);
        String valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, false) + getUnit(value1, true);
        String valStr2 = ChartDataUtil.getPowerTransferVal(value2, value2, false) + getUnit(value2, true);
        if (selectedPositions.size() == 2) {
            tvSubKey.setText(Local.s(mContext.getString(R.string.dual_power)));
            tvSubValue.setText(valStr1);
            tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
            tvValue.setText(valStr2);
            llSubValue.setVisibility(VISIBLE);
        } else {
            llSubValue.setVisibility(GONE);
            Integer pos = selectedPositions.get(0);
            if (pos == 0) {
                tvKey.setText(Local.s(mContext.getString(R.string.dual_power)));
                tvValue.setText(valStr1);
            } else {
                tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                tvValue.setText(valStr2);
            }
        }
    }

    private void setPowerCoreWee2LifeTimeChargeMarker(List<Entry> entries) {
        BarEntry barEntry = (BarEntry) entries.get(0);
        float[] values = barEntry.getVals();
        float value1 = values[0];
        float value2 = values[1];
        float value3 = 0;
        if (isDualPowerOpen) {
            value3 = values[2];
        }
        String valStr1 = "";
        String valStr2 = "";
        if (isDualPowerOpen) {
            if (selectedPositions.size() == 3) {
                float sunVal = value1 + value2;
                valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, false) + getUnit(value1, true);
                valStr2 = ChartDataUtil.getPowerTransferVal(value2, value2, false) + getUnit(value2, true);
                String valStr3 = ChartDataUtil.getPowerTransferVal(value3, value3, false) + getUnit(value3, true);
                tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                tvValue.setText(valStr1);
                tvThirdKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
                tvThirdValue.setText(valStr2);
                tvSubKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                tvSubValue.setText(valStr3);
                llSubValue.setVisibility(VISIBLE);
                llThirdValue.setVisibility(VISIBLE);
            } else if (selectedPositions.size() == 2) {
                llThirdValue.setVisibility(GONE);
                float sunVal = value1 + value2;
                if (selectedPositions.get(0) == 0 && selectedPositions.get(1) == 1) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, false) + getUnit(value1, true);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                    tvValue.setText(valStr1);
                    valStr2 = ChartDataUtil.getPowerTransferVal(value2, value2, false) + getUnit(value2, true);
                    tvSubKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
                    tvSubValue.setText(valStr2);
                } else if (selectedPositions.get(0) == 0 && selectedPositions.get(1) == 2) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, false) + getUnit(value1, true);
                    valStr2 = ChartDataUtil.getPowerTransferVal(value3, value3, false) + getUnit(value3, true);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                    tvValue.setText(valStr1);
                    tvSubKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                    tvSubValue.setText(valStr2);
                } else if (selectedPositions.get(0) == 1 && selectedPositions.get(1) == 2) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value2, value2, false) + getUnit(value2, true);
                    valStr2 = ChartDataUtil.getPowerTransferVal(value3, value3, false) + getUnit(value3, true);
                    tvSubKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
                    tvSubValue.setText(valStr1);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                    tvValue.setText(valStr2);
                }
                llSubValue.setVisibility(VISIBLE);
            } else if (selectedPositions.size() == 1) {
                llSubValue.setVisibility(GONE);
                llThirdValue.setVisibility(GONE);
                Integer pos = selectedPositions.get(0);
                if (pos == 0) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, false) + getUnit(value1, true);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                    tvValue.setText(valStr1);
                } else if (pos == 1) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value2, value2, false) + getUnit(value2, true);
                    tvKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
                    tvValue.setText(valStr1);
                } else {
                    valStr2 = ChartDataUtil.getPowerTransferVal(value3, value3, false) + getUnit(value3, true);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                    tvValue.setText(valStr2);
                }
            }
        } else {
            llThirdValue.setVisibility(GONE);
            valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, false) + getUnit(value1, true);
            valStr2 = ChartDataUtil.getPowerTransferVal(value2, value2, false) + getUnit(value2, true);
            if (selectedPositions.size() == 2) {
                tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                tvValue.setText(valStr1);
                tvSubKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                tvSubValue.setText(valStr2);
                llSubValue.setVisibility(VISIBLE);
            } else {
                llSubValue.setVisibility(GONE);
                Integer pos = selectedPositions.get(0);
                if (pos == 0) {
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                    tvValue.setText(valStr1);
                } else {
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                    tvValue.setText(valStr2);
                }
            }
        }
    }

    private void setPowerStoreDayChargeMarker(List<Entry> entries) {
        float value1 = entries.get(0).getVal();
        float value2 = 0f;
        String valStr1 = "";
        String valStr2 = "";
        float distance = 0f;
        if (entries.size() == 1) {
            llSubValue.setVisibility(GONE);
            llThirdValue.setVisibility(GONE);
            valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
            Integer pos = selectedPositions.get(0);

            if (pos == 0) {
                tvKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
            } else {
                tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
            }

            tvValue.setText(valStr1);
        } else if (entries.size() == 2) {
            llThirdValue.setVisibility(GONE);
            value2 = entries.get(1).getVal();
            valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
            tvSubKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
            tvSubValue.setText(valStr1);
            distance = value2 - value1;
            valStr2 = ChartDataUtil.getPowerTransferVal(distance, distance, true) + getUnit(distance, false);
            tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
            tvValue.setText(valStr2);
            llSubValue.setVisibility(VISIBLE);
        }
    }

    private void setPowerCoreDayChargeMarker(List<Entry> entries) {
        float value1 = entries.get(0).getVal();
        float value2 = 0f;
        String valStr1 = "";
        String valStr2 = "";
        float distance = 0f;
        if (entries.size() == 1) {
            llSubValue.setVisibility(GONE);
            llThirdValue.setVisibility(GONE);
            valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
            Integer pos = selectedPositions.get(0);
            if (isDualPowerOpen) {
                if (pos == 0) {
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                } else if (pos == 1) {
                    tvKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
                } else {
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                }
            } else {
                if (pos == 0) {
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                } else {
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                }
            }
            tvValue.setText(valStr1);
        } else if (entries.size() == 2) {
            llThirdValue.setVisibility(GONE);
            value2 = entries.get(1).getVal();
            if (isDualPowerOpen) {
                if (selectedPositions.get(0) == 0 && selectedPositions.get(1) == 1) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
                    float solarDiff = value2 - value1;
                    valStr2 = ChartDataUtil.getPowerTransferVal(solarDiff, solarDiff, true) + getUnit(solarDiff, false);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                    tvValue.setText(valStr1);
                    tvSubKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
                    tvSubValue.setText(valStr2);
                } else if (selectedPositions.get(0) == 0 && selectedPositions.get(1) == 2) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                    tvValue.setText(valStr1);
                    distance = value2 - value1;
                    valStr2 = ChartDataUtil.getPowerTransferVal(distance, distance, true) + getUnit(distance, false);
                    tvSubKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                    tvSubValue.setText(valStr2);
                } else if (selectedPositions.get(0) == 1 && selectedPositions.get(1) == 2) {
                    valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
                    tvSubKey.setText(Local.s(mContext.getString(R.string.solar_dual)));
                    tvSubValue.setText(valStr1);
                    distance = value2 - value1;
                    valStr2 = ChartDataUtil.getPowerTransferVal(distance, distance, true) + getUnit(distance, false);
                    tvKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                    tvValue.setText(valStr2);
                }
            } else {
                valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
                tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
                tvValue.setText(valStr1);
                distance = value2 - value1;
                valStr2 = ChartDataUtil.getPowerTransferVal(distance, distance, true) + getUnit(distance, false);
                tvSubKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
                tvSubValue.setText(valStr2);
            }
            llSubValue.setVisibility(VISIBLE);
        } else if (entries.size() == 3) {
            value2 = entries.get(1).getVal();
            float value3 = entries.get(2).getVal();
            valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
            tvKey.setText(Local.s(mContext.getString(R.string.power_station_solar)));
            tvValue.setText(valStr1);
            float solarDiff = value2 - value1;
            valStr2 = ChartDataUtil.getPowerTransferVal(solarDiff, solarDiff, true) + getUnit(solarDiff, false);
            tvThirdKey.setText(Local.s(Local.s(mContext.getString(R.string.solar_dual))));
            tvThirdValue.setText(valStr2);
            llSubValue.setVisibility(VISIBLE);
            distance = value3 - value2;
            String valStr3 = ChartDataUtil.getPowerTransferVal(distance, distance, true) + getUnit(distance, false);
            tvSubKey.setText(Local.s(mContext.getString(R.string.power_station_grid)));
            tvSubValue.setText(valStr3);
            llThirdValue.setVisibility(VISIBLE);
        }
    }
}
