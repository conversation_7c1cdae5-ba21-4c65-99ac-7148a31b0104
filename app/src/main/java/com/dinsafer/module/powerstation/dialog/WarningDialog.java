package com.dinsafer.module.powerstation.dialog;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogWarningBinding;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSWarningListModel;
import com.dinsafer.module.powerstation.adapter.WarningListAdapter;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.permission.BaseBottomSheetDialog;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/24 16:12
 * @description :
 */
public class WarningDialog extends BaseBottomSheetDialog<DialogWarningBinding> {

    public static final String TAG = WarningDialog.class.getSimpleName();

    private WarningListAdapter mWarningListAdapter;
    private ArrayList<WarningBean> mData;
    private WarningListener warningListener;
    private BindMultiAdapter<PSWarningListModel> mWarningAdapter;
    private List<PSWarningListModel> mWarningData;

    public static WarningDialog newInstance(ArrayList<WarningBean> data) {
        WarningDialog dialog = new WarningDialog();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(PSKeyConstant.KEY_LIST, data);
        dialog.setArguments(bundle);
        return dialog;
    }

    public void setWarningListener(WarningListener warningListener) {
        this.warningListener = warningListener;
    }

    public void setData(ArrayList<WarningBean> mData) {
        this.mData = mData;
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_warning;
    }

    @Override
    protected void initView() {
        super.initView();
        DDLog.d(TAG, "initView. ");
        setEnableDrag(false);
        mBinding.getRoot().setBackgroundResource(R.drawable.shape_bg_warning_dialog);
        final Bundle args = getArguments();
        if (null == args) {
            return;
        }

        mData = args.getParcelableArrayList(PSKeyConstant.KEY_LIST);
        initRv();
        mBinding.ivClose.setOnClickListener(v -> {
            if (warningListener != null) {
                warningListener.onClose();
            }
            dismiss();
        });

        mBinding.tvContactCustomer.setOnClickListener(v -> {
            if (warningListener != null) {
                warningListener.onContactCustomerSupport();
            }
            dismiss();
        });
    }

    private void initRv() {
        mBinding.rvWarning.setLayoutManager(new LinearLayoutManager(getContext()));
        mWarningAdapter = new BindMultiAdapter<>();
        mWarningData = new ArrayList<>();
        if (CollectionUtil.isListNotEmpty(mData)) {
            for (WarningBean warningBean : mData) {
                mWarningData.add(new PSWarningListModel(getContext(), warningBean));
            }
        }
        mWarningAdapter.setNewData(mWarningData);
        mBinding.rvWarning.setAdapter(mWarningAdapter);
        mWarningAdapter.setOnBindItemChildClickListener(new OnBindItemChildClickListener<PSWarningListModel>() {
            @Override
            public void onItemChildClick(View view, int position, PSWarningListModel model) {
                int viewId = view.getId();
                WarningBean warningBean = model.getWarningBean();
                switch (viewId) {
                    case R.id.tv_restart:
                        if (warningListener!=null) {
                            warningListener.onRestart(warningBean.getType());
                        }
                        dismiss();
                        break;

                    case R.id.tv_without:
                        if (warningListener!=null) {
                            warningListener.onWithout(warningBean.getType());
                        }
                        dismiss();
                        break;
                }
            }
        });

    }

    public interface WarningListener {
        void onClose();

        void onRestart(String type);

        void onWithout(String type);

        void onContactCustomerSupport();
    }
}
