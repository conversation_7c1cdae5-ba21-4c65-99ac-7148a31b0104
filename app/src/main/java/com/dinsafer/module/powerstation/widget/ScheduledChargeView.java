package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutScheduledChargeBinding;
import com.dinsafer.module.powerstation.adapter.ScheduledChargeBgAdapter;
import com.dinsafer.module.powerstation.adapter.ScheduledChargeFgAdapter;
import com.dinsafer.module.powerstation.bean.ScheduledChargeBgBean;
import com.dinsafer.module.powerstation.bean.ScheduledChargeFgBean;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.List;

public class ScheduledChargeView extends ConstraintLayout {
    private String TAG = ScheduledChargeView.class.getSimpleName();
    private Context mContext;
    private LayoutScheduledChargeBinding mBinding;
    private ScheduledChargeBgAdapter mScheduledChargeBgAdapter;
    private ScheduledChargeFgAdapter mScheduledChargeFgAdapter;
    private DragSelectTouchListener mTouchListener;
    private int mLastPosition = -1;

    public ScheduledChargeView(Context context) {
        this(context, null);
    }

    public ScheduledChargeView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScheduledChargeView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private boolean isSelected, isApplied;

    private void init(Context context) {
        this.mContext = context;
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_scheduled_charge, this, true);
        mBinding.rvBg.setLayoutManager(new LinearLayoutManager(mContext));
        mBinding.rvBg.post(() -> {
            mScheduledChargeBgAdapter = new ScheduledChargeBgAdapter(mBinding.rvBg.getHeight() / 24);
            List<ScheduledChargeBgBean> bgData = new ArrayList<>();
            for (int i = 1; i < 25; i++) {
                String time = i < 10 ? ("0" + i + ":00") : (i + ":00");
                bgData.add(new ScheduledChargeBgBean(time));
            }
            mScheduledChargeBgAdapter.setNewData(bgData);
            mBinding.rvBg.setAdapter(mScheduledChargeBgAdapter);
        });
        GridLayoutManager gridLayoutManager = new GridLayoutManager(mContext, 7);
        mBinding.rvFg.setLayoutManager(gridLayoutManager);
        ((SimpleItemAnimator) mBinding.rvFg.getItemAnimator()).setSupportsChangeAnimations(false);
        mBinding.rvFg.post(() -> {

            mTouchListener = new DragSelectTouchListener(false);
            mBinding.rvFg.addOnItemTouchListener(mTouchListener);

            mTouchListener.setSelectListener(new DragSelectTouchListener.onSelectListener() {
                @Override
                public void onUp() {
                    mLastPosition = -1;
                    for (ScheduledChargeFgBean chargeFgBean : mScheduledChargeFgAdapter.getData()) {
                        chargeFgBean.setSelected(false);
                    }
                    mScheduledChargeFgAdapter.notifyDataSetChanged();
                }

                @Override
                public void onStartChange(int position) {
                    mLastPosition = position;
                    ScheduledChargeFgBean chargeFgBean = mScheduledChargeFgAdapter.getItem(position);
                    isSelected = chargeFgBean.isSelected();
                    isApplied = chargeFgBean.isApplied();
                    chargeFgBean.setSelected(!isSelected);
                    chargeFgBean.setApplied(!isApplied);
                    mScheduledChargeFgAdapter.notifyItemChanged(position);
                }

                @Override
                public void onSelectChange(int start, int end, boolean selected) {
                    DDLog.i(TAG, "start===" + start + ", end===" + end);
                    if (mScheduledChargeFgAdapter == null || start < 0 || end >= mScheduledChargeFgAdapter.getData().size()) {
                        return;
                    }
                    if (mLastPosition > -1) {
                        if (mLastPosition % 2 == end % 2) {
                            if (mLastPosition < end) {
                                for (int i = mLastPosition + 2; i < end; i = i + 2) {
                                    updateItem(i);
                                }
                            } else if (mLastPosition > end) {
                                for (int i = mLastPosition - 2; i > end; i = i - 2) {
                                    updateItem(i);
                                }
                            }
                        }
                    }
                    mLastPosition = end;
                    updateItem(end);
                }
            });
            initRvFg();
        });
    }

    private void updateItem(int position) {
        if (mScheduledChargeFgAdapter == null) return;
        List<ScheduledChargeFgBean> scheduledChargeData = mScheduledChargeFgAdapter.getData();
        if (CollectionUtil.isListEmpty(scheduledChargeData) || position < 0 || position >=scheduledChargeData.size()) return;
        ScheduledChargeFgBean chargeFgBean = mScheduledChargeFgAdapter.getItem(position);
        chargeFgBean.setSelected(!isSelected);
        chargeFgBean.setApplied(!isApplied);
        mScheduledChargeFgAdapter.notifyItemChanged(position);
    }

    private void initRvFg() {
        mScheduledChargeFgAdapter = new ScheduledChargeFgAdapter(mBinding.rvFg.getHeight() / 24);
        mScheduledChargeFgAdapter.setSpanSizeLookup(new BaseQuickAdapter.SpanSizeLookup() {
            @Override
            public int getSpanSize(GridLayoutManager gridLayoutManager, int position) {
                if (position % 2 == 0) {
                    return 5;
                } else {
                    return 2;
                }
            }
        });

        mBinding.rvFg.setAdapter(mScheduledChargeFgAdapter);
        List<ScheduledChargeFgBean> data = new ArrayList<>();
        for (int i = 1; i < 25; i++) {
            String time = i < 10 ? ("0" + i + ":00") : (i + ":00");
            ScheduledChargeFgBean weekdaysBean = new ScheduledChargeFgBean(i, time, 0);
            ScheduledChargeFgBean weekendsBean = new ScheduledChargeFgBean(i, time, 1);
            data.add(weekdaysBean);
            data.add(weekendsBean);
        }
        mScheduledChargeFgAdapter.setNewData(data);
    }

    /**
     * 设置某时间应用
     *
     * @param indexList
     */
    public void setScheduledApplied(List<Integer> indexList) {
        if (mScheduledChargeFgAdapter == null) return;
        for (Integer index : indexList) {
            mScheduledChargeFgAdapter.getItem(index).setApplied(true);
            mScheduledChargeFgAdapter.notifyItemChanged(index);
        }
    }

    /**
     * 所有应用数据
     *
     * @return
     */
    public List<ScheduledChargeFgBean> getAppliedData() {
        if (mScheduledChargeFgAdapter == null) return null;
        return mScheduledChargeFgAdapter.getAppliedData();
    }

    /**
     * 所有weekdays应用数据
     *
     * @return
     */
    public List<ScheduledChargeFgBean> getWeekdaysAppliedData() {
        if (mScheduledChargeFgAdapter == null) return null;
        return mScheduledChargeFgAdapter.getWeekdaysAppliedData();
    }

    /**
     * 所有weekdays(用 1 和 0 标识)
     *
     * @return
     */
    public ArrayList<Integer> getWeekdaysData() {
        if (mScheduledChargeFgAdapter == null) return null;
        return mScheduledChargeFgAdapter.getWeekdaysData();
    }

    /**
     * 所有weekends应用数据
     *
     * @return
     */
    public List<ScheduledChargeFgBean> getWeekendsAppliedData() {
        if (mScheduledChargeFgAdapter == null) return null;
        return mScheduledChargeFgAdapter.getWeekendsAppliedData();
    }

    /**
     * 所有weekends(用 1 和 0 标识)
     *
     * @return
     */
    public ArrayList<Integer> getWeekendsData() {
        if (mScheduledChargeFgAdapter == null) return null;
        return mScheduledChargeFgAdapter.getWeekendsData();
    }
}
