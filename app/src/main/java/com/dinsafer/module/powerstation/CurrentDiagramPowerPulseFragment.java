package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentCurrentDiagramPowerPulseBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtShowUpdateDialogEvent;
import com.dinsafer.module.powerstation.event.ReOpenEvent;
import com.dinsafer.module.powerstation.settings.BatteryOverviewFragment;
import com.dinsafer.module.powerstation.widget.currentdiagramview.BatteryInfoBean;
import com.dinsafer.module.powerstation.widget.currentdiagramview.PowerPulseLoadSaturationView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.SettingInfoHelper;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;

public class CurrentDiagramPowerPulseFragment extends MyBaseFragment<FragmentCurrentDiagramPowerPulseBinding> implements IDeviceCallBack {

    private boolean isBSensorInputOn;
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private int mChipsStatus = 0;
    private String subCategory;
    private boolean isThreePhase;
    private int mPhaseCount;
    private List<Boolean> isOverLoadList = new ArrayList<>();

    private final BehaviorSubject<Map<String, Object>> mBatteryStatusSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mBatteryInfoSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mReserveModeSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mPTReserveSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mScheduledReserveSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mCustomScheduleSubject = BehaviorSubject.create();
    private final BehaviorSubject<Map<String, Object>> mChargeModeSubject = BehaviorSubject.create();

    private Subscriber<BatteryInfoBean> mSubscriber;
    private Subscriber<Map<String, Object>> mBatteryInfoSubscriber;
    private Subscriber<Map<String, Object>> mReserveModePTSubscriber;
    private Subscriber<Map<String, Object>> mReserveModeScheduledSubscriber;
    private Subscriber<Map<String, Object>> mReserveModeAISubscriber;
    private final Map<String, Object> mChargeModeMap = new HashMap<>();

    public static CurrentDiagramPowerPulseFragment newInstance(String deviceId, String subCategory) {
        CurrentDiagramPowerPulseFragment fragment = new CurrentDiagramPowerPulseFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_current_diagram_power_pulse;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        initParams();
        mBinding.loadSaturationView.setKeyText();
        if (!BmtUtil.isDeviceConnected(mPSDevice)) {
            checkOfflineChipsStatus();
        }
        isThreePhase = BmtUtil.isThreePhase(mPSDevice);
        mPhaseCount = isThreePhase ? 3 : 1;
        for (int i = 0; i < mPhaseCount; i++) {
            isOverLoadList.add(false);
        }
        initSubscribe();
        initBatteryInfoSubscriber();
        initReserveModePTSubscriber();
        initReserveModeScheduledSubscriber();
        initReserveModeAISubscriber();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.loadSaturationView.setOperateListener(new PowerPulseLoadSaturationView.OnOperateListener() {
            @Override
            public void onClickBattery(View view) {
                if (BmtUtil.isDeviceConnected(mPSDevice)) {
                    getDelegateActivity().addCommonFragment(BatteryOverviewFragment.newInstance(mDeviceId, mSubcategory));
                }
            }

            @Override
            public void onClickUpdate(View view) {
                if (SettingInfoHelper.getInstance().isAdmin()) {
                    if (TextUtils.isEmpty(mDeviceId) || mChipsStatus == 0) {
                        return;
                    }

                    EventBus.getDefault().post(new BmtShowUpdateDialogEvent(mDeviceId, mSubcategory, mChipsStatus));
                }
            }

            @Override
            public void onClickSwitch(View view) {
//                submitCmdInverterClose();
                if (SettingInfoHelper.getInstance().isAdmin()) {
                    BmtManager.getInstance().resetInverter(mPSDevice, true);
                }
            }
        });
    }

    @Override
    public void onDestroyView() {
        mBinding.loadSaturationView.stopAnim();
        EventBus.getDefault().unregister(this);
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        if (null != mSubscriber) {
            mSubscriber.unsubscribe();
        }
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
            mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
            mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
            if (null != mPSDevice) {
                mPSDevice.registerDeviceCallBack(this);
                subCategory = mPSDevice.getSubCategory();
            }
        }
    }

    private void initSubscribe() {
        mSubscriber = new Subscriber<BatteryInfoBean>() {
            @Override
            public void onCompleted() {
            }

            @Override
            public void onError(Throwable e) {
                e.printStackTrace();
            }

            @Override
            public void onNext(BatteryInfoBean batteryInfoBean) {
                mBinding.loadSaturationView.setBatteryInfo(batteryInfoBean);
                showOverLoadStatus();
            }
        };
        Observable.combineLatest(mBatteryInfoSubject, mReserveModeSubject, mPTReserveSubject,
                        mScheduledReserveSubject,
                        (batteryInfoMap, reserveModeMap, ptReserveMap, scheduledReserveMap) -> {
//                            int batteryWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.BATTERY_WAT, 0);
//                            int solarWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.SOLAR_WAT, 0);
//                            int gridWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.GRID_WAT, 0);
//                            int additionWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.ADDITIONAL_LOAD_WAT, 0);
//                            int otherWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.OTHER_LOAD_WAT, 0);
//                            int vehicleWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.VECHI_WAT, 0);
//                            int ip2Wat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.IP2_WAT, 0);
//                            int op2Wat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.OP2_WAT, 0);

                            int percentage = DeviceHelper.getInt(batteryInfoMap, PSKeyConstant.SOC, 0);
                            int reserveMode = DeviceHelper.getInt(reserveModeMap, BmtDataKey.RESERVE_MODE, 0);
                            int ptSmartReserve = DeviceHelper.getInt(ptReserveMap, BmtDataKey.SMART, 0);
                            int ptEmergencyReserve = DeviceHelper.getInt(ptReserveMap, BmtDataKey.EMERGENCY, 0);
                            int scheduledSmartReserve = DeviceHelper.getInt(scheduledReserveMap, BmtDataKey.SMART, 0);
                            int scheduledEmergencyReserve = DeviceHelper.getInt(scheduledReserveMap, BmtDataKey.EMERGENCY, 0);
                            int smartReserve = reserveMode == 2 ? scheduledSmartReserve : ptSmartReserve;
                            int emergencyReserve = reserveMode == 2 ? scheduledEmergencyReserve : ptEmergencyReserve;
//                            int evStatus = DeviceHelper.getInt(evStatusMap, BmtDataKey.ADVANCE_STATUS, 0);

//                            int mode = DeviceHelper.getInt(modeMap, PSKeyConstant.MODE, 0);

//                            final int chipsStatus = DeviceHelper.getInt(chipStatusMap, BmtDataKey.STATUS, -1);
//                            boolean needUpgrade = false;
//                            if (-1 != chipsStatus) {
//                                mChipsStatus = chipsStatus;
//                                final boolean showMarker = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//                                final boolean waitForUpdate = BmtUtil.isWaitForUpdate(chipsStatus);
//                                final boolean ignore = BmtManager.getInstance().isIgnoredUpgrade(mDeviceId);
//                                needUpgrade = waitForUpdate ? !ignore : showMarker;
//                            }
                            BatteryInfoBean batteryInfoBean = new BatteryInfoBean(percentage, reserveMode,
                                    smartReserve, emergencyReserve);
                            return batteryInfoBean;
                        }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mSubscriber);
    }

    private void initBatteryInfoSubscriber() {
        mBatteryInfoSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int percent = DeviceHelper.getInt(resultMap, PSKeyConstant.SOC, 0);
                int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                int reserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);
                if (percent > 100 || percent < 0) {
                    percent = 0;
                }

                BatteryInfoBean batteryInfoBean = new BatteryInfoBean(percent, reserveMode,
                        smartReserve, emergencyReserve);
                mBinding.loadSaturationView.setBatteryInfo(batteryInfoBean);
                showOverLoadStatus();
            }
        };
        Observable.combineLatest(mBatteryStatusSubject, mBatteryInfoSubject, mChargeModeSubject, (batteryStatusMap, batteryInfoMap, chargeModeMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(batteryStatusMap);
                    allMap.putAll(batteryInfoMap);
                    allMap.putAll(chargeModeMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mBatteryInfoSubscriber);
    }

    private void initReserveModePTSubscriber() {
        mReserveModePTSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int reserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);
                if (reserveMode == 1) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, reserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
            }
        };
        Observable.combineLatest(mReserveModeSubject, mPTReserveSubject, (reserveModeMap, ptReserveMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(reserveModeMap);
                    allMap.putAll(ptReserveMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModePTSubscriber);
    }

    private void initReserveModeScheduledSubscriber() {
        mReserveModeScheduledSubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int reserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);
                if (reserveMode == 2) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, reserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
            }
        };

        Observable.combineLatest(mReserveModeSubject, mScheduledReserveSubject, (reserveModeMap, scheduledReserveMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(reserveModeMap);
                    allMap.putAll(scheduledReserveMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModeScheduledSubscriber);
    }

    private void initReserveModeAISubscriber() {
        mReserveModeAISubscriber = new Subscriber<>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> resultMap) {
                int reserveMode = DeviceHelper.getInt(resultMap, BmtDataKey.RESERVE_MODE, 0);

                if (reserveMode == 3) {
                    int emergencyReserve = DeviceHelper.getInt(resultMap, BmtDataKey.EMERGENCY, 0);
                    int smartReserve = DeviceHelper.getInt(resultMap, BmtDataKey.SMART, 0);
                    Integer[] weekdays = (Integer[]) MapUtils.get(resultMap, BmtDataKey.WEEKDAYS, null);
                    if (weekdays != null) {
                        for (int weekday : weekdays) {
                            if (weekday == -128) {
                                emergencyReserve = DeviceHelper.getInt(mPSDevice, BmtDataKey.EMERGENCY_RESERVE, 0);
                                smartReserve = DeviceHelper.getInt(mPSDevice, BmtDataKey.SMART_RESERVE, 0);
                                break;
                            }
                        }
                    }
                    mChargeModeMap.put(BmtDataKey.RESERVE_MODE, reserveMode);
                    mChargeModeMap.put(BmtDataKey.SMART, smartReserve);
                    mChargeModeMap.put(BmtDataKey.EMERGENCY, emergencyReserve);
                    mChargeModeSubject.onNext(mChargeModeMap);
                }
            }
        };

        Observable.combineLatest(mReserveModeSubject, mCustomScheduleSubject, (reserveModeMap, scheduledReserveMap) -> {
                    Map<String, Object> allMap = new HashMap<>();
                    allMap.putAll(reserveModeMap);
                    allMap.putAll(scheduledReserveMap);
                    return allMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mReserveModeAISubscriber);
    }

    private void dealOverLoad(String deviceId, String subCategory, String cmd, Map<String, Object> result) {
        List<Integer> exceptions = (List<Integer>) MapUtils.get(result, PSKeyConstant.EXCEPTIONS, null);
        int index = (int) MapUtils.get(result, PSKeyConstant.INDEX, 0);
        if (index < 0 || index >= mPhaseCount) return;
        isOverLoadList.set(index, BmtUtil.isOverLoad(deviceId, subCategory, cmd, result));
        showOverLoadStatus();
    }

    private void showOverLoadStatus() {
        if (isOverLoadList.contains(true)) {
            mBinding.loadSaturationView.showSwitchStatus(true, false);
            mBinding.loadSaturationView.stopAnim();
        } else {
            mBinding.loadSaturationView.showSwitchStatus(false, false);
        }
    }

    private void submitCmd(String cmd) {
        final Map<String, Object> params = new HashMap<>();
        if (mPSDevice != null) {
            params.put(PSKeyConstant.CMD, cmd);
            mPSDevice.submit(params);
        }
    }

    /**
     * 关闭逆变器
     */
    private void submitCmdInverterClose() {
        if (mPSDevice != null) {
            BmtManager.getInstance().stopPolling();
            EventBus.getDefault().post(new ReOpenEvent(mDeviceId, mSubcategory));
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_INVERTER_OPEN);
            params.put(PSKeyConstant.ON, false);
            List<Integer> indexs = new ArrayList<>();
            int phaseCount = BmtUtil.isThreePhase(mPSDevice) ? 3 : 1;
            for (int i = 0; i < phaseCount; i++) {
                indexs.add(i);
            }
            params.put(PSKeyConstant.INDEXS, indexs);
            mPSDevice.submit(params);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGraphicUpdateEvent event) {
        if (!event.isGraphicCurrent()) {
            return;
        }
        final String deviceId = event.getDeviceId();
        final String deviceSub = event.getSubCategory();
        final String cmd = event.getCmd();
        final Map map = event.getData();

        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && deviceSub.equals(mSubcategory)
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);

            if (status == StatusConstant.STATUS_SUCCESS) {

                if (MapUtils.isNotEmpty(result)) {
                    switch (cmd) {
                        case DsCamCmd.GET_INVERTER_INPUT_INFO:

                            isBSensorInputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_INPUT_ON, false);
                            break;

                        case DsCamCmd.GET_INVERTER_OUTPUT_INFO:
                            boolean bSenorOutputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_OUTPUT_ON, false);
                            PowerStationRoot2Fragment.isAllBSensorInstalled = isBSensorInputOn || bSenorOutputOn;

                            break;

                        case BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO:
                            mBinding.loadSaturationView.setGlobalCurrent(isThreePhase, result);
                            mBatteryStatusSubject.onNext(result);
                            break;

                        case DsCamCmd.GET_BATTERY_ALLINFO:
                            mBatteryInfoSubject.onNext(result);
                            break;

                        case DsCamCmd.SYSTEM_EXCEPTION:
                        case DsCamCmd.INVERTER_EXCEPTION:
                        case DsCamCmd.GET_GLOBAL_EXCEPTIONS:
                        case BmtCmd.GET_INVERTER_INFO:
                            dealOverLoad(deviceId, deviceSub, cmd, result);
                            break;

                        case BmtCmd.GET_MODE:
                        case BmtCmd.GET_MODE_V2:
//                            mModeSubject.onNext(result);
                            int mode = DeviceHelper.getInt(result, PSKeyConstant.MODE, 0);
                            mBinding.loadSaturationView.setMode(mode);
                            break;

                        case BmtCmd.GET_CURRENT_RESERVE_MODE:  // 获取模式
                            mReserveModeSubject.onNext(result);
                            break;

                        case BmtCmd.GET_CHIPS_STATUS:
//                            mChipStatusSubject.onNext(result);
                            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
                            boolean needUpgrade = false;
                            if (-1 != chipsStatus) {
                                mChipsStatus = chipsStatus;
                                final boolean showMarker = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//                                final boolean waitForUpdate = BmtUtil.isWaitForUpdate(chipsStatus);
//                                final boolean ignore = BmtManager.getInstance().isIgnoredUpgrade(mDeviceId);
                                needUpgrade = showMarker;
                            }
                            mBinding.loadSaturationView.changeViewStateByUpdateState(needUpgrade && BmtUtil.isDeviceConnected(mPSDevice));
                            break;

                        case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                            mPTReserveSubject.onNext(result);
                            break;

                        case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                            mScheduledReserveSubject.onNext(result);
                            break;

                        case BmtCmd.GET_CUSTOM_SCHEDULEMODE:
                            mCustomScheduleSubject.onNext(result);
                            break;

                        case BmtCmd.GET_REGULATE_FREQUENCY_STATE:
                            int state = (int) MapUtils.get(result, PSKeyConstant.STATE, 0);
                            dealRegulateFrequency(state);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    private void dealRegulateFrequency(int state) {
        switch (state) {
            case 0:
            case 1:
                mBinding.loadSaturationView.setBalancingMode(LottieManager.AnimBalanceState.END);
                break;
            case 2:
            case 3:
            case 4:
            case 5:
                mBinding.loadSaturationView.setBalancingMode(LottieManager.AnimBalanceState.STATE);
                mBinding.loadSaturationView.setBalancingMode(LottieManager.AnimBalanceState.ING);
                break;
        }
        mBinding.loadSaturationView.setBalancingState(state);
    }


    /**
     * 离线状态下检查是否在更新
     */
    private void checkOfflineChipsStatus() {
        if (mPSDevice != null) {
            mChipsStatus = DeviceHelper.getInt(mPSDevice, "chipsStatus", Mcu.Chips.ChipsUpdateState.uptodate.getCode());
            boolean ignore = BmtManager.getInstance().isIgnoredUpgrade(mDeviceId, mSubcategory);
            if (BmtUtil.isShowUpdate(mChipsStatus) && !ignore && BmtUtil.isDeviceConnected(mPSDevice)) {
                mBinding.loadSaturationView.changeViewStateByUpdateState(true);
            }
        }
    }

    /**
     * BMT状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        final String deviceId = event.getDeviceID();
        if (deviceId != null && deviceId.equals(mDeviceId) && event.getSubcategory().equals(mSubcategory)) {
            int status = event.getConnect_status();
            if (status == -1) {
                mBinding.loadSaturationView.setOffline(true);
                checkOfflineChipsStatus();
            }
        }
    }

    /**
     * 重启逆变器/EV事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventReOpen(ReOpenEvent event) {
        mBinding.loadSaturationView.restartInverter();
    }

    @Override
    public void onCmdCallBack(String s, String subCategory, String s1, Map map) {

    }
}