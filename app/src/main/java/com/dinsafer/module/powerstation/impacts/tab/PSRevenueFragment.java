package com.dinsafer.module.powerstation.impacts.tab;

import static com.dinsafer.module.powerstation.ChartDataUtil.LIFETIME;
import static com.dinsafer.module.powerstation.ChartDataUtil.MONTH;
import static com.dinsafer.module.powerstation.ChartDataUtil.WEEK;
import static com.dinsafer.module.powerstation.ChartDataUtil.YEAR;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsRevenueBinding;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypeDialog;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypePopup;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.marker.RevenueMarkerView;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.impacts.ImpactStrategiesFragment;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.OnFragmentCreatedListener;
import com.dinsafer.util.StringUtil;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class PSRevenueFragment extends BaseChartFragment<RevenueChartModelController, FragmentPsRevenueBinding>
        implements IDeviceCallBack {

    private ElectricityCircleTypeDialog mCircleTypeDialog;
    private ElectricityCircleTypePopup mElectricityCircleTypePopup;
    private CustomCombinedMarkerView mMarkerView;
    private String mType = WEEK;
    private String timezone;
    private long offsetTime = 0L;
    private boolean isFirst = true;

    private static OnFragmentCreatedListener mCreatedListener;

    public static PSRevenueFragment newInstance(int position, OnFragmentCreatedListener createdListener) {
        PSRevenueFragment fragment = new PSRevenueFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_POSITION, position);
        fragment.setArguments(bundle);
        mCreatedListener = createdListener;
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_revenue;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        ImpactStrategiesFragment.sPSDevice.registerDeviceCallBack(this);
        mCycleType = CycleType.WEEK;
        mOffSet = 0;
        mMarkerView = new RevenueMarkerView(getContext());
        mMarkerView.setLimitLeft(DensityUtils.dp2px(getContext(), 24));
        mMarkerView.setLimitRight(DensityUtils.dp2px(getContext(), 24));


        mBinding.fcChart.setFlipChangeListener(new FlipCombinedChartView.OnFlipChangeListener() {
            @Override
            public void flipChange(int index, OperateOrientation orientation) {
                mIndex = index;
                if (orientation == OperateOrientation.LEFT) {
                    mOffSet = mOffSet - 1;
                } else if (orientation == OperateOrientation.RIGHT) {
                    mOffSet = mOffSet + 1;
                }
//                resetTimeLocal(orientation);
                setIvRightEnabled(mOffSet != 0);
                getStatisticData(true);
            }
        });
        Bundle bundle = getArguments();
        int position = bundle.getInt(PSKeyConstant.KEY_POSITION);
        if (mCreatedListener != null) {
            mCreatedListener.onCreated(inflateView, position);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            if (isFirst) {
                isFirst = false;
                initChart(mBinding.fcChart, mMarkerView, 83f);
                initRevenueType();
            }
            if (mCycleType != CycleType.LIFETIME) {
                setIvRightEnabled(mOffSet != 0);
            }
            getStatisticData(true);
        }
    }

    @Override
    public void onDestroyView() {
        if (ImpactStrategiesFragment.sPSDevice != null)
            ImpactStrategiesFragment.sPSDevice.unregisterDeviceCallBack(this);
        mCycleType = CycleType.DAY;
        mOffSet = 0;
        super.onDestroyView();
    }

    private void initRevenueType() {
        mData = new ArrayList<>();
        mData.add(new PSElectricityTypeBean(R.drawable.shape_is_revenue_home_saving_sel, getString(R.string.impact_strategies_home_savings), true));
        mData.add(new PSElectricityTypeBean(R.drawable.shape_is_revenue_vpp_sel, getString(R.string.impact_strategies_vpp_sold), true));
        mBinding.fcChart.setElectricityTypeData(mData);
        mBinding.fcChart.setRvFilterVisible(true);
    }

    @Override
    public void createChartModelController() {
        chartModelController = new RevenueChartModelController();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvOption.setOnClickListener(v -> {
            showElectricityCircleTypePopup();
        });
    }

    /**
     * 显示周期类型弹窗
     */
    private void showElectricityCircleTypePopup() {
        if (mElectricityCircleTypePopup == null) {
            mElectricityCircleTypePopup = new ElectricityCircleTypePopup(getContext(), ElectricityCircleTypePopup.IMPACTS_STRATEGIES_REVENUE_TYPE);
            mElectricityCircleTypePopup.setTypeSelectedListener((type, cycleType) -> {
                mCycleType = cycleType;
                mOffSet = 0;
                mBinding.tvOption.setLocalText(type);
                switch (mCycleType) {

                    case WEEK:
                        mType = WEEK;
                        break;

                    case MONTH:
                        mType = MONTH;
                        break;

                    case YEAR:
                        mType = YEAR;
                        break;

                    case LIFETIME:
                        mType = LIFETIME;
                        break;
                }
                offsetTime = 0L;
                setIvRightEnabled(false);
                setIvLeftEnabled(mCycleType != CycleType.LIFETIME);
                getStatisticData(true);
            });
            mElectricityCircleTypePopup.setOnDismissListener(() -> backgroundAlpha(1f));
        }
        if (mElectricityCircleTypePopup != null && !mElectricityCircleTypePopup.isShowing()) {
            mElectricityCircleTypePopup.showAtLocation(mBinding.tvOption,
                    mBinding.tvOption.getWidth() -
                            DensityUtil.dp2px(getContext(), 136), 0);
            backgroundAlpha(0.5f);
        }
    }

    private void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp = getDelegateActivity().getWindow().getAttributes();
        lp.alpha = f;
        getDelegateActivity().getWindow().setAttributes(lp);
    }

    @Override
    protected void getStatisticData(boolean showLoading) {
        Map<String, Object> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, BmtCmd.GET_STATS_REVENUE_V2);
        params.put(BmtDataKey.INTERVAL, mType);
        params.put(BmtDataKey.OFFSET, mOffSet);
        if (showLoading) {
            showTimeOutLoadinFramgment();
        }
        Map<String, Object> result = null;
        switch (mCycleType) {
            case DAY:
                result = mDayCache.get(mOffSet);
                break;

            case WEEK:
                result = mWeekCache.get(mOffSet);
                break;

            case MONTH:
                result = mMonthCache.get(mOffSet);
                break;

            case YEAR:
                result = mYearCache.get(mOffSet);
                break;
        }
        if (result == null) {
            ImpactStrategiesFragment.sPSDevice.submit(params);
        } else {
            long gmtTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0) * 1000;
            if (offsetTime == 0L) {
                offsetTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0) * 1000;
            }
            timezone = DeviceHelper.getString(result, BmtDataKey.TIMEZONE, "");
            String time = ChartDataUtil.getResetTime(gmtTime, timezone, BaseChartFragment.mCycleType);
            mBinding.tvTime.setLocalText(time);
            setChartData(BaseChartFragment.CHART_ELECTRICITY_REVENUE, result);
            mBinding.fcChart.setEmpty(CollectionUtil.isListEmpty((List<List<Float>>) result.get(BmtDataKey.DATA)));
            closeLoadingFragment();
        }
    }

    @Override
    protected void resetChart() {
        float leftYMaxVal = getYMax(CHART_ELECTRICITY_REVENUE, mChartData);
        String powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        mBinding.tvSumVal.setLocalText(ChartDataUtil.getPowerTransferVal(getSumVal(), getSumVal(), false) + ChartDataUtil.getPowerUnit(getSumVal(), true));
        mMarkerView.setPowerHourUnit(powerHourUnit);
        mMarkerView.setLeftYMaxVal(leftYMaxVal);
        mMarkerView.setDSTTransitionDay(mHourCount == DST_TIME_MINUTE);
        if (mHourCount == DST_TIME_MINUTE) {
            mMarkerView.setTimeType(1);
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            mMarkerView.setTimeType(-1);
        } else {
            mMarkerView.setTimeType(0);
        }
        mMarkerView.setTimestamp(mStartTime);
        mMarkerView.setTimezone(timezone);
        super.resetChart();
    }

    @Override
    protected void setDayChart() {

    }

    @Override
    protected void setWeekChart() {
        refreshWeek2Lifetime(mWeeks);
    }

    @Override
    protected void setMonthChart() {
        refreshWeek2Lifetime(months);
    }

    @Override
    protected void setYearChart() {
        refreshWeek2Lifetime(mYears);
    }

    @Override
    protected void setLifetimeChart() {
        refreshWeek2Lifetime(lifetimes);
    }

    @Override
    protected void setFailDayChart() {

    }

    @Override
    protected void setFailWeekChart() {

    }

    @Override
    protected void setFailMonthChart() {

    }

    @Override
    protected void setFailYearChart() {

    }

    @Override
    protected void setFailLifetimeChart() {

    }

    private void refreshWeek2Lifetime(String[] xLabels) {
        mMarkerView.setPlusMinusType(mPlusMinusType);
        mMarkerView.setMultiply(isMultiply);
        mMarkerView.setSelectedPositions(mBinding.fcChart.getSelectPositions());
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
//        for (int i = 0; i < xLabels.length; i++) {
//            yVals.add(new BarEntry(new float[]{0, 0}, i));
//        }
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            yVals.add(new BarEntry(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
                    mFlipCombinedChartView.isFilterSelected(1) ? sonData.get(2) : 0}, xIndex));
//            yVals.get(xIndex).setVals(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
//                    mFlipCombinedChartView.isFilterSelected(1) ? sonData.get(2) : 0});
        }

        BarDataSet barDataSetWeek = new BarDataSet(yVals, "");
        int[] colorsBar = new int[]{getColor(R.color.bmt_home_saving_color),
                getColor(R.color.color_tip_06)};
        barDataSetWeek.setColors(colorsBar);
        barDataSetWeek.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSetWeek.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSetWeek.setHighLightColor(getColor(R.color.color_white_03));
        barDataSetWeek.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSetWeek);
        BarData barData = new BarData(xVals, dataSets);
        CombinedData data = new CombinedData(xLabels);
        data.setData(barData);
        mBinding.fcChart.getDisplayCombinedChart(mIndex).setData(data);
        mBinding.fcChart.getDisplayCombinedChart(mIndex).invalidate();
    }

    private float getSumVal() {
        float sum = 0;
        List<Float> sumData = getSumData();
        for (Float val : sumData) {
            sum = sum + val;
        }
        return sum;
    }

    private List<Float> getSumData() {
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < mChartData.size(); i++) {
            List<Float> sonData = mChartData.get(i);
            float sum = 0;
            if (mFlipCombinedChartView.isFilterSelected(0)) {
                sum = sum + sonData.get(1);
            }
//            if (isBSensorInstalled) {
//                sum = sum + sonData.get(2);
//            }
            if (mFlipCombinedChartView.isFilterSelected(1)) {
                sum = sum + sonData.get(2);
            }
            sumData.add(sum);
        }
        Collections.sort(sumData);
        return sumData;
    }

    private static final long oneDayTimeTills = 1000L * 60 * 60 * 24;

    public void resetTimeLocal(OperateOrientation orientation) {
        if (TextUtils.isEmpty(timezone)) return;
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(offsetTime);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String title = "";
        switch (BaseChartFragment.mCycleType) {
            case DAY:
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills
                        : offsetTime + oneDayTimeTills;
                break;

            case WEEK:
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills * 7
                        : offsetTime + oneDayTimeTills * 7;
                break;

            case MONTH:
                int minusYear = month > 1 ? year : year - 1;
                int minusMonth = month > 1 ? month - 1 : 12;
                int days = DDDateUtil.getDaysByYearMonth(orientation == OperateOrientation.LEFT ? minusYear : year, orientation == OperateOrientation.LEFT ? minusMonth : month);
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills * days
                        : offsetTime + oneDayTimeTills * days;
                break;

            case YEAR:
                int dayYear = DDDateUtil.getDaysByYear(orientation == OperateOrientation.LEFT ? (year - 1) : year);
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills * dayYear
                        : offsetTime + oneDayTimeTills * dayYear;
                break;

            case LIFETIME:

                break;
        }
        calendar.setTimeInMillis(offsetTime);
        int offsetYear = calendar.get(Calendar.YEAR);
        int offsetMonth = calendar.get(Calendar.MONTH) + 1;
        Date startDate = DDDateUtil.getCurrentWeekDayStartOrEndTime(calendar, true);
        Date endDate = DDDateUtil.getCurrentWeekDayStartOrEndTime(calendar, false);
        switch (BaseChartFragment.mCycleType) {
            case DAY:
                if (BaseChartFragment.mOffSet == 0) {
                    title = getString(R.string.electricity_today);
                } else if (BaseChartFragment.mOffSet == -1) {
                    title = getString(R.string.electricity_yesterday);
                } else {
                    title = DDDateUtil.formatWithTimezone(offsetTime, timezone, "yyyy.MM.dd");
                }
                break;

            case WEEK:
                title = DDDateUtil.formatDate(startDate, "MM.dd") + "-" + DDDateUtil.formatDate(endDate, "MM.dd");
                break;

            case MONTH:
                title = offsetYear + "." + (offsetMonth < 10 ? ("0" + offsetMonth) : ("" + offsetMonth));
                break;

            case YEAR:
                title = offsetYear + "";
                break;

            case LIFETIME:
                title = DinSaferApplication.getAppContext().getString(R.string.electricity_lifetime);
                break;
        }
        mBinding.tvTime.setLocalText(title);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (ImpactStrategiesFragment.sPSDevice != null
                && StringUtil.isNotEmpty(ImpactStrategiesFragment.sPSDevice.getId())
                && StringUtil.isNotEmpty(deviceId)
                && deviceId.equals(ImpactStrategiesFragment.sPSDevice.getId())
                && subCategory.equals(ImpactStrategiesFragment.sPSDevice.getSubCategory())) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (!(isInit && getUserVisibleHint())) return;
            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        switch (cmd) {
                            case BmtCmd.GET_STATS_REVENUE_V2:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mBinding.fcChart.setEmpty(CollectionUtil.isListEmpty((List<List<Float>>) result.get(BmtDataKey.DATA)));
                                long gmtTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0) * 1000;
                                if (offsetTime == 0L) {
                                    offsetTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0) * 1000;
                                }
                                timezone = DeviceHelper.getString(result, BmtDataKey.TIMEZONE, "");
                                String time = ChartDataUtil.getResetTime(gmtTime, timezone, BaseChartFragment.mCycleType);
                                mBinding.tvTime.setLocalText(time);
                                if (CollectionUtil.isListEmpty((List<List<Float>>) result.get(BmtDataKey.DATA))) {
                                    mBinding.tvSumVal.setLocalText("-Wh");
                                    return;
                                }
                                setChartDataFromServer(CHART_ELECTRICITY_REVENUE, result);
                                break;
                        }
                    }
                });
            } else {
                switch (cmd) {
                    case BmtCmd.GET_STATS_REVENUE_V2:
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        mBinding.tvSumVal.setLocalText("-kWh");
                        mBinding.fcChart.setEmpty(true);
                        showErrorToast();
                        break;
                }
            }
        }
    }
}
