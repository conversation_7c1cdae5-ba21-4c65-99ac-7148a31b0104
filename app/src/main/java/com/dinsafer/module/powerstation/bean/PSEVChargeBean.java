package com.dinsafer.module.powerstation.bean;

public class PSEVChargeBean {

    public static final int LOWER_UTILITY_RATE = 1;
    public static final int SOLAR_ONLY = 2;
    public static final int SCHEDULE_CHARGE = 3;
    public static final int UNTIL_FULLY_CHARGED = 4;
    public static final int FIXED_QUANTITY = 5;
    public static final int FIXED_DURATION = 6;

    private boolean selected;
    private String title;
    private String subTitle;
    private int type;
    private int value;
    private String valueStr;
    // -1 未应用  0 应用中  1 已应用
    private int status = -1;
    private float alpha;
    private boolean isSameSelected = true;
    private boolean isNeedAnim;


    public PSEVChargeBean(String title, String subTitle, int type) {
        this.title = title;
        this.subTitle = subTitle;
        this.type = type;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        setSameSelected(this.selected == selected);
        this.selected = selected;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getValueStr() {
        return valueStr;
    }

    public void setValueStr(String valueStr) {
        this.valueStr = valueStr;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public float getAlpha() {
        return alpha;
    }

    public void setAlpha(float alpha) {
        this.alpha = alpha;
    }

    public boolean isSameSelected() {
        return isSameSelected;
    }

    public void setSameSelected(boolean sameSelected) {
        isSameSelected = sameSelected;
    }

    public boolean isNeedAnim() {
        return isNeedAnim;
    }

    public void setNeedAnim(boolean needAnim) {
        isNeedAnim = needAnim;
    }
}
