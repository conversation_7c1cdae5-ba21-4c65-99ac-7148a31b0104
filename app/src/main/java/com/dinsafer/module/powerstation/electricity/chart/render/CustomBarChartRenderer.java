package com.dinsafer.module.powerstation.electricity.chart.render;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Shader;

import com.dinsafer.module.powerstation.electricity.bean.CustomBarDataSet;
import com.dinsafer.util.DDLog;
import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.buffer.BarBuffer;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.dataprovider.BarDataProvider;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.renderer.BarChartRenderer;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.Utils;
import com.github.mikephil.charting.utils.ViewPortHandler;

import java.util.List;

/**
 *
 */
public class CustomBarChartRenderer extends BarChartRenderer {

    private Paint mHighlightCirclePaint;
    private int mOutCircleColor;
    private final RectF mBarShadowRectBuffer = new RectF();
    private int mRadius = 10;
    private Path mHighlightLinePath = new Path();
    private float mHighlightLineWidth = 1.0f;
    private float mHighlightBoarderWidth = 1.5f;
    private boolean isMultiply;

    public CustomBarChartRenderer(BarDataProvider chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {
        super(chart, animator, viewPortHandler);
        mHighlightCirclePaint = new Paint();
        mHighlightCirclePaint.setAntiAlias(true);
        mHighlightCirclePaint.setStyle(Paint.Style.FILL);
    }

    public BarDataProvider getChart() {
        return mChart;
    }

    @Override
    protected void drawDataSet(Canvas c, IBarDataSet dataSet, int index) {
        Transformer trans = mChart.getTransformer(dataSet.getAxisDependency());
        mBarBorderPaint.setColor(dataSet.getBarBorderColor());
        mBarBorderPaint.setStrokeWidth(Utils.convertDpToPixel(dataSet.getBarBorderWidth()));
        mShadowPaint.setColor(dataSet.getBarShadowColor());
        boolean drawBorder = dataSet.getBarBorderWidth() > 0f;
        float phaseX = mAnimator.getPhaseX();
        float phaseY = mAnimator.getPhaseY();
        // initialize the buffer
        BarBuffer buffer = mBarBuffers[index];
        buffer.setPhases(phaseX, phaseY);
        buffer.setBarSpace(dataSet.getBarSpace());
        buffer.setDataSet(index);
        buffer.setInverted(mChart.isInverted(dataSet.getAxisDependency()));

        buffer.feed(dataSet);

        trans.pointValuesToPixel(buffer.buffer);

        // draw the bar shadow before the values
        if (mChart.isDrawBarShadowEnabled()) {
            for (int j = 0; j < buffer.size(); j += 4) {

                if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2]))
                    continue;

                if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
                    break;

                c.drawRect(buffer.buffer[j], mViewPortHandler.contentTop(),
                        buffer.buffer[j + 2],
                        mViewPortHandler.contentBottom(), mShadowPaint);
            }
        }

        int stackSize = dataSet.getStackSize();
        int buffersize = buffer.size();
        // if multiple colors
        int colorSize = dataSet.getColors().size();
//        if (colorSize > 1) {
//            int pos = 0;
//            for (int j = 0; j < buffer.size(); j += 4) {
//                BarEntry e = dataSet.getEntryForIndex(j / (4 * stackSize));
//                if (j % (4 * stackSize) == 0) pos = 0;
//                if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2]))
//                    continue;
//                if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
//                    break;
//
//                // Set the color for the currently drawn value. If the index
//                // is out of bounds, reuse colors.
//                mRenderPaint.setColor(dataSet.getColor(j / 4));
//                float top = buffer.buffer[j + 1];
//                float bottom = buffer.buffer[j + 3];
//                float[] vals = e.getVals();
//                float y = vals[pos];
//                boolean isMax = isTopRound(vals, stackSize, pos);
//                boolean isMin = isBottomRound(vals, stackSize, pos);
//                Path path = roundRect(new RectF(buffer.buffer[j], top, buffer.buffer[j + 2],
//                                bottom), mRadius, mRadius, y > 0 && isMax, y > 0 && isMax,
//                        y < 0 && isMin, y < 0 && isMin);
//                c.drawPath(path, mRenderPaint);
//
//                if (drawBorder) {
//                    c.drawPath(path, mBarBorderPaint);
//                }
//                pos++;
//            }
//        } else {
//
//            mRenderPaint.setColor(dataSet.getColor());
//
//            for (int j = 0; j < buffer.size(); j += 4) {
//                BarEntry e = dataSet.getEntryForIndex(j / 4);
//                float y = e.getVal();
//                if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2]))
//                    continue;
//
//                if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
//                    break;
//
////                c.drawRect(buffer.buffer[j], buffer.buffer[j + 1], buffer.buffer[j + 2],
////                        buffer.buffer[j + 3], mRenderPaint);
//                float top = buffer.buffer[j + 1];
//                float bottom = buffer.buffer[j + 3];
//                Path path = roundRect(new RectF(buffer.buffer[j], top, buffer.buffer[j + 2],
//                                bottom), mRadius, mRadius, y > 0, y > 0,
//                        y < 0, y < 0);
//                c.drawPath(path, mRenderPaint);
//
//                if (drawBorder) {
//                    c.drawPath(path, mBarBorderPaint);
//                }
//            }
//        }

        for (int j = 0; j < buffer.size(); j += 4 * stackSize) {
            BarEntry e = dataSet.getEntryForIndex(j / (4 * stackSize));
            if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2]))
                continue;
            if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
                break;
            boolean isStack = dataSet.getStackSize() > 1;
            final float y1;
            final float y2;

            if (isStack) {
                y1 = e.getPositiveSum();
                y2 = -e.getNegativeSum();
            } else {
                y1 = e.getVal();
                y2 = 0f;
            }

            boolean tl = y1 > 0;
            boolean tr = y1 > 0;
            boolean bl = y1 < 0;
            boolean br = y1 < 0;
            if (isStack) {
                tl = y1 > 0;
                tr = y1 > 0;
                bl = y2 < 0;
                br = y2 < 0;
            }

            float top = buffer.buffer[j + 4 * (stackSize - 1) + 1];
            float bottom = buffer.buffer[j + 3];

            Path path = roundRect(new RectF(buffer.buffer[j], top, buffer.buffer[j + 2],
                            bottom), mRadius, mRadius, tl,
                    tr, bl, br);
            if (colorSize > 1) {
                float[] positions = new float[stackSize];
                for (int k = 0; k < stackSize; k++) {
                    positions[k] = (buffer.buffer[j + 4 * k + 1]-buffer.buffer[j + 4 * k + 3]) / (top-bottom);
                }
                float[] pos = new float[stackSize * 2];
                int[] colors = new int[stackSize * 2];
                int num = 0;
                for (int l = 0; l < positions.length; l++) {
                    colors[num] = dataSet.getColors().get(l % dataSet.getColors().size());
                    colors[num + 1] = dataSet.getColors().get(l % dataSet.getColors().size());
                    if (l == 0) {
                        pos[num] = 0f;
                        pos[num + 1] = positions[l];
                    } else {
                        pos[num] = positions[l - 1];
                        pos[num + 1] = positions[l - 1] + positions[l];
                    }
                    num = num + 2;
                }
                mRenderPaint.setShader(new LinearGradient(0, bottom,
                        0, top, colors,
                        pos, Shader.TileMode.CLAMP));
            } else {
                if (dataSet instanceof CustomBarDataSet) {
                    List<Integer> negativeColors = ((CustomBarDataSet) dataSet).getNegativeColors();
                    if (negativeColors!= null && negativeColors.size() > 0) {
                        if (e.getVal() >= 0) {
                            mRenderPaint.setColor(dataSet.getColor());
                        } else {
                            mRenderPaint.setColor(((CustomBarDataSet) dataSet).getNegativeColor());
                        }
                    } else {
                        mRenderPaint.setColor(dataSet.getColor());
                    }
                } else {
                    mRenderPaint.setColor(dataSet.getColor());
                }
            }
            c.drawPath(path, mRenderPaint);
        }

    }

    private boolean isTopRound(float[] vals, int stackSize, int pos) {
        for (int i = 0; i < stackSize; i++) {
            if (i > pos && vals[i] > 0) {
                return false;
            }
        }
        return true;
    }

    private boolean isBottomRound(float[] vals, int stackSize, int pos) {
        for (int i = 0; i < stackSize; i++) {
            if (i < pos && vals[i] < 0) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void drawHighlighted(Canvas c, Highlight[] indices) {
        BarData barData = mChart.getBarData();
        int setCount = barData.getDataSetCount();

        for (Highlight high : indices) {

            final int minDataSetIndex = high.getDataSetIndex() == -1
                    ? 0
                    : high.getDataSetIndex();
            final int maxDataSetIndex = high.getDataSetIndex() == -1
                    ? barData.getDataSetCount()
                    : (high.getDataSetIndex() + 1);
            if (maxDataSetIndex - minDataSetIndex < 1) continue;

            for (int dataSetIndex = minDataSetIndex;
                 dataSetIndex < maxDataSetIndex;
                 dataSetIndex++) {

                IBarDataSet set = barData.getDataSetByIndex(dataSetIndex);

                if (set == null || !set.isHighlightEnabled())
                    continue;

                float barspaceHalf = set.getBarSpace() / 2f;

                Transformer trans = mChart.getTransformer(set.getAxisDependency());

                mHighlightPaint.setColor(set.getHighLightColor());
                mHighlightPaint.setAlpha(set.getHighLightAlpha());

                int index = high.getXIndex();

                // check outofbounds
                if (index >= 0
                        && index < (mChart.getXChartMax() * mAnimator.getPhaseX()) / setCount) {

                    BarEntry e = set.getEntryForXIndex(index);

                    if (e == null || e.getXIndex() != index)
                        continue;

                    float groupspace = barData.getGroupSpace();
//                    boolean isStack = high.getStackIndex() < 0 ? false : true;
                    boolean isStack = set.getStackSize() > 1;

                    // calculate the correct x-position
                    float x = index * setCount + dataSetIndex + groupspace / 2f
                            + groupspace * index;

                    final float y1;
                    final float y2;

                    if (isStack) {
                        y1 = e.getPositiveSum();
                        y2 = -e.getNegativeSum();
                    } else {
                        y1 = e.getVal();
                        y2 = 0f;
                    }

                    prepareBarHighlight(x, y1, y2, barspaceHalf, trans);

//                    c.drawRect(mBarRect, mHighlightPaint);
// draw the lines
                    if (!isMultiply) {
                        float lineX = (mBarRect.left + mBarRect.right) / 2;
                        drawHighlightLines(c, lineX, mBarRect.top, set);
                        mHighlightCirclePaint.setColor(Color.WHITE);
                        c.drawCircle(lineX, mViewPortHandler.contentTop(), 10, mHighlightCirclePaint);
                        mHighlightCirclePaint.setColor(mOutCircleColor);
                        c.drawCircle(lineX, mViewPortHandler.contentTop(), 18, mHighlightCirclePaint);
                    }
                    boolean tl = y1 > 0;
                    boolean tr = y1 > 0;
                    boolean bl = y1 < 0;
                    boolean br = y1 < 0;
                    if (isStack) {
                        tl = y1 > 0;
                        tr = y1 > 0;
                        bl = y2 < 0;
                        br = y2 < 0;
                    }

                    Path path2 = null;
                    float top = Math.max(mBarRect.top, mViewPortHandler.contentTop());
                    float bottom = Math.min(mBarRect.bottom, mViewPortHandler.contentBottom());
                    if (top == bottom) return;
                    if (isStack && (e.getPositiveSum() > 0 && e.getNegativeSum() > 0)) {
                        path2 = roundRect(new RectF(mBarRect.left, top, mBarRect.right, bottom), mRadius, mRadius, tl,
                                tr, bl, br);
                    } else {
                        path2 = roundRectHighLight(new RectF(mBarRect.left, top, mBarRect.right, bottom), mRadius, mRadius, tl,
                                tr, bl, br);
                    }
                    mHighlightPaint.setStyle(Paint.Style.STROKE);
                    mHighlightPaint.setColor(Color.WHITE);
                    mHighlightPaint.setStrokeWidth(mHighlightBoarderWidth);
                    c.drawPath(path2, mHighlightPaint);
                }
            }
        }
    }

    protected void drawHighlightLines(Canvas c, float x, float y, IBarDataSet set) {

        // set color and stroke-width
        mHighlightPaint.setColor(set.getHighLightColor());
        mHighlightPaint.setStyle(Paint.Style.STROKE);
        mHighlightPaint.setStrokeWidth(mHighlightLineWidth);

        // create vertical path
        mHighlightLinePath.reset();
        mHighlightLinePath.moveTo(x, mViewPortHandler.contentTop());
        mHighlightLinePath.lineTo(x, mViewPortHandler.contentBottom());
        c.drawPath(mHighlightLinePath, mHighlightPaint);
    }

    private Path roundRect(RectF rect, float rx, float ry, boolean tl, boolean tr, boolean br, boolean bl) {
        float top = rect.top;
        float left = rect.left;
        float right = rect.right;
        float bottom = rect.bottom;
        Path path = new Path();
        if (rx < 0) {
            rx = 0;
        }
        if (ry < 0) {
            ry = 0;
        }
        float width = right - left;
        float height = bottom - top;
        if (rx > width / 2) {
            rx = width / 2;
        }
        if (ry > height / 2) {
            ry = height / 2;
        }
        float widthMinusCorners = (width - (2 * rx));
        float heightMinusCorners = (height - (2 * ry));

        path.moveTo(right, top + ry);
        if (tr) {
            //top-right corner
            path.rQuadTo(0, -ry, -rx, -ry);
        } else {
            path.rLineTo(0, -ry);
            path.rLineTo(-rx, 0);
        }
        path.rLineTo(-widthMinusCorners, 0);
        if (tl) {
            //top-left corner
            path.rQuadTo(-rx, 0, -rx, ry);
        } else {
            path.rLineTo(-rx, 0);
            path.rLineTo(0, ry);
        }
        path.rLineTo(0, heightMinusCorners);

        if (bl) {
            //bottom-left corner
            path.rQuadTo(0, ry, rx, ry);
        } else {
            path.rLineTo(0, ry);
            path.rLineTo(rx, 0);
        }

        path.rLineTo(widthMinusCorners, 0);
        if (br) {
            //bottom-right corner
            path.rQuadTo(rx, 0, rx, -ry);
        } else {
            path.rLineTo(rx, 0);
            path.rLineTo(0, -ry);
        }

        path.rLineTo(0, -heightMinusCorners);
        path.close();//Given close, last lineto can be removed.
        return path;
    }

    private Path pathHighlight;

    private Path roundRectHighLight(RectF rect, float rx, float ry, boolean tl, boolean tr, boolean br, boolean bl) {
        float top = rect.top;
        float left = rect.left;
        float right = rect.right;
        float bottom = rect.bottom;
        if (pathHighlight == null) {
            pathHighlight = new Path();
        } else {
            pathHighlight.reset();
        }
        if (rx < 0) {
            rx = 0;
        }
        if (ry < 0) {
            ry = 0;
        }
        float width = right - left;
        float height = bottom - top;
        if (rx > width / 2) {
            rx = width / 2;
        }
        if (ry > height / 2) {
            ry = height / 2;
        }
        float widthMinusCorners = (width - (2 * rx));
        float heightMinusCorners = (height - (2 * ry));
        if (tl) {
            pathHighlight.moveTo(right, top + ry);
            if (tr) {
                //top-right corner
                pathHighlight.rQuadTo(0, -ry, -rx, -ry);
            } else {
                pathHighlight.rLineTo(0, -ry);
                pathHighlight.rLineTo(-rx, 0);
            }
            pathHighlight.rLineTo(-widthMinusCorners, 0);
            if (tl) {
                //top-left corner
                pathHighlight.rQuadTo(-rx, 0, -rx, ry);
            } else {
                pathHighlight.rLineTo(-rx, 0);
                pathHighlight.rLineTo(0, ry);
            }
            pathHighlight.rLineTo(0, heightMinusCorners + ry);
            pathHighlight.moveTo(right, top + ry);
            pathHighlight.rLineTo(0, heightMinusCorners + ry);
        }

        if (bl) {
            pathHighlight.moveTo(left, top);
            pathHighlight.rLineTo(0, heightMinusCorners + ry);

            if (bl) {
                //bottom-left corner
                pathHighlight.rQuadTo(0, ry, rx, ry);
            } else {
                pathHighlight.rLineTo(0, ry);
                pathHighlight.rLineTo(rx, 0);
            }

            pathHighlight.rLineTo(widthMinusCorners, 0);
            if (br) {
                //bottom-right corner
                pathHighlight.rQuadTo(rx, 0, rx, -ry);
            } else {
                pathHighlight.rLineTo(rx, 0);
                pathHighlight.rLineTo(0, -ry);
            }

            pathHighlight.rLineTo(0, -heightMinusCorners - ry);
        }

        return pathHighlight;
    }

    public void setRadius(int radius) {
        this.mRadius = radius;
    }

    public int getOutCircleColor() {
        return mOutCircleColor;
    }

    public void setOutCircleColor(int mOutCircleColor) {
        this.mOutCircleColor = mOutCircleColor;
    }

    public void setHighlightLineWidth(float highlightLineWidth) {
        this.mHighlightLineWidth = highlightLineWidth;
    }

    public void setHighlightBoarderWidth(float highlightBoarderWidth) {
        this.mHighlightBoarderWidth = highlightBoarderWidth;
    }

    public boolean isMultiply() {
        return isMultiply;
    }

    public void setMultiply(boolean multiply) {
        isMultiply = multiply;
    }
}
