package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BlurMaskFilter;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.utils.AIColorUtil;

public class AIProgressBar extends View {

    private final int DEFAULT_HEIGHT;
    private final int DEFAULT_BACKGROUND_COLOR;
    private final int DEFAULT_PROGRESS_COLOR;
    private final int DEFAULT_SHADOW_COLOR;
    private final int DEFAULT_BORDER_COLOR;
    private final int LTR = 0;
    private final int RTL = 1;
    private int mPaddingLeft;
    private int mPaddingTop;
    private int mPaddingRight;
    private int mPaddingBottom;

    private Paint mBackgroundPaint;
    private Paint mProgressPaint;
    private Paint mBorderPaint;
    private Paint mShadowPaint;
    private Paint mBitmapPaint;

    private BlurMaskFilter mBlurMaskFilter;
    private RectF mBackgroundRectF;
    private RectF mProgressRectF;
    private RectF mBorderRectF;
    private RectF mShadowRectF;
    private Bitmap iconBitmap;
    private int direction;
    private int iconRes;
    private boolean isDrawBorder;
    private boolean isDrawIcon;
    private boolean isDrawShadow;
    private int backgroundColor;  // 进度条背景颜色
    private int progressColor;   // 进度条颜色
    private int borderColor;   // 进度条边框颜色
    private int shadowColor;   // 阴影颜色
    private int borderWidth;
    private int cornerRadius;
    private int shadowCornerRadius;
    private int shadowRadius;
    private int iconSpace;
    private int progressBarHeight;
    private float progress;
    private Matrix mBorderMatrix;
    private float horizontalOffset;

    public AIProgressBar(Context context) {
        this(context, null);
    }

    public AIProgressBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AIProgressBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        DEFAULT_HEIGHT = DensityUtil.dp2px(context, 6);
        DEFAULT_BACKGROUND_COLOR = context.getResources().getColor(R.color.transparent);
        DEFAULT_PROGRESS_COLOR = context.getResources().getColor(R.color.color_brand_primary);
        DEFAULT_SHADOW_COLOR = context.getResources().getColor(R.color.color_brand_light_01);
        DEFAULT_BORDER_COLOR = context.getResources().getColor(R.color.color_white_03);
        mBackgroundPaint = new Paint();
        mBackgroundPaint.setAntiAlias(true);
        mProgressPaint = new Paint();
        mProgressPaint.setAntiAlias(true);
        mBorderPaint = new Paint();
        mBorderPaint.setAntiAlias(true);
        mShadowPaint = new Paint();
        mShadowPaint.setAntiAlias(true);
        mShadowPaint.setStyle(Paint.Style.FILL);
        mBitmapPaint = new Paint();
        mBitmapPaint.setAntiAlias(true);
        mBackgroundRectF = new RectF();
        mProgressRectF = new RectF();
        mBorderRectF = new RectF();
        mShadowRectF = new RectF();
        initAttr(context, attrs);
    }

    private void initAttr(Context context, AttributeSet attrs) {

        TypedArray typedArray = context.getTheme().obtainStyledAttributes(
                attrs, R.styleable.AIProgressBar, 0, 0);
        direction = typedArray.getInt(R.styleable.AIProgressBar_ai_progress_direction, LTR);
        iconRes = typedArray.getResourceId(R.styleable.AIProgressBar_ai_progress_icon_res, R.drawable.icon_ai_mode_bar);
        backgroundColor = typedArray.getColor(R.styleable.AIProgressBar_ai_progress_background_color, DEFAULT_BACKGROUND_COLOR);
        progressColor = typedArray.getColor(R.styleable.AIProgressBar_ai_progress_color, DEFAULT_PROGRESS_COLOR);
        borderColor = typedArray.getColor(R.styleable.AIProgressBar_ai_progress_border_color, DEFAULT_PROGRESS_COLOR);
        shadowColor = typedArray.getColor(R.styleable.AIProgressBar_ai_progress_shadow_color, DEFAULT_PROGRESS_COLOR);
        isDrawBorder = typedArray.getBoolean(R.styleable.AIProgressBar_ai_progress_is_draw_border, false);
        isDrawIcon = typedArray.getBoolean(R.styleable.AIProgressBar_ai_progress_is_draw_icon, false);
        isDrawShadow = typedArray.getBoolean(R.styleable.AIProgressBar_ai_progress_is_draw_shadow, false);
        borderWidth = typedArray.getDimensionPixelSize(R.styleable.AIProgressBar_ai_progress_border_width, DensityUtil.dp2px(context, 1));
        cornerRadius = typedArray.getDimensionPixelSize(R.styleable.AIProgressBar_ai_progress_corner_radius, DensityUtil.dp2px(context, 6));
        shadowCornerRadius = typedArray.getDimensionPixelSize(R.styleable.AIProgressBar_ai_progress_shadow_corner_radius, DensityUtil.dp2px(context, 8));
        shadowRadius = typedArray.getDimensionPixelSize(R.styleable.AIProgressBar_ai_progress_shadow_radius, 2);
        iconSpace = typedArray.getDimensionPixelSize(R.styleable.AIProgressBar_ai_progress_icon_space, 0);
        progressBarHeight = typedArray.getDimensionPixelSize(R.styleable.AIProgressBar_ai_progress_height, DensityUtil.dp2px(context, 6));
        typedArray.recycle();
        iconBitmap = BitmapFactory.decodeResource(context.getResources(), iconRes);

        setLayerType(LAYER_TYPE_SOFTWARE, null);
        mBackgroundPaint.setStyle(Paint.Style.FILL);
        mBackgroundPaint.setColor(backgroundColor);
        mProgressPaint.setStyle(Paint.Style.FILL);
        mProgressPaint.setColor(progressColor);
        mBorderPaint.setColor(borderColor);
        mBorderPaint.setStyle(Paint.Style.STROKE);
        mBorderPaint.setStrokeWidth(borderWidth);
        mBorderMatrix = new Matrix();
        mBlurMaskFilter = new BlurMaskFilter(shadowRadius, BlurMaskFilter.Blur.NORMAL);
        mShadowPaint.setMaskFilter(mBlurMaskFilter);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int mode = MeasureSpec.getMode(heightMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);
        if (mode == MeasureSpec.UNSPECIFIED || mode == MeasureSpec.AT_MOST) {
            height = DEFAULT_HEIGHT;
        }
        setMeasuredDimension(getMeasuredWidth(), height);
        mPaddingLeft = getPaddingLeft();
        mPaddingTop = getPaddingTop();
        mPaddingRight = getPaddingRight();
        mPaddingBottom = getPaddingBottom();
        float centerY = getMeasuredHeight() / 2f;
        int offset = isDrawShadow ? shadowRadius : 0;
        horizontalOffset = isDrawShadow ? Math.max(shadowRadius, shadowCornerRadius / 2f) : 0;
        mBackgroundRectF.left = mPaddingLeft + horizontalOffset;
        mBackgroundRectF.top = centerY - progressBarHeight / 2f + offset;
        mBackgroundRectF.right = getMeasuredWidth() - mPaddingRight - horizontalOffset;
        mBackgroundRectF.bottom = centerY + progressBarHeight / 2f - offset;
    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);

        drawProgressBackground(canvas);

        if (isDrawShadow && progress > 0) {
            drawShadow(canvas);
        }

        if (progress > 0) {
            drawProgress(canvas);
        }

        if (isDrawBorder && progress > 0) {
            drawBorder(canvas);
        }
        if (isDrawIcon && progress > 0) {
            drawIcon(canvas);
        }
    }

    private void drawProgressBackground(Canvas canvas) {
        canvas.drawRoundRect(mBackgroundRectF, cornerRadius, cornerRadius, mBackgroundPaint);
    }

    private void drawShadow(Canvas canvas) {
        mShadowPaint.setColor((shadowColor));
        canvas.drawRoundRect(mShadowRectF, shadowCornerRadius, shadowCornerRadius, mShadowPaint);
    }

    private void drawProgress(Canvas canvas) {
        canvas.drawRoundRect(mProgressRectF, cornerRadius, cornerRadius, mProgressPaint);
    }

    private void drawBorder(Canvas canvas) {
        canvas.drawRoundRect(mBorderRectF, cornerRadius, cornerRadius, mBorderPaint);
    }

    private void drawIcon(Canvas canvas) {
        float drawBitmapX = 0f;
        float drawBitmapY = 0f;
        float iconWidth = iconBitmap.getWidth();
        float iconHeight = iconBitmap.getHeight();
        float centerY = (mProgressRectF.top + mProgressRectF.bottom) / 2f;
        if (direction == LTR) {
            drawBitmapX = mProgressRectF.right - iconWidth - iconSpace - (isDrawBorder ? borderWidth : 0);

        } else if (direction == RTL) {
            drawBitmapX = mProgressRectF.left + iconSpace + (isDrawBorder ? borderWidth : 0);
        }
        drawBitmapY = centerY - iconHeight / 2f;
        canvas.drawBitmap(iconBitmap, drawBitmapX, drawBitmapY, mBitmapPaint);
    }

    private void setProgressRect(float progress) {
        this.progress = progress;
        float halfBorderWidth = isDrawBorder ? borderWidth / 2f : 0;
        if (direction == LTR) {
            mProgressRectF.left = mBackgroundRectF.left;
            mProgressRectF.top = mBackgroundRectF.top;
            mProgressRectF.right = mBackgroundRectF.right * progress;
            if (mProgressRectF.right < mProgressRectF.left) {
                mProgressRectF.right = mProgressRectF.left;
            }
            mProgressRectF.bottom = mBackgroundRectF.bottom;
        } else if (direction == RTL) {
            mProgressRectF.left = mBackgroundRectF.right - mBackgroundRectF.right * progress;
            if (mProgressRectF.left < horizontalOffset) {
                mProgressRectF.left = horizontalOffset;
            }
            if (mProgressRectF.left > mBackgroundRectF.right) {
                mProgressRectF.left = mBackgroundRectF.right;
            }
            mProgressRectF.top = mBackgroundRectF.top;
            mProgressRectF.right = mBackgroundRectF.right;
            mProgressRectF.bottom = mBackgroundRectF.bottom;
        }
        mBorderRectF.left = mProgressRectF.left + halfBorderWidth;
        mBorderRectF.top = mProgressRectF.top + halfBorderWidth;
        mBorderRectF.right = mProgressRectF.right - halfBorderWidth;
        mBorderRectF.bottom = mProgressRectF.bottom - halfBorderWidth;

        mShadowRectF.left = mBorderRectF.left - shadowRadius;
        mShadowRectF.top = mBorderRectF.top - shadowRadius;
        mShadowRectF.right = mBorderRectF.right + shadowRadius;
        mShadowRectF.bottom = mBorderRectF.bottom + shadowRadius;
    }

    public void setProgress(float progress) {
        if (progress == this.progress) return;
        setProgressRect(progress);
        invalidate();
    }

    public void setProgress(float progress, int progressColor, int[] borderColors) {
        if (progress == this.progress) return;
        setProgressRect(progress);
        mProgressPaint.setShader(null);
        mProgressPaint.setColor(progressColor);
        LinearGradient borderGradient = new LinearGradient(mBorderRectF.left, mProgressRectF.bottom,
                mProgressRectF.right, mProgressRectF.top, borderColors, null, Shader.TileMode.CLAMP);
        mBorderPaint.setShader(borderGradient);
        invalidate();
    }

    public void setProgress(float progress, int progressColor, int[] borderColors,
                            boolean isDrawShadow, boolean isDrawBorder, boolean isDrawIcon, int alpha) {
        if (progress == this.progress && this.isDrawShadow == isDrawShadow
                && this.isDrawBorder == isDrawBorder && this.isDrawIcon == isDrawIcon) return;
        this.isDrawShadow = isDrawShadow;
        this.isDrawBorder = isDrawBorder;
        this.isDrawIcon = isDrawIcon;
        setProgressRect(progress);
        mProgressPaint.setShader(null);
        mProgressPaint.setColor(progressColor);
        mProgressPaint.setAlpha(alpha);
        LinearGradient borderGradient = new LinearGradient(mBorderRectF.left, mProgressRectF.bottom,
                mProgressRectF.right, mProgressRectF.top, borderColors, null, Shader.TileMode.CLAMP);
        mBorderPaint.setShader(borderGradient);
        invalidate();
    }

    public void setProgress(float progress, int[] progressColors) {
        if (progress == this.progress) return;
        setProgressRect(progress);
        LinearGradient progressGradient = new LinearGradient(mProgressRectF.left, mProgressRectF.top,
                mProgressRectF.right, mProgressRectF.top, progressColors, null, Shader.TileMode.CLAMP);
        mProgressPaint.setShader(progressGradient);
        invalidate();
    }

    public void setProgress(float progress, int[] progressColors,
                            boolean isDrawShadow, boolean isDrawBorder, boolean isDrawIcon) {
        if (progress == this.progress && this.isDrawShadow == isDrawBorder
                && this.isDrawBorder == isDrawBorder && this.isDrawIcon == isDrawIcon) return;
        this.isDrawShadow = isDrawShadow;
        this.isDrawBorder = isDrawBorder;
        this.isDrawIcon = isDrawIcon;
        setProgressRect(progress);
        LinearGradient progressGradient = new LinearGradient(mProgressRectF.left, mProgressRectF.top,
                mProgressRectF.right, mProgressRectF.top, progressColors, null, Shader.TileMode.CLAMP);
        mProgressPaint.setShader(progressGradient);
        invalidate();
    }

    public void setProgress(float progress, int[] progressColors, int[] borderColors) {
        if (progress == this.progress) return;
        setProgressRect(progress);
        LinearGradient progressGradient = new LinearGradient(mProgressRectF.left, mProgressRectF.top,
                mProgressRectF.right, mProgressRectF.top, progressColors, null, Shader.TileMode.CLAMP);
        mProgressPaint.setShader(progressGradient);
        LinearGradient borderGradient = new LinearGradient(mBorderRectF.left, mBorderRectF.bottom,
                mBorderRectF.right, mBorderRectF.top, borderColors, AIColorUtil.getAIColorPosition(), Shader.TileMode.CLAMP);
        mBorderPaint.setShader(borderGradient);
        invalidate();
    }

    public void setProgress(float progress, int[] progressColors, int[] borderColors,
                            boolean isDrawShadow, boolean isDrawBorder, boolean isDrawIcon) {
        if (progress == this.progress && this.isDrawShadow == isDrawBorder
                && this.isDrawBorder == isDrawBorder && this.isDrawIcon == isDrawIcon) return;
        this.isDrawShadow = isDrawShadow;
        this.isDrawBorder = isDrawBorder;
        this.isDrawIcon = isDrawIcon;
        setProgressRect(progress);
        LinearGradient progressGradient = new LinearGradient(mProgressRectF.left, mProgressRectF.top,
                mProgressRectF.right, mProgressRectF.top, progressColors, null, Shader.TileMode.CLAMP);
        mProgressPaint.setShader(progressGradient);
        float w = mBorderRectF.width();
        float h = mBorderRectF.height();
        float max = Math.max(w, h);
        float scaleX = w / max;
        float scaleY = h / max;
        LinearGradient borderGradient = new LinearGradient(mBorderRectF.left, mBorderRectF.top + max,
                mBorderRectF.left + max, mBorderRectF.top, borderColors, AIColorUtil.getAIColorPosition(), Shader.TileMode.CLAMP);
        mBorderMatrix.setScale(scaleX, scaleY);
        borderGradient.setLocalMatrix(mBorderMatrix);
        mBorderPaint.setShader(borderGradient);
        invalidate();
    }

    public int getShadowColor() {
        return shadowColor;
    }

    public void setShadowColor(int shadowColor) {
        this.shadowColor = shadowColor;
    }

    public void setProgressPaintAlpha(int alpha) {
        mProgressPaint.setAlpha(alpha);
    }
}
