package com.dinsafer.module.powerstation.ev;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.dialog.SeekBarDialog;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsEvSonChargeV2Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSEVChargeV2Model;
import com.dinsafer.module.powerstation.bean.PSEVChargeV2Bean;
import com.dinsafer.module.powerstation.event.EVBottomBehaviorEvent;
import com.dinsafer.module.powerstation.event.EVBottomBehaviorStateEvent;
import com.dinsafer.module.powerstation.event.SetEVChargeModeEvent;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.google.android.material.bottomsheet.BottomSheetBehavior;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class PSEVSonChargeV2Fragment extends MyBaseFragment<FragmentPsEvSonChargeV2Binding> {

    private int mBottomSheetBehaviorState = BottomSheetBehavior.STATE_COLLAPSED;
    // 0 普通充电  1 即时充电
    private int mFrom;
    private String mDeviceId;
    private String mSubcategory;
    private BindMultiAdapter<PSEVChargeV2Model> mEVChargeAdapter;
    private List<PSEVChargeV2Model> mEVChargeData;

    private PSEVChargeV2Bean mLowerUtilityRate;
    private PSEVChargeV2Bean mScheduledCharge;
    private PSEVChargeV2Bean mInstantChargeFull;
    private PSEVChargeV2Bean mInstantChargeFixed;
    private List<PSEVChargeV2Bean> mEVChargeModeList = new ArrayList<>();

    private PSEVChargeV2Model mFirstModel;
    private PSEVChargeV2Model mSecondModel;

    private int mAppliedPosition;
    private SeekBarDialog mSeekBarDialog;
    private float mCurrentOffset = 0f;
    private boolean isExpand;

    public static PSEVSonChargeV2Fragment newInstance(int from, String deviceId, String subcategory) {
        PSEVSonChargeV2Fragment fragment = new PSEVSonChargeV2Fragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_ev_son_charge_v2;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        initParam();
        if (mFrom == 0) {
            mInstantChargeFull = new PSEVChargeV2Bean(getString(R.string.ps_ev_instant_charge_title_1),
                    getString(R.string.ps_ev_instant_charge_content_1), PSEVChargeV2Bean.INSTANT_CHARGE_FULL);
            mEVChargeModeList.add(mInstantChargeFull);
            mInstantChargeFixed = new PSEVChargeV2Bean(getString(R.string.ps_ev_instant_charge_title_2),
                    getString(R.string.ps_ev_instant_charge_content_2), PSEVChargeV2Bean.INSTANT_CHARGE_FIXED);
            mEVChargeModeList.add(mInstantChargeFixed);
        } else if (mFrom == 1) {
            mLowerUtilityRate = new PSEVChargeV2Bean(getString(R.string.ps_ev_smart_charge_title_1),
                    getString(R.string.ps_ev_smart_charge_content_1), PSEVChargeV2Bean.LOWER_UTILITY_RATE);
            mEVChargeModeList.add(mLowerUtilityRate);
            mScheduledCharge = new PSEVChargeV2Bean(getString(R.string.ps_ev_smart_charge_title_3),
                    getString(R.string.ps_ev_smart_charge_content_3), PSEVChargeV2Bean.SCHEDULE_CHARGE);
            mEVChargeModeList.add(mScheduledCharge);
        }
        initRV();
    }

    public void resetShrinkData(int mode) {
        if (mEVChargeAdapter != null && mEVChargeData != null) {
            if ((mode == 3 || mode == 5) && (mEVChargeData.size() == 1 && mSecondModel != mEVChargeData.get(0))) {
                mEVChargeData.clear();
                mEVChargeData.add(mSecondModel);
            }
        }
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    private void initParam() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            bundle = getArguments();
            mFrom = bundle.getInt(PSKeyConstant.KEY_FROM);
            mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
            mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        }
    }

    private void initRV() {
        mEVChargeAdapter = new BindMultiAdapter<>();
        mEVChargeData = new ArrayList<>();
        mBinding.rvMode.setLayoutManager(new LinearLayoutManager(getContext()));
        if (mFrom == 0) {
            mFirstModel = new PSEVChargeV2Model(getContext(), mInstantChargeFull);
            mSecondModel = new PSEVChargeV2Model(getContext(), mInstantChargeFixed);
            mEVChargeData.add(mFirstModel);
//            mEVChargeData.add(mSecondModel);
        } else if (mFrom == 1) {
            mFirstModel = new PSEVChargeV2Model(getContext(), mLowerUtilityRate);
            mSecondModel = new PSEVChargeV2Model(getContext(), mScheduledCharge);
            mEVChargeData.add(mFirstModel);
//            mEVChargeData.add(mSecondModel);
        }
        mEVChargeAdapter.setOnBindItemClickListener(new OnBindItemClickListener<PSEVChargeV2Model>() {
            @Override
            public void onItemClick(View v, int position, PSEVChargeV2Model model) {
                if (mBottomSheetBehaviorState == BottomSheetBehavior.STATE_COLLAPSED) return;
                PSEVChargeV2Bean evChargeBean = model.getEvChargeBean();
                int type = evChargeBean.getType();
                if (evChargeBean.isSelected()) return;
                for (PSEVChargeV2Model psevChargeV2Model : mEVChargeData) {
                    PSEVChargeV2Bean chargeV2Bean = psevChargeV2Model.getEvChargeBean();
                    chargeV2Bean.setSelected(false);
                }
                evChargeBean.setSelected(true);
                mEVChargeAdapter.notifyDataSetChanged();
            }
        });

        mEVChargeAdapter.setOnBindItemChildClickListener(new OnBindItemChildClickListener<PSEVChargeV2Model>() {
            @Override
            public void onItemChildClick(View view, int position, PSEVChargeV2Model model) {
                int viewId = view.getId();
                PSEVChargeV2Bean evChargeBean = model.getEvChargeBean();
                int type = evChargeBean.getType();
                switch (viewId) {
                    case R.id.tv_operate:
                        if (type == PSEVChargeV2Bean.SCHEDULE_CHARGE) { //  计划充电
                            mAppliedPosition = 2;
                            getDelegateActivity().addCommonFragment(PSScheduledChargeFragment.newInstance(mDeviceId, mSubcategory));
                        } else if (type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) {  // 定量即时充电
                            showSeekBarDialog(evChargeBean, position);
                        }
                        break;

                    case R.id.ll_status:
                        if (evChargeBean.getStatus() == -1) {
                            applyCharge(evChargeBean, position, false);
                        }
                        break;
                }
            }
        });
        mEVChargeAdapter.setNewData(mEVChargeData);
        mBinding.rvMode.setAdapter(mEVChargeAdapter);
    }

    /**
     * 获取应用view高度
     */
    public int getAppliedView(int position) {
        if (mEVChargeAdapter == null) return 0;
        if (position < 0) position = 0;
        if (position >= mEVChargeData.size()) position = mEVChargeData.size() - 1;
        View view = mEVChargeAdapter.getViewByPosition(mBinding.rvMode, position, R.id.cl_parent);
        DDLog.i(TAG, "viewHeight=getType==" + mEVChargeAdapter.getItem(position).getEvChargeBean().getType());
        return view != null ? view.getMeasuredHeight() : 0;
    }

    /**
     * 应用
     *
     * @param evChargeBean
     * @param position
     */
    private void applyCharge(PSEVChargeV2Bean evChargeBean, int position, boolean isLoading) {
        if (!isLoading) {
            evChargeBean.setStatus(0);
            mEVChargeAdapter.notifyDataSetChanged();
        }
        EventBus.getDefault().post(new SetEVChargeModeEvent(mFrom, evChargeBean, isLoading));
    }

    private void showSeekBarDialog(PSEVChargeV2Bean evChargeBean, int position) {
        if (mSeekBarDialog == null) {
            mSeekBarDialog = new SeekBarDialog(getContext(), evChargeBean.getMaxValue());
            mSeekBarDialog.setOperateListener(new SeekBarDialog.OnOperateListener() {
                @Override
                public void onClickLeft(View view, SeekBarDialog seekBarDialog, int progress) {
                    seekBarDialog.dismiss();
                }

                @Override
                public void onClickRight(View view, SeekBarDialog seekBarDialog, int progress) {
                    evChargeBean.setTempValue(progress);
//                    if (progress != evChargeBean.getValue()) {
//                        evChargeBean.setSameSelected(false);
//                        evChargeBean.setStatus(-1);
//                    }
//                    mEVChargeAdapter.notifyDataSetChanged();
                    applyCharge(evChargeBean, position, true);
                    seekBarDialog.dismiss();
                }
            });
        }
        if (mSeekBarDialog != null && !mSeekBarDialog.isShowing()) {
            mSeekBarDialog.show();
            mSeekBarDialog.setProgress(evChargeBean.getTempValue());
        }
    }

    private void removeShrink() {
        if (mFrom == 0) {
            if (mEVChargeData.contains(mSecondModel) && mEVChargeData.size() > 1) {
                if (mInstantChargeFixed.getStatus() != 1) {
                    mInstantChargeFixed.setSelected(false);
                    mInstantChargeFixed.setAdd(true);
                    mInstantChargeFixed.setSameSelected(true);
                    mEVChargeData.remove(mSecondModel);
                }
            }

            if (mEVChargeData.contains(mFirstModel) && mEVChargeData.size() > 1) {
                if (mInstantChargeFull.getStatus() != 1) {
                    mInstantChargeFull.setSelected(false);
                    mInstantChargeFull.setAdd(true);
                    mInstantChargeFull.setSameSelected(true);
                    mEVChargeData.remove(mFirstModel);
                }
            }

        } else if (mFrom == 1) {
            if (mEVChargeData.contains(mSecondModel) && mEVChargeData.size() > 1) {
                if (mScheduledCharge.getStatus() != 1) {
                    mScheduledCharge.setSelected(false);
                    mScheduledCharge.setAdd(true);
                    mScheduledCharge.setSameSelected(true);
                    mEVChargeData.remove(mSecondModel);
                }
            }

            if (mEVChargeData.contains(mFirstModel) && mEVChargeData.size() > 1) {
                if (mLowerUtilityRate.getStatus() != 1) {
                    mLowerUtilityRate.setSelected(false);
                    mLowerUtilityRate.setAdd(true);
                    mLowerUtilityRate.setSameSelected(true);
                    mEVChargeData.remove(mFirstModel);
                }
            }
        }
        for (int i = 0; i < mEVChargeData.size(); i++) {
            PSEVChargeV2Model model = mEVChargeData.get(i);
            PSEVChargeV2Bean chargeV2Bean = model.getEvChargeBean();
            chargeV2Bean.setSelected(false);
            if (chargeV2Bean.getStatus() == 1) {
                chargeV2Bean.setSameSelected(false);
            }
        }
        mEVChargeAdapter.notifyDataSetChanged();
    }

    private void setExpand() {
        if (CollectionUtil.isListNotEmpty(mEVChargeData)) {
            for (int i = 0; i < mEVChargeData.size(); i++) {
                PSEVChargeV2Model model = mEVChargeData.get(i);
                PSEVChargeV2Bean chargeV2Bean = model.getEvChargeBean();
                if (chargeV2Bean.getFixedStatus() == 1) {
                    chargeV2Bean.setSelected(isExpand);
                }
            }
            mEVChargeAdapter.notifyDataSetChanged();
        }
    }

    public BindMultiAdapter<PSEVChargeV2Model> getEVChargeAdapter() {
        return mEVChargeAdapter;
    }

    public List<PSEVChargeV2Bean> getEVChargeModeList() {
        return mEVChargeModeList;
    }

    /**
     * 换页重置为未选中状态
     */
    public void resetNormalStatus() {
        if (mEVChargeAdapter != null && CollectionUtil.isListNotEmpty(mEVChargeData)) {
            for (PSEVChargeV2Model chargeV2Model : mEVChargeData) {
                PSEVChargeV2Bean chargeV2Bean = chargeV2Model.getEvChargeBean();
                if (chargeV2Bean != null) {
                    if (chargeV2Bean.getStatus() == 0) {
                        chargeV2Bean.setStatus(-1);
                        chargeV2Bean.setFixedStatus(-1);
                    }
                    chargeV2Bean.setSelected(false);
                }
            }
            mEVChargeAdapter.notifyDataSetChanged();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(EVBottomBehaviorEvent event) {
        float slideOffset = event.getSlideOffset();
        mCurrentOffset = slideOffset;
        if (CollectionUtil.isListEmpty(mEVChargeData)) return;
        if (slideOffset <= 0.4f) {
            if (mEVChargeData.size() > 1) {
                isExpand = false;
                removeShrink();
            }
        } else if (slideOffset >= 0.6f) {
            if (isExpand) return;
            isExpand = true;
            if (mFrom == 0) {
                if (mEVChargeData.size() < 2) {
                    if (!mEVChargeData.contains(mFirstModel)) {
                        mEVChargeData.add(0, mFirstModel);
                    }
                }
                if (mEVChargeData.size() < 2) {
                    if (!mEVChargeData.contains(mSecondModel)) {
                        mEVChargeData.add(1, mSecondModel);
                    }
                }
            } else if (mFrom == 1) {
                if (mEVChargeData.size() < 2) {
                    if (!mEVChargeData.contains(mFirstModel)) {
                        mEVChargeData.add(0, mFirstModel);
                    }
                }
                if (mEVChargeData.size() < 2) {
                    if (!mEVChargeData.contains(mSecondModel)) {
                        mEVChargeData.add(1, mSecondModel);
                    }
                }
            }
            mEVChargeAdapter.notifyDataSetChanged();
            setExpand();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(EVBottomBehaviorStateEvent event) {
        mBottomSheetBehaviorState = event.getBehaviorState();
    }
}
