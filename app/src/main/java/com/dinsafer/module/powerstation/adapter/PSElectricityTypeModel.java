package com.dinsafer.module.powerstation.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsElectricityTypeBinding;
import com.dinsafer.ui.rv.BaseBindModel;

public class PSElectricityTypeModel implements BaseBindModel<ItemPsElectricityTypeBinding> {

    private int checkedBgColor;
    private int normalBgColor;
    private String name;
    private boolean isSelected;

    public PSElectricityTypeModel(int checkedBgColor, String name) {
        this.checkedBgColor = checkedBgColor;
        this.name = name;
    }

    public PSElectricityTypeModel(int checkedBgColor, String name, boolean isSelected) {
        this.checkedBgColor = checkedBgColor;
        this.normalBgColor = R.drawable.shape_electricity_type_normal;
        this.name = name;
        this.isSelected = isSelected;
    }

    public PSElectricityTypeModel(int checkedBgColor, int normalBgColor, String name, boolean isSelected) {
        this.checkedBgColor = checkedBgColor;
        this.normalBgColor = normalBgColor;
        this.name = name;
        this.isSelected = isSelected;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_electricity_type;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsElectricityTypeBinding itemPsElectricityTypeBinding) {
        itemPsElectricityTypeBinding.ivChecked.setBackgroundResource(isSelected ? checkedBgColor
                : normalBgColor);
        itemPsElectricityTypeBinding.ivChecked.setImageResource(isSelected ? R.drawable.ps_white_checked : 0);
        itemPsElectricityTypeBinding.tvName.setLocalText(name);
    }

    public int getCheckedBgColor() {
        return checkedBgColor;
    }

    public void setCheckedBgColor(int checkedBgColor) {
        this.checkedBgColor = checkedBgColor;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }
}
