package com.dinsafer.module.powerstation.widget.schedule_mode_view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutScheduledModeBinding;
import com.dinsafer.module.powerstation.widget.DragSelectTouchListener;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

public class ScheduledModeView extends ConstraintLayout {

    private Context mContext;
    private LayoutScheduledModeBinding mBinding;
    private BindMultiAdapter<ScheduledModeModel> mScheduledModeAdapter;
    private List<ScheduledModeModel> mScheduledModeData;
    private List<ScheduledModeModel> mSelectedModelData;
    private List<ScheduledModeBean> mSelectedBeanData;
    private DragSelectTouchListener mTouchListener;
    private int startX;
    private int endX;
    private boolean isEnabled = true;
    private boolean isCharWithGrid;

    public ScheduledModeView(Context context) {
        this(context, null);
    }

    public ScheduledModeView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScheduledModeView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    protected void init(Context context) {
        mContext = context;
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_scheduled_mode, this, true);
        mBinding.rvData.setLayoutManager(new LinearLayoutManager(context));
        ((SimpleItemAnimator) mBinding.rvData.getItemAnimator()).setSupportsChangeAnimations(false);
        mScheduledModeAdapter = new BindMultiAdapter<>();
        mBinding.rvData.setAdapter(mScheduledModeAdapter);
        mScheduledModeData = new ArrayList<>();
        mSelectedModelData = new ArrayList<>();
        mSelectedBeanData = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            ScheduledModeBean scheduledModeBean = new ScheduledModeBean(i);
            ScheduledModeModel scheduledModeModel = new ScheduledModeModel(context, i, scheduledModeBean, mScheduledModeAdapter, isCharWithGrid);
            mScheduledModeData.add(scheduledModeModel);
        }
        mScheduledModeAdapter.setNewData(mScheduledModeData);
        mScheduledModeAdapter.notifyDataSetChanged();
        initTouchListener(context);
    }

    /**
     * 初始化触摸事件
     *
     * @param context
     */
    private void initTouchListener(Context context) {
        mBinding.rvData.post(new Runnable() {
            @Override
            public void run() {
                startX = isCharWithGrid ? mBinding.rvData.getWidth() / 2 - DensityUtil.dp2px(context, 21)
                            : DensityUtil.dp2px(context, 60);
                endX = startX + DensityUtil.dp2px(context, 105);
                mTouchListener = new DragSelectTouchListener(false, startX, endX, true);
                mBinding.rvData.addOnItemTouchListener(mTouchListener);
                mTouchListener.setCanTouch(false);
                mTouchListener.setSelectListener(new DragSelectTouchListener.onSelectListener() {
                    @Override
                    public void onUp() {
                        mSelectedModelData.clear();
                        mSelectedBeanData.clear();
                        for (int i = 0; i < mScheduledModeAdapter.getData().size(); i++) {
                            ScheduledModeModel modeModel = mScheduledModeAdapter.getItem(i);
                            ScheduledModeBean modeBean = modeModel.getScheduledModeBean();
                            if (modeBean.isSelected()) {
                                mSelectedModelData.add(modeModel);
                                mSelectedBeanData.add(modeBean);
                            }
//                            modeBean.setSelected(false);
                        }
                        mScheduledModeAdapter.notifyDataSetChanged();
                        if (mSelectedListener != null && CollectionUtil.isListNotEmpty(mScheduledModeData)) {
                            mSelectedListener.onSelected(mSelectedModelData);
                        }
                    }

                    @Override
                    public void onStartChange(int position) {
                        if(position < 0) return;
                        ScheduledModeModel modeModel = mScheduledModeAdapter.getItem(position);
                        ScheduledModeBean modeBean = modeModel.getScheduledModeBean();
                        modeBean.setSelected(true);
                        mScheduledModeAdapter.notifyItemChanged(position);

                    }

                    @Override
                    public void onSelectChange(int start, int end, boolean isSelected) {
                        if (start < 0 || end >= mScheduledModeAdapter.getData().size()) {
                            return;
                        }
                        for (int i = start; i <= end; i++) {
                            ScheduledModeModel modeModel = mScheduledModeAdapter.getItem(i);
                            ScheduledModeBean modeBean = modeModel.getScheduledModeBean();
                            modeBean.setSelected(isSelected);
                        }
                        mScheduledModeAdapter.notifyDataSetChanged();
                    }
                });
            }
        });
    }

    /**
     * 取消高亮
     */
    public void canCelHighLight() {
        if (CollectionUtil.isListNotEmpty(mScheduledModeData)) {
            for (ScheduledModeModel modeModel : mScheduledModeData) {
                ScheduledModeBean modeBean = modeModel.getScheduledModeBean();
                if (modeBean != null && modeBean.isSelected()) {
                    modeBean.setSelected(false);
                }
            }
            mScheduledModeAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 重置单条数据
     *
     * @param item
     */
    public void resetItem(ScheduledModeBean item) {
        if (item == null || mScheduledModeAdapter == null) return;
        int hour = item.getHour();
        if (hour < 0 || hour >= mScheduledModeAdapter.getData().size()) return;
        ScheduledModeModel scheduledModeModel = mScheduledModeAdapter.getItem(hour);
        scheduledModeModel.setScheduledModeBean(item);
        mScheduledModeAdapter.notifyItemChanged(hour);
    }

    /**
     * 重置多条数据
     *
     * @param data
     */
    public void resetAllData(List<ScheduledModeBean> data) {
        if (CollectionUtil.isListEmpty(data) || mScheduledModeAdapter == null) return;
        for (ScheduledModeBean modeBean : data) {
            int hour = modeBean.getHour();
            if (hour < 0 || hour >= mScheduledModeAdapter.getData().size()) continue;
            ScheduledModeModel scheduledModeModel = mScheduledModeAdapter.getItem(hour);
            scheduledModeModel.setScheduledModeBean(modeBean);
            mScheduledModeAdapter.notifyItemChanged(hour);
        }
    }

    /**
     * 重置 reserve
     *
     * @param emergencyReserve
     * @param smartReserve
     */
    public void resetReserve(int emergencyReserve, int smartReserve) {
        if (mScheduledModeAdapter == null) return;
        for (ScheduledModeModel scheduledModeModel : mScheduledModeAdapter.getData()) {
            ScheduledModeBean scheduledModeBean = scheduledModeModel.getScheduledModeBean();
            int mode = scheduledModeBean.getMode();
            int sectionType = scheduledModeBean.getSectionType();
            if (mode == 1) {
                if (sectionType == 1) {
                    scheduledModeBean.setPercentage(emergencyReserve);
                } else if (sectionType == 2) {
                    scheduledModeBean.setPercentage(100 - smartReserve);
                }
            } else if (mode == -1) {
                if (sectionType == 1) {
                    scheduledModeBean.setPercentage(100 - emergencyReserve);
                } else if (sectionType == 2) {
                    scheduledModeBean.setPercentage(smartReserve);
                }
            }
        }
        mScheduledModeAdapter.notifyDataSetChanged();
    }

    /**
     * 重置 emergencyReserve
     *
     * @param emergencyReserve
     */
    public void resetEmergencyReserve(int emergencyReserve) {
        if (mScheduledModeAdapter == null) return;
        for (ScheduledModeModel scheduledModeModel : mScheduledModeAdapter.getData()) {
            ScheduledModeBean scheduledModeBean = scheduledModeModel.getScheduledModeBean();
            int mode = scheduledModeBean.getMode();
            int sectionType = scheduledModeBean.getSectionType();
            if (mode == 1) {
                if (sectionType == 1) {
                    scheduledModeBean.setPercentage(emergencyReserve);
                }
            } else if (mode == -1) {
                if (sectionType == 2) {
                    scheduledModeBean.setPercentage(100 - emergencyReserve);
                }
            }
        }
        mScheduledModeAdapter.notifyDataSetChanged();
    }

    /**
     * 重置smartReserve
     *
     * @param smartReserve
     */
    public void resetSmartReserve(int smartReserve) {
        if (mScheduledModeAdapter == null) return;
        for (ScheduledModeModel scheduledModeModel : mScheduledModeAdapter.getData()) {
            ScheduledModeBean scheduledModeBean = scheduledModeModel.getScheduledModeBean();
            int mode = scheduledModeBean.getMode();
            int sectionType = scheduledModeBean.getSectionType();
            if (mode == 1) {
                if (sectionType == 2) {
                    scheduledModeBean.setPercentage(smartReserve);
                }
            } else if (mode == -1) {
                if (sectionType == 1) {
                    scheduledModeBean.setPercentage(100 - smartReserve);
                }
            }
        }
        mScheduledModeAdapter.notifyDataSetChanged();
    }

    public List<ScheduledModeModel> getSelectedModelData() {
        return mSelectedModelData;
    }

    public List<ScheduledModeBean> getSelectedBeanData() {
        return mSelectedBeanData;
    }

    /**
     * 获取model
     *
     * @return
     */
    public List<ScheduledModeModel> getScheduledModeData() {
        return mScheduledModeData;
    }

    /**
     * 获取model里面的modelBean
     *
     * @return
     */
    public List<ScheduledModeBean> getScheduledModeBeanData() {
        if (CollectionUtil.isListEmpty(mScheduledModeData)) return null;
        List<ScheduledModeBean> modeBeanData = new ArrayList<>();
        for (ScheduledModeModel scheduledModeModel : mScheduledModeData) {
            modeBeanData.add(scheduledModeModel.getScheduledModeBean());
        }
        return modeBeanData;
    }

    /**
     * 设置 RecyclerView 是否可用
     *
     * @param isEnabled
     */
    public void setRecyclerViewEnabled(boolean isEnabled) {
        this.isEnabled = isEnabled;
        if (mTouchListener != null) {
            mTouchListener.setCanTouch(isEnabled);
        }
        mBinding.rvData.setAlpha(isEnabled ? 1.0f : 0.5f);
    }

    public void setCanTouch(boolean canTouch) {
        if (!isEnabled) return;
        if (mTouchListener != null) {
            mTouchListener.setCanTouch(canTouch);
        }
    }

    /**
     * 刷新数据
     */
    public void notifyDataChange() {
        if (mScheduledModeAdapter == null) return;
        mScheduledModeAdapter.notifyDataSetChanged();
    }

    public void setCharWithGrid(boolean charWithGrid) {
        isCharWithGrid = charWithGrid;
        if (mScheduledModeAdapter == null) return;
        for (ScheduledModeModel scheduledModeModel : mScheduledModeAdapter.getData()) {
            scheduledModeModel.setChargeWithGrid(charWithGrid);
        }
        mScheduledModeAdapter.notifyDataSetChanged();
    }

    public void release() {
        if (mScheduledModeAdapter != null) {
            mScheduledModeAdapter.getData().clear();
        }
        mBinding.rvData.setAdapter(null);
    }

    // 监听事件
    private OnSelectedListener mSelectedListener;

    public void setSelectedListener(OnSelectedListener selectedListener) {
        this.mSelectedListener = selectedListener;
    }

    public interface OnSelectedListener {
        void onSelected(List<ScheduledModeModel> selectedData);
    }
}
