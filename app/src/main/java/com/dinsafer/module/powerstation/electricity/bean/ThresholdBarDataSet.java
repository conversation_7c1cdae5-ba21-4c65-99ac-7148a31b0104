package com.dinsafer.module.powerstation.electricity.bean;

import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import java.util.List;

/**
 * 带阈值信息的BarDataSet
 * 支持X轴标注功能
 */
public class ThresholdBarDataSet extends BarDataSet {
    
    // 数据类型标识
    private String dataType = "";
    
    // 阈值相关配置
    private int thresholdColor = 0xFF826F60;  // 阈值颜色
    private int thresholdFillColor = 0xFF826F60;  // 阈值填充颜色
    
    public ThresholdBarDataSet(List<BarEntry> yVals, String label) {
        super(yVals, label);
    }
    
    /**
     * 获取数据类型
     */
    public String getDataType() {
        return dataType;
    }
    
    /**
     * 设置数据类型
     * @param dataType 数据类型，如 "threshold", "normal", "usage"
     */
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    /**
     * 是否为阈值数据集
     */
    public boolean isThresholdDataSet() {
        return "threshold".equals(dataType);
    }
    
    /**
     * 是否为用量数据集
     */
    public boolean isUsageDataSet() {
        return "usage".equals(dataType) || "normal".equals(dataType);
    }
    
    /**
     * 获取阈值颜色
     */
    public int getThresholdColor() {
        return thresholdColor;
    }
    
    /**
     * 设置阈值颜色
     */
    public void setThresholdColor(int thresholdColor) {
        this.thresholdColor = thresholdColor;
    }
    
    /**
     * 获取阈值填充颜色
     */
    public int getThresholdFillColor() {
        return thresholdFillColor;
    }
    
    /**
     * 设置阈值填充颜色
     */
    public void setThresholdFillColor(int thresholdFillColor) {
        this.thresholdFillColor = thresholdFillColor;
    }
    
    /**
     * 检查是否有有效的阈值数据
     * @param startIndex 开始索引
     * @param count 检查数量
     * @return 是否存在阈值>0的数据
     */
    public boolean hasValidThresholdData(int startIndex, int count) {
        for (int i = startIndex; i < Math.min(startIndex + count, getEntryCount()); i++) {
            BarEntry entry = getEntryForIndex(i);
            if (entry instanceof ThresholdBarEntry) {
                ThresholdBarEntry thresholdEntry = (ThresholdBarEntry) entry;
                if (thresholdEntry.hasThreshold()) {
                    return true;
                }
            } else if (entry != null && entry.getVal() > 0) {
                // 普通BarEntry，如果值>0也认为有效
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取阈值时间段列表
     * 用于X轴标注
     */
    public List<ThresholdTimeSegment> getThresholdTimeSegments(int startIndex, int count) {
        List<ThresholdTimeSegment> segments = new java.util.ArrayList<>();
        ThresholdTimeSegment currentSegment = null;
        
        for (int i = startIndex; i < Math.min(startIndex + count, getEntryCount()); i++) {
            BarEntry entry = getEntryForIndex(i);
            boolean hasThreshold = false;
            float thresholdValue = 0f;
            
            if (entry instanceof ThresholdBarEntry) {
                ThresholdBarEntry thresholdEntry = (ThresholdBarEntry) entry;
                hasThreshold = thresholdEntry.hasThreshold();
                thresholdValue = thresholdEntry.getThresholdValue();
            } else if (entry != null && entry.getVal() > 0) {
                hasThreshold = true;
                thresholdValue = entry.getVal();
            }
            
            if (hasThreshold) {
                if (currentSegment == null) {
                    currentSegment = new ThresholdTimeSegment();
                    currentSegment.startX = entry.getXIndex();
                    currentSegment.startIndex = i;
                }
                currentSegment.endX = entry.getXIndex();
                currentSegment.endIndex = i;
                currentSegment.maxThreshold = Math.max(currentSegment.maxThreshold, thresholdValue);
            } else {
                if (currentSegment != null) {
                    segments.add(currentSegment);
                    currentSegment = null;
                }
            }
        }
        
        // 添加最后一个时间段
        if (currentSegment != null) {
            segments.add(currentSegment);
        }
        
        return segments;
    }
    
    /**
     * 阈值时间段数据类
     */
    public static class ThresholdTimeSegment {
        public float startX;
        public float endX;
        public int startIndex;
        public int endIndex;
        public float maxThreshold;
        
        public ThresholdTimeSegment() {
            this.maxThreshold = 0f;
        }
    }
}
