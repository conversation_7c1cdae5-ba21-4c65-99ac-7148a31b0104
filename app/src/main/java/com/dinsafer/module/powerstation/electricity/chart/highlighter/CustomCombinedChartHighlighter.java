package com.dinsafer.module.powerstation.electricity.chart.highlighter;

import com.github.mikephil.charting.data.ChartData;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.highlight.ChartHighlighter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.dataprovider.BarLineScatterCandleBubbleDataProvider;
import com.github.mikephil.charting.interfaces.datasets.IDataSet;
import com.github.mikephil.charting.utils.SelectionDetail;

import java.util.ArrayList;
import java.util.List;

public class CustomCombinedChartHighlighter extends ChartHighlighter<BarLineScatterCandleBubbleDataProvider> {

    public CustomCombinedChartHighlighter(BarLineScatterCandleBubbleDataProvider chart) {
        super(chart);
    }

    public List<Highlight> getHighlights(float x, float y) {
        List<Highlight> highlights = new ArrayList<>();
        int xIndex = getXIndex(x);
        List<SelectionDetail> valsAtIndex = getSelectionDetailsAtIndex(xIndex, -1);
        for (SelectionDetail selectionDetail : valsAtIndex) {
            if (selectionDetail == null) continue;
            Highlight highlight = new Highlight(xIndex,
                    selectionDetail.value,
                    selectionDetail.dataIndex,
                    selectionDetail.dataSetIndex);
            highlights.add(highlight);
        }
        return highlights;
    }

    /**
     * Returns a list of SelectionDetail object corresponding to the given xIndex.
     *
     * @param xIndex
     * @return
     */
    @Override
    public List<SelectionDetail> getSelectionDetailsAtIndex(int xIndex, int dataSetIndex) {

        List<SelectionDetail> vals = new ArrayList<SelectionDetail>();
        float[] pts = new float[2];

        CombinedData data = (CombinedData) mChart.getData();
        if (data != null) {
            // get all chartdata objects
            List<ChartData> dataObjects = data.getAllData();
            for (int i = 0; i < dataObjects.size(); i++) {

                for (int j = 0; j < dataObjects.get(i).getDataSetCount(); j++) {

                    IDataSet dataSet = dataObjects.get(i).getDataSetByIndex(j);

                    // dont include datasets that cannot be highlighted
                    if (!dataSet.isHighlightEnabled())
                        continue;

                    // extract all y-values from all DataSets at the given x-index
                    final float yVals[] = dataSet.getYValsForXIndex(xIndex);
                    for (float yVal : yVals) {
                        pts[1] = yVal;

                        mChart.getTransformer(dataSet.getAxisDependency()).pointValuesToPixel(pts);

                        if (!Float.isNaN(pts[1])) {
                            vals.add(new SelectionDetail(pts[1], yVal, i, j, dataSet));
                        }
                    }
                }
            }
        }
        return vals;
    }
}
