package com.dinsafer.module.powerstation.event;

public class BmtDeviceStatusChange {

    public static final int OFFLINE = -1;
    public static final int LOADING = 0;
    public static final int ONLINE = 1;

    private String deviceID;
    private String subcategory;
    private int connect_status = -1;
    private boolean showUpdate;
    private int previousStatus = -1;

    public BmtDeviceStatusChange(String deviceId, String subcategory) {
        this.deviceID = deviceId;
        this.subcategory = subcategory;
    }

    public BmtDeviceStatusChange(String deviceID, String subcategory, int connect_status) {
        this.deviceID = deviceID;
        this.subcategory = subcategory;
        this.connect_status = connect_status;
    }

    public BmtDeviceStatusChange(String deviceID, String subcategory, int connect_status, int previousStatus) {
        this.deviceID = deviceID;
        this.subcategory = subcategory;
        this.connect_status = connect_status;
        this.previousStatus = previousStatus;
    }

    public BmtDeviceStatusChange(String deviceID, String subcategory, int connect_status, boolean showUpdate) {
        this.deviceID = deviceID;
        this.subcategory = subcategory;
        this.connect_status = connect_status;
        this.showUpdate = showUpdate;
    }

    public BmtDeviceStatusChange(String deviceID, String subcategory, int connect_status, boolean showUpdate, int previousStatus) {
        this.deviceID = deviceID;
        this.subcategory = subcategory;
        this.connect_status = connect_status;
        this.showUpdate = showUpdate;
        this.previousStatus = previousStatus;
    }

    public String getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(String deviceID) {
        this.deviceID = deviceID;
    }

    public int getConnect_status() {
        return connect_status;
    }

    public void setConnect_status(int connect_status) {
        this.connect_status = connect_status;
    }

    public boolean isShowUpdate() {
        return showUpdate;
    }

    public void setShowUpdate(boolean showUpdate) {
        this.showUpdate = showUpdate;
    }

    public int getPreviousStatus() {
        return previousStatus;
    }

    public void setPreviousStatus(int previousStatus) {
        this.previousStatus = previousStatus;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    @Override
    public String toString() {
        return "BmtDeviceStatusChange{" +
                "deviceID='" + deviceID + '\'' +
                ", subcategory='" + subcategory + '\'' +
                ", connect_status=" + connect_status +
                ", showUpdate=" + showUpdate +
                ", previousStatus=" + previousStatus +
                '}';
    }
}
