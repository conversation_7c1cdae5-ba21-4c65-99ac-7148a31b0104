package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsElectricitySupplierBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSElectricitySupplierModel;
import com.dinsafer.module.powerstation.event.PSElectricitySupplierEvent;
import com.dinsafer.module.powerstation.utils.comparator.PSElectricitySupplierComparator;
import com.dinsafer.module.powerstation.utils.comparator.PSElectricitySupplierSearchComparator;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.ElectricitySupplierBean;
import com.dinsafer.ui.IndexView;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

public class PSElectricitySupplierFragment extends MyBaseFragment<FragmentPsElectricitySupplierBinding> {

    private List<ElectricitySupplierBean> mElectricitySupplier;
    private String mCountryCode;
    private BindMultiAdapter<PSElectricitySupplierModel> mAdapter;
    private List<PSElectricitySupplierModel> mData;
    private int lastScrollIndexPos = 0;
    private ArrayList<String> indexs;
    private LinkedHashMap<String, Integer> indexPosMap;
    private PSElectricitySupplierSearchComparator mSearchSortComparator;

    public static PSElectricitySupplierFragment newInstance(String countryCode) {
        PSElectricitySupplierFragment fragment = new PSElectricitySupplierFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.COUNTRY_CODE, countryCode);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_electricity_supplier;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.electricity_supplier));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.etSearch.setHint(Local.s(getString(R.string.enter_your_electricity_supplier)));

        mBinding.ivClear.setOnClickListener(v -> {
            mBinding.etSearch.setText("");
            mAdapter.setNewData(mData);
        });
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mCountryCode = bundle.getString(PSKeyConstant.COUNTRY_CODE);
        }
        initRv();
        setEditChangeListener();
        if (mCountryCode == null) {
            showErrorToast();
            return;
        }
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinHome.getInstance().getBmtElectricitySupplier(mCountryCode, new IDefaultCallBack2<List<ElectricitySupplierBean>>() {
            @Override
            public void onSuccess(List<ElectricitySupplierBean> suppliers) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                mElectricitySupplier = suppliers;
                PSElectricitySupplierComparator comparator = new PSElectricitySupplierComparator();
                Collections.sort(mElectricitySupplier, comparator);
                for (int i = 0; i < mElectricitySupplier.size(); i++) {
                    ElectricitySupplierBean supplier = mElectricitySupplier.get(i);
                    mData.add(new PSElectricitySupplierModel(getContext(), supplier));
                    String index = String.valueOf(supplier.getName().charAt(0)).toUpperCase();
                    if (!(index.toLowerCase().charAt(0) >= 'a' && index.toLowerCase().charAt(0) <= 'z')) {
                        index = "#";
                    }
                    if (!indexs.contains(index)) {
                        indexs.add(index);
                    }
                    if (!indexPosMap.containsKey(index)) {
                        indexPosMap.put(index, i);
                    }
                }
                mAdapter.setNewData(mData);
                mBinding.indexView.setData(indexs);
            }

            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }
        });
    }

    private void initRv() {
        indexs = new ArrayList<>();
        indexPosMap = new LinkedHashMap<>();
        mBinding.rvSupplier.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new BindMultiAdapter();
        mData = new ArrayList<>();

        mAdapter.setOnBindItemClickListener(new OnBindItemClickListener<PSElectricitySupplierModel>() {
            @Override
            public void onItemClick(View v, int position, PSElectricitySupplierModel model) {
                EventBus.getDefault().post(new PSElectricitySupplierEvent(model.getElectricitySupplierBean()));
                removeSelf();
            }
        });
        mBinding.rvSupplier.setAdapter(mAdapter);
        mBinding.indexView.setTextSize(DensityUtil.sp2px(getContext(), 10));
        mBinding.indexView.setOnTouchIndexViewCallback(new IndexView.OnTouchIndexViewCallback() {
            @Override
            public void onTouchIndex(int pos, String text) {
                if (pos == lastScrollIndexPos) {
                    Log.w(TAG, "onTouchIndex: ");
                    return;
                }
                ((LinearLayoutManager) mBinding.rvSupplier.getLayoutManager()).scrollToPositionWithOffset(indexPosMap.get(text), 0);
                lastScrollIndexPos = pos;
            }

            @Override
            public void onCancelTouchIndex() {

            }
        });
    }

    private void setEditChangeListener() {
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String text = mBinding.etSearch.getText().toString();
                setSearchStatus(!TextUtils.isEmpty(text));
                if (text == null || text.length() == 0) {
                    setEmpty(false);
                    mAdapter.setNewData(mData);
                } else {
                    ArrayList<PSElectricitySupplierModel> newData = new ArrayList<>();
                    for (PSElectricitySupplierModel item : mData) {
                        String name = (item.getElectricitySupplierBean().getName() == null ? ""
                                : item.getElectricitySupplierBean().getName().toLowerCase());
                        if (name.contains(text.toLowerCase())) {
                            newData.add(item);
                        }
                    }
                    if (mSearchSortComparator == null) {
                        mSearchSortComparator = new PSElectricitySupplierSearchComparator(text);
                    } else {
                        mSearchSortComparator.setSearchKeyword(text);
                    }
                    Collections.sort(newData, mSearchSortComparator);
                    mAdapter.setNewData(newData);
                    setEmpty(newData.size() == 0);
                }
            }
        });
    }

    private void setEmpty(boolean empty) {
        mBinding.llEmpty.setVisibility(empty ? View.VISIBLE : View.GONE);
        mBinding.rvSupplier.setVisibility(empty ? View.GONE : View.VISIBLE);
    }

    private void setSearchStatus(boolean isSearched) {
        mBinding.ivClear.setVisibility(isSearched ? View.VISIBLE : View.GONE);
        mBinding.tvCountrySupport.setVisibility(isSearched ? View.GONE : View.VISIBLE);
        mBinding.indexView.setVisibility(isSearched ? View.GONE : View.VISIBLE);
    }
}
