package com.dinsafer.module.powerstation.dialog;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.adapter.ElectricityCircleTypeAdapter;
import com.dinsafer.module.powerstation.electricity.bean.ElectricityCircleTypeBean;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ElectricityCircleTypePopup extends PopupWindow {

    public static final String TAG = ElectricityCircleTypePopup.class.getSimpleName();
    private ElectricityCircleTypeAdapter mElectricityCircleTypeAdapter;
    private List<ElectricityCircleTypeBean> mData = new ArrayList<>();
    // 0. day、week...的类别
    // 1. 电池充放电类别
    // 2. 市电输入输出电类别
    // 3. 影响和策略的eco/Revenue
    private int type;
    public static final int CIRCLE_TYPE = 0;
    public static final int BATTER_CHARGE_TYPE = 1;
    public static final int GRID_TYPE = 2;
    public static final int IMPACTS_STRATEGIES_ECO_TYPE = 3;
    public static final int IMPACTS_STRATEGIES_REVENUE_TYPE = 4;

    private Context mContext;
    private RecyclerView rvData;
    private int mViewWidth;

    public ElectricityCircleTypePopup(Context context, int type) {
        super(context);
        // 获取布局资源
        mContext = context;
        this.type = type;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.popup_electricity_circle_type, null);
        // 设置布局
        setContentView(view);
        mViewWidth = view.getWidth();
        setBackgroundDrawable(null);
        setOutsideTouchable(true);
        setFocusable(true);
        initRv(view);
    }

    private void initRv(View view) {
        rvData = view.findViewById(R.id.rv_data);
        rvData.setLayoutManager(new LinearLayoutManager(mContext));

        if (mData.size() == 0) {
            switch (type) {
                case CIRCLE_TYPE:
                    List<String> strType = Arrays.asList(mContext.getResources().getStringArray(R.array.electricity_circle_type_array));
                    List<CycleType> typeList = CycleType.getTypeList();
                    if (strType.size() != typeList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < strType.size(); i++) {
                        String type = strType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, typeList.get(i)));
                    }
                    break;

                case BATTER_CHARGE_TYPE:
                    List<String> chargeType = Arrays.asList(mContext.getResources().getStringArray(R.array.electricity_charge_type_array));
                    List<PlusMinusType> chargePlusMinusList = PlusMinusType.getPlusMinusTypeList();
                    if (chargeType.size() != chargePlusMinusList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < chargeType.size(); i++) {
                        String type = chargeType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, chargePlusMinusList.get(i)));
                    }
                    break;

                case GRID_TYPE:
                    List<String> gridType = Arrays.asList(mContext.getResources().getStringArray(R.array.electricity_grid_type_array));
                    List<PlusMinusType> gridPlusMinusList = PlusMinusType.getPlusMinusTypeList();
                    if (gridType.size() != gridPlusMinusList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < gridType.size(); i++) {
                        String type = gridType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, gridPlusMinusList.get(i)));
                    }
                    break;

                case IMPACTS_STRATEGIES_ECO_TYPE:
                    List<String> ecoType = Arrays.asList(mContext.getResources().getStringArray(R.array.electricity_circle_type_array));
                    List<CycleType> ecoTypeList = CycleType.getTypeList();
                    if (ecoType.size() != ecoTypeList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < ecoTypeList.size(); i++) {
                        String type = ecoType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, ecoTypeList.get(i)));
                    }
                    break;

                case IMPACTS_STRATEGIES_REVENUE_TYPE:
                    List<String> isType = Arrays.asList(mContext.getResources().getStringArray(R.array.impacts_strategies_type_array));
                    List<CycleType> isTypeList = CycleType.getTypeListExceptDay();
                    if (isType.size() != isTypeList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < isTypeList.size(); i++) {
                        String type = isType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, isTypeList.get(i)));
                    }
                    break;
            }
            mData.get(type == IMPACTS_STRATEGIES_ECO_TYPE ? 1 : 0).setSelected(true);
        }
        mElectricityCircleTypeAdapter = new ElectricityCircleTypeAdapter(R.layout.item_electricity_circle_type_popup, mData);
        rvData.setAdapter(mElectricityCircleTypeAdapter);
        mElectricityCircleTypeAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                for (ElectricityCircleTypeBean etb : mData) {
                    etb.setSelected(false);
                }
                ElectricityCircleTypeBean electricityCircleTypeBean = mElectricityCircleTypeAdapter.getItem(position);
                electricityCircleTypeBean.setSelected(true);
                mElectricityCircleTypeAdapter.notifyDataSetChanged();
                if (typeSelectedListener != null) {
                    ElectricityCircleTypeBean typeBean = mElectricityCircleTypeAdapter.getSelectedItem();
                    typeSelectedListener.onSelected(typeBean.getType(), typeBean.getCycleType());
                }
                dismiss();
            }
        });
    }

    public void setIndexSelected(int index) {
        if (CollectionUtil.isListEmpty(mData)) return;
        for (ElectricityCircleTypeBean circleTypeBean : mData) {
            circleTypeBean.setSelected(false);
        }
        mData.get(index).setSelected(true);
        mElectricityCircleTypeAdapter.notifyDataSetChanged();
    }

    public void showAtLocation(View anchorView, int locationX, int locationY) {
        showAsDropDown(anchorView, locationX, locationY);
    }

    private ElectricityCircleTypeDialog.OnTypeSelectedListener typeSelectedListener;

    public void setTypeSelectedListener(ElectricityCircleTypeDialog.OnTypeSelectedListener typeSelectedListener) {
        this.typeSelectedListener = typeSelectedListener;
    }

    public interface OnTypeSelectedListener {
        void onSelected(String type, CycleType cycleType);
    }
}
