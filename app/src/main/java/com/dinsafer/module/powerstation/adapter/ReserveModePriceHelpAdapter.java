package com.dinsafer.module.powerstation.adapter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ReserveModePriceHelpBean;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/27 15:29
 * @description :
 */
public class ReserveModePriceHelpAdapter extends BaseQuickAdapter<ReserveModePriceHelpBean, BaseViewHolder> {

    public ReserveModePriceHelpAdapter() {
        super(R.layout.item_reserve_mode_price_help);
    }

    @Override
    protected void convert(BaseViewHolder helper, ReserveModePriceHelpBean item) {
        LocalTextView tvTitle = helper.getView(R.id.tv_title);
        LocalTextView tvDesc = helper.getView(R.id.tv_desc);
        tvTitle.setLocalText(item.getTitle());
        tvDesc.setLocalText(item.getDesc());
    }
}
