package com.dinsafer.module.powerstation.dialog;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.SheetPvPreferenceBinding;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

public class PVPreferenceSheet extends BottomSheetDialogFragment {

    private final String TAG = PVPreferenceSheet.class.getSimpleName();
    private SheetPvPreferenceBinding mBinding;
    private int from;
    private OnCallback callback;

    public PVPreferenceSheet(int from, OnCallback callback) {
        this.from = from;
        this.callback = callback;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(STYLE_NORMAL, R.style.BottomSheetDialog);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.sheet_pv_preference, container, false);
        mBinding.llEmaldoAi.setVisibility(from == 0 ? View.VISIBLE : View.GONE);
        mBinding.viewLine1.setVisibility(from == 0 ? View.VISIBLE : View.GONE);
        if (from == 0) {
            int[] colors = AIColorUtil.getAIColor(getContext());
            float[] positions = AIColorUtil.getAIColorPosition();
            mBinding.tvFollowAi.setAIColorShader(colors, positions);
        }
        mBinding.llEmaldoAi.setOnClickListener(v -> {
            if (callback != null) {
                callback.onCallback(1);
            }
            dismiss();
        });
        mBinding.tvLoad.setOnClickListener(v -> {
            if (callback != null) {
                callback.onCallback(2);
            }
            dismiss();
        });
        mBinding.tvBatteryCharge.setOnClickListener(v -> {
            if (callback != null) {
                callback.onCallback(3);
            }
            dismiss();
        });
        mBinding.tvCancel.setOnClickListener(v -> dismiss());
        return mBinding.getRoot();
    }

    public void show(FragmentManager fragmentManager) {
        show(fragmentManager, TAG);
    }

    public interface OnCallback {
        void onCallback(int type);
    }
}
