package com.dinsafer.module.powerstation.electricity;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentElectricityUsageBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.ChartNoteTextAttrBean;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.marker.UsageMarkView;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.module.powerstation.electricity.controller.UsageChartModelController;
import com.dinsafer.module.powerstation.electricity.helper.UsageChartHelper;
import com.dinsafer.module.powerstation.event.StatInfoEvent;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;


import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 负载
 */
public class UsageFragment extends BaseChartFragment<UsageChartModelController,
        FragmentElectricityUsageBinding> {

    private CustomCombinedMarkerView mMarkerView;
    private PSElectricityTypeBean mPowerPeakBean;
    private int mBmtType = -1;
    private UsageChartHelper mUsageChartHelper;

    public static UsageFragment newInstance(int fromIndex, String deviceId, String subCategory) {
        UsageFragment fragment = new UsageFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.INDEX, fromIndex);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_electricity_usage;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        String subCategory = getArguments().getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mBmtType = BmtUtil.getBmtType(subCategory);
        mUsageChartHelper = new UsageChartHelper(getContext(), mBmtType);
        mType = BaseChartFragment.CHART_ELECTRICITY_USAGE;
        mPlusMinusType = PlusMinusType.PLUS;
        mMarkerView = new UsageMarkView(getContext());
        mRightTopText = getAboveNoteTextBean("", ChartNoteTextAttrBean.Gravity.RIGHT_TOP);
        initRefreshLayout(mBinding.refreshLayout);
    }

    @Override
    protected void initChartView() {
        super.initChartView();
        initChart(mFlipCombinedChartView, mMarkerView);
        setIvRightEnabled(mOffSet != 0);
        initElectricityType();
        mFlipCombinedChartView.setFlipChangeListener(new FlipCombinedChartView.OnFlipChangeListener() {
            @Override
            public void flipChange(int index, OperateOrientation orientation) {
                mIndex = index;
                if (orientation == OperateOrientation.LEFT) {
                    mOffSet = mOffSet - 1;
                } else if (orientation == OperateOrientation.RIGHT) {
                    mOffSet = mOffSet + 1;
                }
//                EventBus.getDefault().post(new ChartPageChangeEvent(orientation));
                setIvRightEnabled(mOffSet != 0);
                getStatisticData(true);
            }
        });
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            if (mCycleType != CycleType.LIFETIME) {
                if (isChartViewNotNull()) {
                    setIvRightEnabled(mOffSet != 0);
                }
            }
            getStatisticData(true);
        }
    }

    private ChartNoteTextAttrBean mRightTopText;

    private void initElectricityType() {
        if (isChartViewNotNull()) {
            mData = mUsageChartHelper.getFilterData();
            mPowerPeakBean = new PSElectricityTypeBean(R.drawable.shape_circle_power_peak, R.drawable.shape_electricity_power_peak_normal, getString(R.string.electricity_power_peak), true);
            mFlipCombinedChartView.setElectricityTypeData(mData);
            mFlipCombinedChartView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mFlipCombinedChartView.setRvFilterVisible(mData.size() > 1);
                    if (isDualPowerOpen) {
                        mFlipCombinedChartView.setTPPVDescText(Local.s(getString(R.string.power_station_additional_load)) + ": "
                                + Local.s(getString(R.string.using_third_party_pv_desc)));
                        mFlipCombinedChartView.setTvTPPVDescVisible(true);
                    }
                }
            }, 1000);
        }
    }


    @Override
    public void createChartModelController() {
        chartModelController = new UsageChartModelController();
    }

    @Override
    protected void resetChart() {
        String powerUnit = "";
        String powerHourUnit = "";
        float leftYMaxVal = getYMax(CHART_ELECTRICITY_USAGE, mChartData);
        float rightYMaxVal = getYRightActualMax(mChartData);

        if (mCycleType != CycleType.DAY) {
            String rightYMaxValStr = ChartDataUtil.getPowerTransferVal(rightYMaxVal, rightYMaxVal,
                    true);
            String rightYMaxValUnit = ChartDataUtil.getPowerUnit(rightYMaxVal, false);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(Local.s(getString(R.string.electricity_power_peak)) + "\n");
            stringBuilder.append(rightYMaxValStr);
            stringBuilder.append(rightYMaxValUnit);
            mRightTopText.setNoteText(stringBuilder.toString());
            if (!mAboveChartTexts.contains(mRightTopText)) {
                mAboveChartTexts.add(mRightTopText);
            }
        } else {
            if (mAboveChartTexts.contains(mRightTopText)) {
                mAboveChartTexts.remove(mRightTopText);
            }
        }

        if (mCycleType == CycleType.DAY) {
            powerUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, false);
        } else {
            powerUnit = ChartDataUtil.getPowerUnit(rightYMaxVal, false);
        }
        powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        mMarkerView.setPowerUnit(powerUnit);
        mMarkerView.setPowerHourUnit(powerHourUnit);
        mMarkerView.setLeftYMaxVal(leftYMaxVal);
        mMarkerView.setRightYMaxVal(rightYMaxVal);
        mMarkerView.setDSTTransitionDay(mHourCount == DST_TIME_MINUTE);
        if (mHourCount == DST_TIME_MINUTE) {
            mMarkerView.setTimeType(1);
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            mMarkerView.setTimeType(-1);
        } else {
            mMarkerView.setTimeType(0);
        }
        mMarkerView.setTimestamp(mStartTime);
        mMarkerView.setTimezone(timezone);
        String sumValStr = ChartDataUtil.getPowerTransferVal(getSumVal(), getSumVal(), false);
        String leftUnit = ChartDataUtil.getPowerUnit(getSumVal(), true);
//        mBinding.tvUnit.setText("(" + leftUnit + ")");
        mBinding.esvVal.setLeftVal(sumValStr, leftUnit);
        String middleStr = ChartDataUtil.getPowerTransferVal(getHighVal(), getHighVal(),
                mCycleType == CycleType.DAY);
        String middleUnit = ChartDataUtil.getPowerUnit(getHighVal(), mCycleType != CycleType.DAY);
        mBinding.esvVal.setMiddleVal(mCycleType == CycleType.DAY ?
                        Local.s(getString(R.string.electricity_power_peak)) : Local.s(getString(R.string.electricity_high)),
                middleStr, middleUnit);
        String rightStr = ChartDataUtil.getPowerTransferVal(getLowVal(), getLowVal(), mCycleType == CycleType.DAY);
        String rightUnit = ChartDataUtil.getPowerUnit(getLowVal(), true);
        mBinding.esvVal.setRightVal(mCycleType == CycleType.DAY ?
                        "" : Local.s(getString(R.string.electricity_low)),
                mCycleType == CycleType.DAY ? "" : rightStr,
                mCycleType == CycleType.DAY ? "" : rightUnit);
        mBinding.esvVal.refreshText();
//        setLowVisible(mCycleType != CycleType.DAY);
//        if (mData.size() < 3) {
//            if (isBSensorInstalled) {
//                mData.add(1, new PSElectricityTypeModel(R.drawable.shape_circle_679ef1, getString(R.string.power_station_additional_load), true));
//                mBinding.fcChart.notifyFilterDataChanged();
//            }
//        }
        super.resetChart();
    }

    @Override
    protected void resetFailChart() {
        String powerUnit = "";
        String powerHourUnit = "";
        float leftYMaxVal = 0;
        float rightYMaxVal = 0;

        if (mCycleType != CycleType.DAY) {
            String rightYMaxValStr = ChartDataUtil.getPowerTransferVal(rightYMaxVal, rightYMaxVal,
                    true);
            String rightYMaxValUnit = ChartDataUtil.getPowerUnit(rightYMaxVal, false);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(Local.s(getString(R.string.electricity_power_peak)) + "\n");
            stringBuilder.append(rightYMaxValStr);
            stringBuilder.append(rightYMaxValUnit);
            mRightTopText.setNoteText(stringBuilder.toString());
            if (!mAboveChartTexts.contains(mRightTopText)) {
                mAboveChartTexts.add(mRightTopText);
            }
        } else {
            if (mAboveChartTexts.contains(mRightTopText)) {
                mAboveChartTexts.remove(mRightTopText);
            }
        }

        if (mCycleType == CycleType.DAY) {
            powerUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, false);
        } else {
            powerUnit = ChartDataUtil.getPowerUnit(rightYMaxVal, false);
        }
        powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        String leftUnit = ChartDataUtil.getPowerUnit(getSumVal(), true);

        mBinding.esvVal.setLeftVal(mFailVal, leftUnit);
        mBinding.esvVal.setMiddleVal(mCycleType == CycleType.DAY ?
                        Local.s(getString(R.string.electricity_power_peak)) : Local.s(getString(R.string.electricity_high)),
                mFailVal, mCycleType == CycleType.DAY ? powerUnit : powerHourUnit);
        mBinding.esvVal.setRightVal(mCycleType == CycleType.DAY ?
                        "" : Local.s(getString(R.string.electricity_low)),
                mCycleType == CycleType.DAY ? "" : mFailVal,
                mCycleType == CycleType.DAY ? "" : powerHourUnit);
        mBinding.esvVal.refreshText();
        super.resetFailChart();
    }

    @Override
    protected void getStatisticData(boolean showLoading) {
        if (mFlipCombinedChartView != null) {
            if (BaseChartFragment.mCycleType == CycleType.DAY) {
                if (mData.contains(mPowerPeakBean)) {
                    mFlipCombinedChartView.notifyFilterDataRemove(mData.indexOf(mPowerPeakBean));
                    mData.remove(mPowerPeakBean);
                }
            } else {
                if (!mData.contains(mPowerPeakBean)) {
                    mData.add(mPowerPeakBean);
                    mFlipCombinedChartView.notifyFilterDataInsert(mPowerPeakBean);
                }
            }
            if (CollectionUtil.isListNotEmpty(mData)) {
                mFlipCombinedChartView.setRvFilterVisible(mData.size() > 1);
            }
        }
        if (ElectricityStatisticsFragment.mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_STATS_LOADUSAGE_V2);
            params.put(BmtDataKey.INTERVAL, ElectricityStatisticsFragment.mInterval);
            params.put(BmtDataKey.OFFSET, mOffSet);
            if (showLoading) {
                showTimeOutLoadinFramgmentWithMarginTop(marginTop);
            }
            Map<String, Object> result = null;
            switch (mCycleType) {
                case DAY:
                    result = mDayCache.get(mOffSet);
                    break;

                case WEEK:
                    result = mWeekCache.get(mOffSet);
                    break;

                case MONTH:
                    result = mMonthCache.get(mOffSet);
                    break;

                case YEAR:
                    result = mYearCache.get(mOffSet);
                    break;
            }
            if (result == null) {
                ElectricityStatisticsFragment.mPSDevice.submit(params);
            } else {
                EventBus.getDefault().post(new StatInfoEvent(result));
                setChartData(BaseChartFragment.CHART_ELECTRICITY_USAGE, result);
                closeLoadingFragment();
            }
        }
    }

    @Override
    protected void setDayChart() {
        mMarkerView.setInterval(mInterval);
        ArrayList<Entry> entries = mUsageChartHelper.getUsageEntries(mChartData, mFlipCombinedChartView);

        float[] pos = ChartDataUtil.getPositions(getYMax(CHART_ELECTRICITY_USAGE, mChartData));
        int[] colors = ChartDataUtil.getLineGradientColor(getContext(), getYMax(CHART_ELECTRICITY_USAGE, mChartData), false);
        SectionLineDataSet dayLineData = getSectionLineDataSet(entries, 0, YAxis.AxisDependency.LEFT,
                colors, pos, true, true);
        dayLineData.setMode(LineDataSet.Mode.LINEAR);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < mChartData.size(); i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        lineDataSets.add(dayLineData);
        LineData lineData = new LineData(xVals, lineDataSets);
        data.setData(lineData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    @Override
    protected void setWeekChart() {
        refreshWeek2Lifetime(mWeeks);
    }

    @Override
    protected void setMonthChart() {
        refreshWeek2Lifetime(months);
    }

    @Override
    protected void setYearChart() {
        refreshWeek2Lifetime(mYears);
    }

    @Override
    protected void setLifetimeChart() {
        refreshWeek2Lifetime(lifetimes);
    }

    @Override
    protected void setFailDayChart() {
        mMarkerView.setInterval(mInterval);
        ArrayList<Entry> entries = new ArrayList<Entry>();
        for (int i = 0; i < mChartData.size(); i++) {
            entries.add(new Entry(0, i));
        }
        float[] pos = ChartDataUtil.getPositions(getYMax(CHART_ELECTRICITY_USAGE, mChartData));
        int[] colors = {getColor(R.color.transparent), getColor(R.color.transparent), getColor(R.color.transparent)};
        SectionLineDataSet dayLineData = getSectionLineDataSet(entries, 0, YAxis.AxisDependency.LEFT,
                colors, pos, true, true);
        dayLineData.setMode(LineDataSet.Mode.LINEAR);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < mChartData.size(); i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        lineDataSets.add(dayLineData);
        LineData lineData = new LineData(xVals, lineDataSets);
        data.setData(lineData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    @Override
    protected void setFailWeekChart() {
        refreshFailWeek2Lifetime(mWeeks);
    }

    @Override
    protected void setFailMonthChart() {
        refreshFailWeek2Lifetime(months);
    }

    @Override
    protected void setFailYearChart() {
        refreshFailWeek2Lifetime(mYears);
    }

    @Override
    protected void setFailLifetimeChart() {
        refreshFailWeek2Lifetime(lifetimes);
    }

    private void refreshWeek2Lifetime(String[] xLabels) {
        CombinedData data = new CombinedData(xLabels);
        ArrayList<Entry> entries = new ArrayList<Entry>();
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
//        for (int i = 0; i < xLabels.length; i++) {
//            entries.add(new Entry(0, i));
//            yVals.add(new BarEntry(new float[]{0, 0, 0}, i));
//        }
        int count = mChartData.size();
        boolean hasDataGTZero = false;
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            if (sonData.size() > 4) {
                entries.add(new Entry(sonData.get(4), xIndex));
                if (!hasDataGTZero) {
                    hasDataGTZero = sonData.get(4) > 0;
                }
//                entries.get(xIndex).setVal(sonData.get(4));
            }
        }
        float[] pos = ChartDataUtil.getPositions(getYRightActualMax(mChartData));
        int[] colors = {getColor(R.color.transparent), getColor(R.color.transparent), getColor(R.color.transparent)};
        if (hasDataGTZero) {
            colors = ChartDataUtil.getLineGradientColor(getContext(), getYRightActualMax(mChartData), true);
        }
        SectionLineDataSet lineDataSet = getSectionLineDataSet(entries, 0, YAxis.AxisDependency.RIGHT,
                colors, pos, true, false);
        lineDataSet.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
        LineData lineData = new LineData();
        lineData.addDataSet(lineDataSet);
        lineDataSet.setAxisDependency(YAxis.AxisDependency.RIGHT);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }


        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            float val2 = 0f;
//            if (isBSensorInstalled) {
//                val2 = sonData.get(2);
//            } else {
//                val2 = sonData.get(3);
//            }
            val2 = sonData.get(2);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
//            if (isBSensorInstalled) {
//                yVals.add(new BarEntry(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
//                        mFlipCombinedChartView.isFilterSelected(1) ? val2 : 0,
//                        mFlipCombinedChartView.isFilterSelected(2) ? sonData.get(3) : 0}, xIndex));
//            } else {
//                yVals.add(new BarEntry(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
//                        mFlipCombinedChartView.isFilterSelected(1) ? val2 : 0}, xIndex));
//            }
            yVals.add(mUsageChartHelper.getBarEntry(mFlipCombinedChartView, sonData, val2, xIndex));
        }

        BarDataSet barDataSet = new BarDataSet(yVals, "");

        int colorsBar[] = mUsageChartHelper.getBarColors(true);
//        if (isBSensorInstalled) {
//            colorsBar = new int[]{getColor(R.color.color_tip_05),
//                    getColor(R.color.color_tip_05_2),
//                    getColor(R.color.color_tip_05_3)};
//        } else {
//            colorsBar = new int[]{getColor(R.color.color_tip_05),
//                    getColor(R.color.color_tip_05_3)};
//        }
        barDataSet.setColors(colorsBar);
        barDataSet.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSet.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSet.setHighLightColor(getColor(R.color.color_white_03));
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        boolean hasDualPower = mBmtType == BmtUtil.BMT_POWER_STORE ?
                mFlipCombinedChartView.isFilterSelected(1)
                : mFlipCombinedChartView.isFilterSelected(3);
        if (count > 0 && hasDualPower) {
            data.setData(lineData);
        }
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    private void refreshFailWeek2Lifetime(String[] xLabels) {
        CombinedData data = new CombinedData(xLabels);
        ArrayList<Entry> entries = new ArrayList<Entry>();
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        int count = mChartData.size();
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            if (sonData.size() > 4) {
                entries.add(new Entry(sonData.get(4), xIndex));
            }
        }
        float[] pos = ChartDataUtil.getPositions(getYRightActualMax(mChartData));
        int[] colors = {getColor(R.color.transparent), getColor(R.color.transparent), getColor(R.color.transparent)};
        SectionLineDataSet lineDataSet = getSectionLineDataSet(entries, 0, YAxis.AxisDependency.RIGHT,
                colors, pos, true, false);
        lineDataSet.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
        LineData lineData = new LineData();
        lineData.addDataSet(lineDataSet);
        lineDataSet.setAxisDependency(YAxis.AxisDependency.RIGHT);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }

        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            float val2 = 0f;
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            yVals.add(mUsageChartHelper.getBarEntry(mFlipCombinedChartView, sonData, val2, xIndex));
        }

        BarDataSet barDataSet = new BarDataSet(yVals, "");

        int[] colorsBar = mUsageChartHelper.getBarColors(false);
        barDataSet.setColors(colorsBar);
        barDataSet.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSet.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSet.setHighLightColor(getColor(R.color.color_white_03));
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        if (count > 0 && mFlipCombinedChartView.isFilterSelected(3)) {
            data.setData(lineData);
        }
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    private float getHighVal() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float highVal = 0f;
        List<Float> sumData = getSumData();
        if (CollectionUtil.isListNotEmpty(sumData)) highVal = sumData.get(sumData.size() - 1);
        return highVal;
    }

    private float getSumVal() {
        float sum = 0;
        List<Float> sumData = getSumData();
        for (Float val : sumData) {
            sum = sum + val;
        }
        return mCycleType == CycleType.DAY ? sum * mInterval / 60 : sum;
    }

    private float getLowVal() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float lowVal = 0f;
        List<Float> sumData = getSumData();
        if (CollectionUtil.isListNotEmpty(sumData)) lowVal = sumData.get(0);
        return lowVal;
    }

    private List<Float> getSumData() {
        List<Float> sumData = new ArrayList<>();
        if (mFlipCombinedChartView != null) {
            for (int i = 0; i < mChartData.size(); i++) {
                List<Float> sonData = mChartData.get(i);
                float sum = 0;
//                if (isBSensorInstalled) {
//                    sum = sum + sonData.get(1) + sonData.get(2) + sonData.get(3);
//                } else {
//                    sum = sum + sonData.get(1) + sonData.get(3);
//                }
                if (mBmtType == BmtUtil.BMT_POWER_CORE) {
                    if (mFlipCombinedChartView.isFilterSelected(0)) {
                        sum = sum + sonData.get(1);
                    }
                    if (mFlipCombinedChartView.isFilterSelected(1)) {
                        sum = sum + sonData.get(2);
                    }
                    if (mFlipCombinedChartView.isFilterSelected(2)) {
                        sum = sum + sonData.get(3);
                    }
                } else if (mBmtType == BmtUtil.BMT_POWER_STORE) {
                    if (mFlipCombinedChartView.isFilterSelected(0)) {
                        sum = sum + sonData.get(1);
                    }
                    if (mFlipCombinedChartView.isFilterSelected(1)) {
                        sum = sum + sonData.get(2);
                    }
                }
                sumData.add(sum);
            }
            Collections.sort(sumData);
        }
        return sumData;
    }

    private void setLowVisible(boolean isVisible) {
//        mBinding.esvVal.setRightVisible(isVisible);
    }

}
