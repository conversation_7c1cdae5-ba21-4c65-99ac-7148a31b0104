package com.dinsafer.module.powerstation.adapter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.PSRegionBean;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/28 17:08
 * @description :
 */
public class PsRegionAdapter extends BaseQuickAdapter<PSRegionBean, BaseViewHolder> {

    public PsRegionAdapter() {
        super(R.layout.item_ps_region);
    }

    @Override
    protected void convert(BaseViewHolder helper, PSRegionBean item) {
        LocalTextView tvCity = helper.getView(R.id.tv_city);
        tvCity.setLocalText(item.getCountryNameDisplay());
    }
}
