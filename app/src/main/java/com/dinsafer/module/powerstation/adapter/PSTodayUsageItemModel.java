package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.text.TextUtils;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsTodayUsageBinding;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.manager.LineChartManager;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;

public class PSTodayUsageItemModel extends BasePowerStationItemModel<ItemPsTodayUsageBinding> {

    private List<List<Float>> data = new ArrayList<>();
    private int interval = 5;
    private long startTime;
    private String timezone;
    private boolean isSuccess;

    public PSTodayUsageItemModel(Context context, String deviceId, String subcategory) {
        super(context, deviceId, subcategory);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_today_usage;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsTodayUsageBinding binding) {
        LineChartManager lineChartManager = new LineChartManager(mContext, binding.lcUsage);
        float sumData = getSumVal(data, interval, true);
        String usage = ChartDataUtil.getPowerTransferVal(sumData, sumData, true);
        binding.tvUnit.setText(isSuccess ? ChartDataUtil.getPowerUnit(sumData, true) : "Wh");
        binding.tvValue.setLocalText(isSuccess ? usage : "-");
        float peakVal = lineChartManager.getYMax(data, true);
        String peakStr = isSuccess ? (ChartDataUtil.getPowerTransferVal(peakVal, peakVal, true)
                + ChartDataUtil.getPowerUnit(peakVal, false)) : "-W";
        binding.tvPeak.setText(Local.s(mContext.getString(R.string.ps_peak)) + " " + peakStr);
        binding.tvUsage.setLocalText(mContext.getResources().getString(R.string.power_today_usage));
        int hourCount = 1440;
        if (!TextUtils.isEmpty(timezone)) {
            hourCount = DDDateUtil.isDSTTransitionDay(startTime * 1000, TimeZone.getTimeZone(timezone)) ? 1500 : 1440;
        }
        lineChartManager.initChart(interval, data, true, hourCount);
        lineChartManager.setData(data, false, true, hourCount, isSuccess);
    }

    private float getSumVal(List<List<Float>> data, int interval, boolean isBSensorInstall) {
        float sum = 0;
        if (CollectionUtil.isListNotEmpty(data)) {
            List<Float> sumData = getSumData(data, isBSensorInstall);
            for (Float val : sumData) {
                sum = sum + val;
            }
        }
        return sum * interval / 60;
    }

    private List<Float> getSumData(List<List<Float>> data, boolean isBSensorInstall) {
        List<Float> sumData = new ArrayList<>();
        if (CollectionUtil.isListNotEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                List<Float> sonData = data.get(i);
                float sum = 0;
                if (CollectionUtil.isListNotEmpty(sonData)) {
                    if (sonData.size() > 1) {
                        sum = sum + sonData.get(1);
                    }
                    if (sonData.size() > 2) {
                        if (isBSensorInstall) {
                            sum = sum + sonData.get(2);
                        }
                    }
                    if (sonData.size() > 3) {
                        sum = sum + sonData.get(3);
                    }
                    sumData.add(sum);
                    Collections.sort(sumData);
                }
            }
        }
        return sumData;
    }

    public void setData(List<List<Float>> data) {
        this.data = data;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }
}
