package com.dinsafer.module.powerstation.electricity.chart.marker;


import android.content.Context;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.TimeUtil;
import com.github.mikephil.charting.components.MarkerView;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.utils.Utils;

public class UsageDayMarkerView extends MarkerView {

    private final TextView tvValue;
    private final TextView tvTime;

    public UsageDayMarkerView(Context context) {
        super(context,  R.layout.view_usage_day_marker);
        tvValue = findViewById(R.id.tv_value);
        tvTime = findViewById(R.id.tv_time);
    }

    @Override
    public void refreshContent(Entry e, Highlight highlight) {
        tvValue.setText(e.getVal()+"w");
        tvTime.setText(TimeUtil.minute2HourMinute(e.getXIndex()));
    }

    @Override
    public int getXOffset(float xpos) {
        return -getWidth()/2;
    }

    @Override
    public int getYOffset(float ypos) {
        return -getHeight();
    }
}
