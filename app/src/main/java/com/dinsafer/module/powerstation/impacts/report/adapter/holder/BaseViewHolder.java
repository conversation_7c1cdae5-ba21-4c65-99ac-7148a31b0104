package com.dinsafer.module.powerstation.impacts.report.adapter.holder;

import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.module.powerstation.impacts.report.bean.IMessageInfo;

import java.util.List;

/**
 * Created by chengz
 *
 * @date 2017/8/3.
 */

public abstract class BaseViewHolder<T, V extends ViewDataBinding> extends RecyclerView.ViewHolder {

    public V mBinding;
    public BaseViewHolder(View itemView) {
        super(itemView);
        mBinding = DataBindingUtil.bind(itemView);
    }

    public abstract void setData(int position, T messageInfo, List<IMessageInfo> data);
}
