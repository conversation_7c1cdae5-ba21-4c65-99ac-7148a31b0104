package com.dinsafer.module.powerstation.impacts;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentModeSuggestionBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.common.utils.DensityUtil;

public class ModeSuggestionFragment extends MyBaseFragment<FragmentModeSuggestionBinding> {

    private static final String KEY_POSITION = "key_position";
    private static final String KEY_TYPE = "key_type";
    private static final String KEY_SUGGESTION = "key_suggestion";
    private static OnCreatedListener mCreatedListener;

    public static ModeSuggestionFragment newInstance(int position, String suggestion, int type, OnCreatedListener createdListener) {
        ModeSuggestionFragment fragment = new ModeSuggestionFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_POSITION, position);
        bundle.putInt(KEY_TYPE, type);
        bundle.putString(KEY_SUGGESTION, suggestion);
        fragment.setArguments(bundle);
        mCreatedListener = createdListener;
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_mode_suggestion;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        Bundle bundle = getArguments();
        int position = bundle.getInt(KEY_POSITION);
        int type = bundle.getInt(KEY_TYPE);
        String suggestion = bundle.getString(KEY_SUGGESTION);
        if (type == 1) {
            mBinding.llParent.setPadding(DensityUtil.dp2px(getContext(), 17),
                    DensityUtil.dp2px(getContext(), 67),
                    DensityUtil.dp2px(getContext(), 17),
                    DensityUtil.dp2px(getContext(), 10));
            mBinding.tvSuggestion.setLocalText(suggestion);
        } else {
            mBinding.tvSuggestion.setText(suggestion);
        }
        if (mCreatedListener != null) {
            mCreatedListener.onCreated(inflateView, position);
        }
    }

    public interface OnCreatedListener {
        void onCreated(View view, int position);
    }
}
