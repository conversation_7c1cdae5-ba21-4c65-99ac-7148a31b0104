package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsElectricitySupplierBinding;
import com.dinsafer.module_home.bean.ElectricitySupplierBean;
import com.dinsafer.ui.rv.BindModel;

public class PSElectricitySupplierModel extends BindModel<ItemPsElectricitySupplierBinding> {

    private ElectricitySupplierBean electricitySupplierBean;

    public PSElectricitySupplierModel(Context context, ElectricitySupplierBean electricitySupplierBean) {
        super(context);
        this.electricitySupplierBean = electricitySupplierBean;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_electricity_supplier;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsElectricitySupplierBinding binding) {
        binding.tvName.setText(electricitySupplierBean.getName());
    }

    public ElectricitySupplierBean getElectricitySupplierBean() {
        return electricitySupplierBean;
    }

    public void setElectricitySupplierBean(ElectricitySupplierBean electricitySupplierBean) {
        this.electricitySupplierBean = electricitySupplierBean;
    }
}
