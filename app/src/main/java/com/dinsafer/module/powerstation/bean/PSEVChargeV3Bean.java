package com.dinsafer.module.powerstation.bean;

public class PSEVChargeV3Bean {

    public static final int LOWER_UTILITY_RATE = 1;
    public static final int SCHEDULE_CHARGE = 3;
    public static final int INSTANT_CHARGE_FULL = 4;
    public static final int INSTANT_CHARGE_FIXED = 5;

    private boolean selected;
    private String title;
    private String subTitle;
    // LOWER_UTILITY_RATE SCHEDULE_CHARGE INSTANT_CHARGE_FULL INSTANT_CHARGE_FIXED 中一种
    private int type;
    private int value;
    private int maxValue = 100;
    private int tempValue;
    // -1 未应用  0 应用中  1 已应用
    private int status = -1;
    private int statusHelper;
    private boolean isSameSelected = true;
    private boolean isReset;

    public PSEVChargeV3Bean(String title, String subTitle, int type) {
        this.title = title;
        this.subTitle = subTitle;
        this.type = type;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        setSameSelected(this.selected == selected);
        this.selected = selected;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }

    public int getTempValue() {
        return tempValue;
    }

    public void setTempValue(int tempValue) {
        this.tempValue = tempValue;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatusHelper() {
        return statusHelper;
    }

    public void setStatusHelper(int statusHelper) {
        this.statusHelper = statusHelper;
    }

    public boolean isSameSelected() {
        return isSameSelected;
    }

    public void setSameSelected(boolean sameSelected) {
        isSameSelected = sameSelected;
    }

    public boolean isReset() {
        return isReset;
    }

    public void setReset(boolean reset) {
        isReset = reset;
    }
}
