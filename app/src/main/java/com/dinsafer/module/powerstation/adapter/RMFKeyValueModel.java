package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemRmfKeyValueBinding;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.dinsafer.ui.rv.BindModel;

public class RMFKeyValueModel extends BindModel<ItemRmfKeyValueBinding> {

    private Context context;
    private String key;
    private String value;
    private boolean showLine;
    private boolean isAIValue;
    private boolean isEnabled = true;
    private boolean showArrow;

    public RMFKeyValueModel(Context context) {
        super(context);
        this.context = context;
    }

    public RMFKeyValueModel(Context context, String key) {
        super(context);
        this.context = context;
        this.key = key;
    }

    public RMFKeyValueModel(Context context, String key, boolean showLine) {
        super(context);
        this.context = context;
        this.key = key;
        this.showLine = showLine;
    }

    public RMFKeyValueModel(Context context, String key, boolean showLine, boolean isEnabled) {
        super(context);
        this.context = context;
        this.key = key;
        this.showLine = showLine;
        this.isEnabled = isEnabled;
    }

    public RMFKeyValueModel(Context context, String key, String value) {
        super(context);
        this.context = context;
        this.key = key;
        this.value = value;
    }

    public RMFKeyValueModel(Context context, String key, String value, boolean showLine) {
        super(context);
        this.context = context;
        this.key = key;
        this.value = value;
        this.showLine = showLine;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_rmf_key_value;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemRmfKeyValueBinding binding) {
        if (key != null) {
            binding.tvKey.setLocalText(key);
        }
        if (value != null) {
            binding.tvValue.setLocalText(value);
        }
        if (isAIValue) {
            int[] colors = AIColorUtil.getAIColor(context);
            float[] positions = AIColorUtil.getAIColorPosition();
            binding.tvValue.setAIColorShader(colors, positions);
        } else {
            int normalColor = context.getResources().getColor(R.color.color_white_02);
            binding.tvValue.removeAIShader(normalColor);
        }
        binding.ivSolar.setVisibility(isAIValue ? View.VISIBLE : View.GONE);
        binding.viewLine.setVisibility(showLine ? View.VISIBLE : View.GONE);
        binding.clParent.setAlpha(isEnabled ? 1f : 0.5f);
        binding.clParent.setEnabled(isEnabled);
        binding.ivArrow.setVisibility(showArrow ? View.VISIBLE : View.GONE);
    }

    public boolean isEnabled() {
        return isEnabled;
    }

    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean isShowLine() {
        return showLine;
    }

    public void setShowLine(boolean showLine) {
        this.showLine = showLine;
    }

    public boolean isAIValue() {
        return isAIValue;
    }

    public void setAIValue(boolean AIValue) {
        isAIValue = AIValue;
    }

    public boolean isShowArrow() {
        return showArrow;
    }

    public void setShowArrow(boolean showArrow) {
        this.showArrow = showArrow;
    }
}
