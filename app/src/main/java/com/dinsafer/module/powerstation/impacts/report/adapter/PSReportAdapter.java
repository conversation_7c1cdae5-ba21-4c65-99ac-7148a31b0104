package com.dinsafer.module.powerstation.impacts.report.adapter;

import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.module.powerstation.impacts.report.adapter.holder.BaseViewHolder;
import com.dinsafer.module.powerstation.impacts.report.adapter.holder.TextAcceptViewHolder;
import com.dinsafer.module.powerstation.impacts.report.adapter.holder.TextSendViewHolder;
import com.dinsafer.module.powerstation.impacts.report.adapter.listener.OnItemChildListener;
import com.dinsafer.module.powerstation.impacts.report.bean.IMessageInfo;

import java.util.List;

public class PSReportAdapter extends RecyclerView.Adapter<BaseViewHolder> {

    private String timezone;
    private OnItemChildListener itemChildListener;
    private List<IMessageInfo> messageInfoList;

    public PSReportAdapter(String timezone) {
        this.timezone = timezone;
    }

    public List<IMessageInfo> getMessageInfoList() {
        return messageInfoList;
    }

    public void setMessageInfoList(List<IMessageInfo> messageInfoList) {
        this.messageInfoList = messageInfoList;
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder viewHolder = null;
        switch (viewType) {
            case IMessageInfo.ACCEPT_TEXT:
                viewHolder = new TextAcceptViewHolder(parent, timezone);
                break;

            case IMessageInfo.SEND_TEXT:
                viewHolder = new TextSendViewHolder(timezone, parent, itemChildListener);
                break;
        }
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull BaseViewHolder holder, int position) {
        holder.setData(position, messageInfoList.get(position), messageInfoList);
    }

    @Override
    public int getItemCount() {
        if (messageInfoList == null) {
            return 0;
        } else {
            return messageInfoList.size();
        }
    }

    @Override
    public int getItemViewType(int position) {
        return messageInfoList.get(position).getType();
    }

    public void setItemChildListener(OnItemChildListener itemChildListener) {
        this.itemChildListener = itemChildListener;
    }
}
