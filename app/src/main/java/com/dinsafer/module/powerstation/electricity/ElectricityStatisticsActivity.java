package com.dinsafer.module.powerstation.electricity;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;

import androidx.databinding.ViewDataBinding;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.GridLayoutManager;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityElectricityStatisticsBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.BaseFragmentActivity;
import com.dinsafer.module.powerstation.adapter.ElectricityTabAdapter;
import com.dinsafer.module.powerstation.electricity.bean.ElectricityTabBean;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypeDialog;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.IChartModelController;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;


import java.util.ArrayList;
import java.util.List;

/**
 * 功率/用电统计
 */
public class ElectricityStatisticsActivity extends BaseFragmentActivity {

    private ActivityElectricityStatisticsBinding mBinding;
    private static final String TAG = ElectricityStatisticsActivity.class.getSimpleName();
    private ElectricityTabAdapter mElectricityTabAdapter;
    private CommonPagerAdapter mAdapter;
    private int mCurrentIndex;
    private ElectricityCircleTypeDialog mElectricityCircleTypeDialog;
    private ArrayList<BaseFragment> fragmentList = new ArrayList<BaseFragment>();

    @Override
    protected boolean initVariables() {
        return true;
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_electricity_statistics);
        initTitleBar();
        initRvTab();
        initViewPager();
    }

    /**
     * 标题栏
     */
    private void initTitleBar() {
//        mBinding.commonBar.vDivider.setVisibility(View.GONE);
//        mBinding.commonBar.commonBarTitle.setText("2022.12.20");
//        mBinding.commonBar.commonBarRightText.setVisibility(View.VISIBLE);
//        mBinding.commonBar.commonBarRightText.setText(Local.s(getResources().getString(R.string.electricity_day)));
//        mBinding.commonBar.commonBarBack.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                finish();
//            }
//        });
//
//        mBinding.commonBar.commonBarRightText.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                showElectricityCircleTypeDialog();
//            }
//        });
    }

    /**
     * 显示周期类型弹窗
     */
    private void showElectricityCircleTypeDialog() {
        if (mElectricityCircleTypeDialog == null) {
            mElectricityCircleTypeDialog = new ElectricityCircleTypeDialog();
            mElectricityCircleTypeDialog.setTypeSelectedListener(new ElectricityCircleTypeDialog.OnTypeSelectedListener() {
                @Override
                public void onSelected(String type, CycleType cycleType) {
//                    mBinding.commonBar.commonBarRightText.setText(type);
//                    ((BaseChartFragment<IChartModelController, ViewDataBinding>)fragmentList.get(mCurrentIndex)).resetChart(cycleType);

                }
            });
        }
        if (mElectricityCircleTypeDialog!=null && !mElectricityCircleTypeDialog.isAdded()) {
            mElectricityCircleTypeDialog.show(getSupportFragmentManager(), ElectricityCircleTypeDialog.TAG);
        }
    }

    /**
     * Tab
     */
    private void initRvTab() {

    }

    /**
     * 设置tab和viewpager索引
     * @param index
     */
    private void setCurrentTab(int index, boolean isTab) {
        if (index == mCurrentIndex) return;
        mCurrentIndex = index;
        for (ElectricityTabBean electricityTabBean : mElectricityTabAdapter.getData()) {
            electricityTabBean.setSelected(false);
        }
        ElectricityTabBean elt = mElectricityTabAdapter.getItem(index);
        elt.setSelected(true);
        mElectricityTabAdapter.notifyDataSetChanged();
        if (isTab) {
            mBinding.vpElectricity.setCurrentItem(index);
        }
    }

    /**
     * ViewPager
     */
    private void initViewPager() {
//        fragmentList.add(UsageFragment.newInstance());
//        fragmentList.add(UsageFragment.newInstance());
//        fragmentList.add(UsageFragment.newInstance());
//        fragmentList.add(UsageFragment.newInstance());
        mAdapter = new CommonPagerAdapter(getSupportFragmentManager(), fragmentList);
        mBinding.vpElectricity.setAdapter(mAdapter);
        mBinding.vpElectricity.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                setCurrentTab(i, false);
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mBinding.vpElectricity.setOffscreenPageLimit(4);
        mBinding.vpElectricity.setCurrentItem(mCurrentIndex);
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void onDestroy() {
        DDLog.i(TAG, "onDestroy");
        super.onDestroy();
    }
}