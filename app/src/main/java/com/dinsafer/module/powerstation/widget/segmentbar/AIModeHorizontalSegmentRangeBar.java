package com.dinsafer.module.powerstation.widget.segmentbar;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

public class AIModeHorizontalSegmentRangeBar extends View {

    private Context mContext;
    private List<Segment> mSegments;
    private int mBarWidth;
    private Paint mFillPaint;
    private Paint mThumbPaint;
    private int mThumbColor;
    private int mRadius;
    private int mTouchRadius;
    private int mRoundRadius;
    private int mMinColor;
    private int mMaxColor;
    private int mPercentTextColor;
    private int mPercentTextSize;
    private boolean isDrag;
    private HashMap<String, RectF> mRectFMap = new HashMap<>();


    private Paint mLeftTextPaint;
    private Rect mTextRect;
    private RectF mTrackRect;

    private float mMinThumbCenterX;
    private float mMaxThumbCenterX;
    private float mThumbYDistance; // 0-1
    private float mDelta = 100f;
    private boolean isMinThumbOnDragging;
    private boolean isMaxThumbOnDragging;
    private float mTrackLength;
    private float mMinProgress = 40f;
    private float mMaxProgress = 80f;
    private float mMinX;
    private float mHalfY;

    private float mLeft;
    private float mMinProgressFlag;
    private float mMaxProgressFlag;
    private boolean isMinVibrate = true;
    private boolean isMaxVibrate = true;
    private float mMaxLimitedProgress = 100;


    public AIModeHorizontalSegmentRangeBar(Context context) {
        this(context, null);
    }

    public AIModeHorizontalSegmentRangeBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AIModeHorizontalSegmentRangeBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttrs(context, attrs);
        init();
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        mContext = context;
        TypedArray a = context.getTheme().obtainStyledAttributes(
                attrs, R.styleable.SegmentRangeBar, 0, 0);
        mBarWidth = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_width, DensityUtil.dp2px(context, 10));
        mRadius = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_radius, DensityUtil.dp2px(context, 2));
        mTouchRadius = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_touched_radius, DensityUtil.dp2px(context, 4));
        mRoundRadius = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_round_radius, DensityUtil.dp2px(context, 10));
        mMinColor = a.getColor(R.styleable.SegmentRangeBar_srb_min_color, Color.WHITE);
        mMaxColor = a.getColor(R.styleable.SegmentRangeBar_srb_max_color, Color.WHITE);
        mPercentTextColor = a.getColor(R.styleable.SegmentRangeBar_srb_max_color, Color.WHITE);
        mPercentTextSize = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_percent_text_size, DensityUtil.sp2px(context, 30));
        mThumbYDistance = a.getFloat(R.styleable.SegmentRangeBar_srb_thumbY_distance, 0.15f);
        mThumbColor = a.getColor(R.styleable.SegmentRangeBar_srb_thumb_color, Color.parseColor("#FFFFFFFF"));
        isDrag = a.getBoolean(R.styleable.SegmentRangeBar_srb_is_drag, true);
        a.recycle();
    }

    private void init() {
        mFillPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mFillPaint.setStyle(Paint.Style.FILL);

        mThumbPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mThumbPaint.setStyle(Paint.Style.FILL);
        mThumbPaint.setColor(mThumbColor);

        mLeftTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mLeftTextPaint.setFakeBoldText(true);

        mTextRect = new Rect();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mTrackRect = new RectF(
                getPaddingLeft() + mTouchRadius,
                getPaddingTop() + DensityUtil.dp2px(mContext, 28), // 原20dp改为30dp
                getWidth() - getPaddingRight() - mTouchRadius,
                getPaddingTop() + DensityUtil.dp2px(mContext, 28) + mBarWidth - DensityUtil.dp2px(mContext, 4)
        );
        mTrackLength = mTrackRect.right - mTrackRect.left;
        mHalfY = (mTrackRect.top + mTrackRect.bottom) / 2;
        mLeft = mTrackRect.left + mTouchRadius;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int segmentRangeSize = mSegments == null ? 0 : mSegments.size();
        if (segmentRangeSize > 0) {
            for (int segmentRangeIndex = 0; segmentRangeIndex < segmentRangeSize; segmentRangeIndex++) {
                Segment segmentRange = mSegments.get(segmentRangeIndex);
                drawSegmentRange(canvas, segmentRange, segmentRangeIndex, segmentRangeSize);
            }
            drawBar(canvas);
            int lineWidth = DensityUtil.dp2px(mContext, 1);
            if (!isMinThumbOnDragging)
                mMinThumbCenterX = mTrackRect.left + mTrackLength / mDelta * mMinProgress;
            if (!isMaxThumbOnDragging)
                mMaxThumbCenterX = mTrackRect.left + mTrackLength / mDelta * mMaxProgress;
            if (isDrag) {
                drawCircle(canvas, mMinThumbCenterX, getPaddingTop() + mBarWidth / 2 + DensityUtil.dp2px(mContext, 26), isMinThumbOnDragging ? mTouchRadius : mRadius, mMinColor);
            } else {
                drawRoundRect(canvas, mMinThumbCenterX - lineWidth, mHalfY - mTouchRadius,
                        mMinThumbCenterX + lineWidth, mHalfY + mTouchRadius, 22, 22);
            }
            drawPercentText(canvas, 2, mMinThumbCenterX, getMinProgress());
            if (isDrag) {
                drawCircle(canvas, mMaxThumbCenterX, getPaddingTop() + mBarWidth / 2 + DensityUtil.dp2px(mContext, 26), isMaxThumbOnDragging ? mTouchRadius : mRadius, mMaxColor);
            } else {
                drawRoundRect(canvas, mMaxThumbCenterX - lineWidth, mHalfY - mTouchRadius,
                        mMaxThumbCenterX + lineWidth, mHalfY + mTouchRadius, 22, 22);
            }
            drawPercentText(canvas, 3, mMaxThumbCenterX, getMaxProgress());
        }
    }


    private int getContentWidth() {
        return getWidth() - getPaddingLeft() - getPaddingRight();
    }

    /**
     * 分段条
     *
     * @param canvas
     * @param segmentRange
     * @param segmentRangeIndex
     * @param segmentRangeSize
     */
    private void drawSegmentRange(Canvas canvas, Segment segmentRange, int segmentRangeIndex, int segmentRangeSize) {
        float singleSegmentWidth = mTrackLength * (segmentRange.getMaxValue() - segmentRange.getMinValue());
        float segmentLeft = mTrackRect.left + mTrackLength * (segmentRange.getMinValue());
        float segmentRight = segmentLeft + singleSegmentWidth;
        RectF rectBounds = mRectFMap.get(String.valueOf(segmentRangeIndex));
        if (rectBounds == null) {
            rectBounds = new RectF();
            mRectFMap.put(String.valueOf(segmentRangeIndex), rectBounds);
        }
        rectBounds.set((int) (segmentLeft), (getPaddingTop() + mBarWidth) / 2, (int) (segmentRight), (getPaddingTop() + mBarWidth) / 2);
//        if (segmentRangeIndex == 1) {
//            mMinX = rectBounds.right;
//        }
        mFillPaint.setColor(segmentRange.getColor());
    }

    private void drawBar(Canvas canvas) {
        float[] pos = {mSegments.get(0).getMinValue(), mSegments.get(0).getMaxValue(),
                mSegments.get(1).getMinValue(), mSegments.get(1).getMaxValue(),
                mSegments.get(2).getMinValue(), mSegments.get(2).getMaxValue(),
                mSegments.get(3).getMinValue(), mSegments.get(3).getMaxValue(),
                mSegments.get(4).getMinValue(), mSegments.get(4).getMaxValue()};
        int[] colors = {mSegments.get(0).getColor(), mSegments.get(0).getColor(),
                mSegments.get(1).getColor(), mSegments.get(1).getColor(),
                mSegments.get(2).getColor(), mSegments.get(2).getColor(),
                mSegments.get(3).getColor(), mSegments.get(3).getColor(),
                mSegments.get(4).getColor(), mSegments.get(4).getColor()};
        mFillPaint.setShader(new LinearGradient(mTrackRect.left, 0,
                mTrackRect.right, 0, colors, pos, Shader.TileMode.CLAMP));
        canvas.drawRoundRect(
                mTrackRect,
                mRoundRadius,
                mRoundRadius,
                mFillPaint
        );
    }

    /**
     * 画圆
     *
     * @param canvas
     * @param x
     * @param y
     * @param radius
     * @param color
     */
    private void drawCircle(Canvas canvas, float x, float y, int radius, int color) {
        canvas.drawCircle(x, y, radius, mThumbPaint);
    }

    private void drawRoundRect(Canvas canvas, float left, float top, float right, float bottom, int rx, int ry) {
        canvas.drawRoundRect(left, top, right, bottom, rx, ry, mThumbPaint);
    }

    /**
     * 画百分比文本
     *
     * @param canvas
     * @param index
     * @param centerX
     * @param percent
     */
    private void drawPercentText(Canvas canvas, int index, float centerX, float percent) {
        mLeftTextPaint.setColor(mPercentTextColor);
        mLeftTextPaint.setTextSize(mPercentTextSize);
        String text = (int) percent + "%";
        mLeftTextPaint.getTextBounds(text, 0, text.length(), mTextRect);

        // MODIFIED: 计算文本垂直位置（移动到轨道上方）
        float textBaseLineY = mTrackRect.top - DensityUtil.dp2px(mContext, 8); // 在轨道上方8dp处

        float x = centerX - mTextRect.width() / 2f;
        float rightLimit = getMeasuredWidth() - getPaddingRight() - DensityUtil.dp2px(mContext, 2);

        // MODIFIED: 保持原有的水平位置逻辑不变，只调整垂直位置
        if (x + mTextRect.width() >= rightLimit) {
            x = rightLimit - mTextRect.width();
        }
        if (x <= getPaddingLeft()) {
            x = getPaddingLeft();
        }

        // MODIFIED: 修改绘制Y坐标为计算后的textBaseLineY
        canvas.drawText(text, x, textBaseLineY, mLeftTextPaint);
    }

    private float mMinDX;
    private float mMaxDX;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isDrag) return super.onTouchEvent(event);
        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                performClick();
                getParent().requestDisallowInterceptTouchEvent(true);

                isMinThumbOnDragging = isMinThumbTouched(event);
                isMaxThumbOnDragging = isMaxThumbTouched(event);
                if (isMinThumbOnDragging || isMaxThumbOnDragging) {
                    mMinProgressFlag = mMinProgress;
                    mMaxProgressFlag = mMaxProgress;
                    invalidate();
                }

                mMinDX = mMinThumbCenterX - event.getX();
                mMaxDX = mMaxThumbCenterX - event.getX();
                break;
            case MotionEvent.ACTION_MOVE:
                if (isMinThumbOnDragging || isMaxThumbOnDragging) {
                    boolean flag = true;
                    if (isMinThumbOnDragging) {
                        mMinThumbCenterX = event.getX() + mMinDX;
                        if (mMinThumbCenterX <= mMinX) {
                            mMinThumbCenterX = mMinX;
                        }
                        if (mMinThumbCenterX > (mMaxThumbCenterX - mTrackLength * mThumbYDistance)) {
                            mMinThumbCenterX = mMaxThumbCenterX - mTrackLength * mThumbYDistance;
                        }


                        if (mMinThumbCenterX > mTrackRect.right) {
                            mMinThumbCenterX = mTrackRect.right;
                        }
                        if (mMinThumbCenterX < mTrackRect.left) {
                            mMinThumbCenterX = mTrackRect.left;
                        }

                        if (flag) {
                            mMinProgress = calculateProgress1();
                            if (mSegments != null && mSegments.size() >= 5) {
                                mSegments.get(2).setMaxValue(mMinProgress / 100f);
                                mSegments.get(3).setMinValue(mMinProgress / 100f);
                            }
                            if (Math.abs(mMinProgress - mMinProgressFlag) >= 5 && isMinVibrate) {
                                if (mProgressListener != null) {
                                    mProgressListener.onAlert();
                                }
                                isMinVibrate = false;
                            }

                            if (mProgressListener != null) {
                                mProgressListener.onProgress1Changed(this, getMinProgress(), getMinProgressFloat(), true);
                            }
                            invalidate();
                        }
                    }
                    if (isMaxThumbOnDragging) {
                        mMaxThumbCenterX = event.getX() + mMaxDX;
                        if (mMaxThumbCenterX < (mMinThumbCenterX + mTrackLength * mThumbYDistance)) {
                            mMaxThumbCenterX = mMinThumbCenterX + mTrackLength * mThumbYDistance;
                        }
                        float maxRight = mTrackRect.right - (100f-mMaxLimitedProgress)/100f*mTrackRect.right;
                        if (mMaxThumbCenterX > maxRight) {
                            mMaxThumbCenterX = maxRight;
                        }
                        if (mMaxThumbCenterX < mLeft) {
                            mMaxThumbCenterX = mLeft;
                        }

                        if (flag) {
                            mMaxProgress = calculateMaxProgress();
                            if (mSegments != null && mSegments.size() >= 5) {
                                mSegments.get(3).setMaxValue(mMaxProgress / 100f);
                                mSegments.get(4).setMinValue(mMaxProgress / 100f);
                            }
                            if (Math.abs(mMaxProgress - mMaxProgressFlag) >= 5 && isMaxVibrate) {
//                                mProgressFlag2 = mProgress2;
                                if (mProgressListener != null) {
                                    mProgressListener.onAlert();
                                }
                                isMaxVibrate = false;
                            }
                            if (mProgressListener != null) {
                                mProgressListener.onProgress2Changed(this, getMaxProgress(), getMaxProgressFloat(), true);
                            }
                            invalidate();
                        }
                    }
                }

                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(false);
                isMinThumbOnDragging = false;
                isMaxThumbOnDragging = false;
                isMinVibrate = true;
                isMaxVibrate = true;
                mMinProgressFlag = mMinProgress;
                mMaxProgressFlag = mMaxProgress;
                if (mProgressListener != null) {
                    if (isMinThumbOnDragging) {
                        mProgressListener.onProgress1Changed(this, getMinProgress(), getMinProgressFloat(), true);
                        mProgressListener.getProgress1OnActionUp(this, getMinProgress(), getMinProgressFloat());
                    }
                    if (isMinThumbOnDragging) {
                        mProgressListener.onProgress2Changed(this, getMaxProgress(), getMaxProgressFloat(), true);
                        mProgressListener.getProgress2OnActionUp(this, getMaxProgress(), getMaxProgressFloat());
                    }
                }
                invalidate();
                break;
        }

        return isMinThumbOnDragging || isMaxThumbOnDragging || super.onTouchEvent(event);
    }

    /**
     * 第一条进度
     *
     * @return
     */
    private float calculateProgress1() {
        return (mMinThumbCenterX + mTouchRadius - mLeft) * mDelta / mTrackLength;
    }

    public int getMinProgress() {
        return Math.round(processMinProgress());
    }

    public float getMinProgressFloat() {
        return formatFloat(processMinProgress());
    }

    private float processMinProgress() {
        final float progress = mMinProgress;
        return progress;
    }

    /**
     * 第二条进度
     *
     * @return
     */
    private float calculateMaxProgress() {
        return (mMaxThumbCenterX + mTouchRadius - mLeft) * mDelta / mTrackLength;
    }

    public int getMaxProgress() {
        return Math.round(processMaxProgress());
    }

    public float getMaxProgressFloat() {
        return formatFloat(processMaxProgress());
    }

    private float processMaxProgress() {
        final float progress = mMaxProgress;

        return progress;
    }

    private float formatFloat(float value) {
        BigDecimal bigDecimal = BigDecimal.valueOf(value);
        return bigDecimal.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
    }

    /**
     * Detect effective touch of thumb
     */
    private boolean isMinThumbTouched(MotionEvent event) {
        if (!isEnabled())
            return false;

        float touchX = event.getX();
        float touchY = event.getY();
        float distance = mTrackLength / mDelta * mMinProgress;
        float x = mTrackRect.left + distance;
        float y = (mTrackRect.top + mTrackRect.bottom) / 2f;
        return touchX >= x - mTouchRadius && touchX <= x + mTouchRadius
                && touchY >= y - mTouchRadius && touchY <= x + mTouchRadius;
    }

    /**
     * Detect effective touch of thumb
     */
    private boolean isMaxThumbTouched(MotionEvent event) {
        if (!isEnabled())
            return false;

        float touchX = event.getX();
        float touchY = event.getY();
        float distance = mTrackLength / mDelta * mMaxProgress;
        float x = mTrackRect.left + distance;
        float y = (mTrackRect.top + mTrackRect.bottom) / 2f;
        return touchX >= x - mTouchRadius && touchX <= x + mTouchRadius
                && touchY >= y - mTouchRadius && touchY <= x + mTouchRadius;
    }

    public void setSegmentRanges(List<Segment> data) {
        mSegments = data;
        invalidate();
        requestLayout();
    }

    public void setSegmentRanges(List<Segment> data, float minProgress, float maxProgress) {
        mSegments = data;
        this.mMinProgress = minProgress;
        this.mMaxProgress = maxProgress;
        invalidate();
        requestLayout();
    }

    public void setMinProgress(float minProgress) {
        this.mMinProgress = minProgress;
    }

    public void setMaxProgress(float maxProgress) {
        this.mMaxProgress = maxProgress;
    }

    public void setDrag(boolean isDrag) {
        if (isDrag == this.isDrag) return;
        this.isDrag = isDrag;
        invalidate();
    }

    public void setMaxLimitedProgress(float maxLimitedProgress) {
        if (maxLimitedProgress<0) {
            mMaxLimitedProgress = 0;
        } else if (maxLimitedProgress>100) {
            mMaxLimitedProgress = 100;
        } else {
            mMaxLimitedProgress = maxLimitedProgress;
        }
    }

    public void setMinLimitedProgress(float fixedMinProgress) {
        mMinX = mTrackRect.left + mTrackLength * fixedMinProgress;
    }

    private OnProgressChangedListener mProgressListener;

    public void setProgressListener(OnProgressChangedListener progressListener) {
        this.mProgressListener = progressListener;
    }

    public interface OnProgressChangedListener {

        void onProgress1Changed(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void getProgress1OnActionUp(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat);

        void getProgress1OnFinally(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void onProgress2Changed(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void getProgress2OnActionUp(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat);

        void getProgress2OnFinally(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void onAlert();
    }
}
