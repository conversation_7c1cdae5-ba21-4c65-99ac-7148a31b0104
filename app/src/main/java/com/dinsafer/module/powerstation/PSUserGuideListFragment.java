package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsUserGuideListBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.adapter.PSUserGuideListAdapter;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 18:15
 * @description :
 */
public class PSUserGuideListFragment extends MyBaseFragment<FragmentPsUserGuideListBinding> {

    private String mDeviceId;
    private String mSubcategory;
    private PSUserGuideListAdapter mPSUserGuideListAdapter;
    private List<PSUserGuideEnum> data = new ArrayList<>();


    public static PSUserGuideListFragment newInstance(String deviceId, String subcategory) {
        PSUserGuideListFragment fragment = new PSUserGuideListFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_user_guide_list;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setText(Local.s(getResources().getString(R.string.ps_user_guide)));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        initRv();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
    }

    private void initRv() {
        mBinding.rvGuide.setLayoutManager(new LinearLayoutManager(getContext()));
        mPSUserGuideListAdapter = new PSUserGuideListAdapter();
        mBinding.rvGuide.setAdapter(mPSUserGuideListAdapter);
        mPSUserGuideListAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                getDelegateActivity().addCommonFragment(UserGuideFragment.newInstance(data.get(position).ordinal(), mDeviceId, mSubcategory));
            }
        });
        if (DinConst.TYPE_BMT_POWERPULSE.equals(mSubcategory)) {
            data.add(PSUserGuideEnum.BATTERY_HEALTH);
        } else {
            data.add(PSUserGuideEnum.SOLAR);
            data.add(PSUserGuideEnum.BATTERY_HEALTH);
            data.add(PSUserGuideEnum.GRID);
        }

        mPSUserGuideListAdapter.setNewData(data);
    }

    public enum PSUserGuideEnum {
        SOLAR(R.string.ps_user_guide_solar),
        BATTERY_HEALTH(R.string.ps_user_guide_battery_health),
        GRID(R.string.ps_user_guide_grid);

        int stringResourcesId;

        PSUserGuideEnum(int stringResourcesId) {
            this.stringResourcesId = stringResourcesId;
        }

        public int getStringResourcesId() {
            return stringResourcesId;
        }
    }
}
