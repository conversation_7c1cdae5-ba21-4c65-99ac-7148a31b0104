package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.Constants;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsContractedDevicesBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.util.Local;

public class PSContractedDevicesModel implements BaseBindModel<ItemPsContractedDevicesBinding> {

    public static final int SELECTABLE_STATUS = 1;
    public static final int OFFLINE_STATUS = 2;
    public static final int OFF_GRID_STATUS = 3;
    public static final int MISMATCH_STATUS = 4;
    public static final int ENABLING_STATUS = 5;
    private String displayID;
    private Device device;
    private boolean showLine;
    // 1.可选 3.离线 4.离网 5.不匹配 6.启用
    private int status;
    private boolean selected;

    public PSContractedDevicesModel(Device device, boolean showLine) {
        this.device = device;
        this.showLine = showLine;
    }

    public PSContractedDevicesModel(String displayID, Device device, boolean showLine) {
        this.displayID = displayID;
        this.device = device;
        this.showLine = showLine;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_contracted_devices;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsContractedDevicesBinding binding) {
        if (device == null) return;
        binding.viewLine.setVisibility(showLine ? View.VISIBLE : View.INVISIBLE);
        String name = DeviceHelper.getString(device, DinConst.INFO_NAME, "");
        binding.tvName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);

//        setStatusStyle(binding.getRoot().getContext(), this.status, binding.tvTag);
//        binding.ivSel.setImageResource(selected ? R.drawable.choose_sel : R.drawable.choose_nor);
//        binding.ivSel.setAlpha(status == SELECTABLE_STATUS ? 1f : 0.3f);
        binding.tvTag.setVisibility(View.GONE);
        binding.ivSel.setVisibility(View.GONE);
        binding.tvId.setText(displayID);
//        binding.tvName.post(() -> {
//            int llWidth = binding.llName.getWidth();
//            int tagWidth = binding.tvTag.getWidth();
//            int maxWidth = llWidth - tagWidth - DensityUtil.dp2px(binding.getRoot().getContext(), 6);
//            binding.tvName.setMaxWidth(maxWidth);
//        });
    }

    private void setStatusStyle(Context context, int status, LocalTextView textView) {
        switch (status) {
            case OFFLINE_STATUS:
                textView.setLocalText(context.getString(R.string.Offline));
                textView.setBackgroundResource(R.drawable.shape_tip_06_2_r3);
                textView.setTextColor(context.getResources().getColor(R.color.color_tip_06));
                break;

            case OFF_GRID_STATUS:
                textView.setLocalText(context.getString(R.string.Offgrid));
                textView.setBackgroundResource(R.drawable.shape_tip_06_2_r3);
                textView.setTextColor(context.getResources().getColor(R.color.color_tip_06));
                break;

            case MISMATCH_STATUS:
                textView.setLocalText(context.getString(R.string.area_mismatch));
                textView.setBackgroundResource(R.drawable.shape_1aff778f_r3);
                textView.setTextColor(context.getResources().getColor(R.color.color_minor_1));
                break;

            case ENABLING_STATUS:
                textView.setLocalText(context.getString(R.string.Enabling));
                textView.setBackgroundResource(R.drawable.shape_brand_light_01_r3);
                textView.setTextColor(context.getResources().getColor(R.color.color_brand_text));
                break;

            default:
                textView.setLocalText("");
                textView.setBackgroundResource(0);
                textView.setTextColor(0);
                break;
        }
    }

    public String getDisplayID() {
        return displayID;
    }

    public void setDisplayID(String displayID) {
        this.displayID = displayID;
    }

    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    public boolean isShowLine() {
        return showLine;
    }

    public void setShowLine(boolean showLine) {
        this.showLine = showLine;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
