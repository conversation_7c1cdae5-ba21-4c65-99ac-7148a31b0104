package com.dinsafer.module.powerstation.utils;

import android.content.Context;
import android.text.TextUtils;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.chart.CustomCombinedChart;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class PriceTodayChartHelper {

    private Context mContext;
    private CustomCombinedChart mPriceChart;
    private CustomCombinedChartManager mPriceChartManager;
    private int mHourCount = 1440;
    private int mInterval = 1;
    private final static int DST_TIME_MINUTE = 1500;
    private final static int SUMMER_TIME_MINUTE = 1380;
    private Map<String, Object> mMonetaryUnitMap;

    private XAxisValueFormatter formatter = (original, index, viewPortHandler) -> {
        String text = "";
        if (mHourCount == DST_TIME_MINUTE) {
            if (index == 0) {
                text = "00:00";
            }  else if (index == 24) {
                text = "23:00";
            }
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            if (index == 0) {
                text = "00:00";
            }  else if (index == 22) {
                text = "23:00";
            }
        } else {
            if (index == 0) {
                text = "00:00";
            } else if (index == 12) {
                text = "12:00";
            } else if (index == 23) {
                text = "23:00";
            }
        }
        return text;
    };

    private YAxisValueFormatter yFormatter = (value, yAxis) -> {
        return "";
    };

    public PriceTodayChartHelper(Context context, CustomCombinedChart priceChart) {
        this.mContext = context;
        this.mPriceChart = priceChart;
        String json = loadJSONFromAsset(context);
        mMonetaryUnitMap = new Gson().fromJson(json, new TypeToken<Map<String, Object>>() {}.getType());
        mPriceChartManager = new CustomCombinedChartManager(mContext, mPriceChart);
        mPriceChartManager.initChart(true, 0.5f, mContext.getResources().getColor(R.color.color_white_04),
                false, false, false, 0f,
                0f, 0f, 15f, 0f, 0, false,
                "", 1200, true);
        initXAxis(0);
        initYAxis();
    }

    public void initXAxis(int labelsToSkip) {
        if (mPriceChartManager == null) return;
        mPriceChartManager.initXAxis(false, false, mContext.getResources().getColor(R.color.color_white_04), 10f,
                true, mContext.getResources().getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                labelsToSkip, formatter);
    }

    private void initYAxis() {
        if (mPriceChartManager == null) return;
        mPriceChartManager.initYAxis(false, 10f, mContext.getResources().getColor(R.color.color_white_03),
                0.5f, false, true, mContext.getResources().getColor(R.color.color_white_04),
                yFormatter, false, 6);
        YAxis yAxis = mPriceChart.getAxisLeft();
        yAxis.setAxisMinValue(0);
    }

    public void setPriceData(List<List<Float>> priceChartData, long startTime, String timezone) {

        if (DDDateUtil.isDSTTransitionDay(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1500;
        } else if (DDDateUtil.isWT2ST(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1380;
        } else {
            mHourCount = 1440;
        }

        int count = priceChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            List<Float> sonData = priceChartData.get(i);
            yVals.add(new BarEntry(new float[]{sonData.get(0), sonData.get(2)}, i));
        }

        CombinedData data = new CombinedData(xVals);
        BarDataSet barDataSet = new BarDataSet(yVals, "");
        int colorsBar[] = new int[]{getColor(R.color.color_tip_07),
                getColor(R.color.color_tip_06)};
        barDataSet.setColors(colorsBar);
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(false);
        barDataSet.setBarSpacePercent(mPriceChart.getWidth() * 1f / count * 1.2f);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        float max = ChartDataUtil.findUpperPriceLimit(getYMax(priceChartData));
        mPriceChartManager.setYAxisMaxMin(max,0, 6);
        mPriceChart.setData(data);
        mPriceChart.invalidate();
        mPriceChartManager.initBarChartRender(DensityUtil.dp2px(mContext, 5), getColor(R.color.color_white_03), 1.0f, 2.0f);

    }

    public float getYMax(List<List<Float>> data) {
        float maxVal = 0f;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            List<Float> sonData = data.get(i);
            float sum = 0;
            if (CollectionUtil.isListNotEmpty(sonData)) {
                sum = sonData.get(0) + sonData.get(2);
            }
            sumData.add(sum);
        }
        if (CollectionUtil.isListNotEmpty(data)) {
            Collections.sort(sumData);
            maxVal = sumData.get(sumData.size() - 1);
        }
        return maxVal;
    }

    public float getAverage(List<List<Float>> data) {
        float average = 0f;
        if (CollectionUtil.isListNotEmpty(data)) {
            float sum = 0f;
            for (int i = 0; i < data.size(); i++) {
                List<Float> sonData = data.get(i);
                if (CollectionUtil.isListNotEmpty(sonData)) {
                    sum = sum + sonData.get(0) + sonData.get(2);
                }
            }
            average = sum / data.size();
        }
        return average;
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

    public String loadJSONFromAsset(Context context) {
        String json = null;
        try {
            InputStream is = context.getAssets().open("monetary_unit.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            json = new String(buffer, "UTF-8");
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
        return json;
    }

    public String getUnit(float value, String showPriceUnit, String unit) {
        String result = showPriceUnit;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, unit);
            if (map != null) {
                result = DeviceHelper.getString(map, value >= 1.0 ? "currency_symbols" : "basic_unit", "");
            }
        }
        return result;
    }

    public double getBasicUnitVal(double value, String unit) {
        double val = value;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, unit);
            if (map != null) {
                val = value * DeviceHelper.getDouble(map, "relationship", 1);
            }
        }
        return val;
    }

    public float getBasicUnitVal(float value, String unit) {
        float val = value;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, unit);
            if (map != null) {
                val = value * (int)DeviceHelper.getDouble(map, "relationship", 1);
            }
        }
        return val;
    }

    public boolean isAheadUnit(String unit) {
        if (!TextUtils.isEmpty(unit) && mMonetaryUnitMap != null) {
            String aheadUnits = DeviceHelper.getString(mMonetaryUnitMap, "ahead_unit", "");
            return aheadUnits.contains(unit);
        }
        return false;
    }
}
