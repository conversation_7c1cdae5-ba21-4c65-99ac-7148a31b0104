package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;


public class ScheduledModeViewPager extends ViewPager {

    private float startX;
    private float startY;
    private float endX;
    private float endY;
    private float touchX, touchY;

    public ScheduledModeViewPager(@NonNull Context context) {
        super(context);
    }

    public ScheduledModeViewPager(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        int action = e.getAction();
        switch (action) {
            case MotionEvent.ACTION_POINTER_DOWN:
            case MotionEvent.ACTION_DOWN:
                touchX = e.getX();
                touchY = e.getY();

        }
        if (endX > 0 && endX > 0) {
            if (touchX > startX && touchX < endX) {
                return false;
            }
        }
        return super.onInterceptTouchEvent(e);
    }


    public void setLimitedPoint(float startX, float endX) {
        this.startX = startX;
        this.endX = endX;
    }

    public float getStartX() {
        return startX;
    }

    public void setStartX(float startX) {
        this.startX = startX;
    }

    public float getStartY() {
        return startY;
    }

    public void setStartY(float startY) {
        this.startY = startY;
    }

    public float getEndX() {
        return endX;
    }

    public void setEndX(float endX) {
        this.endX = endX;
    }

    public float getEndY() {
        return endY;
    }

    public void setEndY(float endY) {
        this.endY = endY;
    }
}
