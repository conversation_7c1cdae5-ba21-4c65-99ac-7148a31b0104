package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.utils.KeyBoardUtil;
import com.dinsafer.common.utils.StringUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemOnGridConfigurationProgressBinding;
import com.dinsafer.model.event.EditTextFocusEvent;
import com.dinsafer.module.powerstation.event.OnGridConfigurationEditEvent;
import com.dinsafer.module.powerstation.event.OnGridConfigurationProgressEvent;
import com.dinsafer.ui.BreakableSeekBar;
import com.dinsafer.ui.rv.BindModel;

import org.greenrobot.eventbus.EventBus;

public class OGCProgressModel extends BindModel<ItemOnGridConfigurationProgressBinding> {

    private Context mContext;
    private String key;
    private String unit;
    private float min;
    private float max;
    private float progress;
    private float multiple;
    private boolean isEditable;
    private boolean isEdit;

    public OGCProgressModel(Context context, String key, String unit, float min, float max, float multiple) {
        super(context);
        this.mContext = context;
        this.key = key;
        this.unit = unit;
        this.min = min;
        this.max = max;
        this.multiple = multiple;
    }

    public OGCProgressModel(Context context, String key, String unit, float min, float max, float progress, float multiple) {
        super(context);
        this.mContext = context;
        this.key = key;
        this.unit = unit;
        this.min = min;
        this.max = max;
        this.progress = progress;
        this.multiple = multiple;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_on_grid_configuration_progress;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemOnGridConfigurationProgressBinding binding) {
        binding.tvKey.setLocalText(key);
        binding.tvValue.setLocalText(StringUtil.retainDecimals(progress * multiple, multiple) + unit);

        binding.clContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isEditable && !isEdit) {
                    isEdit = true;
                    binding.llValue.setVisibility(View.INVISIBLE);
                    binding.llEdit.setVisibility(View.VISIBLE);
                    binding.bsbProgress.setVisibility(View.VISIBLE);
                    binding.bsbProgress.resetData(min * multiple, max * multiple, progress * multiple, multiple);
                    EventBus.getDefault().post(new OnGridConfigurationEditEvent(holder.getAdapterPosition()));
                }
            }
        });
        binding.etValue.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
        binding.ivArrow.setVisibility(isEditable && !isEdit ? View.VISIBLE : View.GONE);
        binding.llValue.setVisibility(!isEdit ? View.VISIBLE : View.INVISIBLE);
        binding.etValue.setText(StringUtil.retainDecimals(progress * multiple, multiple));
        binding.tvUnit.setLocalText(unit);
        binding.llEdit.setVisibility(isEditable && isEdit ? View.VISIBLE : View.INVISIBLE);
        binding.bsbProgress.setVisibility(isEditable && isEdit ? View.VISIBLE : View.GONE);

        binding.llEdit.setOnClickListener(view -> {
            KeyBoardUtil.openKeybord(mContext, binding.etValue);
        });
        binding.tvUnit.setOnClickListener(view -> {
            KeyBoardUtil.openKeybord(mContext, binding.etValue);
        });
        int position = holder.getAdapterPosition();
        binding.bsbProgress.setSeekBarChangeListener(new BreakableSeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(BreakableSeekBar breakableSeekBar, int progress, float progressFloat, boolean fromUser) {
                OGCProgressModel.this.progress = progress / multiple;
                binding.tvValue.setLocalText(StringUtil.retainDecimals(progress, multiple) + unit);
                binding.etValue.setText(StringUtil.retainDecimals(progress, multiple));
                if (position == 6) {
                    EventBus.getDefault().post(new OnGridConfigurationProgressEvent(position, OGCProgressModel.this.progress));
                }
            }
        });
        binding.etValue.setOnFocusChangeListener((view, b) -> {
            if (b) {
                EventBus.getDefault().post(new EditTextFocusEvent(binding.llProgress, holder.getAdapterPosition()));
            }
        });
        binding.etValue.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (binding.bsbProgress.isThumbOnDragging()) return;
                String text = binding.etValue.getText().toString();
                if (TextUtils.isEmpty(text) ||
                        text.equals("-") || text.equals("+")
                        || text.equals(".") || text.equals("-.")
                        || text.equals("+.")) return;
                float val = Float.parseFloat(text);
                if (val >= min && val <= max) {
                    binding.bsbProgress.setProgress(val * multiple);
                    binding.etValue.setTextColor(mContext.getResources().getColor(R.color.color_white_01));
                    binding.tvUnit.setTextColor(mContext.getResources().getColor(R.color.color_white_01));
                } else {
                    binding.etValue.setTextColor(mContext.getResources().getColor(R.color.color_tip_warning));
                    binding.tvUnit.setTextColor(mContext.getResources().getColor(R.color.color_tip_warning));
                }
            }
        });
    }

    public float getMin() {
        return min;
    }

    public void setMin(float min) {
        this.min = min;
    }

    public float getMax() {
        return max;
    }

    public void setMax(float max) {
        this.max = max;
    }

    public float getProgress() {
        return progress;
    }

    public void setProgress(float progress) {
        this.progress = progress;
    }

    public boolean isEditable() {
        return isEditable;
    }

    public void setEditable(boolean editable) {
        isEditable = editable;
        if (!editable) {
            isEdit = false;
        }
    }

    public boolean isEdit() {
        return isEdit;
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
    }
}
