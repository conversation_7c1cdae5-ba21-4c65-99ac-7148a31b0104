package com.dinsafer.module.powerstation.event;

import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;

public class ChartPageChangeEvent {

    private OperateOrientation orientation;

    public ChartPageChangeEvent(OperateOrientation orientation) {
        this.orientation = orientation;
    }

    public OperateOrientation getOrientation() {
        return orientation;
    }

    public void setOrientation(OperateOrientation orientation) {
        this.orientation = orientation;
    }
}
