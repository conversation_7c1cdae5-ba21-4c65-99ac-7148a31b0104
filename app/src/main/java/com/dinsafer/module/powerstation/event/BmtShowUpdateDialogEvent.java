package com.dinsafer.module.powerstation.event;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * 显示BMT升级弹窗事件
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/3/23 15:12
 */
@Keep
public class BmtShowUpdateDialogEvent {
    @NonNull
    private final String deviceId;
    private final String subcategory;
    private final int chipStatus;

    public BmtShowUpdateDialogEvent(@NonNull String deviceId, String subcategory, int chipStatus) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.chipStatus = chipStatus;
    }

    @NonNull
    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public int getChipStatus() {
        return chipStatus;
    }

    @Override
    public String toString() {
        return "BmtShowUpdateDialogEvent{" +
                "deviceId='" + deviceId + '\'' +
                ", subcategory='" + subcategory + '\'' +
                ", chipStatus=" + chipStatus +
                '}';
    }
}
