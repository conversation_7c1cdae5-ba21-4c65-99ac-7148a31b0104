package com.dinsafer.module.powerstation.event;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean;

import java.util.Map;

/**
 * 获取到的BMT版本信息有更新
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/3/21 17:21
 */
@Keep
public class PSFirmWareVersionUpdateEvent {
    private final Map<String, PSFirmWareVersionDetailBean> versionInfoMap;
    private final Device device;

    public PSFirmWareVersionUpdateEvent(Map<String, PSFirmWareVersionDetailBean> versionInfoMap, Device device) {
        this.versionInfoMap = versionInfoMap;
        this.device = device;
    }

    public Map<String, PSFirmWareVersionDetailBean> getVersionInfoMap() {
        return versionInfoMap;
    }

    public Device getDevice() {
        return device;
    }
}
