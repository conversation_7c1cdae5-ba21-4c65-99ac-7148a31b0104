package com.dinsafer.module.powerstation.adapter;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;


import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemReserveModeV2Binding;
import com.dinsafer.module.powerstation.bean.PSReserveModeBeanV2;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.dinsafer.ui.rv.BindModel;

public class PSReserveModeModelV2 extends BindModel<ItemReserveModeV2Binding> {

    private Context mContext;
    private PSReserveModeBeanV2 reserveModeBean;

    public PSReserveModeModelV2(Context context, PSReserveModeBeanV2 reserveModeBean) {
        super(context);
        mContext = context;
        this.reserveModeBean = reserveModeBean;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_reserve_mode_v2;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemReserveModeV2Binding binding) {
        if (reserveModeBean == null) return;
        boolean isSelected = reserveModeBean.isSelected();
        binding.tvTitle.setAlpha(reserveModeBean.isEnabled() ? 1f : 0.5f);
        binding.tvSubtitle.setAlpha(reserveModeBean.isEnabled() ? 1f : 0.5f);
        binding.ivLogo.setAlpha(reserveModeBean.isEnabled() ? 1f : 0.5f);
        binding.tvTag.setVisibility(reserveModeBean.isShowTag() ? View.VISIBLE : View.GONE);
        binding.ivLogo.setImageResource(reserveModeBean.getLogo());
        binding.tvTitle.setLocalText(reserveModeBean.getTitle());
        binding.tvSubtitle.setLocalText(reserveModeBean.getSubtitle());
        int type = reserveModeBean.getType();
        int status = reserveModeBean.getStatus();
        binding.lavLogo.setVisibility(type == 2 ? View.VISIBLE : View.GONE);
        binding.ivLogo.setVisibility(type == 1 ? View.VISIBLE : View.GONE);
        if (type == 2) {
            if (isSelected) {
                binding.lavLogo.setFrame(0);
                binding.lavLogo.setMinAndMaxFrame(0, 100);
                binding.lavLogo.setSpeed(1.0f);
                binding.lavLogo.playAnimation();
            } else {
                binding.lavLogo.setFrame(100);
            }
            int[] colors = AIColorUtil.getAIColor(mContext);
            float[] positions = AIColorUtil.getAIColorPosition();
            binding.tvTitle.setAIColorShader(colors, positions);
            binding.tvApplied.setAIColorShader(colors, positions);
            binding.tvOperate.setAIColorShader(colors, positions);
            binding.viewSelected.setGradientEnabled(true);
            binding.viewStatusBg.setGradientEnabled(true);
        }

        if (type == 1) {
            binding.llStatus.setBackgroundResource(status == 0 ? R.drawable.shape_brand_light_03_right_r16 :
                    R.drawable.shape_brand_primary_right_r16);
        }
        if (!reserveModeBean.isSameSelected()) {
            boolean isEdit = isSelected && status != 1;
            if (status == 1) {
                binding.tvOperate.setVisibility(isSelected ? View.VISIBLE : View.GONE);
            }

            int startMargin = isEdit ? 5 : 0;
            int endMargin = isEdit ? 0 : 5;
            ValueAnimator marginAnimator = ValueAnimator.ofInt(startMargin, endMargin);
            marginAnimator.addUpdateListener(animation -> {
                int val = (Integer) animation.getAnimatedValue();
                if (status == 1) {
                    if (isSelected) {
                        if (binding.viewSelected.getAlpha() < 1.0f) {
                            binding.viewSelected.setAlpha(val / 5f);
                            binding.viewNormal.setAlpha(1f - val / 5f);
                        }
                    } else {
                        binding.viewSelected.setAlpha(1f - val / 5f);
                        binding.viewNormal.setAlpha(val / 5f);
                    }

                } else {
                    binding.viewSelected.setAlpha(1 - val / 5f);
                    binding.viewNormal.setAlpha(val / 5f);
                }
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) binding.clContent.getLayoutParams();
                int marginLeft = status == 1 ? DensityUtil.dp2px(mContext, 55) : DensityUtil.dp2px(mContext, 15) + DensityUtil.dp2px(mContext, val * 8);
                int marginRight = status == 1 ? DensityUtil.dp2px(mContext, 15) : DensityUtil.dp2px(mContext, 75) - DensityUtil.dp2px(mContext, val * 12);
                layoutParams.setMargins(marginLeft,
                        layoutParams.topMargin,
                        marginRight,
                        layoutParams.bottomMargin);
                binding.clContent.requestLayout();
            });
            marginAnimator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animator) {
                    if (isEdit) {
                        binding.tvOperate.setVisibility(View.VISIBLE);
                    }
                }

                @Override
                public void onAnimationEnd(Animator animator) {
                    if (!isEdit && status == -1) {
                        binding.tvOperate.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onAnimationCancel(Animator animator) {

                }

                @Override
                public void onAnimationRepeat(Animator animator) {

                }
            });
            reserveModeBean.setSameSelected(true);
            marginAnimator.setDuration(200);
            marginAnimator.start();
        }
        binding.tvApplied.setVisibility(status == 1 ? View.VISIBLE : View.GONE);
//        setAnimResource(binding.ivStatus, status == 0);
        binding.ivStatus.setVisibility(status == 0 ? View.GONE : View.VISIBLE);
        binding.lavLoading.setVisibility(status == 0 ? View.VISIBLE : View.GONE);
    }

    private void setAnimResource(ImageView imageView, boolean loading) {
        if (loading) {
            imageView.setImageTintList(null);
            imageView.setImageResource(R.drawable.icon_ps_accessories_device_loading);
            imageView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.rotation));
        } else {
            imageView.clearAnimation();
            imageView.setImageResource(R.drawable.radiobox_sel_white);
        }
    }

    public PSReserveModeBeanV2 getReserveModeBean() {
        return reserveModeBean;
    }

    public void setReserveModeBean(PSReserveModeBeanV2 reserveModeBean) {
        this.reserveModeBean = reserveModeBean;
    }
}
