package com.dinsafer.module.powerstation.impacts;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPriceTrackingModeBinding;
import com.dinsafer.model.PointBean;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.widget.segmentbar.HorizontalSegmentRangeBar;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.plugin.widget.util.DensityUtil;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;
import com.google.android.material.resources.TextAppearance;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PriceTrackingModeFragment extends MyBaseFragment<FragmentPriceTrackingModeBinding>
        implements IDeviceCallBack {

    private CommonPagerAdapter mAdapter;
    private ArrayList<BaseFragment> mFragments = new ArrayList<BaseFragment>(); // 将要显示的布局存放到list数组

    private String mDeviceId;
    private String subcategory;
    private Device mPSDevice;
    private boolean isDealt;

    public static PriceTrackingModeFragment newInstance(String deviceId, String subcategory) {
        PriceTrackingModeFragment fragment = new PriceTrackingModeFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_price_tracking_mode;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.ps_is_price_tracking_mode));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> {
            removeSelf();
        });
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(map);
        }

//        setSegment();
//        mBinding.viewPriceTracking.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                PriceTrackingModeView.ModeBean modeBean = new PriceTrackingModeView.ModeBean(0,
//                        0, 40, -20, +20, -40);
//                mBinding.viewPriceTracking.setData(modeBean);
//                mBinding.viewPriceTracking.setReserveValue(15, 50);
//            }
//        }, 200);
//
//        initVp();
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        subcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, subcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    private void setSegment(int emergencyReserve, int smartReserve) {
        List<Segment> segments = new ArrayList<>();
        segments.add(new Segment(0.0f, 0.02f, getString(R.string.power_battery_bar_status_text_5), getColor(R.color.power_station_battery_color_5)));
        segments.add(new Segment(0.02f, 0.12f, getString(R.string.power_battery_bar_status_text_4), getColor(R.color.power_station_battery_color_4)));
        segments.add(new Segment(0.12f, emergencyReserve / 100f, getString(R.string.power_battery_bar_status_text_3), getColor(R.color.power_station_battery_color_3)));
        segments.add(new Segment(emergencyReserve / 100f, smartReserve / 100f, getString(R.string.power_battery_bar_status_text_2), getColor(R.color.power_station_battery_color_2)));
        segments.add(new Segment(smartReserve / 100f, 1.0f, getString(R.string.power_battery_bar_status_text_1), getColor(R.color.power_station_battery_color_1)));
        mBinding.segmentRangeBar.setMinProgress(emergencyReserve);
        mBinding.segmentRangeBar.setMaxProgress(smartReserve);
        mBinding.segmentRangeBar.setSegmentRanges(segments);
        mBinding.segmentRangeBar.setProgressListener(new HorizontalSegmentRangeBar.OnProgressChangedListener() {
            @Override
            public void onProgress1Changed(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void getProgress1OnActionUp(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress1OnFinally(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onProgress2Changed(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void getProgress2OnActionUp(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress2OnFinally(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onAlert() {

            }
        });
    }

    private void initVp(int c1, int c2, int c3, int s1, int s2) {
        String[] modeSuggestions = getContext().getResources().getStringArray(R.array.ps_is_mode_suggestion_array);
        modeSuggestions[3] = "In this power range, when the price is below #c2, charge with grid power is allowed. When the price is higher than #s2, battery discharge is allowed. In the middle area, the load is prioritized to be supplied by the grid, and solar energy is prioritized to charge the battery.";
        modeSuggestions[4] = "In this power range, when the price is below #c1, charge with grid power is allowed. When the price is higher than #s1, battery discharge is allowed. In the middle area, the load is prioritized to be supplied by the grid, and solar energy is prioritized to charge the battery.";
        for (int i = 0; i < modeSuggestions.length; i++) {
            String cStr1 = getString(R.string.hashtag_c1);
            String cStr2 = getString(R.string.hashtag_c2);
            String cStr3 = getString(R.string.hashtag_c3);
            String sStr1 = getString(R.string.hashtag_s1);
            String sStr2 = getString(R.string.hashtag_s2);
            String percent = "%";
            String c1Str = c1 > 0 ? ("+" + c1 + percent) : (c1 + percent);
            String c2Str = c2 > 0 ? ("+" + c2 + percent) : (c2 + percent);
            String c3Str = c3 > 0 ? ("+" + c3 + percent) : (c3 + percent);
            String s1Str = s1 > 0 ? ("+" + s1 + percent) : (s1 + percent);
            String s2Str = s2 > 0 ? ("+" + s2 + percent) : (s2 + percent);
            String text = Local.s(modeSuggestions[i]).replace(cStr1, c1Str)
                    .replace(cStr2, c2Str)
                    .replace(cStr3, c3Str)
                    .replace(sStr1, s1Str)
                    .replace(sStr2, s2Str);
            mFragments.add(ModeSuggestionFragment.newInstance(i,
                    text, 0,
                    new ModeFragmentCreatedListener()));
        }

        mAdapter = new CommonPagerAdapter(getChildFragmentManager(), mFragments);
        mBinding.vpMode.setAdapter(mAdapter);
        mBinding.vpMode.setOffscreenPageLimit(5);
        mBinding.indicator.setupViewpager(mBinding.vpMode);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (!isAdded()) return;
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                                if (isDealt) return;
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                int emergencyReserve = DeviceHelper.getInt(result, BmtDataKey.EMERGENCY, 0);
                                int smartReserve = DeviceHelper.getInt(result, BmtDataKey.SMART, 0);
                                setSegment(emergencyReserve, smartReserve);
                                int c1 = DeviceHelper.getInt(result, BmtDataKey.C_1, 0);
                                int c2 = DeviceHelper.getInt(result, BmtDataKey.C_2, 0);
                                int c3 = DeviceHelper.getInt(result, BmtDataKey.C_3, 0);
                                int s1 = DeviceHelper.getInt(result, BmtDataKey.S_1, 0);
                                int s2 = DeviceHelper.getInt(result, BmtDataKey.S_2, 0);
                                mBinding.viewPriceTracking.resetData(emergencyReserve, smartReserve, c1, c2, c3, s1, s2);
                                addPriceTrackingValue(c1, c2, c3, s1, s2);
                                initVp(c1, c2, c3, s1, s2);
                                isDealt = true;
                                break;
                        }
                    } else {
                        switch (cmd) {
                            case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;
                        }
                    }
                }
            });
        }
    }

    private void addPriceTrackingValue(int c1, int c2, int c3, int s1, int s2) {
        PointBean lowerChargeBean = mBinding.viewPriceTracking.getLowChargePoint(1);
        createValueView(c3, lowerChargeBean.getX(), lowerChargeBean.getY(), false);
        PointBean emergencyDischarge = mBinding.viewPriceTracking.getLowChargePoint(2);
        createValueView(s2, emergencyDischarge.getX(), emergencyDischarge.getY(), true);
        PointBean emergencyCharge = mBinding.viewPriceTracking.getLowChargePoint(3);
        createValueView(c2, emergencyCharge.getX(), emergencyCharge.getY(), false);
        PointBean smartDischarge = mBinding.viewPriceTracking.getLowChargePoint(4);
        createValueView(s1, smartDischarge.getX(), smartDischarge.getY(), true);
        PointBean smartCharge = mBinding.viewPriceTracking.getLowChargePoint(5);
        createValueView(c1, smartCharge.getX(), smartCharge.getY(), false);
    }

    private void createValueView(int value, float x, float y, boolean isDisCharge) {
        TextView textView = new TextView(getContext());
        String text = "";
        if (value > 0) {
            text = "+" + value + "%";
        } else {
            text = value + "%";
        }
        textView.setText(text);
        textView.setTextColor(getColor(R.color.color_white_01));
        textView.setTextAppearance(getContext(), R.style.TextFamilyCaptionS);
        textView.setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.icon_bmt_price, 0, 0, 0);
        textView.setCompoundDrawablePadding(DensityUtil.dp2px(getContext(), 2));
        mBinding.clContent.addView(textView);
        textView.post(() -> {
            textView.setX(x - textView.getMeasuredWidth() / 2f);
            int textViewHeight = textView.getMeasuredHeight();
            float top = mBinding.viewPriceTracking.getY();
            float bottom = mBinding.viewPriceTracking.getY() + mBinding.viewPriceTracking.getMeasuredHeight();
            float viewY = isDisCharge ? mBinding.viewPriceTracking.getY() + y - textView.getMeasuredHeight() - DensityUtil.dp2px(getContext(), 4) :
                    mBinding.viewPriceTracking.getY() + y ;
            int overflowVal = DensityUtil.dp2px(getContext(), 2);
            if (viewY < top + overflowVal) {
                viewY = top + overflowVal;
            }
            if (viewY + textViewHeight > bottom - overflowVal) {
                viewY = bottom - textViewHeight -  overflowVal;
            }
            textView.setY(viewY);
        });
    }

    public class ModeFragmentCreatedListener implements ModeSuggestionFragment.OnCreatedListener {

        @Override
        public void onCreated(View view, int position) {
            mBinding.vpMode.setViewPosition(view, position);
        }
    }

    private int getColor(int color) {
        return getResources().getColor(color);
    }

}
