package com.dinsafer.module.powerstation.electricity.chart.render;

import android.graphics.Canvas;
import android.graphics.PointF;

import com.dinsafer.util.DDLog;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.renderer.XAxisRenderer;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.Utils;
import com.github.mikephil.charting.utils.ViewPortHandler;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/1 11:07
 * @description :
 */
public class CustomXAxisRenderer extends XAxisRenderer {

    public CustomXAxisRenderer(ViewPortHandler viewPortHandler, XAxis xAxis, Transformer trans) {
        super(viewPortHandler, xAxis, trans);
    }

    @Override
    protected void drawLabels(Canvas c, float pos, PointF anchor) {
//        super.drawLabels(c, pos, anchor);
        final float labelRotationAngleDegrees = mXAxis.getLabelRotationAngle();

        // pre allocate to save performance (dont allocate in loop)
        float[] position = new float[]{
                0f, 0f
        };

        for (int i = mMinX; i <= mMaxX; i += mXAxis.mAxisLabelModulus) {

            position[0] = i;

            mTrans.pointValuesToPixel(position);

            if (mViewPortHandler.isInBoundsX(position[0])) {
                if (i > mXAxis.getValues().size() - 1) break;
                String label = mXAxis.getValues().get(i);

                String formattedLabel = mXAxis.getValueFormatter().getXValue(label, i, mViewPortHandler);

                if (mXAxis.isAvoidFirstLastClippingEnabled()) {

                    // avoid clipping of the last
                    if (i == mXAxis.getValues().size() - 1 && mXAxis.getValues().size() > 1) {
                        float width = Utils.calcTextWidth(mAxisLabelPaint, formattedLabel);
                        float middle = (mViewPortHandler.contentRight() - width + mViewPortHandler.contentRight()) / 2f;
                        if (position[0] > middle)
                            position[0] = middle;
                        // avoid clipping of the first
                    } else if (i == 0) {
                        float width = Utils.calcTextWidth(mAxisLabelPaint, formattedLabel);
                        float middle = (mViewPortHandler.contentLeft() + (mViewPortHandler.contentLeft() + width)) / 2f;
                        if (position[0] < middle) {
                            position[0] = middle;
                        }
                    }
                }

                drawLabel(c, label, i, position[0], pos, anchor, labelRotationAngleDegrees);
            }
        }
    }
}
