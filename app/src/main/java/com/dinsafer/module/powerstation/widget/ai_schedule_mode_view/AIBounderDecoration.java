package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Shader;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.DensityUtils;

public class AIBounderDecoration extends RecyclerView.ItemDecoration {

    private final RectF rectF;
    private final RectF rectFPV;
    private final Paint mPaint;
    private final Path mPath;
    private LinearGradient mGradient;
    private LinearGradient mPVGradient;
    private final int mStrokeWidth;
    private final int mCornerRadius;
    private final int pvHeaderHeight;
    private final Matrix mMatrix;
    private final Matrix mPVMatrix;
    private int enabledSize;

    public AIBounderDecoration(Context context) {
        rectF = new RectF();
        rectFPV = new RectF();
        mMatrix = new Matrix();
        mPVMatrix = new Matrix();
        mPaint = new Paint();
        mPath = new Path();
        mPaint.setStyle(Paint.Style.STROKE);
        mStrokeWidth = DensityUtils.dp2px(context, 1);
        mPaint.setStrokeWidth(mStrokeWidth);
        mPaint.setAntiAlias(true);

        mCornerRadius = DensityUtils.dp2px(context, 8);
        pvHeaderHeight = DensityUtils.dp2px(context, 58);
    }

    @Override
    public void onDrawOver(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int childCount = parent.getChildCount();
        if (childCount <= 0) return;
        if (mGradient == null) {
            View firstView = parent.getChildAt(0);
            View itemFirst = firstView.findViewById(R.id.view_data);
            rectF.left = itemFirst.getLeft();
            rectF.top = parent.getPaddingTop();
            rectF.bottom = rectF.top + itemFirst.getHeight() * enabledSize;
            rectF.right = itemFirst.getRight();

            rectFPV.left = rectF.left;
            rectFPV.top = parent.getPaddingTop();
            rectFPV.right = rectF.right;
            rectFPV.bottom = rectFPV.top + itemFirst.getHeight() * (144 - enabledSize);

            int[] colors = new int[]{
                    parent.getContext().getResources().getColor(R.color.color_ai_stroke_1),
                    parent.getContext().getResources().getColor(R.color.color_ai_stroke_2),
                    parent.getContext().getResources().getColor(R.color.color_ai_stroke_3),
                    parent.getContext().getResources().getColor(R.color.color_ai_stroke_4)
            };
            float[] positions = new float[]{0f, 0.33f, 0.7f, 1f};
            float max = Math.max(rectF.width(), rectF.height());
            mGradient = new LinearGradient(
                    rectF.left, rectF.top + max,
                    rectF.left + max, rectF.top,
                    colors, positions,
                    Shader.TileMode.CLAMP
            );
            float scaleX = rectF.width() / max;
            float scaleY = rectF.height() / max;
            mMatrix.setScale(scaleX, scaleY, rectF.left, rectF.top);
            mGradient.setLocalMatrix(mMatrix);

            float maxPV = Math.max(rectFPV.width(), rectFPV.height());
            mPVGradient = new LinearGradient(
                    rectFPV.left, rectFPV.top + maxPV,
                    rectFPV.left + maxPV, rectFPV.top,
                    colors, positions,
                    Shader.TileMode.CLAMP
            );
            float scaleXPV = rectFPV.width() / maxPV;
            float scaleYPV = rectFPV.height() / maxPV;
            mPVMatrix.setScale(scaleXPV, scaleYPV, rectFPV.left, rectFPV.top);
            mPVMatrix.postTranslate(0, rectF.height() + pvHeaderHeight);
            mPVGradient.setLocalMatrix(mPVMatrix);

        }

        for (int i = 0; i < childCount; i++) {
            View child = parent.getChildAt(i);
            if (child != null) {
                int position = parent.getChildAdapterPosition(child);
                View viewData = child.findViewById(R.id.view_data);
                if (viewData == null) continue;
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) child.getLayoutParams();
                int left = viewData.getLeft();
                int right = viewData.getRight();
                int bottom = child.getBottom() + params.bottomMargin;
                int top = bottom - viewData.getHeight();
                float drawLeft = left + mStrokeWidth / 2f;
                float drawRight = right - mStrokeWidth / 2f;
                float drawTop = top + mStrokeWidth / 2f;
                float drawBottom = bottom - mStrokeWidth / 2f;

                mPaint.setShader(position < enabledSize ? mGradient : mPVGradient);

                if (position == 0 || position == enabledSize + 1) {
                    mPath.reset();
                    mPath.moveTo(drawLeft, bottom);
                    mPath.lineTo(drawLeft, drawTop + mCornerRadius);
                    mPath.quadTo(drawLeft, drawTop, drawLeft + mCornerRadius, drawTop);
                    mPath.lineTo(drawRight - mCornerRadius, drawTop);
                    mPath.quadTo(drawRight, drawTop, drawRight, drawTop + mCornerRadius);
                    mPath.lineTo(drawRight, bottom);
                    c.drawPath(mPath, mPaint);
                } else if (position == enabledSize - 1 || position == parent.getAdapter().getItemCount() - 1) {
                    mPath.reset();
                    mPath.moveTo(drawLeft, top);
                    mPath.lineTo(drawLeft, drawBottom - mCornerRadius);
                    mPath.quadTo(drawLeft, drawBottom, drawLeft + mCornerRadius, drawBottom);
                    mPath.lineTo(drawRight - mCornerRadius, drawBottom);
                    mPath.quadTo(drawRight, drawBottom, drawRight, drawBottom - mCornerRadius);
                    mPath.lineTo(drawRight, top);
                    c.drawPath(mPath, mPaint);
                } else {
                    c.drawLine(drawLeft, top, drawLeft, bottom, mPaint);
                    c.drawLine(drawRight, top, drawRight, bottom, mPaint);
                }
            }
        }
    }

    public void setScroll(int y) {
        if (mMatrix != null) {
            mMatrix.postTranslate(0, -y);
            if (mGradient != null)
                mGradient.setLocalMatrix(mMatrix);
        }

        if (mPVMatrix != null) {
            mPVMatrix.postTranslate(0, -y);
            if (mPVGradient != null) {
                mPVGradient.setLocalMatrix(mPVMatrix);
            }
        }
    }

    public int getEnabledSize() {
        return enabledSize;
    }

    public void setEnabledSize(int enabledSize) {
        this.enabledSize = enabledSize;
    }
}
