package com.dinsafer.module.powerstation.electricity.chart.render;

import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.interfaces.dataprovider.BubbleDataProvider;
import com.github.mikephil.charting.renderer.BubbleChartRenderer;
import com.github.mikephil.charting.utils.ViewPortHandler;

/**
 *
 */
public class CustomBubbleChartRenderer extends BubbleChartRenderer {
    public CustomBubbleChartRenderer(BubbleDataProvider chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {
        super(chart, animator, viewPortHandler);
    }

    public BubbleDataProvider getChart() {
        return mChart;
    }
}
