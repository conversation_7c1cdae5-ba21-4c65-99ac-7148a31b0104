package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;

import java.util.List;

public class AISelectedDecoration extends RecyclerView.ItemDecoration {

    private final Paint mPaint;
    private int enabledSize;
    private BindMultiAdapter<BindModel> mAdapter;


    public AISelectedDecoration(Context context, BindMultiAdapter<BindModel> adapter) {
        mAdapter = adapter;
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(context.getResources().getColor(R.color.color_brand_light_01));
    }

    @Override
    public void onDrawOver(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int childCount = parent.getChildCount();
        for (int i = 0; i < enabledSize; i++) {
            View child = mAdapter.getViewByPosition(parent, i, R.id.ll_parent);
            if (child != null) {
                AIScheduleModeModel aiScheduleModeBean = (AIScheduleModeModel) mAdapter.getItem(i);
                if (child != null && aiScheduleModeBean != null) {
                    if (aiScheduleModeBean.isSelected()) {
                        RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) child.getLayoutParams();
                        int bottom = child.getBottom();
                        int top = bottom - child.getHeight();
                        c.drawRect(0, top, parent.getWidth(), bottom, mPaint);
                    }
                }
            }
        }
    }

    public int getEnabledSize() {
        return enabledSize;
    }

    public void setEnabledSize(int enabledSize) {
        this.enabledSize = enabledSize;
    }
}
