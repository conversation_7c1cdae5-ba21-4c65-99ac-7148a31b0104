package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import androidx.annotation.DrawableRes;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsProductNameBinding;
import com.dinsafer.ui.rv.BindModel;

public class PSProductNameModel extends BindModel<ItemPsProductNameBinding> {

    private @DrawableRes int coverRes;
    private String name;
    private String model;
    private boolean enabled;
    private boolean checked;
    private boolean showLine;

    public PSProductNameModel(Context context, int coverRes, String name, String model, boolean showLine) {
        super(context);
        this.coverRes = coverRes;
        this.name = name;
        this.model = model;
        this.showLine = showLine;
    }

    public PSProductNameModel(Context context, int coverRes, String name, String model, boolean enabled, boolean showLine) {
        super(context);
        this.coverRes = coverRes;
        this.name = name;
        this.model = model;
        this.enabled = enabled;
        this.showLine = showLine;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_product_name;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsProductNameBinding binding) {
        binding.ivCover.setImageResource(coverRes);
        binding.tvName.setText(name);
        binding.ivChecked.setVisibility(checked ? View.VISIBLE : View.INVISIBLE);
        binding.llParent.setEnabled(enabled);
        binding.llContent.setAlpha(enabled ? 1.0f : 0.5f);
        binding.viewLine.setVisibility(showLine ? View.VISIBLE : View.GONE);
    }

    public int getCoverRes() {
        return coverRes;
    }

    public void setCoverRes(int coverRes) {
        this.coverRes = coverRes;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }
}
