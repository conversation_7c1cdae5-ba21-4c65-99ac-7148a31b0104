package com.dinsafer.module.powerstation.adapter;

import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.BatteryDetailsItemBean;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 17:06
 * @description :
 */
public class BatteryDetailsAdapter extends BaseQuickAdapter<BatteryDetailsItemBean, BaseViewHolder> {

    public BatteryDetailsAdapter() {
        super(R.layout.item_battery_details);
    }

    @Override
    protected void convert(BaseViewHolder helper, BatteryDetailsItemBean item) {
        ImageView ivLogo = helper.getView(R.id.iv_logo);
        View view = helper.getView(R.id.view_line);
        view.setVisibility(helper.getAdapterPosition() == getData().size()-1 ? View.GONE : View.VISIBLE);
        int logo = item.getLogo();
        ivLogo.setVisibility(logo == 0 ? View.GONE : View.VISIBLE);
        if (logo != 0) {
            ivLogo.setImageResource(logo);
        }
        LocalTextView tvKey = helper.getView(R.id.tv_key);
        LocalTextView tvValue = helper.getView(R.id.tv_value);
        tvKey.setLocalText(item.getKey());
        tvValue.setLocalText(item.getValue());
    }
}
