package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutLoadSaturationBinding;
import com.dinsafer.module.powerstation.LottieManager;
import com.dinsafer.module.powerstation.widget.BatteryChargeView;
import com.dinsafer.module_bmt.BmtDataKey;

import com.dinsafer.util.DDLog;
import com.dinsafer.util.UnitUtil;

import java.util.Map;

public class LoadSaturationView extends ConstraintLayout {

    private final Context mContext;
    private final LayoutLoadSaturationBinding mBinding;
    private final LottieManager mLottieManager;
    private boolean mIsLoading;
    private boolean isGridValid;
    private boolean isBSensorValid;
    private boolean isThirdPVOn;
    private boolean isBalancing;

    public LoadSaturationView(Context context) {
        this(context, null);
    }

    public LoadSaturationView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LoadSaturationView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        mLottieManager = new LottieManager();
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_load_saturation, this, true);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.LoadSaturationView);
        int currentWidth = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_width, 0);
        int currentHeight = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_height, 0);
        int backgroundColor = typedArray.getColor(R.styleable.LoadSaturationView_background_color, 0);
        int valTextColor = typedArray.getColor(R.styleable.LoadSaturationView_current_val_text_color, 0);
        int valTextSize = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_val_text_size, 0);
        int unitTextColor = typedArray.getColor(R.styleable.LoadSaturationView_current_unit_text_color, 0);
        int unitTextSize = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_unit_text_size, 0);
        int marginLeft = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_margin_left, 0);
        int centerMarginBottom = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_center_margin_bottom, 0);
        boolean ivArrowVisible = typedArray.getBoolean(R.styleable.LoadSaturationView_vehicle_arrow_visible, true);
        mBinding.ivVehicleArrow.setVisibility(ivArrowVisible ? VISIBLE : GONE);
        typedArray.recycle();
        if (currentWidth > 0 && currentHeight > 0) {
            ConstraintLayout.LayoutParams cdvLayoutParam = (LayoutParams) mBinding.cdvInverter.getLayoutParams();
            cdvLayoutParam.width = currentWidth;
            cdvLayoutParam.height = currentHeight;
            if (marginLeft > 0) {
                cdvLayoutParam.leftMargin = marginLeft;
            }
            mBinding.cdvInverter.setLayoutParams(cdvLayoutParam);
        }

        if (centerMarginBottom > 0) {
            ConstraintLayout.LayoutParams ivModeLayoutParams = (LayoutParams) mBinding.ivMode.getLayoutParams();
            ivModeLayoutParams.bottomMargin = centerMarginBottom;
            mBinding.ivMode.setLayoutParams(ivModeLayoutParams);

            ConstraintLayout.LayoutParams clSwitchLayoutParams = (LayoutParams) mBinding.clSwitch.getLayoutParams();
            clSwitchLayoutParams.bottomMargin = centerMarginBottom;
            mBinding.clSwitch.setLayoutParams(clSwitchLayoutParams);
        }
        if (backgroundColor != 0) {
            mBinding.clParent.setBackgroundColor(backgroundColor);
        }
        if (valTextColor != 0) {
            mBinding.tvBatteryVal.setTextColor(valTextColor);
            mBinding.tvSolarVal.setTextColor(valTextColor);
            mBinding.tvGridVal.setTextColor(valTextColor);
            mBinding.tvAdditionVal.setTextColor(valTextColor);
            mBinding.tvOtherVal.setTextColor(valTextColor);
            mBinding.tvVehicleVal.setTextColor(valTextColor);
        }
        if (valTextSize > 0) {
            mBinding.tvBatteryVal.setTextSize(valTextSize);
            mBinding.tvSolarVal.setTextSize(valTextSize);
            mBinding.tvGridVal.setTextSize(valTextSize);
            mBinding.tvAdditionVal.setTextSize(valTextSize);
            mBinding.tvOtherVal.setTextSize(valTextSize);
            mBinding.tvVehicleVal.setTextSize(valTextSize);
        }

        if (unitTextColor != 0) {
            mBinding.tvBatteryUnit.setTextColor(unitTextColor);
            mBinding.tvSolarUnit.setTextColor(unitTextColor);
            mBinding.tvGridUnit.setTextColor(unitTextColor);
            mBinding.tvExtraAdditionalUnit.setTextColor(unitTextColor);
            mBinding.tvOtherUnit.setTextColor(unitTextColor);
            mBinding.tvVehicleUnit.setTextColor(unitTextColor);
        }
        if (unitTextSize > 0) {
            mBinding.tvBatteryUnit.setTextSize(unitTextSize);
            mBinding.tvSolarUnit.setTextSize(unitTextSize);
            mBinding.tvGridUnit.setTextSize(unitTextSize);
            mBinding.tvExtraAdditionalUnit.setTextSize(unitTextSize);
            mBinding.tvOtherUnit.setTextSize(unitTextSize);
            mBinding.tvVehicleUnit.setTextSize(unitTextSize);
        }

        initListener();
//        showSwitchStatus(true, false);
    }

    public void setKeyText(boolean isThirdPVOn) {
        this.isThirdPVOn = isThirdPVOn;
        mBinding.tvSolarKey.setLocalText(mContext.getString(R.string.power_station_solar));
        mBinding.tvGridKey.setLocalText(mContext.getString(R.string.power_station_grid));
        mBinding.ivExtraSupply.setVisibility(!isThirdPVOn ? VISIBLE : INVISIBLE);
        mBinding.lavDualPower.setVisibility(isThirdPVOn ? VISIBLE : INVISIBLE);
        mBinding.tvExtraSupplyKey.setLocalText(mContext.getString(isThirdPVOn ?
                R.string.dual_power : R.string.power_station_additional_load));
        mBinding.tvSecureSupplyKey.setLocalText(mContext.getString(R.string.power_station_keep_on_load));
        mBinding.tvVehicleKey.setLocalText(mContext.getString(R.string.power_station_vehicle));
        mBinding.tvBatteryKey.setLocalText(mContext.getString(R.string.power_station_battery));
    }

    private void initListener() {
        OnClickListener onEVListener = view -> {
            if (mOperateListener != null) {
                mOperateListener.onClickEV(view);
            }
        };
        mBinding.lavVehicle.setOnClickListener(onEVListener);
        mBinding.tvVehicleKey.setOnClickListener(onEVListener);
        mBinding.llVehicle.setOnClickListener(onEVListener);

        mBinding.viewDisable.setOnClickListener(view -> {
            if (mOperateListener != null) {
                mOperateListener.onClickUpdate(view);
                changeViewStateByUpdateState(false);
            }
        });
        mBinding.ivUpdating.setOnClickListener(view -> {
            if (mOperateListener != null) {
                mOperateListener.onClickUpdate(view);
                changeViewStateByUpdateState(false);
            }
        });
    }

    public void setLoadSaturationVal(LoadSaturationBean loadSaturationBean) {
        if (loadSaturationBean == null) {
            setOffline(true);
            return;
        }
        int batteryWat = loadSaturationBean.getBatteryWat();
        int solarWat = loadSaturationBean.getSolarWat();
        int gridWat = loadSaturationBean.getGridWat();
        int additionWat = loadSaturationBean.getAdditionWat();
        int otherWat = loadSaturationBean.getOtherWat();
        int vehicleWat = loadSaturationBean.getVehicleWat();
        int ip2Wat = loadSaturationBean.getIp2Wat();
        int op2Wat = loadSaturationBean.getOp2Wat();

        boolean isDrawCircle = batteryWat != 0 || solarWat != 0 || gridWat != 0 ||
                additionWat != 0 || otherWat != 0 || vehicleWat != 0;
        mBinding.cdvInverter.setDrawCircle(isDrawCircle);

        mBinding.tvBatteryVal.setText(UnitUtil._100WToKW(Math.abs(batteryWat)));
        mBinding.tvSolarVal.setText(UnitUtil._100WToKW(Math.abs(solarWat)));
        mBinding.tvGridVal.setText(UnitUtil._100WToKW(Math.abs(gridWat)));
        mBinding.tvAdditionVal.setText(UnitUtil._100WToKW(Math.abs(additionWat)));
        mBinding.tvOtherVal.setText(UnitUtil._100WToKW(Math.abs(otherWat)));
        mBinding.tvVehicleVal.setText(UnitUtil._100WToKW(Math.abs(vehicleWat)));
        dealBatteryCurrent(batteryWat);
        dealSolarCurrent(solarWat);
        dealGridCurrent(gridWat, additionWat);
        dealAdditionalCurrent(additionWat, gridWat);
        dealOtherCurrent(otherWat);
        dealVehicleCurrent(vehicleWat);
        /**
         *                              EV-out(OP4)+家庭保障供应(OP1)
         *  负载饱和度        =   ------------------------------------------- *100%
         * Load Saturation      逆变设计负载上限（单相电3.6kW   /  三相电10.8kW）
         */
        float limited = loadSaturationBean.isThreePhase() ? 10800f : 3600;
        float load = ((vehicleWat < 0 ? Math.abs(vehicleWat) * 100 : 0) +
                (otherWat < 0 ? Math.abs(otherWat) * 100 : 0)) / limited * 100;
        int startColor = getColor(R.color.ps_current_path_start_color_1);
        int endColor = getColor(R.color.ps_current_path_end_color_1);
        if (load < 30) {
            startColor = getColor(R.color.ps_current_path_start_color_1);
            endColor = getColor(R.color.ps_current_path_end_color_1);
        } else if (load >= 30 && load < 65) {
            startColor = getColor(R.color.ps_current_path_start_color_2);
            endColor = getColor(R.color.ps_current_path_end_color_2);
        } else if (load >= 65 && load < 90) {
            startColor = getColor(R.color.ps_current_path_start_color_3);
            endColor = getColor(R.color.ps_current_path_end_color_3);
        } else if (load >= 90) {
            startColor = getColor(R.color.ps_current_path_start_color_4);
            endColor = getColor(R.color.ps_current_path_end_color_4);
        }

        mBinding.clParent.setAlpha(1f);
        mBinding.cdvInverter.setSameColor(startColor, endColor);
        int[] colors = {endColor, startColor};
        float[] positions = {0f, 1f};
        mBinding.cdvInverter.setCircleColors(colors);
        mBinding.cdvInverter.setCirclePositions(positions);
        mBinding.cdvInverter.startAnim();

        if (solarWat > 0) {
            int op1 = Math.abs(otherWat);
            int op4 = vehicleWat > 0 ? 0 : Math.abs(vehicleWat);
            int mIP5 = solarWat;
            int mpptStatus = LottieManager.SOLAR_NULL;
            int op2 = Math.abs(additionWat);
            if (mIP5 >= (op1 + op4 + op2) * 0.2 && mIP5 < (op1 + op4 + op2)) {
                mpptStatus = LottieManager.SOLAR_LOW;
            }
            if (mIP5 >= (op1 + op4 + op2)) {
                mpptStatus = LottieManager.SOLAR_HIGH;
            }
            mLottieManager.controlSolarAnim(mBinding.lavSolar, mpptStatus);
        } else {
            mLottieManager.controlSolarAnim(mBinding.lavSolar, LottieManager.SOLAR_NULL);
        }

        mLottieManager.controlKeepOnLoad(mBinding.ivSecureSupply, otherWat < 0 ? LottieManager.KEEP_ON_LOAD_ON : LottieManager.KEEP_ON_LOAD_OFF);
        mLottieManager.controlAdditionLoad(mBinding.ivExtraSupply, additionWat < 0 ? LottieManager.ADDITION_LOAD_ON : LottieManager.ADDITION_LOAD_OFF);
        setEVStatus(loadSaturationBean.getEvStatus());

        int batteryStatus = BatteryChargeView.NORMAL;
        if (batteryWat > 0) {
            batteryStatus = BatteryChargeView.DISCHARGING;
        }
        if (batteryWat < 0) {
            batteryStatus = BatteryChargeView.CHARGING;
        }
        mBinding.viewBattery.setChargeStatus(batteryStatus, false);
        mBinding.viewBattery.setOnline(true);
        int smartReserve = loadSaturationBean.getSmartReserve();
        int emergencyReserve = loadSaturationBean.getEmergencyReserve();
        float er = emergencyReserve / 100f;
        float sr = smartReserve / 100f;
        mBinding.viewBattery.setEmergencyReserve(er);
        mBinding.viewBattery.setSmartReserve(sr);
        mBinding.viewBattery.setProgress(loadSaturationBean.getBatteryPercentage() / 100f, true);

        setMode(loadSaturationBean.getMode());
        changeViewStateByUpdateState(loadSaturationBean.isShowUpdate());
    }

    /**
     * 这个方法提供 AddWidget 时设置默认值调用
     * @param batteryVal
     * @param solarVal
     * @param gridVal
     * @param additional
     * @param otherVal
     * @param vehicleVal
     */
    public void setDefaultVal(String batteryVal, String solarVal, String gridVal, String additional,
                              String otherVal, String vehicleVal) {
        mBinding.tvBatteryVal.setLocalText(batteryVal);
        mBinding.tvSolarVal.setLocalText(solarVal);
        mBinding.tvGridVal.setLocalText(gridVal);
        mBinding.tvAdditionVal.setLocalText(additional);
        mBinding.tvOtherVal.setLocalText(otherVal);
        mBinding.tvVehicleVal.setLocalText(vehicleVal);
        mBinding.ivExtraSupply.setVisibility(VISIBLE);
        mBinding.tvExtraSupplyKey.setVisibility(VISIBLE);
        mBinding.tvExtraSupplyKey.setLocalText(mContext.getResources().getString(R.string.power_station_additional_load));
        mBinding.lavSolar.setFrame(90);
        mBinding.ivExtraSupply.setFrame(100);
        mBinding.ivSecureSupply.setFrame(100);
        mBinding.lavVehicle.setFrame(143);
    }

    public void setDefaultBattery(int percent, boolean online, int chargeStatus,
                                  float smartReserve) {
        mBinding.viewBattery.setOnline(online);
        mBinding.viewBattery.setChargeStatus(chargeStatus, false);
        mBinding.viewBattery.setSmartReserve(smartReserve);
        mBinding.viewBattery.setProgress(percent / 100f, true);
    }

    public void setDefaultSolar(int status) {
        mLottieManager.controlSolarAnim(mBinding.lavSolar, status);
    }

    public void setDefaultAdditional(int status) {
        mLottieManager.controlAdditionLoad(mBinding.ivExtraSupply, status);
    }

    public void setDefaultOther(int status) {
        mLottieManager.controlKeepOnLoad(mBinding.ivSecureSupply, status);
    }

    public void setDefaultVehicle(int status) {
        mLottieManager.controlVehicleAnim(mBinding.lavVehicle, status);
    }

    public void setGlobalCurrent(boolean isThreePhase, Map<String, Object> globalInfoMap) {
        int batteryWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.BATTERY_WAT, 0);
        int solarWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.SOLAR_WAT, 0);
        int gridWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.GRID_WAT, 0);
        int additionWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.ADDITIONAL_LOAD_WAT, 0);
        int otherWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.OTHER_LOAD_WAT, 0);
        int vehicleWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.VECHI_WAT, 0);
        int ip2Wat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.IP2_WAT, 0);
        int op2Wat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.OP2_WAT, 0);
        isGridValid = DeviceHelper.getBoolean(globalInfoMap, BmtDataKey.GRID_VALID, false);
        isBSensorValid = DeviceHelper.getBoolean(globalInfoMap, BmtDataKey.BSENSOR_VALID, false);
        int solarEfficiency = DeviceHelper.getInt(globalInfoMap, BmtDataKey.SOLAR_EFFICIENCY, 0);
        boolean thirdPartyPVOn = DeviceHelper.getBoolean(globalInfoMap, BmtDataKey.THIRD_PARTY_PV_ON, false);
        int dualPowerWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.DUAL_POWER_WAT, 0);
        isThirdPVOn = thirdPartyPVOn;

        mBinding.tvBatteryVal.setText(UnitUtil._100WToKW(Math.abs(batteryWat)));
        mBinding.tvSolarVal.setText(UnitUtil._100WToKW(Math.abs(solarWat)));
        mBinding.tvGridVal.setText(UnitUtil._100WToKW(Math.abs(gridWat)));
        mBinding.tvExtraSupplyKey.setLocalText(mContext.getResources().getString(thirdPartyPVOn ? R.string.dual_power : R.string.power_station_additional_load));
        mBinding.tvAdditionVal.setText(thirdPartyPVOn ? UnitUtil._100WToKW(Math.abs(dualPowerWat))
                : UnitUtil._100WToKW(Math.abs(additionWat)));
        mBinding.tvOtherVal.setText(UnitUtil._100WToKW(Math.abs(otherWat)));
        mBinding.tvVehicleVal.setText(UnitUtil._100WToKW(Math.abs(vehicleWat)));

        mBinding.llGrid.setVisibility(isGridValid ? VISIBLE : INVISIBLE);
        mBinding.tvGridKey.setVisibility(isGridValid ? VISIBLE : INVISIBLE);
        mBinding.ivGrid.setAlpha(isGridValid ? 1f : 0.5f);

        mBinding.ivExtraSupply.setAlpha(isGridValid && isBSensorValid ? 1f : 0.5f);
        mBinding.ivExtraSupply.setVisibility(!thirdPartyPVOn ? VISIBLE : INVISIBLE);
        mBinding.lavDualPower.setVisibility(thirdPartyPVOn ? VISIBLE : INVISIBLE);

        mBinding.ivSecureSupply.setAlpha(isGridValid ? 0.3f : 1f);
        mBinding.tvSecureSupplyKey.setVisibility(isGridValid ? INVISIBLE : VISIBLE);
        mBinding.llSecureSupply.setVisibility(isGridValid ? INVISIBLE : VISIBLE);

        boolean enabled = (isGridValid && isBSensorValid) || thirdPartyPVOn;
        mBinding.tvExtraSupplyKey.setVisibility(enabled ? VISIBLE : INVISIBLE);
        mBinding.llExtraSupply.setVisibility(enabled ? VISIBLE : INVISIBLE);

        dealBatteryCurrent(batteryWat);
        dealSolarCurrent(solarWat);
        if (isGridValid) {
            dealGridCurrent(gridWat, additionWat, dualPowerWat, thirdPartyPVOn);
        } else {
            closeGrid();
        }
        if (thirdPartyPVOn) {
            closeAdditional();
            if (isBSensorValid) {
                dealDualPowerCurrent(dualPowerWat, gridWat);
            } else {
                closeDualPower();
            }
        } else {
            closeDualPower();
            if (isGridValid && isBSensorValid) {
                dealAdditionalCurrent(additionWat, gridWat);
            } else {
                closeAdditional();
            }
        }

        dealOtherCurrent(otherWat);
        dealVehicleCurrent(vehicleWat);

        /**
         *                              EV-out(OP4)+家庭保障供应(OP1)
         *  负载饱和度        =   ------------------------------------------- *100%
         * Load Saturation      逆变设计负载上限（单相电3.6kW   /  三相电10.8kW）
         */
        float limited = isThreePhase ? 10800f : 3600;
        float load = ((vehicleWat < 0 ? Math.abs(vehicleWat) * 100 : 0) +
                (otherWat < 0 ? Math.abs(otherWat) * 100 : 0)) / limited * 100;
        int startColor = getColor(R.color.ps_current_path_start_color_1);
        int endColor = getColor(R.color.ps_current_path_end_color_1);
        if (load < 30) {
            startColor = getColor(R.color.ps_current_path_start_color_1);
            endColor = getColor(R.color.ps_current_path_end_color_1);
        } else if (load >= 30 && load < 65) {
            startColor = getColor(R.color.ps_current_path_start_color_2);
            endColor = getColor(R.color.ps_current_path_end_color_2);
        } else if (load >= 65 && load < 90) {
            startColor = getColor(R.color.ps_current_path_start_color_3);
            endColor = getColor(R.color.ps_current_path_end_color_3);
        } else if (load >= 90) {
            startColor = getColor(R.color.ps_current_path_start_color_4);
            endColor = getColor(R.color.ps_current_path_end_color_4);
        }

        if (isBalancing) {
            startColor = getColor(R.color.ps_current_path_start_color_balancing_power);
            endColor = getColor(R.color.ps_current_path_end_color_balancing_power);
        }

        mBinding.clParent.setAlpha(1f);
        mBinding.cdvInverter.setSameColor(startColor, endColor);
        int[] colors = {endColor, startColor};
        float[] positions = {0f, 1f};
        mBinding.cdvInverter.setCircleColors(colors);
        mBinding.cdvInverter.setCirclePositions(positions);
        mBinding.cdvInverter.startAnim();
        if (solarEfficiency > 0) {
            mLottieManager.controlSolarAnim(mBinding.lavSolar, solarEfficiency - 1);
        } else if (solarWat > 0) {
            int op1 = Math.abs(otherWat);
            int op4 = vehicleWat > 0 ? 0 : Math.abs(vehicleWat);
            int mIP5 = solarWat;
            int mpptStatus = LottieManager.SOLAR_NULL;
            int op2 = Math.abs(additionWat);
            if (mIP5 >= (op1 + op4 + op2) * 0.2 && mIP5 < (op1 + op4 + op2)) {
                mpptStatus = LottieManager.SOLAR_LOW;
            }
            if (mIP5 >= (op1 + op4 + op2)) {
                mpptStatus = LottieManager.SOLAR_HIGH;
            }
            mLottieManager.controlSolarAnim(mBinding.lavSolar, mpptStatus);
        } else {
            mLottieManager.controlSolarAnim(mBinding.lavSolar, LottieManager.SOLAR_NULL);
        }

        mLottieManager.controlKeepOnLoad(mBinding.ivSecureSupply, otherWat < 0 ? LottieManager.KEEP_ON_LOAD_ON : LottieManager.KEEP_ON_LOAD_OFF);

        if (thirdPartyPVOn) {
            mLottieManager.controlDualPower(mBinding.lavDualPower, dualPowerWat != 0 ? LottieManager.DUAL_POWER_ON : LottieManager.DUAL_POWER_OFF);
        } else {
            mLottieManager.controlAdditionLoad(mBinding.ivExtraSupply, additionWat < 0 ? LottieManager.ADDITION_LOAD_ON : LottieManager.ADDITION_LOAD_OFF);
        }

        int batteryStatus = BatteryChargeView.NORMAL;
        if (batteryWat > 0) {
            batteryStatus = BatteryChargeView.DISCHARGING;
        }
        if (batteryWat < 0) {
            batteryStatus = BatteryChargeView.CHARGING;
        }
        mBinding.viewBattery.setChargeStatus(batteryStatus, false);
        mBinding.viewBattery.setOnline(true);
    }

    private void closeGrid() {
        CurrentPathBean gridInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
        CurrentPathBean gridDualPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_DUALPOWER_KEY);
        CurrentPathBean gridAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
        CurrentPathBean inverterGrid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
        CurrentPathBean dualPowerGrid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.DUALPOWER_GRID_KEY);
        if (gridInverter != null) {
            gridInverter.setDrawPath(false);
        }
        if (gridDualPower != null) {
            gridDualPower.setDrawPath(false);
        }
        if (gridAdditional != null) {
            gridAdditional.setDrawPath(false);
        }
        if (inverterGrid != null) {
            inverterGrid.setDrawPath(false);
        }
        if (dualPowerGrid != null) {
            dualPowerGrid.setDrawPath(false);
        }
    }

    private void closeDualPower() {
        CurrentPathBean gridDualPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_DUALPOWER_KEY);
        CurrentPathBean inverterDualPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_DUALPOWER_KEY);
        CurrentPathBean dualPowerGrid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.DUALPOWER_GRID_KEY);
        CurrentPathBean dualPowerInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.DUALPOWER_INVERTER_KEY);
        if (gridDualPower != null) {
            gridDualPower.setDrawPath(false);
        }
        if (inverterDualPower != null) {
            inverterDualPower.setDrawPath(false);
        }
        if (dualPowerGrid != null) {
            dualPowerGrid.setDrawPath(false);
        }
        if (dualPowerInverter != null) {
            dualPowerInverter.setDrawPath(false);
        }
    }

    private void closeAdditional() {
        CurrentPathBean gridAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
        CurrentPathBean inverterAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_ADDITIONAL_KEY);
        if (gridAdditional != null) {
            gridAdditional.setDrawPath(false);
        }
        if (inverterAdditional != null) {
            inverterAdditional.setDrawPath(false);
        }
    }

    public void setBatteryInfo(BatteryInfoBean batteryInfoBean) {
        int smartReserve = batteryInfoBean.getSmartReserve();
        int emergencyReserve = batteryInfoBean.getEmergencyReserve();
        float er = emergencyReserve / 100f;
        float sr = smartReserve / 100f;
        mBinding.viewBattery.setEmergencyReserve(er);
        mBinding.viewBattery.setSmartReserve(sr);
        mBinding.viewBattery.setProgress(batteryInfoBean.getPercentage() / 100f, true);
    }

    /**
     * 电池电流动画控制
     *
     * @param batteryWat
     */
    private void dealBatteryCurrent(int batteryWat) {
        if (batteryWat > 0) {
            CurrentPathBean batteryInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BATTERY_INVERTER_KEY);
            if (batteryInverter == null) {
                batteryInverter = new CurrentPathBean(CurrentPathBean.BATTERY_INVERTER_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.BATTERY_INVERTER_KEY, batteryInverter);
            }
            batteryInverter.setDrawPath(true);
            CurrentPathBean inverter2Battery = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_BATTERY_KEY);
            if (inverter2Battery != null) {
                inverter2Battery.setDrawPath(false);
            }
        } else if (batteryWat < 0) {
            CurrentPathBean inverterBattery = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_BATTERY_KEY);
            if (inverterBattery == null) {
                inverterBattery = new CurrentPathBean(CurrentPathBean.INVERTER_BATTERY_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_BATTERY_KEY, inverterBattery);
            }
            inverterBattery.setDrawPath(true);
            CurrentPathBean battery2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BATTERY_INVERTER_KEY);
            if (battery2Inverter != null) {
                battery2Inverter.setDrawPath(false);
            }
        } else {
            CurrentPathBean battery2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BATTERY_INVERTER_KEY);
            CurrentPathBean inverter2Battery = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_BATTERY_KEY);
            if (battery2Inverter != null) {
                battery2Inverter.setDrawPath(false);
            }
            if (inverter2Battery != null) {
                inverter2Battery.setDrawPath(false);
            }
        }
    }

    /**
     * 太阳能电流动画控制
     *
     * @param solarWat
     */
    private void dealSolarCurrent(int solarWat) {
        CurrentPathBean solarPathBean = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.SOLAR_INVERTER_KEY);
        if (solarWat > 0) {
            if (solarPathBean == null) {
                solarPathBean = new CurrentPathBean(CurrentPathBean.SOLAR_INVERTER_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.SOLAR_INVERTER_KEY, solarPathBean);
            }
            solarPathBean.setDrawPath(true);
        } else {
            if (solarPathBean != null) {
                solarPathBean.setDrawPath(false);
            }
        }
    }

    /**
     * 市电电流动画控制
     *
     * @param gridWat     市电值
     * @param additionWat 额外负载值
     */
    private void dealGridCurrent(int gridWat, int additionWat, int dualPowerWat, boolean thirdPVOn) {
        if (gridWat > 0) {
            int additionOrDualPowerWatAbs = Math.abs(thirdPVOn ? dualPowerWat : additionWat);
            CurrentPathBean gridInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            boolean showGridInverter = false;
            if (thirdPVOn) {
                if (dualPowerWat >= 0) {
                    showGridInverter = true;
                } else {
                    showGridInverter = gridWat > additionOrDualPowerWatAbs;
                }
            } else {
                showGridInverter = gridWat > additionOrDualPowerWatAbs;
            }
            if (showGridInverter) {
                if (gridInverter == null) {
                    gridInverter = new CurrentPathBean(CurrentPathBean.GRID_INVERTER_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.GRID_INVERTER_KEY, gridInverter);
                }
                gridInverter.setDrawPath(true);
            } else {
                if (gridInverter != null) {
                    gridInverter.setDrawPath(false);
                }
            }
            CurrentPathBean gridAdditionalOrDualPower = mBinding.cdvInverter.getPathBeanByKey(thirdPVOn ?
                    CurrentPathBean.GRID_DUALPOWER_KEY : CurrentPathBean.GRID_ADDITIONAL_KEY);
            boolean lessZero = thirdPVOn ? dualPowerWat < 0 : additionWat < 0;
            if (lessZero) {
                if (gridAdditionalOrDualPower == null) {
                    gridAdditionalOrDualPower = new CurrentPathBean(thirdPVOn ?
                            CurrentPathBean.GRID_DUALPOWER_KEY : CurrentPathBean.GRID_ADDITIONAL_KEY);
                    mBinding.cdvInverter.setPathMap(thirdPVOn ?
                                    CurrentPathBean.GRID_DUALPOWER_KEY : CurrentPathBean.GRID_ADDITIONAL_KEY,
                            gridAdditionalOrDualPower);
                }
                gridAdditionalOrDualPower.setDrawPath(true);
            } else {
                if (gridAdditionalOrDualPower != null) {
                    gridAdditionalOrDualPower.setDrawPath(false);
                }
            }
            CurrentPathBean inverterGrid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            if (inverterGrid != null) {
                inverterGrid.setDrawPath(false);
            }
        } else if (gridWat < 0) {
            CurrentPathBean inverter2Grid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            if (inverter2Grid == null) {
                inverter2Grid = new CurrentPathBean(CurrentPathBean.INVERTER_GRID_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_GRID_KEY, inverter2Grid);
            }
            boolean needInvertGrid = !thirdPVOn || dualPowerWat <= 0 || dualPowerWat < Math.abs(gridWat);
            inverter2Grid.setDrawPath(needInvertGrid);
            CurrentPathBean gridInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            if (gridInverter != null) {
                gridInverter.setDrawPath(false);
            }
            CurrentPathBean gridAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            CurrentPathBean gridDualPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_DUALPOWER_KEY);
            if (gridAdditional != null) {
                gridAdditional.setDrawPath(false);
            }
            if (gridDualPower != null) {
                gridDualPower.setDrawPath(false);
            }
        } else {
            CurrentPathBean grid2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            CurrentPathBean grid2Additional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            CurrentPathBean inverter2Grid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            CurrentPathBean grid2DualPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_DUALPOWER_KEY);
            if (grid2Inverter != null) {
                grid2Inverter.setDrawPath(false);
            }
            if (grid2Additional != null) {
                grid2Additional.setDrawPath(false);
            }
            if (inverter2Grid != null) {
                inverter2Grid.setDrawPath(false);
            }
            if (grid2DualPower != null) {
                grid2DualPower.setDrawPath(false);
            }
        }
    }

    /**
     * 市电电流动画控制
     *
     * @param gridWat     市电值
     * @param additionWat 额外负载值
     */
    private void dealGridCurrent(int gridWat, int additionWat) {
        if (gridWat > 0) {
            int additionWatAbs = Math.abs(additionWat);
            CurrentPathBean gridInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            if (gridWat > additionWatAbs) {
                if (gridInverter == null) {
                    gridInverter = new CurrentPathBean(CurrentPathBean.GRID_INVERTER_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.GRID_INVERTER_KEY, gridInverter);
                }
                gridInverter.setDrawPath(true);
            } else {
                if (gridInverter != null) {
                    gridInverter.setDrawPath(false);
                }
            }
            CurrentPathBean gridAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            if (additionWat < 0) {
                if (gridAdditional == null) {
                    gridAdditional = new CurrentPathBean(CurrentPathBean.GRID_ADDITIONAL_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.GRID_ADDITIONAL_KEY, gridAdditional);
                }
                gridAdditional.setDrawPath(true);
            } else {
                if (gridAdditional != null) {
                    gridAdditional.setDrawPath(false);
                }
            }
            CurrentPathBean inverterGrid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            if (inverterGrid != null) {
                inverterGrid.setDrawPath(false);
            }
        } else if (gridWat < 0) {
            CurrentPathBean inverter2Grid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            if (inverter2Grid == null) {
                inverter2Grid = new CurrentPathBean(CurrentPathBean.INVERTER_GRID_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_GRID_KEY, inverter2Grid);
            }
            inverter2Grid.setDrawPath(true);
            CurrentPathBean gridInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            if (gridInverter != null) {
                gridInverter.setDrawPath(false);
            }
            CurrentPathBean gridAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            if (gridAdditional != null) {
                gridAdditional.setDrawPath(false);
            }
        } else {
            CurrentPathBean grid2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            CurrentPathBean grid2Additional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            CurrentPathBean inverter2Grid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            if (grid2Inverter != null) {
                grid2Inverter.setDrawPath(false);
            }
            if (grid2Additional != null) {
                grid2Additional.setDrawPath(false);
            }
            if (inverter2Grid != null) {
                inverter2Grid.setDrawPath(false);
            }
        }
    }

    /**
     * 市电电流动画控制
     *
     * @param gridWat     市电值
     * @param additionWat 额外负载值
     * @param ip2Wat      ip2值
     * @param op2Wat      op2值
     *                    不需要判断ip2和op的值了, 所以不用下面的方法
     *                    {@link #dealGridCurrent(int gridWat, int additionWat)}
     */
    @Deprecated
    private void dealGridCurrent(int gridWat, int additionWat, int ip2Wat, int op2Wat) {
        if (gridWat > 0) {
            if (ip2Wat > op2Wat || (ip2Wat == op2Wat && op2Wat > 0)) {
                CurrentPathBean gridInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
                if (gridInverter == null) {
                    gridInverter = new CurrentPathBean(CurrentPathBean.GRID_INVERTER_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.GRID_INVERTER_KEY, gridInverter);
                }
                gridInverter.setDrawPath(true);
            }
            CurrentPathBean gridAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            if (additionWat < 0) {
                if (gridAdditional == null) {
                    gridAdditional = new CurrentPathBean(CurrentPathBean.GRID_ADDITIONAL_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.GRID_ADDITIONAL_KEY, gridAdditional);
                }
                gridAdditional.setDrawPath(true);
            } else {
                if (gridAdditional != null) {
                    gridAdditional.setDrawPath(false);
                }
            }
            CurrentPathBean inverterGrid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            if (inverterGrid != null) {
                inverterGrid.setDrawPath(false);
            }
        } else if (gridWat < 0) {
            if (ip2Wat < op2Wat) {
                CurrentPathBean inverter2Grid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
                if (inverter2Grid == null) {
                    inverter2Grid = new CurrentPathBean(CurrentPathBean.INVERTER_GRID_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_GRID_KEY, inverter2Grid);
                }
                inverter2Grid.setDrawPath(true);
            }
            CurrentPathBean gridInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            if (gridInverter != null) {
                gridInverter.setDrawPath(false);
            }
            CurrentPathBean gridAdditional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            if (gridAdditional != null) {
                gridAdditional.setDrawPath(false);
            }
        } else {
            CurrentPathBean grid2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_INVERTER_KEY);
            CurrentPathBean grid2Additional = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.GRID_ADDITIONAL_KEY);
            CurrentPathBean inverter2Grid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_GRID_KEY);
            if (grid2Inverter != null) {
                grid2Inverter.setDrawPath(false);
            }
            if (grid2Additional != null) {
                grid2Additional.setDrawPath(false);
            }
            if (inverter2Grid != null) {
                inverter2Grid.setDrawPath(false);
            }
        }
    }

    /**
     * 复合电力电流动画控制
     *
     * @param dualPowerWat
     * @param gridWat
     */
    private void dealDualPowerCurrent(int dualPowerWat, int gridWat) {
        CurrentPathBean inverterDualPowerPathBean = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_DUALPOWER_KEY);
        if (dualPowerWat > 0) {
            CurrentPathBean dualPowerInverterPathBean = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.DUALPOWER_INVERTER_KEY);
            CurrentPathBean dualPowerGridPathBean = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.DUALPOWER_GRID_KEY);
            if (dualPowerInverterPathBean == null) {
                dualPowerInverterPathBean = new CurrentPathBean(CurrentPathBean.DUALPOWER_INVERTER_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.DUALPOWER_INVERTER_KEY, dualPowerInverterPathBean);
            }
            if (gridWat < 0) {
                if (dualPowerGridPathBean == null) {
                    dualPowerGridPathBean = new CurrentPathBean(CurrentPathBean.DUALPOWER_GRID_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.DUALPOWER_GRID_KEY, dualPowerGridPathBean);
                }
                dualPowerGridPathBean.setDrawPath(true);
                dualPowerInverterPathBean.setDrawPath(dualPowerWat > Math.abs(gridWat));
            } else {
                dualPowerInverterPathBean.setDrawPath(true);
                if (dualPowerGridPathBean != null) {
                    dualPowerGridPathBean.setDrawPath(false);
                }
            }
        } else if (dualPowerWat < 0) {
            boolean isGridNormal = gridWat <= 0;
            if (inverterDualPowerPathBean == null) {
                inverterDualPowerPathBean = new CurrentPathBean(CurrentPathBean.INVERTER_DUALPOWER_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_DUALPOWER_KEY, inverterDualPowerPathBean);
            }
            if (isGridNormal) {
                inverterDualPowerPathBean.setDrawPath(true);
            } else {
                boolean isGridGreaterAdditional = gridWat < Math.abs(dualPowerWat);
                inverterDualPowerPathBean.setDrawPath(isGridGreaterAdditional);
            }
            closeDualPowerStart();
        } else {
            if (inverterDualPowerPathBean != null) {
                inverterDualPowerPathBean.setDrawPath(false);
            }
            closeDualPowerStart();
        }
    }

    private void closeDualPowerStart() {
        CurrentPathBean dualPower2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.DUALPOWER_INVERTER_KEY);
        CurrentPathBean dualPower2Grid = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.DUALPOWER_GRID_KEY);
        if (dualPower2Inverter != null) {
            dualPower2Inverter.setDrawPath(false);
        }
        if (dualPower2Grid != null) {
            dualPower2Grid.setDrawPath(false);
        }
    }


    /**
     * 额外负载电流动画控制
     *
     * @param additionWat
     */
    private void dealAdditionalCurrent(int additionWat, int gridWat) {
        CurrentPathBean additionalPathBean = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_ADDITIONAL_KEY);
        boolean isGridNormal = gridWat <= 0;
        if (additionWat < 0) {
            if (additionalPathBean == null) {
                additionalPathBean = new CurrentPathBean(CurrentPathBean.INVERTER_ADDITIONAL_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_ADDITIONAL_KEY, additionalPathBean);
            }
            if (isGridNormal) {
                additionalPathBean.setDrawPath(true);
            } else {
                boolean isGridGreaterAdditional = gridWat < Math.abs(additionWat);
                additionalPathBean.setDrawPath(isGridGreaterAdditional);
            }
        } else {
            if (additionalPathBean != null) {
                additionalPathBean.setDrawPath(false);
            }
        }
    }

    /**
     * 额外负载电流动画控制
     *
     * @param additionWat 不需要判断ip2和op的值了, 所以不用下面的方法
     *                    {@link #dealAdditionalCurrent(int, int)}
     */
    @Deprecated
    private void dealAdditionalCurrent(int additionWat, int ip2Wat, int op2Wat) {
        CurrentPathBean additionalPathBean = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_ADDITIONAL_KEY);
        if (additionWat < 0) {
            if (ip2Wat < op2Wat) {
                if (additionalPathBean == null) {
                    additionalPathBean = new CurrentPathBean(CurrentPathBean.INVERTER_ADDITIONAL_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_ADDITIONAL_KEY, additionalPathBean);
                }
                additionalPathBean.setDrawPath(true);
            }
        } else {
            if (additionalPathBean != null) {
                additionalPathBean.setDrawPath(false);
            }
        }
    }

    /**
     * 其它负载电流动画控制
     *
     * @param otherWat
     */
    private void dealOtherCurrent(int otherWat) {
        CurrentPathBean otherPathBean = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_KEEP_ON_KEY);
        if (otherWat < 0) {
            if (otherPathBean == null) {
                otherPathBean = new CurrentPathBean(CurrentPathBean.INVERTER_KEEP_ON_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_KEEP_ON_KEY, otherPathBean);
            }
            otherPathBean.setDrawPath(true);
        } else {
            if (otherPathBean != null) {
                otherPathBean.setDrawPath(false);
            }
        }
    }

    /**
     * 车充电流动画控制
     *
     * @param vehicleWat
     */
    private void dealVehicleCurrent(int vehicleWat) {
        if (vehicleWat > 0) {
            CurrentPathBean vehicleInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.VEHICLE_INVERTER_KEY);
            if (vehicleInverter == null) {
                vehicleInverter = new CurrentPathBean(CurrentPathBean.VEHICLE_INVERTER_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.VEHICLE_INVERTER_KEY, vehicleInverter);
            }
            vehicleInverter.setDrawPath(true);
            CurrentPathBean inverter2Vehicle = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_VEHICLE_KEY);
            if (inverter2Vehicle != null) {
                inverter2Vehicle.setDrawPath(false);
            }
        } else if (vehicleWat < 0) {
            CurrentPathBean inverterVehicle = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_VEHICLE_KEY);
            if (inverterVehicle == null) {
                inverterVehicle = new CurrentPathBean(CurrentPathBean.INVERTER_VEHICLE_KEY);
                mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_VEHICLE_KEY, inverterVehicle);
            }
            inverterVehicle.setDrawPath(true);
            CurrentPathBean vehicle2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.VEHICLE_INVERTER_KEY);
            if (vehicle2Inverter != null) {
                vehicle2Inverter.setDrawPath(false);
            }
        } else {
            CurrentPathBean vehicle2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.VEHICLE_INVERTER_KEY);
            CurrentPathBean inverter2Vehicle = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_VEHICLE_KEY);
            if (vehicle2Inverter != null) {
                vehicle2Inverter.setDrawPath(false);
            }
            if (inverter2Vehicle != null) {
                inverter2Vehicle.setDrawPath(false);
            }
        }
    }

    public void setEVStatus(int evStatus) {
        if (evStatus <= 0 || evStatus > 4) return;
        mLottieManager.controlVehicleAnim(mBinding.lavVehicle, evStatus);
    }

    public void setMode(int mode) {
        if (mode < 0 || mode > 4) return;
        int[] modeIcons = {R.drawable.icon_power_lightning, R.drawable.icon_power_lightning_mode1,
                R.drawable.icon_power_lightning_mode2, R.drawable.icon_power_lightning_mode3,
                R.drawable.icon_power_lightning_mode4};
        mBinding.ivMode.setImageResource(modeIcons[mode]);
    }

    public void setBalancingMode(LottieManager.AnimBalanceState mode) {
        isBalancing = mode == LottieManager.AnimBalanceState.ING;
        mLottieManager.controlBalancingMode(mBinding.lavBalancingMode, mode);
    }

    public void changeViewStateByUpdateState(final boolean showUpdate) {
        if (showUpdate) {
            mBinding.viewDisable.setVisibility(View.VISIBLE);
            mBinding.ivUpdating.setVisibility(View.VISIBLE);
        } else {
            mBinding.viewDisable.setVisibility(View.GONE);
            mBinding.ivUpdating.setVisibility(View.GONE);
        }
    }

    /**
     * 显示中间开关
     */
    public void showSwitchStatus(boolean show, boolean loading) {
        if (show) {
            mBinding.rippleBackground.startRippleAnimation();
        } else {
            mBinding.rippleBackground.stopRippleAnimation();
        }
        mBinding.clSwitch.setOnClickListener(v -> {
            if (!mIsLoading) {
                setLoading(true);
//                    submitCmd(DsCamCmd.SET_INVERTER_OPEN, 0);
                if (mOperateListener != null) {
                    mOperateListener.onClickSwitch(v);
                }
            }
        });
        if (show) {
            setLoading(loading);
        } else {
            setLoading(false);
        }
        mBinding.clSwitch.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
    }

    /**
     * 是否loading状态
     *
     * @param isLoading
     */
    public void setLoading(boolean isLoading) {
        mIsLoading = isLoading;
        mBinding.rippleBackground.setVisibility(isLoading ? View.INVISIBLE : View.VISIBLE);
        if (isLoading) {
            mBinding.lavLoading.playAnimation();
        } else {
            mBinding.lavLoading.pauseAnimation();
        }
        mBinding.ivSwitch.setVisibility(isLoading ? GONE : VISIBLE);
        mBinding.lavLoading.setVisibility(isLoading ? VISIBLE : GONE);
//        if (isLoading) {
//            mBinding.ivSwitch.setImageTintList(null);
//            mBinding.ivSwitch.setImageResource(R.drawable.icon_plugin_list_status_loading);
//            mBinding.ivSwitch.startAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.rotation));
//        } else {
//            mBinding.ivSwitch.clearAnimation();
//            mBinding.ivSwitch.setImageResource(R.drawable.icon_power_restart);
//        }
    }

    private void setNotVal() {
        mBinding.cdvInverter.stopAnim();
        mBinding.tvSolarVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.tvGridVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.ivExtraSupply.setVisibility(!isThirdPVOn ? VISIBLE : INVISIBLE);
        mBinding.lavDualPower.setVisibility(isThirdPVOn ? VISIBLE : INVISIBLE);
        mBinding.tvExtraSupplyKey.setLocalText(mContext.getString(isThirdPVOn ?
                R.string.dual_power : R.string.power_station_additional_load));
        mBinding.tvAdditionVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.tvOtherVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.tvVehicleVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.tvBatteryVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.viewBattery.setOnline(false);
        mBinding.viewBattery.setProgress(0f, true);
        mBinding.viewBattery.setChargeStatus(0, true);
        mBinding.ivSecureSupply.setAlpha(isGridValid ? 0.3f : 1f);
        mBinding.tvSecureSupplyKey.setVisibility(isGridValid ? INVISIBLE : VISIBLE);
        mBinding.llSecureSupply.setVisibility(isGridValid ? INVISIBLE : VISIBLE);

        mLottieManager.controlSolarAnim(mBinding.lavSolar, LottieManager.SOLAR_NULL);
        mLottieManager.controlVehicleAnim(mBinding.lavVehicle, LottieManager.VEHICLE_NULL);
        mLottieManager.controlKeepOnLoad(mBinding.ivSecureSupply, LottieManager.KEEP_ON_LOAD_OFF);
        mLottieManager.controlAdditionLoad(mBinding.ivExtraSupply, LottieManager.ADDITION_LOAD_OFF);
        mLottieManager.controlAdditionLoad(mBinding.lavDualPower, LottieManager.DUAL_POWER_OFF);
        if (isBalancing) {
            mLottieManager.controlBalancingMode(mBinding.lavBalancingMode, LottieManager.AnimBalanceState.END);
        }
    }

    public void setOffline(boolean offline) {
        if (offline) {
            setNotVal();
            changeViewStateByUpdateState(false);
        } else {
            mBinding.viewBattery.setOnline(true);
        }
    }

    public void restartInverter() {
        setNotVal();
        showSwitchStatus(true, true);
    }

    public void resetViewBattery(float emergencyReserve, float smartReserve) {
        mBinding.viewBattery.setEmergencyReserve(emergencyReserve);
        mBinding.viewBattery.setSmartReserve(smartReserve);
        mBinding.viewBattery.resetColor();
        mBinding.viewBattery.invalidate();
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

    public void stopAnim() {
        stopAnim(false);
    }

    public void stopAnim(boolean drawPath) {
        mBinding.cdvInverter.stopAnim(drawPath);
    }

    public void setBatteryViewAnim(boolean isAnim) {
        mBinding.viewBattery.setAnim(isAnim);
    }

    private OnOperateListener mOperateListener;

    public OnOperateListener getOperateListener() {
        return mOperateListener;
    }

    public void setOperateListener(OnOperateListener operateListener) {
        this.mOperateListener = operateListener;
    }

    public interface OnOperateListener {
        void onClickEV(View view);

        void onClickUpdate(View view);

        void onClickSwitch(View view);
    }
}
