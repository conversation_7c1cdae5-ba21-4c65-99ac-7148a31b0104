package com.dinsafer.module.powerstation.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

public class EcoProgressView extends View {

    private float mBarWidth = 24f;
    private float mCountTextSize = 48f;
    private float mDefTextSize = 24f;
    private int layoutWidth = 0;
    private int layoutHeight = 0;
    private float mSpaceBtwTexts = 20f;

    private int mProgressColor = Color.YELLOW;
    private int mRimColor = Color.GRAY;
    private int mSunColor = Color.WHITE;
    private int mCountTextColor = Color.BLACK;
    private int mDefTextColor = Color.BLACK;

    private float paddingTop = 0f;
    private float paddingBottom = 0f;
    private float paddingLeft = 0f;
    private float paddingRight = 0f;

    private RectF mRimBounds = new RectF();
    private RectF mProgressBounds = new RectF();

    private Paint mCirclePaint = new Paint();
    private Paint mBarPaint = new Paint();
    private Paint mSunPaint = new Paint();
    private TextPaint mCountTextPaint = new TextPaint();
    private TextPaint mDefTextPaint = new TextPaint();

    private int mSunshineCount = 8;
    private int mSunshineInterval = 45;
    private float mSunshineWidth = 4f;

    private String mCountText = "0";
    private String mDefText = null;

    private float mCountTextWidth = 0f;
    private float mCountTextHeight = 0f;
    private float mDefTextHeight = 0f;
    private float mDefTextWidth = 0f;

    private int mPercentage = 0;

    public EcoProgressView(Context context) {
        this(context, null);
    }

    public EcoProgressView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EcoProgressView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttr(context, attrs, defStyleAttr);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        layoutWidth = w;
        layoutHeight = h;
        setupBounds();
        setupPaints();

        invalidate();
    }

    private void setupPaints() {
        mBarPaint.setColor(mProgressColor);
        mBarPaint.setAntiAlias(true);
        mBarPaint.setStyle(Paint.Style.STROKE);
        mBarPaint.setStrokeWidth(mBarWidth);
        mBarPaint.setStrokeCap(Paint.Cap.ROUND);

        mCirclePaint.setColor(mRimColor);
        mCirclePaint.setAntiAlias(true);
        mCirclePaint.setStyle(Paint.Style.STROKE);
        mCirclePaint.setStrokeWidth(mBarWidth);

        mSunPaint.setColor(mSunColor);
        mSunPaint.setAntiAlias(true);
        mSunPaint.setStyle(Paint.Style.FILL);

        mCountTextPaint.setColor(mCountTextColor);
        mCountTextPaint.setFlags(Paint.ANTI_ALIAS_FLAG);
        mCountTextPaint.setFakeBoldText(true);
        mDefTextPaint.setColor(mDefTextColor);
        mDefTextPaint.setFlags(Paint.ANTI_ALIAS_FLAG);
    }

    private void setupBounds() {
        int minValue = Math.min(layoutWidth, layoutHeight);

        // Calc the Offset if needed
        int xOffset = layoutWidth - minValue;
        int yOffset = layoutHeight - minValue;

        paddingTop = this.getPaddingTop();
        paddingBottom = this.getPaddingBottom();
        paddingLeft = this.getPaddingLeft();
        paddingRight = this.getPaddingRight();

        int width = getWidth();
        int height = getHeight();

        mRimBounds = new RectF(
                paddingLeft + mBarWidth / 2,
                paddingTop + mBarWidth / 2,
                width - paddingRight - mBarWidth / 2,
                height - paddingBottom - mBarWidth / 2);

        mProgressBounds = new RectF(
                paddingLeft + mBarWidth / 2,
                paddingTop + mBarWidth / 2,
                width - paddingRight - mBarWidth / 2,
                height - paddingBottom - mBarWidth / 2);

        // Count number text
        mCountTextPaint.setTextSize(mCountTextSize);
        Paint.FontMetrics fontMetrics = mCountTextPaint.getFontMetrics();
        mCountTextHeight = fontMetrics.bottom;
        mCountTextWidth = mCountTextPaint.measureText((mCountText == null || mCountText.isEmpty()) ? " " : mCountText);

        // Definition text
        if (mDefText != null) {
            mDefTextPaint.setTextSize(mDefTextSize);
            Paint.FontMetrics fontDefMetrics = mDefTextPaint.getFontMetrics();
            mDefTextHeight = fontDefMetrics.bottom;
            mDefTextWidth = mDefTextPaint.measureText(mDefText.isEmpty() ? " " : mDefText);
        }
    }

    private void initAttr(Context context, AttributeSet attrs, int defStyle) {
        // Load attributes
        TypedArray a = context.obtainStyledAttributes(
                attrs, R.styleable.EcoProgressView, defStyle, 0);
        mCountText = a.getString(R.styleable.EcoProgressView_countText);
        mDefText = a.getString(R.styleable.EcoProgressView_definitionText);
        mBarWidth = a.getDimension(R.styleable.EcoProgressView_barWidth, mBarWidth);
        mProgressColor = a.getColor(R.styleable.EcoProgressView_progressColor, mProgressColor);
        mRimColor = a.getColor(R.styleable.EcoProgressView_rimColor, mRimColor);
        mCountTextColor = a.getColor(R.styleable.EcoProgressView_countTextColor, mCountTextColor);
        mDefTextColor = a.getColor(R.styleable.EcoProgressView_defTextColor, mDefTextColor);
        mCountTextSize = a.getDimension(R.styleable.EcoProgressView_countTextSize, mCountTextSize);
        mDefTextSize = a.getDimension(R.styleable.EcoProgressView_defTextSize, mDefTextSize);
        mPercentage = a.getInt(R.styleable.EcoProgressView_percentage, mPercentage);
        mSpaceBtwTexts = a.getDimension(R.styleable.EcoProgressView_spaceBtwText, mSpaceBtwTexts);
        mSunColor = a.getColor(R.styleable.EcoProgressView_sunColor, mSunColor);

        a.recycle();

        // Set up a default TextPaint object
        mCountTextPaint = new TextPaint();
        mCountTextPaint.setFlags(Paint.ANTI_ALIAS_FLAG);
        mCountTextPaint.setTextAlign(Paint.Align.LEFT);
        mSunshineInterval = 360 / mSunshineCount;
        // Update TextPaint and text measurements from attributes
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawArc(mRimBounds, 0f, 360f, false, mCirclePaint);
        canvas.drawArc(mProgressBounds, -90f, mPercentage, false, mBarPaint);
        drawSun(canvas);
        if (mCountText != null) {
            float horizontalCountTextOffset = mCountTextPaint.measureText(mCountText) / 2;
            canvas.drawText(mCountText,
                    getWidth() / 2 - horizontalCountTextOffset,
                    (getHeight() / 2) + ((mDefText == null) ? mCountTextSize / 2 : 0f),
                    mCountTextPaint
            );
        }

        if (mDefText != null) {
            float horizontalDefTextOffset = mDefTextPaint.measureText(mDefText) / 2;
            canvas.drawText(Local.s(mDefText),
                    getWidth() / 2 - horizontalDefTextOffset,
                    (getHeight() / 2) + mCountTextHeight + mSpaceBtwTexts,
                    mDefTextPaint
            );
        }

    }

    private void drawSun(Canvas canvas) {
        int cx = getWidth() / 2;
        float cy = mRimBounds.top;
        float radius = mBarWidth / 5;
        mSunPaint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(cx, cy, radius, mSunPaint);
        canvas.save();
        mSunPaint.setStyle(Paint.Style.STROKE);
        mSunPaint.setStrokeWidth(mSunshineWidth);
        mSunPaint.setStrokeCap(Paint.Cap.ROUND);
        canvas.rotate(0f, getWidth() / 2, mRimBounds.top);
        for (int i = 0; i <= mSunshineCount; i++) {
            canvas.drawLine(cx + 1.5f * radius, cy, cx + 2 * radius, cy, mSunPaint);
            canvas.rotate(mSunshineInterval, cx, cy);
        }
        canvas.restore();
    }

    public void setCountText(String countText) {
        mCountText = countText;
        invalidate();
    }

    public void setDefText(String defText) {
        mDefText = defText;
        invalidate();
    }

    public void setProgressColor(int color) {
        mProgressColor = color;
        invalidate();
    }

    public void setPercentage(int per) {
        mCountText = per+"%";
        int degree = (int) (360*(per/100f));
        startAnimation(degree);
    }

    private void startAnimation(int per) {
        int diff = per - mPercentage;
        ValueAnimator valueAnimator = ValueAnimator
                .ofInt(mPercentage, mPercentage + diff)
                .setDuration(1000);
        valueAnimator.addUpdateListener(animation  -> {
            mPercentage = (int) animation.getAnimatedValue();
            invalidate();
        });
        valueAnimator.start();
    }
}
