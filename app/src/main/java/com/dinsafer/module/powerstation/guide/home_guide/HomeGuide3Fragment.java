package com.dinsafer.module.powerstation.guide.home_guide;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentHomeGuide3Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class HomeGuide3Fragment extends MyBaseFragment<FragmentHomeGuide3Binding> {

    public static final String TAG = HomeGuide3Fragment.class.getSimpleName();

    public static HomeGuide3Fragment newInstance() {
        return new HomeGuide3Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_home_guide_3;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
