package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.utils.AIColorUtil;

public class AIGradientView extends View {

    private LinearGradient mLinearGradient;
    private Matrix mMatrix;
    private Context mContext;
    private int strokeWidth;
    private Paint mPaint;
    private Paint mFillPaint;
    private Path mPath;
    private int style;
    private int tlRadius;
    private int blRadius;
    private int trRadius;
    private int brRadius;
    private int color;
    private int fillLayerColor;
    private boolean isGradientEnabled;
    private boolean isDrawFillLayer;

    public AIGradientView(Context context) {
        this(context, null);
    }

    public AIGradientView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AIGradientView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        TypedArray typedArray = context.getTheme().obtainStyledAttributes(
                attrs, R.styleable.AIGradientView, 0, 0);
        style = typedArray.getInt(R.styleable.AIGradientView_ai_gradient_style, 0);
        strokeWidth = typedArray.getDimensionPixelSize(R.styleable.AIGradientView_ai_gradient_stroke_width, 3);
        tlRadius = typedArray.getDimensionPixelSize(R.styleable.AIGradientView_ai_gradient_tl_radius, 0);
        blRadius = typedArray.getDimensionPixelSize(R.styleable.AIGradientView_ai_gradient_bl_radius, 0);
        trRadius = typedArray.getDimensionPixelSize(R.styleable.AIGradientView_ai_gradient_tr_radius, 0);
        brRadius = typedArray.getDimensionPixelSize(R.styleable.AIGradientView_ai_gradient_br_radius, 0);
        color = typedArray.getColor(R.styleable.AIGradientView_ai_gradient_color, 0);
        fillLayerColor = typedArray.getColor(R.styleable.AIGradientView_ai_gradient_fill_layer_color, 0);
        isGradientEnabled = typedArray.getBoolean(R.styleable.AIGradientView_ai_gradient_enabled, true);
        isDrawFillLayer = typedArray.getBoolean(R.styleable.AIGradientView_ai_gradient_draw_fill_layer, false);
        typedArray.recycle();

        mPath = new Path();
        mMatrix = new Matrix();
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(style == 0 ? Paint.Style.STROKE : Paint.Style.FILL);
        mPaint.setStrokeWidth(strokeWidth);
        mFillPaint = new Paint();
        mFillPaint.setAntiAlias(true);
        mFillPaint.setColor(fillLayerColor);
//        mPaint.setColor(color);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        float max = Math.max(w, h) * 1.0f;
        float scaleX = w / max;
        float scaleY = h / max;

        mLinearGradient = new LinearGradient(0, Math.max(h, max),  Math.max(w, max), 0, AIColorUtil.getAIColor(mContext),
                AIColorUtil.getAIColorPosition(), Shader.TileMode.CLAMP);
        if (isGradientEnabled) {
            mMatrix.setScale(scaleX, scaleY);
            mLinearGradient.setLocalMatrix(mMatrix);
            mPaint.setShader(mLinearGradient);
            mPaint.getShader();
        } else {
            mPaint.setShader(null);
            mPaint.setColor(color);
        }
        float halfStrokeWidth = strokeWidth / 2f;
        boolean isStroke = style == 0;
        mPath.reset();
        mPath.moveTo(isStroke ? halfStrokeWidth : 0, isStroke ? tlRadius + halfStrokeWidth : tlRadius);
        if (tlRadius > 0) {
            float tlQuad = tlRadius;
            mPath.rQuadTo(0, -tlQuad, tlQuad, -tlQuad);
        }
        mPath.rLineTo(isStroke ? w - strokeWidth - tlRadius - trRadius : w - tlRadius - trRadius, 0);
        if (trRadius > 0) {
            float trQuad = trRadius;
            mPath.rQuadTo(trQuad, 0, trQuad, trQuad);
        }
        mPath.rLineTo(0, isStroke ? h - strokeWidth - trRadius - brRadius : h - trRadius - brRadius);
        if (brRadius > 0) {
            float brQuad = brRadius;
            mPath.rQuadTo(0, brQuad, -brQuad, brQuad);
        }
        mPath.rLineTo(-(isStroke ? w - strokeWidth - brRadius - blRadius : w - brRadius - blRadius), 0);
        if (blRadius > 0) {
            float blQuad = blRadius;
            mPath.rQuadTo(-blQuad, 0, -blQuad, -blQuad);
        }

        mPath.rLineTo(0, -(isStroke ? h - strokeWidth - blRadius - tlRadius : h - blRadius - tlRadius));
        mPath.close();
        invalidate();
    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);
        if (isDrawFillLayer) {
            canvas.drawPath(mPath, mFillPaint);
        }
        canvas.drawPath(mPath, mPaint);
    }

    public int getStyle() {
        return style;
    }

    public void setStyle(int style) {
        this.style = style;
    }

    public int getStrokeWidth() {
        return strokeWidth;
    }

    public void setStrokeWidth(int strokeWidth) {
        this.strokeWidth = strokeWidth;
    }

    public int getTlRadius() {
        return tlRadius;
    }

    public void setTlRadius(int tlRadius) {
        this.tlRadius = tlRadius;
    }

    public int getBlRadius() {
        return blRadius;
    }

    public void setBlRadius(int blRadius) {
        this.blRadius = blRadius;
    }

    public int getTrRadius() {
        return trRadius;
    }

    public void setTrRadius(int trRadius) {
        this.trRadius = trRadius;
    }

    public int getBrRadius() {
        return brRadius;
    }

    public void setBrRadius(int brRadius) {
        this.brRadius = brRadius;
    }

    public void setRadius(int radius) {
        this.tlRadius = radius;
        this.blRadius = radius;
        this.trRadius = radius;
        this.brRadius = radius;
    }

    public int getColor() {
        return color;
    }

    public void setColor(int color) {
        this.color = color;
    }

    public boolean isGradientEnabled() {
        return isGradientEnabled;
    }

    public void setGradientEnabled(boolean gradientEnabled) {
        isGradientEnabled = gradientEnabled;
    }

    public Path getPath() {
        return mPath;
    }
}
