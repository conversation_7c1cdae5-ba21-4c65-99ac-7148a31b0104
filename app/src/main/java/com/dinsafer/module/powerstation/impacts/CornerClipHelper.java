package com.dinsafer.module.powerstation.impacts;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Outline;
import android.graphics.Path;
import android.graphics.RectF;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.FrameLayout;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;

public class CornerClipHelper {

    private final View mTargetView;
    private final Path mClipPath = new Path();
    private final float[] mRadii = new float[8];
    private final RectF mRectF = new RectF();

    public CornerClipHelper(View view, float topLeft, float topRight) {
        mTargetView = view;

        // 设置圆角参数（单位：dp）
        DisplayMetrics metrics = view.getResources().getDisplayMetrics();
        mRadii[0] = mRadii[1] = topLeft * metrics.density;  // 左上
        mRadii[2] = mRadii[3] = topRight * metrics.density; // 右上
        // 其他角保持直角（右下/左下设为0）
        System.arraycopy(new float[]{0, 0, 0, 0}, 0, mRadii, 4, 4);

        // 设置硬件加速兼容模式
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN_MR2) {
            view.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }
    }

    public void updateClipPath() {
        mRectF.set(0, 0, mTargetView.getWidth(), mTargetView.getHeight());
        mClipPath.reset();
        mClipPath.addRoundRect(mRectF, mRadii, Path.Direction.CW);
        // 分版本处理裁剪
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            applyModernClipping();
        } else {
            applyLegacyClipping();
        }
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private void applyModernClipping() {
        mTargetView.setClipToOutline(true);
        mTargetView.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                // 修改高度，为了左下/右下不显示圆角
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight() + DensityUtil.dp2px(mTargetView.getContext(), 24),
                        Math.max(mRadii[0], mRadii[2]));
            }
        });
    }

    private void applyLegacyClipping() {
        // 通过自定义ViewGroup实现裁剪
        if (mTargetView.getParent() instanceof LegacyClipLayout) return;

        LegacyClipLayout wrapper = new LegacyClipLayout(mTargetView.getContext());
        ViewGroup parent = (ViewGroup) mTargetView.getParent();
        int index = parent.indexOfChild(mTargetView);
        parent.removeView(mTargetView);
        wrapper.addView(mTargetView);
        parent.addView(wrapper, index, mTargetView.getLayoutParams());
    }

    // 旧版本裁剪容器（API 21以下）
    private static class LegacyClipLayout extends FrameLayout {
        private final Path mClipPath = new Path();
        private final RectF mRectF = new RectF();

        public LegacyClipLayout(Context context) {
            super(context);
            setWillNotDraw(false);
        }

        @Override
        protected void onSizeChanged(int w, int h, int oldw, int oldh) {
            // 修改高度，为了左下/右下不显示圆角
            h = h + DensityUtil.dp2px(getContext(), 24);
            super.onSizeChanged(w, h, oldw, oldh);
            mRectF.set(0, 0, w, h);
            mClipPath.reset();
            // 获取子View的圆角参数
            if (getChildCount() > 0) {
                CornerClipHelper helper = (CornerClipHelper) getChildAt(0).getTag(R.id.clip_helper);
                if (helper != null) {
                    mClipPath.addRoundRect(mRectF, helper.mRadii, Path.Direction.CW);
                }
            }
        }

        @Override
        protected void dispatchDraw(Canvas canvas) {
            canvas.save();
            canvas.clipPath(mClipPath);
            super.dispatchDraw(canvas);
            canvas.restore();
        }
    }
}
