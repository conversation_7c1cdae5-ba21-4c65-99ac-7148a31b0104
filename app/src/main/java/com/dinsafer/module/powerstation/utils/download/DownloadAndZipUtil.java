package com.dinsafer.module.powerstation.utils.download;

import android.os.AsyncTask;

import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class DownloadAndZipUtil {

    public static void downloadAndZipFiles(List<DownloadBean> sourceUrls, String zipPath, String zipFileName) {
        new DownloadAndZipTask(sourceUrls, zipPath, zipFileName).execute();
    }

    private static class DownloadAndZipTask extends AsyncTask<Void, Void, Boolean> {

        private final List<DownloadBean> downloadBeans;
        private final String zipPath;
        private final String zipFileName;

        public DownloadAndZipTask(List<DownloadBean> downloadBeans, String zipPath, String zipFileName) {
            this.downloadBeans = downloadBeans;
            this.zipPath = zipPath;
            this.zipFileName = zipFileName;
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            List<File> files = new ArrayList<>();
            File dir = new File(zipPath);
            if (!dir.exists()) dir.mkdirs();
            for (DownloadBean downloadBean : downloadBeans) {
                File file = downloadFile(downloadBean);
                if (file == null) {
                    DDLog.e("DownloadAndZipUtil", "downloadFile is null!");
                }
                files.add(file);
                downloadBean.setFile(file);
            }
            if (files.size() == downloadBeans.size()) {
                return zipFiles(downloadBeans, zipPath, zipFileName);
            } else {
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean result) {
            if (result) {
                DDLog.d("DownloadAndZipUtil", "Files downloaded and zipped successfully.");
            } else {
                DDLog.e("DownloadAndZipUtil", "Failed to download or zip files.");
            }
            DownloadAndZipResultEvent event = new DownloadAndZipResultEvent(result);
            event.setPath(zipPath + zipFileName);
            EventBus.getDefault().post(event);
            for (DownloadBean downloadBean : downloadBeans) {
                File tempFile = new File(zipPath, downloadBean.getName() + ".tmp");
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        }

        private File downloadFile(DownloadBean downloadBean) {
            File file = null;
            try {
                OkHttpClient client = new OkHttpClient();
                Request request = new Request.Builder()
                        .url(downloadBean.getUrl())
                        .build();

                File outputFile = new File(zipPath, downloadBean.getName() + ".tmp");

                // 检查文件是否存在，存在则删除
                if (outputFile.exists()) {
                    boolean deleted = outputFile.delete();
                    if (!deleted) {
                        DDLog.e("DownloadAndZipUtil", "Failed to delete existing file: " + outputFile.getAbsolutePath());
                        return null;  // 终止下载流程
                    }
                }

                Response response = client.newCall(request).execute();
                try (InputStream inputStream = new BufferedInputStream(response.body().byteStream());
                     FileOutputStream outputStream = new FileOutputStream(outputFile)) {

                    byte[] data = new byte[1024];
                    int count;
                    while ((count = inputStream.read(data)) != -1) {
                        outputStream.write(data, 0, count);
                    }
                }
                file = outputFile; // Only assign if download was successful
            } catch (IOException e) {
                e.printStackTrace();
            }
            return file;
        }

        private boolean zipFiles(List<DownloadBean> downloadBeans, String zipPath, String zipFileName) {
            File zipFile = new File(zipPath, zipFileName);
            try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
                for (DownloadBean downloadBean : downloadBeans) {
                    String fileName = downloadBean.getName() + downloadBean.getSuffix();
                    if (!addToZipFile(downloadBean.getFile(), fileName, zos)) {
                        DDLog.e("DownloadAndZipUtil", "Failed to add file to zip: " + fileName);
                        return false;
                    }
                }
                DDLog.i("DownloadAndZipUtil", "ZIP created at: " + zipFile.getAbsolutePath());
                return true;
            } catch (IOException e) {
                DDLog.e("DownloadAndZipUtil", "Zip error: " + e.getMessage() + " File: " + zipFile.getAbsolutePath());
                return false;
            }
        }

        private boolean addToZipFile(File fileToAdd, String fileName, ZipOutputStream zos) throws IOException {
            if (fileToAdd == null) {
                DDLog.e("DownloadAndZipUtil", "file addToZipFile is null!");
                return false;
            }
            ZipEntry zipEntry = new ZipEntry(fileName);
            zos.putNextEntry(zipEntry);
            try (FileInputStream fis = new FileInputStream(fileToAdd)) {
                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zos.write(bytes, 0, length);
                }
            }
            zos.closeEntry();
            return true;
        }
    }
}