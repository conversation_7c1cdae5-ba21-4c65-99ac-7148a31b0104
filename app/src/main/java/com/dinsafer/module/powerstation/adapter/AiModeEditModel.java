package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintSet;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAiModeEditBinding;
import com.dinsafer.dinnet.databinding.ItemScheduledModeDialogBinding;
import com.dinsafer.ui.rv.BaseBindModel;

public class AiModeEditModel implements BaseBindModel<ItemAiModeEditBinding> {

    private Context mContext;
    private int mode;
    private int emergencyReserve;
    private int smartReserve;
    private int sectionType;
    private boolean isSelected;
    private boolean isEnabled;

    public AiModeEditModel(Context context, int mode, boolean isSelected) {
        this.mContext = context;
        this.mode = mode;
        this.isSelected = isSelected;
    }

    public AiModeEditModel(Context mContext, int mode, int sectionType,
                           int emergencyReserve, int smartReserve, boolean isSelected) {
        this.mContext = mContext;
        this.mode = mode;
        this.sectionType = sectionType;
        this.emergencyReserve = emergencyReserve;
        this.smartReserve = smartReserve;
        this.isSelected = isSelected;
    }

    public AiModeEditModel(Context mContext, int mode, int sectionType,
                           int emergencyReserve, int smartReserve,
                           boolean isSelected, boolean isEnabled) {
        this.mContext = mContext;
        this.mode = mode;
        this.sectionType = sectionType;
        this.emergencyReserve = emergencyReserve;
        this.smartReserve = smartReserve;
        this.isSelected = isSelected;
        this.isEnabled = isEnabled;
    }



    @Override
    public int getLayoutID() {
        return R.layout.item_ai_mode_edit;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemAiModeEditBinding binding) {
        binding.clParent.setEnabled(isEnabled);
        binding.tvName.setAlpha(isEnabled ? 1f : 0.5f);
        binding.tvTag.setVisibility(isEnabled ? View.GONE : View.VISIBLE);
        binding.ivCheck.setVisibility(isSelected ? View.VISIBLE : View.INVISIBLE);
        binding.clParent.setSelected(isSelected);
        binding.tvName.setAlpha(isSelected ? 1f : 0.5f);
        binding.tvOne.setLocalText(emergencyReserve + "%");
        binding.tvTwo.setLocalText(smartReserve + "%");

        switch (mode) {
            case -1:
                initDischarge(binding);
                break;

            case 0:
                initNormal(binding);
                break;

            case 1:
                initCharge(binding);
                break;
        }
        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(binding.clValues);
        switch (sectionType) {
            case 1:
                binding.viewThumb.setBackgroundResource(R.drawable.shape_bg_section_one);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.LEFT, R.id.tv_one, ConstraintSet.LEFT);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.TOP, R.id.tv_one, ConstraintSet.TOP);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.RIGHT, R.id.tv_one, ConstraintSet.RIGHT);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.BOTTOM, R.id.tv_one, ConstraintSet.BOTTOM);
                break;

            case 2:
                binding.viewThumb.setBackgroundResource(R.drawable.shape_bg_section_two);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.LEFT, R.id.tv_two, ConstraintSet.LEFT);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.TOP, R.id.tv_two, ConstraintSet.TOP);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.RIGHT, R.id.tv_two, ConstraintSet.RIGHT);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.BOTTOM, R.id.tv_two, ConstraintSet.BOTTOM);
                break;

            case 3:
                binding.viewThumb.setBackgroundResource(R.drawable.shape_bg_section_three);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.LEFT, R.id.tv_three, ConstraintSet.LEFT);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.TOP, R.id.tv_three, ConstraintSet.TOP);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.RIGHT, R.id.tv_three, ConstraintSet.RIGHT);
                constraintSet.connect(R.id.view_thumb, ConstraintSet.BOTTOM, R.id.tv_three, ConstraintSet.BOTTOM);
                break;
        }
        constraintSet.applyTo(binding.clValues);
        binding.viewThumb.setVisibility(sectionType == 0 ? View.GONE : View.VISIBLE);
    }

    private void initDischarge(ItemAiModeEditBinding binding) {
        binding.tvName.setLocalText(mContext.getString(R.string.ps_is_battery_discharge));
        binding.tvSign.setLocalText(mContext.getString(R.string.ps_is_greater_than_sign));
        binding.tvDesc.setLocalText(mContext.getString(R.string.ps_is_scheduled_mode_value_1_desc));
        binding.tvThree.setVisibility(View.GONE);
        binding.tvWhen.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.tvSign.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.clValues.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.viewSpace.setVisibility(isSelected ? View.VISIBLE : View.GONE);
//        binding.tvDesc.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.viewLine.setVisibility(isSelected ? View.VISIBLE : View.GONE);
    }

    private void initNormal(ItemAiModeEditBinding binding) {
        binding.tvName.setLocalText(mContext.getString(R.string.ps_is_no_charge_nor_discharge));
        binding.tvDesc.setLocalText(mContext.getString(R.string.ps_is_scheduled_mode_value_2_desc));
//        binding.tvDesc.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.viewLine.setVisibility(isSelected ? View.VISIBLE : View.GONE);
    }

    private void initCharge(ItemAiModeEditBinding binding) {
        binding.tvName.setLocalText(mContext.getString(R.string.Charge_with_grid));
        binding.tvSign.setLocalText(mContext.getString(R.string.ps_is_less_than_sign));
        binding.tvDesc.setLocalText(mContext.getString(R.string.ps_is_scheduled_mode_value_3_desc));
        binding.tvWhen.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.tvSign.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.clValues.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.viewSpace.setVisibility(isSelected ? View.VISIBLE : View.GONE);
//        binding.tvDesc.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        binding.viewLine.setVisibility(isSelected ? View.VISIBLE : View.GONE);
    }

    public Context getContext() {
        return mContext;
    }

    public void setContext(Context context) {
        this.mContext = context;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int type) {
        this.mode = type;
    }

    public int getEmergencyReserve() {
        return emergencyReserve;
    }

    public void setEmergencyReserve(int emergencyReserve) {
        this.emergencyReserve = emergencyReserve;
    }

    public int getSmartReserve() {
        return smartReserve;
    }

    public void setSmartReserve(int smartReserve) {
        this.smartReserve = smartReserve;
    }

    public int getSectionType() {
        return sectionType;
    }

    public void setSectionType(int sectionType) {
        this.sectionType = sectionType;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public boolean isEnabled() {
        return isEnabled;
    }

    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
    }
}
