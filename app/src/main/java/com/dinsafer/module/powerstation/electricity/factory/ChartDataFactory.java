package com.dinsafer.module.powerstation.electricity.factory;

import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;

import java.util.List;

public class ChartDataFactory {

    private int index;
    private int size;
    private int interval;
    private CycleType cycleType;
    private List<List<Float>> pendingData;

    public ChartDataFactory(int index, int size, int interval, CycleType cycleType, List<List<Float>> pendingData) {
        this.index = index;
        this.size = size;
        this.interval = interval;
        this.cycleType = cycleType;
        this.pendingData = pendingData;
    }

    public List<List<Float>> createChartData() {
        IChartData chartData = null;
        switch (index) {
            case BaseChartFragment.CHART_ELECTRICITY_USAGE:
                chartData = new UsageChartData();
                break;

            case BaseChartFragment.CHART_ELECTRICITY_BATTERY:
                chartData = new BatteryChartData();
                break;

            case BaseChartFragment.CHART_ELECTRICITY_SOLAR:
                chartData = new SolarChartData();
                break;

            case BaseChartFragment.CHART_ELECTRICITY_GRID:
                chartData = new GridChartData();
                break;

            case BaseChartFragment.CHART_ELECTRICITY_PRICE:
                chartData = new PriceChartData();
                break;

            case BaseChartFragment.CHART_ELECTRICITY_REVENUE:
                chartData = new RevenueChartData();
                break;
        }
        if (chartData == null) {
            return null;
        } else {
            return chartData.createChartData(size, interval, cycleType, pendingData);
        }
    }

    public static Builder createBuilder() {
        return new Builder();
    }

    public static class Builder {
        private int index;
        private int size;
        private int interval;
        private CycleType cycleType;
        private List<List<Float>> pendingData;

        public Builder setIndex(int index) {
            this.index = index;
            return this;
        }

        public Builder setSize(int size) {
            this.size = size;
            return this;
        }

        public Builder setInterval(int interval) {
            this.interval = interval;
            return this;
        }

        public Builder setCycType(CycleType cycleType) {
            this.cycleType = cycleType;
            return this;
        }

        public Builder setPendingData(List<List<Float>> pendingData) {
            this.pendingData = pendingData;
            return this;
        }

        public ChartDataFactory build() {
            return new ChartDataFactory(index, size, interval, cycleType, pendingData);
        }
    }
}
