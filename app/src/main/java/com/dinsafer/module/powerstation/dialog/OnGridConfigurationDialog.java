package com.dinsafer.module.powerstation.dialog;

import android.graphics.drawable.Drawable;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogOnGridConfigurationBinding;
import com.dinsafer.module.powerstation.adapter.OGCProgressModel;
import com.dinsafer.module.powerstation.adapter.OGCSwitchModel;
import com.dinsafer.module.powerstation.adapter.OGCTwoProgressModel;
import com.dinsafer.permission.BaseBottomSheetDialog;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;

import java.util.ArrayList;
import java.util.List;

public class OnGridConfigurationDialog extends BaseBottomSheetDialog<DialogOnGridConfigurationBinding> {

    public static final String TAG = OnGridConfigurationDialog.class.getSimpleName();
    private BindMultiAdapter<BindModel> mGCSettingAdapter;
    private List<BindModel> mGCSettingModelData;

    public static OnGridConfigurationDialog newInstance() {
        OnGridConfigurationDialog dialog = new OnGridConfigurationDialog();
        return dialog;
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_on_grid_configuration;
    }

    @Override
    protected void initView() {
        super.initView();
        mBinding.getRoot().setBackgroundResource(R.drawable.shape_bg_ogc_dialog);
        Drawable drawable = getContext().getResources()
                .getDrawable(R.drawable.icon_nav_close);
        drawable.setTint(getResources().getColor(R.color.color_brand_text));
        mBinding.ivClose.setBackground(drawable);
        mBinding.llClose.setOnClickListener(view -> dismiss());
        initRV();
    }

    private void initRV() {
        mBinding.rvSetting.setLayoutManager(new LinearLayoutManager(getContext()));
        ((SimpleItemAnimator) mBinding.rvSetting.getItemAnimator()).setSupportsChangeAnimations(false);
        mGCSettingAdapter = new BindMultiAdapter<>();
        mGCSettingModelData = new ArrayList<>();
        mBinding.rvSetting.setAdapter(mGCSettingAdapter);
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.power_soft_start_rate),
                "", 0.33f, 0.66f, 0.33f, 100f));
        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.app_fast_power_down), false));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.app_overfrequency_load_shedding),
                "Hz", 50.2f, 51.5f, 50.2f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.overfrequency_load_shedding_slope_setting),
                "", 0.02f, 0.12f, 0.02f, 100f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.underfrequency_loading),
                "Hz", 47.5f, 49.8f, 47.5f, 10f));

        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.qu_mode), false));
        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.cos_phi_p_mode), false));
        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.anti_islanding_switch), false));
        mGCSettingModelData.add(new OGCSwitchModel(getContext(), getString(R.string.active_high_and_low_wear_enables_computing), false));

        mGCSettingModelData.add(new OGCTwoProgressModel(getContext(),  getString(R.string.power_factor),
                "", -0.90f, -1.00f, 0.90f, 1.00f,
                0f, 100f));

        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.boot_failure_reconnect_time),
                "s", 10f, 600f, 10f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_1_grid_voltage_overvoltage),
                "V", 252.0f, 253.0f, 252.0f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_2_grid_voltage_overvoltage),
                "V", 265.0f, 287.5f, 265.0f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_1_grid_voltage_undervoltage),
                "V", 184.0f, 195.5f, 184.0f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_2_grid_voltage_undervoltage),
                "V", 103.5f, 172.5f, 103.5f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_1_grid_frequency_overfrequency),
                "Hz", 51.5f, 52.0f, 51.5f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.level_2_grid_frequency_overfrequency),
                "Hz", 56.0f, 57.0f, 56.0f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.class_1_grid_frequency_underfrequency),
                "Hz", 47.0f, 47.5f, 47.0f, 10f));
        mGCSettingModelData.add(new OGCProgressModel(getContext(), getString(R.string.class_2_grid_frequency_underfrequency),
                "Hz", 44.0f, 45.0f, 44.0f, 10f));
        mGCSettingAdapter.setNewData(mGCSettingModelData);
    }
}
