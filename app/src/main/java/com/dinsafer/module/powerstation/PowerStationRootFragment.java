package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.viewpager.widget.ViewPager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.Constants;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.config.PsVersion1EventCode;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityPowerStationBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.module.powerstation.dialog.WarningDialog;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.ElectricityStatisticsFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.factory.ChartDataFactory;
import com.dinsafer.module.powerstation.electricity.manager.LineChartManager;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtListUpdateEvent;
import com.dinsafer.module.powerstation.event.HomePowerStationDefaultEvent;
import com.dinsafer.module.powerstation.event.ReOpenEvent;
import com.dinsafer.module.powerstation.event.ShowBmtGuideTipEvent;
import com.dinsafer.module.powerstation.event.ShowHomeGuideEvent;
import com.dinsafer.module.powerstation.guide.PSGuideFragment;
import com.dinsafer.module.powerstation.impacts.ImpactStrategiesFragment;
import com.dinsafer.module.powerstation.settings.PowerSettingsFragment;
import com.dinsafer.module.powerstation.settings.network.NetworkDiagnosisFragment;
import com.dinsafer.module.powerstation.settings.network.PSNetworkBleScanFragment;
import com.dinsafer.module.powerstation.utils.PriceTodayChartHelper;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.FeedBackFragment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.SettingInfoHelper;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/24 17:56
 * @description :
 */
public class PowerStationRootFragment extends MyBaseFragment<ActivityPowerStationBinding> implements ViewPager.OnPageChangeListener, IDeviceCallBack {
    private static final int PARAM_FROM_DEFAULT = 0;
    private static final int PARAM_FROM_STEP_ADD = 1;
    public static final int SHOW_BMT = 0;
    public static final int SHOW_BATTERY = 1;
    public static final int SHOW_EV = 11;
    public static final int SHOW_EMERGENCY = 21;
    public static final int SHOW_STATS = 31;
    private static final String SHOW_PAGER_ITEM = "show_pager_item";

    private ImageView[] dotViews;
    private ArrayList<BaseFragment> mFragments = new ArrayList<BaseFragment>(); // 将要显示的布局存放到list数组
    private CommonPagerAdapter mAdapter;
    private int curViewPagerSelectedIndex;
    private WarningDialog mWarningDialog;
    private String mDeviceId;
    private String mSubcategory;
    private int mFrom;
    private Device mPSDevice;
    private Map<String, Object> params = new HashMap<>();
    private ArrayList<WarningBean> mWarningList = new ArrayList<>();
    private String subCategory;
    private LineChartManager mLineChartManager;
    public static boolean isBSenorOutputOn;
    private int mInitPagerItem;

    private int mWifiSignal = 1;
    private int mCellularSignal = 1;

    public static int random = -1;
    public static boolean isAllBSensorInstalled;
    private boolean isFirst = true;
    private long mStartime;
    private String mTimezone;
    private boolean isShowWarmingDialog;

    private PriceTodayChartHelper mPriceTodayChartHelper;
    private static final String mFailVal = "-";

    public static PowerStationRootFragment newInstanceForStepAddPS(String title, String deviceId, String subCategory) {
        return newInstance(title, deviceId, subCategory, PARAM_FROM_STEP_ADD, SHOW_BMT);
    }

    public static PowerStationRootFragment newInstance(String title, String deviceId, String subCategory, int initPagerItem) {
        return newInstance(title, deviceId, subCategory, PARAM_FROM_DEFAULT, initPagerItem);
    }

    public static PowerStationRootFragment newInstance(String title, String deviceId, String subCategory, int from, int initPagerItem) {
        PowerStationRootFragment fragment = new PowerStationRootFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_TITLE, title);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putInt(SHOW_PAGER_ITEM, initPagerItem);
        fragment.setArguments(bundle);
        return fragment;
    }

    public boolean isShowWarmingDialog() {
        return isShowWarmingDialog;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.activity_power_station;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        EventBus.getDefault().register(this);
        mBinding.commonBar.commonBarTitle.setMaxLines(1);
        mBinding.commonBar.commonBarTitle.setEllipsize(TextUtils.TruncateAt.END);
        if (SettingInfoHelper.getInstance().isAdmin()) {
            mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);
            mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
            mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PowerSettingsFragment.newInstance(mDeviceId, mSubcategory)));
        }
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PowerSettingsFragment.newInstance(mDeviceId, mSubcategory)));

        mBinding.clUsage.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory));
        });
        mBinding.lcUsage.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory));
        });

        mBinding.clPrice.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory, BaseChartFragment.CHART_ELECTRICITY_PRICE));
        });
        mBinding.ccPrice.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(ElectricityStatisticsFragment.newInstance(mDeviceId, mSubcategory, BaseChartFragment.CHART_ELECTRICITY_PRICE));
        });
        mLineChartManager = new LineChartManager(getContext(), mBinding.lcUsage);

        initClickListener();
//        loadCacheException();
        if (mFrom >= PARAM_FROM_STEP_ADD) {
            // 检查固件版本
            checkNotifyShowGuideTip();
        }
        showTimeOutLoadinFramgment();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        mBinding.vpCurrent.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (BmtUtil.isBmtDevicePowerStore(mPSDevice)) {
                    mFragments.add(CurrentDiagramPowerStoreFragment.newInstance(mDeviceId, mSubcategory));
                } else {
                    mFragments.add(CurrentDiagramV2Fragment.newInstance(mDeviceId, mSubcategory));
                }
                mFragments.add(BatteryFragment.newInstance(mDeviceId, mSubcategory));
                mAdapter = new CommonPagerAdapter(getChildFragmentManager(), mFragments);
                mBinding.vpCurrent.setAdapter(mAdapter);
                mBinding.vpCurrent.addOnPageChangeListener(PowerStationRootFragment.this);
                initIndicator();
                if (mInitPagerItem < 0 || mInitPagerItem >= mAdapter.getCount()) {
                    mInitPagerItem = SHOW_BMT;
                }
                mBinding.vpCurrent.setCurrentItem(mInitPagerItem);
                setVPCanSlide(true);

                HomePowerStationDefaultEvent event = new HomePowerStationDefaultEvent(mDeviceId, mSubcategory);
                EventBus.getDefault().post(event);
                BmtManager.getInstance().removeDeviceGraphicCacheInfoByKey(mDeviceId, mSubcategory);
                submitCmd(BmtCmd.GET_BSENSOR_STATUS);
                getStatisticData();
                getStatRevenue();
                getPriceInfo();
                mPriceTodayChartHelper = new PriceTodayChartHelper(getContext(), mBinding.ccPrice);
                mBinding.clUsage.setVisibility(View.VISIBLE);
                mBinding.clPrice.setVisibility(View.VISIBLE);
                boolean isAdmin = SettingInfoHelper.getInstance().isAdmin();
                mBinding.viewImpactsStrategies.getRoot().setVisibility(isAdmin ? View.VISIBLE : View.GONE);
                mBinding.clHow.setVisibility(isAdmin ? View.VISIBLE : View.GONE);
                closeLoadingFragment();
                updateByDeviceOnlineStatus();
//                submitCmd(DsCamCmd.GET_GLOBAL_EXCEPTIONS);
//                submitCmd(DsCamCmd.GET_COMMUNICATE_SIGNAL);
                if (BmtUtil.isDeviceOffline(mPSDevice)) {
                    showDeviceOfflineDialog(mPSDevice);
                    return;
                }
                BmtManager.getInstance().stopPolling();
                BmtManager.getInstance().getBmtInfo(mPSDevice);
                BmtManager.getInstance().startPolling();

                // 这个功能是emaldo有, HP没有的
                boolean isDeviceGuideShowed = DBUtil.Bool(DBKey.KEY_DEVICE_GUIDE_SHOWED);
                if (!isDeviceGuideShowed) {
                    getDelegateActivity().addCommonFragment(PSGuideFragment.newInstance(PSGuideFragment.DEVICE_GUIDE));
                    DBUtil.Put(DBKey.KEY_DEVICE_GUIDE_SHOWED, true);
                }
            }
        }, 50);
    }

    public void showDeviceOfflineDialog(Device device) {
        AlertDialogV2 offlineDialog = AlertDialogV2.createBuilder(getContext())
                .setContent(getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        BmtManager.getInstance().connectDevice(device, true);
                        showTimeOutLoadinFramgmentWithErrorAlert();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        getDelegateActivity().addCommonFragment(PSNetworkBleScanFragment.newInstance(mDeviceId, mSubcategory));
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    private void loadCacheException() {
        mWarningList.clear();
        List<Integer> vertBatteryExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_BATTERY_EXCEPTIONS);
        List<Integer> vertExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_EXCEPTIONS);
        List<Integer> vertGridExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_GRID_EXCEPTIONS);
        List<Integer> vertSystemExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_SYSTEM_EXCEPTIONS);
        List<Integer> vertmpptExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_MPPT_EXCEPTIONS);
        List<Integer> vertDCExceptions = DeviceHelper.getList(mPSDevice, BmtDataKey.VERT_DC_EXCEPTIONS);
        setVertBatteryException(vertBatteryExceptions);
        setInverterException(vertExceptions);
        setVertGridException(vertGridExceptions);
        setVertSystemException(vertSystemExceptions);
        setVertMpptException(vertmpptExceptions);
//        setVertPresentException(vertPresentExceptions);
        setVertDcException(vertDCExceptions);
        List<Integer> evExceptions = DeviceHelper.getList(mPSDevice, "evExceptions");
        setEvException(evExceptions);
        List<Integer> mpptExceptions = DeviceHelper.getList(mPSDevice, "mpptExceptions");
        setMpptException(mpptExceptions);
        List<Integer> cabinetExceptions = DeviceHelper.getList(mPSDevice, "cabinetExceptions");
        setCabinetException(cabinetExceptions);
        List<Integer> batteryExceptions = DeviceHelper.getList(mPSDevice, "batteryExceptions");
        setBatteryException(batteryExceptions);
        List<Integer> systemExceptions = DeviceHelper.getList(mPSDevice, "systemExceptions");
        setSystemException(systemExceptions);
        List<Integer> communicationExceptions = DeviceHelper.getList(mPSDevice, "communicationExceptions");
        setCommunicationException(communicationExceptions);
        showException();
    }

    private void changeViewStateByUpdateState(final boolean showUpdate) {
        if (showUpdate) {
            mBinding.viewDisable.setVisibility(View.VISIBLE);
        } else {
            mBinding.viewDisable.setVisibility(View.GONE);
        }
    }


    private int getColor(int colorId) {
        return getContext().getResources().getColor(colorId);
    }

    @Override
    public void onDestroyView() {
        isBSenorOutputOn = false;
        EventBus.getDefault().unregister(this);
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        random = -1;
        isAllBSensorInstalled = false;
        super.onDestroyView();
        if (mFrom == PARAM_FROM_STEP_ADD) {
            EventBus.getDefault().post(new ShowHomeGuideEvent());
        }
    }

    private void initParams() {
        Bundle bundle = getArguments();
        String title = bundle.getString(PSKeyConstant.KEY_TITLE);
        mInitPagerItem = bundle.getInt(SHOW_PAGER_ITEM);
        mFrom = bundle.getInt(PSKeyConstant.KEY_FROM);
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
            subCategory = mPSDevice.getSubCategory();
            ExceptionWarning.deviceName = BmtManager.getDeviceName(mPSDevice);
        }
        if (TextUtils.isEmpty(title)) {
            mBinding.commonBar.commonBarTitle.setText("");
        } else {
            mBinding.commonBar.commonBarTitle.setText(title.equals(Constants.POWER_STATION) ? Local.s(title) : title);
        }
    }

    /**
     * 今日负载
     */
    protected void getStatisticData() {
        if (null != mPSDevice) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_STATS_LOADUSAGE_V2);
            params.put(BmtDataKey.INTERVAL, ElectricityStatisticsFragment.mInterval);
            params.put(BmtDataKey.OFFSET, 0);
            mPSDevice.submit(params);
        }
    }

    /**
     * 今日价格
     */
    private void getPriceInfo() {
        if (null != mPSDevice) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_ELEC_PRICE_INFO);
            params.put(BmtDataKey.OFFSET, 0);
            mPSDevice.submit(params);
        }
    }

    /**
     * 今日收益
     */
    private void getStatRevenue() {
        if (null != mPSDevice) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.GET_STATS_REVENUE);
            params.put(BmtDataKey.INTERVAL, ChartDataUtil.DAY);
            params.put(BmtDataKey.OFFSET, 0);
            mPSDevice.submit(params);
        }
    }

    /**
     * 发送cmd
     *
     * @param cmd
     */
    private void submitCmd(String cmd) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            mPSDevice.submit(params);
        }
    }

    /**
     * 发送cmd
     *
     * @param cmd
     */
    private void submitCmd(String cmd, int index) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            params.put(PSKeyConstant.INDEX, index);
            mPSDevice.submit(params);
        }
    }

    /**
     * 关闭逆变器
     */
    private void submitCmdInverterClose() {
        if (mPSDevice != null) {
            BmtManager.getInstance().stopPolling();
            EventBus.getDefault().post(new ReOpenEvent(mDeviceId, mSubcategory));
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_INVERTER_OPEN);
            params.put(PSKeyConstant.ON, false);
            List<Integer> indexs = new ArrayList<>();
            int phaseCount = BmtUtil.isThreePhase(mPSDevice) ? 3 : 1;
            for (int i = 0; i < phaseCount; i++) {
                indexs.add(i);
            }
            params.put(PSKeyConstant.INDEXS, indexs);
            mPSDevice.submit(params);
        }
    }

    /**
     * 设置忽略异常状态
     */
    private void submitCmdExceptionIgnore(int ignoreException) {
        if (mPSDevice != null && ignoreException > -1) {
            showTimeOutLoadinFramgment();
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_EXCEPTION_IGNORE);
            params.put(PSKeyConstant.IGNORE_EXCEPTION, ignoreException);
            mPSDevice.submit(params);
        }
    }

    private void initClickListener() {
        mBinding.clWarning.setOnClickListener(v -> {
            if (mWarningList.size() > 0) {
                showWarningDialog();
            }
        });
        mBinding.viewImpactsStrategies.clParent.setOnClickListener(v -> getDelegateActivity().addCommonFragment(ImpactStrategiesFragment.newInstance(mDeviceId, mSubcategory)));
        mBinding.clHow.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PSUserGuideListFragment.newInstance(mDeviceId, mSubcategory)));
        mBinding.tvDiagnosticNetwork.setOnClickListener(v -> getDelegateActivity().addCommonFragment(NetworkDiagnosisFragment.newInstance(mDeviceId, mSubcategory)));
    }


    /**
     * 如果在更新不可滑动
     *
     * @param canSlide
     */
    private void setVPCanSlide(boolean canSlide) {
        mBinding.vpCurrent.setCanSlide(canSlide);
    }

    /**
     * 初始化指示器
     */
    private void initIndicator() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        //设置小圆点左右之间的间隔
        params.setMargins(8, 0, 8, 0);
        dotViews = new ImageView[mFragments.size()];
        curViewPagerSelectedIndex = 0;
        for (int i = 0; i < mFragments.size(); i++) {
            ImageView imageView = new ImageView(getContext());
            imageView.setLayoutParams(params);
            imageView.setImageResource(R.drawable.shape_bg_power_vp_nor);
            //默认启动时，选中第一个小圆点
            imageView.setSelected(i == curViewPagerSelectedIndex);
            dotViews[i] = imageView;
            dotViews[curViewPagerSelectedIndex].setImageResource(R.drawable.shape_bg_power_vp_sel); // 设置第一个页面已被选择
            mBinding.llIndicator.addView(imageView);
        }
    }

    @Override
    public void onPageScrolled(int i, float v, int i1) {

    }

    @Override
    public void onPageSelected(int index) {
        curViewPagerSelectedIndex = index;
        for (int i = 0; i < dotViews.length; i++) {
            dotViews[i].setSelected(curViewPagerSelectedIndex == i);
            dotViews[i].setImageResource(curViewPagerSelectedIndex == i
                    ? R.drawable.shape_bg_power_vp_sel : R.drawable.shape_bg_power_vp_nor);
        }
    }

    @Override
    public void onPageScrollStateChanged(int i) {

    }

    private void showWarningDialog() {

        if (mWarningDialog == null) {
            mWarningDialog = WarningDialog.newInstance(mWarningList);
            mWarningDialog.setWarningListener(new WarningDialog.WarningListener() {
                @Override
                public void onClose() {
                    isShowWarmingDialog = false;
                }

                @Override
                public void onRestart(String type) {
                    if (TextUtils.isEmpty(type)) {
                        return;
                    }

                    switch (type) {
                        case PsVersion1EventCode.EVENT_PS_1000:
                        case PsVersion1EventCode.EVENT_PS_1001:
                        case PsVersion1EventCode.EVENT_PS_1002:
                        case PsVersion1EventCode.EVENT_PS_1003:
                        case PsVersion1EventCode.EVENT_PS_1004:
                        case PsVersion1EventCode.EVENT_PS_1005:
                        case PsVersion1EventCode.EVENT_PS_1006:
                        case PsVersion1EventCode.EVENT_PS_1007:
                        case PsVersion1EventCode.EVENT_PS_1008:
                        case PsVersion1EventCode.EVENT_PS_1009:
                        case PsVersion1EventCode.EVENT_PS_1010:

                        case PsVersion1EventCode.EVENT_PS_1100:
                        case PsVersion1EventCode.EVENT_PS_1101:
                        case PsVersion1EventCode.EVENT_PS_1103:
                        case PsVersion1EventCode.EVENT_PS_1104:
                        case PsVersion1EventCode.EVENT_PS_1105:
                        case PsVersion1EventCode.EVENT_PS_1106:
                        case PsVersion1EventCode.EVENT_PS_1107:
                        case PsVersion1EventCode.EVENT_PS_1108:
                        case PsVersion1EventCode.EVENT_PS_1109:
                        case PsVersion1EventCode.EVENT_PS_1110:
                        case PsVersion1EventCode.EVENT_PS_1111:

                        case PsVersion1EventCode.EVENT_PS_1200:
                        case PsVersion1EventCode.EVENT_PS_1201:
                        case PsVersion1EventCode.EVENT_PS_1202:
                        case PsVersion1EventCode.EVENT_PS_1203:
                        case PsVersion1EventCode.EVENT_PS_1204:
                        case PsVersion1EventCode.EVENT_PS_1205:
                        case PsVersion1EventCode.EVENT_PS_1206:
                        case PsVersion1EventCode.EVENT_PS_1207:
                        case PsVersion1EventCode.EVENT_PS_1208:
                        case PsVersion1EventCode.EVENT_PS_1209:
                        case PsVersion1EventCode.EVENT_PS_1210:
                        case PsVersion1EventCode.EVENT_PS_1211:
                        case PsVersion1EventCode.EVENT_PS_1212:
                        case PsVersion1EventCode.EVENT_PS_1213:

                        case PsVersion1EventCode.EVENT_PS_1300:
                        case PsVersion1EventCode.EVENT_PS_1301:
                        case PsVersion1EventCode.EVENT_PS_1302:
                        case PsVersion1EventCode.EVENT_PS_1303:
                        case PsVersion1EventCode.EVENT_PS_1304:
                        case PsVersion1EventCode.EVENT_PS_1305:
                        case PsVersion1EventCode.EVENT_PS_1306:
                        case PsVersion1EventCode.EVENT_PS_1307:
                        case PsVersion1EventCode.EVENT_PS_1308:
                        case PsVersion1EventCode.EVENT_PS_1309:
                        case PsVersion1EventCode.EVENT_PS_1310:
                        case PsVersion1EventCode.EVENT_PS_1311:
                        case PsVersion1EventCode.EVENT_PS_1312:

                        case PsVersion1EventCode.EVENT_PS_1400:
                        case PsVersion1EventCode.EVENT_PS_1401:
                        case PsVersion1EventCode.EVENT_PS_1402:
                        case PsVersion1EventCode.EVENT_PS_1403:
                        case PsVersion1EventCode.EVENT_PS_1404:

                        case PsVersion1EventCode.EVENT_PS_1600:
                        case PsVersion1EventCode.EVENT_PS_1601:

                        case PsVersion1EventCode.EVENT_PS_6000:
                        case PsVersion1EventCode.EVENT_PS_6001:
                        case PsVersion1EventCode.EVENT_PS_6003:
                        case PsVersion1EventCode.EVENT_PS_6004:
                        case PsVersion1EventCode.EVENT_PS_6007:
                        case PsVersion1EventCode.EVENT_PS_6010:

                        case PsVersion1EventCode.EVENT_PS_7001:

                        case PsVersion1EventCode.EVENT_PS_2000:
                        case PsVersion1EventCode.EVENT_PS_2001:
                        case PsVersion1EventCode.EVENT_PS_2002:
                        case PsVersion1EventCode.EVENT_PS_2003:
                        case PsVersion1EventCode.EVENT_PS_2004:
                        case PsVersion1EventCode.EVENT_PS_2005:
                        case PsVersion1EventCode.EVENT_PS_2006:
//                            submitCmdInverterClose();
                            BmtManager.getInstance().resetInverter(mPSDevice, true);
                            break;
                    }
                }

                @Override
                public void onWithout(String type) {
                    if (TextUtils.isEmpty(type)) {
                        return;
                    }
                    int ignoreException;
                    switch (type) {
                        case PsVersion1EventCode.EVENT_PS_7001:
                            ignoreException = 0;
                            break;
                        case PsVersion1EventCode.EVENT_PS_1203:
                            ignoreException = 1;
                            break;
                        case PsVersion1EventCode.EVENT_PS_1204:
                            ignoreException = 2;
                            break;
                        case PsVersion1EventCode.EVENT_PS_1205:
                            ignoreException = 3;
                            break;
                        default:
                            ignoreException = -1;
                    }
                    submitCmdExceptionIgnore(ignoreException);
                }

                @Override
                public void onContactCustomerSupport() {
                    getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
                }
            });
        }
        if (mWarningList.size() > 0) {
            isShowWarmingDialog = true;
            mWarningDialog.show(getChildFragmentManager(), WarningDialog.TAG);
        }
    }


    private void setVertBatteryException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertBatteryWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Inverter 异常
     *
     * @param exceptions
     */
    private void setInverterException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getInvertWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertGridException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertGridWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertSystemException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertSystemWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertMpptException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertMpptWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }

    }

    private void setVertPresentException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertPresentWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setVertDcException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getVertDcWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }


    /**
     * 处理 EV 异常
     *
     * @param exceptions
     */
    private void setEvException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getEVWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 MPPT 异常
     *
     * @param exceptions
     */
    private void setMpptException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getMPPTWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Cabinet 异常
     *
     * @param exceptions
     */
    private void setCabinetException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getCabinetWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Battery 异常
     *
     * @param exceptions
     */
    private void setBatteryException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getBatteryWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 System 异常
     *
     * @param exceptions
     */
    private void setSystemException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getSystemWarningBean(getContext(), exception);
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    /**
     * 处理 Communication 异常
     *
     * @param exceptions
     */
    private void setCommunicationException(List<Integer> exceptions) {
        if (exceptions == null) return;
        for (Integer exception : exceptions) {
            WarningBean warningBean = ExceptionWarning.getCommunicationWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
            if (warningBean == null) {
                continue;
            }
            mWarningList.add(warningBean);
        }
    }

    private void setWifiSignal(int wifiSignal) {
        final int index = wifiSignal - 1;
        final int[] wifiIcons = {R.drawable.icon_plugin_wifi_0, R.drawable.icon_plugin_wifi_1,
                R.drawable.icon_plugin_wifi_2, R.drawable.icon_plugin_wifi_3};
        if (index < 1 || index >= wifiIcons.length) {
            return;
        }
        mWifiSignal = wifiSignal;
        mBinding.ivWifi.setImageResource(wifiIcons[index]);
    }

    private void setCellularSignal(int cellular) {
        final int index = cellular - 1;
        final int[] wifiIcons = {R.drawable.icon_plugin_list_signal_0, R.drawable.icon_plugin_list_signal_1,
                R.drawable.icon_plugin_list_signal_2, R.drawable.icon_plugin_list_signal_3};
        if (index < 0 || index >= wifiIcons.length) {
            return;
        }
        mCellularSignal = cellular;
        mBinding.ivCellular.setImageResource(wifiIcons[index]);
        mBinding.ivCellular.setVisibility(index > 0 ? View.VISIBLE : View.GONE);
    }

    private void showException() {
        mBinding.tvCount.setLocalText(mWarningList.size() > 1 ? (mWarningList.size() + "") : "");
        if (mWarningList.size() > 0) {
            WarningBean warningBean = mWarningList.get(0);
            String eventKey = warningBean.getEventKey();
            if (TextUtils.isEmpty(eventKey)) {
                mBinding.tvWarning.setLocalText(warningBean.getTitle());
            } else {
                String title = Local.s(eventKey) + "-" + Local.s(warningBean.getTitle());
                mBinding.tvWarning.setText(title);
            }
        }
        mBinding.tvCount.setVisibility(mWarningList.size() > 0 ? View.VISIBLE : View.INVISIBLE);
        mBinding.clWarning.setVisibility(mWarningList.size() > 0 ? View.VISIBLE : View.GONE);
    }

    private float getSumVal(List<List<Float>> data, int interval, boolean isBSensorInstall) {
        float sum = 0;
        if (CollectionUtil.isListNotEmpty(data)) {
            List<Float> sumData = getSumData(data, isBSensorInstall);
            for (Float val : sumData) {
                sum = sum + val;
            }
        }
        return sum * interval / 60;
    }

    private List<Float> getSumData(List<List<Float>> data, boolean isBSensorInstall) {
        List<Float> sumData = new ArrayList<>();
        if (CollectionUtil.isListNotEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                List<Float> sonData = data.get(i);
                float sum = 0;
                if (CollectionUtil.isListNotEmpty(sonData)) {
                    if (sonData.size() > 1) {
                        sum = sum + sonData.get(1);
                    }
                    if (sonData.size() > 2) {
                        if (isBSensorInstall) {
                            sum = sum + sonData.get(2);
                        }
                    }
                    if (sonData.size() > 3) {
                        sum = sum + sonData.get(3);
                    }
                    sumData.add(sum);
                    Collections.sort(sumData);
                }
            }
        }
        return sumData;
    }

    private void setImpactsStrategiesNoDataVisible(boolean visible) {
        mBinding.viewImpactsStrategies.clContent.setVisibility(visible ? View.GONE : View.VISIBLE);
        mBinding.viewImpactsStrategies.clEmpty.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);

            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    long startTime;
                    String timezone;
                    int interval = 1;
                    int offset;
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case DsCamCmd.GET_GLOBAL_EXCEPTIONS:
//                                closeLoadingFragmentWithCallBack();
//                                mWarningList.clear();
//                                final List<WarningBean> warningBeanForInverter = ExceptionWarning.createWarningBeanForInverter(getContext(), result);
//                                if (warningBeanForInverter.size() > 0) {
//                                    mWarningList.addAll(warningBeanForInverter);
//                                }
//
//                                List<Integer> evs = (List<Integer>) MapUtils.get(result, PSKeyConstant.EV, null);
//                                setEvException(evs);
//                                List<Integer> mppts = (List<Integer>) MapUtils.get(result, PSKeyConstant.MPPT, null);
//                                setMpptException(mppts);
//                                List<Integer> cabinets = (List<Integer>) MapUtils.get(result, PSKeyConstant.CABINET, null);
//                                setCabinetException(cabinets);
//                                List<Integer> batteries = (List<Integer>) MapUtils.get(result, PSKeyConstant.BATTERY, null);
//                                setBatteryException(batteries);
//                                List<Integer> systems = (List<Integer>) MapUtils.get(result, PSKeyConstant.SYSTEM, null);
//                                setSystemException(systems);
//                                List<Integer> communications = (List<Integer>) MapUtils.get(result, PSKeyConstant.COMMUNICATION, null);
//                                setCommunicationException(communications);
//                                showException();
                                break;

                            case DsCamCmd.GET_COMMUNICATE_SIGNAL:
                                int wifi = DeviceHelper.getInt(result, PSKeyConstant.WIFI, 0);
                                int cellular = DeviceHelper.getInt(result, BmtDataKey.CELLULAR, 0);
                                if (wifi > 0) {
                                    setWifiSignal(wifi);
                                    mBinding.ivWifi.setVisibility(View.VISIBLE);
                                }
                                if (cellular > 0) {
                                    setCellularSignal(cellular);
                                }
                                break;

                            case DsCamCmd.INVERTER_EXCEPTION:
                            case DsCamCmd.BATTERY_EXCEPTION:
                            case DsCamCmd.MPPT_EXCEPTION:
                            case DsCamCmd.EV_EXCEPTION:
                            case DsCamCmd.COMMUNICATION_EXCEPTION:
                            case DsCamCmd.CABINET_EXCEPTION:
                            case DsCamCmd.SYSTEM_EXCEPTION:
//                                loadCacheException();
                                break;
                            case DinConst.CMD_SET_NAME:
                                updateUI();
                                break;

                            case BmtCmd.GET_STATS_LOADUSAGE:
                            case BmtCmd.GET_STATS_LOADUSAGE_V2:
                                startTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0);
                                timezone = DeviceHelper.getString(result, BmtDataKey.TIMEZONE, "");
                                interval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);
                                offset = DeviceHelper.getInt(result, BmtDataKey.OFFSET, 0);
                                String cycleType = DeviceHelper.getString(result, BmtDataKey.CYCLE_TYPE, "");
                                if (!(cycleType.equals(ChartDataUtil.DAY) && offset == 0)) {
                                    return;
                                }
                                List<List<Float>> data = (List<List<Float>>) MapUtils.get(result, BmtDataKey.DATA, new ArrayList<>());
                                float sumData = getSumVal(data, interval, true);
                                String usage = ChartDataUtil.getPowerTransferVal(sumData, sumData, true);
                                mBinding.tvUnit.setText(ChartDataUtil.getPowerUnit(sumData, true));
                                mBinding.tvValue.setLocalText(usage);
                                float peakVal = mLineChartManager.getYMax(data, true);
                                String peakStr = ChartDataUtil.getPowerTransferVal(peakVal, peakVal, true)
                                        + ChartDataUtil.getPowerUnit(peakVal, false);
                                mBinding.tvPeak.setText(Local.s(getString(R.string.ps_peak)) + " " + peakStr);
                                mBinding.tvUsage.setLocalText(getResources().getString(R.string.power_today_usage));
                                int hourCount = DDDateUtil.isDSTTransitionDay(startTime * 1000, TimeZone.getTimeZone(timezone)) ? 1500 : 1440;
                                mLineChartManager.initChart(interval, data, true, hourCount);
                                mLineChartManager.setData(data, false, true, hourCount, true);
                                if (isFirst) {
                                    mStartime = startTime;
                                    mTimezone = timezone;
                                    isFirst = false;
                                }
                                break;

                            case DsCamCmd.GET_STATS_REVENUE:
                                if (random == -1) {
                                    random = ChartDataUtil.getRandom(8, 0);
                                }
                                interval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);
                                List<List<Float>> revenueDayData = (List<List<Float>>) result.get(BmtDataKey.DATA);
                                float sumVal = 0;
                                if (CollectionUtil.isListNotEmpty(revenueDayData)) {
                                    for (List<Float> sonData : revenueDayData) {
                                        if (sonData.size() > 1) {
                                            sumVal = sumVal + sonData.get(1);
//                                            if (isAllBSensorInstalled && sonData.size() > 2) {
                                            if (sonData.size() > 2) {
                                                sumVal = sumVal + sonData.get(2);
                                            }
                                        }
                                    }
                                    sumVal = sumVal * interval / 60f;
                                    mBinding.viewImpactsStrategies.tvIsValue.setLocalText(ChartDataUtil.savePointStr(sumVal / 1000f, 1));
                                    float inverter = ChartDataUtil.getValInNote(random, sumVal / 1000f);
                                    String inverterStr = ChartDataUtil.savePointStr(inverter, 1);
                                    String note = ChartDataUtil.getRevenueNote(getContext(), inverterStr, random);
                                    mBinding.viewImpactsStrategies.tvNote.setText(note);
                                    int img = ChartDataUtil.getRevenueImgDrawable(random);
                                    if (img != 0) {
                                        mBinding.viewImpactsStrategies.ivImpactsStrategies.setImageResource(img);
                                    }
                                }
                                setImpactsStrategiesNoDataVisible(CollectionUtil.isListEmpty(revenueDayData));
                                break;

                            case BmtCmd.GET_BSENSOR_STATUS:
                                isAllBSensorInstalled = DeviceHelper.getBoolean(result, BmtDataKey.B_SENSOR_INSTALLED, false);
//                                getStatisticData();
//                                getStatRevenue();
                                break;

                            case DsCamCmd.SET_EXCEPTION_IGNORE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        // 重新获取exceptions
                                        submitCmd(DsCamCmd.GET_GLOBAL_EXCEPTIONS);
                                    }
                                }, 500);
                                break;

                            case BmtCmd.GET_VIEW_EXCEPTIONS:
                                mWarningList.clear();
                                final List<WarningBean> warningBeanForInverter = ExceptionWarning.createWarningBeanForInverter(getContext(), result, BmtUtil.isBmtDeviceV2(mPSDevice));
                                if (warningBeanForInverter.size() > 0) {
                                    mWarningList.addAll(warningBeanForInverter);
                                }
                                List<Integer> evs = DeviceHelper.getList(result, PSKeyConstant.EV);
                                setEvException(evs);
                                List<Integer> mppts = DeviceHelper.getList(result, PSKeyConstant.MPPT);
                                setMpptException(mppts);
                                List<Integer> cabinets = DeviceHelper.getList(result, PSKeyConstant.CABINET);
                                setCabinetException(cabinets);
                                List<Integer> batteries = DeviceHelper.getList(result, PSKeyConstant.BATTERY);
                                setBatteryException(batteries);
                                List<Integer> systems = DeviceHelper.getList(result, PSKeyConstant.SYSTEM);
                                setSystemException(systems);
                                List<Integer> communications = DeviceHelper.getList(result, PSKeyConstant.COMMUNICATION);
                                setCommunicationException(communications);
                                showException();
                                break;

                            case BmtCmd.GET_ELEC_PRICE_INFO:
                                offset = DeviceHelper.getInt(result, BmtDataKey.OFFSET, 0);
                                if (offset != 0) return;
                                startTime = (long) MapUtils.get(result, BmtDataKey.START_TIME, 0);
                                timezone = (String) MapUtils.get(result, BmtDataKey.TIMEZONE, "");
                                String unit = DeviceHelper.getString(result, BmtDataKey.UNIT, "").split("/")[0];
                                String showPriceUnit = DeviceHelper.getString(result, BmtDataKey.UNIT_APP, "");
                                List<List<Float>> chartDataFromServer = DeviceHelper.getList(result, BmtDataKey.DATA);
                                float peakPrice = mPriceTodayChartHelper.getYMax(chartDataFromServer);
                                String peakPriceUnit = mPriceTodayChartHelper.getUnit(peakPrice, showPriceUnit, unit);
                                if (peakPrice < 1.0f) {
                                    peakPrice = mPriceTodayChartHelper.getBasicUnitVal(peakPrice, unit);
                                }
                                String peakPriceStr = ChartDataUtil.savePointStr(peakPrice, 2, 2);
                                mBinding.tvPriceValue.setText(peakPriceStr);
                                boolean isAhead = mPriceTodayChartHelper.isAheadUnit(unit);
                                mBinding.tvAheadPriceUnit.setVisibility(isAhead ? View.VISIBLE : View.GONE);
                                mBinding.tvPriceUnit.setVisibility(isAhead ? View.GONE : View.VISIBLE);
                                mBinding.tvAheadPriceUnit.setText(peakPriceUnit);
                                mBinding.tvPriceUnit.setText(peakPriceUnit);
                                float average = mPriceTodayChartHelper.getAverage(chartDataFromServer);
                                String aveUnit = mPriceTodayChartHelper.getUnit(average, showPriceUnit, unit);
                                if (average < 1.0f) {
                                    average = mPriceTodayChartHelper.getBasicUnitVal(average, unit);
                                }
                                String averageStr = ChartDataUtil.savePointStr(average, 2, 2);
                                String finalAverage = Local.s(getString(R.string.average)) + " "
                                        + (mPriceTodayChartHelper.isAheadUnit(aveUnit) ?
                                        (aveUnit + " " + averageStr)
                                        : (averageStr + " " + aveUnit));
                                mBinding.tvAverage.setText(finalAverage);
                                mPriceTodayChartHelper.setPriceData(chartDataFromServer, startTime, timezone);
                                break;

                        }
                    } else {

                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();

                            case DsCamCmd.GET_STATS_REVENUE:
                                setImpactsStrategiesNoDataVisible(true);
                                break;

                            case DsCamCmd.SET_INVERTER_OPEN:

                            case DsCamCmd.SET_EXCEPTION_IGNORE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                                break;

                            case BmtCmd.GET_STATS_LOADUSAGE:
                            case BmtCmd.GET_STATS_LOADUSAGE_V2:
                                if (!getMainActivity().isFragmentInTopExcludeLoading(PowerStationRootFragment.this))
                                    return;
                                int hourCount = 1440;
                                interval = 5;
                                List<List<Float>> chartDataFromServer = new ArrayList<>();
                                List<List<Float>> data = ChartDataFactory.createBuilder()
                                        .setIndex(BaseChartFragment.CHART_ELECTRICITY_USAGE)
                                        .setSize(hourCount / interval + 1)
                                        .setInterval(interval)
                                        .setCycType(CycleType.DAY)
                                        .setPendingData(chartDataFromServer)
                                        .build()
                                        .createChartData();
                                mBinding.tvUnit.setText("Wh");
                                mBinding.tvValue.setLocalText(mFailVal);
                                mBinding.tvPeak.setText(Local.s(getString(R.string.ps_peak)) + " " + mFailVal + "W");
                                mBinding.tvUsage.setLocalText(getResources().getString(R.string.power_today_usage));
                                mLineChartManager.initChart(interval, data, true, hourCount);
                                mLineChartManager.setData(data, false, true, hourCount, false);
                                break;

                            case BmtCmd.GET_ELEC_PRICE_INFO:
                                if (!getMainActivity().isFragmentInTop(PowerStationRootFragment.this))
                                    return;
                                List<List<Float>> failData = new ArrayList<>();
                                List<List<Float>> priceFailData = ChartDataFactory.createBuilder()
                                        .setIndex(BaseChartFragment.CHART_ELECTRICITY_PRICE)
                                        .setSize(24)
                                        .setInterval(1)
                                        .setCycType(CycleType.DAY)
                                        .setPendingData(failData)
                                        .build()
                                        .createChartData();
                                mBinding.tvPriceValue.setText(mFailVal);
                                mBinding.tvPriceUnit.setText("");
                                String finalAverage = Local.s(getString(R.string.average)) + " "
                                        + mFailVal;
                                mBinding.tvAverage.setText(finalAverage);
                                mPriceTodayChartHelper.setPriceData(priceFailData, 0, "");
                                break;
                        }
                    }
                }
            });

        }
    }

    private void updateUI() {
        if (null == mPSDevice) {
            return;
        }
        String name = DeviceHelper.getString(mPSDevice, DinConst.INFO_NAME, "");
        if (TextUtils.isEmpty(name)) {
            mBinding.commonBar.commonBarTitle.setText("");
        } else {
            mBinding.commonBar.commonBarTitle.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
        }
    }

    /**
     * 检查是否需要显示用户指引
     * 首次添加HP5000和HP5001时，都需要通知显示用户指引
     */
    private void checkNotifyShowGuideTip() {
        final Device d = DinHome.getInstance().getDevice(mDeviceId);
        if (null == d) {
            return;
        }

        if (BmtUtil.isBmtDeviceHP5001(mPSDevice)) {
            // BMT HP5001
            if (!DBUtil.contain(DBKey.KEY_FIRST_ADD_BMT_HP5001)) {
                DBUtil.Put(DBKey.KEY_FIRST_ADD_BMT_HP5001, true);
                EventBus.getDefault().post(new ShowBmtGuideTipEvent(mDeviceId, mPSDevice.getSubCategory()));
            }
            return;
        }

        // BMT HP5000
        if (!DBUtil.contain(DBKey.KEY_FIRST_ADD_BMT_HP5000)) {
            DBUtil.Put(DBKey.KEY_FIRST_ADD_BMT_HP5000, true);
            EventBus.getDefault().post(new ShowBmtGuideTipEvent(mDeviceId, mPSDevice.getSubCategory()));
        }
    }

    /**
     * BMT状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        final String deviceId = event.getDeviceID();
        if (deviceId != null && deviceId.equals(mDeviceId) && event.getSubcategory().equals(mSubcategory)) {
            updateByDeviceOnlineStatus();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGraphicUpdateEvent event) {
        final String deviceId = event.getDeviceId();
        final String cmd = event.getCmd();
        if (TextUtils.isEmpty(deviceId) || !deviceId.equals(mDeviceId) || TextUtils.isEmpty(cmd) || !event.getSubCategory().equals(mSubcategory)) {
            return;
        }

        final Map map = event.getData();

        final Map<String, Object> result = DeviceHelper.getMap(map, PSKeyConstant.RESULT);
        int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
        if (status == StatusConstant.STATUS_SUCCESS && BmtCmd.GET_CHIPS_STATUS.equals(cmd)) {
            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
            if (-1 != chipsStatus) {
                final boolean showMarker = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//                final boolean waitForUpdate = BmtUtil.isWaitForUpdate(chipsStatus);
//                final boolean ignore = BmtManager.getInstance().isIgnoredUpgrade(mDeviceId);
//                final boolean needUpgrade = waitForUpdate ? !ignore : showMarker;
                changeViewStateByUpdateState(showMarker);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtListUpdateEvent event) {
        final String deviceId = event.getDeviceId();
        final String subcategory = event.getSubcategory();
        final int operateType = event.getOperationType();

        if (TextUtils.isEmpty(deviceId) || !deviceId.equals(mDeviceId) || !subcategory.equals(mSubcategory)) {
            return;
        }

        if (BmtListUpdateEvent.OPERATION_DELETE == operateType) {
            getDelegateActivity().removeAllCommonFragment();
        }
    }

    private void updateByDeviceOnlineStatus() {
        final boolean online = BmtUtil.isDeviceConnected(mPSDevice);
        DDLog.i(TAG, "updateByDeviceOnlineStatus: " + online);
        mBinding.tvDiagnosticNetwork.setVisibility(online ? View.GONE : View.VISIBLE);
        mBinding.ivWifi.setVisibility(online ? View.VISIBLE : View.GONE);
        mBinding.ivCellular.setVisibility(online ? View.VISIBLE : View.GONE);

        if (online) {
            setWifiSignal(mWifiSignal);
            setCellularSignal(mCellularSignal);
        } else {
            changeViewStateByUpdateState(false);
        }
    }
}
