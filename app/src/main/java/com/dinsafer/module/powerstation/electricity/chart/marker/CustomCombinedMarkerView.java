package com.dinsafer.module.powerstation.electricity.chart.marker;

import android.content.Context;
import android.widget.LinearLayout;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.TimeUtil;
import com.github.mikephil.charting.components.MarkerView;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;

import java.util.List;

public abstract class CustomCombinedMarkerView extends MarkerView {

    protected Context mContext;
    protected final LinearLayout llValue;
    protected final LinearLayout llSubValue;
    protected final LinearLayout llThirdValue;
//    protected final LinearLayout llFourthValue;
    protected final LocalTextView tvKey;
    protected final LocalTextView tvValue;
    protected final LocalTextView tvSubKey;
    protected final LocalTextView tvSubValue;
    protected final LocalTextView tvThirdKey;
    protected final LocalTextView tvThirdValue;
//    protected final LocalTextView tvFourthKey;
//    protected final LocalTextView tvFourthValue;
    protected final LocalTextView tvTime;
    protected int interval;
    protected PlusMinusType plusMinusType;
    protected boolean isMultiply;
    protected String powerUnit = "";
    protected String powerHourUnit = "";
    protected float leftYMaxVal;
    protected float rightYMaxVal;
    protected boolean isDSTTransitionDay;
    protected int timeType;
    protected long timestamp;
    protected String timezone;
    protected List<Integer> selectedPositions;

    protected int limitLeft;
    protected int limitRight;
    protected int hourCount = 1440;

    protected boolean isDualPowerOpen;

    /**
     * Constructor. Sets up the MarkerView with a custom layout resource.
     *
     * @param context
     */


    public CustomCombinedMarkerView(Context context) {
        super(context, R.layout.view_usage_day_marker);
        mContext = context;
        llValue = findViewById(R.id.ll_value);
        llSubValue = findViewById(R.id.ll_sub_value);
        llThirdValue = findViewById(R.id.ll_third_value);
//        llFourthValue = findViewById(R.id.ll_fourth_value);
        tvKey = findViewById(R.id.tv_key);
        tvValue = findViewById(R.id.tv_value);
        tvSubKey = findViewById(R.id.tv_sub_key);
        tvSubValue = findViewById(R.id.tv_sub_value);
        tvThirdKey = findViewById(R.id.tv_third_key);
        tvThirdValue = findViewById(R.id.tv_third_value);
//        tvFourthKey = findViewById(R.id.tv_fourth_key);
//        tvFourthValue = findViewById(R.id.tv_fourth_value);
        tvTime = findViewById(R.id.tv_time);
    }

    @Override
    public void refreshContent(Entry e, Highlight highlight) {

    }

    @Override
    public int getXOffset(float xpos) {
        return -getWidth() / 2;
    }

    @Override
    public int getYOffset(float ypos) {
        return -getHeight();
    }

    public void setMarkerValueShow(List<Entry> entries, List<Highlight> highlights) {
        if (highLightListener != null) {
            highLightListener.highLight(entries, highlights);
        }
        if (CollectionUtil.isListEmpty(entries)) return;
        setMarker(entries, highlights);
    }


    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }


    public PlusMinusType getPlusMinusType() {
        return plusMinusType;
    }

    public void setPlusMinusType(PlusMinusType plusMinusType) {
        this.plusMinusType = plusMinusType;
    }

    public boolean isMultiply() {
        return isMultiply;
    }

    public void setMultiply(boolean multiply) {
        isMultiply = multiply;
    }

    public List<Integer> getSelectedPositions() {
        return selectedPositions;
    }

    public void setSelectedPositions(List<Integer> selectedPositions) {
        this.selectedPositions = selectedPositions;
    }

    public String getPowerUnit() {
        return powerUnit;
    }

    public void setPowerUnit(String powerUnit) {
        this.powerUnit = powerUnit;
    }

    public String getPowerHourUnit() {
        return powerHourUnit;
    }

    public void setPowerHourUnit(String powerHourUnit) {
        this.powerHourUnit = powerHourUnit;
    }

    public float getLeftYMaxVal() {
        return leftYMaxVal;
    }

    public void setLeftYMaxVal(float leftYMaxVal) {
        this.leftYMaxVal = leftYMaxVal;
    }

    public float getRightYMaxVal() {
        return rightYMaxVal;
    }

    public void setRightYMaxVal(float rightYMaxVal) {
        this.rightYMaxVal = rightYMaxVal;
    }

    public int getTimeType() {
        return timeType;
    }

    public void setTimeType(int timeType) {
        this.timeType = timeType;
    }

    public boolean isDSTTransitionDay() {
        return isDSTTransitionDay;
    }

    public void setDSTTransitionDay(boolean DSTTransitionDay) {
        isDSTTransitionDay = DSTTransitionDay;
    }

    public boolean isDualPowerOpen() {
        return isDualPowerOpen;
    }

    public void setDualPowerOpen(boolean dualPowerOpen) {
        isDualPowerOpen = dualPowerOpen;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public int getLimitLeft() {
        return limitLeft;
    }

    public void setLimitLeft(int limitLeft) {
        this.limitLeft = limitLeft;
    }

    public int getLimitRight() {
        return limitRight;
    }

    public void setLimitRight(int limitRight) {
        this.limitRight = limitRight;
    }

    protected String getUnit(float value, boolean isHour) {
        return ChartDataUtil.getPowerUnit(value, isHour);
    }

    public void setHourCount(int hourCount) {
        this.hourCount = hourCount;
    }

    public abstract void setMarker(List<Entry> entries, List<Highlight> highlights);

    private HighLightListener highLightListener;

    public void setHighLightListener(HighLightListener highLightListener) {
        this.highLightListener = highLightListener;
    }

    public interface HighLightListener {
        void highLight(List<Entry> entries, List<Highlight> highlights);
    }
}
