package com.dinsafer.module.powerstation.impacts.aischeduledchart;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import com.dinsafer.module.powerstation.electricity.chart.listener.CustomCombinedChartTouchListener;
import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.impacts.aischeduledchart.renderer.AIScheduledCombinedRenderer;
import com.dinsafer.module.powerstation.impacts.aischeduledchart.renderer.AIScheduledXAxisRenderer;
import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.data.BarLineScatterCandleBubbleData;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarLineScatterCandleBubbleDataSet;
import com.github.mikephil.charting.utils.ViewPortHandler;

public class AIScheduledCombinedChart<T extends BarLineScatterCandleBubbleData<? extends IBarLineScatterCandleBubbleDataSet<? extends Entry>>> extends CombinedChart {

    /**
     * flag that indicates if offsets calculation has already been done or not
     */
    private boolean mOffsetsCalculated = false;

    private boolean mAutoScaleMinMaxEnabled = false;
    private Integer mAutoScaleLastLowestVisibleXIndex = null;
    private Integer mAutoScaleLastHighestVisibleXIndex = null;
    // for performance tracking
    private long totalTime = 0;
    private long drawCycles = 0;
    private boolean isDrawDefaultXGridLines;
    private boolean isDrawDefaultYGridLines;
    private Path defaultXGridLinesPath = new Path();
    private Path defaultYGridLinesPath = new Path();
    private boolean isNeedNoDataGrid;
    private float noDataOffset;
    private float noDataLeftOffset;
    private float noDataTopOffset;
    private float noDataRightOffset;
    private float noDataBottomOffset;
    private int mYNormalColor;
    private float mYNormalWidth;
    private boolean isDrawAverageLine;
    private DashPathEffect mAverageDashPathEffect;
    private int mAverageDashColor;
    private float mAverageDashWidth;
    private float average;
    private int mVerticalLineCount = 4;
    private IDrawBackground mDrawBackground;
    private IDrawOffsetContent mDrawOffsetContent;
    private IDrawHighLighted mDrawHighLighted;
    private IDrawExtremum mDrawExtremum;
    private IDrawExtra mDrawExtra;
    private boolean isEdit;
    private Typeface mPalanquinTypeface;

    public AIScheduledCombinedChart(Context context) {
        this(context, null);
    }

    public AIScheduledCombinedChart(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AIScheduledCombinedChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mAverageDashPathEffect = new DashPathEffect(new float[]{16, 10f}, 0);
        mPalanquinTypeface = ResourcesCompat.getFont(getContext(), R.font.palanquin);
        mXAxis.setTypeface(mPalanquinTypeface);
    }

    @Override
    protected void init() {
        super.init();
        mChartTouchListener = new AIScheduledCombineChartTouchListener(this, mViewPortHandler.getMatrixTouch());
        mXAxisRenderer = new AIScheduledXAxisRenderer(mViewPortHandler, mXAxis, mLeftAxisTransformer);
    }

    @Override
    public void setData(CombinedData data) {
        super.setData(data);
        mRenderer = new AIScheduledCombinedRenderer(this, mAnimator, mViewPortHandler);
        mRenderer.initBuffers();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (mData == null) {
            drawNoData(canvas);
            return;
        }

        if (!mOffsetsCalculated) {
            calculateOffsets();
            mOffsetsCalculated = true;
        }

        long starttime = System.currentTimeMillis();
        calcModulus();

        mXAxisRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);
        mRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);
        // execute all drawing commands
        if (mDrawBackground != null) {
            mDrawBackground.drawBackground(this, mViewPortHandler, canvas, mGridBackgroundPaint);
        } else {
            drawGridBackground(canvas);
        }

        if (mAxisLeft.isEnabled())
            mAxisRendererLeft.computeAxis(mAxisLeft.mAxisMinimum, mAxisLeft.mAxisMaximum);
        if (mAxisRight.isEnabled())
            mAxisRendererRight.computeAxis(mAxisRight.mAxisMinimum, mAxisRight.mAxisMaximum);

        mXAxisRenderer.renderAxisLine(canvas);
        mAxisRendererLeft.renderAxisLine(canvas);
        mAxisRendererRight.renderAxisLine(canvas);

        if (mAutoScaleMinMaxEnabled) {
            final int lowestVisibleXIndex = getLowestVisibleXIndex();
            final int highestVisibleXIndex = getHighestVisibleXIndex();

            if (mAutoScaleLastLowestVisibleXIndex == null ||
                    mAutoScaleLastLowestVisibleXIndex != lowestVisibleXIndex ||
                    mAutoScaleLastHighestVisibleXIndex == null ||
                    mAutoScaleLastHighestVisibleXIndex != highestVisibleXIndex) {

                calcMinMax();
                calculateOffsets();

                mAutoScaleLastLowestVisibleXIndex = lowestVisibleXIndex;
                mAutoScaleLastHighestVisibleXIndex = highestVisibleXIndex;
            }
        }
        // make sure the graph values and grid cannot be drawn outside the
        // content-rect
        int clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        mXAxisRenderer.renderGridLines(canvas);
        mAxisRendererLeft.renderGridLines(canvas);
        mAxisRendererRight.renderGridLines(canvas);

        if (mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        if (isDrawAverageLine) {
            drawAverageLine(canvas);
        }
        mRenderer.drawData(canvas);

        // Removes clipping rectangle
        canvas.restoreToCount(clipRestoreCount);

        if (mDrawOffsetContent != null) {
            mDrawOffsetContent.drawTopOffsetContent(this, mViewPortHandler, canvas);
            mDrawOffsetContent.drawBottomOffsetContent(this, mViewPortHandler, canvas);
        }

        mRenderer.drawExtras(canvas);

        clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        if (!mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (!mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (!mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        canvas.restoreToCount(clipRestoreCount);

        mXAxisRenderer.renderAxisLabels(canvas);
        mAxisRendererLeft.renderAxisLabels(canvas);
        mAxisRendererRight.renderAxisLabels(canvas);
        mRenderer.drawValues(canvas);

        mLegendRenderer.renderLegend(canvas);

        drawMarkers(canvas);

        drawDescription(canvas);

        if (mDrawExtra != null) {
            mDrawExtra.drawExtra(this, mViewPortHandler, canvas);
        }

        if (mDrawExtremum != null) {
            mDrawExtremum.drawExtremum(this, canvas);
        }

        // if highlighting is enabled
        if (mDrawHighLighted != null) {
            mDrawHighLighted.drawHighlighted(this, canvas, mIndicesToHighlight);
        } else {
            if (valuesToHighlight()) {
                mRenderer.drawHighlighted(canvas, mIndicesToHighlight);
            }
        }

        if (mLogEnabled) {
            long drawtime = (System.currentTimeMillis() - starttime);
            totalTime += drawtime;
            drawCycles += 1;
            long average = totalTime / drawCycles;
            Log.i(LOG_TAG, "Drawtime: " + drawtime + " ms, average: " + average + " ms, cycles: "
                    + drawCycles);
        }
    }

    private void drawNoData(Canvas canvas) {
        RectF noDataRectF = new RectF(noDataLeftOffset, noDataTopOffset,
                getWidth() - noDataRightOffset, getHeight() - noDataBottomOffset);
        canvas.drawRect(noDataRectF, mBorderPaint);
        if (isNeedNoDataGrid) {
            float average = (noDataRectF.right - noDataRectF.left) / 4;
            for (int i = 1; i < 4; i++) {
                float x = noDataRectF.left + average * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(x, noDataRectF.top);
                defaultXGridLinesPath.lineTo(x, noDataRectF.bottom);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }

            float verticalAverage = (noDataRectF.bottom - noDataRectF.top) / 5;

            for (int i = 1; i < 5; i++) {
                float y = noDataRectF.top + verticalAverage * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(noDataRectF.left, y);
                defaultXGridLinesPath.lineTo(noDataRectF.right, y);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }
        }
    }

    @Override
    protected void drawGridBackground(Canvas c) {
        super.drawGridBackground(c);
        float average = (mViewPortHandler.contentRight() - mViewPortHandler.contentLeft()) / 4;
        float averageVertical = (mViewPortHandler.contentBottom() - mViewPortHandler.contentTop()) / (mVerticalLineCount - 1);
        if (isDrawDefaultXGridLines) {
            for (int i = 1; i < 4; i++) {
                float x = mViewPortHandler.contentLeft() + average * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(x, mViewPortHandler.contentTop());
                defaultXGridLinesPath.lineTo(x, mViewPortHandler.contentBottom());
                mBorderPaint.setColor(mYNormalColor);
                mBorderPaint.setStrokeWidth(mYNormalWidth);
                c.drawPath(defaultXGridLinesPath, mBorderPaint);
            }
        }

        if (isDrawDefaultYGridLines) {

            for (int i = 1; i < mVerticalLineCount - 1; i++) {
                float y = mViewPortHandler.contentTop() + averageVertical * i;
                defaultYGridLinesPath.reset();
                defaultYGridLinesPath.moveTo(mViewPortHandler.contentLeft(), y);
                defaultYGridLinesPath.lineTo(mViewPortHandler.contentRight(), y);
                mBorderPaint.setColor(mYNormalColor);
                mBorderPaint.setStrokeWidth(mYNormalWidth);
                c.drawPath(defaultYGridLinesPath, mBorderPaint);
            }
        }
    }

    private void drawAverageLine(Canvas c) {
        float bottom = mViewPortHandler.contentBottom() - mViewPortHandler.contentHeight() * average;
        defaultYGridLinesPath.reset();
        defaultYGridLinesPath.moveTo(mViewPortHandler.contentLeft(), bottom);
        defaultYGridLinesPath.lineTo(mViewPortHandler.contentRight(), bottom);
        mBorderPaint.setColor(mAverageDashColor);
        mBorderPaint.setStrokeWidth(mAverageDashWidth);
        mBorderPaint.setPathEffect(mAverageDashPathEffect);
        c.drawPath(defaultYGridLinesPath, mBorderPaint);
        mBorderPaint.setPathEffect(null);
        mBorderPaint.setColor(mYNormalColor);
        mBorderPaint.setStrokeWidth(mYNormalWidth);
    }

    public boolean isNeedNoDataGrid() {
        return isNeedNoDataGrid;
    }

    public void setNeedNoDataGrid(boolean needNoDataGrid) {
        isNeedNoDataGrid = needNoDataGrid;
    }

    public void setDrawDefaultXGridLines(boolean isDrawXGridLines) {
        this.isDrawDefaultXGridLines = isDrawXGridLines;
    }

    public void setDrawDefaultYGridLines(boolean drawDefaultYGridLines) {
        isDrawDefaultYGridLines = drawDefaultYGridLines;
    }

    public float getNoDataOffset() {
        return noDataOffset;
    }

    public void setNoDataOffset(float noDataOffset) {
        this.noDataOffset = noDataOffset;
    }

    public float getNoDataLeftOffset() {
        return noDataLeftOffset;
    }

    public void setNoDataLeftOffset(float noDataLeftOffset) {
        this.noDataLeftOffset = noDataLeftOffset;
    }

    public float getNoDataTopOffset() {
        return noDataTopOffset;
    }

    public void setNoDataTopOffset(float noDataTopOffset) {
        this.noDataTopOffset = noDataTopOffset;
    }

    public float getNoDataRightOffset() {
        return noDataRightOffset;
    }

    public void setNoDataRightOffset(float noDataRightOffset) {
        this.noDataRightOffset = noDataRightOffset;
    }

    public float getNoDataBottomOffset() {
        return noDataBottomOffset;
    }

    public void setNoDataBottomOffset(float noDataBottomOffset) {
        this.noDataBottomOffset = noDataBottomOffset;
    }

    public void setYNormalColor(int color) {
        this.mYNormalColor = color;
    }

    public void setYNormalWidth(float normalWidth) {
        this.mYNormalWidth = normalWidth;
    }


    public boolean isDrawAverageLine() {
        return isDrawAverageLine;
    }

    public void setDrawAverageLine(boolean drawAverageLine) {
        isDrawAverageLine = drawAverageLine;
    }

    public DashPathEffect getAverageDashPathEffect() {
        return mAverageDashPathEffect;
    }

    public void setAverageDashPathEffect(DashPathEffect averageDashPathEffect) {
        this.mAverageDashPathEffect = averageDashPathEffect;
    }

    public int getAverageDashColor() {
        return mAverageDashColor;
    }

    public void setAverageDashColor(int averageDashColor) {
        this.mAverageDashColor = averageDashColor;
    }

    public float getAverageDashWidth() {
        return mAverageDashWidth;
    }

    public void setAverageDashWidth(float averageDashWidth) {
        this.mAverageDashWidth = averageDashWidth;
    }

    public float getAverage() {
        return average;
    }

    public void setAverage(float average) {
        this.average = average;
    }

    public void setEmojiView(View view) {
        if (mRenderer != null) {
            if (mRenderer instanceof AIScheduledCombinedRenderer) {
                ((AIScheduledCombinedRenderer) mRenderer).setEmojiView(view);
            }
        }
    }

    public void setHighLightPosition(int nowPosition, int clickPosition) {
        if (mRenderer != null) {
            if (mRenderer instanceof AIScheduledCombinedRenderer) {
                ((AIScheduledCombinedRenderer) mRenderer).setHighLightPosition(nowPosition, clickPosition);
            }
        }
    }

    public void setVerticalLineCount(int verticalLineCount) {
        this.mVerticalLineCount = verticalLineCount;
    }

    public void setDrawBackground(IDrawBackground drawBackground) {
        this.mDrawBackground = drawBackground;
    }

    public void setDrawOffsetContent(IDrawOffsetContent drawOffsetContent) {
        this.mDrawOffsetContent = drawOffsetContent;
    }

    public void setDrawHighLighted(IDrawHighLighted drawHighLighted) {
        this.mDrawHighLighted = drawHighLighted;
    }

    public void setEdit(boolean isEdit) {
        this.isEdit = isEdit;
        if (mChartTouchListener !=  null) {
            ((AIScheduledCombineChartTouchListener)mChartTouchListener).setEdit(isEdit);
        }
    }

    public void setDrawExtremum(IDrawExtremum drawExtremum) {
        this.mDrawExtremum = drawExtremum;
    }

    public void setDrawExtra(IDrawExtra drawExtra) {
        this.mDrawExtra = drawExtra;
    }

    public interface IDrawBackground {
        void drawBackground(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas, Paint paint);
    }

    public interface IDrawOffsetContent {
        void drawTopOffsetContent(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas);

        void drawBottomOffsetContent(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas);
    }

    public interface IDrawHighLighted {
        void drawHighlighted(AIScheduledCombinedChart chart, Canvas canvas, Highlight[] mIndicesToHighlight);
    }

    public interface IDrawExtremum {
        void drawExtremum(AIScheduledCombinedChart chart, Canvas canvas);
    }

    public interface IDrawExtra {
        void drawExtra(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas);
    }
}
