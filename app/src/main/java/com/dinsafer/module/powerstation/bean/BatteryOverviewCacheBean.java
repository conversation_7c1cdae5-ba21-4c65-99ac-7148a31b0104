package com.dinsafer.module.powerstation.bean;

import java.util.List;

public class BatteryOverviewCacheBean {

    private int cabinetCount;
    private int capacity;
    private List<BatteryCellCacheBean> batteryCells;

    public BatteryOverviewCacheBean(int cabinetCount, int capacity) {
        this.cabinetCount = cabinetCount;
        this.capacity = capacity;
    }

    public BatteryOverviewCacheBean(int capacity, List<BatteryCellCacheBean> batteryCells) {
        this.capacity = capacity;
        this.batteryCells = batteryCells;
    }

    public int getCabinetCount() {
        return cabinetCount;
    }

    public void setCabinetCount(int cabinetCount) {
        this.cabinetCount = cabinetCount;
    }

    public int getCapacity() {
        return capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }

    public List<BatteryCellCacheBean> getBatteryCells() {
        return batteryCells;
    }

    public void setBatteryCells(List<BatteryCellCacheBean> batteryCells) {
        this.batteryCells = batteryCells;
    }

    public static class BatteryCellCacheBean {
        private int cabinetIndex;
        private int cabinetPositionIndex;
        private int index;
        private int capacity;

        public BatteryCellCacheBean(int cabinetIndex, int cabinetPositionIndex, int index) {
            this.cabinetIndex = cabinetIndex;
            this.cabinetPositionIndex = cabinetPositionIndex;
            this.index = index;
        }

        public BatteryCellCacheBean(int cabinetIndex, int cabinetPositionIndex, int index, int capacity) {
            this.cabinetIndex = cabinetIndex;
            this.cabinetPositionIndex = cabinetPositionIndex;
            this.index = index;
            this.capacity = capacity;
        }

        public int getCabinetIndex() {
            return cabinetIndex;
        }

        public void setCabinetIndex(int cabinetIndex) {
            this.cabinetIndex = cabinetIndex;
        }

        public int getCabinetPositionIndex() {
            return cabinetPositionIndex;
        }

        public void setCabinetPositionIndex(int cabinetPositionIndex) {
            this.cabinetPositionIndex = cabinetPositionIndex;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public int getCapacity() {
            return capacity;
        }

        public void setCapacity(int capacity) {
            this.capacity = capacity;
        }
    }
}
