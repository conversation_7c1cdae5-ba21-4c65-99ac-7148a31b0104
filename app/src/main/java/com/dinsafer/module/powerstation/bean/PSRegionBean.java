package com.dinsafer.module.powerstation.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class PSRegionBean implements Parcelable {

    private String countryCode;
    private String countryName;
    private String countryNameDisplay;
    private String timezone;
    private boolean gridConnSupport;
    private boolean smartTariffTracking;
    private boolean isGridToBattery;
    private List<String> deliveryAreas;
    private boolean balanceContractSupport;
    private List<String> electricitySupplier;

    public PSRegionBean(String countryName, String countryCode, String timezone) {
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.timezone = timezone;
    }

    public PSRegionBean(String countryName, String countryCode, String timezone, boolean gridConnSupport, boolean smartTariffTracking) {
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.timezone = timezone;
        this.gridConnSupport = gridConnSupport;
        this.smartTariffTracking = smartTariffTracking;
    }

    public PSRegionBean(String countryName, String countryCode, String countryNameDisplay
            , String timezone, boolean gridConnSupport, boolean smartTariffTracking
            , boolean isGridToBattery, List<String> deliveryAreas) {
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.countryNameDisplay = countryNameDisplay;
        this.timezone = timezone;
        this.gridConnSupport = gridConnSupport;
        this.smartTariffTracking = smartTariffTracking;
        this.isGridToBattery = isGridToBattery;
        this.deliveryAreas = deliveryAreas;
    }

    public PSRegionBean(String countryName, String countryCode, String countryNameDisplay
            , String timezone, boolean gridConnSupport, boolean smartTariffTracking
            , boolean isGridToBattery, List<String> deliveryAreas, boolean balanceContractSupport,
                        List<String> electricitySupplier) {
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.countryNameDisplay = countryNameDisplay;
        this.timezone = timezone;
        this.gridConnSupport = gridConnSupport;
        this.smartTariffTracking = smartTariffTracking;
        this.isGridToBattery = isGridToBattery;
        this.deliveryAreas = deliveryAreas;
        this.balanceContractSupport = balanceContractSupport;
        this.electricitySupplier = electricitySupplier;
    }



    public String getCountryNameDisplay() {
        return countryNameDisplay;
    }

    public void setCountryNameDisplay(String countryNameDisplay) {
        this.countryNameDisplay = countryNameDisplay;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public List<String> getDeliveryAreas() {
        return deliveryAreas;
    }

    public void setDeliveryAreas(List<String> deliveryAreas) {
        this.deliveryAreas = deliveryAreas;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public boolean isGridConnSupport() {
        return gridConnSupport;
    }

    public void setGridConnSupport(boolean gridConnSupport) {
        this.gridConnSupport = gridConnSupport;
    }

    public boolean isSmartTariffTracking() {
        return smartTariffTracking;
    }

    public void setSmartTariffTracking(boolean smartTariffTracking) {
        this.smartTariffTracking = smartTariffTracking;
    }


    public boolean isGridToBattery() {
        return isGridToBattery;
    }

    public void setGridToBattery(boolean gridToBattery) {
        isGridToBattery = gridToBattery;
    }

    public boolean isBalanceContractSupport() {
        return balanceContractSupport;
    }

    public void setBalanceContractSupport(boolean balanceContractSupport) {
        this.balanceContractSupport = balanceContractSupport;
    }

    public List<String> getElectricitySupplier() {
        return electricitySupplier;
    }

    public void setElectricitySupplier(List<String> electricitySupplier) {
        this.electricitySupplier = electricitySupplier;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.countryCode);
        dest.writeString(this.countryName);
        dest.writeString(this.countryNameDisplay);
        dest.writeString(this.timezone);
        dest.writeByte(this.gridConnSupport ? (byte) 1 : (byte) 0);
        dest.writeByte(this.smartTariffTracking ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isGridToBattery ? (byte) 1 : (byte) 0);
        dest.writeStringList(this.deliveryAreas);
        dest.writeByte(this.balanceContractSupport ? (byte) 1 : (byte) 0);
        dest.writeStringList(this.electricitySupplier);
    }

    public void readFromParcel(Parcel source) {
        this.countryCode = source.readString();
        this.countryName = source.readString();
        this.countryNameDisplay = source.readString();
        this.timezone = source.readString();
        this.gridConnSupport = source.readByte() != 0;
        this.smartTariffTracking = source.readByte() != 0;
        this.isGridToBattery = source.readByte() != 0;
        this.deliveryAreas = source.createStringArrayList();
        this.balanceContractSupport = source.readByte() != 0;
        this.electricitySupplier = source.createStringArrayList();
    }

    protected PSRegionBean(Parcel in) {
        this.countryCode = in.readString();
        this.countryName = in.readString();
        this.countryNameDisplay = in.readString();
        this.timezone = in.readString();
        this.gridConnSupport = in.readByte() != 0;
        this.smartTariffTracking = in.readByte() != 0;
        this.isGridToBattery = in.readByte() != 0;
        this.deliveryAreas = in.createStringArrayList();
        this.balanceContractSupport = in.readByte() != 0;
        this.electricitySupplier = in.createStringArrayList();
    }

    public static final Creator<PSRegionBean> CREATOR = new Creator<PSRegionBean>() {
        @Override
        public PSRegionBean createFromParcel(Parcel source) {
            return new PSRegionBean(source);
        }

        @Override
        public PSRegionBean[] newArray(int size) {
            return new PSRegionBean[size];
        }
    };
}
