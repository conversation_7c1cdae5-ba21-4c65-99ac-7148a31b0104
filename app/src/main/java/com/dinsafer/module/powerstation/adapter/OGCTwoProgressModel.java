package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;


import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.utils.KeyBoardUtil;
import com.dinsafer.common.utils.StringUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemOnGridConfigurationTwoProgressBinding;
import com.dinsafer.model.event.EditTextFocusEvent;
import com.dinsafer.module.powerstation.event.OnGridConfigurationEditEvent;
import com.dinsafer.ui.TwoWaySeekBar;
import com.dinsafer.ui.rv.BindModel;

import org.greenrobot.eventbus.EventBus;

public class OGCTwoProgressModel extends BindModel<ItemOnGridConfigurationTwoProgressBinding> {

    private Context mContext;
    private String key;
    private String unit;
    private float leftMin;
    private float leftMax;
    private float rightMin;
    private float rightMax;
    private float progress;
    private float multiple;
    private boolean isEditable;
    private boolean isEdit;

    public OGCTwoProgressModel(Context context, String key, String unit,
                               float leftMin, float leftMax, float rightMin,
                               float rightMax, float progress, float multiple) {
        super(context);
        this.mContext = context;
        this.key = key;
        this.unit = unit;
        this.leftMin = leftMin;
        this.leftMax = leftMax;
        this.rightMin = rightMin;
        this.rightMax = rightMax;
        this.progress = progress;
        this.multiple = multiple;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_on_grid_configuration_two_progress;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemOnGridConfigurationTwoProgressBinding binding) {
        binding.tvKey.setLocalText(key);
        binding.tvValue.setLocalText(StringUtil.retainDecimals(progress * multiple, multiple) + unit);
        binding.clContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isEditable && !isEdit) {
                    isEdit = true;
                    binding.llValue.setVisibility(View.INVISIBLE);
                    binding.llEdit.setVisibility(View.VISIBLE);
                    binding.twsbProgress.setVisibility(View.VISIBLE);
                    binding.twsbProgress.resetData(leftMin * multiple, leftMax * multiple,
                            rightMin * multiple, rightMax * multiple, progress * multiple, multiple);
                    EventBus.getDefault().post(new OnGridConfigurationEditEvent(holder.getAdapterPosition()));
                }
            }
        });
        binding.etValue.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_SIGNED | InputType.TYPE_NUMBER_FLAG_DECIMAL);
        binding.ivArrow.setVisibility(isEditable && !isEdit ? View.VISIBLE : View.GONE);
        binding.llValue.setVisibility(!isEdit ? View.VISIBLE : View.INVISIBLE);
        binding.etValue.setText(StringUtil.retainDecimals(progress * multiple, multiple));
        binding.tvUnit.setLocalText(unit);
        binding.llEdit.setVisibility(isEditable && isEdit ? View.VISIBLE : View.INVISIBLE);
        binding.twsbProgress.setVisibility(isEditable && isEdit ? View.VISIBLE : View.GONE);

        binding.llEdit.setOnClickListener(view -> {
            KeyBoardUtil.openKeybord(mContext, binding.etValue);
        });
        binding.tvUnit.setOnClickListener(view -> {
            KeyBoardUtil.openKeybord(mContext, binding.etValue);
        });
        binding.twsbProgress.setSeekBarChangeListener(new TwoWaySeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(TwoWaySeekBar twoWaySeekBar, int progress, float progressFloat, boolean fromUser) {
                OGCTwoProgressModel.this.progress = progress / multiple;
                binding.tvValue.setLocalText(StringUtil.retainDecimals(progress, multiple) + unit);
                binding.etValue.setText(StringUtil.retainDecimals(progress, multiple));
            }
        });

        binding.etValue.setOnFocusChangeListener((view, b) -> {
            if (b) {
                EventBus.getDefault().post(new EditTextFocusEvent(binding.llProgress, holder.getAdapterPosition()));
            }
        });
        binding.etValue.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                String text = binding.etValue.getText().toString();
                if (TextUtils.isEmpty(text) ||
                        text.equals("-") || text.equals("+")
                        || text.equals(".") || text.equals("-.")
                        || text.equals("+.")) return;
                float val = Float.parseFloat(text);
                if ((val >= leftMax && val <= leftMin) || (val >= rightMin && val <= rightMax)) {
                    binding.twsbProgress.setProgress(val * multiple);
                    binding.etValue.setTextColor(mContext.getResources().getColor(R.color.color_white_01));
                    binding.tvUnit.setTextColor(mContext.getResources().getColor(R.color.color_white_01));
                } else {
                    binding.etValue.setTextColor(mContext.getResources().getColor(R.color.color_tip_warning));
                    binding.tvUnit.setTextColor(mContext.getResources().getColor(R.color.color_tip_warning));
                }
            }
        });
    }

    public float getLeftMin() {
        return leftMin;
    }

    public void setLeftMin(float leftMin) {
        this.leftMin = leftMin;
    }

    public float getLeftMax() {
        return leftMax;
    }

    public void setLeftMax(float leftMax) {
        this.leftMax = leftMax;
    }

    public float getRightMin() {
        return rightMin;
    }

    public void setRightMin(float rightMin) {
        this.rightMin = rightMin;
    }

    public float getRightMax() {
        return rightMax;
    }

    public void setRightMax(float rightMax) {
        this.rightMax = rightMax;
    }

    public float getProgress() {
        return progress;
    }

    public void setProgress(float progress) {
        this.progress = progress;
    }

    public boolean isEditable() {
        return isEditable;
    }

    public void setEditable(boolean editable) {
        isEditable = editable;
        if (!isEditable) {
            isEdit = false;
        }
    }

    public boolean isEdit() {
        return isEdit;
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
    }
}
