package com.dinsafer.module.powerstation.event;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * 重新加载BMT芯片的升级状态
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/3/22 16:41
 */
@Keep
public class BmtChipsStatusReloadEvent {

    @NonNull
    private final String deviceId;
    private final String subcategory;

    public BmtChipsStatusReloadEvent(@NonNull final String deviceId, final String subcategory) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
    }

    @NonNull
    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    @Override
    public String toString() {
        return "BmtChipsStatusReloadEvent{" +
                "deviceId='" + deviceId + '\'' +
                ", subcategory='" + subcategory + '\'' +
                '}';
    }
}
