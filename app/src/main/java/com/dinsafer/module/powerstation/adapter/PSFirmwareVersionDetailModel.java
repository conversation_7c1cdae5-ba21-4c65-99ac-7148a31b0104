package com.dinsafer.module.powerstation.adapter;

import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_EV;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_EV_BN;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_IOT;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_IOT_HARDWARE;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_IOT_ID;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_MCU;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_MCU_ID;
import static com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean.KEY_INDEX_COMMON_SIDECAR;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsFirmwareVersionDetailBinding;
import com.dinsafer.dinnet.databinding.LayoutPsFirmwareVersonDetailContentBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.bean.KeyValueBean;
import com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;

import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/1 17:13
 * @description :
 */
public class PSFirmwareVersionDetailModel implements BaseBindModel<ItemPsFirmwareVersionDetailBinding> {

    private PSFirmWareVersionDetailBean versionDetailBean;
    private boolean isLast;
    private Context mContext;
    private Device mPSDevice;

    public PSFirmwareVersionDetailModel(Context context, PSFirmWareVersionDetailBean versionDetailBean, Device device) {
        this.mContext = context;
        this.versionDetailBean = versionDetailBean;
        this.mPSDevice = device;
    }

    public PSFirmwareVersionDetailModel(PSFirmWareVersionDetailBean versionDetailBean, boolean isLast) {
        this.versionDetailBean = versionDetailBean;
        this.isLast = isLast;
    }

    public void setLast(boolean last) {
        isLast = last;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_firmware_version_detail;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsFirmwareVersionDetailBinding itemPsFirmwareVersionDetailBinding) {
        if (versionDetailBean != null) {
            final String title = getSectionTittle(versionDetailBean.getInverterSupplier());
            itemPsFirmwareVersionDetailBinding.tvTitle.setText(title);
            itemPsFirmwareVersionDetailBinding.tvTitle.setVisibility(StringUtil.isNotEmpty(title) ? View.VISIBLE : View.GONE);
            itemPsFirmwareVersionDetailBinding.llContent.removeAllViews();
            List<KeyValueBean> items = versionDetailBean.getItems();
            if (items != null && items.size() > 0) {
                createItemView(itemPsFirmwareVersionDetailBinding, itemPsFirmwareVersionDetailBinding.llContent, items, versionDetailBean.getType());
            }
        }
    }

    private void createItemView(ItemPsFirmwareVersionDetailBinding itemPsFirmwareVersionDetailBinding, LinearLayout llItem, List<KeyValueBean> data, final String type) {
        for (int i = 0; i < data.size(); i++) {
            if (PSFirmWareVersionDetailBean.KEY_VERSION_COMMON.equals(type) && checkItemViewNotShow(i)) {
                continue;
            }
            KeyValueBean keyValueBean = data.get(i);
            View view = View.inflate(itemPsFirmwareVersionDetailBinding.getRoot().getContext(), R.layout.layout_ps_firmware_verson_detail_content, null);
            LinearLayout.LayoutParams llParam = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            view.setLayoutParams(llParam);
            LayoutPsFirmwareVersonDetailContentBinding binding = DataBindingUtil.bind(view);
            binding.cclParent.setCopyIndex("0,1");
            binding.tvKey.setText(getItemKeyText(type, i, keyValueBean));
            binding.tvValue.setLocalText(keyValueBean.getValue());
            if (isLast && i == data.size() - 1) {
                binding.viewLine.setVisibility(View.GONE);
            }
            llItem.addView(view);
        }
    }

    private boolean checkItemViewNotShow(int index) {
        if (null == mPSDevice) {
            return false;
        }
        boolean notShow = false;
        if (BmtUtil.isBmtDevicePowerStore(mPSDevice)
                || BmtUtil.isBmtDevicePowerPulse(mPSDevice)) {
            switch (index) {
                case KEY_INDEX_COMMON_MCU_ID:
                case KEY_INDEX_COMMON_MCU:
                case KEY_INDEX_COMMON_EV:
                case KEY_INDEX_COMMON_EV_BN:
                    notShow = true;
                    break;
            }
        } else if ((BmtUtil.isBmtDeviceV2(mPSDevice)
                && (index == KEY_INDEX_COMMON_MCU_ID
                || index == KEY_INDEX_COMMON_MCU))) {
            notShow = true;
        } else if (index == KEY_INDEX_COMMON_SIDECAR
                && !BmtUtil.isBmtDeviceV2(mPSDevice)) {
            notShow = true;
        }

        return notShow;
    }

    private String getItemKeyText(final String type, final int index, final KeyValueBean data) {
        String itemKey = "";
        if (null != data) {
            final String displayKey = data.getDisplayKey();
            final String key = data.getKey();
            if (!TextUtils.isEmpty(displayKey)) {
                itemKey = displayKey;
            } else {
                itemKey = null == key ? "" : key;
                if (!TextUtils.isEmpty(type)) {
                    switch (type) {
                        case PSFirmWareVersionDetailBean.KEY_VERSION_COMMON:
                            if (KEY_INDEX_COMMON_IOT_ID == index) {
                                itemKey = Local.s(mContext.getString(R.string.iot)) + " " + Local.s(mContext.getString(R.string.id));
                            } else if (KEY_INDEX_COMMON_IOT == index) {
                                itemKey = Local.s(mContext.getString(R.string.iot)) + " " + Local.s(mContext.getString(R.string.version));
                            } else if (KEY_INDEX_COMMON_IOT_HARDWARE == index) {
                                itemKey = Local.s(mContext.getString(R.string.iot)) + " " + Local.s(mContext.getString(R.string.hardware)) + " " + Local.s(mContext.getString(R.string.version));
                            } else if (KEY_INDEX_COMMON_MCU_ID == index) {
                                itemKey = Local.s(mContext.getString(R.string.mcu)) + " " + Local.s(mContext.getString(R.string.id));
                            } else if (KEY_INDEX_COMMON_MCU == index) {
                                itemKey = Local.s(mContext.getString(R.string.mcu)) + " " + Local.s(mContext.getString(R.string.version));
                            } else if (KEY_INDEX_COMMON_EV == index) {
                                itemKey = Local.s(mContext.getString(R.string.ev)) + " " + Local.s(mContext.getString(R.string.version));
                            } else if (KEY_INDEX_COMMON_EV_BN == index) {
                                itemKey = Local.s(mContext.getString(R.string.ev)) + " " + Local.s("BN");
                            } else if (KEY_INDEX_COMMON_SIDECAR == index) {
                                itemKey = Local.s(mContext.getString(R.string.sidecar));
                            }
                            break;

                        case PSFirmWareVersionDetailBean.KEY_VERSION_CABINET:
                            itemKey = Local.s(mContext.getString(R.string.cabinet)) + " " + getIndexValue(type, index);
                            break;

                        case PSFirmWareVersionDetailBean.KEY_VERSION_INVERTER:
                            itemKey = Local.s(mContext.getString(R.string.inverter)) + " " + getIndexValue(type, index);
                            break;

                        case PSFirmWareVersionDetailBean.KEY_VERSION_BATTERY:
                            itemKey = getIndexValue(type, index);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return itemKey;
    }

    @NonNull
    private String getIndexValue(final String type, final int index) {
        String indexStr = String.valueOf(index + 1);
//        if (!TextUtils.isEmpty(type)) {
//            final int indexOffset = index + 1;
//            indexStr = indexOffset < 10 ? "0" + indexOffset : String.valueOf(indexOffset);
//            if (PSFirmWareVersionDetailBean.KEY_VERSION_INVERTER.equals(type)) {
//                final int max = 'Z' - 'A';
//                if (0 <= index && index <= max) {
//                    final int aVal = 'A';
//                    final int destVar = aVal + index;
//                    final char destChar = (char) destVar;
//                    indexStr = String.valueOf(destChar);
//                }
//                indexStr = String.valueOf(index + 1);
//            } else if (PSFirmWareVersionDetailBean.KEY_VERSION_COMMON.equals(type)) {
//                indexStr = "";
//            }
//        }
        return indexStr;
    }

    private String getSectionTittle(String supplier) {
        String sectionTittle = "";
        if (null != versionDetailBean) {
            final String type = versionDetailBean.getType();
            if (!TextUtils.isEmpty(type)) {
                switch (type) {
                    case PSFirmWareVersionDetailBean.KEY_VERSION_INVERTER:
                        sectionTittle = BmtUtil.isBmtDeviceV2(mPSDevice) ? Local.s(mContext.getString(R.string.inverter_hardware_hs)) : Local.s(mContext.getString(R.string.inverter));
                        if (!TextUtils.isEmpty(supplier)) {
                            sectionTittle = supplier.equals(PSFirmWareVersionDetailBean.SUPPLIER_BST) ?
                                    Local.s(mContext.getString(R.string.inverter)) + Local.s(mContext.getString(R.string.inverter_hardware_bst)) :
                                    Local.s(mContext.getString(R.string.inverter_hardware_hs));
                        }
                        break;
                    case PSFirmWareVersionDetailBean.KEY_VERSION_CABINET:
                        sectionTittle = BmtUtil.isBmtDeviceV2(mPSDevice) ? (Local.s(mContext.getString(R.string.cabinet)) + " " + Local.s(mContext.getString(R.string.cabinet_hardware))) : Local.s(mContext.getString(R.string.cabinet));
                        break;
                    case PSFirmWareVersionDetailBean.KEY_VERSION_BATTERY:
                        sectionTittle = BmtUtil.isBmtDeviceV2(mPSDevice) ? (Local.s(mContext.getString(R.string.bms)) + " " + Local.s(mContext.getString(R.string.bms_hardware))) : Local.s(mContext.getString(R.string.battery_packs)) + " (pack-106)";
                        break;
                    default:
                        break;
                }
            }

        }
        return sectionTittle;
    }
}
