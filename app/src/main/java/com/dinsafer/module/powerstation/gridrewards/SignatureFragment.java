package com.dinsafer.module.powerstation.gridrewards;

import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentSignatureBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.event.SignatureEvent;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.PermissionUtil;
import com.github.gcacace.signaturepad.views.SignaturePad;

import org.greenrobot.eventbus.EventBus;

/**
 * @describe：
 * @date：2024/10/24
 * @author: create by Sydnee
 */
public class SignatureFragment extends MyBaseFragment<FragmentSignatureBinding> {


    public static SignatureFragment newInstance() {
        return new SignatureFragment();
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
        activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_signature;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.clearButton.setLocalText(getString(R.string.Reset));
        mBinding.saveButton.setLocalText(getString(R.string.save));
        mBinding.title.setLocalText(getString(R.string.signature_area));
        mBinding.saveButton.setAlpha(0.5f);
        mBinding.clearButton.setAlpha(0.5f);
        mBinding.back.setOnClickListener(v -> {
            removeSelf();
            recoveryUI();
        });


        mBinding.signaturePad.setOnSignedListener(new SignaturePad.OnSignedListener() {
            @Override
            public void onStartSigning() {
                mBinding.ivTip.setVisibility(View.GONE);
            }

            @Override
            public void onSigned() {
                mBinding.clearButton.setEnabled(true);
                mBinding.saveButton.setEnabled(true);
                mBinding.clearButton.setAlpha(1f);
                mBinding.saveButton.setAlpha(1f);
            }

            @Override
            public void onClear() {
                mBinding.clearButton.setEnabled(false);
                mBinding.saveButton.setEnabled(false);
                mBinding.saveButton.setAlpha(0.5f);
                mBinding.clearButton.setAlpha(0.5f);
            }
        });

        mBinding.clearButton.setOnClickListener(v -> mBinding.signaturePad.clear());

        mBinding.saveButton.setOnClickListener(v -> {
            DDLog.i(TAG, "save");
            if (PermissionUtil.isStoragePermissionDeny(getContext())) {
                requestReadImagePermission();
                return;
            }
            Bitmap signBitmap = mBinding.signaturePad.getTransparentSignatureBitmap();
            EventBus.getDefault().post(new SignatureEvent(signBitmap));

            removeSelf();
            recoveryUI();

        });
    }

    @Override
    public boolean onBackPressed() {
        recoveryUI();
        return super.onBackPressed();
    }


    @Override
    public void onResume() {
        if (activity.getRequestedOrientation() != ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
            // 当前状态为竖屏
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }
        super.onResume();
    }

    private void recoveryUI() {
        activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
        activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
    }

    @Override
    public void initData() {
        super.initData();
    }


}
