package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsTodayPriceBinding;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.utils.PriceTodayChartHelper;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

public class PSTodayPriceItemModel extends BasePowerStationItemModel<ItemPsTodayPriceBinding> {

    private List<List<Float>> data = new ArrayList<>();
    private int interval;
    private long startTime;
    private String timezone = "";
    private String unit;
    private String showPriceUnit;
    private boolean isSuccess;

    private PriceTodayChartHelper mPriceTodayChartHelper;
    private ItemPsTodayPriceBinding mBinding;

    public PSTodayPriceItemModel(Context context, String deviceId, String subcategory) {
        super(context, deviceId, subcategory);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_today_price;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsTodayPriceBinding binding) {
        mPriceTodayChartHelper = new PriceTodayChartHelper(mContext, binding.ccPrice);
        mBinding = binding;
        refresh();
    }

    public void refresh() {
        if (mBinding != null && mPriceTodayChartHelper != null) {
            float peakPrice = mPriceTodayChartHelper.getYMax(data);
            String peakPriceUnit = mPriceTodayChartHelper.getUnit(peakPrice, showPriceUnit, unit);
            if (peakPrice < 1.0f) {
                peakPrice = mPriceTodayChartHelper.getBasicUnitVal(peakPrice, unit);
            }
            String peakPriceStr = isSuccess ? ChartDataUtil.savePointStr(peakPrice, 2, 2) : "-";
            mBinding.tvPriceValue.setText(peakPriceStr);
            boolean isAhead = mPriceTodayChartHelper.isAheadUnit(unit);
            mBinding.tvAheadPriceUnit.setVisibility(isAhead ? View.VISIBLE : View.GONE);
            mBinding.tvPriceUnit.setVisibility(isAhead ? View.GONE : View.VISIBLE);
            mBinding.tvAheadPriceUnit.setText(peakPriceUnit);
            mBinding.tvPriceUnit.setText(peakPriceUnit);
            float average = mPriceTodayChartHelper.getAverage(data);
            String aveUnit = mPriceTodayChartHelper.getUnit(average, showPriceUnit, unit);
            if (average < 1.0f) {
                average = mPriceTodayChartHelper.getBasicUnitVal(average, unit);
            }
            String averageStr = ChartDataUtil.savePointStr(average, 2, 2);
            String finalAverage = Local.s(mContext.getString(R.string.average)) + " "
                    + (isSuccess ? (mPriceTodayChartHelper.isAheadUnit(aveUnit) ?
                    (aveUnit + " " + averageStr)
                    : (averageStr + " " + aveUnit)) : "-");
            mBinding.tvAverage.setText(finalAverage);
            mPriceTodayChartHelper.setPriceData(data, startTime, timezone);
        }
    }

    public void setData(List<List<Float>> data) {
        this.data = data;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setShowPriceUnit(String showPriceUnit) {
        this.showPriceUnit = showPriceUnit;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }
}
