package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsCountryBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.CountryComparator;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PsCountryAdapter;
import com.dinsafer.module.powerstation.event.PSCountryEvent;
import com.dinsafer.module.powerstation.utils.comparator.PSCountrySearchComparator;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.ui.IndexView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

public class PSCountryFragment extends MyBaseFragment<FragmentPsCountryBinding> {

    private List<CountryBean> mCountries;
    private PsCountryAdapter mPsCountryAdapter;
    private int lastScrollIndexPos = 0;
    private ArrayList<String> indexs;
    private LinkedHashMap<String, Integer> indexPosMap;
    private PSCountrySearchComparator mSearchComparator;

    public static PSCountryFragment newInstance(List<CountryBean> regionBeans) {
        PSCountryFragment fragment = new PSCountryFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PSKeyConstant.KEY_COUNTRIES, (Serializable) regionBeans);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_country;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.country));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.etSearch.setHint(Local.s(getString(R.string.ps_region_enter_your_country)));
        mBinding.ivClear.setOnClickListener(v -> {
            mBinding.etSearch.setText("");
            mPsCountryAdapter.setNewData(mCountries);
        });
        initRv();
        setEditChangeListener();
    }

    @Override
    public void initData() {
        super.initData();
        indexs = new ArrayList<>();
        indexPosMap = new LinkedHashMap<>();
        Bundle bundle = getArguments();
        if (bundle != null) {
            List<CountryBean> countryList = (List<CountryBean>) bundle.getSerializable(PSKeyConstant.KEY_COUNTRIES);
            mCountries = new ArrayList<>();
            for (CountryBean countryBean : countryList) {
                if (countryBean.isBalance_contract_support()) {
                    mCountries.add(countryBean);
                }
            }
        }

        if (mCountries != null) {
            Collections.sort(mCountries, new CountryComparator());
            for (int i = 0; i < mCountries.size(); i++) {
                String city = mCountries.get(i).getCountry_name();
                String index = String.valueOf(city.charAt(0));
                if (!indexs.contains(index)) {
                    indexs.add(index);
                }
                if (!indexPosMap.containsKey(index)) {
                    indexPosMap.put(index, i);
                }
            }
            mPsCountryAdapter.setNewData(mCountries);
            mBinding.indexView.setData(indexs);
        }
    }

    private void initRv() {
        mBinding.rvCountry.setLayoutManager(new LinearLayoutManager(getContext()));
        mPsCountryAdapter = new PsCountryAdapter();
        mBinding.rvCountry.setAdapter(mPsCountryAdapter);
        mPsCountryAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                DDLog.d(TAG, "onItemClick: position: " + position);
                EventBus.getDefault().post(new PSCountryEvent(mPsCountryAdapter.getItem(position)));
                removeSelf();
            }
        });

        mBinding.indexView.setTextSize(DensityUtil.sp2px(getContext(), 10));
        mBinding.indexView.setOnTouchIndexViewCallback(new IndexView.OnTouchIndexViewCallback() {
            @Override
            public void onTouchIndex(int pos, String text) {
                if (pos == lastScrollIndexPos) {
                    Log.w(TAG, "onTouchIndex: ");
                    return;
                }
                ((LinearLayoutManager) mBinding.rvCountry.getLayoutManager()).scrollToPositionWithOffset(indexPosMap.get(text), 0);
                lastScrollIndexPos = pos;
            }

            @Override
            public void onCancelTouchIndex() {

            }
        });
    }

    private void setEditChangeListener() {
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String text = mBinding.etSearch.getText().toString();
                setSearchStatus(!TextUtils.isEmpty(text));
                if (text == null || text.length() == 0) {
                    setEmpty(false);
                    mPsCountryAdapter.setNewData(mCountries);
                } else {
                    ArrayList<CountryBean> newData = new ArrayList<>();
                    for (CountryBean item : mCountries) {
                        String cityStr = (item.getCountry_name_display() == null ? "" : item.getCountry_name_display().toLowerCase());
                        if (cityStr.contains(text.toLowerCase())) {
                            newData.add(item);
                        }
                    }
                    if (mSearchComparator == null) {
                        mSearchComparator = new PSCountrySearchComparator(text);
                    } else {
                        mSearchComparator.setSearchKeyword(text);
                    }
                    Collections.sort(newData, mSearchComparator);
                    mPsCountryAdapter.setNewData(newData);
                    setEmpty(newData.size() == 0);
                }
            }
        });
    }

    private void setEmpty(boolean empty) {
        mBinding.llEmpty.setVisibility(empty ? View.VISIBLE : View.GONE);
        mBinding.rvCountry.setVisibility(empty ? View.GONE : View.VISIBLE);
    }

    private void setSearchStatus(boolean isSearched) {
        mBinding.ivClear.setVisibility(isSearched ? View.VISIBLE : View.GONE);
        mBinding.tvCountrySupport.setVisibility(isSearched ? View.GONE : View.VISIBLE);
        mBinding.indexView.setVisibility(isSearched ? View.GONE : View.VISIBLE);
    }
}
