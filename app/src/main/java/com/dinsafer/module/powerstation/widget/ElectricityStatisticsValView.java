package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutElectricityStatisticsBinding;


public class ElectricityStatisticsValView extends LinearLayout {

    private LayoutElectricityStatisticsBinding mBinding;
    public ElectricityStatisticsValView(Context context) {
        this(context, null);
    }

    public ElectricityStatisticsValView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ElectricityStatisticsValView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    protected void init(Context context) {
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_electricity_statistics, this, true);
    }

    public void setLeftVal(String val, String unit) {
        mBinding.tvLeftVal.setLocalText(val);
        mBinding.tvLeftUnit.setLocalText(unit);
    }

    public void setMiddleVisible(boolean visible) {
        mBinding.clMiddle.setVisibility(visible ? VISIBLE : INVISIBLE);
    }

    public void setMiddleVal(String key, String val, String unit) {
        mBinding.tvMiddle.setLocalText(key);
        mBinding.tvMiddleVal.setLocalText(val);
        mBinding.tvMiddleUnit.setLocalText(unit);
    }

    public void setRightVal(String key, String val, String unit) {
        mBinding.tvRight.setLocalText(key);
        mBinding.tvRightVal.setLocalText(val);
        mBinding.tvRightUnit.setLocalText(unit);
    }

    public void setRightVisible(boolean visible) {
        mBinding.clRight.setVisibility(visible ? VISIBLE : INVISIBLE);
    }
}
