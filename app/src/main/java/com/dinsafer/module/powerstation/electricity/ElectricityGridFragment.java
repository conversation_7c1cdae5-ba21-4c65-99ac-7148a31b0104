package com.dinsafer.module.powerstation.electricity;

import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentElectricityGridBinding;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.ChartNoteTextAttrBean;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypeDialog;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.bean.ThresholdBarEntry;
import com.dinsafer.module.powerstation.electricity.chart.CustomCombinedChart;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.marker.GridMarkerView;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.GridChartModelController;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.module.powerstation.event.StatInfoEvent;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.zhy.view.flowlayout.FlowLayout;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class ElectricityGridFragment extends BaseChartFragment<GridChartModelController,
        FragmentElectricityGridBinding> {

    private ElectricityCircleTypeDialog mElectricityCircleTypeDialog;
    private CustomCombinedMarkerView mMarkerView;

    public static ElectricityGridFragment newInstance(int fromIndex, String deviceId) {
        ElectricityGridFragment fragment = new ElectricityGridFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.INDEX, fromIndex);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_electricity_grid;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mType = BaseChartFragment.CHART_ELECTRICITY_GRID;
        mPlusMinusType = PlusMinusType.ALL;
        mBinding.tvGridType.setText(Local.s(getString(R.string.power_station_grid)) + "-" + Local.s(getString(R.string.electricity_net_stats)));
        initRefreshLayout(mBinding.refreshLayout);
        mMarkerView = new GridMarkerView(getContext());
        mBinding.tvGridType.setOnClickListener(v -> {
            showElectricityCircleTypeDialog();
        });

        mBinding.tvUnit.setOnClickListener(v -> {
            showElectricityCircleTypeDialog();
        });
//        getStatisticData();
    }

    @Override
    protected void initChartView() {
        super.initChartView();
        initChart(mFlipCombinedChartView, mMarkerView);
        List<ChartNoteTextAttrBean> noteTexts = getNoteTexts(Local.s(getString(R.string.electricity_imported)),
                Local.s(getString(R.string.electricity_exported)));
        mFlipCombinedChartView.getCustomCombinedChart1().setNoteTexts(noteTexts);
        mFlipCombinedChartView.getCustomCombinedChart2().setNoteTexts(noteTexts);
        setIvRightEnabled(mOffSet != 0);
        initElectricityType();
        mFlipCombinedChartView.setFlipChangeListener(new FlipCombinedChartView.OnFlipChangeListener() {
            @Override
            public void flipChange(int index, OperateOrientation orientation) {
                mIndex = index;
                if (orientation == OperateOrientation.LEFT) {
                    mOffSet = mOffSet - 1;
                } else if (orientation == OperateOrientation.RIGHT) {
                    mOffSet = mOffSet + 1;
                }
//                EventBus.getDefault().post(new ChartPageChangeEvent(orientation));
                setIvRightEnabled(mOffSet != 0);
                getStatisticData(true);
            }
        });
        showNote(mPlusMinusType == PlusMinusType.ALL);
        mFlipCombinedChartView.setTvNoteVisible(false);
        mFlipCombinedChartView.setRvFilterVisible(false);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            if (mCycleType != CycleType.LIFETIME) {
                if (isChartViewNotNull()) {
                    setIvRightEnabled(mOffSet != 0);
                }
            }
            getStatisticData(true);
        }
    }

    private void initElectricityType() {
        if (isChartViewNotNull()) {
            mData = new ArrayList<>();
            mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_smart_imported_sel, getString(R.string.electricity_smart_imported), true));
            mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_emergency_imported_sel, getString(R.string.electricity_emergency_imported), true));
            mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_peak_shaving_triggered_sel, getString(R.string.electricity_peak_shaving_triggered), true));
            mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_peak_power_exceeds_sel, getString(R.string.electricity_peak_power_exceeds), true));
//            mFlipCombinedChartView.setFitler1Config(FlowLayout.CENTER, FlowLayout.CENTER, 2);
            mFlipCombinedChartView.setElectricityTypeData(mData);
        }
    }

    private void updateDataByCycleType() {
        if (!isChartViewNotNull()) return;

        if (mPlusMinusType == PlusMinusType.ALL) {

        } else {
            mData = new ArrayList<>();
            if (mCycleType == CycleType.DAY) {
                mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_smart_imported_sel, getString(R.string.electricity_smart_imported), true));
                mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_emergency_imported_sel, getString(R.string.electricity_emergency_imported), true));
                mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_peak_shaving_triggered_sel, getString(R.string.electricity_peak_shaving_triggered), true));
                mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_peak_power_exceeds_sel, getString(R.string.electricity_peak_power_exceeds), true));
            } else {
                mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_smart_imported_sel, getString(R.string.electricity_smart_imported), true));
                mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_emergency_imported_sel, getString(R.string.electricity_emergency_imported), true));
                mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_peak_shaving_triggered_sel, getString(R.string.electricity_peak_shaving_triggered), true));
            }
            mFlipCombinedChartView.setElectricityTypeData(mData);
            mFlipCombinedChartView.postInvalidate();
        }
    }
    private void showElectricityCircleTypeDialog() {
        if (mElectricityCircleTypeDialog == null) {
            mElectricityCircleTypeDialog = new ElectricityCircleTypeDialog(ElectricityCircleTypeDialog.GRID_TYPE);
            mElectricityCircleTypeDialog.setPlusMinusTypeListener(new ElectricityCircleTypeDialog.OnPlusMinusTypeListener() {
                @Override
                public void plusMinus(String type, PlusMinusType plusMinusType) {
                    mFlipCombinedChartView.resetHighValues();
                    mPlusMinusType = plusMinusType;
                    mBinding.tvGridType.setText(Local.s(getString(R.string.power_station_grid)) + "-" + Local.s(type));
                    showNote(mPlusMinusType == PlusMinusType.ALL);
                    isMultiply = StringUtil.isNotEmpty(type) && type.equals(getString(R.string.electricity_imported));
                    mFlipCombinedChartView.setRvFilterVisible(type.equals(getString(R.string.electricity_imported)));
                    float maxVal = getYMax(CHART_ELECTRICITY_GRID, mChartData);
                    resetLeftAboveText(CHART_ELECTRICITY_GRID, maxVal);
                    resetYAxisLeft(maxVal, CHART_ELECTRICITY_GRID);
                    if (isSuccess) {
                        resetChart();
                    } else {
                        resetFailChart();
                    }
                }
            });
        }
        if (mElectricityCircleTypeDialog != null && !mElectricityCircleTypeDialog.isAdded()) {
            mElectricityCircleTypeDialog.show(getChildFragmentManager(), ElectricityCircleTypeDialog.CHARGE_TAG);
        }
    }

    @Override
    public void createChartModelController() {
        chartModelController = new GridChartModelController();
    }

    @Override
    protected void resetChart() {
        float leftYMaxVal = getYMax(CHART_ELECTRICITY_GRID, mChartData);
        String powerUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, false);
        String powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        mMarkerView.setPowerUnit(powerUnit);
        mMarkerView.setPowerHourUnit(powerHourUnit);
        mMarkerView.setLeftYMaxVal(leftYMaxVal);
        mMarkerView.setDSTTransitionDay(mHourCount == DST_TIME_MINUTE);
        if (mHourCount == DST_TIME_MINUTE) {
            mMarkerView.setTimeType(1);
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            mMarkerView.setTimeType(-1);
        } else {
            mMarkerView.setTimeType(0);
        }
        mMarkerView.setTimestamp(mStartTime);
        mMarkerView.setTimezone(timezone);
        if (mPlusMinusType == PlusMinusType.ALL) {

//            String smartImported = ChartDataUtil.getPowerTransferVal(getSmartImportedVal(false), getSmartImportedVal(false), false);
//            String emergencyImported = ChartDataUtil.getPowerTransferVal(getEmergencyImportedVal(false), getEmergencyImportedVal(false), false);
//            String smartUnit = ChartDataUtil.getPowerUnit(getSmartImportedVal(false), true);
//            String emergencyUnit = ChartDataUtil.getPowerUnit(getEmergencyImportedVal(false), true);

//            float totalImported = 0f;
//            float smartImportedFl = ChartDataUtil.getBigDecimal(smartImported).floatValue();
//            float emergencyImportedFl = ChartDataUtil.getBigDecimal(emergencyImported).floatValue();
//            if (smartUnit.equals("kWh")) {
//                smartImportedFl = smartImportedFl * 1000f;
//            } else if (smartUnit.equals("MWh")) {
//                smartImportedFl = smartImportedFl * 1000000f;
//            }
//
//            if (emergencyUnit.equals("kWh")) {
//                emergencyImportedFl = emergencyImportedFl * 1000f;
//            } else if (emergencyUnit.equals("MWh")) {
//                emergencyImportedFl = emergencyImportedFl * 1000000f;
//            }
            float totalImported = getSmartImportedVal(false) + getEmergencyImportedVal(false);

            String importedVal = ChartDataUtil.getPowerTransferVal(totalImported, totalImported, false);
            String exportedVal = ChartDataUtil.getPowerTransferVal(getExportedVal(), getExportedVal(), false);
            String importedUnit = ChartDataUtil.getPowerUnit(getImportedVal(false), true);
            String exportedUnit = ChartDataUtil.getPowerUnit(getExportedVal(), true);
            float netStatisticsVal = 0f;
            float importedFl = ChartDataUtil.getBigDecimal(importedVal).floatValue();
            float exportedFl = ChartDataUtil.getBigDecimal(exportedVal).floatValue();
            if (importedUnit.equals("kWh")) {
                importedFl = importedFl * 1000f;
            } else if (importedUnit.equals("MWh")) {
                importedFl = importedFl * 1000000f;
            }

            if (exportedUnit.equals("kWh")) {
                exportedFl = exportedFl * 1000f;
            } else if (exportedUnit.equals("MWh")) {
                exportedFl = exportedFl * 1000000f;
            }
            netStatisticsVal = importedFl - exportedFl;
            String leftUnit = ChartDataUtil.getPowerUnit(netStatisticsVal, true);
            mBinding.esvVal.setLeftVal(ChartDataUtil.getPowerTransferVal(netStatisticsVal, netStatisticsVal, false), leftUnit);
            mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_imported)), importedVal, importedUnit);
            mBinding.esvVal.setRightVal(Local.s(getString(R.string.electricity_exported)), exportedVal, exportedUnit);
        } else {
            String leftUnit;
            if (isMultiply) {
//                String smartImported = ChartDataUtil.getPowerTransferVal(getSmartImportedVal(true), getSmartImportedVal(true), false);
                String emergencyImported = ChartDataUtil.getPowerTransferVal(getEmergencyImportedVal(true), getEmergencyImportedVal(true), false);
//                String smartUnit = ChartDataUtil.getPowerUnit(getSmartImportedVal(true), true);
                String emergencyUnit = ChartDataUtil.getPowerUnit(getEmergencyImportedVal(true), true);

//                float totalImported = 0f;
//                float smartImportedFl = ChartDataUtil.getBigDecimal(smartImported).floatValue();
//                float emergencyImportedFl = ChartDataUtil.getBigDecimal(emergencyImported).floatValue();
//                if (smartUnit.equals("kWh")) {
//                    smartImportedFl = smartImportedFl * 1000f;
//                } else if (smartUnit.equals("MWh")) {
//                    smartImportedFl = smartImportedFl * 1000000f;
//                }
//
//                if (emergencyUnit.equals("kWh")) {
//                    emergencyImportedFl = emergencyImportedFl * 1000f;
//                } else if (emergencyUnit.equals("MWh")) {
//                    emergencyImportedFl = emergencyImportedFl * 1000000f;
//                }
                float totalImported = getSmartImportedVal(false) + getEmergencyImportedVal(false);
                String totalEnergyUnit = ChartDataUtil.getPowerUnit(totalImported, true);
                mBinding.esvVal.setLeftVal(ChartDataUtil.getPowerTransferVal(totalImported, totalImported, false), totalEnergyUnit);
                mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_emergency_imported)),
                        emergencyImported, emergencyUnit);
            } else {
                leftUnit = ChartDataUtil.getPowerUnit(getExportedVal(), true);
                mBinding.esvVal.setLeftVal(ChartDataUtil.getPowerTransferVal(getExportedVal(), getExportedVal(), false), leftUnit);
                mBinding.esvVal.setMiddleVal("", "", "");
            }
            mBinding.esvVal.setRightVal("", "", "");
//            mBinding.tvUnit.setText("(" + leftUnit + ")");
        }
        mBinding.esvVal.refreshText();
//        mBinding.esvVal.setMiddleVisible(mPlusMinusType == PlusMinusType.ALL || isMultiply);
//        mBinding.esvVal.setRightVisible(mPlusMinusType == PlusMinusType.ALL);
        super.resetChart();
    }

    @Override
    protected void resetFailChart() {
        float leftYMaxVal = 0;
        String powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        if (mPlusMinusType == PlusMinusType.ALL) {
            mBinding.esvVal.setLeftVal(mFailVal, powerHourUnit);
            mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_imported)), mFailVal, powerHourUnit);
            mBinding.esvVal.setRightVal(Local.s(getString(R.string.electricity_exported)), mFailVal, powerHourUnit);
        } else {
            String leftUnit;
            if (isMultiply) {
                leftUnit = ChartDataUtil.getPowerUnit(getImportedVal(true), true);
                mBinding.esvVal.setLeftVal(mFailVal, leftUnit);
                mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.electricity_emergency_imported)), mFailVal, powerHourUnit);
            } else {
                leftUnit = ChartDataUtil.getPowerUnit(getExportedVal(), true);
                mBinding.esvVal.setLeftVal(mFailVal, leftUnit);
                mBinding.esvVal.setMiddleVal("", "", "");
            }
            mBinding.esvVal.setRightVal("", "", "");
        }
        mBinding.esvVal.refreshText();
        super.resetFailChart();
    }

    @Override
    protected void getStatisticData(boolean showLoading) {
        if (ElectricityStatisticsFragment.mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.GET_STATS_GRID);
            params.put(BmtDataKey.INTERVAL, ElectricityStatisticsFragment.mInterval);
            params.put(BmtDataKey.GET_REAL, true);
            params.put(BmtDataKey.QUERY_INTERVAL, ChartDataUtil.DAY.equals(ElectricityStatisticsFragment.mInterval) ? 5 : 1);
            params.put(BmtDataKey.OFFSET, mOffSet);
            if (showLoading) {
                showTimeOutLoadinFramgmentWithMarginTop(marginTop);
            }
            Map<String, Object> result = null;
            switch (mCycleType) {
                case DAY:
                    result = mDayCache.get(mOffSet);
                    break;

                case WEEK:
                    result = mWeekCache.get(mOffSet);
                    break;

                case MONTH:
                    result = mMonthCache.get(mOffSet);
                    break;

                case YEAR:
                    result = mYearCache.get(mOffSet);
                    break;
            }
            updateDataByCycleType();
            if (result == null) {
                ElectricityStatisticsFragment.mPSDevice.submit(params);
            } else {
                EventBus.getDefault().post(new StatInfoEvent(result));
                setChartData(BaseChartFragment.CHART_ELECTRICITY_GRID, result);
                closeLoadingFragment();
            }
        }
    }

    @Override
    protected void setDayChart() {
        mMarkerView.setInterval(mInterval);
        mMarkerView.setPlusMinusType(mPlusMinusType);
        mMarkerView.setMultiply(isMultiply);
        mMarkerView.setSelectedPositions(mFlipCombinedChartView.getSelectPositions());
        int count = mHourCount / mInterval + 1;
        ArrayList<Entry> entries = new ArrayList<Entry>();

        ArrayList<Entry> entries2 = new ArrayList<Entry>();
        Random random = new Random();
//        if (isBSensorInstalled || isMultiply) {
        for (int i = 0; i < mChartData.size(); i++) {
            List<Float> sonData = mChartData.get(i);
            float val = 0;
            if (mPlusMinusType == PlusMinusType.ALL) {
                val = val + (sonData.get(1) + (sonData.get(2) - sonData.get(3)));
            } else {
                if (isMultiply) {
                    if (mFlipCombinedChartView.isFilterSelected(0)) {
                        val = val + sonData.get(1);
                    }
                } else {
                    val = val + sonData.get(3);
                }
            }
            float randomFloat = 10f+ random.nextFloat() * (80f);
            val = randomFloat;
            entries.add(new Entry(val, i));
        }
        if (isMultiply) {
            for (int i = 0; i < mChartData.size(); i++) {
                List<Float> sonData = mChartData.get(i);
                float val = 0;
                if (mFlipCombinedChartView.isFilterSelected(0)) {
//                    val = val + sonData.get(1);
                    val = val + entries.get(i).getVal();
                }
                if (mFlipCombinedChartView.isFilterSelected(1)) {
//                    val = val + sonData.get(2);
                    float randomFloat = 10f+ random.nextFloat() * (10f);
                    val = val + randomFloat;
                }

                entries2.add(new Entry(val, i));
            }
        }
//        }

        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);

        if (isMultiply) {
            // 正常电量数据集（Smart Import）
            SectionLineDataSet dayLineData1 = getSectionLineDataSet(entries, getColor(R.color.color_tip_06), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData1.setMode(LineDataSet.Mode.LINEAR);
            dayLineData1.setDataType("smart");
//            dayLineData1.setHighlightEnabled(!mBinding.fcChart.isFilterSelected(1));

            // 紧急电量数据集（Emergency Import）
            SectionLineDataSet dayLineData2 = getSectionLineDataSet(entries2, getColor(R.color.color_tip_warning), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData2.setMode(LineDataSet.Mode.LINEAR);
            dayLineData2.setDataType("emergency");
            //此处需要处理peak shaving的具体数据转化
            SectionLineDataSet dayLineData3 = getSectionLineDataSet(turnData2PeakShaving(entries, entries2), getColor(R.color.color_tip_08), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData3.setMode(LineDataSet.Mode.LINEAR);
            dayLineData3.setDataType("peakshaving");
//            dayLineData3.setThresholdFillColor(Color.parseColor("#826f60"));
            dayLineData3.setThresholdFillColor(getColor(R.color.color_tip_08));
            List<ILineDataSet> lineDataSets = new ArrayList<>();
            if (mFlipCombinedChartView.isFilterSelected(0)) {
                lineDataSets.add(dayLineData1);
            }
            if (mFlipCombinedChartView.isFilterSelected(1)) {
                lineDataSets.add(dayLineData2);
            }
            if (dayLineData3.getEntryCount() > 0) {
                lineDataSets.add(dayLineData3);
            }
            //设置x轴标注
//            mFlipCombinedChartView.getCustomCombinedChart1().setDrawXAxisAnnotation(mFlipCombinedChartView.isFilterSelected(2));
            //设置阈值区域
            mFlipCombinedChartView.getCustomCombinedChart1().setThresholdConfig(dayLineData3, mFlipCombinedChartView.isFilterSelected(3), mFlipCombinedChartView.isFilterSelected(2));
//            mFlipCombinedChartView.getCustomCombinedChart1().setDrawThresholdRegion(mFlipCombinedChartView.isFilterSelected(3));
//            if (mFlipCombinedChartView.isFilterSelected(3)) {
//                lineDataSets.add(dayLineData3);
//            }
            LineData lineData = new LineData(xVals, lineDataSets);
            data.setData(lineData);
        } else {
            SectionLineDataSet dayLineData = getSectionLineDataSet(entries, getColor(R.color.color_tip_06), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData.setMode(LineDataSet.Mode.LINEAR);
            List<ILineDataSet> lineDataSets = new ArrayList<>();
            lineDataSets.add(dayLineData);
            LineData lineData = new LineData(xVals, lineDataSets);
            data.setData(lineData);
        }
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    private ArrayList<Entry> turnData2PeakShaving(ArrayList<Entry> entries, ArrayList<Entry> entries2) {
        ArrayList<Entry> entries3 = new ArrayList<Entry>();
        for (int i = 0; i < entries.size(); i++) {
            float thresholdValue;
            if (i < entries.size()/4) {
                thresholdValue = 0f;
            } else if (i > entries.size()/4 && i < entries.size()/3) {
                thresholdValue = 30f;
            } else if (i > entries.size()/3 && i < entries.size()/2) {
                thresholdValue = 60f;
            } else {
                thresholdValue = 40f;
            }
            entries3.add(new Entry(thresholdValue, i));

        }
        return entries3;
    }

    @Override
    protected void setWeekChart() {
        refreshWeek2Lifetime(mWeeks);
    }

    @Override
    protected void setMonthChart() {
        refreshWeek2Lifetime(months);
    }

    @Override
    protected void setYearChart() {
        refreshWeek2Lifetime(mYears);
    }

    @Override
    protected void setLifetimeChart() {
        refreshWeek2Lifetime(lifetimes);
    }

    @Override
    protected void setFailDayChart() {
        int count = mHourCount / mInterval + 1;
        ArrayList<Entry> entries = new ArrayList<Entry>();
        ArrayList<Entry> entries2 = new ArrayList<Entry>();
        for (int i = 0; i < mChartData.size(); i++) {
            entries.add(new Entry(0, i));
        }
        if (isMultiply) {
            for (int i = 0; i < mChartData.size(); i++) {
                entries2.add(new Entry(0, i));
            }
        }

        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);

        if (isMultiply) {
            SectionLineDataSet dayLineData1 = getSectionLineDataSet(entries, getColor(R.color.transparent), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData1.setMode(LineDataSet.Mode.LINEAR);
//            dayLineData1.setHighlightEnabled(!mBinding.fcChart.isFilterSelected(1));
            SectionLineDataSet dayLineData2 = getSectionLineDataSet(entries2, getColor(R.color.transparent), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData2.setMode(LineDataSet.Mode.LINEAR);
            List<ILineDataSet> lineDataSets = new ArrayList<>();
            if (mFlipCombinedChartView.isFilterSelected(0)) {
                lineDataSets.add(dayLineData1);
            }
            if (mFlipCombinedChartView.isFilterSelected(1)) {
                lineDataSets.add(dayLineData2);
            }
            LineData lineData = new LineData(xVals, lineDataSets);
            data.setData(lineData);
        } else {
            SectionLineDataSet dayLineData = getSectionLineDataSet(entries, getColor(R.color.transparent), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            dayLineData.setMode(LineDataSet.Mode.LINEAR);
            List<ILineDataSet> lineDataSets = new ArrayList<>();
            lineDataSets.add(dayLineData);
            LineData lineData = new LineData(xVals, lineDataSets);
            data.setData(lineData);
        }
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    @Override
    protected void setFailWeekChart() {
        refreshFailWeek2Lifetime(mWeeks);
    }

    @Override
    protected void setFailMonthChart() {
        refreshFailWeek2Lifetime(months);
    }

    @Override
    protected void setFailYearChart() {
        refreshFailWeek2Lifetime(mYears);
    }

    @Override
    protected void setFailLifetimeChart() {
        refreshFailWeek2Lifetime(lifetimes);
    }

    private float getExportedVal() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float dischargedVal = 0f;
        for (List<Float> sonData : mChartData) {
            dischargedVal = dischargedVal + sonData.get(3);
        }
//        if (!isBSensorInstalled && !isMultiply) {
//        if (!isMultiply) {
//            return 0;
//        } else {
//            return mCycleType == CycleType.DAY ? dischargedVal * mInterval / 60 : dischargedVal;
//        }
        return mCycleType == CycleType.DAY ? dischargedVal * mInterval / 60 : dischargedVal;
    }

    private float getImportedVal(boolean isFilter) {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float chargedVal = 0f;
        for (List<Float> sonData : mChartData) {
            if (isFilter) {
                if (mFlipCombinedChartView != null) {
                    if (mFlipCombinedChartView.isFilterSelected(0)) {
                        chargedVal = chargedVal + (sonData.get(1));
                    }

                    if (mFlipCombinedChartView.isFilterSelected(1)) {
                        chargedVal = chargedVal + (sonData.get(2));
                    }
                }
            } else {
                chargedVal = chargedVal + (sonData.get(1) + sonData.get(2));
            }
        }
//        if (!isBSensorInstalled && !isMultiply) {
//        if (!isMultiply) {
//            return 0;
//        } else {
//            return mCycleType == CycleType.DAY ? chargedVal * mInterval / 60 : chargedVal;
//        }
        return mCycleType == CycleType.DAY ? chargedVal * mInterval / 60 : chargedVal;
    }

    private float getNetVal() {
//        if (!isBSensorInstalled && !isMultiply) {
//        if (!isMultiply) {
//            return 0;
//        } else {
//            return getImportedVal(false) - getExportedVal();
//        }
        return getImportedVal(false) - getExportedVal();
    }

    private float getSmartImportedVal(boolean isFilter) {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float solarChargedVal = 0f;
        if (isFilter) {
            if (mFlipCombinedChartView != null && mFlipCombinedChartView.isFilterSelected(0)) {
                for (List<Float> sonData : mChartData) {
                    solarChargedVal = solarChargedVal + sonData.get(1);
                }
            }
        } else {
            for (List<Float> sonData : mChartData) {
                solarChargedVal = solarChargedVal + sonData.get(1);
            }
        }
        return mCycleType == CycleType.DAY ? solarChargedVal * mInterval / 60 : solarChargedVal;
    }

    private float getEmergencyImportedVal(boolean isFilter) {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float gridChargedVal = 0f;
        if (isFilter) {
            if (mFlipCombinedChartView != null && mFlipCombinedChartView.isFilterSelected(1)) {
                for (List<Float> sonData : mChartData) {
                    gridChargedVal = gridChargedVal + sonData.get(2);
                }
            }
        } else {
            for (List<Float> sonData : mChartData) {
                gridChargedVal = gridChargedVal + sonData.get(2);
            }
        }
        return mCycleType == CycleType.DAY ? gridChargedVal * mInterval / 60 : gridChargedVal;
    }

    private void refreshWeek2Lifetime(String[] xLabels) {
        mMarkerView.setPlusMinusType(mPlusMinusType);
        mMarkerView.setMultiply(isMultiply);
        mMarkerView.setSelectedPositions(mFlipCombinedChartView.getSelectPositions());
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < xLabels.length; i++) {
//            if (mPlusMinusType == PlusMinusType.ALL) {
//                yVals.add(new BarEntry(new float[]{0}, i));
//            } else {
//                if (isMultiply) {
//                    yVals.add(new BarEntry(new float[]{0, 0}, i));
//                } else {
//                    yVals.add(new BarEntry(new float[]{0}, i));
//                }
//            }
        }
//        if (isBSensorInstalled || isMultiply) {
        Random random = new Random();
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            if (mPlusMinusType == PlusMinusType.ALL) {
                yVals.add(new BarEntry(new float[]{sonData.get(1) + (sonData.get(2) - sonData.get(3))}, xIndex));
//                    yVals.get(xIndex).setVals(new float[]{sonData.get(1) + (sonData.get(2) - sonData.get(3))});
            } else {
                if (isMultiply) {
//                    yVals.add(new BarEntry(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
//                            mFlipCombinedChartView.isFilterSelected(1) ? sonData.get(2) : 0}, xIndex));
                    ThresholdBarEntry entry = new ThresholdBarEntry(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? 40 + random.nextFloat() * (10f) : 0,
                            mFlipCombinedChartView.isFilterSelected(1) ? 20 + random.nextFloat() * (10f) : 0}, xIndex);
                    entry.setThreshold(random.nextFloat() * (10f));
                    entry.setHasThreshold(random.nextBoolean());
                    mFlipCombinedChartView.getCustomCombinedChart1().setBarThreshold(mFlipCombinedChartView.isFilterSelected(2));
                    yVals.add(entry);
//                    yVals.add(new BarEntry(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? 40 + random.nextFloat() * (10f) : 0,
//                            mFlipCombinedChartView.isFilterSelected(1) ? 20 + random.nextFloat() * (10f) : 0}, xIndex));
//                        yVals.get(xIndex).setVals(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
//                                mFlipCombinedChartView.isFilterSelected(1) ? sonData.get(2) : 0});
                } else {
                    yVals.add(new BarEntry(new float[]{sonData.get(3)}, xIndex));
//                        yVals.get(xIndex).setVals(new float[]{sonData.get(3)});
                }
            }
        }
//        }

        BarDataSet barDataSetWeek = new BarDataSet(yVals, "imported");
        int[] colorsBar;
        if (isMultiply) {
            colorsBar = new int[]{getColor(R.color.color_tip_06),
                    getColor(R.color.color_tip_warning)};
        } else {
            colorsBar = new int[]{getColor(R.color.color_tip_06)};
        }
        barDataSetWeek.setColors(colorsBar);
        barDataSetWeek.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSetWeek.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSetWeek.setHighLightColor(getColor(R.color.color_white_03));
        barDataSetWeek.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSetWeek);
        BarData barData = new BarData(xVals, dataSets);
        CombinedData data = new CombinedData(xLabels);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    private void refreshFailWeek2Lifetime(String[] xLabels) {
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            if (mPlusMinusType == PlusMinusType.ALL) {
                yVals.add(new BarEntry(new float[]{0}, xIndex));
            } else {
                if (isMultiply) {
                    yVals.add(new BarEntry(new float[]{0, 0}, xIndex));
                } else {
                    yVals.add(new BarEntry(new float[]{0}, xIndex));
                }
            }
        }

        BarDataSet barDataSetWeek = new BarDataSet(yVals, "");
        int[] colorsBar;
        if (isMultiply) {
            colorsBar = new int[]{getColor(R.color.transparent),
                    getColor(R.color.transparent)};
        } else {
            colorsBar = new int[]{getColor(R.color.transparent)};
        }
        barDataSetWeek.setColors(colorsBar);
        barDataSetWeek.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSetWeek.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSetWeek.setHighLightColor(getColor(R.color.color_white_03));
        barDataSetWeek.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSetWeek);
        BarData barData = new BarData(xVals, dataSets);
        CombinedData data = new CombinedData(xLabels);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }
}
