package com.dinsafer.module.powerstation;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentInfinitePagerViewTestBinding;
import com.dinsafer.module.MyBaseFragment;

public class InfinitePagerViewTest extends MyBaseFragment<FragmentInfinitePagerViewTestBinding> {
    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_infinite_pager_view_test;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.infiniteViewPager.setAdapter(new HorizontalPagerAdapter(getContext()));
        mBinding.infiniteViewPager.setCurrentItem(1000-1);
    }

    private static class HorizontalPagerAdapter  extends PagerAdapter {
        private Context mContext;
        private LayoutInflater mLayoutInflater;

        public HorizontalPagerAdapter(final Context context) {
            mContext = context;
            mLayoutInflater = LayoutInflater.from(context);
        }

        @Override
        public int getCount() {
            return 1_000;
        }

        @Override
        public Object instantiateItem(final ViewGroup container, final int position) {
            final View view =  mLayoutInflater.inflate(R.layout.fragment_infinite_pager_view_test_son, container, false);;
            final TextView txt = (TextView) view.findViewById(R.id.tv_test);
            txt.setText("InfiniteViewPager" + position);
            container.addView(view);
            return view;
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return view.equals(object);
        }

        @Override
        public void destroyItem(final ViewGroup container, final int position, final Object object) {
            container.removeView((View) object);
        }
    }

}
