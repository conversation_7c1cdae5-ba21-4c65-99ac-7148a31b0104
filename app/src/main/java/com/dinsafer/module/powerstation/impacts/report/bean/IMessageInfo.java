package com.dinsafer.module.powerstation.impacts.report.bean;

public abstract class IMessageInfo<T> {

    public static final int ACCEPT_TEXT = 0;
    public static final int SEND_TEXT = 1;
    // 消息类型: 0是接收消息 1是发送消息
    private final int type;
    private long time;
    private T data;
    // 发送状态  -1失败 0发送中  1发送成功
    private int status;

    public IMessageInfo(int type) {
        this.type = type;
    }

    public IMessageInfo(int type, long time, T data, int status) {
        this.type = type;
        this.time = time;
        this.data = data;
        this.status = status;
    }

    public int getType() {
        return type;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public T getData() {
        return data;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
