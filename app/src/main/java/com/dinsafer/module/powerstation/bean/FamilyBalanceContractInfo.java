package com.dinsafer.module.powerstation.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class FamilyBalanceContractInfo implements Parcelable {

    private String iBAN;
    private String cardholder;
    private String city;
    private String company_name;
    private String country_code;
    private String countryNameDisplay;
    private String home_id;
    private String home_name;
    private String electricitySupplier;
    private String electricitySupplierId;
    private String emailAddress;
    private String euVatNumber;
    private String name;
    private String phoneNumber;
    private String sign;
    private String streetNameAndNumber;
    private Integer type;
    private String zipCode;

    public boolean first_sign;
    public boolean familySigning;

    public void setFamilySigning(boolean familySigning) {
        this.familySigning = familySigning;
    }

    public boolean isFamilySigning() {
        return familySigning;
    }

    public void setFirst_sign(boolean first_sign) {
        this.first_sign = first_sign;
    }

    public boolean isFirst_sign() {
        return first_sign;
    }

    public String getIBAN() {
        return iBAN;
    }

    public void setIBAN(String iBAN) {
        this.iBAN = iBAN;
    }

    public String getCardholder() {
        return cardholder;
    }

    public void setCardholder(String cardholder) {
        this.cardholder = cardholder;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCompany_name() {
        return company_name;
    }

    public void setCompany_name(String company_name) {
        this.company_name = company_name;
    }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getCountryNameDisplay() {
        return countryNameDisplay;
    }

    public void setCountryNameDisplay(String countryNameDisplay) {
        this.countryNameDisplay = countryNameDisplay;
    }

    public String getHome_id() {
        return home_id;
    }

    public void setHome_id(String home_id) {
        this.home_id = home_id;
    }

    public String getHome_name() {
        return home_name;
    }

    public void setHome_name(String home_name) {
        this.home_name = home_name;
    }

    public String getElectricitySupplier() {
        return electricitySupplier;
    }

    public void setElectricitySupplier(String electricitySupplier) {
        this.electricitySupplier = electricitySupplier;
    }

    public String getElectricitySupplierId() {
        return electricitySupplierId;
    }

    public void setElectricitySupplierId(String electricitySupplierId) {
        this.electricitySupplierId = electricitySupplierId;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getEuVatNumber() {
        return euVatNumber;
    }

    public void setEuVatNumber(String euVatNumber) {
        this.euVatNumber = euVatNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getStreetNameAndNumber() {
        return streetNameAndNumber;
    }

    public void setStreetNameAndNumber(String streetNameAndNumber) {
        this.streetNameAndNumber = streetNameAndNumber;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.iBAN);
        dest.writeString(this.cardholder);
        dest.writeString(this.city);
        dest.writeString(this.company_name);
        dest.writeString(this.country_code);
        dest.writeString(this.countryNameDisplay);
        dest.writeString(this.home_id);
        dest.writeString(this.home_name);
        dest.writeString(this.electricitySupplier);
        dest.writeString(this.electricitySupplierId);
        dest.writeString(this.emailAddress);
        dest.writeString(this.euVatNumber);
        dest.writeString(this.name);
        dest.writeString(this.phoneNumber);
        dest.writeString(this.sign);
        dest.writeString(this.streetNameAndNumber);
        dest.writeValue(this.type);
        dest.writeString(this.zipCode);
        dest.writeByte(this.first_sign ? (byte) 1 : (byte) 0);
    }

    public void readFromParcel(Parcel source) {
        this.iBAN = source.readString();
        this.cardholder = source.readString();
        this.city = source.readString();
        this.company_name = source.readString();
        this.country_code = source.readString();
        this.countryNameDisplay = source.readString();
        this.home_id = source.readString();
        this.home_name = source.readString();
        this.electricitySupplier = source.readString();
        this.electricitySupplierId = source.readString();
        this.emailAddress = source.readString();
        this.euVatNumber = source.readString();
        this.name = source.readString();
        this.phoneNumber = source.readString();
        this.sign = source.readString();
        this.streetNameAndNumber = source.readString();
        this.type = (Integer) source.readValue(Integer.class.getClassLoader());
        this.zipCode = source.readString();
        this.first_sign = source.readByte() != 0;
    }

    public FamilyBalanceContractInfo() {
    }

    protected FamilyBalanceContractInfo(Parcel in) {
        this.iBAN = in.readString();
        this.cardholder = in.readString();
        this.city = in.readString();
        this.company_name = in.readString();
        this.country_code = in.readString();
        this.countryNameDisplay = in.readString();
        this.home_id = in.readString();
        this.home_name = in.readString();
        this.electricitySupplier = in.readString();
        this.electricitySupplierId = in.readString();
        this.emailAddress = in.readString();
        this.euVatNumber = in.readString();
        this.name = in.readString();
        this.phoneNumber = in.readString();
        this.sign = in.readString();
        this.streetNameAndNumber = in.readString();
        this.type = (Integer) in.readValue(Integer.class.getClassLoader());
        this.zipCode = in.readString();
        this.first_sign = in.readByte() != 0;
    }

    public static final Creator<FamilyBalanceContractInfo> CREATOR = new Creator<FamilyBalanceContractInfo>() {
        @Override
        public FamilyBalanceContractInfo createFromParcel(Parcel source) {
            return new FamilyBalanceContractInfo(source);
        }

        @Override
        public FamilyBalanceContractInfo[] newArray(int size) {
            return new FamilyBalanceContractInfo[size];
        }
    };
}
