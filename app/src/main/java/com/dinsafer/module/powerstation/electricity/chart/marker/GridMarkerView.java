package com.dinsafer.module.powerstation.electricity.chart.marker;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.TimeUtil;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GridMarkerView extends CustomCombinedMarkerView {

    public GridMarkerView(Context context) {
        super(context);
    }

    @Override
    public void setMarker(List<Entry> entries, List<Highlight> highlights) {
        setGridMarker(entries, highlights);
    }

    private void setGridMarker(List<Entry> entries, List<Highlight> highlights) {
        switch (BaseChartFragment.mCycleType) {
            case DAY:
                llValue.setVisibility(VISIBLE);
                if (plusMinusType == PlusMinusType.ALL) {
                    float value = entries.get(0).getVal();
                    float valAbs = Math.abs(value);
                    String valStr = ChartDataUtil.getPowerTransferVal(valAbs, valAbs, true) + getUnit(valAbs, false);
                    tvKey.setLocalText(value < 0 ? mContext.getString(R.string.electricity_exported) :
                            mContext.getString(R.string.electricity_imported));
                    tvValue.setLocalText(valStr);
                    llSubValue.setVisibility(GONE);
                } else {
                    if (isMultiply) {
                        float value1 = entries.get(0).getVal();
                        float value2 = 0f;
                        float value3 = 0f;
                        if (isDualPowerOpen) {
                            if (entries.size() > 1) {
                                value1 += entries.get(1).getVal();
                            }
                            if (entries.size() > 2) {
                                value2 = entries.get(2).getVal();
                            }
                        } else {
                            if (entries.size() > 1) {
                                value2 = entries.get(1).getVal();
                            }
                            if (entries.size() > 2) {
                                value3 = entries.get(2).getVal();
                            }
                        }
                        float distance = value2 - value1;
                        String valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, true) + getUnit(value1, false);
                        distance = Math.abs(distance);
                        String valStr2 = ChartDataUtil.getPowerTransferVal(distance, distance, true) + getUnit(distance, false);
                        String valStr3 = "";
                        float distance2 = value2 == 0f? value1 - value3 : value2 - value3;
                        if (distance2 > 0) {
                            distance2 = Math.abs(distance2);
                            valStr3 = ChartDataUtil.getPowerTransferVal(distance2, distance2, true) + getUnit(distance2, false);
                        }
//                        if (CollectionUtil.isListNotEmpty(selectedPositions)) {
//                            if (selectedPositions.size() == 4) {
//                                tvKey.setText(Local.s(mContext.getString(R.string.electricity_smart_imported)));
//                                tvValue.setText(valStr1);
//                                tvSubKey.setText(Local.s(mContext.getString(R.string.electricity_emergency_imported)));
//                                tvSubValue.setText(valStr2);
//                                tvThirdKey.setText(Local.s(mContext.getString(R.string.electricity_peak_power_exceeds)));
//                                tvThirdValue.setText(valStr3);
//                                llSubValue.setVisibility(VISIBLE);
//                                llThirdValue.setVisibility(VISIBLE);
//                            } else if (selectedPositions.size() == 3) {
//                                llSubValue.setVisibility(VISIBLE);
//                                Integer pos = selectedPositions.get(0);
//                                if (pos == 0) {
//                                    tvKey.setText(Local.s(mContext.getString(R.string.electricity_smart_imported)));
//                                    tvValue.setText(valStr1);
//                                } else {
//                                    tvKey.setText(Local.s(mContext.getString(R.string.electricity_emergency_imported)));
//                                    tvValue.setText(valStr2);
//                                }
//                            }
//                        } else {
//                            llValue.setVisibility(GONE);
//                        }
                        handleGridImportedDisplay(selectedPositions,valStr1,valStr2,valStr3);
                    } else {
                        llSubValue.setVisibility(GONE);
                        float value = entries.get(0).getVal();
                        String valStr = ChartDataUtil.getPowerTransferVal(value, value, true) + getUnit(value, false);
                        tvKey.setLocalText(mContext.getString(R.string.electricity_exported));
                        tvValue.setLocalText(valStr);
                    }
                }
                int xIndex = entries.get(0).getXIndex();
                String text = "";
                if (Math.abs(timeType) == 1) {
                    text = TimeUtil.getHourMinuteStr(timestamp, timezone, xIndex * interval, timeType);
//                    tvTime.setLocalText(TimeUtil.getHourMinuteStr(timestamp, timezone, xIndex * interval, timeType));
                } else {
                    text = TimeUtil.minute2HourMinute(xIndex * interval);
//                    tvTime.setLocalText(TimeUtil.minute2HourMinute(xIndex * interval));
                }
                if (selectedPositions.contains(2)) {
                    tvTime.setText(Local.s(text)+"("+Local.s(mContext.getString(R.string.electricity_peak_shaving))+")");
                } else {
                    tvTime.setLocalText(text);
                }
                break;

            case WEEK:
            case MONTH:
            case YEAR:
            case LIFETIME:
                if (plusMinusType == PlusMinusType.ALL) {
                    float value = entries.get(0).getVal();
                    float valAbs = Math.abs(value);
                    String valStr = ChartDataUtil.getPowerTransferVal(valAbs, valAbs, false) + getUnit(valAbs, true);
                    tvKey.setLocalText(value < 0 ? mContext.getString(R.string.electricity_exported) :
                            mContext.getString(R.string.electricity_imported));
                    tvValue.setLocalText(valStr);
                    tvTime.setLocalText(TimeUtil.minute2HourMinute(entries.get(0).getXIndex() * interval));
                    llSubValue.setVisibility(GONE);
                } else {
                    if (isMultiply) {
                        BarEntry barEntry = (BarEntry) entries.get(0);
                        float[] values = barEntry.getVals();
                        float value1 = values[0];
                        float value2 = 0f;
                        if (values.length > 1) {
                            value2 = Math.abs(values[1]);
                        }
                        String valStr1 = ChartDataUtil.getPowerTransferVal(value1, value1, false) + getUnit(value1, true);
                        String valStr2 = ChartDataUtil.getPowerTransferVal(value2, value2, false) + getUnit(value2, true);
//                        if (CollectionUtil.isListNotEmpty(selectedPositions)) {
//                            if (selectedPositions.size() == 3) {
//                                tvKey.setText(Local.s(mContext.getString(R.string.electricity_smart_imported)));
//                                tvValue.setText(valStr1);
//                                tvSubKey.setText(Local.s(mContext.getString(R.string.electricity_emergency_imported)));
//                                tvSubValue.setText(valStr2);
//                                llSubValue.setVisibility(VISIBLE);
//                            } else if(selectedPositions.size() == 2) {
//
//                            } else {
//                                llSubValue.setVisibility(GONE);
//                                Integer pos = selectedPositions.get(0);
//                                if (pos == 0) {
//                                    tvKey.setText(Local.s(mContext.getString(R.string.electricity_smart_imported)));
//                                    tvValue.setText(valStr1);
//                                } else {
//                                    tvKey.setText(Local.s(mContext.getString(R.string.electricity_emergency_imported)));
//                                    tvValue.setText(valStr2);
//                                }
//                            }
//                        } else {
//                            llValue.setVisibility(GONE);
//                        }
                        handleWeeksDisplay(valStr1,valStr2);
                    } else {
                        llSubValue.setVisibility(GONE);
                        float value = entries.get(0).getVal();
                        String valStr = ChartDataUtil.getPowerTransferVal(value, value, false) + getUnit(value, true);
                        tvKey.setLocalText(mContext.getString(R.string.electricity_exported));
                        tvValue.setLocalText(valStr);
                    }
                }
                int index = entries.get(0).getXIndex();
                if (BaseChartFragment.mCycleType == CycleType.WEEK) {
                    if (index >= BaseChartFragment.mWeeks.length) return;
                    if (selectedPositions.contains(2)) {
                        tvTime.setText(Local.s(BaseChartFragment.mWeeks[index])+"("+Local.s(mContext.getString(R.string.electricity_peak_shaving))+")");
                    } else {
                        tvTime.setLocalText(BaseChartFragment.mWeeks[index]);
                    }
                } else if (BaseChartFragment.mCycleType == CycleType.MONTH) {
                    if (index >= BaseChartFragment.months.length) return;
                    if (selectedPositions.contains(2)) {
                        tvTime.setText(Local.s(BaseChartFragment.months[index])+"("+Local.s(mContext.getString(R.string.electricity_peak_shaving))+")");
                    } else {
                        tvTime.setLocalText(BaseChartFragment.months[index]);
                    }
                } else if (BaseChartFragment.mCycleType == CycleType.YEAR) {
                    if (index >= BaseChartFragment.mYears.length) return;
                    if (selectedPositions.contains(2)) {
                        tvTime.setText(Local.s(BaseChartFragment.mYears[index])+"("+Local.s(mContext.getString(R.string.electricity_peak_shaving))+")");
                    } else {
                        tvTime.setLocalText(BaseChartFragment.mYears[index]);
                    }
                } else if (BaseChartFragment.mCycleType == CycleType.LIFETIME) {
                    if (index >= BaseChartFragment.lifetimes.length) return;
                    if (selectedPositions.contains(2)) {
                        tvTime.setText(Local.s(BaseChartFragment.lifetimes[index])+"("+Local.s(mContext.getString(R.string.electricity_peak_shaving))+")");
                    } else {
                        tvTime.setLocalText(BaseChartFragment.lifetimes[index]);
                    }
                }
                break;
        }
    }
    private void handleGridImportedDisplay(List<Integer> selectedPositions, String valStr1, String valStr2, String valStr3) {
        if (CollectionUtil.isListEmpty(selectedPositions)) {
            llValue.setVisibility(GONE);
            return;
        }

        llValue.setVisibility(VISIBLE);

        // 处理位置3：使用llThirdValue显示valStr3
        if (selectedPositions.contains(3)) {
            tvThirdKey.setText(Local.s(mContext.getString(R.string.electricity_peak_power_exceeds)));
            tvThirdValue.setText(valStr3);
            llThirdValue.setVisibility(VISIBLE);
        } else {
            llThirdValue.setVisibility(GONE);
        }
        handleWeeksDisplay(valStr1, valStr2);
    }

    private void handleWeeksDisplay(String valStr1, String valStr2) {
        // 处理位置0,1：按优先级显示
        boolean hasPosition0 = selectedPositions.contains(0);
        boolean hasPosition1 = selectedPositions.contains(1);

        if (hasPosition0 && hasPosition1) {
            // 双选：位置0在上，位置1在下
            tvKey.setText(Local.s(mContext.getString(R.string.electricity_smart_imported)));
            tvValue.setText(valStr1);

            tvSubKey.setText(Local.s(mContext.getString(R.string.electricity_emergency_imported)));
            tvSubValue.setText(valStr2);
            llSubValue.setVisibility(VISIBLE);
        } else if (hasPosition0) {
            // 只选位置0
            tvKey.setText(Local.s(mContext.getString(R.string.electricity_smart_imported)));
            tvValue.setText(valStr1);
            llSubValue.setVisibility(GONE);
        } else if (hasPosition1) {
            // 只选位置1
            tvKey.setText(Local.s(mContext.getString(R.string.electricity_emergency_imported)));
            tvValue.setText(valStr2);
            llSubValue.setVisibility(GONE);
        } else {
            // 只选了位置3或位置2，隐藏主显示区域
            llValue.setVisibility(GONE);
            llSubValue.setVisibility(GONE);
        }
    }
}
