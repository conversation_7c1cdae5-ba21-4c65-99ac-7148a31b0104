package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemOnGridConfigurationSwitchBinding;
import com.dinsafer.module.powerstation.event.OnGridConfigurationEditEvent;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.rv.BindModel;

import org.greenrobot.eventbus.EventBus;

public class OGCSwitchModel extends BindModel<ItemOnGridConfigurationSwitchBinding> {

    private String title;
    private boolean isOn;
    private boolean isEditable;
    private boolean isEdit;

    public OGCSwitchModel(Context context, String title, boolean isOn) {
        super(context);
        this.title = title;
        this.isOn = isOn;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_on_grid_configuration_switch;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemOnGridConfigurationSwitchBinding binding) {
        binding.tvTitle.setLocalText(title);
        binding.swOpen.setOn(isOn);
        binding.swOpen.setEnabled(isEditable);
        binding.swOpen.setAlpha(isEditable ? 1f : 0.5f);
        binding.swOpen.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                OGCSwitchModel.this.isOn = isOn;
                EventBus.getDefault().post(new OnGridConfigurationEditEvent(holder.getAdapterPosition(), isOn));
            }
        });
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isOpen() {
        return isOn;
    }

    public void setOpen(boolean isOn) {
        isOn = isOn;
    }

    public boolean isEditable() {
        return isEditable;
    }

    public void setEditable(boolean editable) {
        isEditable = editable;
        if (!isEditable) {
            isEdit = false;
        }
    }

    public boolean isEdit() {
        return isEdit;
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
    }
}
