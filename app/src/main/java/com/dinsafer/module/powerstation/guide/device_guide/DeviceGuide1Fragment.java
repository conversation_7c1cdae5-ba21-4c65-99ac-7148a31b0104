package com.dinsafer.module.powerstation.guide.device_guide;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDeviceGuide1Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class DeviceGuide1Fragment extends MyBaseFragment<FragmentDeviceGuide1Binding> {

    public static DeviceGuide1Fragment newInstance() {
        return new DeviceGuide1Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_device_guide_1;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
