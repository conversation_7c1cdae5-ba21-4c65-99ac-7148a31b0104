package com.dinsafer.module.powerstation.electricity.chart.render;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.text.TextUtils;

import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.dinnet.R;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.renderer.YAxisRenderer;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.Utils;
import com.github.mikephil.charting.utils.ViewPortHandler;

public class CustomYAxisRenderer extends YAxisRenderer {

    private Rect mTextRect;

    public CustomYAxisRenderer(Context context, ViewPortHandler viewPortHandler, YAxis yAxis, Transformer trans) {
        super(viewPortHandler, yAxis, trans);
        mTextRect = new Rect();
        Typeface typeface = ResourcesCompat.getFont(context, R.font.palanquin);
        mYAxis.setTypeface(typeface);
    }

    @Override
    protected void drawYLabels(Canvas c, float fixedPosition, float[] positions, float offset) {
        // draw
        for (int i = 0; i < mYAxis.mEntryCount; i++) {

            String text = mYAxis.getFormattedLabel(i);
            if (!mYAxis.isDrawTopYLabelEntryEnabled() && i >= mYAxis.mEntryCount - 1)
                return;
            float finalOffset = 0f;
            if (i == 0) {
                finalOffset = -2f;
            } else if (i == mYAxis.mEntryCount - 1) {
                finalOffset = 2f * offset + 2f;
            } else {
                finalOffset = offset;
            }
            String[] textArr = text.split("\n");
            float lastY = positions[i * 2 + 1] + finalOffset;
            if (i == mYAxis.mEntryCount - 1) {
                for (int j = 0; j < textArr.length; j++) {
                    String label = textArr[j];
                    mAxisLabelPaint.getTextBounds(label, 0, label.length(), mTextRect);
                    float y = j == 0 ? lastY : lastY + mTextRect.height() + 5;
                    c.drawText(label, fixedPosition, y, mAxisLabelPaint);
                    lastY = y;
                }
            } else {
                for (int j = textArr.length - 1; j >= 0; j--) {
                    String label = textArr[j];
                    mAxisLabelPaint.getTextBounds(label, 0, label.length(), mTextRect);
                    float y = j == textArr.length - 1 ? lastY : lastY - mTextRect.height() - 5;
                    c.drawText(label, fixedPosition, y, mAxisLabelPaint);
                    lastY = y;
                }
            }
        }
    }

    /**
     * 画Y轴标签上的描述
     *
     * @param canvas
     * @param desc
     */
    public void drawAxisYTopDesc(Canvas canvas, String desc) {

        if (!mYAxis.isEnabled() || !mYAxis.isDrawLabelsEnabled() || TextUtils.isEmpty(desc))
            return;

        float[] positions = new float[2];
        positions[1] = mYAxis.mEntries[mYAxis.mEntryCount - 1];

        mTrans.pointValuesToPixel(positions);

        mAxisLabelPaint.setTypeface(mYAxis.getTypeface());
        mAxisLabelPaint.setTextSize(mYAxis.getTextSize());
        mAxisLabelPaint.setColor(mYAxis.getTextColor());

        float xoffset = mYAxis.getXOffset();
        float yoffset = Utils.calcTextHeight(mAxisLabelPaint, "A") / 2.5f + mYAxis.getYOffset();

        YAxis.AxisDependency dependency = mYAxis.getAxisDependency();
        YAxis.YAxisLabelPosition labelPosition = mYAxis.getLabelPosition();

        float xPos = 0f;

        if (dependency == YAxis.AxisDependency.LEFT) {

            if (labelPosition == YAxis.YAxisLabelPosition.OUTSIDE_CHART) {
                mAxisLabelPaint.setTextAlign(Paint.Align.RIGHT);
                xPos = mViewPortHandler.offsetLeft() - xoffset;
            } else {
                mAxisLabelPaint.setTextAlign(Paint.Align.LEFT);
                xPos = mViewPortHandler.offsetLeft() + xoffset;
            }

        } else {

            if (labelPosition == YAxis.YAxisLabelPosition.OUTSIDE_CHART) {
                mAxisLabelPaint.setTextAlign(Paint.Align.LEFT);
                xPos = mViewPortHandler.contentRight() + xoffset;
            } else {
                mAxisLabelPaint.setTextAlign(Paint.Align.RIGHT);
                xPos = mViewPortHandler.contentRight() - xoffset;
            }
        }
        String[] descArr = desc.split("\n");
        float lastY = positions[1] + 2 * yoffset;
        for (int i = descArr.length - 1; i >= 0; i--) {
            String str = descArr[i];
            mAxisLabelPaint.getTextBounds(str, 0, str.length(), mTextRect);
            float y = i == descArr.length - 1 ? lastY - mTextRect.height() - 10 : lastY - mTextRect.height() - 5;
            canvas.drawText(str, xPos, y, mAxisLabelPaint);
            lastY = y;
        }
    }
}
