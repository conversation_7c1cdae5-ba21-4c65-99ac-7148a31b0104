package com.dinsafer.module.powerstation.impacts.report.adapter.holder;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsReportTextAcceptBinding;
import com.dinsafer.module.powerstation.impacts.report.bean.IMessageInfo;
import com.dinsafer.module.powerstation.impacts.report.bean.TextMessageInfo;
import com.dinsafer.module.powerstation.drawable.AIBubbleDrawable;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DensityUtils;

import java.util.List;

public class TextAcceptViewHolder extends BaseViewHolder<TextMessageInfo, ItemPsReportTextAcceptBinding> {

    private Context mContext;
    private String timezone;
    private final long FIVE_MINUTE = 5 * 60 * 1000l;

    public TextAcceptViewHolder(ViewGroup parent, String timezone) {
        super(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_ps_report_text_accept, parent, false));
        mContext = parent.getContext();
        this.timezone = timezone;
    }

    @Override
    public void setData(int position, TextMessageInfo messageInfo, List<IMessageInfo> data) {
        long time = messageInfo.getTime();
        if (position > 0) {
            long lastItemTime = data.get(position - 1).getTime();
            long timeDiff = time - lastItemTime;
            mBinding.tvTime.setVisibility(timeDiff < FIVE_MINUTE ? View.GONE : View.VISIBLE);
        } else {
            mBinding.tvTime.setVisibility(View.VISIBLE);
        }
        mBinding.tvTime.setText(DDDateUtil.formatWithTimezone(time, timezone, "HH:mm"));
        mBinding.tvContent.setLocalText(messageInfo.getData());
        mBinding.tvContent.post(() -> mBinding.tvContent.setBackground(getMsgDrawable(mBinding.tvContent)));
    }

    private AIBubbleDrawable getMsgDrawable(View view) {
        AIBubbleDrawable drawable = new AIBubbleDrawable(view, new int[]{
                mContext.getResources().getColor(R.color.color_ai_stroke_1),
                mContext.getResources().getColor(R.color.color_ai_stroke_2),
                mContext.getResources().getColor(R.color.color_ai_stroke_3),
                mContext.getResources().getColor(R.color.color_ai_stroke_4),
        }, new float[]{0f, 0.33f, 0.7f, 1f}, DensityUtils.dp2px(mContext, 6));
        return drawable;
    }
}
