package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAiTextBinding;
import com.dinsafer.ui.rv.BindModel;

public class AIPVForecastHeaderModel extends BindModel<ItemAiTextBinding> {

    private String text;

    public AIPVForecastHeaderModel(Context context, String text) {
        super(context);
        this.text = text;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ai_text;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAiTextBinding binding) {
        binding.tvContent.setLocalText(text);
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
