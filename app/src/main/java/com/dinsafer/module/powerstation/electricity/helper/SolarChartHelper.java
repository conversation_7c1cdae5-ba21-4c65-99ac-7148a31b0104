package com.dinsafer.module.powerstation.electricity.helper;

import android.content.Context;
import android.graphics.Color;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.util.Local;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;

import java.util.ArrayList;
import java.util.List;

public class SolarChartHelper extends BaseChartHelper {

    private final Context mContext;
    private final int bmtType;

    public SolarChartHelper(Context context, int bmtType) {
        super(context);
        this.mContext = context;
        this.bmtType = bmtType;
    }

    public ArrayList<PSElectricityTypeBean> getFilterData(boolean isThree, boolean isDualPowerOpen) {
        ArrayList<PSElectricityTypeBean> data = new ArrayList<>();
        PSElectricityTypeBean solarDualBean = new PSElectricityTypeBean(R.drawable.shape_electricity_solar_dual_sel, mContext.getString(R.string.solar_dual), true);
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            String mppt = Local.s(mContext.getString(R.string.electricity_mppt));
            if (isThree) {
                data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_mppt_a_sel, mppt + " 1", true));
                data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_mppt_b_sel, mppt + " 2", true));
                data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_mppt_c_sel, mppt + " 3", true));
            } else {
                data.add(new PSElectricityTypeBean(R.drawable.shape_electricity_mppt_a_sel, mppt, true));
            }
            if (isDualPowerOpen) {
                data.add(solarDualBean);
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            data.add(solarDualBean);
        }
        return data;
    }

    public List<ILineDataSet> getLineDataSets(List<List<Float>> chartData, FlipCombinedChartView flipCombinedChartView,
                                              boolean isThree, boolean isDualPowerOpen) {
        ArrayList<Entry> entries1 = new ArrayList<Entry>();
        ArrayList<Entry> entries2 = new ArrayList<Entry>();
        ArrayList<Entry> entries3 = new ArrayList<Entry>();
        ArrayList<Entry> entriesSolarDual = new ArrayList<Entry>();
        for (int i = 0; i < chartData.size(); i++) {
            List<Float> sonData = chartData.get(i);
            float val = 0;
            val = val + sonData.get(1);
            entries1.add(new Entry(val, i));
        }
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            SectionLineDataSet dayLineDataSet1 = getSectionLineDataSet(entries1, getColor(R.color.color_tip_04), YAxis.AxisDependency.LEFT,
                    null, null, false, true);
            if (isDualPowerOpen) {
                dayLineDataSet1.setHighlightEnabled(!flipCombinedChartView.isFilterSelected(1) && !flipCombinedChartView.isFilterSelected(2) && !flipCombinedChartView.isFilterSelected(3));
            } else {
                dayLineDataSet1.setHighlightEnabled(!flipCombinedChartView.isFilterSelected(1) && !flipCombinedChartView.isFilterSelected(2));
            }
            dayLineDataSet1.setMode(LineDataSet.Mode.LINEAR);
            if (isThree) {
                if (flipCombinedChartView.isFilterSelected(0)) {
                    lineDataSets.add(dayLineDataSet1);
                }
                if (flipCombinedChartView.isFilterSelected(1)) {
                    for (int i = 0; i < chartData.size(); i++) {
                        List<Float> sonData = chartData.get(i);
                        float val = sonData.get(2);
                        if (flipCombinedChartView.isFilterSelected(0)) {
                            val = val + sonData.get(1);
                        }
                        entries2.add(new Entry(val, i));
                    }
                    SectionLineDataSet dayLineDataSet2 = getSectionLineDataSet(entries2, getColor(R.color.color_tip_04_2), YAxis.AxisDependency.LEFT,
                            null, null, false, true);
                    if (isDualPowerOpen) {
                        dayLineDataSet2.setHighlightEnabled(!flipCombinedChartView.isFilterSelected(2) && !flipCombinedChartView.isFilterSelected(3));
                    } else {
                        dayLineDataSet2.setHighlightEnabled(!flipCombinedChartView.isFilterSelected(2));
                    }
                    dayLineDataSet2.setMode(LineDataSet.Mode.LINEAR);
                    lineDataSets.add(dayLineDataSet2);
                }

                if (flipCombinedChartView.isFilterSelected(2)) {
                    for (int i = 0; i < chartData.size(); i++) {
                        List<Float> sonData = chartData.get(i);
                        float val = sonData.get(3);
                        if (flipCombinedChartView.isFilterSelected(0)) {
                            val = val + sonData.get(1);
                        }
                        if (flipCombinedChartView.isFilterSelected(1)) {
                            val = val + sonData.get(2);
                        }
                        entries3.add(new Entry(val, i));
                    }
                    SectionLineDataSet dayLineDataSet3 = getSectionLineDataSet(entries3, getColor(R.color.color_tip_04_3), YAxis.AxisDependency.LEFT,
                            null, null, false, true);
                    if (isDualPowerOpen) {
                        dayLineDataSet3.setHighlightEnabled(!flipCombinedChartView.isFilterSelected(3));
                    }
                    dayLineDataSet3.setMode(LineDataSet.Mode.LINEAR);
                    lineDataSets.add(dayLineDataSet3);
                }
                if (isDualPowerOpen && flipCombinedChartView.isFilterSelected(3)) {
                    for (int i = 0; i < chartData.size(); i++) {
                        List<Float> sonData = chartData.get(i);
                        float val = sonData.get(4);
                        if (flipCombinedChartView.isFilterSelected(0)) {
                            val = val + sonData.get(1);
                        }
                        if (flipCombinedChartView.isFilterSelected(1)) {
                            val = val + sonData.get(2);
                        }
                        if (flipCombinedChartView.isFilterSelected(2)) {
                            val = val + sonData.get(3);
                        }
                        entriesSolarDual.add(new Entry(val, i));
                    }
                    SectionLineDataSet dayLineDataSetSolarDual = getSectionLineDataSet(entriesSolarDual, getColor(R.color.color_tip_01), YAxis.AxisDependency.LEFT,
                            null, null, false, true);
                    dayLineDataSetSolarDual.setMode(LineDataSet.Mode.LINEAR);
                    lineDataSets.add(dayLineDataSetSolarDual);
                }

            } else {
                if (isDualPowerOpen) {
                    dayLineDataSet1.setHighlightEnabled(!flipCombinedChartView.isFilterSelected(1));
                }
                if (flipCombinedChartView.isFilterSelected(0)) {
                    lineDataSets.add(dayLineDataSet1);
                }
                if (isDualPowerOpen && flipCombinedChartView.isFilterSelected(1)) {
                    for (int i = 0; i < chartData.size(); i++) {
                        List<Float> sonData = chartData.get(i);
                        float val = sonData.get(4);
                        if (flipCombinedChartView.isFilterSelected(0)) {
                            val = val + sonData.get(1);
                        }
                        entriesSolarDual.add(new Entry(val, i));
                    }
                    SectionLineDataSet dayLineDataSetSolarDual = getSectionLineDataSet(entriesSolarDual, getColor(R.color.color_tip_01), YAxis.AxisDependency.LEFT,
                            null, null, false, true);
                    dayLineDataSetSolarDual.setMode(LineDataSet.Mode.LINEAR);
                    lineDataSets.add(dayLineDataSetSolarDual);
                }
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            if (flipCombinedChartView.isFilterSelected(0)) {
                for (int i = 0; i < chartData.size(); i++) {
                    List<Float> sonData = chartData.get(i);
                    float val = sonData.get(4);
                    entriesSolarDual.add(new Entry(val, i));
                }
                SectionLineDataSet dayLineDataSetSolarDual = getSectionLineDataSet(entriesSolarDual, getColor(R.color.color_tip_01), YAxis.AxisDependency.LEFT,
                        null, null, false, true);
                dayLineDataSetSolarDual.setHighlightEnabled(true);
                dayLineDataSetSolarDual.setMode(LineDataSet.Mode.LINEAR);
                lineDataSets.add(dayLineDataSetSolarDual);
            }
        }
        return lineDataSets;
    }

    public BarEntry getBarEntry(FlipCombinedChartView flipCombinedChartView, List<Float> sonData, int xIndex,
                                boolean isThree, boolean isDualPowerOpen) {
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            if (isThree) {
                if (isDualPowerOpen) {
                    return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
                            flipCombinedChartView.isFilterSelected(1) ? sonData.get(2) : 0,
                            flipCombinedChartView.isFilterSelected(2) ? sonData.get(3) : 0,
                            flipCombinedChartView.isFilterSelected(3) ? sonData.get(4) : 0}, xIndex);
                } else {
                    return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
                            flipCombinedChartView.isFilterSelected(1) ? sonData.get(2) : 0,
                            flipCombinedChartView.isFilterSelected(2) ? sonData.get(3) : 0}, xIndex);
                }
            } else {
                if (isDualPowerOpen) {
                    return new BarEntry(new float[]{flipCombinedChartView.isFilterSelected(0) ? sonData.get(1) : 0,
                            flipCombinedChartView.isFilterSelected(1) ? sonData.get(4) : 0}, xIndex);
                } else {
                    return new BarEntry(new float[]{sonData.get(1)}, xIndex);
                }
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            return new BarEntry(new float[]{sonData.get(4)}, xIndex);
        } else {
            return new BarEntry(new float[]{}, xIndex);
        }
    }

    public int[] getBarColors(boolean isSuccess, boolean isThree, boolean isDualPowerOpen) {
        int[] barColors;
        if (bmtType == BmtUtil.BMT_POWER_CORE) {
            if (isThree) {
                if (isDualPowerOpen) {
                    if (isSuccess) {
                        barColors = new int[]{getColor(R.color.color_tip_04),
                                getColor(R.color.color_tip_04_2),
                                getColor(R.color.color_tip_04_3),
                                getColor(R.color.color_tip_01)};
                    } else {
                        barColors = new int[]{getColor(R.color.transparent),
                                getColor(R.color.transparent),
                                getColor(R.color.transparent),
                                getColor(R.color.transparent)};
                    }
                } else {
                    if (isSuccess) {
                        barColors = new int[]{getColor(R.color.color_tip_04),
                                getColor(R.color.color_tip_04_2),
                                getColor(R.color.color_tip_04_3)};
                    } else {
                        barColors = new int[]{getColor(R.color.transparent),
                                getColor(R.color.transparent),
                                getColor(R.color.transparent)};
                    }
                }
            } else {
                if (isDualPowerOpen) {
                    if (isSuccess) {
                        barColors = new int[]{getColor(R.color.color_tip_04),
                                getColor(R.color.color_tip_01)};
                    } else {
                        barColors = new int[]{getColor(R.color.transparent),
                                getColor(R.color.transparent)};
                    }
                } else {
                    if (isSuccess) {
                        barColors = new int[]{getColor(R.color.color_tip_04)};
                    } else {
                        barColors = new int[]{getColor(R.color.transparent)};
                    }
                }
            }
        } else if (bmtType == BmtUtil.BMT_POWER_STORE) {
            if (isSuccess) {
                barColors = new int[]{mContext.getResources().getColor(R.color.color_tip_01)};
            } else {
                barColors = new int[]{mContext.getResources().getColor(R.color.transparent)};
            }
        } else {
            barColors = new int[]{};
        }
        return barColors;
    }
}
