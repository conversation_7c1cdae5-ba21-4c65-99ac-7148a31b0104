package com.dinsafer.module.powerstation.electricity.chart.marker;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.util.TimeUtil;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;

import java.util.List;

public class SolarMarkerView extends CustomCombinedMarkerView {

    public SolarMarkerView(Context context) {
        super(context);
    }

    @Override
    public void setMarker(List<Entry> entries, List<Highlight> highlights) {
        setSolarMarker(entries, highlights);
    }

    private void setSolarMarker(List<Entry> entries, List<Highlight> highlights) {
        llSubValue.setVisibility(GONE);
        float value = entries.get(entries.size() - 1).getVal();
        String valStr = ChartDataUtil.getPowerTransferVal(value, value, BaseChartFragment.mCycleType == CycleType.DAY)
                + getUnit(value, BaseChartFragment.mCycleType != CycleType.DAY);
        tvKey.setLocalText(BaseChartFragment.mCycleType == CycleType.DAY ?
                mContext.getString(R.string.power) : mContext.getString(R.string.total));
        tvValue.setLocalText(valStr);
        int index = entries.get(0).getXIndex();
        switch (BaseChartFragment.mCycleType) {
            case DAY:
                int xIndex = entries.get(0).getXIndex();
                if (Math.abs(timeType) == 1) {
                    tvTime.setLocalText(TimeUtil.getHourMinuteStr(timestamp, timezone, xIndex * interval, timeType));
                } else {
                    tvTime.setLocalText(TimeUtil.minute2HourMinute(xIndex * interval));
                }
                break;
            case WEEK:
                if (index >= BaseChartFragment.mWeeks.length) return;
                tvTime.setLocalText(BaseChartFragment.mWeeks[index]);
                break;

            case MONTH:
                if (index >= BaseChartFragment.months.length) return;
                tvTime.setLocalText(BaseChartFragment.months[index]);
                break;

            case YEAR:
                if (index >= BaseChartFragment.mYears.length) return;
                tvTime.setLocalText(BaseChartFragment.mYears[index]);
                break;

            case LIFETIME:
                if (index >= BaseChartFragment.lifetimes.length) return;
                tvTime.setLocalText(BaseChartFragment.lifetimes[index]);
                break;
        }

    }
}
