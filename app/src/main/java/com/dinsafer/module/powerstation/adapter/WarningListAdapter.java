package com.dinsafer.module.powerstation.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ExceptionWarning;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.Local;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/24 16:52
 * @description :
 */
public class WarningListAdapter extends BaseQuickAdapter<WarningBean, BaseViewHolder> {

    private OnRestartListener listener;

    public WarningListAdapter(OnRestartListener onRestartListener) {
        super(R.layout.item_warning);
        this.listener = onRestartListener;
    }

    @Override
    protected void convert(BaseViewHolder helper, WarningBean item) {
        LocalTextView tvTitle = helper.getView(R.id.tv_title);
        LocalTextView tvContent = helper.getView(R.id.tv_content);
        LocalTextView tvRestart = helper.getView(R.id.tv_restart);
        tvTitle.setLocalText(item.getTitle());
        String content = Local.s(item.getContent());
        content = content.replace(mContext.getString(R.string.well_device_name), ExceptionWarning.deviceName)
                .replace(mContext.getString(R.string.well_family), ExceptionWarning.currentFamily);
        tvContent.setLocalText(content);
        tvRestart.setVisibility(item.isRestart() ? View.VISIBLE : View.GONE);
    }

    public interface OnRestartListener {
        void onRestartListener(int type);
    }
}
