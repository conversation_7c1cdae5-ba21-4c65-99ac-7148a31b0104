package com.dinsafer.module.powerstation.event;

import java.util.ArrayList;

public class SaveScheduledChargeEvent {

    private ArrayList<Integer> weekdays;
    private ArrayList<Integer> weekend;

    public SaveScheduledChargeEvent(ArrayList<Integer> weekdays, ArrayList<Integer> weekend) {
        this.weekdays = weekdays;
        this.weekend = weekend;
    }

    public ArrayList<Integer> getWeekdays() {
        return weekdays;
    }

    public void setWeekdays(ArrayList<Integer> weekdays) {
        this.weekdays = weekdays;
    }

    public ArrayList<Integer> getWeekend() {
        return weekend;
    }

    public void setWeekend(ArrayList<Integer> weekend) {
        this.weekend = weekend;
    }
}
