package com.dinsafer.module.powerstation.electricity.controller;

import android.content.Context;

import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;

public interface IChartModelController{
    void initXAxis(Context context, CustomCombinedChartManager customCombinedChartManager,
                   CycleType cycleType, PlusMinusType plusMinusType, int labelsToSkip, XAxisValueFormatter formatter);
    void initYAxis(Context context, CustomCombinedChartManager customCombinedChartManager,
                   CycleType cycleType, PlusMinusType plusMinusType, YAxisValueFormatter yFormatter,
                   boolean isAxisRightEnabled);
    void initLineChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType);
    void initBarChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType);
}
