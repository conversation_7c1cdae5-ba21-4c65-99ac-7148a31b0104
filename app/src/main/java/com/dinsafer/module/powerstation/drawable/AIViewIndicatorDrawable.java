package com.dinsafer.module.powerstation.drawable;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class AIViewIndicatorDrawable extends Drawable {

    private Paint mPaint;
    private float x, y, radius;
    private boolean isFill;

    public AIViewIndicatorDrawable(View view, int[] gradientColors, float[] gradientPositions) {
        this(view, gradientColors, gradientPositions, false);
    }

    public AIViewIndicatorDrawable(View view, int[] gradientColors, float[] gradientPositions, boolean isFill) {
        mPaint = new Paint();
        this.isFill = isFill;
        if (isFill) {
            mPaint.setStyle(Paint.Style.FILL);
        } else {
            mPaint.setStyle(Paint.Style.STROKE);
        }
        mPaint.setStrokeWidth(3);
        mPaint.setAntiAlias(true);
        x = view.getWidth() / 2f;
        y = view.getHeight() / 2f;
        radius = view.getWidth() / 2f;
        LinearGradient mGradient = new LinearGradient(
                0, view.getHeight(),
                view.getWidth(), view.getHeight() / 5f,
                gradientColors, gradientPositions,
                Shader.TileMode.CLAMP
        );
        mPaint.setShader(mGradient);
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        canvas.drawCircle(x, y, isFill ? radius : radius -  1.5f, mPaint);
    }

    @Override
    public void setAlpha(int alpha) {
        mPaint.setAlpha(alpha);
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
        mPaint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }
}
