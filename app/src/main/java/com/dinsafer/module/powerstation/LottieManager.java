package com.dinsafer.module.powerstation;

import android.animation.Animator;
import android.animation.ValueAnimator;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;

public class LottieManager {

    public static final int SOLAR_NULL = 0;
    public static final int SOLAR_LOW = 1;
    public static final int SOLAR_HIGH = 2;

    public static final int VEHICLE_NULL = 1;
    public static final int VEHICLE_WAIT = 2;
    public static final int VEHICLE_CHARGING = 3;
    //    public static final int VEHICLE_UNAUTHORIZED = 2;
    public static final int VEHICLE_ERROR = 4;

    public static final int KEEP_ON_LOAD_OFF = 0;
    public static final int KEEP_ON_LOAD_ON = 1;

    public static final int ADDITION_LOAD_OFF = 0;
    public static final int ADDITION_LOAD_ON = 1;

    public static final int DUAL_POWER_OFF = 0;
    public static final int DUAL_POWER_ON = 1;

    public static final int BALANCING_POWER_OFF = 0;
    public static final int BALANCING_POWER_ON = 1;

    public static final int EV_OFF = 0;
    public static final int EV_WAITING = 1;
    public static final int EV_CHARGING = 2;
    public static final int EV_ERROR = 3;
    public static final int EV_UNAUTHORIZED = 4;
    private static final float EV_MAX_FRAME = 993f;

    private int mSolarStatus = SOLAR_NULL;
    private int mVehicleStatus = VEHICLE_NULL;
    private int mKeepOnLoadStatus = KEEP_ON_LOAD_OFF;
    private int mAdditionLoadStatus = ADDITION_LOAD_OFF;
    private int mDualPowerStatus = DUAL_POWER_OFF;
    private int mBalancingPowerStatus = BALANCING_POWER_OFF;
    private int mEVStatus = EV_OFF;

    private AnimBalanceState mBalancingModeStatus = AnimBalanceState.END;

    private EVAnimationListener mEVAnimationListener;

    public LottieManager() {

    }

    /**
     * 1. 极低效——高效：0-90
     * 2. 极低效——低效：0-145
     * 3. 高效——极低效：90-0
     * 4. 高效——低效：90-145
     * 5. 低效——极低效：146-215
     * 6. 低效——高效：146-90
     *
     * @param solarAnim
     * @param status
     */
    public void controlSolarAnim(LottieAnimationView solarAnim, int status) {
        if (status == mSolarStatus) return;
        mSolarStatus = status;
        switch (status) {
            case SOLAR_NULL:
                solarAnim.cancelAnimation();
                solarAnim.setFrame(0);
                break;

            case SOLAR_LOW:
                solarAnim.setMinAndMaxFrame(0, 145);
                solarAnim.playAnimation();
                break;

            case SOLAR_HIGH:
                solarAnim.setMinAndMaxFrame(0, 90);
                solarAnim.playAnimation();
                break;
        }
    }

    /**
     * 1. 不工作——充电中：0-96
     * 2. 不工作——非法车辆：144-432
     * 3. 不工作——EV异常：540-888
     * 4. 不工作——等待充电：1074-1180
     * 5. 充电中——不工作：97-143
     * 6. 充电中——非法车辆：97-432
     * 7. 充电中——EV异常：97-143，540-888
     * 8. 充电中——等待充电：97-143，1074-1180
     * 9. 非法车辆——不工作：433-539
     * 10. 非法车辆——充电中：433-539，0-96
     * 11. 非法车辆——EV异常：433-888
     * 12. 非法车辆——等待充电：433-539，1074-1180
     * 13. EV异常——不工作：889-1080
     * 14. EV异常——充电中：889-1060，0-96
     * 15. EV异常——非法车辆：889-1080，144-432
     * 16. EV异常——等待充电：889-1200
     * 17. 等待充电——不工作：1200-1280
     * 18. 等待充电——充电中：1200-1280，0-96
     * 19. 等待充电——非法车辆：1200-1280，144-432
     * 20. 等待充电——EV异常：1200-1280，540-888
     *
     * @param vehicleAnim
     * @param status
     */
    public void controlVehicleAnim(LottieAnimationView vehicleAnim, int status) {
        if (status == mVehicleStatus) return;
        switch (status) {
            case VEHICLE_NULL:
                if (mVehicleStatus == VEHICLE_WAIT) {
                    vehicleAnim.setMinAndMaxFrame(1200, 1280);
                } else if (mVehicleStatus == VEHICLE_CHARGING) {
                    vehicleAnim.setMinAndMaxFrame(97, 143);
                } else if (mVehicleStatus == VEHICLE_ERROR) {
                    vehicleAnim.setMinAndMaxFrame(889, 1080);
                } else {
                    vehicleAnim.setMinAndMaxFrame(0, 0);
                }
                vehicleAnim.playAnimation();
                break;

            case VEHICLE_WAIT:
                if (mVehicleStatus == VEHICLE_NULL) {
                    vehicleAnim.setMinAndMaxFrame(1074, 1180);
                    vehicleAnim.playAnimation();
                } else if (mVehicleStatus == VEHICLE_CHARGING) {
                    vehicleAnim.setMinAndMaxFrame(433, 539);
                    vehicleAnim.playAnimation();
                    vehicleAnim.setMinAndMaxFrame(1074, 1180);
                    vehicleAnim.playAnimation();
                } else if (mVehicleStatus == VEHICLE_ERROR) {
                    vehicleAnim.setMinAndMaxFrame(889, 1200);
                    vehicleAnim.playAnimation();
                }
                break;

            case VEHICLE_CHARGING:
                if (mVehicleStatus == VEHICLE_NULL) {
                    vehicleAnim.setMinAndMaxFrame(0, 96);
                    vehicleAnim.playAnimation();
                } else if (mVehicleStatus == VEHICLE_WAIT) {
                    vehicleAnim.setMinAndMaxFrame(1200, 1280);
                    vehicleAnim.playAnimation();
                    vehicleAnim.setMinAndMaxFrame(0, 96);
                    vehicleAnim.playAnimation();
                } else if (mVehicleStatus == VEHICLE_ERROR) {
                    vehicleAnim.setMinAndMaxFrame(889, 1060);
                    vehicleAnim.playAnimation();
                    vehicleAnim.setMinAndMaxFrame(0, 96);
                    vehicleAnim.playAnimation();
                }
                break;

//            case VEHICLE_UNAUTHORIZED:
//                vehicleAnim.setMinFrame(144);
//                vehicleAnim.setMaxFrame(432);
//                vehicleAnim.playAnimation();
//                break;

            case VEHICLE_ERROR:
                if (mVehicleStatus == VEHICLE_NULL) {
                    vehicleAnim.setMinAndMaxFrame(540, 888);
                    vehicleAnim.playAnimation();
                } else if (mVehicleStatus == VEHICLE_WAIT) {
                    vehicleAnim.setMinAndMaxFrame(1200, 1280);
                    vehicleAnim.playAnimation();
                    vehicleAnim.setMinAndMaxFrame(540, 888);
                    vehicleAnim.playAnimation();
                } else if (mVehicleStatus == VEHICLE_CHARGING) {
                    vehicleAnim.setMinAndMaxFrame(97, 143);
                    vehicleAnim.playAnimation();
                    vehicleAnim.setMinAndMaxFrame(540, 888);
                    vehicleAnim.playAnimation();
                }
                break;

        }
        mVehicleStatus = status;
    }

    /**
     * 安全供应
     *
     * @param keepOnLoadAnimView
     * @param status
     */
    public void controlKeepOnLoad(LottieAnimationView keepOnLoadAnimView, int status) {
        if (status == mKeepOnLoadStatus) return;
        mKeepOnLoadStatus = status;
        switch (status) {
            case KEEP_ON_LOAD_OFF:
                keepOnLoadAnimView.setMinAndMaxFrame(0, 100);
                keepOnLoadAnimView.setSpeed(-1.0f);
                keepOnLoadAnimView.playAnimation();
                break;

            case KEEP_ON_LOAD_ON:
                keepOnLoadAnimView.setMinAndMaxFrame(0, 100);
                keepOnLoadAnimView.setSpeed(1.0f);
                keepOnLoadAnimView.playAnimation();
                break;
        }
    }

    /**
     * 额外负载
     *
     * @param additionLoadAniView
     * @param status
     */
    public void controlAdditionLoad(LottieAnimationView additionLoadAniView, int status) {
        if (status == mAdditionLoadStatus) return;
        mAdditionLoadStatus = status;
        switch (status) {
            case ADDITION_LOAD_OFF:
                additionLoadAniView.setMinAndMaxFrame(0, 100);
                additionLoadAniView.setSpeed(-1.0f);
                additionLoadAniView.playAnimation();
                break;

            case ADDITION_LOAD_ON:
                additionLoadAniView.setMinAndMaxFrame(0, 100);
                additionLoadAniView.setSpeed(1.0f);
                additionLoadAniView.playAnimation();
                break;
        }
    }

    /**
     * 复合电力
     *
     * @param dualPowerAniView
     * @param status
     */
    public void controlDualPower(LottieAnimationView dualPowerAniView, int status) {
        if (status == mDualPowerStatus) return;
        mDualPowerStatus = status;
        switch (status) {
            case DUAL_POWER_OFF:
                dualPowerAniView.setMinAndMaxFrame(0, 100);
                dualPowerAniView.setSpeed(-1.0f);
                dualPowerAniView.playAnimation();
                break;

            case DUAL_POWER_ON:
                dualPowerAniView.setMinAndMaxFrame(0, 100);
                dualPowerAniView.setSpeed(1.0f);
                dualPowerAniView.playAnimation();
                break;
        }
    }

    /**
     * 调频电力
     *
     * @param balancingPowerAniView
     * @param status
     */
    public void controlBalancingPower(LottieAnimationView balancingPowerAniView, int status) {
        if (status == mBalancingPowerStatus) return;
        mBalancingPowerStatus = status;
        switch (status) {
            case BALANCING_POWER_OFF:
                balancingPowerAniView.setMinAndMaxFrame(0, 100);
                balancingPowerAniView.setSpeed(-1.0f);
                balancingPowerAniView.playAnimation();
                break;

            case BALANCING_POWER_ON:
                balancingPowerAniView.setMinAndMaxFrame(0, 100);
                balancingPowerAniView.setSpeed(1.0f);
                balancingPowerAniView.playAnimation();
                break;
        }
    }


    /**
     * 调频模式
     *
     * @param balancingModeAnimView
     * @param status                调频开始:0-19
     *                              <p>
     *                              调频中:19-39
     *                              <p>
     *                              调频结束:39-60
     */
    public void controlBalancingMode(LottieAnimationView balancingModeAnimView, AnimBalanceState status) {
        if (status == mBalancingModeStatus) {
            return;
        }
        mBalancingModeStatus = status;
        switch (mBalancingModeStatus) {
            case STATE:
                balancingModeAnimView.setMinAndMaxFrame(0, 19);
                balancingModeAnimView.setRepeatCount(0);
                break;
            case ING:
                balancingModeAnimView.setMinAndMaxFrame(19, 39);
                balancingModeAnimView.setRepeatCount(ValueAnimator.INFINITE);
                break;
            case END:
                balancingModeAnimView.setMinAndMaxFrame(39, 60);
                balancingModeAnimView.setRepeatCount(0);
                break;
        }
        balancingModeAnimView.setSpeed(1.0f);
        balancingModeAnimView.playAnimation();
    }

    /**
     * 控制车充
     *
     * @param evAniView
     * @param status    状态切换对应帧数
     *                  **off----charging:** 870-840, 505-530, 725-800
     *                  <p>
     *                  **off----waiting for charge:** 870-840, 505-700
     *                  <p>
     *                  **off----error:** 870-840, 505-480, 215-390
     *                  <p>
     *                  **off----unauthorized:** 870-840, 505-480, 10-155
     *                  <p>
     *                  **charging----off:** 800-870
     *                  <p>
     *                  **charging----waiting for charge:** 800-840, 505-700
     *                  <p>
     *                  **charging----error:** 800-840, 505-480, 215-390
     *                  <p>
     *                  **charging----unauthorized:** 800-840, 505-480, 10-155
     *                  <p>
     *                  **waiting for charge----off:** 700-725, 540-505, 840-870
     *                  <p>
     *                  **waiting for charge----charging:** 700-800
     *                  <p>
     *                  **waiting for charge----error:** 700-725, 540-480, 215-390
     *                  <p>
     *                  **waiting for charge----unauthorized:** 700-725, 540-480, 10-155
     *                  <p>
     *                  **error----off:** 390-505, 840-870
     *                  <p>
     *                  **error----charging:** 390-530, 725-800
     *                  <p>
     *                  **error----waiting for charge:** 390-700
     *                  <p>
     *                  **error----unauthorized:** 390-480, 10-155
     *                  <p>
     *                  **unauthorized----off:** 155-215, 480-505, 840-870
     *                  <p>
     *                  **unauthorized----charging:** 155-215, 480-530, 725-800
     *                  <p>
     *                  **unauthorized----waiting for charge:** 155-215, 480-700
     *                  <p>
     *                  **unauthorized----error:** 155-390
     *                  <p>
     *                  状态常驻循环播放对应帧数（动画样式以json文件为准，可与设计稿不一致）
     *                  **off(静态):** 870
     *                  <p>
     *                  **charging loop:** 750-800
     *                  <p>
     *                  **waiting for charge loop:** 570-705
     *                  <p>
     *                  **error loop:** 330-390
     *                  <p>
     *                  **unauthorized loop:** 115-160
     */
    public void controlEV(LottieAnimationView evAniView, int status) {

        if (status == mEVStatus) return;
        if (mEVAnimationListener == null) {
            mEVAnimationListener = new EVAnimationListener(evAniView);
            evAniView.addAnimatorListener(mEVAnimationListener);
        }
        mEVAnimationListener.setOldStatus(mEVStatus);
        mEVAnimationListener.setNewStatus(status);
        mEVAnimationListener.setStep(1);
        evAniView.cancelAnimation();
        evAniView.setRepeatCount(0);
        if (status == EV_OFF) {
            switch (mEVStatus) {
                case EV_WAITING:
                    evAniView.setMinAndMaxProgress(700 / EV_MAX_FRAME, 725 / EV_MAX_FRAME);
                    evAniView.playAnimation();
                    break;

                case EV_CHARGING:
                    evAniView.setMinAndMaxProgress(800 / EV_MAX_FRAME, 870 / EV_MAX_FRAME);
                    evAniView.playAnimation();
                    break;

                case EV_ERROR:
                    evAniView.setMinAndMaxProgress(390 / EV_MAX_FRAME, 505 / EV_MAX_FRAME);
                    evAniView.playAnimation();
                    break;
            }
        } else if (status == EV_CHARGING) {
            switch (mEVStatus) {
                case EV_OFF:
                    evAniView.setMinAndMaxProgress(840 / EV_MAX_FRAME, 870 / EV_MAX_FRAME);
                    evAniView.setSpeed(-1.0f);
                    evAniView.playAnimation();
                    break;

                case EV_WAITING:
                    evAniView.setMinAndMaxProgress(700 / EV_MAX_FRAME, 800 / EV_MAX_FRAME);
                    evAniView.setSpeed(1.0f);
                    evAniView.playAnimation();
                    break;

                case EV_ERROR:
                    evAniView.setMinAndMaxProgress(390 / EV_MAX_FRAME, 530 / EV_MAX_FRAME);
                    evAniView.setSpeed(1.0f);
                    evAniView.playAnimation();
                    break;
            }
        } else if (status == EV_WAITING) {
            switch (mEVStatus) {
                case EV_OFF:
                    evAniView.setMinAndMaxProgress(840 / EV_MAX_FRAME, 870 / EV_MAX_FRAME);
                    evAniView.setSpeed(-1.0f);
                    evAniView.playAnimation();
                    break;

                case EV_CHARGING:
                    evAniView.setMinAndMaxProgress(800 / EV_MAX_FRAME, 840 / EV_MAX_FRAME);
                    evAniView.setSpeed(1.0f);
                    evAniView.playAnimation();
                    break;

                case EV_ERROR:
                    evAniView.setMinAndMaxProgress(390 / EV_MAX_FRAME, 700 / EV_MAX_FRAME);
                    evAniView.setSpeed(1.0f);
                    evAniView.playAnimation();
                    break;
            }
        } else if (status == EV_ERROR) {
            switch (mEVStatus) {
                case EV_OFF:
                    evAniView.setMinAndMaxProgress(840 / EV_MAX_FRAME, 870 / EV_MAX_FRAME);
                    evAniView.setSpeed(-1.0f);
                    evAniView.playAnimation();
                    break;

                case EV_WAITING:
                    evAniView.setMinAndMaxProgress(700 / EV_MAX_FRAME, 725 / EV_MAX_FRAME);
                    evAniView.setSpeed(1.0f);
                    evAniView.playAnimation();
                    break;


                case EV_CHARGING:
                    evAniView.setMinAndMaxProgress(800 / EV_MAX_FRAME, 840 / EV_MAX_FRAME);
                    evAniView.setSpeed(1.0f);
                    evAniView.playAnimation();
                    break;
            }
        }
        mEVStatus = status;
    }

    public static class EVAnimationListener implements Animator.AnimatorListener {

        private final LottieAnimationView evAniView;

        private int oldStatus = EV_OFF;
        private int newStatus = EV_OFF;
        private int step;

        public EVAnimationListener(LottieAnimationView evAniView) {
            this.evAniView = evAniView;
        }

        public int getOldStatus() {
            return oldStatus;
        }

        public void setOldStatus(int oldStatus) {
            this.oldStatus = oldStatus;
        }

        public int getNewStatus() {
            return newStatus;
        }

        public void setNewStatus(int newStatus) {
            this.newStatus = newStatus;
        }

        public int getStep() {
            return step;
        }

        public void setStep(int step) {
            this.step = step;
        }


        @Override
        public void onAnimationStart(Animator animator) {

        }

        @Override
        public void onAnimationEnd(Animator animator) {
            if (newStatus == EV_OFF) {
                switch (oldStatus) {
                    case EV_WAITING:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(505 / EV_MAX_FRAME, 540 / EV_MAX_FRAME);
                            evAniView.setSpeed(-1.0f);
                            step = 2;
                            evAniView.playAnimation();
                        } else if (step == 2) {
                            evAniView.setMinAndMaxProgress(840 / EV_MAX_FRAME, 870 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;

                    case EV_CHARGING:
                        if (step == 1) {
                            step = 0;
                        }
                        break;

                    case EV_ERROR:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(840 / EV_MAX_FRAME, 870 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(0);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;
                }
            } else if (newStatus == EV_CHARGING) {
                switch (oldStatus) {
                    case EV_OFF:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(505 / EV_MAX_FRAME, 530 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(0);
                            step = 2;
                            evAniView.playAnimation();
                        } else if (step == 2) {
                            evAniView.setMinAndMaxProgress(725 / EV_MAX_FRAME, 800 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(0);
                            step = 3;
                            evAniView.playAnimation();
                        } else if (step == 3) {
                            evAniView.setMinAndMaxProgress(750 / EV_MAX_FRAME, 800 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(LottieDrawable.INFINITE);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;

                    case EV_WAITING:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(750 / EV_MAX_FRAME, 800 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(LottieDrawable.INFINITE);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;

                    case EV_ERROR:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(725 / EV_MAX_FRAME, 800 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(0);
                            step = 2;
                            evAniView.playAnimation();
                        } else if (step == 2) {
                            evAniView.setMinAndMaxProgress(750 / EV_MAX_FRAME, 800 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(LottieDrawable.INFINITE);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;
                }
            } else if (newStatus == EV_WAITING) {
                switch (oldStatus) {
                    case EV_OFF:
                    case EV_CHARGING:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(505 / EV_MAX_FRAME, 700 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(0);
                            step = 2;
                            evAniView.playAnimation();
                        } else if (step == 2) {
                            evAniView.setMinAndMaxProgress(570 / EV_MAX_FRAME, 705 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(LottieDrawable.INFINITE);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;

                    case EV_ERROR:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(570 / EV_MAX_FRAME, 705 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(LottieDrawable.INFINITE);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;
                }
            } else if (newStatus == EV_ERROR) {
                switch (oldStatus) {
                    case EV_OFF:
                    case EV_CHARGING:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(480 / EV_MAX_FRAME, 505 / EV_MAX_FRAME);
                            evAniView.setSpeed(-1.0f);
                            evAniView.setRepeatCount(0);
                            step = 2;
                            evAniView.playAnimation();
                        } else if (step == 2) {
                            evAniView.setMinAndMaxProgress(215 / EV_MAX_FRAME, 390 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(0);
                            step = 3;
                            evAniView.playAnimation();
                        } else if (step == 3) {
                            evAniView.setMinAndMaxProgress(330 / EV_MAX_FRAME, 390 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(LottieDrawable.INFINITE);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;

                    case EV_WAITING:
                        if (step == 1) {
                            evAniView.setMinAndMaxProgress(480 / EV_MAX_FRAME, 540 / EV_MAX_FRAME);
                            evAniView.setSpeed(-1.0f);
                            evAniView.setRepeatCount(0);
                            step = 2;
                            evAniView.playAnimation();
                        } else if (step == 2) {
                            evAniView.setMinAndMaxProgress(215 / EV_MAX_FRAME, 390 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(0);
                            step = 3;
                            evAniView.playAnimation();
                        } else if (step == 3) {
                            evAniView.setMinAndMaxProgress(330 / EV_MAX_FRAME, 390 / EV_MAX_FRAME);
                            evAniView.setSpeed(1.0f);
                            evAniView.setRepeatCount(LottieDrawable.INFINITE);
                            step = 0;
                            evAniView.playAnimation();
                        }
                        break;
                }
            }
        }

        @Override
        public void onAnimationCancel(Animator animator) {

        }

        @Override
        public void onAnimationRepeat(Animator animator) {

        }
    }

    public enum AnimBalanceState {
        STATE,
        ING,
        END
    }

}
