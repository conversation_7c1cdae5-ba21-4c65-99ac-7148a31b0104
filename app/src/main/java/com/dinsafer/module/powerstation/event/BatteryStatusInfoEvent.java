package com.dinsafer.module.powerstation.event;

public class BatteryStatusInfoEvent {

    private String deviceId;
    private String subcategory;
    private int socState;

    public BatteryStatusInfoEvent(int socState) {
        this.socState = socState;
    }

    public BatteryStatusInfoEvent(String deviceId, String subcategory, int socState) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.socState = socState;
    }

    public int getSocState() {
        return socState;
    }

    public void setSocState(int socState) {
        this.socState = socState;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }
}
