package com.dinsafer.module.powerstation.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class PSEVChargeV2Bean implements Parcelable {

    public static final int LOWER_UTILITY_RATE = 1;
    public static final int SCHEDULE_CHARGE = 3;
    public static final int INSTANT_CHARGE_FULL = 4;
    public static final int INSTANT_CHARGE_FIXED = 5;

    private boolean selected;
    private String title;
    private String subTitle;
    // LOWER_UTILITY_RATE SCHEDULE_CHARGE INSTANT_CHARGE_FULL INSTANT_CHARGE_FIXED 中一种
    private int type;
    private int value;
    private int maxValue = 100;
    private int tempValue;
    // -1 未应用  0 应用中  1 已应用
    private int status = -1;
    private int fixedStatus = -1;
    private boolean isSameSelected = true;
    private boolean isAdd = true;

    public PSEVChargeV2Bean(String title, String subTitle, int type) {
        this.title = title;
        this.subTitle = subTitle;
        this.type = type;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        setSameSelected(this.selected == selected);
        this.selected = selected;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(int maxValue) {
        this.maxValue = maxValue;
    }

    public int getTempValue() {
        return tempValue;
    }

    public void setTempValue(int tempValue) {
        this.tempValue = tempValue;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getFixedStatus() {
        return fixedStatus;
    }

    public void setFixedStatus(int fixedStatus) {
        this.fixedStatus = fixedStatus;
    }

    public boolean isSameSelected() {
        return isSameSelected;
    }

    public void setSameSelected(boolean sameSelected) {
        isSameSelected = sameSelected;
    }

    public boolean isAdd() {
        return isAdd;
    }

    public void setAdd(boolean add) {
        isAdd = add;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte(this.selected ? (byte) 1 : (byte) 0);
        dest.writeString(this.title);
        dest.writeString(this.subTitle);
        dest.writeInt(this.type);
        dest.writeInt(this.value);
        dest.writeInt(this.maxValue);
        dest.writeInt(this.tempValue);
        dest.writeInt(this.status);
        dest.writeInt(this.fixedStatus);
        dest.writeByte(this.isSameSelected ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isAdd ? (byte) 1 : (byte) 0);
    }

    public void readFromParcel(Parcel source) {
        this.selected = source.readByte() != 0;
        this.title = source.readString();
        this.subTitle = source.readString();
        this.type = source.readInt();
        this.value = source.readInt();
        this.maxValue = source.readInt();
        this.tempValue = source.readInt();
        this.status = source.readInt();
        this.fixedStatus = source.readInt();
        this.isSameSelected = source.readByte() != 0;
        this.isAdd = source.readByte() != 0;
    }

    protected PSEVChargeV2Bean(Parcel in) {
        this.selected = in.readByte() != 0;
        this.title = in.readString();
        this.subTitle = in.readString();
        this.type = in.readInt();
        this.value = in.readInt();
        this.maxValue = in.readInt();
        this.tempValue = in.readInt();
        this.status = in.readInt();
        this.fixedStatus = in.readInt();
        this.isSameSelected = in.readByte() != 0;
        this.isAdd = in.readByte() != 0;
    }

    public static final Creator<PSEVChargeV2Bean> CREATOR = new Creator<PSEVChargeV2Bean>() {
        @Override
        public PSEVChargeV2Bean createFromParcel(Parcel source) {
            return new PSEVChargeV2Bean(source);
        }

        @Override
        public PSEVChargeV2Bean[] newArray(int size) {
            return new PSEVChargeV2Bean[size];
        }
    };
}
