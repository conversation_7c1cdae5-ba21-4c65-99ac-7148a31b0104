package com.dinsafer.module.powerstation.drawable;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;
import android.view.View;

import com.dinsafer.util.DensityUtils;

public class AIBubbleDrawable extends Drawable {

    private final Paint mPaint;
    private final RectF mRectF;
    private final float radius;
    private float strokeWidth = 1f;

    public AIBubbleDrawable(View view, int[] gradientColors, float[] gradientPositions, float cornerRadius) {
        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth); // 边框宽度
        mPaint.setAntiAlias(true);
        int w = view.getWidth();
        int h = view.getHeight();
        float max = Math.max(w, h) * 1.0f;
        float scaleX = w / max;
        float scaleY = h / max;
        LinearGradient gradient = new LinearGradient(
                0, max,
                max, 0,
                gradientColors, gradientPositions,
                Shader.TileMode.CLAMP
        );
        Matrix matrix = new Matrix();
        matrix.setScale(scaleX, scaleY);
        gradient.setLocalMatrix(matrix);
        mPaint.setShader(gradient);

        mRectF = new RectF();
        this.radius = cornerRadius;
    }

    @Override
    public void draw(Canvas canvas) {
        // 获取当前Drawable的边界
        Rect bounds = getBounds();
        mRectF.set(bounds);
        canvas.drawRoundRect(mRectF.left + strokeWidth,
                mRectF.top + strokeWidth, mRectF.right - strokeWidth,
                mRectF.bottom - strokeWidth, radius, radius, mPaint);
    }

    @Override
    public void setAlpha(int alpha) {
        mPaint.setAlpha(alpha);
    }

    @Override
    public void setColorFilter(ColorFilter colorFilter) {
        mPaint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }
}
