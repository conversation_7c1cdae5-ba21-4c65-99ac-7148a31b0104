package com.dinsafer.module.powerstation.gridrewards;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Html;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsTerminateBinding;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.bean.GridRewardsAuthorization;
import com.dinsafer.module.powerstation.event.SignatureEvent;
import com.dinsafer.module.powerstation.settings.PSAdvancedSettingsFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.plugin.widget.view.LoadingFragment;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/29
 * @author: create by Sydnee
 */
public class PSTerminateFragment extends MyBaseFragment<FragmentPsTerminateBinding> {

    public final String TAG = this.getClass().getName();
    private String ENCRYPT_PATH = "";
    private String IMG_PATH = "";

    private boolean canConfirm = false;

    private Bitmap signBitmap;

    private BindMultiAdapter<TitleContentModel> mAdapter;
    private String homeName;
    private String homeId;
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;
    private GridRewardsAuthorization mAuthorization;
    private String mTerminateTemplateUrl;
    private int mFrom;
    private String mKey;

    public static PSTerminateFragment newInstanceFromContractDetail(String homeName, String homeId) {
        return newInstance(homeName, homeId, 0);
    }

    public static PSTerminateFragment newInstanceFromSetting(String homeName, String homeId) {
        return newInstance(homeName, homeId, 1);
    }

    public static PSTerminateFragment newInstance(String homeName, String homeId, int from) {
        PSTerminateFragment fragment = new PSTerminateFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_HOME_ID, homeId);
        bundle.putString(PSKeyConstant.KEY_HOME_NAME, homeName);
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_terminate;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.terminate));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.tvTitle.setLocalText(getString(R.string.terminate_title));
        mBinding.btnSign.setLocalText(getString(R.string.agree_sign));
        mBinding.btnResign.setLocalText(getString(R.string.re_sign));

        ENCRYPT_PATH = getContext().getCacheDir() + "secret";
        IMG_PATH = getContext().getCacheDir() + "secret.png";

        mBinding.btnSign.setAlpha(0.5f);
        mBinding.btnSign.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (canConfirm && signBitmap != null) {
                    showTerminateTipDialog();
                    return;
                }
                getDelegateActivity().addCommonFragment(SignatureFragment.newInstance());
            }
        });

        mBinding.btnResign.setOnClickListener(v -> getDelegateActivity().addCommonFragment(SignatureFragment.newInstance()));

    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        Bundle bundle = getArguments();
        if (null != bundle) {
            homeId = bundle.getString(PSKeyConstant.KEY_HOME_ID);
            homeName = bundle.getString(PSKeyConstant.KEY_HOME_NAME);
            mFrom = bundle.getInt(PSKeyConstant.KEY_FROM);
            if (TextUtils.isEmpty(homeId)) {
                removeSelf();
                return;
            }
            initRv();
            getBalanceContractUnsignTemplate();
        }

    }

    private void showTerminateTipDialog() {
        AlertDialogV2.createBuilder(getActivity())
                .setContent(Local.s(getString(R.string.terminate_tip)))
                .setCancel(Local.s(getString(R.string.terminate_contract)))
                .setOk(Local.s(getString(R.string.consider_later)))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        getDelegateActivity().removeToFragment(GridRewardsNotFirstFragment.class.getName());
                    }
                })
                .setCancelListener(() -> {
                    getUploadImageKey();
                })
                .preBuilder()
                .show();
    }

    private void showErrorTerminate() {
        AlertDialog.createBuilder(getContext())
                .setOk(getResources().getString(R.string.try_again))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        if (TextUtils.isEmpty(mKey)) {
                            getUploadImageKey();
                        } else {
                            toUnsign(mKey);
                        }
                    }
                })
                .setContent(getResources().getString(R.string.failed_try_again))
                .setIsSuccess(false)
                .setIsShowContentImageView(true)
                .setCancel(getString(R.string.cancel))
                .preBuilder()
                .show();
    }


    private void initRv() {
        mAdapter = new BindMultiAdapter<>();
        mBinding.rcvContent.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rcvContent.setAdapter(mAdapter);
    }

    private void getBalanceContractUnsignTemplate() {
        showTimeOutLoadinFramgment();
        DinHome.getInstance().getBalanceContractUnsignTemplate(homeId, new IDefaultCallBack2<BalanceContractUnsignTemplateResponse.ResultBean>() {
            @Override
            public void onSuccess(BalanceContractUnsignTemplateResponse.ResultBean resultBean) {
                DDLog.e(TAG, "getBalanceContractUnsignTemplate.  onSuccess: " + resultBean);
                closeLoadingFragment();
                if (resultBean != null) {
                    List<TitleContentModel> modelList = new ArrayList<>();
                    String context2 = Local.s(getString(R.string.terminate_family_name)) + ": " + homeName
                            + "\n" + Local.s(getString(R.string.terminate_family_id)) + ": " + homeId;
                    String context1 = Local.s(getString(R.string.terminate_context))
                            .replace("#first", "<u>" + (TextUtils.isEmpty(resultBean.getName()) ? resultBean.getCompany_name() : resultBean.getName()) + "</u>")
                            .replace("#second_info", "<u>" + resultBean.getStreet_name_and_number() + ", " + resultBean.getZip_code() + ", " + resultBean.getCity() + ", " + resultBean.getCountry_name() + "</u>")
                            .replace("#third_info", "<u>" + resultBean.getAuthorization_company_name() + "</u>")
                            .replace("#fourth_info", "<u>" + resultBean.getCountry_name() + "</u>")
                            .replace("#fifth_info", "<u>" + resultBean.getAuthorization_organization_number() + "</u>")
                            .replace("#sixth_info", "<u>" + resultBean.getAuthorization_address() + "</u>");

                    TitleContentModel contentModel = new TitleContentModel("", Html.fromHtml(context1), context2);
                    contentModel.setLineSpace(getResources().getDimensionPixelSize(R.dimen.contract_content_lineSpace));
                    modelList.add(contentModel);
                    mAdapter.setNewData(modelList);
                    mBinding.btnSign.setAlpha(1f);

                    mTerminateTemplateUrl = resultBean.getFamily_terminate_template_url();
                    mFamilyBalanceContractInfo = new FamilyBalanceContractInfo();
                    mFamilyBalanceContractInfo.setType(resultBean.getType());
                    mFamilyBalanceContractInfo.setCompany_name(resultBean.getCompany_name());
                    mFamilyBalanceContractInfo.setName(resultBean.getName());
                    mFamilyBalanceContractInfo.setEuVatNumber(resultBean.getEu_vat_number());
                    mFamilyBalanceContractInfo.setEmailAddress(resultBean.getEmail_address());
                    mFamilyBalanceContractInfo.setCountry_code(resultBean.getCountry_code());
                    mFamilyBalanceContractInfo.setCountryNameDisplay(resultBean.getCountry_name());
                    mFamilyBalanceContractInfo.setCity(resultBean.getCity());
                    mFamilyBalanceContractInfo.setZipCode(resultBean.getZip_code());
                    mFamilyBalanceContractInfo.setStreetNameAndNumber(resultBean.getStreet_name_and_number());
                    mFamilyBalanceContractInfo.setHome_id(homeId);
                    mFamilyBalanceContractInfo.setHome_name(homeName);

                    mAuthorization = new GridRewardsAuthorization(resultBean.getAuthorization_company_name(),
                            resultBean.getAuthorization_organization_number(), resultBean.getAuthorization_address(),
                            resultBean.getAuthorization_email_address());
                    return;
                }
                showErrorToast();
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "getBalanceContractUnsignTemplate.  onError: " + i + ":" + s);
                closeLoadingFragment();
                showErrorToast();
            }
        });
    }

    private void getUploadImageKey() {
        showLoadingFragment(LoadingFragment.BLACK, "");
        saveSign();
        DinHome.getInstance().getUploadImageKey(ENCRYPT_PATH, DinHome.getInstance().QIUNIU_PATH_KEY_CONTRACT, new IDefaultCallBack2<String>() {

            @Override
            public void onSuccess(String key) {
                DDLog.d(TAG, "getUploadImageKey: " + key);
                if (!TextUtils.isEmpty(key)) {
                    mKey = key;
                    toUnsign(key);
                    return;
                }
                closeLoadingFragment();
                showErrorTerminate();
            }

            @Override
            public void onError(int i, String s) {
                DDLog.d(TAG, "getUploadImageKey. onError: " + s);
                closeLoadingFragment();
                showErrorTerminate();
            }
        });

    }

    private void toUnsign(String sign) {
        DinHome.getInstance().terminateBalanceContract(homeId, sign, new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                closeLoadingFragment();
                showSucceedDialog();
            }

            @Override
            public void onError(int i, String s) {
                closeLoadingFragment();
                showErrorTerminate();
            }
        });
    }

    private void showSucceedDialog() {
        AlertDialog.createBuilder(getContext())
                .setContent(getString(R.string.terminate_suc))
                .setOk(getString(R.string.ok))
                .setContentGravity(Gravity.CENTER)
                .setContentLayoutMarginBottom(0)
                .setContentLayoutMarginTop(DensityUtils.dp2px(getContext(), 30))
                .setContentLayoutMinHeight(0)
                .setContentTextMinHeight(DensityUtils.dp2px(getContext(), 60))
                .setIsSuccess(true)
                .setIsShowContentImageView(true)
                .setOKListener(() -> {
                    if (mFrom == 0) {
                        getFamilyBalanceContractInfo();
                    } else {
                        getDelegateActivity().removeToFragment(PSAdvancedSettingsFragment.class.getName());
                    }
                })
                .preBuilder().show();
    }


    private void getFamilyBalanceContractInfo() {
        showTimeOutLoadinFramgment();
        DinHome.getInstance().getFamilyBalanceContractInfo(homeId,
                new IDefaultCallBack2<>() {
                    @Override
                    public void onSuccess(FamilyBalanceContractInfoResponse.ResultBean resultBean) {
                        closeLoadingFragment();
                        getDelegateActivity().removeAllCommonFragment();
                        if (null != resultBean && resultBean.isSigning()) {
                            getDelegateActivity().addCommonFragment(GridRewardsNotFirstFragment
                                    .newInstance(MainPanelHelper.getInstance().getBmtRegionCountryList(), resultBean));
                        } else {
                            getDelegateActivity().addCommonFragment(GridRewardsFragment
                                    .newInstance(MainPanelHelper.getInstance().getBmtRegionCountryList(), resultBean));
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }


    private void saveSign() {
        String out_file_path = DDGlobalEnv.getInstance().getImageFolder();
        File dir = new File(out_file_path);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File photo = new File(IMG_PATH);
        try {
            OutputStream stream = new FileOutputStream(photo);
            signBitmap.compress(Bitmap.CompressFormat.PNG, 80, stream);
            stream.close();
            DDSecretUtil.encryptPNG(IMG_PATH, ENCRYPT_PATH);
        } catch (Exception e) {
            e.printStackTrace();
            closeLoadingFragment();
            showErrorToast();
        }
    }


    private void refreshUI(Bitmap bitmap) {
        mBinding.imgSign.setImageBitmap(bitmap);
        Drawable drawable = mBinding.imgSign.getDrawable();
        Drawable wrap = DrawableCompat.wrap(drawable);
        DrawableCompat.setTint(wrap, ContextCompat.getColor(getContext(), R.color.white));
        mBinding.imgSign.setImageDrawable(wrap);
        mBinding.imgSign.setVisibility(View.VISIBLE);

        String clickablePart = Local.s(getString(R.string.Preview));
        String fullText = "\"" + Local.s(getString(R.string.terminate_preview)) + "\" " + clickablePart;
        SpannableString spannableString = new SpannableString(fullText);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                // 处理点击事件
                toPreview();
            }

            @Override
            public void updateDrawState(android.text.TextPaint ds) {
                super.updateDrawState(ds);
                ds.setUnderlineText(false);
                ds.setColor(getResColor(R.color.color_brand_text));
            }
        };

        int start = fullText.indexOf(clickablePart);
        int end = start + clickablePart.length();
        spannableString.setSpan(clickableSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mBinding.tvPreview.setText(spannableString);
        mBinding.tvPreview.setVisibility(View.VISIBLE);
        mBinding.tvPreview.setMovementMethod(LinkMovementMethod.getInstance());

        mBinding.btnResign.setVisibility(View.VISIBLE);
        mBinding.btnSign.setLocalText(getString(R.string.agree_confirm));
    }


    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(SignatureEvent event) {
        if (event.getBitmap() == null) {
            canConfirm = false;
            return;
        }
        canConfirm = true;
        signBitmap = event.getBitmap();
        refreshUI(signBitmap);
    }

    private void toPreview() {
        if (signBitmap == null) return;
        String signBase64 = DDImageUtil.bitmap2Base64(signBitmap, 80);
        getDelegateActivity().addCommonFragment(PSContractPreviewFragment.newInstance(1, mTerminateTemplateUrl, signBase64, mFamilyBalanceContractInfo, mAuthorization));
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
