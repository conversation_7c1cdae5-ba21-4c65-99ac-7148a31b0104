package com.dinsafer.module.powerstation.electricity.chart.marker;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.util.Local;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;

import java.util.List;

public class RevenueMarkerView extends CustomCombinedMarkerView {

    public RevenueMarkerView(Context context) {
        super(context);
    }

    @Override
    public void setMarker(List<Entry> entries, List<Highlight> highlights) {
        setRevenueMarker(entries, highlights);
    }

    private void setRevenueMarker(List<Entry> entries, List<Highlight> highlights) {
        llSubValue.setVisibility(GONE);
        float value = entries.get(0).getVal();
        String valStr = ChartDataUtil.getPowerTransferVal(value, value, false) + getUnit(value, true);
        tvKey.setLocalText(Local.s(mContext.getString(R.string.total)));
        tvValue.setLocalText(valStr);
        int index = entries.get(0).getXIndex();
        switch (BaseChartFragment.mCycleType) {
            case WEEK:
                if (index >= BaseChartFragment.mWeeks.length) return;
                tvTime.setLocalText(BaseChartFragment.mWeeks[index]);
                break;

            case MONTH:
                if (index >= BaseChartFragment.months.length) return;
                tvTime.setLocalText(BaseChartFragment.months[index]);
                break;

            case YEAR:
                if (index >= BaseChartFragment.mYears.length) return;
                tvTime.setLocalText(BaseChartFragment.mYears[index]);
                break;

            case LIFETIME:
                if (index >= BaseChartFragment.lifetimes.length) return;
                tvTime.setLocalText(BaseChartFragment.lifetimes[index]);
                break;
        }
    }
}
