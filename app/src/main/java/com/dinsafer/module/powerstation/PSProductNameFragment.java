package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.config.AppConfig;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsProductNameBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.adapter.PSProductNameModel;
import com.dinsafer.module.powerstation.settings.network.PSNetworkSettingsFragment;
import com.dinsafer.module_bmt.add.BaseBmtBinder;
import com.dinsafer.module_bmt.add.BmtHP5000Binder;
import com.dinsafer.module_bmt.add.BmtHP5001Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore20Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore30Binder;
import com.dinsafer.module_bmt.add.BmtPowerPulseBinder;
import com.dinsafer.module_bmt.add.BmtPowerStoreBinder;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.List;

public class PSProductNameFragment extends MyBaseFragment<FragmentPsProductNameBinding> {

    private BindMultiAdapter<PSProductNameModel> mAdapter;
    private List<PSProductNameModel> mData;
    private String currentID;
    private BaseBmtBinder mBinder;

    public static PSProductNameFragment newInstance(String currentID) {
        PSProductNameFragment fragment = new PSProductNameFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_CURRENT_ID, currentID);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_product_name;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        currentID = getArguments().getString(PSKeyConstant.KEY_CURRENT_ID);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.product_name));
        mBinding.commonBar.vDivider.setVisibility(View.GONE);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        setSubmitEnabled(false);
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof BmtHP5000Binder) && !(pluginBinder instanceof BmtHP5001Binder)
                && !(pluginBinder instanceof BmtPowerCore20Binder)
                && !(pluginBinder instanceof BmtPowerCore30Binder)
                && !(pluginBinder instanceof BmtPowerStoreBinder)
                && !(pluginBinder instanceof BmtPowerPulseBinder)) {
            DDLog.e(TAG, "Error bmtBinder binder.");
            showErrorToast();
            return;
        }
        mBinder = (BaseBmtBinder) pluginBinder;
        mBinder.setModelCallback(new BaseBmtBinder.IModelCallback() {
            @Override
            public void onModelCallback(String cmd, int status, String result) {
                if (cmd != null && cmd.equals("get_valid_models")) {
                    if (status == 1) {
                        closeLoadingFragment();
                        updateDataEnabled(result);
                    } else if (status == 2) {
                        updateDataEnabled(result);
                    } else {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                }
            }
        });
        initRV();
//        getValidModels();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.lcbConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String model = getCheckedModel();
                if (!TextUtils.isEmpty(model)) {
                    mBinder.setModel(model);
                }
                getDelegateActivity().addCommonFragment(PSNetworkSettingsFragment.newInstanceForScanAdd(currentID, model, true, true));
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mBinder) {
            mBinder.stop();
        }
    }

    private String getCheckedModel() {
        for (PSProductNameModel productNameModel : mData) {
            if (productNameModel.isChecked()) {
                return productNameModel.getModel();
            }
        }
        return null;
    }

    private void updateDataEnabled(String result) {
        if (TextUtils.isEmpty(result)) return;
        for (int i = 0; i < mData.size(); i++) {
            PSProductNameModel productNameModel = mData.get(i);
            if (productNameModel.getModel().equals(result)) {
                productNameModel.setEnabled(true);
                mAdapter.notifyItemChanged(i);
            }
        }
    }

    private void initRV() {
        mBinding.rvProduct.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new BindMultiAdapter<>();
        mData = new ArrayList<>();
        getSupportData();
        mAdapter.setNewData(mData);
        mBinding.rvProduct.setAdapter(mAdapter);
        mAdapter.setOnBindItemClickListener(new OnBindItemClickListener<PSProductNameModel>() {
            @Override
            public void onItemClick(View v, int position, PSProductNameModel model) {
                if (model.isChecked()) return;
                for (PSProductNameModel productNameModel : mData) {
                    productNameModel.setChecked(false);
                }
                model.setChecked(true);
                mAdapter.notifyDataSetChanged();
                setSubmitEnabled(true);
            }
        });
    }

    private void getSupportData() {

        if (AppConfig.Plugins.SUPPORT_BMT_POWERSTORE) {
            mData.add(new PSProductNameModel(getContext(), R.drawable.img_product_ps1,
                    getString(R.string.power_store), DinConst.TYPE_BMT_POWERSTORE, mBinder.isOnePointFive(), true));
        }

        if (AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE) {
            mData.add(new PSProductNameModel(getContext(), R.drawable.img_product_vb1,
                    getString(R.string.power_pulse), DinConst.TYPE_BMT_POWERPULSE, (!mBinder.isOldUUID() && mBinder.isOnePointFive()), true));
        }

//        if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE30) {
//            mData.add(new PSProductNameModel(getContext(), R.drawable.img_product_power_station,
//                    getString(R.string.power_core_3_point_0), DinConst.TYPE_BMT_POWERCORE30, mBinder.isThreePointZero(), true));
//        }

        if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE20) {
            mData.add(new PSProductNameModel(getContext(), R.drawable.img_product_power_station,
                    getString(R.string.power_core_2_point_0), DinConst.TYPE_BMT_POWERCORE20, mBinder.isTwoPointZero(), true));
        }

        if (AppConfig.Plugins.SUPPORT_BMT_HP5000) {
            mData.add(new PSProductNameModel(getContext(), R.drawable.img_product_power_station,
                    getString(R.string.power_core_1_point_0), DinConst.TYPE_BMT_HP5000, mBinder.isOnePointZero(), false));
        }
    }

    private void setSubmitEnabled(boolean enabled) {
        mBinding.lcbConfirm.setEnabled(enabled);
        mBinding.lcbConfirm.setAlpha(enabled ? 1.0f : 0.5f);
    }

    private void getValidModels() {
        if (mBinder != null) {
            showTimeOutLoadinFramgment();
            mBinder.getValidModels();
        } else {
            showErrorToast();
        }
    }
}
