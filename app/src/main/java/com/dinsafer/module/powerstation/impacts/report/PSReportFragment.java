package com.dinsafer.module.powerstation.impacts.report;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.common.utils.KeyBoardUtil;
import com.dinsafer.dialog.KeyboardHeightHelper;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsReportBinding;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.impacts.report.adapter.PSQuickReplyAdapter;
import com.dinsafer.module.powerstation.impacts.report.adapter.PSReportAdapter;
import com.dinsafer.module.powerstation.impacts.report.bean.IMessageInfo;
import com.dinsafer.module.powerstation.impacts.report.bean.TextMessageInfo;
import com.dinsafer.module.powerstation.widget.SpaceItemDecoration;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class PSReportFragment extends MyBaseFragment<FragmentPsReportBinding> {

    private String mDeviceId;
    private String subcategory;
    private Device mPSDevice;
    private String timezone;
    private KeyboardHeightHelper mKeyboardHeightHelper;
    private PSReportAdapter mAdapter;
    private PSQuickReplyAdapter mQuickReplyAdapter;
    private List<String> mQuickReplyMsgList;
    private static final String GREETING_WORD = "Hi, I am your AI Assistant. Feel free to share your thoughts on AI mode.";
    private static final String SEND_SUCCESS = "Thanks for your feedback. The AI of device is getting smarter and more accurate just for you.";
    private static final String SEND_FAIL = "Failed. Try again.";

    public static PSReportFragment newInstance(String deviceId, String subcategory, String timezone) {
        PSReportFragment fragment = new PSReportFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putString(PSKeyConstant.TIMEZONE, timezone);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_report;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        getDelegateActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.Report));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> {
            KeyBoardUtil.closeKeybord(getContext(), mBinding.layoutSend.etMsg);
            removeSelf();
        });
        mBinding.layoutSend.etMsg.setHint(Local.s(getString(R.string.Other_feedback)));
        mKeyboardHeightHelper = new KeyboardHeightHelper(getDelegateActivity());
        mKeyboardHeightHelper.setHeightChangeListener(this::dealKeyBoard);
        mKeyboardHeightHelper.create();
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
            subcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
            mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, subcategory);
            timezone = bundle.getString(PSKeyConstant.TIMEZONE);
            initRV();
            initQuickReplyRV();
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.rvMsg.setOnTouchListener((v, event) -> {
            toCloseInput();
            return false;
        });
        mBinding.layoutSend.etMsg.setOnTouchListener((v, event) -> {
            scrollToLast();
            return false;
        });
        mBinding.layoutSend.etMsg.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String text = s.toString();
                setSendEnabled(!TextUtils.isEmpty(text));
            }
        });
        setSendEnabled(false);
        mBinding.layoutSend.ivSend.setOnClickListener(v -> {
            String text = mBinding.layoutSend.etMsg.getText().toString();
            mAdapter.getMessageInfoList().add(new TextMessageInfo(IMessageInfo.SEND_TEXT, getTime(), text, 0));
            sendMsg(text, mAdapter.getMessageInfoList().size() - 1);
            mBinding.layoutSend.etMsg.setText("");
            KeyBoardUtil.closeKeybord(getContext(), mBinding.layoutSend.etMsg);
            scrollToLast();
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        getDelegateActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
    }

    private void setSendEnabled(boolean enabled) {
        mBinding.layoutSend.ivSend.setEnabled(enabled);
        mBinding.layoutSend.ivSend.setClickable(enabled);
        mBinding.layoutSend.ivSend.setImageResource(enabled ?
                R.drawable.icon_send_enable : R.drawable.icon_send_disable);
    }

    private void setEditMsgEnabled(boolean enabled) {
        mBinding.layoutSend.etMsg.setEnabled(enabled);
        mBinding.layoutSend.llMsg.setAlpha(enabled ? 1.0f : 0.5f);
    }

    private void sendMsg(String msg, int position) {
        setEditMsgEnabled(false);
        DinsafeAPI.getApi().bmtFeedback(mDeviceId, subcategory, msg).enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                StringResponseEntry body = response.body();
                if (body != null && body.getResult() != null) {
                    sendResult(true, position);
                    setEditMsgEnabled(true);
                } else {
                    sendResult(false, position);
                    setEditMsgEnabled(true);
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                sendResult(false, position);
                setEditMsgEnabled(true);
            }
        });
    }

    /**
     * 根据键盘高度处理页面显示
     *
     * @param height
     * @param y
     */
    private void dealKeyBoard(int height, int y) {
        if (height < 0) height = 0;
        RelativeLayout.LayoutParams llParams = (RelativeLayout.LayoutParams) mBinding.llContent.getLayoutParams();
        llParams.bottomMargin = height;
        mBinding.llContent.setLayoutParams(llParams);
        if (mAdapter == null || mAdapter.getMessageInfoList() == null ||
                mAdapter.getMessageInfoList().size() == 0) return;
        mBinding.rvMsg.scrollToPosition(mAdapter.getMessageInfoList().size() - 1);
    }

    private void initRV() {
        mBinding.rvMsg.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new PSReportAdapter(timezone);
        mAdapter.setItemChildListener((view, position) -> {
            int viewId = view.getId();
            if (viewId == R.id.iv_retry) {
                if (!mBinding.layoutSend.etMsg.isEnabled()) return;
                TextMessageInfo textMessageInfo = (TextMessageInfo) mAdapter.getMessageInfoList().remove(position);
                String msg = textMessageInfo.getData();
                mAdapter.notifyItemRemoved(position);
                textMessageInfo.setStatus(0);
                mAdapter.getMessageInfoList().add(textMessageInfo);
                mAdapter.notifyItemInserted(mAdapter.getMessageInfoList().size() - 1);
                mAdapter.notifyItemChanged(mAdapter.getMessageInfoList().size() - 1);
                sendMsg(msg, mAdapter.getMessageInfoList().size() - 1);
                scrollToLast();
            }
        });
        mBinding.rvMsg.setAdapter(mAdapter);
        List<IMessageInfo> data = new ArrayList<>();

        data.add(new TextMessageInfo(IMessageInfo.ACCEPT_TEXT, getTime(), GREETING_WORD, 1));
        mAdapter.setMessageInfoList(data);
        mAdapter.notifyDataSetChanged();
        scrollToLast();
    }

    private long getTime() {
        return System.currentTimeMillis();
    }

    private void scrollToLast() {
        List<IMessageInfo> messageInfoList = mAdapter.getMessageInfoList();
        if (CollectionUtil.isListNotEmpty(messageInfoList)) {
            mBinding.rvMsg.scrollToPosition(messageInfoList.size() - 1);
        }
    }

    private void initQuickReplyRV() {
        mBinding.layoutSend.rvQuickReply.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        mBinding.layoutSend.rvQuickReply.addItemDecoration(new SpaceItemDecoration(getContext(), DensityUtil.dp2px(getContext(), 4), 0));
        mQuickReplyAdapter = new PSQuickReplyAdapter();
        mBinding.layoutSend.rvQuickReply.setAdapter(mQuickReplyAdapter);
        mQuickReplyAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (!mBinding.layoutSend.etMsg.isEnabled()) return;
            String msg = mQuickReplyAdapter.getItem(position);
            mBinding.layoutSend.etMsg.setText(Local.s(msg));
            KeyBoardUtil.openKeybord(getContext(), mBinding.layoutSend.etMsg);
        });
        mQuickReplyMsgList = new ArrayList<>();
        mQuickReplyMsgList.add("Utility too high");
        mQuickReplyMsgList.add("Shouldn't be discharge");
        mQuickReplyMsgList.add("Shouldn't be charging");
        mQuickReplyMsgList.add("Solar Forecast Error");
        mQuickReplyAdapter.setNewData(mQuickReplyMsgList);
    }

    private void sendResult(boolean success, int position) {
        if (mAdapter == null) return;
        List<IMessageInfo> data = mAdapter.getMessageInfoList();
        data.get(data.size() - 1).setStatus(success ? 1 : -1);
        mAdapter.notifyItemChanged(data.size() - 1);
        String responseMsg = success ? SEND_SUCCESS : SEND_FAIL;
        data.add(new TextMessageInfo(IMessageInfo.ACCEPT_TEXT, getTime(), responseMsg, 1));
        mAdapter.notifyItemInserted(data.size() - 1);
        scrollToLast();
    }
}
