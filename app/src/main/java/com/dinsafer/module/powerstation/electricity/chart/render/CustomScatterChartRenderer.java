package com.dinsafer.module.powerstation.electricity.chart.render;

import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.interfaces.dataprovider.ScatterDataProvider;
import com.github.mikephil.charting.renderer.ScatterChartRenderer;
import com.github.mikephil.charting.utils.ViewPortHandler;

/**
 *
 */
public class CustomScatterChartRenderer extends ScatterChartRenderer {

    public CustomScatterChartRenderer(ScatterDataProvider chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {
        super(chart, animator, viewPortHandler);
    }

    public ScatterDataProvider getChart() {
        return mChart;
    }
}
