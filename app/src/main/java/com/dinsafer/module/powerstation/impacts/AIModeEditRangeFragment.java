package com.dinsafer.module.powerstation.impacts;

import android.os.Bundle;
import android.view.View;
import android.view.ViewTreeObserver;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAiModeEditRangeBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.event.ScheduledModeEvent;
import com.dinsafer.module.powerstation.widget.segmentbar.AIModeHorizontalSegmentRangeBar;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

public class AIModeEditRangeFragment extends MyBaseFragment<FragmentAiModeEditRangeBinding> {

    private List<Segment> mSegments = new ArrayList<>();
    private ArrayList<BaseFragment> mModeFragments;
    private CommonPagerAdapter mModeAdapter;

    private int mDaysCurrentPosition;

    private int iotEmergency = 30;
    private int iotSmart = 70;
    private int c1;
    private int c2;
    private int c3;
    private int s1;
    private int s2;

    public static AIModeEditRangeFragment newInstance() {
        AIModeEditRangeFragment fragment = new AIModeEditRangeFragment();
        return fragment;
    }

    public static AIModeEditRangeFragment newInstance(int iotSmart, int iotEmergency,int c1,int c2,int c3,int s1,int s2) {
        AIModeEditRangeFragment fragment = new AIModeEditRangeFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.IOT_SMART, iotSmart);
        bundle.putInt(PSKeyConstant.IOT_EMERGENCY, iotEmergency);
        bundle.putInt(PSKeyConstant.C_1, c1);
        bundle.putInt(PSKeyConstant.C_2, c2);
        bundle.putInt(PSKeyConstant.C_3, c3);
        bundle.putInt(PSKeyConstant.S_1, s1);
        bundle.putInt(PSKeyConstant.S_2, s2);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ai_mode_edit_range;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        Bundle bundle = getArguments();
        iotSmart = bundle.getInt(PSKeyConstant.IOT_SMART);
        iotEmergency = bundle.getInt(PSKeyConstant.IOT_EMERGENCY);
        c1 = bundle.getInt(PSKeyConstant.C_1);
        c2 = bundle.getInt(PSKeyConstant.C_2);
        c3 = bundle.getInt(PSKeyConstant.C_3);
        s1 = bundle.getInt(PSKeyConstant.S_1);
        s2 = bundle.getInt(PSKeyConstant.S_2);
        setSegment();
        initModeVp();
    }

    @Override
    public void initListener() {
        super.initListener();
    }

    private void initModeVp() {
        mModeFragments = new ArrayList<>();
        String[] modeSuggestions = getContext().getResources().getStringArray(R.array.ps_is_mode_suggestion_array);
        for (int i = 0; i < modeSuggestions.length; i++) {
            String cStr1 = getString(R.string.hashtag_c1);
            String cStr2 = getString(R.string.hashtag_c2);
            String cStr3 = getString(R.string.hashtag_c3);
            String sStr1 = getString(R.string.hashtag_s1);
            String sStr2 = getString(R.string.hashtag_s2);
            String percent = "%";
            String c1Str = c1 > 0 ? ("+" + c1 + percent) : (c1 + percent);
            String c2Str = c2 > 0 ? ("+" + c2 + percent) : (c2 + percent);
            String c3Str = c3 > 0 ? ("+" + c3 + percent) : (c3 + percent);
            String s1Str = s1 > 0 ? ("+" + s1 + percent) : (s1 + percent);
            String s2Str = s2 > 0 ? ("+" + s2 + percent) : (s2 + percent);
            String text = Local.s(modeSuggestions[i]).replace(cStr1, c1Str)
                    .replace(cStr2, c2Str)
                    .replace(cStr3, c3Str)
                    .replace(sStr1, s1Str)
                    .replace(sStr2, s2Str);
            mModeFragments.add(ModeSuggestionFragment.newInstance(i, text, 1, new AIModeEditRangeFragment.ModeFragmentCreatedListener()));
        }
        mModeAdapter = new CommonPagerAdapter(getChildFragmentManager(), mModeFragments);
        mBinding.vpMode.setAdapter(mModeAdapter);
        mBinding.vpMode.setOffscreenPageLimit(5);
        mBinding.indicator.setupViewpager(mBinding.vpMode);
    }

    private void setSegment() {
        mSegments.add(new Segment(0.0f, 0.02f, getString(R.string.power_battery_bar_status_text_5), getColor(R.color.color_ai_mode_indicator_1)));
        mSegments.add(new Segment(0.02f, 0.12f, getString(R.string.power_battery_bar_status_text_4), getColor(R.color.color_ai_mode_indicator_2)));
        mSegments.add(new Segment(0.12f, iotEmergency / 100f, getString(R.string.power_battery_bar_status_text_3), getColor(R.color.color_ai_mode_indicator_3)));
        mSegments.add(new Segment(iotEmergency / 100f, iotSmart / 100f, getString(R.string.power_battery_bar_status_text_2), getColor(R.color.color_ai_mode_indicator_4)));
        mSegments.add(new Segment(iotSmart / 100f, 1.0f, getString(R.string.power_battery_bar_status_text_1), getColor(R.color.color_ai_mode_indicator_5)));
        mBinding.segmentRangeBar.setMinProgress(iotEmergency * 1.0f);
        mBinding.segmentRangeBar.setMaxProgress(iotSmart * 1.0f);
        mBinding.segmentRangeBar.setMaxLimitedProgress(99f);
        mBinding.segmentRangeBar.setDrag(true);
        mBinding.segmentRangeBar.setSegmentRanges(mSegments);
        mBinding.segmentRangeBar.setProgressListener(new AIModeHorizontalSegmentRangeBar.OnProgressChangedListener() {
            @Override
            public void onProgress1Changed(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {
                iotEmergency = progress;
                EventBus.getDefault().post(new ScheduledModeEvent(mDaysCurrentPosition, true, iotEmergency));
            }

            @Override
            public void getProgress1OnActionUp(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress1OnFinally(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onProgress2Changed(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {
                iotSmart = progress;
                EventBus.getDefault().post(new ScheduledModeEvent(mDaysCurrentPosition, false, iotSmart));
            }

            @Override
            public void getProgress2OnActionUp(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress2OnFinally(AIModeHorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onAlert() {

            }
        });

        mBinding.segmentRangeBar.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
            mBinding.segmentRangeBar.setMinLimitedProgress(0.15f);
        });
    }

    public class ModeFragmentCreatedListener implements ModeSuggestionFragment.OnCreatedListener {

        @Override
        public void onCreated(View view, int position) {
            mBinding.vpMode.setViewPosition(view, position);
        }
    }

    private int getColor(int color) {
        return getResources().getColor(color);
    }

}
