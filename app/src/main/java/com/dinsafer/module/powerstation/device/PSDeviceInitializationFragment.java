package com.dinsafer.module.powerstation.device;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsDeviceInitializationBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSDeviceInitializationModel;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module.powerstation.settings.PSRegionFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Map;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/2 18:25
 * @description :
 */
public class PSDeviceInitializationFragment extends MyBaseFragment<FragmentPsDeviceInitializationBinding> implements IDeviceCallBack {

    /**
     * 首次连接失败后，在此时间段内会自动重连并一直显示loading状态
     * <p>
     * 问题：由于在刚添加的时候，有可能会出现返回的信息没有end_id的情况，这就会导致进入该页面马上显示了离线状态。
     * 为了改善这里的显示逻辑，所以增加了以下的逻辑：
     * 首次进入该页面，开始连接，30s内，在收到连接失败的时候，每5s重新连接一次，在此过程中一直显示loading，
     * 在30s后按照正式状态显示
     */
    private static final long DURATION_RETRY_FROM_FIRST = 30L * 1000;
    private static final long RE_CONNECT_INTERVAL = 5L * 1000;

    private BindMultiAdapter<PSDeviceInitializationModel> mAdapter;
    private ArrayList<PSDeviceInitializationModel> mData;
    private String mDeviceId;
    private String mSubcategory;
    private Device device;

    private long mFirstConnectTimeMillis;
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final Runnable mTaskConnect = () -> {
        DDLog.d(TAG, "mTaskConnect executed");
        if (null != device && BmtUtil.isDeviceOffline(device)) {
            DDLog.d(TAG, "mTaskConnect-connect");
            BmtManager.getInstance().connectDevice(device, true);
        }
    };
    private final Runnable mTaskRetryTimeout = () -> {
        DDLog.d(TAG, "mTaskRetryTimeout executed");
        updateViewStatus(DeviceHelper.getInt(device, "networkState", -1));
    };

    public static PSDeviceInitializationFragment newInstance(final String deviceId, final String subcategory) {
        PSDeviceInitializationFragment fragment = new PSDeviceInitializationFragment();
        final Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_device_initialization;
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);

        device = DinSDK.getHomeInstance().getDevice(mDeviceId, mSubcategory);
        if (device != null) {
            device.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onDestroyView() {
        mHandler.removeCallbacksAndMessages(null);
        if (null != device) {
            device.unregisterDeviceCallBack(this);
            device = null;
        }
        super.onDestroyView();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarBack.setVisibility(View.INVISIBLE);
        mBinding.commonBar.commonBarBack.setEnabled(false);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.ps_device_initialization));
        initCommonBarRightText();
        setSkipVisible(true);
        initRv();
        mBinding.lcbRetry.setOnClickListener(v -> {
            if (null == device) {
                showErrorToast();
                return;
            }

            if (BmtUtil.isDeviceConnected(device)) {
                openRegionPage();
                return;
            }

            BmtManager.getInstance().connectDevice(device, true);
        });

        if (null == device) {
            showErrorToast();
            setNextVisible(true);
            mBinding.lcbRetry.setLocalText(R.string.Retry);
        } else {
            mFirstConnectTimeMillis = System.currentTimeMillis();
            updateViewStatus(DeviceHelper.getInt(device, "networkState", -1));
            if (BmtUtil.isDeviceOffline(device)) {
                BmtManager.getInstance().connectDevice(device, true);
            }

            mHandler.postDelayed(mTaskRetryTimeout, DURATION_RETRY_FROM_FIRST);
        }
    }

    private void openRegionPage() {
        getDelegateActivity().addCommonFragment(PSRegionFragment.newInstanceForStepAddPS(mDeviceId, device.getSubCategory()));
        EventBus.getDefault().post(new FinishAddBmtEvent());
    }

    private void initCommonBarRightText() {
        mBinding.commonBar.commonBarRightText.setLocalText(getString(R.string.impact_strategies_reserve_mode_skip));
        mBinding.commonBar.commonBarRightText.setTextColor(getResources().getColor(R.color.color_brand_text));
        RelativeLayout.LayoutParams rlParams = (RelativeLayout.LayoutParams) mBinding.commonBar.commonBarRightText.getLayoutParams();
        rlParams.rightMargin = DensityUtil.dp2px(getContext(), 2);
        mBinding.commonBar.commonBarRightText.setLayoutParams(rlParams);
        mBinding.commonBar.commonBarRightText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        mBinding.commonBar.commonBarRightText.setOnClickListener(v -> {
            getDelegateActivity().removeAllCommonFragment();
        });
        mBinding.commonBar.commonBarRightText.setBackground(null);
    }

    private void setSkipVisible(final boolean visible) {
        mBinding.commonBar.commonBarRightText.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
    }

    private void setNextVisible(final boolean visible) {
        mBinding.lcbRetry.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    private void initRv() {
        mBinding.rvStep.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new BindMultiAdapter<>();
        mData = new ArrayList<>();
        // mData.add(new PSDeviceInitializationModel(PSDeviceInitializationModel.SUCCEED, getString(R.string.ps_device_initialization_step_1)));
        // mData.add(new PSDeviceInitializationModel(PSDeviceInitializationModel.SUCCEED, getString(R.string.ps_device_initialization_step_2)));
        // mData.add(new PSDeviceInitializationModel(PSDeviceInitializationModel.SUCCEED, getString(R.string.ps_device_initialization_step_3)));
        // mData.add(new PSDeviceInitializationModel(PSDeviceInitializationModel.SUCCEED, getString(R.string.ps_device_initialization_step_4)));
        // mData.add(new PSDeviceInitializationModel(PSDeviceInitializationModel.SUCCEED, getString(R.string.ps_device_initialization_step_5)));
        // mData.add(new PSDeviceInitializationModel(PSDeviceInitializationModel.FAILED, getString(R.string.ps_device_initialization_step_6)));
        mData.add(new PSDeviceInitializationModel(PSDeviceInitializationModel.LOADING, getString(R.string.connect_device)));
        mAdapter.setNewData(mData);
        mBinding.rvStep.setAdapter(mAdapter);
    }

    /**
     * 检查是否需要自动重连并且将离线状态一直显示成loading状态
     *
     * @return true: 状态变为离线状态后添加重连任务；刷新状态时，如果是离线状态，显示成loading状态
     */
    private boolean checkNeedReconnect() {
        final long durationFromFirstConnect = System.currentTimeMillis() - mFirstConnectTimeMillis;
        return durationFromFirstConnect < DURATION_RETRY_FROM_FIRST;
    }

    private void updateViewStatus(int connectStatus) {
        // 是否需要将离线状态显示成loading状态
        final boolean showLoadingWhenOffline = checkNeedReconnect();
        final int itemStatus = (showLoadingWhenOffline && -1 == connectStatus)
                ? 0
                : connectStatus;
        DDLog.i(TAG, "showLoadingWhenOffline: " + showLoadingWhenOffline + ", itemStatus: " + itemStatus);
        if (mData.size() > 0) {
            mData.get(0).updateStatus(itemStatus);
            mAdapter.notifyItemChanged(0);
        }

        if (BmtUtil.isDeviceConnected(device)) {
            setNextVisible(true);
            mBinding.lcbRetry.setLocalText(R.string.next);
        } else if (BmtUtil.isDeviceConnecting(device) || showLoadingWhenOffline) {
            setNextVisible(false);
        } else {
            setNextVisible(true);
            mBinding.lcbRetry.setLocalText(R.string.Retry);
        }
    }

    @Override
    public void onCmdCallBack(String deviceID, String subCategory, String cmd, Map map) {
        if (!TextUtils.isEmpty(mDeviceId)
                && mDeviceId.equals(deviceID)
                && device.getSubCategory().equals(subCategory)) {
            if (DsCamCmd.CMD_CONNECT.equals(cmd)
                    || DsCamCmd.CONNECT_STATUS_CHANGED.equals(cmd)) {
                runOnMainThread(() -> {
                    if (!getDelegateActivity().isCommonFragmentExist(PSDeviceInitializationFragment.class.getName())) {
                        return;
                    }

                    int connectStatus = (int) MapUtils.get(map, "connect_status", -1);
                    DDLog.i(TAG, "connect_status: " + connectStatus);
                    updateViewStatus(connectStatus);
                    mHandler.removeCallbacks(mTaskConnect);
                    if (BmtUtil.STATUS_DISCONNECTED == connectStatus && checkNeedReconnect()) {
                        DDLog.i(TAG, "Schedule connect task.");
                        mHandler.postDelayed(mTaskConnect, RE_CONNECT_INTERVAL);
                    }
                });
            }
        }
    }

    @Override
    public boolean onBackPressed() {
        return true;
    }
}
