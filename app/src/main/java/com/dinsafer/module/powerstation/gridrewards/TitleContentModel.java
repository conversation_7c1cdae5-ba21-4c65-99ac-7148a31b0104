package com.dinsafer.module.powerstation.gridrewards;

import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.DimenRes;
import androidx.annotation.Keep;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemTitleContentBinding;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * @describe：
 * @date：2024/10/23
 * @author: create by Sydnee
 */
@Keep
public class TitleContentModel implements BaseBindModel<ItemTitleContentBinding> {


    private String title;
    private String content1;
    private Spanned context1Spd;
    private String content2;
    private boolean showPoint;
    @DimenRes
    private int lineSpace;


    public void setLineSpace(@DimenRes int lineSpace) {
        this.lineSpace = lineSpace;
    }

    public void setShowPoint(boolean showPoint) {
        this.showPoint = showPoint;
    }

    public TitleContentModel(String content1) {
        this.content1 = content1;
    }

    public TitleContentModel(String title, String content1, String content2) {
        this.content1 = content1;
        this.content2 = content2;
        this.title = title;
    }

    public TitleContentModel(String title, Spanned content1Spd, String content2) {
        this.context1Spd = content1Spd;
        this.content2 = content2;
        this.title = title;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_title_content;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemTitleContentBinding mBinding) {
        if (!TextUtils.isEmpty(title)) {
            mBinding.tvTitle.setLocalText(title);
            mBinding.tvTitle.setVisibility(View.VISIBLE);
        }
        if (!TextUtils.isEmpty(content1) || !TextUtils.isEmpty(context1Spd)) {
            if (!TextUtils.isEmpty(content1)) {
                mBinding.tvContent1.setLocalText(content1);
            } else {
                mBinding.tvContent1.setText(context1Spd);
            }
            mBinding.tvContent1.setVisibility(View.VISIBLE);
            mBinding.ivPoint.setVisibility(showPoint ? View.VISIBLE : View.GONE);
            if (lineSpace != 0) {
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mBinding.llContent1.getLayoutParams();
                params.setMargins(params.leftMargin, lineSpace, params.rightMargin, params.bottomMargin);
                mBinding.llContent1.setLayoutParams(params);
            }
        }
        if (!TextUtils.isEmpty(content2)) {
            mBinding.tvContent2.setLocalText(content2);
            mBinding.tvContent2.setVisibility(View.VISIBLE);
            if (lineSpace != 0) {
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mBinding.tvContent2.getLayoutParams();
                params.setMargins(params.leftMargin, lineSpace, params.rightMargin, params.bottomMargin);
                mBinding.tvContent2.setLayoutParams(params);
            }
        }

    }

}
