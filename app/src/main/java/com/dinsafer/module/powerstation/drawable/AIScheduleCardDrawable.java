package com.dinsafer.module.powerstation.drawable;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;

public class AIScheduleCardDrawable extends Drawable {

    private Paint mPaint;
    private RectF mRectF;
    private float radius;
    private LinearGradient mLinearGradient;
    private View mView;
    private int mBackgroundColor;
    private float mStrokeWidth;

    public AIScheduleCardDrawable(View view, int backgroundColor, int[] gradientColors, float[] gradientPositions, float cornerRadius) {
        mView = view;
        mBackgroundColor = backgroundColor;
        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setAntiAlias(true);
        mLinearGradient = new LinearGradient(
                0, view.getHeight(),
                view.getWidth(), 0,
                gradientColors, gradientPositions,
                Shader.TileMode.CLAMP
        );
        mRectF = new RectF();
        this.radius = cornerRadius;
        mStrokeWidth = DensityUtils.dp2px(view.getContext(), 0.5f);
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        mRectF.set(mStrokeWidth, mStrokeWidth, mView.getWidth() - mStrokeWidth, mView.getHeight() - mStrokeWidth);
        mPaint.setColor(mBackgroundColor);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setShader(null);
        canvas.drawRoundRect(mRectF, radius, radius, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mStrokeWidth); // 边框宽度
//        mPaint.setColor(mView.getContext().getResources().getColor(R.color.color_brand_primary));
        mPaint.setShader(mLinearGradient);
        canvas.drawRoundRect(mRectF, radius, radius, mPaint);
    }

    @Override
    public void setAlpha(int alpha) {
        mPaint.setAlpha(alpha);
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
        mPaint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }
}
