package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsImpactStrategiesBinding;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.util.CollectionUtil;

import java.util.List;

public class PSImpactStrategiesItemModel extends BasePowerStationItemModel<ItemPsImpactStrategiesBinding> {

    private List<List<Float>> revenueDayData;
    private int interval;
    private int random;

    public PSImpactStrategiesItemModel(Context context, String deviceId, String sub) {
        super(context, deviceId, sub);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_impact_strategies;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsImpactStrategiesBinding binding) {
        float sumVal = 0;
        if (CollectionUtil.isListNotEmpty(revenueDayData)) {
            for (List<Float> sonData : revenueDayData) {
                if (sonData.size() > 1) {
                    sumVal = sumVal + sonData.get(1);
                    if (sonData.size() > 2) {
                        sumVal = sumVal + sonData.get(2);
                    }
                }
            }
            sumVal = sumVal * interval / 60f;
            binding.tvIsValue.setLocalText(ChartDataUtil.savePointStr(sumVal / 1000f, 1));
            float inverter = ChartDataUtil.getValInNote(random, sumVal / 1000f);
            String inverterStr = ChartDataUtil.savePointStr(inverter, 1);
            String note = ChartDataUtil.getRevenueNote(mContext, inverterStr, random);
            binding.tvNote.setText(note);
            int img = ChartDataUtil.getRevenueImgDrawable(random);
            if (img != 0) {
                binding.ivImpactsStrategies.setImageResource(img);
            }
        }
        boolean visible = CollectionUtil.isListEmpty(revenueDayData);
        binding.clContent.setVisibility(visible ? View.GONE : View.VISIBLE);
        binding.clEmpty.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setRevenueDayData(List<List<Float>> revenueDayData) {
        this.revenueDayData = revenueDayData;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }

    public void setRandom(int random) {
        this.random = random;
    }
}
