package com.dinsafer.module.powerstation.impacts;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentReserveModeBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.BottomTipModel;
import com.dinsafer.module.powerstation.adapter.PSReserveModeModelV2;
import com.dinsafer.module.powerstation.bean.PSReserveModeBeanV2;
import com.dinsafer.module.powerstation.dialog.PSTimePickerDialog;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module.powerstation.event.NeedRefreshEvent;
import com.dinsafer.module.powerstation.event.PSReserveRefreshEvent;
import com.dinsafer.module.powerstation.event.SaveScheduledModeEvent;
import com.dinsafer.module.powerstation.settings.EnergySettingFragment;
import com.dinsafer.module.powerstation.settings.PSTotalLoadSettingFragment;
import com.dinsafer.module.powerstation.widget.SpaceItemDecoration;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;
import com.dinsafer.util.VersionCompareUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/25 10:50
 * @description :
 */
public class ReserveModeFragment extends PSConnectLoadingFragment<FragmentReserveModeBinding> implements IDeviceCallBack {

    private BindMultiAdapter<BindModel> mReserveModeAdapterV2;
    private List<BindModel> mReserveModeData;
    private Map<String, Object> params = new HashMap<>();
    private boolean isSupportRegion;
    private boolean isEmergencyOn;
    private long mStartTime;
    private long mEndTime;
    private long mChangeStartTime;
    private long mChangeEndTime;
    private int mStrategyType = 1;
    private boolean isSwitch;
    public static boolean needRefresh = true;
    private boolean isSkip = false;
    private final AtomicBoolean mNeedHandleOutputCmd = new AtomicBoolean(false);

    private int mAppliedPosition;
    private int mSmart = 70;
    private int mEmergency = 30;
    private static final int PLENTY_ENERGY = 100;
    private ArrayList<Integer> mWeekdays = new ArrayList<>();
    private ArrayList<Integer> mWeekend = new ArrayList<>();
    private int mPTEmergency;
    private int mPTSmart;
    private int mSMEmergency;
    private int mSMSmart;
    private int mCustomEmergency;
    private int mCustomSmart;
    private long mCurrentTimeMillis;
    private boolean isFirst = true;
    private boolean isIgnoreMode;
    private boolean hasAI;

    private boolean isGridToBattery;
    private BehaviorSubject<Map<String, Object>> mGetFeatureSubject = BehaviorSubject.create();
    private BehaviorSubject<Map<String, Object>> mGetReserveModeSubject = BehaviorSubject.create();
    private Subscriber<Map<String, Object>> mSubscriber;

    public static ReserveModeFragment newInstanceForStepAddPS(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_STEP_ADD, deviceId, subcategory);
    }

    public static ReserveModeFragment newInstance(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_SETTING, deviceId, subcategory);
    }

    public static ReserveModeFragment newInstance(int from, String deviceId, String subcategory) {
        ReserveModeFragment fragment = new ReserveModeFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_reserve_mode;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        initSubscriber();
        initRv();
        mBinding.commonBar.commonBarTitle.setText(Local.s(getContext().getResources().getString(R.string.impact_strategies_reserve_mode)));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        setNextEnabled(false);
        if (mFrom > PARAM_FROM_SETTING) {
            setNextVisible(true);
        } else {
            setNextVisible(false);
        }
        setEmergencyChargeEnabled(false);
        mBinding.clEmergencyCharge.setVisibility(mFrom == PARAM_FROM_SETTING ? View.VISIBLE : View.GONE);
        mBinding.switchEmergency.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                mChangeStartTime = TimeUtil.getNowHourTimeMillis();
                mChangeEndTime = mChangeStartTime + 172800;
                mChangeStartTime = System.currentTimeMillis() / 1000;
                emergencyChargeSetting(true);
            }
        });

        mBinding.viewClick.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPsTimePickerDialog(mStartTime, mEndTime);
            }
        });
        mBinding.lcbNext.setOnClickListener(v -> {
            getMcuInfo();
        });

    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (BmtUtil.isDeviceOffline(mPSDevice)) {
            showDeviceOfflineDialog(mPSDevice);
            return;
        }
        showTimeOutLoadinFramgment();
        params.clear();
        params.put(PSKeyConstant.CMD, BmtCmd.GET_FEATURE);
        submitCmd();
        params.clear();
        params.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_RESERVE_MODE);
        submitCmd();
        getEmergencyAndSmart();
    }

    private void initSubscriber() {
        mSubscriber = new Subscriber<Map<String, Object>>() {

            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(Map<String, Object> result) {
                closeLoadingFragment();
                if (result != null && result.size() > 0) {
                    int reserveMode = DeviceHelper.getInt(result, BmtDataKey.RESERVE_MODE, 0);
                    isSupportRegion = DeviceHelper.getBoolean(result, BmtDataKey.IS_PRICE_TRACKING_SUPPORTED, false);
                    boolean isElectSupport = DeviceHelper.getBoolean(result, BmtDataKey.ELEC_SUPPORT, false);
                    isGridToBattery = DeviceHelper.getBoolean(result, BmtDataKey.GRID_TO_BATTERY, false);
                    boolean isElecSupport = DeviceHelper.getBoolean(result, BmtDataKey.ELEC_SUPPORT, false);
                    if (isElecSupport) {
                        if (mReserveModeData.size() == 3) {
                            mReserveModeData.remove(2);
                        }
                    } else {
                        // 所选地区不支持才需要加这个提示
                        if (mReserveModeData.size() < 3) {
                            mReserveModeData.add(new BottomTipModel(getContext(), "* " + Local.s(getString(R.string.impact_strategies_not_support_tips))));
                        }
                    }
                    PSReserveModeModelV2 modeModelV2 = (PSReserveModeModelV2) mReserveModeData.get(0);
                    modeModelV2.getReserveModeBean().setShowTag(!isGridToBattery);
                    modeModelV2.getReserveModeBean().setEnabled(isSupportRegion && isElectSupport && isGridToBattery);
                    mReserveModeAdapterV2.notifyDataSetChanged();
                    if (reserveMode < 2 || reserveMode > 3) return;
                    for (int i = 0; i < (isSupportRegion ? mReserveModeData.size() : mReserveModeData.size() - 1); i++) {
                        PSReserveModeModelV2 reserveModeModelV2 = (PSReserveModeModelV2) mReserveModeData.get(i);
                        reserveModeModelV2.getReserveModeBean().setSelected(false);
                        reserveModeModelV2.getReserveModeBean().setStatus(-1);
                    }
                    if (reserveMode == 3) {
                        mAppliedPosition = 0;
                    } else {
                        mAppliedPosition = reserveMode - 1;
                    }
                    ((PSReserveModeModelV2) mReserveModeData.get(mAppliedPosition)).getReserveModeBean().setSelected(true);
                    ((PSReserveModeModelV2) mReserveModeData.get(mAppliedPosition)).getReserveModeBean().setStatus(1);
                    mReserveModeAdapterV2.notifyDataSetChanged();
                    setNextEnabled(true);
                    setEmergencyChargeEnabled(isGridToBattery);
                    showTag(!isGridToBattery);
                }
            }
        };

        Observable.combineLatest(mGetFeatureSubject, mGetReserveModeSubject, (featureMap, reserveModeMap) -> {
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.putAll(featureMap);
                    resultMap.putAll(reserveModeMap);
                    return resultMap;
                }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mSubscriber);
    }

    private void checkBSensor() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        params.clear();
        params.put(PSKeyConstant.CMD, DsCamCmd.GET_INVERTER_OUTPUT_INFO);
        mNeedHandleOutputCmd.set(true);
        submitCmd();
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        if (mSubscriber != null) {
            mSubscriber.unsubscribe();
        }
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }


    private boolean hasLoadingData() {
        if (CollectionUtil.isListEmpty(mReserveModeData)) return false;
        for (BindModel bindModel : mReserveModeData) {
            if (bindModel instanceof PSReserveModeModelV2) {
                if (((PSReserveModeModelV2) bindModel).getReserveModeBean().getStatus() == 0) {
                    return true;
                }
            }
        }
        return false;
    }

    private void initRv() {
        mBinding.llParent.setVisibility(View.VISIBLE);
        mBinding.rvReserveMode.setVisibility(View.VISIBLE);
        mBinding.clEmergencyCharge.setVisibility(View.GONE);
        mBinding.rvReserveMode.setLayoutManager(new LinearLayoutManager(getContext()));
        ((SimpleItemAnimator) mBinding.rvReserveMode.getItemAnimator()).setSupportsChangeAnimations(false);
        mReserveModeAdapterV2 = new BindMultiAdapter<>();
        mReserveModeData = new ArrayList<>();
        mBinding.rvReserveMode.setAdapter(mReserveModeAdapterV2);
        PSReserveModeBeanV2 priceTrackingMode = new PSReserveModeBeanV2(R.drawable.icon_power_price_tracking_mode,
                getString(R.string.Emaldo_AI_Mode), getString(R.string.ps_is_price_tracking_mode_subtitle), 2, false);
        mReserveModeData.add(new PSReserveModeModelV2(getContext(), priceTrackingMode));
        PSReserveModeBeanV2 scheduledMode = new PSReserveModeBeanV2(R.drawable.icon_power_scheduled_mode,
                getString(R.string.ps_is_scheduled_mode), getString(R.string.ps_is_scheduled_mode_subtitle), 1, true);
        mReserveModeData.add(new PSReserveModeModelV2(getContext(), scheduledMode));
        mReserveModeAdapterV2.setNewData(mReserveModeData);
        mReserveModeAdapterV2.setOnBindItemClickListener((OnBindItemClickListener<BindModel>) (v, position, bindModel) -> {
            if (bindModel instanceof PSReserveModeModelV2) {
                PSReserveModeBeanV2 reserveModeBean = ((PSReserveModeModelV2) bindModel).getReserveModeBean();
                if (!reserveModeBean.isEnabled() || reserveModeBean.isSelected() || hasLoadingData())
                    return;
                for (BindModel modeModel : mReserveModeData) {
                    if (modeModel instanceof PSReserveModeModelV2) {
                        PSReserveModeBeanV2 modeBeanV = ((PSReserveModeModelV2) modeModel).getReserveModeBean();
                        modeBeanV.setSelected(false);
                    }
                }
                reserveModeBean.setSelected(true);
                mReserveModeAdapterV2.notifyDataSetChanged();
            }
        });
        mReserveModeAdapterV2.setOnBindItemChildClickListener((OnBindItemChildClickListener<BindModel>) (view, position, reserveModeModel) -> {
            int viewId = view.getId();
            if (reserveModeModel instanceof PSReserveModeModelV2) {
                PSReserveModeBeanV2 reserveModeBean = ((PSReserveModeModelV2) reserveModeModel).getReserveModeBean();
                switch (viewId) {
                    case R.id.tv_operate:
                        int type = reserveModeBean.getType();
                        if (type == 1) {
                            mAppliedPosition = 1;
                            getDelegateActivity().addCommonFragment(ScheduledModeFragment.newInstance(mFrom, mDeviceId, mSubcategory, isGridToBattery));
                        } else {
                            mAppliedPosition = 0;
                            getDelegateActivity().addCommonFragment(AIModeFragment.newInstance(mDeviceId, mSubcategory, isGridToBattery));
                        }
                        break;

                    case R.id.ll_status:
                        applyMode(reserveModeBean, position);
                        break;
                }
            }
        });

        mBinding.rvReserveMode.addItemDecoration(new SpaceItemDecoration(getContext(), DensityUtil.dp2px(getContext(), 15), 0));
    }

    private void setEmergencyChargeEnabled(boolean enabled) {

        mBinding.switchEmergency.setEnabled(enabled);
        mBinding.tvEmergencyCharge.setAlpha(enabled ?
                MainPanelHelper.VIEW_ENABLE_ALPHA : MainPanelHelper.VIEW_DISABLE_ALPHA);
        mBinding.switchEmergency.setAlpha(enabled ?
                MainPanelHelper.VIEW_ENABLE_ALPHA : MainPanelHelper.VIEW_DISABLE_ALPHA);
        mBinding.tvDesc.setAlpha(enabled ?
                MainPanelHelper.VIEW_ENABLE_ALPHA : MainPanelHelper.VIEW_DISABLE_ALPHA);
    }

    private void showTag(boolean show) {
        mBinding.tvTag.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    /**
     * 保存模式
     *
     * @param reserveModeBean
     * @param position
     */
    private void applyMode(PSReserveModeBeanV2 reserveModeBean, int position) {
        reserveModeBean.setStatus(0);
        mReserveModeAdapterV2.notifyDataSetChanged();
        if (position == 0) {
            setAIScheduledMode();
        } else {
            setScheduledMode();
        }
    }

    private void submitCmd() {
        if (mPSDevice != null) {
            mPSDevice.submit(params);
        }
    }

    private void setNextVisible(boolean visible) {
        mBinding.lcbNext.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    /**
     * 设置下一步按钮是否可用
     *
     * @param enabled
     */
    private void setNextEnabled(boolean enabled) {
        mBinding.lcbNext.setEnabled(enabled);
        mBinding.lcbNext.setAlpha(enabled ? 1f : 0.5f);
    }

    /**
     * 时间选择弹窗
     */
    private void showPsTimePickerDialog(long startTime, long endTime) {
        mCurrentTimeMillis = System.currentTimeMillis();
        DDLog.i(TAG, "mCurrentTimeMillis===" + mCurrentTimeMillis);
        new PSTimePickerDialog.Builder()
                .setFromAt(1)
                .setStartTime(startTime)
                .setEndTime(endTime)
                .setStartTimeStr(mBinding.tvStartValue.getText().toString())
                .setEndTimeStr(mBinding.tvEndValue.getText().toString())
                .setConfirmListener(new PSTimePickerDialog.OnConfirmListener() {
                    @Override
                    public void onConfirm(long startTime, long endTime, String startTimeStr, String endTimeStr) {
                        mChangeStartTime = !TextUtils.isEmpty(startTimeStr) && startTimeStr.equals(getString(R.string.impact_strategies_now)) ?
                                mCurrentTimeMillis / 1000 : startTime / 1000;
                        mChangeEndTime = !TextUtils.isEmpty(endTimeStr) && endTimeStr.equals(getString(R.string.impact_strategies_now)) ?
                                mCurrentTimeMillis / 1000 : endTime / 1000;
                        emergencyChargeSetting(false);
                    }
                }).build(getContext()).show(getFragmentManager(), PSTimePickerDialog.TAG);
    }


    /**
     * 设置Emergency Charge
     */
    private void emergencyChargeSetting(boolean isSwitch) {
        this.isSwitch = isSwitch;
        params.clear();
        params.put(PSKeyConstant.CMD, DsCamCmd.SET_EMERGENCY_CHARGE);

        if ((isSwitch && !mBinding.switchEmergency.isOn())) {
            mChangeStartTime = 0;
            mChangeEndTime = 0;
        }
        params.put(PSKeyConstant.START_TIME, mChangeStartTime);
        params.put(PSKeyConstant.END_TIME, mChangeEndTime);
        params.put(PSKeyConstant.ON, isSwitch ? mBinding.switchEmergency.isOn() : true);
        showTimeOutLoadinFramgment();
        submitCmd();
    }

    /**
     * 设置为价格追踪模式(Price Tracking)
     */
    private void setPriceTracking() {
        mAppliedPosition = 0;
        params.clear();
        params.put(PSKeyConstant.CMD, BmtCmd.SET_RESERVE_MODE);
        params.put(BmtDataKey.RESERVE_MODE, 1);
        submitCmd();
    }

    /**
     * 设置为定时模式 (Scheduled Mode)
     */
    private void setScheduledMode() {
        mAppliedPosition = 1;
        params.clear();
        params.put(PSKeyConstant.CMD, BmtCmd.SET_RESERVE_MODE);
        params.put(BmtDataKey.RESERVE_MODE, 2);
        if (mSmart > -1) {
            params.put(BmtDataKey.SMART, mSmart);
        }
        if (mEmergency > -1) {
            params.put(BmtDataKey.EMERGENCY, mEmergency);
        }
        if (CollectionUtil.isListNotEmpty(mWeekdays)) {
            int[] weekdaysArr = new int[mWeekdays.size()];
            for (int i = 0; i < mWeekdays.size(); i++) {
                weekdaysArr[i] = mWeekdays.get(i);
            }
            params.put(BmtDataKey.WEEKDAYS, weekdaysArr);
        }
        if (CollectionUtil.isListNotEmpty(mWeekend)) {
            int[] weekendArr = new int[mWeekend.size()];
            for (int i = 0; i < mWeekend.size(); i++) {
                weekendArr[i] = mWeekend.get(i);
            }
            params.put(BmtDataKey.WEEKEND, weekendArr);
        }
        submitCmd();
    }

    /**
     * 设置为AI模式
     */
    private void setAIScheduledMode() {
        mAppliedPosition = 0;
        params.clear();
        params.put(PSKeyConstant.CMD, BmtCmd.SET_RESERVE_MODE);
        params.put(BmtDataKey.RESERVE_MODE, 3);
        if (mCustomSmart > -1) {
            params.put(BmtDataKey.SMART, mCustomSmart);
        }
        if (mCustomEmergency > -1) {
            params.put(BmtDataKey.EMERGENCY, mCustomEmergency);
        }
        submitCmd();
    }

    private void getEmergencyAndSmart() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_EMERGENCY_CHARGE);
            mPSDevice.submit(map);
            map.put(PSKeyConstant.CMD, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE);
            mPSDevice.submit(map);
            map.put(PSKeyConstant.CMD, BmtCmd.GET_SCHEDULE_RESERVE_MODE);
            mPSDevice.submit(map);
            map.put(PSKeyConstant.CMD, BmtCmd.GET_CUSTOM_SCHEDULEMODE);
            mPSDevice.submit(map);
        }
    }

    private void getMcuInfo() {
        if (mPSDevice != null) {
            int iotVersionStatus = BmtUtil.getIotVersionGETargetVersion(mPSDevice, "1.7.0");
            if (iotVersionStatus == 1) {
                getDelegateActivity().addCommonFragment(PSTotalLoadSettingFragment.newInstanceFromAddPS(mDeviceId, mSubcategory));
            } else if (iotVersionStatus == 0) {
                toPowerOrEnergySetting();
            } else if (iotVersionStatus == -1) {
                Map<String, Object> map = new HashMap<>();
                map.put(PSKeyConstant.CMD, BmtCmd.GET_ADVANCE_INFO);
                showTimeOutLoadinFramgment();
                mPSDevice.submit(map);
            }
        } else {
            showErrorToast();
        }
    }

    private void toPowerOrEnergySetting() {
        if (BmtUtil.isBmtDevicePowerStore(mPSDevice)) {
            getDelegateActivity().removeAllCommonFragment();
            getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstanceForStepAddPS(
                    DeviceHelper.getString(BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory), DinConst.INFO_NAME, "")
                    , mDeviceId, mSubcategory));
        } else {
            getDelegateActivity().addCommonFragment(EnergySettingFragment.newInstanceFromAddPS(mDeviceId, mSubcategory));
        }
    }

    @SuppressLint("StringFormatInvalid")
    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {

        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (cmd != null && cmd.equals(DsCamCmd.SET_EMERGENCY_CHARGE)) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (status == StatusConstant.STATUS_SUCCESS) {
                            mStartTime = mChangeStartTime * 1000;
                            mEndTime = mChangeEndTime * 1000;
                            mBinding.tvStartValue.setLocalText(TimeUtil.isSameDayHour(mStartTime, System.currentTimeMillis()) ?
                                    getString(R.string.impact_strategies_now) : DDDateUtil.long2StrHourTime(mStartTime, DDDateUtil.LOCAL_MONTH_DATE_HOUR_FORMAT));
                            mBinding.tvEndValue.setLocalText(TimeUtil.isSameDayHour(mEndTime, System.currentTimeMillis()) ?
                                    getString(R.string.impact_strategies_now) : DDDateUtil.long2StrHourTime(mEndTime, DDDateUtil.LOCAL_MONTH_DATE_HOUR_FORMAT));
                            String startStr = mBinding.tvStartValue.getText().toString();
                            String endStr = mBinding.tvEndValue.getText().toString();
                            if (!TextUtils.isEmpty(startStr) && !TextUtils.isEmpty(startStr)
                                    && startStr.equals(endStr) && startStr.equals(Local.s(getString(R.string.impact_strategies_now)))) {
                                mBinding.switchEmergency.setOn(false);
                                mBinding.clTime.setVisibility(View.GONE);
                                LinearLayout.LayoutParams llParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                                mBinding.clEmergencyCharge.setLayoutParams(llParams);
                                mBinding.rvReserveMode.setVisibility(View.VISIBLE);
                            }
                            if (isSwitch) {
                                mBinding.clTime.setVisibility(mBinding.switchEmergency.isOn() ? View.VISIBLE : View.GONE);
                                LinearLayout.LayoutParams llParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                                        mBinding.switchEmergency.isOn() ? LinearLayout.LayoutParams.MATCH_PARENT : LinearLayout.LayoutParams.WRAP_CONTENT);
                                mBinding.clEmergencyCharge.setLayoutParams(llParams);
                                mBinding.rvReserveMode.setVisibility(mBinding.switchEmergency.isOn() ? View.GONE : View.VISIBLE);
                                if (mBinding.switchEmergency.isOn()) {
                                    ChargeModeEvent chargeModeEvent = new ChargeModeEvent(mDeviceId, mSubcategory, 4);
                                    EventBus.getDefault().post(chargeModeEvent);
                                }
                            }
                        } else {
                            showErrorToast();
                        }
                    }
                });
            }
            if (cmd != null && cmd.equals(DsCamCmd.GET_REGION)) {
                closeLoadingFragment();
            }
            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

//                            case DsCamCmd.GET_REGION:
//                                if (result != null && result.size() > 0) {
//                                    isSupportRegion = (boolean) MapUtils.get(result, PSKeyConstant.SMART_TARIFF_TRACKING, false);
//                                    if (!isSupportRegion) {
//                                        // 所选地区不支持才需要加这个提示
//                                        mReserveModeData.add(new BottomTipModel(getContext(), getString(R.string.impact_strategies_not_support_tips)));
//                                        PSReserveModeModelV2 modeModelV2 = (PSReserveModeModelV2) mReserveModeData.get(0);
//                                        modeModelV2.getReserveModeBean().setEnabled(false);
//                                        mReserveModeAdapterV2.notifyDataSetChanged();
//                                    }
//                                    if (mFrom == PARAM_FROM_STEP_ADD) {
//                                        params.clear();
//                                        if (isSupportRegion) {
//                                            setPriceTracking();
//                                        } else {
//                                            setScheduledMode();
//                                        }
//
//                                    } else {
//                                        params.clear();
//                                        params.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_RESERVE_MODE);
//                                        showLoadingFragment(0);
//                                        submitCmd();
//                                    }
//                                }
//                                break;

                            case BmtCmd.GET_FEATURE:
                                mGetFeatureSubject.onNext(result);
                                break;

                            case BmtCmd.GET_CURRENT_RESERVE_MODE:  // 获取模式
                                if (isIgnoreMode) return;
                                isIgnoreMode = true;
                                mGetReserveModeSubject.onNext(result);
                                break;

                            case BmtCmd.SET_RESERVE_MODE:  // 设置模式
                                closeLoadingFragment();
                                PSReserveModeModelV2 modeModelV2 = (PSReserveModeModelV2) mReserveModeAdapterV2.getItem(mAppliedPosition);
                                for (BindModel modeModel : mReserveModeData) {
                                    if (modeModel instanceof PSReserveModeModelV2) {
                                        PSReserveModeBeanV2 modeBeanV = ((PSReserveModeModelV2) modeModel).getReserveModeBean();
                                        modeBeanV.setStatus(-1);
                                        modeBeanV.setSelected(false);
                                    }
                                }
                                modeModelV2.getReserveModeBean().setStatus(1);
                                modeModelV2.getReserveModeBean().setSelected(true);
                                mReserveModeAdapterV2.notifyDataSetChanged();
                                setNextEnabled(true);
                                if (getDelegateActivity().isFragmentInTopExcludeLoading(ReserveModeFragment.this)) {
                                    DDLog.i(TAG, "发送事件====");
                                    int aiSmart = hasAI ? DeviceHelper.getInt(mPSDevice, BmtDataKey.SMART_RESERVE, 0) : mCustomSmart;
                                    int aiEmergency = hasAI ? DeviceHelper.getInt(mPSDevice, BmtDataKey.EMERGENCY_RESERVE, 0) : mCustomEmergency;
                                    int mode = mAppliedPosition == 0 ? 3 : 2;
                                    ChargeModeEvent event = new ChargeModeEvent(mDeviceId, mSubcategory, mode,
                                            mAppliedPosition == 0 ? aiSmart : mSMSmart,
                                            mAppliedPosition == 0 ? aiEmergency : mSMEmergency);
                                    EventBus.getDefault().post(event);
                                }
                                break;

                            case DsCamCmd.GET_EMERGENCY_CHARGE:
                                if (!isFirst) {
                                    closeLoadingFragment();
                                }
                                if (result != null && result.size() > 0) {
                                    mStartTime = (long) MapUtils.get(result, PSKeyConstant.START_TIME, 0) * 1000;
                                    mEndTime = (long) MapUtils.get(result, PSKeyConstant.END_TIME, 0) * 1000;
                                    final boolean emergencyOn = (boolean) MapUtils.get(result, PSKeyConstant.ON, false);
                                    long currentMillis = System.currentTimeMillis();
                                    isEmergencyOn = currentMillis <= mEndTime && emergencyOn;
                                    mBinding.tvStartValue.setLocalText(TimeUtil.isSameDayHour(mStartTime, System.currentTimeMillis()) ?
                                            getString(R.string.impact_strategies_now) : DDDateUtil.long2StrHourTime(mStartTime, DDDateUtil.LOCAL_MONTH_DATE_HOUR_FORMAT));
                                    mBinding.tvEndValue.setLocalText(TimeUtil.isSameDayHour(mEndTime, System.currentTimeMillis()) ?
                                            getString(R.string.impact_strategies_now) : DDDateUtil.long2StrHourTime(mEndTime, DDDateUtil.LOCAL_MONTH_DATE_HOUR_FORMAT));
                                    LinearLayout.LayoutParams llParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                                            isEmergencyOn ? LinearLayout.LayoutParams.MATCH_PARENT : LinearLayout.LayoutParams.WRAP_CONTENT);
                                    mBinding.clEmergencyCharge.setLayoutParams(llParams);
                                    mBinding.switchEmergency.setOn(isEmergencyOn);
                                    mBinding.clTime.setVisibility(isEmergencyOn ? View.VISIBLE : View.GONE);
                                    mBinding.rvReserveMode.setVisibility(isEmergencyOn ? View.GONE : View.VISIBLE);
                                    params.clear();
                                    params.put(PSKeyConstant.CMD, DsCamCmd.GET_CHARGE_STRATEGIES);
                                    if (isFirst) {
                                        isFirst = false;
                                    }
                                    submitCmd();
                                }
                                break;

                            case DsCamCmd.GET_INVERTER_OUTPUT_INFO:
//                                if (result != null && result.size() > 0) {
//                                    if (mFrom == PARAM_FROM_STEP_ADD && mNeedHandleOutputCmd.get()) {
//                                        mNeedHandleOutputCmd.set(false);
//                                        closeTimeOutLoadinFramgmentWithErrorAlert();
//                                        if (!getDelegateActivity().isFragmentInTop(ReserveModeFragment.this))
//                                            return;
//                                        boolean isBSensorOutputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_OUTPUT_ON, false);
//                                        if (isBSensorOutputOn) {
//                                            getDelegateActivity().addCommonFragment(EnergySettingFragment.newInstanceFromAddPS(mDeviceId));
//                                        } else {
//                                            toPowerStation();
//                                        }
//                                    }
//                                }
                                break;

                            case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                                mPTEmergency = DeviceHelper.getInt(result, BmtDataKey.EMERGENCY, 0);
                                mPTSmart = DeviceHelper.getInt(result, BmtDataKey.SMART, 0);
                                break;

                            case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                                mSMEmergency = DeviceHelper.getInt(result, BmtDataKey.EMERGENCY, -1);
                                mSMSmart = DeviceHelper.getInt(result, BmtDataKey.SMART, -1);
                                break;

                            case BmtCmd.GET_CUSTOM_SCHEDULEMODE:
                                mCustomEmergency = DeviceHelper.getInt(result, BmtDataKey.EMERGENCY, -1);
                                mCustomSmart = DeviceHelper.getInt(result, BmtDataKey.SMART, -1);
                                Integer[] weekdays = (Integer[]) MapUtils.get(result, BmtDataKey.WEEKDAYS, null);
                                if (weekdays != null) {
                                    for (int weekday : weekdays) {
                                        if (weekday == 128) {
                                            hasAI = true;
                                            break;
                                        }
                                    }
                                }
                                break;

                            case BmtCmd.GET_ADVANCE_INFO:
                                if (mFrom == PARAM_FROM_STEP_ADD) {
                                    closeLoadingFragment();
                                    if (result != null && result.size() > 0) {
                                        final String iotVersion = DeviceHelper.getString(result, BmtDataKey.VERSION, "").replace("v", "");
                                        String[] versionArr = iotVersion.split("-");
                                        boolean isSupportFuse = VersionCompareUtil.isNewVersionLargeThanOrEqualOldVersion("1.7.0", versionArr[0]);
                                        if (isSupportFuse) {
                                            getDelegateActivity().addCommonFragment(PSTotalLoadSettingFragment.newInstanceFromAddPS(mDeviceId, mSubcategory));
                                        } else {
                                            toPowerOrEnergySetting();
                                        }
                                    } else {
                                        toPowerOrEnergySetting();
                                    }
                                }
                                break;
                        }
                    }
                });

            } else {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case DsCamCmd.GET_INVERTER_OUTPUT_INFO:
//                                closeTimeOutLoadinFramgmentWithErrorAlert();
//                                if (mFrom == PARAM_FROM_STEP_ADD && mNeedHandleOutputCmd.get()) {
//                                    mNeedHandleOutputCmd.set(false);
//                                    toPowerStation();
//                                }
                                break;
                            case DsCamCmd.GET_EMERGENCY_CHARGE:
                            case DsCamCmd.GET_CHARGE_STRATEGIES:
                                closeLoadingFragment();
                                if (mFrom == PARAM_FROM_STEP_ADD) {
                                    // 获取出错了，不能显示空白页面
                                    runOnMainThread(() -> {
                                        mBinding.clTime.setVisibility(View.GONE);
                                        mBinding.rvReserveMode.setVisibility(View.VISIBLE);
                                        mBinding.llParent.setVisibility(View.VISIBLE);
                                    });
                                }
                                break;

                            case BmtCmd.SET_RESERVE_MODE:
                                runOnMainThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        PSReserveModeModelV2 modeModelV2 = (PSReserveModeModelV2) mReserveModeAdapterV2.getItem(mAppliedPosition);
                                        modeModelV2.getReserveModeBean().setStatus(-1);
                                        mReserveModeAdapterV2.notifyItemChanged(mAppliedPosition);
//                                showErrorToast();
                                    }
                                });
                                break;

                            case BmtCmd.GET_CURRENT_RESERVE_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case BmtCmd.GET_ADVANCE_INFO:
                                if (mFrom == PARAM_FROM_STEP_ADD) {
                                    closeLoadingFragment();
                                    getDelegateActivity().addCommonFragment(EnergySettingFragment.newInstanceFromAddPS(mDeviceId, mSubcategory));
                                }
                                break;
                        }
                    }
                });
            }
        }
    }

    private void toPowerStation() {
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                getDelegateActivity().removeAllCommonFragment();
                getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstanceForStepAddPS(
                        DeviceHelper.getString(BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory), DinConst.INFO_NAME, "")
                        , mDeviceId, mSubcategory));

            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(PSReserveRefreshEvent event) {
        needRefresh = true;

        if (mFrom == PARAM_FROM_STEP_ADD) {
            setNextEnabled(true);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(NeedRefreshEvent event) {
        needRefresh = event.isNeedFresh();
    }

    /**
     * 定时模式的数据
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSaveScheduleModeEvent(SaveScheduledModeEvent event) {
        mSmart = event.getSmart();
        mEmergency = event.getEmergency();
        mWeekdays = event.getWeekdays();
        mWeekend = event.getWeekend();
    }
}
