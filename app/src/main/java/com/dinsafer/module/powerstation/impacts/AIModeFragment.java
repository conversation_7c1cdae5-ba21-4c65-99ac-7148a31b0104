package com.dinsafer.module.powerstation.impacts;

import static com.blankj.utilcode.util.BarUtils.getStatusBarHeight;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.location.Address;
import android.location.Geocoder;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.Layout;
import android.text.TextUtils;
import android.transition.ChangeBounds;
import android.transition.Transition;
import android.transition.TransitionManager;
import android.view.DisplayCutout;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.ViewTreeObserver;
import android.view.WindowInsets;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.viewpager.widget.ViewPager;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.dinsafer.aop.annotations.Safer;
import com.dinsafer.common.BmtManager;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dialog.CommonAlertDialog;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAiModeBinding;
import com.dinsafer.dinnet.databinding.LayoutAiChartControlBinding;
import com.dinsafer.dinnet.databinding.LayoutAiEditPageBinding;
import com.dinsafer.dinnet.databinding.LayoutAiModeDetailsPageBinding;
import com.dinsafer.dinnet.databinding.LayoutEmaldoAiRetrievingBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.RMFKeyValueModel;
import com.dinsafer.module.powerstation.dialog.PVPreferenceSheet;
import com.dinsafer.module.powerstation.dialog.ReserveModeFunctionPopup;
import com.dinsafer.module.powerstation.event.AIChargeModeEvent;
import com.dinsafer.module.powerstation.event.AIMainChargeModeEvent;
import com.dinsafer.module.powerstation.event.ScheduledModeEvent;
import com.dinsafer.module.powerstation.impacts.report.PSReportFragment;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.dinsafer.module.powerstation.widget.AIScheduledCardView;
import com.dinsafer.module.powerstation.widget.ai_schedule_mode_view.AIScheduleModeModel;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.LocationHelper;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;


import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class AIModeFragment extends MyBaseFragment<FragmentAiModeBinding> implements IDeviceCallBack {

    private static final String TAG = AIModeFragment.class.getSimpleName();

    private CommonPagerAdapter mAIEditAdapter;
    private ArrayList<BaseFragment> mAIEditFragments;
    private int weatherLastType = -1;
    private final Handler handler = new Handler();
    private Runnable repeatRunnable;

    private String mDeviceId;
    private String subcategory;
    private Device mPSDevice;
    private boolean isDealt;
    private boolean isEdit = false;
    private AIScheduledCardView mAIScheduledCardView;
    private boolean isGridToBattery;
    private boolean isInitialized = true;

    private final Map<String, Object> mHTTPDataMap = new HashMap<>();
    // 只有当数据保存成功之后才会改变（原始数据）
    private Map<String, Object> mIotScheduleDataMap = new HashMap<>();
    private final AtomicInteger mResultCount = new AtomicInteger(2);

    private ReserveModeFunctionPopup mReserveModeFunctionPopup;
    private PVPreferenceSheet mPvPreferenceSheet;
    private int mPVPreference = -1;
    private double mLatitude;
    private double mLongitude;
    private String mAdminArea;
    private boolean isSupportPVAndLocation;

    private boolean isChartControlSolar = true;
    private boolean isChartControlPrice = true;
    private boolean isChartControlPriceLevel = true;
    private boolean isChartControlCharge = true;
    private boolean isManualOverrideEnabled = false;

    public static final int AI_MODE = 0;
    public static final int MANUAL_MODE = 1;
    public static final int NEGATIVE_ELECTRICITY_PRICE_MODEL_LIGHT = 2;
    public static final int NEGATIVE_ELECTRICITY_PRICE_MODEL_NIGHT = 3;

    private final static int WEATHER_PARTLY_CLOUDY = 0;    // 部分多云
    private final static int WEATHER_SUNNY = 1;    // 晴天
    private final static int WEATHER_CLOUDY = 2;    // 阴天
    private final static int WEATHER_FOGY = 3;    // 雾
    private final static int WEATHER_RAINY = 4;    // 下雨（使用gif+json文件叠加）
    private final static int WEATHER_SNOW = 5;    // 下雪（使用gif+json文件叠加）
    private final static int WEATHER_RAINY_SNOW = 6;    // 雨夹雪（使用gif+json文件叠加）

    private final String[] mPVStatus = new String[]{
            "power_json/weather_sunny.json",
            "power_json/weather_cloudy.json",
            "power_json/weather_partly_cloudy.json",
            "power_json/animation_weather_fogy.json",
            "power_json/animation_weather_rainy.json",
            "power_json/animation_weather_snow.json"
    };

    private int chartIndex = -1;
    private int startTime;
    private int endTime;
    private int tempSmart = 30;
    private int tempEmergency = 70;
    private int currentIotChargeDischarges;
    // 真实数据
    private Integer[] tempIotWeekdays;
    private int statusBarHeightPortrait;

    private LayoutEmaldoAiRetrievingBinding mRetrievingBinding;

    public static AIModeFragment newInstance(String deviceId, String subcategory, boolean isGridToBattery) {
        AIModeFragment fragment = new AIModeFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY, isGridToBattery);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ai_mode;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        BmtManager.getInstance().stopPolling();
        EventBus.getDefault().register(this);
        initParams();

        if (savedInstanceState == null && getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            statusBarHeightPortrait = getStatusBarHeight();
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (isSupportPVAndLocation) {
            BmtManager.getInstance().getPVDist(mPSDevice);
        }
        getHomeLocation();
        getChargingDischargingPlansV2();
        getCustomSchedule();
        repeatRunnable = new Runnable() {
            @Override
            public void run() {
                mBinding.iconAiModeSupportHelp.playAnimation(); // 播放动画
                handler.postDelayed(repeatRunnable, 62000); // 60 秒后再次执行
            }
        };
        handler.postDelayed(repeatRunnable, 0);
        CornerClipHelper clipHelper = new CornerClipHelper(mBinding.viewAnimHelper, 24, 24);
        mBinding.viewAnimHelper.addOnLayoutChangeListener((v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) -> {
            clipHelper.updateClipPath();
        });
        mBinding.viewAnimHelper.setTag(R.id.clip_helper, clipHelper);
        showTimeOutLoadinFramgmentWithErrorAlert();
    }

    // 获取家庭位置
    private void getHomeLocation() {
        DinSDK.getHomeInstance().getHomeLocation(HomeManager.getInstance().getCurrentHome().getHomeID(),
                new IDefaultCallBack2<>() {
                    @Override
                    public void onSuccess(HomeLocationResponse.ResultBean resultBean) {
                        if (resultBean != null) {
                            mLatitude = resultBean.getLatitude();
                            mLongitude = resultBean.getLongitude();
                            resetLocation();
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                    }
                });
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        subcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, subcategory);
        DDLog.i(TAG, "mDeviceId: " + mDeviceId);
        isGridToBattery = bundle.getBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
            isSupportPVAndLocation = BmtUtil.isSupportPVAndLocation(mPSDevice);
        }
    }

    @Override
    public boolean onBackPressed() {
        if (isEdit) {
            showExitEditMode();
            return true;
        }
        return super.onBackPressed();
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        Glide.with(this).clear(mBinding.lavPvStatusFront);
        mBinding.lavPvStatus.cancelAnimation();
        BmtManager.getInstance().startPolling();
        handler.removeCallbacks(repeatRunnable);
        super.onDestroyView();
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.iconMore.setOnClickListener(v -> showFunctionPopup());
        mBinding.barEditClose.setOnClickListener(v -> showExitEditMode());
        mBinding.tvSave.setOnClickListener(v -> {
            initMainView();
            saveIotData();
        });
        mBinding.llInitialize.setOnClickListener(v -> showInitialized());
        mBinding.switchToLandscapeMode.setOnClickListener(v -> {
            aiDataManager.clearHighlight();
            getDelegateActivity().addCommonFragment(AILandscapeModeFragment.newInstance(mHTTPDataMap,mIotScheduleDataMap,statusBarHeightPortrait,isSupportPVAndLocation,mPVPreference));
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        });
        WindowInsets insets = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            insets = activity.getWindow().getDecorView().getRootWindowInsets();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P && insets != null) {
                DisplayCutout displayCutout = insets.getDisplayCutout();
                if (displayCutout != null) {
                    List<Rect> list = displayCutout.getBoundingRects();
                    if (list != null) {
                        if (list.size() > 0 && displayCutout.getSafeInsetTop() > 0) {
                            WindowManager.LayoutParams attributes = activity.getWindow().getAttributes();
                            attributes.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
                            activity.getWindow().setAttributes(attributes);
                        }
                    }
                }
            }
        }
    }

    private void setMainPageWeatherBg(int type) {
        if (type == weatherLastType) return;
        weatherLastType = type;
        mBinding.lavPvStatusFront.setVisibility(View.GONE);
        switch (type) {
            case WEATHER_SUNNY:
                mBinding.lavPvStatus.setAnimation(mPVStatus[0]);
                break;
            case WEATHER_CLOUDY:
                mBinding.lavPvStatus.setAnimation(mPVStatus[1]);
                break;
            case WEATHER_PARTLY_CLOUDY:
                mBinding.lavPvStatus.setAnimation(mPVStatus[2]);
                break;
            case WEATHER_FOGY:
                mBinding.lavPvStatus.setAnimation(mPVStatus[3]);
                break;
            case WEATHER_RAINY:
                mBinding.lavPvStatusFront.setVisibility(View.VISIBLE);
                mBinding.lavPvStatusFront.setAlpha(0.15f);
                Glide.with(this).asGif().load(R.drawable.weather_rain)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .into(mBinding.lavPvStatusFront);
                mBinding.lavPvStatus.setAnimation(mPVStatus[4]);
                break;
            case WEATHER_SNOW:
                mBinding.lavPvStatusFront.setVisibility(View.VISIBLE);
                mBinding.lavPvStatusFront.setAlpha(0.4f);
                Glide.with(this).asGif().load(R.drawable.weather_snowing)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .into(mBinding.lavPvStatusFront);
                mBinding.lavPvStatus.setAnimation(mPVStatus[5]);
                break;
            case WEATHER_RAINY_SNOW:
                mBinding.lavPvStatusFront.setVisibility(View.VISIBLE);
                mBinding.lavPvStatusFront.setAlpha(0.2f);
                Glide.with(this).asGif().load(R.drawable.weather_rain_snow)
                        .diskCacheStrategy(DiskCacheStrategy.NONE)
                        .into(mBinding.lavPvStatusFront);
                mBinding.lavPvStatus.setAnimation(mPVStatus[4]);
                break;
            default:
                DDLog.d(TAG, "select weather is null !");
                break;
        }
        mBinding.lavPvStatus.playAnimation();
    }

    /******************************************************展示详细数据********************************************************/

    private void showAiModeDetails(int index) {
        if (detailsBinding.aiDetailsPageLayout.getVisibility() != View.VISIBLE) {
            startDetailsAnimation();
        }
        detailsBinding.aiDetailsInstructions.setMaxLines(2);
        detailsBinding.aiDetailsInstructionsOpen.setRotation(0f);
        int DetailsType ,chargeType;
        detailsBinding.currencyUnit.setLocalText(unitPrice);
        handleTextViewLong();
        // 获取当前显示的时间
        startTime = DDDateUtil.getHourByTimestamps(gmTime * 1000 + index * 3600000L, timeZone);
        endTime = DDDateUtil.getHourByTimestamps(gmTime * 1000 + (index + 1) * 3600000L, timeZone);
        detailsBinding.aiDetailsTime.setLocalText(TimeUtil.formatHour(startTime)
                + ":00 - " + TimeUtil.formatHour(endTime) + ":00");
        Integer[] weekdays = (Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null);
        if (isEdit) weekdays = tempIotWeekdays;
        DDLog.i(TAG,"isEdit: " + isEdit + "\n当前详情使用数据： " + Arrays.toString(weekdays));
        currentIotChargeDischarges = weekdays[startTime];
        // 若当前为编辑模式，同步到编辑模式下
        if (isEdit) {
            editBinding.aiEditCurrentTime.setLocalText(TimeUtil.formatHour(startTime)
                    + ":00 - " + TimeUtil.formatHour(endTime) + ":00");
            EventBus.getDefault().post(new AIMainChargeModeEvent(currentIotChargeDischarges));
        }
        if (weekdays[startTime] == -128) {
            chargeType = aiDataManager.getHopeChargeDischarges().get(index);
            if (aiDataManager.getMarketPrices().get(index) < 0f)
                DetailsType = NEGATIVE_ELECTRICITY_PRICE_MODEL_LIGHT;
            else
                DetailsType = AI_MODE;
        } else {
            chargeType = weekdays[startTime];
            if (aiDataManager.getMarketPrices().get(index) < 0f)
                DetailsType = NEGATIVE_ELECTRICITY_PRICE_MODEL_NIGHT;
            else
                DetailsType = MANUAL_MODE;
        }
        DDLog.i(TAG,"getHopeChargeDischarges: " + aiDataManager.getHopeChargeDischarges().get(index) + "    getUserPrices: " + aiDataManager.getUserPrices().get(index) +
                "   weekdays: " + weekdays[startTime]);
        showAiModeDetailsType(DetailsType, aiDataManager.getPlans().get(index), chargeType);
        totalForecastSolar(aiDataManager.getForecastSolars(),aiDataManager.getForecastSolars().get(index),false);
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        BigDecimal bd = new BigDecimal(String.valueOf(aiDataManager.getMarketPrices().get(index)));
        String result = bd.stripTrailingZeros().toPlainString();
        detailsBinding.marketPricesValue.setLocalText(result);
        setMainPageWeatherBg(aiDataManager.getConditions().get(index));
        DDLog.i(TAG, "showAiModeDetails： startTime: " + startTime + "    endTime: " + endTime +
                "\nDetailsType: " + DetailsType + "    plans: " + aiDataManager.getPlans().get(index) +
                "\nIndex: " + index + "   currentIotChargeDischarges" + currentIotChargeDischarges +
                "\ngetForecastSolars: " + String.valueOf(aiDataManager.getForecastSolars().get(index)));
    }

    /**
     * 展示详情动画
     */
    private void startDetailsAnimation() {
        if (mBinding.llEditAiTitle.getVisibility() == View.GONE) return;
        detailsBinding.aiDetailsPageLayout.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                detailsBinding.aiDetailsPageLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                // 获取标题布局高度
                final int aiDetailsHeight = detailsBinding.aiDetailsPageLayout.getHeight();
                final int titleHeight = mBinding.llEditAiTitle.getHeight();
                detailsBinding.aiDetailsPageLayout.setVisibility(View.VISIBLE);
                // ScrollView
                ConstraintLayout.LayoutParams scrollViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
                scrollViewParams.topToBottom = R.id.rl_title;
                scrollViewParams.topMargin = titleHeight;
                mBinding.aiModeChartScrollView.setLayoutParams(scrollViewParams);
                ValueAnimator marginAnimator = ValueAnimator.ofInt(titleHeight, 0).setDuration(500);
                marginAnimator.addUpdateListener(animation -> {
                    int marginValue = (Integer) animation.getAnimatedValue();
                    ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
                    params.topMargin = marginValue;
                    mBinding.aiModeChartScrollView.setLayoutParams(params);
                });
                // ChartView
                ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
                chartViewParams.topMargin = aiDetailsHeight;
                mBinding.aiModeChartView.setLayoutParams(chartViewParams);
                ValueAnimator chartAnimator = ValueAnimator.ofInt(0, aiDetailsHeight).setDuration(500);
                chartAnimator.addUpdateListener(animation -> {
                    int marginValue = (Integer) animation.getAnimatedValue();
                    ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
                    params.topMargin = marginValue;
                    mBinding.aiModeChartView.setLayoutParams(params);
                });
                // 同步执行动画
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(marginAnimator,
                        ObjectAnimator.ofFloat(mBinding.llEditAiTitle, "alpha", 1f, 0f).setDuration(500),
                        chartAnimator,
                        ObjectAnimator.ofFloat(detailsBinding.aiDetailsPageLayout, "alpha", 0f, 1f).setDuration(500));
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        mBinding.llEditAiTitle.setVisibility(View.GONE);
                        ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
                        chartViewParams.topToBottom = R.id.ai_details_page;
                        chartViewParams.topMargin = 0;
                        mBinding.aiModeChartView.setLayoutParams(chartViewParams);
                    }
                });
                animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
                animatorSet.start();
            }
        });
    }

    /**
     * 退出详情动画
     */
    private void exitDetailsAnimation() {
        if (mBinding.llEditAiTitle.getVisibility() == View.VISIBLE) return;
        // 获取标题布局高度
        final int aiDetailsHeight = detailsBinding.aiDetailsPageLayout.getHeight();
        final int titleHeight = mBinding.llEditAiTitle.getHeight();
        mBinding.llEditAiTitle.setVisibility(View.VISIBLE);
        // ScrollView
        ConstraintLayout.LayoutParams scrollViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
        scrollViewParams.topToBottom = R.id.rl_title;
        scrollViewParams.topMargin = 0;
        mBinding.aiModeChartScrollView.setLayoutParams(scrollViewParams);
        ValueAnimator marginAnimator = ValueAnimator.ofInt(0, titleHeight).setDuration(500);
        marginAnimator.addUpdateListener(animation -> {
            int marginValue = (Integer) animation.getAnimatedValue();
            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
            params.topMargin = marginValue;
            mBinding.aiModeChartScrollView.setLayoutParams(params);
        });
        // ChartView
        ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
        chartViewParams.topToBottom = R.id.ll_top_chart_layout;
        chartViewParams.topMargin = aiDetailsHeight;
        mBinding.aiModeChartView.setLayoutParams(chartViewParams);
        ValueAnimator chartAnimator = ValueAnimator.ofInt(aiDetailsHeight, 0).setDuration(500);
        chartAnimator.addUpdateListener(animation -> {
            int marginValue = (Integer) animation.getAnimatedValue();
            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
            params.topMargin = marginValue;
            mBinding.aiModeChartView.setLayoutParams(params);
        });
        // 同步执行动画
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(marginAnimator,
                ObjectAnimator.ofFloat(mBinding.llEditAiTitle, "alpha", 0f, 1f).setDuration(500),
                chartAnimator,
                ObjectAnimator.ofFloat(detailsBinding.aiDetailsPageLayout, "alpha", 1f, 0f).setDuration(500));
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                detailsBinding.aiDetailsPageLayout.setVisibility(View.INVISIBLE);
                ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
                chartViewParams.topToBottom = R.id.ll_edit_ai_title;
                chartViewParams.topMargin = 0;
                mBinding.aiModeChartScrollView.setLayoutParams(chartViewParams);
            }
        });
        animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
        animatorSet.start();
    }

    private void showAiModeDetailsType(int type, int plans, int chargeType) {
        int[] colors = AIColorUtil.getAIColor(getContext());
        float[] positions = AIColorUtil.getAIColorPosition();
        detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_mode_details_charge_discharge));
        DDLog.i(TAG,"showAiModeDetailsType -->  type: " + type + "  plans: " + plans +" chargeType: " + chargeType +
                "   chartIndex: " + chartIndex + "  C1: " + (float)aiDataManager.getC1() / 100 +
                "  RelativePriceNorms: " + aiDataManager.getRelativePriceNorms().get(chartIndex));
        switch (type) {
            case AI_MODE:
            case NEGATIVE_ELECTRICITY_PRICE_MODEL_LIGHT:
                detailsBinding.aiDetailsBatteryMode.setAIColorShader(colors, positions);
                detailsBinding.aiDetailsLoadFirst.setAIColorShader(colors, positions);
                detailsBinding.aiDetailsLoadFirst.setSelected(true);
                detailsBinding.aiDetailsTimeTip.setVisibility(View.GONE);
                detailsBinding.aiDetailsInstructionsLayout.setVisibility(View.VISIBLE);
                switch (plans) {
                    case 0:
                        if (chargeType == 0) {
                            detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.No_Charge_Nor_Discharge));
                            detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_mode_details_discharge));
                        } else if (chargeType < 0) {
                            detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Battery_Discharge));
                            if ((float)aiDataManager.getS2() / 100 < aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_8));
                            else if ((float)aiDataManager.getS1() / 100 < aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_4));
                        } else {
                            detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Charge_with_grid));
                            if ((float)aiDataManager.getC1() / 100 > aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_1));
                            else if ((float)aiDataManager.getC2() / 100 > aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_6));
                            else if ((float)aiDataManager.getC3() / 100 > aiDataManager.getRelativePriceNorms().get(chartIndex))
                                detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_7));
                        }
                        break;
                    case 1:
                        detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Await_Sun_Charge));
                        detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_2));
                        break;
                    case 2:
                        detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Await_Lower_Utility_Charge));
                        detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_scheduled_detail_content_3));
                        break;
                }
                if (type == AI_MODE) {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.GONE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.GONE);
                } else {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.VISIBLE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.VISIBLE);
                }
                break;
            case MANUAL_MODE:
            case NEGATIVE_ELECTRICITY_PRICE_MODEL_NIGHT:
                detailsBinding.aiDetailsBatteryMode.removeAIShader(getColor(R.color.color_white_01));
                detailsBinding.aiDetailsLoadFirst.removeAIShader(getColor(R.color.color_white_01));
                detailsBinding.aiDetailsLoadFirst.setSelected(false);
                detailsBinding.aiDetailsTimeTip.setVisibility(View.VISIBLE);
                if (chargeType == 0) {
                    detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.ps_is_no_charge_nor_discharge));
                    detailsBinding.aiDetailsInstructions.setLocalText(getString(R.string.ai_mode_details_discharge));
                } else if (chargeType < 0) {
                    detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Battery_Discharge));
                } else {
                    detailsBinding.aiDetailsBatteryMode.setLocalText(getString(R.string.Charge_with_grid));
                }
                if (type == MANUAL_MODE) {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.GONE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.GONE);
                } else {
                    detailsBinding.tvAiDetailsWarning.setVisibility(View.VISIBLE);
                    detailsBinding.tvAiDetailsWarningIcon.setVisibility(View.VISIBLE);
                }
                break;
        }
    }

    private boolean isExpanded = false;

    private void handleTextViewLong() {
        detailsBinding.aiDetailsInstructions.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                // 移除监听避免重复调用
                detailsBinding.aiDetailsInstructions.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                Layout layout = detailsBinding.aiDetailsInstructions.getLayout();
                if (layout != null) {
                    boolean showButton = false;
                    int lineCount = layout.getLineCount();
                    if (lineCount > 0) {
                        int lastLineIndex = lineCount - 1;
                        if (layout.getEllipsisCount(lastLineIndex) > 0) {
                            showButton = true;
                        }
                    }
                    detailsBinding.aiDetailsInstructionsOpen.setVisibility(showButton ? View.VISIBLE : View.INVISIBLE);
                }
            }
        });

        detailsBinding.aiDetailsInstructionsOpen.setOnClickListener(v -> {
            isExpanded = !isExpanded;
            Transition transition = new ChangeBounds().setDuration(300);
            transition.setInterpolator(new AccelerateDecelerateInterpolator());
            ViewGroup parent = (ViewGroup) detailsBinding.aiDetailsInstructions.getParent();
            TransitionManager.beginDelayedTransition(parent, transition);
            if (isExpanded) {
                detailsBinding.aiDetailsInstructions.setMaxLines(Integer.MAX_VALUE);
            } else {
                detailsBinding.aiDetailsInstructions.setMaxLines(2);
            }
            detailsBinding.aiDetailsInstructionsOpen.animate().rotation(isExpanded ? 180f : 0f).setDuration(300).start();
        });
    }

    /****************************************************** 编辑模式 ********************************************************/

    private void enterEditMode() {
        setEdit(true);
        isChartControlCharge = isChartControlSolar = isChartControlPriceLevel = isChartControlPrice = true;
        aiDataManager.setDrawChargeStatus(isChartControlCharge);
        aiDataManager.setDrawSolar(isChartControlSolar);
        aiDataManager.setDrawPriceLevel(isChartControlPriceLevel);
        aiDataManager.setDrawPrice(isChartControlPrice);
        editBinding.layoutAiEditPageLayout.setVisibility(View.VISIBLE);
        initDaysTypeVp();
        if (detailsBinding.aiDetailsPageLayout.getVisibility() == View.GONE) {
            startFirstEditAnimation();
            return;
        }
        // 获取标题布局高度
        final int chartTopHeight = mBinding.llTopChartLayout.getHeight();
        // DetailsView
        ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) detailsBinding.aiDetailsPageLayout.getLayoutParams();
        chartViewParams.topToTop = R.id.ll_constraintLayout;
        chartViewParams.topMargin = chartTopHeight;
        detailsBinding.aiDetailsPageLayout.setLayoutParams(chartViewParams);
        ValueAnimator chartAnimator = ValueAnimator.ofInt(chartTopHeight, 0).setDuration(500);
        chartAnimator.addUpdateListener(animation -> {
            int marginValue = (Integer) animation.getAnimatedValue();
            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) detailsBinding.aiDetailsPageLayout.getLayoutParams();
            params.topMargin = marginValue;
            detailsBinding.aiDetailsPageLayout.setLayoutParams(params);
        });

        AnimatorSet set = new AnimatorSet();
        set.playTogether(
                chartAnimator,
                ObjectAnimator.ofFloat(mBinding.llTopChartLayout, "alpha", 1f, 0f),
                ObjectAnimator.ofFloat(mBinding.aiModeMainTittleBtn, "alpha", 1f,0f),
                ObjectAnimator.ofFloat(controlBinding.aiChartControlLayout, "alpha", 1f,0f),
                ObjectAnimator.ofFloat(mBinding.iconAiModeSupportHelp, "alpha", 1f,0f),
                ObjectAnimator.ofFloat(mBinding.aiModeEditTittleBtn, "alpha", 0f,1f),
                ObjectAnimator.ofFloat(editBinding.layoutAiEditPageLayout, "alpha", 0f,1f),
                ObjectAnimator.ofFloat(mBinding.tvTitle, "alpha", 0f,1f)
        );
        set.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                mBinding.aiModeEditTittleBtn.setVisibility(View.VISIBLE);
                mBinding.tvTitle.setVisibility(View.VISIBLE);
                mBinding.llInitialize.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mBinding.aiModeMainTittleBtn.setVisibility(View.GONE);
                controlBinding.aiChartControlLayout.setVisibility(View.GONE);
                mBinding.iconAiModeSupportHelp.setVisibility(View.GONE);
                mBinding.llTopChartLayout.setVisibility(View.GONE);
                isEdit = true;
                if (chartIndex == -1) aiDataManager.setDefaultHighlight();
                else showAiModeDetails(chartIndex);
                DDLog.i(TAG, "chartIndex: " + chartIndex );
                mBinding.hideChartPoint.setClickable(!isEdit);
            }
        });
        set.setInterpolator(new AccelerateDecelerateInterpolator());
        set.setDuration(500);
        set.start();
    }

    /**
     * 展示详情动画
     */
    private void startFirstEditAnimation() {
        if (mBinding.llTopChartLayout.getVisibility() == View.GONE) return;
        detailsBinding.aiDetailsPageLayout.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                detailsBinding.aiDetailsPageLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                // 获取标题布局高度
                final int titleHeight = mBinding.llEditAiTitle.getHeight();
                final int chartTopHeight = mBinding.llTopChartLayout.getHeight();
                detailsBinding.aiDetailsPageLayout.setVisibility(View.VISIBLE);
                mBinding.llTopChartLayout.setVisibility(View.GONE);
                // ScrollView
                ConstraintLayout.LayoutParams scrollViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
                scrollViewParams.topToBottom = R.id.rl_title;
                scrollViewParams.topMargin = titleHeight;
                mBinding.aiModeChartScrollView.setLayoutParams(scrollViewParams);
                ValueAnimator marginAnimator = ValueAnimator.ofInt(titleHeight, 0).setDuration(500);
                marginAnimator.addUpdateListener(animation -> {
                    int marginValue = (Integer) animation.getAnimatedValue();
                    ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
                    params.topMargin = marginValue;
                    mBinding.aiModeChartScrollView.setLayoutParams(params);
                });
                // DetailsView
                ConstraintLayout.LayoutParams detailsViewParams = (ConstraintLayout.LayoutParams) detailsBinding.aiDetailsPageLayout.getLayoutParams();
                detailsViewParams.topToTop = R.id.ll_constraintLayout;
                detailsViewParams.topMargin = chartTopHeight;
                detailsBinding.aiDetailsPageLayout.setLayoutParams(detailsViewParams);
                ValueAnimator detailsAnimator = ValueAnimator.ofInt(chartTopHeight, 0).setDuration(500);
                detailsAnimator.addUpdateListener(animation -> {
                    int marginValue = (Integer) animation.getAnimatedValue();
                    ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) detailsBinding.aiDetailsPageLayout.getLayoutParams();
                    params.topMargin = marginValue;
                    detailsBinding.aiDetailsPageLayout.setLayoutParams(params);
                });
                // 同步执行动画
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(
                        marginAnimator,
                        detailsAnimator,
                        ObjectAnimator.ofFloat(mBinding.llEditAiTitle, "alpha", 1f, 0f),
                        ObjectAnimator.ofFloat(detailsBinding.aiDetailsPageLayout, "alpha", 0f, 1f),
                        ObjectAnimator.ofFloat(mBinding.llTopChartLayout, "alpha", 1f, 0f),
                        ObjectAnimator.ofFloat(mBinding.aiModeMainTittleBtn, "alpha", 1f,0f),
                        ObjectAnimator.ofFloat(controlBinding.aiChartControlLayout, "alpha", 1f,0f),
                        ObjectAnimator.ofFloat(mBinding.iconAiModeSupportHelp, "alpha", 1f,0f),
                        ObjectAnimator.ofFloat(mBinding.aiModeEditTittleBtn, "alpha", 0f,1f),
                        ObjectAnimator.ofFloat(editBinding.layoutAiEditPageLayout, "alpha", 0f,1f),
                        ObjectAnimator.ofFloat(mBinding.tvTitle, "alpha", 0f,1f)
                );
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
                        chartViewParams.topToBottom = R.id.ll_constraintLayout;
                        chartViewParams.topMargin = detailsBinding.aiDetailsPageLayout.getHeight();
                        mBinding.aiModeChartView.setLayoutParams(chartViewParams);
                        super.onAnimationStart(animation);
                        mBinding.aiModeEditTittleBtn.setVisibility(View.VISIBLE);
                        mBinding.tvTitle.setVisibility(View.VISIBLE);
                        mBinding.llInitialize.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        mBinding.llEditAiTitle.setVisibility(View.GONE);
                        mBinding.aiModeMainTittleBtn.setVisibility(View.GONE);
                        controlBinding.aiChartControlLayout.setVisibility(View.GONE);
                        mBinding.iconAiModeSupportHelp.setVisibility(View.GONE);
                        isEdit = true;
                        mBinding.hideChartPoint.setClickable(!isEdit);
                    }
                });
                animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
                animatorSet.setDuration(500);
                animatorSet.start();
            }
        });
    }

    private void showExitEditMode() {
        if (Arrays.toString(tempIotWeekdays).equals(Arrays.toString((Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null)))
                && tempEmergency == DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1)
                && tempSmart == DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1)) {
            isEdit = false;
            mBinding.hideChartPoint.setClickable(!isEdit);
            initMainView();
            setEdit(false);
            DDLog.i(TAG, "tempIotWeekdays & WEEKDAYS: " +Arrays.toString(tempIotWeekdays).equals(Arrays.toString((Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null))));
            return;
        }
        CommonAlertDialog.createBuilder(getDelegateActivity())
                .setContentTxt(Local.s(getString(R.string.ai_mode_exit_edit)))
                .setConfirmTxt(getString(R.string.Discard))
                .setCancelTxt(getString(R.string.Cancel))
                .setAutoDismiss(true)
                .setShowCancel(true)
                .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                    @Override
                    public void onConfirm(CommonAlertDialog dialog) {
                        initMainView();
                        isEdit = false;
                        setEdit(false);
                        mBinding.hideChartPoint.setClickable(!isEdit);
                        System.arraycopy((Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null), 0, tempIotWeekdays, 0, tempIotWeekdays.length);
                        tempEmergency = DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1);
                        tempSmart = DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1);
                        DDLog.i(TAG, "tempIotWeekdays" + Arrays.toString(tempIotWeekdays));
                    }

                    @Override
                    public void onCancel(CommonAlertDialog dialog) {

                    }
                })
                .builder().show();
    }

    private void setEdit(boolean isEdit) {
        if (aiDataManager != null) {
            aiDataManager.setEdit(isEdit);
        }
    }

    /**
     * 初始化主界面界面 (退出编辑模式)
     */
    private void initMainView() {
        aiDataManager.clearHighlight();
        setMainPageWeatherBg(aiDataManager.getConditions().get(0));
        mBinding.llEditAiTitle.setVisibility(View.VISIBLE);
        mBinding.llEditAiTitle.setAlpha(1f);
        mBinding.llTopChartLayout.setVisibility(View.VISIBLE);
        mBinding.llTopChartLayout.setAlpha(1f);
        detailsBinding.aiDetailsPageLayout.setVisibility(View.INVISIBLE);
        // ScrollView
        ConstraintLayout.LayoutParams scrollViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartScrollView.getLayoutParams();
        scrollViewParams.topToBottom = R.id.ll_edit_ai_title;
        scrollViewParams.topMargin = 0;
        mBinding.aiModeChartScrollView.setLayoutParams(scrollViewParams);
        // ChartView
        ConstraintLayout.LayoutParams chartViewParams = (ConstraintLayout.LayoutParams) mBinding.aiModeChartView.getLayoutParams();
        chartViewParams.topToBottom = R.id.ll_top_chart_layout;
        chartViewParams.topMargin = 0;
        mBinding.aiModeChartView.setLayoutParams(chartViewParams);
        // DetailsView
        ConstraintLayout.LayoutParams detailsViewParams = (ConstraintLayout.LayoutParams) detailsBinding.aiDetailsPageLayout.getLayoutParams();
        detailsViewParams.topToTop = ConstraintLayout.LayoutParams.UNSET;   // 清除旧的顶部约束
        detailsViewParams.topToBottom = R.id.ll_top_chart_layout;
        detailsViewParams.topMargin = 0;
        detailsBinding.aiDetailsPageLayout.setLayoutParams(detailsViewParams);

        AnimatorSet set = new AnimatorSet();
        set.playTogether(
                ObjectAnimator.ofFloat(mBinding.aiModeMainTittleBtn, "alpha", 0f,1f),
                ObjectAnimator.ofFloat(controlBinding.aiChartControlLayout, "alpha", 0f,1f),
                ObjectAnimator.ofFloat(mBinding.iconAiModeSupportHelp, "alpha", 0f,1f),
                ObjectAnimator.ofFloat(mBinding.aiModeEditTittleBtn, "alpha", 1f,0f),
                ObjectAnimator.ofFloat(editBinding.layoutAiEditPageLayout, "alpha", 1f,0f),
                ObjectAnimator.ofFloat(mBinding.tvTitle, "alpha", 1f,0f)
        );
        set.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                mBinding.aiModeMainTittleBtn.setVisibility(View.VISIBLE);
                controlBinding.aiChartControlLayout.setVisibility(View.VISIBLE);
                mBinding.iconAiModeSupportHelp.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mBinding.aiModeEditTittleBtn.setVisibility(View.GONE);
                editBinding.layoutAiEditPageLayout.setVisibility(View.GONE);
                mBinding.tvTitle.setVisibility(View.GONE);
            }
        });
        set.setDuration(600);
        set.start();

        if (checkIsInitialized(tempIotWeekdays)) {
            isInitialized = false;
            mBinding.llInitialize.setVisibility(View.VISIBLE);
            mBinding.llInitialize.setClickable(true);
            showDataInitView();
        }
    }

    private void showDataInitView() {
        if (isInitialized) {
            mBinding.tvInitialize.setLocalText(getString(R.string.Initialized));
            mBinding.llInitialize.setSelected(true);
            mBinding.ivInitialize.setVisibility(View.VISIBLE);
            mBinding.llInitialize.setClickable(false);
        } else {
            mBinding.tvInitialize.setLocalText(getString(R.string.Initialize));
            mBinding.ivInitialize.setVisibility(View.GONE);
            mBinding.llInitialize.setSelected(false);
            mBinding.llInitialize.setClickable(true);
        }
    }

    /**
     * 初始化IOT数据，设置为-128
     */
    private void setInitializedAIScheduledMode() {
        Map<String, Object> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, BmtCmd.SET_RESERVE_MODE);
        params.put(BmtDataKey.RESERVE_MODE, 3);
        params.put(BmtDataKey.EMERGENCY, DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.EMERGENCY, -1));
        params.put(BmtDataKey.SMART, DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.SMART, -1));
        for (int i = 0; i < 24; i++) {
            tempIotWeekdays[i] = -128;
        }
        params.put(BmtDataKey.WEEKDAYS, integerToInt(tempIotWeekdays));
        params.put(BmtDataKey.WEEKEND, integerToInt(tempIotWeekdays));
        mPSDevice.submit(params);
        showTimeOutLoadinFramgmentWithErrorAlert();
        DDLog.i(TAG,"setInitializedAIScheduledMode: " + Arrays.toString(tempIotWeekdays));
    }

    /**
     * 保存IOT数据
     */
    private void saveIotData() {
        Map<String, Object> params = new HashMap<>();
        params.put(PSKeyConstant.CMD, BmtCmd.SET_RESERVE_MODE);
        params.put(BmtDataKey.RESERVE_MODE, 3);
        params.put(BmtDataKey.EMERGENCY, tempEmergency);
        params.put(BmtDataKey.SMART, tempSmart);
        params.put(BmtDataKey.WEEKDAYS, integerToInt(tempIotWeekdays));
        params.put(BmtDataKey.WEEKEND, integerToInt(tempIotWeekdays));
        mPSDevice.submit(params);
        showTimeOutLoadinFramgmentWithErrorAlert();
        DDLog.i(TAG,"saveIotData: " + Arrays.toString(tempIotWeekdays));
    }

    /**
     * 初始化Viewpage
     */
    private void initDaysTypeVp() {
        mAIEditFragments = new ArrayList<>();
        mAIEditFragments.add(AIModeEditCurrentFragment.newInstance(currentIotChargeDischarges, DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1), DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1)));
        mAIEditFragments.add(AIModeEditRangeFragment.newInstance(DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1), DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1),aiDataManager.getC1(),aiDataManager.getC2(),aiDataManager.getC3(),aiDataManager.getS1(),aiDataManager.getS2()));
        mAIEditAdapter = new CommonPagerAdapter(getChildFragmentManager(), mAIEditFragments);
        editBinding.aiEditPageView.setAdapter(mAIEditAdapter);
        editBinding.aiEditPageView.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                editBinding.aiEditPageView.requestLayout();
                setIndicatorSelected(position, false);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        editBinding.aiEditPageView.setOffscreenPageLimit(2);
        editBinding.aiEditCurrentTimeView.setOnClickListener(v -> setIndicatorSelected(0, true));
        editBinding.aiEditBatteryRangeView.setOnClickListener(v -> setIndicatorSelected(1, true));
        setIndicatorSelected(0, true);
    }

    private void setIndicatorSelected(int position, boolean isSelect) {
        if (isSelect) editBinding.aiEditPageView.setCurrentItem(position);
        if (position == 0) {
            editBinding.aiEditCurrentTime.setAlpha(1f);
            editBinding.aiEditBatteryRange.setAlpha(0.5f);
            editBinding.aiEditCurrentTimeLine.setVisibility(View.VISIBLE);
            editBinding.aiEditBatteryRangeLine.setVisibility(View.GONE);
        } else {
            editBinding.aiEditCurrentTime.setAlpha(0.5f);
            editBinding.aiEditBatteryRange.setAlpha(1f);
            editBinding.aiEditCurrentTimeLine.setVisibility(View.GONE);
            editBinding.aiEditBatteryRangeLine.setVisibility(View.VISIBLE);
        }
    }

    /****************************************************** EventBus收 ********************************************************/

    /**
     * 滑动bar对应值变化
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onScheduledModeEvent(ScheduledModeEvent event) {
        DDLog.d(TAG, "onScheduledModeEvent: " + event);
        if (event.getEmergency()) {
            refreshData(tempIotWeekdays,tempEmergency,event.getReserve());
            tempEmergency = event.getReserve();
        } else {
            refreshData(tempIotWeekdays,tempSmart,event.getReserve());
            tempSmart = event.getReserve();
        }
        DDLog.d(TAG, "更新阈值：tempIotWeekdays: " + Arrays.toString(tempIotWeekdays));
    }

    /**
     * 处理充电模式
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAIChargeModelEvent(AIChargeModeEvent event) {
        DDLog.d(TAG, "onAIChargeModelEvent: " + event);
        if (event.isInitAI()) {
            tempIotWeekdays[startTime] = -128;
        } else {
            if (event.getSelectedMode() == 1) {
                if (event.getSectionType() == 1) {
                    tempIotWeekdays[startTime] = tempEmergency;
                } else if (event.getSectionType() == 2) {
                    tempIotWeekdays[startTime] = tempSmart;
                } else {
                    tempIotWeekdays[startTime] = 100;
                }
            } else if (event.getSelectedMode() == 0) {
                tempIotWeekdays[startTime] = 0;
            } else {
                if (event.getSectionType() == 1) {
                    tempIotWeekdays[startTime] = -tempEmergency;
                } else if (event.getSectionType() == 2) {
                    tempIotWeekdays[startTime] = -tempSmart;
                }
            }
        }
        // 进入编辑模式之后
        showAiModeDetails(chartIndex);
        aiDataManager.showChartData();
        DDLog.d(TAG, "更新阈值：tempIotWeekdays: " + Arrays.toString(tempIotWeekdays));
    }

    /****************************************************** 更多设置 ********************************************************/

    /**
     * 展示更多选项弹窗
     */
    private void showFunctionPopup() {
        if (mReserveModeFunctionPopup == null) {
            mReserveModeFunctionPopup = new ReserveModeFunctionPopup(getContext(), 0, mPVPreference, mAdminArea, isSupportPVAndLocation, isManualOverrideEnabled);
            mReserveModeFunctionPopup.setBindModelOnBindItemClickListener((v, position, model) -> {
                if (model instanceof RMFKeyValueModel) {
                    RMFKeyValueModel rmfKeyValueModel = (RMFKeyValueModel) model;
                    String key = rmfKeyValueModel.getKey();
                    if (key == null) return;
                    if (key.equals(getString(R.string.Manual_Override))) {
                        initEdit();
                        mReserveModeFunctionPopup.dismiss();
                    } else if (key.equals(getString(R.string.Solar_Priority))) {
                        ViewStub viewStub1 = mBinding.aiDetailsPageViewStub.getViewStub();
                        if (null == detailsBinding) {
                            View view = viewStub1.inflate();
                            detailsBinding = DataBindingUtil.bind(view);
                        }
                        showBottomSheet();
                        mReserveModeFunctionPopup.dismiss();
                    } else if (key.equals(getString(R.string.Location))) {
                        showModifyLocationDialog();
                        mReserveModeFunctionPopup.dismiss();
                    }
                }
            });
            mReserveModeFunctionPopup.setOnDismissListener(() -> backgroundAlpha(1f));
        }
        if (!mReserveModeFunctionPopup.isShowing()) {
            mReserveModeFunctionPopup.showAsDropDown(mBinding.iconMore, -(ScreenUtils.getScreenWidth(getContext()) - DensityUtils.dp2px(getContext(), 49)), 0, Gravity.END);
            backgroundAlpha(0.5f);
        }
    }

    private void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp = getDelegateActivity().getWindow().getAttributes();
        lp.alpha = f;
        getDelegateActivity().getWindow().setAttributes(lp);
    }

    // PV分配偏好
    private void showBottomSheet() {
        if (mPvPreferenceSheet == null) {
            mPvPreferenceSheet = new PVPreferenceSheet(0, type -> {
                showTimeOutLoadinFramgmentWithErrorAlert();
                BmtManager.getInstance().setPVDist(mPSDevice, type, 0);
            });
        }
        mPvPreferenceSheet.show(getChildFragmentManager());
    }

    /**
     * 获取选中的数据分段类型
     *
     * @param selectedData
     * @return
     */
    public int getSectionType(List<AIScheduleModeModel> selectedData) {
        if (CollectionUtil.isListEmpty(selectedData)) return 0;
        int sectionType = selectedData.get(0).getSectionType();
        int mode = selectedData.get(0).getMode();
        if (selectedData.size() > 1) {
            for (int i = 1; i < selectedData.size(); i++) {
                if (selectedData.get(i).getSectionType() != sectionType) {
                    sectionType = 0;
                    break;
                }
            }
        }
        return mode < 0 ? (3 - sectionType) : sectionType;
    }

    private void resetLocation() {
        new Thread(() -> {
            Geocoder geocoder = new Geocoder(getContext(), LocalManager.getInstance().getCurrentLocale());
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                geocoder.getFromLocation(mLatitude, mLongitude, 1, addresses -> runOnMainThread(() -> {
                    dealAddresses(addresses);
                    if (mReserveModeFunctionPopup != null) {
                        mReserveModeFunctionPopup.setLocationVal(mAdminArea);
                    }
                }));
            } else {
                try {
                    List<Address> addresses = geocoder.getFromLocation(mLatitude, mLongitude, 1);
                    runOnMainThread(() -> {
                        dealAddresses(addresses);
                        if (mReserveModeFunctionPopup != null) {
                            mReserveModeFunctionPopup.setLocationVal(mAdminArea);
                        }
                    });
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }).start();

    }

    private void dealAddresses(List<Address> addresses) {
        if (CollectionUtil.isListNotEmpty(addresses)) {
            Address address = addresses.get(0);
            String adminArea = address.getAdminArea();
            String subAdminArea = address.getSubAdminArea();
            String locality = address.getLocality();
            String subLocality = address.getSubLocality();
            if (!TextUtils.isEmpty(subAdminArea)) {
                mAdminArea = subAdminArea;
            } else if (!TextUtils.isEmpty(locality)) {
                mAdminArea = locality;
            } else if (!TextUtils.isEmpty(subLocality)) {
                mAdminArea = subLocality;
            } else if (!TextUtils.isEmpty(adminArea)) {
                mAdminArea = adminArea;
            }
        }
    }

    /****************************************************** 弹窗 ********************************************************/

    /**
     * 展示编辑模式是否退出弹窗
     */
    private void showInitialized() {
        CommonAlertDialog.createBuilder(getDelegateActivity())
                .setContentTxt(Local.s(getString(R.string.ai_mode_initialize)))
                .setConfirmTxt(getString(R.string.Initialize))
                .setCancelTxt(getString(R.string.Cancel))
                .setAutoDismiss(true)
                .setShowCancel(true)
                .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                    @Override
                    public void onConfirm(CommonAlertDialog dialog) {
                        if (!isInitialized) {
                            isInitialized = true;
                            showDataInitView();
                            setInitializedAIScheduledMode();
                        }
                        aiDataManager.clearHighlight();
                    }

                    @Override
                    public void onCancel(CommonAlertDialog dialog) {

                    }
                })
                .builder().show();
    }

    // 展示获取位置Dialog
    private void showModifyLocationDialog() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setAutoDissmiss(true)
                .setContentColor(getResources().getColor(R.color.color_white_01))
                .setBackgroundTint(getResColor(R.color.color_brand_dark_03))
                .setOk(Local.s(getResources().getString(R.string.got_it)))
                .setContent(Local.s("Modifying your location will trigger AI model recalibration, reducing prediction accuracy about 7 days."))
                .setOKListener(() -> showDeviceNearby())
                .preBuilder()
                .show();
    }

    /**
     * 附近设备弹窗
     */
    private void showDeviceNearby() {
        CommonAlertDialog.createBuilder(getDelegateActivity())
                .setTitleTxt(Local.s(getString(R.string.is_the_device_nearby)))
                .setContentTxt(Local.s(getString(R.string.is_the_device_nearby_hint)))
                .setConfirmTxt(getString(R.string.Confirm))
                .setCancelTxt(getString(R.string.set_later_in_ai_Mode))
                .setAutoDismiss(true)
                .setShowCancel(true)
                .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                    @Override
                    public void onConfirm(CommonAlertDialog dialog) {
                        PermissionDialogUtil.requestLocationPermission(getMainActivity(),
                                () -> submitLocation());
                    }

                    @Override
                    public void onCancel(CommonAlertDialog dialog) {

                    }
                })
                .builder().show();
    }

    private void submitLocation() {
        LocationHelper.getInstance(getContext()).getLocation(location -> {
            if (location != null) {
                double latitude = location.getLatitude();
                double longitude = location.getLongitude();
                DinSDK.getHomeInstance().bmtSaveLocation(HomeManager.getInstance().getCurrentHome().getHomeID(),
                        latitude, longitude, new IDefaultCallBack() {
                            @Override
                            public void onSuccess() {
                                DDLog.i(TAG, "bmtSaveLocation success");
                                LocationHelper.getInstance(getContext()).removeListener();
                                mLatitude = latitude;
                                mLongitude = longitude;
                                resetLocation();
                            }

                            @Override
                            public void onError(int i, String s) {
                                DDLog.i(TAG, "bmtSaveLocation failed");
                                LocationHelper.getInstance(getContext()).removeListener();
                            }
                        });
            } else {
                DDLog.i(TAG, "getLocation failed");
            }
        });
    }

    private int getColor(int color) {
        return getResources().getColor(color);
    }

    /****************************************************** 数据获取处理 ********************************************************/

    private void getChargingDischargingPlansV2() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(BmtDataKey.CMD, BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2);
            mPSDevice.submit(map);
        }
    }

    private void getCustomSchedule() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(BmtDataKey.CMD, BmtCmd.GET_CUSTOM_SCHEDULEMODE);
            mPSDevice.submit(map);
        }
    }

    private AIDataManager aiDataManager;
    private String unitPrice;
    private String timeZone;
    private long gmTime;
    private List<Integer> conditions;

    @Safer
    private void setViewData() {
        if (mResultCount.get() < 0) {
            DDLog.i(TAG, "已处理过数据....");
            return;
        }
        if (0 < mResultCount.get()) {
            DDLog.i(TAG, "还需要等待另一个接口结果返回");
            DDLog.i(TAG, "mResultCount===" + mResultCount.get());
            return;
        }
        if (mHTTPDataMap.size() == 0 || mIotScheduleDataMap.size() == 0
                || DeviceHelper.getList(mHTTPDataMap, BmtDataKey.CONDITIONS).size() == 0
                || DeviceHelper.getList(mHTTPDataMap, BmtDataKey.HOPE_CHARGE_DISCHARGES).size() == 0
                || DeviceHelper.getList(mHTTPDataMap, BmtDataKey.MARKET_PRICES).size() == 0
                || DeviceHelper.getList(mHTTPDataMap, BmtDataKey.PLANS).size() == 0
                || DeviceHelper.getList(mHTTPDataMap, BmtDataKey.USER_PRICES).size() == 0
                || DeviceHelper.getList(mHTTPDataMap, BmtDataKey.FORECAST_SOLARS).size() == 0
                || DeviceHelper.getList(mHTTPDataMap, BmtDataKey.RELATIVE_PRICE_NORMS).size() == 0
                || ((Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, new Integer[]{})).length == 0) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (!DeviceHelper.getString(mHTTPDataMap, BmtDataKey.TIMEZONE, "").equals("")) {
                mBinding.iconAiModeSupportHelp.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PSReportFragment.newInstance(mDeviceId, subcategory, DeviceHelper.getString(mHTTPDataMap, BmtDataKey.TIMEZONE, ""))));
            }
            mBinding.llEditAiTitle.setVisibility(View.VISIBLE);
            mBinding.aiModeChartView.setVisibility(View.GONE);
            mBinding.iconAiModeSupportHelp.setVisibility(View.VISIBLE);
            mBinding.lavPvStatus.setVisibility(View.VISIBLE);
            DDLog.i(TAG, "HTTP获取数据集： mHTTPDataMap===" + mHTTPDataMap);
            DDLog.i(TAG, "IOT获取数据集： mIotScheduleDataMap===" + mIotScheduleDataMap);
        }else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            mBinding.llEditAiTitle.setVisibility(View.VISIBLE);
            mBinding.aiModeChartView.setVisibility(View.VISIBLE);
            mBinding.aiModeChartViewBg.setVisibility(View.GONE);
            mBinding.iconAiModeSupportHelp.setVisibility(View.VISIBLE);
            mBinding.lavPvStatus.setVisibility(View.VISIBLE);
            mBinding.switchToLandscapeMode.setVisibility(View.VISIBLE);
            isManualOverrideEnabled = true;
            DDLog.i(TAG, "HTTP获取数据集： mHTTPDataMap===" + mHTTPDataMap);
            DDLog.i(TAG, "IOT获取数据集： mIotScheduleDataMap===" + mIotScheduleDataMap);
            tempEmergency = DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1);
            tempSmart = DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1);
            Integer[] weekdays = (Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null);
            if (checkIsInitialized(weekdays)) {
                isInitialized = false;
                mBinding.llInitialize.setVisibility(View.VISIBLE);
                mBinding.llInitialize.setClickable(true);
                showDataInitView();
            }
            DDLog.i(TAG, "weekdays===" + Arrays.toString(weekdays));
            // 初始化数据
            unitPrice = DeviceHelper.getString(mHTTPDataMap, BmtDataKey.UNIT_PRICE, "");
            conditions = DeviceHelper.getList(mHTTPDataMap, BmtDataKey.CONDITIONS);
            timeZone = DeviceHelper.getString(mHTTPDataMap, BmtDataKey.TIMEZONE, "");
            // 截取时间得到 StartIndex
            long startTimeNow = DeviceHelper.getLong(mHTTPDataMap, BmtDataKey.START_TIME, 0);
            gmTime = DeviceHelper.getLong(mHTTPDataMap, BmtDataKey.GMTTIME, 0) / 1000000000;
            startTime = DDDateUtil.getHourByTimestamps(gmTime * 1000, timeZone);
            endTime = DDDateUtil.getHourByTimestamps(gmTime * 1000 + 3600000L, timeZone);
            currentIotChargeDischarges = weekdays[startTime];
            tempIotWeekdays = new Integer[weekdays.length];
            System.arraycopy(weekdays, 0, tempIotWeekdays, 0, weekdays.length);
            // 计算 ForecastSolars 总和 (根据 relative_price_norms 剔除)
            List<Float> relativePriceNorms = getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.RELATIVE_PRICE_NORMS));
            List<Integer> forecastSolars = getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.FORECAST_SOLARS));
            aiDataManager = new AIDataManager.Builder(mBinding.aiModeChartView)
                    .setHighlightLineOver(true)
                    .setC1(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.C_1, 0))
                    .setC2(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.C_2, 0))
                    .setC3(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.C_3, 0))
                    .setS1(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.S_1, 0))
                    .setS2(DeviceHelper.getInt(mHTTPDataMap, BmtDataKey.S_2, 0))
                    .setStartTime(gmTime)
                    .setTimezone(DeviceHelper.getString(mHTTPDataMap, BmtDataKey.TIMEZONE, ""))
                    // 绝对零电价绝对零电价(归一化相对电价)
                    .setAbsZeroEcPrices(DeviceHelper.getFloat(mHTTPDataMap, BmtDataKey.ABS_ZERO_EC_PRICES, 0))
                    // 每小时的天气编码。0：部分多云，1：晴天，2：阴天，3：雾，4：下雨，5：下雪，6：雨夹雪
                    .setConditions(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.CONDITIONS)))
                    // 每小时的预测PV，单位：wh
                    .setForecastSolars(forecastSolars.subList(0, relativePriceNorms.size()))
                    // 每小时的期望充放电量，单位：百分比
                    .setHopeChargeDischarges(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.HOPE_CHARGE_DISCHARGES)))
                    // 每小时的分时批发价
                    .setMarketPrices(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.MARKET_PRICES)))
                    // 每小时的充放电量计划。0：A计划，1：B计划，2：C计划
                    .setPlans(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.PLANS)))
                    // 归一化后每小时的相对电价
                    .setRelativePriceNorms(relativePriceNorms)
                    // 每小时的用户侧分时价格
                    .setUserPrices(getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.USER_PRICES)))
                    // 每天的日出日落时间戳
                    .setSunrise(DeviceHelper.getList(mHTTPDataMap, BmtDataKey.SUNRISES))
                    .setSunset(DeviceHelper.getList(mHTTPDataMap, BmtDataKey.SUNSETS))
                    // IOT手动充放电数据
                    .setWeekdays(tempIotWeekdays)
                    .setHighlightListener(index -> {
                        // 这里只给索引, 要显示出来的数据根据索引从对应列表获取数据
                        DDLog.i(TAG, "图表回调： index" + index + "   chartIndex: " + chartIndex + "     isEdit: " + isEdit);
                        if (index == -1 && !isEdit) {
                            exitDetailsAnimation();
                            setMainPageWeatherBg(aiDataManager.getConditions().get(0));
                        };
                        if (index == chartIndex) return;
                        chartIndex = index;
                        if (index == -1) return;
                        initDetails(index);
                    })
                    .setRetrievingViewListener((width, height, retrievingSize) -> dealRetrievingView((int) width, (int) Math.ceil(height), retrievingSize)).build();
            setMainPageWeatherBg(aiDataManager.getConditions().get(0));
            mBinding.llTopChartLayout.setOnClickListener(v -> aiDataManager.clearHighlight());
            mBinding.hideChartPoint.setOnClickListener(v -> aiDataManager.clearHighlight());
            // 用户帮助界面
            mBinding.iconAiModeSupportHelp.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PSReportFragment.newInstance(mDeviceId, subcategory, aiDataManager.getTimezone())));
            // 计算 ForecastSolars 总和 (根据 relative_price_norms 剔除)
            totalForecastSolar(aiDataManager.getForecastSolars(),0,true);
            initControl();
            DDLog.i(TAG, "aiDataManager.getRelativePriceNorms().size(): " + (aiDataManager.getRelativePriceNorms().size()) + "  ForecastSolars: " + aiDataManager.getForecastSolars());
            DDLog.d(TAG, "截断数据： conditions: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.CONDITIONS)));
            DDLog.d(TAG, "截断数据： forecast_solars: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.FORECAST_SOLARS)));
            DDLog.d(TAG, "截断数据： hope_charge_discharges: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.HOPE_CHARGE_DISCHARGES)));
            DDLog.d(TAG, "截断数据： market_prices: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.MARKET_PRICES)));
            DDLog.d(TAG, "截断数据： plans: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.PLANS)));
            DDLog.d(TAG, "截断数据： relative_price_norms: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.RELATIVE_PRICE_NORMS)));
            DDLog.d(TAG, "截断数据： user_prices: " + getDataTruncation(startTimeNow, gmTime, DeviceHelper.getList(mHTTPDataMap, BmtDataKey.USER_PRICES)));
        }
    }

    private void dealRetrievingView(int width, int height, int retrievingSize) {
        if (mRetrievingBinding == null) {
            ViewStub viewStub = mBinding.vsRetrieving.getViewStub();
            if (viewStub != null) {
                View view = viewStub.inflate();
                mRetrievingBinding = DataBindingUtil.bind(view);
                if (mRetrievingBinding != null) {
                    mRetrievingBinding.llRetrieving.setOnClickListener(v -> {
                        if (aiDataManager != null) {
                            if (isEdit) {
                                if (chartIndex != aiDataManager.getRelativePriceNorms().size() - 1) {
                                    aiDataManager.setLastHighlightByIndex();
                                }
                            } else {
                                if (chartIndex == aiDataManager.getRelativePriceNorms().size() - 1) {
                                    aiDataManager.clearHighlight();
                                } else {
                                    aiDataManager.setLastHighlightByIndex();
                                }
                            }
                        }
                    });
                }

            }
        }
        if (mRetrievingBinding != null) {
            ConstraintLayout.LayoutParams clParams = (ConstraintLayout.LayoutParams) mRetrievingBinding.llRetrieving.getLayoutParams();
            if (width != clParams.width || height != clParams.height) {
                clParams.width = width;
                clParams.height = height;
                mRetrievingBinding.llRetrieving.setLayoutParams(clParams);
                ViewGroup.LayoutParams logoLLParams = mRetrievingBinding.ivLogo.getLayoutParams();
                if (retrievingSize >= 12) {
                    logoLLParams.width = DensityUtil.dp2px(getContext(), 36);
                    logoLLParams.height = DensityUtil.dp2px(getContext(), 36);
                    mRetrievingBinding.ivLogo.setLayoutParams(logoLLParams);
                    mRetrievingBinding.ivLogo.setVisibility(View.VISIBLE);
                    mRetrievingBinding.tvNote.setVisibility(View.VISIBLE);
                } else if (retrievingSize >= 6) {
                    logoLLParams.width = DensityUtil.dp2px(getContext(), 24);
                    logoLLParams.height = DensityUtil.dp2px(getContext(), 24);
                    mRetrievingBinding.ivLogo.setLayoutParams(logoLLParams);
                    mRetrievingBinding.ivLogo.setVisibility(View.VISIBLE);
                    mRetrievingBinding.tvNote.setVisibility(View.GONE);
                } else {
                    mRetrievingBinding.ivLogo.setVisibility(View.GONE);
                    mRetrievingBinding.tvNote.setVisibility(View.GONE);
                }
            }
        }
    }

    private void totalForecastSolar(List<Integer> forecastSolar, int forecastSolarValue, boolean isList) {
        if (isList) {
            if (forecastSolar == null || forecastSolar.isEmpty()) return;
            long sum = 0;
            double converted;
            for (Integer num : forecastSolar) {
                if (num != null) sum += num;
            }
            if (sum < 1000) {
                converted = sum;
                mBinding.forecastSolarTotalW.setLocalText("Wh");
            } else if (sum < 1_000_000) {
                converted = sum / 1000.0;
                mBinding.forecastSolarTotalW.setLocalText("kWh");
            } else {
                converted = sum / 1_000_000.0;
                mBinding.forecastSolarTotalW.setLocalText("MWh");
            }
            DecimalFormat df = new DecimalFormat("#.#");
            String formattedNumber = df.format(converted);
            mBinding.forecastSolarTotalValue.setLocalText(formattedNumber);
        } else {
            double converted;
            if (forecastSolarValue < 1000) {
                converted = forecastSolarValue;
                detailsBinding.forecastSolarsValueW.setLocalText("Wh");
            } else if (forecastSolarValue < 1_000_000) {
                converted = forecastSolarValue / 1000.0;
                detailsBinding.forecastSolarsValueW.setLocalText("kWh");
            } else {
                converted = forecastSolarValue / 1_000_000.0;
                detailsBinding.forecastSolarsValueW.setLocalText("MWh");
            }
            DecimalFormat df = new DecimalFormat("#.#");
            String formattedNumber = df.format(converted);
            detailsBinding.forecastSolarsValue.setLocalText(formattedNumber);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (!isAdded()) return;
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(() -> {
                if (status == StatusConstant.STATUS_SUCCESS) {
                    switch (cmd) {
                        case BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2:
                            mHTTPDataMap.putAll(result);
                            mResultCount.decrementAndGet();
                            setViewData();
                            break;

                        case BmtCmd.GET_CUSTOM_SCHEDULEMODE:
                            if (!isDealt) {
                                mIotScheduleDataMap.putAll(result);
                                mResultCount.decrementAndGet();
                                setViewData();
                                isDealt = true;
                            }
                            break;

                        case BmtCmd.SET_RESERVE_MODE:  // 设置模式
                            refreshData();
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            break;

                        case BmtCmd.GET_PV_DIST:
                            mPVPreference = DeviceHelper.getInt(result, BmtDataKey.AI, -1);
                            DDLog.i(TAG,"mPVPreference: " + mPVPreference);
                            if (mReserveModeFunctionPopup != null) {
                                mReserveModeFunctionPopup.setPVPreferenceVal(mPVPreference);
                            }
                            break;

                        case BmtCmd.SET_PV_DIST:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            mPVPreference = DeviceHelper.getInt(result, BmtDataKey.AI, -1);
                            if (mReserveModeFunctionPopup != null) {
                                mReserveModeFunctionPopup.setPVPreferenceVal(mPVPreference);
                            }
                            DDLog.i(TAG,"mPVPreference: " + mPVPreference);
                            if (isManualOverrideEnabled) setPvTag();
                            break;

                        case BmtCmd.GET_LOCATION:
                            mLatitude = DeviceHelper.getDouble(result, BmtDataKey.LATITUDE, 0);
                            mLongitude = DeviceHelper.getDouble(result, BmtDataKey.LONGITUDE, 0);
                            resetLocation();
                            break;
                    }
                } else {
                    switch (cmd) {
                        case BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2:
                            mResultCount.decrementAndGet();
                            setViewData();
                            break;

                        case BmtCmd.GET_CUSTOM_SCHEDULEMODE:
                            if (!isDealt) {
                                isDealt = true;
                                mResultCount.decrementAndGet();
                                setViewData();
                            }
                            break;

                        case BmtCmd.SET_RESERVE_MODE:  // 设置模式
                            isEdit = false;
                            mBinding.hideChartPoint.setClickable(!isEdit);
                            System.arraycopy((Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null), 0, tempIotWeekdays, 0, tempIotWeekdays.length);
                            tempEmergency = DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1);
                            tempSmart = DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1);
                            DDLog.i(TAG, "设置失败恢复数据：tempIotWeekdays" + Arrays.toString(tempIotWeekdays));
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showErrorToast();
                            break;
                        case BmtCmd.SET_PV_DIST:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showErrorToast();
                            break;
                    }
                }
            });
        }
    }

    private void refreshData() {
        isEdit = false;
        mBinding.hideChartPoint.setClickable(!isEdit);
        // 保存数据成功,更新 IOT map
        mIotScheduleDataMap.put(BmtDataKey.EMERGENCY,tempEmergency);
        mIotScheduleDataMap.put(BmtDataKey.SMART,tempSmart);
        mIotScheduleDataMap.put(BmtDataKey.WEEKDAYS,tempIotWeekdays);
        DDLog.i(TAG, "tempSmart: " + tempSmart + "  tempEmergency: " + tempEmergency + "    图表数据： tempIotWeekdays" + Arrays.toString(tempIotWeekdays));
        DDLog.i(TAG, "DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1): " + DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1) + "  DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1): " + DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1));
        DDLog.i(TAG, "EMERGENCY: " + DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.EMERGENCY, -1) +
                "   SMART: " + DeviceHelper.getInt(mIotScheduleDataMap, BmtDataKey.SMART, -1));
        DDLog.i(TAG, "mCustomScheduleMap数据 --> weekdays" + Arrays.toString((Integer[]) MapUtils.get(mIotScheduleDataMap, BmtDataKey.WEEKDAYS, null)));
    }

    /****************************************************** 布局初始化 ********************************************************/

    private LayoutAiModeDetailsPageBinding detailsBinding;
    private LayoutAiChartControlBinding controlBinding;
    private LayoutAiEditPageBinding editBinding;

    private void initDetails(int index) {
        ViewStub viewStub = mBinding.aiDetailsPageViewStub.getViewStub();
        if (null == detailsBinding) {
            View view = viewStub.inflate();
            detailsBinding = DataBindingUtil.bind(view);
            detailsBinding.aiDetailsLoadFirst.setVisibility(isSupportPVAndLocation ? View.VISIBLE : View.GONE);
            detailsBinding.aiDetailsTimeTip.setLocalText("(" + Local.s(getString(R.string.Edited)) + ")");
            setPvTag();
        }
        showAiModeDetails(index);
    }

    private void initEdit() {
        ViewStub viewStub = mBinding.aiEditPageViewStub.getViewStub();
        ViewStub viewStub1 = mBinding.aiDetailsPageViewStub.getViewStub();
        if (null == detailsBinding) {
            View view = viewStub1.inflate();
            detailsBinding = DataBindingUtil.bind(view);
            detailsBinding.aiDetailsLoadFirst.setVisibility(isSupportPVAndLocation ? View.VISIBLE : View.GONE);
            setPvTag();
        }
        if (null == editBinding) {
            View view = viewStub.inflate();
            editBinding = DataBindingUtil.bind(view);
        }
        enterEditMode();
    }

    private void initControl() {
        ViewStub viewStub = mBinding.aiChartControlViewStub.getViewStub();
        if (null == controlBinding) {
            View view = viewStub.inflate();
            controlBinding = DataBindingUtil.bind(view);
            controlBinding.chartControlChargeText.setLocalText(Local.s(getString(R.string.Charge)) + " / " + Local.s(getString(R.string.Discharge)));
            // 图表控制
            controlBinding.chartControlSolar.setOnClickListener(v -> {
                if (!isChartControlPrice && !isChartControlPriceLevel && !isChartControlCharge) return;
                isChartControlSolar = !isChartControlSolar;
                controlBinding.chartControlSolar.setAlpha(isChartControlSolar ? 1f : 0.5f);
                aiDataManager.setDrawSolar(isChartControlSolar);
            });
            controlBinding.chartControlPrice.setOnClickListener(v -> {
                if (!isChartControlSolar && !isChartControlPriceLevel && !isChartControlCharge) return;
                isChartControlPrice = !isChartControlPrice;
                controlBinding.chartControlPrice.setAlpha(isChartControlPrice ? 1f : 0.5f);
                aiDataManager.setDrawPrice(isChartControlPrice);
            });
            controlBinding.chartControlPriceLevel.setOnClickListener(v -> {
                if (!isChartControlSolar && !isChartControlPrice && !isChartControlCharge) return;
                isChartControlPriceLevel = !isChartControlPriceLevel;
                controlBinding.chartControlPriceLevel.setAlpha(isChartControlPriceLevel ? 1f : 0.5f);
                aiDataManager.setDrawPriceLevel(isChartControlPriceLevel);
            });
            controlBinding.chartControlCharge.setOnClickListener(v -> {
                if (!isChartControlSolar && !isChartControlPrice && !isChartControlPriceLevel) return;
                isChartControlCharge = !isChartControlCharge;
                controlBinding.chartControlCharge.setAlpha(isChartControlCharge ? 1f : 0.5f);
                aiDataManager.setDrawChargeStatus(isChartControlCharge);
            });
        }
    }

    private void setPvTag(){
        if (mPVPreference == Mcu.PVPreference.PVPreferenceType.followEmaldoAI.getPreference()) {
            detailsBinding.aiDetailsLoadFirst.setLocalText(getString(R.string.Follow_Emaldo_AI));
        } else if (mPVPreference == Mcu.PVPreference.PVPreferenceType.load.getPreference()) {
            detailsBinding.aiDetailsLoadFirst.setLocalText(getString(R.string.Load_First));
        } else if (mPVPreference == Mcu.PVPreference.PVPreferenceType.batteryCharge.getPreference()) {
            detailsBinding.aiDetailsLoadFirst.setLocalText(getString(R.string.Battery_Charge_First));
        }
    }

    /****************************************************** 数据截取 ********************************************************/

    public <T> List<T> getDataTruncation(long startTime, long gmTime, List<T> hourlyData) {
        DDLog.i(TAG, "startTime===" + startTime + "   gmTime===" + gmTime + "    " + hourlyData.size());
        List<T> result = new ArrayList<>();
        if (hourlyData == null) return result;
        // 计算相差小时数（整小时）
        long diffSeconds = gmTime - startTime;
        if (diffSeconds < 0) return result;
        int hourDiff = (int) (diffSeconds / 3600);
        int startIndex = hourDiff;
        int endIndex;
        if (hourlyData.size() < 36) {
            endIndex = hourlyData.size() ;
        } else {
            endIndex = Math.min(startIndex + 36, hourlyData.size());
        }
        DDLog.i(TAG, "分割操作： hourDiff===" + hourDiff + "  startIndex===" + startIndex +
                "  endIndex===" + endIndex + "  startTime===" + startTime + "   gmTime===" + gmTime +
                "  hourlyData.size(): " + hourlyData.size() + "  result.size(): " + result.size());
        result = new ArrayList<T>(hourlyData.subList(startIndex, endIndex));
        return result;
    }

    public boolean checkIsInitialized(Integer[] array) {
        if (array == null) return false;
        for (Integer num : array) {
            if (num == null) continue;
            if (num != -128) return true;
        }
        return false;
    }

    public Integer[] refreshData(Integer[] array, int oldData, int newData) {
        if (array == null) return null;
        for (int i = 0; i < array.length; i++) {
            if (array[i] == oldData) array[i] = newData;
            if (array[i] == -oldData) array[i] = -newData;
        }
        return array;
    }

    public int[] integerToInt(Integer[] array) {
        if (array == null) return null;
        int[] arrays = new int[array.length];
        for (int i = 0; i < array.length; i++) {
            arrays[i] = array[i];
        }
        return arrays;
    }

}
