package com.dinsafer.module.powerstation.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/25 13:32
 * @description :
 */
public class ReserveModeBean implements MultiItemEntity, Parcelable {

    public static final int ITEM_MODE = 1;
    public static final int ITEM_TIP = 2;

    private int logo;
    private String name;
    private String desc;
    private int strategyType;   // int - 策略类型 StrategyType
    private int smartReserve;    // int - 电量百分比 [0 - 100]
    private int goodPricePercentage;  // int - 低价比例，被乘数暂定每日电价平均值。-1 为 ignore
    private int emergencyReserve;   // int - 电量百分比 [0 - 100]
    private int acceptablePricePercentage;  // int - 能容忍的价格比例，被乘数暂定每日电价平均值。-1 为 ignore
    private boolean selected;
    private boolean enabled;
    private List<PriceBean> prices;
    private int itemType;

    public ReserveModeBean(int itemType) {
        this.itemType = itemType;
    }

    public ReserveModeBean(int logo, String name, String desc) {
        this.logo = logo;
        this.name = name;
        this.desc = desc;
    }

    public ReserveModeBean(int logo, String name, String desc, boolean selected, boolean enabled) {
        this.logo = logo;
        this.name = name;
        this.desc = desc;
        this.selected = selected;
        this.enabled = enabled;
    }

    public ReserveModeBean(int logo, String name, String desc, boolean selected, boolean enabled, int itemType) {
        this.logo = logo;
        this.name = name;
        this.desc = desc;
        this.selected = selected;
        this.enabled = enabled;
        this.itemType = itemType;
    }

    public ReserveModeBean(int logo, String name, String desc, int strategyType, boolean selected, boolean enabled, int itemType) {
        this.logo = logo;
        this.name = name;
        this.desc = desc;
        this.strategyType = strategyType;
        this.selected = selected;
        this.enabled = enabled;
        this.itemType = itemType;
    }

    public int getLogo() {
        return logo;
    }

    public void setLogo(int logo) {
        this.logo = logo;
    }

    public String getName() {
        return name == null ? "" : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc == null ? "" : desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getStrategyType() {
        return strategyType;
    }

    public void setStrategyType(int strategyType) {
        this.strategyType = strategyType;
    }

    public int getSmartReserve() {
        return smartReserve;
    }

    public void setSmartReserve(int smartReserve) {
        this.smartReserve = smartReserve;
    }

    public int getGoodPricePercentage() {
        return goodPricePercentage;
    }

    public void setGoodPricePercentage(int goodPricePercentage) {
        this.goodPricePercentage = goodPricePercentage;
    }

    public int getEmergencyReserve() {
        return emergencyReserve;
    }

    public void setEmergencyReserve(int emergencyReserve) {
        this.emergencyReserve = emergencyReserve;
    }

    public int getAcceptablePricePercentage() {
        return acceptablePricePercentage;
    }

    public void setAcceptablePricePercentage(int acceptablePricePercentage) {
        this.acceptablePricePercentage = acceptablePricePercentage;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public List<PriceBean> getPrices() {
        if (prices == null) {
            return new ArrayList<>();
        }
        return prices;
    }

    public void setPrices(List<PriceBean> prices) {
        this.prices = prices;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    @Override
    public int getItemType() {
        return itemType;
    }



    public static class PriceBean implements Parcelable{
        private String content;
        private boolean showEdit;

        public PriceBean(String content) {
            this.content = content;
        }

        public PriceBean(String content, boolean showEdit) {
            this.content = content;
            this.showEdit = showEdit;
        }

        public String getContent() {
            return content == null ? "" : content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public boolean isShowEdit() {
            return showEdit;
        }

        public void setShowEdit(boolean showEdit) {
            this.showEdit = showEdit;
        }


        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.content);
            dest.writeByte(this.showEdit ? (byte) 1 : (byte) 0);
        }

        public void readFromParcel(Parcel source) {
            this.content = source.readString();
            this.showEdit = source.readByte() != 0;
        }

        protected PriceBean(Parcel in) {
            this.content = in.readString();
            this.showEdit = in.readByte() != 0;
        }

        public static final Creator<PriceBean> CREATOR = new Creator<PriceBean>() {
            @Override
            public PriceBean createFromParcel(Parcel source) {
                return new PriceBean(source);
            }

            @Override
            public PriceBean[] newArray(int size) {
                return new PriceBean[size];
            }
        };
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.logo);
        dest.writeString(this.name);
        dest.writeString(this.desc);
        dest.writeInt(this.strategyType);
        dest.writeInt(this.smartReserve);
        dest.writeInt(this.goodPricePercentage);
        dest.writeInt(this.emergencyReserve);
        dest.writeInt(this.acceptablePricePercentage);
        dest.writeByte(this.selected ? (byte) 1 : (byte) 0);
        dest.writeByte(this.enabled ? (byte) 1 : (byte) 0);
        dest.writeTypedList(this.prices);
        dest.writeInt(this.itemType);
    }

    public void readFromParcel(Parcel source) {
        this.logo = source.readInt();
        this.name = source.readString();
        this.desc = source.readString();
        this.strategyType = source.readInt();
        this.smartReserve = source.readInt();
        this.goodPricePercentage = source.readInt();
        this.emergencyReserve = source.readInt();
        this.acceptablePricePercentage = source.readInt();
        this.selected = source.readByte() != 0;
        this.enabled = source.readByte() != 0;
        this.prices = source.createTypedArrayList(PriceBean.CREATOR);
        this.itemType = source.readInt();
    }

    protected ReserveModeBean(Parcel in) {
        this.logo = in.readInt();
        this.name = in.readString();
        this.desc = in.readString();
        this.strategyType = in.readInt();
        this.smartReserve = in.readInt();
        this.goodPricePercentage = in.readInt();
        this.emergencyReserve = in.readInt();
        this.acceptablePricePercentage = in.readInt();
        this.selected = in.readByte() != 0;
        this.enabled = in.readByte() != 0;
        this.prices = in.createTypedArrayList(PriceBean.CREATOR);
        this.itemType = in.readInt();
    }

    public static final Creator<ReserveModeBean> CREATOR = new Creator<ReserveModeBean>() {
        @Override
        public ReserveModeBean createFromParcel(Parcel source) {
            return new ReserveModeBean(source);
        }

        @Override
        public ReserveModeBean[] newArray(int size) {
            return new ReserveModeBean[size];
        }
    };
}
