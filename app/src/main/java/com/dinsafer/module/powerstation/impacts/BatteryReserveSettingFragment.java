package com.dinsafer.module.powerstation.impacts;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBatteryReserveSettingBinding;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PSReserveModeConstant;
import com.dinsafer.module.powerstation.bean.ReserveModeBean;
import com.dinsafer.module.powerstation.dialog.ReserveModePriceHelpDialog;
import com.dinsafer.module.powerstation.dialog.ReserveModePriceTypeDialog;
import com.dinsafer.module.powerstation.event.NeedRefreshEvent;
import com.dinsafer.module.powerstation.event.PSReserveRefreshEvent;
import com.dinsafer.module.powerstation.widget.BRSSRBMarkView;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.module.powerstation.widget.segmentbar.SegmentRangeBar;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> WZH
 * @date : 2022/11/26 16:38
 * @description :
 */
public class BatteryReserveSettingFragment extends MyBaseFragment<FragmentBatteryReserveSettingBinding> implements IDeviceCallBack {

    private BRSSRBMarkView mMinBRSSRBMarkView;
    private BRSSRBMarkView mMaxBRSSRBMarkView;
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private ReserveModeBean mReserveModeBean;
    private int mPosition;
    private int mSmartReserve;
    private int mGoodPricePercentage;
    private int mEmergencyReserve;
    private int mAcceptablePricePercentage;

    private int mOriginalSmartReserve;
    private int mOriginalGoodPricePercentage;
    private int mOriginalEmergencyReserve;
    private int mOriginalAcceptablePricePercentage;

    private boolean isDefault;
    private Map<String, Object> params = new HashMap<>();

    private Vibrator vibrator;

    public static BatteryReserveSettingFragment newInstance(int position, String deviceId, String subcategory, ReserveModeBean reserveModeBean) {
        BatteryReserveSettingFragment fragment = new BatteryReserveSettingFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_POSITION, position);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        bundle.putParcelable(PSKeyConstant.KEY_BEAN, reserveModeBean);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_battery_reserve_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.impact_strategies_battery_reserve_settings));
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_reserver_mode_price_help);
        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> {
            if (isChanged()) {
                showNotSaveTip();
            } else {
                EventBus.getDefault().post(new NeedRefreshEvent(true));
                removeSelf();
            }
        });
        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> {
            ReserveModePriceHelpDialog.newInstance().show(getFragmentManager(), ReserveModePriceHelpDialog.TAG);
        });

        createMarkView();
        setSegment();
        vibrator = (Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
        mBinding.tvConfirm.setOnClickListener(v -> {
            if (mGoodPricePercentage > -1 && mAcceptablePricePercentage > -1) {
                if (mAcceptablePricePercentage < mGoodPricePercentage) {
                    showErrorToast(getString(R.string.got_it),
                            getString(R.string.impact_strategies_acceptable_higher_than_good));
                } else {
                    isDefault = false;
                    submitCmd();
                }
            } else {
                isDefault = false;
                submitCmd();
            }
        });
        mBinding.tvResume.setOnClickListener(v -> {
            isDefault = true;
            submitCmd();
        });
    }

    private boolean isChanged() {
        boolean isChanged = mSmartReserve != mOriginalSmartReserve
                || mGoodPricePercentage != mOriginalGoodPricePercentage
                || mEmergencyReserve != mOriginalEmergencyReserve
                || mAcceptablePricePercentage != mOriginalAcceptablePricePercentage;
        return isChanged;
    }

    private void showNotSaveTip() {
        AlertDialog dialog = null;
        dialog = AlertDialog.createBuilder(getContext())
                .setContent(Local.s(getString(R.string.ipc_setting_not_save_tip)))
                .setOk(getString(R.string.ok))
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    submitCmd();
                })
                .setCancelListener(() -> {
                    EventBus.getDefault().post(new NeedRefreshEvent(true));
                    removeSelf();
                }).preBuilder();
        dialog.show();
    }

    @Override
    public boolean onBackPressed() {
        if (isChanged()) {
            showNotSaveTip();
            return true;
        }
        EventBus.getDefault().post(new NeedRefreshEvent(true));
        return super.onBackPressed();
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }


    private void initParams() {
        Bundle bundle = getArguments();
        mPosition = bundle.getInt(PSKeyConstant.KEY_POSITION);
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        mReserveModeBean = bundle.getParcelable(PSKeyConstant.KEY_BEAN);
        mSmartReserve = mReserveModeBean.getSmartReserve();
        mGoodPricePercentage = mReserveModeBean.getGoodPricePercentage();
        mEmergencyReserve = mReserveModeBean.getEmergencyReserve();
        mAcceptablePricePercentage = mReserveModeBean.getAcceptablePricePercentage();

        mOriginalSmartReserve = mReserveModeBean.getSmartReserve();
        mOriginalGoodPricePercentage = mReserveModeBean.getGoodPricePercentage();
        mOriginalEmergencyReserve = mReserveModeBean.getEmergencyReserve();
        mOriginalAcceptablePricePercentage = mReserveModeBean.getAcceptablePricePercentage();

        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    private void createMarkView() {
        mMinBRSSRBMarkView = new BRSSRBMarkView(getContext());
        mMaxBRSSRBMarkView = new BRSSRBMarkView(getContext());
        mMinBRSSRBMarkView.setWeakBar(mBinding.segmentRangeBar);
        mMaxBRSSRBMarkView.setWeakBar(mBinding.segmentRangeBar);
        mMinBRSSRBMarkView.setKeyText(Local.s(StringUtil.getStringFromRes(getContext(), R.string.impact_strategies_batter_reserve_setting_acceptable_price)));
        String acceptableContent = getString(R.string.impact_strategies_batter_reserve_setting_ignore);
        int acceptablePercent = mReserveModeBean.getAcceptablePricePercentage();
        if (acceptablePercent >= 0 && acceptablePercent <= 1000) {
            acceptableContent = acceptablePercent == 100 ? getString(R.string.impact_strategies_batter_reserve_setting_lt_today_average) : getString(R.string.impact_strategies_batter_reserve_setting_lt_today_average) + "*" + acceptablePercent + "%%";
        }
        mMinBRSSRBMarkView.setValueText(acceptableContent);
        mMinBRSSRBMarkView.setCallback(new BRSSRBMarkView.Callback() {
            @Override
            public void onEdit() {
                Log.i("XX", "MinMarkView");
                showTypeDialog(1, mAcceptablePricePercentage < 0 || mAcceptablePricePercentage > 1000 ? 1 : 2, mAcceptablePricePercentage);
            }
        });

        mMaxBRSSRBMarkView.setKeyText(Local.s(StringUtil.getStringFromRes(getContext(), R.string.impact_strategies_batter_reserve_setting_good_price)));
        String goodContent = getString(R.string.impact_strategies_batter_reserve_setting_ignore);
        int goodPercent = mReserveModeBean.getGoodPricePercentage();
        if (goodPercent >= 0 && goodPercent <= 1000) {
            goodContent = goodPercent == 100 ? getString(R.string.impact_strategies_batter_reserve_setting_lt_today_average) : getString(R.string.impact_strategies_batter_reserve_setting_lt_today_average) + "*" + goodPercent + "%%";
        }
        mMaxBRSSRBMarkView.setValueText(goodContent);
        mMaxBRSSRBMarkView.setCallback(new BRSSRBMarkView.Callback() {
            @Override
            public void onEdit() {
                Log.i("XX", "MaxMarkView");
                showTypeDialog(2, mGoodPricePercentage < 0 || mGoodPricePercentage > 1000 ? 1 : 2, mGoodPricePercentage);
            }
        });

        mBinding.segmentRangeBar.setMinMarkView(mMinBRSSRBMarkView);
        mBinding.segmentRangeBar.setMaxMarkView(mMaxBRSSRBMarkView);
    }

    private void showTypeDialog(int markViewType, int priceType, int percent) {
        new ReserveModePriceTypeDialog.Builder()
                .setType(priceType)
                .setPercent(percent)
                .setPriceTypeListener(new ReserveModePriceTypeDialog.OnPriceTypeListener() {
                    @Override
                    public void onPriceType(int type, int percent) {
                        String content = getString(R.string.impact_strategies_batter_reserve_setting_lt_today_average);
                        if (percent != 100) {
                            content = getString(R.string.impact_strategies_batter_reserve_setting_lt_today_average) + "*" + percent + "%%";
                        }
                        if (markViewType == 1) {
                            mAcceptablePricePercentage = percent;
                            mMinBRSSRBMarkView.setValueText(type == 1 ? getString(R.string.impact_strategies_batter_reserve_setting_ignore)
                                    : content);
                        } else {
                            mGoodPricePercentage = percent;
                            mMaxBRSSRBMarkView.setValueText(type == 1 ? getString(R.string.impact_strategies_batter_reserve_setting_ignore)
                                    : content);
                        }
                    }
                }).build().show(getFragmentManager(), ReserveModePriceTypeDialog.TAG);
    }

    private void setSegment() {
        List<Segment> segments = new ArrayList<>();
        segments.add(new Segment(mReserveModeBean.getSmartReserve() / 100f, 1.0f, getString(R.string.power_battery_bar_status_text_1), getColor(R.color.power_station_battery_color_1)));
        segments.add(new Segment(mReserveModeBean.getEmergencyReserve() / 100f, mReserveModeBean.getSmartReserve() / 100f, getString(R.string.power_battery_bar_status_text_2), getColor(R.color.power_station_battery_color_2)));
        segments.add(new Segment(0.12f, mReserveModeBean.getEmergencyReserve() / 100f, getString(R.string.power_battery_bar_status_text_3), getColor(R.color.power_station_battery_color_3)));
        segments.add(new Segment(0.02f, 0.12f, getString(R.string.power_battery_bar_status_text_4), getColor(R.color.power_station_battery_color_4)));
        segments.add(new Segment(0.0f, 0.02f, getString(R.string.power_battery_bar_status_text_5), getColor(R.color.power_station_battery_color_5)));
        mBinding.segmentRangeBar.setMinProgress(mReserveModeBean.getEmergencyReserve());
        mBinding.segmentRangeBar.setMaxProgress(mReserveModeBean.getSmartReserve());
        mBinding.segmentRangeBar.setSegmentRanges(segments);
        mBinding.segmentRangeBar.setProgressListener(new SegmentRangeBar.OnProgressChangedListener() {
            @Override
            public void onProgress1Changed(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {
                mEmergencyReserve = progress;
            }

            @Override
            public void getProgress1OnActionUp(SegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress1OnFinally(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onProgress2Changed(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {
                mSmartReserve = progress;
            }

            @Override
            public void getProgress2OnActionUp(SegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress2OnFinally(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onAlert() {
                /*
                 * 震动的方式
                 */
                vibrator.vibrate(200);//振动两毫秒
                // 下边是可以使震动有规律的震动 -1：表示不重复 0：循环的震动
//                long[] pattern = {200, 2000, 2000, 200, 200, 200};
//                vibrator.vibrate(pattern, -1);
            }
        });
    }

    /**
     * 充电策略设置(0xa003)
     */
    private void submitCmd() {
        if (mPSDevice != null) {
            int smartReserve = 0;
            int goodPricePercentage = 0;
            int emergencyReserve = 0;
            int acceptablePricePercentage = 0;
            switch (mPosition + 1) {
                case PSReserveModeConstant.PRICE_INSENSITIVE:
                    smartReserve = PSReserveModeConstant.PRICE_INSENSITIVE_SMART;
                    goodPricePercentage = PSReserveModeConstant.PRICE_INSENSITIVE_GOOD_PRICE;
                    emergencyReserve = PSReserveModeConstant.PRICE_INSENSITIVE_EMERGENCY;
                    acceptablePricePercentage = PSReserveModeConstant.PRICE_INSENSITIVE_ACCEPTABLE_PRICE;
                    break;

                case PSReserveModeConstant.SMART_CHARGE:
                    smartReserve = PSReserveModeConstant.SMART_CHARGE_SMART;
                    goodPricePercentage = PSReserveModeConstant.SMART_CHARGE_GOOD_PRICE;
                    emergencyReserve = PSReserveModeConstant.SMART_CHARGE_EMERGENCY;
                    acceptablePricePercentage = PSReserveModeConstant.SMART_CHARGE_ACCEPTABLE_PRICE;
                    break;

                case PSReserveModeConstant.EXTREME_SAVING:
                    smartReserve = PSReserveModeConstant.EXTREME_SAVING_SMART;
                    goodPricePercentage = PSReserveModeConstant.EXTREME_SAVING_GOOD_PRICE;
                    emergencyReserve = PSReserveModeConstant.EXTREME_SAVING_EMERGENCY;
                    acceptablePricePercentage = PSReserveModeConstant.EXTREME_SAVING_ACCEPTABLE_PRICE;
                    break;
            }
            params.clear();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_CHARGE_STRATEGIES);
            params.put(PSKeyConstant.STRATEGY_TYPE, mPosition + 1);
            params.put(PSKeyConstant.SMART_RESERVE, isDefault ? smartReserve : mSmartReserve);
            params.put(PSKeyConstant.GOOD_PRICE_PERCENTAGE, isDefault ? goodPricePercentage : mGoodPricePercentage);
            params.put(PSKeyConstant.EMERGENCY_RESERVE, isDefault ? emergencyReserve : mEmergencyReserve);
            params.put(PSKeyConstant.ACCEPTABLE_PRICE_PERCENTAGE, isDefault ? acceptablePricePercentage : mAcceptablePricePercentage);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    private int getColor(int color) {
        return getResources().getColor(color);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (cmd != null && cmd.equals(DsCamCmd.SET_CHARGE_STRATEGIES)) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (status == 1) {
                    int smartReserve = 0;
                    int goodPricePercentage = 0;
                    int emergencyReserve = 0;
                    int acceptablePricePercentage = 0;
                    switch (mPosition + 1) {
                        case PSReserveModeConstant.PRICE_INSENSITIVE:
                            smartReserve = PSReserveModeConstant.PRICE_INSENSITIVE_SMART;
                            goodPricePercentage = PSReserveModeConstant.PRICE_INSENSITIVE_GOOD_PRICE;
                            emergencyReserve = PSReserveModeConstant.PRICE_INSENSITIVE_EMERGENCY;
                            acceptablePricePercentage = PSReserveModeConstant.PRICE_INSENSITIVE_ACCEPTABLE_PRICE;
                            break;

                        case PSReserveModeConstant.SMART_CHARGE:
                            smartReserve = PSReserveModeConstant.SMART_CHARGE_SMART;
                            goodPricePercentage = PSReserveModeConstant.SMART_CHARGE_GOOD_PRICE;
                            emergencyReserve = PSReserveModeConstant.SMART_CHARGE_EMERGENCY;
                            acceptablePricePercentage = PSReserveModeConstant.SMART_CHARGE_ACCEPTABLE_PRICE;
                            break;

                        case PSReserveModeConstant.EXTREME_SAVING:
                            smartReserve = PSReserveModeConstant.EXTREME_SAVING_SMART;
                            goodPricePercentage = PSReserveModeConstant.EXTREME_SAVING_GOOD_PRICE;
                            emergencyReserve = PSReserveModeConstant.EXTREME_SAVING_EMERGENCY;
                            acceptablePricePercentage = PSReserveModeConstant.EXTREME_SAVING_ACCEPTABLE_PRICE;
                            break;
                    }
                    mReserveModeBean.setSmartReserve(isDefault ? smartReserve : mSmartReserve);
                    mReserveModeBean.setGoodPricePercentage(isDefault ? goodPricePercentage : mGoodPricePercentage);
                    mReserveModeBean.setEmergencyReserve(isDefault ? emergencyReserve : mEmergencyReserve);
                    mReserveModeBean.setAcceptablePricePercentage(isDefault ? acceptablePricePercentage : mAcceptablePricePercentage);
                    String foldOne = getString(R.string.impact_strategies_reserve_mode_item_2_price_desc_1);
                    String foldTwo = getString(R.string.impact_strategies_reserve_mode_item_2_price_desc_2);
                    String hashtagSR = getString(R.string.is_reserve_mode_hashtag_smart_reserve);
                    String hashtagER = getString(R.string.is_reserve_mode_hashtag_emergency_reserve);
                    String priceLimit = getString(R.string.is_reserve_mode_hashtag_price_limit);
                    mReserveModeBean.getPrices().get(0).setContent(Local.s(foldOne).replace(hashtagSR, (isDefault ? smartReserve : mSmartReserve) + "%").replace(priceLimit, getGoodPriceStr(isDefault ? goodPricePercentage : mGoodPricePercentage)));
                    mReserveModeBean.getPrices().get(1).setContent(Local.s(foldTwo).replace(hashtagER, (isDefault ? emergencyReserve : mEmergencyReserve) + "%").replace(priceLimit, getAcceptablePriceStr(isDefault ? acceptablePricePercentage : mAcceptablePricePercentage)));
                    runOnMainThread(new Runnable() {
                        @Override
                        public void run() {
                            new Handler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    EventBus.getDefault().post(new PSReserveRefreshEvent(mPosition));
                                    if (isDefault) {
                                        showSuccess();
                                        setSegment();
                                    } else {
                                        removeSelf();
                                    }
                                }
                            }, 200);
                        }
                    });
                } else {
                    showErrorToast();
                }
            }
        }
    }

    private String getGoodPriceStr(int goodPricePercentage) {
        String goodPrice = Local.s(getString(R.string.impact_strategies_batter_reserve_setting_ignore));
        if (goodPricePercentage >= 0 && goodPricePercentage <= 1000) {
            goodPrice = (goodPricePercentage == 100 ? getString(R.string.impact_strategies_less_than_average_today)
                    : getString(R.string.impact_strategies_less_than_average_today) + "*" + goodPricePercentage + "%");
        }
        return goodPrice;
    }

    private String getAcceptablePriceStr(int acceptablePricePercentage) {
        String acceptablePrice = Local.s(getString(R.string.impact_strategies_batter_reserve_setting_ignore));
        if (acceptablePricePercentage >= 0 && acceptablePricePercentage <= 1000) {
            acceptablePrice = (acceptablePricePercentage == 100 ? getString(R.string.impact_strategies_less_than_average_today)
                    : getString(R.string.impact_strategies_less_than_average_today) + "*" + acceptablePricePercentage + "%");
        }
        return acceptablePrice;
    }
}
