package com.dinsafer.module.powerstation.widget;

import android.content.Context;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import android.graphics.Rect;
import android.graphics.RectF;
import android.text.method.LinkMovementMethod;
import android.text.method.MovementMethod;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutSegmentRangeBarMarkBinding;
import com.dinsafer.module.powerstation.widget.segmentbar.SegmentBarMarkView;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/26 17:29
 * @description :
 */
public class BRSSRBMarkView extends SegmentBarMarkView {

//    private LayoutSegmentRangeBarMarkBinding mBinding;
    private ConstraintLayout clParent;
    private LocalTextView tvKey;
    private LocalTextView tvValue;
    private ImageView ivEdit;
    private RectF mEditRectF;
    private Rect mEditRect;

    public BRSSRBMarkView(Context context) {
        this(context, R.layout.layout_segment_range_bar_mark);
    }

    public BRSSRBMarkView(Context context, int layoutRes) {
        super(context, layoutRes);
//        mBinding = DataBindingUtil.bind(getView());
//        mBinding.ivEdit.setOnClickListener( v -> {if(callback!=null) callback.onEdit();});
        clParent = findViewById(R.id.cl_parent);
        tvKey = findViewById(R.id.tv_key);
        tvValue = findViewById(R.id.tv_value);
        ivEdit = findViewById(R.id.iv_edit);
        mEditRectF = new RectF();
    }

    @Override
    public void refreshContent() {
        super.refreshContent();
//        mEditRectF.left = ivEdit.getLeft();
//        mEditRectF.top  = ivEdit.getTop();
//        mEditRectF.right  = ivEdit.getRight();
//        mEditRectF.bottom  = ivEdit.getBottom();
        mEditRectF.left = clParent.getLeft();
        mEditRectF.top  = clParent.getTop();
        mEditRectF.right  = clParent.getRight();
        mEditRectF.bottom  = clParent.getBottom();
    }

    @Override
    public void markViewClick() {
        super.markViewClick();
        float x = getRelativeTouchPointX();
        float y = getRelativeTouchPointY();
        if (callback!=null) {
            if (mEditRectF.contains(x, y)) {
                callback.onEdit();
            }
        }
    }

    public void setKeyText(String key) {
        tvKey.setText(key);
    }

    public void setValueText(String value) {
        tvValue.setMovementMethod(LinkMovementMethod.getInstance());
        tvValue.setText(StringUtil.getTextEndWithIcon(getContext(), Local.s(value),
                R.drawable.icon_edit_small, new StringUtil.OnTextEndWithIconListener() {
                    @Override
                    public void getRect(Rect rect) {
                        mEditRect = rect;

                    }
                }));
        getRangeBarView().invalidate();
    }

    private Callback callback;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void onEdit();
    }
}
