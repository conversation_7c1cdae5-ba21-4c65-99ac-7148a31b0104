package com.dinsafer.module.powerstation.electricity.chart.render;

import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.interfaces.dataprovider.CandleDataProvider;
import com.github.mikephil.charting.renderer.CandleStickChartRenderer;
import com.github.mikephil.charting.utils.ViewPortHandler;

/**
 *
 */
public class CustomCandleStickChartRenderer extends CandleStickChartRenderer {
    public CustomCandleStickChartRenderer(CandleDataProvider chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {
        super(chart, animator, viewPortHandler);
    }

    public CandleDataProvider getChart() {
        return mChart;
    }
}
