package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;

import com.dinsafer.util.viewanimator.SvgPathParser;

public class PowerPulseCurrentView extends BaseCurrentView {

    private final String TOPO_PATH = "M1 22C1 22.5523 1.44772 23 2 23L31.5081 23V23.0011C44.3591 23.0011 56.1431 28.04 64.6635 36.3455C68.4956 40.4407 73.9489 43 80 43C86.0511 43 91.5044 40.4407 95.3365 36.3455C103.857 28.04 115.641 23.0011 128.492 23.0011V23L145 23C145.552 23 146 22.5523 146 22V22C146 21.4477 145.552 21 145 21L128.248 21C115.433 20.9995 103.811 15.8983 95.298 7.61346C91.4679 3.54221 86.0308 1 80 1C73.9692 1 68.5321 3.54221 64.702 7.61346C56.1895 15.8983 44.5672 20.9995 31.7518 21L2 21C1.44771 21 1 21.4477 1 22V22ZM80 41C85.1041 41 89.7381 38.9874 93.1517 35.7125L93.1456 35.7066C93.3963 35.4505 93.6499 35.1972 93.9063 34.9467C97.0666 31.5536 99 27.0026 99 22C99 16.9783 97.0519 12.4117 93.8702 9.01458C93.5948 8.74607 93.3225 8.47437 93.0535 8.19953L93.0564 8.19667C89.6521 4.97543 85.0567 3 80 3C74.9433 3 70.3479 4.97543 66.9436 8.19667L66.9465 8.19953C66.6775 8.47437 66.4052 8.74607 66.1298 9.01458C62.9481 12.4117 61 16.9783 61 22C61 27.0026 62.9334 31.5536 66.0937 34.9467C66.3501 35.1972 66.6037 35.4505 66.8544 35.7066L66.8483 35.7125C70.2619 38.9874 74.8959 41 80 41ZM59 22C59 25.0061 59.6316 27.8647 60.7694 30.4503C55.1648 26.3697 48.6415 23.4532 41.5956 22.0156C48.6525 20.5836 55.1618 17.6428 60.7715 13.5449C59.6324 16.1318 59 18.992 59 22ZM101 22C101 25.0061 100.368 27.8647 99.2306 30.4503C104.835 26.3697 111.359 23.4532 118.404 22.0156C111.347 20.5836 104.838 17.6428 99.2285 13.5449C100.368 16.1318 101 18.992 101 22Z";

    public PowerPulseCurrentView() {
        mTopoPath = SvgPathParser.tryParsePath(TOPO_PATH);
        if (mTopoPath != null) {
            mTopoPath.setFillType(Path.FillType.EVEN_ODD);
        }
    }

    @Override
    public void drawTB(Canvas canvas, Paint paint) {
        canvas.drawPath(mTopoPath, paint);
    }
}
