package com.dinsafer.module.powerstation.electricity;


import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.LruCache;
import android.view.View;
import android.view.ViewStub;
import android.view.animation.Animation;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;

import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.config.DBKey;
import com.dinsafer.dialog.CommonAlertDialog;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.view.HomeRefreshHeader;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.bean.ChartNoteTextAttrBean;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.chart.CustomCombinedChart;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.IChartModelController;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.module.powerstation.electricity.factory.ChartDataFactory;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.FillFormatter;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.utils.ViewPortHandler;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public abstract class BaseChartFragment<T extends IChartModelController, V extends ViewDataBinding> extends MyBaseFragment<V> {

    public static final int CHART_ELECTRICITY_USAGE = 0;
    public static final int CHART_ELECTRICITY_BATTERY = 1;
    public static final int CHART_ELECTRICITY_SOLAR = 2;
    public static final int CHART_ELECTRICITY_GRID = 3;
    public static final int CHART_ELECTRICITY_PRICE = 4;
    public static final int CHART_ELECTRICITY_REVENUE = 5;
    public static CycleType mCycleType = CycleType.DAY;
    public final static int DST_TIME_MINUTE = 1500;
    public final static int SUMMER_TIME_MINUTE = 1380;
    protected FlipCombinedChartView mFlipCombinedChartView;
    protected ArrayList<PSElectricityTypeBean> mData;
    protected CustomCombinedChartManager mCombinedChartManager;
    protected CustomCombinedChartManager mCombinedChartManager1;
    protected CustomCombinedChartManager mCombinedChartManager2;
    protected List<CustomCombinedChartManager> chartManagers;
    protected T chartModelController;
    protected int mIndex = 0;
    protected PlusMinusType mPlusMinusType;
    public static int mOffSet = 0;
    protected Map<String, Object> mResult;
    protected boolean isMultiply;
    protected SmartRefreshLayout refreshLayout;
    protected List<List<Float>> mChartData = new ArrayList<>();
    protected int mInterval = 1;
    protected int mStartIndex;
    protected long mStartTime;
    protected String timezone = "";
    protected boolean isBSensorInstalled = false;
    public static String[] mWeeks = new String[]{
            "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"
    };
    public static String[] months;

    public static String[] mYears = new String[]{
            "Jan", "Feb", "Mar", "Apr", "May", "June", "July", "Aug", "Sep", "Oct", "Nov", "Dec"
    };

    public static String[] lifetimes;

    public int mHourCount = 1440;

    public List<ChartNoteTextAttrBean> mAboveChartTexts;
    public ChartNoteTextAttrBean mLeftAboveText;
    protected XAxisValueFormatter formatter = new XAxisValueFormatter() {
        @Override
        public String getXValue(String original, int index, ViewPortHandler viewPortHandler) {
            switch (mCycleType) {
                case DAY:
                    if (mHourCount == DST_TIME_MINUTE) {
                        if ((index * mInterval) % DST_TIME_MINUTE == 0) {
                            int hour = (index * mInterval) / 60;
                            if (hour > 24) hour = 24;
                            String text = hour < 10 ? ("0" + hour) + ":00" : hour + ":00";
                            return text;
                        }
                    } else if (mHourCount == SUMMER_TIME_MINUTE) {
                        if ((index * mInterval) % SUMMER_TIME_MINUTE == 0) {
                            int hour = (index * mInterval) / 60;
                            if (hour == 23) hour = 24;
                            String text = hour < 10 ? ("0" + hour) + ":00" : hour + ":00";
                            return text;
                        }
                    } else {
                        if ((index * mInterval) % 720 == 0) {
                            int hour = (index * mInterval) / 60;
                            String text = hour < 10 ? ("0" + hour) + ":00" : hour + ":00";
                            return text;
                        }
                    }
                    break;

                case WEEK:
                    if (index >= mWeeks.length) return "";
                    return Local.s(mWeeks[index]);

                case MONTH:
                    if (null == months || index >= months.length) return "";
                    if (index == 0 ||
                            (months != null && index == months.length - 1)) {
                        return Local.s(months[index]);
                    }
                    break;

                case YEAR:
                    if (index >= mYears.length) return "";
                    if (index == 0 ||
                            (mYears != null && index == mYears.length - 1)) {
                        return Local.s(mYears[index]);
                    }
                    break;
                case LIFETIME:
                    if (lifetimes == null || index >= lifetimes.length) return "";
                    if (index == 0 || index == lifetimes.length - 1) {
                        return lifetimes[index];
                    }
                    break;
            }
            return "";
        }
    };

    protected YAxisValueFormatter yFormatter = (value, yAxis) -> {
        value = Math.abs(value);
        if (value == yAxis.getAxisMaximum()) {
            if (mCycleType == CycleType.DAY) {
                if (value >= 1000) {
                    String result = ChartDataUtil.getW2KWStr(value);
                    return result.length() < 3 ? result + "kW" : result + "\nkW";
                } else {
                    return (int) (value) + "W";
                }
            } else {
                if (value < 1000) {
                    return (int) (value) + "\nWh";
                } else if (value < 1000000) {
                    String result = ChartDataUtil.getW2KWStr(value);
                    return result.length() < 2 ? result + "kWh" : result + "\nkWh";
                } else {
                    String result = ChartDataUtil.getW2MWStr(value);
                    return result.length() < 2 ? result + "MWh" : result + "\nMWh";
                }
            }

        }

        if (value == 0f && mPlusMinusType == PlusMinusType.ALL) {
            return "0";
        }

        return "";
    };

    protected FillFormatter fillFormatter = (dataSet, dataProvider) -> 0;
    public int marginTop;

    protected LruCache<Integer, Map<String, Object>> mDayCache;
    protected LruCache<Integer, Map<String, Object>> mWeekCache;
    protected LruCache<Integer, Map<String, Object>> mMonthCache;
    protected LruCache<Integer, Map<String, Object>> mYearCache;
    protected static final String mFailVal = "-";
    protected boolean isSuccess;
    protected CommonAlertDialog mFailAlertDialog;
    protected int mType;
    protected int mFromIndex;
    protected boolean isDualPowerOpen = false;
    protected String mDeviceId;

    protected ViewStub mViewStub;

    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        return null;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mFromIndex = bundle.getInt(PSKeyConstant.INDEX);
            mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        }
        isDualPowerOpen = DBUtil.Bool(getDualPowerOpenKey());
        mDayCache = new LruCache<>(20);
        mWeekCache = new LruCache<>(20);
        mMonthCache = new LruCache<>(20);
        mYearCache = new LruCache<>(20);

        isBSensorInstalled = PowerStationRoot2Fragment.isAllBSensorInstalled;
        super.initView(inflateView, savedInstanceState);
        marginTop = DensityUtil.dp2px(getContext(), 101);
        mAboveChartTexts = new ArrayList<>();
        mLeftAboveText = getAboveNoteTextBean("", ChartNoteTextAttrBean.Gravity.LEFT_TOP);
        mAboveChartTexts.add(mLeftAboveText);

        mViewStub = inflateView.findViewById(R.id.view_stub);
    }

    protected void initChart(CustomCombinedChart combinedChart) {
        mCombinedChartManager = new CustomCombinedChartManager(getContext(), combinedChart);
        mCombinedChartManager.initChart(true, 0.5f, getContext().getResources().getColor(R.color.color_white_04),
                true, true, false, 10f,
                55f, 10f, 10f, 10f, 0, false,
                "", 1200, true);
        createChartModelController();
    }

    protected void initChart(FlipCombinedChartView flipCombinedChartView, CustomCombinedMarkerView markerView) {
        initChart(flipCombinedChartView, markerView, 150f);
    }

    protected void initChart(FlipCombinedChartView flipCombinedChartView, CustomCombinedMarkerView markerView, float topOffset) {
        this.mFlipCombinedChartView = flipCombinedChartView;
        mFlipCombinedChartView.setFilterListener(() -> {
            if (isSuccess) {
                resetChart();
            } else {
                resetFailChart();
            }
        });
        mCombinedChartManager1 = new CustomCombinedChartManager(getContext(), flipCombinedChartView.getCustomCombinedChart1());
        mCombinedChartManager1.initChart(true, 0.5f, getContext().getResources().getColor(R.color.color_white_04),
                true, true, false, 10f,
                topOffset, 10f, 20f, 10f, 0, false,
                "", 800, true);
        mCombinedChartManager2 = new CustomCombinedChartManager(getContext(), flipCombinedChartView.getCustomCombinedChart2());
        mCombinedChartManager2.initChart(true, 0.5f, getContext().getResources().getColor(R.color.color_white_04),
                true, true, false, 10f,
                topOffset, 10f, 20f, 10f, 0, false,
                "", 800, true);
        mCombinedChartManager1.setMarkerView(markerView);
        mCombinedChartManager2.setMarkerView(markerView);
        mCombinedChartManager1.setAboveChartTexts(mAboveChartTexts);
        mCombinedChartManager2.setAboveChartTexts(mAboveChartTexts);
        chartManagers = new ArrayList<>();
        chartManagers.add(mCombinedChartManager1);
        chartManagers.add(mCombinedChartManager2);
        createChartModelController();
    }

    public void setIvLeftEnabled(boolean enabled) {
        if (isChartViewNotNull()) {
            mFlipCombinedChartView.setIvLeftEnabled(enabled);
        }
    }

    public void setIvRightEnabled(boolean enabled) {
        if (isChartViewNotNull()) {
            mFlipCombinedChartView.setIvRightEnabled(enabled);
        }
    }

    public void onCycleTypeChanged(CycleType newCycleType) {
    }

    protected void initRefreshLayout(SmartRefreshLayout refreshLayout) {
        this.refreshLayout = refreshLayout;
        HomeRefreshHeader header = new HomeRefreshHeader(getContext());
        refreshLayout.setRefreshHeader(header);
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                getStatisticData(false);
            }
        });
    }

    protected List<ChartNoteTextAttrBean> getNoteTexts(String note1, String note2) {
        List<ChartNoteTextAttrBean> noteTextAttrBeans = new ArrayList<>();
        ChartNoteTextAttrBean noteTextAttrBean1 = new ChartNoteTextAttrBean(note1,
                getContext().getResources().getColor(R.color.color_white_04),
                DensityUtil.dp2px(getContext(), 12));
        noteTextAttrBean1.setPadding(10);
        noteTextAttrBeans.add(noteTextAttrBean1);

        ChartNoteTextAttrBean noteTextAttrBean2 = new ChartNoteTextAttrBean(note2,
                getContext().getResources().getColor(R.color.color_white_04),
                DensityUtil.sp2px(getContext(), 12));
        noteTextAttrBean2.setGravity(ChartNoteTextAttrBean.Gravity.LEFT_BOTTOM);
        noteTextAttrBean2.setPadding(10);
        noteTextAttrBeans.add(noteTextAttrBean2);
        return noteTextAttrBeans;
    }

    protected ChartNoteTextAttrBean getAboveNoteTextBean(String aboveText, ChartNoteTextAttrBean.Gravity position) {
        ChartNoteTextAttrBean chartNoteTextAttrBean = new ChartNoteTextAttrBean(aboveText,
                getContext().getResources().getColor(R.color.color_white_03),
                DensityUtil.sp2px(getContext(), 10));
        chartNoteTextAttrBean.setGravity(position);
        return chartNoteTextAttrBean;
    }

    protected void showNote(boolean show) {
        mFlipCombinedChartView.getCustomCombinedChart1().setShowNoteText(show);
        mFlipCombinedChartView.getCustomCombinedChart2().setShowNoteText(show);
    }

    public abstract void createChartModelController();

    protected void resetChart() {
        mFlipCombinedChartView.setHighlightEnable(true);
        mFlipCombinedChartView.setTimezone(timezone);
        if (mCombinedChartManager != null)
            mCombinedChartManager.setDrawDefaultXGridLines(mCycleType == CycleType.DAY);
        if (mCombinedChartManager1 != null)
            mCombinedChartManager1.setDrawDefaultXGridLines(mCycleType == CycleType.DAY);
        if (mCombinedChartManager2 != null)
            mCombinedChartManager2.setDrawDefaultXGridLines(mCycleType == CycleType.DAY);
        switch (mCycleType) {
            case DAY:
                setDayChart();
                break;

            case WEEK:
                setWeekChart();
                break;

            case MONTH:
                setMonthChart();
                break;

            case YEAR:
                setYearChart();
                break;

            case LIFETIME:
                setLifetimeChart();
                break;
        }
        chartModelController.initLineChartRender(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType);
        chartModelController.initBarChartRender(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType);
        mFlipCombinedChartView.setTvNoteVisible(mCycleType != CycleType.DAY);
    }

    protected void resetYAxisLeft(float maxVal, int index) {
        if (CollectionUtil.isListNotEmpty(chartManagers)) {
            for (CustomCombinedChartManager chartManager : chartManagers) {
                boolean isAll = mPlusMinusType == PlusMinusType.ALL;
                if (maxVal == 0f) {
                    if (mCycleType == CycleType.DAY) {
                        maxVal = 500f;
                    } else {
                        maxVal = 5000f;
                    }
                }
                switch (index) {
                    case CHART_ELECTRICITY_BATTERY:
                    case CHART_ELECTRICITY_GRID:
                        chartManager.showDrawLeftYAxisLabel(isAll);
                        chartManager.setShowAboveChartText(!isAll);
                        break;

                    case CHART_ELECTRICITY_REVENUE:
                        chartManager.showDrawLeftYAxisLabel(true);
                        chartManager.setShowAboveChartText(false);
                        break;

                    default:
                        chartManager.showDrawLeftYAxisLabel(false);
                        chartManager.setShowAboveChartText(true);
                        break;
                }
                chartManager.setYAxisMaxMin(maxVal, isAll ? -maxVal : 0, isAll ? 3 : 6);
            }
        }
    }

    protected void resetYAxisRight(int index, float maxRightVal) {
        if (CollectionUtil.isListNotEmpty(chartManagers)) {
            if (index == CHART_ELECTRICITY_USAGE && mCycleType != CycleType.DAY) {
                for (CustomCombinedChartManager chartManager : chartManagers) {
                    chartManager.initYAxisRight(false, false, false, getColor(R.color.color_white_03));
                    chartManager.setYAxisRightMaxMin(maxRightVal, 0);
                }
            }
        }
    }

    protected void checkChartViewNull() {
        if (mViewStub != null) {
            if (mFlipCombinedChartView == null) {
                View view = mViewStub.inflate();
                mFlipCombinedChartView = view.findViewById(R.id.fc_chart);
                initChartView();
            }
        }
    }

    protected void setChartDataFromServer(int index, Map<String, Object> result) {
        if (result == null) return;
        switch (mCycleType) {
            case DAY:
                Map<String, Object> resultCache = mDayCache.get(-1);
                if (resultCache != null) {
                    long cacheStartTime = DeviceHelper.getLong(resultCache, BmtDataKey.START_TIME, 0);
                    long startTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0);
                    // offset(0) 开始时间减缓存offset(-1)开始时间大于一天要清除全部缓存
                    if (startTime - cacheStartTime > 86400) {
                        mDayCache.evictAll();
                    }
                }
                if (mOffSet < 0) {
                    mDayCache.put(mOffSet, result);
                }
                break;

            case WEEK:
                mWeekCache.put(mOffSet, result);
                break;

            case MONTH:
                mMonthCache.put(mOffSet, result);
                break;

            case YEAR:
                mYearCache.put(mOffSet, result);
                break;
        }
        setChartData(index, result);
    }

    protected void setChartData(int index, Map<String, Object> result) {
        checkChartViewNull();
        if (refreshLayout != null)
            refreshLayout.finishRefresh();
        isSuccess = true;
        timezone = (String) MapUtils.get(result, BmtDataKey.TIMEZONE, "");
        long startTime = (long) MapUtils.get(result, BmtDataKey.START_TIME, 0);
        Boolean bSensorInstalled = (Boolean) MapUtils.get(result, BmtDataKey.B_SENSOR_INSTALLED, null);
        if (bSensorInstalled != null) {
            isBSensorInstalled = bSensorInstalled;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(startTime * 1000);
        if (DDDateUtil.isDSTTransitionDay(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1500;
        } else if (DDDateUtil.isWT2ST(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1380;
        } else {
            mHourCount = 1440;
        }
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        mStartIndex = hour * 60 + minute;

        mResult = result;
        mInterval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);

        if (mResult != null) {
            mStartTime = DeviceHelper.getLong(mResult, BmtDataKey.START_TIME, 0) * 1000;
            timezone = DeviceHelper.getString(mResult, BmtDataKey.TIMEZONE, "");
        }

        mChartData.clear();
        List<List<Float>> chartDataFromServer = (List<List<Float>>) result.get(BmtDataKey.DATA);
        mChartData = dealChartData(index, chartDataFromServer);
//        mChartData.addAll((List<List<Float>>) result.get(BmtDataKey.DATA));
        float maxVal = getYMax(index, mChartData);
        resetLeftAboveText(index, maxVal);
        float maxRightVal = getYRightActualMax(mChartData);
        int labelsToSkip = mCycleType == CycleType.DAY ? (mHourCount / mInterval / 4 - 1) : 0;
        chartModelController.initXAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, labelsToSkip, formatter);
        chartModelController.initYAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, yFormatter, index == 0 && mCycleType != CycleType.DAY);
//        if (index == 3 && !isBSensorInstalled && !isMultiply) {
//        if (index == 3 && !isMultiply) {
//            if (mCycleType == CycleType.DAY) {
//                maxVal = 500f;
//            } else {
//                maxVal = 5000f;
//            }
//        }
        resetYAxisLeft(maxVal, index);
        resetYAxisRight(index, maxRightVal);
        mFlipCombinedChartView.resetHighValues();
        resetChart();

    }

    protected void resetLeftAboveText(int index, float maxVal) {
        String leftYMaxValStr = ChartDataUtil.getPowerTransferVal(maxVal, maxVal,
                mCycleType == CycleType.DAY);
        String leftYMaxValUnit = ChartDataUtil.getPowerUnit(maxVal, mCycleType != CycleType.DAY);
        StringBuilder stringBuilder = new StringBuilder();
        if (index == CHART_ELECTRICITY_USAGE && mCycleType != CycleType.DAY) {
            stringBuilder.append(Local.s(getString(R.string.energy)) + "\n");
        }
        stringBuilder.append(leftYMaxValStr);
        stringBuilder.append(leftYMaxValUnit);
        mLeftAboveText.setNoteText(stringBuilder.toString());
    }

    private List<List<Float>> dealChartData(int index, List<List<Float>> chartDataFromServer) {
        int size = 0;

        switch (mCycleType) {
            case DAY:
                size = mHourCount / mInterval + 1;
                break;

            case WEEK:
                size = mWeeks.length;
                break;

            case MONTH:
                size = months.length;
                break;

            case YEAR:
                size = mYears.length;
                break;

            case LIFETIME:
                List<Float> lifeData = getXOffsetList(chartDataFromServer);
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                if (CollectionUtil.isListNotEmpty(lifeData)) {
                    float minF = lifeData.get(0);
                    float maxF = lifeData.get(lifeData.size() - 1);
                    int min = (int) minF;
                    int max = (int) maxF;
                    int count = max - min + 1;
                    lifetimes = new String[count];
                    int startYear = (int) (year + lifeData.get(0));
                    for (int i = 0; i < count; i++) {
                        lifetimes[i] = String.valueOf(startYear + i);
                    }
                } else {
                    lifetimes = new String[]{String.valueOf(year)};
                }
                size = lifetimes.length;
                break;
        }
        return ChartDataFactory.createBuilder()
                .setIndex(index)
                .setSize(size)
                .setInterval(mInterval)
                .setCycType(mCycleType)
                .setPendingData(chartDataFromServer)
                .build()
                .createChartData();
    }

    protected float getYMax(int index, List<List<Float>> data) {
        if (CollectionUtil.isListEmpty(data)) return 0f;
        float maxVal = 0;
        switch (index) {
            case CHART_ELECTRICITY_USAGE:
            case CHART_ELECTRICITY_SOLAR:
            case CHART_ELECTRICITY_REVENUE:
                List<Float> sumData = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    List<Float> sonData = data.get(i);
                    float sum = 0;
                    if (sonData.size() > 1) {
//                        for (int j = 1; j < sonData.size(); j++) {
//                            if (j == 4) break;
//                            sum = sum + sonData.get(j);
//                        }
                        sum = sum + sonData.get(1);
                    }
                    if (sonData.size() > 2) {
//                        if (index == 0 || index == 4) {
////                        if (index == 4) {
//                            if (isBSensorInstalled) {
//                                sum = sum + sonData.get(2);
//                            }
//                        } else {
//                            sum = sum + sonData.get(2);
//                        }
                        sum = sum + sonData.get(2);
                    }
                    if (sonData.size() > 3 && index != CHART_ELECTRICITY_REVENUE) {
                        sum = sum + sonData.get(3);
                    }
                    if (sonData.size() > 4 && index == CHART_ELECTRICITY_SOLAR) {
                        if (isDualPowerOpen) {
                            sum = sum + sonData.get(4);
                        }
                    }
                    sumData.add(sum);
                }
                Collections.sort(sumData);
                maxVal = sumData.get(sumData.size() - 1);
                break;

            case CHART_ELECTRICITY_BATTERY:
            case CHART_ELECTRICITY_GRID:
                List<Float> allSumData = new ArrayList<>();
                List<Float> sumData1 = new ArrayList<>();
                List<Float> sumData2 = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    List<Float> sonData = data.get(i);
                    float sum = 0;
                    if (index == CHART_ELECTRICITY_BATTERY) {
                        float batteryDataOne = sonData.size() > 1 ? sonData.get(1) : 0f;
                        sumData1.add(batteryDataOne);
                        if (sonData.size() > 2) {
                            sum = sum + sonData.get(2);
                        }
                        if (sonData.size() > 3) {
                            sum = sum + sonData.get(3);
                        }
                        if (sonData.size() > 4) {
                            if (isDualPowerOpen) {
                                sum = sum + sonData.get(4);
                            }
                        }
                        allSumData.add(Math.abs(batteryDataOne - sum));
                    } else {
                        float gridDataOne = sonData.size() > 3 ? sonData.get(3) : 0f;
                        sumData1.add(gridDataOne);
                        if (sonData.size() > 1) {
                            sum = sum + sonData.get(1);
                        }
                        if (sonData.size() > 2) {
                            sum = sum + sonData.get(2);
                        }
                        allSumData.add(Math.abs(gridDataOne - sum));
                    }
                    sumData2.add(sum);
                }
                Collections.sort(sumData1);
                Collections.sort(sumData2);
                Collections.sort(allSumData);
                float sumMax1 = sumData1.get(sumData1.size() - 1);
                float sumMax2 = sumData2.get(sumData2.size() - 1);
                if (mPlusMinusType == PlusMinusType.ALL) {
                    maxVal = allSumData.get(allSumData.size() - 1);
                } else {
                    maxVal = isMultiply ? sumMax2 : sumMax1;
                }
                break;
        }
        return ChartDataUtil.findUpperLimit(maxVal, mCycleType == CycleType.DAY);
    }

    protected float getYRightMax(List<List<Float>> data) {
        if (CollectionUtil.isListEmpty(data)) return 0f;
        float maxVal = 0;
        List<Float> loadData = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            List<Float> sonData = data.get(i);
            loadData.add(sonData.get(sonData.size() - 1));
        }
        Collections.sort(loadData);
        maxVal = loadData.get(loadData.size() - 1);
        return maxVal;

    }

    protected float getYRightActualMax(List<List<Float>> data) {
        float maxVal = getYRightMax(data);
        return ChartDataUtil.findUpperLimit(maxVal, true);
    }


    protected SectionLineDataSet getSectionLineDataSet(ArrayList<Entry> entries, int color,
                                                       YAxis.AxisDependency dependency,
                                                       int[] gradientColor, float[] pos,
                                                       boolean isSection, boolean isFilled) {
        SectionLineDataSet lineDataSet = new SectionLineDataSet(entries, "");
        lineDataSet.setLineWidth(0.5f);
        lineDataSet.setDrawCircles(false);
        lineDataSet.setCircleRadius(5f);
        lineDataSet.setDrawCubic(false);
        lineDataSet.setDrawValues(false);
        lineDataSet.setValueTextSize(10f);
        lineDataSet.setValueTextColor(Color.rgb(240, 238, 70));
        lineDataSet.setFillFormatter(fillFormatter);
        lineDataSet.setDrawHorizontalHighlightIndicator(false);
        lineDataSet.setHighLightColor(getContext().getResources().getColor(R.color.color_white_03));
        lineDataSet.setHighlightLineWidth(1f);
        lineDataSet.setDrawCircleHole(false);
        lineDataSet.setFillAlpha(60);
        if (!isSection) {
            lineDataSet.setColor(color);
            lineDataSet.setFillColor(color);
        }
        lineDataSet.setAxisDependency(dependency);
        lineDataSet.setMode(LineDataSet.Mode.CUBIC_BEZIER);
        if (isSection) {
            lineDataSet.setGradientColors(gradientColor);
            lineDataSet.setGradientPosition(pos);
        }
        lineDataSet.setSection(isSection);
        lineDataSet.setDrawFilled(isFilled);
        return lineDataSet;
    }

    protected void setFilterVisible(int mCurrentIndex) {
        if (mFlipCombinedChartView != null) {
            if (mCurrentIndex == 1 || mCurrentIndex == 3) {
                if (isMultiply) {
                    mFlipCombinedChartView.setRvFilterVisible(true);
                    mFlipCombinedChartView.setTvNoteVisible(mCycleType != CycleType.DAY);
                }
            } else {
                mFlipCombinedChartView.setRvFilterVisible(true);
                mFlipCombinedChartView.setTvNoteVisible(mCycleType != CycleType.DAY);
            }

        }
    }

    protected void getDataFailed(int index) {
        checkChartViewNull();
        isSuccess = false;
        closeTimeOutLoadinFramgmentWithErrorAlert();
        setFailChartData(index);
        if (refreshLayout != null) {
            refreshLayout.finishRefresh();
        }
        if (mFailAlertDialog == null) {
            mFailAlertDialog = CommonAlertDialog.createBuilder(getDelegateActivity())
                    .setTitleTxt("")
                    .setContentTxt(Local.s(getString(R.string.failed_try_again)))
                    .setConfirmTxt(getString(R.string.Retry))
                    .setCancelTxt(getString(R.string.cancel))
                    .setAutoDismiss(true)
                    .setShowCancel(true)
                    .setConfirmCallback(new CommonAlertDialog.OnConfirmCallback() {
                        @Override
                        public void onConfirm(CommonAlertDialog dialog) {
                            getStatisticData(true);
                        }

                        @Override
                        public void onCancel(CommonAlertDialog dialog) {

                        }
                    })
                    .builder();
        }
        if (getUserVisibleHint() && mFailAlertDialog != null && !mFailAlertDialog.isShowing()) {
            mFailAlertDialog.show();
        }
    }

    /**
     * 二维数组数据第一个索引集合
     *
     * @param data
     * @return
     */
    protected List<Float> getXOffsetList(List<List<Float>> data) {
        List<Float> result = new ArrayList<>();
        if (CollectionUtil.isListNotEmpty(data)) {
            for (List<Float> sonData : data) {
                if (CollectionUtil.isListNotEmpty(sonData)) {
                    result.add(sonData.get(0));
                }
            }
        }
        Collections.sort(result);
        return result;
    }

    protected int getXOffset(String str, String[] strArr) {
        if (strArr != null && strArr.length > 0) {
            if (!TextUtils.isEmpty(str)) {
                for (int i = 0; i < strArr.length; i++) {
                    if (str.equals(strArr[i])) {
                        return i;
                    }
                }
            }
        }
        return 0;
    }

    protected int getColor(int colorId) {
        return getContext().getResources().getColor(colorId);
    }

    /**
     * 接口失败
     */
    protected void setFailChartData(int index) {
        mHourCount = 1440;
        mInterval = 5;
        mChartData.clear();
        List<List<Float>> chartDataFromServer = new ArrayList<>();
        mChartData = dealChartData(index, chartDataFromServer);
        float maxVal = getYMax(index, mChartData);

        String leftYMaxValStr = ChartDataUtil.getPowerTransferVal(maxVal, maxVal,
                mCycleType == CycleType.DAY);
        String leftYMaxValUnit = ChartDataUtil.getPowerUnit(maxVal, mCycleType != CycleType.DAY);
        StringBuilder stringBuilder = new StringBuilder();
        if (index == CHART_ELECTRICITY_USAGE && mCycleType != CycleType.DAY) {
            stringBuilder.append(Local.s(getString(R.string.energy)) + "\n");
        }
        stringBuilder.append(leftYMaxValStr);
        stringBuilder.append(leftYMaxValUnit);
        mLeftAboveText.setNoteText(stringBuilder.toString());
        float maxRightVal = getYRightActualMax(mChartData);
        int labelsToSkip = mCycleType == CycleType.DAY ? (mHourCount / mInterval / 4 - 1) : 0;
        chartModelController.initXAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, labelsToSkip, formatter);
        chartModelController.initYAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, yFormatter, index == 0 && mCycleType != CycleType.DAY);
        resetYAxisLeft(maxVal, index);
        resetYAxisRight(index, maxRightVal);
        mFlipCombinedChartView.resetHighValues();
        resetFailChart();
    }

    protected void resetFailChart() {
        mFlipCombinedChartView.setHighlightEnable(false);
        mFlipCombinedChartView.setTvNoteVisible(false);
        if (mCombinedChartManager != null)
            mCombinedChartManager.setDrawDefaultXGridLines(mCycleType == CycleType.DAY);
        if (mCombinedChartManager1 != null)
            mCombinedChartManager1.setDrawDefaultXGridLines(mCycleType == CycleType.DAY);
        if (mCombinedChartManager2 != null)
            mCombinedChartManager2.setDrawDefaultXGridLines(mCycleType == CycleType.DAY);
        switch (mCycleType) {
            case DAY:
                setFailDayChart();
                break;

            case WEEK:
                setFailWeekChart();
                break;

            case MONTH:
                setFailMonthChart();
                break;

            case YEAR:
                setFailYearChart();
                break;

            case LIFETIME:
                setFailLifetimeChart();
                break;
        }
        chartModelController.initLineChartRender(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType);
        chartModelController.initBarChartRender(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType);
    }

    protected String getDualPowerOpenKey() {
        return DBKey.KEY_DUAL_POWER_OPEN + "_" + HomeManager.getInstance().getCurrentHome().getHomeID() + "_" + mDeviceId;
    }

    protected boolean isChartViewNotNull() {
        return mFlipCombinedChartView != null;
    }

    protected void initChartView() {

    }

    protected abstract void getStatisticData(boolean showLoading);

    protected abstract void setDayChart();

    protected abstract void setWeekChart();

    protected abstract void setMonthChart();

    protected abstract void setYearChart();

    protected abstract void setLifetimeChart();

    protected abstract void setFailDayChart();

    protected abstract void setFailWeekChart();

    protected abstract void setFailMonthChart();

    protected abstract void setFailYearChart();

    protected abstract void setFailLifetimeChart();
}

