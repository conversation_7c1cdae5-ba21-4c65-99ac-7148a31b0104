package com.dinsafer.module.powerstation.electricity.controller;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.common.utils.DensityUtil;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;

public class UsageChartModelController implements IChartModelController{

    @Override
    public void initXAxis(Context context, CustomCombinedChartManager customCombinedChartManager,
                          CycleType cycleType, PlusMinusType plusMinusType, int labelsToSkip, XAxisValueFormatter formatter) {
        switch (cycleType) {
            case DAY:
                customCombinedChartManager.initXAxis(false, false, context.getResources().getColor(R.color.color_white_04), 10f,
                        true, context.getResources().getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                        labelsToSkip, formatter);
                break;

            case WEEK:
//                customCombinedChartManager.initXAxis(true, false, context.getResources().getColor(R.color.color_white_04), 10f,
//                        false, context.getResources().getColor(R.color.color_white_04), 0.5f, XAxis.XAxisPosition.BOTTOM,
//                        0, formatter);
//                break;

            case MONTH:
//                customCombinedChartManager.initXAxis(true, false, context.getResources().getColor(R.color.color_white_04), 10f,
//                        false, context.getResources().getColor(R.color.color_white_04), 0.5f, XAxis.XAxisPosition.BOTTOM,
//                        0, formatter);
//                break;

            case YEAR:
//                customCombinedChartManager.initXAxis(true, false, context.getResources().getColor(R.color.color_white_04), 10f,
//                        false, context.getResources().getColor(R.color.color_white_04), 0.5f, XAxis.XAxisPosition.BOTTOM,
//                        0, formatter);
//                break;

            case LIFETIME:
                customCombinedChartManager.initXAxis(false, false, context.getResources().getColor(R.color.color_white_04), 10f,
                        false, context.getResources().getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                        0, formatter);
                break;
        }
    }

    @Override
    public void initYAxis(Context context, CustomCombinedChartManager customCombinedChartManager,
                          CycleType cycleType, PlusMinusType plusMinusType,
                          YAxisValueFormatter yFormatter, boolean isAxisRightEnabled) {
        switch (cycleType) {
            case DAY:
//                customCombinedChartManager.initYAxis(true, 10f, context.getResources().getColor(R.color.color_white_04),
//                        0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
//                         yFormatter, isAxisRightEnabled, 5);
//                break;

            case WEEK:
//                customCombinedChartManager.initYAxis(true, 10f, context.getResources().getColor(R.color.color_white_04),
//                        0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
//                        10000f, 0f, yFormatter, false);
//                break;

            case MONTH:
//                customCombinedChartManager.initYAxis(true, 10f, context.getResources().getColor(R.color.color_white_04),
//                        0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
//                        10000f, 0f, yFormatter, false);
//                break;

            case YEAR:
//                customCombinedChartManager.initYAxis(true, 10f, context.getResources().getColor(R.color.color_white_04),
//                        0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
//                        10000f, 0f, yFormatter, false);
//                break;

            case LIFETIME:
                customCombinedChartManager.initYAxis(false, 10f, context.getResources().getColor(R.color.color_white_03),
                        0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
                        yFormatter, isAxisRightEnabled, 6);
                break;
        }
    }

    @Override
    public void initLineChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType) {
        switch (cycleType) {
            case DAY:
                customCombinedChartManager.initLineChartRender(false);
                break;

            case WEEK:
//                customCombinedChartManager.initLineChartRender(true);
//                break;

            case MONTH:
//                customCombinedChartManager.initLineChartRender(true);
//                break;

            case YEAR:
//                customCombinedChartManager.initLineChartRender(true);
//                break;

            case LIFETIME:
                customCombinedChartManager.initLineChartRender(true);
                break;
        }
    }

    @Override
    public void initBarChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType) {
        switch (cycleType) {
            case DAY:

                break;

            case WEEK:
            case MONTH:
            case YEAR:
            case LIFETIME:
                customCombinedChartManager.initBarChartRender(DensityUtil.dp2px(context, 3), context.getResources().getColor(R.color.color_white_03), 1.0f, 2.0f);
                break;
        }
    }
}
