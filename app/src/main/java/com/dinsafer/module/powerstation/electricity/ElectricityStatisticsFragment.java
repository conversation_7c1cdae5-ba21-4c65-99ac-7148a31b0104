package com.dinsafer.module.powerstation.electricity;

import static com.dinsafer.module.powerstation.ChartDataUtil.DAY;
import static com.dinsafer.module.powerstation.ChartDataUtil.LIFETIME;
import static com.dinsafer.module.powerstation.ChartDataUtil.MONTH;
import static com.dinsafer.module.powerstation.ChartDataUtil.WEEK;
import static com.dinsafer.module.powerstation.ChartDataUtil.YEAR;

import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;

import androidx.databinding.ViewDataBinding;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.BmtManager;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityElectricityStatisticsBinding;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypeDialog;
import com.dinsafer.module.powerstation.dialog.ElectricityCircleTypePopup;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.IChartModelController;
import com.dinsafer.module.powerstation.event.ChartPageChangeEvent;
import com.dinsafer.module.powerstation.event.StatInfoEvent;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

public class ElectricityStatisticsFragment extends MyBaseFragment<ActivityElectricityStatisticsBinding>
        implements IDeviceCallBack {

    private String mDeviceId;
    private String mSubcategory;
    private CommonPagerAdapter mAdapter;
    private int mCurrentIndex;
    private ElectricityCircleTypeDialog mElectricityCircleTypeDialog;
    private ElectricityCircleTypePopup mElectricityCircleTypePopup;
    private ArrayList<BaseFragment> fragmentList = new ArrayList<BaseFragment>();

    public static Device mPSDevice;
    public static String mInterval = DAY;
    private long mStartTime;
    private String timezone = "";
    private String time = "";
    private long offsetTime = 0L;
    public static boolean isPriceReq;
    private boolean isDualPowerOpen = false;
    public static ElectricityStatisticsFragment newInstance(String deviceId, String subcategory) {
        return newInstance(deviceId, subcategory,0);
    }

    public static ElectricityStatisticsFragment newInstance(String deviceId, String subcategory, int index) {
        ElectricityStatisticsFragment fragment = new ElectricityStatisticsFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putInt(PSKeyConstant.INDEX, index);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.activity_electricity_statistics;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        isDualPowerOpen = DBUtil.Bool(getDualPowerOpenKey());
        initParams();
        initTitleBar();
        initRvTab();
        int marginTop = DensityUtil.dp2px(getContext(), 101);
        showTimeOutLoadinFramgmentWithMarginTop(marginTop);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (isDualPowerOpen) {
            initViewPager();
        } else {
            getDualPowerOpen();
        }

    }

    @Override
    public void onDestroyView() {
        mInterval = DAY;
        EventBus.getDefault().unregister(this);
        BaseChartFragment.mCycleType = CycleType.DAY;
        BaseChartFragment.mOffSet = 0;
        mPSDevice = null;
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mCurrentIndex = bundle.getInt(PSKeyConstant.INDEX);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    /**
     * 标题栏
     */
    private void initTitleBar() {
        mBinding.tvTitle.setLocalText(getString(R.string.electricity_today));
        mBinding.ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeSelf();
            }
        });

        mBinding.tvMenu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showElectricityCircleTypePopup();
            }
        });
    }

    private void getDualPowerOpen() {
        if (mPSDevice == null) {
            closeLoadingFragment();
            showErrorToast();
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put(BmtDataKey.CMD, BmtCmd.GET_DUALPOWER_OPEN);
        mPSDevice.submit(map);
    }

    /**
     * 显示周期类型弹窗
     */
    private void showElectricityCircleTypeDialog() {
        if (mElectricityCircleTypeDialog == null) {
            mElectricityCircleTypeDialog = new ElectricityCircleTypeDialog();
            mElectricityCircleTypeDialog.setTypeSelectedListener(new ElectricityCircleTypeDialog.OnTypeSelectedListener() {
                @Override
                public void onSelected(String type, CycleType cycleType) {
                    mBinding.tvMenu.setLocalText(type);
                    BaseChartFragment.mCycleType = cycleType;
                    BaseChartFragment.mOffSet = 0;
                    ((ElectricityBatteryFragment) fragmentList.get(1)).setPowerLevelVisible(BaseChartFragment.mCycleType == CycleType.DAY);
                    switch (BaseChartFragment.mCycleType) {
                        case DAY:
                            mInterval = DAY;
                            break;

                        case WEEK:
                            mInterval = WEEK;
                            break;

                        case MONTH:
                            mInterval = MONTH;
                            break;

                        case YEAR:
                            mInterval = YEAR;
                            break;

                        case LIFETIME:
                            mInterval = LIFETIME;
                            break;
                    }
                    offsetTime = 0L;
                    ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(mCurrentIndex)).getStatisticData(true);
                    for (BaseFragment fragment : fragmentList) {
                            ((BaseChartFragment) fragment).setIvRightEnabled(false);
                            ((BaseChartFragment) fragment).setIvLeftEnabled(BaseChartFragment.mCycleType != CycleType.LIFETIME);
                    }
                }
            });
        }
        if (mElectricityCircleTypeDialog != null && !mElectricityCircleTypeDialog.isAdded()) {
            mElectricityCircleTypeDialog.show(getChildFragmentManager(), ElectricityCircleTypeDialog.TAG);
        }
    }

    /**
     * 显示周期类型弹窗
     */
    private void showElectricityCircleTypePopup() {
        if (mElectricityCircleTypePopup == null) {
            CycleType lastCycleType = BaseChartFragment.mCycleType;
            mElectricityCircleTypePopup = new ElectricityCircleTypePopup(getContext(), ElectricityCircleTypePopup.CIRCLE_TYPE);
            mElectricityCircleTypePopup.setTypeSelectedListener(new ElectricityCircleTypeDialog.OnTypeSelectedListener() {
                @Override
                public void onSelected(String type, CycleType cycleType) {
                    mBinding.tvMenu.setLocalText(type);
                    BaseChartFragment.mCycleType = cycleType;
                    BaseChartFragment.mOffSet = 0;
                    ((ElectricityBatteryFragment) fragmentList.get(1)).setPowerLevelVisible(BaseChartFragment.mCycleType == CycleType.DAY);
                    switch (BaseChartFragment.mCycleType) {
                        case DAY:
                            mInterval = DAY;
                            break;

                        case WEEK:
                            mInterval = WEEK;
                            break;

                        case MONTH:
                            mInterval = MONTH;
                            break;

                        case YEAR:
                            mInterval = YEAR;
                            break;

                        case LIFETIME:
                            mInterval = LIFETIME;
                            break;
                    }
                    offsetTime = 0L;
                    ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(mCurrentIndex)).getStatisticData(true);
                    for (BaseFragment fragment : fragmentList) {
                        if (lastCycleType != cycleType) {
                            ((BaseChartFragment) fragment).onCycleTypeChanged(cycleType);
                        }
                            ((BaseChartFragment) fragment).setIvRightEnabled(false);
                            ((BaseChartFragment) fragment).setIvLeftEnabled(BaseChartFragment.mCycleType != CycleType.LIFETIME);
                    }
                }
            });
            mElectricityCircleTypePopup.setOnDismissListener(() -> backgroundAlpha(1f));
        }
        if (mElectricityCircleTypePopup != null && !mElectricityCircleTypePopup.isShowing()) {
            mElectricityCircleTypePopup.showAtLocation(mBinding.tvMenu,
                    mBinding.tvMenu.getWidth() -
                            DensityUtil.dp2px(getContext(), 136), 0);
            backgroundAlpha(0.5f);
        }
    }

    private void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp = getDelegateActivity().getWindow().getAttributes();
        lp.alpha = f;
        getDelegateActivity().getWindow().setAttributes(lp);
    }

    /**
     * Tab
     */
    private void initRvTab() {
        mBinding.tabElectricity.setTabSelectedListener(index -> setCurrentTab(index, true));
        mBinding.tabElectricity.post(() -> {
            mBinding.tabElectricity.setFirstPosition(mCurrentIndex);
        });
    }

    /**
     * 设置tab和viewpager索引
     *
     * @param index
     */
    private void setCurrentTab(int index, boolean isTab) {
        if (index == mCurrentIndex) return;
        mCurrentIndex = index;
        mBinding.tvMenu.setVisibility(mCurrentIndex != 4 ? View.VISIBLE : View.GONE);
        if (mCurrentIndex == 4) {
            BaseChartFragment.mCycleType = CycleType.DAY;
            mInterval = DAY;
            mBinding.tvMenu.setLocalText(getString(R.string.electricity_day));
            for (BaseFragment fragment : fragmentList) {
                    ((BaseChartFragment) fragment).setIvLeftEnabled(true);
            }
            if (mElectricityCircleTypePopup != null) {
                mElectricityCircleTypePopup.setIndexSelected(0);
            }
        }
        if (isTab) {
            mBinding.vpElectricity.setCurrentItem(index);
        } else {
            mBinding.tabElectricity.setTabSelected(index, false);
        }
    }

    /**
     * ViewPager
     */
    private void initViewPager() {
        fragmentList.add(UsageFragment.newInstance(mCurrentIndex, mDeviceId, mPSDevice.getSubCategory()));
        fragmentList.add(ElectricityBatteryFragment.newInstance(mCurrentIndex, mDeviceId, mPSDevice.getSubCategory()));
        fragmentList.add(ElectricitySolarFragment.newInstance(mCurrentIndex, mDeviceId, mPSDevice.getSubCategory()));
        fragmentList.add(ElectricityGridFragment.newInstance(mCurrentIndex, mDeviceId));

        mAdapter = new CommonPagerAdapter(getChildFragmentManager(), fragmentList);
        mBinding.vpElectricity.setAdapter(mAdapter);
        mBinding.vpElectricity.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                setCurrentTab(i, false);
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mBinding.vpElectricity.setOffscreenPageLimit(fragmentList.size());
        mBinding.tvMenu.setVisibility(mCurrentIndex != 4 ? View.VISIBLE : View.GONE);
        mBinding.vpElectricity.setCurrentItem(mCurrentIndex);
        ((BaseChartFragment) fragmentList.get(mCurrentIndex)).getStatisticData(false);
    }

    private void resetTitle() {
        if (CollectionUtil.isListEmpty(fragmentList)) return;
//        ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(mCurrentIndex)).setFilterVisible(mCurrentIndex);
        if (BaseChartFragment.mCycleType == CycleType.DAY) {
            if (BaseChartFragment.mOffSet == 0) {
                mBinding.tvTitle.setLocalText(getString(R.string.electricity_today));
            } else if (BaseChartFragment.mOffSet == -1) {
                mBinding.tvTitle.setLocalText(getString(R.string.electricity_yesterday));
            } else {
                mBinding.tvTitle.setLocalText(time);
            }
        } else {
            mBinding.tvTitle.setLocalText(time);
        }
    }

    private void resetBaseInfo(Map<String, Object> result) {
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (result == null) return;
        mStartTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0) * 1000;
        if (offsetTime == 0L) {
            offsetTime = DeviceHelper.getLong(result, BmtDataKey.START_TIME, 0) * 1000;
        }
        timezone = DeviceHelper.getString(result, BmtDataKey.TIMEZONE, "");
        time = ChartDataUtil.getResetTime(mStartTime, timezone, BaseChartFragment.mCycleType);
        resetTitle();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (!isAdded()) return;
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            if (!getDelegateActivity().isFragmentInTopExcludeLoading(this)) return;
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (cmd.equals(BmtCmd.GET_DUALPOWER_OPEN)) {
                            isDualPowerOpen = DeviceHelper.getBoolean(result, BmtDataKey.IS_OPEN, false);
                            if (isDualPowerOpen) {
                                DBUtil.Put(getDualPowerOpenKey(), isDualPowerOpen);
                            }
                            initViewPager();

                        } else {
                            if (fragmentList.size() < 4) return;
                            switch (cmd) {
                                case BmtCmd.GET_STATS_LOADUSAGE_V2:
                                    resetBaseInfo(result);
                                    ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(0)).setChartDataFromServer(BaseChartFragment.CHART_ELECTRICITY_USAGE, result);
                                    break;
                                case BmtCmd.GET_STATS_BATTERY_V2:
                                    resetBaseInfo(result);
                                    ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(1)).setChartDataFromServer(BaseChartFragment.CHART_ELECTRICITY_BATTERY, result);
                                    break;

                                case BmtCmd.GET_STATS_MPPT_V2:
                                    resetBaseInfo(result);
                                    ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(2)).setChartDataFromServer(BaseChartFragment.CHART_ELECTRICITY_SOLAR, result);
                                    break;

                                case DsCamCmd.GET_STATS_GRID:
                                    resetBaseInfo(result);
                                    ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(3)).setChartDataFromServer(BaseChartFragment.CHART_ELECTRICITY_GRID, result);
                                    break;

                                case DsCamCmd.GET_STATS_BATTERY_POWERLEVEL:
                                    ((ElectricityBatteryFragment) fragmentList.get(1)).setPowerLevelDataFromServer(result);
                                    break;

                                case BmtCmd.GET_ELEC_PRICE_INFO:
                                    if (fragmentList.size() < 5) return;
                                    resetBaseInfo(result);
                                    ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(4)).setChartDataFromServer(BaseChartFragment.CHART_ELECTRICITY_PRICE, result);
                                    isPriceReq = false;
                                    break;
                            }
                        }
                    }
                });
            } else {
                if (cmd.equals(BmtCmd.GET_DUALPOWER_OPEN)) {
                    initViewPager();
                } else {
                    if (fragmentList.size() < 4) return;
                    switch (cmd) {
                        case BmtCmd.GET_DUALPOWER_OPEN:

                            break;

                        case BmtCmd.GET_STATS_LOADUSAGE_V2:
                            resetBaseInfo(result);
                            ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(0)).getDataFailed(BaseChartFragment.CHART_ELECTRICITY_USAGE);
                            break;
                        case BmtCmd.GET_STATS_BATTERY_V2:
                            resetBaseInfo(result);
                            ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(1)).getDataFailed(BaseChartFragment.CHART_ELECTRICITY_BATTERY);
                            break;

                        case BmtCmd.GET_STATS_MPPT_V2:
                            resetBaseInfo(result);
                            ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(2)).getDataFailed(BaseChartFragment.CHART_ELECTRICITY_SOLAR);
                            break;

                        case DsCamCmd.GET_STATS_GRID:
                            resetBaseInfo(result);
                            ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(3)).getDataFailed(BaseChartFragment.CHART_ELECTRICITY_GRID);
                            break;

                        case BmtCmd.GET_ELEC_PRICE_INFO:
                            if (fragmentList.size() < 5) return;

                            if (isPriceReq) {
                                resetBaseInfo(result);
                                ((BaseChartFragment<IChartModelController, ViewDataBinding>) fragmentList.get(4)).getDataFailed(BaseChartFragment.CHART_ELECTRICITY_PRICE);
                            }
                            isPriceReq = false;
                            break;

                        case DsCamCmd.GET_STATS_BATTERY_POWERLEVEL:
                            resetBaseInfo(result);
                            ((ElectricityBatteryFragment) fragmentList.get(1)).getPowerLevelFailed();
                            break;
                    }
                }
            }
        }
    }

    private static final long oneDayTimeTills = 1000L * 60 * 60 * 24;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ChartPageChangeEvent event) {
        OperateOrientation orientation = event.getOrientation();
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(offsetTime);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String title = "";
        switch (BaseChartFragment.mCycleType) {
            case DAY:
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills
                        : offsetTime + oneDayTimeTills;
                break;

            case WEEK:
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills * 7
                        : offsetTime + oneDayTimeTills * 7;
                break;

            case MONTH:
                int minusYear = month > 1 ? year : year - 1;
                int minusMonth = month > 1 ? month - 1 : 12;
                int days = DDDateUtil.getDaysByYearMonth(orientation == OperateOrientation.LEFT ? minusYear : year, orientation == OperateOrientation.LEFT ? minusMonth : month);
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills * days
                        : offsetTime + oneDayTimeTills * days;
                break;

            case YEAR:
                int dayYear = DDDateUtil.getDaysByYear(orientation == OperateOrientation.LEFT ? (year - 1) : year);
                offsetTime = orientation == OperateOrientation.LEFT ? offsetTime - oneDayTimeTills * dayYear
                        : offsetTime + oneDayTimeTills * dayYear;
                break;

            case LIFETIME:

                break;
        }
        calendar.setTimeInMillis(offsetTime);
        int offsetYear = calendar.get(Calendar.YEAR);
        int offsetMonth = calendar.get(Calendar.MONTH) + 1;
        Date startDate = DDDateUtil.getCurrentWeekDayStartOrEndTime(calendar, true);
        Date endDate = DDDateUtil.getCurrentWeekDayStartOrEndTime(calendar, false);
        switch (BaseChartFragment.mCycleType) {
            case DAY:
                if (BaseChartFragment.mOffSet == 0) {
                    title = getString(R.string.electricity_today);
                } else if (BaseChartFragment.mOffSet == -1) {
                    title = getString(R.string.electricity_yesterday);
                } else {
                    title = DDDateUtil.formatWithTimezone(offsetTime, timezone, "yyyy.MM.dd");
                }
                break;

            case WEEK:
                title = DDDateUtil.formatDate(startDate, "MM.dd") + "-" + DDDateUtil.formatDate(endDate, "MM.dd");
                break;

            case MONTH:
                title = offsetYear + "." + (offsetMonth < 10 ? ("0" + offsetMonth) : ("" + offsetMonth));
                break;

            case YEAR:
                title = offsetYear + "";
                break;

            case LIFETIME:
                title = DinSaferApplication.getAppContext().getString(R.string.electricity_lifetime);
                break;
        }
        mBinding.tvTitle.setLocalText(title);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(StatInfoEvent event) {
        Map<String, Object> result = event.getResult();
        resetBaseInfo(result);
    }

    private String getDualPowerOpenKey() {
        return DBKey.KEY_DUAL_POWER_OPEN + "_" + HomeManager.getInstance().getCurrentHome().getHomeID() + "_" + mDeviceId;
    }
}
