package com.dinsafer.module.powerstation.dialog;

import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogPsFirmwareVersionDetailBinding;
import com.dinsafer.module.powerstation.adapter.PSFirmwareVersionDetailModel;
import com.dinsafer.module.powerstation.bean.KeyValueBean;
import com.dinsafer.module.powerstation.bean.PSFirmWareVersionDetailBean;
import com.dinsafer.module.powerstation.event.PSFirmWareVersionUpdateEvent;
import com.dinsafer.permission.BaseBottomSheetDialog;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/1 16:39
 * @description :  固件版本信息弹窗
 */
public class PSFirmWareDetailDialog extends BaseBottomSheetDialog<DialogPsFirmwareVersionDetailBinding> {

    public static String TAG = PSFirmWareDetailDialog.class.getSimpleName();

    private BindMultiAdapter<PSFirmwareVersionDetailModel> mAdapter;
    private final ArrayList<PSFirmwareVersionDetailModel> mData = new ArrayList<>();
    private DialogInterface.OnShowListener mOnShowListener;

    public static PSFirmWareDetailDialog newInstance(DialogInterface.OnShowListener l) {
        final PSFirmWareDetailDialog dialog = new PSFirmWareDetailDialog();
        dialog.setOnShowListener(l);
        return dialog;
    }

    public void setOnShowListener(DialogInterface.OnShowListener listener) {
        this.mOnShowListener = listener;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        DDLog.i(TAG, "onCreate");
        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroyView() {
        DDLog.i(TAG, "onDestroyView");
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialog1 -> {
            if (null != mOnShowListener) {
                mOnShowListener.onShow(dialog1);
            }
        });
        return dialog;

    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_ps_firmware_version_detail;
    }

    @Override
    protected void initView() {
        super.initView();
        setEnableDrag(false);
        mBinding.getRoot().setBackgroundResource(R.drawable.shape_bg_warning_dialog);
        mBinding.ivClose.setOnClickListener(v -> dismiss());
        initRv();
    }

    private void initRv() {
        mAdapter = new BindMultiAdapter<>();
        mBinding.rvVersion.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvVersion.setAdapter(mAdapter);
        mAdapter.openLoadAnimation();
        mAdapter.setNewData(mData);
        // mData = new ArrayList<PSFirmwareVersionDetailModel>();
        // List<PSFirmWareVersionDetailBean> data = new ArrayList<>();
        // List<KeyValueBean> keyValueBeanList1 = new ArrayList<>();
        // keyValueBeanList1.add(new KeyValueBean("IOT", "1.1.2"));
        // keyValueBeanList1.add(new KeyValueBean("MCU", "1.1.2"));
        // keyValueBeanList1.add(new KeyValueBean("MPPT", "1.1.2"));
        // data.add(new PSFirmWareVersionDetailBean("", keyValueBeanList1));
        //
        // List<KeyValueBean> keyValueBeanList2 = new ArrayList<>();
        // keyValueBeanList2.add(new KeyValueBean("MPPT A", "1.1.2"));
        // keyValueBeanList2.add(new KeyValueBean("MPPT B", "1.1.2"));
        // keyValueBeanList2.add(new KeyValueBean("MPPT C", "1.1.2"));
        // data.add(new PSFirmWareVersionDetailBean("MPPT", keyValueBeanList2));
        //
        // List<KeyValueBean> keyValueBeanList3 = new ArrayList<>();
        // keyValueBeanList3.add(new KeyValueBean("Inverter A", "1.1.2"));
        // keyValueBeanList3.add(new KeyValueBean("Inverter B", "1.1.2"));
        // keyValueBeanList3.add(new KeyValueBean("Inverter C", "1.1.2"));
        // data.add(new PSFirmWareVersionDetailBean("Inverter", keyValueBeanList3));
        //
        // List<KeyValueBean> keyValueBeanList4 = new ArrayList<>();
        // keyValueBeanList4.add(new KeyValueBean("#battery_id", "1.1.2"));
        // keyValueBeanList4.add(new KeyValueBean("#battery_id", "1.1.2"));
        // keyValueBeanList4.add(new KeyValueBean("#battery_id", "1.1.2"));
        // keyValueBeanList4.add(new KeyValueBean("#battery_id", "1.1.2"));
        // data.add(new PSFirmWareVersionDetailBean("Battery Packs", keyValueBeanList4));
        //
        // for (int i = 0; i < data.size(); i++) {
        //     mData.add(new PSFirmwareVersionDetailModel(data.get(i), i == data.size() - 1));
        // }
        // mAdapter.setNewData(mData);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(final PSFirmWareVersionUpdateEvent ev) {
        DDLog.d(TAG, "onEvent: " + ev);
        Map<String, PSFirmWareVersionDetailBean> versionInfoMap = ev.getVersionInfoMap();
        if (null == mAdapter || null == versionInfoMap) {
            return;
        }

        DDLog.d(TAG, "UPDATE...");
        synchronized (mData) {
            boolean loaded = false;
            mData.clear();
            PSFirmWareVersionDetailBean common = versionInfoMap.get(PSFirmWareVersionDetailBean.KEY_VERSION_COMMON);
            if (null != common) {
                final List<KeyValueBean> items = common.getItems();
                if (null != items) {
                    mData.add(new PSFirmwareVersionDetailModel(getContext(), common, ev.getDevice()));
                }
                if (common.isLoadFinished()) {
                    loaded = true;
                }
            }

            PSFirmWareVersionDetailBean cabinet = versionInfoMap.get(PSFirmWareVersionDetailBean.KEY_VERSION_CABINET);
            if (null != cabinet) {
                final List<KeyValueBean> items = cabinet.getItems();
                if (null != items) {
                    mData.add(new PSFirmwareVersionDetailModel(getContext(), cabinet, ev.getDevice()));
                }
                if (cabinet.isLoadFinished()) {
                    loaded = true;
                }
            }

            PSFirmWareVersionDetailBean inverter = versionInfoMap.get(PSFirmWareVersionDetailBean.KEY_VERSION_INVERTER);
            if (null != inverter) {
                final List<KeyValueBean> items = inverter.getItems();
                if (null != items) {
                    mData.add(new PSFirmwareVersionDetailModel(getContext(), inverter, ev.getDevice()));
                }
                if (inverter.isLoadFinished()) {
                    loaded = true;
                }
            }

            PSFirmWareVersionDetailBean battery = versionInfoMap.get(PSFirmWareVersionDetailBean.KEY_VERSION_BATTERY);
            if (null != battery) {
                final List<KeyValueBean> items = battery.getItems();
                if (null != items) {
                    mData.add(new PSFirmwareVersionDetailModel(getContext(), battery, ev.getDevice()));
                }
                if (battery.isLoadFinished()) {
                    loaded = true;
                }
            }

            final int size = mData.size();
            if (size > 0) {
                mData.get(size - 1).setLast(true);
            }
            mBinding.pbVersionLoading.setVisibility(loaded ? View.INVISIBLE : View.VISIBLE);
        }

        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mAdapter.notifyDataSetChanged();
            }
        });
    }
}
