package com.dinsafer.module.powerstation.event;

import java.util.ArrayList;

public class SaveScheduledModeEvent {

    private int smart;
    private int emergency;
    private ArrayList<Integer> weekdays;
    private ArrayList<Integer> weekend;

    public SaveScheduledModeEvent(int smart, int emergency, ArrayList<Integer> weekdays, ArrayList<Integer> weekend) {
        this.smart = smart;
        this.emergency = emergency;
        this.weekdays = weekdays;
        this.weekend = weekend;
    }

    public int getSmart() {
        return smart;
    }

    public void setSmart(int smart) {
        this.smart = smart;
    }

    public int getEmergency() {
        return emergency;
    }

    public void setEmergency(int emergency) {
        this.emergency = emergency;
    }

    public ArrayList<Integer> getWeekdays() {
        return weekdays;
    }

    public void setWeekdays(ArrayList<Integer> weekdays) {
        this.weekdays = weekdays;
    }

    public ArrayList<Integer> getWeekend() {
        return weekend;
    }

    public void setWeekend(ArrayList<Integer> weekend) {
        this.weekend = weekend;
    }
}
