package com.dinsafer.module.powerstation.settings.network;

import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentNetworkDiagnosisBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSNetworkSuggestionsAdapter;
import com.dinsafer.module.powerstation.bean.PSNetworkSuggestionsBean;
import com.dinsafer.module.settting.ui.FeedBackFragment;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 10:32
 * @description :
 */
public class NetworkDiagnosisFragment extends MyBaseFragment<FragmentNetworkDiagnosisBinding> {

    private PSNetworkSuggestionsAdapter mPSNetworkSuggestionsAdapter;
    private String mDeviceId;
    private String mSubcategory;

    public static NetworkDiagnosisFragment newInstance(final String deviceId, String subcategory) {
        NetworkDiagnosisFragment fragment = new NetworkDiagnosisFragment();
        final Bundle args = new Bundle();
        args.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        args.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_network_diagnosis;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_advanced_settings_network_diagnosis));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.switchBluetoothMode.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {

            }
        });
        String content = Local.s(getString(R.string.ps_advanced_settings_none_work));
        String contactUs = Local.s(getString(R.string.ps_advanced_settings_contact_us));
        String text = content + contactUs;
        mBinding.tvContactUs.setMovementMethod(LinkMovementMethod.getInstance());
        mBinding.tvContactUs.setText(StringUtil.getSpannableString(getResources().getColor(R.color.color_FF6497FD), text, text.length() - contactUs.length(), text.length(), true,
                new StringUtil.OnSpanClickListener() {
                    @Override
                    public void onClick() {
                        getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
                    }
                }));
        mBinding.tvChangeNetwork.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(PSNetworkBleScanFragment.newInstance(mDeviceId, mSubcategory));
        });
        initRv();
    }

    private void initRv() {
        mBinding.rvSuggestions.setLayoutManager(new LinearLayoutManager(getContext()));
        mPSNetworkSuggestionsAdapter = new PSNetworkSuggestionsAdapter();
        mBinding.rvSuggestions.setAdapter(mPSNetworkSuggestionsAdapter);
        List<PSNetworkSuggestionsBean> data = new ArrayList<>();
        if (mSubcategory.equals(DinConst.TYPE_BMT_HP5000)) {
            data.add(new PSNetworkSuggestionsBean(getString(R.string.ps_advanced_settings_wifi_troubleshooting), getString(R.string.ps_advanced_settings_network_suggestions_2_1)));
        } else {
            data.add(new PSNetworkSuggestionsBean(getString(R.string.ps_advanced_settings_ethernet_troubleshooting), getString(R.string.ps_advanced_settings_network_suggestions_1)));
            data.add(new PSNetworkSuggestionsBean(getString(R.string.ps_advanced_settings_wifi_troubleshooting), getString(R.string.ps_advanced_settings_network_suggestions_2_2)));
        }
        data.add(new PSNetworkSuggestionsBean(getString(R.string.ps_advanced_settings_4g_troubleshooting), getString(R.string.ps_advanced_settings_network_suggestions_3)));
        mPSNetworkSuggestionsAdapter.setNewData(data);
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
    }
}
