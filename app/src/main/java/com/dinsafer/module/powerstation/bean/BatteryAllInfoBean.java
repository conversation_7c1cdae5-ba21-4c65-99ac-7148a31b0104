package com.dinsafer.module.powerstation.bean;

public class BatteryAllInfoBean {

    private int percentage;
    private boolean isEmergency;
    private int chargeMode;
    private int chargeStatus;
    private int emergencyReserve;
    private int smartReserve;
    private String balance;
    private String timeKey;
    private String hour;
    private String minute;
    private boolean showUpdate;

    public BatteryAllInfoBean() {
    }

    public BatteryAllInfoBean(int percentage, boolean isEmergency, int chargeMode, int chargeStatus, int emergencyReserve, int smartReserve, String balance, String timeKey, String hour, String minute, boolean showUpdate) {
        this.percentage = percentage;
        this.isEmergency = isEmergency;
        this.chargeMode = chargeMode;
        this.chargeStatus = chargeStatus;
        this.emergencyReserve = emergencyReserve;
        this.smartReserve = smartReserve;
        this.balance = balance;
        this.timeKey = timeKey;
        this.hour = hour;
        this.minute = minute;
        this.showUpdate = showUpdate;
    }

    public int getPercentage() {
        return percentage;
    }

    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    public boolean isEmergency() {
        return isEmergency;
    }

    public void setEmergency(boolean emergency) {
        isEmergency = emergency;
    }

    public int getChargeMode() {
        return chargeMode;
    }

    public void setChargeMode(int chargeMode) {
        this.chargeMode = chargeMode;
    }

    public int getChargeStatus() {
        return chargeStatus;
    }

    public void setChargeStatus(int chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

    public int getEmergencyReserve() {
        return emergencyReserve;
    }

    public void setEmergencyReserve(int emergencyReserve) {
        this.emergencyReserve = emergencyReserve;
    }

    public int getSmartReserve() {
        return smartReserve;
    }

    public void setSmartReserve(int smartReserve) {
        this.smartReserve = smartReserve;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getTimeKey() {
        return timeKey;
    }

    public void setTimeKey(String timeKey) {
        this.timeKey = timeKey;
    }

    public String getHour() {
        return hour;
    }

    public void setHour(String hour) {
        this.hour = hour;
    }

    public String getMinute() {
        return minute;
    }

    public void setMinute(String minute) {
        this.minute = minute;
    }

    public boolean isShowUpdate() {
        return showUpdate;
    }

    public void setShowUpdate(boolean showUpdate) {
        this.showUpdate = showUpdate;
    }
}
