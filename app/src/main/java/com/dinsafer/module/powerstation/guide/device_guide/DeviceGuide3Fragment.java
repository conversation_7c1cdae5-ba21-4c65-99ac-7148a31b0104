package com.dinsafer.module.powerstation.guide.device_guide;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDeviceGuide3Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class DeviceGuide3Fragment extends MyBaseFragment<FragmentDeviceGuide3Binding> {

    public static DeviceGuide3Fragment newInstance() {
        return new DeviceGuide3Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_device_guide_3;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
