package com.dinsafer.module.powerstation.utils.comparator;

import com.dinsafer.module_home.bean.ElectricitySupplierBean;

import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;

public class PSElectricitySupplierComparator implements Comparator<ElectricitySupplierBean> {
    @Override
    public int compare(ElectricitySupplierBean model1, ElectricitySupplierBean model2) {
        String s1 = model1.getName();
        String s2 = model2.getName();
        int minLength = Math.min(s1.length(), s2.length());
        for (int i = 0; i < minLength; i++) {
            char c1 = s1.charAt(i);
            char c2 = s2.charAt(i);
            int result = compareChars(c1, c2);
            if (result != 0) {
                return result;
            }
        }
        return Integer.compare(s1.length(), s2.length());
    }

    private int compareChars(char c1, char c2) {
        if (isEnglish(c1) && isEnglish(c2)) {
            char lower1 = Character.toLowerCase(c1);
            char lower2 = Character.toLowerCase(c2);
            if (lower1 != lower2) {
                return lower1 - lower2;
            }
            return getNum(c1) - getNum(c2);
        }

        int type1 = getCharType(c1);
        int type2 = getCharType(c2);

        if (type1 != type2) {
            return Integer.compare(type1, type2);
        } else {
            return Character.compare(c1, c2);
        }
    }

    private int getCharType(char c) {
        if (Character.isUpperCase(c)) return 1;
        if (Character.isLowerCase(c)) return 1;
        if (Character.isDigit(c)) return 3;
        return 4;
    }

    private boolean isEnglish(char c) {
        return (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z');
    }

    private int getNum(char c) {
        if (Character.isUpperCase(c)) return (c - 'A') * 2;
        else if (Character.isLowerCase(c)) return (c - 'a') * 2 + 1;
        return 0;
    }
}