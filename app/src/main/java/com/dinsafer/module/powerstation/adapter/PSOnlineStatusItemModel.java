package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.BmtManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsOnlineStatusBinding;
import com.dinsafer.dinsdk.BmtUtil;

public class PSOnlineStatusItemModel extends BasePowerStationItemModel<ItemPsOnlineStatusBinding> {

    final int[] wifiIcons = {R.drawable.icon_plugin_wifi_0, R.drawable.icon_plugin_wifi_1,
            R.drawable.icon_plugin_wifi_2, R.drawable.icon_plugin_wifi_3};

    final int[] cellularIcons = {R.drawable.icon_plugin_list_signal_0, R.drawable.icon_plugin_list_signal_1,
            R.drawable.icon_plugin_list_signal_2, R.drawable.icon_plugin_list_signal_3};

    private boolean isOnline;
    private int wifiSignal;
    private int cellularSignal;
    private int ethernetSignal;
    private int balancingState;

    public PSOnlineStatusItemModel(Context context, String deviceId, String sub) {
        super(context, deviceId, sub);
        isOnline = BmtUtil.isDeviceConnected(BmtManager.getInstance().getDeviceById(deviceId, sub));
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_online_status;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsOnlineStatusBinding binding) {
        if (isOnline) {
            binding.tvDiagnosticNetwork.setVisibility(View.GONE);
            if (wifiSignal > 1 && wifiSignal < wifiIcons.length) {
                binding.ivWifi.setImageResource(wifiIcons[wifiSignal]);
                binding.ivWifi.setVisibility(ethernetSignal > 1 ? View.GONE : View.VISIBLE);
            } else {
                binding.ivWifi.setVisibility(View.GONE);
            }

            if (cellularSignal >= 0 && cellularSignal < cellularIcons.length) {
                binding.ivCellular.setImageResource(cellularIcons[cellularSignal]);
                binding.ivCellular.setVisibility(cellularSignal > 0 ? View.VISIBLE : View.GONE);
            } else {
                binding.ivCellular.setVisibility(View.GONE);
            }
            binding.ivLan.setVisibility(ethernetSignal > 1 ? View.VISIBLE : View.GONE);
            binding.tvState.setVisibility(balancingState > 0 ? View.VISIBLE : View.GONE);
            if (balancingState == 1) {
                binding.tvState.setLocalText(mContext.getString(R.string.pre_balancing));
                binding.tvState.setTextColor(mContext.getResources().getColor(R.color.color_tip_05_2));
                binding.tvState.setBackground(mContext.getDrawable(R.drawable.shape_color_tip_05_4_r3));
            } else if (balancingState > 1) {
                binding.tvState.setLocalText(mContext.getString(R.string.balancing));
                binding.tvState.setTextColor(mContext.getResources().getColor(R.color.secondary_tip_01));
                binding.tvState.setBackground(mContext.getDrawable(R.drawable.bg_power_station_state));
            }

        } else {
            binding.tvDiagnosticNetwork.setVisibility(View.VISIBLE);
            binding.ivWifi.setVisibility(View.GONE);
            binding.ivCellular.setVisibility(View.GONE);
            binding.ivLan.setVisibility(View.GONE);
            binding.tvState.setVisibility(View.GONE);
        }
    }

    public void setOnline(boolean online) {
        isOnline = online;
    }

    public void setWifiSignal(int wifiSignal) {
        this.wifiSignal = wifiSignal;
    }

    public void setCellularSignal(int cellularSignal) {
        this.cellularSignal = cellularSignal;
    }

    public void setEthernetSignal(int ethernetSignal) {
        this.ethernetSignal = ethernetSignal;
    }

    public void setBalancingState(int balancingState) {
        this.balancingState = balancingState;
    }
}
