package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsUserInformationBinding;
import com.dinsafer.model.event.HideKeyboardOnTouchBlankEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.util.ClearAllEditText;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.RegxUtil;
import com.google.gson.Gson;
import com.jakewharton.rxbinding.view.RxView;
import com.jakewharton.rxbinding.widget.RxTextView;

import org.greenrobot.eventbus.EventBus;

import java.io.Serializable;
import java.util.List;

import rx.Observable;

public class PSUserInformationFragment extends MyBaseFragment<FragmentPsUserInformationBinding> {

    private List<CountryBean> mCountryList;
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;

    public static PSUserInformationFragment newInstance(List<CountryBean> countries, FamilyBalanceContractInfo familyBalanceContractInfo) {
        PSUserInformationFragment fragment = new PSUserInformationFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PSKeyConstant.KEY_COUNTRIES, (Serializable) countries);
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_user_information;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
//        EventBus.getDefault().post(new HideKeyboardOnTouchBlankEvent(true));
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.user_information));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.llParent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCloseInput();
            }
        });
        mBinding.llContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCloseInput();
            }
        });
        mBinding.btnNext.setOnClickListener(v -> {
            if (!RegxUtil.isEmail(mBinding.etEmailAddress.getText().toString())) {
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.email_format_illegal));
                return;
            }
            saveCache();
            ClearAllEditText.clearAllEditTextFocus(this);
            getDelegateActivity().addCommonFragment(PSAddressInformationFragment.newInstance(mCountryList, mFamilyBalanceContractInfo));
        });
    }

    private void saveCache() {
        int type = mFamilyBalanceContractInfo.getType();
        if (type == 0) {
            mFamilyBalanceContractInfo.setCompany_name(mBinding.etNameValue.getText().toString());
            mFamilyBalanceContractInfo.setEuVatNumber(mBinding.etEuVatNumber.getText().toString());
            mFamilyBalanceContractInfo.setName(null);
        } else {
            mFamilyBalanceContractInfo.setCompany_name(null);
            mFamilyBalanceContractInfo.setEuVatNumber(null);
            mFamilyBalanceContractInfo.setName(mBinding.etNameValue.getText().toString());
        }

        mFamilyBalanceContractInfo.setEmailAddress(mBinding.etEmailAddress.getText().toString());
        String json = new Gson().toJson(mFamilyBalanceContractInfo);
        String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        DBUtil.Put(DBKey.KEY_FAMILY_BALANCE_CONTRACT_INFO + "_" + type  + "_" + homeId, json);
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mCountryList = (List<CountryBean>) bundle.getSerializable(PSKeyConstant.KEY_COUNTRIES);
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
            int type = mFamilyBalanceContractInfo.getType();
            mBinding.tvNameKey.setLocalText(type == 0 ? getString(R.string.company_name) : getString(R.string.first_and_last_name));
            String name = type == 0 ?
                    mFamilyBalanceContractInfo.getCompany_name()
                    : mFamilyBalanceContractInfo.getName();
            if (!TextUtils.isEmpty(name)) {
                mBinding.etNameValue.setText(name);
            }
            String euVatNumber = mFamilyBalanceContractInfo.getEuVatNumber();
            mBinding.tvEuVatNumberKey.setVisibility(type == 0 ? View.VISIBLE : View.GONE);
            mBinding.etEuVatNumber.setVisibility(type == 0 ? View.VISIBLE : View.GONE);
            if (!TextUtils.isEmpty(euVatNumber)) {
                mBinding.etEuVatNumber.setText(euVatNumber);
            }
            String email = mFamilyBalanceContractInfo.getEmailAddress();
            if (!TextUtils.isEmpty(email)) {
                mBinding.etEmailAddress.setText(email);
            } else {
                mBinding.etEmailAddress.setText(DinSDK.getUserInstance().getUser().getEmail());
            }
        }

        Observable<CharSequence> ObservableCompanyName = RxTextView.textChanges(mBinding.etNameValue);
        Observable<CharSequence> ObservableEuVatNumber = RxTextView.textChanges(mBinding.etEuVatNumber);
        Observable<CharSequence> ObservableEmailAddress = RxTextView.textChanges(mBinding.etEmailAddress);
        if (mBinding.etEuVatNumber.getVisibility() == View.VISIBLE) {
            Observable.combineLatest(ObservableCompanyName, ObservableEuVatNumber, ObservableEmailAddress, (companyName, euVatNumber, emailAddress) -> !TextUtils.isEmpty(companyName.toString())
                    && !TextUtils.isEmpty(euVatNumber.toString())
                    && !TextUtils.isEmpty(emailAddress.toString())).subscribe(aBoolean -> {
                mBinding.btnNext.setAlpha(aBoolean ? 1f : 0.5f);
                RxView.enabled(mBinding.btnNext).call(aBoolean);
            });
        } else {
            Observable.combineLatest(ObservableCompanyName, ObservableEmailAddress, (companyName, emailAddress) ->
                    !TextUtils.isEmpty(companyName.toString())
                            && !TextUtils.isEmpty(emailAddress.toString())).subscribe(aBoolean -> {
                mBinding.btnNext.setAlpha(aBoolean ? 1f : 0.5f);
                RxView.enabled(mBinding.btnNext).call(aBoolean);
            });
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
//        EventBus.getDefault().post(new HideKeyboardOnTouchBlankEvent(false));
    }
}
