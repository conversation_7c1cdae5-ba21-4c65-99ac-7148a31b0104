package com.dinsafer.module.powerstation.electricity;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentElectricityPriceBinding;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.marker.PriceMarkerView;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PriceChartModelController;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.module.powerstation.event.StatInfoEvent;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.ScreenUtils;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class ElectricityPriceFragment extends BaseChartFragment<PriceChartModelController,
        FragmentElectricityPriceBinding> {

    private String mUnit;
    private String mPriceUnit = "";
    private String mShowPriceUnit = "";
    private String mNullVal = "--";
    private CustomCombinedMarkerView mMarkerView;
    private int mHighLightIndex = 12;
    private StringBuilder mUnitTimeBuilder;
    private Map<String, Object> mMonetaryUnitMap;

    private XAxisValueFormatter mPriceFormatter = (original, index, viewPortHandler) -> {

        if (mHourCount == DST_TIME_MINUTE) {
            if (index == 0) {
                return "00:00";
            } else if (index == 24) {
                return "23:00";
            }
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            if (index == 0) {
                return "00:00";
            } else if (index == 22) {
                return "23:00";
            }
        } else {
            String hour = index < 10 ? ("0" + index) + ":00" : index + ":00";
            if (index % 23 == 0 || index == 12) {
                return hour;
            }
        }
        return "";
    };

    private YAxisValueFormatter yRightFormatter = (value, yAxis) -> {
        float valueAsb = Math.abs(value);
        if (valueAsb == yAxis.getAxisMaximum()) {
            return (int)value + "%";
        }
        if (value == 0) {
            return "0%";
        }
        return "";
    };

    private boolean isFirst = true;

    public static ElectricityPriceFragment newInstance(int fromIndex, String deviceId) {
        ElectricityPriceFragment fragment = new ElectricityPriceFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.INDEX, fromIndex);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_electricity_price;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        String json = loadJSONFromAsset(getDelegateActivity());
        mMonetaryUnitMap = new Gson().fromJson(json, new TypeToken<Map<String, Object>>() {}.getType());
        mType = BaseChartFragment.CHART_ELECTRICITY_PRICE;
        mUnitTimeBuilder = new StringBuilder();
        mMarkerView = new PriceMarkerView(getContext());
        mMarkerView.setHighLightListener(new CustomCombinedMarkerView.HighLightListener() {
            @Override
            public void highLight(List<Entry> entries, List<Highlight> highlights) {
                if (CollectionUtil.isListNotEmpty(entries)) {
                    int xIndex = entries.get(0).getXIndex();
                    mHighLightIndex = xIndex;
                    String unit = "(" + mPriceUnit + ") ";
                    int h = xIndex;

                    if (mHourCount == DST_TIME_MINUTE) {
                        if (h > 3) {
                            h -= 1;
                        }
                    }

                    if (mHourCount == SUMMER_TIME_MINUTE) {
                        if (h > 1) {
                            h += 1;
                        }
                    }

                    String hour = h < 10 ? ("0" + h) + ":00" : h + ":00";
                    mUnitTimeBuilder.delete(0, mUnitTimeBuilder.length());
                    mBinding.tvTime.setText(mUnitTimeBuilder.append("(").append(Local.s(getString(R.string.per)))
                            .append(" kWh)  ").append(hour).toString());
                    boolean isMarketSelected = mFlipCombinedChartView.isFilterSelected(0);
                    boolean isTariffSelected = mFlipCombinedChartView.isFilterSelected(1);
                    if (isMarketSelected || isTariffSelected) {
                        BarEntry barEntry = (BarEntry) entries.get(entries.size() - 1);
                        float[] values = barEntry.getVals();
                        float value1 = values[0];
                        float value2 = values[1];
                        String val1Str = "";
                        String val2Str = "";
                        String totalUnit = "";
                        String val1Unit = getUnit(value1);
                        String val2Unit = getUnit(value2);
                        if (value1 < 1) {
                            value1 = getBasicUnitVal(value1);
                        }
                        if (value2 < 1) {
                            value2 = getBasicUnitVal(value2);
                        }
                        if (isMarketSelected && isTariffSelected) {
                            val1Str = ChartDataUtil.savePointStr(value1, 2, 2);
                            val2Str = ChartDataUtil.savePointStr(value2, 2, 2);
                            double val1SavePoint = ChartDataUtil.str2DoubleByDecimalFormat(val1Str, 2);
                            double val2SavePoint = ChartDataUtil.str2DoubleByDecimalFormat(val2Str, 2);
                            if (values[0] >= 1) {
                                val1SavePoint = getBasicUnitVal(val1SavePoint);
                            }
                            if (values[1] >= 1) {
                                val2SavePoint = getBasicUnitVal(val2SavePoint);
                            }
                            totalUnit = getBasicUnit();
                            double totalVal = val1SavePoint + val2SavePoint;
                            double relationShip = getRelationShip();
                            if (totalVal >= relationShip) {
                                totalVal /= relationShip;
                                totalUnit = getCurrencySymbols();
                            }
                            mBinding.esvVal.setLeftRTL(isAheadUnit(totalUnit));
                            mBinding.esvVal.setLeftVal(ChartDataUtil.savePointStr(totalVal, 2, 2), totalUnit);
                            mBinding.esvVal.setMiddleRTL(isAheadUnit(val1Unit));
                            mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.market_price)), val1Str, val1Unit);
                            mBinding.esvVal.setRightRTL(isAheadUnit(val2Unit));
                            mBinding.esvVal.setRightVal(Local.s(getString(R.string.tariff)), val2Str, val2Unit);
                        } else if (isMarketSelected) {
                            val1Str = ChartDataUtil.savePointStr(value1, 2, 2);
                            mBinding.esvVal.setLeftRTL(isAheadUnit(val1Unit));
                            mBinding.esvVal.setMiddleRTL(isAheadUnit(val1Unit));
                            mBinding.esvVal.setRightRTL(isAheadUnit(val1Unit));
                            mBinding.esvVal.setLeftVal(val1Str, val1Unit);
                            mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.market_price)), val1Str, val1Unit);
                            mBinding.esvVal.setRightVal(Local.s(getString(R.string.tariff)), mNullVal, val1Unit);
                        } else if (isTariffSelected) {
                            val2Str = ChartDataUtil.savePointStr(value2, 2, 2);
                            mBinding.esvVal.setLeftRTL(isAheadUnit(val2Unit));
                            mBinding.esvVal.setMiddleRTL(isAheadUnit(val2Unit));
                            mBinding.esvVal.setRightRTL(isAheadUnit(val2Unit));
                            mBinding.esvVal.setLeftVal(val2Str, val2Unit);
                            mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.market_price)), mNullVal, val2Unit);
                            mBinding.esvVal.setRightVal(Local.s(getString(R.string.tariff)), val2Str, val2Unit);
                        }
                    } else {
                        setDefaultVal();
                    }
                } else {
                    setDefaultVal();
                }
                mBinding.esvVal.refreshText();
            }
        });
    }

    @Override
    protected void initChartView() {
        super.initChartView();
        initChart(mFlipCombinedChartView, mMarkerView);
        setIvRightEnabled(mOffSet != 0);
        initElectricityType();
        initRefreshLayout(mBinding.refreshLayout);
        mFlipCombinedChartView.setFlipChangeListener(new FlipCombinedChartView.OnFlipChangeListener() {
            @Override
            public void flipChange(int index, OperateOrientation orientation) {
                mIndex = index;
                if (orientation == OperateOrientation.LEFT) {
                    mOffSet = mOffSet - 1;
                } else if (orientation == OperateOrientation.RIGHT) {
                    mOffSet = mOffSet + 1;
                }
                mHighLightIndex = 12;
                setIvRightEnabled(mOffSet != 0);
                getStatisticData(true);
            }
        });
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            if (mCycleType != CycleType.LIFETIME) {
                if (isChartViewNotNull()) {
                    setIvRightEnabled(mOffSet != 0);
                }
            }
            if (mFromIndex == BaseChartFragment.CHART_ELECTRICITY_PRICE) {
                getStatisticData(!isFirst);
                isFirst = false;
            } else {
                getStatisticData(true);
            }
        }
    }

    private void setDefaultVal() {
        mBinding.esvVal.setLeftRTL(isAheadUnit(mShowPriceUnit));
        mBinding.esvVal.setMiddleRTL(isAheadUnit(mShowPriceUnit));
        mBinding.esvVal.setRightRTL(isAheadUnit(mShowPriceUnit));
        mBinding.esvVal.setLeftVal(mNullVal, mShowPriceUnit);
        mBinding.esvVal.setMiddleVal(Local.s(getString(R.string.market_price)), mNullVal, mShowPriceUnit);
        mBinding.esvVal.setRightVal(Local.s(getString(R.string.tariff)), mNullVal, mShowPriceUnit);
    }

    private void initElectricityType() {
        if(isChartViewNotNull()) {
            mData = new ArrayList<>();
            mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_market_price_sel, R.drawable.shape_electricity_market_price_normal, getString(R.string.market_price), true));
            mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_price_tariff_sel, R.drawable.shape_electricity_price_tariff_normal, getString(R.string.tariff), true));
            mData.add(new PSElectricityTypeBean(R.drawable.shape_electricity_relative_price_sel, R.drawable.shape_electricity_relative_price_normal, getString(R.string.relative_price), true));
            mFlipCombinedChartView.setElectricityTypeData(mData);
            mFlipCombinedChartView.setReverse(false);
            mFlipCombinedChartView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mFlipCombinedChartView.setRvFilterVisible(true);
                }
            }, 1000);
        }
    }

    @Override
    public void createChartModelController() {
        chartModelController = new PriceChartModelController();
    }

    @Override
    protected void getStatisticData(boolean showLoading) {
        if (ElectricityStatisticsFragment.mPSDevice != null) {
            if (showLoading) {
                showTimeOutLoadinFramgmentWithMarginTop(marginTop);
            }
            Map<String, Object> result = mDayCache.get(mOffSet);
            if (result == null) {
                Map<String, Object> params = new HashMap<>();
                params.put(PSKeyConstant.CMD, BmtCmd.GET_ELEC_PRICE_INFO);
                params.put(BmtDataKey.OFFSET, mOffSet);
                ElectricityStatisticsFragment.isPriceReq = true;
                ElectricityStatisticsFragment.mPSDevice.submit(params);
            } else {
                EventBus.getDefault().post(new StatInfoEvent(result));
                setChartData(BaseChartFragment.CHART_ELECTRICITY_PRICE, result);
                closeLoadingFragment();
            }
        }
    }

    @Override
    protected void setChartData(int index, Map<String, Object> result) {
        checkChartViewNull();
        if (refreshLayout != null)
            refreshLayout.finishRefresh();
        isSuccess = true;
        timezone = (String) MapUtils.get(result, BmtDataKey.TIMEZONE, "");
//        mPriceUnit = DeviceHelper.getString(result, BmtDataKey.UNIT, "");
        mPriceUnit = Local.s(getString(R.string.per)) + " kWh";
        mUnit = DeviceHelper.getString(result, BmtDataKey.UNIT, "").split("/")[0];
        mShowPriceUnit = DeviceHelper.getString(result, BmtDataKey.UNIT_APP, "");
        long startTime = (long) MapUtils.get(result, BmtDataKey.START_TIME, 0);
        Boolean bSensorInstalled = (Boolean) MapUtils.get(result, BmtDataKey.B_SENSOR_INSTALLED, null);
        if (bSensorInstalled != null) {
            isBSensorInstalled = bSensorInstalled;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(startTime * 1000);
        if (DDDateUtil.isDSTTransitionDay(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1500;
        } else if (DDDateUtil.isWT2ST(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1380;
        } else {
            mHourCount = 1440;
        }
        mMarkerView.setHourCount(mHourCount);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        mStartIndex = hour * 60 + minute;

        mResult = result;
        mInterval = result.get(BmtDataKey.INTERVAL) == null ? 1 : (int) result.get(BmtDataKey.INTERVAL);

        if (mResult != null) {
            mStartTime = DeviceHelper.getLong(mResult, BmtDataKey.START_TIME, 0) * 1000;
            timezone = DeviceHelper.getString(mResult, BmtDataKey.TIMEZONE, "");
        }

        mChartData.clear();
        mChartData.addAll((List<List<Float>>) result.get(BmtDataKey.DATA));
        float maxVal = getPriceYMax();
        String unit = getUnit(maxVal);
        float transMaxVal = maxVal;
        if (maxVal < 1.0f) {
            transMaxVal = getBasicUnitVal(transMaxVal);
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(Local.s(getString(R.string.price)));
        stringBuilder.append("\n");
        if (isAheadUnit(unit)) {
            stringBuilder.append(unit);
            stringBuilder.append(ChartDataUtil.savePoint(transMaxVal, 1));
        } else {
            stringBuilder.append(ChartDataUtil.savePoint(transMaxVal, 1));
            stringBuilder.append(unit);
        }
        stringBuilder.append("/kWh");
        mLeftAboveText.setNoteText(stringBuilder.toString());
        chartModelController.initXAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, 0, mPriceFormatter);
        chartModelController.initYAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, yFormatter, true);

        resetYAxisLeft(maxVal, index);
        int maxRightVal = getRelativePriceMax();
        resetYAxisRight(index, maxRightVal);
        mFlipCombinedChartView.resetHighValues();
        resetChart();
    }

    @Override
    protected void setFailChartData(int index) {
        checkChartViewNull();
        if (refreshLayout != null)
            refreshLayout.finishRefresh();
        timezone = "";
        mPriceUnit = "("+ Local.s(getString(R.string.per)) + " kWh)";
        mShowPriceUnit = "Kr";
        mHourCount = 1440;
        mInterval = 1;

        mChartData.clear();
        List<List<Float>> data = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            List<Float> sonData = new ArrayList<>();
            sonData.add(0f);
            sonData.add(0f);
            sonData.add(0f);
            data.add(sonData);
        }
        mChartData.addAll(data);
        float maxVal = 2.0f;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(Local.s(getString(R.string.price)));
        mLeftAboveText.setNoteText(stringBuilder.toString());
        chartModelController.initXAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, 0, mPriceFormatter);
        chartModelController.initYAxis(getContext(), chartManagers.get(mIndex), mCycleType,
                mPlusMinusType, yFormatter, true);

        resetYAxisLeft(maxVal, index);
        resetYAxisRight(index, 100);
        mFlipCombinedChartView.resetHighValues();
        resetFailChart();
    }

    private float getPriceYMax() {
        if (CollectionUtil.isListEmpty(mChartData)) return 2.0f;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < mChartData.size(); i++) {
            List<Float> sonData = mChartData.get(i);
            float sum = 0;
            if (sonData.size() > 0) {
                sum = sum + sonData.get(0);
            }
            if (sonData.size() > 2) {
                sum = sum + sonData.get(2);
            }
            sumData.add(Math.abs(sum));
        }
        Collections.sort(sumData);
        float maxVal = sumData.get(sumData.size() - 1);
//        int maxIntVal = (int) Math.ceil(maxVal);
        return ChartDataUtil.findUpperPriceLimit(maxVal);
    }

    private int getRelativePriceMax() {
        if (CollectionUtil.isListEmpty(mChartData)) return 100;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < mChartData.size(); i++) {
            List<Float> sonData = mChartData.get(i);
            sumData.add(Math.abs(ChartDataUtil.saveFloatPoint(sonData.get(1), 2)));
        }
        Collections.sort(sumData);
        float maxVal = sumData.get(sumData.size() - 1);
        return ChartDataUtil.findUpperRelativePrice(maxVal);
    }

    @Override
    protected void resetYAxisLeft(float maxVal, int index) {
        if (CollectionUtil.isListNotEmpty(chartManagers)) {
            for (CustomCombinedChartManager chartManager : chartManagers) {
                chartManager.setYAxisMaxMin(maxVal, 0, 5);
                chartManager.setShowAboveChartText(true);
            }
        }
    }

    @Override
    protected void resetYAxisRight(int index, float maxRightVal) {
        if (CollectionUtil.isListNotEmpty(chartManagers)) {
            for (CustomCombinedChartManager chartManager : chartManagers) {
                chartManager.initYAxisRight(true, 10f, getColor(R.color.color_white_03),
                        0.5f, false, false, getColor(R.color.color_white_04),
                        yRightFormatter, 3);
                chartManager.setYAxisRightMaxMin(maxRightVal, -maxRightVal);
                chartManager.setDrawRightAxisYDesc(true);
                chartManager.setRightAxisYDesc(Local.s(getString(R.string.relative_price_multiLine)));
            }
        }
    }

    @Override
    protected void setDayChart() {
        int count = mChartData.size();
        ArrayList<Entry> entries = new ArrayList<Entry>();
        ArrayList<String> xVals = new ArrayList<String>();
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
            List<Float> sonData = mChartData.get(i);
            if (sonData.size() > 1) {
                entries.add(new Entry(sonData.get(1), i));
            }
            yVals.add(new BarEntry(new float[]{mFlipCombinedChartView.isFilterSelected(0) ? sonData.get(0) : 0,
                    mFlipCombinedChartView.isFilterSelected(1) ? sonData.get(2) : 0}, i));
        }
        float[] pos = new float[]{0, 0.5f, 0.5f, 1f};
        int[] colors = new int[]{getColor(R.color.chart_line_gradient_color_1), getColor(R.color.chart_line_gradient_color_1),
                getColor(R.color.chart_line_gradient_color_3), getColor(R.color.chart_line_gradient_color_3)};
        SectionLineDataSet dayLineData = getSectionLineDataSet(entries, 0, YAxis.AxisDependency.RIGHT,
                colors, pos, true, false);
        dayLineData.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
        CombinedData data = new CombinedData(xVals);
        if (mFlipCombinedChartView.isFilterSelected(2)) {
            List<ILineDataSet> lineDataSets = new ArrayList<>();
            lineDataSets.add(dayLineData);
            LineData lineData = new LineData(xVals, lineDataSets);
            data.setData(lineData);
        }

        BarDataSet barDataSet = new BarDataSet(yVals, "");
        int colorsBar[] = new int[]{getColor(R.color.color_tip_07),
                getColor(R.color.color_tip_06)};
        barDataSet.setColors(colorsBar);
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(true);
        barDataSet.setHighLightColor(getColor(R.color.color_white_03));
        barDataSet.setBarSpacePercent((ScreenUtils.getScreenWidth(getContext()) - DensityUtil.dp2px(getContext(), 65)) * 1f / count * 1.2f);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
        if (mFlipCombinedChartView.isFilterSelected(0) ||
                mFlipCombinedChartView.isFilterSelected(1) ||
                mFlipCombinedChartView.isFilterSelected(2)) {
            mFlipCombinedChartView.setDefaultHighLight(mIndex, mHighLightIndex);
        } else {
            String unit = "(" + mPriceUnit + ")";
            mBinding.tvTime.setText(unit);
            setDefaultVal();
            mBinding.esvVal.refreshText();
        }
    }


    @Override
    protected void setWeekChart() {

    }

    @Override
    protected void setMonthChart() {

    }

    @Override
    protected void setYearChart() {

    }

    @Override
    protected void setLifetimeChart() {

    }

    @Override
    protected void setFailDayChart() {
        int count = mChartData.size();
        ArrayList<Entry> entries = new ArrayList<Entry>();
        ArrayList<String> xVals = new ArrayList<String>();
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
            List<Float> sonData = mChartData.get(i);
            if (sonData.size() > 1) {
                entries.add(new Entry(sonData.get(1), i));
            }
            yVals.add(new BarEntry(new float[]{0, 0}, i));
        }
        float[] pos = new float[]{0, 0.5f, 0.5f, 1f};
        int[] colors = new int[]{getColor(R.color.transparent), getColor(R.color.transparent),
                getColor(R.color.transparent), getColor(R.color.transparent)};
        SectionLineDataSet dayLineData = getSectionLineDataSet(entries, 0, YAxis.AxisDependency.RIGHT,
                colors, pos, true, false);
        dayLineData.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
        CombinedData data = new CombinedData(xVals);
        if (mFlipCombinedChartView.isFilterSelected(2)) {
            List<ILineDataSet> lineDataSets = new ArrayList<>();
            lineDataSets.add(dayLineData);
            LineData lineData = new LineData(xVals, lineDataSets);
            data.setData(lineData);
        }

        BarDataSet barDataSet = new BarDataSet(yVals, "");
        int colorsBar[] = new int[]{getColor(R.color.transparent),
                getColor(R.color.transparent)};
        barDataSet.setColors(colorsBar);
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(true);
        barDataSet.setHighLightColor(getColor(R.color.color_white_03));
        barDataSet.setBarSpacePercent((ScreenUtils.getScreenWidth(getContext()) - DensityUtil.dp2px(getContext(), 65)) * 1f / count * 1.2f);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
        mBinding.tvTime.setText("");
        mShowPriceUnit = "";
        setDefaultVal();
        mBinding.esvVal.refreshText();
    }

    @Override
    protected void setFailWeekChart() {

    }

    @Override
    protected void setFailMonthChart() {

    }

    @Override
    protected void setFailYearChart() {

    }

    @Override
    protected void setFailLifetimeChart() {

    }

    public String loadJSONFromAsset(Context context) {
        String json = null;
        try {
            InputStream is = context.getAssets().open("monetary_unit.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            json = new String(buffer, "UTF-8");
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
        return json;
    }

    private String getUnit(double value) {
        String result = mShowPriceUnit;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, mUnit);
            if (map != null) {
                result = DeviceHelper.getString(map, value >= 1.0 ? "currency_symbols" : "basic_unit", "");
            }
        }
        return result;
    }

    private String getBasicUnit() {
        String result = mShowPriceUnit;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, mUnit);
            if (map != null) {
                result = DeviceHelper.getString(map, "basic_unit", "");
            }
        }
        return result;
    }

    private String getCurrencySymbols() {
        String result = mShowPriceUnit;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, mUnit);
            if (map != null) {
                result = DeviceHelper.getString(map, "currency_symbols", "");
            }
        }
        return result;
    }

    private double getBasicUnitVal(double value) {
        double val = value;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, mUnit);
            if (map != null) {
                val = value * DeviceHelper.getDouble(map, "relationship", 1);
            }
        }
        return val;
    }

    private float getBasicUnitVal(float value) {
        float val = value;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, mUnit);
            if (map != null) {
                val = value * (int)DeviceHelper.getDouble(map, "relationship", 1);
            }
        }
        return val;
    }

    private double getRelationShip() {
        double val = 1;
        if (mMonetaryUnitMap != null) {
            Map<String, Object> map = DeviceHelper.getMap(mMonetaryUnitMap, mUnit);
            if (map != null) {
                val =  DeviceHelper.getDouble(map, "relationship", 1);
            }
        }
        return val;
    }

    private boolean isAheadUnit(String unit) {
        if (mMonetaryUnitMap != null) {
            String aheadUnits = DeviceHelper.getString(mMonetaryUnitMap, "ahead_unit", "");
            return aheadUnits.contains(unit);
        }
        return false;
    }
}
