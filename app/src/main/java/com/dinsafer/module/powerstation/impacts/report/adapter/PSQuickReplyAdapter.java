package com.dinsafer.module.powerstation.impacts.report.adapter;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalTextView;


public class PSQuickReplyAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    public PSQuickReplyAdapter() {
        super(R.layout.item_ps_report_quick_reply);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, String item) {
        LocalTextView textView = helper.getView(R.id.tv_content);
        textView.setLocalText(item);
    }
}
