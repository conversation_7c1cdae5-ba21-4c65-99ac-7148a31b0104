package com.dinsafer.module.powerstation.widget.currentdiagramview;

public class LoadSaturationBean {

    private boolean isThreePhase;
    private int batteryWat;
    private int solarWat;
    private int gridWat;
    private int additionWat;
    private int otherWat;
    private int vehicleWat;
    private int ip2Wat;
    private int op2Wat;
    private int evStatus;
    private int emergencyReserve;
    private int smartReserve;
    private int batteryPercentage;
    private int mode;
    private boolean showUpdate;

    public LoadSaturationBean() {
    }

    public LoadSaturationBean(boolean isThreePhase, int batteryWat, int solarWat, int gridWat, int additionWat, int otherWat, int vehicleWat, int ip2Wat, int op2Wat, int evStatus, int emergencyReserve, int smartReserve, int batteryPercentage, int mode, boolean showUpdate) {
        this.isThreePhase = isThreePhase;
        this.batteryWat = batteryWat;
        this.solarWat = solarWat;
        this.gridWat = gridWat;
        this.additionWat = additionWat;
        this.otherWat = otherWat;
        this.vehicleWat = vehicleWat;
        this.ip2Wat = ip2Wat;
        this.op2Wat = op2Wat;
        this.evStatus = evStatus;
        this.emergencyReserve = emergencyReserve;
        this.smartReserve = smartReserve;
        this.batteryPercentage = batteryPercentage;
        this.mode = mode;
        this.showUpdate = showUpdate;
    }

    public int getBatteryWat() {
        return batteryWat;
    }

    public void setBatteryWat(int batteryWat) {
        this.batteryWat = batteryWat;
    }

    public int getSolarWat() {
        return solarWat;
    }

    public void setSolarWat(int solarWat) {
        this.solarWat = solarWat;
    }

    public int getGridWat() {
        return gridWat;
    }

    public void setGridWat(int gridWat) {
        this.gridWat = gridWat;
    }

    public int getAdditionWat() {
        return additionWat;
    }

    public void setAdditionWat(int additionWat) {
        this.additionWat = additionWat;
    }

    public int getOtherWat() {
        return otherWat;
    }

    public void setOtherWat(int otherWat) {
        this.otherWat = otherWat;
    }

    public int getVehicleWat() {
        return vehicleWat;
    }

    public void setVehicleWat(int vehicleWat) {
        this.vehicleWat = vehicleWat;
    }

    public int getIp2Wat() {
        return ip2Wat;
    }

    public void setIp2Wat(int ip2Wat) {
        this.ip2Wat = ip2Wat;
    }

    public int getOp2Wat() {
        return op2Wat;
    }

    public void setOp2Wat(int op2Wat) {
        this.op2Wat = op2Wat;
    }

    public int getEvStatus() {
        return evStatus;
    }

    public void setEvStatus(int evStatus) {
        this.evStatus = evStatus;
    }

    public int getEmergencyReserve() {
        return emergencyReserve;
    }

    public void setEmergencyReserve(int emergencyReserve) {
        this.emergencyReserve = emergencyReserve;
    }

    public int getSmartReserve() {
        return smartReserve;
    }

    public void setSmartReserve(int smartReserve) {
        this.smartReserve = smartReserve;
    }

    public int getBatteryPercentage() {
        return batteryPercentage;
    }

    public void setBatteryPercentage(int batteryPercentage) {
        this.batteryPercentage = batteryPercentage;
    }

    public boolean isThreePhase() {
        return isThreePhase;
    }

    public void setThreePhase(boolean threePhase) {
        isThreePhase = threePhase;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public boolean isShowUpdate() {
        return showUpdate;
    }

    public void setShowUpdate(boolean showUpdate) {
        this.showUpdate = showUpdate;
    }
}
