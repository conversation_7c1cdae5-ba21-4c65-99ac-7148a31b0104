package com.dinsafer.module.powerstation.adapter;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsEvChargeV2Binding;
import com.dinsafer.module.powerstation.bean.PSEVChargeV2Bean;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.Local;

public class PSEVChargeV2Model extends BindModel<ItemPsEvChargeV2Binding> {

    private Context mContext;
    private PSEVChargeV2Bean evChargeBean;

    public PSEVChargeV2Model(Context context, PSEVChargeV2Bean evChargeBean) {
        super(context);
        mContext = context;
        this.evChargeBean = evChargeBean;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_ev_charge_v2;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsEvChargeV2Binding binding) {
        if (evChargeBean != null) {
            int status = evChargeBean.getStatus();
            int type = evChargeBean.getType();
            boolean isSelected = evChargeBean.isSelected();
            binding.tvTitle.setLocalText(evChargeBean.getTitle());
            binding.tvSubtitle.setLocalText(evChargeBean.getSubTitle());
            binding.cvParent.setBackgroundResource(isSelected
                    ? R.drawable.shape_bg_item_ev_charge_sel : R.drawable.shape_bg_item_ev_charge_nor);
            String operateText = "";
            if (type == PSEVChargeV2Bean.SCHEDULE_CHARGE) {
                operateText = mContext.getString(R.string.go_setting);
            } else if (type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) {
                String oldStr = mContext.getString(R.string.hashtag_charge_amount);
                String subtitle = Local.s(evChargeBean.getSubTitle()).replace(oldStr, evChargeBean.getTempValue() + "kWh");
                binding.tvSubtitle.setLocalText(subtitle);
                operateText = mContext.getString(R.string.edit);
            } else if (type == PSEVChargeV2Bean.LOWER_UTILITY_RATE) {
                String oldStr = mContext.getString(R.string.hashtag_c2);
                int value = evChargeBean.getValue();
                String valStr = value > 0 ? ("+"+value+"%") : (value+"%");
                String subtitle = Local.s(evChargeBean.getSubTitle()).replace(oldStr,  valStr);
                binding.tvSubtitle.setText(subtitle);
            }
            binding.tvOperate.setVisibility(!TextUtils.isEmpty(operateText) ?
                    View.VISIBLE : View.GONE);
            binding.tvOperate.setLocalText(operateText);

            binding.llStatus.setBackgroundResource(status == 0 ? R.drawable.shape_brand_light_03_right_r16 :
                    R.drawable.shape_brand_primary_right_r16);
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) binding.clContent.getLayoutParams();
            if (evChargeBean.isAdd()) {
                evChargeBean.setAdd(false);
                layoutParams.setMargins(layoutParams.leftMargin,
                        layoutParams.topMargin,
                        0,
                        layoutParams.bottomMargin);
                binding.clContent.requestLayout();
            } else {
                if (!evChargeBean.isSameSelected()) {
                    boolean isEdit = isSelected && status != 1;
                    if (status == 1) {
                        binding.tvOperate.setVisibility(isSelected ? View.VISIBLE : View.GONE);
                    }

                    int startMargin = isEdit ? 5 : 0;
                    int endMargin = isEdit ? 0 : 5;
                    ValueAnimator marginAnimator = ValueAnimator.ofInt(startMargin, endMargin);
                    marginAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                        @Override
                        public void onAnimationUpdate(ValueAnimator animation) {
                            int val = (Integer) animation.getAnimatedValue();
                            int marginRight = status == 1 ? 0 : DensityUtil.dp2px(mContext, 60) - DensityUtil.dp2px(mContext, val * 12);
                            layoutParams.setMargins(layoutParams.leftMargin,
                                    layoutParams.topMargin,
                                    marginRight,
                                    layoutParams.bottomMargin);
                            binding.clContent.requestLayout();
                        }
                    });
                    marginAnimator.addListener(new Animator.AnimatorListener() {
                        @Override
                        public void onAnimationStart(Animator animator) {
                            if (isEdit) {
                                binding.tvOperate.setVisibility(View.VISIBLE);
                            }
                        }

                        @Override
                        public void onAnimationEnd(Animator animator) {
                            if (!isEdit && status == -1) {
                                binding.tvOperate.setVisibility(View.GONE);
                            }
                        }

                        @Override
                        public void onAnimationCancel(Animator animator) {

                        }

                        @Override
                        public void onAnimationRepeat(Animator animator) {

                        }
                    });
                    evChargeBean.setSameSelected(true);
                    marginAnimator.setDuration(200);
                    marginAnimator.start();
                }
            }
            binding.tvOperate.setVisibility((type == PSEVChargeV2Bean.SCHEDULE_CHARGE ||
                    type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) && isSelected ?
                    View.VISIBLE : View.GONE);
            binding.tvApplied.setVisibility(status == 1 ? View.VISIBLE : View.GONE);
            binding.ivStatus.setVisibility(status == 0 ? View.GONE : View.VISIBLE);
            binding.lavLoading.setVisibility(status == 0 ? View.VISIBLE : View.GONE);
        }
    }

    public PSEVChargeV2Bean getEvChargeBean() {
        return evChargeBean;
    }

    public void setEvChargeBean(PSEVChargeV2Bean evChargeBean) {
        this.evChargeBean = evChargeBean;
    }
}
