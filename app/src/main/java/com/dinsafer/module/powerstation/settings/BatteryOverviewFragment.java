package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.dinsafer.common.BmtManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBatteryOverviewBinding;
import com.dinsafer.dinnet.databinding.ItemBatteryOverviewCabinetBinding;
import com.dinsafer.dinnet.databinding.LayoutCabinetBatteryBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.bean.BatteryOverviewCacheBean;
import com.dinsafer.module.powerstation.bean.CabinetBatteryBean;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.widget.ultraviewpager.UltraViewPager;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.UnitUtil;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/28 22:27
 * @description :
 */
public class BatteryOverviewFragment extends MyBaseFragment<FragmentBatteryOverviewBinding> implements IDeviceCallBack {

    private final int CHECK_BATTERY = 0x01;
    private final long CHECK_BATTERY_TIME = 2000;
    private ArrayList<View> mViewList = new ArrayList<View>(); // 将要显示的布局存放到list数组
    private ImageView[] dotViews;
    private int mCurrentIndex;
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private Map<String, Object> params = new HashMap<>();
    private Map<String, CabinetBatteryBean> mBatteryMap = new HashMap<>();
    private Map<String, LayoutCabinetBatteryBinding> mCellBindingMap = new HashMap<>();
    private List<LinearLayout> mLLBatteryLayouts = new ArrayList<>(); // 电池包
    private boolean isEmpty = true;
    private int mCabinetCount;
    private int mBatteryCount;
    private int mTotalCapacity;
    private int mBatteryResCount;
    private int mBatteryCacheCount;
    private boolean isAllLoad;
    private List<BatteryOverviewCacheBean.BatteryCellCacheBean> mBatteryCellsCache;
    private Set<Integer> mIndexSet = new HashSet<>();
    private int mRetryCount = 0;

    public static BatteryOverviewFragment newInstance(String deviceId, String subcategory) {
        BatteryOverviewFragment fragment = new BatteryOverviewFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == CHECK_BATTERY) {
                if (mRetryCount < 2) {  // 重试次数小于2
                    mRetryCount += 1;
                    for (int i = 0; i < mBatteryCount; i++) {
                        if (!mIndexSet.contains(i)) {
                            submitCmdWithIndex(DsCamCmd.GET_BATTERY_INFO, i);
                        }
                    }
                } else {  // 如果还要电池没拿到数据, 当所有电池包拿到数据
                    if (mIndexSet.size() != mBatteryCount)
                        totalCapacity();
                }
            }
        }
    };

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_battery_overview;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        BmtManager.getInstance().stopPolling();
        EventBus.getDefault().register(this);
        initViewPager();
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_battery_overview));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
    }

    @Override
    public void onDestroyView() {
        mHandler.removeMessages(CHECK_BATTERY);
        BmtManager.getInstance().startPolling();
        EventBus.getDefault().unregister(this);
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BmtDeviceStatusChange event) {
        final String targetId = event.getDeviceID();
        if (TextUtils.isEmpty(targetId) || !targetId.equals(mDeviceId) || !event.getSubcategory().equals(mSubcategory)) {
            return;
        }

        final boolean connected = BmtUtil.isDeviceConnected(mPSDevice);
        if (!connected) {
            if (getDelegateActivity().isCommonFragmentExist(PowerSettingsFragment.class.getName())) {
                getDelegateActivity().removeToFragment(PowerSettingsFragment.class.getName());
            }
        }
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            loadCache();
            mPSDevice.registerDeviceCallBack(this);
            showLoadingFragment(0);
            submitCmd(DsCamCmd.GET_CABINET_ALL_INFO);
        } else {
            showErrorToast();
        }
    }

    private int allB = 0;

    private void submitCmd(String cmd) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            mPSDevice.submit(params);
        }
    }

    private void submitCmdWithIndex(String cmd, int index) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            params.put(PSKeyConstant.INDEX, index);
            mPSDevice.submit(params);
        }
    }

    private void loadCache() {
        String json = DBUtil.Str(getCacheKey());
        if (!TextUtils.isEmpty(json)) {
            Gson gson = new Gson();
            BatteryOverviewCacheBean batteryOverviewCacheBean = gson.fromJson(json, BatteryOverviewCacheBean.class);
            int capacity = batteryOverviewCacheBean.getCapacity();
            double capacityDou = capacity / 1000.0;
            double finalCapacity = UnitUtil.savePoint(capacityDou, 1, BigDecimal.ROUND_HALF_UP);
            mBinding.tvTotalCapacityValue.setLocalText(finalCapacity + getString(R.string.power_station_kWh));
            mBatteryCacheCount = batteryOverviewCacheBean.getCabinetCount();
            mBatteryCellsCache = batteryOverviewCacheBean.getBatteryCells();
            addCabinetView(mBatteryCacheCount, true);
        }
    }

    /**
     * cabinet
     */
    private void addCabinetView(int count, boolean isCache) {
        int viewSize = mViewList.size();
        for (int i = viewSize; i < viewSize + count; i++) {
            View view = View.inflate(getContext(), R.layout.item_battery_overview_cabinet, null);
            ItemBatteryOverviewCabinetBinding binding = DataBindingUtil.bind(view);
            LinearLayout.LayoutParams llParam = new LinearLayout.LayoutParams(DensityUtil.dp2px(getContext(), 266), DensityUtil.dp2px(getContext(), 468));
            view.setPadding(DensityUtil.dp2px(getContext(), 15), 0, DensityUtil.dp2px(getContext(), 15), 0);
            llParam.gravity = Gravity.CENTER_HORIZONTAL;
            view.setLayoutParams(llParam);
            List<CabinetBatteryBean> data = new ArrayList<>();
            binding.llParent.setBackgroundResource(i == 0 ? R.drawable.img_power_main_cabinet_background : R.drawable.img_power_cabinet_background);
            mLLBatteryLayouts.add(binding.llBattery);
            int size = i == 0 ? 3 : 5;
            for (int j = 0; j < size; j++) {
                CabinetBatteryBean cabinetBatteryBean = new CabinetBatteryBean(true, (size - 1 - j), i + "-" + j);
                cabinetBatteryBean.setCurrentIndex(i == 0);
                cabinetBatteryBean.setCabinetIndex(i);
                if (isCache && CollectionUtil.isListNotEmpty(mBatteryCellsCache)) {
                    for (BatteryOverviewCacheBean.BatteryCellCacheBean batteryCell : mBatteryCellsCache) {
                        String cacheKey = batteryCell.getCabinetIndex() + "-" + batteryCell.getCabinetPositionIndex();
                        if (cacheKey.equals(i + "-" + (size - 1 - j))) {
                            cabinetBatteryBean.setCache(true);
                            break;
                        }
                    }
                }
                mBatteryMap.put(i + "-" + (size - 1 - j), cabinetBatteryBean);
                data.add(cabinetBatteryBean);
            }
            mViewList.add(view);
            createCellView(binding.llBattery, data);
        }
        mBinding.uvpCabinet.refresh();
        if (count > 0) {
            mBinding.elsParent.setVisibility(View.VISIBLE);
            if (mCurrentIndex > mViewList.size() - 1) {
                mCurrentIndex = mViewList.size() - 1;
            }
            initIndicator();
            mBinding.uvpCabinet.setCurrentItem(mCurrentIndex);
        }
    }

    /**
     * 移除多的机柜
     */
    private void removeCabinetView(int count) {
        int endIndex = mViewList.size() - 1;
        for (int i = endIndex; i  > count - 1; i--) {
            mLLBatteryLayouts.remove(i);
            int size = i == 0 ? 3 : 5;
            for (int j = 0; j < size; j++) {
                mBatteryMap.remove(i + "-" + (size - 1 - j));
                mCellBindingMap.remove(i + "-" + j);
            }
            mViewList.remove(i);
        }
        mBinding.uvpCabinet.refresh();
        initIndicator();
    }

    /**
     * cabinet下的电池
     *
     * @param llLayout
     * @param items
     */
    private void createCellView(LinearLayout llLayout, List<CabinetBatteryBean> items) {

        for (int i = 0; i < items.size(); i++) {
            CabinetBatteryBean item = items.get(i);
            LinearLayout.LayoutParams llParam = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DensityUtil.dp2px(getContext(), 70));
            llParam.leftMargin = DensityUtil.dp2px(getContext(), 6);
            llParam.rightMargin = DensityUtil.dp2px(getContext(), 6);
            llParam.bottomMargin = DensityUtil.dp2px(getContext(), 6);
            View contentView = View.inflate(getContext(), R.layout.layout_cabinet_battery, null);
            LayoutCabinetBatteryBinding binding = DataBindingUtil.bind(contentView);
            if (item.isCache()) {
                binding.tvPercentValue.setLocalText("-");
//                binding.ivI.setVisibility(View.GONE);
//                binding.viewState.setVisibility(View.GONE);
//                binding.llInfo.setVisibility(View.GONE);
//                binding.clContent.setVisibility(View.VISIBLE);
                setEmptyView(binding, true, true, item.isUnknown());
            } else {
                int soc = item.getSoc();
                if (soc < 0 || soc > 100) {
                    soc = 0;
                }
                binding.tvPercentValue.setText(soc + "");
//                binding.ivI.setVisibility(View.VISIBLE);
//                binding.viewState.setVisibility(View.VISIBLE);
//                binding.llInfo.setVisibility(View.VISIBLE);
//                binding.clContent.setVisibility(item.isEmpty() ? View.GONE : View.VISIBLE);
                setEmptyView(binding, false, item.isEmpty(), item.isUnknown());
            }
            mCellBindingMap.put(item.getKey(), binding);
            contentView.setLayoutParams(llParam);
            binding.cl.setOnClickListener(v -> {
                if (!item.isEmpty()) {
                    binding.cl.startFlip();
                }
            });
            if (item.isCurrentIndex()) {
                binding.cl.setVisibility(!item.isEmpty() || item.isCache() ? View.VISIBLE : View.GONE);
                binding.viewEmpty.setVisibility(item.isEmpty() && !item.isCache() ? View.VISIBLE : View.GONE);
                binding.viewMask.setVisibility(View.GONE);
            } else {
                binding.cl.setVisibility(View.GONE);
                binding.viewEmpty.setVisibility(View.GONE);
                binding.viewMask.setVisibility(View.VISIBLE);
            }
//            binding.viewMask.setVisibility(item.isCurrentIndex() ? View.GONE : View.VISIBLE);
            llLayout.addView(contentView);
        }
        if (items.size() > 0) {
            mBinding.tvCabinetName.setText(getCabinetIndexActStr("01"));
        }
    }

    private String getCabinetIndexActStr(String replaceStr) {
        String cabinetIndexKey = getString(R.string.ps_battery_overview_cabinet);
        String cabinetIndexSuffix = getString(R.string.ps_hashtag_cabinet_index);
        return Local.s(cabinetIndexKey).replace(cabinetIndexSuffix, replaceStr);
    }

    /**
     * 更新cabinet下的电池
     *
     * @param item
     */
    private void updateCellView(CabinetBatteryBean item) {
        if (item == null) return;
        LayoutCabinetBatteryBinding binding = mCellBindingMap.get(item.getKey());
        if (binding == null) return;
        if (item.isCurrentIndex()) {
            binding.cl.setVisibility(!item.isEmpty() || item.isCache() ? View.VISIBLE : View.GONE);
            binding.viewEmpty.setVisibility(item.isEmpty() && !item.isCache() ? View.VISIBLE : View.GONE);
            binding.viewMask.setVisibility(View.GONE);
        } else {
            binding.cl.setVisibility(View.GONE);
            binding.viewEmpty.setVisibility(View.GONE);
            binding.viewMask.setVisibility(View.VISIBLE);
        }
//        binding.viewMask.setVisibility(item.isCurrentIndex() ? View.GONE : View.VISIBLE);
        binding.tvBatteryId.setText(item.getBarcode());
        if (item.isCache()) {
            binding.tvPercentValue.setLocalText("-");
//            binding.ivI.setVisibility(View.GONE);
//            binding.viewState.setVisibility(View.GONE);
//            binding.llInfo.setVisibility(View.GONE);
//            binding.clContent.setVisibility(View.VISIBLE);
            setEmptyView(binding, true, false, item.isUnknown());
        } else {
            if (item.isEmpty()) {
//                binding.clContent.setVisibility(View.GONE);
                setEmptyView(binding, false, true, item.isUnknown());
            } else {
                int index = item.getIndex();
                if (index < 0) return;
//                binding.ivI.setVisibility(View.VISIBLE);
//                binding.viewState.setVisibility(View.VISIBLE);
//                binding.llInfo.setVisibility(View.VISIBLE);
//                binding.clContent.setVisibility(View.VISIBLE);
                setEmptyView(binding, false, false, item.isUnknown());
                int soc = item.getSoc();
                if (soc < 0 || soc > 100) {
                    soc = 0;
                }
                binding.tvPercentValue.setLocalText(soc + "");
                Double temperature = item.getElectrodeBTemp();
                String unit = DBUtil.SGet(mDeviceId + DBKey.TEMPERATURE_UNIT);
                if (TextUtils.isEmpty(unit)) {
                    unit = getString(R.string.ps_advanced_settings_celsius_unit);
                }
                if (temperature != null) {
                    temperature = UnitUtil.getTemperature(getContext(), unit, temperature);
                }
                if (soc <= 15) {
                    binding.tvPercentValue.setTextColor(getResColor(R.color.color_tip_01));
                    binding.tvPercentUnit.setTextColor(getResColor(R.color.color_tip_01));
                } else {
                    binding.tvPercentValue.setTextColor(getResColor(R.color.color_white_02));
                    binding.tvPercentUnit.setTextColor(getResColor(R.color.color_white_02));
                }
                binding.tvTemperature.setLocalText(temperature == null ? "" : UnitUtil.covertTemperatureStr(temperature) + unit);
//            if (item.isHighTemp()) {
//                binding.ivTemperatureStatus.setImageResource(R.drawable.icon_accessories_temperture_high);
//            } else if (item.isLowTemp()) {
//                binding.ivTemperatureStatus.setImageResource(R.drawable.icon_accessories_temperture_low);
//            } else {
//                binding.ivTemperatureStatus.setImageResource(R.drawable.icon_accessories_temperture_nor);
//            }
                binding.ivTemperatureStatus.setImageResource(R.drawable.icon_accessories_temperture_nor);
                if (item.getHeatAvailable() != null) {
                    if (item.getHeatAvailable()) { //  bool - 加热膜是否在位
                        if (item.getHeating() != null && item.getHeating()) {
                            binding.ivHeatingStatus.setImageResource(R.drawable.icon_accessories_heating_on);
                            binding.tvHeating.setLocalText(getString(R.string.ps_battery_overview_heating_working));
                            binding.ivHeatingStatus.setVisibility(View.VISIBLE);
                            binding.tvHeating.setVisibility(View.VISIBLE);
                        } else {
                            binding.ivHeatingStatus.setVisibility(View.GONE);
                            binding.tvHeating.setVisibility(View.GONE);
                        }
                    } else {
                        binding.ivHeatingStatus.setImageResource(R.drawable.icon_accessories_heating_null);
                        binding.tvHeating.setLocalText(getString(R.string.ps_battery_overview_heating_absent));
                        binding.ivHeatingStatus.setVisibility(View.VISIBLE);
                        binding.tvHeating.setVisibility(View.VISIBLE);
                    }
                } else {
                    binding.ivHeatingStatus.setImageResource(R.drawable.icon_accessories_heating_null);
                    binding.tvHeating.setLocalText(getString(R.string.ps_battery_overview_unknown));
                    binding.ivHeatingStatus.setVisibility(View.VISIBLE);
                    binding.tvHeating.setVisibility(View.VISIBLE);
                }
//            List<Integer> exceptions = item.getExceptions();
//            binding.viewState.setBackgroundResource(CollectionUtil.isListEmpty(exceptions) ? R.drawable.shape_bg_cabinet_battery_status_normal
//                    : R.drawable.shape_bg_cabinet_battery_status_warning);
                binding.viewState.setBackgroundResource(R.drawable.shape_bg_cabinet_battery_status_normal);
            }
        }
    }

    private void setEmptyView(LayoutCabinetBatteryBinding binding, boolean isCache, boolean isEmpty, boolean isUnknown) {
        if (binding == null) return;
        binding.tvPercentValue.setVisibility(isCache || isEmpty ? View.GONE : View.VISIBLE);
        binding.tvPercentUnit.setVisibility(isCache || isEmpty ? View.GONE : View.VISIBLE);
        binding.ivI.setVisibility(isCache || isEmpty ? View.GONE : View.VISIBLE);
        binding.viewState.setVisibility(isCache || isEmpty ? View.GONE : View.VISIBLE);
        binding.llInfo.setVisibility(isCache || isEmpty ? View.GONE : View.VISIBLE);
        if (isCache) {
            binding.lvLoading.setVisibility(isUnknown ? View.GONE : View.VISIBLE);
            binding.tvStatus.setVisibility(isUnknown ? View.VISIBLE : View.GONE);
        } else {
            binding.lvLoading.setVisibility(View.GONE);
            binding.tvStatus.setVisibility(View.GONE);
        }

    }

    /**
     * viewpager
     */
    private void initViewPager() {
        UltraViewPager ultraViewPager = mBinding.uvpCabinet;
        ultraViewPager.getViewPager().removeAllViews();
        ultraViewPager.setScrollMode(UltraViewPager.ScrollMode.HORIZONTAL);
        UltraPagerAdapter pageAdapter = new UltraPagerAdapter(mViewList);
        ultraViewPager.setAdapter(pageAdapter);
        ultraViewPager.setMultiScreen(0.7f);
        ultraViewPager.setItemRatio(1.0f);
        ultraViewPager.setMaxHeight(DensityUtil.dp2px(getContext(), 468));
        ultraViewPager.setAutoMeasureHeight(false);
        ultraViewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int index) {
                mCurrentIndex = index;
                String indexStr = (mCurrentIndex + 1) < 10 ? "0" + (mCurrentIndex + 1) : "" + (mCurrentIndex + 1);
                mBinding.tvCabinetName.setText(getCabinetIndexActStr(indexStr));
                for (int i = 0; i < dotViews.length; i++) {
                    dotViews[i].setSelected(mCurrentIndex == i);
                    dotViews[i].setImageResource(mCurrentIndex == i
                            ? R.drawable.shape_bg_power_vp_sel : R.drawable.shape_bg_power_vp_nor);
                }
                for (int i = 0; i < mViewList.size(); i++) {
                    mViewList.get(i).setAlpha(i == index ? 1f : 0.5f);
                }
                for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
                    CabinetBatteryBean cabinetBatteryBean = entry.getValue();
                    cabinetBatteryBean.setCurrentIndex(index == cabinetBatteryBean.getCabinetIndex());
                    updateCellView(cabinetBatteryBean);
                }
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
    }

    /**
     * 初始化指示器
     */
    private void initIndicator() {
        mBinding.llIndicator.removeAllViews();
        // 有分页才显示指示器
        if (mViewList.size() < 2) {
            return;
        }
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        //设置小圆点左右之间的间隔
        params.setMargins(8, 0, 8, 0);
        dotViews = new ImageView[mViewList.size()];
        for (int i = 0; i < mViewList.size(); i++) {
            ImageView imageView = new ImageView(getContext());
            imageView.setLayoutParams(params);
            imageView.setImageResource(R.drawable.shape_bg_conbinet_vp_nor);
            //默认启动时，选中第一个小圆点
            imageView.setSelected(i == mCurrentIndex);
            dotViews[i] = imageView;
            if (mCurrentIndex == 0) {
                dotViews[mCurrentIndex].setImageResource(R.drawable.shape_bg_conbinet_vp_sel);
            }
            mBinding.llIndicator.addView(imageView);
        }
    }

    private void saveBatteryOverviewInfo() {
        BatteryOverviewCacheBean batteryOverviewCacheBean = new BatteryOverviewCacheBean(mCabinetCount, mTotalCapacity);
        List<BatteryOverviewCacheBean.BatteryCellCacheBean> batteryCells = new ArrayList<>();
        for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
            CabinetBatteryBean cabinetBatteryBean = entry.getValue();
            if (!cabinetBatteryBean.isEmpty()) {
                BatteryOverviewCacheBean.BatteryCellCacheBean batteryCell = new BatteryOverviewCacheBean.BatteryCellCacheBean(
                        cabinetBatteryBean.getCabinetIndex(), cabinetBatteryBean.getIndexInCabinet(), cabinetBatteryBean.getIndex(), cabinetBatteryBean.getCapacity()
                );
                batteryCells.add(batteryCell);
            }
        }
        batteryOverviewCacheBean.setBatteryCells(batteryCells);
        Gson gson = new Gson();
        String json = gson.toJson(batteryOverviewCacheBean);
        DBUtil.Put(getCacheKey(), json);

    }

    private String getCacheKey() {
        String key = DBKey.KEY_BATTERY_OVERVIEW_CACHE + mDeviceId;
        return key;
    }

    private void removeCacheOver() {
        if (mBatteryCacheCount > mBatteryCount) {
            for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
                CabinetBatteryBean cabinetBatteryBean = entry.getValue();
                if (cabinetBatteryBean.getIndex() >= mBatteryCount) {
                    cabinetBatteryBean.reset();
                    cabinetBatteryBean.setEmpty(true);
                    updateCellView(cabinetBatteryBean);
                }
            }
        }
    }

    private boolean checkSuccess(int index) {
        for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
            CabinetBatteryBean cabinetBatteryBean = entry.getValue();
            if (cabinetBatteryBean.getIndex() == index) {
                return true;
            }
        }
        return false;
    }

    private void totalCapacity() {
        mTotalCapacity = 0;
        for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
            CabinetBatteryBean cabinetBatteryBean = entry.getValue();
            if (cabinetBatteryBean.getCapacity() > 0) {
                mTotalCapacity += cabinetBatteryBean.getCapacity();
            }
            if (cabinetBatteryBean.isCache()) {
                cabinetBatteryBean.setUnknown(true);
                updateCellView(cabinetBatteryBean);
            }
        }
        double capacityDou = mTotalCapacity / 1000.0;
        double finalCapacity = UnitUtil.savePoint(capacityDou, 1, BigDecimal.ROUND_HALF_UP);
        mBinding.tvTotalCapacityValue.setLocalText(finalCapacity + getString(R.string.power_station_kWh));
        saveBatteryOverviewInfo();
    }

    private void setBatteryUnKnown() {
        for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
            CabinetBatteryBean cabinetBatteryBean = entry.getValue();
            if (cabinetBatteryBean.isCache()) {
                cabinetBatteryBean.setUnknown(true);
                updateCellView(cabinetBatteryBean);
            }
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            if (StringUtil.isNotEmpty(cmd) && cmd.equals(DsCamCmd.GET_CABINET_ALL_INFO)) {
                closeLoadingFragment();
            }
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (result != null && result.size() > 0) {
                            int count;
                            int index;
                            int cabinetIndex;
                            int cabinetPositionIndex;
                            switch (cmd) {
                                case DsCamCmd.GET_CABINET_ALL_INFO:
                                    count = (int) MapUtils.get(result, PSKeyConstant.COUNT, 0);
                                    int viewListSize = mViewList.size();
                                    mCabinetCount = count;
                                    if (mCabinetCount > viewListSize) {
                                        addCabinetView(mCabinetCount - viewListSize, false);
                                    } else if (mCabinetCount < viewListSize) {
                                        removeCabinetView(viewListSize - mCabinetCount);
                                    }
                                    submitCmd(DsCamCmd.GET_BATTERY_ALLINFO);
                                    break;

                                case DsCamCmd.GET_BATTERY_ALLINFO:
                                    count = (int) MapUtils.get(result, PSKeyConstant.COUNT, 0);
                                    mBatteryCount = count;
                                    mBatteryResCount = 0;
                                    mTotalCapacity = 0;
                                    isAllLoad = false;
                                    removeCacheOver();
                                    mRetryCount = 0;
                                    mIndexSet.clear();
                                    if (count > 0) {
                                        for (int i = 0; i < count; i++) {
                                            submitCmdWithIndex(DsCamCmd.GET_BATTERY_INFO, i);
                                        }
                                        mHandler.sendEmptyMessageDelayed(CHECK_BATTERY, CHECK_BATTERY_TIME);
                                        mHandler.sendEmptyMessageDelayed(CHECK_BATTERY, CHECK_BATTERY_TIME * 2);
                                        mHandler.sendEmptyMessageDelayed(CHECK_BATTERY, CHECK_BATTERY_TIME * 6);
                                    } else {
                                        mBinding.tvTotalCapacityValue.setLocalText(0 + getString(R.string.power_station_kWh));
                                        for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
                                            CabinetBatteryBean batteryBean = entry.getValue();
                                            batteryBean.reset();
                                            batteryBean.setEmpty(true);
                                            updateCellView(batteryBean);
                                        }
                                        DBUtil.Put(getCacheKey(), "");
                                    }

                                    break;

                                case DsCamCmd.GET_BATTERY_INFO:
                                    cabinetIndex = (int) MapUtils.get(result, PSKeyConstant.CABINET_INDEX, -1);
                                    index = (int) MapUtils.get(result, PSKeyConstant.INDEX, -1);
                                    if (mIndexSet.contains(index)) return;
                                    mIndexSet.add(index);
                                    cabinetPositionIndex = (int) MapUtils.get(result, PSKeyConstant.CABINET_POSITION_INDEX, -1);
                                    int capacity = (int) MapUtils.get(result, PSKeyConstant.CAPACITY, 0);
//                                    mTotalCapacity = mTotalCapacity + capacity;
                                    mBatteryResCount = mBatteryResCount + 1;
//                                    if (mBatteryResCount == mBatteryCount) {
//                                        double capacityDou = mTotalCapacity / 1000.0;
//                                        double finalCapacity = UnitUtil.savePoint(capacityDou, 1, BigDecimal.ROUND_HALF_UP);
//                                        mBinding.tvTotalCapacityValue.setLocalText(finalCapacity + getString(R.string.power_station_kWh));
//                                        isAllLoad = true;
//                                    }
                                    isAllLoad = mIndexSet.size() == mBatteryCount;
                                    if (cabinetIndex < 0 || index < 0 || cabinetPositionIndex < 0) {
                                        if (isAllLoad) {
                                            saveBatteryOverviewInfo();
                                        }
                                        return;
                                    }
                                    if (cabinetIndex == 0 && cabinetPositionIndex > 2) {
                                        if (isAllLoad) {
                                            saveBatteryOverviewInfo();
                                        }
                                        return;
                                    }
                                    if (cabinetIndex > 0 && cabinetPositionIndex > 4) {
                                        if (isAllLoad) {
                                            saveBatteryOverviewInfo();
                                        }
                                        return;
                                    }
                                    String key = cabinetIndex + "-" + cabinetPositionIndex;
                                    CabinetBatteryBean cabinetBatteryBean = mBatteryMap.get(key);
                                    if (cabinetBatteryBean != null && cabinetBatteryBean.isEmpty()) {
                                        cabinetBatteryBean.setState((int) MapUtils.get(result, PSKeyConstant.STATE, -1));
                                        cabinetBatteryBean.setBmsTemp((Double) MapUtils.get(result, PSKeyConstant.BMS_TEMP, null));
                                        cabinetBatteryBean.setElectrodeATemp((Double) MapUtils.get(result, PSKeyConstant.ELECTRODE_A_TEMP, null));
                                        cabinetBatteryBean.setElectrodeBTemp((Double) MapUtils.get(result, PSKeyConstant.ELECTRODE_B_TEMP, null));
                                        cabinetBatteryBean.setAmpere((int) MapUtils.get(result, PSKeyConstant.AMPERE, -1));
                                        cabinetBatteryBean.setVoltage((int) MapUtils.get(result, PSKeyConstant.VOLTAGE, -1));
                                        cabinetBatteryBean.setSoc((int) MapUtils.get(result, PSKeyConstant.SOC, -1));
                                        cabinetBatteryBean.setCurPower((double) MapUtils.get(result, PSKeyConstant.CUR_POWER, -1));
                                        cabinetBatteryBean.setFullPower((double) MapUtils.get(result, PSKeyConstant.FULL_POWER, -1));
                                        cabinetBatteryBean.setReleaseTimes((int) MapUtils.get(result, PSKeyConstant.RELEASE_TIMES, -1));
                                        cabinetBatteryBean.setRecycleTimes((int) MapUtils.get(result, PSKeyConstant.RECYCLE_TIMES, -1));
                                        cabinetBatteryBean.setHealthy((int) MapUtils.get(result, PSKeyConstant.HEALTHY, -1));
                                        cabinetBatteryBean.setIdInfo((String) MapUtils.get(result, PSKeyConstant.ID_INFO, null));
                                        cabinetBatteryBean.setVersion((String) MapUtils.get(result, PSKeyConstant.VERSION, null));
                                        cabinetBatteryBean.setBarcode((String) MapUtils.get(result, PSKeyConstant.BAR_CODE, null));
                                        cabinetBatteryBean.setCabinetIndex((int) MapUtils.get(result, PSKeyConstant.CABINET_INDEX, -1));
                                        cabinetBatteryBean.setIndex((int) MapUtils.get(result, PSKeyConstant.INDEX, -1));
                                        cabinetBatteryBean.setCabinetPositionIndex((int) MapUtils.get(result, PSKeyConstant.CABINET_POSITION_INDEX, -1));
                                        cabinetBatteryBean.setDischargeSwitchOn((Boolean) MapUtils.get(result, PSKeyConstant.DISCHARGE_SWITCH_ON, false));
                                        cabinetBatteryBean.setChargeSwitchOn((Boolean) MapUtils.get(result, PSKeyConstant.CHARGE_SWITCH_ON, false));
                                        cabinetBatteryBean.setExceptions((List<Integer>) MapUtils.get(result, PSKeyConstant.EXCEPTIONS, null));
                                        cabinetBatteryBean.setEmpty(false);
                                        cabinetBatteryBean.setCurrentIndex(mCurrentIndex == cabinetIndex);
                                        cabinetBatteryBean.setCapacity(capacity);
                                        cabinetBatteryBean.setHeating(false);
                                        cabinetBatteryBean.setHeatAvailable(true);
                                        cabinetBatteryBean.setCache(false);
                                        updateCellView(cabinetBatteryBean);
                                        submitCmdWithIndex(DsCamCmd.GET_BATTERY_ACCESSORYSTATE, index);
                                    }

                                    if (isAllLoad) {
                                        totalCapacity();
                                    }
                                    break;

                                case DsCamCmd.GET_BATTERY_ACCESSORYSTATE:
                                case DsCamCmd.BATTERY_ACCESSORYSTATE_CHANGED:
                                    index = (int) MapUtils.get(result, PSKeyConstant.INDEX, -1);
                                    Boolean heating = (Boolean) MapUtils.get(result, PSKeyConstant.HEATING, null);
                                    Boolean heatAvailable = (Boolean) MapUtils.get(result, PSKeyConstant.HEAT_AVAILABLE, null);
                                    for (Map.Entry<String, CabinetBatteryBean> entry : mBatteryMap.entrySet()) {
                                        CabinetBatteryBean batteryBean = entry.getValue();
                                        if (index > -1 && batteryBean != null && index == batteryBean.getIndex()) {
                                            batteryBean.setHeating(heating);
                                            batteryBean.setHeatAvailable(heatAvailable);
                                            updateCellView(batteryBean);
                                            break;
                                        }
                                    }
                                    break;

                                case DsCamCmd.BATTERY_INDEX_CHANGED:
                                    index = (int) MapUtils.get(result, PSKeyConstant.INDEX, -1);
                                    Boolean isAdd = (Boolean) MapUtils.get(result, PSKeyConstant.IS_ADD, null);
                                    cabinetIndex = (int) MapUtils.get(result, PSKeyConstant.CABINET_INDEX, -1);
                                    cabinetPositionIndex = (int) MapUtils.get(result, PSKeyConstant.CABINET_POSITION_INDEX, -1);
                                    if (isAdd != null && cabinetIndex > -1) {
                                        if (isAdd) {
                                            mBatteryCount += 1;
                                            submitCmdWithIndex(DsCamCmd.GET_BATTERY_INFO, index);
                                        } else {
                                            mIndexSet.remove(index);
                                            mBatteryCount -= 1;
                                            String changedKey = cabinetIndex + "-" + cabinetPositionIndex;
                                            CabinetBatteryBean batteryBean = mBatteryMap.get(changedKey);
                                            if (batteryBean != null) {
                                                mTotalCapacity = mTotalCapacity - batteryBean.getCapacity();
                                                double capacityDou = mTotalCapacity / 1000.0;
                                                double finalCapacity = UnitUtil.savePoint(capacityDou, 1, BigDecimal.ROUND_FLOOR);
                                                mBinding.tvTotalCapacityValue.setLocalText(finalCapacity + getString(R.string.power_station_kWh));
                                                batteryBean.reset();
                                                batteryBean.setEmpty(true);
                                                updateCellView(batteryBean);
                                                saveBatteryOverviewInfo();
                                            }
                                        }
                                    }
                                    break;

                                case DsCamCmd.CABINET_INDEX_CHANGED:
                                    submitCmd(DsCamCmd.GET_CABINET_ALL_INFO);
                                    break;
                            }
                        }
                    }
                });

            } else {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        switch (cmd) {
                            case BmtCmd.GET_CABINET_ALLINFO:
                            case BmtCmd.GET_BATTERY_ALLINFO:
                                setBatteryUnKnown();
                                break;

                            case DsCamCmd.GET_BATTERY_INFO:
//                                if (mRetryCount == 2) {
//                                    mBatteryResCount = mBatteryResCount + 1;
//                                    if (mBatteryResCount == mBatteryCount) {
//                                        double capacityDou = mTotalCapacity / 1000.0;
//                                        double finalCapacity = UnitUtil.savePoint(capacityDou, 1, BigDecimal.ROUND_HALF_UP);
//                                        mBinding.tvTotalCapacityValue.setLocalText(finalCapacity + getString(R.string.power_station_kWh));
//                                        isAllLoad = true;
//                                        saveBatteryOverviewInfo();
//                                    }
//                                }
                                break;
                        }
                    }
                });
            }
        }
    }

    public class UltraPagerAdapter extends PagerAdapter {

        private ArrayList<View> mViewList;

        public UltraPagerAdapter(ArrayList<View> viewList) {
            this.mViewList = viewList;
        }

        @Override
        public int getCount() {
            return mViewList == null ? 0 : mViewList.size();
        }


        @Override
        public int getItemPosition(@NonNull Object object) {
            return POSITION_NONE;
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
            return view == object;
        }

        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            container.addView(mViewList.get(position));
            return mViewList.get(position);
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
        }
    }
}
