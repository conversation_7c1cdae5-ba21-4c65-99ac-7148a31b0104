package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsWarningBinding;

public class PSWarningItemModel extends BasePowerStationItemModel<ItemPsWarningBinding> {

    private int count;
    private String title;
    private ItemPsWarningBinding mBinding;

    public PSWarningItemModel(Context context, String deviceId, String subcategory) {
        super(context, deviceId, subcategory);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_warning;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsWarningBinding binding) {
        mBinding = binding;

    }

    public void setCount(int count) {
        this.count = count;
        if (mBinding != null) {
            mBinding.clParent.setVisibility(count > 0 ? View.VISIBLE : View.GONE);
            mBinding.tvCount.setLocalText(count > 1 ? (count + "") : "");
        }
    }

    public void setTitle(String title) {
        this.title = title;
        if (mBinding != null) {
            mBinding.tvWarning.setText(title);
        }
    }
}
