package com.dinsafer.module.powerstation.event;

public class BatteryIndexChangerEvent {

    private String deviceId;
    private String subcategory;
    private int cabinetIndex;
    private int cabinetPositionIndex;
    private boolean isAdd;

    public BatteryIndexChangerEvent(String deviceId, String subcategory, int cabinetIndex, int cabinetPositionIndex, boolean isAdd) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.cabinetIndex = cabinetIndex;
        this.cabinetPositionIndex = cabinetPositionIndex;
        this.isAdd = isAdd;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public int getCabinetIndex() {
        return cabinetIndex;
    }

    public void setCabinetIndex(int cabinetIndex) {
        this.cabinetIndex = cabinetIndex;
    }

    public int getCabinetPositionIndex() {
        return cabinetPositionIndex;
    }

    public void setCabinetPositionIndex(int cabinetPositionIndex) {
        this.cabinetPositionIndex = cabinetPositionIndex;
    }

    public boolean isAdd() {
        return isAdd;
    }

    public void setAdd(boolean add) {
        isAdd = add;
    }
}
