package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsRegionSearchBinding;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PsRegionAdapter;
import com.dinsafer.module.powerstation.bean.PSRegionBean;
import com.dinsafer.module.powerstation.event.FinishEvent;
import com.dinsafer.module.powerstation.event.PSRegionEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/28 18:06
 * @description :
 */
public class PSRegionSearchFragment extends MyBaseFragment<FragmentPsRegionSearchBinding> implements IDeviceCallBack {

    private PsRegionAdapter mPsRegionAdapter;
    private String mCountry;
    private ArrayList<PSRegionBean> mData;
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private Map<String, Object> params = new HashMap<>();

    private PSRegionBean mSelRegion;

    public static PSRegionSearchFragment newInstance(String deviceId, String subcategory, String country, ArrayList<PSRegionBean> data) {
        PSRegionSearchFragment fragment = new PSRegionSearchFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putString(PSKeyConstant.KEY_COUNTRY, country);
        bundle.putParcelableArrayList(PSKeyConstant.KEY_LIST, data);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_region_search;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setText(getResources().getString(R.string.ps_region));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.ivClear.setOnClickListener(v -> {
            mBinding.etSearch.setText("");
            mPsRegionAdapter.setNewData(mData);
        });
        setEditChangeListener();
        initRv();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if(null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
        mCountry = bundle.getString(PSKeyConstant.KEY_COUNTRY);
        mData = bundle.getParcelableArrayList(PSKeyConstant.KEY_LIST);
    }

    private void setEditChangeListener() {
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String text = mBinding.etSearch.getText().toString();
                if (text == null || text.length() == 0) {
                    setEmpty(false);
                    mPsRegionAdapter.setNewData(mData);
                } else {
                    ArrayList<PSRegionBean> newData = new ArrayList<>();
                    for (PSRegionBean item : mData) {
                        if (item.getCountryName().toUpperCase().startsWith(text.toUpperCase())) {
                            newData.add(item);
                        }
                    }
                    mPsRegionAdapter.setNewData(newData);
                    setEmpty(newData.size() == 0);
                }
            }
        });
    }

    private void initRv() {
        mBinding.rvRegionSupport.setLayoutManager(new LinearLayoutManager(getContext()));
        mPsRegionAdapter = new PsRegionAdapter();
        mBinding.rvRegionSupport.setAdapter(mPsRegionAdapter);
        mPsRegionAdapter.setNewData(mData);
        mPsRegionAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                PSRegionBean regionBean = mPsRegionAdapter.getItem(position);
                mSelRegion = regionBean;
//                submitUpdateRegionCmd(DsCamCmd.UPDATE_REGION, regionBean);
                submitSetRegionCmd(regionBean);
            }
        });
        setEmpty(mData == null || mData.isEmpty());
    }

    private void setEmpty(boolean empty) {
        mBinding.llEmpty.setVisibility(empty ? View.VISIBLE : View.GONE);
        mBinding.rvRegionSupport.setVisibility(empty ? View.GONE : View.VISIBLE);
    }

    /**
     * 发送cmd
     *
     * @param cmd
     */
    private void submitCmd(String cmd, String city) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            params.put(PSKeyConstant.COUNTRY, mCountry);
            if (cmd.equals(DsCamCmd.UPDATE_REGION)) {
                params.put(PSKeyConstant.COUNTRY, city);
            }
            mPSDevice.submit(params);
        }
    }

    private void submitUpdateRegionCmd(String cmd, PSRegionBean psRegionBean) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            params.put(PSKeyConstant.COUNTRY_CODE, psRegionBean.getCountryCode());
            params.put(PSKeyConstant.COUNTRY_NAME, psRegionBean.getCountryName());
            params.put(PSKeyConstant.CITY_NAME, "");
            params.put(PSKeyConstant.TIMEZONE, psRegionBean.getTimezone());
            showTimeOutLoadinFramgmentWithErrorAlert();
            mPSDevice.submit(params);
        }
    }

    /**
     * 同步时区到BMT-MSCT
     * 不需要等待结果的，仅发送请求
     * (设置地区改为这里实现, 把数据传个bmt, bmt再设置 需要等待结果)
     *
     * @param psRegionBean
     */
    private void submitSetRegionCmd(final PSRegionBean psRegionBean) {
        final String timezone = psRegionBean.getTimezone();
        if (mPSDevice != null && !TextUtils.isEmpty(timezone)) {
            DDLog.i(TAG, "submitSetRegionCmd, timezone: " + timezone);
            final Map<String, Object> param = new HashMap<>();
            param.put(BmtDataKey.CMD, BmtCmd.SET_REGION);
            param.put(BmtDataKey.IS_PRICE_TRACKING_SUPPORTED, true);
            param.put(BmtDataKey.COUNTRY_CODE, psRegionBean.getCountryCode());
            param.put(BmtDataKey.COUNTRY_NAME, psRegionBean.getCountryName());
            param.put(BmtDataKey.CITY_NAME, "");
            param.put(BmtDataKey.TIMEZONE, psRegionBean.getTimezone());
            showTimeOutLoadinFramgmentWithErrorAlert();
            mPSDevice.submit(param);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    switch (cmd) {
//                        case DsCamCmd.UPDATE_REGION:
                        case BmtCmd.SET_REGION:
                            if (status == StatusConstant.STATUS_SUCCESS) {
                                EventBus.getDefault().post(new PSRegionEvent(mSelRegion));
                                EventBus.getDefault().post(new FinishEvent());
                                removeSelf();
                            } else {
                                showErrorToast();
                            }
                            break;
                    }
                }
            });
        }
    }
}
