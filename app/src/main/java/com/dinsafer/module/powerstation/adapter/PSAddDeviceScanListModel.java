package com.dinsafer.module.powerstation.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsAddDeviceScanListBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.powerstation.settings.network.PSNetworkSettingsFragment;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/2 14:50
 * @description :
 */
public class PSAddDeviceScanListModel  implements BaseBindModel<ItemPsAddDeviceScanListBinding> {

    private BaseFragment baseFragment;

    public PSAddDeviceScanListModel(BaseFragment baseFragment) {
        this.baseFragment = baseFragment;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_add_device_scan_list;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsAddDeviceScanListBinding itemPsAddDeviceScanListBinding) {

    }
}
