package com.dinsafer.module.powerstation.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Outline;
import android.graphics.Rect;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.databinding.DataBindingUtil;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.PopupAiScheduledDetailBinding;
import com.dinsafer.module.powerstation.impacts.aischeduledchart.bean.AIScheduledBarEntry;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AIScheduledCardView extends RelativeLayout {

    private PopupAiScheduledDetailBinding mBinding;
    private int mItemWidth;
    private int mItemHeight;
    private float leftX;
    private float centerX;
    private float diffX;
    private Context mContext;
    private final int mMarginLeft;
    private View mEmojiView;
    private ImageView ivEmoji;
    private LocalTextView tvEmojiStatus;
    private int strokeWidth = 3;
    private final Rect mRect;
    private final int mTopBottomOffset;
    private int mHourCount;
    private String timezone;
    private long startTime;

    private XAxisValueFormatter formatter = (original, index, viewPortHandler) -> {
        String date = "(" + DDDateUtil.formatWithTimezone(startTime, timezone, "MM.dd") + ")";
        if (mHourCount == 25) {
            if (index == 0) {
                return "00:00" + date;
            } else if (index == 24) {
                return "24:00";
            }
        } else if (mHourCount == 23) {
            if (index == 0) {
                return "00:00" + date;
            } else if (index == 22) {
                return "24:00";
            }
        } else {
            if (index == 0) {
                return "00:00" + date;
            } else if (index == 12) {
                return "12:00";
            } else if (index == 23) {
                return "24:00";
            }
        }
        return "";
    };

    private YAxisValueFormatter yFormatter = (value, yAxis) -> {
        return "";
    };

    public AIScheduledCardView(Context context) {
        this(context, null);
    }

    public AIScheduledCardView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AIScheduledCardView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        mRect = new Rect();
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.popup_ai_scheduled_detail, this, true);
        mItemWidth = DensityUtil.dp2px(context, 42);
        mItemHeight = DensityUtil.dp2px(context, 24);
        mMarginLeft = DensityUtil.dp2px(mContext, 18);
        mTopBottomOffset = DensityUtil.dp2px(mContext, 5);
        mEmojiView = LayoutInflater.from(mContext).inflate(R.layout.layout_ai_scheduled_type, null);
        if (mEmojiView != null) {
            ivEmoji = mEmojiView.findViewById(R.id.iv_type);
            tvEmojiStatus = mEmojiView.findViewById(R.id.tv_status);
        }
        int[] colors = AIColorUtil.getAIColor(context);
        float[] positions = AIColorUtil.getAIColorPosition();
        mBinding.tvTitle.getViewTreeObserver().addOnGlobalLayoutListener(() -> mBinding.tvTitle.setAIColorShader(colors, positions, 0, 0, -15, 5));
        mBinding.tvTitleNote.getViewTreeObserver().addOnGlobalLayoutListener(() -> mBinding.tvTitleNote.setAIColorShader(colors, positions, 0, -5, -20, 10));

        String clickablePart = Local.s(context.getResources().getString(R.string.Report));
        String fullText = Local.s(context.getResources().getString(R.string.ai_scheduled_detail_notice))
                .replace("#report", clickablePart);
        SpannableString spannableString = new SpannableString(fullText);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                // 处理点击事件
                if (clickReportListener != null) {
                    clickReportListener.onClickReport();
                }
            }

            @Override
            public void updateDrawState(android.text.TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(context.getResources().getColor(R.color.color_brand_primary));
            }
        };
        int start = fullText.indexOf(clickablePart);
        int end = start + clickablePart.length();
        spannableString.setSpan(clickableSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mBinding.tvNotice.setText(spannableString);
        mBinding.tvNotice.setMovementMethod(LinkMovementMethod.getInstance());
        initPriceChart();
    }


    public void closeLoading() {
        mBinding.lavLoading.playAnimation();
        mBinding.lavLoading.postDelayed(() -> setBlurAlpha(), 1000);
    }

    private void setBlurAlpha() {
        ValueAnimator animator = ValueAnimator.ofFloat(1.0f, 0.0f);
        animator.addUpdateListener(animation -> {
            float val = (float) animation.getAnimatedValue();
            mBinding.viewAnimHelper.setAlpha(val);
            mBinding.lavLoading.setAlpha(val);
        });
        animator.setDuration(1000);
        animator.start();
    }

    private void initPriceChart() {
        mBinding.viewChart.setDrawBorders(true);
        mBinding.viewChart.setBorderWidth(0.5f);
        mBinding.viewChart.setBorderColor(mContext.getResources().getColor(R.color.color_white_04));
        mBinding.viewChart.setHighlightPerDragEnabled(false);
        mBinding.viewChart.setHighlightFullBarEnabled(false);
        mBinding.viewChart.setScaleEnabled(false);
        //设置图表距离上下左右的距离
        mBinding.viewChart.setExtraOffsets(10, 40, 10, 8);
        mBinding.viewChart.setMinOffset(0);
        //图例
        mBinding.viewChart.getLegend().setEnabled(false);
        mBinding.viewChart.setDescription("");
        mBinding.viewChart.animateX(0);
        mBinding.viewChart.setNeedNoDataGrid(true);
        mBinding.viewChart.setDrawDefaultXGridLines(true);
        mBinding.viewChart.setDrawDefaultYGridLines(true);
        mBinding.viewChart.setYNormalColor(mContext.getResources().getColor(R.color.color_white_04));
        mBinding.viewChart.setYNormalWidth(DensityUtil.dp2px(mContext, 0.5f));

        //获取X轴
        XAxis xAxis = mBinding.viewChart.getXAxis();
        //将垂直于X轴的网格线隐藏，将X轴显示
        xAxis.setDrawGridLines(false);
        xAxis.setDrawAxisLine(false);
        xAxis.setGridColor(mContext.getResources().getColor(R.color.color_white_04));
        //设置X轴上label颜色和大小
        xAxis.setTextSize(10f);
        xAxis.setAvoidFirstLastClipping(true);
        xAxis.setTextColor(mContext.getResources().getColor(R.color.color_white_03));
        //设置X轴高度
        xAxis.setAxisLineWidth(0.5f);
        //x轴刻度值的位置
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
//        设置在”绘制下一个标签”时，要忽略的标签数。
        xAxis.setLabelsToSkip(0);
        xAxis.setValueFormatter(formatter);

        //获取左侧侧坐标轴
        YAxis yAxis = mBinding.viewChart.getAxisLeft();
        //设置是否显示Y轴的值
        yAxis.setDrawLabels(false);
        //是否绘制坐标轴
        yAxis.setDrawAxisLine(false);
        //设置所有垂直Y轴的的网格线是否显示
        yAxis.setDrawGridLines(false);
        yAxis.setValueFormatter(yFormatter);
        yAxis.setLabelCount(5, true);
        mBinding.viewChart.getAxisRight().setEnabled(false);
    }

    public void setViewAinHelperVisible(boolean visible) {
        mBinding.viewAnimHelper.setVisibility(visible ? VISIBLE : GONE);
        mBinding.viewAnimHelper.setAlpha(1.0f);

    }

    public void setLoadingVisible(boolean visible) {
        mBinding.lavLoading.setProgress(0);
        mBinding.lavLoading.cancelAnimation();
        mBinding.lavLoading.setVisibility(visible ? VISIBLE : INVISIBLE);
        mBinding.lavLoading.setAlpha(1.0f);
    }

    private void clipView(int width, int height, int offHeight, float progress) {
        if (width < getCardWidth()) {
            mBinding.rlContent.setOutlineProvider(new ViewOutlineProvider() {
                @Override
                public void getOutline(View view, Outline outline) {
                    if (width < getCardWidth()) {
                        outline.setRect((int) mBinding.flAnimHelper.getX(), 0,
                                (int) (mBinding.flAnimHelper.getX() + width), height + offHeight);
                    }
                }
            });
            mBinding.rlContent.setClipToOutline(true);
            if (mBinding.rlContent.getVisibility() != VISIBLE) {
                mBinding.rlContent.setVisibility(View.VISIBLE);
            }
        } else {
            mBinding.rlContent.setClipToOutline(false);
        }
        if (progress > 0.8f) {
            mBinding.viewAnimHelper.setOutlineProvider(new ViewOutlineProvider() {
                @Override
                public void getOutline(View view, Outline outline) {
                    mBinding.viewBg.getDrawingRect(mRect);
                    int viewBgStrokeWidth = mBinding.viewBg.getStrokeWidth();
                    float radius = (DensityUtil.dp2px(mContext, 12.8f));
                    outline.setRoundRect(mBinding.viewBg.getLeft() + viewBgStrokeWidth, mBinding.viewBg.getTop() + viewBgStrokeWidth,
                            mBinding.viewBg.getRight() - viewBgStrokeWidth, mBinding.viewBg.getBottom() - viewBgStrokeWidth, radius);
                }
            });
            mBinding.viewAnimHelper.setClipToOutline(true);
        } else {
            mBinding.viewAnimHelper.setOutlineProvider(new ViewOutlineProvider() {
                @Override
                public void getOutline(View view, Outline outline) {
                    mBinding.viewBg.getDrawingRect(mRect);

                    int viewBgStrokeWidth = mBinding.viewBg.getStrokeWidth();
                    outline.setRect(mBinding.viewBg.getLeft(), mBinding.viewBg.getTop() + viewBgStrokeWidth,
                            mBinding.viewBg.getRight(), mBinding.viewBg.getBottom() - viewBgStrokeWidth);
                }
            });
            mBinding.viewAnimHelper.setClipToOutline(true);
        }

    }

    public void setContentY(int y) {
        if (mBinding != null) {
            mBinding.rlContent.setY(y + mTopBottomOffset);
        }
    }

    public void updateContentHeight(int[] viewLocation, int height, int expandHeight) {
        if (mBinding != null) {
            if (mBinding.flAnimHelper.getX() != viewLocation[0] - mMarginLeft) {
                mBinding.flAnimHelper.setX(viewLocation[0] - mMarginLeft);
                leftX = viewLocation[0] - mMarginLeft;
                centerX = viewLocation[0] - mMarginLeft + DensityUtil.dp2px(mContext, 42) / 2f;
                diffX = centerX - leftX;
            }
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mBinding.flAnimHelper.getLayoutParams();
            layoutParams.height = Math.min(height, expandHeight) - mTopBottomOffset * 2;
            mBinding.flAnimHelper.setLayoutParams(layoutParams);
            clipView(mItemWidth, layoutParams.height, 0, 0);
        }
    }

    public void updateContentWidth(int width) {
        float progress = (width - DensityUtil.dp2px(getContext(), 42)) / (getCardWidth() * 1.0f);
        float percent = width / (getCardWidth() * 1.0f);
        if (percent > 1f) percent = 1f;
        mBinding.lavLoading.setTranslationX(DensityUtil.dp2px(mContext, 21) * (1 - percent));
        mBinding.viewBg.setRadius(progress > 0.8f ? DensityUtil.dp2px(mContext, 16) : 0);
        float x = width == getCardWidth() ? 0 : (centerX - (progress * diffX) - width / 2f);
        mBinding.flAnimHelper.setX(x);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mBinding.flAnimHelper.getLayoutParams();
        layoutParams.width = width;
        mBinding.flAnimHelper.setLayoutParams(layoutParams);
        clipView(width, mBinding.flAnimHelper.getHeight(), strokeWidth, progress);
    }

    public void setViewOnClickListener(View.OnClickListener clickListener) {
        mBinding.viewClick.setOnClickListener(clickListener);
    }

    /**
     * @param startTime  点击item时间
     * @param chargeType 充放电类型  -1:放电  0:不充不放  1:充电
     * @param plan       充电计划   每小时的充放电量计划。0：A计划，1：B计划，2：C计划
     * @param chartData  数据
     */
    public void setStatus(String timezone, long startTime, int lastItemHour, float pricePercent, float c1, float c2, float c3,
                          float s1, float s2, int chargeType, int plan, List<Float> chartData) {
        mHourCount = chartData.size();
        this.timezone = timezone;
        this.startTime = plan == 1 ? (startTime + 24 * 60 * 60 * 1000L) : startTime;
        boolean isDrawIcon = false;
        switch (plan) {
            case 0:
                if (chargeType == 0) {
                    mBinding.viewChart.setVisibility(GONE);
                    mBinding.tvTitleNote.setVisibility(GONE);
                    mBinding.tvTitle.setLocalText(getString(R.string.ps_is_no_charge_nor_discharge));
                    mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_5));
                } else {
                    mBinding.tvTitleNote.setVisibility(View.VISIBLE);
                    mBinding.viewChart.setVisibility(VISIBLE);
                    if (chargeType == 1) {
                        setChargeText(pricePercent, c1, c2, c3);
                        setEmojiStatus(R.drawable.icon_ai_mode_love, R.string.Low_Utility);
                        isDrawIcon = true;
                    } else {
                        setDischargeText(pricePercent, s1, s2);
                        setEmojiStatus(R.drawable.icon_ai_mode_fear, R.string.High_Utility);
                    }
                }
                break;

            case 1:
                mBinding.tvTitleNote.setVisibility(View.VISIBLE);
                mBinding.viewChart.setVisibility(VISIBLE);
                mBinding.tvTitle.setLocalText(getString(R.string.Charge_with_solar));
                mBinding.tvTitleNote.setLocalText(getString(R.string.Await_Sun));
                mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_2));
                setEmojiStatus(R.drawable.icon_ai_mode_sunglass, R.string.High_Solar);
                break;

            case 2:
                mBinding.tvTitleNote.setVisibility(View.VISIBLE);
                mBinding.viewChart.setVisibility(VISIBLE);
                mBinding.tvTitle.setLocalText(getString(R.string.Charge_with_grid_power));
                mBinding.tvTitleNote.setLocalText(getString(R.string.Await_Lower_Utility));
                mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_3));
                setEmojiStatus(R.drawable.icon_ai_mode_happy, R.string.Lower_Utility);
                break;
        }
        if (plan == 1) {
            setPVChartData(chartData);
        } else {
            if (chargeType != 0) {
                setPriceChartData(timezone, startTime, lastItemHour, chartData, isDrawIcon);
            }
        }

        RelativeLayout.LayoutParams lavLoadingParams = (LayoutParams) mBinding.lavLoading.getLayoutParams();
        if (plan == 0 && chargeType == 0) {
            lavLoadingParams.width = DensityUtils.dp2px(getContext(), 32);
            lavLoadingParams.height = DensityUtils.dp2px(getContext(), 32);
        } else {
            lavLoadingParams.width = DensityUtils.dp2px(getContext(), 50);
            lavLoadingParams.height = DensityUtils.dp2px(getContext(), 50);
        }
        mBinding.lavLoading.setLayoutParams(lavLoadingParams);
    }

    private void setChargeText(float pricePercent, float c1, float c2, float c3) {
        if (pricePercent < c1) {
            mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_1));
        } else if (pricePercent < c2) {
            mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_6));
        } else if (pricePercent < c3) {
            mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_7));
        }
        mBinding.tvTitle.setLocalText(getString(R.string.Charge_with_grid_power));
        mBinding.tvTitleNote.setLocalText(getString(R.string.Charge));
    }

    private void setDischargeText(float pricePercent, float s1, float s2) {
        if (pricePercent > s2) {
            mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_8));
        } else if (pricePercent > s1) {
            mBinding.tvContent.setLocalText(getString(R.string.ai_scheduled_detail_content_4));
        }
        mBinding.tvTitle.setLocalText(getString(R.string.Battery_discharge));
        mBinding.tvTitleNote.setLocalText(getString(R.string.Discharge));
    }

    private String getString(int strId) {
        return mContext.getResources().getString(strId);
    }

    private void setPVChartData(List<Float> chartData) {
        if (CollectionUtil.isListEmpty(chartData)) return;
        int count = chartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }

        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        float maxVal = Collections.max(chartData);
        maxVal = maxVal + (maxVal * 0.1f);
        mBinding.viewChart.getAxisLeft().setAxisMinValue(0);
        mBinding.viewChart.getAxisLeft().setAxisMaxValue(maxVal);
        for (int i = 0; i < count; i++) {
            float val = chartData.get(i);
            AIScheduledBarEntry scheduledBarEntry = new AIScheduledBarEntry(val, i,
                    mContext.getResources().getColor(R.color.color_tip_04));
            scheduledBarEntry.setDrawBorder(val > 0f);
            scheduledBarEntry.setBorderColor(mContext.getResources().getColor(R.color.secondary_tip_ai));
            scheduledBarEntry.setBorderWidth(DensityUtil.dp2px(mContext, 1f));
            scheduledBarEntry.setDrawIcon(val > 0f);
            scheduledBarEntry.setIconRes(R.drawable.icon_sun_mini_sel);
            scheduledBarEntry.setIconTopOffset(DensityUtil.dp2px(mContext, 1.7f));
            yVals.add(scheduledBarEntry);
        }
        mBinding.viewChart.setVerticalLineCount(4);
        mBinding.viewChart.setDrawAverageLine(false);
        mBinding.viewChart.setAverageDashWidth(DensityUtil.dp2px(mContext, 1));
        CombinedData data = new CombinedData(xVals);
        BarDataSet barDataSet = new BarDataSet(yVals, "");
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(false);
        barDataSet.setBarSpacePercent(40);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        mBinding.viewChart.setData(data);
        mBinding.viewChart.setEmojiView(mEmojiView);
        mBinding.viewChart.invalidate();
    }

    private void setEmojiStatus(int resId, int strId) {
        if (ivEmoji != null) {
            ivEmoji.setImageResource(resId);
        }
        if (tvEmojiStatus != null) {
            tvEmojiStatus.setLocalText(getString(strId));
        }
    }

    private void setPriceChartData(String timezone, long startTime, int lastItemHour, List<Float> chartData, boolean isDrawIcon) {
        if (CollectionUtil.isListEmpty(chartData)) return;
        int hour = DDDateUtil.getHourByTimestamps(startTime, timezone);
        long currentTimeMillis = System.currentTimeMillis();
        boolean isSameDay = DDDateUtil.isSameDay(startTime, currentTimeMillis, timezone);
        int nowHour = DDDateUtil.getHourByTimestamps(currentTimeMillis, timezone);
        int lastHour = DDDateUtil.getHourByTimestamps(currentTimeMillis - 60 * 60 * 1000L, timezone);
        int count = chartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }

        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();

        List<Float> absList = new ArrayList<>();
        for (Float val : chartData) {
            absList.add(Math.abs(val));
        }
        float maxVal = Collections.max(absList);
        maxVal = (float) Math.ceil(maxVal) + 10f;
        mBinding.viewChart.setVerticalLineCount(5);
        mBinding.viewChart.getAxisLeft().setAxisMinValue(0);
        mBinding.viewChart.getAxisLeft().setAxisMaxValue(maxVal * 2f);
        int hourPos = hour;
        int nowHourPos = nowHour;
        if (count == 25) {
            if (hour - lastItemHour == 0 || hour > 2) {
                hourPos = hour + 1;
            }
            if (nowHour - lastHour == 0 || nowHour > 2) {
                nowHourPos = nowHour + 1;
            }
        }

        if (count == 23) {
            if (hour > 2) {
                hourPos = hour - 1;
            }
            if (nowHour > 2) {
                nowHourPos = nowHour - 1;
            }
        }

        for (int i = 0; i < count; i++) {
            float val = chartData.get(i) + maxVal;
            AIScheduledBarEntry scheduledBarEntry = new AIScheduledBarEntry(val, i,
                    mContext.getResources().getColor(val >= maxVal ? R.color.color_ai_chart_red_alpha : R.color.color_ai_chart_blue_alpha));

            if (i == hourPos || (i == nowHourPos && isSameDay)) {
                scheduledBarEntry.setCustomColor(mContext.getResources().getColor(
                        val >= maxVal ? R.color.color_ai_chart_red : R.color.color_ai_chart_blue));
                scheduledBarEntry.setDrawBorder(true);
                scheduledBarEntry.setBorderColor(mContext.getResources().getColor(R.color.secondary_tip_ai));
                scheduledBarEntry.setBorderWidth(DensityUtil.dp2px(mContext, 1f));
                if (isDrawIcon) {
                    scheduledBarEntry.setDrawIcon(true);
                    scheduledBarEntry.setIconRes(R.drawable.icon_bmt_ai_price);
                    scheduledBarEntry.setIconTopOffset(DensityUtil.dp2px(mContext, 1.7f));
                }
                scheduledBarEntry.setTextBackgroundRadius(DensityUtil.dp2px(mContext, 2));
                String note = "";
                if (count == 25) {
                    int indexHour = hour - lastItemHour == 0 || hour > 2 ? i - 1 : i;
                    String indexHourStr = indexHour < 10 ? "0" + indexHour : String.valueOf(indexHour);
                    note = (i == nowHourPos && isSameDay) ? Local.s("Now") : "" + indexHourStr + ":00";
                } else if (count == 23) {
                    int indexHour = hour > 2 ? i + 1 : i;
                    String indexHourStr = indexHour < 10 ? "0" + indexHour : String.valueOf(indexHour);
                    note = (i == nowHourPos && isSameDay) ? Local.s("Now") : "" + indexHourStr + ":00";
                } else {
                    String indexHourStr = i < 10 ? "0" + i : String.valueOf(i);
                    note = (i == nowHourPos && isSameDay) ? Local.s("Now") : "" + indexHourStr + ":00";
                }
                scheduledBarEntry.setDrawTopNote(true);
                scheduledBarEntry.setTopNote(note);
                scheduledBarEntry.setNoteColor(mContext.getResources().getColor(R.color.color_black_02));
                scheduledBarEntry.setNoteTextSize(DensityUtil.sp2px(mContext, 10));
                scheduledBarEntry.setTextPaddingLeft(DensityUtil.dp2px(mContext, 2));
                scheduledBarEntry.setTextPaddingTop(DensityUtil.dp2px(mContext, 2));
                scheduledBarEntry.setTextPaddingRight(DensityUtil.dp2px(mContext, 2));
                scheduledBarEntry.setTextPaddingBottom(DensityUtil.dp2px(mContext, 2));
            }
            yVals.add(scheduledBarEntry);
        }

        mBinding.viewChart.setDrawAverageLine(true);
        mBinding.viewChart.setAverage(0.5f);
        mBinding.viewChart.setAverageDashColor(mContext.getResources().getColor(R.color.secondary_tip_ai));
        mBinding.viewChart.setAverageDashWidth(DensityUtil.dp2px(mContext, 1));
        CombinedData data = new CombinedData(xVals);
        BarDataSet barDataSet = new BarDataSet(yVals, "");
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(false);
        barDataSet.setBarSpacePercent(40);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        mBinding.viewChart.setData(data);
        mBinding.viewChart.setEmojiView(mEmojiView);
        mBinding.viewChart.setHighLightPosition(isSameDay ? nowHourPos : -1, hourPos);
        mBinding.viewChart.invalidate();
    }

    public int getContentHeight() {
        return mBinding.rlContent.getHeight();
    }

    public void setContentHeight(int height) {
        RelativeLayout.LayoutParams rlContentBgParams = (LayoutParams) mBinding.rlContent.getLayoutParams();
        rlContentBgParams.height = height;
        mBinding.rlContent.setLayoutParams(rlContentBgParams);

        RelativeLayout.LayoutParams lavLoadingParams = (LayoutParams) mBinding.lavLoading.getLayoutParams();
        if (height == DensityUtils.dp2px(getContext(), 326)) {
            lavLoadingParams.width = DensityUtils.dp2px(getContext(), 50);
            lavLoadingParams.height = DensityUtils.dp2px(getContext(), 50);
        } else {
            lavLoadingParams.width = DensityUtils.dp2px(getContext(), 32);
            lavLoadingParams.height = DensityUtils.dp2px(getContext(), 32);
        }
        mBinding.lavLoading.setLayoutParams(lavLoadingParams);
    }

    public int getCardWidth() {
        return mBinding.rlContent.getWidth();
    }

    private ClickReportListener clickReportListener;

    public void setClickReportListener(ClickReportListener clickReportListener) {
        this.clickReportListener = clickReportListener;
    }

    public interface ClickReportListener {
        void onClickReport();
    }
}
