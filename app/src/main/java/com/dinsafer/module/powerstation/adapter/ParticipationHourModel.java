package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemParticipationHourBinding;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.DDDateUtil;

import java.util.Collections;
import java.util.List;

public class ParticipationHourModel extends BindModel<ItemParticipationHourBinding> {

    private ParticipationHour participationHour;
    private boolean isOne;
    private final StringBuilder sb;

    public ParticipationHourModel(Context context, ParticipationHour participationHour, boolean isOne) {
        super(context);
        this.participationHour = participationHour;
        this.isOne = isOne;
        sb = new StringBuilder();

    }

    @Override
    public int getLayoutID() {
        return R.layout.item_participation_hour;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemParticipationHourBinding binding) {
        String startTime = participationHour.getStart();
        String endTime = participationHour.getEnd();
        List<Integer> repeats = participationHour.getRepeat();
        if (sb.length() > 0) {
            sb.delete(0, sb.length());
        }
        if (participationHour.getAll_day()) {
            sb.append("00:00-23:59");
        } else {
            sb.append(startTime).append("-").append(endTime);
        }
        binding.tvTime.setText(sb.toString());
        Collections.sort(repeats);
        binding.tvRepeatValue.setText(DDDateUtil.sumWeekStr(repeats));
        binding.ivDelete.setVisibility(isOne ? View.GONE : View.VISIBLE);
        binding.ivDeleteHelp.setVisibility(isOne ? View.GONE : View.VISIBLE);
    }

    public ParticipationHour getParticipationHour() {
        return participationHour;
    }

    public void setParticipationHour(ParticipationHour participationHour) {
        this.participationHour = participationHour;
    }

    public boolean isOne() {
        return isOne;
    }

    public void setOne(boolean one) {
        isOne = one;
    }
}
