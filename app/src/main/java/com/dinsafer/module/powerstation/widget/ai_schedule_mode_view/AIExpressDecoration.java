package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DensityUtils;

import java.util.List;

public class AIExpressDecoration extends RecyclerView.ItemDecoration {

    private final Context mContext;
    private final Paint mPaint;
    private int enabledSize;
    private BindMultiAdapter<BindModel> mAdapter;
    private final int leftRightSpace;

    public AIExpressDecoration(Context context, BindMultiAdapter<BindModel> adapter) {
        mContext = context;
        this.mAdapter = adapter;
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        leftRightSpace = DensityUtils.dp2px(context, 11.5f);
    }

    @Override
    public void onDrawOver(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDrawOver(c, parent, state);
        if (mAdapter == null) return;
        int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = parent.getChildAt(i);
            if (child != null) {
                int position = parent.getChildAdapterPosition(child);
                if (position >= enabledSize) break;
                List<BindModel> data = mAdapter.getData();
                AIScheduleModeModel aiScheduleModeModel = (AIScheduleModeModel) data.get(position);
                if (position == 0) {
                    drawExpress(c, aiScheduleModeModel, child);
                } else {
                    long sourceTime = aiScheduleModeModel.getStartTime();
                    boolean isDrawn = false;
                    for (int j = 0; j < position; j++) {
                        AIScheduleModeModel scheduleModeModel = (AIScheduleModeModel) data.get(j);
                        long compareTime = scheduleModeModel.getStartTime();
                        if (DDDateUtil.isSameDay(sourceTime, compareTime, aiScheduleModeModel.getTimezone())) {
                            if ((aiScheduleModeModel.isMaxPrice() && scheduleModeModel.isMaxPrice())
                                    || (aiScheduleModeModel.isMinPrice() && scheduleModeModel.isMinPrice())) {
                                isDrawn = true;
                                break;
                            }
                        }
                    }
                    if (isDrawn) continue;
                    AIScheduleModeModel lastModel = (AIScheduleModeModel) data.get(position - 1);
                    if ((aiScheduleModeModel.isMinPrice() && lastModel.isMinPrice())
                            || (aiScheduleModeModel.isMaxPrice() && lastModel.isMaxPrice()))
                        continue;
                    drawExpress(c, aiScheduleModeModel, child);
                }
            }
        }
    }

    private void drawExpress(Canvas c, AIScheduleModeModel aiScheduleModeModel, View child) {
        if (!aiScheduleModeModel.isPlanA() && aiScheduleModeModel.getMode() == 1) {
            return;
        }
        int bottom = child.getBottom();
        if (aiScheduleModeModel.isMaxPrice()) {
            Bitmap bitmap = BitmapFactory.decodeResource(mContext.getResources(), R.drawable.icon_ai_mode_fear);
            c.drawBitmap(bitmap, child.getWidth() - leftRightSpace - bitmap.getWidth(),
                    bottom - child.getHeight() / 2f - bitmap.getHeight() / 2f, mPaint);
        }
        if (aiScheduleModeModel.isMinPrice()) {
            Bitmap bitmap = BitmapFactory.decodeResource(mContext.getResources(), R.drawable.happy);
            c.drawBitmap(bitmap, leftRightSpace,
                    bottom - child.getHeight() / 2f - bitmap.getHeight() / 2f, mPaint);
        }
    }

    public int getEnabledSize() {
        return enabledSize;
    }

    public void setEnabledSize(int enabledSize) {
        this.enabledSize = enabledSize;
    }
}
