package com.dinsafer.module.powerstation.adapter;

import android.view.View;
import android.view.ViewGroup;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ScheduledChargeBgBean;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.LocalTextView;

public class ScheduledChargeBgAdapter extends BaseQuickAdapter<ScheduledChargeBgBean, BaseViewHolder> {

    private int mItemHeight;

    public ScheduledChargeBgAdapter(int itemHeight) {
        super(R.layout.item_scheduled_charge_bg);
        mItemHeight = itemHeight;
    }

    @Override
    protected void convert(BaseViewHolder helper, ScheduledChargeBgBean item) {
        ConstraintLayout clParent = helper.getView(R.id.cl_parent);
        ViewGroup.LayoutParams layoutParams = clParent.getLayoutParams();
        layoutParams.height = mItemHeight;
        clParent.setLayoutParams(layoutParams);
        LocalTextView tvTime = helper.getView(R.id.tv_time);
        int position = (helper.getAdapterPosition() + 1);
        tvTime.setVisibility(position % 6 == 0 ? View.VISIBLE : View.INVISIBLE);
        tvTime.setLocalText(item.getTime());

        View viewScale = helper.getView(R.id.view_scale);
        ViewGroup.LayoutParams clLayoutParams = viewScale.getLayoutParams();
        clLayoutParams.width = position % 3 == 0 ? DensityUtil.dp2px(mContext, 8)
                : DensityUtil.dp2px(mContext, 6);
        viewScale.setLayoutParams(clLayoutParams);
        viewScale.setBackgroundResource(position % 3 == 0 ? R.drawable.shape_bg_scheduled_charge_scale_big
                : R.drawable.shape_bg_scheduled_charge_scale_small);
        viewScale.setVisibility(position % 6 == 0 ? View.INVISIBLE : View.VISIBLE);
    }
}
