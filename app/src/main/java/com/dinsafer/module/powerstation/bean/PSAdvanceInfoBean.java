package com.dinsafer.module.powerstation.bean;

import android.text.TextUtils;

public class PSAdvanceInfoBean {
    private String name;
    private String signal;
    private String ip;
    private String mac;
    private String id;
    private String mcuId;
    private String version;
    private boolean isConnectWifi;
    private boolean showDot;
    private String ethernetIp;
    private String ethernetMac;
    private boolean isConnectEthernet;

    public PSAdvanceInfoBean(String name, String signal, String ip, String mac, String id
            , String mcuId, String version, boolean isConnectWifi, boolean showDot, String ethernetIp, String ethernetMac) {
        this.name = name;
        this.signal = signal;
        this.ip = ip;
        this.mac = mac;
        this.id = id;
        this.mcuId = mcuId;
        this.version = version;
        this.isConnectWifi = isConnectWifi;
        this.showDot = showDot;
        this.ethernetIp = ethernetIp;
        this.ethernetMac = ethernetMac;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSignal() {
        return signal;
    }

    public void setSignal(String signal) {
        this.signal = signal;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMcuId() {
        return mcuId;
    }

    public void setMcuId(String mcuId) {
        this.mcuId = mcuId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public boolean isConnectWifi() {
        return isConnectWifi;
    }

    public void setConnectWifi(boolean connectWifi) {
        isConnectWifi = connectWifi;
    }

    public boolean isShowDot() {
        return showDot;
    }

    public void setShowDot(boolean showDot) {
        this.showDot = showDot;
    }

    public String getEthernetIp() {
        return ethernetIp;
    }

    public void setEthernetIp(String ethernetIp) {
        this.ethernetIp = ethernetIp;
    }

    public String getEthernetMac() {
        return ethernetMac;
    }

    public void setEthernetMac(String ethernetMac) {
        this.ethernetMac = ethernetMac;
    }

    public boolean isConnectEthernet() {
        isConnectEthernet = !TextUtils.isEmpty(ethernetIp) && !TextUtils.isEmpty(ethernetMac);
        return isConnectEthernet;
    }
}
