package com.dinsafer.module.powerstation.ev;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsInstantChargeBinding;
import com.dinsafer.dinnet.databinding.FragmentPsSonChargeBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSEVChargeAdapter;
import com.dinsafer.module.powerstation.bean.PSEVChargeBean;
import com.dinsafer.module.powerstation.event.EVBottomBehaviorEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class PSInstantChargeFragment extends MyBaseFragment<FragmentPsInstantChargeBinding> {

    private static final int SMART_CHARGE = 1;
    private static final int INSTANT_CHARGE = 2;
    // 1. Smart Charge 2.Instant Charge
    private int mTabType;
    private PSEVChargeAdapter mEVChargeAdapter;

    public static PSInstantChargeFragment newSmartChargeInstance() {
        return newInstance(SMART_CHARGE);
    }

    public static PSInstantChargeFragment newInstantChargeInstance() {
        return newInstance(INSTANT_CHARGE);
    }

    public static PSInstantChargeFragment newInstance(int mTabType) {
        PSInstantChargeFragment fragment = new PSInstantChargeFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_TAB_TYPE, mTabType);
        fragment.setArguments(bundle);
        return fragment;
    }
    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_instant_charge;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        EventBus.getDefault().register(this);
        mBinding.viewSelected.getRoot().setBackgroundResource(R.drawable.shape_bg_item_ev_charge_sel);
        initRv();
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mTabType = bundle.getInt(PSKeyConstant.KEY_TAB_TYPE);
    }

    private void initRv() {
        mBinding.rvData.setLayoutManager(new LinearLayoutManager(getDelegateActivity()));
        mEVChargeAdapter = new PSEVChargeAdapter();
        mBinding.rvData.setAdapter(mEVChargeAdapter);
        mEVChargeAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {

            }
        });
        loadChargeData();
    }

    private void loadChargeData() {

        if (mTabType == SMART_CHARGE) {
            List<PSEVChargeBean> evChargeList = new ArrayList<>();
            PSEVChargeBean lowerUtility = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_1),
                    "当市电价格基于今日均价的涨跌符合以下条件，允许EV充电。", PSEVChargeBean.LOWER_UTILITY_RATE);
            lowerUtility.setValue(-30);
            lowerUtility.setValueStr("-30%");
            lowerUtility.setSelected(true);
            lowerUtility.setStatus(1);
            evChargeList.add(lowerUtility);

            PSEVChargeBean solarOnly = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_2),
                    "EV充电功率将被限制在太阳能发电功率以内。", PSEVChargeBean.SOLAR_ONLY);
            evChargeList.add(solarOnly);

            PSEVChargeBean scheduleCharge = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_3),
                    "在预设的时段内允许EV充电。", PSEVChargeBean.SCHEDULE_CHARGE);
            evChargeList.add(scheduleCharge);
            setSelectValue(lowerUtility);
            mEVChargeAdapter.setNewData(evChargeList);
        } else if (mTabType == INSTANT_CHARGE) {
            List<PSEVChargeBean> evChargeList = new ArrayList<>();
            PSEVChargeBean untilFully = new PSEVChargeBean(getString(R.string.ps_ev_instant_charge_title_1),
                    "充满后车端将自动断开。", PSEVChargeBean.UNTIL_FULLY_CHARGED);
            untilFully.setSelected(true);
            untilFully.setStatus(1);
            evChargeList.add(untilFully);

            PSEVChargeBean fixedCharging = new PSEVChargeBean(getString(R.string.ps_ev_instant_charge_title_2),
                    "EV充电功率将被限制在太阳能发电功率以内。", PSEVChargeBean.FIXED_QUANTITY);
            fixedCharging.setValue(10);
            fixedCharging.setValueStr("10kWh");
            evChargeList.add(fixedCharging);

            PSEVChargeBean fixedDuration = new PSEVChargeBean(getString(R.string.ps_ev_instant_charge_title_2),
                    "预设每次充电的时长。", PSEVChargeBean.FIXED_DURATION);
            fixedDuration.setValue(1);
            fixedDuration.setValueStr("1 hour(s)");
            evChargeList.add(fixedDuration);
            setSelectValue(untilFully);
            mEVChargeAdapter.setNewData(evChargeList);
        }
    }

    private void setSelectValue(PSEVChargeBean chargeBean) {
        mBinding.viewSelected.tvTitle.setLocalText(chargeBean.getTitle());
        mBinding.viewSelected.tvSubtitle.setLocalText(chargeBean.getSubTitle());
        int type = chargeBean.getType();
        switch (type) {
            case PSEVChargeBean.LOWER_UTILITY_RATE:
                mBinding.viewSelected.tvLess.setVisibility(View.VISIBLE);
                mBinding.viewSelected.llOperate.setVisibility(View.VISIBLE);
                mBinding.viewSelected.tvValue.setLocalText(chargeBean.getValue());
                break;

            case PSEVChargeBean.SOLAR_ONLY:

            case PSEVChargeBean.UNTIL_FULLY_CHARGED:

                break;

            case PSEVChargeBean.SCHEDULE_CHARGE:
                mBinding.viewSelected.tvGoSettings.setVisibility(View.VISIBLE);
                break;

            case PSEVChargeBean.FIXED_QUANTITY:
            case PSEVChargeBean.FIXED_DURATION:
                mBinding.viewSelected.llOperate.setVisibility(View.VISIBLE);
                mBinding.viewSelected.tvValue.setLocalText(chargeBean.getValue());
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(EVBottomBehaviorEvent event) {
//        boolean isVisible = event.isVisible();
//        mBinding.viewSelected.getRoot().setVisibility(isVisible ? View.VISIBLE : View.GONE);
//        mBinding.rvData.setVisibility(isVisible ? View.GONE : View.VISIBLE);
    }
}
