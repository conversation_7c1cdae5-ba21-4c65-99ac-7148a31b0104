package com.dinsafer.module.powerstation.guide.home_guide;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentHomeGuide4Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class HomeGuide4Fragment extends MyBaseFragment<FragmentHomeGuide4Binding> {

    public static final String TAG = HomeGuide4Fragment.class.getSimpleName();

    public static HomeGuide4Fragment newInstance() {
        return new HomeGuide4Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_home_guide_4;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
