package com.dinsafer.module.powerstation.widget;

import androidx.recyclerview.widget.LinearLayoutManager;

import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 12:03
 * @description :
 */
public class StartSnapHelper extends PagerSnapHelper {
    private static final String TAG = StartSnapHelper.class.getSimpleName();
    private OrientationHelper mHorizontalHelper, mVerticalHelper;
    private int mCurrentPosition = 0;
    private int mLastPosition = 0;
    private PageListener mPageListener;

    public StartSnapHelper() {
        mCurrentPosition = 0;
        mLastPosition = 0;
    }

    public interface PageListener {
        void onPageSnapChanged();
    }

    public void setPageListener(PageListener pageListener) {
        mPageListener = pageListener;
    }

    @Override
    public int[] calculateDistanceToFinalSnap(RecyclerView.LayoutManager layoutManager, View targetView) {
        int[] out = new int[2];
        if (layoutManager.canScrollHorizontally()) {
            out[0] = distanceToStart(targetView, getHorizontalHelper(layoutManager));
        } else {
            out[0] = 0;
        }
        if (layoutManager.canScrollVertically()) {
            out[1] = distanceToStart(targetView, getVerticalHelper(layoutManager));
        } else {
            out[1] = 0;
        }
        return out;
    }

    private int distanceToStart(View targetView, OrientationHelper helper) {
        return helper.getDecoratedStart(targetView) - helper.getStartAfterPadding();
    }

    @Override
    public View findSnapView(RecyclerView.LayoutManager layoutManager) {

        View view = super.findSnapView(layoutManager);
        if (null != view) {
            mCurrentPosition = layoutManager.getPosition(view);
            //LogUtil.i(TAG + "findSnapView()22 tag: " + view.getTag()
            //    + " mCurrentPosition:" + mCurrentPosition + " mLastPosition:" + mLastPosition+" mPageListener:"+mPageListener);
            if (mLastPosition != mCurrentPosition && null != mPageListener) {
                mPageListener.onPageSnapChanged();
            }
            mLastPosition = mCurrentPosition;
        }

        if (layoutManager instanceof LinearLayoutManager) {

            if (layoutManager.canScrollHorizontally()) {
                return findStartView(layoutManager, getHorizontalHelper(layoutManager));
            } else {
                return findStartView(layoutManager, getVerticalHelper(layoutManager));
            }
        }
        return super.findSnapView(layoutManager);
    }


    private View findStartView(RecyclerView.LayoutManager layoutManager,
                               OrientationHelper helper) {
        if (layoutManager instanceof LinearLayoutManager) {
            int firstChild = ((LinearLayoutManager) layoutManager).findFirstVisibleItemPosition();
            //check if last item, if last item should not align, otherwise it maybe show incompletely
            boolean isLastItem = ((LinearLayoutManager) layoutManager)
                    .findLastCompletelyVisibleItemPosition()
                    == layoutManager.getItemCount() - 1;

            if (firstChild == RecyclerView.NO_POSITION || isLastItem) {
                return null;
            }

            View child = layoutManager.findViewByPosition(firstChild);

            if (helper.getDecoratedEnd(child) >= helper.getDecoratedMeasurement(child) / 2
                    && helper.getDecoratedEnd(child) > 0) {
                return child;
            } else {
                if (((LinearLayoutManager) layoutManager).findLastCompletelyVisibleItemPosition()
                        == layoutManager.getItemCount() - 1) {
                    return null;
                } else {
                    return layoutManager.findViewByPosition(firstChild + 1);
                }
            }
        }

        return super.findSnapView(layoutManager);
    }

    private OrientationHelper getHorizontalHelper(
            RecyclerView.LayoutManager layoutManager) {
        if (mHorizontalHelper == null) {
            mHorizontalHelper = OrientationHelper.createHorizontalHelper(layoutManager);
        }
        return mHorizontalHelper;
    }

    private OrientationHelper getVerticalHelper(RecyclerView.LayoutManager layoutManager) {
        if (mVerticalHelper == null) {
            mVerticalHelper = OrientationHelper.createVerticalHelper(layoutManager);
        }
        return mVerticalHelper;
    }
}
