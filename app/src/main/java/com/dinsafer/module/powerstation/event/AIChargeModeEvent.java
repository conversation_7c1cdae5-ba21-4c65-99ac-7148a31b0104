package com.dinsafer.module.powerstation.event;

public class AIChargeModeEvent {

    private int sectionType;
    private int selectedMode;
    private boolean initAI;

    public AIChargeModeEvent(int sectionType, int selectedMode, boolean initAI) {
        this.sectionType = sectionType;
        this.selectedMode = selectedMode;
        this.initAI = initAI;
    }

    public int getSectionType() {
        return sectionType;
    }

    public void setSectionType(int sectionType) {
        this.sectionType = sectionType;
    }

    public int getSelectedMode() {
        return selectedMode;
    }

    public void setSelectedMode(int selectedMode) {
        this.selectedMode = selectedMode;
    }

    public boolean isInitAI() {
        return initAI;
    }

    public void setInitAI(boolean initAI) {
        this.initAI = initAI;
    }

    @Override
    public String toString() {
        return "AIChargeModeEvent{" +
                "sectionType=" + sectionType +
                ", selectedMode=" + selectedMode +
                ", initAI=" + initAI +
                '}';
    }
}
