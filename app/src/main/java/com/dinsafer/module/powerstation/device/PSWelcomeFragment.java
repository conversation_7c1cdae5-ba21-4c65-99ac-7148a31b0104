package com.dinsafer.module.powerstation.device;

import android.os.Bundle;
import android.util.TypedValue;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsWelcomeBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.iap.GetDeviceExpirationDateResponseV2;
import com.dinsafer.module.iap.RenewSuccessEvent;
import com.dinsafer.module.iap.powercare.TrafficPackageServiceRenewFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PSWelcomeInfoModel;
import com.dinsafer.module.powerstation.impacts.ReserveModeFragment;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/2 17:09
 * @description :
 */
public class PSWelcomeFragment extends MyBaseFragment<FragmentPsWelcomeBinding> implements IDeviceCallBack {

    private BindMultiAdapter<PSWelcomeInfoModel> mAdapter;
    private ArrayList<PSWelcomeInfoModel> mData;
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private Map<String, Object> params = new HashMap<>();
    private long mExpirationDate;
    private int mStatus;
    private boolean isExpired;

    public static PSWelcomeFragment newInstance(final String deviceId, final String subcategory) {
        PSWelcomeFragment fragment = new PSWelcomeFragment();
        final Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static PSWelcomeFragment newInstance(final String deviceId, final String subcategory, long expirationDate, int status) {
        PSWelcomeFragment fragment = new PSWelcomeFragment();
        final Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putLong(PSKeyConstant.KEY_EXPIRATION_DATE, expirationDate);
        bundle.putInt(PSKeyConstant.KEY_STATUS, status);
        fragment.setArguments(bundle);
        return fragment;
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        mPSDevice.registerDeviceCallBack(this);
        mExpirationDate = bundle.getLong(PSKeyConstant.KEY_EXPIRATION_DATE);
        mStatus = bundle.getInt(PSKeyConstant.KEY_STATUS, -1);
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_welcome;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText("");
        mBinding.commonBar.vDivider.setVisibility(View.GONE);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.lcbNext.setOnClickListener(v -> {
//            params.clear();
//            params.put(PSKeyConstant.CMD, DsCamCmd.SET_CHARGE_STRATEGIES);
//            params.put(PSKeyConstant.STRATEGY_TYPE, PSReserveModeConstant.PRICE_INSENSITIVE);
//            params.put(PSKeyConstant.SMART_RESERVE, PSReserveModeConstant.PRICE_INSENSITIVE_SMART);
//            params.put(PSKeyConstant.GOOD_PRICE_PERCENTAGE, PSReserveModeConstant.PRICE_INSENSITIVE_EMERGENCY);
//            params.put(PSKeyConstant.EMERGENCY_RESERVE, PSReserveModeConstant.PRICE_INSENSITIVE_GOOD_PRICE);
//            params.put(PSKeyConstant.ACCEPTABLE_PRICE_PERCENTAGE, PSReserveModeConstant.PRICE_INSENSITIVE_ACCEPTABLE_PRICE);
//            showTimeOutLoadinFramgment();
//            submitCmd();
            if (isExpired) {
                getDelegateActivity().addCommonFragment(TrafficPackageServiceRenewFragment.newInstance(mDeviceId, mSubcategory,""
                        , 4, false));
            } else {
                goNextFragment();
            }
        });
        setViewByStatus();
        initRv();
    }

    private void goNextFragment() {
        if (BmtUtil.isBmtDevicePowerPulse(mPSDevice)) {
            getDelegateActivity().removeAllCommonFragment();
            getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstanceForStepAddPS(
                    DeviceHelper.getString(BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory), DinConst.INFO_NAME, "")
                    , mDeviceId, mSubcategory));
        } else {
            getDelegateActivity().addCommonFragment(ReserveModeFragment.newInstanceForStepAddPS(mDeviceId, mSubcategory));
        }
    }

    private void setViewByStatus() {
        long currentTime = System.currentTimeMillis();
        long daysDiff = TimeUtil.getDaysDiff(currentTime, mExpirationDate  / 1000_000);
        if (mStatus > -1 && daysDiff < 1) { // 服务端接口 改为不返回过期状态，是否过期可用过期时间进行判断
            mBinding.lcbNext.setLocalText(getString(R.string.iap_renew));
            isExpired = true;
            mBinding.tvWelcome.setLocalText(getString(R.string.welcome_back));
            String expiredStr = Local.s(getString(R.string.your_hashtag_service_is_expired))
                    .replace(getString(R.string.hashtag_service), Local.s(getString(R.string.iap_4g_traffic_package)));
            mBinding.tvDesc.setLocalText(expiredStr);
            mBinding.commonBar.commonBarRightText.setLocalText(getString(R.string.skip));
            mBinding.commonBar.commonBarRightText.setTextColor(getResources().getColor(R.color.color_brand_text));
            RelativeLayout.LayoutParams rlParams = (RelativeLayout.LayoutParams) mBinding.commonBar.commonBarRightText.getLayoutParams();
            rlParams.rightMargin = DensityUtil.dp2px(getContext(), 2);
            mBinding.commonBar.commonBarRightText.setLayoutParams(rlParams);
            mBinding.commonBar.commonBarRightText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
            mBinding.commonBar.commonBarRightText.setBackground(null);
            mBinding.commonBar.commonBarRightText.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    goNextFragment();
                }
            });
            mBinding.commonBar.commonBarRightText.setVisibility(View.VISIBLE);
        } else {
            mBinding.lcbNext.setLocalText(getString(R.string.next));
            isExpired = false;
            mBinding.commonBar.commonBarRightText.setVisibility(View.GONE);
            switch (mStatus) {
                case GetDeviceExpirationDateResponseV2.STATUS_FREE_TRIAL:
                    mBinding.tvWelcome.setLocalText(getString(R.string.ps_device_welcome));
                    String freeStr = Local.s(getString(R.string.your_service_remaining_day))
                            .replace(getString(R.string.hashtag_service), Local.s(getString(R.string.iap_4g_traffic_package)))
                            .replace(getString(R.string.hashtag_validity_period), String.valueOf(daysDiff));
                    mBinding.tvDesc.setText(freeStr);
                    break;

                case GetDeviceExpirationDateResponseV2.STATUS_IN_PURCHASE:
                    mBinding.tvWelcome.setLocalText(getString(R.string.welcome_back));
                    String purchaseStr = Local.s(getString(R.string.your_service_validity_day))
                            .replace(getString(R.string.hashtag_service), Local.s(getString(R.string.iap_4g_traffic_package)))
                            .replace(getString(R.string.hashtag_validity_period), String.valueOf(daysDiff));
                    mBinding.tvDesc.setText(purchaseStr);
                    break;

                case GetDeviceExpirationDateResponseV2.STATUS_EXPIRED:
                    mBinding.tvWelcome.setLocalText(getString(R.string.welcome_back));
                    String expiredStr = Local.s(getString(R.string.your_hashtag_service_is_expired))
                            .replace(getString(R.string.hashtag_service), Local.s(getString(R.string.iap_4g_traffic_package)));
                    mBinding.tvDesc.setLocalText(expiredStr);
                    break;

                default:
                    mBinding.tvDesc.setLocalText(getString(R.string.ps_welcome_desc));
                    mBinding.tvWelcome.setLocalText(getString(R.string.ps_device_welcome));
                    break;
            }
        }
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    private void initRv() {
        mBinding.rvInfo.setLayoutManager(new LinearLayoutManager(getContext()));
        mAdapter = new BindMultiAdapter<>();
        mData = new ArrayList<>();
        List<String> titles = Arrays.asList(getResources().getStringArray(R.array.ps_welcome_info_titles));
        List<String> contents = Arrays.asList(getResources().getStringArray(R.array.ps_welcome_info_contents));
        int[] logos = {R.drawable.icon_bmt_iap_4g, R.drawable.icon_bmt_iap_weather, R.drawable.icon_bmt_iap_ai, R.drawable.icon_bmt_iap_report};
        for (int i = 0; i < titles.size(); i++) {
            mData.add(new PSWelcomeInfoModel(logos[i], titles.get(i), contents.get(i)));
        }
        mAdapter.setNewData(mData);
        mBinding.rvInfo.setAdapter(mAdapter);
    }

    private void submitCmd() {
        if (mPSDevice != null) {
            mPSDevice.submit(params);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (cmd != null && cmd.equals(DsCamCmd.SET_CHARGE_STRATEGIES)) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (status == StatusConstant.STATUS_SUCCESS) {
                            goNextFragment();
                        } else {
                            showErrorToast();
                        }
                    }
                });
            }
        }
    }

    private void setNextEnabled(boolean isEnabled) {
        mBinding.lcbNext.setEnabled(isEnabled);
        mBinding.lcbNext.setAlpha(isEnabled ? 1f: 0.5f);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onRenewSuccessEvent(RenewSuccessEvent event) {
        setNextEnabled(false);
        DinsafeAPI.getApi().getBmtIAPExpirationData(mDeviceId).enqueue(new Callback<GetDeviceExpirationDateResponseV2>() {
            @Override
            public void onResponse(Call<GetDeviceExpirationDateResponseV2> call, Response<GetDeviceExpirationDateResponseV2> response) {
                if (response.body() != null && response.body().getResult() != null) {
                    mExpirationDate = response.body().getResult().getExpirationDate();
                    mStatus = response.body().getResult().getStatus();
                    setNextEnabled(true);
                    setViewByStatus();
                } else {
                    showErrorToast();
                }
            }

            @Override
            public void onFailure(Call<GetDeviceExpirationDateResponseV2> call, Throwable t) {
                showErrorToast();
            }
        });
    }
}
