package com.dinsafer.module.powerstation.electricity;

import android.os.Bundle;
import android.view.View;


import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentElectricitySolarBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.marker.SolarMarkerView;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.module.powerstation.electricity.controller.SolarChartModelController;
import com.dinsafer.module.powerstation.electricity.helper.SolarChartHelper;
import com.dinsafer.module.powerstation.event.StatInfoEvent;
import com.dinsafer.module.powerstation.widget.FlipCombinedChartView;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.UnitUtil;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ElectricitySolarFragment extends BaseChartFragment<SolarChartModelController,
        FragmentElectricitySolarBinding> {

    private CustomCombinedMarkerView mMarkerView;
    private boolean isThree;
    private PSElectricityTypeBean mSolarDualBean;
    private int  mBmtType = -1;
    private SolarChartHelper mSolarChartHelper;

    public static ElectricitySolarFragment newInstance(int fromIndex, String deviceId, String subCategory) {
        ElectricitySolarFragment fragment = new ElectricitySolarFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.INDEX, fromIndex);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_electricity_solar;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        String subCategory = getArguments().getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mBmtType = BmtUtil.getBmtType(subCategory);
        mSolarChartHelper = new SolarChartHelper(getContext(), mBmtType);
        mType = BaseChartFragment.CHART_ELECTRICITY_SOLAR;
        mPlusMinusType = PlusMinusType.PLUS;
        isThree = BmtUtil.isThreePhase(ElectricityStatisticsFragment.mPSDevice);
        mMarkerView = new SolarMarkerView(getContext());
        initRefreshLayout(mBinding.refreshLayout);

//        getStatisticData();
    }

    @Override
    protected void initChartView() {
        super.initChartView();
        initChart(mFlipCombinedChartView, mMarkerView);
        setIvRightEnabled(mOffSet != 0);
        initElectricityType();
        mFlipCombinedChartView.setTvNoteVisible(mCycleType != CycleType.DAY);
        mFlipCombinedChartView.setFlipChangeListener(new FlipCombinedChartView.OnFlipChangeListener() {
            @Override
            public void flipChange(int index, OperateOrientation orientation) {
                mIndex = index;
                if (orientation == OperateOrientation.LEFT) {
                    mOffSet = mOffSet - 1;
                } else if (orientation == OperateOrientation.RIGHT) {
                    mOffSet = mOffSet + 1;
                }
//                EventBus.getDefault().post(new ChartPageChangeEvent(orientation));
                setIvRightEnabled(mOffSet != 0);
                getStatisticData(true);
            }
        });
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser && isInit) {
            if (mCycleType != CycleType.LIFETIME) {
                if (isChartViewNotNull()) {
                    setIvRightEnabled(mOffSet != 0);
                }
            }
            getStatisticData(true);
        }
    }

    private void initElectricityType() {
        if (isChartViewNotNull()) {
            mData = mSolarChartHelper.getFilterData(isThree, isDualPowerOpen);
            mFlipCombinedChartView.setElectricityTypeData(mData);
            mFlipCombinedChartView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mFlipCombinedChartView.setRvFilterVisible(mData.size() > 1);
                    if (isDualPowerOpen) {
                        mFlipCombinedChartView.setTPPVDescText(Local.s(getString(R.string.solar_dual)) + ": "
                                + Local.s(getString(R.string.using_third_party_pv_desc)));
                        mFlipCombinedChartView.setTvTPPVDescVisible(true);
                    }
                }
            }, 1000);

        }
    }

    @Override
    public void createChartModelController() {
        chartModelController = new SolarChartModelController();
    }

    @Override
    protected void getStatisticData(boolean showLoading) {
        if (ElectricityStatisticsFragment.mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.GET_STATS_MPPT_V2);
            params.put(BmtDataKey.INTERVAL, ElectricityStatisticsFragment.mInterval);
            params.put(BmtDataKey.OFFSET, mOffSet);
            if (showLoading) {
                showTimeOutLoadinFramgmentWithMarginTop(marginTop);
            }

            Map<String, Object> result = null;
            switch (mCycleType) {
                case DAY:
                    result = mDayCache.get(mOffSet);
                    break;

                case WEEK:
                    result = mWeekCache.get(mOffSet);
                    break;

                case MONTH:
                    result = mMonthCache.get(mOffSet);
                    break;

                case YEAR:
                    result = mYearCache.get(mOffSet);
                    break;
            }
            if (result == null) {
                ElectricityStatisticsFragment.mPSDevice.submit(params);
            } else {
                EventBus.getDefault().post(new StatInfoEvent(result));
                setChartData(BaseChartFragment.CHART_ELECTRICITY_SOLAR, result);
                closeLoadingFragment();
            }
        }
    }

    @Override
    protected void resetChart() {
        float leftYMaxVal = getYMax(CHART_ELECTRICITY_SOLAR, mChartData);
        String powerUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, false);
        String powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);
        mMarkerView.setPowerUnit(powerUnit);
        mMarkerView.setPowerHourUnit(powerHourUnit);
        mMarkerView.setLeftYMaxVal(leftYMaxVal);
        mMarkerView.setDSTTransitionDay(mHourCount == DST_TIME_MINUTE);
        if (mHourCount == DST_TIME_MINUTE) {
            mMarkerView.setTimeType(1);
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            mMarkerView.setTimeType(-1);
        } else {
            mMarkerView.setTimeType(0);
        }
        mMarkerView.setTimestamp(mStartTime);
        mMarkerView.setTimezone(timezone);
        String leftUnit = ChartDataUtil.getPowerUnit(getSumVal(), true);
//        mBinding.tvUnit.setText("(" + leftUnit + ")");
        mBinding.esvVal.setLeftVal(ChartDataUtil.getPowerTransferVal(getSumVal(), getSumVal(), false), leftUnit);
        mBinding.esvVal.setMiddleVal(mCycleType == CycleType.DAY ?
                        Local.s(getString(R.string.electricity_power_peak)) : Local.s(getString(R.string.electricity_high)),
                ChartDataUtil.getPowerTransferVal(getHighVal(), getHighVal(), mCycleType == CycleType.DAY),
                ChartDataUtil.getPowerUnit(getHighVal(), mCycleType != CycleType.DAY));
        mBinding.esvVal.setRightVal(mCycleType == CycleType.DAY ?
                        "" : Local.s(getString(R.string.electricity_low)),
                mCycleType == CycleType.DAY ?
                        "" : ChartDataUtil.getPowerTransferVal(getLowVal(), getLowVal(), false),
                mCycleType == CycleType.DAY ? "" : ChartDataUtil.getPowerUnit(getLowVal(), true));
        mBinding.esvVal.refreshText();
//        setLowVisible(mCycleType != CycleType.DAY);
        super.resetChart();
    }

    @Override
    protected void resetFailChart() {
        float leftYMaxVal = 0;
        String powerUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, false);
        String powerHourUnit = ChartDataUtil.getPowerUnit(leftYMaxVal, true);

        String leftUnit = ChartDataUtil.getPowerUnit(0, true);
        mBinding.esvVal.setLeftVal(mFailVal, leftUnit);
        mBinding.esvVal.setMiddleVal(mCycleType == CycleType.DAY ?
                        Local.s(getString(R.string.electricity_power_peak)) : Local.s(getString(R.string.electricity_high)),
                mFailVal,
                mCycleType == CycleType.DAY ? powerUnit : powerHourUnit);
        mBinding.esvVal.setRightVal(mCycleType == CycleType.DAY ?
                        "" : Local.s(getString(R.string.electricity_low)),
                mCycleType == CycleType.DAY ?
                        "" : mFailVal,
                mCycleType == CycleType.DAY ? "" : powerHourUnit);
        mBinding.esvVal.refreshText();
        super.resetFailChart();
    }

    @Override
    protected void setDayChart() {
        mMarkerView.setInterval(mInterval);
        int count = mHourCount / mInterval + 1;
        List<ILineDataSet> lineDataSets = mSolarChartHelper.getLineDataSets(mChartData, mFlipCombinedChartView, isThree, isDualPowerOpen);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        LineData lineData = new LineData(xVals, lineDataSets);
        data.setData(lineData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    @Override
    protected void setWeekChart() {
        refreshWeek2LifetimeData(mWeeks);
    }

    @Override
    protected void setMonthChart() {
        refreshWeek2LifetimeData(months);

    }

    @Override
    protected void setYearChart() {
        refreshWeek2LifetimeData(mYears);
    }

    @Override
    protected void setLifetimeChart() {
        refreshWeek2LifetimeData(lifetimes);
    }

    @Override
    protected void setFailDayChart() {
        int count = mHourCount / mInterval + 1;
        List<ILineDataSet> lineDataSets = mSolarChartHelper.getLineDataSets(mChartData, mFlipCombinedChartView, isThree, isDualPowerOpen);
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        CombinedData data = new CombinedData(xVals);
        LineData lineData = new LineData(xVals, lineDataSets);
        data.setData(lineData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    @Override
    protected void setFailWeekChart() {
        refreshFailWeek2LifetimeData(mWeeks);
    }

    @Override
    protected void setFailMonthChart() {
        refreshFailWeek2LifetimeData(months);
    }

    @Override
    protected void setFailYearChart() {
        refreshFailWeek2LifetimeData(mYears);
    }

    @Override
    protected void setFailLifetimeChart() {
        refreshFailWeek2LifetimeData(lifetimes);
    }

    private void refreshWeek2LifetimeData(String[] xLabels) {
        mMarkerView.setPlusMinusType(mPlusMinusType);
        mMarkerView.setMultiply(isMultiply);
        mMarkerView.setSelectedPositions(mFlipCombinedChartView.getSelectPositions());
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            yVals.add(mSolarChartHelper.getBarEntry(mFlipCombinedChartView, sonData, xIndex, isThree, isDualPowerOpen));
        }
        BarDataSet barDataSetWeek = new BarDataSet(yVals, "");
        int[] colorsBar = mSolarChartHelper.getBarColors(true, isThree, isDualPowerOpen);
        barDataSetWeek.setColors(colorsBar);
        barDataSetWeek.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSetWeek.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSetWeek.setHighLightColor(getColor(R.color.color_white_03));
        barDataSetWeek.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSetWeek);
        BarData barData = new BarData(xVals, dataSets);
        CombinedData data = new CombinedData(xLabels);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    private void refreshFailWeek2LifetimeData(String[] xLabels) {
        int count = mChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < xLabels.length + 1; i++) {
            xVals.add(xLabels[i % xLabels.length]);
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            List<Float> sonData = mChartData.get(i);
            int xIndex = Math.round(sonData.get(0));
            if (xLabels == lifetimes) {
                int year = ChartDataUtil.getYear(mStartTime, timezone);
                String curYear = String.valueOf(year + xIndex);
                xIndex = getXOffset(curYear, lifetimes);
            }
            mSolarChartHelper.getBarEntry(mFlipCombinedChartView, sonData, xIndex, isThree, isDualPowerOpen);
        }
        BarDataSet barDataSetWeek = new BarDataSet(yVals, "");
        int[] colorsBar = mSolarChartHelper.getBarColors(true, isThree, isDualPowerOpen);
        barDataSetWeek.setColors(colorsBar);
        barDataSetWeek.setDrawValues(false);
        float spacePercent = 100f - xLabels.length * 5f;
        barDataSetWeek.setBarSpacePercent(spacePercent < 10f ? 40f : spacePercent);
        barDataSetWeek.setHighLightColor(getColor(R.color.color_white_03));
        barDataSetWeek.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSetWeek);
        BarData barData = new BarData(xVals, dataSets);
        CombinedData data = new CombinedData(xLabels);
        data.setData(barData);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).setData(data);
        mFlipCombinedChartView.getDisplayCombinedChart(mIndex).invalidate();
    }

    private void setLowVisible(boolean isVisible) {
//        mBinding.esvVal.setRightVisible(isVisible);
    }


    private float getHighVal() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float highVal = 0f;
        List<Float> sumData = getSumData();
        if (CollectionUtil.isListNotEmpty(sumData)) highVal = sumData.get(sumData.size() - 1);
        return highVal;
    }

    private float getSumVal() {
        float sum = 0;
        List<Float> sumData = getSumData();
        for (Float val : sumData) {
            sum = sum + val;
        }
        return mCycleType == CycleType.DAY ? sum * mInterval / 60 : sum;
    }

    private float getLowVal() {
        if (CollectionUtil.isListEmpty(mChartData)) return 0f;
        float lowVal = 0f;
        List<Float> sumData = getSumData();
        if (CollectionUtil.isListNotEmpty(sumData)) lowVal = sumData.get(0);
        return lowVal;
    }

    private List<Float> getSumData() {
        List<Float> sumData = new ArrayList<>();
        if (mFlipCombinedChartView != null) {
            for (int i = 0; i < mChartData.size(); i++) {
                List<Float> sonData = mChartData.get(i);
                float sum = 0;
                if (mBmtType == BmtUtil.BMT_POWER_CORE) {
                    if (isThree) {
                        if (mFlipCombinedChartView.isFilterSelected(0)) {
                            sum = sum + sonData.get(1);
                        }

                        if (mFlipCombinedChartView.isFilterSelected(1)) {
                            sum = sum + sonData.get(2);
                        }

                        if (mFlipCombinedChartView.isFilterSelected(2)) {
                            sum = sum + sonData.get(3);
                        }

                        if (isDualPowerOpen && sonData.size() > 4 && mFlipCombinedChartView.isFilterSelected(3)) {
                            sum = sum + sonData.get(4);
                        }
//                    sum = sum + sonData.get(1) + sonData.get(2) + sonData.get(3);
                    } else {
                        if (mFlipCombinedChartView.isFilterSelected(0)) {
                            sum = sum + sonData.get(1);
                        }

                        if (isDualPowerOpen && sonData.size() > 4 && mFlipCombinedChartView.isFilterSelected(1)) {
                            sum = sum + sonData.get(4);
                        }
                    }
                } else if (mBmtType == BmtUtil.BMT_POWER_STORE){
                    if (sonData.size() > 4 && mFlipCombinedChartView.isFilterSelected(0)) {
                        sum = sum + sonData.get(4);
                    }
                }
                sumData.add(sum);
            }
            Collections.sort(sumData);
        }
        return sumData;
    }
}
