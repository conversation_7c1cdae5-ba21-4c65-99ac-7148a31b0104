package com.dinsafer.module.powerstation.impacts.tab;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.IChartModelController;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.common.utils.DensityUtil;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;

public class RevenueChartModelController implements IChartModelController {
    @Override
    public void initXAxis(Context context, CustomCombinedChartManager customCombinedChartManager,
                          CycleType cycleType, PlusMinusType plusMinusType, int labelsToSkip, XAxisValueFormatter formatter) {
        switch (cycleType) {
            case DAY:
                customCombinedChartManager.initXAxis(false, false, context.getResources().getColor(R.color.color_white_04), 10f,
                        true, context.getResources().getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                        labelsToSkip, formatter);
                break;

            case WEEK:
            case MONTH:
            case YEAR:
            case LIFETIME:
                customCombinedChartManager.initXAxis(false, false, context.getResources().getColor(R.color.color_white_04), 10f,
                        false, context.getResources().getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                        0, formatter);
                break;
        }
    }

    @Override
    public void initYAxis(Context context, CustomCombinedChartManager customCombinedChartManager,
                          CycleType cycleType, PlusMinusType plusMinusType,
                          YAxisValueFormatter yFormatter, boolean isAxisRightEnabled) {
        switch (cycleType) {
            case DAY:
                customCombinedChartManager.initYAxis(true, 10f, context.getResources().getColor(R.color.color_white_03),
                        0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
                         yFormatter, isAxisRightEnabled, 3);
                break;

            case WEEK:
            case MONTH:
            case YEAR:
            case LIFETIME:
                customCombinedChartManager.initYAxis(true, 10f, context.getResources().getColor(R.color.color_white_03),
                        0.5f, false, true, context.getResources().getColor(R.color.color_white_04),
                        yFormatter, isAxisRightEnabled, 5);
                break;
        }
    }

    @Override
    public void initLineChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType) {
        customCombinedChartManager.initLineChartRender(false);
    }

    @Override
    public void initBarChartRender(Context context, CustomCombinedChartManager customCombinedChartManager, CycleType cycleType, PlusMinusType plusMinusType) {
        switch (cycleType) {
            case DAY:

                break;

            case WEEK:
            case MONTH:
            case YEAR:
            case LIFETIME:
                customCombinedChartManager.initBarChartRender(DensityUtil.dp2px(context, 3), context.getResources().getColor(R.color.color_white_03), 1.0f, 2.0f);
                break;
        }
    }
}
