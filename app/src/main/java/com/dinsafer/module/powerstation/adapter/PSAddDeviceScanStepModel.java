package com.dinsafer.module.powerstation.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsAddDeviceScanStepBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.powerstation.settings.network.PSNetworkSettingsFragment;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/2 14:35
 * @description :
 */
public class PSAddDeviceScanStepModel implements BaseBindModel<ItemPsAddDeviceScanStepBinding> {

    private String content;

    public PSAddDeviceScanStepModel(String content) {
        this.content = content;
    }


    @Override
    public int getLayoutID() {
        return R.layout.item_ps_add_device_scan_step;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsAddDeviceScanStepBinding itemPsAddDeviceScanStepBinding) {
        itemPsAddDeviceScanStepBinding.tvOrder.setLocalText((holder.getAdapterPosition() + 1) + ".");
        itemPsAddDeviceScanStepBinding.tvStep.setLocalText(content);
    }
}
