package com.dinsafer.module.powerstation.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.module.powerstation.event.BatteryStatusEvent;
import com.dinsafer.module.powerstation.event.BmtChartDataEvent;
import com.dinsafer.module.powerstation.event.BmtGetFeatureEvent;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Bmt图标缓存信息
 * 由于Device没有缓存数据，首页卡片滑动需要重复读取数据，只能在这里缓存了
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/3/3 17:19
 */
public class BmtGraphicCacheInfo {
    @NonNull
    private final String deviceId;
    @NonNull
    private final String subcategory;
    @NonNull
    private final LinkedHashMap<String, BmtGraphicUpdateEvent> mGraphicUpdateEvents = new LinkedHashMap<>();
    @Nullable
    private BatteryStatusEvent mLastBatteryStatusEvent;
    @Nullable
    private BmtChartDataEvent mLastBmtChartDataEvent;
    @Nullable
    private BmtGetFeatureEvent mLastBmtGetFeatureEvent;

    public BmtGraphicCacheInfo(@NonNull String deviceId, @NonNull String subcategory) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
    }

    @NonNull
    public String getDeviceId() {
        return deviceId;
    }

    public boolean addGraphicUpdateEvent(@NonNull final BmtGraphicUpdateEvent event) {
        final String id = event.getDeviceId();
        final String deviceSub = event.getSubCategory();
        final String cmd = event.getCmd();
        if (!TextUtils.isEmpty(cmd) && !TextUtils.isEmpty(id) && id.equals(deviceId) && deviceSub.equals(subcategory)) {
            mGraphicUpdateEvents.put(cmd, event);
            return true;
        }
        return false;
    }

    public void setLastBmtGetFeatureEvent(@NonNull BmtGetFeatureEvent event) {
        final String id = event.getDeviceId();
        if (!TextUtils.isEmpty(id) && id.equals(deviceId) && event.getSubcategory().equals(subcategory)) {
            this.mLastBmtGetFeatureEvent = event;
        }
    }

    @Nullable
    public BmtGetFeatureEvent getLastBmtGetFeatureEvent() {
        return mLastBmtGetFeatureEvent;
    }

    public void setLastChartDataEvent(@NonNull BmtChartDataEvent event) {
        final String id = event.getDeviceId();
        if (!TextUtils.isEmpty(id) && id.equals(deviceId) && event.getSubcategory().equals(subcategory)) {
            this.mLastBmtChartDataEvent = event;
        }
    }
    @Nullable
    public BmtChartDataEvent getLastBmtChartDataEvent() {
        return mLastBmtChartDataEvent;
    }

    public void setLastBatteryStatusEvent(@NonNull BatteryStatusEvent event) {
        final String id = event.getDeviceId();
        if (!TextUtils.isEmpty(id) && id.equals(deviceId) && event.getSubcategory().equals(subcategory)) {
            this.mLastBatteryStatusEvent = event;
        }
    }

    @Nullable
    public BatteryStatusEvent getLastBatteryStatusEvent() {
        return mLastBatteryStatusEvent;
    }

    @Nullable
    public BmtGraphicUpdateEvent getBmtGraphicUpdateEventByCmd(final String cmd) {
        if (!TextUtils.isEmpty(cmd)) {
            return mGraphicUpdateEvents.get(cmd);
        }
        return null;
    }

    @NonNull
    public List<BmtGraphicUpdateEvent> getBmtGraphicUpdateEvents() {
        final List<BmtGraphicUpdateEvent> result = new ArrayList<>(mGraphicUpdateEvents.size());
        result.addAll(mGraphicUpdateEvents.values());
        return result;
    }

    public void reset() {
        mLastBmtGetFeatureEvent = null;
        mLastBmtChartDataEvent = null;
        mLastBatteryStatusEvent = null;
        mGraphicUpdateEvents.clear();
    }
}
