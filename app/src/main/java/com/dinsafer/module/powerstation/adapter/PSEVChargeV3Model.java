package com.dinsafer.module.powerstation.adapter;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsEvChargeV3Binding;
import com.dinsafer.module.powerstation.bean.PSEVChargeV2Bean;
import com.dinsafer.module.powerstation.bean.PSEVChargeV3Bean;
import com.dinsafer.module.settting.ui.FeedBackFragment;
import com.dinsafer.ui.progressbar.CustomSeekBar;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;

public class PSEVChargeV3Model extends BindModel<ItemPsEvChargeV3Binding> {

    private Context mContext;
    private PSEVChargeV3Bean evChargeBean;

    public PSEVChargeV3Model(Context context, PSEVChargeV3Bean evChargeBean) {
        super(context);
        mContext = context;
        this.evChargeBean = evChargeBean;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_ev_charge_v3;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsEvChargeV3Binding binding) {
        if (evChargeBean != null) {
            int status = evChargeBean.getStatus();
            int type = evChargeBean.getType();
            boolean isSelected = evChargeBean.isSelected();
            boolean isEdit = isSelected && status != 1;
            binding.tvTitle.setLocalText(evChargeBean.getTitle());
            binding.tvSubtitle.setLocalText(evChargeBean.getSubTitle());
            String operateText = "";
            if (type == PSEVChargeV2Bean.SCHEDULE_CHARGE) {
                operateText = mContext.getString(R.string.go_setting);
            } else if (type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) {
                String oldStr = mContext.getString(R.string.hashtag_charge_amount);
                String chargeVal = evChargeBean.getValue() + "kWh";
                String subtitle = Local.s(evChargeBean.getSubTitle()).replace(oldStr, chargeVal);
                if(isEdit) {
                    binding.tvSubtitle.setLocalText(subtitle);
                } else {
                    int index = subtitle.indexOf(chargeVal);
                    binding.tvSubtitle.setMovementMethod(LinkMovementMethod.getInstance());
                    binding.tvSubtitle.setText(StringUtil.getSpannableString(mContext.getResources().getColor(R.color.color_brand_text),
                            subtitle, index, index+chargeVal.length(), false, null));
                }
                binding.customSeekBar.setProgress(evChargeBean.getValue() * 1.0f);
            } else if (type == PSEVChargeV2Bean.LOWER_UTILITY_RATE) {
                String oldStr = mContext.getString(R.string.hashtag_c2);
                int value = evChargeBean.getValue();
                String valStr = value > 0 ? ("+" + value + "%") : (value + "%");
                String subtitle = Local.s(evChargeBean.getSubTitle()).replace(oldStr, valStr);
                binding.tvSubtitle.setText(subtitle);
            }
            binding.tvOperate.setVisibility(!TextUtils.isEmpty(operateText) ?
                    View.VISIBLE : View.GONE);
            binding.tvEdit.setVisibility(type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED && status == 1 && isSelected ?
                    View.VISIBLE : View.GONE);
            binding.customSeekBar.setVisibility(View.GONE);
            binding.scaleRulerView.setVisibility(View.GONE);
            binding.customSeekBar.setSeekBarChangeListener(new CustomSeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(CustomSeekBar customSeekBar, int progress, float progressFloat, boolean fromUser) {
                    evChargeBean.setTempValue(binding.customSeekBar.getProgress());
                }
            });
            binding.tvOperate.setLocalText(operateText);
            binding.llStatus.setBackgroundResource(status == 0 ? R.drawable.shape_brand_light_03_right_r16 :
                    R.drawable.shape_brand_primary_right_r16);

            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) binding.clContent.getLayoutParams();
            if (evChargeBean.isReset()) {
                evChargeBean.setReset(false);
                layoutParams.setMargins(layoutParams.leftMargin,
                        layoutParams.topMargin,
                        0,
                        layoutParams.bottomMargin);
                binding.clContent.requestLayout();
                binding.viewSelected.setAlpha(isSelected ? 1f : 0f);
                binding.viewNormal.setAlpha(isSelected ? 0f : 1f);
            } else {
                if (!evChargeBean.isSameSelected()) {
                    int startMargin = isEdit ? 5 : 0;
                    int endMargin = isEdit ? 0 : 5;
                    ValueAnimator marginAnimator = ValueAnimator.ofInt(startMargin, endMargin);
                    marginAnimator.addUpdateListener(animation -> {
                        int val = (Integer) animation.getAnimatedValue();
                        if (evChargeBean.getStatusHelper() == 1) {
                            if (isSelected) {
                                if (binding.viewSelected.getAlpha() < 1.0f) {
                                    binding.viewSelected.setAlpha(val / 5f);
                                    binding.viewNormal.setAlpha(1f - val / 5f);
                                }
                            } else {
                                binding.viewSelected.setAlpha(1f - val / 5f);
                                binding.viewNormal.setAlpha(val / 5f);
                            }

                        } else {
                            binding.viewSelected.setAlpha(1 - val / 5f);
                            binding.viewNormal.setAlpha(val / 5f);
                        }
                        int marginRight = status == 1 ? 0 : DensityUtil.dp2px(mContext, 60) - DensityUtil.dp2px(mContext, val * 12);
                        layoutParams.setMargins(layoutParams.leftMargin,
                                layoutParams.topMargin,
                                marginRight,
                                layoutParams.bottomMargin);
                        binding.clContent.requestLayout();
                    });
                    marginAnimator.addListener(new Animator.AnimatorListener() {
                        @Override
                        public void onAnimationStart(Animator animator) {
                            if (isEdit) {
                                if (type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) {
                                    binding.customSeekBar.setVisibility(View.VISIBLE);
                                    binding.scaleRulerView.setVisibility(View.VISIBLE);
                                } else {
                                    binding.tvOperate.setVisibility(View.VISIBLE);
                                }
                            }
                        }

                        @Override
                        public void onAnimationEnd(Animator animator) {
                            if (!isEdit && status == -1) {
                                if (type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) {
                                    binding.customSeekBar.setVisibility(View.GONE);
                                    binding.scaleRulerView.setVisibility(View.GONE);
                                } else {
                                    binding.tvOperate.setVisibility(View.GONE);
                                }
                            }
                        }

                        @Override
                        public void onAnimationCancel(Animator animator) {

                        }

                        @Override
                        public void onAnimationRepeat(Animator animator) {

                        }
                    });
                    evChargeBean.setSameSelected(true);
                    marginAnimator.setDuration(200);
                    marginAnimator.start();
                }
            }
            binding.tvOperate.setVisibility((type == PSEVChargeV2Bean.SCHEDULE_CHARGE ||
                    type == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) && isSelected ?
                    View.VISIBLE : View.GONE);
            binding.tvApplied.setVisibility(status == 1 ? View.VISIBLE : View.GONE);
            binding.ivStatus.setVisibility(status == 0 ? View.GONE : View.VISIBLE);
            binding.lavLoading.setVisibility(status == 0 ? View.VISIBLE : View.GONE);
        }
    }

    public PSEVChargeV3Bean getEvChargeBean() {
        return evChargeBean;
    }

    public void setEvChargeBean(PSEVChargeV3Bean evChargeBean) {
        this.evChargeBean = evChargeBean;
    }
}
