package com.dinsafer.module.powerstation.settings.network;

import android.os.Bundle;
import android.text.InputType;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsConfigureNetworkSettingsBinding;
import com.dinsafer.dinsdk.BmtBinderWrapper;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.device.PSBleBaseBindFragment;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;
import com.jakewharton.rxbinding.view.RxView;
import com.jakewharton.rxbinding.widget.RxTextView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import io.reactivex.annotations.NonNull;
import rx.Observable;
import rx.functions.Action1;
import rx.functions.Func2;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 16:39
 * @description :
 */
public class PSConfigureNetworkSettingsFragment extends PSBleBaseBindFragment<FragmentPsConfigureNetworkSettingsBinding> {

    private static final String KEY_SSID = "ssid";
    private static final String KEY_RSSI = "rssi";
    private static final String KEY_OTHER = "is_other";

    private int mRssi;
    private boolean mOther;

    public static PSConfigureNetworkSettingsFragment newInstanceForScanAdd(String deviceId
            , String model, String ssid, int rssi, boolean other) {
        return newInstance(KEY_FROM_SCAN_ADD, deviceId, model, ssid, rssi, other);
    }

    public static PSConfigureNetworkSettingsFragment newInstanceForChangeNetwork(String deviceId
            , String ssid, int rssi, boolean other) {
        return newInstance(KEY_FROM_CHANGE_NETWORK, deviceId, null,  ssid, rssi, other);
    }

    public static PSConfigureNetworkSettingsFragment newInstance(int from, String deviceId, String model
            ,  String ssid, int rssi, boolean other) {
        PSConfigureNetworkSettingsFragment fragment = new PSConfigureNetworkSettingsFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_FROM, from);
        bundle.putString(KEY_SSID, ssid);
        bundle.putString(KEY_DEVICE_ID, deviceId);
        bundle.putInt(KEY_RSSI, rssi);
        bundle.putBoolean(KEY_OTHER, other);
        if (model != null) {
            bundle.putString(PSKeyConstant.KEY_MODEL, model);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_configure_network_settings;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.setClick(new OnClickHandler());
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_settings_network_configure_network_settings));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.ivWifiRssi.setOnClickListener(v -> showWifiRssiDialog());
        initParams();

        if (mOther) {
            mBinding.etName.setClickable(true);
            mBinding.etName.setPressed(true);
            mBinding.etName.setFocusable(true);
        } else {
            mBinding.etName.setPressed(false);
            mBinding.etName.setFocusable(false);
            mBinding.etName.setClickable(false);
            mBinding.etName.setText(wifiSSid);
        }
        updateWifiRssiIcon();

        Observable<CharSequence> ObservableName = RxTextView.textChanges(mBinding.etName);
        Observable<CharSequence> ObservablePassword = RxTextView.textChanges(mBinding.etPassword);

        Observable.combineLatest(ObservableName, ObservablePassword, new Func2<CharSequence, CharSequence, Boolean>() {
            @Override
            public Boolean call(CharSequence wifiName, CharSequence wifiPassword) {
                return !TextUtils.isEmpty(wifiName.toString())
                        && !TextUtils.isEmpty(wifiPassword.toString());
            }
        }).subscribe(new Action1<Boolean>() {
            @Override
            public void call(Boolean aBoolean) {
                if (aBoolean) {
                    mBinding.lcbConfirm.setAlpha(1f);
                } else {
                    mBinding.lcbConfirm.setAlpha(0.3f);
                }
                RxView.enabled(mBinding.lcbConfirm).call(aBoolean);
            }
        });
    }

    private void showWifiRssiDialog() {
        final String pre = Local.s(getString(R.string.wifi_signal));
        final String content = pre + ":" + mRssi;
        AlertDialog.createBuilder(getDelegateActivity())
                .setContent(content)
                .setOk(Local.s(getString(R.string.got_it)))
                .preBuilder()
                .show();
    }

    /**
     * 更新WiFi信号图标
     */
    private void updateWifiRssiIcon() {
        final int iconResId = BmtUtil.getBleWifiRssiIconResId(mRssi);
        if (0 == iconResId) {
            mBinding.ivWifiRssi.setVisibility(View.INVISIBLE);
        } else {
            mBinding.ivWifiRssi.setVisibility(View.VISIBLE);
            mBinding.ivWifiRssi.setImageResource(iconResId);
        }
    }

    @Override
    protected void onBinderReady(@NonNull BmtBinderWrapper pluginBinder) {
        mBinder.addBindCallBack(this);
        mBinding.etName.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ipc_wifi_set_name)));
        mBinding.etPassword.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ap_step_wifi_pass_hint)));
        mBinding.etPassword.setInputType(129);

        mBinding.wifiRemember.setChecked(true);
        mBinding.wifiRemember.setText(Local.s(getResources().getString(R.string.remember_password)));

        if (wifiSSid.equals(DBUtil.SGet(DBKey.REMEMBER_WIFI))) {
            mBinding.etPassword.setText(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
        }
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mFrom = bundle.getInt(KEY_FROM);
        wifiSSid = getArguments().getString("ssid");
        deviceID = getArguments().getString("deviceId");
        mRssi = getArguments().getInt(KEY_RSSI);
        mOther = getArguments().getBoolean(KEY_OTHER);
        mModel = bundle.getString(PSKeyConstant.KEY_MODEL);
        mBinding.tvTip.setLocalText(getString(R.string.ps_device_only_supported));
    }

    public void toSave() {
        final String pwd1 = mBinding.etPassword.getText().toString();
        if (mBinding.etName.getText().toString().length() > 0) {
            showTimeOutLoadinFramgmentWithCallBack(mConfigTimeout, this);
            wifiSSid = mBinding.etName.getText().toString();
            wifiPassword = pwd1;
            mBinder.setSsid(wifiSSid);
            mBinder.setSsidPassword(wifiPassword);
            mBinder.setSsidAuth(true);
            mBinder.bindDevice(null);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mBinder) {
            mBinder.removeBindCallBack(this);
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void onPreBindSuccess() {
        super.onPreBindSuccess();
        if (mBinding.wifiRemember.isChecked()) {
            DBUtil.SPut(DBKey.REMEMBER_WIFI, mBinding.etName.getText().toString());
            DBUtil.SPut(DBKey.REMEMBER_WIFI_PASSWORD, mBinding.etPassword.getText().toString());
        } else {
            DBUtil.Delete(DBKey.REMEMBER_WIFI);
            DBUtil.Delete(DBKey.REMEMBER_WIFI_PASSWORD);
        }
    }

    public void toClose() {
        removeSelf();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(FinishAddBmtEvent event) {
        removeSelf();
    }

    public class OnClickHandler {
        public void passwordVisible() {
            if (mBinding.etPassword.getInputType() == 129) {
                mBinding.ivPasswordVisible.setImageResource(R.drawable.icon_form_show);
                mBinding.etPassword.setInputType(InputType.TYPE_CLASS_TEXT);

            } else {
                mBinding.ivPasswordVisible.setImageResource(R.drawable.icon_form_hide);
                mBinding.etPassword.setInputType(129);
            }
            mBinding.etPassword.setSelection(mBinding.etPassword.getText().toString().length());
        }

        public void confirm() {
//            if (mFrom == 0) {
//                getDelegateActivity().addCommonFragment(PSConfigureNetworkResultFragment.newInstance());
//            } else {
//                toSave();
//            }
            toSave();
        }
    }
}
