package com.dinsafer.module.powerstation.utils.comparator;

import java.util.Comparator;

public abstract class BaseSearchComparator<T> implements Comparator<T> {

    private String searchKeyword;

    public BaseSearchComparator(String searchKeyword) {
        this.searchKeyword = searchKeyword;
    }

    public int compareResult(String s1, String s2) {
        int indexInS1 = s1.indexOf(searchKeyword.toLowerCase());
        int indexInS2 = s2.indexOf(searchKeyword.toLowerCase());
        if (indexInS1 < indexInS2) {
            return -1;
        } else if (indexInS1 > indexInS2) {
            return 1;
        } else {
            return 0;
        }
    }

    public String getSearchKeyword() {
        return searchKeyword;
    }

    public void setSearchKeyword(String searchKeyword) {
        this.searchKeyword = searchKeyword;
    }
}
