package com.dinsafer.module.powerstation.electricity.chart.render;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;

import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.util.CollectionUtil;
import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.data.DataSet;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.dataprovider.LineDataProvider;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.renderer.LineChartRenderer;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.ViewPortHandler;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 不同区间不同颜色折线图渲染器
 */
public class SectionLineChartRender extends LineChartRenderer {

    private Paint mHighlightCirclePaint;
    private float[] mLineBuffer = new float[4];
    private int mInCircleColor;
    private int mOutCircleColor;
    private boolean isMultiply;
    private LinearGradient mTextureGradient;
    private Path mTexturePath;
    private List<Path> mFilledPathList = new ArrayList<>();

    public SectionLineChartRender(LineDataProvider chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {
        super(chart, animator, viewPortHandler);
        mHighlightCirclePaint = new Paint();
        mHighlightCirclePaint.setAntiAlias(true);
        mHighlightCirclePaint.setStyle(Paint.Style.FILL);
    }

    @Override
    public void drawData(Canvas c) {
        mFilledPathList.clear();
        int width = (int) mViewPortHandler.getChartWidth();
        int height = (int) mViewPortHandler.getChartHeight();

        if (mDrawBitmap == null
                || (mDrawBitmap.get().getWidth() != width)
                || (mDrawBitmap.get().getHeight() != height)) {

            if (width > 0 && height > 0) {

                mDrawBitmap = new WeakReference<Bitmap>(Bitmap.createBitmap(width, height, mBitmapConfig));
                mBitmapCanvas = new Canvas(mDrawBitmap.get());
            } else
                return;
        }

        mDrawBitmap.get().eraseColor(Color.TRANSPARENT);

        LineData lineData = mChart.getLineData();

        for (int i = lineData.getDataSets().size() - 1; i >= 0; i--) {
            ILineDataSet set = lineData.getDataSets().get(i);
            ILineDataSet lastSet = i > 0 ? lineData.getDataSets().get(i - 1) : null;
            if (set.isVisible() && set.getEntryCount() > 0)
                drawDataSetHasLast(c, set, lastSet);
        }

//        for (ILineDataSet set : lineData.getDataSets()) {
//
//            if (set.isVisible() && set.getEntryCount() > 0)
//                drawDataSet(c, set);
//        }
        c.drawBitmap(mDrawBitmap.get(), 0, 0, mRenderPaint);
    }

    //    @Override
    protected void drawDataSetHasLast(Canvas c, ILineDataSet dataSet, ILineDataSet lastDataSet) {
        if (dataSet.getEntryCount() < 1)
            return;

        mRenderPaint.setStrokeWidth(dataSet.getLineWidth());
        mRenderPaint.setPathEffect(dataSet.getDashPathEffect());

        switch (dataSet.getMode()) {
            default:
            case LINEAR:
            case STEPPED:
                if (dataSet instanceof SectionLineDataSet
                        && ((SectionLineDataSet) dataSet).isSection()) {
                    drawSectionLineLinear(c, (SectionLineDataSet) dataSet);
                } else {
                    mRenderPaint.setShader(null);
                    drawLinearHasLast(c, dataSet, lastDataSet);
                }
                break;

            case CUBIC_BEZIER:
                if (dataSet instanceof SectionLineDataSet
                        && ((SectionLineDataSet) dataSet).isSection()) {
                    drawSectionCubicBezier(c, (SectionLineDataSet) dataSet);
                } else {
                    mRenderPaint.setShader(null);
                    drawCubicBezier(c, dataSet);
                }
                break;

            case HORIZONTAL_BEZIER:
                if (dataSet instanceof SectionLineDataSet
                        && ((SectionLineDataSet) dataSet).isSection()) {
                    drawSectionHorizontalBezier(c, (SectionLineDataSet) dataSet);
                } else {
                    mRenderPaint.setShader(null);
                    drawHorizontalBezier(c, dataSet);
                }
                break;
        }

        mRenderPaint.setPathEffect(null);
    }

    @Override
    protected void drawCubicFill(Canvas c, ILineDataSet dataSet, Path spline, Transformer trans, int from, int to) {
        if (to - from <= 1)
            return;

        float fillMin = dataSet.getFillFormatter()
                .getFillLinePosition(dataSet, mChart);

        // Take the from/to xIndex from the entries themselves,
        // so missing entries won't screw up the filling.
        // What we need to draw is line from points of the xIndexes - not arbitrary entry indexes!

        final Entry toEntry = dataSet.getEntryForIndex(to - 1);
        final Entry fromEntry = dataSet.getEntryForIndex(from);
        final float xTo = toEntry == null ? 0 : toEntry.getXIndex();
        final float xFrom = fromEntry == null ? 0 : fromEntry.getXIndex();

        spline.lineTo(xTo, fillMin);
        spline.lineTo(xFrom, fillMin);
        spline.close();

        trans.pathValueToPixel(spline);

        final Drawable drawable = dataSet.getFillDrawable();
        if (drawable != null) {
            drawFilledPath(c, spline, drawable);
        } else {
            if (dataSet instanceof SectionLineDataSet && ((SectionLineDataSet) dataSet).isSection()) {
                drawSectionFilledPath(c, spline, (SectionLineDataSet) dataSet,
                        dataSet.getFillColor(), dataSet.getFillAlpha());
            } else {
                drawFilledPath(c, spline, dataSet.getFillColor(), dataSet.getFillAlpha());
            }
        }
    }

    //    @Override
    protected void drawLinearHasLast(Canvas c, ILineDataSet dataSet, ILineDataSet lastDataSet) {
        int entryCount = dataSet.getEntryCount();

        final boolean isDrawSteppedEnabled = dataSet.isDrawSteppedEnabled();
        final int pointsPerEntryPair = isDrawSteppedEnabled ? 4 : 2;

        Transformer trans = mChart.getTransformer(dataSet.getAxisDependency());

        float phaseX = Math.max(0.f, Math.min(1.f, mAnimator.getPhaseX()));
        float phaseY = mAnimator.getPhaseY();

        mRenderPaint.setStyle(Paint.Style.STROKE);

        Canvas canvas = null;

        // if the data-set is dashed, draw on bitmap-canvas
        if (dataSet.isDashedLineEnabled()) {
            canvas = mBitmapCanvas;
        } else {
            canvas = c;
        }

        Entry entryFrom = dataSet.getEntryForXIndex((mMinX < 0) ? 0 : mMinX, DataSet.Rounding.DOWN);
        Entry entryTo = dataSet.getEntryForXIndex(mMaxX, DataSet.Rounding.UP);

        int diff = (entryFrom == entryTo) ? 1 : 0;
        int minx = Math.max(dataSet.getEntryIndex(entryFrom) - diff, 0);
        int maxx = Math.min(Math.max(minx + 2, dataSet.getEntryIndex(entryTo) + 1), entryCount);

        final int count = (int) (Math.ceil((float) (maxx - minx) * phaseX + (float) (minx)));

        // more than 1 color
        if (dataSet.getColors().size() > 1) {

            if (mLineBuffer.length != pointsPerEntryPair * 2)
                mLineBuffer = new float[pointsPerEntryPair * 2];

            for (int j = minx;
                 j < count;
                 j++) {

                if (count > 1 && j == count - 1) {
                    // Last point, we have already drawn a line to this point
                    break;
                }

                Entry e = dataSet.getEntryForIndex(j);
                if (e == null) continue;

                mLineBuffer[0] = e.getXIndex();
                mLineBuffer[1] = e.getVal() * phaseY;

                if (j + 1 < count) {

                    e = dataSet.getEntryForIndex(j + 1);

                    if (e == null) break;

                    if (isDrawSteppedEnabled) {
                        mLineBuffer[2] = e.getXIndex();
                        mLineBuffer[3] = mLineBuffer[1];
                        mLineBuffer[4] = mLineBuffer[2];
                        mLineBuffer[5] = mLineBuffer[3];
                        mLineBuffer[6] = e.getXIndex();
                        mLineBuffer[7] = e.getVal() * phaseY;
                    } else {
                        mLineBuffer[2] = e.getXIndex();
                        mLineBuffer[3] = e.getVal() * phaseY;
                    }

                } else {
                    mLineBuffer[2] = mLineBuffer[0];
                    mLineBuffer[3] = mLineBuffer[1];
                }

                trans.pointValuesToPixel(mLineBuffer);

                if (!mViewPortHandler.isInBoundsRight(mLineBuffer[0]))
                    break;

                // make sure the lines don't do shitty things outside
                // bounds
                if (!mViewPortHandler.isInBoundsLeft(mLineBuffer[2])
                        || (!mViewPortHandler.isInBoundsTop(mLineBuffer[1]) && !mViewPortHandler
                        .isInBoundsBottom(mLineBuffer[3]))
                        || (!mViewPortHandler.isInBoundsTop(mLineBuffer[1]) && !mViewPortHandler
                        .isInBoundsBottom(mLineBuffer[3])))
                    continue;

                // get the color that is set for this line-segment
                mRenderPaint.setColor(dataSet.getColor(j));

                canvas.drawLines(mLineBuffer, 0, pointsPerEntryPair * 2, mRenderPaint);


            }

        } else { // only one color per dataset
            if (((SectionLineDataSet)dataSet).getDataType().isEmpty()) {
                if (mLineBuffer.length != Math.max((entryCount - 1) * pointsPerEntryPair, pointsPerEntryPair) * 2)
                    mLineBuffer = new float[Math.max((entryCount - 1) * pointsPerEntryPair, pointsPerEntryPair) * 2];

                Entry e1, e2;

                e1 = dataSet.getEntryForIndex(minx);

                if (e1 != null) {

                    int j = 0;
                    int xInit = count > 1 ? minx + 1 : minx;
                    for (int x = xInit; x < count; x++) {

                        e1 = dataSet.getEntryForIndex(x == 0 ? 0 : (x - 1));
                        e2 = dataSet.getEntryForIndex(x);

                        if (e1 == null || e2 == null) continue;
                        mLineBuffer[j++] = e1.getXIndex();
                        mLineBuffer[j++] = e1.getVal() * phaseY;
                        if (isDrawSteppedEnabled) {
                            mLineBuffer[j++] = e2.getXIndex();
                            mLineBuffer[j++] = e1.getVal() * phaseY;
                            mLineBuffer[j++] = e2.getXIndex();
                            mLineBuffer[j++] = e1.getVal() * phaseY;
                        }
                        mLineBuffer[j++] = e2.getXIndex();
                        mLineBuffer[j++] = e2.getVal() * phaseY;
                    }

                    if (j > 0) {
                        trans.pointValuesToPixel(mLineBuffer);
                        final int size =
                                Math.max((count - minx - 1) * pointsPerEntryPair, pointsPerEntryPair) *
                                        2;

                        mRenderPaint.setColor(dataSet.getColor());
                        canvas.drawLines(mLineBuffer, 0, size,
                                mRenderPaint);
                    }
                }
                mRenderPaint.setPathEffect(null);
                if (true) {
                    // 打开蒙版染色功能进行测试
                    drawWithThresholdMask(canvas, dataSet, minx, count, pointsPerEntryPair,
                            isDrawSteppedEnabled, phaseY, trans);
                } else {
                    // if drawing filled is enabled
                    if (dataSet.isDrawFilledEnabled() && entryCount > 0) {
                        drawLinearFillHasLast(c, dataSet, minx, maxx, trans, lastDataSet);
                    }
                }
            } else {
                drawWithThresholdMask(canvas, dataSet, minx, count, pointsPerEntryPair,
                        isDrawSteppedEnabled, phaseY, trans);
            }
        }

    }
    private void drawWithThresholdMask(Canvas canvas, ILineDataSet dataSet, int minx, int count,
                                       int pointsPerEntryPair, boolean isDrawSteppedEnabled,
                                       float phaseY, Transformer trans) {

        // 只在第一个数据集时执行完整的分层绘制
        if (isFirstDataSetForGridImported(dataSet)) {
            drawGridImportedLayersWithNewRenderer(canvas, minx, count, pointsPerEntryPair, isDrawSteppedEnabled, phaseY, trans);
        }
    }

    private void drawGridImportedLayersWithNewRenderer(Canvas canvas, int minx, int count, int pointsPerEntryPair,
                                                       boolean isDrawSteppedEnabled, float phaseY, Transformer trans) {

        // 获取Grid-imported渲染器单例实例
        GridImportedLineChartRender gridRenderer = GridImportedLineChartRender.getInstance();

        // 设置LineData用于自动查找数据集
        gridRenderer.setLineData(mChart.getLineData());

        // 设置Context用于获取颜色资源
        gridRenderer.setContext(null);

        // 完全委托给新类处理
        gridRenderer.drawGridImportedLayersAuto(canvas, minx, count, pointsPerEntryPair, isDrawSteppedEnabled, phaseY, trans);
    }

    /**
     * 判断是否为第一个数据集（Grid-imported版本）
     */
    private boolean isFirstDataSetForGridImported(ILineDataSet dataSet) {
        // 获取Grid-imported渲染器单例实例来判断
        GridImportedLineChartRender gridRenderer = GridImportedLineChartRender.getInstance();
        gridRenderer.setLineData(mChart.getLineData());
        return gridRenderer.isFirstDataSet(dataSet);
    }

    @Override
    protected void drawLinearFill(Canvas c, ILineDataSet dataSet, int minx, int maxx, Transformer trans) {
        Path filled = generateFilledPath(
                dataSet, minx, maxx);
        trans.pathValueToPixel(filled);
        if (CollectionUtil.isListNotEmpty(mFilledPathList)) {
            for (Path path : mFilledPathList) {
                filled.op(path, Path.Op.DIFFERENCE);
            }
        }
        mFilledPathList.add(filled);

        final Drawable drawable = dataSet.getFillDrawable();
        if (drawable != null) {
            drawFilledPath(c, filled, drawable);
        } else {

            if (dataSet instanceof SectionLineDataSet && ((SectionLineDataSet) dataSet).isSection()) {
                drawSectionFilledPath(c, filled, (SectionLineDataSet) dataSet,
                        dataSet.getFillColor(), dataSet.getFillAlpha());
            } else {
                drawFilledPath(c, filled, dataSet.getFillColor(), dataSet.getFillAlpha());
            }
        }
    }

    protected void drawLinearFillHasLast(Canvas c, ILineDataSet dataSet, int minx, int maxx, Transformer trans, ILineDataSet lastDataSet) {
        Path filled = generateFilledPathHasLast(
                dataSet, minx, maxx, lastDataSet);
        trans.pathValueToPixel(filled);

        final Drawable drawable = dataSet.getFillDrawable();
        if (drawable != null) {
            drawFilledPath(c, filled, drawable);
        } else {

            if (dataSet instanceof SectionLineDataSet && ((SectionLineDataSet) dataSet).isSection()) {
                drawSectionFilledPath(c, filled, (SectionLineDataSet) dataSet,
                        dataSet.getFillColor(), dataSet.getFillAlpha());
            } else {
                drawFilledPath(c, filled, dataSet.getFillColor(), dataSet.getFillAlpha());
            }
        }
    }

    @Override
    public void drawHighlighted(Canvas c, Highlight[] indices) {
        if (isMultiply) return;
        LineData lineData = mChart.getLineData();
        for (Highlight high : indices) {

            final int minDataSetIndex = high.getDataSetIndex() == -1
                    ? 0
                    : high.getDataSetIndex();
            final int maxDataSetIndex = high.getDataSetIndex() == -1
                    ? lineData.getDataSetCount()
                    : (high.getDataSetIndex() + 1);
            if (maxDataSetIndex - minDataSetIndex < 1) continue;

            for (int dataSetIndex = minDataSetIndex;
                 dataSetIndex < maxDataSetIndex;
                 dataSetIndex++) {

                ILineDataSet set = lineData.getDataSetByIndex(dataSetIndex);

                if (set == null || !set.isHighlightEnabled())
                    continue;

                int xIndex = high.getXIndex(); // get the
                // x-position

                if (xIndex > mChart.getXChartMax() * mAnimator.getPhaseX())
                    continue;

                final float yVal = set.getYValForXIndex(xIndex);
                if (Float.isNaN(yVal))
                    continue;

                float y = yVal * mAnimator.getPhaseY(); // get
                // the
                // y-position

                float[] pts = new float[]{
                        xIndex, y
                };

                mChart.getTransformer(set.getAxisDependency()).pointValuesToPixel(pts);

                // draw the lines
                drawHighlightLines(c, pts, set);
                mHighlightCirclePaint.setColor(mInCircleColor);
                c.drawCircle(pts[0], mViewPortHandler.contentTop(), 10, mHighlightCirclePaint);
                mHighlightCirclePaint.setColor(mOutCircleColor);
                c.drawCircle(pts[0], mViewPortHandler.contentTop(), 18, mHighlightCirclePaint);
                break;
            }
            break;
        }
    }

    public void drawSectionLineLinear(Canvas c, SectionLineDataSet dataSet) {
        int entryCount = dataSet.getEntryCount();

        final boolean isDrawSteppedEnabled = dataSet.isDrawSteppedEnabled();
        final int pointsPerEntryPair = isDrawSteppedEnabled ? 4 : 2;

        Transformer trans = mChart.getTransformer(dataSet.getAxisDependency());

        float phaseX = Math.max(0.f, Math.min(1.f, mAnimator.getPhaseX()));
        float phaseY = mAnimator.getPhaseY();

        mRenderPaint.setStyle(Paint.Style.STROKE);

        Canvas canvas = null;

        // if the data-set is dashed, draw on bitmap-canvas
        if (dataSet.isDashedLineEnabled()) {
            canvas = mBitmapCanvas;
        } else {
            canvas = c;
        }

        Entry entryFrom = dataSet.getEntryForXIndex((mMinX < 0) ? 0 : mMinX, DataSet.Rounding.DOWN);
        Entry entryTo = dataSet.getEntryForXIndex(mMaxX, DataSet.Rounding.UP);

        int diff = (entryFrom == entryTo) ? 1 : 0;
        int minx = Math.max(dataSet.getEntryIndex(entryFrom) - diff, 0);
        int maxx = Math.min(Math.max(minx + 2, dataSet.getEntryIndex(entryTo) + 1), entryCount);

        final int count = (int) (Math.ceil((float) (maxx - minx) * phaseX + (float) (minx)));

        if (mLineBuffer.length != Math.max((entryCount - 1) * pointsPerEntryPair, pointsPerEntryPair) * 2)
            mLineBuffer = new float[Math.max((entryCount - 1) * pointsPerEntryPair, pointsPerEntryPair) * 2];

        Entry e1, e2;

        e1 = dataSet.getEntryForIndex(minx);

        if (e1 != null) {

            int j = 0;
            for (int x = count > 1 ? minx + 1 : minx; x < count; x++) {

                e1 = dataSet.getEntryForIndex(x == 0 ? 0 : (x - 1));
                e2 = dataSet.getEntryForIndex(x);

                if (e1 == null || e2 == null) continue;

                mLineBuffer[j++] = e1.getXIndex();
                mLineBuffer[j++] = e1.getVal() * phaseY;

                if (isDrawSteppedEnabled) {
                    mLineBuffer[j++] = e2.getXIndex();
                    mLineBuffer[j++] = e1.getVal() * phaseY;
                    mLineBuffer[j++] = e2.getXIndex();
                    mLineBuffer[j++] = e1.getVal() * phaseY;
                }

                mLineBuffer[j++] = e2.getXIndex();
                mLineBuffer[j++] = e2.getVal() * phaseY;
            }

            if (j > 0) {
                trans.pointValuesToPixel(mLineBuffer);

                final int size =
                        Math.max((count - minx - 1) * pointsPerEntryPair, pointsPerEntryPair) *
                                2;

                mRenderPaint.setColor(dataSet.getColor());
                mRenderPaint.setShader(new LinearGradient(0, mViewPortHandler.getContentRect().top,
                        0, mViewPortHandler.getContentRect().bottom, dataSet.getGradientColors(),
                        dataSet.getGradientPosition(), Shader.TileMode.CLAMP));
                canvas.drawLines(mLineBuffer, 0, size,
                        mRenderPaint);
                mRenderPaint.setAlpha(100);
            }
        }
        // if drawing filled is enabled
        if (dataSet.isDrawFilledEnabled() && entryCount > 0) {
            drawLinearFill(c, dataSet, minx, maxx, trans);
        }
    }

    public void drawSectionCubicBezier(Canvas c, SectionLineDataSet dataSet) {

        Transformer trans = mChart.getTransformer(dataSet.getAxisDependency());

        int entryCount = dataSet.getEntryCount();

        Entry entryFrom = dataSet.getEntryForXIndex((mMinX < 0) ? 0 : mMinX, DataSet.Rounding.DOWN);
        Entry entryTo = dataSet.getEntryForXIndex(mMaxX, DataSet.Rounding.UP);

        int diff = (entryFrom == entryTo) ? 1 : 0;
        int minx = Math.max(dataSet.getEntryIndex(entryFrom) - diff - 1, 0);
        int maxx = Math.min(Math.max(minx + 2, dataSet.getEntryIndex(entryTo) + 1), entryCount);

        float phaseX = Math.max(0.f, Math.min(1.f, mAnimator.getPhaseX()));
        float phaseY = mAnimator.getPhaseY();

        float intensity = dataSet.getCubicIntensity();

        cubicPath.reset();

        int size = (int) Math.ceil((maxx - minx) * phaseX + minx);

        if (size - minx >= 2) {

            float prevDx = 0f;
            float prevDy = 0f;
            float curDx = 0f;
            float curDy = 0f;

            Entry prevPrev = dataSet.getEntryForIndex(minx);
            Entry prev = prevPrev;
            Entry cur = prev;
            Entry next = dataSet.getEntryForIndex(minx + 1);

            // let the spline start
            cubicPath.moveTo(cur.getXIndex(), cur.getVal() * phaseY);

            for (int j = minx + 1, count = Math.min(size, entryCount); j < count; j++) {

                prevPrev = dataSet.getEntryForIndex(j == 1 ? 0 : j - 2);
                prev = dataSet.getEntryForIndex(j - 1);
                cur = dataSet.getEntryForIndex(j);
                next = entryCount > j + 1 ? dataSet.getEntryForIndex(j + 1) : cur;

                prevDx = (cur.getXIndex() - prevPrev.getXIndex()) * intensity;
                prevDy = (cur.getVal() - prevPrev.getVal()) * intensity;
                curDx = (next.getXIndex() - prev.getXIndex()) * intensity;
                curDy = (next.getVal() - prev.getVal()) * intensity;

                cubicPath.cubicTo(prev.getXIndex() + prevDx, (prev.getVal() + prevDy) * phaseY,
                        cur.getXIndex() - curDx,
                        (cur.getVal() - curDy) * phaseY, cur.getXIndex(), cur.getVal() * phaseY);
            }
        }

        // if filled is enabled, close the path
        if (dataSet.isDrawFilledEnabled()) {

            cubicFillPath.reset();
            cubicFillPath.addPath(cubicPath);
            // create a new path, this is bad for performance
            drawCubicFill(mBitmapCanvas, dataSet, cubicFillPath, trans,
                    minx, size);
        }

        mRenderPaint.setColor(dataSet.getColor());

        mRenderPaint.setStyle(Paint.Style.STROKE);

        trans.pathValueToPixel(cubicPath);

        mRenderPaint.setShader(new LinearGradient(0, mViewPortHandler.getContentRect().top,
                0, mViewPortHandler.getContentRect().bottom, dataSet.getGradientColors(),
                dataSet.getGradientPosition(), Shader.TileMode.CLAMP));
        mBitmapCanvas.drawPath(cubicPath, mRenderPaint);

        mRenderPaint.setPathEffect(null);
    }

    protected void drawSectionHorizontalBezier(Canvas c, SectionLineDataSet dataSet) {

        Transformer trans = mChart.getTransformer(dataSet.getAxisDependency());

        int entryCount = dataSet.getEntryCount();

        Entry entryFrom = dataSet.getEntryForXIndex((mMinX < 0) ? 0 : mMinX, DataSet.Rounding.DOWN);
        Entry entryTo = dataSet.getEntryForXIndex(mMaxX, DataSet.Rounding.UP);

        int diff = (entryFrom == entryTo) ? 1 : 0;
        int minx = Math.max(dataSet.getEntryIndex(entryFrom) - diff, 0);
        int maxx = Math.min(Math.max(minx + 2, dataSet.getEntryIndex(entryTo) + 1), entryCount);

        float phaseX = Math.max(0.f, Math.min(1.f, mAnimator.getPhaseX()));
        float phaseY = mAnimator.getPhaseY();

        cubicPath.reset();

        int size = (int) Math.ceil((maxx - minx) * phaseX + minx);

        if (size - minx >= 2) {

            Entry prev = dataSet.getEntryForIndex(minx);
            Entry cur = prev;

            // let the spline start
            cubicPath.moveTo(cur.getXIndex(), cur.getVal() * phaseY);
            for (int j = minx + 1, count = Math.min(size, entryCount); j < count; j++) {

                prev = dataSet.getEntryForIndex(j - 1);
                cur = dataSet.getEntryForIndex(j);

                final float cpx = (float) (prev.getXIndex())
                        + (float) (cur.getXIndex() - prev.getXIndex()) / 2.0f;

                cubicPath.cubicTo(
                        cpx, prev.getVal() * phaseY,
                        cpx, cur.getVal() * phaseY,
                        cur.getXIndex(), cur.getVal() * phaseY);
            }
        }

        // if filled is enabled, close the path
        if (dataSet.isDrawFilledEnabled()) {

            cubicFillPath.reset();
            cubicFillPath.addPath(cubicPath);
            // create a new path, this is bad for performance
            drawCubicFill(mBitmapCanvas, dataSet, cubicFillPath, trans,
                    minx, size);
        }

        mRenderPaint.setColor(dataSet.getColor());

        mRenderPaint.setStyle(Paint.Style.STROKE);

        mRenderPaint.setStrokeCap(Paint.Cap.ROUND);

        trans.pathValueToPixel(cubicPath);
        float left = mViewPortHandler.contentLeft();
        float top = mViewPortHandler.contentTop();
        float right = mViewPortHandler.contentRight();
        float bottom = mViewPortHandler.contentBottom();
        mRenderPaint.setShader(new LinearGradient(0, top,
                0, bottom, dataSet.getGradientColors(),
                dataSet.getGradientPosition(), Shader.TileMode.CLAMP));
        mBitmapCanvas.drawPath(cubicPath, mRenderPaint);
        List<Integer> intervalList = dataSet.getIntervalList();
        if (dataSet.isDrawTexture() && intervalList != null && intervalList.size() > 0) {
            if (mTextureGradient == null) {
                mTextureGradient = new LinearGradient(left, top, left + 8, top + 5,
                        new int[]{dataSet.getTextureColor(), dataSet.getTextureColor(),
                                Color.TRANSPARENT, Color.TRANSPARENT},
                        new float[]{0, 0.5f, 0.5f, 1f}, Shader.TileMode.REPEAT);
                mTexturePath = new Path();
            }
            cubicPath.reset();
            mRenderPaint.setShader(mTextureGradient);
            for (int i = 0; i < intervalList.size(); i = i + 2) {
                mTexturePath.reset();
                int startX = intervalList.get(i);
                int endX = intervalList.get(i+1);

                Entry textureFrom = dataSet.getEntryForXIndex(startX, DataSet.Rounding.DOWN);
                Entry textureTo = dataSet.getEntryForXIndex(endX, DataSet.Rounding.UP);

                int textureDiff = (textureFrom == textureTo) ? 1 : 0;
                int textureMinx = Math.max(dataSet.getEntryIndex(textureFrom) - textureDiff, 0);

                mTexturePath.reset();
                if (size - textureMinx >= 2) {

                    Entry prev = dataSet.getEntryForIndex(textureMinx);
                    Entry cur = prev;

                    // let the spline start
                    mTexturePath.moveTo(cur.getXIndex(), cur.getVal() * phaseY);
                    for (int j = textureMinx + 1, count =  Math.min(size, endX); j < count; j++) {
                        prev = dataSet.getEntryForIndex(j - 1);
                        cur = dataSet.getEntryForIndex(j);

                        final float cpx = (float) (prev.getXIndex())
                                + (float) (cur.getXIndex() - prev.getXIndex()) / 2.0f;

                        mTexturePath.cubicTo(
                                cpx, prev.getVal() * phaseY,
                                cpx, cur.getVal() * phaseY,
                                cur.getXIndex(), cur.getVal() * phaseY);
                    }
                    trans.pathValueToPixel(mTexturePath);
                    cubicPath.addPath(mTexturePath);
                }
                mBitmapCanvas.drawPath(cubicPath, mRenderPaint);
            }
        }
        mRenderPaint.setPathEffect(null);
    }

    public void drawSectionFilledPath(Canvas c, Path filledPath, SectionLineDataSet dataSet,
                                      int fillColor, int fillAlpha) {
        int color = (fillAlpha << 24) | (fillColor & 0xffffff);
        Paint.Style previous = mRenderPaint.getStyle();
        int previousColor = mRenderPaint.getColor();

        // set
        mRenderPaint.setStyle(Paint.Style.FILL);
        mRenderPaint.setColor(color);
        mRenderPaint.setAntiAlias(true);
        if (dataSet.isSection()) {
            mRenderPaint.setShader(new LinearGradient(0, mViewPortHandler.getContentRect().top,
                    0, mViewPortHandler.getContentRect().bottom, dataSet.getGradientColors(),
                    dataSet.getGradientPosition(), Shader.TileMode.CLAMP));
        }
        c.drawPath(filledPath, mRenderPaint);

        // restore
        mRenderPaint.setColor(previousColor);
        mRenderPaint.setStyle(previous);
    }

    /**
     * Generates the path that is used for filled drawing.
     *
     * @param dataSet
     * @return
     */
    private Path generateFilledPath(ILineDataSet dataSet, int from, int to) {

        float fillMin = dataSet.getFillFormatter().getFillLinePosition(dataSet, mChart);
        float phaseX = Math.max(0.f, Math.min(1.f, mAnimator.getPhaseX()));
        float phaseY = mAnimator.getPhaseY();
        final boolean isDrawSteppedEnabled = dataSet.isDrawSteppedEnabled();

        Path filled = new Path();
        Entry entry = dataSet.getEntryForIndex(from);

        filled.moveTo(entry.getXIndex(), fillMin);
        filled.lineTo(entry.getXIndex(), entry.getVal() * phaseY);

        // create a new path
        for (int x = from + 1, count = (int) Math.ceil((to - from) * phaseX + from); x < count; x++) {

            Entry e = dataSet.getEntryForIndex(x);

            if (isDrawSteppedEnabled) {
                final Entry ePrev = dataSet.getEntryForIndex(x - 1);
                if (ePrev == null) continue;

                filled.lineTo(e.getXIndex(), ePrev.getVal() * phaseY);
            }

            filled.lineTo(e.getXIndex(), e.getVal() * phaseY);
        }

        // close up
        filled.lineTo(
                dataSet.getEntryForIndex(
                        Math.max(
                                Math.min((int) Math.ceil((to - from) * phaseX + from) - 1,
                                        dataSet.getEntryCount() - 1), 0)).getXIndex(), fillMin);

        filled.close();

        return filled;
    }

    private Path generateFilledPathHasLast(ILineDataSet dataSet, int from, int to, ILineDataSet lastDataSet) {

        float fillMin = dataSet.getFillFormatter().getFillLinePosition(dataSet, mChart);
        float phaseX = Math.max(0.f, Math.min(1.f, mAnimator.getPhaseX()));
        float phaseY = mAnimator.getPhaseY();
        final boolean isDrawSteppedEnabled = dataSet.isDrawSteppedEnabled();

        Path filled = new Path();
        Entry entry = dataSet.getEntryForIndex(from);
        Entry lastEntry = null;
        if (lastDataSet != null) {
            lastEntry = lastDataSet.getEntryForIndex(from);
        }
        if (entry != null) {
            if (lastEntry != null) {
                filled.moveTo(entry.getXIndex(), lastEntry.getVal() * phaseY);
            } else {
                filled.moveTo(entry.getXIndex(), fillMin);
            }
            filled.lineTo(entry.getXIndex(), entry.getVal() * phaseY);
        }

        // create a new path
        for (int x = from + 1, count = (int) Math.ceil((to - from) * phaseX + from); x < count; x++) {

            Entry e = dataSet.getEntryForIndex(x);

            if (isDrawSteppedEnabled) {
                final Entry ePrev = dataSet.getEntryForIndex(x - 1);
                if (ePrev == null) continue;

                filled.lineTo(e.getXIndex(), ePrev.getVal() * phaseY);
            }

            filled.lineTo(e.getXIndex(), e.getVal() * phaseY);
        }
        Entry finalEntry = dataSet.getEntryForIndex(Math.max(
                Math.min((int) Math.ceil((to - from) * phaseX + from) - 1,
                        dataSet.getEntryCount() - 1), 0));
        Entry lastFinalEntry = null;
        if (lastDataSet != null) {
            lastDataSet.getEntryForIndex(
                    Math.max(
                            Math.min((int) Math.ceil((to - from) * phaseX + from) - 1,
                                    lastDataSet.getEntryCount() - 1), 0));
        }
        if (finalEntry != null) {
            if (lastFinalEntry != null) {
                filled.lineTo(finalEntry.getXIndex(), lastFinalEntry.getVal() * phaseY);
            } else {
                filled.lineTo(finalEntry.getXIndex(), fillMin);
            }
        }
        if (lastDataSet != null) {
            for (int count = (int) Math.ceil((to - from) * phaseX + from), x = count - 1; x > from; x--) {
                Entry e = lastDataSet.getEntryForIndex(x);

                if (isDrawSteppedEnabled) {
                    final Entry ePrev = lastDataSet.getEntryForIndex(x - 1);
                    if (ePrev == null) continue;

                    filled.lineTo(e.getXIndex(), ePrev.getVal() * phaseY);
                }

                filled.lineTo(e.getXIndex(), e.getVal() * phaseY);
            }
        }
        filled.close();
        return filled;
    }


    public int getInCircleColor() {
        return mInCircleColor;
    }

    public void setInCircleColor(int inCircleColor) {
        this.mInCircleColor = inCircleColor;
    }

    public int getOutCircleColor() {
        return mOutCircleColor;
    }

    public void setOutCircleColor(int outCircleColor) {
        this.mOutCircleColor = outCircleColor;
    }

    public LineDataProvider getChart() {
        return mChart;
    }

    public void setMultiply(boolean multiply) {
        isMultiply = multiply;
    }

    /**
     * 使用新的Grid-imported渲染器绘制4层渲染（公共接口，保持向后兼容）
     */
    public void drawGridImportedLayersWithNewRenderer(Canvas canvas,
                                                    ILineDataSet emergencyPowerDataSet,
                                                    ILineDataSet normalPowerDataSet,
                                                    ILineDataSet thresholdDataSet,
                                                    int minx, int count, int pointsPerEntryPair,
                                                    boolean isDrawSteppedEnabled, float phaseY,
                                                    Transformer trans) {

        // 获取Grid-imported渲染器单例实例
        GridImportedLineChartRender gridRenderer = GridImportedLineChartRender.getInstance();

        // 设置Context用于获取颜色资源
        gridRenderer.setContext(null);

        // 直接调用新渲染器的4层渲染方法
        gridRenderer.drawGridImportedLayers(canvas, emergencyPowerDataSet, normalPowerDataSet, thresholdDataSet,
                                          minx, count, pointsPerEntryPair, isDrawSteppedEnabled, phaseY, trans);
    }



}
