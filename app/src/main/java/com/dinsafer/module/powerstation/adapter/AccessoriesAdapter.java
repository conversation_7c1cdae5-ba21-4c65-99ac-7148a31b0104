package com.dinsafer.module.powerstation.adapter;


import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.PSAccessoryListBean;
import com.dinsafer.module.powerstation.widget.SpaceItemDecoration;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.LocalTextView;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 18:16
 * @description :
 */
public class AccessoriesAdapter extends BaseQuickAdapter<PSAccessoryListBean, BaseViewHolder> {

    public AccessoriesAdapter() {
        super(R.layout.item_accessories);
    }

    @Override
    protected void convert(BaseViewHolder helper, PSAccessoryListBean item) {
        LocalTextView tvName = helper.getView(R.id.tv_name);
        tvName.setPadding(0, helper.getAdapterPosition() == 0 ? DensityUtil.dp2px(mContext, 15) : 0, 0, 0);
        tvName.setLocalText(item.getName());
        RecyclerView rvDevice = helper.getView(R.id.rv_device);
        ((SimpleItemAnimator) rvDevice.getItemAnimator()).setSupportsChangeAnimations(false);
        final RecyclerView.Adapter<?> adapter = rvDevice.getAdapter();
        final AccessoriesDeviceAdapter accessoriesDeviceAdapter;
        if (!(adapter instanceof AccessoriesDeviceAdapter)) {
            accessoriesDeviceAdapter = new AccessoriesDeviceAdapter();
            rvDevice.setLayoutManager(new GridLayoutManager(mContext, 2));
            if (rvDevice.getItemDecorationCount() == 0) {
                rvDevice.addItemDecoration(new SpaceItemDecoration(mContext, DensityUtil.dp2px(mContext, 15f), DensityUtil.dp2px(mContext, 7.5f)));
            }
            rvDevice.setAdapter(accessoriesDeviceAdapter);
        } else {
            accessoriesDeviceAdapter = (AccessoriesDeviceAdapter) adapter;
        }
        accessoriesDeviceAdapter.setNewData(item.getAccessories());
    }
}
