package com.dinsafer.module.powerstation.event;

import androidx.annotation.NonNull;

public class BatteryStatusEvent {


    private final String deviceId;
    private final String subcategory;
    private int status;

    public BatteryStatusEvent(@NonNull final String deviceId, @NonNull final String subcategory, int status) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.status = status;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
