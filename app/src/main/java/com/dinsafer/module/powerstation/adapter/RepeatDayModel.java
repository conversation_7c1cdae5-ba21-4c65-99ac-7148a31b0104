package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemRepeatDayBinding;
import com.dinsafer.ui.rv.BindModel;

public class RepeatDayModel extends BindModel<ItemRepeatDayBinding> {

    private String day;
    private boolean selected;

    public RepeatDayModel(Context context, String day, boolean selected) {
        super(context);
        this.day = day;
        this.selected = selected;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_repeat_day;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemRepeatDayBinding binding) {
        binding.ivSelected.setVisibility(isSelected() ? View.VISIBLE : View.INVISIBLE);
        binding.tvDay.setLocalText(day);
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
