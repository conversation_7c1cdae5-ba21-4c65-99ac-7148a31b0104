package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;

import com.dinsafer.util.viewanimator.SvgPathParser;

public class PowerCoreCurrentView extends BaseCurrentView{

    private final String TOPO_PATH = "M105 1C105 0.447715 104.552 0 104 0C103.448 0 103 0.447715 103 1V17.75C103 30.5659 97.8976 42.1902 89.6134 50.702C89.3004 50.9965 88.9965 51.3004 88.702 51.6134C80.1906 59.8972 68.567 64.9995 55.7518 65H55.75L1 65C0.447716 65 0 65.4477 0 66C0 66.5523 0.447716 67 1 67L55.75 67C66.0858 67 75.6465 70.3187 83.4247 75.9486C82.7847 79.5383 80.9334 82.9237 77.9538 85.4383C77.894 85.4838 77.8344 85.5298 77.775 85.5762L77.7801 85.5828C77.7189 85.6331 77.6572 85.683 77.595 85.7325C77.5466 85.7586 77.4998 85.7891 77.4549 85.8239L2.38606 144.211C1.95011 144.55 1.87158 145.178 2.21065 145.614C2.54972 146.05 3.17799 146.129 3.61394 145.79L78.6828 87.4026L78.6947 87.3932L78.704 87.4051C78.8735 87.2726 79.04 87.1377 79.2036 87.0005C83.0325 84.102 87.7612 83.1877 92.1373 84.1062C98.9216 92.2866 103 102.792 103 114.25V146C103 146.552 103.448 147 104 147C104.552 147 105 146.552 105 146V114.25C105 101.434 110.102 89.8098 118.387 81.298C118.7 81.0035 119.004 80.6996 119.298 80.3866C127.81 72.1024 139.434 67 152.25 67H228V146C228 146.552 228.448 147 229 147C229.552 147 230 146.552 230 146V1C230 0.447715 229.552 0 229 0C228.448 0 228 0.447715 228 1V65H152.25C139.434 65 127.81 59.8976 119.298 51.6134C119.004 51.3004 118.7 50.9965 118.387 50.702C110.102 42.1902 105 30.5659 105 17.75V1ZM83.3804 70.0172L83.3837 70.0165C83.1319 68.7165 83 67.3736 83 66C83 62.9921 83.6324 60.1319 84.7714 57.5451C79.182 61.6283 72.6999 64.5625 65.6727 66C72.2417 67.3438 78.3343 69.9953 83.6666 73.6708C83.7096 72.4518 83.6152 71.2256 83.3804 70.0172ZM82.8943 82.7726C83.9665 81.0679 84.7346 79.2121 85.1885 77.2891C86.4106 78.2638 87.5832 79.2977 88.702 80.3866C88.9965 80.6996 89.3004 81.0035 89.6134 81.298C89.7777 81.4668 89.9408 81.6369 90.1026 81.8081C87.6863 81.6381 85.2309 81.953 82.8943 82.7726ZM104 104.327C105.438 97.3001 108.372 90.818 112.455 85.2286C109.868 86.3676 107.008 87 104 87C100.992 87 98.1319 86.3676 95.5452 85.2286C99.6283 90.818 102.562 97.3001 104 104.327ZM91.0147 79.8703C94.4118 83.0519 98.9784 85 104 85C109.022 85 113.588 83.0519 116.985 79.8703C117.277 79.5716 117.572 79.2765 117.87 78.9853C121.052 75.5882 123 71.0216 123 66C123 60.9784 121.052 56.4118 117.87 53.0147C117.572 52.7234 117.277 52.4284 116.985 52.1297C113.588 48.9481 109.022 47 104 47C98.9784 47 94.4118 48.9481 91.0147 52.1297C90.7234 52.4284 90.4284 52.7234 90.1297 53.0147C86.9481 56.4118 85 60.9784 85 66C85 71.0216 86.9481 75.5882 90.1297 78.9853C90.4284 79.2765 90.7235 79.5716 91.0147 79.8703ZM95.5452 46.7714C99.6283 41.182 102.562 34.6999 104 27.6727C105.438 34.6999 108.372 41.182 112.455 46.7714C109.868 45.6324 107.008 45 104 45C100.992 45 98.1319 45.6324 95.5452 46.7714ZM123.229 57.5451C128.818 61.6283 135.3 64.5625 142.327 66C135.3 67.4375 128.818 70.3717 123.229 74.4548C124.368 71.8681 125 69.0079 125 66C125 62.9921 124.368 60.1319 123.229 57.5451Z";

    public PowerCoreCurrentView() {
        super();
        mTopoPath = SvgPathParser.tryParsePath(TOPO_PATH);
        if (mTopoPath != null) {
            mTopoPath.setFillType(Path.FillType.EVEN_ODD);
        }
    }

    @Override
    public void drawTB(Canvas canvas, Paint paint) {
        canvas.drawPath(mTopoPath, paint);
    }
}
