package com.dinsafer.module.powerstation.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.adapter.RMFKeyValueModel;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

public class ReserveModeFunctionPopup extends PopupWindow {

    private final Context mContext;
    private int type;
    private BindMultiAdapter<BindModel> mAdapter;
    private List<BindModel> mData;
    private RMFKeyValueModel mEditBatteryRangeMode;
    private RMFKeyValueModel mPVPreferenceModel;
    private RMFKeyValueModel mLocationModel;
    private OnBindItemClickListener<BindModel> bindModelOnBindItemClickListener;
    private int pvPreference;
    private String locationVal;
    private boolean isSupportPVAndLocation;
    private boolean isManualOverrideEnabled = true;


    public ReserveModeFunctionPopup(Context context, int type, int pvPreference, boolean isSupportPVAndLocation) {
        super(context);
        mContext = context;
        this.type = type;
        this.pvPreference = pvPreference;
        this.isSupportPVAndLocation = isSupportPVAndLocation;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.popup_reserve_mode_function, null);
        // 设置布局
        setContentView(view);
        setBackgroundDrawable(null);
        setOutsideTouchable(true);
        setFocusable(true);
        initRv(view);
    }

    public ReserveModeFunctionPopup(Context context, int type, int pvPreference, String locationVal, boolean isSupportPVAndLocation) {
        super(context);
        mContext = context;
        this.type = type;
        this.pvPreference = pvPreference;
        this.locationVal = locationVal;
        this.isSupportPVAndLocation = isSupportPVAndLocation;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.popup_reserve_mode_function, null);
        // 设置布局
        setContentView(view);
        setBackgroundDrawable(null);
        setOutsideTouchable(true);
        setFocusable(true);
        initRv(view);
    }

    public ReserveModeFunctionPopup(Context context, int type, int pvPreference, String locationVal, boolean isSupportPVAndLocation,boolean isManualOverrideEnabled) {
        super(context);
        mContext = context;
        this.type = type;
        this.pvPreference = pvPreference;
        this.locationVal = locationVal;
        this.isSupportPVAndLocation = isSupportPVAndLocation;
        this.isManualOverrideEnabled = isManualOverrideEnabled;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.popup_reserve_mode_function, null);
        // 设置布局
        setContentView(view);
        setBackgroundDrawable(null);
        setOutsideTouchable(true);
        setFocusable(true);
        initRv(view);
    }

    private void initRv(View view) {
        RecyclerView rvData = view.findViewById(R.id.rv_data);
        ViewGroup.LayoutParams rvLayoutParams = rvData.getLayoutParams();
        rvLayoutParams.width = ScreenUtils.getScreenWidth(mContext) - DensityUtils.dp2px(mContext, 59);
        rvData.setLayoutParams(rvLayoutParams);
        rvData.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new BindMultiAdapter<>();
        mData = new ArrayList<>();
        mEditBatteryRangeMode = new RMFKeyValueModel(mContext, type == 0 ? mContext.getResources().getString(R.string.Manual_Override)
                : mContext.getResources().getString(R.string.Edit_Battery_Range), type == 0 || isSupportPVAndLocation,isManualOverrideEnabled);
        mEditBatteryRangeMode.setShowArrow(type == 0);
        mData.add(mEditBatteryRangeMode);
        String pvPreferenceStr = "";
        if (pvPreference == Mcu.PVPreference.PVPreferenceType.followEmaldoAI.getPreference()) {
            pvPreferenceStr = mContext.getResources().getString(R.string.Follow_Emaldo_AI);
        } else if (pvPreference == Mcu.PVPreference.PVPreferenceType.load.getPreference()) {
            pvPreferenceStr = mContext.getResources().getString(R.string.Load_First);
        } else if (pvPreference == Mcu.PVPreference.PVPreferenceType.batteryCharge.getPreference()) {
            pvPreferenceStr = mContext.getResources().getString(R.string.Battery_Charge_First);
        }
        if (isSupportPVAndLocation) {
            mPVPreferenceModel = new RMFKeyValueModel(mContext, type == 0 ? mContext.getResources().getString(R.string.Solar_Priority)
                    : mContext.getResources().getString(R.string.PV_Distribution_Preference),
                    pvPreferenceStr, type == 0);
            mData.add(mPVPreferenceModel);
        }
        if (type == 0) {
            mLocationModel = new RMFKeyValueModel(mContext, mContext.getResources().getString(R.string.Location), false);
            String val = !TextUtils.isEmpty(locationVal) ?
                    locationVal : mContext.getResources().getString(R.string.Acquire_precise_forecasts);
            mLocationModel.setValue(val);
            mLocationModel.setAIValue(TextUtils.isEmpty(locationVal));
            mData.add(mLocationModel);
        }
        mAdapter.setNewData(mData);

        rvData.setAdapter(mAdapter);
    }

    public void setBindModelOnBindItemClickListener(OnBindItemClickListener<BindModel> bindModelOnBindItemClickListener) {
        this.bindModelOnBindItemClickListener = bindModelOnBindItemClickListener;
        if (mAdapter != null) {
            mAdapter.setOnBindItemClickListener(bindModelOnBindItemClickListener);
        }
    }

    public void setPVPreferenceVal(int pvPreference) {
        if (mPVPreferenceModel != null) {
            this.pvPreference = pvPreference;
            String pvPreferenceStr = "";
            if (pvPreference == Mcu.PVPreference.PVPreferenceType.followEmaldoAI.getPreference()) {
                pvPreferenceStr = mContext.getResources().getString(R.string.Follow_Emaldo_AI);
            } else if (pvPreference == Mcu.PVPreference.PVPreferenceType.load.getPreference()) {
                pvPreferenceStr = mContext.getResources().getString(R.string.Load_First);
            } else if (pvPreference == Mcu.PVPreference.PVPreferenceType.batteryCharge.getPreference()) {
                pvPreferenceStr = mContext.getResources().getString(R.string.Battery_Charge_First);
            }
            mPVPreferenceModel.setValue(pvPreferenceStr);
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    public void setLocationVal(String locationVal) {
        if (mLocationModel != null) {
            String val = !TextUtils.isEmpty(locationVal) ?
                    locationVal : mContext.getResources().getString(R.string.Acquire_precise_forecasts);
            mLocationModel.setValue(val);
            mLocationModel.setAIValue(TextUtils.isEmpty(locationVal));
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }
}
