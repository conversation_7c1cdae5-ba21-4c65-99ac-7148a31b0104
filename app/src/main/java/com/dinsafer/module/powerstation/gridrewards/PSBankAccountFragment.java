package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.view.MotionEvent;
import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.dialog.InputPasswordDialog;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsBankAccountBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.utils.IBANValidatorUtil;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.util.ClearAllEditText;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;
import com.google.gson.Gson;
import com.jakewharton.rxbinding.view.RxView;
import com.jakewharton.rxbinding.widget.RxTextView;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import rx.Observable;

public class PSBankAccountFragment extends MyBaseFragment<FragmentPsBankAccountBinding> {

    private boolean shouldStopChange = false;
    public static final int TYPE_SIGN_CONTRACT = 0;
    public static final int TYPE_CHANGE_ACCOUNT = 1;
    private List<CountryBean> mCountryList = new ArrayList<>();
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;
    private int mFrom;
    private boolean showedTip = false;
    // 输入框内容改变后onTextChanged方法会调用多次，设置一个变量让其每次改变之后仅仅调用一次
    private boolean isTextChanged = false;

    public static PSBankAccountFragment newInstance(List<CountryBean> countryBeans, FamilyBalanceContractInfo familyBalanceContractInfo) {
        PSBankAccountFragment fragment = new PSBankAccountFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, TYPE_SIGN_CONTRACT);
        bundle.putSerializable(PSKeyConstant.KEY_COUNTRIES, (Serializable) countryBeans);
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static PSBankAccountFragment newInstance(FamilyBalanceContractInfo familyBalanceContractInfo) {
        PSBankAccountFragment fragment = new PSBankAccountFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, TYPE_CHANGE_ACCOUNT);
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_bank_account;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.bank_account));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        // IBAN只能是中文加数字, 所以文本输入类型为文本密码, 需要代码设置明文显示
        mBinding.etIban.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
        mBinding.etIban.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN
                    && !showedTip
                    && !mFamilyBalanceContractInfo.isFirst_sign()
                    && !TextUtils.isEmpty(mBinding.etIban.getText())) {
                showedTip = true;
                showChangeIBANTipDialog();
                return true;
            }
            return false;
        });

        mBinding.llParent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCloseInput();
            }
        });

        mBinding.llContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCloseInput();
            }
        });

        mBinding.btnNext.setOnClickListener(v -> {
            String iban = mBinding.etIban.getText().toString().replace(" ", "").toUpperCase();
            if (!IBANValidatorUtil.isValidIBAN(iban)) {
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.Wrong_IBAN_entered));
                return;
            }
            if (mFrom == TYPE_SIGN_CONTRACT) {
                saveCache();
                ClearAllEditText.clearAllEditTextFocus(this);
                getDelegateActivity().addCommonFragment(PSContractedDevicesFragment.newInstance(mCountryList, mFamilyBalanceContractInfo));
            } else {
                showPasswordDialog();
            }
        });

        mBinding.etIban.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (isTextChanged) {
                    isTextChanged = false;
                    return;
                }
                isTextChanged = true;
                // 处理输入内容空格与位数以及光标位置的逻辑
                handleInputContent(s, before);
            }

            @Override
            public void afterTextChanged(Editable s) {
//                format(s);
            }
        });
    }

    // 卡号内容
    private String content;
    // 卡号最大长度,卡号一般最长21位
    public static final int MAX_CONTENT_LENGHT = 42;
    // 缓冲分隔后的新内容串
    private String result = "";
    private int splitNumber = 4;
    private int lastCursor;
    private int lastLen;

    /**
     * 处理输入内容空格与位数的逻辑
     */
    private void handleInputContent(CharSequence s, int before) {
        //假设isCardNumber=true，说明是银行卡号输入框。控制仅仅能输入数字，否则按原特性处理
        content = s.toString();
        //先缓存输入框内容
        result = content;
        //去掉空格，以防止用户自己输入空格
        content = content.replace(" ", "");
        // 限制输入的数字位数最多42位（银行卡号一般最多42位）
        if (content != null && content.length() <= MAX_CONTENT_LENGHT) {
            result = "";
            int i = 0;
            // 先把splitNumber倍的字符串进行分隔
            while (i + splitNumber < content.length()) {
                result += content.substring(i, i + splitNumber) + " ";
                i += splitNumber;
            }
            // 最后把不够splitNumber倍的字符串加到末尾
            result += content.substring(i, content.length());
        } else {
            //假设用户输入的位数
            result = result.substring(0, result.length() - 1);
        }
        // 获取光标開始位置
        // 必须放在设置内容之前
        int j = mBinding.etIban.getSelectionStart();
        mBinding.etIban.setText(result);
        // 处理光标位置
        handleCursor(before, j);

    }

    /**
     * 处理光标位置
     *
     * @param before
     * @param j
     */
    private void handleCursor(int before, int j) {
        // 处理光标位置
        try {
            if (j < result.length()) {
                // 加入字符
                if (before == 0) {
                    //遇到空格，光标跳过空格，定位到空格后的位置
                    if (j % splitNumber + 1 == 0) {
                        mBinding.etIban.setSelection(j + 1);
                    } else {
                        //否则，光标定位到内容之后 （光标默认定位方式）
                        if (lastCursor < result.length()
                                && result.charAt(lastCursor) == ' '
                                && lastLen < result.length()) {
                            j = j + 1;
                        }
                        mBinding.etIban.setSelection(j);
                    }
                    // 回退清除一个字符
                } else if (before == 1) {
                    //回退到上一个位置（遇空格跳过）
                    if (lastCursor < result.length()
                            && result.charAt(lastCursor) == ' '
                            && lastLen < result.length()) {
                        j = j + 1;
                    }
                    mBinding.etIban.setSelection(j);
                }
            } else {
                mBinding.etIban.setSelection(result.length());
            }
            lastCursor = mBinding.etIban.getSelectionStart();
            lastLen = result.length();
        } catch (Exception e) {

        }
    }

    private void format(Editable editable) {
        if (shouldStopChange) {
            shouldStopChange = false;
            return;
        }

        shouldStopChange = true;

        String str = editable.toString().trim().replaceAll(" ", "");
        int len = str.length();
        int courPos;

        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < len; i++) {
            builder.append(str.charAt(i));
            if ((i + 1) % 4 == 0) {
                if (i != len - 1)
                    builder.append(" ");
            }
        }
        courPos = builder.length();
        mBinding.etIban.setText(builder.toString());
        mBinding.etIban.setSelection(courPos);
    }

    private void showChangeIBANTipDialog() {
        AlertDialogV2.createBuilder(getActivity())
                .setContent(Local.s(getString(R.string.change_IBAN_tip)))
                .setCancel(Local.s(getString(R.string.modify)))
                .setOk(Local.s(getString(R.string.cancel)))
                .preBuilder()
                .show();
    }

    private void showPasswordDialog() {
        new InputPasswordDialog.Builder(getContext())
                .setAutoDismiss(false)
                .setConfirmListener((dialog, password) -> {
                    if (TextUtils.isEmpty(password)) {
                        return;
                    }
                    if (!password.equals(DBUtil.SGet(DBKey.USER_PASSWORD))) {
                        showErrorToast();
                        return;
                    }
                    dialog.dismiss();
                    updateBalanceContractBank(password);
                }).build().show();
    }

    private void updateBalanceContractBank(String pwd) {
        showTimeOutLoadinFramgment();
        String iban = mBinding.etIban.getText().toString().replace(" ", "").toUpperCase();
        mFamilyBalanceContractInfo.setCardholder(mBinding.etName.getText().toString());
        DinHome.getInstance().updateBalanceContractBank(HomeManager.getInstance().getCurrentHome().getHomeID()
                , mFamilyBalanceContractInfo.getCardholder(), iban, pwd, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        saveCache();
                        closeLoadingFragment();
                        mBinding.btnNext.setAlpha(0.5f);
                        mBinding.btnNext.setEnabled(false);
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragment();
                        showErrorToast();
                    }
                });
    }

    private void saveCache() {
        mFamilyBalanceContractInfo.setCardholder(mBinding.etName.getText().toString());
        mFamilyBalanceContractInfo.setIBAN(mBinding.etIban.getText().toString().replace(" ", "").toUpperCase());
        String json = new Gson().toJson(mFamilyBalanceContractInfo);
        String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        if (mFrom == TYPE_SIGN_CONTRACT) {
            int type = mFamilyBalanceContractInfo.getType();
            DBUtil.Put(DBKey.KEY_FAMILY_BALANCE_CONTRACT_INFO + "_" + type + "_" + homeId, json);
        } else {
            // 设置类型 0. 公司 1个人
            DBUtil.Put(DBKey.KEY_FAMILY_BALANCE_CONTRACT_INFO + "_" + 0 + "_" + homeId, json);
            DBUtil.Put(DBKey.KEY_FAMILY_BALANCE_CONTRACT_INFO + "_" + 1 + "_" + homeId, json);
        }
    }

    @Override
    public void initData() {
        super.initData();
        Observable<CharSequence> ObservableName = RxTextView.textChanges(mBinding.etName);
        Observable<CharSequence> ObservableIban = RxTextView.textChanges(mBinding.etIban);

        Observable.combineLatest(ObservableName, ObservableIban, (name, iban) -> !TextUtils.isEmpty(name.toString())
                && !TextUtils.isEmpty(iban.toString())).subscribe(aBoolean -> {
            mBinding.btnNext.setAlpha(aBoolean ? 1f : 0.5f);
            RxView.enabled(mBinding.btnNext).call(aBoolean);
        });

        Bundle bundle = getArguments();
        if (bundle != null) {
            mFrom = bundle.getInt(PSKeyConstant.KEY_FROM);
            List<CountryBean> cb = (List<CountryBean>) bundle.getSerializable(PSKeyConstant.KEY_COUNTRIES);
            if (cb != null) {
                mCountryList.addAll(cb);
            }
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
            String name = mFamilyBalanceContractInfo.getCardholder();
            if (!TextUtils.isEmpty(name)) {
                mBinding.etName.setText(name);
            } else {
                mBinding.etName.setText(UserManager.getInstance().getUser().getUid());
            }
            String iban = mFamilyBalanceContractInfo.getIBAN();
            if (!TextUtils.isEmpty(iban)) {
                mBinding.etIban.setText(iban);
            }
            mBinding.llSubtitle.setVisibility(mFrom == TYPE_SIGN_CONTRACT ? View.VISIBLE : View.GONE);
            mBinding.btnNext.setLocalText(mFrom == TYPE_SIGN_CONTRACT ? getString(R.string.next) : getString(R.string.save));

            if (mFrom == TYPE_CHANGE_ACCOUNT) {
                mBinding.btnNext.setAlpha(0.5f);
                mBinding.btnNext.setEnabled(false);
            }
        }
    }
}
