package com.dinsafer.module.powerstation.electricity.chart;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.util.Log;

import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomXAxisRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.SectionLineChartRender;
import com.github.mikephil.charting.charts.BarLineChartBase;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.dataprovider.LineDataProvider;
import com.github.mikephil.charting.utils.Utils;

/**
 * 不同区间不同颜色折线图
 */
public class SectionLineChart extends BarLineChartBase<LineData> implements LineDataProvider {

    private Context mContext;
    private String mNoDataTextDescription;
    /**
     * text that is displayed when the chart is empty
     */
    private String mNoDataText = "No chart data available.";

    /**
     * flag that indicates if offsets calculation has already been done or not
     */
    private boolean mOffsetsCalculated = false;

    private Integer mAutoScaleLastLowestVisibleXIndex = null;
    private Integer mAutoScaleLastHighestVisibleXIndex = null;
    // for performance tracking
    private long totalTime = 0;
    private long drawCycles = 0;

    private boolean isNeedNoDataGrid;
    private Path defaultXGridLinesPath = new Path();
    private float noDataOffset;
    private float noDataLeftOffset;
    private float noDataTopOffset;
    private float noDataRightOffset;
    private float noDataBottomOffset;

    public SectionLineChart(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SectionLineChart(Context context) {
        this(context, null);
    }

    public SectionLineChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mContext = context;
        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.palanquin);
        mXAxis.setTypeface(typeface);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SectionLineChart);
        noDataOffset = typedArray.getDimension(R.styleable.SectionLineChart_noDataOffset, 0);
        noDataLeftOffset = typedArray.getDimension(R.styleable.SectionLineChart_noDataLeftOffset, 0);
        noDataTopOffset = typedArray.getDimension(R.styleable.SectionLineChart_noDataTopOffset, 0);
        noDataRightOffset = typedArray.getDimension(R.styleable.SectionLineChart_noDataRightOffset, 0);
        noDataBottomOffset = typedArray.getDimension(R.styleable.SectionLineChart_noDataBottomOffset, 0);
        if (noDataLeftOffset == 0.0f) {
            noDataLeftOffset = noDataOffset;
        }

        if (noDataTopOffset == 0.0f) {
            noDataTopOffset = noDataOffset;
        }

        if (noDataRightOffset == 0.0f) {
            noDataRightOffset = noDataOffset;
        }

        if (noDataBottomOffset == 0.0f) {
            noDataBottomOffset = noDataOffset;
        }
        typedArray.recycle();
    }

    @Override
    protected void init() {
        super.init();
        mRenderer = new SectionLineChartRender(this, mAnimator, mViewPortHandler);
        mXAxisRenderer = new CustomXAxisRenderer(mViewPortHandler, mXAxis, mLeftAxisTransformer);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (mData == null) {
            drawNoData(canvas);
            return;
        }

        if (!mOffsetsCalculated) {

            calculateOffsets();
            mOffsetsCalculated = true;
        }


        if (mData == null)
            return;

        long starttime = System.currentTimeMillis();
        calcModulus();

        mXAxisRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);
        mRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);

        // execute all drawing commands
        drawGridBackground(canvas);

        if (mAxisLeft.isEnabled())
            mAxisRendererLeft.computeAxis(mAxisLeft.mAxisMinimum, mAxisLeft.mAxisMaximum);
        if (mAxisRight.isEnabled())
            mAxisRendererRight.computeAxis(mAxisRight.mAxisMinimum, mAxisRight.mAxisMaximum);

        mXAxisRenderer.renderAxisLine(canvas);
        mAxisRendererLeft.renderAxisLine(canvas);
        mAxisRendererRight.renderAxisLine(canvas);

        if (isAutoScaleMinMaxEnabled()) {
            final int lowestVisibleXIndex = getLowestVisibleXIndex();
            final int highestVisibleXIndex = getHighestVisibleXIndex();

            if (mAutoScaleLastLowestVisibleXIndex == null ||
                    mAutoScaleLastLowestVisibleXIndex != lowestVisibleXIndex ||
                    mAutoScaleLastHighestVisibleXIndex == null ||
                    mAutoScaleLastHighestVisibleXIndex != highestVisibleXIndex) {

                calcMinMax();
                calculateOffsets();

                mAutoScaleLastLowestVisibleXIndex = lowestVisibleXIndex;
                mAutoScaleLastHighestVisibleXIndex = highestVisibleXIndex;
            }
        }

        // make sure the graph values and grid cannot be drawn outside the
        // content-rect
        int clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        mXAxisRenderer.renderGridLines(canvas);
        mAxisRendererLeft.renderGridLines(canvas);
        mAxisRendererRight.renderGridLines(canvas);

        if (mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        mRenderer.drawData(canvas);

        // Removes clipping rectangle
        canvas.restoreToCount(clipRestoreCount);

        // if highlighting is enabled
        if (valuesToHighlight()) {
            mRenderer.drawHighlighted(canvas, mIndicesToHighlight);
        }

        mRenderer.drawExtras(canvas);

        clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        if (!mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (!mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (!mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        canvas.restoreToCount(clipRestoreCount);

        mXAxisRenderer.renderAxisLabels(canvas);
        mAxisRendererLeft.renderAxisLabels(canvas);
        mAxisRendererRight.renderAxisLabels(canvas);

        mRenderer.drawValues(canvas);

        mLegendRenderer.renderLegend(canvas);

        drawMarkers(canvas);

        drawDescription(canvas);

        if (mLogEnabled) {
            long drawtime = (System.currentTimeMillis() - starttime);
            totalTime += drawtime;
            drawCycles += 1;
            long average = totalTime / drawCycles;
            Log.i(LOG_TAG, "Drawtime: " + drawtime + " ms, average: " + average + " ms, cycles: "
                    + drawCycles);
        }
    }

    /**
     * Sets the text that informs the user that there is no data available with
     * which to draw the chart.
     *
     * @param text
     */
    public void setNoDataText(String text) {
        mNoDataText = text;
    }

    /**
     * Sets descriptive text to explain to the user why there is no chart
     * available Defaults to empty if not set
     *
     * @param text
     */
    public void setNoDataTextDescription(String text) {
        mNoDataTextDescription = text;
    }

    @Override
    public void resetTracking() {
        super.resetTracking();
        totalTime = 0;
        drawCycles = 0;
    }

    @Override
    public void setData(LineData data) {
        if (data == null) {
            Log.e(LOG_TAG,
                    "Cannot set data for chart. Provided data object is null.");
            return;
        }
        mOffsetsCalculated = false;
        super.setData(data);

    }

    @Override
    protected void calcMinMax() {
        super.calcMinMax();
        if (mXAxis.mAxisRange == 0 && mData.getYValCount() > 0)
            mXAxis.mAxisRange = 1;
    }

    @Override
    public LineData getLineData() {
        return mData;
    }

    @Override
    protected void onDetachedFromWindow() {
        // releases the bitmap in the renderer to avoid oom error
        if(mRenderer != null && mRenderer instanceof SectionLineChartRender) {
            ((SectionLineChartRender) mRenderer).releaseBitmap();
        }
        super.onDetachedFromWindow();
    }

    @Override
    protected void drawMarkers(Canvas canvas) {
        // if there is no marker view or drawing marker is disabled
        if (mMarkerView == null || !mDrawMarkerViews || !valuesToHighlight())
            return;

        for (int i = 0; i < mIndicesToHighlight.length; i++) {

            Highlight highlight = mIndicesToHighlight[i];
            int xIndex = highlight.getXIndex();
            int dataSetIndex = highlight.getDataSetIndex();

            float deltaX = mXAxis != null
                    ? mXAxis.mAxisRange
                    : ((mData == null ? 0.f : mData.getXValCount()) - 1.f);

            if (xIndex <= deltaX && xIndex <= deltaX * mAnimator.getPhaseX()) {

                Entry e = mData.getEntryForHighlight(mIndicesToHighlight[i]);

                // make sure entry not null
                if (e == null || e.getXIndex() != mIndicesToHighlight[i].getXIndex())
                    continue;

                float[] pos = getMarkerPosition(e, highlight);

                // check bounds
                if (!mViewPortHandler.isInBounds(pos[0], pos[1]))
                    continue;

                // callbacks to update the content
                mMarkerView.refreshContent(e, highlight);

                // mMarkerView.measure(MeasureSpec.makeMeasureSpec(0,
                // MeasureSpec.UNSPECIFIED),
                // MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
                // mMarkerView.layout(0, 0, mMarkerView.getMeasuredWidth(),
                // mMarkerView.getMeasuredHeight());
                // mMarkerView.draw(mDrawCanvas, pos[0], pos[1]);

                mMarkerView.measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                        MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
                mMarkerView.layout(0, 0, mMarkerView.getMeasuredWidth(),
                        mMarkerView.getMeasuredHeight());

//                if (pos[1] - mMarkerView.getHeight() <= 0) {
//                    float y = mMarkerView.getHeight() - pos[1];
//                    mMarkerView.draw(canvas, pos[0], pos[1] + y);
//                } else {
//                    mMarkerView.draw(canvas, pos[0], pos[1]);
//                }
                mMarkerView.draw(canvas, pos[0], mViewPortHandler.contentTop()-10);
            }
        }
    }

    private void drawNoData(Canvas canvas) {
        RectF noDataRectF = new RectF(noDataLeftOffset, noDataTopOffset,
                getWidth() - noDataRightOffset, getHeight() - noDataBottomOffset);
        mBorderPaint.setColor(mContext.getResources().getColor(R.color.color_white_04));
        canvas.drawRect(noDataRectF, mBorderPaint);
        if (isNeedNoDataGrid) {
            float average = (noDataRectF.right - noDataRectF.left) / 4;
            for (int i = 1; i < 4; i++) {
                float x = noDataRectF.left + average * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(x, noDataRectF.top);
                defaultXGridLinesPath.lineTo(x, noDataRectF.bottom);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }

            float verticalAverage = (noDataRectF.bottom - noDataRectF.top) / 5;

            for (int i = 1; i < 5; i++) {
                float y = noDataRectF.top + verticalAverage * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(noDataRectF.left, y);
                defaultXGridLinesPath.lineTo(noDataRectF.right, y);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }
        }
    }

    public boolean isNeedNoDataGrid() {
        return isNeedNoDataGrid;
    }

    public void setNeedNoDataGrid(boolean needNoDataGrid) {
        isNeedNoDataGrid = needNoDataGrid;
    }

    public float getNoDataOffset() {
        return noDataOffset;
    }

    public void setNoDataOffset(float noDataOffset) {
        this.noDataOffset = noDataOffset;
    }

    public float getNoDataLeftOffset() {
        return noDataLeftOffset;
    }

    public void setNoDataLeftOffset(float noDataLeftOffset) {
        this.noDataLeftOffset = noDataLeftOffset;
    }

    public float getNoDataTopOffset() {
        return noDataTopOffset;
    }

    public void setNoDataTopOffset(float noDataTopOffset) {
        this.noDataTopOffset = noDataTopOffset;
    }

    public float getNoDataRightOffset() {
        return noDataRightOffset;
    }

    public void setNoDataRightOffset(float noDataRightOffset) {
        this.noDataRightOffset = noDataRightOffset;
    }

    public float getNoDataBottomOffset() {
        return noDataBottomOffset;
    }

    public void setNoDataBottomOffset(float noDataBottomOffset) {
        this.noDataBottomOffset = noDataBottomOffset;
    }
}
