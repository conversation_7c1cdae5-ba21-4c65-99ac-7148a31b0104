package com.dinsafer.module.powerstation.dialog;

import android.app.Dialog;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Window;
import android.view.WindowManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogNotDevcieFoundBinding;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.util.ScreenUtils;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 14:21
 * @description :
 */
public class NotDeviceFoundDialog extends Dialog {

    private DialogNotDevcieFoundBinding mBinding;

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        Window window = getWindow();
        window.setLayout(ScreenUtils.getScreenWidth(getContext())/5*4, WindowManager.LayoutParams.WRAP_CONTENT);
        window.setGravity(Gravity.CENTER);
    }

    public NotDeviceFoundDialog(@NonNull Context context) {
        super(context, R.style.SosDialogStyle);
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_not_devcie_found, null, false);
        setContentView(mBinding.getRoot());
        mBinding.tvLeft.setOnClickListener(v -> {
            if (callback != null) {
                callback.onLeft();
            }
        });

        mBinding.tvRight.setOnClickListener(v -> {
            if (callback != null) {
                callback.onRight();
            }
        });
    }

    private OnCallback callback;

    public void setCallback(OnCallback callback) {
        this.callback = callback;
    }

    public interface OnCallback {
        void onLeft();
        void onRight();
    }
}
