package com.dinsafer.module.powerstation;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentCurrentDiagramBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.ev.PSEVChargeV3Fragment;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.BmtGraphicUpdateEvent;
import com.dinsafer.module.powerstation.event.BmtShowUpdateDialogEvent;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module.powerstation.event.ReOpenEvent;
import com.dinsafer.module.powerstation.widget.BatteryChargeView;
import com.dinsafer.module.powerstation.widget.currentdiagramview.PathBean;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.UnitUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/23 10:55
 * @description :
 */
public class CurrentDiagramFragment extends MyBaseFragment<FragmentCurrentDiagramBinding> implements IDeviceCallBack {

    private boolean mIsLoading;
    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private Map<String, Object> params = new HashMap<>();
    private boolean mpptInputOn;
    private boolean cityInputOn;
    private boolean evInputOn;
    private boolean isBSensorInputOn;
    private int bSensorInput;
    private int startColor;
    private int endColor;
    private int ev;

    private boolean inverterOutputOn; //是否打开了逆变器通过市电入口输出
    private boolean otherOutOn; //是否打开了逆变器输出到其他负载
    private boolean evOutputOn;  // 是否打开了逆变器输出到车充
    private int additionalVal;
    private boolean needCityInputPath;

    private String subCategory;
    private LottieManager mLottieManager;
    private boolean isThreePhase;
    private int mPhaseCount;
    private int mIP2;
    private int mOP2;
    private int citySourceCount = 0;
    private int mIP5;
    private List<Boolean> isOverLoadList = new ArrayList<>();

    private float mSmartReserve = 0.9f;
    private float mEmergencyReserve = 0.5f;
    private int mChipsStatus = 0;
    private int mPercent;

    public static CurrentDiagramFragment newInstance(String deviceId, String subCategory) {
        CurrentDiagramFragment fragment = new CurrentDiagramFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subCategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_current_diagram;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        EventBus.getDefault().register(this);
        mBinding.llVehicle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (BmtUtil.isDeviceConnected(mPSDevice)) {
                    getDelegateActivity().addCommonFragment(PSEVChargeV3Fragment.newInstance(mDeviceId, mSubcategory));
                }
            }
        });
        mBinding.ivUpdating.setOnClickListener(v -> {
            if (TextUtils.isEmpty(mDeviceId) || mChipsStatus == 0) {
                return;
            }

            EventBus.getDefault().post(new BmtShowUpdateDialogEvent(mDeviceId, mSubcategory, mChipsStatus));
        });

        if (null != mPSDevice) {
            subCategory = mPSDevice.getSubCategory();
        }
        isThreePhase = !TextUtils.isEmpty(subCategory) && subCategory.equals(PSKeyConstant.HP5000);
        mPhaseCount = isThreePhase ? 3 : 1;
        for (int i = 0; i < mPhaseCount; i++) {
            isOverLoadList.add(false);
        }

        submitCmd(BmtCmd.GET_CHIPS_STATUS, 0);
        submitCmd(DsCamCmd.GET_INVERTER_INPUT_INFO, 0);
        BmtManager.mIndexMap.put(mDeviceId, 0);
//        submitCmd(DsCamCmd.GET_INVERTER_INFO, 0);
        submitCmd(DsCamCmd.GET_MODE, 0);
        submitCmd(BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO, 0);
        mLottieManager = new LottieManager();
        updateByDeviceOnlineStatus();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, subCategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    private void submitCmd(String cmd, int index) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            switch (cmd) {
                case DsCamCmd.GET_INVERTER_INFO:
                    params.put(PSKeyConstant.INDEX, index);
                    break;

                case DsCamCmd.SET_INVERTER_OPEN:
                    params.put(PSKeyConstant.ON, true);
                    List<Integer> indexs = new ArrayList<>();
                    for (int i = 0; i < mPhaseCount; i++) {
                        indexs.add(i);
                    }
                    params.put(PSKeyConstant.INDEXS, indexs);
                    break;

            }
            mPSDevice.submit(params);
        }
    }

    @Override
    public void onDestroyView() {
        mBinding.cdvInverter.stopAnim();
        EventBus.getDefault().unregister(this);
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    /**
     * 显示中间开关
     */
    private void showSwitchStatus(boolean show, boolean loading) {
        if (show) {
            mBinding.rippleBackground.startRippleAnimation();
        } else {
            mBinding.rippleBackground.stopRippleAnimation();
        }
        mBinding.clSwitch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!mIsLoading) {
                    setLoading(true);
                    submitCmd(DsCamCmd.SET_INVERTER_OPEN, 0);
                }
            }
        });
        if (show) {
            setLoading(loading);
        } else {
            setLoading(false);
        }
        mBinding.clSwitch.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
    }

    /**
     * 是否loading状态
     *
     * @param isLoading
     */
    private void setLoading(boolean isLoading) {
        mIsLoading = isLoading;
        mBinding.rippleBackground.setVisibility(isLoading ? View.INVISIBLE : View.VISIBLE);
        if (isLoading) {
            mBinding.ivSwitch.setImageTintList(null);
            mBinding.ivSwitch.setImageResource(R.drawable.icon_plugin_list_status_loading);
            mBinding.ivSwitch.startAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.rotation));
        } else {
            mBinding.ivSwitch.clearAnimation();
            mBinding.ivSwitch.setImageResource(R.drawable.icon_power_restart);
        }
    }

    private void showOverLoadStatus() {
        if (isOverLoadList.contains(true)) {
            showSwitchStatus(true, false);
            mBinding.cdvInverter.stopAnim();
        } else {
            showSwitchStatus(false, false);
        }
    }

    /**
     * BMT状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtDeviceStatusChange event) {
        int connectStatus = event.getConnect_status();
        String deviceId = event.getDeviceID();
        if (deviceId != null && deviceId.equals(mDeviceId) && event.getSubcategory().equals(mSubcategory)) {
            updateByDeviceOnlineStatus();
        }
    }

    /**
     * 重启逆变器/EV事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventReOpen(ReOpenEvent event) {
        mBinding.cdvInverter.stopAnim();
        mBinding.tvSolarVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
        mBinding.tvGridVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
        mBinding.tvExtraSupplyVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
        mBinding.tvSecureSupplyVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
        mBinding.tvVehicleVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
        mBinding.tvBatteryVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
        mBinding.viewBattery.setProgress(0, true);
        mLottieManager.controlSolarAnim(mBinding.lavSolar, LottieManager.SOLAR_NULL);
        mLottieManager.controlVehicleAnim(mBinding.lavVehicle, LottieManager.VEHICLE_NULL);
        mLottieManager.controlKeepOnLoad(mBinding.ivSecureSupply, LottieManager.KEEP_ON_LOAD_OFF);
        mLottieManager.controlAdditionLoad(mBinding.ivExtraSupply, LottieManager.ADDITION_LOAD_OFF);
        showSwitchStatus(true, true);
    }

    private void updateByDeviceOnlineStatus() {
        final boolean online = BmtUtil.isDeviceConnected(mPSDevice);
        mBinding.viewBattery.setOnline(online);
        if (!online) {
            mBinding.cdvInverter.stopAnim();
            mBinding.tvSolarVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
            mBinding.tvGridVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
            mBinding.tvExtraSupplyVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
            mBinding.tvSecureSupplyVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
            mBinding.tvVehicleVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
            mBinding.tvBatteryVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
            mBinding.cdvInverter.stopAnim();
            mBinding.clParent.setAlpha(0.5f);
            changeViewStateByUpdateState(false);
        }
    }

    private void setMode(int mode) {
        if (mode < 0 || mode > 4) return;
        int[] modeIcons = {R.drawable.icon_power_lightning, R.drawable.icon_power_lightning_mode1,
                R.drawable.icon_power_lightning_mode2, R.drawable.icon_power_lightning_mode3,
                R.drawable.icon_power_lightning_mode4};
        mBinding.ivMode.setImageResource(modeIcons[mode]);
    }

    private void dealOverLoad(String deviceId, String subCategory, String cmd, Map<String, Object> result) {
        List<Integer> exceptions = (List<Integer>) MapUtils.get(result, PSKeyConstant.EXCEPTIONS, null);
        int index = (int) MapUtils.get(result, PSKeyConstant.INDEX, 0);
        if (index < 0 || index >= mPhaseCount) return;
        isOverLoadList.set(index, BmtUtil.isOverLoad(deviceId, subCategory, cmd, result));
        showOverLoadStatus();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BmtGraphicUpdateEvent event) {
        if (!event.isGraphicCurrent()) {
            return;
        }
        final String deviceId = event.getDeviceId();
        final String deviceSub = event.getSubCategory();
        final String cmd = event.getCmd();
        final Map map = event.getData();

        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && deviceSub.equals(mSubcategory)
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (StringUtil.isNotEmpty(cmd) && cmd.equals(DsCamCmd.REBOOT_INVERTER)) {
                if (status == StatusConstant.STATUS_SUCCESS) {
                    submitCmd(DsCamCmd.GET_INVERTER_INPUT_INFO, 0);
                } else {
                    showSwitchStatus(true, false);
                    showErrorToast();
                }
            }
            if (status == StatusConstant.STATUS_SUCCESS) {

                if (MapUtils.isNotEmpty(result)) {
                    boolean on;
                    switch (cmd) {
                        case DsCamCmd.GET_INVERTER_INPUT_INFO:
//                            evInputOn = (boolean) MapUtils.get(result, PSKeyConstant.EV_ON, false);
//                            ev = (int) MapUtils.get(result, PSKeyConstant.EV, 0);
//                            if (ev < 0) {
//                                ev = 0;
//                            }
//                            if (evInputOn) {
//                                mBinding.cdvInverter.setPathMap(PathBean.VEHICLE_KEY, new PathBean(PathBean.VEHICLE_SUPPLY));
//                            }
////                            mBinding.tvVehicleVal.setLocalText(evInputOn ? UnitUtil.w2kwStrOnePoint(ev) : getString(R.string.power_station_cdv_zero));
                            isBSensorInputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_INPUT_ON, false);
//                            bSensorInput = (int) MapUtils.get(result, PSKeyConstant.B_SENSOR_INPUT, 0);
//                            if (bSensorInput < 0) {
//                                bSensorInput = 0;
//                            }
//                            int mpptCount = 0;
//                            citySourceCount = 0;
//                            mIP5 = 0;
//                            for (int i = 0; i < mPhaseCount; i++) {
//                                Map<String, Object> phase = (Map<String, Object>) MapUtils.get(result, String.valueOf(i), null);
//                                boolean citySourceOn = (boolean) MapUtils.get(phase, PSKeyConstant.CITY_SOURCE_ON, false);
//                                if (citySourceOn) {
//                                    cityInputOn = true;
//                                    citySourceCount = citySourceCount + (int) MapUtils.get(phase, PSKeyConstant.CITY_SOURCE, 0);
//                                }
//                                boolean mpptOn = (boolean) MapUtils.get(phase, PSKeyConstant.MPPT_ON, false);
//                                if (mpptOn) {
//                                    mpptInputOn = true;
//                                }
//                                mpptCount = mpptCount + (int) MapUtils.get(phase, PSKeyConstant.MPPT, 0);
//                                mIP5 = mpptCount;
//                            }
//                            if (citySourceCount < 0) {
//                                citySourceCount = 0;
//                            }
//                            mIP2 = citySourceCount;
//                            if (mpptCount < 0) {
//                                mpptCount = 0;
//                            }
//                            if (mpptInputOn) {
//                                mBinding.cdvInverter.setPathMap(PathBean.SOLAR_KEY, new PathBean(PathBean.SOLAR_SUPPLY));
//                            }
//                            mBinding.tvSolarVal.setLocalText(mpptInputOn ? UnitUtil.w2kwStrOnePoint(mpptCount) : getString(R.string.power_station_cdv_zero));
                            break;

                        case DsCamCmd.GET_INVERTER_OUTPUT_INFO:
                            boolean bSenorOutputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_OUTPUT_ON, false);
                            PowerStationRootFragment.isAllBSensorInstalled = isBSensorInputOn || bSenorOutputOn;
//                            boolean bSenorOutputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_OUTPUT_ON, false);
//                            PowerStationRootFragment.isBSenorOutputOn = bSenorOutputOn;
//                            int bSensorOutput = (int) MapUtils.get(result, PSKeyConstant.B_SENSOR_OUTPUT, 0);
//                            if (bSensorOutput < 0) {
//                                bSensorOutput = 0;
//                            }
//                            boolean bSenorOutputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_OUTPUT_ON, false);
//                            PowerStationRootFragment.isBSenorOutputOn = bSenorOutputOn;
//                            evOutputOn = (boolean) MapUtils.get(result, PSKeyConstant.EV_ON, false);
//                            int evOut = (int) MapUtils.get(result, PSKeyConstant.EV, 0);
//                            if (evInputOn) {
//                                mBinding.tvVehicleVal.setLocalText(UnitUtil.w2kwStrOnePoint(ev));
//                            } else {
//                                mBinding.tvVehicleVal.setLocalText(evOutputOn ? UnitUtil.w2kwStrOnePoint(evOut) : getString(R.string.power_station_cdv_zero));
//                            }
//                            int inverterInVal = 0;
//                            int otherOutVal = 0;
//                            otherOutOn = false;
//
//                            for (int i = 0; i < mPhaseCount; i++) {
//                                Map<String, Object> phase = (Map<String, Object>) MapUtils.get(result, String.valueOf(i), null);
//                                boolean inverterInOn = (boolean) MapUtils.get(phase, PSKeyConstant.INVERTER_IN_ON, false);
//                                if (inverterInOn) {
//                                    inverterOutputOn = true;
//                                    Integer inverterIn = (Integer) MapUtils.get(phase, PSKeyConstant.INVERTER_IN, 0);
//                                    if (inverterIn != null) {
//                                        inverterInVal = inverterInVal + (int) MapUtils.get(phase, PSKeyConstant.INVERTER_IN, 0);
//                                    }
//                                }
//
//                                otherOutOn = (boolean) MapUtils.get(phase, PSKeyConstant.OTHER_OUT_ON, false);
//                                otherOutVal = otherOutVal + (int) MapUtils.get(phase, PSKeyConstant.OTHER_OUT, 0);
//
//                                boolean batteryOn = (boolean) MapUtils.get(phase, PSKeyConstant.BATTERY_ON, false);
//                            }
//                            if (inverterInVal < 0) {
//                                inverterInVal = 0;
//                            }
//                            mOP2 = inverterInVal;
//                            if (otherOutVal < 0) {
//                                otherOutVal = 0;
//                            }
//                            if (evOut < 0) {
//                                evOut = 0;
//                            }
////                            ip5 = 读取inverter输入的太阳能功率（单相就一个，三相3个相加）
////                            op1 = 读取inverter输出的inverterIn字段（单相就一个，三相3个相加）
////                            op4 = 读取inverter输出的ev字段
//                            if (mpptInputOn && mLottieManager != null) {
//                                int op1 = otherOutVal;
//                                int op4 = evOut;
//                                int mpptStatus = LottieManager.SOLAR_NULL;
//                                if (mIP5 >= (op1 + op4) * 0.2 && mIP5 < (op1 + op4)) {
//                                    mpptStatus = LottieManager.SOLAR_LOW;
//                                }
//                                if (mIP5 >= (op1 + op4)) {
//                                    mpptStatus = LottieManager.SOLAR_HIGH;
//                                }
//                                mLottieManager.controlSolarAnim(mBinding.lavSolar, mpptStatus);
//                            }
//
//                            mBinding.tvSecureSupplyVal.setLocalText(otherOutOn ? UnitUtil.w2kwStrOnePoint(otherOutVal) : getString(R.string.power_station_cdv_zero));
//
//                            additionalVal = 0;
//
//                            needCityInputPath = false;
//                            if (!isBSensorInputOn && !bSenorOutputOn) {  // 没有bsensor
//                                mBinding.tvExtraSupplyVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
//                                setAdditionalVal(false);
//                                if (citySourceCount >= inverterInVal) {
//                                    mBinding.tvGridVal.setText(UnitUtil.w2kwStrOnePoint(citySourceCount));
//                                    mBinding.cdvInverter.setPathMap(PathBean.GRID_KEY, new PathBean(PathBean.GRID_SUPPLY));
//                                    needCityInputPath = citySourceCount > 0;
//                                    inverterOutputOn = false;
//                                } else {
//                                    mBinding.tvGridVal.setText(UnitUtil.w2kwStrOnePoint(inverterInVal));
//                                    needCityInputPath = false;
//                                    inverterOutputOn = inverterInVal > 0;
//                                }
//                            } else {
//                                PowerStationRootFragment.isAllBSensorInstalled = true;
//                                if (mIP2 >= mOP2) {
//                                    if (bSensorInput >= bSensorOutput) {
//                                        mBinding.tvGridVal.setText(UnitUtil.w2kwStrOnePoint(bSensorInput));
//                                        additionalVal = Math.abs(bSensorInput) - mIP2;
//                                        if (additionalVal < 0) {
//                                            additionalVal = 0;
//                                        }
//                                        mBinding.tvExtraSupplyVal.setLocalText(UnitUtil.w2kwStrOnePoint(additionalVal));
//                                        setAdditionalVal(true);
//                                        needCityInputPath = bSensorInput > 0;
//                                        inverterOutputOn = false;
//                                    } else {
//                                        mBinding.tvGridVal.setText(UnitUtil.w2kwStrOnePoint(bSensorOutput));
//                                        mBinding.tvExtraSupplyVal.setLocalText(getString(R.string.power_station_cdv_offline_val));
//                                        setAdditionalVal(false);
//                                        needCityInputPath = false;
//                                        inverterOutputOn = bSensorOutput > 0;
//                                    }
//                                } else {
//                                    if (bSensorInput >= bSensorOutput) {
//                                        mBinding.tvGridVal.setText(UnitUtil.w2kwStrOnePoint(bSensorInput));
//                                        additionalVal = bSensorInput + mOP2;
//                                        if (additionalVal < 0) {
//                                            additionalVal = 0;
//                                        }
//                                        mBinding.tvExtraSupplyVal.setLocalText(UnitUtil.w2kwStrOnePoint(additionalVal));
//                                        setAdditionalVal(true);
//                                        needCityInputPath = bSensorInput > 0;
//                                        inverterOutputOn = false;
//                                    } else {
//                                        mBinding.tvGridVal.setText(UnitUtil.w2kwStrOnePoint(bSensorOutput));
//                                        additionalVal = mOP2 - bSensorOutput;
//                                        if (additionalVal < 0) {
//                                            additionalVal = 0;
//                                        }
//                                        mBinding.tvExtraSupplyVal.setLocalText(UnitUtil.w2kwStrOnePoint(additionalVal));
//                                        setAdditionalVal(true);
//                                        needCityInputPath = false;
//                                        inverterOutputOn = bSensorOutput > 0;
//                                    }
//                                }
//                            }
//                            mLottieManager.controlKeepOnLoad(mBinding.ivSecureSupply, otherOutOn ? LottieManager.KEEP_ON_LOAD_ON : LottieManager.KEEP_ON_LOAD_OFF);
//                            mLottieManager.controlAdditionLoad(mBinding.ivExtraSupply, additionalVal > 0 ? LottieManager.ADDITION_LOAD_ON : LottieManager.ADDITION_LOAD_OFF);
//
//                            PathBean mPPTPathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.SOLAR_KEY);
//                            if (mPPTPathBean != null) {
//                                if (mpptInputOn) {
//                                    mPPTPathBean.setDrawPath2(evOutputOn);
//                                    mPPTPathBean.setDrawPath3(otherOutOn);
//                                    mPPTPathBean.setDrawPath4(additionalVal > 0);
//                                    mPPTPathBean.setDrawPath5(inverterOutputOn);
//                                } else {
//                                    mPPTPathBean.setDrawPath2(false);
//                                    mPPTPathBean.setDrawPath3(false);
//                                    mPPTPathBean.setDrawPath4(false);
//                                    mPPTPathBean.setDrawPath5(false);
//                                }
//                            }
////                            boolean needCityInputPath = isBSensorInputOn || (!isBSensorInputOn && !inverterOutputOn && cityInputOn);
//                            if (needCityInputPath) {
//                                mBinding.cdvInverter.setPathMap(PathBean.GRID_KEY, new PathBean(PathBean.GRID_SUPPLY));
//                            }
//
//                            PathBean gridPathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.GRID_KEY);
//                            if (gridPathBean != null) {
//                                if (needCityInputPath) {
//                                    gridPathBean.setDrawPath2(evOutputOn);
//                                    gridPathBean.setDrawPath3(otherOutOn);
//                                    gridPathBean.setDrawPath4(additionalVal > 0);
//                                } else {
//                                    gridPathBean.setDrawPath2(false);
//                                    gridPathBean.setDrawPath3(false);
//                                    gridPathBean.setDrawPath4(false);
//                                }
//                            }
//
//
//                            PathBean evPathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.VEHICLE_KEY);
//                            if (evPathBean != null) {
//                                if (evInputOn) {
//                                    evPathBean.setDrawPath2(otherOutOn);
//                                    evPathBean.setDrawPath3(additionalVal > 0);
//                                    evPathBean.setDrawPath4(inverterOutputOn);
//                                } else {
//                                    evPathBean.setDrawPath2(false);
//                                    evPathBean.setDrawPath3(false);
//                                    evPathBean.setDrawPath4(false);
//                                }
//                            }

                            /**
                             *                              EV-out(OP4)+家庭保障供应(OP1)
                             *  负载饱和度        =   ------------------------------------------- *100%
                             * Load Saturation      逆变设计负载上限（单相电3.6kW   /  三相电10.8kW）
                             */
//                            float limited = PSKeyConstant.HP5001.equals(subCategory) ? 3600f : 10800f;
//                            float load = (evOut + otherOutVal) / limited * 100;
//                            startColor = getColor(R.color.ps_current_path_start_color_1);
//                            endColor = getColor(R.color.ps_current_path_end_color_1);
//                            if (load < 30) {
//                                startColor = getColor(R.color.ps_current_path_start_color_1);
//                                endColor = getColor(R.color.ps_current_path_end_color_1);
//                            } else if (load >= 30 && load < 65) {
//                                startColor = getColor(R.color.ps_current_path_start_color_2);
//                                endColor = getColor(R.color.ps_current_path_end_color_2);
//                            } else if (load >= 65 && load < 90) {
//                                startColor = getColor(R.color.ps_current_path_start_color_3);
//                                endColor = getColor(R.color.ps_current_path_end_color_3);
//                            } else if (load >= 90) {
//                                startColor = getColor(R.color.ps_current_path_start_color_4);
//                                endColor = getColor(R.color.ps_current_path_end_color_4);
//                            }
//
//                            showOverLoadStatus();
//
//                            mBinding.clParent.setAlpha(1f);
                            // BmtManager统一处理
                            // EventBus.getDefault().postSticky(new BatteryStatusEvent(batteryStatus));
                            break;

                        case BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO:
                            int batteryWat = DeviceHelper.getInt(result, BmtDataKey.BATTERY_WAT, 0);
                            int solarWat = DeviceHelper.getInt(result, BmtDataKey.SOLAR_WAT, 0);
                            int gridWat = DeviceHelper.getInt(result, BmtDataKey.GRID_WAT, 0);
                            int additionWat = DeviceHelper.getInt(result, BmtDataKey.ADDITIONAL_LOAD_WAT, 0);
                            int otherWat = DeviceHelper.getInt(result, BmtDataKey.OTHER_LOAD_WAT, 0);
                            int vehicleWat = DeviceHelper.getInt(result, BmtDataKey.VECHI_WAT, 0);
                            mBinding.tvBatteryVal.setText(UnitUtil._100WToKW(Math.abs(batteryWat)));
                            mBinding.tvSolarVal.setText(UnitUtil._100WToKW(Math.abs(solarWat)));
                            mBinding.tvGridVal.setText(UnitUtil._100WToKW(Math.abs(gridWat)));
                            mBinding.tvExtraSupplyVal.setText(UnitUtil._100WToKW(Math.abs(additionWat)));
                            mBinding.tvSecureSupplyVal.setText(UnitUtil._100WToKW(Math.abs(otherWat)));
                            mBinding.tvVehicleVal.setText(UnitUtil._100WToKW(Math.abs(vehicleWat)));
                            PathBean batteryPathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.BATTERY_KEY);
                            if (batteryWat > 0) {
                                if (batteryPathBean == null) {
                                    batteryPathBean = new PathBean(PathBean.BATTERY_SUPPLY);
                                    mBinding.cdvInverter.setPathMap(PathBean.BATTERY_KEY, batteryPathBean);
                                }
                                batteryPathBean.setDrawPath1(vehicleWat < 0);
                                batteryPathBean.setDrawPath2(otherWat < 0);
                                batteryPathBean.setDrawPath3(additionWat < 0);
                                batteryPathBean.setDrawPath4(gridWat < 0);
                            } else {
                                if (batteryPathBean != null) {
                                    batteryPathBean.setAllPathFalse();
                                }
                            }
                            PathBean solarPathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.SOLAR_KEY);
                            if (solarWat > 0) {
                                if (solarPathBean == null) {
                                    solarPathBean = new PathBean(PathBean.SOLAR_SUPPLY);
                                    mBinding.cdvInverter.setPathMap(PathBean.SOLAR_KEY, solarPathBean);
                                }
                                solarPathBean.setDrawPath1(batteryWat < 0);
                                solarPathBean.setDrawPath2(vehicleWat < 0);
                                solarPathBean.setDrawPath3(otherWat < 0);
                                solarPathBean.setDrawPath4(additionWat < 0);
                                solarPathBean.setDrawPath5(gridWat < 0);
                                if (mLottieManager != null) {
                                    int op1 = Math.abs(otherWat);
                                    int op4 = vehicleWat > 0 ? 0 : Math.abs(vehicleWat);
                                    mIP5 = solarWat;
                                    int mpptStatus = LottieManager.SOLAR_NULL;
                                    if (mIP5 >= (op1 + op4) * 0.2 && mIP5 < (op1 + op4)) {
                                        mpptStatus = LottieManager.SOLAR_LOW;
                                    }
                                    if (mIP5 >= (op1 + op4)) {
                                        mpptStatus = LottieManager.SOLAR_HIGH;
                                    }
                                    mLottieManager.controlSolarAnim(mBinding.lavSolar, mpptStatus);
                                }
                            } else {
                                if (solarPathBean != null) {
                                    solarPathBean.setAllPathFalse();
                                }
                                if (mLottieManager != null) {
                                    mLottieManager.controlSolarAnim(mBinding.lavSolar, LottieManager.SOLAR_NULL);
                                }
                            }
                            PathBean gridPathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.GRID_KEY);
                            if (gridWat > 0) {
                                if (gridPathBean == null) {
                                    gridPathBean = new PathBean(PathBean.GRID_SUPPLY);
                                    mBinding.cdvInverter.setPathMap(PathBean.GRID_KEY, gridPathBean);
                                }
                                gridPathBean.setDrawPath1(batteryWat < 0);
                                gridPathBean.setDrawPath2(vehicleWat < 0);
                                gridPathBean.setDrawPath3(otherWat < 0);
                                gridPathBean.setDrawPath4(additionWat < 0);
                            } else {
                                if (gridPathBean != null) {
                                    gridPathBean.setAllPathFalse();
                                }
                            }

                            PathBean vehiclePathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.VEHICLE_KEY);
                            if (vehicleWat > 0) {
                                if (vehiclePathBean == null) {
                                    vehiclePathBean = new PathBean(PathBean.VEHICLE_SUPPLY);
                                    mBinding.cdvInverter.setPathMap(PathBean.VEHICLE_KEY, vehiclePathBean);
                                }
                                vehiclePathBean.setDrawPath1(batteryWat < 0);
                                vehiclePathBean.setDrawPath2(otherWat < 0);
                                vehiclePathBean.setDrawPath3(additionWat < 0);
                                vehiclePathBean.setDrawPath4(gridWat < 0);
                            } else {
                                if (vehiclePathBean != null) {
                                    vehiclePathBean.setAllPathFalse();
                                }
                            }
                            if (mLottieManager != null) {
                                mLottieManager.controlKeepOnLoad(mBinding.ivSecureSupply, otherWat < 0 ? LottieManager.KEEP_ON_LOAD_ON : LottieManager.KEEP_ON_LOAD_OFF);
                                mLottieManager.controlAdditionLoad(mBinding.ivExtraSupply, additionWat < 0 ? LottieManager.ADDITION_LOAD_ON : LottieManager.ADDITION_LOAD_OFF);
                            }
                            /**
                             *                              EV-out(OP4)+家庭保障供应(OP1)
                             *  负载饱和度        =   ------------------------------------------- *100%
                             * Load Saturation      逆变设计负载上限（单相电3.6kW   /  三相电10.8kW）
                             */
                            float limited = PSKeyConstant.HP5001.equals(subCategory) ? 3600f : 10800f;
                            float load = ((vehicleWat < 0 ? Math.abs(vehicleWat) * 100 : 0) +
                                    (otherWat < 0 ? Math.abs(otherWat) * 100 : 0)) / limited * 100;
                            startColor = getColor(R.color.ps_current_path_start_color_1);
                            endColor = getColor(R.color.ps_current_path_end_color_1);
                            if (load < 30) {
                                startColor = getColor(R.color.ps_current_path_start_color_1);
                                endColor = getColor(R.color.ps_current_path_end_color_1);
                            } else if (load >= 30 && load < 65) {
                                startColor = getColor(R.color.ps_current_path_start_color_2);
                                endColor = getColor(R.color.ps_current_path_end_color_2);
                            } else if (load >= 65 && load < 90) {
                                startColor = getColor(R.color.ps_current_path_start_color_3);
                                endColor = getColor(R.color.ps_current_path_end_color_3);
                            } else if (load >= 90) {
                                startColor = getColor(R.color.ps_current_path_start_color_4);
                                endColor = getColor(R.color.ps_current_path_end_color_4);
                            }
                            int batteryStatus = BatteryChargeView.NORMAL;
                            if (batteryWat > 0) {
                                batteryStatus = BatteryChargeView.DISCHARGING;
                            }
                            if (batteryWat < 0) {
                                batteryStatus = BatteryChargeView.CHARGING;
                            }
                            mBinding.viewBattery.setChargeStatus(batteryStatus, false);
                            showOverLoadStatus();
                            mBinding.clParent.setAlpha(1f);
                            mBinding.cdvInverter.setSameColor(startColor, endColor);
                            mBinding.cdvInverter.startAnim();
                            break;

                        case DsCamCmd.GET_BATTERY_ALLINFO:
                            mPercent = (int) MapUtils.get(result, PSKeyConstant.SOC, 0);
                            mBinding.viewBattery.setOnline(true);
//                            int totalVolt = (int) MapUtils.get(result, PSKeyConstant.TOTAL_VOLT, 0);
//                            int ampere = (int) MapUtils.get(result, PSKeyConstant.AMPERE, 0);
//                            double total = 1.0 * totalVolt * ampere;
//                            double balance = Math.abs(total) / 1000.0 / 1000.0 / 1000.0;
//                            balance = UnitUtil.savePoint(balance, 1);
//                            String balanceStr = String.valueOf(balance);
//                            balanceStr = UnitUtil.trimPointZero(balanceStr);
//                            mBinding.tvBatteryVal.setLocalText(balanceStr);
//                            if (ampere < 0) {
//                                mBinding.cdvInverter.setPathMap(PathBean.BATTERY_KEY, new PathBean(PathBean.BATTERY_SUPPLY));
//                            }
//                            PathBean pathBean = mBinding.cdvInverter.getPathBeanByKey(PathBean.BATTERY_KEY);
//                            if (pathBean != null) {
//                                if (ampere < 0) {
//                                    pathBean.setDrawPath1(evOutputOn);
//                                    pathBean.setDrawPath2(otherOutOn);
//                                    pathBean.setDrawPath3(additionalVal > 0);
//                                    pathBean.setDrawPath4(inverterOutputOn);
//                                } else {
//                                    pathBean.setDrawPath1(false);
//                                    pathBean.setDrawPath2(false);
//                                    pathBean.setDrawPath3(false);
//                                    pathBean.setDrawPath4(false);
//                                }
//                            }
//
//                            int batteryStatus = BatteryChargeView.NORMAL;
//                            if (ampere < 0) {
//                                batteryStatus = BatteryChargeView.DISCHARGING;
//                            }
//                            if (ampere > 0) {
//                                batteryStatus = BatteryChargeView.CHARGING;
//                            }
//                            mBinding.viewBattery.setChargeStatus(batteryStatus, false);
//                            PathBean solarPB = mBinding.cdvInverter.getPathBeanByKey(PathBean.SOLAR_KEY);
//                            if (solarPB != null && mpptInputOn) {
//                                solarPB.setDrawPath1(batteryStatus > 0);
//                            }
//                            PathBean gridPB = mBinding.cdvInverter.getPathBeanByKey(PathBean.GRID_KEY);
//                            if (gridPB != null && needCityInputPath) {
//                                gridPB.setDrawPath1(batteryStatus > 0);
//                            }
//                            PathBean vehiclePB = mBinding.cdvInverter.getPathBeanByKey(PathBean.VEHICLE_KEY);
//                            if (vehiclePB != null && evInputOn) {
//                                vehiclePB.setDrawPath1(batteryStatus > 0);
//                            }
//                            mBinding.cdvInverter.setSameColor(startColor, endColor);
//                            mBinding.cdvInverter.startAnim();
                            break;

                        case DsCamCmd.SET_INVERTER_OPEN:

                            break;

                        case DsCamCmd.GET_GLOBAL_LOADSTATE:
                            on = (boolean) MapUtils.get(result, PSKeyConstant.ON, false);

                            break;

                        case DsCamCmd.GET_MPPT_STATE:
//                            on = (boolean) MapUtils.get(result, PSKeyConstant.ON_0, false) ||
//                                    (boolean) MapUtils.get(result, PSKeyConstant.ON_1, false) ||
//                                    (boolean) MapUtils.get(result, PSKeyConstant.ON_2, false);
//                            if (mLottieManager != null) {
//                                mLottieManager.controlSolarAnim(mBinding.lavSolar, on ? LottieManager.SOLAR_HIGH : LottieManager.SOLAR_NULL);
//                            }
                            break;

//                        case DsCamCmd.GET_EV_STATE:
//                            int lightState = (int) MapUtils.get(result, PSKeyConstant.LIGHT_STATE, 1);
//                            if (mLottieManager != null) {
//                                mLottieManager.controlVehicleAnim(mBinding.lavVehicle, lightState);
//                            }
//                            break;

                        case BmtCmd.GET_CURRENT_EVADVANCESTATUS:
                        case BmtCmd.EV_ADVANCESTATUS_CHANGED:
                            int advanceStatus = DeviceHelper.getInt(result, BmtDataKey.ADVANCE_STATUS, 0);
                            if (advanceStatus <= 0 || advanceStatus > 4) return;
                            if (mLottieManager != null) {
                                mLottieManager.controlVehicleAnim(mBinding.lavVehicle, advanceStatus);
                            }
                            break;

                        case DsCamCmd.SYSTEM_EXCEPTION:
                        case DsCamCmd.INVERTER_EXCEPTION:
                        case DsCamCmd.GET_GLOBAL_EXCEPTIONS:
                            dealOverLoad(deviceId, deviceSub, cmd, result);
                            break;

                        case DsCamCmd.GET_MODE:
                            int mode = (int) MapUtils.get(result, PSKeyConstant.MODE, 0);
                            setMode(mode);
                            break;

                        case BmtCmd.GET_CHIPS_STATUS:
                            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
                            if (-1 != chipsStatus) {
                                mChipsStatus = chipsStatus;
                                final boolean showMarker = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//                                final boolean waitForUpdate = BmtUtil.isWaitForUpdate(chipsStatus);
//                                final boolean ignore = BmtManager.getInstance().isIgnoredUpgrade(mDeviceId);
//                                final boolean needUpgrade = waitForUpdate ? !ignore : showMarker;
                                changeViewStateByUpdateState(showMarker);
                            }
                            break;

                        case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                        case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                            int smartReserve = DeviceHelper.getInt(result, BmtDataKey.SMART, 0);
                            int emergencyReserve = DeviceHelper.getInt(result, BmtDataKey.EMERGENCY, 0);
                            float er = emergencyReserve / 100f;
                            float sr = smartReserve / 100f;
                            // if (er == mEmergencyReserve && sr == mSmartReserve) return;
                            mEmergencyReserve = er;
                            mSmartReserve = sr;
                            mBinding.viewBattery.setEmergencyReserve(mEmergencyReserve);
                            mBinding.viewBattery.setSmartReserve(mSmartReserve);
                            mBinding.viewBattery.setProgress(mPercent / 100f, true);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(ChargeModeEvent event) {
        if (event.isOnlyMode()) {
            return;
        }

        int mode = event.getMode();
        int smartReserve = event.getSmartReserve();
        int emergencyReserve = event.getEmergencyReserve();

        float er = emergencyReserve / 100f;
        float sr = smartReserve / 100f;
        mEmergencyReserve = er;
        mSmartReserve = sr;
        mBinding.viewBattery.setEmergencyReserve(mEmergencyReserve);
        mBinding.viewBattery.setSmartReserve(mSmartReserve);
        mBinding.viewBattery.resetColor();
        mBinding.viewBattery.invalidate();
    }

    private void changeViewStateByUpdateState(final boolean showUpdate) {
        if (showUpdate) {
            mBinding.viewDisable.setVisibility(View.VISIBLE);
            mBinding.ivUpdating.setVisibility(View.VISIBLE);
        } else {
            mBinding.viewDisable.setVisibility(View.GONE);
            mBinding.ivUpdating.setVisibility(View.GONE);
        }
    }

    private void setAdditionalVal(boolean enabled) {
        mBinding.llExtraSupply.setAlpha(enabled ? 1f : 0.5f);
        mBinding.tvExtraSupplyKey.setAlpha(enabled ? 1f : 0.5f);
        mBinding.ivExtraSupply.setAlpha(enabled ? 1f : 0.5f);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && deviceId.equals(mDeviceId)
                && subCategory.equals(mPSDevice.getSubCategory())) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);

            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (MapUtils.isNotEmpty(result)) {
                            boolean on;
                            switch (cmd) {
                                case DsCamCmd.SET_INVERTER_OPEN:
                                    break;
                                case DsCamCmd.GET_GLOBAL_LOADSTATE:
                                    on = (boolean) MapUtils.get(result, PSKeyConstant.ON, false);
                                    break;
                            }
                        }
                    }
                });

            }
        }
    }

    private int getColor(int colorId) {
        return getContext().getResources().getColor(colorId);
    }
}
