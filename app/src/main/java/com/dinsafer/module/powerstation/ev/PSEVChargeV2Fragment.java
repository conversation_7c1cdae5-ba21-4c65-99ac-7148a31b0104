package com.dinsafer.module.powerstation.ev;

import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsEvChargeV2Binding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.ExceptionWarning;
import com.dinsafer.module.powerstation.LottieManager;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PSEVChargeV2Model;
import com.dinsafer.module.powerstation.bean.PSEVChargeV2Bean;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.module.powerstation.dialog.WarningDialog;
import com.dinsafer.module.powerstation.event.EVBottomBehaviorEvent;
import com.dinsafer.module.powerstation.event.EVBottomBehaviorStateEvent;
import com.dinsafer.module.powerstation.event.ReOpenEvent;
import com.dinsafer.module.powerstation.event.SetEVChargeModeEvent;
import com.dinsafer.module.powerstation.settings.network.PSNetworkBleScanFragment;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.FeedBackFragment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.CustomSwitch;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;
import com.dinsafer.util.UnitUtil;
import com.google.android.material.bottomsheet.BottomSheetBehavior;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

public class PSEVChargeV2Fragment extends MyBaseFragment<FragmentPsEvChargeV2Binding> implements IDeviceCallBack {

    private CommonPagerAdapter mModeAdapter;
    private ArrayList<BaseFragment> mModeFragments;
    private int mMaxOffset;
    private int mCurrentOffset;

    private BottomSheetBehavior mBottomSheetBehavior;
    private LottieManager mLottieManager;
    private int status = 0;
    private boolean isInstantSelected;

    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;

    private Subscription mPollTimer;
    private boolean isClickInstant;

    private ArrayList<WarningBean> mWarningList = new ArrayList<>();
    private int mStatus;
    private WarningDialog mWarningDialog;

    private BindMultiAdapter<PSEVChargeV2Model> mInstantChargeAdapter;
    private BindMultiAdapter<PSEVChargeV2Model> mNormalChargeAdapter;
    private List<PSEVChargeV2Bean> mChargeModeList;

    private int mEvChargeMode;
    private int mCurrentItem;

    public static PSEVChargeV2Fragment newInstance(String deviceId, String subcategory) {
        PSEVChargeV2Fragment fragment = new PSEVChargeV2Fragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_ev_charge_v2;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        BmtManager.getInstance().stopPolling();
        EventBus.getDefault().register(this);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.ps_ev_charge));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mLottieManager = new LottieManager();
        mBinding.lavEv.setProgress(870 / 993f);
        initViewPager();
        initBottomSheet();
        setEvStatus(0);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (BmtUtil.isDeviceOffline(mPSDevice)) {
            showDeviceOfflineDialog(mPSDevice);
            return;
        }
        mBinding.lavEv.postDelayed(new Runnable() {
            @Override
            public void run() {
                getEVAdvanceStatus();
                getEVChargingInfo();
                getEvChargeMode();
            }
        }, 200);
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        BmtManager.getInstance().startPolling();
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    private void initViewPager() {
        mModeFragments = new ArrayList<>();
        mModeFragments.add(PSEVSonChargeV2Fragment.newInstance(0, mDeviceId, mSubcategory));
        mModeFragments.add(PSEVSonChargeV2Fragment.newInstance(1, mDeviceId, mSubcategory));
        mModeAdapter = new CommonPagerAdapter(getChildFragmentManager(), mModeFragments);
        mBinding.vpMode.setAdapter(mModeAdapter);
        mBinding.vpMode.setOffscreenPageLimit(2);
        mBinding.vpMode.post(new Runnable() {
            @Override
            public void run() {
                mMaxOffset = mBinding.vpMode.getWidth();
            }
        });
        mBinding.vpMode.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                mCurrentItem = position;
                if (mCurrentItem == 0) {
                    ((PSEVSonChargeV2Fragment) mModeFragments.get(1)).resetNormalStatus();
                } else {
                    ((PSEVSonChargeV2Fragment) mModeFragments.get(0)).resetNormalStatus();
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        mCurrentItem = 1;
        mBinding.vpMode.setCurrentItem(1);
    }

    @Override
    public void initListener() {
        super.initListener();

//        mBinding.llStatus.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                if (LottieManager.EV_ERROR == mStatus && CollectionUtil.isListNotEmpty(mWarningList)) {
//                    showWarningDialog();
//                }
//            }
//        });

        mBinding.csInstantCharge.setSwitchListener(new CustomSwitch.OnSwitchListener() {
            @Override
            public void onSwitch(CustomSwitch customSwitch, boolean isOn) {
//                customSwitch.setLoading(true);
                setInstantCharge(isOn);
            }

            @Override
            public void onChange(float progress) {
                if (!mBinding.vpMode.isFakeDragging()) {
                    mBinding.vpMode.beginFakeDrag();
                }
                if (mBinding.vpMode.isFakeDragging()) {
                    int offset = (int) (mMaxOffset * progress);
                    int dragBy = offset - mCurrentOffset;
                    mBinding.vpMode.fakeDragBy(dragBy);
                    mCurrentOffset = offset;
                    if (progress == 0f || progress == 1f) {
                        mBinding.vpMode.endFakeDrag();
                    }
                }
                calculateViewDiff(1f - progress);
            }

            @Override
            public void onStartTouch() {
                mMaxOffset = mBinding.vpMode.getWidth();
                mBinding.vpMode.beginFakeDrag();
            }

            @Override
            public void onEndTouch() {

            }
        });
    }

    private void calculateViewDiff(float progress) {
        int instantHeight = ((PSEVSonChargeV2Fragment) mModeFragments.get(0))
                .getAppliedView(mEvChargeMode <= 4 ? 0 : 1);
        int normalHeight = ((PSEVSonChargeV2Fragment) mModeFragments.get(1))
                .getAppliedView(mEvChargeMode <= 1 ? 0 : 1);
        int diffHeight = instantHeight - normalHeight;
        int diffHeightAbs = Math.abs(diffHeight);
        int height = 0;
        if (diffHeight < 0) {
            height = (int) (instantHeight + diffHeightAbs * progress);
        } else {
            height = (int) (instantHeight - diffHeightAbs * progress);
        }
        resetPeekHeight(height);
    }

    /**
     * 重置bottomSheet高度
     */
    private void resetPeekHeight(int viewHeight) {
        mBinding.viewLine.post(new Runnable() {
            @Override
            public void run() {
                int height = viewHeight +
                        mBinding.tvTitle.getHeight() + DensityUtil.dp2px(getContext(), 63);
                ViewGroup.LayoutParams layoutParams = mBinding.scrollView.getLayoutParams();
                layoutParams.height = mBinding.colParent.getHeight() - height;
                mBinding.scrollView.setLayoutParams(layoutParams);
                mBottomSheetBehavior.setPeekHeight(height);
            }
        });
    }

    private void setCSInstantSWitchEnabled(boolean enabled) {
        mBinding.csInstantCharge.setEnabled(enabled);
    }

    private void showWarningDialog() {
        if (mWarningDialog == null) {
            mWarningDialog = WarningDialog.newInstance(mWarningList);
            mWarningDialog.setWarningListener(new WarningDialog.WarningListener() {
                @Override
                public void onClose() {

                }

                @Override
                public void onRestart(String type) {
//                    submitCmdInverterClose();
                    BmtManager.getInstance().resetInverter(mPSDevice, true);
                }

                @Override
                public void onWithout(String type) {
                }

                @Override
                public void onContactCustomerSupport() {
                    getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
                }
            });
        }
        mWarningDialog.show(getChildFragmentManager(), WarningDialog.TAG);
    }

    /**
     * 初始化bottomSheet
     */
    private void initBottomSheet() {
        mBottomSheetBehavior = BottomSheetBehavior.from(mBinding.rlBottom);
        mBottomSheetBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                mBinding.scrollView.setScrollable(newState == BottomSheetBehavior.STATE_COLLAPSED);
                EventBus.getDefault().post(new EVBottomBehaviorStateEvent(newState));
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mBinding.scrollView.getLayoutParams();
                params.topMargin = (int) (-mBinding.lavEv.getHeight() * slideOffset);
                mBinding.scrollView.setLayoutParams(params);
                float offset = 1 - slideOffset;
                mBinding.lavEv.setAlpha(offset);
                EventBus.getDefault().post(new EVBottomBehaviorEvent(slideOffset));
            }
        });
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    /**
     * 初始化轮询
     */
    private void startPollTimer() {
        closePollTimer();
        mPollTimer = Observable.interval(0, 15, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "subscribe里");
                        getEVChargingInfo();
                    }
                });
    }

    public void closePollTimer() {
        if (mPollTimer != null && !mPollTimer.isUnsubscribed()) {
            mPollTimer.unsubscribe();
        }
    }

    /**
     * 获取EV充电状态信息(0xa027)
     */
    private void getEVAdvanceStatus() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
            mPSDevice.submit(map);
        }
    }

    /**
     * 获取EV充电状态信息(0xa027)
     */
    private void getEVChargingInfo() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_EVCHARGING_INFO);
            mPSDevice.submit(map);
        }
    }


    /**
     * 获取EV充电模式(0xa020)
     */
    private void getEvChargeMode() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(map);
        }
    }


    /**
     * 关闭逆变器
     */
    private void submitCmdInverterClose() {
        if (mPSDevice != null) {
            BmtManager.getInstance().stopPolling();
            EventBus.getDefault().post(new ReOpenEvent(mDeviceId, mSubcategory));
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_INVERTER_OPEN);
            params.put(PSKeyConstant.ON, false);
            List<Integer> indexs = new ArrayList<>();
            int phaseCount = BmtUtil.isThreePhase(mPSDevice) ? 3 : 1;
            for (int i = 0; i < phaseCount; i++) {
                indexs.add(i);
            }
            params.put(PSKeyConstant.INDEXS, indexs);
            mPSDevice.submit(params);
        }
    }

    /**
     * 设置车充状态
     *
     * @param status
     */
    private void setEvStatus(int status) {
        mStatus = status;
        mBinding.clValue.setVisibility(status == LottieManager.EV_CHARGING ?
                View.VISIBLE : View.GONE);

        mBinding.rlBottom.post(new Runnable() {
            @Override
            public void run() {
                ViewGroup.LayoutParams llParams = mBinding.rlBottom.getLayoutParams();
                llParams.height = mBinding.colParent.getHeight() -
                        (status == LottieManager.EV_CHARGING ? mBinding.clValue.getHeight() : 0)
                        - mBinding.tvStatus.getHeight() - DensityUtil.dp2px(getDelegateActivity(),
                        status == LottieManager.EV_CHARGING ? 35 : 25);
                mBinding.rlBottom.setLayoutParams(llParams);
            }
        });

        mLottieManager.controlEV(mBinding.lavEv, status);
        switch (status) {
            case LottieManager.EV_OFF:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_off));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_02));
                break;

            case LottieManager.EV_CHARGING:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_charging));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_01));
                break;

            case LottieManager.EV_WAITING:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_waiting_for_charge));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_01));
                break;

            case LottieManager.EV_ERROR:
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_tip_warning));
                loadEVException();
                break;

            case LottieManager.EV_UNAUTHORIZED:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_unauthorized));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_tip_warning));
                break;
        }
    }

    private void loadEVException() {
        List<Integer> evExceptions = DeviceHelper.getList(mPSDevice, "evExceptions");
        mWarningList.clear();
        if (CollectionUtil.isListNotEmpty(evExceptions)) {
            for (Integer exception : evExceptions) {
                WarningBean warningBean = ExceptionWarning.getEVWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
                if (warningBean == null) {
                    continue;
                }
                mWarningList.add(warningBean);
            }
        }
        String error = CollectionUtil.isListNotEmpty(mWarningList) ? mWarningList.get(0).getTitle()
                : getString(R.string.error);
        mBinding.tvStatus.setLocalText(error);
    }

    private void checkDataNull() {
        if (mInstantChargeAdapter == null) {
            mInstantChargeAdapter = ((PSEVSonChargeV2Fragment) mModeFragments.get(0)).getEVChargeAdapter();
        }
        if (mNormalChargeAdapter == null) {
            mNormalChargeAdapter = ((PSEVSonChargeV2Fragment) mModeFragments.get(1)).getEVChargeAdapter();
        }
        if (mChargeModeList == null) {
            mChargeModeList = new ArrayList<>();
            mChargeModeList.addAll(((PSEVSonChargeV2Fragment) mModeFragments.get(1)).getEVChargeModeList());
            mChargeModeList.addAll(((PSEVSonChargeV2Fragment) mModeFragments.get(0)).getEVChargeModeList());
        }
    }

    private void getSelectViewHeight(PSEVChargeV2Bean chargeV2Bean) {
        if (chargeV2Bean == null) return;
        mBinding.tvModeTitle.setLocalText(chargeV2Bean.getTitle());
        if (chargeV2Bean.getType() == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED) {
            String oldStr = getString(R.string.hashtag_charge_amount);
            String subtitle = Local.s(chargeV2Bean.getSubTitle()).replace(oldStr, chargeV2Bean.getValue() + "kWh");
            mBinding.tvModeSubtitle.setLocalText(subtitle);
        } else if (chargeV2Bean.getType() == PSEVChargeV2Bean.LOWER_UTILITY_RATE) {
            String oldStr = getString(R.string.hashtag_c2);
            String subtitle = Local.s(chargeV2Bean.getSubTitle()).replace(oldStr, chargeV2Bean.getValue() + "%");
            mBinding.tvModeSubtitle.setLocalText(subtitle);
        } else {
            mBinding.tvModeSubtitle.setLocalText(chargeV2Bean.getSubTitle());
        }
        new Handler().postDelayed(() -> {
            int viewHeight = mBinding.clMode.getMeasuredHeight();
            resetPeekHeight(viewHeight);
        }, 200);
    }

    public void showDeviceOfflineDialog(Device device) {
        AlertDialogV2 offlineDialog = AlertDialogV2.createBuilder(getContext())
                .setContent(getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        BmtManager.getInstance().connectDevice(device, true);
                        showTimeOutLoadinFramgmentWithErrorAlert();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        getDelegateActivity().addCommonFragment(PSNetworkBleScanFragment.newInstance(mDeviceId, mSubcategory));
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case BmtCmd.GET_CURRENT_EVADVANCESTATUS:
                            case BmtCmd.EV_ADVANCESTATUS_CHANGED:
                                int status = DeviceHelper.getInt(result, BmtDataKey.ADVANCE_STATUS, 0);
                                if (status <= 0 || status > 4) return;
                                setEvStatus(status - 1);
                                if (status == 3) {
                                    startPollTimer();
                                } else {
                                    closePollTimer();
                                }
                                break;

                            case BmtCmd.GET_EVCHARGING_INFO:
                                int batteryCharged = DeviceHelper.getInt(result, BmtDataKey.BATTERY_CHARGED, 0);
                                int chargeTime = DeviceHelper.getInt(result, BmtDataKey.CHARGE_TIME, 0);
                                double batteryChargedDou = batteryCharged / 10.0d;
                                mBinding.tvValue.setLocalText(UnitUtil.savePointStr(batteryChargedDou, 1) + getString(R.string.power_station_kWh));
                                String hmStr = TimeUtil.minute2HourMinute(chargeTime);
                                String[] hmArr = hmStr.split(":");
                                String timeStr = hmArr[0] + Local.s(getString(R.string.power_h)) + hmArr[1] + Local.s(getString(R.string.power_min));
                                mBinding.tvTime.setText(timeStr);
                                break;


                            case BmtCmd.GET_CURRENT_EV_CHARGING_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mEvChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                int fixed = DeviceHelper.getInt(result, BmtDataKey.FIXED, 0);
                                int fixedFull = DeviceHelper.getInt(result, BmtDataKey.FIXED_FULL, 0);
                                int pricePercent = DeviceHelper.getInt(result, BmtDataKey.PRICE_PERCENT, 0);
                                DDLog.i(TAG, "pricePercent==="+pricePercent);
                                if (mEvChargeMode <= 0 || mEvChargeMode == 2 || mEvChargeMode > 5)
                                    return;
                                checkDataNull();

                                if (mInstantChargeAdapter == null || mNormalChargeAdapter == null
                                        || CollectionUtil.isListEmpty(mChargeModeList)) return;

                                for (PSEVChargeV2Bean chargeV2Bean : mChargeModeList) {
                                    int type = chargeV2Bean.getType();
                                    chargeV2Bean.setStatus(type == mEvChargeMode ? 1 : -1);
                                    chargeV2Bean.setFixedStatus(type == mEvChargeMode ? 1 : -1);
                                    chargeV2Bean.setValue(type == PSEVChargeV2Bean.LOWER_UTILITY_RATE ? pricePercent : fixed);
                                    chargeV2Bean.setTempValue(fixed);
                                    chargeV2Bean.setMaxValue(fixedFull);
                                    if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
                                        chargeV2Bean.setSelected(chargeV2Bean.getType() == mEvChargeMode);
                                    }
                                    if (chargeV2Bean.getType() == mEvChargeMode) {
                                        getSelectViewHeight(chargeV2Bean);
                                    }
                                }

                                if (mEvChargeMode < 4) {
                                    mBinding.vpMode.setCurrentItem(1);
                                    ((PSEVSonChargeV2Fragment) mModeFragments.get(1)).resetShrinkData(mEvChargeMode);
                                } else {
                                    mBinding.vpMode.setCurrentItem(0);
                                    ((PSEVSonChargeV2Fragment) mModeFragments.get(0)).resetShrinkData(mEvChargeMode);
                                    mBinding.csInstantCharge.setSwitchStatic(true);
                                    isInstantSelected = true;
                                }
                                mInstantChargeAdapter.notifyDataSetChanged();
                                mNormalChargeAdapter.notifyDataSetChanged();
                                break;

                            case BmtCmd.SET_EV_CHARGING_MODE:
                            case BmtCmd.SET_EVCHARGINGMODE_INSTANT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mEvChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                isInstantSelected = mEvChargeMode > 3;
                                checkDataNull();
                                for (PSEVChargeV2Bean chargeV2Bean : mChargeModeList) {
                                    chargeV2Bean.setStatus(chargeV2Bean.getType() == mEvChargeMode ? 1 : -1);
                                    chargeV2Bean.setFixedStatus(chargeV2Bean.getType() == mEvChargeMode ? 1 : -1);
                                    if (chargeV2Bean.getType() != PSEVChargeV2Bean.LOWER_UTILITY_RATE) {
                                        chargeV2Bean.setValue(chargeV2Bean.getTempValue());
                                    }
                                    chargeV2Bean.setSelected(false);
                                    if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
                                        chargeV2Bean.setSelected(chargeV2Bean.getType() == mEvChargeMode);
                                    }
                                    if (chargeV2Bean.getType() == mEvChargeMode) {
                                        getSelectViewHeight(chargeV2Bean);
                                    }
                                }
                                mInstantChargeAdapter.notifyDataSetChanged();
                                mNormalChargeAdapter.notifyDataSetChanged();
                                setCSInstantSWitchEnabled(false);
                                break;

                            case BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE:
                                checkDataNull();
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mEvChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                if (mEvChargeMode <= 0 || mEvChargeMode == 2 || mEvChargeMode > 5)
                                    return;
                                isInstantSelected = !isInstantSelected;
                                for (PSEVChargeV2Bean chargeV2Bean : mChargeModeList) {
                                    chargeV2Bean.setStatus(chargeV2Bean.getType() == mEvChargeMode ? 1 : -1);
                                    chargeV2Bean.setFixedStatus(chargeV2Bean.getType() == mEvChargeMode ? 1 : -1);
                                    if (chargeV2Bean.getType() != PSEVChargeV2Bean.LOWER_UTILITY_RATE) {
                                        chargeV2Bean.setValue(chargeV2Bean.getTempValue());
                                    }
                                    chargeV2Bean.setSelected(false);
                                    if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
                                        if ((mEvChargeMode >= 4 && mCurrentItem == 0) ||
                                                mEvChargeMode <= 3 && mCurrentItem == 1) {
                                            chargeV2Bean.setSelected(chargeV2Bean.getType() == mEvChargeMode);
                                        }
                                    }
                                    if (chargeV2Bean.getType() == mEvChargeMode) {
                                        getSelectViewHeight(chargeV2Bean);
                                    }
                                }
                                if (mEvChargeMode < 4) {
                                    ((PSEVSonChargeV2Fragment) mModeFragments.get(1)).resetShrinkData(mEvChargeMode);
                                } else {
                                    ((PSEVSonChargeV2Fragment) mModeFragments.get(0)).resetShrinkData(mEvChargeMode);
                                    mBinding.csInstantCharge.setSwitchStatic(true);
                                    isInstantSelected = true;
                                }
                                mInstantChargeAdapter.notifyDataSetChanged();
                                mNormalChargeAdapter.notifyDataSetChanged();
                                setCSInstantSWitchEnabled(false);
                                break;
                        }
                    } else {
                        switch (cmd) {
                            case DsCamCmd.CMD_CONNECT:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case BmtCmd.GET_CURRENT_EV_CHARGING_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                checkDataNull();
                                getSelectViewHeight(mChargeModeList.get(0));
                                break;

                            case BmtCmd.SET_EV_CHARGING_MODE:
                            case BmtCmd.SET_EVCHARGINGMODE_INSTANT:
                                checkDataNull();
                                closeLoadingFragment();
                                setCSInstantSWitchEnabled(true);
                                for (PSEVChargeV2Bean chargeV2Bean : mChargeModeList) {
                                    if (chargeV2Bean.isSelected()) {
                                        chargeV2Bean.setStatus(chargeV2Bean.getFixedStatus());
                                    }
                                    if (cmd.equals(BmtCmd.SET_EVCHARGINGMODE_INSTANT)) {
                                        chargeV2Bean.setTempValue(chargeV2Bean.getValue());
                                    }
                                }
                                mInstantChargeAdapter.notifyDataSetChanged();
                                mNormalChargeAdapter.notifyDataSetChanged();
                                showErrorToast();
                                break;

                            case BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE:
                                closeLoadingFragment();
                                showErrorToast();
                                mBinding.csInstantCharge.setSwitch(isInstantSelected, false);
                                break;
                        }
                    }
                }
            });
        }
    }

    /**
     * 设置EV充电模式(0xa022)
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void setEvChargingMode(SetEVChargeModeEvent event) {
        setCSInstantSWitchEnabled(false);
        PSEVChargeV2Bean psevChargeV2Bean = event.getPsEVChargeV2Bean();
        int chargeType = event.getChargeType();
        int mode = psevChargeV2Bean.getType();
        if (chargeType == 1) {
            if (mPSDevice != null) {
                Map<String, Object> map = new HashMap<>();
                map.put(PSKeyConstant.CMD, BmtCmd.SET_EV_CHARGING_MODE);
                map.put(BmtDataKey.EV_CHARGING_MODE, mode);
                mPSDevice.submit(map);
            }
        } else if (chargeType == 0) {
            if (mPSDevice != null) {
                Map<String, Object> map = new HashMap<>();
                map.put(PSKeyConstant.CMD, BmtCmd.SET_EVCHARGINGMODE_INSTANT);
                map.put(BmtDataKey.EV_CHARGING_MODE, mode);
                int fixed = psevChargeV2Bean.getTempValue();
                map.put(BmtDataKey.FIXED, psevChargeV2Bean.getType() == PSEVChargeV2Bean.INSTANT_CHARGE_FIXED ? fixed : 0);
                if (event.isLoading()) {
                    showTimeOutLoadinFramgment();
                }
                mPSDevice.submit(map);
            }
        }
    }

    public void setInstantCharge(boolean open) {
        if (mPSDevice != null) {
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
            params.put(BmtDataKey.OPEN, open);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }
}
