package com.dinsafer.module.powerstation.bean;

import com.dinsafer.dinnet.R;

public class PSElectricityTypeBean {

    private int checkedBgColor;
    private int normalBgColor;
    private String name;
    private boolean isSelected;

    public PSElectricityTypeBean(int checkedBgColor, String name, boolean isSelected) {
        this.checkedBgColor = checkedBgColor;
        this.normalBgColor = R.drawable.shape_electricity_type_normal;
        this.name = name;
        this.isSelected = isSelected;
    }

    public PSElectricityTypeBean(int checkedBgColor, int normalBgColor, String name, boolean isSelected) {
        this.checkedBgColor = checkedBgColor;
        this.normalBgColor = normalBgColor;
        this.name = name;
        this.isSelected = isSelected;
    }

    public int getCheckedBgColor() {
        return checkedBgColor;
    }

    public void setCheckedBgColor(int checkedBgColor) {
        this.checkedBgColor = checkedBgColor;
    }

    public int getNormalBgColor() {
        return normalBgColor;
    }

    public void setNormalBgColor(int normalBgColor) {
        this.normalBgColor = normalBgColor;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }
}
