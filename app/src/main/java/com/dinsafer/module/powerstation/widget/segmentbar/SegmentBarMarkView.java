package com.dinsafer.module.powerstation.widget.segmentbar;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.RectF;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.RelativeLayout;

import java.lang.ref.WeakReference;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/26 16:29
 * @description :
 */
public class SegmentBarMarkView extends RelativeLayout {

    private View inflated;
    private SBPoint mOffset = new SBPoint();
    private SBPoint mOffset2 = new SBPoint();
    private WeakReference<SegmentRangeBar> mWeakBar;
    private SBPoint mSBPoint = new SBPoint();
    private float mRelativeTouchPointX = 0;
    private float mRelativeTouchPointY = 0;
    private RectF mTouchRectF;

    public SegmentBarMarkView(Context context, int layoutRes) {
        super(context);
        setupLayoutResource(layoutRes);
        mTouchRectF = new RectF();
    }

    /**
     * Sets the layout resource for a custom MarkerView.
     *
     * @param layoutResource
     */
    private void setupLayoutResource(int layoutResource) {

         inflated = LayoutInflater.from(getContext()).inflate(layoutResource, this);

        inflated.setLayoutParams(new LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT));
        inflated.measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED), MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));

        // measure(getWidth(), getHeight());
        inflated.layout(0, 0, inflated.getMeasuredWidth(), inflated.getMeasuredHeight());
    }

    public View getView() {
        return inflated;
    }

    public void setOffset(SBPoint offset) {
        mOffset = offset;

        if (mOffset == null) {
            mOffset = new SBPoint();
        }
    }

    public void setOffset(float offsetX, float offsetY) {
        mOffset.x = offsetX;
        mOffset.y = offsetY;
    }

    public SBPoint getOffset() {
        return mOffset;
    }

    public void setWeakBar(SegmentRangeBar bar) {
        mWeakBar = new WeakReference<>(bar);
    }

    public SegmentRangeBar getRangeBarView() {
        return mWeakBar == null ? null : mWeakBar.get();
    }

    public SBPoint getOffsetForDrawingAtPoint(float posX, float posY) {

        SBPoint offset = getOffset();
        mOffset2.x = offset.x;
        mOffset2.y = offset.y;

        SegmentRangeBar segmentRangeBar = getRangeBarView();

        float width = getWidth();
        float height = getHeight();

        if (posX + mOffset2.x < 0) {
            mOffset2.x = - posX;
        } else if (segmentRangeBar != null && posX + width + mOffset2.x > segmentRangeBar.getWidth()) {
            mOffset2.x = segmentRangeBar.getWidth() - posX - width;
        }

        if (posY + mOffset2.y < 0) {
            mOffset2.y = - posY;
        } else if (segmentRangeBar != null && posY + height + mOffset2.y > segmentRangeBar.getHeight()) {
            mOffset2.y = segmentRangeBar.getHeight() - posY - height;
        }

        return mOffset2;
    }

    public float getRelativeTouchPointX() {
        return mRelativeTouchPointX;
    }

    public void setRelativeTouchPointX(float relativeTouchPointX) {
        this.mRelativeTouchPointX = relativeTouchPointX;
    }

    public float getRelativeTouchPointY() {
        return mRelativeTouchPointY;
    }

    public void setRelativeTouchPointY(float relativeTouchPointY) {
        this.mRelativeTouchPointY = relativeTouchPointY;
    }

    public void refreshContent() {
        measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
        layout(0, 0, getMeasuredWidth(), getMeasuredHeight());
    }

    public void draw(Canvas canvas, float posX, float posY) {
        SBPoint offset = getOffsetForDrawingAtPoint(posX, posY);
        int saveId = canvas.save();
        // translate to the correct position and draw
        canvas.translate(posX + offset.x, posY + offset.y);
        draw(canvas);
        canvas.restoreToCount(saveId);
        mSBPoint.x = posX;
        mSBPoint.y = posY;
    }

    public boolean isTouched(MotionEvent event) {
        mTouchRectF.left = mSBPoint.x;
        mTouchRectF.top = mSBPoint.y;
        mTouchRectF.right = mSBPoint.x + getWidth();
        mTouchRectF.bottom = mSBPoint.y+getHeight()  ;

        boolean touchInMarkerView = mTouchRectF.contains(event.getX(), event.getY());
        if (touchInMarkerView) {
            setRelativeTouchPointX(event.getX() - mTouchRectF.left);
            setRelativeTouchPointY(event.getY()-mTouchRectF.top);
        }
        if (mSBPoint.x == 0) {
            touchInMarkerView = false;
        }
//       mSBPoint.x = 0;
//       mSBPoint.y = 0;
        return touchInMarkerView;
    }

    public void markViewClick() {

    }
}
