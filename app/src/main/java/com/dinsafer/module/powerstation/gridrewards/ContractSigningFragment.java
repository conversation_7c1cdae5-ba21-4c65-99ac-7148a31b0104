package com.dinsafer.module.powerstation.gridrewards;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentContractSigningBinding;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.OnlinePdfFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.bean.GridRewardsAuthorization;
import com.dinsafer.module.powerstation.event.ErrorDeviceSignedEvent;
import com.dinsafer.module.powerstation.event.SignatureEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.FamilyBalanceContract;
import com.dinsafer.plugin.widget.view.LoadingFragment;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/23
 * @author: create by Sydnee
 */
public class ContractSigningFragment extends MyBaseFragment<FragmentContractSigningBinding> {

    private String ENCRYPT_PATH = "";
    private String IMG_PATH = "";
    /**
     * 重复签约错误码
     */
    private static final int ERROR_CODE_REPEATED_SIGN = -79;

    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;

    private BindMultiAdapter<TitleContentModel> mAdapter;

    private boolean canConfirm = false;

    private Bitmap signBitmap;

    private String mVersion;
    private String mFamilyAttorneyTemplateUrl;
    private String mAttorneyAuthUrl;
    private String mCompanyName;
    private String mOrganizationNumber;
    private String mAddress;
    private String mEmailAddress;
    private int mClickType;

    public static ContractSigningFragment newInstance(FamilyBalanceContractInfo familyBalanceContractInfo) {
        ContractSigningFragment fragment = new ContractSigningFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_contract_signing;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.contract_signing));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        ENCRYPT_PATH = getContext().getCacheDir() + "secret";
        IMG_PATH = getContext().getCacheDir() + "secret.png";

        mBinding.tvTitle.setLocalText(getString(R.string.contract_title));
        mBinding.btnSign.setLocalText(getString(R.string.agree_sign));
        mBinding.btnResign.setLocalText(getString(R.string.re_sign));

        mBinding.btnSign.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (canConfirm && signBitmap != null) {
                    if (TextUtils.isEmpty(mVersion)) {
                        mClickType = 3;
                        getTemplate();
                    } else {
                        getUploadImageKey();
                    }
                    return;
                }
                getDelegateActivity().addCommonFragment(SignatureFragment.newInstance());
            }
        });

        mBinding.btnResign.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDelegateActivity().addCommonFragment(SignatureFragment.newInstance());

            }
        });

        initRv();


    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        Bundle bundle = getArguments();
        if (bundle != null) {
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
        }
    }

    private void initRv() {
        mAdapter = new BindMultiAdapter<>();
        mBinding.rcvContent.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rcvContent.setAdapter(mAdapter);

        String[] contents = getContext().getResources().getStringArray(R.array.contract_content);
        List<TitleContentModel> modelList = new ArrayList<>();
        for (int i = 0; i < contents.length; i++) {
            TitleContentModel contentModel = new TitleContentModel(contents[i]);
            contentModel.setShowPoint(i != 0 && i != (contents.length - 1));
            contentModel.setLineSpace(getResources().getDimensionPixelSize(R.dimen.contract_content_lineSpace));
            modelList.add(contentModel);
        }
        mAdapter.setNewData(modelList);
    }

    /**
     * 获取签约模板数据
     */
    private void getTemplate() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinHome.getInstance().getBalanceContractSignTemplate(mFamilyBalanceContractInfo.getCountry_code(),
                new IDefaultCallBack2<>() {
                    @Override
                    public void onSuccess(BalanceContractSignTemplateResponse.ResultBean resultBean) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (resultBean != null) {
                            mVersion = resultBean.getVersion();
                            mFamilyAttorneyTemplateUrl = resultBean.getFamily_attorney_template_url();
                            mAttorneyAuthUrl = resultBean.getAttorney_auth_url();
                            mCompanyName = resultBean.getAuthorization_company_name();
                            mOrganizationNumber = resultBean.getAuthorization_organization_number();
                            mAddress = resultBean.getAuthorization_address();
                            mEmailAddress = resultBean.getAuthorization_email_address();
                            if (mClickType == 1) {
                                toPreview();
                            } else if (mClickType == 2) {
                                toTerms();
                            } else if (mClickType == 3) {
                                getUploadImageKey();
                            }
                        } else {
                            showErrorToast();
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                    }
                });
    }

    private void getUploadImageKey() {
        showLoadingFragment(LoadingFragment.BLACK, "");
        saveSign();
        DinHome.getInstance().getUploadImageKey(ENCRYPT_PATH, DinHome.getInstance().QIUNIU_PATH_KEY_CONTRACT, new IDefaultCallBack2<String>() {

            @Override
            public void onSuccess(String key) {
                DDLog.d(TAG, "getUploadImageKey: " + key);
                if (!TextUtils.isEmpty(key) && mFamilyBalanceContractInfo != null) {
                    mFamilyBalanceContractInfo.setSign(key);

                    toSaveInfo();
                    return;
                }
                closeLoadingFragment();
                showErrorToast();
            }

            @Override
            public void onError(int i, String s) {
                DDLog.d(TAG, "getUploadImageKey. onError: " + s);
                closeLoadingFragment();
                showErrorToast();

            }
        });
    }

    private void toSaveInfo() {
        FamilyBalanceContract info = new FamilyBalanceContract();
        info.setIBAN(mFamilyBalanceContractInfo.getIBAN());
        info.setCardholder(mFamilyBalanceContractInfo.getCardholder());
        info.setCity(mFamilyBalanceContractInfo.getCity());
        info.setCountry_code(mFamilyBalanceContractInfo.getCountry_code());
        info.setCompany_name(mFamilyBalanceContractInfo.getCompany_name());
        info.setElectricitySupplier(mFamilyBalanceContractInfo.getElectricitySupplier());
        info.setElectricitySupplierId(mFamilyBalanceContractInfo.getElectricitySupplierId());
        info.setEmailAddress(mFamilyBalanceContractInfo.getEmailAddress());
        info.setEuVatNumber(mFamilyBalanceContractInfo.getEuVatNumber());
        info.setName(mFamilyBalanceContractInfo.getName());
        info.setPhoneNumber(mFamilyBalanceContractInfo.getPhoneNumber());
        info.setSign(mFamilyBalanceContractInfo.getSign());
        info.setStreetNameAndNumber(mFamilyBalanceContractInfo.getStreetNameAndNumber());
        info.setType(mFamilyBalanceContractInfo.getType());
        info.setZipCode(mFamilyBalanceContractInfo.getZipCode());
        info.setVersion(mVersion);

        DinHome.getInstance().saveFamilyBalanceContractInfo(mFamilyBalanceContractInfo.getHome_id()
                , info, new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        DDLog.d(TAG, "saveFamilyBalanceContractInfo. onSuccess");
                        final String key = DBKey.KEY_FAMILY_BALANCE_CONTRACT_INFO + "_" + mFamilyBalanceContractInfo.getType() + "_" + mFamilyBalanceContractInfo.getHome_id();
                        if (DBUtil.Exists(key)) {
                            DBUtil.Delete(key);
                        }
                        closeLoadingFragment();
                        getDelegateActivity().addCommonFragment(ParticipationHoursFragment.newInstance(1));

                    }

                    @Override
                    public void onError(int i, String s) {
                        DDLog.d(TAG, "saveFamilyBalanceContractInfo. onError : " + s);
                        closeLoadingFragment();
                        if (i == ERROR_CODE_REPEATED_SIGN) {
                            DDLog.d(TAG, "saveFamilyBalanceContractInfo. onError : code: " + i + " " + s);
                            showErrorRepeatedSign();
                            return;
                        }
                        showErrorToast();
                    }
                });
    }

    private void showErrorRepeatedSign() {
        AlertDialog.createBuilder(getContext()).setContent(getString(R.string.failed_family_signed))
                .setOk(getString(R.string.reselect))
                .setIsSuccess(false)
                .setIsShowContentImageView(true)
                .setCanCancel(false)
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    EventBus.getDefault().post(new ErrorDeviceSignedEvent());
                    removeSelf();
                })
                .preBuilder()
                .show();
    }

    private void saveSign() {
        String out_file_path = DDGlobalEnv.getInstance().getImageFolder();
        File dir = new File(out_file_path);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File photo = new File(IMG_PATH);
        try {
            OutputStream stream = new FileOutputStream(photo);
            signBitmap.compress(Bitmap.CompressFormat.PNG, 80, stream);
            stream.close();
            DDSecretUtil.encryptPNG(IMG_PATH, ENCRYPT_PATH);
        } catch (Exception e) {
            e.printStackTrace();
            closeLoadingFragment();
            showErrorToast();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(SignatureEvent event) {
        if (event.getBitmap() == null) {
            canConfirm = false;
            return;
        }
        canConfirm = true;
        signBitmap = event.getBitmap();
        refreshUI(signBitmap);
    }

    private void refreshUI(Bitmap bitmap) {
        mBinding.imgSign.setImageBitmap(bitmap);
        Drawable drawable = mBinding.imgSign.getDrawable();
        Drawable wrap = DrawableCompat.wrap(drawable);
        DrawableCompat.setTint(wrap, ContextCompat.getColor(getContext(), R.color.white));
        mBinding.imgSign.setImageDrawable(wrap);
        mBinding.imgSign.setVisibility(View.VISIBLE);

        String clickablePart = Local.s(getString(R.string.Preview));
        String fullText = "\"" + Local.s(getString(R.string.contract_preview)) + "\" " + clickablePart;
        SpannableString spannableString = new SpannableString(fullText);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                // 处理点击事件
                if (TextUtils.isEmpty(mFamilyAttorneyTemplateUrl)) {
                    mClickType = 1;
                    getTemplate();
                } else {
                    toPreview();
                }
            }

            @Override
            public void updateDrawState(android.text.TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResColor(R.color.color_brand_text));
                ds.setUnderlineText(false);
            }
        };

        int start = fullText.indexOf(clickablePart);
        int end = start + clickablePart.length();
        spannableString.setSpan(clickableSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        mBinding.tvPreview.setText(spannableString);
        mBinding.tvPreview.setVisibility(View.VISIBLE);
        mBinding.tvPreview.setMovementMethod(LinkMovementMethod.getInstance());

        StringBuilder sbTerms = new StringBuilder();
        sbTerms.append("\"");
        sbTerms.append(Local.s(getString(R.string.emaldo_terms)));
        sbTerms.append("\" ");
        sbTerms.append(clickablePart);
        int startTerms = sbTerms.toString().indexOf(clickablePart);
        mBinding.tvTerms.setText(StringUtil.getSpannableString(getResources().getColor(R.color.color_brand_text), sbTerms.toString(), startTerms, startTerms + clickablePart.length(), true,
                () -> {
                    if (TextUtils.isEmpty(mAttorneyAuthUrl)) {
                        mClickType = 2;
                        getTemplate();
                    } else {
                        toTerms();
                    }
                }));
        mBinding.tvTerms.setMovementMethod(LinkMovementMethod.getInstance());
        mBinding.tvTerms.setVisibility(View.VISIBLE);

        mBinding.btnResign.setVisibility(View.VISIBLE);
        mBinding.btnSign.setLocalText(getString(R.string.agree_confirm));
    }

    private void toTerms() {
        getDelegateActivity().addCommonFragment(OnlinePdfFragment.newInstance(mAttorneyAuthUrl));
    }

    private void toPreview() {
        if (signBitmap == null) return;
        String signBase64 = DDImageUtil.bitmap2Base64(signBitmap, 80);
        GridRewardsAuthorization authorization = new GridRewardsAuthorization(mCompanyName, mOrganizationNumber, mAddress, mEmailAddress);
        getDelegateActivity().addCommonFragment(PSContractPreviewFragment.newInstance(0, mFamilyAttorneyTemplateUrl, signBase64,
                mFamilyBalanceContractInfo, authorization));
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
