package com.dinsafer.module.powerstation.settings;

import android.Manifest;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityBindInverterBinding;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.StringUtil;
import com.google.zxing.ResultPoint;
import com.google.zxing.client.android.Intents;
import com.google.zxing.integration.android.IntentIntegrator;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
public class BindInverterActivity extends BaseMainActivity implements BarcodeCallback, IDeviceCallBack {
    private static final String TAG = "BindInverterActivity";
    private ActivityBindInverterBinding mBinding;
    private Device device;
    private String mcu_id;
    private static MainActivity activity;

    public static void startScan(MainActivity mainActivity, String id, String subcategory, String mcu_id) {
        if (null == mainActivity) {
            DDLog.e(TAG, "Can't open scan activity, because mainActivity is null.");
            return;
        }

        mainActivity.setNotNeedToLogin(true);
        activity = mainActivity;
        if (mainActivity.needCameraPermission()) {
            // 申请相机，不管是否获得权限都进入扫描页
            mainActivity.requestCameraPermission((requestCode, permissions, grantResults) -> {
                mainActivity.setNotNeedToLogin(true);
                Intent intent = new Intent(mainActivity, BindInverterActivity.class);
                intent.putExtra("id", id);
                intent.putExtra("subcategory", subcategory);
                intent.putExtra("mcu_id", mcu_id);
                mainActivity.startActivity(intent);
            });
        } else {
            mainActivity.setNotNeedToLogin(true);
            Intent intent = new Intent(mainActivity, BindInverterActivity.class);
            intent.putExtra("id", id);
            intent.putExtra("subcategory", subcategory);
            intent.putExtra("mcu_id", mcu_id);
            mainActivity.startActivity(intent);
        }
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        super.initViews(savedInstanceState);
        device = BmtManager.getInstance().getDeviceById(getIntent().getStringExtra("id"), getIntent().getStringExtra("subcategory"));
        mcu_id = getIntent().getStringExtra("mcu_id");
        if (device == null || TextUtils.isEmpty(mcu_id)) {
            finish();
            return;
        }
        device.registerDeviceCallBack(this::onCmdCallBack);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_bind_inverter);

        mBinding.commonBarBack.setOnClickListener(v -> finish());
        mBinding.btnBind.setOnClickListener(v -> clickBind());
        setBtnEnable(false);

        mBinding.zxingScanner.decodeSingle(this);
        mBinding.zxingScanner.setStatusText("");
        IntentIntegrator intentIntegrator = new IntentIntegrator(this).addExtra(Intents.Scan.MODE, Intents.Scan.ONE_D_MODE);
        mBinding.zxingScanner.initializeFromIntent(intentIntegrator.createScanIntent());

        if (!PermissionUtil.hasPermission(this, Manifest.permission.CAMERA)) {
            showPermissionNotGrantTip(getString(R.string.permission_tip_camera_not_grant));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mBinding.zxingScanner.resume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mBinding.zxingScanner.pause();
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (device != null) {
            device.unregisterDeviceCallBack(this::onCmdCallBack);
        }
        activity = null;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
    }

    @Override
    public void barcodeResult(BarcodeResult result) {
        Log.d(TAG, "barcodeResult: " + result.getText());
        if (result != null && !TextUtils.isEmpty(result.getText())) {
            mBinding.tvInfo.setVisibility(View.VISIBLE);
            mBinding.tvInfo.setText(result.getText());
            setBtnEnable(true);
        } else {
            mBinding.tvInfo.setVisibility(View.GONE);
            setBtnEnable(false);
        }
    }

    @Override
    public void possibleResultPoints(List<ResultPoint> resultPoints) {

    }

    protected void showPermissionNotGrantTip(String tip) {
        AlertDialog.createBuilder(this)
                .setOk(getString(R.string.go_setting))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        goIntentSetting();
                    }
                })
                .setCancel(getString(R.string.cancel))
                .setContent(tip)
                .preBuilder()
                .show();
    }

    private void goIntentSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        try {
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setBtnEnable(boolean enable) {
        if (enable) {
            mBinding.btnBind.setEnabled(true);
            mBinding.btnBind.setAlpha(1.0f);
        } else {
            mBinding.btnBind.setEnabled(false);
            mBinding.btnBind.setAlpha(0.5f);
        }
    }

    private void reset() {
        mBinding.tvInfo.setVisibility(View.GONE);
        mBinding.tvInfo.setText("");
        setBtnEnable(false);
        mBinding.zxingScanner.decodeSingle(this);
    }

    private void clickBind() {
        String inverter_id = mBinding.tvInfo.getText().toString();
        if (TextUtils.isEmpty(inverter_id)) {
            showErrorToast();
            return;
        }
        //本地检查：逆变条码是否与MCU ID一致
        if (!inverter_id.equals(mcu_id)) {
            showToast(getString(R.string.got_it), getString(R.string.inverter_mcu_id_not_match));
            return;
        }
        //服务器校验：逆变条码是否不重复
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> params = new HashMap<>();
        params.put("cmd", BmtCmd.BIND_INVERTER);
        params.put("inverter_id", inverter_id);
        params.put("mcu_id", mcu_id);
        device.submit(params);
    }

    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (device != null && StringUtil.isNotEmpty(id) && id.equals(device.getId()) && !isFinishing() && !isDestroyed() && activity != null) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    closeLoadingFragmentWithTimeOut();
                    Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
                    int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
                    if (cmd.equals(BmtCmd.BIND_INVERTER)) {
                        if (status == 1) {
                            activity.showTopToast(R.drawable.icon_toast_succeed, getString(R.string.success));
                            finish();
                        } else {
                            int originStatus = (int) MapUtils.get(result, "originStatus", -1);
                            if (originStatus == -4091) {
                                showToast(getString(R.string.got_it), getString(R.string.inverter_bind_conflict));
                            } else {
                                showErrorToast();
                            }
                            reset();
                        }
                    }
                }
            });
        }
    }
}
