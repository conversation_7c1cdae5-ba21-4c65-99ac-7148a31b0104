package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutFlipCombinedChartBinding;
import com.dinsafer.module.powerstation.bean.PSElectricityTypeBean;
import com.dinsafer.module.powerstation.electricity.chart.CustomCombinedChart;
import com.dinsafer.module.powerstation.electricity.chart.listener.OnScrollChartListener;
import com.dinsafer.module.powerstation.electricity.chart.listener.OperateOrientation;
import com.dinsafer.ui.FlowLayout;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.timeruler.TimePartUtil;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;


public class FlipCombinedChartView extends ConstraintLayout implements OnScrollChartListener {

    private Context mContext;
    private int chartHeight;
    private ArrayList<PSElectricityTypeBean> mData;
    private LayoutFlipCombinedChartBinding mBinding;

    private float noDataOffset;
    private float noDataLeftOffset;
    private float noDataTopOffset;
    private float noDataRightOffset;
    private float noDataBottomOffset;
    private boolean isAnimating;

    public FlipCombinedChartView(Context context) {
        this(context, null);
    }

    public FlipCombinedChartView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FlipCombinedChartView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.FlipCombinedChartView);
        chartHeight = (int) typedArray.getDimension(R.styleable.FlipCombinedChartView_chart_height, 320);
        noDataOffset = typedArray.getDimension(R.styleable.FlipCombinedChartView_noDataOffset, 0);
        noDataLeftOffset = typedArray.getDimension(R.styleable.FlipCombinedChartView_noDataLeftOffset, 0);
        noDataTopOffset = typedArray.getDimension(R.styleable.FlipCombinedChartView_noDataTopOffset, 0);
        noDataRightOffset = typedArray.getDimension(R.styleable.FlipCombinedChartView_noDataRightOffset, 0);
        noDataBottomOffset = typedArray.getDimension(R.styleable.FlipCombinedChartView_noDataBottomOffset, 0);
        if (noDataLeftOffset == 0.0f) {
            noDataLeftOffset = noDataOffset;
        }

        if (noDataTopOffset == 0.0f) {
            noDataTopOffset = noDataOffset;
        }

        if (noDataRightOffset == 0.0f) {
            noDataRightOffset = noDataOffset;
        }

        if (noDataBottomOffset == 0.0f) {
            noDataBottomOffset = noDataOffset;
        }

        typedArray.recycle();
        init(context);
    }

    protected void init(Context context) {
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_flip_combined_chart, this, true);
        mBinding.ivLeft.setOnClickListener(v -> {
            dealClick(OperateOrientation.LEFT);
        });

        mBinding.ivRight.setOnClickListener(v -> {
            dealClick(OperateOrientation.RIGHT);
        });
        RelativeLayout.LayoutParams llParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                chartHeight);
        mBinding.cccUsage1.setLayoutParams(llParams);
        mBinding.cccUsage2.setLayoutParams(llParams);
        mBinding.cccUsage1.setNoDataOffset(noDataOffset);
        mBinding.cccUsage2.setNoDataOffset(noDataOffset);
        mBinding.cccUsage1.setNoDataLeftOffset(noDataLeftOffset);
        mBinding.cccUsage2.setNoDataLeftOffset(noDataLeftOffset);
        mBinding.cccUsage1.setNoDataTopOffset(noDataTopOffset);
        mBinding.cccUsage2.setNoDataTopOffset(noDataTopOffset);
        mBinding.cccUsage1.setNoDataRightOffset(noDataRightOffset);
        mBinding.cccUsage2.setNoDataRightOffset(noDataRightOffset);
        mBinding.cccUsage1.setNoDataBottomOffset(noDataBottomOffset);
        mBinding.cccUsage2.setNoDataBottomOffset(noDataBottomOffset);
        setIvRightEnabled(false);
        initElectricityType();
    }

    public void dealClick(OperateOrientation orientation) {
        if (isAnimating) return;
        int displayChild = mBinding.vfChart.getDisplayedChild();
        Animation inAnim = AnimationUtils.loadAnimation(getContext(),
                orientation == OperateOrientation.LEFT ? R.anim.slide_in_right
                        : R.anim.slide_in_left);
        inAnim.setFillAfter(true);
        inAnim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                isAnimating = true;
                if (startFlipChangeListener != null) {
                    startFlipChangeListener.startFlipChange(displayChild == 0 ? 1 : 0, orientation);
                }
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (displayChild == 0) {
                    resetChart1();
                } else {
                    resetChart2();
                }
                resetHighValues();
                if (flipChangeListener != null) {
                    flipChangeListener.flipChange(displayChild == 0 ? 1 : 0, orientation);
                }
                isAnimating = false;
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mBinding.vfChart.setInAnimation(inAnim);
        mBinding.vfChart.setOutAnimation(AnimationUtils.loadAnimation(getContext(),
                orientation == OperateOrientation.LEFT ? R.anim.slide_out_right
                        : R.anim.slide_out_left));
//        跳转到指定的页面
        mBinding.vfChart.setDisplayedChild(displayChild == 1 ? 0 : 1);
    }

    private void initElectricityType() {

    }

    public boolean isFilterSelected(int index) {
        if (CollectionUtil.isListEmpty(mData) || index >= mData.size() || index < 0) return false;
        return mData.get(index).isSelected();
    }

    public boolean isAllFilterSelected() {
        if (CollectionUtil.isListEmpty(mData)) return false;
        for (PSElectricityTypeBean electricityTypeBean : mData) {
            if (!electricityTypeBean.isSelected()) {
                return false;
            }
        }
        return true;
    }

    public List<Integer> getSelectPositions() {
        List<Integer> selectedPos = new ArrayList<>();
        if (CollectionUtil.isListNotEmpty(mData)) {
            for (int i = 0; i < mData.size(); i++) {
                PSElectricityTypeBean electricityTypeModel = mData.get(i);
                if (electricityTypeModel.isSelected()) {
                    selectedPos.add(i);
                }
            }
        }
        return selectedPos;
    }

    public void addFilterView() {
        mBinding.flFilter1.removeAllViews();
        mBinding.flFilter2.removeAllViews();
        for (int i=0; i<mData.size(); i++) {
            PSElectricityTypeBean electricityTypeBean = mData.get(i);
            View view1 = createFilterView(electricityTypeBean);
            View view2 = createFilterView(electricityTypeBean);
            mBinding.flFilter1.addView(view1);
            mBinding.flFilter2.addView(view2);
        }
        if (mBinding.flFilter1.getChildCount() > 0) {
            for (int i=0; i<mBinding.flFilter1.getChildCount(); i++) {
                View view = mBinding.flFilter1.getChildAt(i);
                int finalI = i;
                view.setOnClickListener(v -> {
                    PSElectricityTypeBean electricityTypeBean = mData.get(finalI);
                    filterClick(finalI, electricityTypeBean);
                });
            }
        }

        if (mBinding.flFilter2.getChildCount() > 0) {
            for (int i=0; i<mBinding.flFilter2.getChildCount(); i++) {
                View view = mBinding.flFilter2.getChildAt(i);
                int finalI = i;
                view.setOnClickListener(v -> {
                    PSElectricityTypeBean electricityTypeBean = mData.get(finalI);
                    filterClick(finalI, electricityTypeBean);
                });
            }
        }
    }

    private void filterClick(int index, PSElectricityTypeBean electricityTypeBean) {
        electricityTypeBean.setSelected(!electricityTypeBean.isSelected());
        boolean isSelected =  electricityTypeBean.isSelected();
        View view = mBinding.flFilter1.getChildAt(index);
        if (view != null) {
            ImageView ivChecked = view.findViewById(R.id.iv_checked);
            ivChecked.setBackgroundResource(isSelected ? electricityTypeBean.getCheckedBgColor()
                    : electricityTypeBean.getNormalBgColor());
            ivChecked.setImageResource(isSelected ? R.drawable.ps_white_checked : 0);
        }
        View view2 = mBinding.flFilter2.getChildAt(index);
        if (view2 != null) {
            ImageView ivChecked = view2.findViewById(R.id.iv_checked);
            ivChecked.setBackgroundResource(isSelected ? electricityTypeBean.getCheckedBgColor()
                    : electricityTypeBean.getNormalBgColor());
            ivChecked.setImageResource(isSelected ? R.drawable.ps_white_checked : 0);
        }
        if (filterListener != null) {
            resetHighValues();
            filterListener.filterChange();
        }
    }



    private View createFilterView(PSElectricityTypeBean electricityTypeBean) {
        boolean isSelected =  electricityTypeBean.isSelected();
        int checkedBgColor = electricityTypeBean.getCheckedBgColor();
        int normalBgColor = electricityTypeBean.getNormalBgColor();
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_ps_electricity_type, null, false);
        ImageView ivChecked = view.findViewById(R.id.iv_checked);
        LocalTextView tvName= view.findViewById(R.id.tv_name);
        ivChecked.setBackgroundResource(isSelected ? checkedBgColor
                : normalBgColor);
        ivChecked.setImageResource(isSelected ? R.drawable.ps_white_checked : 0);
        tvName.setLocalText(electricityTypeBean.getName());

        FlowLayout.LayoutParams lap = new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT,
                FlowLayout.LayoutParams.WRAP_CONTENT);
        view.setLayoutParams(lap);
        return view;
    }

    public void setElectricityTypeData(ArrayList<PSElectricityTypeBean> data) {
        mData = data;
        addFilterView();
    }

    public void notifyFilterDataInsert(PSElectricityTypeBean electricityTypeBean) {
        View view1 = createFilterView(electricityTypeBean);
        View view2 = createFilterView(electricityTypeBean);
        view1.setOnClickListener(v -> {
            filterClick(mData.size() - 1, electricityTypeBean);
        });
        view2.setOnClickListener(v -> {
            filterClick(mData.size() - 1, electricityTypeBean);
        });
        mBinding.flFilter1.addView(view1);
        mBinding.flFilter2.addView(view2);
    }

    public void notifyFilterDataRemove(int index) {
        mBinding.flFilter1.removeViewAt(index);
        mBinding.flFilter2.removeViewAt(index);
    }

    public CustomCombinedChart getCustomCombinedChart1() {
        return mBinding.cccUsage1;
    }

    public CustomCombinedChart getCustomCombinedChart2() {
        return mBinding.cccUsage2;
    }

    public CustomCombinedChart getDisplayCombinedChart(int displayChild) {
        if (displayChild == 0) {
            return mBinding.cccUsage1;
        } else {
            return mBinding.cccUsage2;
        }
    }

    public void resetChart1() {
        mBinding.cccUsage1.setData(null);

    }

    public void resetChart2() {
        mBinding.cccUsage2.setData(null);
    }

    public void resetChart() {
        resetChart1();
        resetChart2();
    }

    public void resetHighValues() {
        mBinding.cccUsage1.highlightValues(null);
        mBinding.cccUsage2.highlightValues(null);
    }

    public void setRvFilterVisible(boolean visible) {
        mBinding.flFilter1.setVisibility(visible ? VISIBLE : GONE);
        mBinding.flFilter2.setVisibility(visible ? VISIBLE : GONE);
    }

    public void setTimezone(String timezone) {
        mBinding.tvNote1.setText(Local.s(mContext.getString(R.string.electricity_stats_note)).replace(mContext.getString(R.string.impact_strategies_hashtag_timezone), TimePartUtil.getTimezoneDisplayName(timezone)));
        mBinding.tvNote2.setText(Local.s(mContext.getString(R.string.electricity_stats_note)).replace(mContext.getString(R.string.impact_strategies_hashtag_timezone), TimePartUtil.getTimezoneDisplayName(timezone)));
    }

    public void setTvNoteVisible(boolean visible) {
        mBinding.tvNote1.setVisibility(visible ? VISIBLE : GONE);
        mBinding.tvNote2.setVisibility(visible ? VISIBLE : GONE);
        int paddingTop = mBinding.tvNote1.getVisibility() == VISIBLE ? 0 : DensityUtil.dp2px(mContext, 7);
        mBinding.tvTpPvDesc1.setPadding(0, paddingTop,
                0, DensityUtil.dp2px(mContext, 7));
        mBinding.tvTpPvDesc2.setPadding(0, paddingTop,
                0, DensityUtil.dp2px(mContext, 7));
    }

    public void setTvNotePreBalancingVisible(boolean visible) {
        mBinding.tvNote0.setVisibility(visible ? VISIBLE : GONE);
        int paddingTop = mBinding.tvNote1.getVisibility() == VISIBLE ? 0 : DensityUtil.dp2px(mContext, 7);
        mBinding.tvNote0.setPadding(0, paddingTop,
                0, 0);
    }

    public void setTPPVDescText(String desc) {
        mBinding.tvTpPvDesc1.setLocalText(desc);
        mBinding.tvTpPvDesc2.setLocalText(desc);
    }

    public void setTvTPPVDescVisible(boolean visible) {
        mBinding.tvTpPvDesc1.setVisibility(visible ? VISIBLE : GONE);
        mBinding.tvTpPvDesc2.setVisibility(visible ? VISIBLE : GONE);
    }

    public void setPowerLevelVisible() {
        mBinding.ivLeft.setVisibility(GONE);
        mBinding.ivRight.setVisibility(GONE);
        mBinding.flFilter1.setVisibility(GONE);
        mBinding.flFilter2.setVisibility(GONE);
        mBinding.tvNote1.setVisibility(GONE);
        mBinding.tvNote2.setVisibility(GONE);
        mBinding.tvPowerLevel1.setVisibility(VISIBLE);
        mBinding.tvPowerLevel2.setVisibility(VISIBLE);
    }

    public void setIvLeftEnabled(boolean enabled) {
        mBinding.ivLeft.setEnabled(enabled);
        mBinding.ivLeft.setAlpha(enabled ? 1f : 0.5f);
    }

    public void setIvRightEnabled(boolean enabled) {
        mBinding.ivRight.setEnabled(enabled);
        mBinding.ivRight.setAlpha(enabled ? 1f : 0.5f);
    }

    private OnStartFlipChangeListener startFlipChangeListener;
    private OnFlipChangeListener flipChangeListener;

    public void setStartFlipChangeListener(OnStartFlipChangeListener startFlipChangeListener) {
        this.startFlipChangeListener = startFlipChangeListener;
    }

    public void setFlipChangeListener(OnFlipChangeListener flipChangeListener) {
        this.flipChangeListener = flipChangeListener;
    }

    public void setEmpty(boolean isEmpty) {
//        mBinding.vfEmpty.setVisibility(isEmpty ? View.VISIBLE : View.GONE);
        if (isEmpty) {
            mBinding.layoutEmpty1.getRoot().setVisibility(VISIBLE);
            mBinding.layoutEmpty2.getRoot().setVisibility(VISIBLE);
            if (mBinding.layoutEmpty2.getRoot().getHeight() == 0) {
                RelativeLayout.LayoutParams clParams = (RelativeLayout.LayoutParams) mBinding.layoutEmpty2.getRoot().getLayoutParams();
                clParams.height = mBinding.layoutEmpty1.getRoot().getHeight();
                mBinding.layoutEmpty2.getRoot().setLayoutParams(clParams);
            }
        } else {
            mBinding.layoutEmpty1.getRoot().setVisibility(GONE);
            mBinding.layoutEmpty2.getRoot().setVisibility(GONE);
        }
    }

    @Override
    public void onScroll(OperateOrientation orientation) {
        dealClick(orientation);
    }

    public void setReverse(boolean isReverse) {
        mBinding.cccUsage1.setReverse(isReverse);
        mBinding.cccUsage2.setReverse(isReverse);
    }

    public void setHighlightEnable(boolean enable) {
        mBinding.cccUsage1.setHighlightPerTapEnabled(enable);
        mBinding.cccUsage1.setHighlightPerDragEnabled(enable);
        mBinding.cccUsage1.setHighlightFullBarEnabled(enable);
        mBinding.cccUsage2.setHighlightPerTapEnabled(enable);
        mBinding.cccUsage2.setHighlightPerDragEnabled(enable);
        mBinding.cccUsage2.setHighlightFullBarEnabled(enable);
    }

    public void setDefaultHighLight(int childIndex, int xIndex) {
        if (childIndex == 0) {
            mBinding.cccUsage1.setDefaultHighLight(xIndex);
        } else {
            mBinding.cccUsage2.setDefaultHighLight(xIndex);
        }
    }

    public interface OnStartFlipChangeListener {
        void startFlipChange(int index, OperateOrientation orientation);
    }

    public interface OnFlipChangeListener {
        void flipChange(int index, OperateOrientation orientation);
    }

    private OnFilterListener filterListener;

    public void setFilterListener(OnFilterListener filterListener) {
        this.filterListener = filterListener;
    }

    public interface OnFilterListener {
        void filterChange();
    }
}
