package com.dinsafer.module.powerstation.adapter;

import android.widget.ImageView;


import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.PsEcoRateBean;


public class PSEcoRateAdapter extends BaseQuickAdapter<PsEcoRateBean, BaseViewHolder> {

    public PSEcoRateAdapter() {
        super(R.layout.item_ps_eco_rate);
    }

    @Override
    protected void convert(BaseViewHolder helper, PsEcoRateBean item) {
        ImageView imageView = helper.getView(R.id.iv_rate);
        imageView.setImageResource(item.isRated() ? R.drawable.icon_power_leaves_fill : R.drawable.icon_power_leaves_empty);
    }
}
