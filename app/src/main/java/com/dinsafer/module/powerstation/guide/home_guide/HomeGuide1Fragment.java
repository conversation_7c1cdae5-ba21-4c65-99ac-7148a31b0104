package com.dinsafer.module.powerstation.guide.home_guide;

import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentHomeGuide1Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class HomeGuide1Fragment extends MyBaseFragment<FragmentHomeGuide1Binding> {

    public static final String TAG = HomeGuide1Fragment.class.getSimpleName();

    public static HomeGuide1Fragment newInstance() {
        return new HomeGuide1Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_home_guide_1;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
