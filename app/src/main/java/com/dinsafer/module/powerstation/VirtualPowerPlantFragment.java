package com.dinsafer.module.powerstation;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentVirtualPowerPlantBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.impacts.ImpactStrategiesFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/25 18:02
 * @description :
 */
public class VirtualPowerPlantFragment extends PSConnectLoadingFragment<FragmentVirtualPowerPlantBinding> implements IDeviceCallBack {

    private Map<String, Object> params = new HashMap<>();
    private final AtomicBoolean mNeedHandleOutputCmd = new AtomicBoolean(false);

    public static VirtualPowerPlantFragment newInstanceForStepAddPS(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_STEP_ADD, deviceId,subcategory);
    }

    public static VirtualPowerPlantFragment newInstance(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_SETTING, deviceId, subcategory);
    }

    public static VirtualPowerPlantFragment newInstance(int from, String deviceId, String subcategory) {
        VirtualPowerPlantFragment fragment = new VirtualPowerPlantFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (mPSDevice != null) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_virtual_power_plant;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(StringUtil.getStringFromRes(getContext(), R.string.sell_back_to_grid));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.switchVpp.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                openVirtualPP();
            }
        });

        mBinding.btnDone.setVisibility(PARAM_FROM_STEP_ADD == mFrom ? View.VISIBLE : View.GONE);
        mBinding.btnDone.setOnClickListener(v -> {
            getDelegateActivity().removeAllCommonFragment();
            getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstanceForStepAddPS(
                    DeviceHelper.getString(BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory), DinConst.INFO_NAME, "")
                    , mDeviceId, mSubcategory));
        });
        // BmtManager.needGetOutputInfo = false;
        params.clear();
        params.put(PSKeyConstant.CMD, DsCamCmd.GET_VIRTUAL_POWER_PLANT);
        submitCmd();
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        // BmtManager.needGetOutputInfo = true;
        super.onDestroyView();
    }

    private void submitCmd() {
        if (mPSDevice != null) {
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    private void showDialog(String msg) {
        AlertDialog builder = AlertDialog.createBuilder(getContext())
                .setAutoDissmiss(true)
                .setContent(msg)
                .setOk(getResources().getString(R.string.got_it))
                .setCancel(getResources().getString(R.string.virtual_power_plant_take_a_look))
                .setCancelListener(new AlertDialog.AlertCancelClickCallback() {
                    @Override
                    public void onClick() {
                        getDelegateActivity().addCommonFragment(UserGuideFragment.newInstance(2, mDeviceId, mSubcategory));
                    }
                })
                .preBuilder();
        builder.show();
    }

    /**
     * 打开虚拟电厂
     */
    private void openVirtualPP() {
        params.clear();
        params.put(PSKeyConstant.CMD, DsCamCmd.SET_VIRTUAL_POWER_PLANT);
        params.put(PSKeyConstant.ON, mBinding.switchVpp.isOn());
        showTimeOutLoadinFramgmentWithErrorAlert();
        submitCmd();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BmtDeviceStatusChange event) {
        final String targetId = event.getDeviceID();
        if (TextUtils.isEmpty(targetId) || !targetId.equals(mDeviceId) || !event.getSubcategory().equals(mSubcategory)) {
            return;
        }

        final boolean connected = BmtUtil.isDeviceConnected(mPSDevice);
        if (!connected) {
            if (getDelegateActivity().isCommonFragmentExist(ImpactStrategiesFragment.class.getName())) {
                getDelegateActivity().removeToFragment(ImpactStrategiesFragment.class.getName());
            }
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (cmd.equals(DsCamCmd.SET_VIRTUAL_POWER_PLANT)) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (status == StatusConstant.STATUS_SUCCESS) {
                        } else {
                            mBinding.switchVpp.setOn(false);
                            showErrorToast();
                        }
                    }

                    if (StringUtil.isNotEmpty(cmd) && (cmd.equals(DsCamCmd.GET_VIRTUAL_POWER_PLANT)
                            || cmd.equals(DsCamCmd.GET_INVERTER_OUTPUT_INFO))) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                    }
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        if (result != null && result.size() > 0) {
                            switch (cmd) {
                                case DsCamCmd.GET_VIRTUAL_POWER_PLANT:
                                    boolean isOn = (boolean) MapUtils.get(result, PSKeyConstant.ON, false);
                                    mBinding.switchVpp.setOn(isOn);
                                    break;
                                case DsCamCmd.GET_INVERTER_OUTPUT_INFO:
                                    if (mNeedHandleOutputCmd.get()) {
                                        boolean isBSensorOutputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_OUTPUT_ON, false);
                                        if (isBSensorOutputOn) {
                                            openVirtualPP();
                                        } else {
                                            mBinding.switchVpp.setOn(false);
                                            showDialog(StringUtil.getStringFromRes(getContext(), R.string.virtual_power_plant_tip_2));
                                        }
                                        mNeedHandleOutputCmd.set(false);
                                    }
                                    break;

                                // case DsCamCmd.GET_INVERTER_INPUT_INFO:
                                //     boolean isBSensorInputOn = (boolean) MapUtils.get(result, PSKeyConstant.B_SENSOR_INPUT_ON, false);
                                //     if (isBSensorInputOn) {
                                //         openVirtualPP();
                                //     } else {
                                //         showDialog(StringUtil.getStringFromRes(getContext(), R.string.virtual_power_plant_tip_2));
                                //     }
                                //     break;
                                default:
                                    break;
                            }
                        }
                    } else {
                        switch (cmd) {
                            case DsCamCmd.GET_INVERTER_OUTPUT_INFO:
                                if (mNeedHandleOutputCmd.get()) {
                                    showErrorToast();
                                    mBinding.switchVpp.setOn(false);
                                    mNeedHandleOutputCmd.set(false);
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
            });

        }
    }
}
