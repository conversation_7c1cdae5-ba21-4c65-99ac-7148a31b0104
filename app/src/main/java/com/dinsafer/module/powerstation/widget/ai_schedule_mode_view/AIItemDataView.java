package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.utils.AIColorUtil;

public class AIItemDataView extends View {

    private Context mContext;
    private Paint mPaint;
    private Paint mExpandPaint;
    private Path mPath;
    private Path mSelectedPath;
    private Rect mRect;
    private RectF mRectF;
    //-1.放电 0. 正常 1.充电
    private int status;
    // -1. 没有选中 0. 正常 1.第一个 2.最后一个  3. 第一个也是最后一个
    private int selectedStatus = -1;
    private int rx, ry;
    private int selectedRX, selectedRY;
    private int strokeWidth;
    private float halfStrokeWidth;
    private int pvMode = -1;
    private boolean isCustom;
    private boolean isEdit;
    private boolean isExpand;
    private boolean isFirst;
    private boolean isLast;
    private LinearGradient mExpandGradient;
    private Matrix mMatrix;

    public AIItemDataView(Context context) {
        this(context, null);
    }

    public AIItemDataView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AIItemDataView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mExpandPaint = new Paint();
        mExpandPaint.setAntiAlias(true);
        mExpandPaint.setStyle(Paint.Style.FILL);
        mPath = new Path();
        mSelectedPath = new Path();
        mRect = new Rect();
        mRectF = new RectF();
        rx = DensityUtil.dp2px(mContext, 8);
        ry = DensityUtil.dp2px(mContext, 8);
        selectedRX = DensityUtil.dp2px(mContext, 3);
        selectedRY = DensityUtil.dp2px(mContext, 3);
        strokeWidth = DensityUtil.dp2px(mContext, 1);
        halfStrokeWidth = strokeWidth / 2f;
        mMatrix = new Matrix();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mPath.reset();
        if (isFirst) {
            mPath.moveTo(0, h);
            mPath.lineTo(0, ry);
            mPath.quadTo(0, 0, rx, 0);
            mPath.lineTo(w - rx, 0);
            mPath.quadTo(w, 0, w, ry);
            mPath.lineTo(w, h);
            mPath.lineTo(0, h);
        } else if (isLast) {
            mPath.moveTo(0, 0);
            mPath.lineTo(w, 0);
            mPath.lineTo(w, h - ry);
            mPath.quadTo(w, h, w - rx, h);
            mPath.lineTo(rx, h);
            mPath.quadTo(0, h, 0, h - ry);
            mPath.lineTo(0, 0);
        } else {
            mPath.moveTo(0, 0);
            mPath.lineTo(w, 0);
            mPath.lineTo(w, h);
            mPath.lineTo(0, h);
        }
        float max = Math.max(w, h) * 1.0f;
        float scaleX = w / max;
        float scaleY = h / max;
        mExpandGradient = new LinearGradient(0, Math.max(h, max), Math.max(h, max), 0, AIColorUtil.getAIColor(mContext),
                AIColorUtil.getAIColorPosition(), Shader.TileMode.CLAMP);
        mMatrix.setScale(scaleX, scaleY);
        mExpandGradient.setLocalMatrix(mMatrix);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (isEdit) {
            mPaint.setShader(null);
            switch (status) {
                case -1:
                    drawDischarge(canvas);
                    break;

                case 0:
                    drawNormal(canvas);
                    break;

                case 1:
                    drawCharge(canvas);
                    break;
            }
        } else {
            if (isExpand) {
                drawExpandView(canvas);
            } else {
                mPaint.setShader(null);
                mPaint.setStyle(Paint.Style.FILL);
                switch (pvMode) {
                    case 0:
                        drawPVHigh(canvas);
                        break;

                    case 1:
                        drawPVMiddle(canvas);
                        break;

                    case 2:
                        drawPVLow(canvas);
                        break;

                    case 3:
                        drawPVNone(canvas);
                        break;
                }
            }
        }
    }

    private void drawPVNone(Canvas canvas) {
        mPaint.setColor(getColor(R.color.transparent));
        canvas.drawPath(mPath, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawPVLow(Canvas canvas) {
        mPaint.setColor(getColor(R.color.color_tip_06_2));
        canvas.drawPath(mPath, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawPVMiddle(Canvas canvas) {
        mPaint.setColor(getColor(R.color.color_tip_01_3));
        canvas.drawPath(mPath, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawPVHigh(Canvas canvas) {
        mPaint.setColor(getColor(R.color.color_tip_01_2));
        canvas.drawPath(mPath, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }


    private void drawExpandView(Canvas canvas) {
        mExpandPaint.setShader(mExpandGradient);
        canvas.drawPath(mPath, mExpandPaint);
    }

    private void drawNormal(Canvas canvas) {
        mRect.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_dark_01));
        canvas.drawPath(mPath, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawCharge(Canvas canvas) {
        mRect.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_tip_06_2));
        canvas.drawPath(mPath, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawDischarge(Canvas canvas) {
        mRect.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_tip_03_2));
        canvas.drawPath(mPath, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawSelected(Canvas canvas) {
        if (selectedStatus == 0) {
            drawNormalSelected(canvas);
        } else if (selectedStatus == 1) {
            drawFirstSelected(canvas);
        } else if (selectedStatus == 2) {
            drawFinalSelected(canvas);
        } else if (selectedStatus == 3) {
            drawFirstFinalSelected(canvas);
        }
    }

    private void drawFirstFinalSelected(Canvas canvas) {
        mSelectedPath.reset();
        mSelectedPath.moveTo(halfStrokeWidth, getHeight() - (isFirst || isLast ? ry : selectedRY) - halfStrokeWidth);
        mSelectedPath.lineTo(halfStrokeWidth, (isFirst || isLast ? ry : selectedRY) + halfStrokeWidth);
        mSelectedPath.quadTo(halfStrokeWidth, halfStrokeWidth, ((isFirst || isLast ? rx : selectedRX) + halfStrokeWidth), halfStrokeWidth);
        mSelectedPath.lineTo(getWidth() - (isFirst || isLast ? rx : selectedRX) - halfStrokeWidth, halfStrokeWidth);
        mSelectedPath.quadTo(getWidth() - halfStrokeWidth, halfStrokeWidth, getWidth() - halfStrokeWidth, (isFirst || isLast ? ry : selectedRY) + halfStrokeWidth);
        mSelectedPath.lineTo(getWidth() - halfStrokeWidth, getHeight() - (isLast || isFirst ? ry : selectedRY) - halfStrokeWidth);
        mSelectedPath.quadTo(getWidth() - halfStrokeWidth, getHeight() - halfStrokeWidth,
                getWidth() - (isLast || isFirst ? rx : selectedRX) - halfStrokeWidth, getHeight() - halfStrokeWidth);
        mSelectedPath.lineTo((isFirst || isLast ? rx : selectedRX) + halfStrokeWidth, getHeight() - halfStrokeWidth);
        mSelectedPath.quadTo(halfStrokeWidth, getHeight() - halfStrokeWidth, halfStrokeWidth, getHeight() - (isLast || isFirst ? ry : selectedRY) - halfStrokeWidth);
        mSelectedPath.lineTo(halfStrokeWidth, (isLast || isFirst ? ry : selectedRY) + halfStrokeWidth);

        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawPath(mSelectedPath, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawPath(mSelectedPath, mPaint);
    }

    private void drawNormalSelected(Canvas canvas) {
        mSelectedPath.reset();
        mSelectedPath.moveTo(getPaddingLeft() + halfStrokeWidth, getPaddingTop());
        mSelectedPath.lineTo(getWidth() - getPaddingRight() - halfStrokeWidth, getPaddingTop());
        mSelectedPath.lineTo(getWidth() - getPaddingRight() - halfStrokeWidth, getHeight() - getPaddingBottom());
        mSelectedPath.lineTo(getPaddingLeft() + halfStrokeWidth, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawPath(mSelectedPath, mPaint);
        mSelectedPath.reset();
        mSelectedPath.moveTo(getPaddingLeft() + halfStrokeWidth, getPaddingTop() + halfStrokeWidth);
        mSelectedPath.lineTo(getPaddingLeft() + halfStrokeWidth, getHeight() - getPaddingBottom() - halfStrokeWidth);
        mSelectedPath.moveTo(getWidth() - getPaddingRight() - halfStrokeWidth, getPaddingTop() + halfStrokeWidth);
        mSelectedPath.lineTo(getWidth() - getPaddingRight() - halfStrokeWidth, getHeight() - getPaddingBottom() - halfStrokeWidth);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawPath(mSelectedPath, mPaint);
    }

    private void drawFirstSelected(Canvas canvas) {
        mSelectedPath.reset();
        mSelectedPath.moveTo(halfStrokeWidth, getHeight() - halfStrokeWidth);
        mSelectedPath.lineTo(halfStrokeWidth, (isFirst ? ry : selectedRY) + halfStrokeWidth);
        mSelectedPath.quadTo(halfStrokeWidth, halfStrokeWidth, ((isFirst ? rx : selectedRX) + halfStrokeWidth), halfStrokeWidth);
        mSelectedPath.lineTo(getWidth() - (isFirst ? rx : selectedRX) - halfStrokeWidth, halfStrokeWidth);
        mSelectedPath.quadTo(getWidth() - halfStrokeWidth, halfStrokeWidth, getWidth() - halfStrokeWidth, (isFirst ? ry : selectedRY) + halfStrokeWidth);
        mSelectedPath.lineTo(getWidth() - halfStrokeWidth, getHeight());
        mSelectedPath.lineTo(halfStrokeWidth, getHeight());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawPath(mSelectedPath, mPaint);

        mSelectedPath.reset();
        mSelectedPath.moveTo(halfStrokeWidth, getHeight() - halfStrokeWidth);
        mSelectedPath.lineTo(halfStrokeWidth, (isFirst ? ry : selectedRY) + halfStrokeWidth);
        mSelectedPath.quadTo(halfStrokeWidth, halfStrokeWidth, (isFirst ? rx : selectedRX) + halfStrokeWidth, halfStrokeWidth);
        mSelectedPath.lineTo(getWidth() - (isFirst ? rx : selectedRX) - halfStrokeWidth, halfStrokeWidth);
        mSelectedPath.quadTo(getWidth() - halfStrokeWidth, halfStrokeWidth, getWidth() - halfStrokeWidth, (isFirst ? ry : selectedRY) + halfStrokeWidth);
        mSelectedPath.lineTo(getWidth() - halfStrokeWidth, getHeight() - halfStrokeWidth);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawPath(mSelectedPath, mPaint);

    }

    private void drawFinalSelected(Canvas canvas) {
        mSelectedPath.reset();
        mSelectedPath.moveTo(halfStrokeWidth, 0);
        mSelectedPath.lineTo(getWidth() - halfStrokeWidth, 0);
        mSelectedPath.lineTo(getWidth() - halfStrokeWidth, getHeight() - (isLast ? ry : selectedRY) - halfStrokeWidth);
        mSelectedPath.quadTo(getWidth() - halfStrokeWidth, getHeight() - halfStrokeWidth,
                getWidth() - (isLast ? rx : selectedRX) - halfStrokeWidth, getHeight() - halfStrokeWidth);
        mSelectedPath.lineTo(isLast ? rx : selectedRX, getHeight() - halfStrokeWidth);
        mSelectedPath.quadTo(halfStrokeWidth, getHeight() - halfStrokeWidth, halfStrokeWidth, getHeight() - (isLast ? ry : selectedRY) - halfStrokeWidth);
        mSelectedPath.lineTo(halfStrokeWidth, 0);

        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawPath(mSelectedPath, mPaint);

        mSelectedPath.reset();
        mSelectedPath.moveTo(getWidth() - halfStrokeWidth, 0);
        mSelectedPath.lineTo(getWidth() - halfStrokeWidth, getHeight() - (isLast ? ry : selectedRY) - halfStrokeWidth);
        mSelectedPath.quadTo(getWidth() - halfStrokeWidth, getHeight() - halfStrokeWidth,
                getWidth() - (isLast ? rx : selectedRX) - halfStrokeWidth, getHeight() - halfStrokeWidth);
        mSelectedPath.lineTo(isLast ? rx : selectedRX, getHeight() - halfStrokeWidth);
        mSelectedPath.quadTo(halfStrokeWidth, getHeight() - halfStrokeWidth, halfStrokeWidth, getHeight() - (isLast ? ry : selectedRY) - halfStrokeWidth);
        mSelectedPath.lineTo(halfStrokeWidth, 0);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawPath(mSelectedPath, mPaint);
    }

    public void resetStatus(int status, int selectedStatus) {
        this.status = status;
        this.selectedStatus = selectedStatus;
        invalidate();
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
        invalidate();
    }

    public int getPvMode() {
        return pvMode;
    }

    public void setPvMode(int pvMode) {
        this.pvMode = pvMode;
    }

    public boolean isCustom() {
        return isCustom;
    }

    public void setCustom(boolean custom) {
        isCustom = custom;
    }

    public boolean isEdit() {
        return isEdit;
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
    }

    public boolean isExpand() {
        return isExpand;
    }

    public void setExpand(boolean expand) {
        isExpand = expand;
    }

    public boolean isFirst() {
        return isFirst;
    }

    public void setFirst(boolean first) {
        isFirst = first;
    }

    public boolean isLast() {
        return isLast;
    }

    public void setLast(boolean last) {
        isLast = last;
    }
}
