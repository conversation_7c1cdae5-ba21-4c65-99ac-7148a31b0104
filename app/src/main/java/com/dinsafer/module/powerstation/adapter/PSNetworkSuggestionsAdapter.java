package com.dinsafer.module.powerstation.adapter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.PSNetworkSuggestionsBean;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 10:57
 * @description :
 */
public class PSNetworkSuggestionsAdapter extends BaseQuickAdapter<PSNetworkSuggestionsBean, BaseViewHolder> {

    public PSNetworkSuggestionsAdapter() {
        super(R.layout.item_ps_network_suggestions);
    }

    @Override
    protected void convert(BaseViewHolder helper, PSNetworkSuggestionsBean item) {
        LocalTextView tvTitle = helper.getView(R.id.tv_title);
        LocalTextView tvDesc = helper.getView(R.id.tv_desc);
        tvTitle.setLocalText(item.getTitle());
        tvDesc.setLocalText(item.getDesc());
    }
}
