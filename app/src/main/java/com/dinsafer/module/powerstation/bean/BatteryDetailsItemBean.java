package com.dinsafer.module.powerstation.bean;


import androidx.annotation.DrawableRes;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 17:05
 * @description :
 */
public class BatteryDetailsItemBean {

    private String key;
    private String value;
    @DrawableRes
    private int logo;

    public BatteryDetailsItemBean(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public BatteryDetailsItemBean(String key, String value, int logo) {
        this.key = key;
        this.value = value;
        this.logo = logo;
    }

    public String getKey() {
        return key == null ? "" : key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value == null ? "" : value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getLogo() {
        return logo;
    }

    public void setLogo(int logo) {
        this.logo = logo;
    }
}
