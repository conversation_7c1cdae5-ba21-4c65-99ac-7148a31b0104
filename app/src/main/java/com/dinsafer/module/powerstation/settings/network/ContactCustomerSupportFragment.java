package com.dinsafer.module.powerstation.settings.network;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentContactCustomerSupportBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.Local;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 11:19
 * @description : 不要用这个, 用 FeedbackFragment
 */
@Deprecated
public class ContactCustomerSupportFragment extends MyBaseFragment<FragmentContactCustomerSupportBinding> {

    public static ContactCustomerSupportFragment newInstance() {
        ContactCustomerSupportFragment fragment = new ContactCustomerSupportFragment();
        return fragment;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_contact_customer_support;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setText(getResources().getString(R.string.app_setting_feedback));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_navbar_phonecall);
        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> {
            showPhoneCall("17777777777");
        } );
    }

    private void showPhoneCall(String phoneNum) {

        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.ps_advanced_settings_cancel)))
                .setOtherButtonTitles(getString(R.string.ps_customer_contact_customer_call_space) + phoneNum)
                .setCancelableOnTouchOutside(true)
                .setLastButtonTextColor(getResources().getColor(R.color.color_FF6497FD))
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);
                        Intent intent = new Intent(Intent.ACTION_DIAL);
                        Uri data = Uri.parse("tel:"+phoneNum);
                        intent.setData(data);
                        startActivity(intent);
                    }
                }).show();
    }
}
