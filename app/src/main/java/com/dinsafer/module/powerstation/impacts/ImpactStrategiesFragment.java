package com.dinsafer.module.powerstation.impacts;

import static com.dinsafer.model.panel.MainPanelHelper.VIEW_DISABLE_ALPHA;
import static com.dinsafer.model.panel.MainPanelHelper.VIEW_ENABLE_ALPHA;

import android.graphics.Typeface;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentImpactStrategiesBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.VirtualPowerPlantFragment;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.impacts.tab.PSEcoFragment;
import com.dinsafer.module.powerstation.impacts.tab.PSImpactsFragment;
import com.dinsafer.module.powerstation.impacts.tab.PSRevenueFragment;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.OnFragmentCreatedListener;
import com.flyco.tablayout.listener.OnTabSelectListener;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/24 18:26
 * @description :
 */
public class ImpactStrategiesFragment extends PSConnectLoadingFragment<FragmentImpactStrategiesBinding> {


    private CommonPagerAdapter mAdapter;
    public static Device sPSDevice;

    public static ImpactStrategiesFragment newInstance(String deviceId, String subcategory) {
        ImpactStrategiesFragment fragment = new ImpactStrategiesFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_impact_strategies;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setText(Local.s(getResources().getString(R.string.power_impacts_and_strategies)));
        mBinding.commonBar.vDivider.setVisibility(View.GONE);
        initVp();
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.tvReserveMode.setOnClickListener(v -> {
            getDelegateActivity().addCommonFragment(ReserveModeFragment.newInstance(mDeviceId, mSubcategory));
        });
        updateViewStateByDeviceOnlineStatus();
    }

    @Override
    public void onDestroyView() {
        sPSDevice = null;
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        sPSDevice = mPSDevice;
    }

    //    private void initParams() {
//        Bundle bundle = getArguments();
//        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
//        sPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId);
//    }

    private void initVp() {
        ArrayList<BaseFragment> fragmentList = new ArrayList<BaseFragment>();
        fragmentList.add(PSImpactsFragment.newInstance(0, new FragmentCreatedListener()));
        fragmentList.add(PSEcoFragment.newInstance(1, new FragmentCreatedListener()));
        fragmentList.add(PSRevenueFragment.newInstance(2, new FragmentCreatedListener()));
        mAdapter = new CommonPagerAdapter(getChildFragmentManager(), fragmentList);
        mBinding.vpIncome.setAdapter(mAdapter);
        String[] tabTittles = {Local.s(getString(R.string.impact_strategies_impacts)),
                Local.s(getString(R.string.impact_strategies_eco)), Local.s(getString(R.string.impact_strategies_revenue))};
        mBinding.vpIncome.setOffscreenPageLimit(3);
        mBinding.vpIncome.setScrollable(false);
        mBinding.tabIncome.setViewPager(mBinding.vpIncome, tabTittles);
        setTabTextStyle(0);
        mBinding.tabIncome.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                setTabTextStyle(position);
                mBinding.vpIncome.updateHeight(position);
            }

            @Override
            public void onTabReselect(int position) {

            }
        });
        mBinding.vpIncome.setCurrentItem(0);
    }

    private void setTabTextStyle(int position) {
        int tabCount = mBinding.tabIncome.getTabCount();
        for (int i = 0; i < tabCount; i++) {
            TextView title = mBinding.tabIncome.getTitleView(i);
            Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.poppins_medium);
            title.setTypeface(typeface);
            title.setPadding(DensityUtils.dp2px(getContext(), 8), 0, DensityUtils.dp2px(getContext(), 8), 0);
            title.setTextSize(TypedValue.COMPLEX_UNIT_SP, i == position ? 20 : 16);
            title.getPaint().setFakeBoldText(i == position);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BmtDeviceStatusChange event) {
        final String targetId = event.getDeviceID();
        if (TextUtils.isEmpty(targetId) || !targetId.equals(mDeviceId) || !event.getSubcategory().equals(mSubcategory)) {
            return;
        }

        updateViewStateByDeviceOnlineStatus();
    }

    private void updateViewStateByDeviceOnlineStatus() {
        final boolean connected = BmtUtil.isDeviceConnected(sPSDevice);
        if (connected) {
            mBinding.tvReserveMode.setEnabled(true);
            mBinding.tvReserveMode.setAlpha(VIEW_ENABLE_ALPHA);
        } else {
            mBinding.tvReserveMode.setEnabled(false);
            mBinding.tvReserveMode.setAlpha(VIEW_DISABLE_ALPHA);
        }
    }

    public class FragmentCreatedListener implements OnFragmentCreatedListener {

        @Override
        public void onCreated(View view, int position) {
            mBinding.vpIncome.setViewPosition(view, position);
        }
    }
}
