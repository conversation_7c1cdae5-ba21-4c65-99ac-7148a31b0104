package com.dinsafer.module.powerstation.settings;

import static com.dinsafer.model.panel.MainPanelHelper.VIEW_DISABLE_ALPHA;
import static com.dinsafer.model.panel.MainPanelHelper.VIEW_ENABLE_ALPHA;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.Constants;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPowerSettingsBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.iap.GetDeviceExpirationDateResponseV2;
import com.dinsafer.module.iap.UpdateServiceCardEvent;
import com.dinsafer.module.iap.powercare.TrafficPackageServiceFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PSUserGuideListFragment;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.bean.PSRegionBean;
import com.dinsafer.module.powerstation.ev.PSEVChargeV3Fragment;
import com.dinsafer.module.powerstation.event.BmtDeviceStatusChange;
import com.dinsafer.module.powerstation.event.PSRegionEvent;
import com.dinsafer.module.powerstation.impacts.ImpactStrategiesFragment;
import com.dinsafer.module.settting.ui.EdittextDialog;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.RegxUtil;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/28 11:59
 * @description :
 */
public class PowerSettingsFragment extends MyBaseFragment<FragmentPowerSettingsBinding> implements IDeviceCallBack {

    public static final String TAG = PowerSettingsFragment.class.getSimpleName();

    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;
    private boolean isSupportRegion;
    private Map<String, Object> params = new HashMap<>();
    private Call<GetDeviceExpirationDateResponseV2> getDeviceExpirationDateResponseCall;

    public static PowerSettingsFragment newInstance(String deviceId, String subcategory) {
        PowerSettingsFragment fragment = new PowerSettingsFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_power_settings;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        initParams();
        mBinding.llEnergySetting.setVisibility(BmtUtil.isBmtDevicePowerStore(mPSDevice)
                ? View.GONE : View.VISIBLE);
        mBinding.viewEnergySettingLine.setVisibility(BmtUtil.isBmtDevicePowerStore(mPSDevice) ? View.GONE : View.VISIBLE);
        mBinding.commonBar.commonBarTitle.setText("");
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.vDivider.setVisibility(View.GONE);
        mBinding.setClick(new OnClickHandler());
        mBinding.iv.setImageResource(BmtUtil.getCoverRes(mPSDevice));
        String unit = DBUtil.SGet(mDeviceId + DBKey.TEMPERATURE_UNIT);
        if (TextUtils.isEmpty(unit)) {
            unit = getString(R.string.ps_advanced_settings_celsius_unit);
        }

        mBinding.tvTemperatureUnits.setLocalText(unit);

        if (AppConfig.Functions.SUPPORT_TRAFFIC_PACKAGE_SERVICE) {
            mBinding.viewAlertService.tvTitle.setLocalText(getString(R.string.iap_4g_traffic_package));
            mBinding.viewAlertService.getRoot().setVisibility(View.VISIBLE);
            mBinding.viewAlertService.viewBottom.setBackgroundResource(R.color.device_management_content_background);
        } else {
            mBinding.viewAlertService.getRoot().setVisibility(View.GONE);
        }
        updateViewStateByDeviceSubcategory();
        updateViewStateByDeviceOnlineStatus();
    }

    @Override
    public void initData() {
        super.initData();
        getBmtIAPInfo();
    }

    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        if (getDeviceExpirationDateResponseCall != null) {
            getDeviceExpirationDateResponseCall.cancel();
            getDeviceExpirationDateResponseCall = null;
        }
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            String name = DeviceHelper.getString(mPSDevice, DinConst.INFO_NAME, "");
            if (TextUtils.isEmpty(name)) {
                mBinding.tvDeviceName.setText("");
            } else {
                mBinding.tvDeviceName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
            }
            mPSDevice.registerDeviceCallBack(this);
            int iotVersionStatus = BmtUtil.getIotVersionGETargetVersion(mPSDevice, "1.7.0");
            if (iotVersionStatus == 1) {
                mBinding.llTotalLoadSetting.setVisibility(View.VISIBLE);
                mBinding.viewTotalLoadSetting.setVisibility(View.VISIBLE);
            }
            showTimeOutLoadinFramgmentWithErrorAlert();
            submitCmd(DsCamCmd.GET_REGION);
        } else {
            showErrorToast();
        }
    }

    private void submitCmd(String cmd) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            mPSDevice.submit(params);
        }
    }

    private void showTemperatureUnits() {
        String[] titles = {Local.s(getResources().getString(R.string.ps_advanced_settings_celsius_unit)),
                Local.s(getResources().getString(R.string.ps_advanced_settings_fahrenheit_unit))};
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.ps_advanced_settings_cancel)))
                .setOtherButtonTitles(titles)
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);
                        DBUtil.SPut(mDeviceId + DBKey.TEMPERATURE_UNIT, titles[index]);
                        mBinding.tvTemperatureUnits.setText(titles[index]);

                    }
                }).show();
    }

    private void getBmtIAPInfo() {
        showTimeOutLoadinFramgment();
        getDeviceExpirationDateResponseCall = DinsafeAPI.getApi().getBmtIAPExpirationData(mDeviceId);
        getDeviceExpirationDateResponseCall.enqueue(new Callback<GetDeviceExpirationDateResponseV2>() {
            @Override
            public void onResponse(Call<GetDeviceExpirationDateResponseV2> call, Response<GetDeviceExpirationDateResponseV2> response) {
                if (response != null && response.body() != null && response.body().getResult() != null) {
                    int status = response.body().getResult().getStatus();
                    long expirationDate = response.body().getResult().getExpirationDate() / 1000_000;
                    String time = DDDateUtil.formatDate(new Date(expirationDate), "yyyy-MM-dd");
                    long currentTime = System.currentTimeMillis();
                    long daysDiff = TimeUtil.getDaysDiff(currentTime, expirationDate);
                    if (status > -1 && daysDiff < 1) { // 服务端接口 改为不返回过期状态，是否过期可用过期时间进行判断
                        mBinding.viewAlertService.tvStatus.setLocalText(R.string.expired);
                        mBinding.viewAlertService.tvSubtitle.setLocalText(R.string.ps_welcome_tips);
                        mBinding.viewAlertService.tvSubtitle.setTextColor(getResColor(R.color.color_setting_service_card_subtitle_color));
                    } else {
                        if (status == GetDeviceExpirationDateResponseV2.STATUS_FREE_TRIAL) {
                            mBinding.viewAlertService.tvStatus.setLocalText(R.string.free_trial);
                            mBinding.viewAlertService.tvSubtitle.setText(String.format("%s%s", Local.s(getString(R.string.free_trial_until)) + " ", time));
                            mBinding.viewAlertService.tvSubtitle.setTextColor(daysDiff <= 15 ? getResColor(R.color.color_tip_warning_02) : getResColor(R.color.color_setting_service_card_subtitle_color));
                        } else if (status == GetDeviceExpirationDateResponseV2.STATUS_IN_PURCHASE) {
                            mBinding.viewAlertService.tvStatus.setLocalText(R.string.active);
                            mBinding.viewAlertService.tvSubtitle.setText(String.format("%s%s", Local.s(getString(R.string.iap_expiration_date)) + " ", time));
                            mBinding.viewAlertService.tvSubtitle.setTextColor(daysDiff <= 15 ? getResColor(R.color.color_tip_warning_02) : getResColor(R.color.color_setting_service_card_subtitle_color));
                        } else if (status == GetDeviceExpirationDateResponseV2.STATUS_EXPIRED) {
                            mBinding.viewAlertService.tvStatus.setLocalText(R.string.expired);
                            mBinding.viewAlertService.tvSubtitle.setLocalText(R.string.ps_welcome_tips);
                            mBinding.viewAlertService.tvSubtitle.setTextColor(getResColor(R.color.color_setting_service_card_subtitle_color));
                        } else {
                            mBinding.viewAlertService.tvStatus.setLocalText(R.string.free_trial);
                            mBinding.viewAlertService.tvSubtitle.setLocalText(R.string.ps_welcome_tips);
                            mBinding.viewAlertService.tvSubtitle.setTextColor(getResColor(R.color.color_setting_service_card_subtitle_color));
                        }
                    }
                }
                closeLoadingFragment();
            }

            @Override
            public void onFailure(Call<GetDeviceExpirationDateResponseV2> call, Throwable t) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }
        });
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    switch (cmd) {
                        case DsCamCmd.GET_REGION:
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            if (status == StatusConstant.STATUS_SUCCESS) {
                                String region = (String) MapUtils.get(result, PSKeyConstant.REGION, null);
                                String deliveryAreas = (String) MapUtils.get(result, PSKeyConstant.DELIVERY_AREAS, "");
                                isSupportRegion = (boolean) MapUtils.get(result, PSKeyConstant.SMART_TARIFF_TRACKING, false);
                                boolean isGridConnSupport = DeviceHelper.getBoolean(result, BmtDataKey.GRID_CONN_SUPPORT, false);
//                                mBinding.llGridConfiguration.setVisibility(isGridConnSupport ? View.VISIBLE : View.GONE);
//                                mBinding.viewGridConfigurationLine.setVisibility(isGridConnSupport ? View.VISIBLE : View.GONE);

                                // 兼容服务器返回旧数据（city,country）
                                if (region.startsWith(",")) {
                                    region = region.substring(1);
                                }
                                region = region + " " + deliveryAreas;
                                mBinding.tvRegion.setLocalText(region);

                            }
                            break;
                        case DinConst.CMD_SET_NAME:
                            if (status == StatusConstant.STATUS_SUCCESS) {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                getDelegateActivity().showTopToast(R.drawable.icon_toast_succeed, getString(R.string.success));
                            } else {
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                            }
                            break;
                    }
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PSRegionEvent event) {
        PSRegionBean regionBean = event.getRegionBean();
        if (regionBean != null) {
            String region = regionBean.getCountryName();
            mBinding.tvRegion.setLocalText(region);
            mBinding.llGridConfiguration.setVisibility(regionBean.isGridConnSupport() ? View.VISIBLE : View.GONE);
            mBinding.viewGridConfigurationLine.setVisibility(regionBean.isGridConnSupport() ? View.VISIBLE : View.GONE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BmtDeviceStatusChange event) {
        final String targetId = event.getDeviceID();
        if (TextUtils.isEmpty(targetId) || !targetId.equals(mDeviceId) || !event.getSubcategory().equals(mPSDevice)) {
            return;
        }

        updateViewStateByDeviceOnlineStatus();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(UpdateServiceCardEvent event) {
        getBmtIAPInfo();
    }

    private void updateViewStateByDeviceOnlineStatus() {
        final boolean connected = BmtUtil.isDeviceConnected(mPSDevice);
        if (connected) {
            mBinding.tvBatteryOverview.setEnabled(true);
            mBinding.tvAccessories.setEnabled(true);
            boolean evEnabled = !BmtUtil.isBmtDevicePowerStore(mPSDevice);
            mBinding.tvEvCharge.setEnabled(evEnabled);
            mBinding.llTotalLoadSetting.setEnabled(true);
            mBinding.llEnergySetting.setEnabled(true);
            mBinding.tvBatteryOverview.setAlpha(VIEW_ENABLE_ALPHA);
            mBinding.tvAccessories.setAlpha(VIEW_ENABLE_ALPHA);
            mBinding.tvEvCharge.setAlpha(!evEnabled
                    ? VIEW_DISABLE_ALPHA : VIEW_ENABLE_ALPHA);
            mBinding.llTotalLoadSetting.setAlpha(VIEW_ENABLE_ALPHA);
            mBinding.llEnergySetting.setAlpha(VIEW_ENABLE_ALPHA);
        } else {
            mBinding.tvBatteryOverview.setEnabled(false);
            mBinding.tvAccessories.setEnabled(false);
            mBinding.tvEvCharge.setEnabled(false);
            mBinding.llTotalLoadSetting.setEnabled(false);
            mBinding.llEnergySetting.setEnabled(false);
            mBinding.tvBatteryOverview.setAlpha(VIEW_DISABLE_ALPHA);
            mBinding.tvAccessories.setAlpha(VIEW_DISABLE_ALPHA);
            mBinding.tvEvCharge.setAlpha(VIEW_DISABLE_ALPHA);
            mBinding.llTotalLoadSetting.setAlpha(VIEW_DISABLE_ALPHA);
            mBinding.llEnergySetting.setAlpha(VIEW_DISABLE_ALPHA);
        }
    }

    private void updateViewStateByDeviceSubcategory() {
        if (null == mPSDevice) {
            return;
        }
        boolean isPowerPulse = BmtUtil.isBmtDevicePowerPulse(mPSDevice);
        mBinding.tvBatteryOverview.setVisibility(View.VISIBLE);
        mBinding.tvAccessories.setVisibility(View.VISIBLE);
        mBinding.tvImpactStrategies.setVisibility(isPowerPulse ? View.GONE : View.VISIBLE);
        mBinding.tvEvCharge.setVisibility(isPowerPulse ? View.GONE : View.VISIBLE);
        if (isPowerPulse) {
            String text = (String) mBinding.tvBatteryOverview.getText();
            text = text.replace(" ", "\n");
            mBinding.tvBatteryOverview.setText(text);
            text = (String) mBinding.tvAccessories.getText();
            text = text.replace(" ", "\n");
            mBinding.tvAccessories.setText(text);
        }
        mBinding.llTemperatureUnits.setVisibility(View.VISIBLE);
        mBinding.llTotalLoadSetting.setVisibility(isPowerPulse ? View.GONE : View.VISIBLE);
        mBinding.viewTotalLoadSetting.setVisibility(isPowerPulse ? View.GONE : View.VISIBLE);
        mBinding.llEnergySetting.setVisibility((isPowerPulse || BmtUtil.isBmtDevicePowerStore(mPSDevice))
                ? View.GONE : View.VISIBLE);
        mBinding.viewEnergySettingLine.setVisibility((isPowerPulse || BmtUtil.isBmtDevicePowerStore(mPSDevice))
                ? View.GONE : View.VISIBLE);
        mBinding.llUserGuide.setVisibility(View.VISIBLE);
        mBinding.llAdvancedSetting.setVisibility(View.VISIBLE);
    }

    public void showChangeNameDialog() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(Local.s(getResources().getString(R.string.Confirm)))
                .setCancel(Local.s(getResources().getString(R.string.Cancel)))
                .setDefaultName(mBinding.tvDeviceName.getText().toString())
                .setContent(Local.s(getResources().getString(R.string.rename_accessory)))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
//                                            change ipc password
                        if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                            dialog.dismiss();
                            return;
                        }
                        dialog.dismiss();
                        if (string.equals(mBinding.tvDeviceName.getText().toString())) {
                            return;
                        }
                        mBinding.tvDeviceName.setText(string);
                        saveName(string);
                    }
                })
                .preBuilder()
                .show();
    }

    private void saveName(@NonNull final String newName) {
        if (null == mPSDevice) {
            showErrorToast();
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DinConst.CMD_SET_NAME);
        data.put("name", newName);
        mPSDevice.submit(data);
    }

    public class OnClickHandler {
        public void deviceName() {
            showChangeNameDialog();
        }

        public void region() {
            getDelegateActivity().addCommonFragment(PSRegionFragment.newInstance(mDeviceId, mSubcategory));
        }

        public void batteryOverview() {
            getDelegateActivity().addCommonFragment(BatteryOverviewFragment.newInstance(mDeviceId, mSubcategory));
        }

        public void accessories() {
            getDelegateActivity().addCommonFragment(AccessoriesFragment.newInstance(mDeviceId, mSubcategory));
        }

        public void powerCare() {
            getDelegateActivity().addCommonFragment(TrafficPackageServiceFragment.newInstance(mDeviceId, mSubcategory));
        }

        public void impactStrategies() {
            getDelegateActivity().addCommonFragment(ImpactStrategiesFragment.newInstance(mDeviceId, mSubcategory));
        }

        public void evCharge() {
            if (BmtUtil.isDeviceConnected(mPSDevice)) {
                getDelegateActivity().addCommonFragment(PSEVChargeV3Fragment.newInstance(mDeviceId, mSubcategory));
            }
        }

        public void temperatureUnits() {
            showTemperatureUnits();
        }

        public void gridConfiguration() {
            getDelegateActivity().addCommonFragment(OnGridConfigurationFragment.newInstance(mDeviceId, mSubcategory));
        }

        public void totalLoadSetting() {
            getDelegateActivity().addCommonFragment(PSTotalLoadSettingFragment.newInstanceFromSetting(mDeviceId, mSubcategory));
        }

        public void energySetting() {
            getDelegateActivity().addCommonFragment(EnergySettingFragment.newInstanceFromSetting(mDeviceId, mSubcategory));
        }

        public void userGuide() {
            getDelegateActivity().addCommonFragment(PSUserGuideListFragment.newInstance(mDeviceId, mSubcategory));
        }

        public void advancedSetting() {
            getDelegateActivity().addCommonFragment(PSAdvancedSettingsFragment.newInstance(mDeviceId, mSubcategory));
        }
    }
}
