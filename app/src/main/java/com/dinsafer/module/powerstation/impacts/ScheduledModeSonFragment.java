package com.dinsafer.module.powerstation.impacts;

import android.os.Bundle;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentScheduledModeSonBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.dialog.ScheduledModeDialog;
import com.dinsafer.module.powerstation.event.SameWithWeekdaysEvent;
import com.dinsafer.module.powerstation.event.ScheduledModeEvent;
import com.dinsafer.module.powerstation.widget.schedule_mode_view.ScheduledModeBean;
import com.dinsafer.module.powerstation.widget.schedule_mode_view.ScheduledModeModel;
import com.dinsafer.module.powerstation.widget.schedule_mode_view.ScheduledModeView;
import com.dinsafer.util.CollectionUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class ScheduledModeSonFragment extends MyBaseFragment<FragmentScheduledModeSonBinding> {

    private static final String KEY_EMERGENCY_RESERVE = "key_emergency_reserve";
    private static final String KEY_SMART_RESERVE = "key_smart_reserve";
    private static final String KEY_SCHEDULE_DATA = "key_scheduled_data";
    // 0. 工作日 1. 周末
    private int mPosition;
    private int mEmergencyReserve;
    private int mSmartReserve;
    private boolean isSame;
    private ArrayList<Integer> mScheduledData;
    private List<ScheduledModeModel> mCacheData = new ArrayList<>();
    private List<ScheduledModeBean> mScheduledModeBeanCacheData = new ArrayList<>();
    private boolean isGridToBattery;

    public static ScheduledModeSonFragment newInstance(int position, int emergencyReserve,
                                                       int smartReserve, ArrayList<Integer> scheduledData,
                                                       boolean isGridToBattery) {
        ScheduledModeSonFragment fragment = new ScheduledModeSonFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_POSITION, position);
        bundle.putInt(KEY_EMERGENCY_RESERVE, emergencyReserve);
        bundle.putInt(KEY_SMART_RESERVE, smartReserve);
        bundle.putIntegerArrayList(KEY_SCHEDULE_DATA, scheduledData);
        bundle.putBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY, isGridToBattery);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_scheduled_mode_son;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        initParams();
        mBinding.tvCharge.setVisibility(isGridToBattery ? View.VISIBLE : View.GONE);
        ConstraintLayout.LayoutParams viewHelperLayoutParams = (ConstraintLayout.LayoutParams) mBinding.viewHelper.getLayoutParams();
        viewHelperLayoutParams.width = DensityUtil.dp2px(getContext(), isGridToBattery ? 52 : 110);
        mBinding.viewHelper.setLayoutParams(viewHelperLayoutParams);
        initScheduleMode();
    }

    @Override
    public void onDestroyView() {
        mBinding.viewScheduledMode.release();
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mPosition = bundle.getInt(PSKeyConstant.KEY_POSITION);
        mEmergencyReserve = bundle.getInt(KEY_EMERGENCY_RESERVE);
        mSmartReserve = bundle.getInt(KEY_SMART_RESERVE);
        mScheduledData = bundle.getIntegerArrayList(KEY_SCHEDULE_DATA);
        isGridToBattery = bundle.getBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY);
    }

    private void initScheduleMode() {
        mBinding.viewScheduledMode.setSelectedListener(selectedData -> new ScheduledModeDialog.Builder()
                .setSelectedMode(getSelectedMode(selectedData))
                .setSectionType(getSectionType(selectedData))
                .setEmergencyReserve(mEmergencyReserve)
                .setSmartReserve(mSmartReserve)
                .setGridToBattery(isGridToBattery)
                .setConfirmListener(new ScheduledModeDialog.OnConfirmListener() {
                    @Override
                    public void confirm(int mode, int sectionType) {
                        for (ScheduledModeModel scheduledModeModel : selectedData) {
                            ScheduledModeBean scheduledModeBean = scheduledModeModel.getScheduledModeBean();
                            scheduledModeBean.setMode(mode);
                            scheduledModeBean.setSectionType(sectionType);
                            if (sectionType == 1) {
                                int secOneVal = mEmergencyReserve;
                                if (mode == -1) {
                                    secOneVal = 100 - mSmartReserve;
                                }
                                scheduledModeBean.setPercentage(secOneVal);
                            } else if (sectionType == 2) {
                                int secTwoVal = mSmartReserve;
                                if (mode == -1) {
                                    secTwoVal = 100 - mEmergencyReserve;
                                }
                                scheduledModeBean.setPercentage(secTwoVal);
                            } else if (sectionType == 3) {
                                scheduledModeBean.setPercentage(100);
                            }
                            if (mode == 0) {
                                scheduledModeBean.setPercentage(0);
                            }
                            mBinding.viewScheduledMode.resetItem(scheduledModeBean);
                        }
                        if (mPosition == 0 && isSame) {
                            EventBus.getDefault().post(new SameWithWeekdaysEvent(1, isSame, selectedData));
                        }
                    }

                    @Override
                    public void onCancel() {
                        mBinding.viewScheduledMode.canCelHighLight();
                    }
                }).build().show(getChildFragmentManager(), ScheduledModeDialog.TAG));
        mBinding.viewScheduledMode.setCharWithGrid(isGridToBattery);
        mBinding.viewScheduledMode.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (CollectionUtil.isListNotEmpty(mScheduledData)) {
                    List<ScheduledModeBean> data = new ArrayList<>();
                    for (int i = 0; i < mScheduledData.size(); i++) {
                        int percentage = mScheduledData.get(i);

                        int mode = 0;
                        if (percentage < 0) {
                            mode = -1;
                        } else if (percentage == 0) {
                            mode = 0;
                        } else {
                            mode = 1;
                        }
                        int sectionType = 1;
                        int perAbs = Math.abs(percentage);
                        if (perAbs <= mEmergencyReserve) {
                            sectionType = 1;
                        } else if (perAbs <= mSmartReserve) {
                            sectionType = 2;
                        } else if (perAbs <= 100) {
                            sectionType = mode > 0 ? 3 : 2;
                        }
                        ScheduledModeBean scheduledModeBean = new ScheduledModeBean(mode, i, Math.abs(percentage));
                        scheduledModeBean.setSectionType(sectionType);
                        data.add(scheduledModeBean);
                    }
                    mBinding.viewScheduledMode.resetAllData(data);
                }
            }
        }, 200);
    }

    public void setScheduledData(ArrayList<Integer> scheduledData) {
        this.mScheduledData = scheduledData;
        if (CollectionUtil.isListNotEmpty(mScheduledData)) {
            List<ScheduledModeBean> data = new ArrayList<>();
            for (int i = 0; i < mScheduledData.size(); i++) {
                int percentage = mScheduledData.get(i);

                int mode = 0;
                if (percentage < 0) {
                    mode = -1;
                } else if (percentage == 0) {
                    mode = 0;
                } else {
                    mode = 1;
                }
                int sectionType = 1;
                int perAbs = Math.abs(percentage);
                if (perAbs <= mEmergencyReserve) {
                    sectionType = mode < 0 ? 2 : 1;
                } else if (perAbs <= mSmartReserve) {
                    sectionType = mode < 0 ? 1 : 2;
                } else if (perAbs <= 100) {
                    sectionType = mode > 0 ? 3 : 2;
                }
                ScheduledModeBean scheduledModeBean = new ScheduledModeBean(mode, i, mode < 0 ? (100 - perAbs) : perAbs);
                scheduledModeBean.setSectionType(sectionType);
                data.add(scheduledModeBean);
            }
            mBinding.viewScheduledMode.resetAllData(data);
        }
    }

    /**
     * 缓存数据
     *
     * @param weekendData
     */
    public void setCacheData(ArrayList<Integer> weekendData) {
        mScheduledModeBeanCacheData.clear();
        for (int i = 0; i < weekendData.size(); i++) {
            int percentage = weekendData.get(i);
            int mode = 0;
            if (percentage < 0) {
                mode = -1;
            } else if (percentage == 0) {
                mode = 0;
            } else {
                mode = 1;
            }
            int sectionType = 1;
            int perAbs = Math.abs(percentage);
            if (perAbs <= mEmergencyReserve) {
                sectionType = mode < 0 ? 2 : 1;
            } else if (perAbs <= mSmartReserve) {
                sectionType = mode < 0 ? 1 : 2;
            } else if (perAbs <= 100) {
                sectionType = mode > 0 ? 3 : 2;
            }
            ScheduledModeBean scheduledModeBean = new ScheduledModeBean(mode, i, mode < 0 ? (100 - perAbs) : perAbs);
            scheduledModeBean.setSectionType(sectionType);
            mScheduledModeBeanCacheData.add(scheduledModeBean);
        }
    }

    /**
     * 模式对应值变化
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onScheduledModeEvent(ScheduledModeEvent event) {
        int position = event.getPosition();
//        if (isSame && position == 1) return;
        if (isSame || mPosition == position) {
            Boolean isEmergency = event.getEmergency();
            if (isEmergency != null) {
                int reserve = event.getReserve();
                if (isEmergency) {
                    mEmergencyReserve = reserve;
                    mBinding.viewScheduledMode.resetEmergencyReserve(reserve);
                } else {
                    mSmartReserve = reserve;
                    mBinding.viewScheduledMode.resetSmartReserve(reserve);
                }
            } else {
                int emergencyReserve = event.getEmergencyReserve();
                int smartReserve = event.getSmartReserve();
                mEmergencyReserve = emergencyReserve;
                mSmartReserve = smartReserve;
                mBinding.viewScheduledMode.resetReserve(emergencyReserve, smartReserve);
            }
        }
    }

    /**
     * 与工作日同步
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSameWithWeekdaysEvent(SameWithWeekdaysEvent event) {
        int position = event.getPosition();
        isSame = event.isSame();
        if (mPosition == 1) {
            if (isSame && position == 0) {
                List<ScheduledModeModel> modeModels = mBinding.viewScheduledMode.getScheduledModeData();
                if (CollectionUtil.isListEmpty(mCacheData)) {
                    for (ScheduledModeModel modeModel : modeModels) {
                        ScheduledModeBean scheduledModeBean = modeModel.getScheduledModeBean();
                        ScheduledModeBean cacheBean = new ScheduledModeBean(scheduledModeBean.getMode(),
                                scheduledModeBean.getHour(), scheduledModeBean.getPercentage(),
                                scheduledModeBean.getSectionType(), scheduledModeBean.isSelected());
                        mCacheData.add(new ScheduledModeModel(modeModel.getContext(), modeModel.getHour(),
                                cacheBean, modeModel.getEmergencyReserve(),
                                modeModel.getSmartReserve(), isGridToBattery));
                    }
                } else {
                    for (int i = 0; i < modeModels.size(); i++) {
                        ScheduledModeModel modeModel = modeModels.get(i);
                        ScheduledModeModel modelModelCache = mCacheData.get(i);
                        modelModelCache.setSmartReserve(modeModel.getSmartReserve());
                        modelModelCache.setEmergencyReserve(modeModel.getEmergencyReserve());
                        ScheduledModeBean scheduledModeBean = modeModel.getScheduledModeBean();
                        ScheduledModeBean cacheBean = modelModelCache.getScheduledModeBean();
                        cacheBean.setMode(scheduledModeBean.getMode());
                        cacheBean.setHour(scheduledModeBean.getHour());
                        cacheBean.setPercentage(scheduledModeBean.getPercentage());
                        cacheBean.setSectionType(scheduledModeBean.getSectionType());
                        cacheBean.setSelected(scheduledModeBean.isSelected());
                        modelModelCache.setScheduledModeBean(cacheBean);
                    }
                }
            }
            mBinding.tvCharge.setAlpha(isSame ? 0.5f : 1f);
            mBinding.tvDischarge.setAlpha(isSame ? 0.5f : 1f);
            mBinding.viewScheduledMode.setRecyclerViewEnabled(!isSame);
            if (CollectionUtil.isListEmpty(event.getModelData())) {
                return;
            }
        }
        if (position == 0 && mPosition == 0) {
            List<ScheduledModeModel> modelData = mBinding.viewScheduledMode.getScheduledModeData();
            EventBus.getDefault().post(new SameWithWeekdaysEvent(1, isSame, modelData));
        } else if (position == 1 && mPosition == 1) {
            List<ScheduledModeModel> modelData = event.getModelData();
            List<ScheduledModeModel> weekendsData = mBinding.viewScheduledMode.getScheduledModeData();
            if (isSame) {
                for (int i = 0; i < modelData.size(); i++) {
                    ScheduledModeModel weekdaysModel = modelData.get(i);
                    int hour = weekdaysModel.getHour();
                    ScheduledModeModel weekendsModel = weekendsData.get(hour);
                    weekendsModel.setEmergencyReserve(weekdaysModel.getEmergencyReserve());
                    weekendsModel.setSmartReserve(weekdaysModel.getSmartReserve());
                    ScheduledModeBean weekdaysBean = weekdaysModel.getScheduledModeBean();
                    ScheduledModeBean weekendsBean = weekendsModel.getScheduledModeBean();
                    weekendsBean.setMode(weekdaysBean.getMode());
                    weekendsBean.setSectionType(weekdaysBean.getSectionType());
                    weekendsBean.setPercentage(weekdaysBean.getPercentage());
                }
                mBinding.viewScheduledMode.notifyDataChange();
            } else {
                if (CollectionUtil.isListNotEmpty(mCacheData)) {
                    for (int i = 0; i < mCacheData.size(); i++) {
                        ScheduledModeModel cacheModel = mCacheData.get(i);
                        int hour = cacheModel.getHour();
                        ScheduledModeModel weekendsModel = weekendsData.get(hour);
                        weekendsModel.setEmergencyReserve(cacheModel.getEmergencyReserve());
                        weekendsModel.setSmartReserve(cacheModel.getSmartReserve());
                        ScheduledModeBean cacheBean = cacheModel.getScheduledModeBean();
                        ScheduledModeBean weekendsBean = weekendsModel.getScheduledModeBean();
                        weekendsBean.setMode(cacheBean.getMode());
                        weekendsBean.setSectionType(cacheBean.getSectionType());
                        weekendsBean.setPercentage(cacheBean.getPercentage());
                    }
                    mBinding.viewScheduledMode.notifyDataChange();
                } else {
                    mBinding.viewScheduledMode.resetAllData(mScheduledModeBeanCacheData);
                }
            }

        }
    }

    /**
     * 获取选中的数据模式
     *
     * @param selectedData
     * @return
     */
    public int getSelectedMode(List<ScheduledModeModel> selectedData) {
        if (CollectionUtil.isListEmpty(selectedData)) return -2;
        int mode = selectedData.get(0).getScheduledModeBean().getMode();
        int percentage = selectedData.get(0).getScheduledModeBean().getPercentage();
        if (selectedData.size() > 1) {
            for (int i = 1; i < selectedData.size(); i++) {
                if (selectedData.get(i).getScheduledModeBean().getMode() != mode
                        || selectedData.get(i).getScheduledModeBean().getPercentage() != percentage) {
                    mode = -2;
                    break;
                }
            }
        }
        return mode;
    }

    /**
     * 获取选中的数据分段类型
     *
     * @param selectedData
     * @return
     */
    public int getSectionType(List<ScheduledModeModel> selectedData) {
        if (CollectionUtil.isListEmpty(selectedData)) return 0;
        int sectionType = selectedData.get(0).getScheduledModeBean().getSectionType();
        int mode = selectedData.get(0).getScheduledModeBean().getMode();
        if (selectedData.size() > 1) {
            for (int i = 1; i < selectedData.size(); i++) {
                if (selectedData.get(i).getScheduledModeBean().getSectionType() != sectionType) {
                    sectionType = 0;
                    break;
                }
            }
        }
        return mode < 0 ? (3 - sectionType) : sectionType;
    }

    public List<ScheduledModeBean> getScheduleModeData() {
        return mBinding.viewScheduledMode.getScheduledModeBeanData();
    }

    public void setEmergencyReserve(int emergencyReserve) {
        this.mEmergencyReserve = emergencyReserve;
    }

    public void setSmartReserve(int smartReserve) {
        this.mSmartReserve = smartReserve;
    }

    public void setEdit(boolean isEdit) {
        mBinding.viewScheduledMode.setCanTouch(isEdit);
    }
}
