package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import android.util.Log;
import android.view.View;

import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAccessoriesBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.AccessoriesAdapter;
import com.dinsafer.module.powerstation.bean.PSAccessoryBean;
import com.dinsafer.module.powerstation.bean.PSAccessoryListBean;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 18:10
 * @description :
 */
public class AccessoriesFragment extends PSConnectLoadingFragment<FragmentAccessoriesBinding> implements IDeviceCallBack {

    private AccessoriesAdapter mAccessoriesAdapter;

    private Map<String, Object> params = new HashMap<>();
    private List<PSAccessoryListBean> mCabinetList;
    private String subCategory;

    public static AccessoriesFragment newInstance(String deviceId, String subcategory) {
        AccessoriesFragment fragment = new AccessoriesFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_accessories;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_accessories));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        initRv();
        submitCmd(DsCamCmd.GET_CABINET_ALL_INFO);
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
            subCategory = mPSDevice.getSubCategory();
        }
    }

    private void submitCmd(String cmd) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            showLoadingFragment(0);
            mPSDevice.submit(params);
        }
    }

    private void submitCmdWithIndex(String cmd, int index) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            params.put(PSKeyConstant.INDEX, index);
            showLoadingFragment(0);
            mPSDevice.submit(params);
        }
    }

    private void initRv() {
        ((SimpleItemAnimator) mBinding.rvAccessories.getItemAnimator()).setSupportsChangeAnimations(false);
        mBinding.rvAccessories.setLayoutManager(new LinearLayoutManager(getContext()));
        mAccessoriesAdapter = new AccessoriesAdapter();
        mBinding.rvAccessories.setAdapter(mAccessoriesAdapter);
        mCabinetList = new ArrayList<>();
        mAccessoriesAdapter.setNewData(mCabinetList);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        Log.v(TAG, "onCmdCallBack, bmtDevice===: " + deviceId
                + " " + subCategory
                + " /cmd:" + cmd
                + " /result:" + map.toString()
                + " /" + Thread.currentThread().getName());
        if (StringUtil.isNotEmpty(deviceId) && StringUtil.isNotEmpty(mDeviceId) && mPSDevice != null && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (StringUtil.isNotEmpty(cmd) && (cmd.equals(DsCamCmd.GET_CABINET_ALL_INFO) || cmd.equals(DsCamCmd.GET_CABINET_STATE))) {
                closeLoadingFragment();
            }
            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (result != null && result.size() > 0) {
                            int count;
                            int index;
                            int waterState = 0;
                            Integer smokeState;
                            int fanState = 0;
                            switch (cmd) {
                                case DsCamCmd.GET_CABINET_ALL_INFO:
                                    count = (int) MapUtils.get(result, PSKeyConstant.COUNT, 0);
                                    mCabinetList.clear();

                                    for (int i = 0; i < count; i++) {
                                        String cabinetIndexKey = getString(R.string.ps_battery_overview_cabinet);
                                        String cabinetIndexSuffix = getString(R.string.ps_hashtag_cabinet_index);
                                        String indexStr = (i + 1) < 10 ? "0" + (i + 1) : "" + (i + 1);
                                        PSAccessoryListBean psAccessoryListBean = new PSAccessoryListBean(i, Local.s(cabinetIndexKey).replace(cabinetIndexSuffix, indexStr));
                                        int fanSize = BmtUtil.isThreePhase(mPSDevice) ? 3 : 1;
                                        List<PSAccessoryBean> accessoryList = new ArrayList<>();
                                        if (i == 0) {
                                            for (int j = 0; j < fanSize; j++) {
                                                String accessoryName = Local.s(getString(R.string.ps_accessory_fans_pack)).replace("#fan_index", " " + "0" + (j + 1));
                                                accessoryList.add(new PSAccessoryBean(accessoryName, PSAccessoryBean.FANS_TYPE, fanState));
                                            }
                                        }
                                        String waterSensorName = getString(R.string.ps_accessory_water_sensor);
                                        accessoryList.add(new PSAccessoryBean(waterSensorName, PSAccessoryBean.WATER_TYPE, waterState));
                                        psAccessoryListBean.setAccessories(accessoryList);
                                        mCabinetList.add(psAccessoryListBean);
                                        submitCmdWithIndex(DsCamCmd.GET_CABINET_STATE, i);
                                        if (i == 0) {
                                            for (int j = 0; j < fanSize; j++) {
                                                submitCmdWithIndex(DsCamCmd.GET_INVERTER_INFO, j);
                                            }
                                        }
                                    }
                                    mAccessoriesAdapter.notifyDataSetChanged();
                                    break;

                                case DsCamCmd.GET_INVERTER_INFO:
                                    index = (int) MapUtils.get(result, PSKeyConstant.INDEX, -1);
                                    fanState = (int) MapUtils.get(result, PSKeyConstant.FAN_STATE, 0);
                                    List<Integer> exceptions = (List<Integer>) MapUtils.get(result, BmtDataKey.VERT_SYSTEM_EXCEPTIONS, null);
                                    Integer fanException = Mcu.Inverter.InverterSystemException.Fan.getCode();
                                    if (CollectionUtil.isListNotEmpty(exceptions) && exceptions.contains(fanException)) {
                                        fanState = PSAccessoryBean.FAN_STATE_EXCEPTION;
                                    }
                                    int fanSize = BmtUtil.isThreePhase(mPSDevice) ? 3 : 1;
                                    if (index > -1 && index < fanSize && mAccessoriesAdapter != null && CollectionUtil.isListNotEmpty(mAccessoriesAdapter.getData())) {
                                        mAccessoriesAdapter.getItem(0).getAccessories().get(index).setStatus(fanState);
                                        mAccessoriesAdapter.notifyItemChanged(0);
                                    }
                                    break;

                                case DsCamCmd.GET_CABINET_STATE:
                                case DsCamCmd.CABINET_STATE_CHANGED:
                                    index = (int) MapUtils.get(result, PSKeyConstant.INDEX, -1);
                                    List<PSAccessoryBean> changeAccessoryList = mCabinetList.get(index).getAccessories();
                                    fanState = (int) MapUtils.get(result, PSKeyConstant.FAN_STATE, -1);
                                    waterState = (Integer) MapUtils.get(result, PSKeyConstant.WATER_STATE, null);
                                    if (changeAccessoryList != null) {
                                        for (int i = 0; i < changeAccessoryList.size(); i++) {
                                            PSAccessoryBean psAccessoryBean = changeAccessoryList.get(i);
                                            if (psAccessoryBean.getType() == PSAccessoryBean.WATER_TYPE) {
                                                psAccessoryBean.setStatus(waterState);
                                                mAccessoriesAdapter.notifyItemChanged(index);
                                            }
                                        }
                                    }
                                    smokeState = (Integer) MapUtils.get(result, PSKeyConstant.SMOKE_STATE, null);
                                    break;
                            }
                        }
                    }
                });

            }
        }
    }
}
