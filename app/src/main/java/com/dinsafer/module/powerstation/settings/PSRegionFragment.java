package com.dinsafer.module.powerstation.settings;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsRegionBinding;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.iap.GetDeviceExpirationDateResponseV2;
import com.dinsafer.module.powerstation.CityComparator;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PsElectricityAreaAdapter;
import com.dinsafer.module.powerstation.adapter.PsRegionAdapter;
import com.dinsafer.module.powerstation.bean.PSRegionBean;
import com.dinsafer.module.powerstation.bean.PsElectricityAreaBean;
import com.dinsafer.module.powerstation.device.PSWelcomeFragment;
import com.dinsafer.module.powerstation.event.FinishEvent;
import com.dinsafer.module.powerstation.event.PSRegionEvent;
import com.dinsafer.module.powerstation.utils.comparator.PSRegionComparator;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.IndexView;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/28 16:32
 * @description :
 */
public class PSRegionFragment extends PSConnectLoadingFragment<FragmentPsRegionBinding> implements IDeviceCallBack {

//    private static final int PARAM_FROM_SETTING = 0;
//    private static final int PARAM_FROM_STEP_ADD = 1;

    private static final String KEY_FROM = "key_from";
    private PsRegionAdapter mPsRegionAdapter;

    // 0. PowerSettingsFragment
    // 1. PSDeviceSettingFragment
//    private int mFrom = PARAM_FROM_SETTING;

    private Map<String, Object> params = new HashMap<>();
    private String mCountry = "Denmark";
    private ArrayList<PSRegionBean> mRegions = new ArrayList<>();
    private CityComparator mCityComparator = new CityComparator();
    private PSRegionComparator mSearchComparator;
    private PSRegionBean mSelRegion;
    private ArrayList<String> indexs;
    private LinkedHashMap<String, Integer> indexPosMap;
    private int lastScrollIndexPos = 0;

    private PsElectricityAreaAdapter electricityAreaAdapter;
    private ArrayList<PsElectricityAreaBean> electricityAreaBeans = new ArrayList<>();
    private float listUpPx;


    public static PSRegionFragment newInstance(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_SETTING, deviceId, subcategory);
    }

    public static PSRegionFragment newInstanceForStepAddPS(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_STEP_ADD, deviceId, subcategory);
    }

    public static PSRegionFragment newInstance(int from, String deviceId, String subcategory) {
        PSRegionFragment fragment = new PSRegionFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_FROM, from);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_region;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_region));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        initParams();
        mBinding.tvSearch.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PSRegionSearchFragment.newInstance(mDeviceId, mSubcategory, mCountry, mRegions)));
        mBinding.tvGotIt.setOnClickListener(v -> {
            DBUtil.Put(DBKey.KEY_PS_REGION_TIPS_NEVER_SHOW_AGAIN, true);
            mBinding.clDesc.setVisibility(View.GONE);
        });
        mBinding.etSearch.setHint(Local.s(getString(R.string.ps_region_enter_your_country)));
        mBinding.ivClear.setOnClickListener(v -> {
            mBinding.etSearch.setText("");
            mPsRegionAdapter.setNewData(mRegions);
        });

        setEditChangeListener();
        controlClDesc();
        initRv();
        initElectricityAreaRv();

        submitCmd(DsCamCmd.GET_REGION_COUNTRIES, "");
    }

    private void controlClDesc() {
        boolean isNever = DBUtil.Bool(DBKey.KEY_PS_REGION_TIPS_NEVER_SHOW_AGAIN);
        if (!isNever) {
            mBinding.clDesc.setVisibility(View.VISIBLE);
        }
    }

    private boolean isNotSupport(PSRegionBean psRegionBean) {
        String psCountryCode = psRegionBean.getCountryCode();
        if (StringUtil.isNotEmpty(psCountryCode)) {
            return !psRegionBean.isSmartTariffTracking() || !psRegionBean.isGridToBattery();
        }
        return true;
    }

    private void setEditChangeListener() {
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String text = mBinding.etSearch.getText().toString();
                setSearchStatus(!TextUtils.isEmpty(text));
                if (text == null || text.length() == 0) {
                    setEmpty(false);
                    mPsRegionAdapter.setNewData(mRegions);
                } else {
                    ArrayList<PSRegionBean> newData = new ArrayList<>();
                    for (PSRegionBean item : mRegions) {
                        String cityStr = (item.getCountryNameDisplay() == null ? "" : item.getCountryNameDisplay().toLowerCase());
                        if (cityStr.contains(text.toLowerCase())) {
                            newData.add(item);
                        }
                    }
                    if (mSearchComparator == null) {
                        mSearchComparator = new PSRegionComparator(text);
                    } else {
                        mSearchComparator.setSearchKeyword(text);
                    }
                    Collections.sort(newData, mSearchComparator);
                    mPsRegionAdapter.setNewData(newData);
                    setEmpty(newData.size() == 0);
                }
            }
        });
    }

    private void setEmpty(boolean empty) {
        mBinding.llEmpty.setVisibility(empty ? View.VISIBLE : View.GONE);
        mBinding.rvRegionSupport.setVisibility(empty ? View.GONE : View.VISIBLE);
    }

    private void setSearchStatus(boolean isSearched) {
        mBinding.ivClear.setVisibility(isSearched ? View.VISIBLE : View.GONE);
        mBinding.tvRegionSupport.setVisibility(isSearched ? View.GONE : View.VISIBLE);
        mBinding.indexView.setVisibility(isSearched ? View.GONE : View.VISIBLE);
        boolean isNever = DBUtil.Bool(DBKey.KEY_PS_REGION_TIPS_NEVER_SHOW_AGAIN);
        if (!isNever) {
            mBinding.clDesc.setVisibility(isSearched ? View.GONE : View.VISIBLE);
        }
    }

    @Override
    public void initData() {
        super.initData();
        indexs = new ArrayList<>();
        indexPosMap = new LinkedHashMap<>();
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
            Map<String, String> map = new HashMap<>();
            map.put(BmtDataKey.CMD, BmtCmd.GET_ALL_SUPPORT_FEATURES);
            mPSDevice.submit(map);
        }
    }


    private void initRv() {
        mBinding.rvRegionSupport.setLayoutManager(new LinearLayoutManager(getContext()));
        mPsRegionAdapter = new PsRegionAdapter();
        mBinding.rvRegionSupport.setAdapter(mPsRegionAdapter);
        mPsRegionAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                DDLog.d(TAG, "onItemClick: position: " + position);
                PSRegionBean regionBean = mPsRegionAdapter.getItem(position);
                mSelRegion = regionBean;
                if (isNotSupport(mSelRegion)) {
                    showNonsupportDialog(getString(R.string.ps_region_regions_nonsupport), mSelRegion);
                } else {
                    initElectricityAreaData();

                    if (mSelRegion.getDeliveryAreas() != null) {
                        if (mSelRegion.getDeliveryAreas().size() == 1) {
                            submitSetRegionCmd(regionBean
                                    , regionBean.isSmartTariffTracking() && regionBean.isGridToBattery()
                                    ,  mSelRegion.getDeliveryAreas().get(0));
                        } else {
                            toAppear();
                        }
                    } else {
                        submitSetRegionCmd(regionBean
                                , regionBean.isSmartTariffTracking() && regionBean.isGridToBattery()
                                , "");
                    }
                }

            }
        });

        mBinding.indexView.setTextSize(DensityUtil.sp2px(getContext(), 10));
        mBinding.indexView.setOnTouchIndexViewCallback(new IndexView.OnTouchIndexViewCallback() {
            @Override
            public void onTouchIndex(int pos, String text) {
                if (pos == lastScrollIndexPos) {
                    Log.w(TAG, "onTouchIndex: ");
                    return;
                }
                ((LinearLayoutManager) mBinding.rvRegionSupport.getLayoutManager()).scrollToPositionWithOffset(indexPosMap.get(text), 0);
                lastScrollIndexPos = pos;
            }

            @Override
            public void onCancelTouchIndex() {

            }
        });
    }

    private void initElectricityAreaData() {
        electricityAreaBeans.clear();
        if (mSelRegion.getDeliveryAreas() != null) {
            for (String area : mSelRegion.getDeliveryAreas()) {
                electricityAreaBeans.add(new PsElectricityAreaBean(area, R.drawable.icon_electricity_areas));
            }
            electricityAreaAdapter.setData(electricityAreaBeans);
            electricityAreaAdapter.notifyDataSetChanged();
        }
    }

    private void initElectricityAreaRv() {

        mBinding.rvElectricityAreaList.setLayoutManager(new LinearLayoutManager(getContext()));
        electricityAreaAdapter = new PsElectricityAreaAdapter();
        electricityAreaAdapter.setData(electricityAreaBeans);
        mBinding.rvElectricityAreaList.setAdapter(electricityAreaAdapter);
        listUpPx = mBinding.clElectricityArea.getTranslationY();

        electricityAreaAdapter.setOnItemClickListener(new PsElectricityAreaAdapter.OnItemClick() {
            @Override
            public void onItemClick(PsElectricityAreaBean psElectricityAreaBean, int position) {
                DDLog.d(TAG, "onItemClick: " + psElectricityAreaBean.getSimpleName());
                submitSetRegionCmd(mSelRegion, mSelRegion.isSmartTariffTracking() && mSelRegion.isGridToBattery(), psElectricityAreaBean.getSimpleName());
            }
        });

        mBinding.imgBtnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toDisappear();
            }
        });
    }

    public void toAppear() {
        DDLog.d(TAG, "toAppear.");
        ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.rlElectricityArea, "translationY"
                , ScreenUtils.getScreenHeight(getDelegateActivity()), 0);
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {
                mBinding.clDesc.setVisibility(View.GONE);
                mBinding.clElectricityArea.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        animator.setDuration(300);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.start();
    }

    private void toDisappear() {
        DDLog.d(TAG, "toDisappear.");
        ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.rlElectricityArea, "translationY"
                , 0, ScreenUtils.getScreenHeight(getDelegateActivity()));
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                mBinding.clElectricityArea.setVisibility(View.GONE);
                controlClDesc();
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        animator.setDuration(300);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.start();
    }

    /**
     * 发送cmd
     *
     * @param cmd
     */
    private void submitCmd(String cmd, String city) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            showLoadingFragment(0);
            mPSDevice.submit(params);
        }
    }

    /**
     * 同步时区到BMT-MSCT
     * 不需要等待结果的，仅发送请求
     * (设置地区改为这里实现, 把数据传个bmt, bmt再设置 需要等待结果)
     *
     * @param psRegionBean
     */
    private void submitSetRegionCmd(final PSRegionBean psRegionBean, boolean isPTSupported, String area) {
        final String timezone = psRegionBean.getTimezone();
        if (mPSDevice != null && !TextUtils.isEmpty(timezone)) {
            final Map<String, Object> param = new HashMap<>();
            param.put(BmtDataKey.CMD, BmtCmd.SET_REGION);
            param.put(BmtDataKey.IS_PRICE_TRACKING_SUPPORTED, isPTSupported);
            param.put(BmtDataKey.COUNTRY_CODE, psRegionBean.getCountryCode());
            param.put(BmtDataKey.COUNTRY_NAME, psRegionBean.getCountryName());
            param.put(BmtDataKey.CITY_NAME, "");
            param.put(BmtDataKey.TIMEZONE, psRegionBean.getTimezone());
            param.put(BmtDataKey.DELIVERY_AREAS, area);
            showTimeOutLoadinFramgmentWithErrorAlert();
            mPSDevice.submit(param);
        }

        if (!psRegionBean.isGridToBattery()) {
            setScheduledMode();
        }
    }

    /**
     * 设置为定时模式 (Scheduled Mode)
     */
    private void setScheduledMode() {
        if (mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_RESERVE_MODE);
            params.put(BmtDataKey.RESERVE_MODE, 2);
            params.put(BmtDataKey.SMART, 70);
            params.put(BmtDataKey.EMERGENCY, 30);

            int[] weekdaysArr = new int[24];
            for (int i = 0; i < 7; i++) {
                weekdaysArr[i] = 0;
            }
            for (int i = 7; i < 10; i++) {
                weekdaysArr[i] = -30;
            }
            for (int i = 10; i < 17; i++) {
                weekdaysArr[i] = 0;
            }
            for (int i = 17; i < 19; i++) {
                weekdaysArr[i] = -70;
            }
            for (int i = 19; i < 23; i++) {
                weekdaysArr[i] = -30;
            }
            weekdaysArr[23] = 0;
            params.put(BmtDataKey.WEEKDAYS, weekdaysArr);


            int[] weekendArr = new int[24];
            weekendArr[0] = -30;
            for (int i = 1; i < 9; i++) {
                weekendArr[i] = 0;
            }
            for (int i = 9; i < 12; i++) {
                weekendArr[i] = -30;
            }
            for (int i = 12; i < 19; i++) {
                weekendArr[i] = 0;
            }
            for (int i = 19; i < 21; i++) {
                weekendArr[i] = -70;
            }
            for (int i = 21; i < 24; i++) {
                weekendArr[i] = -30;
            }
            params.put(BmtDataKey.WEEKEND, weekendArr);
            params.put(BmtDataKey.SYNC, false);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    private void submitUpdateRegionCmd(String cmd, PSRegionBean psRegionBean) {
//        submitSetRegionCmd(psRegionBean);
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            params.put(PSKeyConstant.COUNTRY_CODE, psRegionBean.getCountryCode());
            params.put(PSKeyConstant.COUNTRY_NAME, psRegionBean.getCountryName());
            params.put(PSKeyConstant.CITY_NAME, "");
            params.put(PSKeyConstant.TIMEZONE, psRegionBean.getTimezone());
            showTimeOutLoadinFramgmentWithErrorAlert();
            mPSDevice.submit(params);
        }
    }

    /**
     * 不支持弹窗
     *
     * @param msg
     */
    private void showNonsupportDialog(String msg, PSRegionBean regionBean) {
        AlertDialog builder = AlertDialog.createBuilder(getContext())
                .setAutoDissmiss(true)
                .setContent(msg)
                .setOk(getResources().getString(R.string.ps_region_regions_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
//                        submitUpdateRegionCmd(DsCamCmd.UPDATE_REGION, regionBean);
                        submitSetRegionCmd(regionBean, false, "");
                    }
                })
                .setCancel(getResources().getString(R.string.ps_region_regions_cancel))
                .setCancelListener(new AlertDialog.AlertCancelClickCallback() {
                    @Override
                    public void onClick() {

                    }
                })
                .preBuilder();
        builder.show();
    }

    /**
     * 添加设备流程设置地区成功
     */
    private void setRegionFromAdd() {
        DinsafeAPI.getApi().getBmtIAPExpirationData(mDeviceId).enqueue(new Callback<GetDeviceExpirationDateResponseV2>() {
            @Override
            public void onResponse(Call<GetDeviceExpirationDateResponseV2> call, Response<GetDeviceExpirationDateResponseV2> response) {
                if (response.body() != null && response.body().getResult() != null) {
                    long expirationData = response.body().getResult().getExpirationDate();
                    int status = response.body().getResult().getStatus();
                    getDelegateActivity().addCommonFragment(PSWelcomeFragment.newInstance(mDeviceId, mSubcategory, expirationData, status));
                } else {
                    getDelegateActivity().addCommonFragment(PSWelcomeFragment.newInstance(mDeviceId, mSubcategory));
                }
            }

            @Override
            public void onFailure(Call<GetDeviceExpirationDateResponseV2> call, Throwable t) {
                getDelegateActivity().addCommonFragment(PSWelcomeFragment.newInstance(mDeviceId, mSubcategory));
            }
        });
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        DDLog.d(TAG, "onCmdCallBack: " + deviceId + " cmd: " + cmd + " map: " + map);
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            if (StringUtil.isNotEmpty(cmd) && cmd.equals(DsCamCmd.UPDATE_REGION)) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (status == 1) {
                    if (PARAM_FROM_STEP_ADD == mFrom) {
                        setRegionFromAdd();
                    } else {
                        EventBus.getDefault().post(new PSRegionEvent(mSelRegion));
                        removeSelf();
                    }
                } else {
                    showErrorToast();
                }
            }

            if (StringUtil.isNotEmpty(cmd) && cmd.equals(BmtCmd.SET_REGION)) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (status == 1) {
                            if (PARAM_FROM_STEP_ADD == mFrom) {
//                                getDelegateActivity().addCommonFragment(PSWelcomeFragment.newInstance(deviceId));
                                setRegionFromAdd();
                            } else {
                                EventBus.getDefault().post(new PSRegionEvent(mSelRegion));
                                removeSelf();
                            }
                        } else {
                            showErrorToast();
                        }
                    }
                });
            }

            if (StringUtil.isNotEmpty(cmd) && cmd.equals(DsCamCmd.GET_REGION_COUNTRIES)) {
                closeLoadingFragment();
            }

            if (status == StatusConstant.STATUS_SUCCESS) {
                runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        if (result != null && result.size() > 0) {
                            switch (cmd) {
                                case DsCamCmd.GET_REGION_COUNTRIES:
                                    List<Map<String, Object>> countries = (List<Map<String, Object>>) MapUtils.get(result, PSKeyConstant.LIST, null);
                                    if (countries != null) {
                                        for (Map<String, Object> countryBean : countries) {
                                            mRegions.add(new PSRegionBean((String) MapUtils.get(countryBean, PSKeyConstant.COUNTRY_NAME, null)
                                                    , (String) MapUtils.get(countryBean, PSKeyConstant.COUNTRY_CODE, null)
                                                    , (String) MapUtils.get(countryBean, PSKeyConstant.COUNTRY_NAME_DISPLAY, null)
                                                    , (String) MapUtils.get(countryBean, PSKeyConstant.TIMEZONE, null)
                                                    , (boolean) MapUtils.get(countryBean, BmtDataKey.GRID_CONN_SUPPORT, false)
                                                    , (boolean) MapUtils.get(countryBean, BmtDataKey.ELEC_SUPPORT, false)
                                                    , (boolean) MapUtils.get(countryBean, BmtDataKey.GRID_TO_BATTERY, false)
                                                    , (List<String>) MapUtils.get(countryBean, BmtDataKey.DELIVERY_AREAS, null)));

                                        }
                                        Collections.sort(mRegions, mCityComparator);
                                        for (int i = 0; i < mRegions.size(); i++) {
                                            String city = mRegions.get(i).getCountryName();
                                            String index = String.valueOf(city.charAt(0));
                                            if (!indexs.contains(index)) {
                                                indexs.add(index);
                                            }
                                            if (!indexPosMap.containsKey(index)) {
                                                indexPosMap.put(index, i);
                                            }
                                        }
                                        mPsRegionAdapter.setNewData(mRegions);
                                        mBinding.indexView.setData(indexs);
                                    }
                                    mBinding.llParent.setVisibility(View.VISIBLE);
                                    break;

                            }
                        }
                    }
                });

            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(FinishEvent event) {
        removeSelf();
    }
}
