package com.dinsafer.module.powerstation.bean;



public class ChartNoteTextAttrBean {
    private String noteText;
    private int noteTextColor;
    private float noteTextSize;
    private float paddingLeft;
    private float paddingTop;
    private float paddingRight;
    private float paddingBottom;
    private Gravity gravity;

    public ChartNoteTextAttrBean() {
        gravity = Gravity.LEFT_TOP;
    }

    public ChartNoteTextAttrBean(String noteText, int noteTextColor, float noteTextSize) {
        this.noteText = noteText;
        this.noteTextColor = noteTextColor;
        this.noteTextSize = noteTextSize;
        gravity = Gravity.LEFT_TOP;
    }

    public String getNoteText() {
        return noteText;
    }

    public void setNoteText(String noteText) {
        this.noteText = noteText;
    }

    public int getNoteTextColor() {
        return noteTextColor;
    }

    public void setNoteTextColor(int noteTextColor) {
        this.noteTextColor = noteTextColor;
    }

    public float getNoteTextSize() {
        return noteTextSize;
    }

    public void setNoteTextSize(float noteTextSize) {
        this.noteTextSize = noteTextSize;
    }

    public float getPaddingLeft() {
        return paddingLeft;
    }

    public void setPaddingLeft(float paddingLeft) {
        this.paddingLeft = paddingLeft;
    }

    public float getPaddingTop() {
        return paddingTop;
    }

    public void setPaddingTop(float paddingTop) {
        this.paddingTop = paddingTop;
    }

    public float getPaddingRight() {
        return paddingRight;
    }

    public void setPaddingRight(float paddingRight) {
        this.paddingRight = paddingRight;
    }

    public float getPaddingBottom() {
        return paddingBottom;
    }

    public void setPaddingBottom(float paddingBottom) {
        this.paddingBottom = paddingBottom;
    }

    public Gravity getGravity() {
        return gravity;
    }

    public void setPadding(float padding) {
        paddingLeft = padding;
        paddingTop = padding;
        paddingRight = padding;
        paddingBottom = padding;
    }

    public void setGravity(Gravity gravity) {
        this.gravity = gravity;
    }

    public enum Gravity {
        LEFT_TOP,
        RIGHT_TOP,
        LEFT_BOTTOM,
        RIGHT_BOTTOM,
        CENTER,
        TOP_CENTER_HORIZONTAL,
        BOTTOM_CENTER_HORIZONTAL,
        LEFT_CENTER_VERTICAL,
        RIGHT_CENTER_VERTICAL
    }
}
