package com.dinsafer.module.powerstation.utils.download;

public class DownloadAndZipResultEvent {

    private boolean success;
    private String path;

    public DownloadAndZipResultEvent(boolean success) {
        this.success = success;
    }

    public DownloadAndZipResultEvent(boolean success, String path) {
        this.success = success;
        this.path = path;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
