package com.dinsafer.module.powerstation.guide;

import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsGuideBinding;
import com.dinsafer.model.event.CheckFamilyBalanceContractInfoEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class PSGuideFragment extends MyBaseFragment<FragmentPsGuideBinding> {

    public static final int HOME_GUIDE = 0;
    public static final int DEVICE_GUIDE = 1;
    private int mType;

    private static final int COUNT = 4;
    private int mIndex = 0;
    private List<View> mViewList;

    public static PSGuideFragment newInstance(int type) {
        PSGuideFragment fragment = new PSGuideFragment();
        Bundle bundle = new Bundle();
        bundle.putInt("type", type);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_guide;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mType = getArguments().getInt("type");
        mViewList = new ArrayList<>();
        if (mType == HOME_GUIDE) {
            View homeGuide1 = View.inflate(getContext(), R.layout.fragment_home_guide_1, null);
            View homeGuide2 = View.inflate(getContext(), R.layout.fragment_home_guide_2, null);
            View homeGuide3 = View.inflate(getContext(), R.layout.fragment_home_guide_3, null);
            View homeGuide4 = View.inflate(getContext(), R.layout.fragment_home_guide_4, null);
            mViewList.add(homeGuide1);
            mViewList.add(homeGuide2);
            mViewList.add(homeGuide3);
            mViewList.add(homeGuide4);
        } else if (mType == DEVICE_GUIDE){
            View deviceGuide1 = View.inflate(getContext(), R.layout.fragment_device_guide_1, null);
            View deviceGuide2 = View.inflate(getContext(), R.layout.fragment_device_guide_2, null);
            View deviceGuide3 = View.inflate(getContext(), R.layout.fragment_device_guide_3, null);
            View deviceGuide4 = View.inflate(getContext(), R.layout.fragment_device_guide_4, null);
            mViewList.add(deviceGuide1);
            mViewList.add(deviceGuide2);
            mViewList.add(deviceGuide3);
            mViewList.add(deviceGuide4);
        }
       if (CollectionUtil.isListNotEmpty(mViewList)) {
           for (View view : mViewList) {
               LocalTextView tvNext = view.findViewById(R.id.tv_next);
               if (tvNext != null) {
                   tvNext.setOnClickListener(view1 -> click());
               }
           }
           setGuidePage();
       }

    }

    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        return null;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.ivClose.setOnClickListener(view -> {
            removeSelf();
            EventBus.getDefault().post(new CheckFamilyBalanceContractInfoEvent());
        });
        mBinding.clParent.setOnClickListener(view -> click());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(NextGuideEvent event) {
        click();
    }

    private void click() {
        if (mIndex < COUNT) {
            setGuidePage();
        } else {
            removeSelf();
            EventBus.getDefault().post(new CheckFamilyBalanceContractInfoEvent());
        }
    }

    private void setGuidePage() {
        if (CollectionUtil.isListEmpty(mViewList)) return;
        mBinding.flGuide.removeAllViews();
        mBinding.flGuide.addView(mViewList.get(mIndex));
        mIndex++;
    }
}
