package com.dinsafer.module.powerstation.guide.device_guide;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDeviceGuide4Binding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.guide.NextGuideEvent;

import org.greenrobot.eventbus.EventBus;

public class DeviceGuide4Fragment extends MyBaseFragment<FragmentDeviceGuide4Binding> {

    public static DeviceGuide4Fragment newInstance() {
        return new DeviceGuide4Fragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_device_guide_4;
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvNext.setOnClickListener(view -> EventBus.getDefault().post(new NextGuideEvent()));
    }
}
