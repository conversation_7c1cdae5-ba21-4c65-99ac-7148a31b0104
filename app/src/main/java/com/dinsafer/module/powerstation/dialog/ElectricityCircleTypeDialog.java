package com.dinsafer.module.powerstation.dialog;

import androidx.recyclerview.widget.LinearLayoutManager;

import android.view.View;
import android.view.WindowManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogElectricityCircleTypeBinding;
import com.dinsafer.module.powerstation.adapter.ElectricityCircleTypeAdapter;
import com.dinsafer.module.powerstation.electricity.bean.ElectricityCircleTypeBean;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.module.powerstation.electricity.controller.PlusMinusType;
import com.dinsafer.permission.BaseBottomSheetDialog;
import com.dinsafer.util.DDLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 用电统计周期类型
 */
public class ElectricityCircleTypeDialog extends BaseBottomSheetDialog<DialogElectricityCircleTypeBinding> {

    public static final String TAG = ElectricityCircleTypeDialog.class.getSimpleName();
    public static final String CHARGE_TAG = ElectricityCircleTypeDialog.class.getSimpleName() + "_Charge";
    private ElectricityCircleTypeAdapter mElectricityCircleTypeAdapter;
    private List<ElectricityCircleTypeBean> mData = new ArrayList<>();
    // 0. day、week...的类别
    // 1. 电池充放电类别
    // 2. 市电输入输出电类别
    // 3. 影响和策略的eco/Revenue
    private int type;
    public static final int CIRCLE_TYPE = 0;
    public static final int BATTER_CHARGE_TYPE = 1;
    public static final int GRID_TYPE = 2;
    public static final int IMPACTS_STRATEGIES_ECO_TYPE = 3;
    public static final int IMPACTS_STRATEGIES_REVENUE_TYPE = 4;

    public ElectricityCircleTypeDialog() {
    }

    public ElectricityCircleTypeDialog(int type) {
        this.type = type;
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_electricity_circle_type;
    }

    @Override
    protected void initView() {
        super.initView();
        setEnableDrag(false);
        initRv();
        mBinding.tvCancel.setOnClickListener(v -> dismiss());
        mBinding.tvConfirm.setOnClickListener(v -> {
            switch (type) {
                case CIRCLE_TYPE:
                case IMPACTS_STRATEGIES_ECO_TYPE:
                case IMPACTS_STRATEGIES_REVENUE_TYPE:
                    if (typeSelectedListener != null) {
                        ElectricityCircleTypeBean typeBean = mElectricityCircleTypeAdapter.getSelectedItem();
                        typeSelectedListener.onSelected(typeBean.getType(), typeBean.getCycleType());
                    }
                    break;

                case BATTER_CHARGE_TYPE:
                case GRID_TYPE:
                    if (plusMinusTypeListener != null) {
                        ElectricityCircleTypeBean typeBean = mElectricityCircleTypeAdapter.getSelectedItem();
                        plusMinusTypeListener.plusMinus(typeBean.getType(), typeBean.getPlusMinusType());
                    }
                    break;
            }

            dismiss();
        });
    }

    @Override
    protected int provideDialogHeight() {
        return WindowManager.LayoutParams.WRAP_CONTENT;
    }

    private void initRv() {
        mBinding.rvType.setLayoutManager(new LinearLayoutManager(getContext()));
        if (mData.size() == 0) {
            switch (type) {
                case CIRCLE_TYPE:
                    List<String> strType = Arrays.asList(getContext().getResources().getStringArray(R.array.electricity_circle_type_array));
                    List<CycleType> typeList = CycleType.getTypeList();
                    if (strType.size() != typeList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < strType.size(); i++) {
                        String type = strType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, typeList.get(i)));
                    }
                    break;

                case BATTER_CHARGE_TYPE:
                    List<String> chargeType = Arrays.asList(getContext().getResources().getStringArray(R.array.electricity_charge_type_array));
                    List<PlusMinusType> chargePlusMinusList = PlusMinusType.getPlusMinusTypeList();
                    if (chargeType.size() != chargePlusMinusList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < chargeType.size(); i++) {
                        String type = chargeType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, chargePlusMinusList.get(i)));
                    }
                    break;

                case GRID_TYPE:
                    List<String> gridType = Arrays.asList(getContext().getResources().getStringArray(R.array.electricity_grid_type_array));
                    List<PlusMinusType> gridPlusMinusList = PlusMinusType.getPlusMinusTypeList();
                    if (gridType.size() != gridPlusMinusList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < gridType.size(); i++) {
                        String type = gridType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, gridPlusMinusList.get(i)));
                    }
                    break;

                case IMPACTS_STRATEGIES_ECO_TYPE:
                    List<String> ecoType = Arrays.asList(getContext().getResources().getStringArray(R.array.electricity_circle_type_array));
                    List<CycleType> ecoTypeList = CycleType.getTypeList();
                    if (ecoType.size() != ecoTypeList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < ecoTypeList.size(); i++) {
                        String type = ecoType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, ecoTypeList.get(i)));
                    }
                    break;

                case IMPACTS_STRATEGIES_REVENUE_TYPE:
                    List<String> isType = Arrays.asList(getContext().getResources().getStringArray(R.array.impacts_strategies_type_array));
                    List<CycleType> isTypeList = CycleType.getTypeListExceptDay();
                    if (isType.size() != isTypeList.size()) {
                        DDLog.e(TAG, "显示isType和isTypeList长度不一致");
                    }
                    for (int i = 0; i < isTypeList.size(); i++) {
                        String type = isType.get(i);
                        mData.add(new ElectricityCircleTypeBean(type, isTypeList.get(i)));
                    }
                    break;
            }
            mData.get(type == IMPACTS_STRATEGIES_ECO_TYPE ? 1 : 0).setSelected(true);
        }
        mElectricityCircleTypeAdapter = new ElectricityCircleTypeAdapter(R.layout.item_electricity_circle_type, mData);
        mBinding.rvType.setAdapter(mElectricityCircleTypeAdapter);
        mElectricityCircleTypeAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                for (ElectricityCircleTypeBean etb : mData) {
                    etb.setSelected(false);
                }
                ElectricityCircleTypeBean electricityCircleTypeBean = mElectricityCircleTypeAdapter.getItem(position);
                electricityCircleTypeBean.setSelected(true);
                mElectricityCircleTypeAdapter.notifyDataSetChanged();

            }
        });
    }

    private OnTypeSelectedListener typeSelectedListener;

    public void setTypeSelectedListener(OnTypeSelectedListener typeSelectedListener) {
        this.typeSelectedListener = typeSelectedListener;
    }

    public interface OnTypeSelectedListener {
        void onSelected(String type, CycleType cycleType);
    }

    private OnPlusMinusTypeListener plusMinusTypeListener;

    public void setPlusMinusTypeListener(OnPlusMinusTypeListener plusMinusTypeListener) {
        this.plusMinusTypeListener = plusMinusTypeListener;
    }

    public interface OnPlusMinusTypeListener {
        void plusMinus(String type, PlusMinusType plusMinusType);
    }
}
