package com.dinsafer.module.powerstation.adapter;


import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ReserveModeBean;
import com.dinsafer.ui.LocalTextView;


/**
 * <AUTHOR> WZH
 * @date : 2022/11/25 14:04
 * @description :
 */
public class ReserveModePriceAdapter extends BaseQuickAdapter<ReserveModeBean.PriceBean, BaseViewHolder> {

    public ReserveModePriceAdapter() {
        super(R.layout.item_reserve_mode_price);
    }

    @Override
    protected void convert(BaseViewHolder helper, ReserveModeBean.PriceBean item) {
        helper.addOnClickListener(R.id.tv_edit);
        LocalTextView tvDesc = helper.getView(R.id.tv_desc);
        tvDesc.setText(item.getContent());
        LocalTextView tvEdit = helper.getView(R.id.tv_edit);
        tvEdit.setVisibility(item.isShowEdit() ? View.VISIBLE : View.GONE);
    }
}
