package com.dinsafer.module.powerstation.impacts.aischeduledchart.renderer;

import android.graphics.Canvas;
import android.view.View;

import com.dinsafer.module.powerstation.electricity.chart.render.CustomBarChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomBubbleChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomCandleStickChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomScatterChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.SectionLineChartRender;
import com.dinsafer.module.powerstation.impacts.aischeduledchart.AIScheduledCombinedChart;
import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.charts.Chart;
import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.data.ChartData;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.dataprovider.BarLineScatterCandleBubbleDataProvider;
import com.github.mikephil.charting.renderer.BubbleChartRenderer;
import com.github.mikephil.charting.renderer.CandleStickChartRenderer;
import com.github.mikephil.charting.renderer.DataRenderer;
import com.github.mikephil.charting.renderer.LineChartRenderer;
import com.github.mikephil.charting.renderer.ScatterChartRenderer;
import com.github.mikephil.charting.utils.ViewPortHandler;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

public class AIScheduledCombinedRenderer  extends DataRenderer {

    protected List<DataRenderer> mRenderers;
    protected WeakReference<Chart> mChart;
    protected  AIScheduledBarChartRenderer mBarChartRenderer;

    public AIScheduledCombinedRenderer(AIScheduledCombinedChart chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {
        super(animator, viewPortHandler);
        mChart = new WeakReference<Chart>(chart);
        createRenderers(chart, animator, viewPortHandler);
    }

    /**
     * Creates the renderers needed for this combined-renderer in the required order. Also takes the DrawOrder into
     * consideration.
     *
     * @param chart
     * @param animator
     * @param viewPortHandler
     */
    protected void createRenderers(AIScheduledCombinedChart chart, ChartAnimator animator, ViewPortHandler viewPortHandler) {

        mRenderers = new ArrayList<DataRenderer>();

        AIScheduledCombinedChart.DrawOrder[] orders = chart.getDrawOrder();

        for (AIScheduledCombinedChart.DrawOrder order : orders) {

            switch (order) {
                case BAR:
                    if (chart.getBarData() != null) {
                        mBarChartRenderer = new AIScheduledBarChartRenderer(chart.getContext(), chart, animator, viewPortHandler);
                        mRenderers.add(mBarChartRenderer);
                    }
                    break;
                case BUBBLE:
                    if (chart.getBubbleData() != null)
                        mRenderers.add(new BubbleChartRenderer(chart, animator, viewPortHandler));
                    break;
                case LINE:
                    if (chart.getLineData() != null)
                        mRenderers.add(new SectionLineChartRender(chart, animator, viewPortHandler));
                    break;
                case CANDLE:
                    if (chart.getCandleData() != null)
                        mRenderers.add(new CandleStickChartRenderer(chart, animator, viewPortHandler));
                    break;
                case SCATTER:
                    if (chart.getScatterData() != null)
                        mRenderers.add(new ScatterChartRenderer(chart, animator, viewPortHandler));
                    break;
            }
        }
    }

    @Override
    public void initBuffers() {
        for (DataRenderer renderer : mRenderers)
            renderer.initBuffers();
    }

    @Override
    public void drawData(Canvas c) {
        for (DataRenderer renderer : mRenderers)
            renderer.drawData(c);
    }

    @Override
    public void drawValues(Canvas c) {
        for (DataRenderer renderer : mRenderers)
            renderer.drawValues(c);
    }

    @Override
    public void drawExtras(Canvas c) {
        for (DataRenderer renderer : mRenderers)
            renderer.drawExtras(c);
    }

    @Override
    public void drawHighlighted(Canvas c, Highlight[] indices) {

    }

    @Override
    public void calcXBounds(BarLineScatterCandleBubbleDataProvider chart, int xAxisModulus) {
        for (DataRenderer renderer : mRenderers)
            renderer.calcXBounds(chart, xAxisModulus);
    }

    public void setEmojiView(View view) {
        if (mBarChartRenderer !=  null) {
            mBarChartRenderer.setEmojiView(view);
        }
    }

    public void setHighLightPosition(int nowPosition, int clickPosition) {
        if (mBarChartRenderer !=  null) {
            mBarChartRenderer.setHighLightPosition(nowPosition, clickPosition);
        }
    }
}
