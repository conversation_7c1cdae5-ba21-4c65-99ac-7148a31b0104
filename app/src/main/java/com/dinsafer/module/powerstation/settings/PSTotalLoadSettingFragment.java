package com.dinsafer.module.powerstation.settings;

import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsTotalLoadSettingBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PowerStationRoot2Fragment;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PSTotalSettingModel;
import com.dinsafer.module.powerstation.adapter.PSTotalSizeModel;
import com.dinsafer.module.powerstation.bean.TotalLoadSize;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PSTotalLoadSettingFragment extends PSConnectLoadingFragment<FragmentPsTotalLoadSettingBinding> implements IDeviceCallBack {

    private BindMultiAdapter<PSTotalSettingModel> mRatingsAdapter;
    private List<PSTotalSettingModel> mRatings = new ArrayList<>();

    private BindMultiAdapter<PSTotalSizeModel> mSizeAdapter;
    private List<PSTotalSizeModel> mCurSize = new ArrayList<>();
    private List<PSTotalSizeModel> mHigherSize = new ArrayList<>();
    private List<PSTotalSizeModel> mMediumSize = new ArrayList<>();
    private List<PSTotalSizeModel> mLowerSize = new ArrayList<>();


    public static PSTotalLoadSettingFragment newInstanceFromAddPS(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_STEP_ADD, deviceId, subcategory);
    }

    public static PSTotalLoadSettingFragment newInstanceFromSetting(String deviceId, String subcategory) {
        return newInstance(PARAM_FROM_SETTING, deviceId, subcategory);
    }

    public static PSTotalLoadSettingFragment newInstance(int from, String deviceId, String subcategory) {
        PSTotalLoadSettingFragment fragment = new PSTotalLoadSettingFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_total_load_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        initTitle();
        setNextEnabled(false);
        setNextVisible(mFrom == PARAM_FROM_STEP_ADD);
        mBinding.lcbNext.setLocalText(BmtUtil.isBmtDevicePowerStore(mPSDevice) ?
                getString(R.string.done) : getString(R.string.next));
        initRatings();
        initSizes();
    }

    private void initRatings() {
        mRatingsAdapter = new BindMultiAdapter<>();
        mRatingsAdapter.setOnBindItemClickListener((OnBindItemClickListener<PSTotalSettingModel>) (v, position, model) -> {
            for (PSTotalSettingModel totalSettingModel : mRatings) {
                totalSettingModel.setSelected(false);
            }
            model.setSelected(true);
            refreshSizeList(model.getRatings());
            mRatingsAdapter.notifyDataSetChanged();
        });
        mBinding.rvRatings.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvRatings.setAdapter(mRatingsAdapter);
        mRatingsAdapter.setNewData(mRatings);
    }

    private void initSizes() {
        mSizeAdapter = new BindMultiAdapter<>();
        mSizeAdapter.setOnBindItemClickListener((OnBindItemClickListener<PSTotalSizeModel>) (v, position, model) -> {
            for (PSTotalSizeModel sizeModel : mLowerSize) {
                sizeModel.setSelected(false);
            }
            for (PSTotalSizeModel sizeModel : mMediumSize) {
                sizeModel.setSelected(false);
            }
            for (PSTotalSizeModel sizeModel : mHigherSize) {
                sizeModel.setSelected(false);
            }
            model.setSelected(true);
            mSizeAdapter.notifyDataSetChanged();
            setNextEnabled(true);
        });
        mBinding.rcvSize.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rcvSize.setAdapter(mSizeAdapter);
        mSizeAdapter.setNewData(mCurSize);
    }

    private void refreshSizeList(String rating) {
        mCurSize.clear();
        switch (rating) {
            case BmtDataKey.FUSE_POWER_HIGHER:
                mCurSize.addAll(mHigherSize);
                break;

            case BmtDataKey.FUSE_POWER_MEDIUM:
                mCurSize.addAll(mMediumSize);
                break;

            case BmtDataKey.FUSE_POWER_LOWER:
            default:
                mCurSize.addAll(mLowerSize);
                break;

        }
        mSizeAdapter.setNewData(mCurSize);
        mSizeAdapter.notifyDataSetChanged();
    }

    @Override
    public void initData() {
        super.initData();
        if (mPSDevice != null) {
            Map<String, String> map = new HashMap<>();
            map.put(BmtDataKey.CMD, BmtCmd.GET_FUSE_POWER_CAPS);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(map);
        } else {
            showErrorToast();
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        View.OnClickListener clickListener = view -> setFuseSpecs();
        mBinding.lcbNext.setOnClickListener(clickListener);
        mBinding.commonBar.commonBarRightText.setOnClickListener(clickListener);
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }


    private void initTitle() {
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.total_load_settings));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        if (mFrom == PARAM_FROM_SETTING) {
            RelativeLayout.LayoutParams rlParams = (RelativeLayout.LayoutParams) mBinding.commonBar.commonBarRightText.getLayoutParams();
            rlParams.rightMargin = DensityUtils.dp2px(getContext(), 8);
            mBinding.commonBar.commonBarRightText.setLayoutParams(rlParams);
            mBinding.commonBar.commonBarRightText.setMinWidth(0);
            mBinding.commonBar.commonBarRightText.setVisibility(View.VISIBLE);
            mBinding.commonBar.commonBarRightText.setLocalText(getString(R.string.save));
            mBinding.commonBar.commonBarRightText.setTextColor(getResources().getColor(R.color.color_brand_text));
            mBinding.commonBar.commonBarRightText.setBackgroundResource(0);

            mBinding.commonBar.commonBarRightText.setPadding(DensityUtils.dp2px(getContext(), 10),
                    DensityUtils.dp2px(getContext(), 4), 10, DensityUtils.dp2px(getContext(), 4));
        }
    }

    private void setNextVisible(boolean visible) {
        mBinding.lcbNext.setVisibility(visible ? View.VISIBLE : View.GONE);
        mBinding.viewSpace.setVisibility(visible ? View.VISIBLE : View.GONE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(outMetrics);

        mBinding.tvDesc.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                mBinding.tvDesc.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int topHeight = mBinding.commonBar.rlParent.getHeight() + mBinding.tvDesc.getHeight();
                int screenHeight = outMetrics.heightPixels - topHeight;
                // Top: common_bar + tv_desc
                // Bottom: 按钮高度 48 + 50(layout_marginBottom) + 10(view_space) = 108
                if (!visible) {
                    ViewGroup.LayoutParams params = mBinding.lyData.getLayoutParams();
                    params.height = screenHeight;
                    mBinding.lyData.setLayoutParams(params);
                } else {
                    ViewGroup.LayoutParams params = mBinding.lyData.getLayoutParams();
                    params.height = screenHeight - DensityUtils.dp2px(getContext(), 108);
                    mBinding.lyData.setLayoutParams(params);
                }
            }
        });
    }

    /**
     * 设置下一步按钮是否可用
     *
     * @param enabled
     */
    private void setNextEnabled(boolean enabled) {
        mBinding.lcbNext.setEnabled(enabled);
        mBinding.lcbNext.setAlpha(enabled ? 1f : 0.5f);
    }

    /**
     * 设置主保险丝规格
     */
    private void setFuseSpecs() {
        if (mPSDevice != null && getSelectedModel() != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(BmtDataKey.CMD, BmtCmd.SET_FUSE_SPECS);
            params.put(BmtDataKey.SPEC, getSelectedModel().getTotalLoadSize().getSpec());
            params.put(BmtDataKey.POWER_CAP, getSelectedModel().getTotalLoadSize().getPowerCap());
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        } else {
            showErrorToast();
        }
    }

    /**
     * 选中的model
     *
     * @return
     */
    private PSTotalSizeModel getSelectedModel() {
        if (mLowerSize != null) {
            for (PSTotalSizeModel totalSizeModel : mLowerSize) {
                if (totalSizeModel.isSelected()) {
                    return totalSizeModel;
                }
            }
        }

        if (mMediumSize != null) {
            for (PSTotalSizeModel totalSizeModel : mMediumSize) {
                if (totalSizeModel.isSelected()) {
                    return totalSizeModel;
                }
            }
        }

        if (mHigherSize != null) {
            for (PSTotalSizeModel totalSizeModel : mHigherSize) {
                if (totalSizeModel.isSelected()) {
                    return totalSizeModel;
                }
            }
        }
        return null;
    }

    /**
     * 获取主保险丝规格(0xa039)
     */
    private void getFuseSpecs() {
        if (mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(BmtDataKey.CMD, BmtCmd.GET_FUSE_SPECS);
            mPSDevice.submit(params);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case BmtCmd.GET_FUSE_POWER_CAPS:
                                closeLoadingFragment();
                                mLowerSize.clear();
                                List<Map<String, Object>> fusePowerLower = DeviceHelper.getList(result, BmtDataKey.FUSE_POWER_LOWER);
                                if (fusePowerLower != null) {
                                    for (int i = 0; i < fusePowerLower.size(); i++) {
                                        Map<String, Object> fusePowerCap = fusePowerLower.get(i);
                                        double powerCap = DeviceHelper.getDouble(fusePowerCap, BmtDataKey.POWER_CAP, -1);
                                        String spec = DeviceHelper.getString(fusePowerCap, BmtDataKey.SPEC, "");
                                        if (powerCap < 0 || spec == null) {
                                            continue;
                                        }
                                        mLowerSize.add(new PSTotalSizeModel(getContext(), new TotalLoadSize((int) powerCap, spec)
                                                , false, !(i == fusePowerLower.size() - 1)));
                                    }
                                    Collections.sort(mLowerSize, new FuseComparator());
                                    mRatings.add(new PSTotalSettingModel(getContext(), getString(R.string.lower_ratings)));
                                }

                                mMediumSize.clear();
                                List<Map<String, Object>> fusePowerMedium = DeviceHelper.getList(result, BmtDataKey.FUSE_POWER_MEDIUM);
                                if (fusePowerMedium != null) {
                                    for (int i = 0; i < fusePowerMedium.size(); i++) {
                                        Map<String, Object> fusePowerCap = fusePowerMedium.get(i);
                                        double powerCap = DeviceHelper.getDouble(fusePowerCap, BmtDataKey.POWER_CAP, -1);
                                        String spec = DeviceHelper.getString(fusePowerCap, BmtDataKey.SPEC, "");
                                        if (powerCap < 0 || spec == null) {
                                            continue;
                                        }
                                        mMediumSize.add(new PSTotalSizeModel(getContext(), new TotalLoadSize((int) powerCap, spec)
                                                , false, !(i == fusePowerMedium.size() - 1)));
                                    }
                                    Collections.sort(mMediumSize, new FuseComparator());
                                    mRatings.add(new PSTotalSettingModel(getContext(), getString(R.string.medium_ratings)));
                                }

                                mHigherSize.clear();
                                List<Map<String, Object>> fusePowerHigher = DeviceHelper.getList(result, BmtDataKey.FUSE_POWER_HIGHER);
                                if (fusePowerHigher != null) {
                                    for (int i = 0; i < fusePowerHigher.size(); i++) {
                                        Map<String, Object> fusePowerCap = fusePowerHigher.get(i);
                                        double powerCap = DeviceHelper.getDouble(fusePowerCap, BmtDataKey.POWER_CAP, -1);
                                        String spec = DeviceHelper.getString(fusePowerCap, BmtDataKey.SPEC, "");
                                        if (powerCap < 0 || spec == null) {
                                            continue;
                                        }
                                        mHigherSize.add(new PSTotalSizeModel(getContext(), new TotalLoadSize((int) powerCap, spec)
                                                , false, !(i == fusePowerHigher.size() - 1)));
                                    }
                                    Collections.sort(mHigherSize, new FuseComparator());
                                    mRatings.add(new PSTotalSettingModel(getContext(), getString(R.string.higher_ratings)));
                                }

                                if (CollectionUtil.isListNotEmpty(mLowerSize)) {
                                    mRatings.get(0).setSelected(true);
                                    refreshSizeList(mRatings.get(0).getRatings());
                                }

                                mRatingsAdapter.notifyDataSetChanged();

                                if (mFrom == PARAM_FROM_SETTING) {
                                    getFuseSpecs();
                                }
                                break;

                            case BmtCmd.SET_FUSE_SPECS:
                                closeLoadingFragment();
                                if (mFrom == PARAM_FROM_STEP_ADD) {
                                    if (BmtUtil.isBmtDevicePowerStore(mPSDevice)) {
                                        getDelegateActivity().removeAllCommonFragment();
                                        getDelegateActivity().addCommonFragment(PowerStationRoot2Fragment.newInstanceForStepAddPS(
                                                DeviceHelper.getString(BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory), DinConst.INFO_NAME, "")
                                                , mDeviceId, mSubcategory));
                                    } else {
                                        getDelegateActivity().addCommonFragment(EnergySettingFragment.newInstanceFromAddPS(mDeviceId, mSubcategory));
                                    }
                                } else {
                                    getDelegateActivity().showTopToast(getString(R.string.success));
                                    removeSelf();
                                }
                                break;

                            case BmtCmd.GET_FUSE_SPECS:
                                if (result != null && result.size() > 0) {
                                    int powerCap = DeviceHelper.getInt(result, BmtDataKey.POWER_CAP, -1);
                                    String spec = DeviceHelper.getString(result, BmtDataKey.SPEC, "");
                                    boolean finish = false;
                                    if (CollectionUtil.isListNotEmpty(mLowerSize)) {
                                        for (PSTotalSizeModel totalSizeModel : mLowerSize) {
                                            if (totalSizeModel.getTotalLoadSize().getPowerCap() == powerCap
                                                    && spec.equals(totalSizeModel.getTotalLoadSize().getSpec())) {
                                                totalSizeModel.setSelected(true);
                                                mRatings.get(0).setSelected(true);
                                                refreshSizeList(mRatings.get(0).getRatings());
                                                finish = true;
                                                break;
                                            }
                                        }
                                    }

                                    if (!finish && CollectionUtil.isListNotEmpty(mMediumSize)) {
                                        for (PSTotalSizeModel totalSizeModel : mMediumSize) {
                                            if (totalSizeModel.getTotalLoadSize().getPowerCap() == powerCap
                                                    && spec.equals(totalSizeModel.getTotalLoadSize().getSpec())) {
                                                totalSizeModel.setSelected(true);
                                                mRatings.get(0).setSelected(false);
                                                mRatings.get(1).setSelected(true);
                                                refreshSizeList(mRatings.get(1).getRatings());
                                                finish = true;
                                                break;
                                            }
                                        }
                                    }

                                    if (!finish && CollectionUtil.isListNotEmpty(mHigherSize)) {
                                        for (PSTotalSizeModel totalSizeModel : mHigherSize) {
                                            if (totalSizeModel.getTotalLoadSize().getPowerCap() == powerCap
                                                    && spec.equals(totalSizeModel.getTotalLoadSize().getSpec())) {
                                                totalSizeModel.setSelected(true);
                                                mRatings.get(0).setSelected(false);
                                                mRatings.get(2).setSelected(true);
                                                refreshSizeList(mRatings.get(2).getRatings());
                                                finish = true;
                                                break;
                                            }
                                        }
                                    }

                                    if (!finish && CollectionUtil.isListNotEmpty(mLowerSize)) {
                                        mRatings.get(0).setSelected(true);
                                        refreshSizeList(mRatings.get(0).getRatings());
                                    }

                                    mRatingsAdapter.notifyDataSetChanged();
                                    mSizeAdapter.notifyDataSetChanged();
                                }
                                break;
                        }
                    } else {
                        switch (cmd) {
                            case BmtCmd.GET_FUSE_POWER_CAPS:
                            case BmtCmd.SET_FUSE_SPECS:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                                break;
                        }
                    }
                }
            });
        }
    }

    public static class FuseComparator implements Comparator<PSTotalSizeModel> {

        @Override
        public int compare(PSTotalSizeModel model1, PSTotalSizeModel model2) {
            int result;
            double powerCap1 = model1.getTotalLoadSize().getPowerCap();
            double powerCap2 = model2.getTotalLoadSize().getPowerCap();
            if (powerCap1 > powerCap2) {
                result = 1;
            } else if (powerCap1 < powerCap2) {
                result = -1;
            } else {
                result = 0;
            }
            if (result == 0) {
                String spec1 = model1.getTotalLoadSize().getSpec();
                String spec2 = model2.getTotalLoadSize().getSpec();
                return spec1.compareTo(spec2);
            } else {
                return result;
            }
        }
    }
}
