package com.dinsafer.module.powerstation.electricity.chart.marker;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.module.powerstation.electricity.controller.CycleType;
import com.dinsafer.util.Local;
import com.dinsafer.util.TimeUtil;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;

import java.util.List;

public class UsageMarkView extends CustomCombinedMarkerView {

    public UsageMarkView(Context context) {
        super(context);
    }

    @Override
    public void setMarker(List<Entry> entries, List<Highlight> highlights) {
        setUsageMarker(entries, highlights);
    }

    private void setUsageMarker(List<Entry> entries, List<Highlight> highlights) {
        if (BaseChartFragment.mCycleType == CycleType.DAY) {
            llSubValue.setVisibility(GONE);
            float value = entries.get(0).getVal();
            String valStr = ChartDataUtil.getPowerTransferVal(value, value, true) + getUnit(value, false);
            tvKey.setLocalText(mContext.getString(R.string.power));
            tvValue.setLocalText(valStr);
            int xIndex = entries.get(0).getXIndex();
            if (Math.abs(timeType) == 1) {
                tvTime.setLocalText(TimeUtil.getHourMinuteStr(timestamp, timezone, xIndex * interval, timeType));
            } else {
                tvTime.setLocalText(TimeUtil.minute2HourMinute(xIndex * interval));
            }
        } else {

            float value = entries.get(entries.size() < 2 ? 0 : 1).getVal();
            String valStr = ChartDataUtil.getPowerTransferVal(value, value, false) + getUnit(value, true);
            tvKey.setLocalText(mContext.getString(R.string.total));
            tvValue.setLocalText(valStr);
            if (entries.size() > 1) {
                float value2 = entries.get(0).getVal();
                String valStr2 = ChartDataUtil.getPowerTransferVal(value2, value2, true) + getUnit(value, false);
                tvSubKey.setText(Local.s(mContext.getString(R.string.ps_peak)));
                tvSubValue.setText(valStr2);
                llSubValue.setVisibility(VISIBLE);
            } else {
                llSubValue.setVisibility(GONE);
            }
            int index = entries.get(0).getXIndex();
            switch (BaseChartFragment.mCycleType) {
                case WEEK:
                    if (index >= BaseChartFragment.mWeeks.length) return;
                    tvTime.setLocalText(BaseChartFragment.mWeeks[index]);
                    break;

                case MONTH:
                    if (index >= BaseChartFragment.months.length) return;
                    tvTime.setLocalText(BaseChartFragment.months[index]);
                    break;

                case YEAR:
                    if (index >= BaseChartFragment.mYears.length) return;
                    tvTime.setLocalText(BaseChartFragment.mYears[index]);
                    break;

                case LIFETIME:
                    if (index >= BaseChartFragment.lifetimes.length) return;
                    tvTime.setLocalText(BaseChartFragment.lifetimes[index]);
                    break;
            }
        }
    }
}
