package com.dinsafer.module.powerstation.widget.schedule_mode_view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.dinsafer.dinnet.R;
import com.dinsafer.common.utils.DensityUtil;

public class ItemDataView extends View {

    private Context mContext;
    private Paint mPaint;
    private Path mPath;
    private Rect mRect;
    private RectF mRectF;
    //-1.放电 0. 正常 1.充电
    private int status;
    // -1. 没有选中 0. 正常 1.第一个 2.最后一个  3. 第一个也是最后一个
    private int selectedStatus;
    private int rx, ry;
    private int strokeWidth;

    public ItemDataView(Context context) {
        this(context, null);
    }

    public ItemDataView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ItemDataView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPath = new Path();
        mRect = new Rect();
        mRectF = new RectF();
        rx = DensityUtil.dp2px(mContext, 3);
        ry = DensityUtil.dp2px(mContext, 3);
        strokeWidth = DensityUtil.dp2px(mContext, 1);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        switch (status) {
            case -1:
                drawDischarge(canvas);
                break;

            case 0:
                drawNormal(canvas);
                break;

            case 1:
                drawCharge(canvas);
                break;
        }
    }

    private void drawNormal(Canvas canvas) {
        mRect.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_dark_01));
        canvas.drawRect(mRect, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_white_04));
        canvas.drawRect(mRect, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawCharge(Canvas canvas) {
        mRect.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_tip_06_2));
        canvas.drawRect(mRect, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_white_04));
        canvas.drawRect(mRect, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawDischarge(Canvas canvas) {
        mRect.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_tip_03_2));
        canvas.drawRect(mRect, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_white_04));
        canvas.drawRect(mRect, mPaint);
        if (selectedStatus > -1) {
            drawSelected(canvas);
        }
    }

    private void drawSelected(Canvas canvas) {
        if (selectedStatus == 0) {
            drawNormalSelected(canvas);
        } else if (selectedStatus == 1) {
            drawFirstSelected(canvas);
        } else if (selectedStatus == 2) {
            drawFinalSelected(canvas);
        } else if (selectedStatus == 3) {
            drawFirstFinalSelected(canvas);
        }
    }

    private void drawFirstFinalSelected(Canvas canvas) {
        mRectF.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);
    }

    private void drawNormalSelected(Canvas canvas) {
        mRectF.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop() - 20, getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom() + 20);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);
    }

    private void drawFirstSelected(Canvas canvas) {
        mRectF.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop(), getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom() + 20);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);

    }

    private void drawFinalSelected(Canvas canvas) {
        mRectF.set(getPaddingLeft() + strokeWidth / 2, getPaddingTop() - 20, getWidth() - getPaddingRight() - strokeWidth / 2, getHeight() - getPaddingBottom());
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(getColor(R.color.color_brand_light_03));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(strokeWidth);
        mPaint.setColor(getColor(R.color.color_brand_primary));
        canvas.drawRoundRect(mRectF, rx, ry, mPaint);
    }

    public void resetStatus(int status, int selectedStatus) {
        this.status = status;
        this.selectedStatus = selectedStatus;
        invalidate();
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
        invalidate();
    }
}
