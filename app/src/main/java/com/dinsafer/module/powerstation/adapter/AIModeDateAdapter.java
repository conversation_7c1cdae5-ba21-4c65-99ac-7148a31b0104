package com.dinsafer.module.powerstation.adapter;


import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.AIModeDateBean;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.dinsafer.ui.GradientLocalTextView;
import com.dinsafer.util.DDDateUtil;


public class AIModeDateAdapter extends BaseQuickAdapter<AIModeDateBean, BaseViewHolder> {

    public AIModeDateAdapter() {
        super(R.layout.item_ai_mode_date);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, AIModeDateBean item) {
        boolean isSelected = item.isSelected();
        long time = item.getTime();
        GradientLocalTextView tvDate = helper.getView(R.id.tv_date);
        GradientLocalTextView tvDay = helper.getView(R.id.tv_day);
        String date = DDDateUtil.formatLong(time, "dd");
        tvDate.setText(date);
        String day = DDDateUtil.getDayOfWeekByTimestamp(time);
        tvDay.setText(day);
        int[] colors = AIColorUtil.getAIColor(mContext);
        float[] positions = AIColorUtil.getAIColorPosition();
        long todayTime = item.getTodayTime();
        if (isSelected || time >= todayTime) {
            tvDate.setAIColorShader(colors, positions, 0, -5, -10, 10);
            tvDay.setAIColorShader(colors, positions, 0, -5, -10, 10);
        } else {
            int normalColor = mContext.getResources().getColor(R.color.color_white_01);
            tvDate.removeAIShader(normalColor);
            tvDay.removeAIShader(normalColor);
        }
    }
}
