package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.config.LocalKey;
import com.dinsafer.config.PsVersion1EventCode;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsWarningListBinding;
import com.dinsafer.module.powerstation.ExceptionWarning;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.Local;

public class PSWarningListModel extends BindModel<ItemPsWarningListBinding> {

    private Context mContext;
    private WarningBean warningBean;

    public PSWarningListModel(Context context, WarningBean warningBean) {
        super(context);
        this.mContext = context;
        this.warningBean = warningBean;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_warning_list;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsWarningListBinding binding) {
        if (warningBean == null) return;
        String eventKey = warningBean.getEventKey();
        if (TextUtils.isEmpty(eventKey)) {
            binding.tvTitle.setLocalText(warningBean.getTitle());
        } else {
            String title = Local.s(eventKey) + "-" + Local.s(warningBean.getTitle());
            binding.tvTitle.setText(title);
        }
        String content = Local.s(warningBean.getContent());
        content = content.replace(mContext.getString(R.string.well_device_name), ExceptionWarning.deviceName)
                .replace(mContext.getString(R.string.well_family), ExceptionWarning.currentFamily);
        binding.tvSubtitle.setText(content);
        binding.tvRestart.setVisibility(warningBean.isRestart() ? View.VISIBLE : View.GONE);
        binding.tvWithout.setVisibility(warningBean.isWithout() ? View.VISIBLE : View.GONE);
        switch (warningBean.getType()) {
            case PsVersion1EventCode.EVENT_PS_1000:
            case PsVersion1EventCode.EVENT_PS_1001:
            case PsVersion1EventCode.EVENT_PS_1002:
            case PsVersion1EventCode.EVENT_PS_1003:
            case PsVersion1EventCode.EVENT_PS_1004:
            case PsVersion1EventCode.EVENT_PS_1005:
            case PsVersion1EventCode.EVENT_PS_1006:
            case PsVersion1EventCode.EVENT_PS_1007:
            case PsVersion1EventCode.EVENT_PS_1008:
            case PsVersion1EventCode.EVENT_PS_1009:
            case PsVersion1EventCode.EVENT_PS_1010:

            case PsVersion1EventCode.EVENT_PS_1100:
            case PsVersion1EventCode.EVENT_PS_1101:
            case PsVersion1EventCode.EVENT_PS_1103:
            case PsVersion1EventCode.EVENT_PS_1104:
            case PsVersion1EventCode.EVENT_PS_1105:
            case PsVersion1EventCode.EVENT_PS_1106:
            case PsVersion1EventCode.EVENT_PS_1107:
            case PsVersion1EventCode.EVENT_PS_1108:
            case PsVersion1EventCode.EVENT_PS_1109:
            case PsVersion1EventCode.EVENT_PS_1110:
            case PsVersion1EventCode.EVENT_PS_1111:

            case PsVersion1EventCode.EVENT_PS_1200:
            case PsVersion1EventCode.EVENT_PS_1201:
            case PsVersion1EventCode.EVENT_PS_1202:
            case PsVersion1EventCode.EVENT_PS_1206:
            case PsVersion1EventCode.EVENT_PS_1207:
            case PsVersion1EventCode.EVENT_PS_1208:
            case PsVersion1EventCode.EVENT_PS_1209:
            case PsVersion1EventCode.EVENT_PS_1210:
            case PsVersion1EventCode.EVENT_PS_1211:
            case PsVersion1EventCode.EVENT_PS_1212:
            case PsVersion1EventCode.EVENT_PS_1213:

            case PsVersion1EventCode.EVENT_PS_1300:
            case PsVersion1EventCode.EVENT_PS_1301:
            case PsVersion1EventCode.EVENT_PS_1302:
            case PsVersion1EventCode.EVENT_PS_1303:
            case PsVersion1EventCode.EVENT_PS_1304:
            case PsVersion1EventCode.EVENT_PS_1305:
            case PsVersion1EventCode.EVENT_PS_1306:
            case PsVersion1EventCode.EVENT_PS_1307:
            case PsVersion1EventCode.EVENT_PS_1308:
            case PsVersion1EventCode.EVENT_PS_1309:
            case PsVersion1EventCode.EVENT_PS_1310:
            case PsVersion1EventCode.EVENT_PS_1311:
            case PsVersion1EventCode.EVENT_PS_1312:

            case PsVersion1EventCode.EVENT_PS_1400:
            case PsVersion1EventCode.EVENT_PS_1401:
            case PsVersion1EventCode.EVENT_PS_1402:
            case PsVersion1EventCode.EVENT_PS_1403:
            case PsVersion1EventCode.EVENT_PS_1404:

            case PsVersion1EventCode.EVENT_PS_1600:
            case PsVersion1EventCode.EVENT_PS_1601:

            case PsVersion1EventCode.EVENT_PS_6000:
            case PsVersion1EventCode.EVENT_PS_6001:
            case PsVersion1EventCode.EVENT_PS_6003:
            case PsVersion1EventCode.EVENT_PS_6004:
            case PsVersion1EventCode.EVENT_PS_6007:
            case PsVersion1EventCode.EVENT_PS_6010:

            case PsVersion1EventCode.EVENT_PS_2000:
            case PsVersion1EventCode.EVENT_PS_2001:
            case PsVersion1EventCode.EVENT_PS_2002:
            case PsVersion1EventCode.EVENT_PS_2003:
            case PsVersion1EventCode.EVENT_PS_2004:
            case PsVersion1EventCode.EVENT_PS_2005:
            case PsVersion1EventCode.EVENT_PS_2006:
                binding.tvRestart.setLocalText(mContext.getString(R.string.power_restart_inverter));
                break;

            case PsVersion1EventCode.EVENT_PS_7001:
            case PsVersion1EventCode.EVENT_PS_1203:
            case PsVersion1EventCode.EVENT_PS_1204:
            case PsVersion1EventCode.EVENT_PS_1205:
                binding.tvRestart.setLocalText(mContext.getString(R.string.power_restart_inverter));
                binding.tvWithout.setLocalText(warningBean.getWithoutText());
                break;

        }
    }

    public WarningBean getWarningBean() {
        return warningBean;
    }

    public void setWarningBean(WarningBean warningBean) {
        this.warningBean = warningBean;
    }
}
