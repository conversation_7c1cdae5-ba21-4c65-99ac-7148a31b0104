package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsTotalLoadSettingBinding;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.ui.rv.BindModel;

public class PSTotalSettingModel extends BindModel<ItemPsTotalLoadSettingBinding> {

    private Context context;
    private String ratings;
    private boolean selected;


    public PSTotalSettingModel(Context context, String ratings) {
        super(context);
        this.context = context;
        this.ratings = ratings;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_total_load_setting;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsTotalLoadSettingBinding binding) {
        holder.addOnClickListener(R.id.ly_rating);

        binding.tvRatingsName.setLocalText(ratings);
        binding.tvRatingsName.setTextColor(selected ? context.getResources().getColor(R.color.color_brand_text) : context.getResources().getColor(R.color.color_white_01));
        binding.lyRating.setBackgroundColor(selected ? context.getResources().getColor(R.color.color_brand_dark_02) : context.getResources().getColor(R.color.transparent));
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getRatings() {
        if (ratings.equals(context.getResources().getString(R.string.higher_ratings))) {
            return BmtDataKey.FUSE_POWER_HIGHER;
        } else if (ratings.equals(context.getResources().getString(R.string.medium_ratings))) {
            return BmtDataKey.FUSE_POWER_MEDIUM;
        } else {
            return BmtDataKey.FUSE_POWER_LOWER;
        }
    }
}
