package com.dinsafer.module.powerstation.utils.comparator;


import com.dinsafer.module.powerstation.adapter.PSElectricitySupplierModel;


public class PSElectricitySupplierSearchComparator extends BaseSearchComparator<PSElectricitySupplierModel> {

    public PSElectricitySupplierSearchComparator(String searchKeyword) {
        super(searchKeyword);
    }

    @Override
    public int compare(PSElectricitySupplierModel model1, PSElectricitySupplierModel model2) {
        String s1 = model1.getElectricitySupplierBean().getName().toLowerCase();
        String s2 = model2.getElectricitySupplierBean().getName().toLowerCase();
        return compareResult(s1, s2);
    }
}
