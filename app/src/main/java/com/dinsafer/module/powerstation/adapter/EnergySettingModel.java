package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemEnergySettingBinding;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.rv.BindModel;

public class EnergySettingModel extends BindModel<ItemEnergySettingBinding> {

    private String title;
    private String subtitle;
    private boolean open;
    private IOSSwitch.OnSwitchStateChangeListener stateChangeListener;

    public EnergySettingModel(Context context, String title, String subtitle,
                              IOSSwitch.OnSwitchStateChangeListener stateChangeListener) {
        super(context);
        this.title = title;
        this.subtitle = subtitle;
        this.stateChangeListener = stateChangeListener;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_energy_setting;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemEnergySettingBinding binding) {
        binding.tvTitle.setLocalText(title);
        binding.tvSubtitle.setText(subtitle);
        binding.swOpen.setOn(open);
        binding.swOpen.setOnSwitchStateChangeListener(isOn -> {
            open = isOn;
            if (stateChangeListener != null) {
                stateChangeListener.onStateSwitched(isOn);
            }
        });
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public void setStateChangeListener(IOSSwitch.OnSwitchStateChangeListener stateChangeListener) {
        this.stateChangeListener = stateChangeListener;
    }
}
