package com.dinsafer.module.powerstation.device;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ModifyPlugsLayoutBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rinfon on 16/7/12.
 */
public class PSSetNameFragment extends MyBaseFragment<ModifyPlugsLayoutBinding> implements IDeviceCallBack {

    public static final String ID = "id";
    private String id;
    private Device device;

    private String mDefaultBmtName = "";

    public static PSSetNameFragment newInstance(String id, String model) {
        PSSetNameFragment modifyPlugsFragment = new PSSetNameFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        args.putString(PSKeyConstant.KEY_MODEL, model);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.modify_plugs_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.btnSave.setOnClickListener(v -> toSave());
        mBinding.btnSkip.setOnClickListener(v -> toSkip());
    }

    @Override
    public void initData() {
        super.initData();
//        initBmtDefaultName();
        String text = getArguments().getString(ID);
        String model = getArguments().getString(PSKeyConstant.KEY_MODEL);

        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.device_settings));
        mBinding.btnSave.setLocalText(getResources().getString(R.string.save));
        mBinding.btnSkip.setLocalText(getResources().getString(R.string.skip));
        mBinding.btnSkip.setVisibility(View.GONE);

        mBinding.modifyPlugsNetwork.setVisibility(View.GONE);
        mBinding.modifyPlugsHint.setVisibility(View.GONE);
        mBinding.sirenSetting.setVisibility(View.GONE);
        id = text;
        device = DinSDK.getHomeInstance().getDevice(id, model);
        mDefaultBmtName = BmtUtil.getDeviceDefaultName(device);
        mBinding.modifyPlugsInput.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icon_edit_small, 0);
        mBinding.modifyPlugsInput.setCompoundDrawablePadding(DensityUtil.dp2px(getContext(), 19));
        mBinding.modifyPlugsInput.setHint(mDefaultBmtName);
        mBinding.modifyPlugsType.setLocalText(BmtUtil.getDeviceDefaultName(device));
        mBinding.modifyPlugsId.setText("ID:" + id);
        mBinding.commonBarBack.setVisibility(View.INVISIBLE);
        mBinding.llFamily.setVisibility(View.INVISIBLE);

        if (device == null) {
            showErrorToast();
            return;
        }
        device.registerDeviceCallBack(this);
    }

    public void toClose() {
        removeSelf();
    }

    public void toSave() {
        final String inputName = mBinding.modifyPlugsInput.getText().toString().trim();
        final String newName;
        if (TextUtils.isEmpty(inputName)) {
            newName = mDefaultBmtName;
        } else {
            newName = inputName;
        }

        if (TextUtils.isEmpty(newName) || !RegxUtil.isLegalName(newName)) {
            getMainActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            return;
        }

        toChangeName(newName);
    }

    public void toSkip() {
        getDelegateActivity().removeAllCommonFragment();
    }


    private void toChangeName(@NotNull final String setName) {
        showTimeOutLoadinFramgmentWithErrorAlert();

        DDLog.i(TAG, "toChangePluginName: " + id);
        if (null != device) {
            DDLog.i(TAG, "修改名字");
            Map<String, Object> result = new HashMap();
            result.put("cmd", "set_name");
            result.put("name", setName);
            device.submit(result);
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            device = DinHome.getInstance().getDevice(id);
            if (null != device) {
                device.registerDeviceCallBack(this);
            }
        }
    }

    /**
     * 初始化BMT默认名字
     */
    private void initBmtDefaultName() {
        final List<Device> deviceList = BmtManager.getInstance().getAllBmtDevice();
        final List<String> mbtNameList = new ArrayList<>(deviceList.size());
        for (Device d : deviceList) {
            mbtNameList.add(DeviceHelper.getString(d, DinConst.INFO_NAME, ""));
        }

        String familyNamePrefix = Local.s(getString(R.string.power_station));
        if (mbtNameList.size() <= 0
                || !mbtNameList.contains(familyNamePrefix)) {
            mDefaultBmtName = familyNamePrefix;
            return;
        }

        StringBuilder sb = new StringBuilder();
        String tempName;
        for (int i = 0; i < mbtNameList.size(); i++) {
            sb.delete(0, sb.length());
            sb.append(familyNamePrefix)
                    .append(" ");
            if (i < 9) {
                sb.append("0");
            }
            sb.append(i + 1);
            tempName = sb.toString();
            if (mbtNameList.contains(tempName)) {
                continue;
            }

            mDefaultBmtName = tempName;
            break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        if (null != device) {
            device.unregisterDeviceCallBack(this);
        }
    }

    @Override
    public boolean onBackPressed() {
        return true;
    }

    @Override
    public void onCmdCallBack(final String deviceID, String subCategory, final String cmd, Map map) {
        if (TextUtils.isEmpty(deviceID) || TextUtils.isEmpty(cmd) || !deviceID.equals(id) || !subCategory.equals(device.getSubCategory())) {
            return;
        }
        runOnMainThread(() -> {
            if (!getDelegateActivity().isCommonFragmentExist(PSSetNameFragment.class.getName())) {
                return;
            }

            if (cmd.equals("set_name")) {
                int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (status == 1) {
                    // 此处不进行连接了，进入初始化页面后再去连接
                    getDelegateActivity().addCommonFragment(PSDeviceInitializationFragment.newInstance(deviceID, subCategory));
                } else {
                    showErrorToast();
                }
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(FinishAddBmtEvent event) {
        removeSelf();
    }
}
