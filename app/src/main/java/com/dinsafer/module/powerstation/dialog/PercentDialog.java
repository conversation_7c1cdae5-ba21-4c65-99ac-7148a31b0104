package com.dinsafer.module.powerstation.dialog;

import android.os.Bundle;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogPercentBinding;
import com.dinsafer.permission.BaseBottomSheetDialog;

import java.util.ArrayList;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/27 16:09
 * @description :
 */
public class PercentDialog extends BaseBottomSheetDialog<DialogPercentBinding> {

    public static final String TAG = PercentDialog.class.getSimpleName();
    private final ArrayList<String> percentList = new ArrayList<>();
    private static final String KEY_PERCENT = "key_index";
    private int mPercent;

    public static PercentDialog newInstance(final Builder builder) {
        PercentDialog dialog = new PercentDialog();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_PERCENT, builder.percent);
        dialog.setArguments(bundle);
        dialog.setSelectPercentListener(builder.selectPercentListener);
        return dialog;
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_percent;
    }

    @Override
    protected int provideDialogHeight() {
        return dip2px(257);
    }

    @Override
    protected void initView() {
        super.initView();
        mBinding.tvCancel.setOnClickListener(v -> dismiss());
        mBinding.tvConfirm.setOnClickListener(v -> {
            if (selectPercentListener!=null) {
                int percent = mBinding.wpPercent.getCurrentItemPosition()*10;
                selectPercentListener.onSelect(percent);
            }
            dismiss();
        });
        initWheelData();
        Bundle bundle = getArguments();
        mPercent = bundle.getInt(KEY_PERCENT);
        mBinding.wpPercent.setSelectedItemPosition(mPercent/10);
    }
    // 初始化时间列表数据
    private void initWheelData() {
        for (int i=0;i<101; i++) {
            percentList.add(String.valueOf(i*10));
        }
        mBinding.wpPercent.setData(percentList);
    }

    private OnSelectPercentListener selectPercentListener;

    public void setSelectPercentListener(OnSelectPercentListener selectPercentListener) {
        this.selectPercentListener = selectPercentListener;
    }

    public interface OnSelectPercentListener {
        void onSelect(int percent);
    }

    public static final class Builder {
        private int percent;
        private OnSelectPercentListener selectPercentListener;

        public Builder setPercent(int percent) {
            this.percent = percent;
            return this;
        }

        public Builder setSelectPercentListener(OnSelectPercentListener selectPercentListener) {
            this.selectPercentListener = selectPercentListener;
            return this;
        }

        public PercentDialog build() {
            return PercentDialog.newInstance(this);
        }
    }
}
