package com.dinsafer.module.powerstation.impacts.report.adapter.holder;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;


import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsReportTextSendBinding;
import com.dinsafer.module.powerstation.impacts.report.adapter.listener.OnItemChildListener;
import com.dinsafer.module.powerstation.impacts.report.bean.IMessageInfo;
import com.dinsafer.module.powerstation.impacts.report.bean.TextMessageInfo;
import com.dinsafer.util.DDDateUtil;

import java.util.List;

public class TextSendViewHolder extends BaseViewHolder<TextMessageInfo, ItemPsReportTextSendBinding> {

    private String timezone;
    private OnItemChildListener itemChildListener;
    private static final long FIVE_MINUTE = 5 * 60 * 1000L;

    public TextSendViewHolder(ViewGroup parent) {
        super(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_ps_report_text_send, parent, false));
        mBinding.ivRetry.setOnClickListener(v -> {
            if (itemChildListener != null) {
                itemChildListener.onClickView(mBinding.ivRetry, (Integer) itemView.getTag());
            }
        });
    }

    public TextSendViewHolder(String timezone, ViewGroup parent, OnItemChildListener itemChildListener) {
        super(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_ps_report_text_send, parent, false));
        this.timezone = timezone;
        this.itemChildListener = itemChildListener;
        mBinding.flRetry.setOnClickListener(v -> {
            if (itemChildListener != null) {
                itemChildListener.onClickView(mBinding.ivRetry, getAdapterPosition());
            }
        });
    }

    @Override
    public void setData(int position, TextMessageInfo messageInfo, List<IMessageInfo> data) {
        int status = messageInfo.getStatus();
        setAnimResource(mBinding.ivLoading, status == 0, R.drawable.icon_ps_accessories_device_loading);
        mBinding.ivRetry.setVisibility(status == -1 ? View.VISIBLE : View.GONE);
        long time = messageInfo.getTime();
        if (position > 0) {
            long lastItemTime = data.get(position - 1).getTime();
            long timeDiff = time - lastItemTime;
            mBinding.tvTime.setVisibility(timeDiff < FIVE_MINUTE ? View.GONE : View.VISIBLE);
        } else {
            mBinding.tvTime.setVisibility(View.VISIBLE);
        }
        mBinding.tvTime.setText(DDDateUtil.formatWithTimezone(time, timezone, "HH:mm"));
        mBinding.tvContent.setLocalText(messageInfo.getData());
    }

    private void setAnimResource(ImageView imageView, boolean loading, int resource) {
        if (loading) {
            imageView.setImageTintList(null);
            imageView.setImageResource(resource);
            imageView.startAnimation(AnimationUtils.loadAnimation(imageView.getContext(), R.anim.loading_rotating));
            imageView.setVisibility(View.VISIBLE);
        } else {
            imageView.clearAnimation();
            imageView.setImageResource(resource);
            imageView.setVisibility(View.GONE);
        }
    }

    public void setItemChildListener(OnItemChildListener itemChildListener) {
        this.itemChildListener = itemChildListener;
    }
}
