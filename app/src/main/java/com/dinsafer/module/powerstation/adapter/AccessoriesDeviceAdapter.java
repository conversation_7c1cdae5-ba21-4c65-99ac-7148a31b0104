package com.dinsafer.module.powerstation.adapter;

import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;

import com.airbnb.lottie.LottieAnimationView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.PSAccessoryBean;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 18:20
 * @description :
 */
public class AccessoriesDeviceAdapter extends BaseQuickAdapter<PSAccessoryBean, BaseViewHolder> {

    public AccessoriesDeviceAdapter() {
        super(R.layout.item_accessories_device);
    }

    @Override
    protected void convert(BaseViewHolder helper, PSAccessoryBean item) {
        LocalTextView tvName = helper.getView(R.id.tv_name);
        LocalTextView tvStatus = helper.getView(R.id.tv_status);
        ImageView ivLoading = helper.getView(R.id.iv_loading);
        ImageView ivType = helper.getView(R.id.iv_type);
        LottieAnimationView lavIcon = helper.getView(R.id.lav_icon);
        LottieAnimationView lavLoading = helper.getView(R.id.lav_loading);
        tvName.setLocalText(item.getName());
        tvStatus.setVisibility(View.GONE);
        ivLoading.setVisibility(View.GONE);
        lavLoading.setVisibility(View.GONE);
        ivLoading.clearAnimation();
        lavIcon.cancelAnimation();
        int type = item.getType();
        int status = item.getStatus();

        if (type == PSAccessoryBean.FANS_TYPE) {
            lavIcon.setAnimation("power_json/animation_accessory_fan_on.json");
            ivType.setImageResource(status == PSAccessoryBean.FAN_STATE_RUNNING ? R.drawable.icon_accesories_fan_on
                    : R.drawable.icon_accesories_fan_off);
            switch (status) {
                case PSAccessoryBean.FAN_STATE_UNKNOWN:
                    tvStatus.setVisibility(View.VISIBLE);
                    tvStatus.setLocalText(mContext.getString(R.string.ps_battery_overview_unknown));
                    tvStatus.setSelected(false);
                    tvStatus.setVisibility(View.VISIBLE);
                    break;

                case PSAccessoryBean.FAN_STATE_LOADING:
//                    ivLoading.setVisibility(View.VISIBLE);
//                    setAnimResource(ivLoading, true, R.drawable.icon_ps_accessories_device_loading);
                    lavLoading.setVisibility(View.VISIBLE);
                    break;

                case PSAccessoryBean.FAN_STATE_STOP:
                case PSAccessoryBean.FAN_STATE_RUNNING:
                    tvStatus.setVisibility(View.VISIBLE);
                    tvStatus.setLocalText(mContext.getString(R.string.Online));
                    tvStatus.setSelected(true);
                    break;

                case PSAccessoryBean.FAN_STATE_EXCEPTION:
//                    setAnimResource(ivLoading, false, R.drawable.icon_power_warning);
//                    ivType.setVisibility(View.VISIBLE);
                    break;
            }
            if (status == PSAccessoryBean.FAN_STATE_RUNNING) {
                lavIcon.playAnimation();
            } else {
                lavIcon.cancelAnimation();
            }
            ivType.setImageResource(R.drawable.icon_accesories_fan_off);
            lavIcon.setVisibility(status == PSAccessoryBean.FAN_STATE_RUNNING ? View.VISIBLE : View.GONE);
            ivType.setVisibility(status != PSAccessoryBean.FAN_STATE_RUNNING ? View.VISIBLE : View.GONE);
        } else if (type == PSAccessoryBean.WATER_TYPE) {
            lavIcon.setAnimation("power_json/animation_accessory_watersensor_warning.json");
            switch (status) {
                case PSAccessoryBean.WATER_STATE_UNKNOWN:
                    tvStatus.setVisibility(View.VISIBLE);
                    tvStatus.setLocalText(mContext.getString(R.string.ps_battery_overview_unknown));
                    tvStatus.setSelected(false);
                    break;

                case PSAccessoryBean.WATER_STATE_LOADING:
//                    ivLoading.setVisibility(View.VISIBLE);
//                    setAnimResource(ivLoading, true, R.drawable.icon_ps_accessories_device_loading);
                    lavLoading.setVisibility(View.VISIBLE);
                    break;

                case PSAccessoryBean.WATER_STATE_VALID:
                case PSAccessoryBean.WATER_STATE_ALERT:
                    tvStatus.setVisibility(View.VISIBLE);
                    tvStatus.setLocalText(mContext.getString(R.string.Online));
                    tvStatus.setSelected(true);

                    break;
            }
//            if (status == PSAccessoryBean.WATER_STATE_ALERT) {
//                lavIcon.playAnimation();
//            } else {
//                lavIcon.cancelAnimation();
//            }
            ivType.setImageResource(R.drawable.icon_accesories_water_nor);
            lavIcon.setVisibility(View.GONE);
            ivType.setVisibility(View.VISIBLE);
//            lavIcon.setVisibility(status == PSAccessoryBean.WATER_STATE_ALERT ? View.VISIBLE : View.GONE);
//            ivType.setVisibility(status != PSAccessoryBean.WATER_STATE_ALERT ? View.VISIBLE : View.GONE);
        }

    }

    private void setAnimResource(ImageView imageView, boolean loading, int resource) {
        if (loading) {
            imageView.setImageTintList(null);
            imageView.setImageResource(resource);
            imageView.startAnimation(AnimationUtils.loadAnimation(mContext, R.anim.rotation));
        } else {
            imageView.clearAnimation();
            imageView.setImageResource(resource);
        }
    }
}
