package com.dinsafer.module.powerstation.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.PopupWindow;
import android.widget.TextClock;

import androidx.databinding.DataBindingUtil;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.PopupEvBinding;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.DDLog;

/**
 * @describe：
 * @date：2025/4/18
 * @author: create by Sydnee
 */
public class EVPopup extends PopupWindow {

    private Context mContext;
    private String mTitle;
    private String mContent;
    private boolean mIsOn;
    private PopupEvBinding mBinding;

    private OnSwitchStateChangeListener listener;

    public EVPopup (Context context, String title, String content, boolean isOn) {
        super(context);
        this.mContext = context;
        this.mTitle = title;
        this.mContent = content;
        this.mIsOn = isOn;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mBinding = DataBindingUtil.inflate(inflater, R.layout.popup_ev, null, false);
        // 设置布局
        setContentView(mBinding.getRoot());
        setBackgroundDrawable(null);
        setOutsideTouchable(true);
        setFocusable(true);
        initPopup();
    }

    public void setListener(OnSwitchStateChangeListener listener) {
        this.listener = listener;
    }

    private void initPopup() {
        if (!TextUtils.isEmpty(mTitle)) {
            mBinding.tvTitle.setLocalText(mTitle);
        }

        if (!TextUtils.isEmpty(mContent)) {
            mBinding.tvContent.setLocalText(mContent);
        }

        mBinding.evSwitch.setOn(mIsOn);
        mBinding.evSwitch.setOnSwitchStateChangeListener(isOn -> {
            if (null != listener) {
                listener.onSwitchStateChange(isOn);
            }
        });
    }

    public void setIsOn(boolean isOn) {
        this.mIsOn = isOn;
        if (null != mBinding) {
            mBinding.evSwitch.setOn(mIsOn);
        }
    }

    public interface OnSwitchStateChangeListener {
        void onSwitchStateChange(boolean isOn);
    }
}
