package com.dinsafer.module.powerstation.adapter;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalTextView;


public class PSAddDeviceHintAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    public PSAddDeviceHintAdapter() {
        super(R.layout.item_ps_add_device_hint);
    }

    @Override
    protected void convert(BaseViewHolder helper, String item) {
        LocalTextView tvOrder = helper.getView(R.id.tv_order);
        LocalTextView tvHint = helper.getView(R.id.tv_hint);
        tvOrder.setLocalText((helper.getAdapterPosition()+1)+".");
        tvHint.setLocalText(item);
    }
}
