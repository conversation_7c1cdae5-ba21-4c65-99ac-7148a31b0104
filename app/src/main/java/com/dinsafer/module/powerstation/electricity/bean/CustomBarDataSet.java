package com.dinsafer.module.powerstation.electricity.bean;

import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.utils.ColorTemplate;

import java.util.List;

public class CustomBarDataSet extends BarDataSet {

    protected List<Integer> mNegativeColors = null;

    public CustomBarDataSet(List<BarEntry> yVals, String label) {
        super(yVals, label);
    }

    public List<Integer> getNegativeColors() {
        return mNegativeColors;
    }

    public void setNegativeColors(int[] colors) {
        this.mNegativeColors = ColorTemplate.createColors(colors);
    }

    public int getNegativeColor() {
        return mNegativeColors.get(0);
    }
}
