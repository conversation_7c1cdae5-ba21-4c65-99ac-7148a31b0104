package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.dinsafer.dinnet.R;
import com.dinsafer.model.PointBean;
import com.dinsafer.plugin.widget.util.DensityUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CustomPTMView extends View {

    private Paint mPaint;
    private int mLeftSpace;
    private int mRightSpace;
    private int mBorderColor;
    private int mDashLineColor;
    private int mBorderWidth;
    private int mDashWidth;
    private int mRectColor;
    private int mDischargeStartColor;
    private int mDischargeEndColor;
    private int mChargeStartColor;
    private int mChargeEndColor;
    private int mDischargeLineColor;
    private int mChargeLineColor;
    private Rect mRect;
    private Rect mProtectRect;
    private Rect mLowerChargeRect;
    private Rect mEmergencyDischargeRect;
    private Rect mEmergencyChargeRect;
    private Rect mSmartDischargeRect;
    private Rect mSmartChargeRect;
    private int mCenterY;
    private float mRectWidth;
    private List<Integer> mData = new ArrayList<>();

    public CustomPTMView(Context context) {
        this(context, null);
    }

    public CustomPTMView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomPTMView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mLeftSpace = DensityUtil.dp2px(context, 35);
        mRightSpace = DensityUtil.dp2px(context, 35);
        mBorderWidth = 1;
        mDashWidth = 4;
        mBorderColor = context.getResources().getColor(R.color.color_tip_06);
        mDashLineColor = context.getResources().getColor(R.color.color_white_04);
        mRectColor = context.getResources().getColor(R.color.color_brand_light_01);
        mDischargeLineColor = context.getResources().getColor(R.color.price_tracking_mode_discharge_data_line_color);
        mChargeLineColor = context.getResources().getColor(R.color.price_tracking_mode_charge_data_line_color);
        mDischargeStartColor = Color.parseColor("#4D38808F");
        mDischargeEndColor = Color.parseColor("#CC38808F");
        mChargeStartColor = Color.parseColor("#CC62707F");
        mChargeEndColor = Color.parseColor("#4D62707F");
        mRect = new Rect();
        mProtectRect = new Rect();
        mLowerChargeRect = new Rect();
        mEmergencyDischargeRect = new Rect();
        mEmergencyChargeRect = new Rect();
        mSmartDischargeRect = new Rect();
        mSmartChargeRect = new Rect();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mRect.left = getPaddingLeft() + mLeftSpace;
        mRect.top = getPaddingTop();
        mRect.right = w - getPaddingRight() - mRightSpace;
        mRect.bottom = getMeasuredHeight() - getPaddingBottom();
        mRectWidth = mRect.right - mRect.left;
        mProtectRect.left = mRect.left + mBorderWidth;
        mProtectRect.top = mRect.top + mBorderWidth;
        mProtectRect.right = (int) (mRect.left + mBorderWidth + mRectWidth * 0.02f);
        mProtectRect.bottom = mRect.bottom - mBorderWidth;
        mCenterY = (mRect.top + mRect.bottom) / 2;

    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        mPaint.setColor(mRectColor);
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mRect, mPaint);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mDashWidth); // 线条宽度
        mPaint.setPathEffect(new DashPathEffect(new float[]{15, 10}, 0)); // 设置虚线效果，数组中的数字代表了线和空白的长度，第二个参数是偏移量
        mPaint.setColor(mDashLineColor);
        canvas.drawLine(mRect.left, mCenterY, mRect.right, mCenterY, mPaint);
        mPaint.setPathEffect(null);
        mPaint.setColor(mBorderColor);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mBorderWidth);
        canvas.drawRect(mRect, mPaint);

        mPaint.setShader(new LinearGradient(0, mProtectRect.top, 0, mProtectRect.bottom,
                mChargeStartColor, mChargeEndColor, Shader.TileMode.CLAMP));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mProtectRect, mPaint);
        mPaint.setShader(null);
        mPaint.setColor(mBorderColor);
        canvas.drawLine(mProtectRect.right, mRect.top, mProtectRect.right + mBorderWidth, mRect.bottom, mPaint);

        mPaint.setShader(new LinearGradient(0, mLowerChargeRect.top, 0, mLowerChargeRect.bottom,
                mChargeStartColor, mChargeEndColor, Shader.TileMode.CLAMP));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mLowerChargeRect, mPaint);
        mPaint.setShader(null);
        mPaint.setColor(mBorderColor);
        canvas.drawLine(mLowerChargeRect.right, mRect.top, mLowerChargeRect.right + mBorderWidth, mRect.bottom, mPaint);

        mPaint.setShader(new LinearGradient(0, mEmergencyDischargeRect.top, 0, mEmergencyDischargeRect.bottom,
                mDischargeStartColor, mDischargeEndColor, Shader.TileMode.CLAMP));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mEmergencyDischargeRect, mPaint);

        mPaint.setShader(new LinearGradient(0, mEmergencyChargeRect.top, 0, mEmergencyChargeRect.bottom,
                mChargeStartColor, mChargeEndColor, Shader.TileMode.CLAMP));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mEmergencyChargeRect, mPaint);
        mPaint.setShader(null);
        mPaint.setColor(mBorderColor);
        canvas.drawLine(mEmergencyChargeRect.right, mRect.top, mEmergencyChargeRect.right + mBorderWidth, mRect.bottom, mPaint);

        mPaint.setShader(new LinearGradient(0, mSmartDischargeRect.top, 0, mSmartDischargeRect.bottom,
                mDischargeStartColor, mDischargeEndColor, Shader.TileMode.CLAMP));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mSmartDischargeRect, mPaint);

        mPaint.setShader(new LinearGradient(0, mSmartChargeRect.top, 0, mSmartChargeRect.bottom,
                mChargeStartColor, mChargeEndColor, Shader.TileMode.CLAMP));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mSmartChargeRect, mPaint);
        mPaint.setShader(null);

        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mBorderWidth * 3);
        mPaint.setColor(mChargeLineColor);
        canvas.drawLine(mLowerChargeRect.left, mLowerChargeRect.top, mLowerChargeRect.right, mLowerChargeRect.top, mPaint);
        canvas.drawLine(mEmergencyChargeRect.left, mEmergencyChargeRect.top, mEmergencyChargeRect.right, mEmergencyChargeRect.top, mPaint);
        canvas.drawLine(mSmartChargeRect.left, mSmartChargeRect.top, mSmartChargeRect.right, mSmartChargeRect.top, mPaint);

        mPaint.setColor(mDischargeLineColor);
        canvas.drawLine(mEmergencyDischargeRect.left, mEmergencyDischargeRect.bottom, mEmergencyDischargeRect.right, mEmergencyDischargeRect.bottom, mPaint);
        canvas.drawLine(mSmartDischargeRect.left, mSmartDischargeRect.bottom, mSmartDischargeRect.right, mSmartDischargeRect.bottom, mPaint);
    }

    public void resetData(int emergencyReserve, int smartReserve,
                          int c1, int c2, int c3, int s1, int s2) {
        mData.clear();
        mData.add(Math.abs(c1));
        mData.add(Math.abs(c2));
        mData.add(Math.abs(c3));
        mData.add(Math.abs(s1));
        mData.add(Math.abs(s2));
        Collections.sort(mData);
        float maxVal = mData.get(mData.size() - 1) < 100 ? 100f : mData.get(mData.size() - 1);
        float emWeight = emergencyReserve / 100f;
        float smartWeight = smartReserve / 100f;
        float lower = emWeight - 0.02f;
        float emergency = smartWeight - emWeight;
        float smart = 1 - smartWeight;
        mLowerChargeRect.left = mProtectRect.right + mBorderWidth;
        mLowerChargeRect.top = (int) (mCenterY + mDashWidth + (mRect.bottom - mCenterY - mBorderWidth) * (-c3 / maxVal));
        mLowerChargeRect.right = (int) (mProtectRect.right + mBorderWidth + lower * mRectWidth);
        mLowerChargeRect.bottom = mRect.bottom - mBorderWidth;

        mEmergencyDischargeRect.left = mLowerChargeRect.right + mBorderWidth;
        mEmergencyDischargeRect.top = mRect.top + mBorderWidth;
        mEmergencyDischargeRect.right = (int) (mLowerChargeRect.right + mBorderWidth + emergency * mRectWidth);
        mEmergencyDischargeRect.bottom = (int) (mRect.top + mBorderWidth + (mCenterY - mBorderWidth) * (1 - (s2 / maxVal)));


        mEmergencyChargeRect.left = mEmergencyDischargeRect.left;
        mEmergencyChargeRect.top = (int) (mCenterY + mDashWidth + (mRect.bottom - mCenterY - mBorderWidth) * (-c2 / maxVal));
        mEmergencyChargeRect.right = mEmergencyDischargeRect.right;
        mEmergencyChargeRect.bottom = mLowerChargeRect.bottom;

        mSmartDischargeRect.left = mEmergencyDischargeRect.right + mBorderWidth;
        mSmartDischargeRect.top = mEmergencyDischargeRect.top;
        mSmartDischargeRect.right = mRect.right - mBorderWidth;
        mSmartDischargeRect.bottom = (int) (mRect.top - mBorderWidth + (mCenterY - mBorderWidth) * (1 - (s1 / maxVal)));

        mSmartChargeRect.left = mSmartDischargeRect.left;
        mSmartChargeRect.top = (int) (mCenterY + mDashWidth + (mRect.bottom - mCenterY - mBorderWidth) * (-c1 / maxVal));
        mSmartChargeRect.right = mSmartDischargeRect.right;
        mSmartChargeRect.bottom = mEmergencyChargeRect.bottom;
        postInvalidate();
    }

    public PointBean getLowChargePoint(int type) {
        float x = 0f;
        float y = 0f;
        switch (type) {
            case 1:
                x = (mLowerChargeRect.left + mLowerChargeRect.right) / 2f;
                y = mLowerChargeRect.top;
                break;

            case 2:
                x = (mEmergencyDischargeRect.left + mEmergencyDischargeRect.right) / 2f;
                y = mEmergencyDischargeRect.bottom;
                break;

            case 3:
                x = (mEmergencyChargeRect.left + mEmergencyChargeRect.right) / 2f;
                y = mEmergencyChargeRect.top;
                break;

            case 4:
                x = (mSmartDischargeRect.left + mSmartDischargeRect.right) / 2f;
                y = mSmartDischargeRect.bottom;
                break;

            case 5:
                x = (mSmartChargeRect.left + mSmartChargeRect.right) / 2f;
                y = mSmartChargeRect.top;
                break;
        }
        return new PointBean(x, y);
    }
}
