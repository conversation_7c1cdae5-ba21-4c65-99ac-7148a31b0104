package com.dinsafer.module.powerstation.electricity.bean;

import androidx.annotation.DrawableRes;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/10 17:30
 * @description :
 */
public class ElectricityTabBean {

    private @DrawableRes int iconSel;
    private @DrawableRes int iconNor;
    private @DrawableRes int selectedBgColor;
    private boolean selected;

    public ElectricityTabBean(int iconSel, int iconNor, int selectedBgColor, boolean selected) {
        this.iconSel = iconSel;
        this.iconNor = iconNor;
        this.selectedBgColor = selectedBgColor;
        this.selected = selected;
    }

    public int getIconSel() {
        return iconSel;
    }

    public void setIconSel(int iconSel) {
        this.iconSel = iconSel;
    }

    public int getIconNor() {
        return iconNor;
    }

    public void setIconNor(int iconNor) {
        this.iconNor = iconNor;
    }

    public int getSelectedBgColor() {
        return selectedBgColor;
    }

    public void setSelectedBgColor(int selectedBgColor) {
        this.selectedBgColor = selectedBgColor;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
