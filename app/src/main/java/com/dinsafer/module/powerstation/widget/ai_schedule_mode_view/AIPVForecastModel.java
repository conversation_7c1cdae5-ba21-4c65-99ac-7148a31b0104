package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAiPvForecastBinding;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.DDDateUtil;

public class AIPVForecastModel extends BindModel<ItemAiPvForecastBinding> {

    private Context mContext;
    private int hour;
    private long startTime;
    private long endTime;
    private String timezone;
    private int status;
    private boolean isFirst;
    private boolean isLast;
    private AIPVForecastModel lastModel;

    public AIPVForecastModel(Context context, long startTime, long endTime, String timezone,
                             int status, boolean isFirst, boolean isLast, AIPVForecastModel lastModel) {
        super(context);
        this.mContext = context;
        this.startTime = startTime;
        this.endTime = endTime;
        this.timezone = timezone;
        this.status = status;
        this.isFirst = isFirst;
        this.isLast = isLast;
        this.lastModel = lastModel;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ai_pv_forecast;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAiPvForecastBinding binding) {
        binding.tvTimeTop.setTranslationY(DensityUtil.dp2px(mContext, -9));
        hour = DDDateUtil.getHourByTimestamps(startTime, timezone);
        int endHour = (isLast && hour == 23) ? (hour + 1) : DDDateUtil.getHourByTimestamps(endTime, timezone);
        binding.tvTimeCenter.setVisibility(hour == 0 ? View.VISIBLE : View.GONE);
        binding.tvTimeBottom.setTranslationY(DensityUtil.dp2px(mContext, isLast ? 5 : 10));
        String hourStr = hour < 10 ? ("0" + hour + ":00") : (hour + ":00");
        String endHourStr = endHour < 10 ? ("0" + endHour + ":00") : (endHour + ":00");
        binding.tvTimeTop.setText(hourStr);
        String date = "(" + DDDateUtil.formatWithTimezone(startTime, timezone, "MM.dd") + ")";
        binding.tvTimeCenter.setLocalText(date);
        binding.tvTimeBottom.setText(endHourStr);
        binding.tvTimeTop.setVisibility(hour % 3 == 0 && endHour != 24 ? View.VISIBLE : View.INVISIBLE);
        binding.tvTimeBottom.setVisibility(endHour == 24 ? View.VISIBLE : View.GONE);
        binding.viewData.setFirst(isFirst);
        binding.viewData.setLast(isLast);
        int percent = 30;
        switch (status) {
            case 0:
                binding.ivStatus.setImageResource(R.drawable.icon_timebar_pv_high);
                binding.tvStatus.setLocalText(mContext.getResources().getString(R.string.Intense_solar));
                percent = 100;
                binding.viewData.setPvMode(0);
                break;

            case 1:
                binding.ivStatus.setImageResource(R.drawable.icon_timebar_pv_medium);
                binding.tvStatus.setLocalText(mContext.getResources().getString(R.string.Sufficient_solar));
                percent = 66;
                binding.viewData.setPvMode(1);
                break;

            case 2:
                binding.ivStatus.setImageResource(R.drawable.icon_timebar_pv_low);
                binding.tvStatus.setLocalText(mContext.getResources().getString(R.string.Low_solar));
                percent = 33;
                binding.viewData.setPvMode(2);
                break;

            case 3:
                binding.ivStatus.setImageResource(R.drawable.icon_timebar_pv_no);
                binding.tvStatus.setLocalText(mContext.getResources().getString(R.string.No_solar));
                percent = 0;
                binding.viewData.setPvMode(3);
                break;

            default:
                binding.ivStatus.setImageResource(0);
        }
        binding.viewData.invalidate();
        if (lastModel != null) {
            boolean showLine = status == lastModel.getStatus();
            binding.ivStatus.setVisibility(showLine ? View.INVISIBLE : View.VISIBLE);
            binding.tvStatus.setVisibility(showLine ? View.INVISIBLE : View.VISIBLE);
            binding.viewLine.setVisibility(showLine ? View.VISIBLE : View.INVISIBLE);
        } else {
            binding.ivStatus.setVisibility(View.VISIBLE);
            binding.tvStatus.setVisibility(View.VISIBLE);
            binding.viewLine.setVisibility(View.INVISIBLE);
        }
        int finalPercent = percent;
        binding.apbStatus.post(new Runnable() {
            @Override
            public void run() {
                binding.apbStatus.setProgress(finalPercent / 100f, new int[]{getColor(R.color.color_pv_forecast_start), getColor(R.color.color_pv_forecast_end)});
            }
        });
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }
}
