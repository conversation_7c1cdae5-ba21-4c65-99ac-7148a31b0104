package com.dinsafer.module.powerstation.adapter;

import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.electricity.bean.ElectricityCircleTypeBean;
import com.dinsafer.ui.LocalTextView;

import java.util.List;

/**
 * 用电统计周期类型适配器
 */
public class ElectricityCircleTypeAdapter extends BaseQuickAdapter<ElectricityCircleTypeBean, BaseViewHolder> {

    public ElectricityCircleTypeAdapter(@LayoutRes int layout, @Nullable List<ElectricityCircleTypeBean> data) {
        super(layout, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ElectricityCircleTypeBean item) {
        LocalTextView tvType = helper.getView(R.id.tv_type);
        tvType.setLocalText(item.getType());
        helper.setVisible(R.id.iv_selected, item.isSelected());
    }

    public ElectricityCircleTypeBean getSelectedItem() {
        for (ElectricityCircleTypeBean electricityCircleTypeBean : getData()) {
            if (electricityCircleTypeBean.isSelected()) {
                return electricityCircleTypeBean;
            }
        }
        return null;
    }
}
