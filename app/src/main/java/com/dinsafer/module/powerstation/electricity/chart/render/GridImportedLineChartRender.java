package com.dinsafer.module.powerstation.electricity.chart.render;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.util.Log;
import androidx.core.content.ContextCompat;

import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.utils.Transformer;
import com.dinsafer.module.powerstation.electricity.bean.ThresholdBarDataSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Grid-imported类型折线图渲染器
 * 专门处理带阈值的4层分层渲染
 * 使用单例模式避免多次实例化导致的配置覆盖问题
 *
 * 渲染层级：
 * 第1层：紧急电量和正常电量围成的区域（底层）
 * 第2层：正常电量到底部的区域
 * 第3层：阈值区域（阈值折线上方区域与电量区域的交集）
 * 第4层：正常电量上层覆盖（正常电量大于阈值时的覆盖层）
 */
public class GridImportedLineChartRender {

    private static final String TAG = "GridImportedRender";

    // 单例实例
    private static volatile GridImportedLineChartRender sInstance;

    /**
     * 获取单例实例
     */
    public static GridImportedLineChartRender getInstance() {
        if (sInstance == null) {
            synchronized (GridImportedLineChartRender.class) {
                if (sInstance == null) {
                    sInstance = new GridImportedLineChartRender();
                    Log.i(TAG, "创建GridImportedLineChartRender单例实例");
                }
            }
        }
        return sInstance;
    }

    private Paint mRenderPaint;
    private LineData mLineData; // 用于查找数据集
    private Context mContext; // 用于获取颜色资源

    // X轴标注配置参数（用于CustomCombinedChart调用）
    private boolean mDrawXAxisAnnotation = true; // 是否绘制X轴标注
    private float mXAxisAnnotationGap = 1f;     // X轴线与标注区域的间距（像素）
    private float mXAxisAnnotationHeight = 5f; // 标注区域的高度（像素）

    // 阈值区域控制参数
    private boolean mDrawThresholdRegion = false; // 是否绘制阈值区域
    private ILineDataSet mThresholdDataSet; // 直接传递的阈值数据集

    public GridImportedLineChartRender() {
        mRenderPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mRenderPaint.setStyle(Paint.Style.FILL);
    }

    public GridImportedLineChartRender(Context context) {
        this();
        this.mContext = context;
    }

    /**
     * 设置LineData，用于查找数据集
     */
    public void setLineData(LineData lineData) {
        this.mLineData = lineData;
    }

    /**
     * 设置Context，用于获取颜色资源
     */
    public void setContext(Context context) {
        this.mContext = context;
    }

    /**
     * 设置是否绘制X轴阈值时间段标注
     * 这个方法被CustomCombinedChart调用
     */
    public void setDrawXAxisAnnotation(boolean drawXAxisAnnotation) {
        this.mDrawXAxisAnnotation = drawXAxisAnnotation;
        Log.i(TAG, "设置X轴标注: " + (drawXAxisAnnotation ? "启用" : "禁用"));
    }

    /**
     * 获取是否绘制X轴标注的状态
     */
    public boolean isDrawXAxisAnnotation() {
        return mDrawXAxisAnnotation;
    }

    /**
     * 设置X轴标注的间距和高度
     * 这个方法被CustomCombinedChart调用
     * @param gap X轴与标注区域的间距（像素）
     * @param height 标注区域的高度（像素）
     */
    public void setXAxisAnnotationDimensions(float gap, float height) {
        this.mXAxisAnnotationGap = gap;
        this.mXAxisAnnotationHeight = height;
        Log.i(TAG, String.format("设置X轴标注尺寸: 间距=%.1f, 高度=%.1f", gap, height));
    }

    /**
     * 设置是否绘制阈值区域
     */
    public void setDrawThresholdRegion(boolean drawThresholdRegion) {
        Log.i(TAG, String.format("setDrawThresholdRegion（单例）: 旧值=%s, 新值=%s, 实例ID=%s",
              mDrawThresholdRegion ? "启用" : "禁用",
              drawThresholdRegion ? "启用" : "禁用",
              Integer.toHexString(this.hashCode())));

        this.mDrawThresholdRegion = drawThresholdRegion;

        // 打印调用栈的前几层，帮助调试
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (int i = 0; i < Math.min(3, stackTrace.length); i++) {
            Log.i(TAG, "调用栈[" + i + "]: " + stackTrace[i].getMethodName());
        }
    }

    /**
     * 获取是否绘制阈值区域
     */
    public boolean isDrawThresholdRegion() {
        return mDrawThresholdRegion;
    }

    /**
     * 设置阈值数据集
     */
    public void setThresholdDataSet(ILineDataSet thresholdDataSet) {
        this.mThresholdDataSet = thresholdDataSet;
        Log.i(TAG, "设置阈值数据集: " + (thresholdDataSet != null ? thresholdDataSet.getLabel() : "null"));
    }



    /**
     * 绘制Grid-imported类型折线图的4层渲染
     *
     * @param canvas 画布
     * @param emergencyPowerDataSet 紧急电量数据集
     * @param normalPowerDataSet 正常电量数据集
     * @param thresholdDataSet 阈值数据集
     * @param minx 最小X索引
     * @param count 数据点数量
     * @param pointsPerEntryPair 每个条目对的点数
     * @param isDrawSteppedEnabled 是否启用阶梯绘制
     * @param phaseY Y轴相位
     * @param trans 坐标变换器
     */
    public void drawGridImportedLayers(Canvas canvas,
                                     ILineDataSet emergencyPowerDataSet,
                                     ILineDataSet normalPowerDataSet,
                                     ILineDataSet thresholdDataSet,
                                     int minx, int count, int pointsPerEntryPair,
                                     boolean isDrawSteppedEnabled, float phaseY,
                                     Transformer trans) {

        Log.i(TAG, "开始Grid-imported类型折线图4层渲染");

        // 第1层：绘制紧急电量区域（紧急电量和正常电量围成的区域）
        if (emergencyPowerDataSet != null && normalPowerDataSet != null) {
            Log.i(TAG, "第1层：绘制紧急电量区域（与正常电量围成的区域）");
            drawEmergencyPowerRegionBetweenLines(canvas, emergencyPowerDataSet, normalPowerDataSet,
                                                minx, count, phaseY, trans);
        } else if (emergencyPowerDataSet != null) {
            Log.i(TAG, "第1层：绘制紧急电量区域（到底部）");
            drawPowerRegion(canvas, emergencyPowerDataSet, minx, count, phaseY, trans, "紧急电量");
        }

        // 第2层：绘制正常电量区域（折线下方区域）
        if (normalPowerDataSet != null) {
            Log.i(TAG, "第2层：绘制正常电量区域（折线下方）");
            drawPowerRegion(canvas, normalPowerDataSet, minx, count, phaseY, trans, "正常电量");
        }

        // 第3层：绘制阈值区域（折线上方区域，与电量区域取交集）
        Log.i(TAG, String.format("第3层检查: 阈值数据集=%s, 有效数据=%s, 过滤器启用=%s",
              thresholdDataSet != null ? "存在" : "无",
              thresholdDataSet != null ? hasValidThresholdData(thresholdDataSet, minx, count, phaseY) : "N/A",
              isThresholdRegionFilterEnabled()));

        if (thresholdDataSet != null && hasValidThresholdData(thresholdDataSet, minx, count, phaseY) && isThresholdRegionFilterEnabled()) {
            if (emergencyPowerDataSet != null) {
                Log.i(TAG, "第3层：绘制阈值区域（折线上方，与紧急电量交集）");
                drawThresholdRegion(canvas, emergencyPowerDataSet, thresholdDataSet, minx, count, phaseY, trans);
            } else if (normalPowerDataSet != null) {
                Log.i(TAG, "第3层：绘制阈值区域（折线上方，与正常电量交集）");
                drawThresholdRegion(canvas, normalPowerDataSet, thresholdDataSet, minx, count, phaseY, trans);
            } else {
                Log.w(TAG, "第3层：无法绘制阈值区域（没有电量数据集）");
            }
        } else if (thresholdDataSet == null) {
            Log.w(TAG, "第3层：跳过阈值区域绘制（阈值数据集为空）");
        } else if (!isThresholdRegionFilterEnabled()) {
            Log.i(TAG, "第3层：跳过阈值区域绘制（阈值区域过滤器未启用）");
        } else if (!hasValidThresholdData(thresholdDataSet, minx, count, phaseY)) {
            Log.i(TAG, "第3层：跳过阈值区域绘制（没有有效的阈值数据>0）");
        }

        // 第4层：正常电量上层覆盖（正常电量大于阈值时的覆盖层）
//        if (emergencyPowerDataSet != null && normalPowerDataSet != null && thresholdDataSet != null
//            && hasValidThresholdData(thresholdDataSet, minx, count, phaseY) && isThresholdRegionFilterEnabled()) {
//            Log.i(TAG, "第4层：正常电量上层覆盖（性能优化版本）");
//            drawNormalPowerTopCoverage(canvas, normalPowerDataSet, thresholdDataSet, minx, count, phaseY, trans);
//        } else if (emergencyPowerDataSet != null && normalPowerDataSet != null && thresholdDataSet != null && !isThresholdRegionFilterEnabled()) {
//            Log.i(TAG, "第4层：跳过正常电量上层覆盖（阈值区域过滤器未启用）");
//        } else if (emergencyPowerDataSet != null && normalPowerDataSet != null && thresholdDataSet != null) {
//            Log.i(TAG, "第4层：跳过正常电量上层覆盖（没有有效的阈值数据>0）");
//        }

        Log.i(TAG, "Grid-imported类型折线图4层渲染完成");
    }

    /**
     * 绘制紧急电量和正常电量围成的区域
     */
    private void drawEmergencyPowerRegionBetweenLines(Canvas canvas, ILineDataSet emergencyPowerDataSet, ILineDataSet normalPowerDataSet,
                                                    int minx, int count, float phaseY, Transformer trans) {

        Log.i(TAG, "绘制紧急电量和正常电量围成的区域");

        // 生成紧急电量和正常电量围成的区域路径
        Path regionPath = generateEmergencyNormalRegionPath(emergencyPowerDataSet, normalPowerDataSet, minx, count, phaseY);

        if (regionPath != null && !regionPath.isEmpty()) {
            trans.pathValueToPixel(regionPath);

            // 保存画笔状态
            Paint.Style previousStyle = mRenderPaint.getStyle();
            int previousColor = mRenderPaint.getColor();

            try {
                // 绘制填充区域 - 使用透明颜色
                mRenderPaint.setStyle(Paint.Style.FILL);
                int fillColor = emergencyPowerDataSet.getFillColor();
                int alpha = 0x80; // 50% 透明
                mRenderPaint.setColor((alpha << 24) | (fillColor & 0xffffff));
                mRenderPaint.setAntiAlias(true);

                canvas.drawPath(regionPath, mRenderPaint);

                Log.i(TAG, String.format("紧急电量围成区域 - 填充透明色: #%08X", (alpha << 24) | (fillColor & 0xffffff)));

                // 绘制紧急电量折线 - 使用原色（不透明）
                mRenderPaint.setStyle(Paint.Style.STROKE);
                mRenderPaint.setColor(emergencyPowerDataSet.getColor());
                mRenderPaint.setStrokeWidth(emergencyPowerDataSet.getLineWidth());
                mRenderPaint.setPathEffect(null);

                // 生成紧急电量折线路径并进行坐标转换
                Path emergencyLinePath = generatePowerLinePath(emergencyPowerDataSet, minx, count, phaseY);
                trans.pathValueToPixel(emergencyLinePath);
                canvas.drawPath(emergencyLinePath, mRenderPaint);

                Log.i(TAG, String.format("紧急电量折线 - 线条原色: #%08X", emergencyPowerDataSet.getColor()));

            } finally {
                // 恢复画笔状态
                mRenderPaint.setStyle(previousStyle);
                mRenderPaint.setColor(previousColor);
            }
        }
    }

    /**
     * 绘制电量区域（通用方法）
     */
    private void drawPowerRegion(Canvas canvas, ILineDataSet powerDataSet, int minx, int count,
                               float phaseY, Transformer trans, String regionName) {

        Log.i(TAG, "绘制" + regionName + "区域");

        // 生成电量区域路径
        Path regionPath = generatePowerRegionPath(powerDataSet, minx, count, phaseY);

        if (regionPath != null && !regionPath.isEmpty()) {
            trans.pathValueToPixel(regionPath);

            // 保存画笔状态
            Paint.Style previousStyle = mRenderPaint.getStyle();
            int previousColor = mRenderPaint.getColor();

            try {
                // 绘制填充区域 - 使用透明颜色
                mRenderPaint.setStyle(Paint.Style.FILL);
                int fillColor = powerDataSet.getFillColor();
                int alpha = 0x80; // 50% 透明
                mRenderPaint.setColor((alpha << 24) | (fillColor & 0xffffff));
                mRenderPaint.setAntiAlias(true);

                canvas.drawPath(regionPath, mRenderPaint);

                Log.i(TAG, String.format("%s区域 - 填充透明色: #%08X", regionName, (alpha << 24) | (fillColor & 0xffffff)));

                // 绘制折线 - 使用原色（不透明）
                mRenderPaint.setStyle(Paint.Style.STROKE);
                mRenderPaint.setColor(powerDataSet.getColor());
                mRenderPaint.setStrokeWidth(powerDataSet.getLineWidth());
                mRenderPaint.setPathEffect(null);

                // 生成折线路径并进行坐标转换
                Path linePath = generatePowerLinePath(powerDataSet, minx, count, phaseY);
                trans.pathValueToPixel(linePath);
                canvas.drawPath(linePath, mRenderPaint);

                Log.i(TAG, String.format("%s区域 - 线条原色: #%08X", regionName, powerDataSet.getColor()));

            } finally {
                // 恢复画笔状态
                mRenderPaint.setStyle(previousStyle);
                mRenderPaint.setColor(previousColor);
            }
        }
    }

    /**
     * 绘制阈值区域（阈值折线及其上方区域，与电量区域取交集）
     */
    private void drawThresholdRegion(Canvas canvas, ILineDataSet powerDataSet, ILineDataSet thresholdDataSet,
                                   int minx, int count, float phaseY, Transformer trans) {

        Log.i(TAG, "绘制阈值区域（折线上方与电量区域交集）");

        // 生成阈值区域路径（阈值折线到顶部）
        Path thresholdPath = generateThresholdIntersectionPath(powerDataSet, thresholdDataSet, minx, count, phaseY);

        if (thresholdPath != null && !thresholdPath.isEmpty()) {
            trans.pathValueToPixel(thresholdPath);

            // 保存画布状态
            int saveCount = canvas.save();

            try {
                // 生成电量区域作为裁剪路径（电量折线到底部）
                Path powerClipPath = generatePowerRegionPath(powerDataSet, minx, count, phaseY);
                trans.pathValueToPixel(powerClipPath);

                // 使用电量区域裁剪阈值区域（只显示交集部分）
                canvas.clipPath(powerClipPath);
                Log.i(TAG, "使用电量区域（折线下方）裁剪阈值区域（折线上方）");

                // 绘制阈值区域填充
                if (thresholdDataSet instanceof SectionLineDataSet) {
                    SectionLineDataSet sectionDataSet = (SectionLineDataSet) thresholdDataSet;

                    int fillColor = sectionDataSet.getThresholdFillColor();

                    if (fillColor != 0) {
                        // 保存画笔状态
                        Paint.Style previousStyle = mRenderPaint.getStyle();
                        int previousColor = mRenderPaint.getColor();

                        try {
                            mRenderPaint.setStyle(Paint.Style.FILL);
                            // 使用透明的阈值填充颜色
                            int alpha = 0x80; // 50% 透明
                            mRenderPaint.setColor((alpha << 24) | (fillColor & 0xffffff));
                            mRenderPaint.setAntiAlias(true);

                            // 使用 SRC_ATOP 模式：保持透明度，但完全替换重叠区域
                            mRenderPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_ATOP));

                            canvas.drawPath(thresholdPath, mRenderPaint);

                            // 恢复默认混合模式
                            mRenderPaint.setXfermode(null);

                            Log.i(TAG, String.format("绘制阈值区域交集 - 透明色: #%08X", (alpha << 24) | (fillColor & 0xffffff)));

                        } finally {
                            // 恢复画笔状态
                            mRenderPaint.setStyle(previousStyle);
                            mRenderPaint.setColor(previousColor);
                        }

                        // 绘制阈值区域的上边缘（Y轴最高边缘）为非透明阈值颜色
                        drawThresholdTopEdge(canvas, powerDataSet, thresholdDataSet, minx, count, phaseY, trans, fillColor);
                    }
                }

            } finally {
                // 恢复画布状态（移除裁剪）
                canvas.restoreToCount(saveCount);
                Log.i(TAG, "恢复画布状态，移除阈值区域裁剪");
            }
        }
    }

    /**
     * 绘制正常电量上层覆盖（性能优化版本）
     */
    private void drawNormalPowerTopCoverage(Canvas canvas, ILineDataSet normalPowerDataSet, ILineDataSet thresholdDataSet,
                                          int minx, int count, float phaseY, Transformer trans) {

        Log.i(TAG, "开始正常电量上层覆盖（性能优化版本）");

        // 生成正常电量到阈值的区域路径（性能优化：只绘制需要覆盖的部分）
        Path normalToThresholdPath = generateNormalToThresholdRegionPath(normalPowerDataSet, thresholdDataSet, minx, count, phaseY);

        if (normalToThresholdPath != null && !normalToThresholdPath.isEmpty()) {
            trans.pathValueToPixel(normalToThresholdPath);

            // 保存画布状态
            int saveCount = canvas.save();

            try {
                // 使用阈值裁剪：只在正常电量大于阈值的区域绘制
                Path thresholdClipPath = generateNormalAboveThresholdClipPath(normalPowerDataSet, thresholdDataSet, minx, count, phaseY);
                if (thresholdClipPath != null && !thresholdClipPath.isEmpty()) {
                    trans.pathValueToPixel(thresholdClipPath);
                    canvas.clipPath(thresholdClipPath);
                    Log.i(TAG, "应用正常电量大于阈值的裁剪");
                }

                // 保存画笔状态
                Paint.Style previousStyle = mRenderPaint.getStyle();
                int previousColor = mRenderPaint.getColor();

                try {
                    // 直接使用 SRC 模式完全替换（推荐）
                    mRenderPaint.setStyle(Paint.Style.FILL);
                    int fillColor = normalPowerDataSet.getFillColor();
                    int alpha = 0x80; // 50% 透明
                    mRenderPaint.setColor((alpha << 24) | (fillColor & 0xffffff));
                    mRenderPaint.setAntiAlias(true);

                    // 使用 SRC 模式：完全替换，避免与底层混合
                    mRenderPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC));
                    canvas.drawPath(normalToThresholdPath, mRenderPaint);

                    // 恢复默认混合模式
                    mRenderPaint.setXfermode(null);

                    Log.i(TAG, String.format("SRC模式直接替换 - 透明色: #%08X", (alpha << 24) | (fillColor & 0xffffff)));

                    // 绘制正常电量折线（在正常电量大于阈值的区域内）
                    drawNormalPowerLineInThresholdClip(canvas, normalPowerDataSet, thresholdDataSet, minx, count, phaseY, trans);

                } finally {
                    // 恢复画笔状态
                    mRenderPaint.setStyle(previousStyle);
                    mRenderPaint.setColor(previousColor);
                }

            } finally {
                // 恢复画布状态（移除裁剪）
                canvas.restoreToCount(saveCount);
                Log.i(TAG, "恢复画布状态，移除阈值裁剪");
            }
        }
    }

    // ==================== 路径生成方法 ====================

    /**
     * 生成紧急电量和正常电量围成的区域路径
     */
    private Path generateEmergencyNormalRegionPath(ILineDataSet emergencyPowerDataSet, ILineDataSet normalPowerDataSet,
                                                 int minx, int count, float phaseY) {
        Path path = new Path();
        boolean pathStarted = false;

        Log.i(TAG, "生成紧急电量和正常电量围成的区域路径");

        // 第一步：沿着紧急电量折线绘制
        for (int x = minx; x < count; x++) {
            Entry emergencyEntry = emergencyPowerDataSet.getEntryForIndex(x);
            if (emergencyEntry == null) continue;

            float xValue = emergencyEntry.getXIndex();
            float emergencyValue = emergencyEntry.getVal() * phaseY;

            if (!pathStarted) {
                path.moveTo(xValue, emergencyValue);
                pathStarted = true;
                Log.i(TAG, String.format("紧急电量路径起点: x=%.1f, y=%.1f", xValue, emergencyValue));
            } else {
                path.lineTo(xValue, emergencyValue);
            }
        }

        // 第二步：沿着正常电量折线反向绘制（从最后一个点到第一个点）
        for (int x = count - 1; x >= minx; x--) {
            Entry normalEntry = normalPowerDataSet.getEntryForIndex(x);
            if (normalEntry == null) continue;

            float xValue = normalEntry.getXIndex();
            float normalValue = normalEntry.getVal() * phaseY;

            path.lineTo(xValue, normalValue);

            // 输出前几个点的坐标用于调试
            if (x == count - 1) {
                Log.i(TAG, String.format("正常电量路径起点: x=%.1f, y=%.1f", xValue, normalValue));
            }
        }

        // 第三步：封闭路径
        if (pathStarted) {
            path.close();
            Log.i(TAG, "紧急电量和正常电量围成区域路径封闭完成");
        }

        return path;
    }

    /**
     * 生成电量区域路径（从折线到底部）
     */
    private Path generatePowerRegionPath(ILineDataSet powerDataSet, int minx, int count, float phaseY) {
        Path path = new Path();
        boolean pathStarted = false;

        for (int x = minx; x < count; x++) {
            Entry entry = powerDataSet.getEntryForIndex(x);
            if (entry == null) continue;

            float xValue = entry.getXIndex();
            float yValue = entry.getVal() * phaseY;

            if (!pathStarted) {
                path.moveTo(xValue, 0);      // 从底部开始
                path.lineTo(xValue, yValue); // 到折线点
                pathStarted = true;
            } else {
                path.lineTo(xValue, yValue); // 沿着折线
            }
        }

        // 封闭区域：回到底部
        if (pathStarted && count > minx) {
            Entry lastEntry = powerDataSet.getEntryForIndex(count - 1);
            if (lastEntry != null) {
                path.lineTo(lastEntry.getXIndex(), 0);
                path.close();
            }
        }

        return path;
    }

    /**
     * 生成电量折线路径
     */
    private Path generatePowerLinePath(ILineDataSet powerDataSet, int minx, int count, float phaseY) {
        Path path = new Path();
        boolean pathStarted = false;

        for (int x = minx; x < count; x++) {
            Entry entry = powerDataSet.getEntryForIndex(x);
            if (entry == null) continue;

            float xValue = entry.getXIndex();
            float yValue = entry.getVal() * phaseY;

            if (!pathStarted) {
                path.moveTo(xValue, yValue);
                pathStarted = true;
            } else {
                path.lineTo(xValue, yValue);
            }
        }

        return path;
    }



    // ==================== 辅助方法（从原类中提取） ====================

    /**
     * 生成阈值交集路径（阈值折线到图表顶部）
     * 修正：只有阈值大于0的数据才有意义
     */
    private Path generateThresholdIntersectionPath(ILineDataSet powerDataSet, ILineDataSet thresholdDataSet,
                                                 int minx, int count, float phaseY) {
        Path path = new Path();
        boolean pathStarted = false;

        Log.i(TAG, "生成阈值交集路径（只处理阈值>0的数据）");

        // 获取图表顶部值
        float chartTop = getChartTopValue(powerDataSet, thresholdDataSet, minx, count, phaseY);

        // 收集所有阈值大于0的点
        List<PointF> validThresholdPoints = new ArrayList<>();

        for (int x = minx; x < count; x++) {
            Entry thresholdEntry = thresholdDataSet.getEntryForIndex(x);
            if (thresholdEntry == null) continue;

            float xValue = thresholdEntry.getXIndex();
            float thresholdValue = thresholdEntry.getVal() * phaseY;

            // 只有阈值大于0的数据才有意义
            if (thresholdValue > 0) {
                validThresholdPoints.add(new PointF(xValue, thresholdValue));
                Log.i(TAG, String.format("有效阈值点: x=%.1f, threshold=%.1f", xValue, thresholdValue));
            }
        }

        if (validThresholdPoints.isEmpty()) {
            Log.i(TAG, "没有找到阈值>0的数据点，返回空路径");
            return path;
        }

        Log.i(TAG, String.format("找到 %d 个有效阈值点", validThresholdPoints.size()));

        // 第一步：沿着阈值折线绘制（从阈值线到图表顶部）
        PointF firstPoint = validThresholdPoints.get(0);
        path.moveTo(firstPoint.x, firstPoint.y);
        path.lineTo(firstPoint.x, chartTop);

        for (int i = 1; i < validThresholdPoints.size(); i++) {
            PointF point = validThresholdPoints.get(i);
            path.lineTo(point.x, chartTop);
        }

        // 第二步：沿着图表顶部回到起点，然后沿着阈值线封闭
        for (int i = validThresholdPoints.size() - 1; i >= 0; i--) {
            PointF point = validThresholdPoints.get(i);
            path.lineTo(point.x, point.y);
        }

        path.close();
        Log.i(TAG, "阈值交集路径生成完成");

        return path;
    }

    /**
     * 生成正常电量到阈值的区域路径（性能优化版本）
     */
    private Path generateNormalToThresholdRegionPath(ILineDataSet normalPowerDataSet, ILineDataSet thresholdDataSet,
                                                   int minx, int count, float phaseY) {
        Path path = new Path();

        Log.i(TAG, "生成正常电量到阈值的区域路径（性能优化）");

        // 收集所有正常电量大于阈值的点
        List<PointF> normalPoints = new ArrayList<>();
        List<PointF> thresholdPoints = new ArrayList<>();

        for (int x = minx; x < count; x++) {
            Entry normalEntry = normalPowerDataSet.getEntryForIndex(x);
            Entry thresholdEntry = thresholdDataSet.getEntryForIndex(x);

            if (normalEntry == null || thresholdEntry == null) continue;

            float xValue = normalEntry.getXIndex();
            float normalValue = normalEntry.getVal() * phaseY;
            float thresholdValue = thresholdEntry.getVal() * phaseY;

            // 只有当阈值大于0且正常电量大于阈值时，才收集点
            if (thresholdValue > 0 && normalValue > thresholdValue) {
                normalPoints.add(new PointF(xValue, normalValue));
                thresholdPoints.add(new PointF(xValue, thresholdValue));
                Log.i(TAG, String.format("有效覆盖点: x=%.1f, normal=%.1f, threshold=%.1f", xValue, normalValue, thresholdValue));
            }
        }

        // 如果有有效点，生成封闭路径
        if (!normalPoints.isEmpty()) {
            Log.i(TAG, String.format("找到 %d 个有效点", normalPoints.size()));

            // 第一步：沿着正常电量折线绘制
            PointF firstPoint = normalPoints.get(0);
            path.moveTo(firstPoint.x, firstPoint.y);
            Log.i(TAG, String.format("路径起点: x=%.1f, y=%.1f", firstPoint.x, firstPoint.y));

            for (int i = 1; i < normalPoints.size(); i++) {
                PointF point = normalPoints.get(i);
                path.lineTo(point.x, point.y);
            }

            // 第二步：沿着阈值折线反向绘制回到起点
            for (int i = thresholdPoints.size() - 1; i >= 0; i--) {
                PointF point = thresholdPoints.get(i);
                path.lineTo(point.x, point.y);
            }

            // 第三步：封闭路径
            path.close();

            Log.i(TAG, "正常电量到阈值的区域路径封闭完成");
        } else {
            Log.i(TAG, "没有找到正常电量大于阈值的点，返回空路径");
        }

        return path;
    }

    /**
     * 生成正常电量大于阈值的裁剪路径
     */
    private Path generateNormalAboveThresholdClipPath(ILineDataSet normalPowerDataSet, ILineDataSet thresholdDataSet,
                                                    int minx, int count, float phaseY) {
        Path path = new Path();
        boolean inAboveThresholdRegion = false;

        Log.i(TAG, "生成正常电量大于阈值的裁剪路径");

        // 获取图表的Y轴范围
        float chartTop = getChartTopValue(normalPowerDataSet, thresholdDataSet, minx, count, phaseY);
        float chartBottom = 0f;

        for (int x = minx; x < count; x++) {
            Entry normalEntry = normalPowerDataSet.getEntryForIndex(x);
            Entry thresholdEntry = thresholdDataSet.getEntryForIndex(x);

            if (normalEntry == null || thresholdEntry == null) continue;

            float xValue = normalEntry.getXIndex();
            float normalValue = normalEntry.getVal() * phaseY;
            float thresholdValue = thresholdEntry.getVal() * phaseY;

            // 判断阈值是否大于0且正常电量是否大于阈值
            boolean thresholdValid = (thresholdValue > 0);
            boolean normalAboveThreshold = (normalValue > thresholdValue);

            if (thresholdValid && normalAboveThreshold) {
                if (!inAboveThresholdRegion) {
                    // 开始正常电量大于阈值的区域
                    path.moveTo(xValue, chartBottom);
                    path.lineTo(xValue, chartTop);
                    inAboveThresholdRegion = true;
                    Log.i(TAG, String.format("正常电量大于阈值区域开始: x=%.1f, normal=%.1f, threshold=%.1f",
                          xValue, normalValue, thresholdValue));
                } else {
                    // 继续正常电量大于阈值的区域
                    path.lineTo(xValue, chartTop);
                }
            } else {
                if (inAboveThresholdRegion) {
                    // 结束正常电量大于阈值的区域
                    path.lineTo(xValue, chartTop);
                    path.lineTo(xValue, chartBottom);
                    path.close();
                    inAboveThresholdRegion = false;
                    Log.i(TAG, String.format("正常电量大于阈值区域结束: x=%.1f", xValue));
                }
            }
        }

        // 如果区域还没有结束，封闭它
        if (inAboveThresholdRegion && count > minx) {
            Entry lastEntry = normalPowerDataSet.getEntryForIndex(count - 1);
            if (lastEntry != null) {
                float lastX = lastEntry.getXIndex();
                path.lineTo(lastX, chartTop);
                path.lineTo(lastX, chartBottom);
                path.close();
                Log.i(TAG, String.format("正常电量大于阈值区域最终封闭: x=%.1f", lastX));
            }
        }

        Log.i(TAG, "正常电量大于阈值的裁剪路径生成完成");
        return path;
    }

    // ==================== 完整实现的方法 ====================

    /**
     * 绘制阈值区域的上边缘（Y轴最高边缘）为非透明阈值颜色
     */
    private void drawThresholdTopEdge(Canvas canvas, ILineDataSet powerDataSet, ILineDataSet thresholdDataSet,
                                    int minx, int count, float phaseY, Transformer trans, int thresholdFillColor) {

        Log.i(TAG, "绘制阈值区域上边缘（非透明阈值颜色）");

        // 生成阈值区域上边缘的路径
        Path topEdgePath = generateThresholdTopEdgePath(powerDataSet, thresholdDataSet, minx, count, phaseY);

        if (topEdgePath != null && !topEdgePath.isEmpty()) {
            trans.pathValueToPixel(topEdgePath);

            // 保存画笔状态
            Paint.Style previousStyle = mRenderPaint.getStyle();
            int previousColor = mRenderPaint.getColor();
            float previousStrokeWidth = mRenderPaint.getStrokeWidth();

            try {
                // 绘制上边缘线条 - 使用非透明阈值颜色
                mRenderPaint.setStyle(Paint.Style.STROKE);
                mRenderPaint.setColor(thresholdFillColor | 0xFF000000); // 非透明阈值颜色
                mRenderPaint.setStrokeWidth(3.0f); // 稍微粗一点的线条
                mRenderPaint.setAntiAlias(true);
                mRenderPaint.setPathEffect(null);

                canvas.drawPath(topEdgePath, mRenderPaint);

                Log.i(TAG, String.format("绘制阈值上边缘 - 非透明色: #%08X", thresholdFillColor | 0xFF000000));

            } finally {
                // 恢复画笔状态
                mRenderPaint.setStyle(previousStyle);
                mRenderPaint.setColor(previousColor);
                mRenderPaint.setStrokeWidth(previousStrokeWidth);
            }
        }
    }

    /**
     * 生成阈值区域上边缘的路径
     */
    private Path generateThresholdTopEdgePath(ILineDataSet powerDataSet, ILineDataSet thresholdDataSet,
                                            int minx, int count, float phaseY) {
        Path path = new Path();
        boolean pathStarted = false;

        Log.i(TAG, "生成阈值区域上边缘路径");

        for (int x = minx; x < count; x++) {
            Entry powerEntry = powerDataSet.getEntryForIndex(x);
            Entry thresholdEntry = thresholdDataSet.getEntryForIndex(x);

            if (powerEntry == null || thresholdEntry == null) continue;

            float xValue = powerEntry.getXIndex();
            float powerValue = powerEntry.getVal() * phaseY;
            float thresholdValue = thresholdEntry.getVal() * phaseY;

            // 只有当阈值大于0且电量超过阈值时，才绘制上边缘
            if (thresholdValue > 0 && powerValue > thresholdValue) {
                // 上边缘是电量值（因为电量超过了阈值）
                float topEdgeValue = powerValue;

                if (!pathStarted) {
                    path.moveTo(xValue, topEdgeValue);
                    pathStarted = true;
                    Log.i(TAG, String.format("上边缘起点: x=%.1f, y=%.1f", xValue, topEdgeValue));
                } else {
                    path.lineTo(xValue, topEdgeValue);
                }
            } else {
                // 电量未超过阈值，中断路径
                if (pathStarted) {
                    pathStarted = false;
                }
            }
        }

        Log.i(TAG, "阈值区域上边缘路径生成完成");
        return path;
    }

    /**
     * 在正常电量大于阈值的裁剪区域内绘制正常电量折线
     */
    private void drawNormalPowerLineInThresholdClip(Canvas canvas, ILineDataSet normalPowerDataSet, ILineDataSet thresholdDataSet,
                                                  int minx, int count, float phaseY, Transformer trans) {

        Log.i(TAG, "在正常电量大于阈值的裁剪区域内绘制正常电量折线");

        // 生成正常电量折线路径
        Path linePath = generatePowerLinePath(normalPowerDataSet, minx, count, phaseY);

        if (linePath != null && !linePath.isEmpty()) {
            trans.pathValueToPixel(linePath);

            // 保存画笔状态
            Paint.Style previousStyle = mRenderPaint.getStyle();
            int previousColor = mRenderPaint.getColor();
            float previousStrokeWidth = mRenderPaint.getStrokeWidth();

            try {
                // 绘制正常电量折线 - 使用原色（不透明）
                mRenderPaint.setStyle(Paint.Style.STROKE);
                mRenderPaint.setColor(normalPowerDataSet.getColor()); // 原色，不透明
                mRenderPaint.setStrokeWidth(normalPowerDataSet.getLineWidth());
                mRenderPaint.setAntiAlias(true);
                mRenderPaint.setPathEffect(null);

                canvas.drawPath(linePath, mRenderPaint);

                Log.i(TAG, String.format("正常电量折线（大于阈值区域内） - 原色: #%08X", normalPowerDataSet.getColor()));

            } finally {
                // 恢复画笔状态
                mRenderPaint.setStyle(previousStyle);
                mRenderPaint.setColor(previousColor);
                mRenderPaint.setStrokeWidth(previousStrokeWidth);
            }
        }
    }

    /**
     * 获取图表顶部值
     */
    private float getChartTopValue(ILineDataSet powerDataSet, ILineDataSet thresholdDataSet, int minx, int count, float phaseY) {
        float maxValue = 0f;

        // 找到最大值
        for (int x = minx; x < count; x++) {
            if (powerDataSet != null) {
                Entry entry = powerDataSet.getEntryForIndex(x);
                if (entry != null) {
                    maxValue = Math.max(maxValue, entry.getVal() * phaseY);
                }
            }
            if (thresholdDataSet != null) {
                Entry entry = thresholdDataSet.getEntryForIndex(x);
                if (entry != null) {
                    maxValue = Math.max(maxValue, entry.getVal() * phaseY);
                }
            }
        }

        // 在最大值基础上增加20%边距
        return maxValue * 1.2f;
    }

    // ==================== 数据集查找方法 ====================

    /**
     * 自动查找数据集并绘制Grid-imported图层
     */
    public void drawGridImportedLayersAuto(Canvas canvas, int minx, int count, int pointsPerEntryPair,
                                         boolean isDrawSteppedEnabled, float phaseY, Transformer trans) {

        Log.i(TAG, "自动查找数据集并绘制Grid-imported图层");

        if (mLineData == null) {
            Log.w(TAG, "LineData未设置，无法自动查找数据集");
            return;
        }

        // 查找各种数据集
        ILineDataSet normalPowerDataSet = findNormalPowerDataSet();
        ILineDataSet emergencyPowerDataSet = findEmergencyPowerDataSet();

        // 优先使用传递的阈值数据集，如果没有则自动查找
        ILineDataSet thresholdDataSet = mThresholdDataSet != null ? mThresholdDataSet : findThresholdDataSet();

        Log.i(TAG, String.format("数据集状态: 正常电量=%s, 紧急电量=%s, 阈值=%s（%s）",
              normalPowerDataSet != null ? "存在" : "无",
              emergencyPowerDataSet != null ? "存在" : "无",
              thresholdDataSet != null ? "存在" : "无",
              mThresholdDataSet != null ? "直接传递" : "自动查找"));

        Log.i(TAG, String.format("数据集状态 - 正常电量: %s, 紧急电量: %s, 阈值: %s",
              normalPowerDataSet != null ? "有" : "无",
              emergencyPowerDataSet != null ? "有" : "无",
              thresholdDataSet != null ? "有" : "无"));

        // 调用主要的绘制方法
        drawGridImportedLayers(canvas, emergencyPowerDataSet, normalPowerDataSet, thresholdDataSet,
                             minx, count, pointsPerEntryPair, isDrawSteppedEnabled, phaseY, trans);
    }

    /**
     * 查找正常电量数据集
     */
    private ILineDataSet findNormalPowerDataSet() {
        if (mLineData == null) return null;

        for (int i = 0; i < mLineData.getDataSetCount(); i++) {
            ILineDataSet dataSet = mLineData.getDataSetByIndex(i);
            if (dataSet instanceof SectionLineDataSet) {
                SectionLineDataSet sectionDataSet = (SectionLineDataSet) dataSet;
                if (sectionDataSet.isNormalPower()) {
                    return dataSet;
                }
            }
        }

        Log.i(TAG, "未找到正常电量数据集");
        return null;
    }

    /**
     * 查找紧急电量数据集
     */
    private ILineDataSet findEmergencyPowerDataSet() {
        if (mLineData == null) return null;

        for (int i = 0; i < mLineData.getDataSetCount(); i++) {
            ILineDataSet dataSet = mLineData.getDataSetByIndex(i);
            if (dataSet instanceof SectionLineDataSet) {
                SectionLineDataSet sectionDataSet = (SectionLineDataSet) dataSet;
                if (sectionDataSet.isEmergencyPower()) {
                    return dataSet;
                }
            }
        }

        Log.i(TAG, "未找到紧急电量数据集");
        return null;
    }

    /**
     * 查找阈值数据集
     */
    public ILineDataSet findThresholdDataSet() {
        if (mLineData == null) return null;

        for (int i = 0; i < mLineData.getDataSetCount(); i++) {
            ILineDataSet dataSet = mLineData.getDataSetByIndex(i);
            if (dataSet instanceof SectionLineDataSet) {
                SectionLineDataSet sectionDataSet = (SectionLineDataSet) dataSet;

                // 统一使用dataType查找
                if (sectionDataSet.isThresholdLine()) {
                    Log.i(TAG, "找到阈值数据集: " + sectionDataSet.getLabel());
                    return dataSet;
                }
            }
        }

        Log.i(TAG, "未找到阈值数据集");
        return null;
    }

    /**
     * 判断是否为第一个数据集（用于控制何时执行分层绘制）
     */
    public boolean isFirstDataSet(ILineDataSet dataSet) {
        if (mLineData == null || dataSet == null) return false;

        // 检查是否为第一个数据集
        if (mLineData.getDataSetCount() > 0) {
            ILineDataSet firstDataSet = mLineData.getDataSetByIndex(0);
            boolean isFirst = (firstDataSet == dataSet);
            Log.i(TAG, String.format("数据集 %s 是否为第一个: %s", dataSet.getLabel(), isFirst ? "是" : "否"));
            return isFirst;
        }

        return false;
    }



    /**
     * 验证阈值数据集是否包含有效数据（阈值>0）
     */
    private boolean hasValidThresholdData(ILineDataSet thresholdDataSet, int minx, int count, float phaseY) {
        if (thresholdDataSet == null) return false;

        for (int x = minx; x < count; x++) {
            Entry thresholdEntry = thresholdDataSet.getEntryForIndex(x);
            if (thresholdEntry == null) continue;

            float thresholdValue = thresholdEntry.getVal();
            if (thresholdValue > 0) {
                Log.i(TAG, String.format("找到有效阈值数据: x=%d, threshold=%.1f", x, thresholdValue));
                return true;
            }
        }

        Log.i(TAG, "阈值数据集中没有找到大于0的阈值数据");
        return false;
    }

    /**
     * 检查阈值区域是否启用
     */
    private boolean isThresholdRegionFilterEnabled() {
        Log.i(TAG, String.format("检查阈值区域状态（单例）: mDrawThresholdRegion=%s, 返回=%s, 实例ID=%s",
              mDrawThresholdRegion, (mDrawThresholdRegion ? "启用" : "禁用"),
              Integer.toHexString(this.hashCode())));
        return mDrawThresholdRegion;
    }

    // ==================== X轴标注方法（供CustomCombinedChart调用） ====================

    /**
     * 在图表绘制完成后绘制X轴标注
     * 这个方法被CustomCombinedChart在onDraw最后调用
     */
    public void drawXAxisAnnotationAfterChart(Canvas canvas, ILineDataSet thresholdDataSet,
                                            int minx, int count, float phaseX, Transformer trans) {

        if (!mDrawXAxisAnnotation) {
            Log.i(TAG, "X轴标注已禁用，跳过绘制");
            return;
        }

        if (thresholdDataSet == null || !hasValidThresholdData(thresholdDataSet, minx, count, 1.0f)) {
            Log.i(TAG, "X轴标注跳过绘制（没有有效的阈值数据>0）");
            return;
        }

        Log.i(TAG, "在图表绘制完成后绘制X轴标注（避免被X轴标签覆盖）");
        drawXAxisThresholdAnnotation(canvas, thresholdDataSet, minx, count, phaseX, trans);
    }

    /**
     * 绘制X轴下方的阈值时间段标注
     * 支持动画同步
     */
    private void drawXAxisThresholdAnnotation(Canvas canvas, ILineDataSet thresholdDataSet,
                                            int minx, int count, float phaseX, Transformer trans) {

        Log.i(TAG, "开始绘制X轴阈值时间段标注");

        // 生成阈值时间段的标注区域
        List<ThresholdTimeSegment> thresholdSegments = generateThresholdTimeSegments(thresholdDataSet, minx, count, 1.0f);

        if (thresholdSegments.isEmpty()) {
            Log.i(TAG, "没有找到阈值大于0的时间段");
            return;
        }

        Log.i(TAG, String.format("找到 %d 个阈值时间段，动画相位=%.2f", thresholdSegments.size(), phaseX));

        // 获取X轴的位置信息
        XAxisPosition xAxisPosition = getXAxisPosition(canvas, trans);

        // 绘制每个阈值时间段（支持动画）
        for (int i = 0; i < thresholdSegments.size(); i++) {
            ThresholdTimeSegment segment = thresholdSegments.get(i);
            drawThresholdTimeSegmentWithAnimation(canvas, segment, xAxisPosition, trans, i, phaseX, minx);
        }

        Log.i(TAG, "X轴阈值时间段标注绘制完成");
    }

    /**
     * 为柱状图绘制X轴标注
     * @param canvas 画布
     * @param barDataSet 柱状图数据集
     * @param minx 最小X值
     * @param count 数据数量
     * @param phaseX 动画相位
     * @param trans 坐标转换器
     */
//    public void drawBarChartXAxisAnnotation(Canvas canvas, IBarDataSet barDataSet,
//                                          int minx, int count, float phaseX, Transformer trans) {
//
//        if (!mDrawXAxisAnnotation) {
//            Log.i(TAG, "柱状图X轴标注已禁用，跳过绘制");
//            return;
//        }
//
////        if (!(barDataSet instanceof ThresholdBarDataSet)) {
////            Log.i(TAG, "不是ThresholdBarDataSet，跳过柱状图X轴标注");
////            return;
////        }
//
//        ThresholdBarDataSet thresholdBarDataSet = (ThresholdBarDataSet) barDataSet;
//
////        if (!thresholdBarDataSet.hasValidThresholdData(minx, count)) {
////            Log.i(TAG, "柱状图X轴标注跳过绘制（没有有效的阈值数据>0）");
////            return;
////        }
//
//        Log.i(TAG, "开始绘制柱状图X轴标注");
//
//        // 获取阈值时间段
//        List<ThresholdBarDataSet.ThresholdTimeSegment> thresholdSegments =
//            thresholdBarDataSet.getThresholdTimeSegments(minx, count);
//
//        if (thresholdSegments.isEmpty()) {
//            Log.i(TAG, "没有找到柱状图阈值时间段");
//            return;
//        }
//
//        Log.i(TAG, String.format("找到 %d 个柱状图阈值时间段，动画相位=%.2f", thresholdSegments.size(), phaseX));
//
//        // 获取X轴的位置信息
//        XAxisPosition xAxisPosition = getXAxisPosition(canvas, trans);
//
//        // 绘制每个阈值时间段
//        for (int i = 0; i < thresholdSegments.size(); i++) {
//            ThresholdBarDataSet.ThresholdTimeSegment segment = thresholdSegments.get(i);
//            drawBarThresholdTimeSegmentWithAnimation(canvas, segment, xAxisPosition, trans, i, phaseX, minx);
//        }
//
//        Log.i(TAG, "柱状图X轴标注绘制完成");
//    }

    /**
     * 绘制柱状图的单个阈值时间段（支持动画）
     */
    private void drawBarThresholdTimeSegmentWithAnimation(Canvas canvas, ThresholdBarDataSet.ThresholdTimeSegment segment,
                                                        XAxisPosition xAxisPosition, Transformer trans,
                                                        int segmentIndex, float phaseX, int minx) {

        // 将数据坐标转换为像素坐标
        float[] startPoint = new float[]{segment.startX, 0};
        float[] endPoint = new float[]{segment.endX, 0};
        trans.pointValuesToPixel(startPoint);
        trans.pointValuesToPixel(endPoint);

        float startPixelX = startPoint[0];
        float endPixelX = endPoint[0];

        // 优化动画效果：从图表的实际起始位置开始
        float[] chartStartPoint = new float[]{minx, 0};
        trans.pointValuesToPixel(chartStartPoint);
        float chartStartPixelX = chartStartPoint[0];

        // 计算当前动画应该显示到的X位置
        float totalVisibleWidth = endPixelX - chartStartPixelX;
        float animatedVisibleWidth = totalVisibleWidth * phaseX;
        float animatedRightEdge = chartStartPixelX + animatedVisibleWidth;

        // 计算这个时间段在动画中的可见部分
        float visibleStartX = Math.max(startPixelX, chartStartPixelX);
        float visibleEndX = Math.min(endPixelX, animatedRightEdge);

        // 如果动画还没有到达这个时间段，跳过绘制
        if (visibleEndX <= visibleStartX) {
            return;
        }

        // 保存画笔状态
        Paint.Style previousStyle = mRenderPaint.getStyle();
        int previousColor = mRenderPaint.getColor();

        try {
            // 绘制阈值时间段（不使用透明度变化）
            mRenderPaint.setStyle(Paint.Style.FILL);
            mRenderPaint.setColor(getThresholdAnnotationColor());
            mRenderPaint.setAntiAlias(true);

            // 使用 SRC_OVER 模式确保绘制在最上层
            mRenderPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_OVER));

            // 绘制动画化的矩形（只绘制可见部分）
            canvas.drawRect(visibleStartX, xAxisPosition.annotationTop,
                          visibleEndX, xAxisPosition.annotationBottom, mRenderPaint);

            // 恢复混合模式
            mRenderPaint.setXfermode(null);

            Log.i(TAG, String.format("绘制柱状图动画阈值标注 %d: 完整范围=%.1f-%.1f, 可见范围=%.1f-%.1f, 动画进度=%.2f",
                  segmentIndex, startPixelX, endPixelX, visibleStartX, visibleEndX, phaseX));

        } finally {
            // 恢复画笔状态
            mRenderPaint.setStyle(previousStyle);
            mRenderPaint.setColor(previousColor);
        }
    }

    /**
     * 生成阈值时间段列表
     */
    private List<ThresholdTimeSegment> generateThresholdTimeSegments(ILineDataSet thresholdDataSet,
                                                                   int minx, int count, float phaseY) {
        List<ThresholdTimeSegment> segments = new ArrayList<>();
        ThresholdTimeSegment currentSegment = null;

        for (int x = minx; x < count; x++) {
            Entry thresholdEntry = thresholdDataSet.getEntryForIndex(x);
            if (thresholdEntry == null) continue;

            float xValue = thresholdEntry.getXIndex();
            float thresholdValue = thresholdEntry.getVal();

            // 判断阈值是否大于0（只有大于0的阈值才有意义）
            boolean hasThreshold = (thresholdValue > 0);

            if (hasThreshold) {
                if (currentSegment == null) {
                    // 开始新的阈值时间段
                    currentSegment = new ThresholdTimeSegment();
                    currentSegment.startX = xValue;
                    currentSegment.startIndex = x;
                }
                // 更新当前时间段的结束位置
                currentSegment.endX = xValue;
                currentSegment.endIndex = x;
                currentSegment.maxThreshold = Math.max(currentSegment.maxThreshold, thresholdValue);
            } else {
                if (currentSegment != null) {
                    // 结束当前阈值时间段
                    segments.add(currentSegment);
                    currentSegment = null;
                }
            }
        }

        // 如果最后还有未结束的时间段，添加到列表中
        if (currentSegment != null) {
            segments.add(currentSegment);
        }

        return segments;
    }

    /**
     * 获取X轴的位置信息
     */
    private XAxisPosition getXAxisPosition(Canvas canvas, Transformer trans) {
        XAxisPosition position = new XAxisPosition();

        // 获取画布高度
        position.canvasHeight = canvas.getHeight();

        // 智能估算X轴的位置
        position.xAxisY = getSmartXAxisPosition(canvas, trans);

        // 标注区域在X轴标签下方
        position.annotationTop = position.xAxisY + mXAxisAnnotationGap;
        position.annotationBottom = position.xAxisY + mXAxisAnnotationGap + mXAxisAnnotationHeight;

        return position;
    }

    /**
     * 智能检测X轴的实际位置
     * 修正：返回X轴线位置，而不是标签底部位置
     */
    private float getSmartXAxisPosition(Canvas canvas, Transformer trans) {
        try {
            // 通过坐标转换获取Y=0的像素位置（这就是X轴线的位置）
            float[] zeroPoint = new float[]{0, 0};
            trans.pointValuesToPixel(zeroPoint);
            float xAxisLineY = zeroPoint[1];

            // 验证计算结果是否合理
            int canvasHeight = canvas.getHeight();
            if (xAxisLineY > 0 && xAxisLineY < canvasHeight) {
                Log.i(TAG, String.format("检测到X轴线位置: %.1f", xAxisLineY));
                // 直接返回X轴线位置，标注将在X轴线下方
                return xAxisLineY;
            } else {
                Log.w(TAG, String.format("坐标转换结果异常: %.1f，使用估算位置", xAxisLineY));
            }
        } catch (Exception e) {
            Log.w(TAG, "智能检测X轴位置失败，使用估算位置", e);
        }

        // 备用方案：估算X轴线位置
        int canvasHeight = canvas.getHeight();
        float estimatedXAxisLineY = canvasHeight - 120f; // 估算X轴线位置（考虑底部边距和标签空间）
        Log.i(TAG, String.format("使用估算X轴线位置: %.1f", estimatedXAxisLineY));
        return estimatedXAxisLineY;
    }

    /**
     * 绘制单个阈值时间段（支持动画）
     * 优化：去掉透明度变化，动画从图表起始位置开始
     */
    private void drawThresholdTimeSegmentWithAnimation(Canvas canvas, ThresholdTimeSegment segment,
                                                     XAxisPosition xAxisPosition, Transformer trans,
                                                     int segmentIndex, float phaseX, int minx) {

        // 将数据坐标转换为像素坐标
        float[] startPoint = new float[]{segment.startX, 0};
        float[] endPoint = new float[]{segment.endX, 0};
        trans.pointValuesToPixel(startPoint);
        trans.pointValuesToPixel(endPoint);

        float startPixelX = startPoint[0];
        float endPixelX = endPoint[0];

        // 优化动画效果：从图表起始位置开始，与图表数据保持一致
        // 计算图表的实际起始位置（使用minx对应的像素位置）
        float[] chartStartPoint = new float[]{minx, 0}; // 使用实际的数据起始位置
        trans.pointValuesToPixel(chartStartPoint);
        float chartStartPixelX = chartStartPoint[0];

        // 计算当前动画应该显示到的X位置
        float totalVisibleWidth = endPixelX - chartStartPixelX;
        float animatedVisibleWidth = totalVisibleWidth * phaseX;
        float animatedRightEdge = chartStartPixelX + animatedVisibleWidth;

        // 计算这个时间段在动画中的可见部分
        float visibleStartX = Math.max(startPixelX, chartStartPixelX);
        float visibleEndX = Math.min(endPixelX, animatedRightEdge);

        // 如果动画还没有到达这个时间段，跳过绘制
        if (visibleEndX <= visibleStartX) {
            return;
        }

        // 保存画笔状态
        Paint.Style previousStyle = mRenderPaint.getStyle();
        int previousColor = mRenderPaint.getColor();

        try {
            // 绘制阈值时间段（不使用透明度变化）
            mRenderPaint.setStyle(Paint.Style.FILL);
            mRenderPaint.setColor(getThresholdAnnotationColor());
            mRenderPaint.setAntiAlias(true);

            // 使用 SRC_OVER 模式确保绘制在最上层
            mRenderPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_OVER));

            // 绘制动画化的矩形（只绘制可见部分）
            canvas.drawRect(visibleStartX, xAxisPosition.annotationTop,
                          visibleEndX, xAxisPosition.annotationBottom, mRenderPaint);

            // 恢复混合模式
            mRenderPaint.setXfermode(null);

            Log.i(TAG, String.format("绘制动画阈值标注 %d: 完整范围=%.1f-%.1f, 可见范围=%.1f-%.1f, 动画进度=%.2f",
                  segmentIndex, startPixelX, endPixelX, visibleStartX, visibleEndX, phaseX));

        } finally {
            // 恢复画笔状态
            mRenderPaint.setStyle(previousStyle);
            mRenderPaint.setColor(previousColor);
        }
    }

    /**
     * 绘制单个阈值时间段（无动画版本，保持向后兼容）
     */
    private void drawThresholdTimeSegment(Canvas canvas, ThresholdTimeSegment segment,
                                        XAxisPosition xAxisPosition, Transformer trans, int segmentIndex) {
        // 调用动画版本，phaseX=1.0表示完全显示，minx=0作为默认值
        drawThresholdTimeSegmentWithAnimation(canvas, segment, xAxisPosition, trans, segmentIndex, 1.0f, 0);
    }

    /**
     * 获取阈值标注的填充颜色（使用color_tip_10）
     */
    private int getThresholdAnnotationColor() {
        if (mContext != null) {
            try {
                // 使用colors.xml中的color_tip_10
                int colorResId = mContext.getResources().getIdentifier("color_tip_10", "color", mContext.getPackageName());
                if (colorResId != 0) {
                    int color = ContextCompat.getColor(mContext, colorResId);
                    // 强制设置为不透明，避免被X轴标签覆盖
                    int opaqueColor = color | 0x80000000;
                    return opaqueColor;
                }
            } catch (Exception e) {
                Log.w(TAG, "获取color_tip_10失败，使用默认颜色", e);
            }
        }

        return 0x806FCEF4;
    }

    // ==================== 内部数据类 ====================

    /**
     * 阈值时间段数据类
     */
    private static class ThresholdTimeSegment {
        float startX;           // 开始X坐标
        float endX;             // 结束X坐标
        int startIndex;         // 开始索引
        int endIndex;           // 结束索引
        float maxThreshold;     // 该时间段内的最大阈值

        public ThresholdTimeSegment() {
            this.maxThreshold = 0f;
        }
    }

    /**
     * X轴位置信息数据类
     */
    private static class XAxisPosition {
        int canvasHeight;       // 画布高度
        float xAxisY;           // X轴线的Y坐标
        float annotationTop;    // 标注区域顶部Y坐标
        float annotationBottom; // 标注区域底部Y坐标
    }

    // ==================== 柱状图X轴标注方法 ====================

    /**
     * 为柱状图绘制X轴标注（简化版本，用于测试）
     * 假设每个BarEntry都有peakshaving字段，默认为true时进行标注
     */
    public void drawBarChartXAxisAnnotation(Canvas canvas, IBarDataSet barDataSet,
                                          int minx, int count, float phaseX, Transformer trans,
                                          float barWidth) {

        if (!mDrawXAxisAnnotation) {
            Log.i(TAG, "柱状图X轴标注已禁用，跳过绘制");
            return;
        }

        if (barDataSet == null || barDataSet.getEntryCount() == 0) {
            Log.i(TAG, "柱状图数据集为空，跳过X轴标注");
            return;
        }

        Log.i(TAG, "开始绘制柱状图X轴标注（简化版本）");

        // 简化逻辑：假设所有数据都需要标注（peakshaving=true）
        List<SimpleBarTimeSegment> segments = generateSimpleBarTimeSegments(barDataSet, minx, count);

        if (segments.isEmpty()) {
            Log.i(TAG, "没有找到需要标注的柱状图时间段");
            return;
        }

        Log.i(TAG, String.format("找到 %d 个柱状图标注时间段", segments.size()));

        // 获取X轴的位置信息
        XAxisPosition xAxisPosition = getXAxisPosition(canvas, trans);

        // 绘制每个时间段
        for (int i = 0; i < segments.size(); i++) {
            SimpleBarTimeSegment segment = segments.get(i);
            drawSimpleBarTimeSegment(canvas, segment, xAxisPosition, trans, i, phaseX, minx, barWidth);
        }

        Log.i(TAG, "柱状图X轴标注绘制完成");
    }

    /**
     * 生成柱状图标注段（每个柱子单独判断peakshaving）
     */
    private List<SimpleBarTimeSegment> generateSimpleBarTimeSegments(IBarDataSet barDataSet, int minx, int count) {
        List<SimpleBarTimeSegment> segments = new ArrayList<>();

        for (int i = minx; i < Math.min(minx + count, barDataSet.getEntryCount()); i++) {
            BarEntry entry = barDataSet.getEntryForIndex(i);
            if (entry == null) continue;

            // 简化逻辑：假设每个柱子都有peakshaving=true（测试用）
            // TODO: 后续可以从entry.getData()或其他字段获取peakshaving值
            boolean hasPeakshaving = true; // 默认为true，用于测试

            if (hasPeakshaving) {
                // 为每个柱子创建单独的标注段
                SimpleBarTimeSegment segment = new SimpleBarTimeSegment();
                segment.startX = entry.getXIndex();
                segment.endX = entry.getXIndex(); // 单个柱子，起始和结束X相同
                segment.startIndex = i;
                segment.endIndex = i;
                segments.add(segment);
            }
        }

        return segments;
    }

    /**
     * 绘制单个柱子的标注
     */
    private void drawSimpleBarTimeSegment(Canvas canvas, SimpleBarTimeSegment segment,
                                        XAxisPosition xAxisPosition, Transformer trans,
                                        int segmentIndex, float phaseX, int minx, float actualBarWidth) {

        // 将数据坐标转换为像素坐标
        float[] centerPoint = new float[]{segment.startX, 0};
        trans.pointValuesToPixel(centerPoint);

        float centerPixelX = centerPoint[0];

        // 使用传入的实际柱状图宽度
        float barWidth = actualBarWidth;

        // 计算标注区域的左右边界（与柱子完全对齐）
        float leftX = centerPixelX - barWidth / 2;
        float rightX = centerPixelX + barWidth / 2;

        // 保存画笔状态
        Paint.Style previousStyle = mRenderPaint.getStyle();
        int previousColor = mRenderPaint.getColor();

        try {
            // 绘制单个柱子的标注（使用绿色区分折线图标注）
            mRenderPaint.setStyle(Paint.Style.FILL);
            mRenderPaint.setColor(0xFF00FF00); // 绿色，用于区分折线图标注
            mRenderPaint.setAntiAlias(true);

            // 绘制矩形（对应单个柱子的底部）
            canvas.drawRect(leftX, xAxisPosition.annotationTop,
                          rightX, xAxisPosition.annotationBottom, mRenderPaint);

            Log.i(TAG, String.format("绘制柱子标注 %d: X=%.1f, 宽度=%.1f, 范围=%.1f-%.1f",
                  segmentIndex, centerPixelX, barWidth, leftX, rightX));

        } finally {
            // 恢复画笔状态
            mRenderPaint.setStyle(previousStyle);
            mRenderPaint.setColor(previousColor);
        }
    }

    /**
     * 简化的柱状图时间段数据类
     */
    private static class SimpleBarTimeSegment {
        float startX;
        float endX;
        int startIndex;
        int endIndex;
    }












}
