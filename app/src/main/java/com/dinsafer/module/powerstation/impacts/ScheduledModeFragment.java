package com.dinsafer.module.powerstation.impacts;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentScheduledModeBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.main.entry.MainWidgetBean;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.RMFKeyValueModel;
import com.dinsafer.module.powerstation.dialog.PVPreferenceSheet;
import com.dinsafer.module.powerstation.dialog.ReserveModeFunctionPopup;
import com.dinsafer.module.powerstation.event.ChargeModeEvent;
import com.dinsafer.module.powerstation.event.RecyclerViewDragOnTouchEvent;
import com.dinsafer.module.powerstation.event.SameWithWeekdaysEvent;
import com.dinsafer.module.powerstation.event.ScheduledModeEvent;
import com.dinsafer.module.powerstation.widget.schedule_mode_view.ScheduledModeBean;
import com.dinsafer.module.powerstation.widget.segmentbar.HorizontalSegmentRangeBar;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.StringUtil;
import com.google.android.material.bottomsheet.BottomSheetBehavior;


import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ScheduledModeFragment extends PSConnectLoadingFragment<FragmentScheduledModeBinding>
        implements IDeviceCallBack {

    private CommonPagerAdapter mDaysTypeAdapter;
    private ArrayList<BaseFragment> mDaysFragments;
    private int mDaysCurrentPosition;

    private CommonPagerAdapter mModeAdapter;
    private ArrayList<BaseFragment> mModeFragments;

    private BottomSheetBehavior mBottomSheetBehavior;

    private int mEmergencyReserve = 30;
    private int mSmartReserve = 70;

    private ArrayList<Integer> mWeekdays = new ArrayList<>();
    private ArrayList<Integer> mWeekend = new ArrayList<>();
    private List<Segment> mSegments = new ArrayList<>();
    private boolean isDealData;
    private boolean isGridToBattery;

    private boolean isEdit;
    private ReserveModeFunctionPopup mReserveModeFunctionPopup;
    private PVPreferenceSheet mPvPreferenceSheet;
    private int mPVPreference = -1;
    private boolean isSupportPVAndLocation;

    public static ScheduledModeFragment newInstance(int from, String deviceId, String subcategory, boolean isGridToBattery) {
        ScheduledModeFragment fragment = new ScheduledModeFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        bundle.putBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY, isGridToBattery);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_scheduled_mode;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        initTitle();
        setSegment();
        initDaysTypeVp();
        initBottomSheet();
        initModeVp();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        getScheduledReserveMode();
        if (isSupportPVAndLocation) {
            BmtManager.getInstance().getPVDist(mPSDevice);
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mBinding.tvWeekdays.setOnClickListener(v -> setIndicatorSelected(0, true));

        mBinding.tvWeekends.setOnClickListener(v -> setIndicatorSelected(1, true));

        mBinding.llSwitch.setOnClickListener((v) -> {
            if (mBottomSheetBehavior != null) {
                mBottomSheetBehavior.setState(mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_COLLAPSED ?
                        BottomSheetBehavior.STATE_EXPANDED : BottomSheetBehavior.STATE_COLLAPSED);
            }
        });

        mBinding.viewBlank.setOnClickListener(view -> {
            if (mBottomSheetBehavior != null) {
                mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
            }
        });

        mBinding.tvSame.setOnClickListener(view -> sameWithWeekdays());

        mBinding.ivCheck.setOnClickListener(view -> sameWithWeekdays());
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        Bundle bundle = getArguments();
        isGridToBattery = bundle.getBoolean(PSKeyConstant.KEY_GRID_TO_BATTERY);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
            isSupportPVAndLocation = BmtUtil.isSupportPVAndLocation(mPSDevice);
        }
    }


    /**
     * 初始化标题
     */
    private void initTitle() {
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.ps_is_scheduled_mode));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarRightText.setLocalText(getString(R.string.ps_ev_save));
        mBinding.commonBar.commonBarRightText.setTextColor(getResources().getColor(R.color.color_brand_text));
        mBinding.commonBar.commonBarRightText.setBackgroundResource(0);
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);

        // 保存
        mBinding.commonBar.commonBarRightText.setOnClickListener(v -> {
            List<ScheduledModeBean> weekdaysData = ((ScheduledModeSonFragment) mDaysFragments.get(0)).getScheduleModeData();
            List<ScheduledModeBean> weekendsData = ((ScheduledModeSonFragment) mDaysFragments.get(1)).getScheduleModeData();
            ArrayList<Integer> weekdays = new ArrayList<>();
            for (ScheduledModeBean scheduledModeBean : weekdaysData) {
                int mode = scheduledModeBean.getMode();
                weekdays.add(mode < 0 ? -(100 - scheduledModeBean.getPercentage()) : scheduledModeBean.getPercentage());
            }
            ArrayList<Integer> weekend = new ArrayList<>();
            for (ScheduledModeBean scheduledModeBean : weekendsData) {
                int mode = scheduledModeBean.getMode();
                weekend.add(mode < 0 ? -(100 - scheduledModeBean.getPercentage()) : scheduledModeBean.getPercentage());
            }
            mWeekdays.clear();
            mWeekdays.addAll(weekdays);
            mWeekend.clear();
            mWeekend.addAll(weekend);
            setScheduledMode();
        });

        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> showFunctionPopup());
    }

    private void showFunctionPopup() {
        if (mReserveModeFunctionPopup == null) {
            mReserveModeFunctionPopup = new ReserveModeFunctionPopup(getContext(), 1, mPVPreference, isSupportPVAndLocation);
            mReserveModeFunctionPopup.setBindModelOnBindItemClickListener(new OnBindItemClickListener<BindModel>() {
                @Override
                public void onItemClick(View v, int position, BindModel model) {
                    if (model instanceof RMFKeyValueModel) {
                        RMFKeyValueModel rmfKeyValueModel = (RMFKeyValueModel) model;
                        String key = rmfKeyValueModel.getKey();
                        if (key == null) return;
                        if (key.equals(getString(R.string.Edit_Battery_Range))) {
                            showEditStatusView(true);
                            mReserveModeFunctionPopup.dismiss();
                        } else if (key.equals(getString(R.string.PV_Distribution_Preference))) {
                            showBottomSheet();
                            mReserveModeFunctionPopup.dismiss();
                        }
                    }
                }
            });
            mReserveModeFunctionPopup.setOnDismissListener(() -> backgroundAlpha(1f));
        }
        if (!mReserveModeFunctionPopup.isShowing()) {
            mReserveModeFunctionPopup.showAsDropDown(mBinding.commonBar.commonBarRightIcon, -(ScreenUtils.getScreenWidth(getContext()) - DensityUtils.dp2px(getContext(), 49)), 0, Gravity.END);
            backgroundAlpha(0.5f);
        }
    }

    private void backgroundAlpha(float f) {
        WindowManager.LayoutParams lp = getDelegateActivity().getWindow().getAttributes();
        lp.alpha = f;
        getDelegateActivity().getWindow().setAttributes(lp);
    }

    private void showBottomSheet() {
        if (mPvPreferenceSheet == null) {
            mPvPreferenceSheet = new PVPreferenceSheet(1, type -> {
                showTimeOutLoadinFramgmentWithErrorAlert();
                BmtManager.getInstance().setPVDist(mPSDevice, 0, type);
            });
        }
        mPvPreferenceSheet.show(getChildFragmentManager());
    }

    private void showEditStatusView(boolean isEdit) {
        this.isEdit = isEdit;
        mBinding.commonBar.commonBarRightIcon.setVisibility(isEdit ? View.GONE : View.VISIBLE);
        mBinding.commonBar.commonBarRightText.setVisibility(isEdit ? View.VISIBLE : View.GONE);
        mBinding.segmentRangeBar.setDrag(isEdit);
        for (BaseFragment fragment : mDaysFragments) {
            ((ScheduledModeSonFragment) fragment).setEdit(isEdit);
        }
        mBinding.commonBar.commonBarTitle.setLocalText(isEdit ? getString(R.string.Edit_Battery_Range) :
                getString(R.string.ps_is_scheduled_mode));
        mBinding.tvSame.setVisibility(mDaysCurrentPosition == 1 && isEdit ?
                View.VISIBLE : View.GONE);
        mBinding.ivCheck.setVisibility(mDaysCurrentPosition == 1 && isEdit ?
                View.VISIBLE : View.GONE);
    }

    private void initDaysTypeVp() {
        mBinding.vpDaysType.post(() -> {
            ViewGroup.LayoutParams layoutParams = mBinding.vpDaysType.getLayoutParams();
            layoutParams.height = mBinding.col.getHeight() - DensityUtil.dp2px(getContext(), 130);
            mBinding.vpDaysType.setLayoutParams(layoutParams);
            float startX = mBinding.vpDaysType.getWidth() / 2 - DensityUtil.dp2px(getContext(), 21);
            float endX = startX + DensityUtil.dp2px(getContext(), 105);
            mBinding.vpDaysType.setLimitedPoint(startX, endX);
        });
        mDaysFragments = new ArrayList<>();
        mDaysFragments.add(ScheduledModeSonFragment.newInstance(0, mEmergencyReserve, mSmartReserve, mWeekdays, isGridToBattery));
        ScheduledModeSonFragment weekendFragment = ScheduledModeSonFragment.newInstance(1, mEmergencyReserve,
                mSmartReserve, mBinding.ivCheck.isSelected() ? mWeekdays : mWeekend, isGridToBattery);
        weekendFragment.setCacheData(mWeekend);
        mDaysFragments.add(weekendFragment);
        mDaysTypeAdapter = new CommonPagerAdapter(getChildFragmentManager(), mDaysFragments);
        mBinding.vpDaysType.setAdapter(mDaysTypeAdapter);
        mBinding.vpDaysType.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                setIndicatorSelected(position, false);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        mBinding.vpDaysType.setOffscreenPageLimit(2);
//        EventBus.getDefault().post(new ScheduledModeEvent(mDaysCurrentPosition, mEmergencyReserve, mSmartReserve));
    }

    private void setIndicatorSelected(int index, boolean isSelf) {
        mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
        if (index == mDaysCurrentPosition) return;
        mDaysCurrentPosition = index;
//        if (mDaysCurrentPosition == 0) {
//            mBinding.segmentRangeBar.setDrag(true);
//        } else {
//            mBinding.segmentRangeBar.setDrag(!mBinding.ivCheck.isSelected());
//        }
        mBinding.tvWeekdays.setTextColor(mDaysCurrentPosition == 0 ?
                getColor(R.color.color_white_01) :
                getColor(R.color.color_white_02));

        mBinding.tvWeekends.setTextColor(mDaysCurrentPosition == 1 ?
                getColor(R.color.color_white_01) :
                getColor(R.color.color_white_02));

        mBinding.tvSame.setVisibility(mDaysCurrentPosition == 1 && isEdit ?
                View.VISIBLE : View.GONE);
        mBinding.ivCheck.setVisibility(mDaysCurrentPosition == 1 && isEdit ?
                View.VISIBLE : View.GONE);
        mBinding.viewIndicator.animate().translationX(index == 0 ?
                        0 :
                        DensityUtil.dp2px(getContext(), 78))
                .setListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animator) {
                        if (isSelf) {
                            mBinding.vpDaysType.setCurrentItem(index);
                        }
                    }

                    @Override
                    public void onAnimationEnd(Animator animator) {

                    }

                    @Override
                    public void onAnimationCancel(Animator animator) {

                    }

                    @Override
                    public void onAnimationRepeat(Animator animator) {

                    }
                })
                .setDuration(200)
                .start();
    }


    private boolean needRotate = true;

    private void initBottomSheet() {
        mBottomSheetBehavior = BottomSheetBehavior.from(mBinding.rlBottom);
        mBottomSheetBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                    mBinding.viewBlank.setVisibility(View.VISIBLE);
                }

                if (newState == BottomSheetBehavior.STATE_COLLAPSED) {
                    mBinding.viewBlank.setVisibility(View.GONE);
                }
            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                if (slideOffset == 0f) {
                    if (!needRotate) {
                        ObjectAnimator.ofFloat(mBinding.ivSwitch, "rotation", 180, 0).start();
                        needRotate = true;
                    }
                } else {
                    if (needRotate) {
                        ObjectAnimator.ofFloat(mBinding.ivSwitch, "rotation", 0, 180).start();
                        needRotate = false;
                    }
                }
            }
        });
    }

    private void setSegment() {

        mSegments.add(new Segment(0.0f, 0.02f, getString(R.string.power_battery_bar_status_text_5), getColor(R.color.power_station_battery_color_5)));
        mSegments.add(new Segment(0.02f, 0.12f, getString(R.string.power_battery_bar_status_text_4), getColor(R.color.power_station_battery_color_4)));
        mSegments.add(new Segment(0.12f, mEmergencyReserve / 100f, getString(R.string.power_battery_bar_status_text_3), getColor(R.color.power_station_battery_color_3)));
        mSegments.add(new Segment(mEmergencyReserve / 100f, mSmartReserve / 100f, getString(R.string.power_battery_bar_status_text_2), getColor(R.color.power_station_battery_color_2)));
        mSegments.add(new Segment(mSmartReserve / 100f, 1.0f, getString(R.string.power_battery_bar_status_text_1), getColor(R.color.power_station_battery_color_1)));
        mBinding.segmentRangeBar.setMinProgress(mEmergencyReserve * 1.0f);
        mBinding.segmentRangeBar.setMaxProgress(mSmartReserve * 1.0f);
        mBinding.segmentRangeBar.setMaxLimitedProgress(99f);
        mBinding.segmentRangeBar.setSegmentRanges(mSegments);
        mBinding.segmentRangeBar.setProgressListener(new HorizontalSegmentRangeBar.OnProgressChangedListener() {
            @Override
            public void onProgress1Changed(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {
                mEmergencyReserve = progress;
                EventBus.getDefault().post(new ScheduledModeEvent(mDaysCurrentPosition, true, mEmergencyReserve));
            }

            @Override
            public void getProgress1OnActionUp(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress1OnFinally(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onProgress2Changed(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {
                mSmartReserve = progress;
                EventBus.getDefault().post(new ScheduledModeEvent(mDaysCurrentPosition, false, mSmartReserve));
            }

            @Override
            public void getProgress2OnActionUp(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat) {

            }

            @Override
            public void getProgress2OnFinally(HorizontalSegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser) {

            }

            @Override
            public void onAlert() {
                /*
                 * 震动的方式
                 */
//                vibrator.vibrate(200);//振动两毫秒
                // 下边是可以使震动有规律的震动 -1：表示不重复 0：循环的震动
//                long[] pattern = {200, 2000, 2000, 200, 200, 200};
//                vibrator.vibrate(pattern, -1);
            }
        });
    }

    private void initModeVp() {
        mModeFragments = new ArrayList<>();
        String[] modeSuggestions = getContext().getResources().getStringArray(R.array.ps_scheduled_mode_suggestion_array);
        for (int i = 0; i < modeSuggestions.length; i++) {
            mModeFragments.add(ModeSuggestionFragment.newInstance(i,
                    modeSuggestions[i], 1,
                    new ModeFragmentCreatedListener()));
        }

        mModeAdapter = new CommonPagerAdapter(getChildFragmentManager(), mModeFragments);
        mBinding.vpMode.setAdapter(mModeAdapter);
        mBinding.vpMode.setOffscreenPageLimit(5);
        mBinding.indicator.setupViewpager(mBinding.vpMode);
    }

    private void sameWithWeekdays() {
        boolean isSelected = mBinding.ivCheck.isSelected();
//        mBinding.segmentRangeBar.setDrag(isSelected);
        mBinding.ivCheck.setSelected(!isSelected);
        SameWithWeekdaysEvent event = new SameWithWeekdaysEvent(0, !isSelected);
        EventBus.getDefault().post(event);
    }

    private void getScheduledReserveMode() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_SCHEDULE_RESERVE_MODE);
            showTimeOutLoadinFramgmentWithBack();
            mPSDevice.submit(map);
        }
    }

    private void setViewVisible() {
        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.llSwitch.setVisibility(View.VISIBLE);
        mBinding.clDaysType.setVisibility(View.VISIBLE);
        mBinding.col.setVisibility(View.VISIBLE);
    }

    /**
     * 设置为定时模式 (Scheduled Mode)
     */
    private void setScheduledMode() {
        if (mPSDevice != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_RESERVE_MODE);
            params.put(BmtDataKey.RESERVE_MODE, 2);
            params.put(BmtDataKey.SMART, mSmartReserve);
            params.put(BmtDataKey.EMERGENCY, mEmergencyReserve);
            if (CollectionUtil.isListNotEmpty(mWeekdays)) {
                int[] weekdaysArr = new int[mWeekdays.size()];
                for (int i = 0; i < mWeekdays.size(); i++) {
                    weekdaysArr[i] = mWeekdays.get(i);
                }
                params.put(BmtDataKey.WEEKDAYS, weekdaysArr);
            }
            if (CollectionUtil.isListNotEmpty(mWeekend)) {
                int[] weekendArr = new int[mWeekend.size()];
                for (int i = 0; i < mWeekend.size(); i++) {
                    weekendArr[i] = mWeekend.get(i);
                }
                params.put(BmtDataKey.WEEKEND, weekendArr);
            }
            params.put(BmtDataKey.SYNC, mBinding.ivCheck.isSelected());
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {
                            case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                                if (!isDealData) {
                                    isDealData = true;
                                    closeTimeOutLoadinFramgmentWithErrorAlert();
                                    mEmergencyReserve = DeviceHelper.getInt(result, BmtDataKey.EMERGENCY, -1);
                                    mSmartReserve = DeviceHelper.getInt(result, BmtDataKey.SMART, -1);
                                    ((ScheduledModeSonFragment) mDaysFragments.get(0)).setEmergencyReserve(mEmergencyReserve);
                                    ((ScheduledModeSonFragment) mDaysFragments.get(0)).setSmartReserve(mSmartReserve);
                                    ((ScheduledModeSonFragment) mDaysFragments.get(1)).setEmergencyReserve(mEmergencyReserve);
                                    ((ScheduledModeSonFragment) mDaysFragments.get(1)).setSmartReserve(mSmartReserve);
                                    mSegments.get(2).setMaxValue(mEmergencyReserve / 100f);
                                    mSegments.get(3).setMinValue(mEmergencyReserve / 100f);
                                    mSegments.get(3).setMaxValue(mSmartReserve / 100f);
                                    mSegments.get(4).setMinValue(mSmartReserve / 100f);
                                    Integer[] weekdays = (Integer[]) MapUtils.get(result, BmtDataKey.WEEKDAYS, null);
                                    Integer[] weekend = (Integer[]) MapUtils.get(result, BmtDataKey.WEEKEND, null);
                                    boolean isSync = DeviceHelper.getBoolean(result, BmtDataKey.SYNC, false);
                                    mBinding.ivCheck.setSelected(isSync);
                                    if (weekdays != null) {
                                        mWeekdays.clear();
                                        for (int i = 0; i < weekdays.length; i++) {
                                            mWeekdays.add(weekdays[i]);
                                        }
                                    }
                                    if (weekend != null) {
                                        mWeekend.clear();
                                        for (int i = 0; i < weekend.length; i++) {
                                            mWeekend.add(weekend[i]);
                                        }
                                    }
                                    if (CollectionUtil.isListNotEmpty(mDaysFragments)) {
                                        ((ScheduledModeSonFragment) mDaysFragments.get(0)).setScheduledData(mWeekdays);
                                        ((ScheduledModeSonFragment) mDaysFragments.get(1)).setScheduledData(
                                                isSync ? mWeekdays : mWeekend
                                        );
                                        ((ScheduledModeSonFragment) mDaysFragments.get(1)).setCacheData(mWeekend);
                                    }
                                    setViewVisible();
                                    mBinding.segmentRangeBar.setMinProgress(mEmergencyReserve * 1.0f);
                                    mBinding.segmentRangeBar.setMaxProgress(mSmartReserve * 1.0f);
                                    mBinding.segmentRangeBar.setMinLimitedProgress(0.15f);
                                    mBinding.segmentRangeBar.invalidate();
                                    SameWithWeekdaysEvent syncEvent = new SameWithWeekdaysEvent(1, isSync);
                                    EventBus.getDefault().post(syncEvent);
                                }
                                break;

                            case BmtCmd.SET_RESERVE_MODE:
                                ChargeModeEvent event = new ChargeModeEvent(mDeviceId, mSubcategory, 2, mSmartReserve, mEmergencyReserve);
                                EventBus.getDefault().post(event);
                                showEditStatusView(false);
                                break;

                            case BmtCmd.GET_PV_DIST:
                                mPVPreference = DeviceHelper.getInt(result, BmtDataKey.SCHEDULED, -1);
                                if (mReserveModeFunctionPopup != null) {
                                    mReserveModeFunctionPopup.setPVPreferenceVal(mPVPreference);
                                }
                                break;

                            case BmtCmd.SET_PV_DIST:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                mPVPreference = DeviceHelper.getInt(result, BmtDataKey.SCHEDULED, -1);
                                if (mReserveModeFunctionPopup != null) {
                                    mReserveModeFunctionPopup.setPVPreferenceVal(mPVPreference);
                                }
                                break;
                        }
                    } else {
                        switch (cmd) {
                            case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                                if (!isDealData) {
                                    closeTimeOutLoadinFramgmentWithErrorAlert();
                                    showErrorToast();
                                    isDealData = true;
                                }
                                break;

                            case BmtCmd.SET_PV_DIST:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                showErrorToast();
                                break;
                        }
                    }
                }
            });

        }
    }


    public class ModeFragmentCreatedListener implements ModeSuggestionFragment.OnCreatedListener {

        @Override
        public void onCreated(View view, int position) {
            mBinding.vpMode.setViewPosition(view, position);
        }
    }

    private int getColor(int color) {
        return getResources().getColor(color);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRecyclerViewTouchEvent(RecyclerViewDragOnTouchEvent event) {
        if (mBottomSheetBehavior != null &&
                mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
            mBottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
        }
    }
}
