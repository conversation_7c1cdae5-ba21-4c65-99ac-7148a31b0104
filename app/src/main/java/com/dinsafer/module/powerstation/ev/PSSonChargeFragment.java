package com.dinsafer.module.powerstation.ev;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.dialog.SingleWheelPickerBottomDialog;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsInstantChargeBinding;
import com.dinsafer.dinnet.databinding.FragmentPsSonChargeBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSEVChargeAdapter;
import com.dinsafer.module.powerstation.bean.PSEVChargeBean;
import com.dinsafer.module.powerstation.event.EVBottomBehaviorEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class PSSonChargeFragment extends MyBaseFragment<FragmentPsSonChargeBinding> {

    private static final int SMART_CHARGE = 1;
    private static final int INSTANT_CHARGE = 2;
    // 1. Smart Charge 2.Instant Charge
    private int mTabType;
    private PSEVChargeAdapter mEVChargeAdapter;
    private int mAppliedPosition;

    private ArrayList<SingleWheelPickerBottomDialog.WheelKeyValueBean> mLowerUtilityData = new ArrayList<>();
    private ArrayList<SingleWheelPickerBottomDialog.WheelKeyValueBean> mFixedQuantityData = new ArrayList<>();
    private ArrayList<SingleWheelPickerBottomDialog.WheelKeyValueBean> mFixedDurationData = new ArrayList<>();


    public static PSSonChargeFragment newSmartChargeInstance() {
        return newInstance(SMART_CHARGE);
    }

    public static PSSonChargeFragment newInstantChargeInstance() {
        return newInstance(INSTANT_CHARGE);
    }

    public static PSSonChargeFragment newInstance(int mTabType) {
        PSSonChargeFragment fragment = new PSSonChargeFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_TAB_TYPE, mTabType);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_son_charge;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        EventBus.getDefault().register(this);
        mBinding.viewSelected.getRoot().setBackgroundResource(R.drawable.shape_bg_item_ev_charge_sel);
        initWheelData();
        initRv();
        mBinding.viewSelected.llOperate.setOnClickListener(v -> {
            showWheelPicker(mEVChargeAdapter.getItem(mAppliedPosition), mAppliedPosition);
        });
    }


    @Override
    public void onDestroyView() {
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mTabType = bundle.getInt(PSKeyConstant.KEY_TAB_TYPE);
    }

    private void initWheelData() {
        for (int i = 20; i >= 0; i--) {
            int value = 0;
            if (i < 10) {
                value = (i * 10) - 100;

            } else if (i == 10) {
                value = 0;
            } else {
                value = (i - 10) * 10;
            }
            String key = value <= 0 ? (value + "%") : ("+" + value + "%");
            mLowerUtilityData.add(new SingleWheelPickerBottomDialog.WheelKeyValueBean(key, value));
        }

        for (int i = 20; i > 0; i--) {
            int value = i * 10;
            mFixedQuantityData.add(new SingleWheelPickerBottomDialog.WheelKeyValueBean(String.valueOf(value), value));
        }

        for (int i = 24; i > 0; i--) {
            int value = i;
            mFixedDurationData.add(new SingleWheelPickerBottomDialog.WheelKeyValueBean(String.valueOf(value), value));
        }
    }

    private void initRv() {
        mBinding.rvData.setLayoutManager(new LinearLayoutManager(getDelegateActivity()));
        mEVChargeAdapter = new PSEVChargeAdapter();
        mBinding.rvData.setAdapter(mEVChargeAdapter);
        mEVChargeAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                PSEVChargeBean psevChargeBean = mEVChargeAdapter.getItem(position);
                for (PSEVChargeBean chargeBean : mEVChargeAdapter.getData()) {
                    chargeBean.setSelected(false);
                }
                psevChargeBean.setSelected(true);
                mEVChargeAdapter.notifyDataSetChanged();
            }
        });
        mEVChargeAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                int viewId = view.getId();
                PSEVChargeBean psevChargeBean = mEVChargeAdapter.getItem(position);
                switch (viewId) {
                    case R.id.ll_operate:
                        showWheelPicker(psevChargeBean, position);
                        break;

                    case R.id.tv_go_settings:
//                        getDelegateActivity().addCommonFragment(PSScheduledChargeFragment.newInstance());
                        break;

                    case R.id.ll_status:
                        if (psevChargeBean.getStatus() == -1) {
                            applyCharge(psevChargeBean, position);
                        }
                        break;
                }
            }
        });
        loadChargeData();
    }

    private void applyCharge(PSEVChargeBean psevChargeBean, int position) {
        psevChargeBean.setStatus(0);
        mEVChargeAdapter.notifyItemChanged(position);
        mBinding.rvData.postDelayed(new Runnable() {
            @Override
            public void run() {
                for (PSEVChargeBean chargeBean : mEVChargeAdapter.getData()) {
                    chargeBean.setStatus(-1);
                }
                psevChargeBean.setStatus(1);
                mAppliedPosition = position;
                mEVChargeAdapter.notifyDataSetChanged();
                setSelectValue(mEVChargeAdapter.getItem(position), position);
            }
        }, 2000);
    }

    private void showWheelPicker(PSEVChargeBean psevChargeBean, int position) {
        SingleWheelPickerBottomDialog.Builder builder = new SingleWheelPickerBottomDialog.Builder();
        builder.setRvPosition(position);
        int selectedPosition = 0;
        int type = psevChargeBean.getType();
        switch (type) {
            case PSEVChargeBean.LOWER_UTILITY_RATE:
                for (int i = 0; i < mLowerUtilityData.size(); i++) {
                    SingleWheelPickerBottomDialog.WheelKeyValueBean keyValueBean = mLowerUtilityData.get(i);
                    if (keyValueBean.getKey().equals(psevChargeBean.getValueStr())) {
                        selectedPosition = i;
                        builder.setSelectedPosition(selectedPosition);
                        break;
                    }
                }
                builder.setKeyValueList(mLowerUtilityData);
                break;

            case PSEVChargeBean.FIXED_QUANTITY:
                for (int i = 0; i < mFixedQuantityData.size(); i++) {
                    SingleWheelPickerBottomDialog.WheelKeyValueBean keyValueBean = mFixedQuantityData.get(i);
                    if (keyValueBean.getKey().equals(psevChargeBean.getValueStr())) {
                        selectedPosition = i;
                        builder.setSelectedPosition(selectedPosition);
                        break;
                    }
                }
                builder.setKeyValueList(mFixedQuantityData);
                break;

            case PSEVChargeBean.FIXED_DURATION:
                for (int i = 0; i < mFixedDurationData.size(); i++) {
                    SingleWheelPickerBottomDialog.WheelKeyValueBean keyValueBean = mFixedDurationData.get(i);
                    if (keyValueBean.getKey().equals(psevChargeBean.getValueStr())) {
                        selectedPosition = i;
                        builder.setSelectedPosition(selectedPosition);
                        break;
                    }
                }
                builder.setKeyValueList(mFixedDurationData);
                break;
        }
        builder.setConfirmListener(new SingleWheelPickerBottomDialog.OnConfirmClickListener() {
            @Override
            public void onConfirm(int rvPosition, SingleWheelPickerBottomDialog.WheelKeyValueBean wheelKeyValueBean) {
                psevChargeBean.setValue(wheelKeyValueBean.getValue());
                String unit = "";
                int type = psevChargeBean.getType();
                if (type == PSEVChargeBean.FIXED_QUANTITY) {
                    unit = "kWh";
                }
                if (type == PSEVChargeBean.FIXED_DURATION) {
                    unit = "hour(s)";
                }
                String valueStr = wheelKeyValueBean.getKey();
                if (!TextUtils.isEmpty(unit)) {
                    valueStr = valueStr + " " + unit;
                }
                psevChargeBean.setValueStr(valueStr);
                mEVChargeAdapter.notifyItemChanged(position);
                if (position == mAppliedPosition) {
                    mBinding.viewSelected.tvValue.setLocalText(valueStr);
                }
            }
        });
        builder.build().show(getChildFragmentManager(), SingleWheelPickerBottomDialog.TAG);
    }

    private void loadChargeData() {
        List<PSEVChargeBean> evChargeList = new ArrayList<>();
        if (mTabType == SMART_CHARGE) {
            PSEVChargeBean lowerUtility = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_1),
                    "当市电价格基于今日均价的涨跌符合以下条件，允许EV充电。", PSEVChargeBean.LOWER_UTILITY_RATE);
            lowerUtility.setValue(-30);
            lowerUtility.setValueStr("-30%");
            lowerUtility.setSelected(true);
            lowerUtility.setStatus(1);
            evChargeList.add(lowerUtility);

            PSEVChargeBean solarOnly = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_2),
                    "EV充电功率将被限制在太阳能发电功率以内。", PSEVChargeBean.SOLAR_ONLY);
            evChargeList.add(solarOnly);

            PSEVChargeBean scheduleCharge = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_3),
                    "在预设的时段内允许EV充电。", PSEVChargeBean.SCHEDULE_CHARGE);
            evChargeList.add(scheduleCharge);
            setSelectValue(lowerUtility, 0);
        } else if (mTabType == INSTANT_CHARGE) {
            PSEVChargeBean untilFully = new PSEVChargeBean(getString(R.string.ps_ev_instant_charge_title_1),
                    "充满后车端将自动断开。", PSEVChargeBean.UNTIL_FULLY_CHARGED);
            untilFully.setSelected(true);
            untilFully.setStatus(1);
            evChargeList.add(untilFully);

            PSEVChargeBean fixedCharging = new PSEVChargeBean(getString(R.string.ps_ev_instant_charge_title_2),
                    "EV充电功率将被限制在太阳能发电功率以内。", PSEVChargeBean.FIXED_QUANTITY);
            fixedCharging.setValue(10);
            fixedCharging.setValueStr("10kWh");
            evChargeList.add(fixedCharging);

            PSEVChargeBean fixedDuration = new PSEVChargeBean(getString(R.string.ps_ev_instant_charge_title_2),
                    "预设每次充电的时长。", PSEVChargeBean.FIXED_DURATION);
            fixedDuration.setValue(1);
            fixedDuration.setValueStr("1 hour(s)");
            evChargeList.add(fixedDuration);
            setSelectValue(untilFully, 0);

        }
        mEVChargeAdapter.setNewData(evChargeList);
    }

    private void setSelectValue(PSEVChargeBean chargeBean, int position) {
        if (chargeBean == null) return;
        mAppliedPosition = position;
        mBinding.viewSelected.tvTitle.setLocalText(chargeBean.getTitle());
        mBinding.viewSelected.tvSubtitle.setLocalText(chargeBean.getSubTitle());
        mBinding.viewSelected.tvApplied.setVisibility(chargeBean.getStatus() == 1 ?
                View.VISIBLE : View.GONE);
        int type = chargeBean.getType();
        switch (type) {
            case PSEVChargeBean.LOWER_UTILITY_RATE:
                mBinding.viewSelected.tvLess.setVisibility(View.VISIBLE);
                mBinding.viewSelected.llOperate.setVisibility(View.VISIBLE);
                mBinding.viewSelected.tvGoSettings.setVisibility(View.GONE);
                mBinding.viewSelected.tvValue.setLocalText(chargeBean.getValueStr());
                break;

            case PSEVChargeBean.SOLAR_ONLY:

            case PSEVChargeBean.UNTIL_FULLY_CHARGED:
                mBinding.viewSelected.tvGoSettings.setVisibility(View.GONE);
                mBinding.viewSelected.llOperate.setVisibility(View.GONE);
                break;

            case PSEVChargeBean.SCHEDULE_CHARGE:
                mBinding.viewSelected.tvGoSettings.setVisibility(View.VISIBLE);
                mBinding.viewSelected.llOperate.setVisibility(View.GONE);
                break;

            case PSEVChargeBean.FIXED_QUANTITY:
            case PSEVChargeBean.FIXED_DURATION:
                mBinding.viewSelected.llOperate.setVisibility(View.VISIBLE);
                mBinding.viewSelected.tvGoSettings.setVisibility(View.GONE);
                mBinding.viewSelected.tvValue.setLocalText(chargeBean.getValueStr());
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(EVBottomBehaviorEvent event) {
//        boolean isVisible = event.isVisible();
//        mBinding.viewSelected.getRoot().setVisibility(isVisible ? View.VISIBLE : View.GONE);
//        mBinding.rvData.setVisibility(isVisible ? View.GONE : View.VISIBLE);
        if (mEVChargeAdapter != null) {
            for (PSEVChargeBean psevChargeBean : mEVChargeAdapter.getData()) {
                psevChargeBean.setSelected(psevChargeBean.getStatus() == 1);
            }
            mEVChargeAdapter.notifyDataSetChanged();
        }
    }
}
