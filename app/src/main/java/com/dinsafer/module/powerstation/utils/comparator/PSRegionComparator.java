package com.dinsafer.module.powerstation.utils.comparator;

import com.dinsafer.module.powerstation.bean.PSRegionBean;

public class PSRegionComparator extends BaseSearchComparator<PSRegionBean> {

    public PSRegionComparator(String searchKeyword) {
        super(searchKeyword);
    }

    @Override
    public int compare(PSRegionBean o1, PSRegionBean o2) {
        String s1 = o1.getCountryNameDisplay().toLowerCase();
        String s2 = o2.getCountryNameDisplay().toLowerCase();
        return compareResult(s1, s2);
    }
}
