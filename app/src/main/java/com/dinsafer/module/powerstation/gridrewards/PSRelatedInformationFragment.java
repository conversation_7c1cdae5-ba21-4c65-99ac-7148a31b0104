package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsRelatedInformationBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.PSAdvancedSettingsAdapter;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.bean.PSAdvancedSettingsItemBean;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/29
 * @author: create by Sydnee
 */
public class PSRelatedInformationFragment extends MyBaseFragment<FragmentPsRelatedInformationBinding> {

    private PSAdvancedSettingsAdapter infoAdapter;
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;


    public static PSRelatedInformationFragment newInstance(FamilyBalanceContractInfo familyBalanceContractInfo) {
        PSRelatedInformationFragment fragment = new PSRelatedInformationFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_related_information;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.related_information));
        initRv();

    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
        }
    }

    private void initRv() {
        mBinding.rvInfo.setLayoutManager(new LinearLayoutManager(getContext()));
        infoAdapter = new PSAdvancedSettingsAdapter();
        mBinding.rvInfo.setAdapter(infoAdapter);

        List<PSAdvancedSettingsItemBean> data = new ArrayList<>();
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.Participation_Hours), R.drawable.btn_device_setting_arrow));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.bank_account), R.drawable.btn_device_setting_arrow));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.authorization_records), R.drawable.btn_device_setting_arrow));
        infoAdapter.setNewData(data);

        infoAdapter.setOnItemClickListener((adapter, view, position) -> {
            PSAdvancedSettingsItemBean bean = infoAdapter.getItem(position);
            if (bean.getKey().equals(getString(R.string.Participation_Hours))) {
                getDelegateActivity().addCommonFragment(ParticipationHoursFragment.newInstance(0));
            } if (bean.getKey().equals(getString(R.string.bank_account))) {
                getDelegateActivity().addCommonFragment(PSBankAccountFragment.newInstance(mFamilyBalanceContractInfo));
            } else if (bean.getKey().equals(getString(R.string.authorization_records))) {
                getDelegateActivity().addCommonFragment(PSAuthorizationRecordsFragment.newInstance(mFamilyBalanceContractInfo));
            }
        });
    }

}
