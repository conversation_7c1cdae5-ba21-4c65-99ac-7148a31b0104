package com.dinsafer.module.powerstation.bean;

import androidx.annotation.Nullable;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/1 17:14
 * @description :
 */
public class KeyValueBean {

    private String key;
    private String value;
    @Nullable
    private String displayKey;

    public KeyValueBean(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key == null ? "" : key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value == null ? "" : value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Nullable
    public String getDisplayKey() {
        return displayKey;
    }

    public void setDisplayKey(String displayKey) {
        this.displayKey = displayKey;
    }
}
