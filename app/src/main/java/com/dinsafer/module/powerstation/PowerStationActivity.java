package com.dinsafer.module.powerstation;

import androidx.databinding.DataBindingUtil;
import android.graphics.Color;
import android.os.Bundle;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityPowerStationBinding;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.BaseFragmentActivity;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.main.view.AddWidgetFragment;
import com.dinsafer.module.powerstation.dialog.WarningDialog;
import com.dinsafer.module.powerstation.electricity.chart.marker.UsageDayMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.render.SectionLineChartRender;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.FillFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.interfaces.dataprovider.LineDataProvider;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.annotations.NonNull;

public class PowerStationActivity extends BaseFragmentActivity implements ViewPager.OnPageChangeListener {

    private ActivityPowerStationBinding mBinding;
    private ImageView[] dotViews;
    private ArrayList<BaseFragment> mFragments = new ArrayList<BaseFragment>(); // 将要显示的布局存放到list数组
    private CommonPagerAdapter mAdapter;
    private int curViewPagerSelectedIndex;
    private WarningDialog mWarningDialog;

    @Override
    protected boolean initVariables() {
        return true;
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_power_station);
        mBinding.commonBar.commonBarTitle.setText(getResources().getString(R.string.power_station_02));
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);
        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mFragments.add(CurrentDiagramFragment.newInstance("", ""));
        mFragments.add(BatteryFragment.newInstance("", ""));
        mAdapter = new CommonPagerAdapter(getSupportFragmentManager(), mFragments);
        mBinding.vpCurrent.setAdapter(mAdapter);
        mBinding.vpCurrent.addOnPageChangeListener(this);
        initIndicator();
        setVPCanSlide(true);
        initChart();
        initListener();
    }

    private void initListener() {
        mBinding.clWarning.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showWarningDialog();
            }
        });
    }

    @Override
    protected void loadData() {

    }

    /**
     * 如果在更新不可滑动
     * @param canSlide
     */
    private void setVPCanSlide(boolean canSlide) {
        mBinding.vpCurrent.setCanSlide(canSlide);
    }

    /**
     * 初始化指示器
     */
    private void initIndicator() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        //设置小圆点左右之间的间隔
        params.setMargins(8, 0, 8, 0);
        dotViews = new ImageView[mFragments.size()];
        curViewPagerSelectedIndex = 0;
        for (int i = 0; i < mFragments.size(); i++) {
            ImageView imageView = new ImageView(this);
            imageView.setLayoutParams(params);
            imageView.setImageResource(R.drawable.shape_bg_power_vp_nor);
            //默认启动时，选中第一个小圆点
            imageView.setSelected(i == curViewPagerSelectedIndex);
            dotViews[i] = imageView;
            dotViews[curViewPagerSelectedIndex].setImageResource(R.drawable.shape_bg_power_vp_sel); // 设置第一个页面已被选择
            mBinding.llIndicator.addView(imageView);
        }
    }

    @Override
    public void onPageScrolled(int i, float v, int i1) {

    }

    @Override
    public void onPageSelected(int index) {
        curViewPagerSelectedIndex = index;
        for (int i = 0; i < dotViews.length; i++) {
            dotViews[i].setSelected(curViewPagerSelectedIndex == i);
            dotViews[i].setImageResource(curViewPagerSelectedIndex == i
                    ? R.drawable.shape_bg_power_vp_sel : R.drawable.shape_bg_power_vp_nor);
        }
    }

    @Override
    public void onPageScrollStateChanged(int i) {

    }

    private void showWarningDialog() {
        if (mWarningDialog == null) {
            mWarningDialog = new WarningDialog();
        }
        mWarningDialog.show(getSupportFragmentManager(), WarningDialog.TAG);
    }

    private void initChart() {
        mBinding.lcUsage.setDrawBorders(true);
        mBinding.lcUsage.setBorderWidth(0.5f);
        mBinding.lcUsage.setBorderColor(getResources().getColor(R.color.color_white_04));
        mBinding.lcUsage.setHighlightPerDragEnabled(false);
        mBinding.lcUsage.setHighlightFullBarEnabled(false);
        mBinding.lcUsage.setScaleEnabled(false);
        mBinding.lcUsage.setExtraTopOffset(0f);
        //设置图表距离上下左右的距离
        mBinding.lcUsage.setExtraOffsets(0f, 50f, 10f, 15f);
        //图例
        mBinding.lcUsage.getLegend().setEnabled(false);
        mBinding.lcUsage.setDescription("");

        mBinding.lcUsage.animateX(1200);

        UsageDayMarkerView markerView = new UsageDayMarkerView(this);
        mBinding.lcUsage.setMarkerView(markerView);

        //获取X轴
        XAxis xAxis = mBinding.lcUsage.getXAxis();
        //将垂直于X轴的网格线隐藏，将X轴显示
        xAxis.setDrawGridLines(true);
        xAxis.setDrawAxisLine(false);
        xAxis.setGridColor(getResources().getColor(R.color.color_white_04));
        //设置X轴上lable颜色和大小
        xAxis.setTextSize(13f);
        xAxis.setTextColor(getResources().getColor(R.color.tuya_electric_word_color_80percent));
        //设置X轴高度
        xAxis.setAxisLineWidth(0.5f);
        //x轴刻度值的位置
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
//        设置在”绘制下一个标签”时，要忽略的标签数。
        xAxis.setLabelsToSkip(359);

        //获取左侧侧坐标轴
        YAxis yAxis = mBinding.lcUsage.getAxisLeft();
        //设置是否显示Y轴的值
        yAxis.setDrawLabels(false);
        yAxis.setAxisLineWidth(0.5f);
        //是否绘制坐标轴
        yAxis.setDrawAxisLine(true);
        yAxis.setTextColor(getResources().getColor(R.color.tuya_electric_word_color_80percent));
        yAxis.setTextSize(13f);
        //设置虚线
        yAxis.disableGridDashedLine();
        //设置网格线在0刻度那里的显示
        yAxis.setDrawZeroLine(true);
        yAxis.setZeroLineColor(getResources().getColor(R.color.tuya_electric_line_color));
        //设置所有垂直Y轴的的网格线是否显示
        yAxis.setDrawGridLines(true);
        yAxis.setGridColor(getResources().getColor(R.color.color_white_04));
        yAxis.setAxisMaxValue(5000f);
        yAxis.setAxisMinValue(0f);



        if (mBinding.lcUsage.getRenderer() instanceof SectionLineChartRender) {
            SectionLineChartRender renderer = (SectionLineChartRender) mBinding.lcUsage.getRenderer();
            int medium = 300;
            int larger = 1500;
            int limit = 2000 ;

            int[] colors = new int[4];
            colors[0] = Color.parseColor("#FF778F");
            colors[1] = Color.parseColor("#FFD12E");
            colors[2] = Color.parseColor("#35C38A");
            colors[3] = Color.parseColor("#4485E8");
//            renderer.setSectionLine(true, medium,larger,limit, colors);
            renderer.setOutCircleColor(getResources().getColor(R.color.electricity_hig_light_circle_out_color));
        }

        mBinding.lcUsage.getAxisRight().setEnabled(false);

        LineDataSet weekOneSales = new LineDataSet(generateData(), null);
        weekOneSales.setLineWidth(1f);
        weekOneSales.setValueTextSize(15f);
        weekOneSales.setMode(LineDataSet.Mode.LINEAR);
        weekOneSales.setDrawCircles(false);
//        weekOneSales.enableDashedLine(20f, 10f, 0f);
        weekOneSales.setDrawValues(false);
        weekOneSales.setColor(Color.GREEN);
//        weekOneSales.setColors(Color.RED, Color.GREEN, Color.BLUE);
        weekOneSales.setDrawFilled(true);
        weekOneSales.setDrawHorizontalHighlightIndicator(false);
        weekOneSales.setHighLightColor(getResources().getColor(R.color.color_white_03));
        weekOneSales.setHighlightLineWidth(1f);
        weekOneSales.setDrawCircleHole(false);
        weekOneSales.setFillAlpha(60);
        weekOneSales.setFillFormatter(new FillFormatter() {
            @Override
            public float getFillLinePosition(ILineDataSet dataSet, LineDataProvider dataProvider) {
                return 0;
            }
        });


        ArrayList<String> xVals = new ArrayList<String>();
        for (int i=0; i<generateData().size(); i++) {
            xVals.add(String.valueOf(i));
        }
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        lineDataSets.add(weekOneSales);
        LineData lineData = new LineData(xVals, lineDataSets);
        mBinding.lcUsage.setData(lineData);

        mBinding.lcUsage.invalidate();
    }

    private List<Entry> generateData() {
        List<Entry> dataList = new ArrayList<>(1440);
        for (int i=0; i<50; i++) {
            dataList.add(new Entry(200, i));
        }
        for (int i=50; i<100; i++) {
            dataList.add(new Entry(100, i));
        }

        for (int i=100; i<110; i++) {
            dataList.add(new Entry(200+i, i));
        }

        for (int i=110; i<150; i++) {
            dataList.add(new Entry(1200, i));
        }
        for (int i=150; i<160; i++) {
            dataList.add(new Entry(1200+i, i));
        }
        for (int i=160; i<600;i++) {
            dataList.add(new Entry(1200+i, i));
        }

        for (int i=600; i<800;i++) {
            float val = (float) (Math.random() * 500);
            dataList.add(new Entry(val, i));
        }

        for (int i=800; i<1440;i++) {
            dataList.add(new Entry(2000+i, i));
        }
        return dataList;
    }
}