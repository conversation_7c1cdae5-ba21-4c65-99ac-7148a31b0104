package com.dinsafer.module.powerstation.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class WarningBean implements Parcelable {

    private String title;
    private String content;
    private String withoutText;
    private boolean restart = false;
    private boolean without = false;
    private String type;
    private String eventKey;

    public WarningBean() {
    }

    public WarningBean(String title, String content) {
        this(title, content, false, "");
    }

    public WarningBean(String title, String content, boolean restart, String type) {
        this.title = title;
        this.content = content;
        this.restart = restart;
        this.type = type;
    }

    public WarningBean(String title, String content, String withoutText, boolean restart, boolean without, String type) {
        this.title = title;
        this.content = content;
        this.withoutText = withoutText;
        this.restart = restart;
        this.without = without;
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setWithoutText(String withoutText) {
        this.withoutText = withoutText;
    }

    public String getWithoutText() {
        return withoutText;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setRestart(boolean restart) {
        this.restart = restart;
    }

    public boolean isRestart() {
        return restart;
    }

    public void setWithout(boolean without) {
        this.without = without;
    }

    public boolean isWithout() {
        return without;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEventKey() {
        return eventKey;
    }

    public void setEventKey(String eventKey) {
        this.eventKey = eventKey;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.title);
        dest.writeString(this.content);
        dest.writeString(this.withoutText);
        dest.writeByte(this.restart ? (byte) 1 : (byte) 0);
        dest.writeByte(this.without ? (byte) 1 : (byte) 0);
        dest.writeString(this.type);
        dest.writeString(this.eventKey);
    }

    public void readFromParcel(Parcel source) {
        this.title = source.readString();
        this.content = source.readString();
        this.withoutText = source.readString();
        this.restart = source.readByte() != 0;
        this.without = source.readByte() != 0;
        this.type = source.readString();
        this.eventKey = source.readString();
    }

    protected WarningBean(Parcel in) {
        this.title = in.readString();
        this.content = in.readString();
        this.withoutText = in.readString();
        this.restart = in.readByte() != 0;
        this.without = in.readByte() != 0;
        this.type = in.readString();
        this.eventKey = in.readString();
    }

    public static final Creator<WarningBean> CREATOR = new Creator<WarningBean>() {
        @Override
        public WarningBean createFromParcel(Parcel source) {
            return new WarningBean(source);
        }

        @Override
        public WarningBean[] newArray(int size) {
            return new WarningBean[size];
        }
    };
}
