package com.dinsafer.module.powerstation.widget.ai_schedule_mode_view;

import android.content.Context;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAiScheduleModeBinding;
import com.dinsafer.module.powerstation.utils.AIColorUtil;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.DDDateUtil;

import java.util.List;

public class AIScheduleModeModel extends BindModel<ItemAiScheduleModeBinding> {

    private Context mContext;
    //-1. 放电 0. 不充不放  1. 充电
    private int mode;
    private int hour = -1;
    private int percentage;
    private int sectionType = 1; // 1. 一段  2. 两段  3. 三段
    private boolean selected;
    private long startTime;
    private long endTime;
    private String timezone;
    private int pvVal;
    private int mavPvVal;
    private int plan;
    private int customPercentage;
    private boolean isCustom;
    private int mEmergencyReserve = 30;
    private int mSmartReserve = 70;
    private int customEmergency = 30;
    private int customSmart = 70;
    private boolean isLast;
    private boolean isExpand;
    private float electricityPricePercents;
    private List<Float> electricityPriceData;
    private List<Float> pvData;
    private float[] maxAndMin;
    private BindMultiAdapter<BindModel> mScheduledModeAdapter;
    private AIItemDataView itemDataView;
    private boolean isEdit;


    public AIScheduleModeModel(Context context, int hour, int mode, int percentage,
                               BindMultiAdapter<BindModel> scheduledModeAdapter) {
        super(context);
        this.mContext = context;
        this.hour = hour;
        this.mode = mode;
        this.percentage = percentage;
        mScheduledModeAdapter = scheduledModeAdapter;
    }

    public AIScheduleModeModel(Context context, int percentage, long startTime, long endTime,
                               String timezone, int pvVal, int mavPvVal, int plan, int customPercentage,
                               boolean isCustom, int emergencyReserve, int smartReserve, int customEmergency,
                               int customSmart, float electricity_price_percents, boolean isLast,
                               BindMultiAdapter<BindModel> scheduledModeAdapter) {
        super(context);
        this.mContext = context;
        this.percentage = percentage;
        this.startTime = startTime;
        this.endTime = endTime;
        this.timezone = timezone;
        this.pvVal = pvVal;
        this.mavPvVal = mavPvVal;
        this.plan = plan;
        this.customPercentage = customPercentage;
        this.isCustom = isCustom;
        this.mEmergencyReserve = emergencyReserve;
        this.mSmartReserve = smartReserve;
        this.customEmergency = customEmergency;
        this.customSmart = customSmart;
        this.isLast = isLast;
        this.mScheduledModeAdapter = scheduledModeAdapter;
        hour = DDDateUtil.getHourByTimestamps(startTime, timezone);
        this.electricityPricePercents = electricity_price_percents;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ai_schedule_mode;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAiScheduleModeBinding binding) {
        binding.tvTimeTop.setTranslationY(DensityUtil.dp2px(mContext, -9));
        int position = holder.getAdapterPosition();
        binding.tvTimeBottom.setTranslationY(DensityUtil.dp2px(mContext, isLast ? 4 : 10));
        binding.tvTimeBottom.setVisibility(isLast ?
                View.VISIBLE : View.INVISIBLE);
        int endHour = (isLast && hour == 23) ? (hour + 1) : DDDateUtil.getHourByTimestamps(endTime, timezone);
        String hourStr = hour < 10 ? ("0" + hour + ":00") : (hour + ":00");
        String endHourStr = endHour < 10 ? ("0" + endHour + ":00") : (endHour + ":00");
        binding.tvTimeTop.setText(hourStr);
        binding.tvTimeCenter.setVisibility(hour == 0 ? View.VISIBLE : View.GONE);
        String date = "(" + DDDateUtil.formatWithTimezone(startTime, timezone, "MM.dd") + ")";
        binding.tvTimeCenter.setLocalText(date);
        binding.tvTimeBottom.setText(endHourStr);
        itemDataView = binding.viewData;
        boolean normalTimeVisible = false;
        boolean isFirstSelected = false;
        int selectedStatus = -1;
        if (selected) {
            selectedStatus = 0;
            if (position == 0) {
                normalTimeVisible = true;
                isFirstSelected = true;
            } else {
                AIScheduleModeModel lastModeBean = (AIScheduleModeModel) mScheduledModeAdapter.getItem(position - 1);
                normalTimeVisible = !lastModeBean.isSelected();
                isFirstSelected = !lastModeBean.isSelected();
            }
        }

        boolean endTimeVisible = false;
        boolean isFinalSelected = false;
        if (isLast) {
            endTimeVisible = true;
            isFinalSelected = selected;
        } else {
            AIScheduleModeModel nextModeBean = (AIScheduleModeModel) mScheduledModeAdapter.getItem(position + 1);
            endTimeVisible = selected && !nextModeBean.isSelected();
            isFinalSelected = selected && !nextModeBean.isSelected();
        }

        binding.tvTimeTop.setVisibility(normalTimeVisible || hour % 3 == 0 ? View.VISIBLE : View.INVISIBLE);
        binding.tvTimeTop.setTextColor(normalTimeVisible ? getColor(R.color.color_brand_text) : getColor(R.color.color_white_03));
        binding.tvTimeCenter.setTextColor(endTimeVisible ? getColor(R.color.color_brand_text) : getColor(R.color.color_white_03));
        binding.tvTimeBottom.setTextColor(selected ? getColor(R.color.color_brand_text) : getColor(R.color.color_white_03));
        binding.tvTimeBottom.setVisibility(endTimeVisible ? View.VISIBLE : View.GONE);
        int percent = isCustom ? customPercentage : percentage;
        int emergency = isCustom ? customEmergency : mEmergencyReserve;
        int smart = isCustom ? customSmart : mSmartReserve;

        if (isFirstSelected && isFinalSelected) {
            selectedStatus = 3;
        } else if (isFirstSelected) {
            selectedStatus = 1;
        } else if (isFinalSelected) {
            selectedStatus = 2;
        }

        binding.viewData.setCustom(isCustom);
        binding.viewData.setFirst(position == 0);
        binding.viewData.setLast(isLast);
        if (percent > 0) {
            mode = 1;
        } else if (percent < 0) {
            mode = -1;
        } else {
            mode = 0;
        }
        binding.viewData.setEdit(isEdit);
        if (isEdit) {
            int customMode = 0;
            if (isCustom) {
                if (customPercentage > 0) {
                    customMode = 1;
                } else if (customPercentage < 0) {
                    customMode = -1;
                }
            }
            binding.viewData.resetStatus(customMode, selectedStatus);
        } else {
            if (mavPvVal > 0) {
                float temp = pvVal / (mavPvVal * 1.0f);
                if (temp >= 0.7f) {
                    binding.viewData.setPvMode(0);
                } else if (temp >= 0.3f) {
                    binding.viewData.setPvMode(1);
                } else {
                    binding.viewData.setPvMode(pvVal == 0 ? 3 : 2);
                }
            } else {
                binding.viewData.setPvMode(3);
            }
            binding.viewData.resetStatus(mode, selectedStatus);
        }
        binding.viewData.setExpand(isExpand);
        binding.viewData.invalidate();


        binding.apbCharge.post(() -> {
            if (percent > 0) {
                binding.apbCharge.setShadowColor(isCustom ? getColor(R.color.transparent) : getColor(R.color.color_ai_schedule_bar_blur));
                int alpha = isCustom ? 0x99 : 0xcc;
                binding.apbCharge.setProgressPaintAlpha(alpha);
                if (percent <= emergency) {
                    binding.apbCharge.setProgress(percent / 100f, getColor(R.color.color_tip_01), AIColorUtil.getAIColor(mContext),
                            true, !isCustom, !isCustom, alpha);
                    sectionType = 1;
                } else if (percent <= smart) {
                    binding.apbCharge.setProgress(percent / 100f, new int[]{getColor(R.color.price_scheduled_mode_second_color),
                                    getColor(R.color.color_tip_01)}, AIColorUtil.getAIColor(mContext),
                            true, !isCustom, !isCustom);
                    sectionType = 2;
                } else {
                    binding.apbCharge.setProgress(percent / 100f, new int[]{getColor(R.color.color_tip_03),
                                    getColor(R.color.price_scheduled_mode_second_color),
                                    getColor(R.color.color_tip_01)}, AIColorUtil.getAIColor(mContext),
                            true, !isCustom, !isCustom);
                    sectionType = 3;
                }
                binding.apbDischarge.setProgress(0f, getColor(R.color.color_tip_01), AIColorUtil.getAIColor(mContext));

            } else if (percent == 0) {
                binding.apbCharge.setProgress(0f, getColor(R.color.color_tip_01), AIColorUtil.getAIColor(mContext));
                binding.apbDischarge.setProgress(0f, getColor(R.color.color_tip_01), AIColorUtil.getAIColor(mContext));
            } else {
                int perAbs = Math.abs(percent);
                binding.apbDischarge.setShadowColor(isCustom ? getColor(R.color.transparent) : getColor(R.color.color_ai_schedule_bar_blur));
                int alpha = isCustom ? 0x99 : 0xcc;
                binding.apbDischarge.setProgressPaintAlpha(alpha);
                if (perAbs <= emergency) {
                    binding.apbDischarge.setProgress((100 - perAbs) / 100f, new int[]{getColor(R.color.color_tip_03),
                                    getColor(R.color.price_scheduled_mode_discharge_second_color)},
                            AIColorUtil.getAIColor(mContext), true, !isCustom, !isCustom);
                    sectionType = 2;
                } else if (perAbs <= smart) {
                    binding.apbDischarge.setProgress((100 - perAbs) / 100f, getColor(R.color.color_tip_03),
                            AIColorUtil.getAIColor(mContext), true, !isCustom, !isCustom, alpha);
                    sectionType = 1;
                } else {
                    binding.apbDischarge.setProgress(0, getColor(R.color.color_tip_03),
                            AIColorUtil.getAIColor(mContext));
                }
                binding.apbCharge.setProgress(0f, getColor(R.color.color_tip_01), AIColorUtil.getAIColor(mContext));
            }

            binding.apbCharge.setAlpha(plan != 0 ? 0.4f : 1.0f);

        });
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getPercentage() {
        return percentage;
    }

    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    public int getSectionType() {
        return sectionType;
    }

    public void setSectionType(int sectionType) {
        this.sectionType = sectionType;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public int getPvVal() {
        return pvVal;
    }

    public void setPvVal(int pvVal) {
        this.pvVal = pvVal;
    }

    public int getMavPvVal() {
        return mavPvVal;
    }

    public void setMavPvVal(int mavPvVal) {
        this.mavPvVal = mavPvVal;
    }

    public int getPlan() {
        return plan;
    }

    public void setPlan(int plan) {
        this.plan = plan;
    }

    public int getCustomPercentage() {
        return customPercentage;
    }

    public void setCustomPercentage(int customPercentage) {
        this.customPercentage = customPercentage;
    }

    public boolean isCustom() {
        return isCustom;
    }

    public void setCustom(boolean custom) {
        isCustom = custom;
    }

    public int getCustomEmergency() {
        return customEmergency;
    }

    public void setCustomEmergency(int customEmergency) {
        this.customEmergency = customEmergency;
    }

    public int getCustomSmart() {
        return customSmart;
    }

    public void setCustomSmart(int customSmart) {
        this.customSmart = customSmart;
    }

    public boolean isLast() {
        return isLast;
    }

    public void setLast(boolean last) {
        isLast = last;
    }

    public boolean isExpand() {
        return isExpand;
    }

    public void setExpand(boolean expand) {
        isExpand = expand;
        if (itemDataView != null) {
            itemDataView.setExpand(expand);
            itemDataView.invalidate();
        }
    }

    public float getElectricityPricePercents() {
        return electricityPricePercents;
    }

    public void setElectricityPricePercents(float electricityPricePercents) {
        this.electricityPricePercents = electricityPricePercents;
    }

    public List<Float> getElectricityPriceData() {
        return electricityPriceData;
    }

    public void setElectricityPriceData(List<Float> electricityPriceData) {
        this.electricityPriceData = electricityPriceData;
    }

    public List<Float> getPvData() {
        return pvData;
    }

    public void setPvData(List<Float> pvData) {
        this.pvData = pvData;
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

    public float[] getMaxAndMin() {
        return maxAndMin;
    }

    public void setMaxAndMin(float[] maxAndMin) {
        this.maxAndMin = maxAndMin;
    }

    public boolean isMinPrice() {
        return electricityPricePercents == maxAndMin[1];
    }

    public boolean isMaxPrice() {
        return electricityPricePercents == maxAndMin[0];
    }

    public boolean isEdit() {
        return isEdit;
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
    }

    public boolean isPlanA() {
        return plan == 0;
    }
}
