package com.dinsafer.module.powerstation.ev;

import android.animation.ValueAnimator;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsEvChargeBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.ExceptionWarning;
import com.dinsafer.module.powerstation.LottieManager;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PSEVChargeAdapter;
import com.dinsafer.module.powerstation.bean.PSEVChargeBean;
import com.dinsafer.module.powerstation.bean.WarningBean;
import com.dinsafer.module.powerstation.dialog.WarningDialog;
import com.dinsafer.module.powerstation.event.EVBottomBehaviorEvent;
import com.dinsafer.module.powerstation.event.ReOpenEvent;
import com.dinsafer.module.settting.ui.FeedBackFragment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.StringUtil;
import com.dinsafer.util.TimeUtil;
import com.dinsafer.util.UnitUtil;
import com.google.android.material.bottomsheet.BottomSheetBehavior;

import org.greenrobot.eventbus.EventBus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;


public class PSEVChargeFragment extends MyBaseFragment<FragmentPsEvChargeBinding>
        implements IDeviceCallBack {

    private BottomSheetBehavior mBottomSheetBehavior;
    private PSEVChargeAdapter mEVChargeAdapter;
    private int mAppliedPosition;
    private LottieManager mLottieManager;
    private int status = 0;
    private boolean isInstantSelected;
    private ValueAnimator mCollapsedAnimator;
    private ValueAnimator mExpandAnimator;

    private String mDeviceId;
    private String mSubcategory;
    private Device mPSDevice;

    private Subscription mPollTimer;
    private boolean isClickInstant;

    private ArrayList<WarningBean> mWarningList = new ArrayList<>();
    private int mStatus;
    private WarningDialog mWarningDialog;

    private PSEVChargeBean mLowerUtility;
//    private PSEVChargeBean mSolarOnly;
    private PSEVChargeBean mScheduleCharge;
    private List<PSEVChargeBean> mEvChargeList = new ArrayList<>();

    public static PSEVChargeFragment newInstance(String deviceId, String subcategory) {
        PSEVChargeFragment fragment = new PSEVChargeFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_ev_charge;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.ps_ev_charge));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonBar.commonBarTitle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                status = status == 3 ? 0 : status + 1;
                setEvStatus(status);
            }
        });
        mLottieManager = new LottieManager();
        mBinding.lavEv.setProgress(870 / 993f);
        initBottomSheet();
        initRv();
        setEvStatus(0);
        getEVAdvanceStatus();
        getEVChargingInfo();
        getEvChargeMode();
    }

    @Override
    public void initListener() {
        super.initListener();

        mBinding.llStatus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (LottieManager.EV_ERROR == mStatus && CollectionUtil.isListNotEmpty(mWarningList)) {
                    showWarningDialog();
                }
            }
        });
        mBinding.tvChargeInstant.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                isClickInstant = true;
                if (isInstantSelected) {
                    exitInstantCharge();
                } else {
                    setEvChargingMode(4);
                }
            }
        });
    }

    @Override
    public void onDestroyView() {
        if (null != mPSDevice) {
            mPSDevice.unregisterDeviceCallBack(this);
            mPSDevice = null;
        }
        closePollTimer();
        super.onDestroyView();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        mDeviceId = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        mSubcategory = bundle.getString(PSKeyConstant.KEY_SUB_CATEGORY);
        mPSDevice = BmtManager.getInstance().getDeviceById(mDeviceId, mSubcategory);
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
        }
    }

    private boolean needRotate = true;

    private void removeShrink() {
        if (mLowerUtility.getStatus() != 1 && mAppliedPosition != 0) {
            mLowerUtility.setNeedAnim(false);
            mLowerUtility.setSelected(false);
            mEvChargeList.remove(mLowerUtility);
        }
//        if (mSolarOnly.getStatus() != 1 && mAppliedPosition != 1) {
//            mSolarOnly.setNeedAnim(false);
//            mSolarOnly.setSelected(false);
//            mEvChargeList.remove(mSolarOnly);
//        }
        if (mScheduleCharge.getStatus() != 1 && mAppliedPosition != 1) {
            mScheduleCharge.setNeedAnim(false);
            mScheduleCharge.setSelected(false);
            mEvChargeList.remove(mScheduleCharge);
        }
        mEVChargeAdapter.notifyDataSetChanged();
    }
    /**
     * 初始化bottomSheet
     */
    private void initBottomSheet() {
        mBottomSheetBehavior = BottomSheetBehavior.from(mBinding.rlBottom);
        mBottomSheetBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {

            }

            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
//                if (mEVChargeAdapter != null) {
//                    for (PSEVChargeBean psevChargeBean : mEVChargeAdapter.getData()) {
//                        psevChargeBean.setAlpha(slideOffset);
//                    }
//                    mEVChargeAdapter.notifyDataSetChanged();
//                }
                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mBinding.scrollView.getLayoutParams();
                params.topMargin = (int) (-mBinding.lavEv.getHeight() * slideOffset);
                mBinding.scrollView.setLayoutParams(params);
                mBinding.lavEv.setAlpha(1 - slideOffset);
                if (slideOffset <= 0.4f) {
                    if (mEvChargeList.size() > 1) {
                        removeShrink();
                        mEVChargeAdapter.setExpand(false);
                    }
                } else if (slideOffset >= 0.6f) {
                    if (mEvChargeList.size()<2) {
                        if (mLowerUtility.getStatus() != 1 && mAppliedPosition != 0) {
                            mEvChargeList.add(0, mLowerUtility);
                        }
//                        if (mSolarOnly.getStatus() != 1 && mAppliedPosition != 1) {
//                            mEvChargeList.add(1, mSolarOnly);
//                        }
                        if (mScheduleCharge.getStatus() != 1 && mAppliedPosition != 1) {
                            mEvChargeList.add(1, mScheduleCharge);
                        }
                        mEVChargeAdapter.notifyDataSetChanged();
                        mEVChargeAdapter.setExpand(true);
                    }
                }
            }
        });
    }

    private void initRv() {
        mBinding.rvMode.setLayoutManager(new LinearLayoutManager(getDelegateActivity()));
        mEVChargeAdapter = new PSEVChargeAdapter();
        mBinding.rvMode.setAdapter(mEVChargeAdapter);
        mEVChargeAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_COLLAPSED) return;
                if (isInstantSelected) return;
                isClickInstant = false;
                PSEVChargeBean psevChargeBean = mEVChargeAdapter.getItem(position);
                if (psevChargeBean.isSelected()) return;
                for (PSEVChargeBean chargeBean : mEVChargeAdapter.getData()) {
                    chargeBean.setNeedAnim(true);
                    chargeBean.setSelected(false);
                }
                psevChargeBean.setSelected(true);
                mEVChargeAdapter.notifyDataSetChanged();
            }
        });

        mEVChargeAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            int viewId = view.getId();
            PSEVChargeBean psevChargeBean = mEVChargeAdapter.getItem(position);
            switch (viewId) {
                case R.id.tv_go_settings:
                    mAppliedPosition = 2;
                    getDelegateActivity().addCommonFragment(PSScheduledChargeFragment.newInstance(mDeviceId, mSubcategory));
                    break;

                case R.id.ll_status:
                    if (psevChargeBean.getStatus() == -1) {
                        psevChargeBean.setNeedAnim(false);
                        applyCharge(psevChargeBean, position);
                    }
                    break;
            }
        });

        mLowerUtility = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_1),
                getString(R.string.ps_ev_smart_charge_content_1), PSEVChargeBean.LOWER_UTILITY_RATE);
//        lowerUtility.setSelected(true);
//        lowerUtility.setStatus(1);
//        mSolarOnly = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_2),
//                getString(R.string.ps_ev_smart_charge_content_2), PSEVChargeBean.SOLAR_ONLY);
        mScheduleCharge = new PSEVChargeBean(getString(R.string.ps_ev_smart_charge_title_3),
                getString(R.string.ps_ev_smart_charge_content_3), PSEVChargeBean.SCHEDULE_CHARGE);
        mEvChargeList.add(mLowerUtility);
//        mEvChargeList.add(mSolarOnly);
        mEvChargeList.add(mScheduleCharge);
        mEVChargeAdapter.setNewData(mEvChargeList);
        setSelectValue(mLowerUtility, 0, true);
    }

    private void showWarningDialog() {
        if (mWarningDialog == null) {
            mWarningDialog = WarningDialog.newInstance(mWarningList);
            mWarningDialog.setWarningListener(new WarningDialog.WarningListener() {
                @Override
                public void onClose() {

                }

                @Override
                public void onRestart(String type) {
//                    submitCmdInverterClose();
                    BmtManager.getInstance().resetInverter(mPSDevice, true);
                }

                @Override
                public void onWithout(String type) {
                }

                @Override
                public void onContactCustomerSupport() {
                    getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
                }
            });
        }
        mWarningDialog.show(getChildFragmentManager(), WarningDialog.TAG);
    }

    /**
     * 初始化轮询
     */
    private void startPollTimer() {
        closePollTimer();
        mPollTimer = Observable.interval(0, 15, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "subscribe里");
                        getEVChargingInfo();
                    }
                });
    }

    public void closePollTimer() {
        if (mPollTimer != null && !mPollTimer.isUnsubscribed()) {
            mPollTimer.unsubscribe();
        }
    }

    /**
     * 选中item
     *
     * @param chargeBean
     * @param position
     */
    private void setSelectValue(PSEVChargeBean chargeBean, int position, boolean isFirst) {
        if (chargeBean == null) return;
        mBinding.viewSelected.tvTitle.setLocalText(chargeBean.getTitle());
        mBinding.viewSelected.tvSubtitle.setLocalText(chargeBean.getSubTitle());
        mBinding.viewSelected.tvApplied.setVisibility(chargeBean.getStatus() == 1 ?
                View.VISIBLE : View.GONE);
        if (!isFirst) {
            mBinding.viewSelected.getRoot().setY(mViewSelectY);
        }
        resetPeekHeight();
    }

    /**
     * 应用
     *
     * @param psevChargeBean
     * @param position
     */
    private void applyCharge(PSEVChargeBean psevChargeBean, int position) {
        psevChargeBean.setStatus(0);
        mEVChargeAdapter.notifyItemChanged(position);
        mAppliedPosition = position;
        int mode = position == 0 ? 1 : 3;
        setEvChargingMode(mode);
    }

    /**
     * 重置bottomSheet高度
     */
    private int mViewSelectY;

    private void resetPeekHeight() {
        mBinding.viewLine.post(new Runnable() {
            @Override
            public void run() {
                int height = mBinding.viewSelected.getRoot().getHeight() +
                        mBinding.tvTitle.getHeight() + DensityUtil.dp2px(getContext(), 63);
                mViewSelectY = height - DensityUtil.dp2px(getContext(), 15);
                ViewGroup.LayoutParams layoutParams = mBinding.scrollView.getLayoutParams();
                layoutParams.height = mBinding.colParent.getHeight() - height;
                mBinding.scrollView.setLayoutParams(layoutParams);
                mBottomSheetBehavior.setPeekHeight(height);
            }
        });
    }

    /**
     * 设置车充状态
     *
     * @param status
     */
    private void setEvStatus(int status) {
        mStatus = status;
        mBinding.clValue.setVisibility(status == LottieManager.EV_CHARGING ?
                View.VISIBLE : View.GONE);

        mBinding.rlBottom.post(new Runnable() {
            @Override
            public void run() {
                ViewGroup.LayoutParams llParams = mBinding.rlBottom.getLayoutParams();
                llParams.height = mBinding.colParent.getHeight() -
                        (status == LottieManager.EV_CHARGING ? mBinding.clValue.getHeight() : 0)
                        - mBinding.tvStatus.getHeight() - DensityUtil.dp2px(getDelegateActivity(),
                        status == LottieManager.EV_CHARGING ? 35 : 25);
                mBinding.rlBottom.setLayoutParams(llParams);
            }
        });

        mLottieManager.controlEV(mBinding.lavEv, status);
        mBinding.ivArrow.setVisibility(View.GONE);
        switch (status) {
            case LottieManager.EV_OFF:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_off));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_02));
                break;

            case LottieManager.EV_CHARGING:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_charging));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_01));
                break;

            case LottieManager.EV_WAITING:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_waiting_for_charge));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_white_01));
                break;

            case LottieManager.EV_ERROR:
                mBinding.ivArrow.setVisibility(View.VISIBLE);
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_tip_warning));
                loadEVException();
                break;

            case LottieManager.EV_UNAUTHORIZED:
                mBinding.tvStatus.setLocalText(getString(R.string.ps_ev_unauthorized));
                mBinding.tvStatus.setTextColor(getContext().getResources().getColor(R.color.color_tip_warning));
                break;
        }
    }

    private void loadEVException() {
        List<Integer> evExceptions = DeviceHelper.getList(mPSDevice, "evExceptions");
        mWarningList.clear();
        if (CollectionUtil.isListNotEmpty(evExceptions)) {
            for (Integer exception : evExceptions) {
                WarningBean warningBean = ExceptionWarning.getEVWarningBean(getContext(), exception, BmtUtil.isBmtDeviceV2(mPSDevice));
                if (warningBean == null) {
                    continue;
                }
                mWarningList.add(warningBean);
            }
        }
        String error = CollectionUtil.isListNotEmpty(mWarningList) ? mWarningList.get(0).getTitle()
                : getString(R.string.error);
        mBinding.tvStatus.setLocalText(error);
    }

    /**
     * 获取EV充电状态信息(0xa027)
     */
    private void getEVAdvanceStatus() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
            mPSDevice.submit(map);
        }
    }

    /**
     * 获取EV充电状态信息(0xa027)
     */
    private void getEVChargingInfo() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_EVCHARGING_INFO);
            mPSDevice.submit(map);
        }
    }


    /**
     * 获取EV充电模式(0xa020)
     */
    private void getEvChargeMode() {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(map);
        }
    }

    /**
     * 设置EV充电模式(0xa022)
     *
     * @param mode
     */
    private void setEvChargingMode(int mode) {
        if (mPSDevice != null) {
            Map<String, Object> map = new HashMap<>();
            map.put(PSKeyConstant.CMD, BmtCmd.SET_EV_CHARGING_MODE);
            map.put(BmtDataKey.EV_CHARGING_MODE, mode);
            if (mode == 4) {
                showTimeOutLoadinFramgment();
            }
            mPSDevice.submit(map);
        }
    }

    /**
     * 是否立即充电界面展示
     */
    private void setInstanceChargeView() {
        mBinding.tvChargeInstant.setSelected(isInstantSelected);
        mBinding.rvMode.setAlpha(isInstantSelected ? 0.5f : 1f);
        mBinding.tvChargeInstant.setLocalText(isInstantSelected ?
                getString(R.string.ps_ev_exit_instant_charge) :
                getString(R.string.ps_ev_instant_charge));
    }

    /**
     * 退出即使充电
     */
    private void exitInstantCharge() {
        if (mPSDevice != null) {
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
            showTimeOutLoadinFramgment();
            mPSDevice.submit(params);
        }
    }

    /**
     * 关闭逆变器
     */
    private void submitCmdInverterClose() {
        if (mPSDevice != null) {
            BmtManager.getInstance().stopPolling();
            EventBus.getDefault().post(new ReOpenEvent(mDeviceId, mSubcategory));
            HashMap<String, Object> params = new HashMap<>();
            params.put(PSKeyConstant.CMD, DsCamCmd.SET_INVERTER_OPEN);
            params.put(PSKeyConstant.ON, false);
            List<Integer> indexs = new ArrayList<>();
            int phaseCount = BmtUtil.isThreePhase(mPSDevice) ? 3 : 1;
            for (int i = 0; i < phaseCount; i++) {
                indexs.add(i);
            }
            params.put(PSKeyConstant.INDEXS, indexs);
            mPSDevice.submit(params);
        }
    }

    private void resetItem() {
        if (mLowerUtility != null) {
            mLowerUtility.setStatus(-1);
            mLowerUtility.setSelected(false);
        }
//        if (mSolarOnly != null) {
//            mSolarOnly.setStatus(-1);
//            mSolarOnly.setSelected(false);
//        }
        if (mScheduleCharge != null) {
            mScheduleCharge.setStatus(-1);
            mScheduleCharge.setSelected(false);
        }
    }

    private PSEVChargeBean getSelectedItem() {
        PSEVChargeBean psevChargeBean = null;
        if (mAppliedPosition == 0) {
            psevChargeBean = mLowerUtility;
        }
//        else if (mAppliedPosition == 1) {
//            psevChargeBean = mSolarOnly;
//        }
        else if (mAppliedPosition == 1) {
            psevChargeBean = mScheduleCharge;
        }
        return psevChargeBean;
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            int status = DeviceHelper.getInt(map, PSKeyConstant.STATUS, -1);
            Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
            runOnMainThread(new Runnable() {
                @Override
                public void run() {
                    if (status == StatusConstant.STATUS_SUCCESS) {
                        switch (cmd) {

                            case BmtCmd.GET_CURRENT_EVADVANCESTATUS:
                            case BmtCmd.EV_ADVANCESTATUS_CHANGED:
                                int status = DeviceHelper.getInt(result, BmtDataKey.ADVANCE_STATUS, 0);
                                if (status <= 0 || status > 4) return;
                                setEvStatus(status - 1);
                                if (status == 3) {
                                    startPollTimer();
                                } else {
                                    closePollTimer();
                                }
                                break;

                            case BmtCmd.GET_EVCHARGING_INFO:
                                int batteryCharged = DeviceHelper.getInt(result, BmtDataKey.BATTERY_CHARGED, 0);
                                int chargeTime = DeviceHelper.getInt(result, BmtDataKey.CHARGE_TIME, 0);
                                double batteryChargedDou = batteryCharged / 10.0d;
                                mBinding.tvValue.setLocalText(UnitUtil.savePointStr(batteryChargedDou, 1) + getString(R.string.power_station_kWh));
                                String hmStr = TimeUtil.minute2HourMinute(chargeTime);
                                String[] hmArr = hmStr.split(":");
                                String timeStr = hmArr[0] + Local.s(getString(R.string.power_h)) + hmArr[1] + Local.s(getString(R.string.power_min));
                                mBinding.tvTime.setText(timeStr);
                                break;


                            case BmtCmd.GET_CURRENT_EV_CHARGING_MODE:
                            case BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                int evChargeMode = DeviceHelper.getInt(result, BmtDataKey.EV_CHARGING_MODE, 0);
                                if (evChargeMode <= 0 || evChargeMode == 2) return;
                                if (evChargeMode < 4) {
//                                    mAppliedPosition = evChargeMode - 1;
//                                    int pos = evChargeMode - 1;
                                    isInstantSelected = false;
                                    PSEVChargeBean psevChargeBean = null;
                                    if (evChargeMode == 1) {
                                        mAppliedPosition = 0;
                                        psevChargeBean = mLowerUtility;
                                    }
//                                    else if (evChargeMode == 2) {
//                                        psevChargeBean = mSolarOnly;
//                                    }
                                    else if (evChargeMode == 3) {
                                        mAppliedPosition = 1;
                                        psevChargeBean = mScheduleCharge;
                                    }
                                    if (psevChargeBean != null) {
                                        psevChargeBean.setStatus(1);
                                        psevChargeBean.setSelected(true);
                                        mEVChargeAdapter.notifyItemChanged(mAppliedPosition);
                                        setSelectValue(psevChargeBean, mAppliedPosition, true);
                                    }
                                } else {
                                    isInstantSelected = true;
                                    resetItem();
                                    mEVChargeAdapter.notifyDataSetChanged();
                                }
                                mBinding.viewSelected.getRoot().setAlpha(evChargeMode == 4 ? 0.5f : 1.0f);
                                setInstanceChargeView();
                                if (mBottomSheetBehavior.getState() == BottomSheetBehavior.STATE_COLLAPSED) {
                                    removeShrink();
                                    mEVChargeAdapter.setExpand(false);
                                }
                                break;

                            case BmtCmd.SET_EV_CHARGING_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
//                                for (PSEVChargeBean chargeBean : mEVChargeAdapter.getData()) {
//                                    chargeBean.setStatus(-1);
//                                    chargeBean.setSelected(false);
//                                }
                                resetItem();
//                                PSEVChargeBean psevChargeBean = mEVChargeAdapter.getItem(mAppliedPosition);
                                PSEVChargeBean psevChargeBean =  getSelectedItem();
                                if (isClickInstant) {
                                    isInstantSelected = !isInstantSelected;
                                    if (!isInstantSelected) {
                                        if (psevChargeBean != null) {
                                            psevChargeBean.setStatus(1);
                                            psevChargeBean.setSelected(true);
                                        }
                                    }
                                    mBinding.viewSelected.getRoot().setAlpha(0.5f);
                                    setInstanceChargeView();
                                } else {
                                    if (psevChargeBean != null) {
                                        psevChargeBean.setStatus(1);
                                        psevChargeBean.setSelected(true);
                                    }
                                }
                                mEVChargeAdapter.notifyDataSetChanged();
                                setSelectValue(psevChargeBean, mAppliedPosition, true);
                                break;
                        }
                    } else {
                        switch (cmd) {
                            case BmtCmd.GET_CURRENT_EV_CHARGING_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                break;

                            case BmtCmd.SET_EV_CHARGING_MODE:
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                                PSEVChargeBean psevChargeBean = getSelectedItem();
                                if (psevChargeBean!=null) {
                                    psevChargeBean.setStatus(-1);
                                    mEVChargeAdapter.notifyItemChanged(mAppliedPosition);
                                }
                                showErrorToast();
                                break;

                            case BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE:
                                closeLoadingFragment();
                                showErrorToast();
                                break;
                        }
                    }
                }
            });
        }
    }
}
