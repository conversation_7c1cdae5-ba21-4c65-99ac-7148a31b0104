package com.dinsafer.module.powerstation.event;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

public class BmtExceptionEvent {

    private final String deviceId;
    private final String subcategory;
    private final String cmd;
    @NonNull
    private final Map<String, Object> data;

    public BmtExceptionEvent(String deviceId, String subcategory, String cmd, @NonNull Map<String, Object> data) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.cmd = cmd;
        this.data = new HashMap<>();
        if (null != data) {
            this.data.putAll(data);
        }
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public String getCmd() {
        return cmd;
    }

    @NonNull
    public Map<String, Object> getData() {
        return data;
    }

    @Override
    public String toString() {
        return "BmtExceptionEvent{" +
                "deviceId='" + deviceId + '\'' +
                ", subcategory='" + subcategory + '\'' +
                ", cmd='" + cmd + '\'' +
                ", data=" + data +
                '}';
    }
}
