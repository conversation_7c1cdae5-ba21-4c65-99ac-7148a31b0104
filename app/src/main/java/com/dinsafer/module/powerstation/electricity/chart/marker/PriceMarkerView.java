package com.dinsafer.module.powerstation.electricity.chart.marker;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.util.Local;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;

import java.util.List;

public class PriceMarkerView extends CustomCombinedMarkerView {

    public PriceMarkerView(Context context) {
        super(context);
    }

    @Override
    public void setMarker(List<Entry> entries, List<Highlight> highlights) {
        llSubValue.setVisibility(GONE);
        setPriceMarker(entries, highlights);
    }

    private void setPriceMarker(List<Entry> entries, List<Highlight> highlights) {
        int xIndex = entries.get(0).getXIndex();
        int h = xIndex;

        if (hourCount == BaseChartFragment.DST_TIME_MINUTE) {
            if (h > 2) {
                h -= 1;
            }
        }

        if (hourCount == BaseChartFragment.SUMMER_TIME_MINUTE) {
            if (h > 1) {
                h += 1;
            }
        }
        String hour = h < 10 ? ("0" + h) + ":00" : h + ":00";
        tvTime.setLocalText(hour);
        if (entries.size() > 1) {
            float value = entries.get(0).getVal();
            String save2Point = ChartDataUtil.savePointStr(value, 2, 2);
            String valStr = value > 0 ? ("+" + save2Point + "%") : (save2Point + "%");
            tvKey.setText(Local.s(mContext.getString(R.string.relative_price)));
            tvValue.setText(valStr);
            llValue.setVisibility(VISIBLE);
        } else {
            llValue.setVisibility(GONE);
        }
    }
}
