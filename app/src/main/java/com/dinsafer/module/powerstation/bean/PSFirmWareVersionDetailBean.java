package com.dinsafer.module.powerstation.bean;

import androidx.annotation.Nullable;

import com.dinsafer.util.CollectionUtil;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> WZH
 * @date : 2022/12/1 17:15
 * @description :
 */
public class PSFirmWareVersionDetailBean {

    public static final int KEY_INDEX_COMMON_IOT_ID = 0;
    public static final int KEY_INDEX_COMMON_IOT_HARDWARE = 1;
    public static final int KEY_INDEX_COMMON_IOT = 2;
    public static final int KEY_INDEX_COMMON_MCU_ID = 3;
    public static final int KEY_INDEX_COMMON_MCU = 4;
    public static final int KEY_INDEX_COMMON_EV = 5;
    public static final int KEY_INDEX_COMMON_EV_BN = 6;

    public static final int KEY_INDEX_COMMON_SIDECAR = 7;

    public static final String KEY_VERSION_COMMON = "common";
    public static final String KEY_VERSION_INVERTER = "inverter";
    public static final String KEY_VERSION_CABINET = "cabinet";
    public static final String KEY_VERSION_BATTERY = "battery";

    public final static String SUPPLIER_BST = "BST";
    public final static String SUPPLIER_HS = "HS";

    private String type;
    private List<KeyValueBean> items;
    private int totalItemCount;
    private final AtomicInteger loadedCount = new AtomicInteger(0);

    private String inverterSupplier;


    public PSFirmWareVersionDetailBean(String type, List<KeyValueBean> items, int totalItemCount) {
        this.type = type;
        this.items = items;
        this.totalItemCount = totalItemCount;
    }

    public void setInverterSupplier(String inverterSupplier) {
        this.inverterSupplier = inverterSupplier;
    }

    public String getInverterSupplier() {
        return inverterSupplier;
    }

    public String getType() {
        return type == null ? "" : type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Nullable
    public List<KeyValueBean> getItems() {
        return items;
    }

    public void setItems(List<KeyValueBean> items) {
        this.items = items;
    }

    public void addItem(KeyValueBean item) {
        if (CollectionUtil.isListNotEmpty(items)) {
            items.add(item);
            totalItemCount = totalItemCount + 1;
        }
    }

    public int getTotalItemCount() {
        return totalItemCount;
    }

    public void setTotalItemCount(int totalItemCount) {
        this.totalItemCount = totalItemCount;
        loadedCount.set(0);
    }

    public void markLoadItem() {
        loadedCount.getAndIncrement();
    }

    public boolean isLoadFinished() {
        final int currentCount = loadedCount.get();
        return currentCount >= totalItemCount;
    }
}
