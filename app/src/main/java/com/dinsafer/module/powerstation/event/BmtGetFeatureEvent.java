package com.dinsafer.module.powerstation.event;

import java.util.Map;

public class BmtGetFeatureEvent {

    private final String deviceId;
    private final String subcategory;
    private Map<String, Object> result;

    public BmtGetFeatureEvent(String deviceId, String subcategory) {
        this.deviceId = deviceId;
        this.subcategory =subcategory;
    }

    public BmtGetFeatureEvent(String deviceId, String subcategory, Map<String, Object> result) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.result = result;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public Map<String, Object> getResult() {
        return result;
    }

    public void setResult(Map<String, Object> result) {
        this.result = result;
    }
}
