package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathMeasure;
import android.graphics.RectF;
import android.graphics.Shader;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

import androidx.annotation.Nullable;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.DDLog;

import java.util.HashMap;
import java.util.Map;

/**
 * 电流图
 */
public class CurrentDiagramView2 extends View {

    // 左边坐标
    float leftPointX;
    float leftPointY;
    // 上边坐标
    float topPointX;
    float topPointY;
    // 右边坐标
    float rightPointX;
    float rightPointY;
    // 下边坐标
    float bottomPointX;
    float bottomPointY;
    // 左下汇合点坐标
    float leftBottomX;
    float leftBottomY;
    // 左下第一条坐标
    float leftBottomX1;
    float leftBottomY1;
    // 左下第二条坐标
    float leftBottomX2;
    float leftBottomY2;
    // 左下控制点坐标
    float leftBottomQuadX;
    float leftBottomQuadY;

    float bottomLeftQuadX;
    float bottomLeftQuadY;

    private float mRightX;

    private RectF mRect;
    private int mColor = Color.BLACK;
    private int mRadius = 60;
    private int mActualRadius;
    private Paint mPaint;
    private Path mCurrentPath;

    private Path firsPath; // 用于截取path
    private Path secondPath; // 用于截取path
    private Path thirdPath; // 用于截取path
    private Path forthPath; // 用于截取path


    // 第一限项控制点
    private float arcQuadX1;
    private float arcQuadY1;
    private float quadX1;
    private float quadY1;

    // 第二限项控制点
    private float arcQuadX2;
    private float arcQuadY2;
    private float quadX2;
    private float quadY2;

    // 第三限项控制点
    private float arcQuadX3;
    private float arcQuadY3;
    private float quadX3;
    private float quadY3;

    // 第四限项控制点
    private float arcQuadX4;
    private float arcQuadY4;
    private float quadX4;
    private float quadY4;

    private float lbArcX;
    private float lbArxY;
    private float endY;
    private float endX;

    private int mPathColor = Color.BLACK; // 路径颜色
    private int mAnimPathColor = Color.RED;  // 动画路径颜色
    private int mPathWidth = 3;  // 路径宽度
    private int offsetX = 0; // 中心X偏移量
    private int offsetY = 0; // 中心Y偏移量
    private int paddingTopBottom = 20;
    private int offsetLeft;
    private int offsetTop;
    private int offsetRight;
    private int offsetBottom;

    private ValueAnimator mValueAnimator;


    private final int REFRESH_CURRENT = 0X01;


    private boolean hasSetMRect;

    private int startColor = Color.parseColor("#104485E8");
    private int endColor = Color.parseColor("#4485E8");
    private HashMap<String, PathBean> mPathMap = new HashMap<>();
    private boolean isStop;

    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mAnimRunnable = new Runnable() {
        @Override
        public void run() {
            if (mValueAnimator != null) {
                mValueAnimator.start();
            }
        }
    };

    public CurrentDiagramView2(Context context) {
        this(context, null);
    }

    public CurrentDiagramView2(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CurrentDiagramView2(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CurrentDiagramView);
        mActualRadius = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_radius, 50);
        mPathColor = typedArray.getColor(R.styleable.CurrentDiagramView_path_color, mPathColor);
        mAnimPathColor = typedArray.getColor(R.styleable.CurrentDiagramView_anim_path_color, mAnimPathColor);
        mPathWidth = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_path_width, dp2px(context, 3));
        offsetX = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_x, 0);
        offsetY = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_y, 0);
        offsetLeft = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_left, 0);
        offsetTop = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_top, 0);
        offsetRight = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_right, 0);
        offsetBottom = (int) typedArray.getDimension(R.styleable.CurrentDiagramView_offset_bottom, 0);
        typedArray.recycle();
        init();
    }

    private void init() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStrokeWidth(mPathWidth);
        mPaint.setStyle(Paint.Style.STROKE);

        mPaint.setColor(mPathColor);
        mCurrentPath = new Path();

        mRect = new RectF();

        firsPath = new Path();
        secondPath = new Path();
        thirdPath = new Path();
        forthPath = new Path();

        mValueAnimator = ValueAnimator.ofFloat(0, 1);
        mValueAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!isStop) {
                    mHandler.postDelayed(mAnimRunnable, 500);
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        mValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                float value = (float) valueAnimator.getAnimatedValue();
                for (Map.Entry<String, PathBean> map : mPathMap.entrySet()) {
                    map.getValue().setAnimatorValue1(value);
                    map.getValue().setAnimatorValue2(value);
                    map.getValue().setAnimatorValue3(value);
                    map.getValue().setAnimatorValue4(value);
                    map.getValue().setAnimatorValue5(value);
                }
                invalidate();
            }
        });
        mValueAnimator.setDuration(3000);
        mValueAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        //设置宽高,默认200dp
        int defaultSize = dp2px(getContext(), 200);
        setMeasuredDimension(measureWidth(widthMeasureSpec, defaultSize),
                measureHeight(heightMeasureSpec, defaultSize));
    }

    /**
     * 测量宽
     *
     * @param measureSpec
     * @param defaultSize
     * @return
     */
    private int measureWidth(int measureSpec, int defaultSize) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = defaultSize + getPaddingLeft() + getPaddingRight();
            if (specMode == MeasureSpec.AT_MOST) {
                result = Math.min(result, specSize);
            }
        }
        result = Math.max(result, getSuggestedMinimumWidth());
        return result;
    }

    /**
     * 测量高
     *
     * @param measureSpec
     * @param defaultSize
     * @return
     */
    private int measureHeight(int measureSpec, int defaultSize) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = defaultSize + getPaddingTop() + getPaddingBottom();
            if (specMode == MeasureSpec.AT_MOST) {
                result = Math.min(result, specSize);
            }
        }
        result = Math.max(result, getSuggestedMinimumHeight());
        return result;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //计算圆的圆心
        int cx = getPaddingLeft() + (getWidth() - getPaddingLeft() - getPaddingRight()) / 2 - offsetX;
        int cy = getPaddingTop() + (getHeight() - getPaddingTop() - getPaddingBottom()) / 2 - offsetY;
        mRightX = getWidth() - getPaddingLeft() - 3;
//        if (mRect == null) {
//            mRect = new RectF(cx - mActualRadius, cy - mActualRadius, cx + mActualRadius, cy + mActualRadius);
//        }
        if (!hasSetMRect) {
            mRect.left = cx - mActualRadius;
            mRect.top = cy - mActualRadius;
            mRect.right = cx + mActualRadius;
            mRect.bottom = cy + mActualRadius;
            hasSetMRect = true;
        }

        drawPathTB(canvas, cx, cy);

        drawPath1(canvas, cx, cy);
        drawPath2(canvas, cx, cy);
        drawPath3(canvas, cx, cy);
        drawPath4(canvas, cx, cy);
        drawPath5(canvas, cx, cy);

    }

    /**
     * 第一条路径动画
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath1(Canvas canvas, int cx, int cy) {
        for (Map.Entry<String, PathBean> entry : mPathMap.entrySet()) {
            PathBean pathBean = entry.getValue();
            if (pathBean.isDrawPath1()) {
                Path path1 = pathBean.getPath1();
                PathMeasure pathMeasure1 = pathBean.getPathMeasure1();
                if (path1.isEmpty()) {
                    switch (Integer.parseInt(entry.getKey())) {
                        case PathBean.SOLAR_SUPPLY:
                            path1.moveTo(cx, offsetTop);
                            path1.lineTo(topPointX, topPointY);
                            path1.quadTo(quadX4, quadY4, leftPointX, leftPointY);
                            path1.lineTo(offsetLeft, cy);
                            break;

                        case PathBean.GRID_SUPPLY:
                            path1.moveTo(mRightX, offsetTop);
                            path1.lineTo(mRightX, cy);
                            path1.lineTo(rightPointX, rightPointY);
                            float qx = (quadX1 + rightPointX) / 2;
                            float qy = (quadY1 + rightPointY) / 2;
                            path1.quadTo(qx, qy, arcQuadX1, arcQuadY1);
                            path1.arcTo(mRect, 315, -90, false);
                            float qx2 = (quadX4 + leftPointX) / 2;
                            float qy2 = (quadY4 + leftPointY) / 2;
                            path1.quadTo(qx2, qy2, leftPointX, leftPointY);
                            path1.lineTo(offsetLeft, cy);
                            break;

                        case PathBean.VEHICLE_SUPPLY:
                            path1.moveTo(endX, endY);
                            path1.lineTo(lbArcX, lbArxY);
                            path1.quadTo(leftBottomX1, leftBottomY1, leftPointX, leftPointY);
                            path1.lineTo(offsetLeft, cy);
                            break;

                        case PathBean.BATTERY_SUPPLY:
                            path1.moveTo(offsetLeft, cy);
                            path1.lineTo(leftPointX, leftPointY);
                            path1.quadTo(leftBottomX1, leftBottomY1, lbArcX, lbArxY);
                            path1.lineTo(endX, endY);
                            break;
                    }
                    pathMeasure1.setPath(path1, false);
                    pathBean.setLength1(pathMeasure1.getLength());

                }
                Path dst1 = pathBean.getDst1();
                dst1.reset();
                float stop = pathBean.getLength1() * pathBean.getAnimatorValue1();
                float start = (float) (stop - ((0.5 - Math.abs(pathBean.getAnimatorValue1() - 0.5)) * pathBean.getLength1()));
                pathMeasure1.getSegment(start, stop + 15, dst1, true);

                pathMeasure1.getPosTan(start, pathBean.getPointStart1(), null);
                pathMeasure1.getPosTan(stop, pathBean.getPointEnd1(), null);

                RectF rectF1 = pathBean.getRectF1();
                dst1.computeBounds(rectF1, true);
                float xDistance = rectF1.right - rectF1.left;
                float yDistance = rectF1.bottom - rectF1.top;
                Paint paint1 = pathBean.getAnimPaint1();
                startColor = pathBean.getStartColor1();
                endColor = pathBean.getEndColor1();
                if (yDistance > xDistance) {
                    paint1.setShader(new LinearGradient(0, rectF1.top, 0, rectF1.bottom,
                            pathBean.startGreaterEndY1() ? endColor : startColor, pathBean.startGreaterEndY1() ? startColor : endColor, Shader.TileMode.CLAMP));
                } else {
                    paint1.setShader(new LinearGradient(rectF1.left, 0,
                            rectF1.right, 0,
                            pathBean.startGreaterEndX1() ? endColor : startColor, pathBean.startGreaterEndX1() ? startColor : endColor, Shader.TileMode.CLAMP));
                }
                canvas.drawPath(dst1, paint1);
            }
        }
    }

    /**
     * 第二条动画路径
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath2(Canvas canvas, int cx, int cy) {
        for (Map.Entry<String, PathBean> entry : mPathMap.entrySet()) {
            PathBean pathBean = entry.getValue();
            if (pathBean.isDrawPath2()) {
                Path path2 = pathBean.getPath2();
                PathMeasure pathMeasure2 = pathBean.getPathMeasure2();
                if (path2.isEmpty()) {
                    float qx;
                    float qy;
                    switch (Integer.parseInt(entry.getKey())) {
                        case PathBean.SOLAR_SUPPLY:
                            path2.moveTo(cx, offsetTop);
                            path2.lineTo(topPointX, topPointY);
                            qx = (quadX4 + topPointX) / 2;
                            qy = (quadY4 + topPointY) / 2;
                            path2.quadTo(qx, qy, arcQuadX4, arcQuadY4);
                            path2.arcTo(mRect, 225, -65, false);
                            path2.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX, leftBottomY);
                            path2.lineTo(endX, endY);
                            break;

                        case PathBean.GRID_SUPPLY:
                            path2.moveTo(mRightX, offsetTop);
                            path2.lineTo(mRightX, cy);
                            path2.lineTo(rightPointX, rightPointY);
                            qx = (quadX2 + rightPointX) / 2;
                            qy = (quadY2 + rightPointY) / 2;
                            path2.quadTo(qx, qy, arcQuadX2, arcQuadY2);
                            path2.arcTo(mRect, 45, 65, false);
                            path2.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX, leftBottomY);
                            path2.lineTo(endX, endY);
                            break;

                        case PathBean.VEHICLE_SUPPLY:
                            double arc = (2 * Math.PI / 360) * 215;
                            float x = (float) (cx + Math.sin(arc) * (mActualRadius));
                            float y = (float) (cy - Math.cos(arc) * (mActualRadius));

                            path2.moveTo(endX, endY);
                            path2.lineTo(leftBottomX, leftBottomY);
//                            path2.quadTo(qx, qy, x, y);
//                            dx = (x-leftPointX)/(bottomPointX-leftPointX);
//                            dy = (y-leftPointY)/(bottomPointY-leftPointY);
//                            qx = (quadX3+bottomPointX)*dx;
//                            qy = (quadY3+bottomPointY)*dy;
//                            path2.quadTo(qx, qy, bottomPointX, bottomPointY);
                            path2.quadTo(leftBottomQuadX, leftBottomQuadY, x, y);
                            qx = (quadX3 + bottomPointX) / 1.999f;
                            qy = (quadY3 + bottomPointY) / 1.999f;
                            path2.quadTo(qx, qy, bottomPointX, bottomPointY);
                            path2.lineTo(cx, getHeight() - offsetBottom);
                            break;

                        case PathBean.BATTERY_SUPPLY:
                            path2.moveTo(offsetLeft, cy);
                            path2.lineTo(leftPointX, leftPointY);
                            path2.quadTo(quadX3, quadY3, bottomPointX, bottomPointY);
                            path2.lineTo(cx, getHeight() - offsetBottom);
                            break;

                    }
                    pathMeasure2.setPath(path2, false);
                    pathBean.setLength2(pathMeasure2.getLength());

                }
                Path dst2 = pathBean.getDst2();
                dst2.reset();
                float stop = pathBean.getLength2() * pathBean.getAnimatorValue2();
                float start = (float) (stop - ((0.5 - Math.abs(pathBean.getAnimatorValue2() - 0.5)) * pathBean.getLength2()));
                pathMeasure2.getSegment(start, stop + 15, dst2, true);

                pathMeasure2.getPosTan(start, pathBean.getPointStart2(), null);
                pathMeasure2.getPosTan(stop, pathBean.getPointEnd2(), null);

                RectF rectF2 = pathBean.getRectF2();
                dst2.computeBounds(rectF2, true);
                Paint animPaint2 = pathBean.getAnimPaint2();
                float xDistance = rectF2.right - rectF2.left;
                float yDistance = rectF2.bottom - rectF2.top;
                startColor = pathBean.getStartColor2();
                endColor = pathBean.getEndColor2();

                if (yDistance > xDistance) {
                    animPaint2.setShader(new LinearGradient(0, rectF2.top, 0, rectF2.bottom,
                            pathBean.startGreaterEndY2() ? endColor : startColor, pathBean.startGreaterEndY2() ? startColor : endColor, Shader.TileMode.CLAMP));
                } else {
                    animPaint2.setShader(new LinearGradient(rectF2.left, 0,
                            rectF2.right, 0,
                            pathBean.startGreaterEndX2() ? endColor : startColor, pathBean.startGreaterEndX2() ? startColor : endColor, Shader.TileMode.CLAMP));
                }
//                animPaint2.setColor(Color.RED);
                canvas.drawPath(dst2, animPaint2);
            }
        }
    }

    /**
     * 第三条动画路径
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath3(Canvas canvas, int cx, int cy) {
        for (Map.Entry<String, PathBean> entry : mPathMap.entrySet()) {
            PathBean pathBean = entry.getValue();
            if (pathBean.isDrawPath3()) {
                Path path3 = pathBean.getPath3();
                PathMeasure pathMeasure3 = pathBean.getPathMeasure3();
                float qx;
                float qy;
                if (path3.isEmpty()) {
                    switch (Integer.parseInt(entry.getKey())) {
                        case PathBean.SOLAR_SUPPLY:
                            path3.moveTo(cx, offsetTop);
                            path3.lineTo(topPointX, topPointY);
                            qx = (quadX4 + topPointX) / 2;
                            qy = (quadY4 + topPointY) / 2;
                            path3.quadTo(qx, qy, arcQuadX4, arcQuadY4);
                            path3.arcTo(mRect, 225, -90, false);
                            float qx1 = (quadX3 + bottomPointX) / 2;
                            float qy2 = (quadY3 + bottomPointY) / 2;
                            path3.quadTo(qx1, qy2, bottomPointX, bottomPointY);
                            path3.lineTo(bottomPointX, getHeight() - offsetBottom);
                            break;

                        case PathBean.GRID_SUPPLY:
                            path3.moveTo(mRightX, offsetTop);
                            path3.lineTo(mRightX, cy);
                            path3.lineTo(rightPointX, rightPointY);
                            path3.quadTo(quadX2, quadY2, bottomPointX, bottomPointY);
                            path3.lineTo(bottomPointX, getHeight() - offsetBottom);
                            break;

                        case PathBean.VEHICLE_SUPPLY:
                            path3.moveTo(endX, endY);
                            path3.lineTo(leftBottomX, leftBottomY);
//                            qx = (bottomLeftQuadX + leftBottomX) / 2;
//                            qy = (bottomLeftQuadY + leftBottomY) / 2;
                            path3.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX2, leftBottomY2);
//                            path3.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX2, leftBottomY2);
                            path3.arcTo(mRect, 115, -65, false);
                            qx = (quadX2 + rightPointX) / 2;
                            qy = (quadY2 + rightPointY) / 2;
                            path3.quadTo(qx, qy, rightPointX, rightPointY);
                            path3.lineTo(mRightX, cy);
                            path3.lineTo(mRightX, getHeight() - offsetBottom);
                            break;

                        case PathBean.BATTERY_SUPPLY:
                            path3.moveTo(offsetLeft, cy);
                            path3.lineTo(leftPointX, leftPointY);
                            qx = (quadX4 + leftPointX) / 2;
                            qy = (quadY4 + leftPointY) / 2;
                            path3.quadTo(qx, qy, arcQuadX4, arcQuadY4);
                            path3.arcTo(mRect, 225, 90, false);
                            qx = (quadX1 + rightPointX) / 2;
                            qy = (quadY1 + rightPointY) / 2;
                            path3.quadTo(qx, qy, rightPointX, rightPointY);
                            path3.lineTo(mRightX, cy);
                            path3.lineTo(mRightX, getHeight() - offsetBottom);
                            break;
                    }
                    pathMeasure3.setPath(path3, false);
                    pathBean.setLength3(pathMeasure3.getLength());
                }
                Path dst3 = pathBean.getDst3();
                dst3.reset();
                float stop = pathBean.getLength3() * pathBean.getAnimatorValue3();
                float start = (float) (stop - ((0.5 - Math.abs(pathBean.getAnimatorValue3() - 0.5)) * pathBean.getLength3()));
                pathMeasure3.getSegment(start, stop + 15, dst3, true);

                pathMeasure3.getPosTan(start, pathBean.getPointStart3(), null);
                pathMeasure3.getPosTan(stop, pathBean.getPointEnd3(), null);

                RectF rectF3 = pathBean.getRectF3();
                dst3.computeBounds(rectF3, true);
                float xDistance = rectF3.right - rectF3.left;
                float yDistance = rectF3.bottom - rectF3.top;
                Paint animPaint3 = pathBean.getAnimPaint3();
                startColor = pathBean.getStartColor3();
                endColor = pathBean.getEndColor3();

                if (yDistance > xDistance) {
                    animPaint3.setShader(new LinearGradient(0, rectF3.top, 0, rectF3.bottom,
                            pathBean.startGreaterEndY3() ? endColor : startColor, pathBean.startGreaterEndY3() ? startColor : endColor, Shader.TileMode.CLAMP));
                } else {
                    animPaint3.setShader(new LinearGradient(rectF3.left, 0,
                            rectF3.right, 0,
                            pathBean.startGreaterEndX3() ? endColor : startColor, pathBean.startGreaterEndX3() ? startColor : endColor, Shader.TileMode.CLAMP));
                }
//                animPaint3.setColor(Color.RED);
                canvas.drawPath(dst3, animPaint3);
            }
        }
    }

    /**
     * 第四条路径动画
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath4(Canvas canvas, int cx, int cy) {
        for (Map.Entry<String, PathBean> entry : mPathMap.entrySet()) {
            PathBean pathBean = entry.getValue();
            if (pathBean.isDrawPath4()) {
                Path path4 = pathBean.getPath4();
                PathMeasure pathMeasure4 = pathBean.getPathMeasure4();
                float qx;
                float qy;
                if (path4.isEmpty()) {
                    switch (Integer.parseInt(entry.getKey())) {
                        case PathBean.SOLAR_SUPPLY:
                            path4.moveTo(cx, offsetTop);
                            path4.lineTo(topPointX, topPointY);
                            path4.quadTo(quadX1, quadY1, rightPointX, rightPointY);
                            path4.lineTo(mRightX, cy);
                            path4.lineTo(mRightX, getHeight() - offsetBottom);
                            break;

                        case PathBean.GRID_SUPPLY:
                            path4.moveTo(mRightX, offsetTop);
                            path4.lineTo(mRightX, getHeight() - offsetBottom);
                            break;

                        case PathBean.VEHICLE_SUPPLY:
                            path4.moveTo(endX, endY);
                            path4.lineTo(leftBottomX, leftBottomY);
                            path4.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX2, leftBottomY2);
                            path4.arcTo(mRect, 115, -65, false);
                            qx = (quadX2 + rightPointX) / 2;
                            qy = (quadY2 + rightPointY) / 2;
                            path4.quadTo(qx, qy, rightPointX, rightPointY);
                            path4.lineTo(mRightX, cy);
                            path4.lineTo(mRightX, offsetTop);
                            break;

                        case PathBean.BATTERY_SUPPLY:
                            path4.moveTo(offsetLeft, cy);
                            path4.lineTo(leftPointX, leftPointY);
                            qx = (quadX4 + leftPointX) / 2;
                            qy = (quadY4 + leftPointY) / 2;
                            path4.quadTo(qx, qy, arcQuadX4, arcQuadY4);
                            path4.arcTo(mRect, 225, 90, false);
                            qx = (quadX1 + rightPointX) / 2;
                            qy = (quadY1 + rightPointY) / 2;
                            path4.quadTo(qx, qy, rightPointX, rightPointY);
                            path4.lineTo(mRightX, cy);
                            path4.lineTo(mRightX, offsetTop);
                            break;
                    }
                    pathMeasure4.setPath(path4, false);
                    pathBean.setLength4(pathMeasure4.getLength());
                }

                Path dst4 = pathBean.getDst4();
                dst4.reset();
                float stop = pathBean.getLength4() * pathBean.getAnimatorValue4();
                float start = (float) (stop - ((0.5 - Math.abs(pathBean.getAnimatorValue4() - 0.5)) * pathBean.getLength4()));
                pathMeasure4.getSegment(start, stop + 15, dst4, true);

                pathMeasure4.getPosTan(start, pathBean.getPointStart4(), null);
                pathMeasure4.getPosTan(stop, pathBean.getPointEnd4(), null);

                RectF rectF4 = pathBean.getRectF4();
                dst4.computeBounds(rectF4, true);

                float xDistance = rectF4.right - rectF4.left;
                float yDistance = rectF4.bottom - rectF4.top;
                Paint animPaint4 = pathBean.getAnimPaint4();

                startColor = pathBean.getStartColor4();
                endColor = pathBean.getEndColor4();

                if (yDistance > xDistance) {
                    animPaint4.setShader(new LinearGradient(0, rectF4.top, 0, rectF4.bottom,
                            pathBean.startGreaterEndY4() ? endColor : startColor, pathBean.startGreaterEndY4() ? startColor : endColor, Shader.TileMode.CLAMP));
                } else {
                    animPaint4.setShader(new LinearGradient(rectF4.left, 0,
                            rectF4.right, 0,
                            pathBean.startGreaterEndX4() ? endColor : startColor, pathBean.startGreaterEndX4() ? startColor : endColor, Shader.TileMode.CLAMP));
                }
                canvas.drawPath(dst4, animPaint4);
            }
        }
    }

    /**
     * 第五条路径动画(太阳能才有)
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPath5(Canvas canvas, int cx, int cy) {
        String key = String.valueOf(PathBean.SOLAR_SUPPLY);
        if (mPathMap.containsKey(key)) {
            PathBean pathBean = mPathMap.get(key);
            if (pathBean.isDrawPath5()) {
                Path path5 = pathBean.getPath5();
                PathMeasure pathMeasure5 = pathBean.getPathMeasure5();
                if (path5.isEmpty()) {
                    path5.moveTo(cx, offsetTop);
                    path5.lineTo(topPointX, topPointY);
                    path5.quadTo(quadX1, quadY1, rightPointX, rightPointY);
                    path5.lineTo(mRightX, cy);
                    path5.lineTo(mRightX, offsetTop);
                    pathMeasure5.setPath(path5, false);
                    pathBean.setLength5(pathMeasure5.getLength());
                }
                Path dst5 = pathBean.getDst5();
                dst5.reset();
                float stop = pathBean.getLength5() * pathBean.getAnimatorValue5();
                float start = (float) (stop - ((0.5 - Math.abs(pathBean.getAnimatorValue5() - 0.5)) * pathBean.getLength5()));
                pathMeasure5.getSegment(start, stop + 15, dst5, true);
                pathMeasure5.getPosTan(start, pathBean.getPointStart5(), null);
                pathMeasure5.getPosTan(stop, pathBean.getPointEnd5(), null);
                RectF rectF5 = pathBean.getRectF5();
                dst5.computeBounds(rectF5, true);
                float xDistance = rectF5.right - rectF5.left;
                float yDistance = rectF5.bottom - rectF5.top;
                Paint animPaint5 = pathBean.getAnimPaint5();

                startColor = pathBean.getStartColor5();
                endColor = pathBean.getEndColor5();

                if (yDistance > xDistance) {
                    animPaint5.setShader(new LinearGradient(0, rectF5.top, 0, rectF5.bottom,
                            pathBean.startGreaterEndY5() ? endColor : startColor, pathBean.startGreaterEndY5() ? startColor : endColor, Shader.TileMode.CLAMP));
                } else {
                    animPaint5.setShader(new LinearGradient(rectF5.left, 0,
                            rectF5.right, 0,
                            pathBean.startGreaterEndX5() ? endColor : startColor, pathBean.startGreaterEndX5() ? startColor : endColor, Shader.TileMode.CLAMP));
                }
                canvas.drawPath(dst5, animPaint5);
            }
        }
    }

    /**
     * 画拓补图
     *
     * @param canvas
     * @param cx
     * @param cy
     */
    private void drawPathTB(Canvas canvas, int cx, int cy) {
        if (mCurrentPath.isEmpty()) {
            mCurrentPath.addCircle(cx, cy, mActualRadius, Path.Direction.CCW);
            // 左边坐标
            leftPointX = (float) (cx - (2 * mActualRadius));
            leftPointY = cy;
            // 上边坐标
            topPointX = cx;
            topPointY = (float) (cy - (2 * mActualRadius));
            // 右边坐标
            rightPointX = (float) (cx + (2 * mActualRadius));
            rightPointY = cy;
            // 下边坐标
            bottomPointX = cx;
            bottomPointY = (float) (cy + (2 * mActualRadius));

            // 绘制路径
            mCurrentPath.moveTo(topPointX, offsetTop);
            mCurrentPath.lineTo(topPointX, topPointY);
            // 第一限项控制点
            double arcQuad1 = (2 * Math.PI / 360) * 45;
            arcQuadX1 = (float) (cx + Math.sin(arcQuad1) * (mActualRadius));
            arcQuadY1 = (float) (cy - Math.cos(arcQuad1) * (mActualRadius));
            // 贝塞尔公式算得
            quadX1 = (float) ((arcQuadX1 - 0.25 * topPointX - 0.25 * rightPointX) / 0.5);
            quadY1 = (float) ((arcQuadY1 - 0.25 * topPointY - 0.25 * rightPointY) / 0.5);
            mCurrentPath.quadTo(quadX1, quadY1, rightPointX, rightPointY);
            if (firsPath.isEmpty()) {
                firsPath.moveTo(topPointX, topPointY);
                firsPath.quadTo(quadX1, quadY1, rightPointX, rightPointY);
            }
            mCurrentPath.lineTo(getWidth() - 1, rightPointY);
            mCurrentPath.moveTo(rightPointX, rightPointY);
            // 第二限项控制点
            double arcQuad2 = (2 * Math.PI / 360) * 135;
            arcQuadX2 = (float) (cx + Math.sin(arcQuad2) * (mActualRadius));
            arcQuadY2 = (float) (cy - Math.cos(arcQuad2) * (mActualRadius));
            quadX2 = (float) ((arcQuadX2 - 0.25 * rightPointX - 0.25 * bottomPointX) / 0.5);
            quadY2 = (float) ((arcQuadY2 - 0.25 * rightPointY - 0.25 * bottomPointY) / 0.5);
            mCurrentPath.quadTo(quadX2, quadY2, bottomPointX, bottomPointY);
            if (secondPath.isEmpty()) {
                secondPath.moveTo(rightPointX, rightPointY);
                secondPath.quadTo(quadX2, quadY2, bottomPointX, bottomPointY);
            }
            mCurrentPath.lineTo(bottomPointX, getHeight() - offsetBottom);
            mCurrentPath.moveTo(bottomPointX, bottomPointY);
            // 第三限项控制点
            double arcQuad3 = (2 * Math.PI / 360) * 225;
            arcQuadX3 = (float) (cx + Math.sin(arcQuad3) * (mActualRadius));
            arcQuadY3 = (float) (cy - Math.cos(arcQuad3) * (mActualRadius));
            quadX3 = calculateBezierX(arcQuadX3, bottomPointX, leftPointX, 0.5f);
            quadY3 = calculateBezierY(arcQuadY3, bottomPointY, leftPointY, 0.5f);
            mCurrentPath.quadTo(quadX3, quadY3, leftPointX, leftPointY);
            if (thirdPath.isEmpty()) {
                thirdPath.moveTo(leftPointX, leftPointY);
                thirdPath.quadTo(quadX3, quadY3, bottomPointX, bottomPointY);
            }
            mCurrentPath.lineTo(offsetLeft, leftPointY);
            mCurrentPath.moveTo(leftPointX, leftPointY);
            // 第四限项控制点
            double arcQuad4 = (2 * Math.PI / 360) * 315;
            arcQuadX4 = (float) (cx + Math.sin(arcQuad4) * (mActualRadius));
            arcQuadY4 = (float) (cy - Math.cos(arcQuad4) * (mActualRadius));
            quadX4 = calculateBezierX(arcQuadX4, leftPointX, topPointX, 0.5f);
            quadY4 = calculateBezierY(arcQuadY4, leftPointY, topPointY, 0.5f);
            mCurrentPath.quadTo(quadX4, quadY4, topPointX, topPointY);
            if (forthPath.isEmpty()) {
                forthPath.moveTo(topPointX, topPointY);
                forthPath.quadTo(quadX4, quadY4, leftPointX, leftPointY);
            }

            // 左下路径
            double arc1 = (2 * Math.PI / 360) * 255;
            double arc2 = (2 * Math.PI / 360) * 205;
            leftBottomX1 = (float) (cx + Math.sin(arc1) * (mActualRadius));
            leftBottomY1 = (float) (cy - Math.cos(arc1) * (mActualRadius));
            leftBottomX2 = (float) (cx + Math.sin(arc2) * (mActualRadius));
            leftBottomY2 = (float) (cy - Math.cos(arc2) * (mActualRadius));
            leftBottomX = (leftPointX + cy) / 2 + mActualRadius / 2;
            leftBottomY = (leftBottomX - cx) / (quadX3 - cx) * (quadY3 - cy) + cy;
            leftBottomQuadX = (leftBottomX1 + leftBottomX2) / 2;
            leftBottomQuadY = (leftBottomY1 + leftBottomY2) / 2;
            mCurrentPath.moveTo(leftBottomX, leftBottomY);
            mCurrentPath.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX1, leftBottomY1);
            mCurrentPath.moveTo(leftBottomX2, leftBottomY2);
            mCurrentPath.quadTo(leftBottomQuadX, leftBottomQuadY, leftBottomX, leftBottomY);
//            bottomLeftQuadX = calculateBezierX(leftBottomX, leftBottomX2, bottomPointX, 0.5f);
//            bottomLeftQuadY = calculateBezierY(leftBottomY, leftBottomY2, bottomPointY, 0.5f);
//            mCurrentPath.moveTo(bottomPointX, bottomPointY);
//            mCurrentPath.quadTo((cx+leftBottomX2)/2, (quadY3+cy)/2, leftBottomX, leftBottomY);
//            mCurrentPath.quadTo(bottomLeftQuadX, bottomLeftQuadY, leftBottomX, leftBottomY);
            mCurrentPath.moveTo(leftBottomX, leftBottomY);
            lbArcX = (leftPointX + cy) / 2;
            lbArxY = (lbArcX - leftBottomX) / (quadX3 - leftBottomX) * (quadY3 - leftBottomY) + leftBottomY;
            endY = getHeight();
            endX = (endY - lbArxY) / (leftBottomY - lbArxY) * (leftBottomX - lbArcX) + lbArcX;
            mCurrentPath.lineTo(endX, endY);
            mCurrentPath.moveTo(mRightX, offsetTop);
            mCurrentPath.lineTo(mRightX, getHeight() - offsetBottom);
            mCurrentPath.moveTo(leftPointX, leftPointY);
            mCurrentPath.quadTo(leftBottomX1, leftBottomX1, lbArcX, lbArxY);
        }
        canvas.drawPath(mCurrentPath, mPaint);
    }

//    private PointF getBezierPoint(PointF start, PointF end, PointF control, float t) {
//        PointF bezierPoint = new PointF();
//        bezierPoint.x = (1 - t) * (1 - t) * start.x + 2 * t * (1 - t) * control.x + t * t * end.x;
//        bezierPoint.y = (1 - t) * (1 - t) * start.y + 2 * t * (1 - t) * control.y + t * t * end.y;
//        return bezierPoint;
//    }

    /**
     * 贝塞尔控制点X坐标
     *
     * @param x1
     * @param x2
     * @param x3
     * @param t
     * @return
     */
    private float calculateBezierX(float x1, float x2, float x3, float t) {
        float x = (x1 - (1 - t) * (1 - t) * x2 - t * t * x3) / (2 * t * (1 - t));
        return x;
    }

    /**
     * 贝塞尔控制点Y坐标
     *
     * @param y1
     * @param y2
     * @param y3
     * @param t
     * @return
     */
    private float calculateBezierY(float y1, float y2, float y3, float t) {
        float y = (y1 - (1 - t) * (1 - t) * y2 - t * t * y3) / (2 * t * (1 - t));
        return y;
    }

    /**
     * 贝塞尔通过x求t
     *
     * @param startX
     * @param controlX
     * @param endX
     * @return
     */
    private float calculateBezierT(float startX, float controlX, float endX) {
        float t = (float) Math.sqrt(startX / (0.25 * startX + controlX + endX));
        return t;
    }

    private float calculateBezierCenterY(float startY, float controlY, float endY, float t) {
        float y = (1 - t) * (1 - t) * startY + 2 * t * (1 - t) * controlY + t * t * endY;
        return y;
    }


    /**
     * 开始动画
     */
    public void startAnim() {
        isStop = false;
        if (mValueAnimator != null) {
            if (mValueAnimator != null && !mValueAnimator.isRunning()) {
                mValueAnimator.start();
            }
        }
    }

    /**
     * 停止动画
     */
    public void stopAnim() {
        isStop = true;
        if (mHandler != null) {
            if (mAnimRunnable != null) {
                mHandler.removeCallbacks(mAnimRunnable);
            }
            mHandler.removeMessages(REFRESH_CURRENT);
        }
        if (mValueAnimator != null) {
            mHandler.removeCallbacks(mAnimRunnable);
            mValueAnimator.cancel();
        }
        for (Map.Entry<String, PathBean> map : mPathMap.entrySet()) {
            PathBean pathBean = map.getValue();
            pathBean.setDrawPath1(false);
            pathBean.setDrawPath2(false);
            pathBean.setDrawPath3(false);
            pathBean.setDrawPath4(false);
            pathBean.setDrawPath5(false);
        }
        invalidate();
    }

    public void resetPath() {
        mCurrentPath.reset();
        for (Map.Entry<String, PathBean> map : mPathMap.entrySet()) {
            if (map.getValue().getPath1() != null) {
                map.getValue().getPath1().reset();
            }
            if (map.getValue().getPath2() != null) {
                map.getValue().getPath2().reset();
            }
            if (map.getValue().getPath3() != null) {
                map.getValue().getPath3().reset();
            }
            if (map.getValue().getPath4() != null) {
                map.getValue().getPath4().reset();
            }
            if (map.getValue().getPath5() != null) {
                map.getValue().getPath5().reset();
            }
        }
        firsPath.reset();
        secondPath.reset();
        thirdPath.reset();
        forthPath.reset();
        hasSetMRect = false;
        invalidate();
    }

    /**
     * 设置动画路径
     *
     * @param key
     * @param pathBean
     */
    public void setPathMap(String key, PathBean pathBean) {
        if (!mPathMap.containsKey(key)) {
            pathBean.getAnimPaint1().setAntiAlias(true);
            pathBean.getAnimPaint1().setStyle(Paint.Style.STROKE);
            pathBean.getAnimPaint1().setColor(mAnimPathColor);
            pathBean.getAnimPaint1().setStrokeWidth(mPathWidth);
            pathBean.getAnimPaint1().setStrokeCap(Paint.Cap.ROUND);

            pathBean.getAnimPaint2().setAntiAlias(true);
            pathBean.getAnimPaint2().setStyle(Paint.Style.STROKE);
            pathBean.getAnimPaint2().setColor(mAnimPathColor);
            pathBean.getAnimPaint2().setStrokeWidth(mPathWidth);
            pathBean.getAnimPaint2().setStrokeCap(Paint.Cap.ROUND);

            pathBean.getAnimPaint3().setAntiAlias(true);
            pathBean.getAnimPaint3().setStyle(Paint.Style.STROKE);
            pathBean.getAnimPaint3().setColor(mAnimPathColor);
            pathBean.getAnimPaint3().setStrokeWidth(mPathWidth);
            pathBean.getAnimPaint3().setStrokeCap(Paint.Cap.ROUND);

            pathBean.getAnimPaint4().setAntiAlias(true);
            pathBean.getAnimPaint4().setStyle(Paint.Style.STROKE);
            pathBean.getAnimPaint4().setColor(mAnimPathColor);
            pathBean.getAnimPaint4().setStrokeWidth(mPathWidth);
            pathBean.getAnimPaint4().setStrokeCap(Paint.Cap.ROUND);
            if (key.equals(PathBean.SOLAR_KEY)) {
                pathBean.getAnimPaint5().setAntiAlias(true);
                pathBean.getAnimPaint5().setStyle(Paint.Style.STROKE);
                pathBean.getAnimPaint5().setColor(mAnimPathColor);
                pathBean.getAnimPaint5().setStrokeWidth(mPathWidth);
                pathBean.getAnimPaint5().setStrokeCap(Paint.Cap.ROUND);
            }

            mPathMap.put(key, pathBean);
        }
    }

    public HashMap<String, PathBean> getPathMap() {
        return mPathMap;
    }

    public PathBean getPathBeanByKey(String key) {
        return mPathMap.get(key);
    }

    public void setSameColor(int startColor, int endColor) {
        for (Map.Entry<String, PathBean> map : mPathMap.entrySet()) {
            PathBean pathBean = map.getValue();
            pathBean.setStartColor1(startColor);
            pathBean.setEndColor1(endColor);
            pathBean.setStartColor2(startColor);
            pathBean.setEndColor2(endColor);
            pathBean.setStartColor3(startColor);
            pathBean.setEndColor3(endColor);
            pathBean.setStartColor4(startColor);
            pathBean.setEndColor4(endColor);
            pathBean.setStartColor5(startColor);
            pathBean.setEndColor5(endColor);
        }
    }

    /**
     * dp转px
     */
    private static int dp2px(Context context, float dpVal) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                dpVal, context.getResources().getDisplayMetrics());
    }


    @Override
    protected void onDetachedFromWindow() {
//        stopAnim();
        super.onDetachedFromWindow();
    }
}
