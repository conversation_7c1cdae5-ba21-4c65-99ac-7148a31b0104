package com.dinsafer.module.powerstation;


import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentOnlinePdfBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.util.DDLog;
import com.github.barteksc.pdfviewer.listener.OnErrorListener;
import com.github.barteksc.pdfviewer.listener.OnLoadCompleteListener;
import com.github.barteksc.pdfviewer.listener.OnPageChangeListener;
import com.github.barteksc.pdfviewer.listener.OnPageErrorListener;
import com.shockwave.pdfium.PdfDocument;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import rx.Emitter;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

public class OnlinePdfFragment extends MyBaseFragment<FragmentOnlinePdfBinding> implements OnPageChangeListener, OnLoadCompleteListener,
        OnPageErrorListener, OnErrorListener {

    public static OnlinePdfFragment newInstance(String url) {
        OnlinePdfFragment fragment = new OnlinePdfFragment();
        Bundle bundle = new Bundle();
        bundle.putString("url", url);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_online_pdf;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText("");
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            String url = bundle.getString("url");
            Observable.create((Action1<Emitter<InputStream>>) emitter -> {
                        OkHttpClient client = new OkHttpClient();
                        Request request = new Request.Builder()
                                .url(url)
                                .build();
                        try {
                            Response response = client.newCall(request).execute();
                            if (response.isSuccessful()) {
                                emitter.onNext(response.body().byteStream());
                            } else {
                                emitter.onError(new Throwable("请求失败, 请稍后重试..."));
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                            emitter.onError(e);
                        }
                    }, Emitter.BackpressureMode.DROP).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Subscriber<>() {
                        @Override
                        public void onStart() {
                            super.onStart();
                            showLoadingFragment(0);
                        }

                        @Override
                        public void onCompleted() {

                        }

                        @Override
                        public void onError(Throwable e) {
                            DDLog.i(TAG, "onError...");
                            closeLoadingFragment();
                            showErrorToast();
                            e.printStackTrace();
                        }

                        @Override
                        public void onNext(InputStream inputStream) {
                            if (inputStream == null) {
                                closeLoadingFragment();
                                showErrorToast();
                                return;
                            }
                            mBinding.pdfView.fromStream(inputStream)
                                    .defaultPage(0)
                                    .onPageChange(OnlinePdfFragment.this)
                                    .onLoad(OnlinePdfFragment.this)
                                    .onError(OnlinePdfFragment.this)
                                    .spacing(10) // in dp
                                    .onPageError(OnlinePdfFragment.this)
                                    .enableAntialiasing(true)
                                    .enableAnnotationRendering(false)
                                    .invalidPageColor(Color.WHITE)
                                    .load();
                        }
                    });
        }
    }

    @Override
    public void loadComplete(int nbPages) {
        DDLog.i(TAG, "loadComplete...");
        closeLoadingFragment();
        PdfDocument.Meta meta = mBinding.pdfView.getDocumentMeta();
        Log.e(TAG, "title = " + meta.getTitle());
        Log.e(TAG, "author = " + meta.getAuthor());
        Log.e(TAG, "subject = " + meta.getSubject());
        Log.e(TAG, "keywords = " + meta.getKeywords());
        Log.e(TAG, "creator = " + meta.getCreator());
        Log.e(TAG, "producer = " + meta.getProducer());
        Log.e(TAG, "creationDate = " + meta.getCreationDate());
        Log.e(TAG, "modDate = " + meta.getModDate());

        printBookmarksTree(mBinding.pdfView.getTableOfContents(), "-");

    }

    public void printBookmarksTree(List<PdfDocument.Bookmark> tree, String sep) {
        for (PdfDocument.Bookmark b : tree) {

            Log.e(TAG, String.format("%s %s, p %d", sep, b.getTitle(), b.getPageIdx()));

            if (b.hasChildren()) {
                printBookmarksTree(b.getChildren(), sep + "-");
            }
        }
    }


    @Override
    public void onPageError(int page, Throwable t) {
        Log.e(TAG, "Cannot load page " + page);
    }

    @Override
    public void onPageChanged(int page, int pageCount) {
        Log.e(TAG, "current page is " + page + ", pageCount is " + pageCount);
    }

    @Override
    public void onError(Throwable t) {
        closeLoadingFragment();
        showErrorToast();
        Log.e("PDFView", "load pdf error", t);
    }
}
