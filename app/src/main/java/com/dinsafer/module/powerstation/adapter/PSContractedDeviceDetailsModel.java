package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.util.Log;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.BmtManager;
import com.dinsafer.common.Constants;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsContractedDeviceDetailsBinding;
import com.dinsafer.dinnet.databinding.ItemPsContractedDevicesBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;

public class PSContractedDeviceDetailsModel implements BaseBindModel<ItemPsContractedDeviceDetailsBinding> {

    public static final int SELECTABLE_STATUS = 1;
    public static final int OFFLINE_STATUS = 2;
    public static final int OFF_GRID_STATUS = 3;
    public static final int MISMATCH_STATUS = 4;
    public static final int ENABLING_STATUS = 5;
    private Device device;
    // 1.离线 2.离网 3.启用
    private int status;
    private String displayID;
    private FamilyBalanceContractInfoResponse.ResultBean.SignedDevices deviceInfo;

    public PSContractedDeviceDetailsModel(String displayID, Device device) {
        this.device = device;
        this.displayID = displayID;
    }


    @Override
    public int getLayoutID() {
        return R.layout.item_ps_contracted_device_details;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsContractedDeviceDetailsBinding binding) {
        if (device == null) return;
        String name = DeviceHelper.getString(device, DinConst.INFO_NAME, "");
        binding.tvName.setText(name.equals(Constants.POWER_STATION) ? Local.s(name) : name);
        setStatusStyle(binding.getRoot().getContext(), this.status, binding.tvTag);
        binding.ivDevice.setImageResource(BmtUtil.getCoverRes(device));
        binding.tvId.setText(displayID);
        String displayEffectiveTime = Local.s(holder.itemView.getContext().getString(R.string.effective_time))
                + ": " + DDDateUtil.formatLong((deviceInfo.getEffective_time() / 1000000), DDDateUtil.LOCAL_YEAR_MONTH_DATE_FORMAT);
        binding.tvEffectiveTime.setText(displayEffectiveTime);
        binding.tvName.post(() -> {
            int llWidth = binding.clName.getWidth();
            int tagWidth = binding.tvTag.getWidth();
            int maxWidth = llWidth - tagWidth - DensityUtil.dp2px(binding.getRoot().getContext(), 6);
            binding.tvName.setMaxWidth(maxWidth);
        });
    }

    private void setStatusStyle(Context context, int status, LocalTextView textView) {
        switch (status) {
            case OFFLINE_STATUS:
                textView.setLocalText(context.getString(R.string.Offline));
                textView.setBackgroundResource(R.drawable.shape_tip_06_2_r3);
                textView.setTextColor(context.getResources().getColor(R.color.color_tip_06));
                break;

            case OFF_GRID_STATUS:
                textView.setLocalText(context.getString(R.string.Offgrid));
                textView.setBackgroundResource(R.drawable.shape_tip_06_2_r3);
                textView.setTextColor(context.getResources().getColor(R.color.color_tip_06));
                break;

            case ENABLING_STATUS:
                textView.setLocalText(context.getString(R.string.Enabling));
                textView.setBackgroundResource(R.drawable.shape_brand_light_01_r3);
                textView.setTextColor(context.getResources().getColor(R.color.color_brand_text));
                break;

            default:
                textView.setLocalText("");
                textView.setBackgroundResource(0);
                textView.setTextColor(0);
                break;
        }
    }

    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setDisplayID(String displayID) {
        this.displayID = displayID;
    }

    public String getDisplayID() {
        return displayID;
    }

    public void setDeviceInfo(FamilyBalanceContractInfoResponse.ResultBean.SignedDevices deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public FamilyBalanceContractInfoResponse.ResultBean.SignedDevices getDeviceInfo() {
        return deviceInfo;
    }
}
