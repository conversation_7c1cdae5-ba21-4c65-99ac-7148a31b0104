package com.dinsafer.module.powerstation.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsElectricityAreaBinding;
import com.dinsafer.dinnet.generated.callback.OnClickListener;
import com.dinsafer.model.BleDeviceSimpleEntry;
import com.dinsafer.module.add.ui.adapter.BleScanDeviceAdapter;
import com.dinsafer.module.powerstation.bean.PsElectricityAreaBean;

import java.util.ArrayList;

/**
 * @describe：
 * @date：2024/9/9
 * @author: create by Sydnee
 */
public class PsElectricityAreaAdapter extends RecyclerView.Adapter<PsElectricityAreaAdapter.ViewHolder> {

    private ArrayList<PsElectricityAreaBean> mData;
    private PsElectricityAreaAdapter.OnItemClick onItemClickListener = null;

    @NonNull
    @Override
    public PsElectricityAreaAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_ps_electricity_area, parent, false);
        PsElectricityAreaAdapter.ViewHolder viewHolder = new PsElectricityAreaAdapter.ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull PsElectricityAreaAdapter.ViewHolder holder, int position) {

        holder.binding.imgIcon.setImageResource(mData.get(position).getIconID());
        holder.binding.tvName.setLocalText(mData.get(position).getSimpleName());
        holder.binding.imgCheck.setVisibility(View.GONE);
        holder.binding.tvDeviceStatus.setVisibility(View.GONE);
        holder.binding.rlItemRoot.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (position >= getItemCount()) {
                    return;
                }
                onItemClickListener.onItemClick(mData.get(position), position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemPsElectricityAreaBinding binding;

        public ViewHolder(View itemView) {
            super(itemView);
            binding = DataBindingUtil.bind(itemView);
        }
    }

    public interface OnItemClick {
        void onItemClick(PsElectricityAreaBean psElectricityAreaBean, int position);
    }

    public void setData(ArrayList<PsElectricityAreaBean> mData) {
        this.mData = mData;
    }

    public void setOnItemClickListener(OnItemClick onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }
}
