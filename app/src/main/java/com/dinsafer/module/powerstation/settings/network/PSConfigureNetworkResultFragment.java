package com.dinsafer.module.powerstation.settings.network;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsConfigureNetworkResultBinding;
import com.dinsafer.dinsdk.BmtBinderWrapper;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.device.PSBleBaseBindFragment;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module_bmt.add.BaseBmtBinder;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

import io.reactivex.annotations.NonNull;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 17:41
 * @description :
 */
public class PSConfigureNetworkResultFragment extends PSBleBaseBindFragment<FragmentPsConfigureNetworkResultBinding> {

    private static final String KEY_ERROR_CODE = "key_error_code";

    private static final String KEY_NETWORK_TYPE = "key_network_type";
    public static final int TYPE_NETWORK_WIFI = 0;
    public static final int TYPE_NETWORK_WIRED = 1;
    private int mErrorCode;
    private int mNetworkType;

    public static PSConfigureNetworkResultFragment newInstance(int from, int errorCode, String deviceId
            , String model, String wifiSSid, String wifiPassword, int networkType) {
        PSConfigureNetworkResultFragment fragment = new PSConfigureNetworkResultFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_FROM, from);
        bundle.putInt(KEY_ERROR_CODE, errorCode);
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_WIFI_SSID, TextUtils.isEmpty(wifiSSid) ? "" : wifiSSid);
        bundle.putString(PSKeyConstant.KEY_WIFI_PASSWORD, TextUtils.isEmpty(wifiPassword) ? "" : wifiPassword);
        bundle.putString(PSKeyConstant.KEY_MODEL, model);
        bundle.putInt(KEY_NETWORK_TYPE, networkType);
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_configure_network_result;
    }

    @SuppressLint("StringFormatMatches")
    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.setClick(new OnClickHandler());
        mBinding.tvInfo.setVisibility( mNetworkType == TYPE_NETWORK_WIRED ? View.GONE : View.VISIBLE);
        mBinding.lcbRetry.setText(Local.s(getString(R.string.Retry)));
        mBinding.tvFailed.setText(Local.s(getString(R.string.ps_settings_network_configure_network_failed)));
    }

    private void initParams() {
        final Bundle bundle = getArguments();
        if (null == bundle) {
            return;
        }
        mFrom = bundle.getInt(KEY_FROM);
        mErrorCode = bundle.getInt(KEY_ERROR_CODE);
        deviceID = bundle.getString(PSKeyConstant.KEY_DEVICE_ID);
        wifiSSid = bundle.getString(PSKeyConstant.KEY_WIFI_SSID);
        wifiPassword = bundle.getString(PSKeyConstant.KEY_WIFI_PASSWORD);
        mModel = bundle.getString(PSKeyConstant.KEY_MODEL);
        mNetworkType = bundle.getInt(KEY_NETWORK_TYPE);
    }

    @Override
    protected void onBinderReady(@NonNull BmtBinderWrapper pluginBinder) {
        mBinder.addBindCallBack(this);
        mBinder.addEthernetCallback(this);

        String info = Local.s(getString(R.string.ps_settings_network_configure_network_failed_info))
                + ": \n"
                + Local.s(getString(R.string.ps_settings_network_configure_network_failed_info_ssid))
                + ": " + wifiSSid + "\n"
                + Local.s(getString(R.string.ps_settings_network_configure_network_failed_info_pwd))
                + ": " + wifiPassword;
        mBinding.tvInfo.setText(info);
        if (TextUtils.isEmpty(wifiSSid)) {
            mBinding.tvInfo.setVisibility(View.GONE);
        }
        updateErrorMsgFromCode(mErrorCode);
    }

    public void toSave() {
        if (null != mBinder && wifiSSid.length() > 0) {
            showTimeOutLoadinFramgmentWithCallBack(mConfigTimeout, this);
            mBinder.setSsid(wifiSSid);
            mBinder.setSsidPassword(wifiPassword);
            mBinder.bindDevice(null);
        } else {
            showErrorToast();
            if (ActivityController.getInstance().getFragment(PSNetworkSettingsFragment.class) != null) {
                getDelegateActivity().removeToFragment(PSNetworkSettingsFragment.class.getName());
            } else {
                removeSelf();
            }
        }
    }

    public void toEthernetConnect() {
        if (null != mBinder) {
            showTimeOutLoadinFramgmentWithCallBack(mConfigTimeout, this);
            isEthernetConnect = true;
            mBinder.getEthernetState();

        } else {
            showErrorToast();
            if (ActivityController.getInstance().getFragment(PSNetworkSettingsFragment.class) != null) {
                getDelegateActivity().removeToFragment(PSNetworkSettingsFragment.class.getName());
            } else {
                removeSelf();
            }
        }
    }

    private void updateErrorMsgFromCode(int code) {
        String msg = BmtUtil.getConfigErrorMsgByCode(code);
        if (TextUtils.isEmpty(msg)) {
            msg = getString(R.string.ps_settings_network_configure_network_failed);
        }
        mBinding.tvFailed.setText(Local.s(msg));
    }

    @Override
    protected void onResultError(int code) {
        getDelegateActivity().runOnUiThread(() -> {
            updateErrorMsgFromCode(code);
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mBinder) {
            mBinder.removeBindCallBack(this);
            mBinder.removeEthernetCallback(this);
        }
    }

    public class OnClickHandler {
        public void retry() {
            if (mNetworkType == TYPE_NETWORK_WIFI) {
                toSave();
            } else {
                // 重新获取固网状态
                toEthernetConnect();
            }
        }

        public void wifiRetry() {
            toSave();
        }

        public void reselect() {
            if (ActivityController.getInstance().getFragment(PSNetworkSettingsFragment.class) != null) {
                getDelegateActivity().removeToFragment(PSNetworkSettingsFragment.class.getName());
            } else {
                removeSelf();
            }
        }

        public void quit() {
            removeSelf();
            EventBus.getDefault().post(new FinishAddBmtEvent());
        }
    }
}
