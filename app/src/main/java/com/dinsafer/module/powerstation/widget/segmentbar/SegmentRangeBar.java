package com.dinsafer.module.powerstation.widget.segmentbar;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.Typeface;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.util.Local;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/26 15:07
 * @description :
 */
public class SegmentRangeBar extends View {

    private Context mContext;
    private List<Segment> mSegments;
    private int mBarWidth;
    private Paint mFillPaint;
    private Paint mThumbPaint;
    private int mThumbColor;
    private int mRadius;
    private int mTouchRadius;
    private int mRoundRadius;
    private String mMinText = "";
    private String mMaxText = "";
    private int mMinColor;
    private int mMaxColor;
    private int mPercentTextColor;
    private int mDescTextColor;
    private int mPercentTextSize;
    private int mDescTextSize;
    private HashMap<String, RectF> mRectFMap = new HashMap<>();


    private Paint mLeftTextPaint;
    private Rect mTextRect;
    private RectF mTrackRect;

    private Paint mTrianglePaint;
    private int mTriangleColor;
    private Path mTrianglePath;
    private Point mPoint1;
    private Point mPoint2;
    private Point mPoint3;

    private float mMinThumbCenterY;
    private float mMaxThumbCenterY;
    private float mThumbYDistance; // 0-1
    private float mDelta = 100f;
    private boolean isMinThumbOnDragging;
    private boolean isMaxThumbOnDragging;
    private float mTrackLength;
    private float mMinProgress = 40f;
    private float mMaxProgress = 80f;
    private float mMinY;

    private float mBottom;
    private float mMinProgressFlag;
    private float mMaxProgressFlag;
    private boolean isMinVibrate = true;
    private boolean isMaxVibrate = true;

    private SegmentBarMarkView mMinMarkView;
    private SegmentBarMarkView mMaxMarkView;
    private boolean isTouchMinMarkView;
    private boolean isTouchMaxMarkView;

    public SegmentRangeBar(Context context) {
        this(context, null);
    }

    public SegmentRangeBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SegmentRangeBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttrs(context, attrs);
        init();
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        mContext = context;
        TypedArray a = context.getTheme().obtainStyledAttributes(
                attrs, R.styleable.SegmentRangeBar, 0, 0);
        mBarWidth = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_width, DensityUtil.dp2px(context, 10));
        mRadius = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_radius, DensityUtil.dp2px(context, 2));
        mTouchRadius = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_touched_radius, DensityUtil.dp2px(context, 4));
        mRoundRadius = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_bar_round_radius, DensityUtil.dp2px(context, 10));
        mMinText = Local.s(a.getString(R.styleable.SegmentRangeBar_srb_min_text));
        mMaxText = Local.s(a.getString(R.styleable.SegmentRangeBar_srb_max_text));
        mMinColor = a.getColor(R.styleable.SegmentRangeBar_srb_min_color, Color.WHITE);
        mMaxColor = a.getColor(R.styleable.SegmentRangeBar_srb_max_color, Color.WHITE);
        mPercentTextColor = a.getColor(R.styleable.SegmentRangeBar_srb_max_color, Color.WHITE);
        mDescTextColor = a.getColor(R.styleable.SegmentRangeBar_srb_max_color, Color.WHITE);
        mPercentTextSize = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_percent_text_size, DensityUtil.sp2px(context, 30));
        mDescTextSize = a.getDimensionPixelSize(R.styleable.SegmentRangeBar_srb_desc_text_size, DensityUtil.sp2px(context, 15));
        mThumbYDistance = a.getFloat(R.styleable.SegmentRangeBar_srb_thumbY_distance, 0.15f);
        mTriangleColor = a.getColor(R.styleable.SegmentRangeBar_srb_triangle_color, Color.parseColor("#1AFFFFFF"));
        mThumbColor = a.getColor(R.styleable.SegmentRangeBar_srb_thumb_color, Color.parseColor("#FFFFFFFF"));
        a.recycle();
    }

    private void init() {
        mFillPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mFillPaint.setStyle(Paint.Style.FILL);

        mThumbPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mThumbPaint.setStyle(Paint.Style.FILL);
        mThumbPaint.setColor(mThumbColor);

        mLeftTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        Typeface typeface = ResourcesCompat.getFont(getContext(), R.font.poppins);
        mLeftTextPaint.setTypeface(typeface);
        mLeftTextPaint.setFakeBoldText(true);

        mTextRect = new Rect();

        mTrianglePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mTrianglePaint.setColor(mTriangleColor);
        mTrianglePath = new Path();
        mPoint1 = new Point();
        mPoint2 = new Point();
        mPoint3 = new Point();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mTrackRect = new RectF(getWidth() / 2 - mBarWidth / 2, (int) (getPaddingTop() + mTouchRadius),
                getWidth() / 2 + mBarWidth / 2, (int) (getHeight() - getPaddingBottom() - mTouchRadius));
        mTrackLength = mTrackRect.bottom - mTrackRect.top;
        mBottom = mTrackRect.bottom;
    }

    private int getContentWidth() {
        return getWidth() - getPaddingLeft() - getPaddingRight();
    }

    private int getContentHeight() {
        return getHeight() - getPaddingTop() - getPaddingBottom();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int segmentRangeSize = mSegments == null ? 0 : mSegments.size();
        if (segmentRangeSize > 0) {
            for (int segmentRangeIndex = 0; segmentRangeIndex < segmentRangeSize; segmentRangeIndex++) {
                Segment segmentRange = mSegments.get(segmentRangeIndex);
                drawSegmentRange(canvas, segmentRange, segmentRangeIndex, segmentRangeSize);
            }
//            RectF rectFFirst = rectFHashMap.get("1");
            drawBar(canvas);

            if (!isMinThumbOnDragging)
                mMinThumbCenterY = mTrackRect.bottom - mTrackLength / mDelta * mMinProgress;
            if (!isMaxThumbOnDragging)
                mMaxThumbCenterY = mTrackRect.bottom - mTrackLength / mDelta * mMaxProgress;
            drawCircle(canvas, getWidth() / 2, mMinThumbCenterY, isMinThumbOnDragging ? mTouchRadius : mRadius, mMinColor);
            drawPercentText(canvas, 1, mMinThumbCenterY, getMinProgress());
            drawDesc(canvas, mMinText, mMinThumbCenterY);
            mPoint1.set((int) (mTrackRect.right + DensityUtil.dp2px(mContext, 16)), (int) mMinThumbCenterY);
            mPoint2.set(mPoint1.x + DensityUtil.dp2px(mContext, 6), mPoint1.y - DensityUtil.dp2px(mContext, 7));
            mPoint3.set(mPoint1.x + DensityUtil.dp2px(mContext, 6), mPoint1.y + DensityUtil.dp2px(mContext, 7));
            drawTriangle(canvas, mPoint1, mPoint2, mPoint3);
            drawMarkers(canvas, mMinThumbCenterY, mMinMarkView);

            drawCircle(canvas, getWidth() / 2, mMaxThumbCenterY, isMaxThumbOnDragging ? mTouchRadius : mRadius, mMaxColor);
            drawPercentText(canvas, 2, mMaxThumbCenterY, getMaxProgress());
            drawDesc(canvas, mMaxText, mMaxThumbCenterY);
            mPoint1.set((int) (mTrackRect.right + DensityUtil.dp2px(mContext, 16)), (int) mMaxThumbCenterY);
            mPoint2.set(mPoint1.x + DensityUtil.dp2px(mContext, 6), mPoint1.y - DensityUtil.dp2px(mContext, 7));
            mPoint3.set(mPoint1.x + DensityUtil.dp2px(mContext, 6), mPoint1.y + DensityUtil.dp2px(mContext, 7));
            drawTriangle(canvas, mPoint1, mPoint2, mPoint3);
            drawMarkers(canvas, mMaxThumbCenterY, mMaxMarkView);
        }
    }

    private void drawBar(Canvas canvas) {
        float[] pos = {mSegments.get(4).getMinValue(), mSegments.get(4).getMaxValue(),
                mSegments.get(3).getMinValue(), mSegments.get(3).getMaxValue(),
                mSegments.get(2).getMinValue(), mSegments.get(2).getMaxValue(),
                mSegments.get(1).getMinValue(), mSegments.get(1).getMaxValue(),
                mSegments.get(0).getMinValue(), mSegments.get(0).getMaxValue()};
        int[] colors = {mSegments.get(4).getColor(), mSegments.get(4).getColor(),
                mSegments.get(3).getColor(), mSegments.get(3).getColor(),
                mSegments.get(2).getColor(), mSegments.get(2).getColor(),
                mSegments.get(1).getColor(), mSegments.get(1).getColor(),
                mSegments.get(0).getColor(), mSegments.get(0).getColor()};
        mFillPaint.setShader(new LinearGradient(0, mTrackRect.bottom,
                0, mTrackRect.top, colors, pos, Shader.TileMode.CLAMP));
        canvas.drawRoundRect(
                mTrackRect,
                mRoundRadius,
                mRoundRadius,
                mFillPaint
        );
    }

    /**
     * 分段条
     *
     * @param canvas
     * @param segmentRange
     * @param segmentRangeIndex
     * @param segmentRangeSize
     */
    private void drawSegmentRange(Canvas canvas, Segment segmentRange, int segmentRangeIndex, int segmentRangeSize) {
        float singleSegmentHeight = getContentHeight() * (segmentRange.getMaxValue() - segmentRange.getMinValue());
        float segmentTop = mTrackRect.top + (mTrackRect.bottom - mTrackRect.top) * (1 - segmentRange.getMaxValue());
        float segmentBottom = segmentTop + singleSegmentHeight;
        RectF rectBounds = mRectFMap.get(String.valueOf(segmentRangeIndex));
        if (rectBounds == null) {
            rectBounds = new RectF();
            mRectFMap.put(String.valueOf(segmentRangeIndex), rectBounds);
        }
        rectBounds.set(getWidth() / 2 - mBarWidth / 2, (int) (segmentTop), getWidth() / 2 + mBarWidth / 2, (int) (segmentBottom));
        if (segmentRangeIndex == 3) {
            mMinY = rectBounds.top;
        }
        mFillPaint.setColor(segmentRange.getColor());
//        if (segmentRange.getMaxValue() == 1.0f) {
//            canvas.drawRoundRect(
//                    rectBounds,
//                    mRoundRadius,
//                    mRoundRadius,
//                    mFillPaint
//            );
//            canvas.drawRect(rectBounds.left, rectBounds.top + mRadius, rectBounds.right, rectBounds.bottom, mFillPaint);
//        } else if (segmentRange.getMinValue() == 0.0f) {
//            canvas.drawRoundRect(
//                    rectBounds,
//                    mRoundRadius,
//                    mRoundRadius,
//                    mFillPaint
//            );
//            canvas.drawRect(rectBounds.left, rectBounds.top, rectBounds.right, rectBounds.bottom - mRadius, mFillPaint);
//
//        } else {
//            canvas.drawRect(
//                    rectBounds,
//                    mFillPaint
//            );
//        }

    }

    /**
     * 画圆
     *
     * @param canvas
     * @param x
     * @param y
     * @param radius
     * @param color
     */
    private void drawCircle(Canvas canvas, float x, float y, int radius, int color) {

        canvas.drawCircle(x, y, radius, mThumbPaint);
    }

    /**
     * 画百分比文本
     *
     * @param canvas
     * @param index
     * @param centerY
     * @param percent
     */
    private void drawPercentText(Canvas canvas, int index, float centerY, float percent) {
        RectF rectF = mRectFMap.get(String.valueOf(index));
        mLeftTextPaint.setColor(mPercentTextColor);
        mLeftTextPaint.setTextSize(mPercentTextSize);
        String text = (int) percent + "%";
        mLeftTextPaint.getTextBounds(text, 0, text.length(), mTextRect);
        float y = centerY;
        if (centerY >= mTrackRect.bottom - mTouchRadius / 2) {
            y = centerY - mTouchRadius / 2;
        }
        if (centerY <= mTrackRect.top + mTouchRadius / 2) {
            y = centerY + mTouchRadius / 2;
        }
        canvas.drawText(text, rectF.left - mTextRect.width() - DensityUtil.dp2px(mContext, 30), y, mLeftTextPaint);
    }

    /**
     * 百分比下面文本
     *
     * @param canvas
     * @param text
     * @param centerY
     */
    private void drawDesc(Canvas canvas, String text, float centerY) {
        if (text != null && text.length() > 0) {
            mLeftTextPaint.setColor(mDescTextColor);
            mLeftTextPaint.setTextSize(mDescTextSize);
            mLeftTextPaint.getTextBounds(text, 0, text.length(), mTextRect);
            float y = centerY + 2 * mTextRect.height();
            if (centerY >= mTrackRect.bottom - mTouchRadius / 2) {
                y = y - mTouchRadius / 2;
            }
            if (centerY <= mTrackRect.top + mTouchRadius / 2) {
                y = y + mTouchRadius / 2;
            }
            canvas.drawText(text, getWidth() / 2 - mBarWidth / 2 - mTextRect.width() - DensityUtil.dp2px(mContext, 30), y, mLeftTextPaint);
        }
    }

    /**
     * Markers
     *
     * @param canvas
     * @param centerY
     * @param segmentMarkView
     */
    private void drawMarkers(Canvas canvas, float centerY, SegmentBarMarkView segmentMarkView) {
        if (segmentMarkView != null) {
            segmentMarkView.refreshContent();
            float y = centerY - segmentMarkView.getHeight() / 2;
            if (centerY >= mTrackRect.bottom - mTouchRadius / 2) {
                y = y - mTouchRadius / 2;
            }
            if (centerY <= mTrackRect.top + mTouchRadius / 2) {
                y = y + mTouchRadius / 2;
            }
            segmentMarkView.draw(canvas, mTrackRect.right + DensityUtil.dp2px(mContext, 22), y);
        }
    }

    /**
     * 画三角
     *
     * @param canvas
     * @param point1
     * @param point2
     * @param point3
     */
    private void drawTriangle(Canvas canvas, Point point1, Point point2, Point point3) {
        mTrianglePath.reset();
        mTrianglePath.moveTo(point1.x, point1.y);
        mTrianglePath.lineTo(point2.x, point2.y);
        mTrianglePath.lineTo(point3.x, point3.y);
        mTrianglePath.lineTo(point1.x, point1.y);
        mTrianglePath.close();
        canvas.drawPath(mTrianglePath, mTrianglePaint);
    }

    private float mMinDY;
    private float mMaxDY;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                performClick();
                getParent().requestDisallowInterceptTouchEvent(true);

                isMinThumbOnDragging = isMinThumbTouched(event);
                isMaxThumbOnDragging = isMaxThumbTouched(event);
                if (isMinThumbOnDragging || isMaxThumbOnDragging) {
                    mMinProgressFlag = mMinProgress;
                    mMaxProgressFlag = mMaxProgress;
                    invalidate();
                }

                if (mMinMarkView != null) {
                    isTouchMinMarkView = mMinMarkView.isTouched(event);
                }

                if (mMaxMarkView != null) {
                    isTouchMaxMarkView = mMaxMarkView.isTouched(event);
                }

                mMinDY = mMinThumbCenterY - event.getY();
                mMaxDY = mMaxThumbCenterY - event.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                if (isMinThumbOnDragging || isMaxThumbOnDragging) {
                    boolean flag = true;
                    if (isMinThumbOnDragging) {
                        mMinThumbCenterY = event.getY() + mMinDY;
                        if (mMinThumbCenterY >= mMinY) {
                            mMinThumbCenterY = mMinY;
                        }
                        if (mMinThumbCenterY < (mMaxThumbCenterY + (mTrackRect.bottom-mTrackRect.top)*mThumbYDistance)) {
                            mMinThumbCenterY = mMaxThumbCenterY + (mTrackRect.bottom-mTrackRect.top)*mThumbYDistance;
                        }
                        if (mMinThumbCenterY < mTrackRect.top) {
                            mMinThumbCenterY = mTrackRect.top;
                        }
                        if (mMinThumbCenterY > mBottom) {
                            mMinThumbCenterY = mBottom;
                        }

                        if (flag) {
                            mMinProgress = calculateProgress1();
                            if (mSegments!=null && mSegments.size()>=5) {
                                mSegments.get(1).setMinValue(mMinProgress/100f);
                                mSegments.get(2).setMaxValue(mMinProgress/100f);
                            }
                            if (Math.abs(mMinProgress - mMinProgressFlag) >= 5 && isMinVibrate) {
                                if (mProgressListener != null) {
                                    mProgressListener.onAlert();
                                }
                                isMinVibrate = false;
                            }

                            if (mProgressListener != null) {
                                mProgressListener.onProgress1Changed(this, getMinProgress(), getMinProgressFloat(), true);
                            }
                            invalidate();
                        }
                    }
                    if (isMaxThumbOnDragging) {
                        mMaxThumbCenterY = event.getY() + mMaxDY;
                        if (mMaxThumbCenterY > (mMinThumbCenterY - (mTrackRect.bottom-mTrackRect.top)*mThumbYDistance)) {
                            mMaxThumbCenterY = mMinThumbCenterY - (mTrackRect.bottom-mTrackRect.top)*mThumbYDistance;
                        }
                        if (mMaxThumbCenterY < mTrackRect.top) {
                            mMaxThumbCenterY = mTrackRect.top;
                        }

                        if (mMaxThumbCenterY > mBottom) {
                            mMaxThumbCenterY = mBottom;
                        }

                        if (flag) {
                            mMaxProgress = calculateMaxProgress();
                            if (mSegments!=null && mSegments.size()>=5) {
                                mSegments.get(1).setMaxValue(mMaxProgress/100f);
                                mSegments.get(0).setMinValue(mMaxProgress/100f);
                            }
                            if (Math.abs(mMaxProgress - mMaxProgressFlag) >= 5 && isMaxVibrate) {
//                                mProgressFlag2 = mProgress2;
                                if (mProgressListener != null) {
                                    mProgressListener.onAlert();
                                }
                                isMaxVibrate = false;
                            }
                            if (mProgressListener != null) {
                                mProgressListener.onProgress2Changed(this, getMaxProgress(), getMaxProgressFloat(), true);
                            }
                            invalidate();
                        }
                    }
                }

                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(false);
                if (isTouchMinMarkView) {
                    mMinMarkView.markViewClick();
                }
                if (isTouchMaxMarkView) {
                    mMaxMarkView.markViewClick();
                }
                isMinThumbOnDragging = false;
                isMaxThumbOnDragging = false;
                isTouchMinMarkView = false;
                isTouchMinMarkView = false;
                isMinVibrate = true;
                isMaxVibrate = true;
                mMinProgressFlag = mMinProgress;
                mMaxProgressFlag = mMaxProgress;
                if (mProgressListener != null) {
                    if (isMinThumbOnDragging) {
                        mProgressListener.onProgress1Changed(this, getMinProgress(), getMinProgressFloat(), true);
                        mProgressListener.getProgress1OnActionUp(this, getMinProgress(), getMinProgressFloat());
                    }
                    if (isMinThumbOnDragging) {
                        mProgressListener.onProgress2Changed(this, getMaxProgress(), getMaxProgressFloat(), true);
                        mProgressListener.getProgress2OnActionUp(this, getMaxProgress(), getMaxProgressFloat());
                    }
                }
                invalidate();
                break;
        }

        return isMinThumbOnDragging || isMaxThumbOnDragging || isTouchMinMarkView || isTouchMaxMarkView || super.onTouchEvent(event);
    }

    /**
     * 第一条进度
     *
     * @return
     */
    private float calculateProgress1() {
        return (mBottom - mMinThumbCenterY) * mDelta / mTrackLength;
    }

    public int getMinProgress() {
        return Math.round(processMinProgress());
    }

    public float getMinProgressFloat() {
        return formatFloat(processMinProgress());
    }

    private float processMinProgress() {
        final float progress = mMinProgress;
        return progress;
    }

    /**
     * 第二条进度
     *
     * @return
     */
    private float calculateMaxProgress() {
        return (mBottom - mMaxThumbCenterY) * mDelta / mTrackLength;
    }

    public int getMaxProgress() {
        return Math.round(processMaxProgress());
    }

    public float getMaxProgressFloat() {
        return formatFloat(processMaxProgress());
    }

    private float processMaxProgress() {
        final float progress = mMaxProgress;

        return progress;
    }

    private float formatFloat(float value) {
        BigDecimal bigDecimal = BigDecimal.valueOf(value);
        return bigDecimal.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
    }

    /**
     * Detect effective touch of thumb
     */
    private boolean isMinThumbTouched(MotionEvent event) {
        if (!isEnabled())
            return false;

        float distance = mTrackLength / mDelta * mMinProgress;
        float x = getWidth() / 2f;
        float y = mTrackRect.bottom - distance;
        return (event.getX() - x) * (event.getX() - x) + (event.getY() - y) * (event.getY() - y)
                <= mBottom;
    }

    /**
     * Detect effective touch of thumb
     */
    private boolean isMaxThumbTouched(MotionEvent event) {
        if (!isEnabled())
            return false;

        float distance = mTrackLength / mDelta * mMaxProgress;
        float x = getWidth() / 2f;
        float y = mTrackRect.bottom - distance;
        return (event.getX() - x) * (event.getX() - x) + (event.getY() - y) * (event.getY() - y)
                <= mBottom;
    }

    public void setSegmentRanges(List<Segment> data) {
        mSegments = data;
        invalidate();
        requestLayout();
    }

    public void setSegmentRanges(List<Segment> data, float minProgress, float maxProgress) {
        mSegments = data;
        this.mMinProgress = minProgress;
        this.mMaxProgress = maxProgress;
        invalidate();
        requestLayout();
    }

    public void setMinProgress(float minProgress) {
        this.mMinProgress = minProgress;
    }

    public void setMaxProgress(float maxProgress) {
        this.mMaxProgress = maxProgress;
    }

    private OnProgressChangedListener mProgressListener;

    public void setProgressListener(OnProgressChangedListener progressListener) {
        this.mProgressListener = progressListener;
    }

    public void setMinMarkView(SegmentBarMarkView minMarkView) {
        this.mMinMarkView = minMarkView;
    }

    public void setMaxMarkView(SegmentBarMarkView maxMarkView) {
        this.mMaxMarkView = maxMarkView;
    }


    public interface OnProgressChangedListener {

        void onProgress1Changed(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void getProgress1OnActionUp(SegmentRangeBar segmentRangeBar, int progress, float progressFloat);

        void getProgress1OnFinally(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void onProgress2Changed(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void getProgress2OnActionUp(SegmentRangeBar segmentRangeBar, int progress, float progressFloat);

        void getProgress2OnFinally(SegmentRangeBar segmentRangeBar, int progress, float progressFloat, boolean fromUser);

        void onAlert();
    }
}
