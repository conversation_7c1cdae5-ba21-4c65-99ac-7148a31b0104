package com.dinsafer.module.powerstation;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.style.ImageSpan;

import androidx.annotation.NonNull;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.utils.DensityUtil;

public class PSImageSpan extends ImageSpan {

    private Rect mRect;

    public PSImageSpan(@NonNull Drawable drawable) {
        super(drawable);
        mRect = new Rect();
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
        Drawable b = getDrawable();
        Paint.FontMetricsInt fm = paint.getFontMetricsInt();
        int transY = (y + fm.descent + y + fm.ascent) / 2 - b.getBounds().bottom / 2;//计算y方向的位移
        canvas.save();
        canvas.translate(x, transY);//绘制图片位移一段距离
        b.draw(canvas);
//        canvas.getClipBounds(mRect);
        mRect.left = (int) x- DensityUtil.dp2px(DinSaferApplication.getAppContext(), 10);
        mRect.top = transY-DensityUtil.dp2px(DinSaferApplication.getAppContext(), 10);
        mRect.right = (int) x+b.getIntrinsicWidth()+DensityUtil.dp2px(DinSaferApplication.getAppContext(), 10);
        mRect.bottom = transY+b.getIntrinsicHeight()+DensityUtil.dp2px(DinSaferApplication.getAppContext(), 10);
        canvas.restore();
    }

    public Rect getBounds() {
        return mRect;
    }
}
