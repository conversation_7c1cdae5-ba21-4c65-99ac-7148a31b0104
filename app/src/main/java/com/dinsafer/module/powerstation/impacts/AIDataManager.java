package com.dinsafer.module.powerstation.impacts;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.impacts.aischeduledchart.AIScheduledCombinedChart;
import com.dinsafer.module.powerstation.impacts.aischeduledchart.bean.AIScheduledBarEntry;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.TimeUtil;
import com.github.mikephil.charting.animation.ChartAnimator;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.ViewPortHandler;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class AIDataManager implements AIScheduledCombinedChart.IDrawBackground,
        AIScheduledCombinedChart.IDrawOffsetContent,
        AIScheduledCombinedChart.IDrawHighLighted,
        AIScheduledCombinedChart.IDrawExtremum,
        AIScheduledCombinedChart.IDrawExtra {

    private XAxisValueFormatter formatter = (original, index, viewPortHandler) -> "";

    private YAxisValueFormatter yFormatter = (value, yAxis) -> "";


    private final Context mContext;
    private final AIScheduledCombinedChart combinedChart;
    private final RectF mRectF;
    private float mRadius;
    private final DashPathEffect mDashPathEffect;
    private final Path mLinesPath;
    private final int mHourGridStrokeWidth;
    private final Paint mChargePlanPaint;
    private final RectF mChargePlanRectF;
    private final Rect mTextRect;
    private final Path mClipPath;
    private final int mChargeDataHeight;
    private final float mExtremumOuterRadius;
    private final float mExtremumInnerRadius;
    private final float mHighLightOuterRadius;
    private final float mHighLightInnerRadius;
    private final float mPriceAverWidth;
    private final float mNowOffset;
    private final Paint mPaint;

    private LinearGradient mFillGradientC1;
    private LinearGradient mFillGradientC2;
    private LinearGradient mFillGradientC3;
    private LinearGradient mFillGradientS1;
    private LinearGradient mFillGradientS2;
    private LinearGradient mRetrievingGradient;

    private final List<Float> mHelpData;

    private boolean isDrawSolar = true;
    private boolean isDrawPrice = true;
    private boolean isDrawPriceLevel = true;
    private boolean isDrawChargeStatus = true;

    private int c1;
    private int c2;
    private int c3;
    private int s1;
    private int s2;
    private long startTime;
    private String timezone;
    private float absZeroEcPrices;
    private boolean isHighlightLineOver;
    private List<Integer> conditions;
    private List<Integer> forecastSolars;
    private List<Integer> forecastSolarsHelpData;
    private List<Integer> hopeChargeDischarges;
    private List<Float> marketPrices;
    private List<Integer> plans;
    private List<Float> relativePriceNorms;
    private List<Long> sunriseSunsetDates;
    private List<Long> sunrises;
    private List<Long> sunsets;
    private List<Float> userPrices;
    private Integer[] weekdays;
    private OnHighlightListener highlightListener;
    private OnRetrievingViewListener retrievingViewListener;
    private int mDivideColor;
    private View mNowView;
    private List<View> mSunriseViews;
    private List<View> mSunsetViews;
    private List<View> mSunriseSunsetViews;
    private View mSecondDayView;
    private Map<View, Float> mSunriseSunsetXOffsetMap;
    private boolean isEdit;
    private final float mPriceMaxPercent = 120f;
    private List<Integer> startEndNegativeIndex = new ArrayList<>();

    public AIDataManager(AIScheduledCombinedChart combinedChart, Builder builder) {
        mContext = combinedChart.getContext();
        mRectF = new RectF();
        mLinesPath = new Path();
        mDashPathEffect = new DashPathEffect(new float[]{16, 10f}, 0);
        mHourGridStrokeWidth = DensityUtil.dp2px(mContext, 0.5f);
        mExtremumOuterRadius = DensityUtil.dp2px(mContext, 3);
        mExtremumInnerRadius = DensityUtil.dp2px(mContext, 1.5f);
        mHighLightOuterRadius = DensityUtil.dp2px(mContext, 10f);
        mHighLightInnerRadius = DensityUtil.dp2px(mContext, 7f);
        mPriceAverWidth = DensityUtil.dp2px(mContext, 1);
        mNowOffset = DensityUtil.dp2px(mContext, 4);
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setTextSize(DensityUtil.sp2px(mContext, 10));
        this.combinedChart = combinedChart;
        this.combinedChart.setDrawBackground(this);
        this.combinedChart.setDrawOffsetContent(this);
        this.combinedChart.setDrawHighLighted(this);
        this.combinedChart.setDrawExtremum(this);
        this.combinedChart.setDrawExtra(this);
        mRadius = DensityUtil.dp2px(mContext, 8);
        mChargePlanPaint = new Paint();
        mChargePlanPaint.setAntiAlias(true);
        mChargePlanRectF = new RectF();
        mTextRect = new Rect();
        mClipPath = new Path();
        mChargeDataHeight = DensityUtil.dp2px(mContext, 1.5f);
        initPriceChart();
        this.c1 = builder.c1;
        this.c2 = builder.c2;
        this.c3 = builder.c3;
        this.s1 = builder.s1;
        this.s2 = builder.s2;
        this.startTime = builder.startTime;
        this.timezone = builder.timezone;
        this.absZeroEcPrices = builder.absZeroEcPrices;
        this.isHighlightLineOver = builder.isHighlightLineOver;
        this.conditions = builder.conditions;
        this.forecastSolars = builder.forecastSolars;
        this.hopeChargeDischarges = builder.hopeChargeDischarges;
        this.marketPrices = builder.marketPrices;
        this.plans = builder.plans;
        this.relativePriceNorms = builder.relativePriceNorms;
        this.sunriseSunsetDates = builder.sunriseSunsetDates;
        this.sunrises = builder.sunrises;
        this.sunsets = builder.sunsets;
        this.userPrices = builder.userPrices;
        this.weekdays = builder.weekdays;
        this.highlightListener = builder.highlightListener;
        this.retrievingViewListener = builder.retrievingViewListener;
        mHelpData = new ArrayList<>();
        forecastSolarsHelpData = new ArrayList<>();
        for (int i = 0; i < 36; i++) {
            mHelpData.add(0f);
            forecastSolarsHelpData.add(0);
        }
        createColorAnim();
        showChartData();
    }

    private void createColorAnim() {
        if (absZeroEcPrices > 0) {
            ValueAnimator mColorAnimator = ValueAnimator.ofObject(new ArgbEvaluator(), getColor(R.color.color_ai_price_line_start_color), getColor(R.color.color_ai_price_line_end_color));
            mColorAnimator.setDuration(2000L);
            mColorAnimator.setCurrentPlayTime((long) (absZeroEcPrices * mColorAnimator.getDuration()));
            mDivideColor = (int) mColorAnimator.getAnimatedValue();
        } else {
            mDivideColor = getColor(R.color.color_ai_price_line_end_color);
        }
    }

    private void initPriceChart() {
        combinedChart.setDrawBorders(true);
        combinedChart.setBorderWidth(0.5f);
        combinedChart.setBorderColor(mContext.getResources().getColor(R.color.color_white_04));
        combinedChart.setHighlightPerTapEnabled(true);
        combinedChart.setHighlightPerDragEnabled(true);
        combinedChart.setHighlightFullBarEnabled(true);
        combinedChart.setScaleEnabled(false);
        //设置图表距离上下左右的距离
        combinedChart.setExtraOffsets(10, 10, 10, 52);
        combinedChart.setMinOffset(0);
        //图例
        combinedChart.getLegend().setEnabled(false);
        combinedChart.setDescription("");
        combinedChart.animateX(0);
        combinedChart.setNeedNoDataGrid(true);
        combinedChart.setDrawDefaultXGridLines(true);
        combinedChart.setDrawDefaultYGridLines(true);
        combinedChart.setYNormalColor(mContext.getResources().getColor(R.color.color_white_04));
        combinedChart.setYNormalWidth(DensityUtil.dp2px(mContext, 0.5f));
        combinedChart.animateX(1000);

        //获取X轴
        XAxis xAxis = combinedChart.getXAxis();
        //将垂直于X轴的网格线隐藏，将X轴显示
        xAxis.setDrawGridLines(false);
        xAxis.setDrawAxisLine(false);
        xAxis.setGridColor(mContext.getResources().getColor(R.color.color_white_04));
        //设置X轴上label颜色和大小
        xAxis.setTextSize(10f);
        xAxis.setAvoidFirstLastClipping(true);
        xAxis.setTextColor(mContext.getResources().getColor(R.color.color_white_03));
        //设置X轴高度
        xAxis.setAxisLineWidth(0.5f);
        //x轴刻度值的位置
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
//        设置在”绘制下一个标签”时，要忽略的标签数。
        xAxis.setLabelsToSkip(0);
        xAxis.setValueFormatter(formatter);

        //获取左侧侧坐标轴
        YAxis yAxis = combinedChart.getAxisLeft();
        //设置是否显示Y轴的值
        yAxis.setDrawLabels(false);
        //是否绘制坐标轴
        yAxis.setDrawAxisLine(false);
        //设置所有垂直Y轴的的网格线是否显示
        yAxis.setDrawGridLines(false);
        yAxis.setValueFormatter(yFormatter);
        yAxis.setLabelCount(5, true);

        YAxis rightAxis = combinedChart.getAxisRight();
        rightAxis.setEnabled(true);
        rightAxis.setDrawLabels(false);
        rightAxis.setDrawAxisLine(false);
        rightAxis.setDrawGridLines(false);
        rightAxis.setValueFormatter(yFormatter);
        rightAxis.setLabelCount(3, true);
        rightAxis.setAxisMaxValue(mPriceMaxPercent/100f);
        rightAxis.setAxisMinValue(-mPriceMaxPercent/100f);

        mSunriseViews = new ArrayList<>();
        mSunsetViews = new ArrayList<>();
        mSunriseSunsetViews = new ArrayList<>();
        mSunriseSunsetXOffsetMap = new HashMap<>();
    }

    public void setDefaultHighlight() {
        if (combinedChart != null) {
            combinedChart.highlightValue(0, 0);
        }
    }

    public void setLastHighlightByIndex() {
        if (relativePriceNorms.size() > 0) {
            combinedChart.highlightValue(relativePriceNorms.size() - 1, 0);
        }
    }

    public void clearHighlight() {
        if (combinedChart != null) {
            combinedChart.highlightValue(-1, 0);
        }
    }

    @Override
    public void drawBackground(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas, Paint paint) {
        mRectF.left = portHandler.contentLeft();
        mRectF.top = portHandler.contentTop();
        mRectF.right = portHandler.contentRight();
        float bottom = portHandler.contentBottom() + DensityUtil.dp2px(mContext, 52);
        mRectF.bottom = bottom;
        paint.setAlpha(255);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(mHourGridStrokeWidth);
        paint.setPathEffect(mDashPathEffect);
        paint.setShader(null);
        paint.setColor(getColor(R.color.color_white_04));

        for (int i = 0; i < 36; i++) {
            long indexTime = startTime + 60 * 60 * i;
            int hour = getHour(indexTime);
            if (hour % 6 == 0) {
                float[] circlesBuffer = new float[2];
                Transformer trans = chart.getTransformer(YAxis.AxisDependency.LEFT);
                circlesBuffer[0] = i;
                circlesBuffer[1] = 0;
                trans.pointValuesToPixel(circlesBuffer);
                float x = circlesBuffer[0];
                float y = circlesBuffer[1];
                if (x > portHandler.contentLeft() && x < portHandler.contentRight()) {
                    mLinesPath.reset();
                    mLinesPath.moveTo(x, portHandler.contentTop());
                    mLinesPath.lineTo(x, bottom);
                    canvas.drawPath(mLinesPath, paint);
                }
            }
        }
        paint.setPathEffect(null);
        canvas.drawLine(portHandler.contentLeft(), portHandler.contentBottom(),
                portHandler.contentRight(), portHandler.contentBottom(), paint);
        canvas.drawRoundRect(mRectF, mRadius, mRadius, paint);


        if (isDrawPrice) {
            float priceAver = getPriceAver();
            float percent = (priceAver - combinedChart.getAxisRight().getAxisMinimum()) /
                    (combinedChart.getAxisRight().getAxisMaximum() - combinedChart.getAxisRight().getAxisMinimum());
            float priceAverY = portHandler.contentBottom() - percent * portHandler.contentHeight();
            paint.setStyle(Paint.Style.STROKE);
            paint.setColor(getColor(R.color.color_white_04));
            paint.setStrokeWidth(mPriceAverWidth);
            canvas.drawLine(portHandler.contentLeft(), priceAverY, portHandler.contentRight(), priceAverY, paint);
        }

        if (isDrawPriceLevel) {
            mClipPath.reset();
            float left = portHandler.contentLeft();
            float top = portHandler.contentTop();
            float right = portHandler.contentRight();
            float handlerBottom = portHandler.contentBottom();
            mClipPath.moveTo(left, handlerBottom);
            mClipPath.lineTo(left, top + mRadius);
            mClipPath.quadTo(left, top, left + mRadius, top);
            mClipPath.lineTo(right - mRadius, top);
            mClipPath.quadTo(right, top, right, top + mRadius);
            mClipPath.lineTo(right, bottom);
            mClipPath.lineTo(left, bottom);
            canvas.save();
            canvas.clipPath(mClipPath);
            float contentHalfHeight = portHandler.contentHeight() / 2f;
            float portHandlerCenter = (portHandler.contentTop() + portHandler.contentBottom()) / 2f;
            float s2StartPos = portHandlerCenter - (s2 / mPriceMaxPercent * contentHalfHeight);

            if (mFillGradientS2 == null) {
                mFillGradientS2 = new LinearGradient(portHandler.contentLeft(), 0, portHandler.contentRight(), 0,
                        new int[]{getColor(R.color.color_ai_s2_start_color), getColor(R.color.color_ai_s2_end_color)},
                        new float[]{0, 1}, Shader.TileMode.CLAMP);
            }
            drawSmartPriceTracking(canvas, paint, portHandler, portHandler.contentTop(), s2StartPos, mFillGradientS2);

            if (s1 != s2) {
                float s1StartPos = portHandlerCenter - (s1 / mPriceMaxPercent * contentHalfHeight);
                if (mFillGradientS1 == null) {
                    mFillGradientS1 = new LinearGradient(portHandler.contentLeft(), 0, portHandler.contentRight(), 0,
                            new int[]{getColor(R.color.color_ai_s1_start_color), getColor(R.color.color_ai_s1_end_color)},
                            new float[]{0, 1}, Shader.TileMode.CLAMP);
                }
                drawSmartPriceTracking(canvas, paint, portHandler, s2StartPos, s1StartPos, mFillGradientS1);
            }


            float c1StartPos = portHandlerCenter - (c1 / mPriceMaxPercent * contentHalfHeight);
            float c2StartPos = portHandlerCenter - (c2 / mPriceMaxPercent * contentHalfHeight);
            float c3StartPos = portHandlerCenter - (c3 / mPriceMaxPercent * contentHalfHeight);
            if (mFillGradientC1 == null) {
                mFillGradientC1 = new LinearGradient(portHandler.contentLeft(), 0, portHandler.contentRight(), 0,
                        new int[]{getColor(R.color.color_ai_c1_start_color), getColor(R.color.color_ai_c1_end_color)},
                        new float[]{0, 1}, Shader.TileMode.CLAMP);
            }
            drawCPriceTracking(canvas, paint, portHandler, c1StartPos, portHandler.contentBottom(), mFillGradientC1);

            if (c2 != c1) {
                if (mFillGradientC2 == null) {
                    mFillGradientC2 = new LinearGradient(portHandler.contentLeft(), 0, portHandler.contentRight(), 0,
                            new int[]{getColor(R.color.color_ai_c2_start_color), getColor(R.color.color_ai_c2_end_color)},
                            new float[]{0, 1}, Shader.TileMode.CLAMP);
                }
                drawCPriceTracking(canvas, paint, portHandler, c2StartPos, c1StartPos, mFillGradientC2);
            }

            if (c3 != c1 && c3 != c2) {
                if (mFillGradientC3 == null) {
                    mFillGradientC3 = new LinearGradient(portHandler.contentLeft(), 0, portHandler.contentRight(), 0,
                            new int[]{getColor(R.color.color_ai_c3_start_color), getColor(R.color.color_ai_c3_end_color)},
                            new float[]{0, 1}, Shader.TileMode.CLAMP);
                }
                drawCPriceTracking(canvas, paint, portHandler, c3StartPos, c2StartPos, mFillGradientC3);
            }
            canvas.restore();
        }

        if (isDrawSolar) {
            mPaint.setColor(getColor(R.color.color_white_02));
            mPaint.setTextSize(DensityUtils.sp2px(mContext, 10));
            mPaint.setStyle(Paint.Style.FILL);
            float maxVal = getPVMaxVal();
            String maxValStr = ChartDataUtil.unitFormat(maxVal, false);
            int maxPadding = DensityUtils.dp2px(mContext, 5);
            mPaint.getTextBounds(maxValStr, 0, maxValStr.length(), mTextRect);
            canvas.drawText(maxValStr, portHandler.contentLeft() + +maxPadding,
                    portHandler.contentTop() + mTextRect.height()
                            + maxPadding, mPaint);
        }
    }

    private float getPVMaxVal() {
        float maxVal = 0;
        if (CollectionUtil.isListNotEmpty(forecastSolars)) {
            for (int i = 0; i < forecastSolars.size(); i++) {
                int val = forecastSolars.get(i);
                if (val > maxVal) {
                    maxVal = val;
                }
            }
        }
        return ChartDataUtil.findUpperLimit(maxVal, false);
    }

    private void drawSmartPriceTracking(Canvas canvas, Paint paint, ViewPortHandler portHandler,
                                        float top, float bottom, LinearGradient gradient) {

        mRectF.left = portHandler.contentLeft();
        mRectF.top = top;
        mRectF.right = portHandler.contentRight();
        mRectF.bottom = bottom;
        paint.setShader(gradient);
        paint.setAlpha(38);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mRectF, paint);
        mLinesPath.reset();
        mLinesPath.moveTo(portHandler.contentLeft(), bottom);
        mLinesPath.lineTo(portHandler.contentRight(), bottom);
        paint.setPathEffect(mDashPathEffect);
        paint.setAlpha(77);
        paint.setStyle(Paint.Style.STROKE);
        canvas.drawPath(mLinesPath, paint);
        paint.setPathEffect(null);
    }

    private void drawCPriceTracking(Canvas canvas, Paint paint, ViewPortHandler portHandler,
                                    float top, float bottom, LinearGradient gradient) {

        mRectF.left = portHandler.contentLeft();
        mRectF.top = top;
        mRectF.right = portHandler.contentRight();
        mRectF.bottom = bottom;
        paint.setShader(gradient);
        paint.setAlpha(38);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mRectF, paint);
        mLinesPath.reset();
        mLinesPath.moveTo(portHandler.contentLeft(), top);
        mLinesPath.lineTo(portHandler.contentRight(), top);
        paint.setPathEffect(mDashPathEffect);
        paint.setAlpha(77);
        paint.setStyle(Paint.Style.STROKE);
        canvas.drawPath(mLinesPath, paint);
        paint.setPathEffect(null);
    }

    private int getColor(int color) {
        return mContext.getResources().getColor(color);
    }

    public void showChartData() {
        int count = 36;
        ArrayList<String> xVals = new ArrayList<String>();
        CombinedData data = new CombinedData(xVals);
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }

        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        float maxVal = getPVMaxVal();
        if (maxVal == 0) maxVal = 100f;
        combinedChart.getAxisLeft().setAxisMinValue(0);
        combinedChart.getAxisLeft().setAxisMaxValue(maxVal);
        if (isDrawSolar && CollectionUtil.isListNotEmpty(forecastSolars)) {
            for (int i = 0; i < forecastSolars.size(); i++) {
                float val = forecastSolars.get(i);
                AIScheduledBarEntry scheduledBarEntry = new AIScheduledBarEntry(val, i,
                        mContext.getResources().getColor(R.color.color_ai_pv_column_color));
                yVals.add(scheduledBarEntry);
            }
        } else {
            for (int i = 0; i < forecastSolarsHelpData.size(); i++) {
                float val = forecastSolarsHelpData.get(i);
                AIScheduledBarEntry scheduledBarEntry = new AIScheduledBarEntry(val, i,
                        mContext.getResources().getColor(R.color.color_ai_pv_column_color));
                yVals.add(scheduledBarEntry);
            }
        }
        combinedChart.setVerticalLineCount(4);
        combinedChart.setDrawAverageLine(false);
        combinedChart.setAverageDashWidth(DensityUtil.dp2px(mContext, 1));
        BarDataSet barDataSet = new BarDataSet(yVals, "");
        barDataSet.setDrawValues(false);
        barDataSet.setBarSpacePercent(40);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        LineData lineData = new LineData();
        if (isDrawPrice && CollectionUtil.isListNotEmpty(relativePriceNorms)) {
            ArrayList<Entry> entries = new ArrayList<Entry>();
            ArrayList<Entry> negativeEntries = new ArrayList<Entry>();
            for (int i = 0; i < relativePriceNorms.size(); i++) {
                entries.add(new Entry(relativePriceNorms.get(i), i));
                negativeEntries.add(new Entry(relativePriceNorms.get(i), i));
            }

            SectionLineDataSet lineDataSet = new SectionLineDataSet(entries, "");
            lineDataSet.setLineWidth(3f);
            lineDataSet.setDrawValues(false);
            lineDataSet.setDrawCircleHole(false);
            lineDataSet.setDrawCircles(false);
            lineDataSet.setAxisDependency(YAxis.AxisDependency.RIGHT);
            lineDataSet.setMode(LineDataSet.Mode.HORIZONTAL_BEZIER);
            lineDataSet.setGradientColors(new int[]{getColor(R.color.color_ai_price_line_start_color),
                    getColor(R.color.color_ai_price_line_end_color),
                    getColor(R.color.color_ai_price_line_end_color)});
            lineDataSet.setGradientPosition(new float[]{0, 0.5f, 1});
            lineDataSet.setSection(true);
            lineDataSet.setDrawTexture(true);
            lineDataSet.setTextureColor(getColor(R.color.color_tip_ai));
            startEndNegativeIndex.clear();
            getStartEndNegativeIndex(0);
            if (startEndNegativeIndex.size() % 2 != 0) {
                startEndNegativeIndex.add(marketPrices.size() - 1);
            }
            lineDataSet.setIntervalList(startEndNegativeIndex);
            lineData.addDataSet(lineDataSet);
        }

        ArrayList<Entry> helpEntries = new ArrayList<Entry>();
        for (int i = 0; i < mHelpData.size(); i++) {
            helpEntries.add(new Entry(mHelpData.get(i), i));
        }
        LineDataSet helpDataset = new LineDataSet(helpEntries, "");
        helpDataset.setColor(getColor(R.color.transparent));
        helpDataset.setHighlightEnabled(true);
        helpDataset.setDrawHighlightIndicators(false);
        helpDataset.setDrawValues(false);
        helpDataset.setDrawCircles(false);
        helpDataset.setDrawCircleHole(false);
        lineData.addDataSet(helpDataset);
        data.setData(lineData);
        combinedChart.setData(data);
        combinedChart.invalidate();
    }

    private void getStartEndNegativeIndex (int start) {
        if (start < -1 || start >= marketPrices.size()-1) return;
        int startIndex = -1;
        for (int i=start; i<marketPrices.size(); i++) {
            if (marketPrices.get(i) < 0) {
                if (startIndex<0) {
                    startIndex = i;
                    startEndNegativeIndex.add(i);
                }
            } else {
                if (startIndex > -1) {
                    startEndNegativeIndex.add(i);
                    getStartEndNegativeIndex(i);
                    break;
                }
            }
        }
    }

    private View createTimeView(String text, int sunStatus, boolean highlight, boolean showSunStatus) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.layout_sunrise_sunset, null, false);
        LocalTextView tvTime = view.findViewById(R.id.tv_time);
        tvTime.setTextColor(getColor(highlight ? R.color.color_white_01 : R.color.color_white_02));
        tvTime.setLocalText(text);
        ImageView ivSun = view.findViewById(R.id.iv_sun_status);
        if (showSunStatus) {
            if (sunStatus == 0) {
                ivSun.setImageResource(R.drawable.icon_sunrise);
            } else if (sunStatus == 1) {
                ivSun.setImageResource(R.drawable.icon_sunset);
            }
        } else {
            ivSun.setVisibility(View.INVISIBLE);
        }
        view.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, DensityUtil.dp2px(mContext, 52)));
        view.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED), View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
        view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
        return view;
    }

    public void setDrawSolar(boolean drawSolar) {
        isDrawSolar = drawSolar;
        showChartData();
    }

    public void setDrawPrice(boolean drawPrice) {
        isDrawPrice = drawPrice;
        showChartData();
    }

    public void setDrawPriceLevel(boolean drawPriceLevel) {
        isDrawPriceLevel = drawPriceLevel;
        showChartData();
    }

    public void setDrawChargeStatus(boolean drawChargeStatus) {
        isDrawChargeStatus = drawChargeStatus;
        showChartData();
    }

    private int getChargeMode(int index) {
        int nowHour = getHour(startTime);
        int hour = (nowHour + index) % 24;
        boolean isCustom = weekdays[hour] != -128;
        int percent = isCustom ? weekdays[hour] : hopeChargeDischarges.get(index);
        if (percent < 0) {
            return -1;
        } else if (percent > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    private int getHour(long time) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        calendar.setTimeInMillis(time * 1000);
        int nowHour = calendar.get(Calendar.HOUR_OF_DAY);
        return nowHour;
    }

    @Override
    public void drawTopOffsetContent(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas) {
        if (isDrawChargeStatus) {
            float itemWidth = portHandler.contentWidth() / 36f;
            for (int i = 0; i < hopeChargeDischarges.size(); i++) {
                mChargePlanRectF.left = portHandler.contentLeft() + i * itemWidth;
                mChargePlanRectF.top = portHandler.contentTop() - mHourGridStrokeWidth - mChargeDataHeight;
                mChargePlanRectF.right = mChargePlanRectF.left + itemWidth;
                mChargePlanRectF.bottom = portHandler.contentTop();
                int mode = getChargeMode(i);
                if (mode == -1) {
                    mChargePlanPaint.setColor(getColor(R.color.color_brand_primary));
                } else if (mode == 1) {
                    mChargePlanPaint.setColor(getColor(R.color.color_white_02));
                } else {
                    mChargePlanPaint.setColor(getColor(R.color.transparent));
                }
                canvas.drawRect(mChargePlanRectF, mChargePlanPaint);
            }
        }
    }

    @Override
    public void drawBottomOffsetContent(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas) {
        float leftOffset = combinedChart.getExtraLeftOffset();
        if (leftOffset == 0) {
            leftOffset = DensityUtil.dp2px(mContext, 10);
        }
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        int nowHour = getHour(startTime);
        String now = Local.s("Now");
        if (mNowView == null) {
            mNowView = createTimeView(now, 0, true, false);
        }
        canvas.translate(portHandler.offsetLeft() + mNowOffset, portHandler.contentBottom());
        mNowView.draw(canvas);
        canvas.translate(-(portHandler.offsetLeft() + mNowOffset), -portHandler.contentBottom());
        float nowEnd = portHandler.contentLeft() + mNowView.getMeasuredWidth();
        calendar.setTimeInMillis(startTime * 1000L);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long startTimeDealt = calendar.getTimeInMillis() / 1000L;
        float[] indexBuffer = new float[2];
        Transformer trans = chart.getTransformer(YAxis.AxisDependency.LEFT);
        if (CollectionUtil.isListNotEmpty(sunrises)) {
            for (int i = 0; i < sunrises.size(); i++) {
                long time = sunrises.get(i);
                calendar.setTimeInMillis(time * 1000);
                calendar.setTimeZone(TimeZone.getTimeZone(timezone));
                int hour = calendar.get(Calendar.HOUR_OF_DAY);
                int minute = calendar.get(Calendar.MINUTE);
                int diffHour = (int) ((time - startTimeDealt) / 60 / 60);
                if (diffHour < 36) {
                    boolean nowIsSun = (TimeUtil.isSameDay(startTime * 1000, time * 1000) && hour == nowHour);
                    String text = (hour < 10 ? ("0" + hour) : String.valueOf(hour))
                            + ":"
                            + (minute < 10 ? ("0" + minute) : String.valueOf(minute));
                    View viewSun;
                    if (mSunriseViews.size() > i) {
                        viewSun = mSunriseViews.get(i);
                    } else {
                        viewSun = createTimeView(nowIsSun ? now : text, 0, nowIsSun, true);
                        mSunriseViews.add(viewSun);
                        mSunriseSunsetViews.add(viewSun);
                    }
                    indexBuffer[0] = diffHour;
                    indexBuffer[1] = 0;
                    trans.pointValuesToPixel(indexBuffer);
                    float translateX = indexBuffer[0] - viewSun.getMeasuredWidth() / 2f;
                    if (translateX > portHandler.contentLeft() + nowEnd && translateX < portHandler.contentRight()) {
                        if (translateX + viewSun.getMeasuredWidth() > portHandler.contentRight()) {
                            translateX = portHandler.contentRight() - viewSun.getMeasuredWidth();
                        }
                        canvas.translate(translateX, portHandler.contentBottom());
                        viewSun.draw(canvas);
                        canvas.translate(-translateX, -portHandler.contentBottom());
                        mSunriseSunsetXOffsetMap.put(viewSun, translateX);
                    }
                }
            }
        }

        if (CollectionUtil.isListNotEmpty(sunsets)) {
            for (int i = 0; i < sunsets.size(); i++) {
                long time = sunsets.get(i);
                calendar.setTimeInMillis(time * 1000);
                calendar.setTimeZone(TimeZone.getTimeZone(timezone));
                int hour = calendar.get(Calendar.HOUR_OF_DAY);
                int minute = calendar.get(Calendar.MINUTE);
                boolean nowIsSun = (TimeUtil.isSameDay(startTime * 1000, time * 1000) && hour == nowHour);
                String text = (hour < 10 ? ("0" + hour) : String.valueOf(hour))
                        + ":"
                        + (minute < 10 ? ("0" + minute) : String.valueOf(minute));
                View viewSun;
                if (mSunsetViews.size() > i) {
                    viewSun = mSunsetViews.get(i);
                } else {
                    viewSun = createTimeView(nowIsSun ? now : text, 1, nowIsSun, true);
                    mSunsetViews.add(viewSun);
                    mSunriseSunsetViews.add(viewSun);
                }
                int diffHour = (int) ((time - startTimeDealt) / 60 / 60);
                if (diffHour < 36) {
                    indexBuffer[0] = diffHour;
                    indexBuffer[1] = 0;
                    trans.pointValuesToPixel(indexBuffer);
                    float translateX = indexBuffer[0] - viewSun.getMeasuredWidth() / 2f;
                    if (translateX > portHandler.contentLeft() + nowEnd && translateX < portHandler.contentRight()) {
                        if (translateX + viewSun.getMeasuredWidth() > portHandler.contentRight()) {
                            translateX = portHandler.contentRight() - viewSun.getMeasuredWidth();
                        }
                        canvas.translate(translateX, portHandler.contentBottom());
                        viewSun.draw(canvas);
                        canvas.translate(-translateX, -portHandler.contentBottom());
                        mSunriseSunsetXOffsetMap.put(viewSun, translateX);
                    }
                }
            }
        }

        long secondDayTime = (startTime + 60 * 60 * 24) * 1000;
        calendar.setTimeInMillis(secondDayTime);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long secondDayTimeDealt = calendar.getTimeInMillis();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd MMM", LocalManager.getInstance().getCurrentLocale());
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(timezone));
        String secondDay = simpleDateFormat.format(new Date(secondDayTimeDealt));
        if (mSecondDayView == null) {
            mSecondDayView = createTimeView(secondDay, 0, false, false);
        }
        int diffHour = (int) ((secondDayTimeDealt / 1000L - startTime) / (60 * 60));
        float secondDayOffset = diffHour / 36f * portHandler.contentWidth() + leftOffset + 10;
        boolean isOverSecondDay = false;
        for (View view : mSunriseSunsetViews) {
            Float viewOffset = mSunriseSunsetXOffsetMap.get(view);
            if (viewOffset != null) {
                if ((viewOffset > secondDayOffset && viewOffset < secondDayOffset + mSecondDayView.getMeasuredWidth())
                        || (secondDayOffset > viewOffset && secondDayOffset < viewOffset + view.getMeasuredWidth())) {
                    isOverSecondDay = true;
                    break;
                }
            }

        }
        if (!isOverSecondDay) {
            canvas.translate(secondDayOffset, portHandler.contentBottom());
            mSecondDayView.draw(canvas);
            canvas.translate(-secondDayOffset, -portHandler.contentBottom());
        }
    }

    @Override
    public void drawHighlighted(AIScheduledCombinedChart chart, Canvas canvas, Highlight[] mIndicesToHighlight) {
        int xIndex = -1;
        if (mIndicesToHighlight != null && mIndicesToHighlight.length > 0) {
            xIndex = mIndicesToHighlight[0].getXIndex();
            if (xIndex >= relativePriceNorms.size()) {
                xIndex = relativePriceNorms.size() - 1;
            }
            float[] pts = new float[]{
                    xIndex, 0
            };
            chart.getTransformer(YAxis.AxisDependency.LEFT).pointValuesToPixel(pts);
            mPaint.setColor(getColor(R.color.color_white_01));
            mPaint.setStyle(Paint.Style.STROKE);
            mPaint.setStrokeWidth(DensityUtil.dp2px(mContext, 3));
            float x = pts[0];
            float stopY = isHighlightLineOver ? chart.getViewPortHandler().contentTop() - chart.getExtraTopOffset():
                    chart.getViewPortHandler().contentTop();
            canvas.drawLine(x, chart.getViewPortHandler().contentBottom(),
                    x, stopY, mPaint);
            mPaint.setStyle(Paint.Style.FILL);
            if (isDrawPrice) {
                ChartAnimator animator = chart.getAnimator();
                float phaseX = Math.max(0.f, Math.min(1.f, animator.getPhaseX()));
                float phaseY = animator.getPhaseY();
                float[] circlesBuffer = new float[2];
                List<ILineDataSet> dataSets = chart.getLineData().getDataSets();
                ILineDataSet dataSet = dataSets.get(0);
                Transformer trans = chart.getTransformer(dataSet.getAxisDependency());
                Entry e = dataSet.getEntryForIndex(xIndex);
                circlesBuffer[0] = e.getXIndex();
                circlesBuffer[1] = e.getVal() * phaseY;
                trans.pointValuesToPixel(circlesBuffer);
                float highlightCircleX = circlesBuffer[0];
                float highlightCircleY = circlesBuffer[1];
                mPaint.setColor(getColor(R.color.color_black_01));
                canvas.drawCircle(highlightCircleX, highlightCircleY, mHighLightOuterRadius, mPaint);
                mPaint.setColor(getColor(R.color.color_white_01));
                canvas.drawCircle(highlightCircleX, highlightCircleY, mHighLightInnerRadius, mPaint);
                if (isDrawChargeStatus) {
                    int mode = getChargeMode(xIndex);
                    if (mode != 0) {
                        Bitmap bitmap = BitmapFactory.decodeResource(mContext.getResources(), mode == -1 ?
                                R.drawable.icon_ai_discharge : R.drawable.icon_ai_charge);
                        canvas.drawBitmap(bitmap, highlightCircleX - bitmap.getWidth() / 2f,
                                highlightCircleY - bitmap.getWidth() / 2f, mPaint);
                    }
                }
            }
        }
        if (highlightListener != null) {
            highlightListener.highlight(xIndex);
        }
    }

    @Override
    public void drawExtremum(AIScheduledCombinedChart chart, Canvas canvas) {
        if (isDrawPrice) {
            int maxIndex = getMaxPriceIndex();
            int minIndex = getMinPriceIndex();
            mPaint.setStyle(Paint.Style.FILL);
            drawCircleByIndex(chart, canvas, maxIndex, getColor(R.color.transparent),
                    Local.s(mContext.getResources().getString(R.string.Price_Peak)), true);
            drawCircleByIndex(chart, canvas, minIndex, getColor(R.color.transparent),
                    Local.s(mContext.getResources().getString(R.string.Price_Bottom)), false);
        }
    }

    @Override
    public void drawExtra(AIScheduledCombinedChart chart, ViewPortHandler portHandler, Canvas canvas) {
        if (relativePriceNorms != null && relativePriceNorms.size() < 36) {
            int retrievingSize = 36 - relativePriceNorms.size();
            if (retrievingSize < 0) retrievingSize = 0;
            float coverWidth = retrievingSize / 36f * portHandler.contentWidth();
            float coverHeight = portHandler.contentHeight();
            float startX = portHandler.contentWidth() - coverWidth + chart.getExtraLeftOffset();
            mPaint.setAlpha(255);
            mPaint.setStyle(Paint.Style.FILL);
            if (mRetrievingGradient == null) {
                mRetrievingGradient = new LinearGradient(startX, portHandler.contentTop(),
                        startX, portHandler.contentBottom(),
                        new int[]{getColor(R.color.color_ai_retrieving_start_color),
                                getColor(R.color.color_ai_retrieving_end_color)},
                        new float[]{0f, 1f}, Shader.TileMode.CLAMP);
            }
            mPaint.setShader(mRetrievingGradient);
            mLinesPath.reset();
            mLinesPath.moveTo(startX, portHandler.contentBottom());
            mLinesPath.lineTo(startX, portHandler.contentTop());
            mLinesPath.lineTo(portHandler.contentRight() - mRadius, portHandler.contentTop());
            mLinesPath.quadTo(portHandler.contentRight(), portHandler.contentTop(),
                    portHandler.contentRight(), portHandler.contentTop() + mRadius);
            mLinesPath.lineTo(portHandler.contentRight(), portHandler.contentBottom());
            mLinesPath.close();
            canvas.drawPath(mLinesPath, mPaint);
            mPaint.setShader(null);
            mPaint.setColor(getColor(R.color.color_white_04));
            mPaint.setStyle(Paint.Style.STROKE);
            mPaint.setStrokeWidth(mHourGridStrokeWidth);
            canvas.drawPath(mLinesPath, mPaint);
            if (retrievingViewListener != null) {
                retrievingViewListener.widthAndHeight(coverWidth, coverHeight, retrievingSize);
            }
        }
    }

    private void drawCircleByIndex(AIScheduledCombinedChart chart, Canvas canvas,
                                   int index, int innerCircleColor, String note, boolean isNoteUp) {
        ChartAnimator animator = chart.getAnimator();
        float phaseX = Math.max(0.f, Math.min(1.f, animator.getPhaseX()));
        float phaseY = animator.getPhaseY();
        float[] circlesBuffer = new float[2];
        List<ILineDataSet> dataSets = chart.getLineData().getDataSets();
        ILineDataSet dataSet = dataSets.get(0);
        Transformer trans = chart.getTransformer(dataSet.getAxisDependency());
        Entry e = dataSet.getEntryForIndex(index);
        circlesBuffer[0] = e.getXIndex();
        circlesBuffer[1] = e.getVal() * phaseY;
        trans.pointValuesToPixel(circlesBuffer);
        float x = circlesBuffer[0];
        float y = circlesBuffer[1];
        mPaint.setColor(getColor(R.color.color_black_01));
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(DensityUtil.dp2px(mContext, 3));
        canvas.drawCircle(x, y, mExtremumOuterRadius, mPaint);
        mPaint.setColor(innerCircleColor);
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(x, y, mExtremumInnerRadius, mPaint);
        mPaint.getTextBounds(note, 0, note.length(), mTextRect);
        int leftRightPadding = DensityUtil.dp2px(mContext, 6);
        int topBottomPadding = DensityUtil.dp2px(mContext, 3);
        int extremumSpace = DensityUtil.dp2px(mContext, 2);
        mRectF.left = x - mTextRect.width() / 2f - leftRightPadding;
        mRectF.right = x + mTextRect.width() / 2f + leftRightPadding;
        if (isNoteUp) {
            mRectF.top = y - mExtremumOuterRadius - extremumSpace - mTextRect.height() - 2 * topBottomPadding;
            mRectF.bottom = y - mExtremumOuterRadius - extremumSpace;
        } else {
            mRectF.top = y + mExtremumOuterRadius + extremumSpace;
            mRectF.bottom = y + mExtremumOuterRadius + extremumSpace + mTextRect.height() + 2 * topBottomPadding;
        }
        float rectWidth = mRectF.width();
        int radius = DensityUtil.dp2px(mContext, 15);
        if (mRectF.right > ScreenUtils.getScreenWidth(mContext)) {
            mRectF.right = ScreenUtils.getScreenWidth(mContext) - radius / 3f * 2f;
            mRectF.left = mRectF.right - rectWidth;
        }
        if (mRectF.left < 0) {
            mRectF.left = 0;
            mRectF.right = mRectF.left + rectWidth;
        }
        mPaint.setColor(getColor(R.color.color_brand_dark_03));
        canvas.drawRoundRect(mRectF, radius, radius, mPaint);
        mPaint.setColor(getColor(R.color.color_white_01));
        canvas.drawText(note, mRectF.left + leftRightPadding, mRectF.bottom - topBottomPadding, mPaint);
    }

    private int getMaxPriceIndex() {
        int index = 0;
        if (CollectionUtil.isListNotEmpty(relativePriceNorms)) {
            float maxPrice = relativePriceNorms.get(0);
            for (int i = 1; i < relativePriceNorms.size(); i++) {
                float price = relativePriceNorms.get(i);
                if (price > maxPrice) {
                    maxPrice = price;
                    index = i;
                }
            }
        }
        return index;
    }

    private int getMinPriceIndex() {
        int index = 0;
        if (CollectionUtil.isListNotEmpty(relativePriceNorms)) {
            float minPrice = relativePriceNorms.get(0);
            for (int i = 1; i < relativePriceNorms.size(); i++) {
                float price = relativePriceNorms.get(i);
                if (price < minPrice) {
                    minPrice = price;
                    index = i;
                }
            }
        }
        return index;
    }

    private float getPriceAver() {
        float aver = 0f;
        if (CollectionUtil.isListNotEmpty(relativePriceNorms)) {
            float sum = 0;
            for (float price : relativePriceNorms) {
                sum = sum + price;
            }
            aver = sum / relativePriceNorms.size();
        }
        return aver;
    }

    public int getC1() {
        return c1;
    }

    public void setC1(int c1) {
        this.c1 = c1;
    }

    public int getC2() {
        return c2;
    }

    public void setC2(int c2) {
        this.c2 = c2;
    }

    public int getC3() {
        return c3;
    }

    public void setC3(int c3) {
        this.c3 = c3;
    }

    public int getS1() {
        return s1;
    }

    public void setS1(int s1) {
        this.s1 = s1;
    }

    public int getS2() {
        return s2;
    }

    public void setS2(int s2) {
        this.s2 = s2;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = 1745905344;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public List<Integer> getConditions() {
        return conditions;
    }

    public void setConditions(List<Integer> conditions) {
        this.conditions = conditions;
    }

    public List<Integer> getForecastSolars() {
        return forecastSolars;
    }

    public void setForecastSolars(List<Integer> forecastSolars) {
        this.forecastSolars = forecastSolars;
    }

    public List<Integer> getHopeChargeDischarges() {
        return hopeChargeDischarges;
    }

    public void setHopeChargeDischarges(List<Integer> hopeChargeDischarges) {
        this.hopeChargeDischarges = hopeChargeDischarges;
    }

    public List<Float> getMarketPrices() {
        return marketPrices;
    }

    public void setMarketPrices(List<Float> marketPrices) {
        this.marketPrices = marketPrices;
    }

    public List<Integer> getPlans() {
        return plans;
    }

    public void setPlans(List<Integer> plans) {
        this.plans = plans;
    }

    public List<Float> getRelativePriceNorms() {
        return relativePriceNorms;
    }

    public void setRelativePriceNorms(List<Float> relativePriceNorms) {
        this.relativePriceNorms = relativePriceNorms;
    }

    public List<Long> getSunriseSunsetDates() {
        return sunriseSunsetDates;
    }

    public void setSunriseSunsetDates(List<Long> sunriseSunsetDates) {
        this.sunriseSunsetDates = sunriseSunsetDates;
    }

    public List<Float> getUserPrices() {
        return userPrices;
    }

    public void setUserPrices(List<Float> userPrices) {
        this.userPrices = userPrices;
    }

    public Integer[] getWeekdays() {
        return weekdays;
    }

    public void setWeekdays(Integer[] weekdays) {
        this.weekdays = weekdays;
    }

    public void setHighlightListener(OnHighlightListener highlightListener) {
        this.highlightListener = highlightListener;
    }

    public void setRetrievingViewListener(OnRetrievingViewListener retrievingViewListener) {
        this.retrievingViewListener = retrievingViewListener;
    }

    public void setEdit(boolean edit) {
        isEdit = edit;
        if (combinedChart != null) {
            combinedChart.setEdit(isEdit);
        }
    }

    public static class Builder {
        private AIScheduledCombinedChart combinedChart;
        private int c1 = -60;
        private int c2 = -40;
        private int c3 = -20;
        private int s1 = 20;
        private int s2 = 40;
        private long startTime;
        private String timezone;
        private float absZeroEcPrices;
        private boolean isHighlightLineOver;
        private List<Integer> conditions;
        private List<Integer> forecastSolars;
        private List<Integer> hopeChargeDischarges;
        private List<Float> marketPrices;
        private List<Integer> plans;
        private List<Float> relativePriceNorms;
        private List<Long> sunriseSunsetDates;
        private List<Long> sunrises;
        private List<Long> sunsets;
        private List<Float> userPrices;
        private Integer[] weekdays;
        private OnHighlightListener highlightListener;
        private OnRetrievingViewListener retrievingViewListener;

        public Builder(AIScheduledCombinedChart combinedChart) {
            this.combinedChart = combinedChart;
        }

        public Builder setC1(int c1) {
            this.c1 = c1;
            return this;
        }

        public Builder setC2(int c2) {
            this.c2 = c2;
            return this;
        }

        public Builder setC3(int c3) {
            this.c3 = c3;
            return this;
        }

        public Builder setS1(int s1) {
            this.s1 = s1;
            return this;
        }

        public Builder setS2(int s2) {
            this.s2 = s2;
            return this;
        }

        public Builder setStartTime(long startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder setTimezone(String timezone) {
            this.timezone = timezone;
            return this;
        }

        public Builder setAbsZeroEcPrices(float absZeroEcPrices) {
            this.absZeroEcPrices = absZeroEcPrices;
            return this;
        }

        public Builder setHighlightLineOver(boolean isHighlightLineOver) {
            this.isHighlightLineOver = isHighlightLineOver;
            return this;
        }

        public Builder setConditions(List<Integer> conditions) {
            this.conditions = conditions;
            return this;
        }

        public Builder setForecastSolars(List<Integer> forecastSolars) {
            this.forecastSolars = forecastSolars;
            return this;
        }

        public Builder setHopeChargeDischarges(List<Integer> hopeChargeDischarges) {
            this.hopeChargeDischarges = hopeChargeDischarges;
            return this;
        }

        public Builder setMarketPrices(List<Float> marketPrices) {
            this.marketPrices = marketPrices;
            return this;
        }

        public Builder setPlans(List<Integer> plans) {
            this.plans = plans;
            return this;
        }

        public Builder setRelativePriceNorms(List<Float> relativePriceNorms) {
            this.relativePriceNorms = relativePriceNorms;
            return this;
        }

        public Builder setSunriseSunsetDates(List<Long> sunriseSunsetDates) {
            this.sunriseSunsetDates = sunriseSunsetDates;
            return this;
        }

        public Builder setSunrise(List<Long> sunrise) {
            this.sunrises = sunrise;
            return this;
        }

        public Builder setSunset(List<Long> sunsets) {
            this.sunsets = sunsets;
            return this;
        }

        public Builder setUserPrices(List<Float> userPrices) {
            this.userPrices = userPrices;
            return this;
        }

        public Builder setWeekdays(Integer[] weekdays) {
            this.weekdays = weekdays;
            return this;
        }

        public Builder setHighlightListener(OnHighlightListener highlightListener) {
            this.highlightListener = highlightListener;
            return this;
        }

        public Builder setRetrievingViewListener(OnRetrievingViewListener retrievingViewListener) {
            this.retrievingViewListener = retrievingViewListener;
            return this;
        }

        public AIDataManager build() {
            return new AIDataManager(combinedChart, this);
        }
    }

    public interface OnHighlightListener {
        void highlight(int index);
    }

    public interface OnRetrievingViewListener {
        void widthAndHeight(float width, float height, int retrievingSize);
    }
}
