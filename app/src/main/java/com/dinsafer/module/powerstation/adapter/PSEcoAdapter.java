package com.dinsafer.module.powerstation.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsEconSonBinding;
import com.dinsafer.module.powerstation.bean.PsEcoRateBean;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class PSEcoAdapter extends PagerAdapter {

    private Context mContext;
    private LayoutInflater mLayoutInflater;
    private View mCurrentView;

    public PSEcoAdapter(Context context) {
        this.mContext = mContext;
        mLayoutInflater = LayoutInflater.from(context);
    }

    @Override
    public int getCount() {
        return 1000;
    }

    @Override
    public Object instantiateItem(final ViewGroup container, final int position) {
        FragmentPsEconSonBinding binding = DataBindingUtil.inflate(mLayoutInflater, R.layout.fragment_ps_econ_son, container, false);
        binding.rvRate.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.HORIZONTAL, false));
        PSEcoRateAdapter psEcoRateAdapter = new PSEcoRateAdapter();
        List<PsEcoRateBean> data = new ArrayList<>();
        data.add(new PsEcoRateBean(false));
        data.add(new PsEcoRateBean(false));
        data.add(new PsEcoRateBean(false));
        data.add(new PsEcoRateBean(false));
        psEcoRateAdapter.setNewData(data);
        binding.rvRate.setAdapter(psEcoRateAdapter);
        container.addView(binding.getRoot());
        return binding.getRoot();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view.equals(object);
    }

    @Override
    public void setPrimaryItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        super.setPrimaryItem(container, position, object);
        mCurrentView = (View) object;
    }

    public View getCurrentView() {
        return mCurrentView;
    }

    @Override
    public void destroyItem(final ViewGroup container, final int position, final Object object) {
        container.removeView((View) object);
    }


    public View getCurrentView(ViewPager viewPager) {
        try {
            final int currentItem = viewPager.getCurrentItem();
            for (int i = 0; i < viewPager.getChildCount(); i++) {
                final View child = viewPager.getChildAt(i);
                final ViewPager.LayoutParams layoutParams = (ViewPager.LayoutParams) child.getLayoutParams();

                Field f = layoutParams.getClass().getDeclaredField("position"); //NoSuchFieldException
                f.setAccessible(true);
                int position = (Integer) f.get(layoutParams); //IllegalAccessException

                if (!layoutParams.isDecor && currentItem == position) {
                    return child;
                }
            }
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }
}
