package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutPriceTrackingModeBinding;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDLog;

public class PriceTrackingModeView extends ConstraintLayout {

    private final String TAG = PriceTrackingModeView.class.getSimpleName();
    private Context mContext;
    private LayoutPriceTrackingModeBinding mBinding;

    public PriceTrackingModeView(Context context) {
        this(context, null);
    }

    public PriceTrackingModeView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PriceTrackingModeView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);

    }

    private void init(Context context) {
        mContext = context;
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_price_tracking_mode, this, true);
    }

    /**
     * 设置数据
     * @param modeBean
     */
    public void setData(ModeBean modeBean) {
        int dischargeLower = modeBean.getDischargeLower();
        int chargeLower = modeBean.getChargeLower();
        int dischargeEmergency = modeBean.getDischargeEmergency();
        int chargeEmergency = modeBean.getChargeEmergency();
        int dischargeSmart = modeBean.getDischargeSmart();
        int chargeSmart = modeBean.getChargeSmart();
        if (chargeLower <= 100 && chargeLower >= -100) {
            setViewHeight(mBinding.clBatteryChargeLower, mBinding.viewLowerDataArea,
                    mBinding.tvChargeLowerRevenue, chargeLower, false);

        }

        if (dischargeEmergency <= 100 && dischargeEmergency >= -100) {
            setViewHeight(mBinding.clBatteryDischargeEmergency, mBinding.viewDischargeEmergencyDataArea,
                    mBinding.tvDischargeEmergencyRevenue, dischargeEmergency, true);
        }

        if (chargeEmergency <= 100 && chargeEmergency >= -100) {
            setViewHeight(mBinding.clBatteryChargeEmergency, mBinding.viewChargeEmergencyDataArea,
                    mBinding.tvChargeEmergencyRevenue, chargeEmergency, false);
        }

        if (dischargeSmart <= 100 && dischargeSmart >= -100) {
            setViewHeight(mBinding.clBatteryDischargeSmart, mBinding.viewDischargeSmartDataArea,
                    mBinding.tvDischargeSmartRevenue, dischargeSmart, true);
        }

        if (chargeSmart <= 100 && chargeSmart >= -100) {
            setViewHeight(mBinding.clBatteryChargeSmart, mBinding.viewChargeSmartDataArea,
                    mBinding.tvChargeSmartRevenue, chargeSmart, false);
        }
    }

    /**
     * 设置权重
     * @param emergencyReserve
     * @param smartReserve
     */
    public void setReserveValue(int emergencyReserve, int smartReserve) {
        float emWeight = emergencyReserve / 100f;
        float smartWeight = smartReserve / 100f;
        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(mBinding.clParent);
        constraintSet.setHorizontalWeight(R.id.cl_battery_charge_lower, emWeight - 0.02f);
        constraintSet.setHorizontalWeight(R.id.cl_battery_discharge_emergency, smartWeight-emWeight);
        constraintSet.setHorizontalWeight(R.id.cl_battery_charge_emergency, smartWeight-emWeight);
        constraintSet.setHorizontalWeight(R.id.cl_battery_discharge_smart, 1-smartWeight);
        constraintSet.setHorizontalWeight(R.id.cl_battery_charge_smart, 1-smartWeight);
        constraintSet.applyTo(mBinding.clParent);
    }

    private void setViewHeight(ConstraintLayout constraintLayout, View view, LocalTextView tvVal, int percent, boolean discharge) {
        LayoutParams clParams = (LayoutParams) view.getLayoutParams();
        int parentHeight = constraintLayout.getHeight() / 2;
        int height = (int) (parentHeight * (1 - Math.abs(percent) / 100f));
        if (discharge) {
            if (percent < 0) {
                height = parentHeight + (int) (parentHeight * (Math.abs(percent) / 100f));
            }
        } else {
            if (percent > 0) {
                height = parentHeight + (int) (parentHeight * (Math.abs(percent) / 100f));
            }
        }
        clParams.height = height;
        view.setLayoutParams(clParams);

        String valStr = String.valueOf(Math.abs(percent)) + "%";
        if (percent < 0) {
            valStr = "-" + valStr;
        }
        if (percent > 0) {
            valStr = "+" + valStr;
        }
        tvVal.setLocalText(valStr);
    }

    public static class ModeBean {
        private int dischargeLower;
        private int chargeLower;
        private int dischargeEmergency;
        private int chargeEmergency;
        private int dischargeSmart;
        private int chargeSmart;

        public ModeBean(int dischargeLower, int chargeLower, int dischargeEmergency, int chargeEmergency, int dischargeSmart, int chargeSmart) {
            this.dischargeLower = dischargeLower;
            this.chargeLower = chargeLower;
            this.dischargeEmergency = dischargeEmergency;
            this.chargeEmergency = chargeEmergency;
            this.dischargeSmart = dischargeSmart;
            this.chargeSmart = chargeSmart;
        }

        public int getDischargeLower() {
            return dischargeLower;
        }

        public void setDischargeLower(int dischargeLower) {
            this.dischargeLower = dischargeLower;
        }

        public int getChargeLower() {
            return chargeLower;
        }

        public void setChargeLower(int chargeLower) {
            this.chargeLower = chargeLower;
        }

        public int getDischargeEmergency() {
            return dischargeEmergency;
        }

        public void setDischargeEmergency(int dischargeEmergency) {
            this.dischargeEmergency = dischargeEmergency;
        }

        public int getChargeEmergency() {
            return chargeEmergency;
        }

        public void setChargeEmergency(int chargeEmergency) {
            this.chargeEmergency = chargeEmergency;
        }

        public int getDischargeSmart() {
            return dischargeSmart;
        }

        public void setDischargeSmart(int dischargeSmart) {
            this.dischargeSmart = dischargeSmart;
        }

        public int getChargeSmart() {
            return chargeSmart;
        }

        public void setChargeSmart(int chargeSmart) {
            this.chargeSmart = chargeSmart;
        }
    }
}
