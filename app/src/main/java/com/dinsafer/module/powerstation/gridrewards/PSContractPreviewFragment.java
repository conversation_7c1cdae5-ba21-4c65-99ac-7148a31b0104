package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsContractPreviewBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.bean.GridRewardsAuthorization;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;

import org.glassfish.tyrus.core.StrictUtf8;

import java.io.IOException;
import java.util.Date;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import rx.Emitter;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.schedulers.Schedulers;

public class PSContractPreviewFragment extends MyBaseFragment<FragmentPsContractPreviewBinding> {

    // 0. 签约 1.解约
    private int mFrom;
    private String mSignBase64;
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;
    private GridRewardsAuthorization mAuthorization;
    private String mHtmlStr;
    private String mHomeId;
    private String mHomeName;
    private final String SERIALNUMBER = "Contract No.AUTH-YYYY-MM-DD-XXXXX-ZZZ";

    public static PSContractPreviewFragment newInstance(int from, String htmUrl, String signBase64, FamilyBalanceContractInfo familyBalanceContractInfo,
                                                        GridRewardsAuthorization authorization) {
        PSContractPreviewFragment fragment = new PSContractPreviewFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.KEY_FROM, from);
        bundle.putString("htmUrl", htmUrl);
        bundle.putString("signBase64", signBase64);
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        bundle.putParcelable("authorization", authorization);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_contract_preview;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText("");
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            mFrom = bundle.getInt(PSKeyConstant.KEY_FROM);
            mSignBase64 = bundle.getString("signBase64");
            String htmUrl = bundle.getString("htmUrl");
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
            mAuthorization = bundle.getParcelable("authorization");

            mHomeId = mFamilyBalanceContractInfo.getHome_id();
            mHomeName = mFamilyBalanceContractInfo.getHome_name();
            if (TextUtils.isEmpty(htmUrl) || TextUtils.isEmpty(mSignBase64) || mFamilyBalanceContractInfo == null) {
                showErrorToast();
                return;
            }

            mBinding.wvContract.getSettings().setJavaScriptEnabled(true);
            mBinding.wvContract.getSettings().setAllowFileAccess(true);
            mBinding.wvContract.getSettings().setSupportZoom(true);
            mBinding.wvContract.getSettings().setBuiltInZoomControls(true);
            mBinding.wvContract.getSettings().setDisplayZoomControls(false);
            mBinding.wvContract.setInitialScale(100);
            getHtmlStr(htmUrl);
        }
    }

    private void getHtmlStr(String htmUrl) {
        Observable.create(new Action1<Emitter<String>>() {
                    @Override
                    public void call(Emitter<String> emitter) {
                        OkHttpClient client = new OkHttpClient();
                        Request request = new Request.Builder()
                                .url(htmUrl)
                                .build();
                        try {
                            Response response = client.newCall(request).execute();
                            if (response.isSuccessful()) {
                                String htmlStr = response.body().byteString().string(StrictUtf8.defaultCharset());
                                DDLog.i(TAG, "htmlStr===" + htmlStr);
                                emitter.onNext(htmlStr);
                            } else {
                                emitter.onError(new Throwable("请求失败, 请稍后重试..."));
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                            emitter.onError(e);
                        }
                    }
                }, Emitter.BackpressureMode.DROP)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<String>() {

                    @Override
                    public void onStart() {
                        super.onStart();
                        showTimeOutLoadinFramgmentWithErrorAlert();
                    }

                    @Override
                    public void onCompleted() {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                        showErrorToast();
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                    }

                    @Override
                    public void onNext(String htmlStr) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        int type = mFamilyBalanceContractInfo.getType();

                        StringBuilder sbAddress = new StringBuilder();
                        sbAddress.append("    ");
                        sbAddress.append(mFamilyBalanceContractInfo.getStreetNameAndNumber());
                        sbAddress.append(", ");
                        sbAddress.append(mFamilyBalanceContractInfo.getZipCode());
                        sbAddress.append(", ");
                        sbAddress.append(mFamilyBalanceContractInfo.getCity());
                        sbAddress.append(", ");
                        sbAddress.append(mFamilyBalanceContractInfo.getCountryNameDisplay());
                        StringBuilder sbSign = new StringBuilder();
                        sbSign.append("data:image/png;base64,");
                        sbSign.append(mSignBase64);
                        String signingDay = "    " + DDDateUtil.formatDate(new Date(), "yyyy-MM-dd");
                        if (mFrom == 0) {
                            replaceSignHtmlStr(htmlStr, type,
                                    sbAddress.toString(), sbSign.toString(), signingDay);
                        } else {
                            replaceTerminateHtmlStr(htmlStr, type,
                                    sbAddress.toString(), sbSign.toString(), signingDay);
                        }
                        mBinding.wvContract.loadDataWithBaseURL(null, mHtmlStr, "text/html", "UTF-8", null);
                    }
                });
    }

    private void replaceSignHtmlStr(String htmlStr, int type,
                                    String addressStr, String signStr, String signingDayStr) {
        String firstAndLastName = mFamilyBalanceContractInfo.getName();
        String companyName = mFamilyBalanceContractInfo.getCompany_name();
        String euVatNumber = mFamilyBalanceContractInfo.getEuVatNumber();
        Log.i("TAG", "==========name: " + mHomeName + " " + mHomeId);
        mHtmlStr = htmlStr.replace("{{.SerialNumber}}", SERIALNUMBER)
                .replace("{{.AuthorizationCompanyName}}", mAuthorization.getCompanyName())
                .replace("{{.AuthorizationOrganizationNumber}}", mAuthorization.getOrganizationNumber())
                .replace("{{.AuthorizationAddress}}", mAuthorization.getAddress())
                .replace("{{.AuthorizationEmailAddress}}", mAuthorization.getEmailAddress())
                .replace("{{.FamilyName}}", mHomeName)
                .replace("{{.FamilyId}}", mHomeId)
                .replace("{{.Type}}", type == 0 ? "company" : "personal")
                .replace("{{.FirstAndLastName}}", !TextUtils.isEmpty(firstAndLastName) ? firstAndLastName : "")
                .replace("{{.CompanyName}}", !TextUtils.isEmpty(companyName) ? companyName : "")
                .replace("{{.EuVatNumber}}", !TextUtils.isEmpty(euVatNumber) ? euVatNumber : "")
                .replace("{{.EmailAddress}}", "    " + mFamilyBalanceContractInfo.getEmailAddress())
                .replace("{{.Address}}", addressStr)
                .replace("{{.SignURL}}", signStr)
                .replace("{{.SigningDay}}", signingDayStr)
                .replace("{{.Preview}}", "preview");
    }

    private void replaceTerminateHtmlStr(String htmlStr, int type,
                                         String addressStr, String signStr, String signingDayStr) {
        String name = mFamilyBalanceContractInfo.getType() == 0 ?
                mFamilyBalanceContractInfo.getCompany_name() :
                mFamilyBalanceContractInfo.getName();

        String firstAndLastName = mFamilyBalanceContractInfo.getName();
        String companyName = mFamilyBalanceContractInfo.getCompany_name();
        String euVatNumber = mFamilyBalanceContractInfo.getEuVatNumber();

        mHtmlStr = htmlStr.replace("{{.SerialNumber}}", SERIALNUMBER)
                .replace("{{.AuthorizationCompanyName}}", mAuthorization.getCompanyName())
                .replace("{{.AuthorizationOrganizationNumber}}", mAuthorization.getOrganizationNumber())
                .replace("{{.AuthorizationAddress}}", mAuthorization.getAddress())
                .replace("{{.AuthorizationEmailAddress}}", mAuthorization.getEmailAddress())
                .replace("{{.FirstAndLastName}}", !TextUtils.isEmpty(name) ? name : "")
                .replace("{{.StreetNameAndNumber}}", mFamilyBalanceContractInfo.getStreetNameAndNumber())
                .replace("{{.ZipCode}}", mFamilyBalanceContractInfo.getZipCode())
                .replace("{{.City}}", mFamilyBalanceContractInfo.getCity())
                .replace("{{.Country}}", mFamilyBalanceContractInfo.getCountryNameDisplay())
                .replace("{{.FamilyName}}", mHomeName)
                .replace("{{.FamilyId}}", mHomeId)
                .replace("{{.Type}}", type == 0 ? "company" : "personal")
                .replace("{{.FirstAndLastName}}", !TextUtils.isEmpty(firstAndLastName) ? firstAndLastName : "")
                .replace("{{.CompanyName}}", !TextUtils.isEmpty(companyName) ? companyName : "")
                .replace("{{.EuVatNumber}}", !TextUtils.isEmpty(euVatNumber) ? euVatNumber : "")
                .replace("{{.EmailAddress}}", "    " + mFamilyBalanceContractInfo.getEmailAddress())
                .replace("{{.Address}}", addressStr)
                .replace("{{.SignURL}}", signStr)
                .replace("{{.SigningDay}}", signingDayStr)
                .replace("{{.Preview}}", "preview");
    }

    private String getAttorneyHtmlText() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "  <meta charset=\"UTF-8\">\n" +
                "  <meta name=\"viewport\" content=\"initial-scale=1.0\">\n" +
                "  <link href=\"https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap\" rel=\"stylesheet\">\n" +
                "  <title>Power of attorney(preview)</title>\n" +
                "</head>\n" +
                "\n" +
                "<style>\n" +
                "  @page{\n" +
                "    size: A4;\n" +
                "    margin: 0.4in;\n" +
                "  }\n" +
                "\n" +
                "  @media print {\n" +
                "    .keep-together {\n" +
                "      page-break-inside: avoid;\n" +
                "      break-inside: avoid;\n" +
                "    }\n" +
                "    body{\n" +
                "      width: unset;\n" +
                "    }\n" +
                "    .container{\n" +
                "      padding: unset !important;\n" +
                "    }\n" +
                "  }\n" +
                "\n" +
                "  body{\n" +
                "    font-size: 10pt;\n" +
                "    width: 210mm;\n" +
                "    font-family: \"Poppins\";\n" +
                "  }\n" +
                "\n" +
                "  table {\n" +
                "      width: 100%;\n" +
                "      border-collapse: collapse; /* 合并边框 */\n" +
                "  }\n" +
                "  th, td {\n" +
                "      border: 1pt solid black; /* 添加边框 */\n" +
                "  }\n" +
                "  td{\n" +
                "    padding: 4pt 0;\n" +
                "    text-align: center;\n" +
                "  }\n" +
                "\n" +
                "  li{\n" +
                "    margin-top: 10pt;\n" +
                "    margin-bottom: 10pt;\n" +
                "  }\n" +
                "\n" +
                "  .c08{\n" +
                "    color: rgba(0, 0, 0, 0.8);\n" +
                "  }\n" +
                "\n" +
                "  .input-item{\n" +
                "    display: -webkit-flex;\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    flex: 1;\n" +
                "    margin-top: 16pt;\n" +
                "    align-items: baseline;\n" +
                "  }\n" +
                "  .input-content{\n" +
                "    border-bottom: 1pt solid rgba(0,0,0,0.8); \n" +
                "    flex: 1; \n" +
                "    margin-right: 4pt;\n" +
                "    white-space: pre-wrap;\n" +
                "    color: rgba(0,0,0,0.8);\n" +
                "    margin-left: 4pt;\n" +
                "  }\n" +
                "\n" +
                "  .sign-image{\n" +
                "    max-width: 186pt;\n" +
                "    max-height: 32pt;\n" +
                "    object-fit: contain;\n" +
                "  }\n" +
                "\n" +
                "  .container{\n" +
                "    padding: 0.4in;\n" +
                "  }\n" +
                "  \n" +
                "  #CompanyName, #EuVatNumber, #PersonName{\n" +
                "    display: none;\n" +
                "  }\n" +
                "\n" +
                "  .company #CompanyName, .company #EuVatNumber{\n" +
                "    display: flex;\n" +
                "  }\n" +
                "\n" +
                "  .personal #PersonName{\n" +
                "    display: flex;\n" +
                "  }\n" +
                "</style>\n" +
                "<body>\n" +
                "  <div class=\"container {{.Type}}\">\n" +
                "    <h2 class=\"c08\" style=\"text-align: center;\">Power of attorney(preview)</h2>\n" +
                "    <div class=\"c08\" style=\"text-align: center; margin-bottom: 24pt;\">Contract No.{{.SerialNumber}}</div>\n" +
                "    <div class=\"c08\">\n" +
                "      <p>For the holder of the power of attorney to represent us/me at the distribution network owner, electricity trading company, balance responsible and Svenska Kraftnät in the following:</p>\n" +
                "      <ul>\n" +
                "        <li>To handle bids with available flexible power from solar power, battery storage and electric car chargers that we/I own towards electricity trading companies, the balance responsible and Svenska Kraftnät.</li>\n" +
                "        <li>To represent us/me against distribution network owners regarding notification of EDIEL transmission of measurement data (hourly data) from connection points to the electricity network that we/I own.</li>\n" +
                "        <li>To obtain from distribution network owners the plant ID for connection points to the electricity network that we/I hold.</li>\n" +
                "        <li>To obtain from distribution network owners historical hourly data from connection points to the electricity network that we/I own.</li>\n" +
                "      </ul>\n" +
                "      <p>This Power of Attorney may be revoked by me at any time by providing written notice to my Authorization Holder, with the revocation taking effect within 7 days.</p>\n" +
                "    </div>\n" +
                "    <h4 class=\"c08\" style=\"margin-bottom: 8pt;\">Authorised devices information</h4>\n" +
                "    <table style=\"margin-bottom: 30pt; border: 0.5pt solid #000;\" >\n" +
                "      <colgroup>\n" +
                "        <col style=\"width: 16%;\">\n" +
                "        <col style=\"width: 34%;\">\n" +
                "        <col style=\"width: 16%;\">\n" +
                "        <col style=\"width: 34%;\">\n" +
                "      </colgroup>\n" +
                "      <tbody>\n" +
                "        {{.Devices}}\n" +
                "      </tbody>\n" +
                "    </table>\n" +
                "    <div class=\"keep-together\" >\n" +
                "      <div style=\"margin-bottom: 30pt;\">\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Power of Attorney Company Name</strong>\n" +
                "          <div class='input-content'>    {{.AuthorizationCompanyName}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorization Holder's Organization Number</strong>\n" +
                "          <div class='input-content'>    {{.AuthorizationOrganizationNumber}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorization Holder's Address</strong>\n" +
                "          <div class='input-content' style=\"color: rgba(0,0,0,1);\">    {{.AuthorizationAddress}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorization Holder's Email Address</strong>\n" +
                "          <div class='input-content'>    {{.AuthorizationEmailAddress}}</div>\n" +
                "        </div>\n" +
                "      </div>\n" +
                "      <div>\n" +
                "        <div class='input-item' id=\"PersonName\">\n" +
                "          <strong>Authorized Person's First and Last Name</strong>\n" +
                "          <div class='input-content'>    {{.FirstAndLastName}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item' id=\"CompanyName\">\n" +
                "          <strong>Authorized Person's  Company Name</strong>\n" +
                "          <div class='input-content'>    {{.CompanyName}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item' id=\"EuVatNumber\">\n" +
                "          <strong>The Authoriser's  EU VAT Number</strong>\n" +
                "          <div class='input-content'>    {{.EuVatNumber}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorized Person's Email Address</strong>\n" +
                "          <div class='input-content' style=\"color: rgba(0,0,0,1);\">    {{.EmailAddress}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Address</strong>\n" +
                "          <div class='input-content' style=\"color: rgba(0,0,0,1);\">    {{.Address}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <div style=\"display: flex; justify-content: space-between; flex: 1;  align-items: baseline;\">\n" +
                "            <strong>Signature</strong>\n" +
                "            <div class='input-content'>    <img class=\"sign-image\" src=\"{{.SignURL}}\" alt=\"Signature\"></div>\n" +
                "        </div>\n" +
                "          <div style=\"width: 35pt;\"></div>\n" +
                "          <div style=\"display: flex; justify-content: space-between; flex: 1; align-items: baseline;\">\n" +
                "            <strong>Date</strong>\n" +
                "            <div class='input-content' style=\"color: rgba(0,0,0,1);\" id=\"date\">    {{.SigningDay}}</div>\n" +
                "          </div>\n" +
                "        </div>\n" +
                "        <div class=\"c08\" style=\"margin-top: 16pt; color: rgba(0,0,0,0.8);\">*All information and data cited in this agreement shall be subject to the latest version</div>\n" +
                "      </div>\n" +
                "    </div>\n" +
                "  </div>\n" +
                "</body>\n" +
                "</html>";
    }

    private String getTerminateHtmlText() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "  <meta charset=\"UTF-8\">\n" +
                "  <meta name=\"viewport\" content=\"initial-scale=1.0\">\n" +
                "  <link href=\"https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap\" rel=\"stylesheet\">\n" +
                "  <title>TERMINATE STATEMENT(preview)</title>\n" +
                "</head>\n" +
                "\n" +
                "<style>\n" +
                "  @page{\n" +
                "    size: A4;\n" +
                "    margin: 0.4in;\n" +
                "  }\n" +
                "\n" +
                "  @media print {\n" +
                "    .keep-together {\n" +
                "      page-break-inside: avoid;\n" +
                "      break-inside: avoid;\n" +
                "    }\n" +
                "    body{\n" +
                "      width: unset;\n" +
                "    }\n" +
                "    .container{\n" +
                "      padding: unset !important;\n" +
                "    }\n" +
                "  }\n" +
                "\n" +
                "  body{\n" +
                "    font-size: 10pt;\n" +
                "    width: 210mm;\n" +
                "    font-family: \"Poppins\";\n" +
                "  }\n" +
                "\n" +
                "  table {\n" +
                "      width: 100%;\n" +
                "      border-collapse: collapse; /* 合并边框 */\n" +
                "  }\n" +
                "  th, td {\n" +
                "      border: 1pt solid black; /* 添加边框 */\n" +
                "  }\n" +
                "  td{\n" +
                "    padding: 4pt 0;\n" +
                "    text-align: center;\n" +
                "  }\n" +
                "\n" +
                "  li{\n" +
                "    margin-top: 10pt;\n" +
                "    margin-bottom: 10pt;\n" +
                "  }\n" +
                "\n" +
                "  .c08{\n" +
                "    color: rgba(0, 0, 0, 0.8);\n" +
                "  }\n" +
                "\n" +
                "  .input-item{\n" +
                "    display: -webkit-flex;\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    flex: 1;\n" +
                "    margin-top: 16pt;\n" +
                "    align-items: baseline;\n" +
                "  }\n" +
                "  .input-content{\n" +
                "    border-bottom: 1pt solid rgba(0,0,0,0.8); \n" +
                "    flex: 1; \n" +
                "    margin-right: 4pt;\n" +
                "    white-space: pre-wrap;\n" +
                "    color: rgba(0,0,0,0.8);\n" +
                "    margin-left: 4pt;\n" +
                "  }\n" +
                "\n" +
                "  .sign-image{\n" +
                "    max-width: 186pt;\n" +
                "    max-height: 32pt;\n" +
                "    object-fit: contain;\n" +
                "  }\n" +
                "\n" +
                "  .container{\n" +
                "    padding: 0.4in;\n" +
                "  }\n" +
                "\n" +
                "  #CompanyName, #EuVatNumber, #PersonName{\n" +
                "    display: none;\n" +
                "  }\n" +
                "\n" +
                "  .company #CompanyName, .company #EuVatNumber{\n" +
                "    display: flex;\n" +
                "  }\n" +
                "\n" +
                "  .personal #PersonName{\n" +
                "    display: flex;\n" +
                "  }\n" +
                "</style>\n" +
                "<body>\n" +
                "  <div class=\"container {{.Type}}\">\n" +
                "    <h2 id=\"a\" style=\"text-align: center;\">TERMINATE STATEMENT(preview)</h2>\n" +
                "    <div class=\"c08\" style=\"text-align: center; margin-bottom: 24pt;\">Contract No.{{.SerialNumber}}</div>\n" +
                "    <p style=\"margin-bottom: 30pt; font-size: 10pt;color: rgba(0,0,0,0.8); line-height: 1.4;\" >\n" +
                "      I, \n" +
                "      <span style=\"border-bottom: 1pt solid #000; white-space: pre-wrap;\">{{.FirstAndLastName}}</span>\n" +
                "      , residing at \n" +
                "      <span style=\"border-bottom: 1pt solid #000; white-space: pre-wrap;\">{{.StreetNameAndNumber}}, {{.ZipCode}}, {{.City}}, {{.Country}}</span>\n" +
                "      , voluntarily terminate the authorization granted to Emaldo ApS, incorporated in Denmark under business registration number DK 43724002 and registered office at Bygmarken 4A, 3650 Ølstykke, Denmark, for the following devices:</p>\n" +
                "    <h4 style=\"margin-bottom: 8pt; color: rgba(0,0,0,0.8);\">Terminating device information</h4>\n" +
                "    <table style=\"margin-bottom: 30pt; border: 0.5pt solid #000;\" >\n" +
                "      <colgroup>\n" +
                "        <col style=\"width: 16%;\">\n" +
                "        <col style=\"width: 34%;\">\n" +
                "        <col style=\"width: 16%;\">\n" +
                "        <col style=\"width: 34%;\">\n" +
                "      </colgroup>\n" +
                "      <tbody>\n" +
                "        {{.Devices}}\n" +
                "      </tbody>\n" +
                "    </table>\n" +
                "    <div class=\"keep-together\" >\n" +
                "      <div style=\"margin-bottom: 30pt;\">\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Power of Attorney Company Name</strong>\n" +
                "          <div class='input-content'>    {{.AuthorizationCompanyName}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorization Holder's Organization Number</strong>\n" +
                "          <div class='input-content'>    {{.AuthorizationOrganizationNumber}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorization Holder's Address</strong>\n" +
                "          <div class='input-content' style=\"color: rgba(0,0,0,1);\">    {{.AuthorizationAddress}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorization Holder's Email Address</strong>\n" +
                "          <div class='input-content'>    {{.AuthorizationEmailAddress}}</div>\n" +
                "        </div>\n" +
                "      </div>\n" +
                "      <div>\n" +
                "        <div class='input-item' id=\"PersonName\">\n" +
                "          <strong>Authorized Person's First and Last Name</strong>\n" +
                "          <div class='input-content'>    {{.FirstAndLastName}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item' id=\"CompanyName\">\n" +
                "          <strong>Authorized Person's  Company Name</strong>\n" +
                "          <div class='input-content'>    {{.CompanyName}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item' id=\"EuVatNumber\">\n" +
                "          <strong>The Authoriser's  EU VAT Number</strong>\n" +
                "          <div class='input-content'>    {{.EuVatNumber}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Authorized Person's Email Address</strong>\n" +
                "          <div class='input-content' style=\"color: rgba(0,0,0,1);\">    {{.EmailAddress}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <strong>Address</strong>\n" +
                "          <div class='input-content' style=\"color: rgba(0,0,0,1);\">    {{.Address}}</div>\n" +
                "        </div>\n" +
                "        <div class='input-item'>\n" +
                "          <div style=\"display: flex; justify-content: space-between; flex: 1;  align-items: baseline;\">\n" +
                "            <strong>Signature</strong>\n" +
                "            <div class='input-content'>    <img class=\"sign-image\" src=\"{{.SignURL}}\" alt=\"Signature\"></div>\n" +
                "          </div>\n" +
                "          <div style=\"width: 35pt;\"></div>\n" +
                "          <div style=\"display: flex; justify-content: space-between; flex: 1; align-items: baseline;\">\n" +
                "            <strong>Date</strong>\n" +
                "            <div class='input-content' style=\"color: rgba(0,0,0,1);\" id=\"date\">    {{.SigningDay}}</div>\n" +
                "          </div>\n" +
                "        </div>\n" +
                "        <div class=\"c08\" style=\"margin-top: 16pt; color: rgba(0,0,0,0.8);\">*All information and data cited in this agreement shall be subject to the latest version</div>\n" +
                "      </div>\n" +
                "    </div>\n" +
                "  </div>\n" +
                "</body>\n" +
                "</html>";
    }
}
