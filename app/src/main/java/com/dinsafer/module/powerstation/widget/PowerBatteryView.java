package com.dinsafer.module.powerstation.widget;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.CornerPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RadialGradient;
import android.graphics.Rect;
import android.graphics.Shader;
import android.graphics.Xfermode;

import androidx.annotation.Nullable;

import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.widget.segmentbar.Segment;
import com.dinsafer.util.DisplayUtil;

import java.util.List;

/**
 * 电池
 */
public class PowerBatteryView extends View {

    private static final int DEFAULT_BORDER_COLOR = Color.parseColor("#26FFFFFF");
    private static final int DEFAULT_INNER_LINE_COLOR = Color.parseColor("#08FFFFFF");


    private Context mContext;

    private int mWidth;
    private int mHeight;

    // 边框属性定义
    private Paint mBorderPaint;
    private Path mBorderPath;
    private float mBorderWidth;
    private int mCornerRadius;
    private int mBorderColor;

    // 顶部立体
    private Paint mTopViewPaint;
    private Path mTopViewPath;
    private int mTopViewHeight;

    // 中间线条
    private Paint mLinePaint;
    private Path mLinePath;
    private int mInnerLineWidth;
    private int mInnerLineColor;

    private Paint mShadowPaint;
    private Path mShadowPath;

    private Path mClipPath;

    // 波浪
    private Paint mWavePaint;
    private Path mWavePath;
    private int mWaveColor;
    private int mWaveDistance = dp2px(30); // 两个波的距离
    private float mWaveHeightPercent = 0.0f; // 当前电量百分比
    private float mWaveLen;  // 波浪长
    private int mWaveHeight = dp2px(30);  // 波浪高
    private float mCanvasFastOffsetX = 0f;  // 快偏移量
    private float mCanvasSlowOffsetX = 0f;  // 慢偏移量
    private float mFastWaveOffsetX = dp2px(6); // 移动快
    private float mSlowWaveOffsetX = dp2px(5); // 移动慢
    private ValueAnimator mWaveValueAnim;
    private ObjectAnimator mChargingValueAnim;

    // 显示百分比
    private float mProgress = 0.0f;
    private Paint mProgressPaint;//进度画笔
    private Paint mUnitPaint;//进度画笔
    private int mProgressColor; // 颜色
    private float mProgressTextSize;
    private float mUnitTextSize;
    private boolean mShowProgress;
    private Rect mProgressBounds = new Rect();
    private Rect mUnitBounds = new Rect();

    private Paint bPaint;

    private boolean isOnline;
    private int chargeStatus;
    public static final int DISCHARGING = -1;  // 放电中
    public static final int NORMAL = 0;  // 补充不放
    public static final int CHARGING = 1;  // 充电中

    private List<Segment> segments;

    public PowerBatteryView(Context context) {
        this(context, null);
    }

    public PowerBatteryView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PowerBatteryView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initAttrs(context, attrs);
        init(context);
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.PowerBatteryView);
        mBorderWidth = typedArray.getDimension(R.styleable.PowerBatteryView_battery_border_width, 0);
        mCornerRadius = (int) typedArray.getDimension(R.styleable.PowerBatteryView_battery_corner_radius, mBorderWidth * 2);
        mBorderColor = typedArray.getColor(R.styleable.PowerBatteryView_battery_boarder_color, DEFAULT_BORDER_COLOR);
        mTopViewHeight = (int) typedArray.getDimension(R.styleable.PowerBatteryView_battery_top_view_height, 0);
        mInnerLineWidth = (int) typedArray.getDimension(R.styleable.PowerBatteryView_battery_inner_line_width, dp2px(2));
        mInnerLineColor = typedArray.getColor(R.styleable.PowerBatteryView_battery_inner_line_color, DEFAULT_INNER_LINE_COLOR);
        mWaveColor = typedArray.getColor(R.styleable.PowerBatteryView_battery_wave_color, Color.parseColor("#a5FFDA58"));
        mWaveDistance = (int) typedArray.getDimension(R.styleable.PowerBatteryView_battery_wave_distance, dp2px(15));
        mProgressColor = typedArray.getColor(R.styleable.PowerBatteryView_battery_progress_color, Color.WHITE);
        mProgressTextSize = typedArray.getDimension(R.styleable.PowerBatteryView_battery_progress_text_size, sp2px(30));
        mUnitTextSize = typedArray.getDimension(R.styleable.PowerBatteryView_battery_unit_text_size, sp2px(14));
        mShowProgress = typedArray.getBoolean(R.styleable.PowerBatteryView_battery_show_progress, false);
        typedArray.recycle();
    }

    private void init(Context context) {
        bPaint = new Paint();
        bPaint.setAntiAlias(true);
        mTopViewPaint = new Paint();
        mTopViewPath = new Path();
        mTopViewPaint.setAntiAlias(true);
        mTopViewPaint.setStyle(Paint.Style.FILL);
        mTopViewPaint.setPathEffect(new CornerPathEffect(dp2px(5)));
        mTopViewPaint.setStrokeJoin(Paint.Join.ROUND);


        mBorderPaint = new Paint();
        mBorderPath = new Path();
//        mBorderPaint.setColor(mBorderColor);

        mBorderPaint.setStyle(Paint.Style.STROKE);
        mBorderPaint.setStrokeWidth(mBorderWidth);
        mBorderPaint.setStrokeCap(Paint.Cap.ROUND);
        mBorderPaint.setStrokeJoin(Paint.Join.ROUND);
        mBorderPaint.setAntiAlias(true);
        mBorderPaint.setDither(true);

        mClipPath = new Path();

        mLinePaint = new Paint();
        mLinePath = new Path();
        mLinePaint.setColor(mInnerLineColor);
        mLinePaint.setStyle(Paint.Style.STROKE);
        mLinePaint.setStrokeCap(Paint.Cap.ROUND);
        mLinePaint.setStrokeJoin(Paint.Join.ROUND);
        mLinePaint.setPathEffect(new CornerPathEffect(dp2px(5)));
        mLinePaint.setStrokeWidth(mInnerLineWidth);
        mLinePaint.setAntiAlias(true);
        mLinePaint.setDither(true);

        mShadowPaint = new Paint();
        mShadowPath = new Path();
        mShadowPaint.setAntiAlias(true);
        mShadowPaint.setColor(DEFAULT_BORDER_COLOR);
        mShadowPaint.setStyle(Paint.Style.FILL);
        mShadowPaint.setPathEffect(new CornerPathEffect(dp2px(5)));

        mWavePath = new Path();
        mWavePaint = new Paint();
        mWavePaint.setColor(mWaveColor);
        mWavePaint.setStyle(Paint.Style.FILL);
        mWavePaint.setAntiAlias(true);
        mWavePaint.setDither(true);

        mProgressPaint = new Paint();
        mProgressPaint.setAntiAlias(true);
        mProgressPaint.setColor(mProgressColor);
        mProgressPaint.setTextSize(mProgressTextSize);
        mProgressPaint.setStrokeWidth(dp2px(2));
        mUnitPaint = new Paint();
        mUnitPaint.setAntiAlias(true);
        mUnitPaint.setColor(mProgressColor);
        mUnitPaint.setTextSize(mUnitTextSize);
        mUnitPaint.setStrokeWidth(dp2px(2));

        initLoading();

    }

    private void refreshPath() {
        if (mTopViewHeight == 0) mTopViewHeight = mHeight / 10;
        int top = getPaddingTop() + mTopViewHeight;
        mTopViewPath.reset();
        mTopViewPath.moveTo(getPaddingLeft() + mBorderWidth / 2, top);
        mTopViewPath.lineTo(getPaddingLeft() + mBorderWidth * 3, getPaddingTop());
        mTopViewPath.lineTo(mWidth - getPaddingRight() - mBorderWidth * 3, getPaddingTop());
        mTopViewPath.lineTo(mWidth - getPaddingRight() - mBorderWidth, top);
        mTopViewPath.close();

        mTopViewPaint.setShader(new LinearGradient(getPaddingLeft() + mBorderWidth, 0, mWidth - getPaddingRight() - mBorderWidth, 0,
                Color.parseColor("#00ffffff"), Color.parseColor("#26ffffff"), Shader.TileMode.CLAMP));
        mBorderPaint.setShader(new LinearGradient(mWidth / 2, 0, mWidth / 2, mHeight,
                Color.parseColor("#4dffffff"), Color.parseColor("#26ffffff"), Shader.TileMode.CLAMP));

        mBorderPath.reset();
        mBorderPath.addRoundRect(getPaddingLeft() + mBorderWidth / 2f, top, mWidth - getPaddingRight() - mBorderWidth / 2f, mHeight - getPaddingBottom() - mBorderWidth / 2f, mCornerRadius, mCornerRadius, Path.Direction.CW);
//
//        if (mTopBitmap == null) {
//            mTopBitmap = Bitmap.createBitmap(mWidth, mHeight, Bitmap.Config.ARGB_8888);
//            Canvas canvas = new Canvas(mTopBitmap);
//            canvas.drawPath(mTopViewPath, mTopViewPaint);
//        }
//        if (mBorderBitmap == null) {
//            mBorderBitmap = Bitmap.createBitmap(mWidth, mHeight, Bitmap.Config.ARGB_8888);
//            Canvas canvas = new Canvas(mBorderBitmap);
//            canvas.drawRoundRect(getPaddingLeft()+ mBorderWidth / 2f, top, mWidth-getPaddingRight()- mBorderWidth / 2f,
//                    mHeight-getPaddingBottom()-+ mBorderWidth / 2f, mCornerRadius, mCornerRadius, mBorderPaint);
//        }
        mClipPath.reset();
        mClipPath.addRoundRect(getPaddingLeft() + mBorderWidth - dp2px(0.5f), top + mBorderWidth / 2, mWidth - getPaddingRight() - mBorderWidth + dp2px(0.5f), mHeight - getPaddingBottom() - mBorderWidth, mCornerRadius * 3 / 4, mCornerRadius * 3 / 4, Path.Direction.CW);

        mLinePath.reset();
        float startX = getPaddingLeft() + mBorderWidth;
        int startY = top;
        mLinePath.moveTo(startX, startY);
        mLinePath.rLineTo(2 * mBorderWidth, -3 * mBorderWidth);
        mLinePath.rLineTo(mWidth - getPaddingLeft() - getPaddingRight() - 6 * mBorderWidth, 0);
        mLinePath.rLineTo(2 * mBorderWidth, 3 * mBorderWidth);

        mShadowPath.moveTo(mBorderWidth, top + mBorderWidth);
        mShadowPath.rLineTo(2 * mBorderWidth, 0);
        mShadowPath.rLineTo(0, mHeight - mHeight / 5);
        mShadowPath.rLineTo(-2 * mBorderWidth, mHeight / 5);
        mShadowPath.close();

        mWaveLen = mWidth - mBorderWidth * 2;
        mWaveDistance = chargeStatus == NORMAL ? mHeight / 20 : mHeight / 10;
        mWaveHeight = (int) ((mHeight - top - 2 * mBorderWidth) / 6);
        mFastWaveOffsetX = mWidth / 50;
        mSlowWaveOffsetX = mWidth / 60;
        mWavePath.reset();
        if (chargeStatus == NORMAL) {
            mWavePath.moveTo(mBorderWidth, 0);
            mWavePath.rLineTo(0, mHeight - top - 2 * mBorderWidth);
            mWavePath.rLineTo(mWidth - mBorderWidth, 0);
            mWavePath.rLineTo(0, -(mHeight - top - 2 * mBorderWidth));
        } else {
            mWavePath.moveTo(-mWaveLen * 1.5f, 0);
            for (int i = 0; i < 4; i++) {
                mWavePath.rQuadTo(mWaveLen / 4f, mWaveHeight / 2f, mWaveLen / 2f, 0);
                mWavePath.rQuadTo(mWaveLen / 4f, -mWaveHeight / 2f, mWaveLen / 2f, 0);
            }
            mWavePath.rLineTo(0, mHeight);
            mWavePath.rLineTo(-mWaveLen * 4, 0);
        }
        mWavePath.close();
        postInvalidate();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        refreshPath();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        //设置宽高,默认200dp
        int defaultSize = dp2px(200);
        setMeasuredDimension(measureWidth(widthMeasureSpec, defaultSize),
                measureHeight(heightMeasureSpec, defaultSize));
        setMeasuredDimension(measureWidth(widthMeasureSpec, defaultSize),
                measureHeight(heightMeasureSpec, defaultSize));
        mWidth = getMeasuredWidth();
        mHeight = getMeasuredHeight();

    }

    /**
     * 测量宽
     *
     * @param measureSpec
     * @param defaultSize
     * @return
     */
    private int measureWidth(int measureSpec, int defaultSize) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = defaultSize + getPaddingLeft() + getPaddingRight();
            if (specMode == MeasureSpec.AT_MOST) {
                result = Math.min(result, specSize);
            }
        }
        result = Math.max(result, getSuggestedMinimumWidth());
        return result;
    }

    /**
     * 测量高
     *
     * @param measureSpec
     * @param defaultSize
     * @return
     */
    private int measureHeight(int measureSpec, int defaultSize) {
        int result = 0;
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);

        if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        } else {
            result = defaultSize + getPaddingTop() + getPaddingBottom();
            if (specMode == MeasureSpec.AT_MOST) {
                result = Math.min(result, specSize);
            }
        }
        result = Math.max(result, getSuggestedMinimumHeight());
        return result;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawPath(mTopViewPath, mTopViewPaint);

        canvas.drawPath(mBorderPath, mBorderPaint);
        drawLineInner(canvas);
        drawWave(canvas);
        drawProgress(canvas);
    }

    private void drawLineInner(Canvas canvas) {
        canvas.save();
        canvas.clipPath(mClipPath);
        float innerHeight = mHeight - getPaddingTop() - getPaddingBottom() - 2 * mBorderWidth;
        float gap = innerHeight / 4;

        for (int i = 0; i < 3; i++) {
            canvas.translate(0, gap);
            canvas.drawPath(mLinePath, mLinePaint);
        }
        canvas.restore();
//        canvas.save();
//        canvas.clipPath(mBorderPath);
//        canvas.drawPath(mShadowPath, mShadowPaint);
//        canvas.restore();
    }

    /**
     * 画波浪
     *
     * @param canvas
     */
    private void drawWave(Canvas canvas) {
        canvas.save();
        canvas.clipPath(mClipPath);
        float top = mHeight * (1 - mWaveHeightPercent);

        canvas.save();
        if (mProgress <= 0.1f) {
            mWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DE34D43));
        } else if (mProgress > 0.1f && mProgress <= 0.3f) {
            mWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DE9A632));
        } else if (mProgress > 0.3f && mProgress <= 0.5f) {
            mWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4DFFD12E));
        } else if (mProgress > 0.5f && mProgress <= 0.8f) {
            mWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4D35C38A));
        } else if (mProgress > 0.8f && mProgress <= 1f) {
            mWavePaint.setColor(mContext.getResources().getColor(R.color.power_station_battery_4D00C2B7));
        }
        canvas.translate(mCanvasFastOffsetX, top);
        canvas.drawPath(mWavePath, mWavePaint);
        canvas.restore();
        canvas.translate(mCanvasSlowOffsetX, top + mWaveDistance);
        canvas.drawPath(mWavePath, mWavePaint);

        canvas.restore();
    }

    /**
     * 画百分比
     *
     * @param canvas
     */
    private void drawProgress(Canvas canvas) {
        if (mShowProgress) {
            String progress = String.valueOf((int) (mProgress * 100));
            mProgressPaint.getTextBounds(progress, 0, progress.length(), mProgressBounds);
            String percent = "%";
            mUnitPaint.getTextBounds(percent, 0, percent.length(), mUnitBounds);
            canvas.drawText(isOnline ? progress : mContext.getString(R.string.power_station_cdv_offline_val), mWidth / 2 - mProgressBounds.width() / 2, mHeight / 2 + mProgressBounds.height() / 2, mProgressPaint);
            canvas.drawText(percent, mWidth / 2 + mProgressBounds.width() / 2 + dp2px(5), mHeight / 2 + mUnitBounds.height() / 2, mUnitPaint);
        }
    }


    private void initLoading() {
        mWaveValueAnim = ValueAnimator.ofFloat(0f, 1f);
        mWaveValueAnim.setDuration(1000);
        mWaveValueAnim.setRepeatCount(ValueAnimator.INFINITE);
        mWaveValueAnim.setRepeatMode(ValueAnimator.RESTART);
        mWaveValueAnim.setInterpolator(new LinearInterpolator());
        mWaveValueAnim.addUpdateListener(animation -> {
            if (0 == mWaveLen) {
                return;
            }
            mCanvasFastOffsetX = (mCanvasFastOffsetX + mFastWaveOffsetX) % mWaveLen;
            mCanvasSlowOffsetX = (mCanvasSlowOffsetX + mSlowWaveOffsetX) % mWaveLen;
            postInvalidate();
        });
    }

    private void initChargingLoading() {
        if (mChargingValueAnim == null) {
            mChargingValueAnim = ObjectAnimator.ofFloat(this, "waveHeightPercent", mProgress, 1);
            mChargingValueAnim.setDuration(5000);
            mChargingValueAnim.setRepeatCount(ValueAnimator.INFINITE);
            mChargingValueAnim.setRepeatMode(ValueAnimator.REVERSE);
            mChargingValueAnim.setInterpolator(new LinearInterpolator());
        }

    }

    private void startLoading() {
        if (mWaveValueAnim != null)
            mWaveValueAnim.start();
    }

    public void cancelLoading() {
        if (mWaveValueAnim != null)
            mWaveValueAnim.cancel();
    }

    private void startCharging() {
        if (mChargingValueAnim != null)
            mChargingValueAnim.start();
    }

    public void cancelCharging() {
        if (mChargingValueAnim != null)
            mChargingValueAnim.cancel();
    }

    public void setWaveHeightPercent(float progress) {
        this.mWaveHeightPercent = progress;
        invalidate();
    }

    public void setProgress(float progress) {
        this.mProgress = progress;
        initChargingLoading();
        invalidate();
    }

    public void setWaveColor(int waveColor) {
        this.mWaveColor = waveColor;
        if (mWavePaint != null) {
            mWavePaint.setColor(mWaveColor);
        }
    }

    public void setChargeStatus(int status) {
        this.chargeStatus = status;
        if (chargeStatus == CHARGING) {
            startCharging();
        } else {
            cancelCharging();
        }
        if (chargeStatus == NORMAL) {
            mCanvasFastOffsetX = 0;
            mCanvasSlowOffsetX = 0;
            cancelLoading();
            cancelCharging();
        } else {
            startLoading();
        }
        refreshPath();
        invalidate();
    }

    public void setOnline(boolean online) {
        isOnline = online;
    }

    /**
     * 分段数据
     *
     * @param segments 分段数据
     */
    public void setSegments(List<Segment> segments) {
        this.segments = segments;
        invalidate();
        requestLayout();
    }

    @Override
    protected void onDetachedFromWindow() {
//        cancelLoading();
        super.onDetachedFromWindow();
    }

    private static int dp2px(final float dp) {
        return DisplayUtil.dip2px(DinSaferApplication.getAppContext(), dp);
    }

    private static int sp2px(final float sp) {
        return DisplayUtil.sp2px(DinSaferApplication.getAppContext(), sp);
    }

}
