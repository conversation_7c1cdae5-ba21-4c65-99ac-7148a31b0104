package com.dinsafer.module.powerstation.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.PSAdvancedSettingsItemBean;
import com.dinsafer.ui.CopyMenuTextView;
import com.dinsafer.ui.LocalTextView;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 23:02
 * @description :
 */
public class PSAdvancedSettingsAdapter extends BaseQuickAdapter<PSAdvancedSettingsItemBean, BaseViewHolder> {

    public PSAdvancedSettingsAdapter() {
        super(R.layout.item_ps_advanced_settings);
    }

    @Override
    protected void convert(BaseViewHolder helper, PSAdvancedSettingsItemBean item) {
        ImageView ivLogo = helper.getView(R.id.iv_logo);
        View view = helper.getView(R.id.view_line);
        LocalTextView tvKey = helper.getView(R.id.tv_key);
        LocalTextView tvValue = helper.getView(R.id.tv_value);
        CopyMenuTextView tvCopiedValue = helper.getView(R.id.tv_copied_value);
        tvKey.setLocalText(item.getKey());
        tvValue.setLocalText(item.getValue());
        tvCopiedValue.setLocalText(item.getValue());
        view.setVisibility(helper.getAdapterPosition() == getData().size() - 1 ? View.GONE : View.VISIBLE);
        String value = item.getValue();
        boolean isCopied = item.isCopied();
        tvValue.setVisibility(((value != null && value.trim().length() > 0) || item.isShowDot()) && !isCopied ? View.VISIBLE : View.GONE);
        tvCopiedValue.setVisibility(((value != null && value.trim().length() > 0) || item.isShowDot()) && isCopied ? View.VISIBLE : View.GONE);
        refreshBmtUpdateDotVisible(item.isShowDot(), tvValue);
        int logo = item.getLogo();
        ivLogo.setVisibility(logo == 0 ? View.GONE : View.VISIBLE);
        if (logo != 0) {
            ivLogo.setImageResource(logo);
        }
        LinearLayout itemRoot = helper.getView(R.id.item_root);
        LinearLayout.LayoutParams  params = (LinearLayout.LayoutParams) itemRoot.getLayoutParams();
        params.height = item.isVisible() ? LinearLayout.LayoutParams.WRAP_CONTENT : 0;
        itemRoot.setLayoutParams(params);
        view.setVisibility(item.isVisible() ? View.VISIBLE : View.GONE);
        itemRoot.setAlpha(item.isOnline() ? 1f : 0.5f);
        helper.addOnClickListener(R.id.tv_copied_value);
    }

    private void refreshBmtUpdateDotVisible(final boolean showDot, final LocalTextView tvValue) {
        if (showDot) {
            tvValue.setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.shape_dot, 0, 0, 0);
        } else {
            tvValue.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0);
        }
    }
}
