package com.dinsafer.module.powerstation.event;

/**
 * Bmt电池数量更新事件
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/12/30 3:42 下午
 */
public class BmtListUpdateEvent {

    public static final int OPERATION_ADD = 0x1;
    public static final int OPERATION_DELETE = 0x1 << 1;

    private final int operationType;
    private final String deviceId;
    private final String subcategory;

    public BmtListUpdateEvent() {
        this("", "", 0);
    }

    public BmtListUpdateEvent(String deviceId, String subcategory, int operationType) {
        this.operationType = operationType;
        this.deviceId = deviceId;
        this.subcategory = subcategory;
    }

    public int getOperationType() {
        return operationType;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    @Override
    public String toString() {
        return "BmtListUpdateEvent{" +
                "operationType=" + operationType +
                ", deviceId='" + deviceId + '\'' +
                ", subcategory='" + subcategory + '\'' +
                '}';
    }
}
