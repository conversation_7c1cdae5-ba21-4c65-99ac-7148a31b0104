package com.dinsafer.module.powerstation.impacts;

import android.os.Bundle;
import android.view.View;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.dinsafer.common.BmtManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAiModeEditCurrentBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.adapter.AiModeEditModel;
import com.dinsafer.module.powerstation.event.AIChargeModeEvent;
import com.dinsafer.module.powerstation.event.AIMainChargeModeEvent;
import com.dinsafer.module.powerstation.event.ScheduledModeEvent;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

public class AIModeEditCurrentFragment extends MyBaseFragment<FragmentAiModeEditCurrentBinding> {

    private static final String TAG = AIModeEditCurrentFragment.class.getSimpleName();

    private BindMultiAdapter<AiModeEditModel> mAdapter;
    private ArrayList<AiModeEditModel> mData;

    private int selectedMode;
    private int sectionType;
    private int iotEmergency = 30;
    private int iotSmart = 70;
    private boolean isGridToBattery;
    private int currentIotChargeDischarges;
    
    public static AIModeEditCurrentFragment newInstance() {
        AIModeEditCurrentFragment fragment = new AIModeEditCurrentFragment();
        return fragment;
    }

    public static AIModeEditCurrentFragment newInstance(int currentIotChargeDischarges, int iotSmart, int iotEmergency) {
        AIModeEditCurrentFragment fragment = new AIModeEditCurrentFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PSKeyConstant.CURRENT_IOT_CHARGE_DISCHARGES, currentIotChargeDischarges);
        bundle.putInt(PSKeyConstant.IOT_SMART, iotSmart);
        bundle.putInt(PSKeyConstant.IOT_EMERGENCY, iotEmergency);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ai_mode_edit_current;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        Bundle bundle = getArguments();
        currentIotChargeDischarges = bundle.getInt(PSKeyConstant.CURRENT_IOT_CHARGE_DISCHARGES);
        iotSmart = bundle.getInt(PSKeyConstant.IOT_SMART);
        iotEmergency = bundle.getInt(PSKeyConstant.IOT_EMERGENCY);
        if(currentIotChargeDischarges > 0) {
            selectedMode = 1;
            if(currentIotChargeDischarges == iotEmergency) {
                sectionType = 1;
            } else if (currentIotChargeDischarges == iotSmart) {
                sectionType = 2;
            } else {
                sectionType = 3;
            }
        } else if(currentIotChargeDischarges == 0) {
            selectedMode = 0;
        } else if(currentIotChargeDischarges == -128) {
            selectedMode = -2;
        }else {
            selectedMode = -1;
            if(currentIotChargeDischarges == -iotEmergency) {
                sectionType = 1;
            } else if (currentIotChargeDischarges == -iotSmart) {
                sectionType = 2;
            }
        }
        DDLog.i(TAG,"iotSmart: " + iotSmart + "  iotEmergency: " + iotEmergency + "  currentIotChargeDischarges:" + currentIotChargeDischarges);
        initRv();
    }

    private void initRv() {
        mBinding.rvMode.setLayoutManager(new LinearLayoutManager(getContext()));
        ((SimpleItemAnimator)mBinding.rvMode.getItemAnimator()).setSupportsChangeAnimations(false);
        mAdapter = new BindMultiAdapter<>();
        mBinding.rvMode.setAdapter(mAdapter);
        mAdapter.setOnBindItemClickListener((OnBindItemClickListener<AiModeEditModel>) (v, position, model) -> {
            DDLog.d(TAG,"setOnBindItemClickListener ——> position: " + position );
            if (model.isSelected()) {
                for (AiModeEditModel aiModeEditModel : mData) {
                    aiModeEditModel.setSelected(false);
                }
                EventBus.getDefault().post(new AIChargeModeEvent(model.getSectionType(), model.getMode(),true));
                return;
            }
            for (AiModeEditModel aiModeEditModel : mData) {
                aiModeEditModel.setSelected(false);
            }
            model.setSectionType(2);
            model.setSelected(true);
            EventBus.getDefault().post(new AIChargeModeEvent(model.getSectionType(), model.getMode(),false));
            mAdapter.notifyDataSetChanged();
        });

        mAdapter.setOnBindItemChildClickListener((OnBindItemChildClickListener<AiModeEditModel>) (view, position, model) -> {
            DDLog.d(TAG,"setOnBindItemChildClickListener ——> position: " + position );
            int viewId = view.getId();
            if (viewId == R.id.tv_one) {
                model.setSectionType(1);
            } else if (viewId == R.id.tv_two) {
                model.setSectionType(2);
            } else if (viewId == R.id.tv_three) {
                model.setSectionType(3);
            }
            EventBus.getDefault().post(new AIChargeModeEvent(model.getSectionType(), model.getMode(),false));
            mAdapter.notifyItemChanged(position);
        });

        mData = new ArrayList<>();
        mData.add(new AiModeEditModel(getContext(), 1, sectionType, iotEmergency, iotSmart, selectedMode == 1, true));
        mData.add(new AiModeEditModel(getContext(), 0, sectionType, iotEmergency, iotSmart, selectedMode == 0, true));
        mData.add(new AiModeEditModel(getContext(), -1, sectionType, iotEmergency, iotSmart, selectedMode == -1, true));
        mAdapter.setNewData(mData);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    /**
     * 滑动bar对应值变化
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onScheduledModeEvent(ScheduledModeEvent event) {
        DDLog.d(TAG,"event: " + event);
        if (event.getEmergency()) {
            refreshData(event.getReserve(),true);
            iotEmergency = event.getReserve();
        } else {
            refreshData(event.getReserve(),false);
            iotSmart = event.getReserve();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAIMainChargeModeEvent(AIMainChargeModeEvent event) {
        DDLog.d(TAG,"AIMainChargeModeEvent: " + event);
        for (AiModeEditModel aiModeEditModel : mData) {
            aiModeEditModel.setSelected(false);
        }
        currentIotChargeDischarges = event.getCurrentIotChargeDischarges();
        if(currentIotChargeDischarges > 0) {
            mData.get(0).setSelected(true);
            if(currentIotChargeDischarges == iotEmergency) {
                mData.get(0).setSectionType(1);
            } else if (currentIotChargeDischarges == iotSmart) {
                mData.get(0).setSectionType(2);
            } else {
                mData.get(0).setSectionType(3);
            }
        } else if(currentIotChargeDischarges == 0) {
            mData.get(1).setSelected(true);
        } else if(currentIotChargeDischarges == -128) {
            mAdapter.notifyDataSetChanged();
            return;
        }else {
            mData.get(2).setSelected(true);
            if(currentIotChargeDischarges == -iotEmergency) {
                mData.get(2).setSectionType(1);
            } else if (currentIotChargeDischarges == -iotSmart) {
                mData.get(2).setSectionType(2);
            }
        }
        mAdapter.notifyDataSetChanged();
    }

    private void refreshData(int data,boolean isIotEmergency) {
        if (null == mData) return;
        if (isIotEmergency) {
            mData.get(0).setEmergencyReserve(data);
            mData.get(1).setEmergencyReserve(data);
            mData.get(2).setEmergencyReserve(data);
        } else {
            mData.get(0).setSmartReserve(data);
            mData.get(1).setSmartReserve(data);
            mData.get(2).setSmartReserve(data);
        }
        mAdapter.notifyDataSetChanged();
    }
}
