package com.dinsafer.module.powerstation;

public class PSKeyConstant {
    public static final String KEY_TITLE = "key_title";
    public static final String KEY_DEVICE_ID = "key_device_id";
    public static final String KEY_LIST = "KEY_LIST";
    public static final String KEY_COUNTRY = "key_country";
    public static final String KEY_BEAN = "key_bean";
    public static final String KEY_POSITION = "key_position";
    public static final String KEY_UNIT = "key_unit";
    public static final String KEY_WIFI_SSID = "key_wifi_ssid";
    public static final String KEY_WIFI_PASSWORD = "key_wifi_password";

    public static final String CMD = "cmd";
    public static final String ON = "on";
    public static final String ON_0 = "0On";
    public static final String ON_1 = "1On";
    public static final String ON_2 = "2On";
    public static final String INDEX = "index";
    public static final String INDEXS = "indexs";
    public static final String HP5001 = "HP5001"; // 单相
    public static final String HP5000 = "HP5000"; // 三相
    public static final String COUNT = "count"; // count
    public static final String COUNTRY = "country"; // count
    public static final String CITY = "city"; // count
    public static final String LIST = "list"; //
    public static final String RECOMMENDED = "recommended"; //
    public static final String CITYS = "citys"; //
    public static final String COUNTRY_NAME = "country_name"; //
    public static final String COUNTRY_CODE = "country_code"; //
    public static final String COUNTRY_NAME_DISPLAY = "country_name_display"; //
    public static final String RESULT = "result"; //
    public static final String TIMEZONE = "timezone"; //
    public static final String CITY_NAME = "city_name"; //
    public static final String KEY_FROM = "key_from";
    public static final String IGNORE_EXCEPTION = "ignoreException";


    public static final String DC_ON = "dcOn";  // bool - 是否打开了输入到逆变器直流模块
    public static final String DC = "dc";   // int - 输入到逆变器直流模块的功率 单位：w
    public static final String AC_ON = "acOn";  // bool - 是否打开了输入到逆变器交流模块
    public static final String AC = "ac";    // int - 输入到逆变器交流模块的功率 单位：w
    public static final String BATTERY_ON = "batteryOn";   // bool - 是否打开了电池输入到逆变器
    public static final String BATTERY = "battery";  // bool - 是否打开了电池输入到逆变器
    public static final String MPPT_ON = "mpptOn";   // bool - 是否打开了太阳能输入到逆变器
    public static final String MPPT = "mppt";   // int - 太阳能输入到逆变器的功率 单位：w
    public static final String EV_ON = "evOn";   // bool - 是否打开了车充输入到逆变器
    public static final String EV = "ev";   // int - 车充输入到逆变器的功率 单位：w
    public static final String INVERTER_IN_ON = "inverterInOn";    // bool - 是否打开了逆变器通过市电入口输出
    public static final String INVERTER_IN = "inverterIn";    // int - 逆变器通过市电入口输出的功率 单位：w
    public static final String OTHER_OUT_ON = "otherOutOn";    // bool - 是否打开了逆变器输出到其他负载
    public static final String OTHER_OUT = "otherOut";    // int - 逆变器输出到其他负载的功率单位：w
    public static final String STATUS = "status";    // int - 执行结果, 1:成功, 其他:失败
    public static final String CUR_POWER = "curPower";    // int - 当前电量 单位：mah
    public static final String FULL_POWER = "fullPower";    // int - 满电电量 单位：mah
    public static final String SOC = "soc";    // int - 满电电量 单位：mah
    public static final String REMAIN_TIME = "remainTime";    // Double - 可输出电能时间 单位：minute
    public static final String CHARGE_TIME = "chargeTime";    // Double - 充满时间 单位：minute
    public static final String CHARGE = "charge";    // bool - 工作状态, true:输出, false:输入
    public static final String LIGHT_STATE = "lightState";    // lightState
    public static final String EXCEPTIONS = "exceptions";    // exceptions
    public static final String REGION = "region";    // exceptions
    public static final String DELIVERY_AREAS = "delivery_areas";
    public static final String SMART_TARIFF_TRACKING = "smart_tariff_tracking";    // 是否支持的地区

    public static final String STATE = "state";    // int - 电池的状态, 0:正常, 1: BMS关闭, 其他:异常
    public static final String BMS_TEMP = "bmsTemp";    // int - BMS板子温度 单位：k（开尔文）
    public static final String ELECTRODE_A_TEMP = "electrodeATemp";    // int - 电芯A温度 单位：k（开尔文）
    public static final String ELECTRODE_B_TEMP = "electrodeBTemp";    // int - 电芯B温度 单位：k（开尔文）
    public static final String AMPERE = "ampere";    // int - 电流 单位：mA
    public static final String VOLTAGE = "voltage";    // int - 电压 单位：mV
    public static final String RELEASE_TIMES = "releaseTimes";    // int - 放点次数
    public static final String RECYCLE_TIMES = "recycleTimes";    // int - 电池循环次数
    public static final String HEALTHY = "healthy";    // int - 健康程度 单位%
    public static final String ID_INFO = "idInfo";    // String - 型号信息
    public static final String VERSION = "version";    // String - 版本型号
    public static final String BAR_CODE = "barcode";    // barcode
    public static final String CABINET_INDEX = "cabinetIndex";    // int - 所属的机柜index
    public static final String CABINET_POSITION_INDEX = "cabinetPositionIndex";    // int - 表示机柜的第几格 [0,255]
    public static final String HEATING = "heating";    // bool - 电池的状态是否在加热
    public static final String HEAT_AVAILABLE = "heatAvailable";    // bool - 加热膜是否在位
    public static final String IS_ADD = "isAdd";    //  bool - true:添加, false:减少

    public static final String WATER_STATE = "waterState";    //  水感状态, CabinetWaterState说明
    public static final String SMOKE_STATE = "smokeState";    //  烟感状态, CabinetSmokeState说明
    public static final String FAN_STATE = "fanState";    //  风扇状态, CabinetFanState说明
    public static final String START_TIME = "startTime";    //  double - 时间按照double，但是没有小数  单位s
    public static final String END_TIME = "endTime";    //  double - 时间按照double，但是没有小数  单位s
    public static final String STRATEGY_TYPE = "strategyType";    //  int - 策略类型 StrategyType
    public static final String SMART_RESERVE = "smartReserve";    //  int - 电量百分比 [0 - 100]
    public static final String EMERGENCY_RESERVE = "emergencyReserve";    //  int - 电量百分比 [0 - 100]
    public static final String GOOD_PRICE_PERCENTAGE = "goodPricePercentage";    //  int - 低价比例，被乘数暂定每日电价平均值。-1 为 ignore
    public static final String ACCEPTABLE_PRICE_PERCENTAGE = "acceptablePricePercentage";    //  int - 低价比例，被乘数暂定每日电价平均值。-1 为


    public static final String B_SENSOR_INPUT_ON = "BSensorInputOn";    //  bool - 是否打开了三相电表的输入
    public static final String B_SENSOR_INPUT = "BSensorInput";
    public static final String INVERTER = "inverter";
    public static final String CABINET = "cabinet";
    public static final String SYSTEM = "system";
    public static final String COMMUNICATION = "communication";
    public static final String DISCHARGE_SWITCH_ON = "dischargeSwitchOn";
    public static final String CHARGE_SWITCH_ON = "chargeSwitchOn";
    public static final String CAPACITY = "capacity";

    public static final String CITY_SOURCE_ON = "citySourceOn";
    public static final String CITY_SOURCE = "citySource";
    public static final String B_SENSOR_OUTPUT_ON = "BSensorOutputOn";
    public static final String B_SENSOR_OUTPUT = "BSensorOutput";
    public static final String CELLULAR = "cellular";
    public static final String WIFI = "wifi";
    public static final String ETHERNET = "ethernet";
    public static final String WIFI_NAME = "wifiName";
    public static final String IP = "ip";
    public static final String MAC = "mac";
    public static final String TOTAL_VOLT = "totalVolt";
    public static final String MODE = "mode";

    public static final String KEY_TAB_TYPE = "key_tab_type";
    public static final String KEY_GRID_TO_BATTERY = "key_grid_to_battery";

    public static final String KEY_EXPIRATION_DATE = "key_expiration_date";
    public static final String KEY_STATUS = "key_status";
    public static final String KEY_SUB_CATEGORY = "key_sub_category";
    public static final String KEY_CURRENT_ID = "key_current_id";
    public static final String KEY_MODEL = "model";
    public static final String KEY_DEVICE_TYPE = "deviceType";

    public static final String KEY_COUNTRIES = "countries";
    public static final String KEY_FAMILY_BALANCE_CONTRACT_INFO = "FamilyBalanceContractInfo";
    public static final String KEY_ELECTRICITY_SUPPLIER = "electricity_supplier";
    public static final String KEY_EFFECTIVE_TIME= "effective_time";
    public static final String KEY_AUTHORIZATION_COMPANY_NAME = "authorization_company_name";
    public static final String KEY_MCU_ID = "mcu_id";

    public static final String CURRENT_IOT_CHARGE_DISCHARGES = "current_iot_charge_discharges";
    public static final String IOT_SMART = "iot_smart";
    public static final String IOT_EMERGENCY = "iot_emergency";
    public static final String IOT_SCHEDULE_DATA_MAP = "iot_schedule_data_map";
    public static final String HTTP_DATA_MAP = "http_data_map";
    public static final String STATUS_BAR_HEIGHT_PORTRAIT = "status_bar_height_portrait";
    public static final String IS_SUPPORT_PV_AND_LOCATION = "is_support_pv_and_location";
    public static final String PV_PREFERENCE = "pv_preference";
    public static final String C_1 = "c1";
    public static final String C_2 = "c2";
    public static final String C_3 = "c3";
    public static final String S_1 = "s1";
    public static final String S_2 = "s2";
    public static final String KEY_HOME_ID = "home_id";
    public static final String KEY_HOME_NAME = "home_name";
}
