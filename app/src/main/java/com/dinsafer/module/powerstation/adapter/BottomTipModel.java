package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemBottomTipBinding;
import com.dinsafer.ui.rv.BindModel;
import com.dinsafer.util.Local;

public class BottomTipModel extends BindModel<ItemBottomTipBinding> {

    private String mTip;
    public BottomTipModel(Context context, String tip) {
        super(context);
        this.mTip = tip;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_bottom_tip;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemBottomTipBinding binding) {
        binding.tvTip.setLocalText(mTip);
    }
}
