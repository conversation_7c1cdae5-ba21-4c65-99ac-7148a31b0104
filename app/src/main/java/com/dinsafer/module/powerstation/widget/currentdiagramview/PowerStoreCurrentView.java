package com.dinsafer.module.powerstation.widget.currentdiagramview;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;

import com.dinsafer.util.viewanimator.SvgPathParser;

public class PowerStoreCurrentView extends BaseCurrentView{

    private final String TOPO_PATH = "M229 0C229.552 0 230 0.447715 230 1V146C230 146.552 229.552 147 229 147C228.448 147 228 146.552 228 146V67H152.25C150.648 67 149.065 67.0797 147.504 67.2354C138.138 68.1694 129.578 71.838 122.633 77.4323C121.475 78.3648 120.362 79.3509 119.298 80.3866C119.004 80.6996 118.7 81.0035 118.387 81.298C110.102 89.8099 105 101.434 105 114.25V146C105 146.552 104.552 147 104 147C103.448 147 103 146.552 103 146V114.25C103 101.434 97.8976 89.8098 89.6133 81.2979C89.3004 81.0035 88.9965 80.6996 88.7021 80.3866C86.7449 78.4818 84.6233 76.7453 82.3604 75.2003C74.7204 70.0062 65.4324 67.0011 55.5081 67.0011V67L1 67C0.447716 67 0 66.5523 0 66C0 65.4477 0.447716 65 1 65L55.7518 65C68.5672 64.9995 80.1895 59.8983 88.702 51.6135C92.5321 47.5422 97.9692 45 104 45C109.727 45 114.919 47.2928 118.708 51.0107L118.71 51.0081C127.272 59.6504 139.137 65 152.25 65H228V1C228 0.447715 228.448 0 229 0ZM83.5768 73.609C78.2657 69.9655 72.2046 67.3361 65.6724 65.9999C72.6998 64.5623 79.1822 61.6279 84.7715 57.5449C83.6324 60.1318 83 62.992 83 66C83 69.0061 83.6316 71.8647 84.7694 74.4503C84.3763 74.1641 83.9787 73.8836 83.5768 73.609ZM91.0148 79.8703C90.9614 79.8156 90.908 79.761 90.8544 79.7066C90.6037 79.4505 90.3501 79.1972 90.0937 78.9467C86.9334 75.5536 85 71.0026 85 66C85 60.9783 86.9481 56.4117 90.1298 53.0146C90.4052 52.7461 90.6775 52.4744 90.9465 52.1995L90.9436 52.1967C94.3479 48.9754 98.9433 47 104 47C114.493 47 123 55.5066 123 66C123 71.0216 121.052 75.5882 117.87 78.9853C117.675 79.1754 117.482 79.3671 117.29 79.5603C117.188 79.6632 117.086 79.7665 116.985 79.8703C113.588 83.0519 109.022 85 104 85C98.9784 85 94.4118 83.0519 91.0148 79.8703ZM95.5451 85.2286C99.6283 90.818 102.562 97.3001 104 104.327C105.438 97.3001 108.372 90.818 112.455 85.2286C109.868 86.3676 107.008 87 104 87C100.992 87 98.1319 86.3676 95.5451 85.2286ZM123.229 74.4548C128.819 70.3708 135.303 67.4363 142.332 65.9991C135.298 64.5586 128.811 61.6165 123.219 57.5225C124.364 60.1153 125 62.9833 125 66C125 69.0079 124.368 71.8681 123.229 74.4548Z";

    public PowerStoreCurrentView() {
        super();
        mTopoPath = SvgPathParser.tryParsePath(TOPO_PATH);
        if (mTopoPath != null) {
            mTopoPath.setFillType(Path.FillType.EVEN_ODD);
        }
    }

    @Override
    public void drawTB(Canvas canvas, Paint paint) {
        canvas.drawPath(mTopoPath, paint);
    }
}
