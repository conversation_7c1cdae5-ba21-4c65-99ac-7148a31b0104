package com.dinsafer.module.powerstation.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import android.view.View;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/25 15:27
 * @description :
 */
public class SpaceItemDecoration extends RecyclerView.ItemDecoration {

    private Drawable mDivider = null;
    private Context mContext;
    private int mSectionOffsetV = 0;
    private int mSectionOffsetH = 0;
    private boolean mDrawOver = true;
    private int[] attrs = {android.R.attr.listDivider};

    public SpaceItemDecoration(Context context, int sectionOffsetV, int sectionOffsetH){
        this.mContext = context;
        TypedArray a = context.obtainStyledAttributes(attrs);
        this.mSectionOffsetV = sectionOffsetV;
        this.mSectionOffsetH = sectionOffsetH;
        mDivider = a.getDrawable(0);
        a.recycle();
    }

    @Override
    public void onDrawOver(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDrawOver(c, parent, state);
        if (mDivider!=null && mDrawOver){
            draw(c, parent);
        }
    }

    @Override
    public void onDraw(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDraw(c, parent, state);
        if (mDivider!=null && mDrawOver){
            draw(c, parent);
        }
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        if (getOrientation(parent.getLayoutManager()) == RecyclerView.VERTICAL) {
            outRect.set(mSectionOffsetH, 0, mSectionOffsetH, mSectionOffsetV);
        } else {
            outRect.set(0, 0, mSectionOffsetV, 0);
        }
    }

    private void draw(Canvas c, RecyclerView parent){
        int left = parent.getPaddingLeft();
        int right = parent.getPaddingRight();
        int childCount = parent.getChildCount();
        for (int i=0; i<childCount; i++){
            View child = parent.getChildAt(i);
            RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) child.getLayoutParams();
            int top = child.getBottom()+params.bottomMargin+Math.round(child.getTop());
            int bottom = top + mDivider.getIntrinsicHeight()<=0 ? 1 : mDivider.getIntrinsicHeight();
            mDivider.setBounds(left, top, right, bottom);
            mDivider.draw(c);
        }
    }

    private int getOrientation(RecyclerView.LayoutManager layoutManager){
        if (layoutManager instanceof LinearLayoutManager){
            return ((LinearLayoutManager) layoutManager).getOrientation();
        }else if (layoutManager instanceof StaggeredGridLayoutManager){
            return ((StaggeredGridLayoutManager) layoutManager).getOrientation();
        }
        return OrientationHelper.HORIZONTAL;
    }
}
