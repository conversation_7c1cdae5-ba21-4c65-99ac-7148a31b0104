package com.dinsafer.module.powerstation.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.Local;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 16:24
 * @description :
 */
public class PSNetworkSettingsWifiAdapter extends BaseQuickAdapter<PSWifiInfoBean, BaseViewHolder> {

    public PSNetworkSettingsWifiAdapter() {
        super(R.layout.item_ps_network_setting_wifi);
    }

    @Override
    protected void convert(BaseViewHolder helper, PSWifiInfoBean item) {
        View viewLine = helper.getView(R.id.view_line);
        viewLine.setVisibility(helper.getAdapterPosition() == getData().size() - 1 ? View.GONE : View.VISIBLE);
        LocalTextView tvName = helper.getView(R.id.tv_name);
        tvName.setLocalText(item.getSsid());
        ImageView ivRssi = helper.getView(R.id.iv_rssi);
        ImageView ivLock = helper.getView(R.id.iv_lock);
        ImageView ivArrow = helper.getView(R.id.iv_arrow);

        final int iconResId = BmtUtil.getBleWifiRssiIconResId(item.getRssi());
        if (0 == iconResId) {
            ivRssi.setVisibility(View.GONE);
        } else {
            ivRssi.setVisibility(View.VISIBLE);
            ivRssi.setImageResource(iconResId);
        }

        ivLock.setImageResource(R.drawable.icon_lock);
        ivLock.setVisibility(item.isAuth() ? View.VISIBLE : View.GONE);

        if (Local.s(mContext.getResources().getString(R.string.wifi_other)).equals(item.getSsid())
                || Local.s(mContext.getResources().getString(R.string.ethernet)).equals(item.getSsid())) {
            ivArrow.setImageResource(R.drawable.btn_device_setting_arrow);
            ivArrow.setVisibility(View.VISIBLE);
        } else {
            ivArrow.setVisibility(View.GONE);
        }
    }
}
