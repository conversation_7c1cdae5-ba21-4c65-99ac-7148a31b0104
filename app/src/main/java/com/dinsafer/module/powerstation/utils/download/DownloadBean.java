package com.dinsafer.module.powerstation.utils.download;

import java.io.File;

public class DownloadBean {

    private String name;
    private String url;
    private String suffix;
    private File file;

    public DownloadBean(String name, String url, String suffix) {
        this.name = name;
        this.url = url;
        this.suffix = suffix;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }
}
