package com.dinsafer.module.powerstation.electricity.chart;


import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.util.Log;

import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ChartNoteTextAttrBean;
import com.dinsafer.module.powerstation.electricity.chart.axis.CustomYAxis;
import com.dinsafer.module.powerstation.electricity.chart.highlighter.CustomCombinedChartHighlighter;
import com.dinsafer.module.powerstation.electricity.chart.listener.CustomCombinedChartTouchListener;
import com.dinsafer.module.powerstation.electricity.chart.listener.OnScrollChartListener;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomCombinedChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomXAxisRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomYAxisRenderer;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.ScreenUtils;
import com.github.mikephil.charting.charts.BarLineChartBase;
import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarLineScatterCandleBubbleData;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarLineScatterCandleBubbleDataSet;
import com.github.mikephil.charting.utils.SelectionDetail;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义CombinedChart
 */
public class CustomCombinedChart<T extends BarLineScatterCandleBubbleData<? extends IBarLineScatterCandleBubbleDataSet<? extends Entry>>> extends CombinedChart {

    private String mNoDataTextDescription;
    /**
     * text that is displayed when the chart is empty
     */
    private String mNoDataText = "No chart data available.";

    /**
     * flag that indicates if offsets calculation has already been done or not
     */
    private boolean mOffsetsCalculated = false;

    private boolean mAutoScaleMinMaxEnabled = false;
    private Integer mAutoScaleLastLowestVisibleXIndex = null;
    private Integer mAutoScaleLastHighestVisibleXIndex = null;
    // for performance tracking
    private long totalTime = 0;
    private long drawCycles = 0;
    private List<Highlight> highlights;
    private boolean showNoteText;
    private boolean showAboveChartText;
    private List<ChartNoteTextAttrBean> noteTexts;
    private List<ChartNoteTextAttrBean> aboveChartTexts;

    private Paint mNoteTextPaint;
    private Rect mNoteTextRect;
    private Path defaultXGridLinesPath = new Path();
    private boolean isDrawDefaultXGridLines;
    private OnScrollChartListener scrollChartListener;

    private boolean isNeedNoDataGrid;
    private float noDataOffset;
    private float noDataLeftOffset;
    private float noDataTopOffset;
    private float noDataRightOffset;
    private float noDataBottomOffset;

    private boolean isDrawLefAxisYDesc;
    private boolean isDrawRightAxisYDesc;
    private String mLefAxisYDesc;
    private String mRightAxisYDesc;

    private Typeface mPoppinsTypeface;
    private Typeface mPalanquinTypeface;

    public CustomCombinedChart(Context context) {
        this(context, null);
    }

    public CustomCombinedChart(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomCombinedChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mPoppinsTypeface = ResourcesCompat.getFont(getContext(), R.font.poppins);
        mPalanquinTypeface = ResourcesCompat.getFont(getContext(), R.font.palanquin);
        mXAxis.setTypeface(mPalanquinTypeface);
        mAxisLeft = new CustomYAxis(YAxis.AxisDependency.LEFT);
        mAxisRight = new CustomYAxis(YAxis.AxisDependency.RIGHT);
        mAxisRendererLeft = new CustomYAxisRenderer(context, mViewPortHandler, mAxisLeft, mLeftAxisTransformer);
        mAxisRendererRight = new CustomYAxisRenderer(context, mViewPortHandler, mAxisRight, mRightAxisTransformer);
        mNoteTextPaint = new Paint();
        mNoteTextPaint.setAntiAlias(true);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomCombinedChart);
        noDataOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataOffset, 0);
        noDataLeftOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataLeftOffset, 0);
        noDataTopOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataTopOffset, 0);
        noDataRightOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataRightOffset, 0);
        noDataBottomOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataBottomOffset, 0);
        if (noDataLeftOffset == 0.0f) {
            noDataLeftOffset = noDataOffset;
        }

        if (noDataTopOffset == 0.0f) {
            noDataTopOffset = noDataOffset;
        }

        if (noDataRightOffset == 0.0f) {
            noDataRightOffset = noDataOffset;
        }

        if (noDataBottomOffset == 0.0f) {
            noDataBottomOffset = noDataOffset;
        }
        typedArray.recycle();
    }

    @Override
    protected void init() {
        super.init();
        setHighlighter(new CustomCombinedChartHighlighter(this));
        mChartTouchListener = new CustomCombinedChartTouchListener(this, mViewPortHandler.getMatrixTouch(), scrollChartListener);
        mXAxisRenderer = new CustomXAxisRenderer(mViewPortHandler, mXAxis, mLeftAxisTransformer);
    }

    @Override
    public void setData(CombinedData data) {
        super.setData(data);
        mRenderer = new CustomCombinedChartRenderer(this, mAnimator, mViewPortHandler);
        mRenderer.initBuffers();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (mData == null) {
            drawNoData(canvas);
            return;
        }

        if (!mOffsetsCalculated) {

            calculateOffsets();
            mOffsetsCalculated = true;
        }

        if (mData == null)
            return;

        long starttime = System.currentTimeMillis();
        calcModulus();

        mXAxisRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);
        mRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);
        // execute all drawing commands
        drawGridBackground(canvas);

        if (mAxisLeft.isEnabled())
            mAxisRendererLeft.computeAxis(mAxisLeft.mAxisMinimum, mAxisLeft.mAxisMaximum);
        if (mAxisRight.isEnabled())
            mAxisRendererRight.computeAxis(mAxisRight.mAxisMinimum, mAxisRight.mAxisMaximum);

        mXAxisRenderer.renderAxisLine(canvas);
        mAxisRendererLeft.renderAxisLine(canvas);
        mAxisRendererRight.renderAxisLine(canvas);

        if (mAutoScaleMinMaxEnabled) {
            final int lowestVisibleXIndex = getLowestVisibleXIndex();
            final int highestVisibleXIndex = getHighestVisibleXIndex();

            if (mAutoScaleLastLowestVisibleXIndex == null ||
                    mAutoScaleLastLowestVisibleXIndex != lowestVisibleXIndex ||
                    mAutoScaleLastHighestVisibleXIndex == null ||
                    mAutoScaleLastHighestVisibleXIndex != highestVisibleXIndex) {

                calcMinMax();
                calculateOffsets();

                mAutoScaleLastLowestVisibleXIndex = lowestVisibleXIndex;
                mAutoScaleLastHighestVisibleXIndex = highestVisibleXIndex;
            }
        }
        // make sure the graph values and grid cannot be drawn outside the
        // content-rect
        int clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        mXAxisRenderer.renderGridLines(canvas);
        mAxisRendererLeft.renderGridLines(canvas);
        mAxisRendererRight.renderGridLines(canvas);

        if (mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        if (showNoteText) {
            for (ChartNoteTextAttrBean chartNoteTextAttrBean : noteTexts) {
                drawNoteText(canvas, chartNoteTextAttrBean);
            }
        }


        mRenderer.drawData(canvas);

        // Removes clipping rectangle
        canvas.restoreToCount(clipRestoreCount);

        if (showAboveChartText) {
            if (CollectionUtil.isListNotEmpty(aboveChartTexts)) {
                for (ChartNoteTextAttrBean chartNoteTextAttrBean : aboveChartTexts) {
                    drawAboveChartText(canvas, chartNoteTextAttrBean);
                }
            }
        }

        // if highlighting is enabled
        if (valuesToHighlight())
            mRenderer.drawHighlighted(canvas, mIndicesToHighlight);

        mRenderer.drawExtras(canvas);

        clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        if (!mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (!mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (!mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        canvas.restoreToCount(clipRestoreCount);

        mXAxisRenderer.renderAxisLabels(canvas);
        mAxisRendererLeft.renderAxisLabels(canvas);
        mAxisRendererRight.renderAxisLabels(canvas);
        if (isDrawLefAxisYDesc) {
            drawYTopDesc(canvas, YAxis.AxisDependency.LEFT);
        }
        if (isDrawRightAxisYDesc) {
            drawYTopDesc(canvas, YAxis.AxisDependency.RIGHT);
        }

        mRenderer.drawValues(canvas);

        mLegendRenderer.renderLegend(canvas);

        drawMarkers(canvas);

        drawDescription(canvas);

        if (mLogEnabled) {
            long drawtime = (System.currentTimeMillis() - starttime);
            totalTime += drawtime;
            drawCycles += 1;
            long average = totalTime / drawCycles;
            Log.i(LOG_TAG, "Drawtime: " + drawtime + " ms, average: " + average + " ms, cycles: "
                    + drawCycles);
        }
    }

    private void drawNoData(Canvas canvas) {
        RectF noDataRectF = new RectF(noDataLeftOffset, noDataTopOffset,
                getWidth() - noDataRightOffset, getHeight() - noDataBottomOffset);
        canvas.drawRect(noDataRectF, mBorderPaint);
        if (isNeedNoDataGrid) {
            float average = (noDataRectF.right - noDataRectF.left) / 4;
            for (int i = 1; i < 4; i++) {
                float x = noDataRectF.left + average * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(x, noDataRectF.top);
                defaultXGridLinesPath.lineTo(x, noDataRectF.bottom);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }

            float verticalAverage = (noDataRectF.bottom - noDataRectF.top) / 5;

            for (int i = 1; i < 5; i++) {
                float y = noDataRectF.top + verticalAverage * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(noDataRectF.left, y);
                defaultXGridLinesPath.lineTo(noDataRectF.right, y);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }
        }
    }

    @Override
    protected void drawGridBackground(Canvas c) {
        super.drawGridBackground(c);
        float average = (mViewPortHandler.contentRight() - mViewPortHandler.contentLeft()) / 4;
        if (isDrawDefaultXGridLines) {
            for (int i = 1; i < 4; i++) {
                float x = mViewPortHandler.contentLeft() + average * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(x, mViewPortHandler.contentTop());
                defaultXGridLinesPath.lineTo(x, mViewPortHandler.contentBottom());
                c.drawPath(defaultXGridLinesPath, mBorderPaint);
            }
        }
    }

    private void drawNoteText(Canvas canvas, ChartNoteTextAttrBean noteTextAttrBean) {
        if (mNoteTextRect == null) {
            mNoteTextRect = new Rect();
        }
        mNoteTextPaint.setColor(noteTextAttrBean.getNoteTextColor());
        mNoteTextPaint.setTextSize(noteTextAttrBean.getNoteTextSize());
        mNoteTextPaint.setTypeface(mPoppinsTypeface);
        mNoteTextPaint.setFakeBoldText(true);
        String text = noteTextAttrBean.getNoteText();
        mNoteTextPaint.getTextBounds(text, 0, text.length(), mNoteTextRect);

        float left = mViewPortHandler.contentLeft();
        float top = mViewPortHandler.contentTop();
        float right = mViewPortHandler.contentRight();
        float bottom = mViewPortHandler.contentBottom();
        float x = left;
        float y = top;
        switch (noteTextAttrBean.getGravity()) {
            case RIGHT_TOP:
                x = right - noteTextAttrBean.getPaddingRight();
                y = top + mNoteTextRect.height() + noteTextAttrBean.getPaddingTop();
                break;

            case LEFT_BOTTOM:
                x = left + noteTextAttrBean.getPaddingLeft();
                y = bottom - mNoteTextRect.height() / 2 - noteTextAttrBean.getPaddingBottom();
                break;

            case RIGHT_BOTTOM:
                x = right - noteTextAttrBean.getPaddingRight();
                y = bottom - mNoteTextRect.height() / 2 - noteTextAttrBean.getPaddingBottom();
                break;

            case CENTER:
                x = (left + right) / 2;
                y = (top + bottom) / 2;
                break;

            case TOP_CENTER_HORIZONTAL:
                x = (left + right) / 2;
                y = top + mNoteTextRect.height() + noteTextAttrBean.getPaddingTop();
                break;

            case BOTTOM_CENTER_HORIZONTAL:
                x = (left + right) / 2;
                y = bottom - mNoteTextRect.height() / 2 - noteTextAttrBean.getPaddingBottom();
                break;

            case LEFT_CENTER_VERTICAL:
                x = left + noteTextAttrBean.getPaddingLeft();
                y = (top + bottom) / 2;
                break;

            case RIGHT_CENTER_VERTICAL:
                x = right - noteTextAttrBean.getPaddingRight();
                y = (top + bottom) / 2;
                break;

            default:
                x = left + noteTextAttrBean.getPaddingLeft();
                y = top + mNoteTextRect.height() + noteTextAttrBean.getPaddingTop();
        }
        canvas.drawText(text, x, y, mNoteTextPaint);
    }

    private void drawAboveChartText(Canvas canvas, ChartNoteTextAttrBean noteTextAttrBean) {
        if (mNoteTextRect == null) {
            mNoteTextRect = new Rect();
        }
        mNoteTextPaint.setColor(noteTextAttrBean.getNoteTextColor());
        mNoteTextPaint.setTextSize(noteTextAttrBean.getNoteTextSize());
        mNoteTextPaint.setTypeface(mPalanquinTypeface);
        mNoteTextPaint.setFakeBoldText(false);
        String text = noteTextAttrBean.getNoteText();
        String[] textArr = text.split("\n");
        float left = mViewPortHandler.contentLeft();
        float top = mViewPortHandler.contentTop();
        float right = mViewPortHandler.contentRight();
        float bottom = mViewPortHandler.contentBottom();
        float x = left;
        for (int i=textArr.length - 1; i>=0; i--) {
            String str = textArr[i];
            mNoteTextPaint.getTextBounds(str, 0, str.length(), mNoteTextRect);
            float y = i==textArr.length - 1 ? top - mNoteTextRect.height() + 8 : top - mNoteTextRect.height() - 2;
            switch (noteTextAttrBean.getGravity()) {
                case CENTER:
                    x = (left + right) / 2;
                    break;

                case RIGHT_TOP:
                case RIGHT_CENTER_VERTICAL:
                case RIGHT_BOTTOM:
                    x = right - mNoteTextRect.width() - noteTextAttrBean.getPaddingRight();
                    break;

                default:
                    x = left + noteTextAttrBean.getPaddingLeft();
                    break;
            }
            canvas.drawText(str, x, y, mNoteTextPaint);
            top = y;
        }
    }

    @Override
    protected void drawMarkers(Canvas canvas) {
        // if there is no marker view or drawing marker is disabled
        if (mMarkerView == null || !mDrawMarkerViews || !valuesToHighlight())
            return;
        if (CollectionUtil.isListEmpty(highlights)) return;
        List<Entry> entries = new ArrayList<>();
        float x = 0f;
        for (Highlight highlight : highlights) {
            int xIndex = highlight.getXIndex();
            int dataSetIndex = highlight.getDataSetIndex();
            float deltaX = mXAxis != null
                    ? mXAxis.mAxisRange
                    : ((mData == null ? 0.f : mData.getXValCount()) - 1.f);
            if (xIndex <= deltaX && xIndex <= deltaX * mAnimator.getPhaseX()) {

                Entry e = mData.getEntryForHighlight(highlight);

                // make sure entry not null
                if (e == null || e.getXIndex() != highlight.getXIndex())
                    continue;

                float[] pos = getMarkerPosition(e, highlight);

                // check bounds
                if (!mViewPortHandler.isInBoundsX(pos[0]))
                    continue;
                entries.add(e);
                x = pos[0];
            }
        }
        if (CollectionUtil.isListEmpty(entries)) return;
        ((CustomCombinedMarkerView) mMarkerView).setMarkerValueShow(entries, highlights);
        mMarkerView.measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
        mMarkerView.layout(0, 0, mMarkerView.getMeasuredWidth(),
                mMarkerView.getMeasuredHeight());
        float half = mMarkerView.getWidth() / 2;
        float screenWidth = ScreenUtils.getScreenWidth(getContext());
        int limitLeft = ((CustomCombinedMarkerView) mMarkerView).getLimitLeft();
        int limitRight = ((CustomCombinedMarkerView) mMarkerView).getLimitRight();
        if (x + half > screenWidth - limitRight) {
            x = screenWidth - half - limitRight;
        }

        if (x - half < limitLeft) {
            x = half + limitLeft;
        }
        mMarkerView.draw(canvas, x, mViewPortHandler.contentTop() - 10);
    }

    @Override
    public void highlightValue(Highlight high, boolean callListener) {
        Entry e = null;

        if (high == null)
            mIndicesToHighlight = null;
        else {

            if (mLogEnabled)
                Log.i(LOG_TAG, "Highlighted: " + high.toString());

            e = mData.getEntryForHighlight(high);
            if (e == null) {
                mIndicesToHighlight = null;
                high = null;
            } else {
                if (this instanceof BarLineChartBase
                        && ((BarLineChartBase) this).isHighlightFullBarEnabled())
                    high = new Highlight(high.getXIndex(), Float.NaN, -1, -1, -1);

                // set the indices to highlight
                mIndicesToHighlight = new Highlight[]{
                        high
                };
            }
        }

        if (callListener && mSelectionListener != null) {

            if (!valuesToHighlight())
                mSelectionListener.onNothingSelected();
            else {
                // notify the listener
                mSelectionListener.onValueSelected(e, high.getDataSetIndex(), high);
            }
        }
        // redraw the chart
        invalidate();
    }

    /**
     * 画Y轴标签顶部描述
     * @param canvas
     * @param dependency
     */
    public void drawYTopDesc(Canvas canvas, YAxis.AxisDependency dependency) {
        if (dependency == YAxis.AxisDependency.LEFT) {
           if (mAxisRendererLeft instanceof CustomYAxisRenderer) {
               ((CustomYAxisRenderer)mAxisRendererLeft).drawAxisYTopDesc(canvas, mLefAxisYDesc);
           }
        } else {
            if (mAxisRendererRight instanceof CustomYAxisRenderer) {
                ((CustomYAxisRenderer)mAxisRendererRight).drawAxisYTopDesc(canvas, mRightAxisYDesc);
            }
        }
    }

    public void highlightsValue(List<Highlight> highlights) {
        if (CollectionUtil.isListNotEmpty(highlights)) {
            this.highlights = highlights;
        }
    }

    public void setDrawDefaultXGridLines(boolean isDrawXGridLines) {
        this.isDrawDefaultXGridLines = isDrawXGridLines;
    }

    /**
     * Returns the Highlight object (contains x-index and DataSet index) of the
     * selected value at the given touch point inside the Line-, Scatter-, or
     * CandleStick-Chart.
     *
     * @param x
     * @param y
     * @return
     */
    public List<Highlight> getHighlightsByTouchPoint(float x, float y) {

        if (mData == null) {
            Log.e(LOG_TAG, "Can't select by touch. No data set.");
            return null;
        } else
            return ((CustomCombinedChartHighlighter) getHighlighter()).getHighlights(x, y);
    }


    public void setDefaultHighLight(int xIndex) {
        List<Highlight> highlights = new ArrayList<>();
        List<SelectionDetail> valsAtIndex = ((CustomCombinedChartHighlighter) getHighlighter()).getSelectionDetailsAtIndex(xIndex, -1);
        if (valsAtIndex.size() > 0) {
            for (SelectionDetail selectionDetail : valsAtIndex) {
                if (selectionDetail == null) continue;
                Highlight highlight = new Highlight(xIndex,
                        selectionDetail.value,
                        selectionDetail.dataIndex,
                        selectionDetail.dataSetIndex);
                highlights.add(highlight);
            }
            highlightsValue(highlights);
            highlightValues(highlights.toArray(new Highlight[highlights.size()]));
        }
    }

    public boolean isShowNoteText() {
        return showNoteText;
    }

    public void setShowNoteText(boolean showNoteText) {
        this.showNoteText = showNoteText;
    }

    public boolean isShowAboveChartText() {
        return showAboveChartText;
    }

    public void setShowAboveChartText(boolean showAboveChartText) {
        this.showAboveChartText = showAboveChartText;
    }

    public List<ChartNoteTextAttrBean> getNoteTexts() {
        return noteTexts;
    }

    public void setNoteTexts(List<ChartNoteTextAttrBean> noteTexts) {
        this.noteTexts = noteTexts;
    }

    public void setAboveChartTexts(List<ChartNoteTextAttrBean> aboveChartTexts) {
        this.aboveChartTexts = aboveChartTexts;
    }

    public void setScrollChartListener(OnScrollChartListener scrollChartListener) {
        this.scrollChartListener = scrollChartListener;
        if (mChartTouchListener != null) {
            ((CustomCombinedChartTouchListener) mChartTouchListener).setScrollChartListener(scrollChartListener);
        }
    }

    public boolean isNeedNoDataGrid() {
        return isNeedNoDataGrid;
    }

    public void setNeedNoDataGrid(boolean needNoDataGrid) {
        isNeedNoDataGrid = needNoDataGrid;
    }

    public float getNoDataOffset() {
        return noDataOffset;
    }

    public void setNoDataOffset(float noDataOffset) {
        this.noDataOffset = noDataOffset;
    }

    public float getNoDataLeftOffset() {
        return noDataLeftOffset;
    }

    public void setNoDataLeftOffset(float noDataLeftOffset) {
        this.noDataLeftOffset = noDataLeftOffset;
    }

    public float getNoDataTopOffset() {
        return noDataTopOffset;
    }

    public void setNoDataTopOffset(float noDataTopOffset) {
        this.noDataTopOffset = noDataTopOffset;
    }

    public float getNoDataRightOffset() {
        return noDataRightOffset;
    }

    public void setNoDataRightOffset(float noDataRightOffset) {
        this.noDataRightOffset = noDataRightOffset;
    }

    public float getNoDataBottomOffset() {
        return noDataBottomOffset;
    }

    public void setNoDataBottomOffset(float noDataBottomOffset) {
        this.noDataBottomOffset = noDataBottomOffset;
    }

    public void setDrawLefAxisYDesc(boolean drawLefAxisYDesc) {
        isDrawLefAxisYDesc = drawLefAxisYDesc;
    }

    public void setDrawRightAxisYDesc(boolean drawRightAxisYDesc) {
        isDrawRightAxisYDesc = drawRightAxisYDesc;
    }

    public void setLefAxisYDesc(String lefAxisYDesc) {
        this.mLefAxisYDesc = lefAxisYDesc;
    }

    public void setRightAxisYDesc(String rightAxisYDesc) {
        this.mRightAxisYDesc = rightAxisYDesc;
    }

    public void setReverse(boolean isReverse) {
        if (mChartTouchListener != null) {
            ((CustomCombinedChartTouchListener) mChartTouchListener).setReverse(isReverse);
        }
    }
}
