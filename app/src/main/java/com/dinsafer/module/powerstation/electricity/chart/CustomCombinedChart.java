package com.dinsafer.module.powerstation.electricity.chart;


import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.util.Log;

import androidx.core.content.res.ResourcesCompat;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.bean.ChartNoteTextAttrBean;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.chart.axis.CustomYAxis;
import com.dinsafer.module.powerstation.electricity.chart.highlighter.CustomCombinedChartHighlighter;
import com.dinsafer.module.powerstation.electricity.chart.listener.CustomCombinedChartTouchListener;
import com.dinsafer.module.powerstation.electricity.chart.listener.OnScrollChartListener;
import com.dinsafer.module.powerstation.electricity.chart.marker.CustomCombinedMarkerView;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomCombinedChartRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomXAxisRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.CustomYAxisRenderer;
import com.dinsafer.module.powerstation.electricity.chart.render.GridImportedLineChartRender;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.bean.ThresholdBarDataSet;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.ScreenUtils;
import com.github.mikephil.charting.charts.BarLineChartBase;
import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarLineScatterCandleBubbleData;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IBarLineScatterCandleBubbleDataSet;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.utils.SelectionDetail;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义CombinedChart
 */
public class CustomCombinedChart<T extends BarLineScatterCandleBubbleData<? extends IBarLineScatterCandleBubbleDataSet<? extends Entry>>> extends CombinedChart {

    private String mNoDataTextDescription;
    /**
     * text that is displayed when the chart is empty
     */
    private String mNoDataText = "No chart data available.";

    /**
     * flag that indicates if offsets calculation has already been done or not
     */
    private boolean mOffsetsCalculated = false;

    private boolean mAutoScaleMinMaxEnabled = false;
    private Integer mAutoScaleLastLowestVisibleXIndex = null;
    private Integer mAutoScaleLastHighestVisibleXIndex = null;

    // Grid-imported X轴标注相关
    private GridImportedLineChartRender mGridRenderer;
    private boolean mDrawXAxisAnnotation = false;
    private boolean mDrawThresholdRegion = false;

    // 阈值数据集（直接传递，避免复杂的查找逻辑）
    private ILineDataSet mThresholdDataSet;
    // for performance tracking
    private long totalTime = 0;
    private long drawCycles = 0;
    private List<Highlight> highlights;
    private boolean showNoteText;
    private boolean showAboveChartText;
    private List<ChartNoteTextAttrBean> noteTexts;
    private List<ChartNoteTextAttrBean> aboveChartTexts;

    private Paint mNoteTextPaint;
    private Rect mNoteTextRect;
    private Path defaultXGridLinesPath = new Path();
    private boolean isDrawDefaultXGridLines;
    private OnScrollChartListener scrollChartListener;

    private boolean isNeedNoDataGrid;
    private float noDataOffset;
    private float noDataLeftOffset;
    private float noDataTopOffset;
    private float noDataRightOffset;
    private float noDataBottomOffset;

    private boolean isDrawLefAxisYDesc;
    private boolean isDrawRightAxisYDesc;
    private String mLefAxisYDesc;
    private String mRightAxisYDesc;

    private Typeface mPoppinsTypeface;
    private Typeface mPalanquinTypeface;

    public CustomCombinedChart(Context context) {
        this(context, null);
    }

    public CustomCombinedChart(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomCombinedChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mPoppinsTypeface = ResourcesCompat.getFont(getContext(), R.font.poppins);
        mPalanquinTypeface = ResourcesCompat.getFont(getContext(), R.font.palanquin);
        mXAxis.setTypeface(mPalanquinTypeface);

        // 初始化Grid-imported渲染器
        initGridRenderer();
        mAxisLeft = new CustomYAxis(YAxis.AxisDependency.LEFT);
        mAxisRight = new CustomYAxis(YAxis.AxisDependency.RIGHT);
        mAxisRendererLeft = new CustomYAxisRenderer(context, mViewPortHandler, mAxisLeft, mLeftAxisTransformer);
        mAxisRendererRight = new CustomYAxisRenderer(context, mViewPortHandler, mAxisRight, mRightAxisTransformer);
        mNoteTextPaint = new Paint();
        mNoteTextPaint.setAntiAlias(true);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CustomCombinedChart);
        noDataOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataOffset, 0);
        noDataLeftOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataLeftOffset, 0);
        noDataTopOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataTopOffset, 0);
        noDataRightOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataRightOffset, 0);
        noDataBottomOffset = typedArray.getDimension(R.styleable.CustomCombinedChart_noDataBottomOffset, 0);
        if (noDataLeftOffset == 0.0f) {
            noDataLeftOffset = noDataOffset;
        }

        if (noDataTopOffset == 0.0f) {
            noDataTopOffset = noDataOffset;
        }

        if (noDataRightOffset == 0.0f) {
            noDataRightOffset = noDataOffset;
        }

        if (noDataBottomOffset == 0.0f) {
            noDataBottomOffset = noDataOffset;
        }
        typedArray.recycle();
    }

    @Override
    protected void init() {
        super.init();
        setHighlighter(new CustomCombinedChartHighlighter(this));
        mChartTouchListener = new CustomCombinedChartTouchListener(this, mViewPortHandler.getMatrixTouch(), scrollChartListener);
        mXAxisRenderer = new CustomXAxisRenderer(mViewPortHandler, mXAxis, mLeftAxisTransformer);
    }

    @Override
    public void setData(CombinedData data) {
        super.setData(data);
        mRenderer = new CustomCombinedChartRenderer(this, mAnimator, mViewPortHandler);
        mRenderer.initBuffers();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (mData == null) {
            drawNoData(canvas);
            return;
        }

        if (!mOffsetsCalculated) {

            calculateOffsets();
            mOffsetsCalculated = true;
        }

        if (mData == null)
            return;

        long starttime = System.currentTimeMillis();
        calcModulus();

        mXAxisRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);
        mRenderer.calcXBounds(this, mXAxis.mAxisLabelModulus);
        // execute all drawing commands
        drawGridBackground(canvas);

        if (mAxisLeft.isEnabled())
            mAxisRendererLeft.computeAxis(mAxisLeft.mAxisMinimum, mAxisLeft.mAxisMaximum);
        if (mAxisRight.isEnabled())
            mAxisRendererRight.computeAxis(mAxisRight.mAxisMinimum, mAxisRight.mAxisMaximum);

        mXAxisRenderer.renderAxisLine(canvas);
        mAxisRendererLeft.renderAxisLine(canvas);
        mAxisRendererRight.renderAxisLine(canvas);

        if (mAutoScaleMinMaxEnabled) {
            final int lowestVisibleXIndex = getLowestVisibleXIndex();
            final int highestVisibleXIndex = getHighestVisibleXIndex();

            if (mAutoScaleLastLowestVisibleXIndex == null ||
                    mAutoScaleLastLowestVisibleXIndex != lowestVisibleXIndex ||
                    mAutoScaleLastHighestVisibleXIndex == null ||
                    mAutoScaleLastHighestVisibleXIndex != highestVisibleXIndex) {

                calcMinMax();
                calculateOffsets();

                mAutoScaleLastLowestVisibleXIndex = lowestVisibleXIndex;
                mAutoScaleLastHighestVisibleXIndex = highestVisibleXIndex;
            }
        }
        // make sure the graph values and grid cannot be drawn outside the
        // content-rect
        int clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        mXAxisRenderer.renderGridLines(canvas);
        mAxisRendererLeft.renderGridLines(canvas);
        mAxisRendererRight.renderGridLines(canvas);

        if (mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        if (showNoteText) {
            for (ChartNoteTextAttrBean chartNoteTextAttrBean : noteTexts) {
                drawNoteText(canvas, chartNoteTextAttrBean);
            }
        }


        mRenderer.drawData(canvas);

        // Removes clipping rectangle
        canvas.restoreToCount(clipRestoreCount);

        if (showAboveChartText) {
            if (CollectionUtil.isListNotEmpty(aboveChartTexts)) {
                for (ChartNoteTextAttrBean chartNoteTextAttrBean : aboveChartTexts) {
                    drawAboveChartText(canvas, chartNoteTextAttrBean);
                }
            }
        }

        // if highlighting is enabled
        if (valuesToHighlight())
            mRenderer.drawHighlighted(canvas, mIndicesToHighlight);

        mRenderer.drawExtras(canvas);

        clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        if (!mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (!mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (!mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        canvas.restoreToCount(clipRestoreCount);

        mXAxisRenderer.renderAxisLabels(canvas);
        mAxisRendererLeft.renderAxisLabels(canvas);
        mAxisRendererRight.renderAxisLabels(canvas);
        if (isDrawLefAxisYDesc) {
            drawYTopDesc(canvas, YAxis.AxisDependency.LEFT);
        }
        if (isDrawRightAxisYDesc) {
            drawYTopDesc(canvas, YAxis.AxisDependency.RIGHT);
        }

        mRenderer.drawValues(canvas);

        mLegendRenderer.renderLegend(canvas);

        drawMarkers(canvas);

        drawDescription(canvas);

        // 绘制Grid-imported X轴标注（在所有图表元素之后）
        drawGridImportedXAxisAnnotation(canvas);

        if (mLogEnabled) {
            long drawtime = (System.currentTimeMillis() - starttime);
            totalTime += drawtime;
            drawCycles += 1;
            long average = totalTime / drawCycles;
            Log.i(LOG_TAG, "Drawtime: " + drawtime + " ms, average: " + average + " ms, cycles: "
                    + drawCycles);
        }
    }

    private void drawNoData(Canvas canvas) {
        RectF noDataRectF = new RectF(noDataLeftOffset, noDataTopOffset,
                getWidth() - noDataRightOffset, getHeight() - noDataBottomOffset);
        canvas.drawRect(noDataRectF, mBorderPaint);
        if (isNeedNoDataGrid) {
            float average = (noDataRectF.right - noDataRectF.left) / 4;
            for (int i = 1; i < 4; i++) {
                float x = noDataRectF.left + average * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(x, noDataRectF.top);
                defaultXGridLinesPath.lineTo(x, noDataRectF.bottom);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }

            float verticalAverage = (noDataRectF.bottom - noDataRectF.top) / 5;

            for (int i = 1; i < 5; i++) {
                float y = noDataRectF.top + verticalAverage * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(noDataRectF.left, y);
                defaultXGridLinesPath.lineTo(noDataRectF.right, y);
                canvas.drawPath(defaultXGridLinesPath, mBorderPaint);
            }
        }
    }

    @Override
    protected void drawGridBackground(Canvas c) {
        super.drawGridBackground(c);
        float average = (mViewPortHandler.contentRight() - mViewPortHandler.contentLeft()) / 4;
        if (isDrawDefaultXGridLines) {
            for (int i = 1; i < 4; i++) {
                float x = mViewPortHandler.contentLeft() + average * i;
                defaultXGridLinesPath.reset();
                defaultXGridLinesPath.moveTo(x, mViewPortHandler.contentTop());
                defaultXGridLinesPath.lineTo(x, mViewPortHandler.contentBottom());
                c.drawPath(defaultXGridLinesPath, mBorderPaint);
            }
        }
    }

    private void drawNoteText(Canvas canvas, ChartNoteTextAttrBean noteTextAttrBean) {
        if (mNoteTextRect == null) {
            mNoteTextRect = new Rect();
        }
        mNoteTextPaint.setColor(noteTextAttrBean.getNoteTextColor());
        mNoteTextPaint.setTextSize(noteTextAttrBean.getNoteTextSize());
        mNoteTextPaint.setTypeface(mPoppinsTypeface);
        mNoteTextPaint.setFakeBoldText(true);
        String text = noteTextAttrBean.getNoteText();
        mNoteTextPaint.getTextBounds(text, 0, text.length(), mNoteTextRect);

        float left = mViewPortHandler.contentLeft();
        float top = mViewPortHandler.contentTop();
        float right = mViewPortHandler.contentRight();
        float bottom = mViewPortHandler.contentBottom();
        float x = left;
        float y = top;
        switch (noteTextAttrBean.getGravity()) {
            case RIGHT_TOP:
                x = right - noteTextAttrBean.getPaddingRight();
                y = top + mNoteTextRect.height() + noteTextAttrBean.getPaddingTop();
                break;

            case LEFT_BOTTOM:
                x = left + noteTextAttrBean.getPaddingLeft();
                y = bottom - mNoteTextRect.height() / 2 - noteTextAttrBean.getPaddingBottom();
                break;

            case RIGHT_BOTTOM:
                x = right - noteTextAttrBean.getPaddingRight();
                y = bottom - mNoteTextRect.height() / 2 - noteTextAttrBean.getPaddingBottom();
                break;

            case CENTER:
                x = (left + right) / 2;
                y = (top + bottom) / 2;
                break;

            case TOP_CENTER_HORIZONTAL:
                x = (left + right) / 2;
                y = top + mNoteTextRect.height() + noteTextAttrBean.getPaddingTop();
                break;

            case BOTTOM_CENTER_HORIZONTAL:
                x = (left + right) / 2;
                y = bottom - mNoteTextRect.height() / 2 - noteTextAttrBean.getPaddingBottom();
                break;

            case LEFT_CENTER_VERTICAL:
                x = left + noteTextAttrBean.getPaddingLeft();
                y = (top + bottom) / 2;
                break;

            case RIGHT_CENTER_VERTICAL:
                x = right - noteTextAttrBean.getPaddingRight();
                y = (top + bottom) / 2;
                break;

            default:
                x = left + noteTextAttrBean.getPaddingLeft();
                y = top + mNoteTextRect.height() + noteTextAttrBean.getPaddingTop();
        }
        canvas.drawText(text, x, y, mNoteTextPaint);
    }

    private void drawAboveChartText(Canvas canvas, ChartNoteTextAttrBean noteTextAttrBean) {
        if (mNoteTextRect == null) {
            mNoteTextRect = new Rect();
        }
        mNoteTextPaint.setColor(noteTextAttrBean.getNoteTextColor());
        mNoteTextPaint.setTextSize(noteTextAttrBean.getNoteTextSize());
        mNoteTextPaint.setTypeface(mPalanquinTypeface);
        mNoteTextPaint.setFakeBoldText(false);
        String text = noteTextAttrBean.getNoteText();
        String[] textArr = text.split("\n");
        float left = mViewPortHandler.contentLeft();
        float top = mViewPortHandler.contentTop();
        float right = mViewPortHandler.contentRight();
        float bottom = mViewPortHandler.contentBottom();
        float x = left;
        for (int i=textArr.length - 1; i>=0; i--) {
            String str = textArr[i];
            mNoteTextPaint.getTextBounds(str, 0, str.length(), mNoteTextRect);
            float y = i==textArr.length - 1 ? top - mNoteTextRect.height() + 8 : top - mNoteTextRect.height() - 2;
            switch (noteTextAttrBean.getGravity()) {
                case CENTER:
                    x = (left + right) / 2;
                    break;

                case RIGHT_TOP:
                case RIGHT_CENTER_VERTICAL:
                case RIGHT_BOTTOM:
                    x = right - mNoteTextRect.width() - noteTextAttrBean.getPaddingRight();
                    break;

                default:
                    x = left + noteTextAttrBean.getPaddingLeft();
                    break;
            }
            canvas.drawText(str, x, y, mNoteTextPaint);
            top = y;
        }
    }

    @Override
    protected void drawMarkers(Canvas canvas) {
        // if there is no marker view or drawing marker is disabled
        if (mMarkerView == null || !mDrawMarkerViews || !valuesToHighlight())
            return;
        if (CollectionUtil.isListEmpty(highlights)) return;
        List<Entry> entries = new ArrayList<>();
        float x = 0f;
        for (Highlight highlight : highlights) {
            int xIndex = highlight.getXIndex();
            int dataSetIndex = highlight.getDataSetIndex();
            float deltaX = mXAxis != null
                    ? mXAxis.mAxisRange
                    : ((mData == null ? 0.f : mData.getXValCount()) - 1.f);
            if (xIndex <= deltaX && xIndex <= deltaX * mAnimator.getPhaseX()) {

                Entry e = mData.getEntryForHighlight(highlight);

                // make sure entry not null
                if (e == null || e.getXIndex() != highlight.getXIndex())
                    continue;

                float[] pos = getMarkerPosition(e, highlight);

                // check bounds
                if (!mViewPortHandler.isInBoundsX(pos[0]))
                    continue;
                entries.add(e);
                x = pos[0];
            }
        }
        if (CollectionUtil.isListEmpty(entries)) return;
        ((CustomCombinedMarkerView) mMarkerView).setMarkerValueShow(entries, highlights);
        mMarkerView.measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
        mMarkerView.layout(0, 0, mMarkerView.getMeasuredWidth(),
                mMarkerView.getMeasuredHeight());
        float half = mMarkerView.getWidth() / 2;
        float screenWidth = ScreenUtils.getScreenWidth(getContext());
        int limitLeft = ((CustomCombinedMarkerView) mMarkerView).getLimitLeft();
        int limitRight = ((CustomCombinedMarkerView) mMarkerView).getLimitRight();
        if (x + half > screenWidth - limitRight) {
            x = screenWidth - half - limitRight;
        }

        if (x - half < limitLeft) {
            x = half + limitLeft;
        }
        mMarkerView.draw(canvas, x, mViewPortHandler.contentTop() - 10);
    }

    @Override
    public void highlightValue(Highlight high, boolean callListener) {
        Entry e = null;

        if (high == null)
            mIndicesToHighlight = null;
        else {

            if (mLogEnabled)
                Log.i(LOG_TAG, "Highlighted: " + high.toString());

            e = mData.getEntryForHighlight(high);
            if (e == null) {
                mIndicesToHighlight = null;
                high = null;
            } else {
                if (this instanceof BarLineChartBase
                        && ((BarLineChartBase) this).isHighlightFullBarEnabled())
                    high = new Highlight(high.getXIndex(), Float.NaN, -1, -1, -1);

                // set the indices to highlight
                mIndicesToHighlight = new Highlight[]{
                        high
                };
            }
        }

        if (callListener && mSelectionListener != null) {

            if (!valuesToHighlight())
                mSelectionListener.onNothingSelected();
            else {
                // notify the listener
                mSelectionListener.onValueSelected(e, high.getDataSetIndex(), high);
            }
        }
        // redraw the chart
        invalidate();
    }

    /**
     * 画Y轴标签顶部描述
     * @param canvas
     * @param dependency
     */
    public void drawYTopDesc(Canvas canvas, YAxis.AxisDependency dependency) {
        if (dependency == YAxis.AxisDependency.LEFT) {
           if (mAxisRendererLeft instanceof CustomYAxisRenderer) {
               ((CustomYAxisRenderer)mAxisRendererLeft).drawAxisYTopDesc(canvas, mLefAxisYDesc);
           }
        } else {
            if (mAxisRendererRight instanceof CustomYAxisRenderer) {
                ((CustomYAxisRenderer)mAxisRendererRight).drawAxisYTopDesc(canvas, mRightAxisYDesc);
            }
        }
    }

    public void highlightsValue(List<Highlight> highlights) {
        if (CollectionUtil.isListNotEmpty(highlights)) {
            this.highlights = highlights;
        }
    }

    public void setDrawDefaultXGridLines(boolean isDrawXGridLines) {
        this.isDrawDefaultXGridLines = isDrawXGridLines;
    }

    /**
     * Returns the Highlight object (contains x-index and DataSet index) of the
     * selected value at the given touch point inside the Line-, Scatter-, or
     * CandleStick-Chart.
     *
     * @param x
     * @param y
     * @return
     */
    public List<Highlight> getHighlightsByTouchPoint(float x, float y) {

        if (mData == null) {
            Log.e(LOG_TAG, "Can't select by touch. No data set.");
            return null;
        } else
            return ((CustomCombinedChartHighlighter) getHighlighter()).getHighlights(x, y);
    }


    public void setDefaultHighLight(int xIndex) {
        List<Highlight> highlights = new ArrayList<>();
        List<SelectionDetail> valsAtIndex = ((CustomCombinedChartHighlighter) getHighlighter()).getSelectionDetailsAtIndex(xIndex, -1);
        if (valsAtIndex.size() > 0) {
            for (SelectionDetail selectionDetail : valsAtIndex) {
                if (selectionDetail == null) continue;
                Highlight highlight = new Highlight(xIndex,
                        selectionDetail.value,
                        selectionDetail.dataIndex,
                        selectionDetail.dataSetIndex);
                highlights.add(highlight);
            }
            highlightsValue(highlights);
            highlightValues(highlights.toArray(new Highlight[highlights.size()]));
        }
    }

    public boolean isShowNoteText() {
        return showNoteText;
    }

    public void setShowNoteText(boolean showNoteText) {
        this.showNoteText = showNoteText;
    }

    public boolean isShowAboveChartText() {
        return showAboveChartText;
    }

    public void setShowAboveChartText(boolean showAboveChartText) {
        this.showAboveChartText = showAboveChartText;
    }

    public List<ChartNoteTextAttrBean> getNoteTexts() {
        return noteTexts;
    }

    public void setNoteTexts(List<ChartNoteTextAttrBean> noteTexts) {
        this.noteTexts = noteTexts;
    }

    public void setAboveChartTexts(List<ChartNoteTextAttrBean> aboveChartTexts) {
        this.aboveChartTexts = aboveChartTexts;
    }

    public void setScrollChartListener(OnScrollChartListener scrollChartListener) {
        this.scrollChartListener = scrollChartListener;
        if (mChartTouchListener != null) {
            ((CustomCombinedChartTouchListener) mChartTouchListener).setScrollChartListener(scrollChartListener);
        }
    }

    public boolean isNeedNoDataGrid() {
        return isNeedNoDataGrid;
    }

    public void setNeedNoDataGrid(boolean needNoDataGrid) {
        isNeedNoDataGrid = needNoDataGrid;
    }

    public float getNoDataOffset() {
        return noDataOffset;
    }

    public void setNoDataOffset(float noDataOffset) {
        this.noDataOffset = noDataOffset;
    }

    public float getNoDataLeftOffset() {
        return noDataLeftOffset;
    }

    public void setNoDataLeftOffset(float noDataLeftOffset) {
        this.noDataLeftOffset = noDataLeftOffset;
    }

    public float getNoDataTopOffset() {
        return noDataTopOffset;
    }

    public void setNoDataTopOffset(float noDataTopOffset) {
        this.noDataTopOffset = noDataTopOffset;
    }

    public float getNoDataRightOffset() {
        return noDataRightOffset;
    }

    public void setNoDataRightOffset(float noDataRightOffset) {
        this.noDataRightOffset = noDataRightOffset;
    }

    public float getNoDataBottomOffset() {
        return noDataBottomOffset;
    }

    public void setNoDataBottomOffset(float noDataBottomOffset) {
        this.noDataBottomOffset = noDataBottomOffset;
    }

    public void setDrawLefAxisYDesc(boolean drawLefAxisYDesc) {
        isDrawLefAxisYDesc = drawLefAxisYDesc;
    }

    public void setDrawRightAxisYDesc(boolean drawRightAxisYDesc) {
        isDrawRightAxisYDesc = drawRightAxisYDesc;
    }

    public void setLefAxisYDesc(String lefAxisYDesc) {
        this.mLefAxisYDesc = lefAxisYDesc;
    }

    public void setRightAxisYDesc(String rightAxisYDesc) {
        this.mRightAxisYDesc = rightAxisYDesc;
    }

    public void setReverse(boolean isReverse) {
        if (mChartTouchListener != null) {
            ((CustomCombinedChartTouchListener) mChartTouchListener).setReverse(isReverse);
        }
    }


    /**
     * 初始化Grid-imported渲染器（使用单例）
     */
    private void initGridRenderer() {
        mGridRenderer = GridImportedLineChartRender.getInstance();
        mGridRenderer.setContext(getContext());
        mGridRenderer.setDrawXAxisAnnotation(mDrawXAxisAnnotation);
        mGridRenderer.setDrawThresholdRegion(mDrawThresholdRegion);
        mGridRenderer.setXAxisAnnotationDimensions(1f, 5f);
    }

    /**
     * 绘制Grid-imported X轴标注（在所有图表元素之后）
     */
    private void drawGridImportedXAxisAnnotation(Canvas canvas) {
//        if (!mDrawXAxisAnnotation || mData == null || mGridRenderer == null) {
//            return;
//        }
//
//        // 检查X轴标注的独立过滤器状态
//        if (!isXAxisAnnotationFilterEnabled()) {
//            Log.i("CustomCombinedChart", "X轴标注过滤器未启用，跳过绘制");
//            return;
//        }

        try {
            // 获取绘制参数
            int minx = getLowestVisibleXIndex();
            float phaseX = mAnimator.getPhaseX();
            float phaseY = mAnimator.getPhaseY();

            Log.i("CustomCombinedChart", String.format("X轴标注动画相位: phaseX=%.2f, phaseY=%.2f", phaseX, phaseY));

            // 尝试绘制折线图X轴标注
            ILineDataSet thresholdLineDataSet = findThresholdDataSet();
            if (thresholdLineDataSet != null) {
                int count = Math.min(getHighestVisibleXIndex() + 1, thresholdLineDataSet.getEntryCount());
                mGridRenderer.setLineData(mData.getLineData());
                mGridRenderer.drawXAxisAnnotationAfterChart(canvas, thresholdLineDataSet, minx, count, phaseX, mLeftAxisTransformer);
                Log.i("CustomCombinedChart", "折线图X轴标注绘制完成");
            }

            // 尝试绘制柱状图X轴标注
            IBarDataSet thresholdBarDataSet = findThresholdBarDataSet();
            if (thresholdBarDataSet != null) {
                int count = Math.min(getHighestVisibleXIndex() + 1, thresholdBarDataSet.getEntryCount());

                // 获取实际的柱状图宽度
                float barWidth = getActualBarWidth();

                mGridRenderer.drawBarChartXAxisAnnotation(canvas, thresholdBarDataSet, minx, count, phaseX, mLeftAxisTransformer, barWidth);
                Log.i("CustomCombinedChart", "柱状图X轴标注绘制完成");
            }

        } catch (Exception e) {
            Log.e("CustomCombinedChart", "绘制X轴标注时发生错误", e);
        }
    }




    /**
     * 检查X轴标注是否启用
     */
    private boolean isXAxisAnnotationFilterEnabled() {
        return mDrawXAxisAnnotation;
    }

    /**
     * 获取阈值数据集（直接返回设置的数据集）
     */
    private ILineDataSet findThresholdDataSet() {
        if (mThresholdDataSet != null) {
            return mThresholdDataSet;
        }

        return null;
    }

    /**
     * 查找柱状图阈值数据集
     */
    private IBarDataSet findThresholdBarDataSet() {
        if (mData == null || mData.getBarData() == null) {
            return null;
        }

        // 简化逻辑：返回第一个柱状图数据集（假设都有peakshaving=true）
        if (mData.getBarData().getDataSetCount() > 0) {
            IBarDataSet dataSet = mData.getBarData().getDataSetByIndex(0);
            Log.i("CustomCombinedChart", "找到柱状图数据集（简化版本）: " + dataSet.getLabel());
            return dataSet;
        }

        Log.i("CustomCombinedChart", "未找到柱状图阈值数据集");
        return null;
    }

    /**
     * 获取实际的柱状图宽度
     */
    private float getActualBarWidth() {
        if (mData == null || mData.getBarData() == null) {
            return 30f; // 默认宽度
        }

        try {
            // 方法1：从BarData获取barWidth
            float barWidth = mData.getBarData().getGroupSpace();
            float barWidth = (chartWidth - groupSpace) / (barCount + (groupCount - 1) * barSpace);
            if (barWidth > 0) {
                // 将数据宽度转换为像素宽度
                float[] point1 = new float[]{0, 0};
                float[] point2 = new float[]{barWidth, 0};
                mLeftAxisTransformer.pointValuesToPixel(point1);
                mLeftAxisTransformer.pointValuesToPixel(point2);

                float pixelWidth = Math.abs(point2[0] - point1[0]);
                Log.i("CustomCombinedChart", String.format("获取柱状图宽度: 数据宽度=%.2f, 像素宽度=%.2f", barWidth, pixelWidth));
                return pixelWidth;
            }
        } catch (Exception e) {
            Log.w("CustomCombinedChart", "获取柱状图宽度失败，使用默认值", e);
        }

        // 方法2：估算宽度（基于X轴间距）
        try {
            float[] point1 = new float[]{0, 0};
            float[] point2 = new float[]{1, 0};
            mLeftAxisTransformer.pointValuesToPixel(point1);
            mLeftAxisTransformer.pointValuesToPixel(point2);

            float spacing = Math.abs(point2[0] - point1[0]);
            float estimatedWidth = spacing * 0.8f; // 柱子宽度为间距的80%

            Log.i("CustomCombinedChart", String.format("估算柱状图宽度: 间距=%.2f, 估算宽度=%.2f", spacing, estimatedWidth));
            return estimatedWidth;
        } catch (Exception e) {
            Log.w("CustomCombinedChart", "估算柱状图宽度失败，使用默认值", e);
        }

        // 默认宽度
        return 30f;
    }

    /**
     * 设置X轴标注的尺寸
     */
    public void setXAxisAnnotationDimensions(float gap, float height) {
        if (mGridRenderer != null) {
            mGridRenderer.setXAxisAnnotationDimensions(gap, height);
        }
    }

    /**
     * 设置阈值数据集和控制参数
     * @param thresholdDataSet 阈值数据集
     * @param drawThresholdRegion 是否绘制阈值区域
     * @param drawXAxisAnnotation 是否绘制X轴标注
     */
    public void setThresholdConfig(ILineDataSet thresholdDataSet, boolean drawThresholdRegion, boolean drawXAxisAnnotation) {
        this.mThresholdDataSet = thresholdDataSet;
        this.mDrawThresholdRegion = drawThresholdRegion;
        this.mDrawXAxisAnnotation = drawXAxisAnnotation;

        // 同时设置给GridRenderer
        if (mGridRenderer != null) {
            mGridRenderer.setDrawXAxisAnnotation(drawXAxisAnnotation);
            mGridRenderer.setDrawThresholdRegion(drawThresholdRegion);
            mGridRenderer.setThresholdDataSet(thresholdDataSet);
        }
    }
}
