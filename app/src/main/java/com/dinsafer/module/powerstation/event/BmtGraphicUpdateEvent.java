package com.dinsafer.module.powerstation.event;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

/**
 * bmt电流图更新事件
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/12/31 4:20 下午
 */
public class BmtGraphicUpdateEvent {
    /**
     * 电流图
     */
    public static final int TYPE_CURRENT = 0;
    /**
     * 电池图
     */
    public static final int TYPE_BATTERY = 1;
    /**
     * 电流图和电池图
     */
    public static final int TYPE_CURRENT_BATTERY = 2;

    private final String deviceId;
    private final String subCategory;
    private final String cmd;
    @NonNull
    private final Map<String, Object> data;
    private final int graphicType;

    public BmtGraphicUpdateEvent(String deviceId, String subCategory, String cmd, Map<String, Object> data, final int updateGraphicType) {
        this.deviceId = deviceId;
        this.subCategory = subCategory;
        this.cmd = cmd;
        this.graphicType = updateGraphicType;
        this.data = new HashMap<>();
        if (null != data) {
            this.data.putAll(data);
        }
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public String getCmd() {
        return cmd;
    }

    public int getGraphicType() {
        return graphicType;
    }

    /**
     * 是否需要更新电流视图
     *
     * @return true 需要更新
     */
    public boolean isGraphicCurrent() {
        return TYPE_CURRENT == graphicType || TYPE_CURRENT_BATTERY == graphicType;
    }

    /**
     * 是否需要更新电池视图
     *
     * @return true 需要更新
     */
    public boolean isGraphicBattery() {
        return TYPE_BATTERY == graphicType || TYPE_CURRENT_BATTERY == graphicType;
    }

    @NonNull
    public Map<String, Object> getData() {
        return data;
    }

    @Override
    public String toString() {
        return "BmtGraphicUpdateEvent{" +
                "deviceId='" + deviceId + '\'' +
                ", subCategory='" + subCategory + '\'' +
                ", cmd='" + cmd + '\'' +
                ", data=" + data +
                ", graphicType=" + graphicType +
                '}';
    }
}
