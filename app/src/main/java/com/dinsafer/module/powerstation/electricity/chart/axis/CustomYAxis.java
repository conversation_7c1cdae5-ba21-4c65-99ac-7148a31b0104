package com.dinsafer.module.powerstation.electricity.chart.axis;

import com.github.mikephil.charting.components.YAxis;

public class CustomYAxis extends YAxis {

    public CustomYAxis() {
        super();
    }

    public CustomYAxis(AxisDependency position) {
        super(position);
    }

    @Override
    public String getLongestLabel() {

        String longest = "";

        for (int i = 0; i < mEntries.length; i++) {
            String text = getFormattedLabel(i);
            String[] textArr = text.split("\n");
            for (int j=0; j<textArr.length; j++) {
                if (longest.length() < textArr[j].length())
                    longest = textArr[j];
            }
        }

        return longest;
    }
}
