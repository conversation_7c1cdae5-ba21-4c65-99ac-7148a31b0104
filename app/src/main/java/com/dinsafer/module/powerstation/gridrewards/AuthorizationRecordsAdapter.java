package com.dinsafer.module.powerstation.gridrewards;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import androidx.databinding.DataBindingUtil;

import com.dinsafer.common.utils.DateUtil;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAuthorizationRecordBinding;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.Local;

import java.util.List;

/**
 * @describe：
 * @date：2024/10/30
 * @author: create by Sydnee
 */
public class AuthorizationRecordsAdapter extends BaseAdapter {

    private List<BalanceContractRecordsResponse.Record> data;
    private Activity mActivity;

    public AuthorizationRecordsAdapter(Activity activity, List<BalanceContractRecordsResponse.Record> data) {
        this.data = data;
        this.mActivity = activity;
    }


    @Override
    public int getCount() {
        return data.size();
    }

    @Override
    public Object getItem(int position) {
        return data.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder = null;
        if (null == convertView) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.item_authorization_record, null);
            viewHolder = new ViewHolder(convertView);
            convertView.setTag(viewHolder);
        } else {
            try {
                viewHolder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        BalanceContractRecordsResponse.Record record = data.get(position);
        int status = record.getStatus();
        StringBuilder sbName = new StringBuilder();
        String time = DDDateUtil.formatLong(record.getCreate_time() / 1000L / 1000L, "yyyyMMdd");
        sbName.append(time);
        sbName.append(record.getSeq());
        sbName.append(" ");
        Drawable drawable;
        if (status == 0) {  // 签约
            sbName.append(Local.s(mActivity.getString(R.string.power_of_attorney)));
            viewHolder.mBinding.recordName.setText(sbName.toString());
            drawable = mActivity.getDrawable(R.drawable.icon_contract_sign);
            viewHolder.mBinding.tvRecordContent.setVisibility(View.GONE);
            viewHolder.mBinding.recordNor.setVisibility(View.VISIBLE);

        } else if (status == 1) {  // 解约
            sbName.append(Local.s(mActivity.getString(R.string.terminate_statement)));
            viewHolder.mBinding.recordName.setText(sbName.toString());
            drawable = mActivity.getDrawable(R.drawable.icon_contract_release);
            viewHolder.mBinding.tvRecordContent.setVisibility(View.GONE);
            viewHolder.mBinding.recordNor.setVisibility(View.VISIBLE);

        } else {  // 声明
            viewHolder.mBinding.recordName.setText(Local.s(mActivity.getString(R.string.family_authorization_statement)));
            drawable = mActivity.getDrawable(R.drawable.icon_home);
            time = DateUtil.getFormatDateFromNow((record.getCreate_time() / 1000L / 1000L), DateUtil.formatYYYYMMDDHour);
            viewHolder.mBinding.tvRecordContent.setText(Local.s(mActivity.getString(R.string.family_authorization_statement_content))
                    .replace("#family_name", record.getHome_name())
                    .replace("#time", time));
            viewHolder.mBinding.tvRecordContent.setVisibility(View.VISIBLE);
            viewHolder.mBinding.recordNor.setVisibility(View.GONE);
            viewHolder.mBinding.getRoot().setOnTouchListener((v, event) -> true);
        }
        viewHolder.mBinding.recordName.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null);

        return convertView;
    }

    static class ViewHolder {
        ItemAuthorizationRecordBinding mBinding;

        ViewHolder(View itemView) {
            mBinding = DataBindingUtil.bind(itemView);
        }
    }
}
