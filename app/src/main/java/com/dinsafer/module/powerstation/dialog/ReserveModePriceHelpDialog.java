package com.dinsafer.module.powerstation.dialog;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogReserveModePriceHelpBinding;
import com.dinsafer.module.powerstation.adapter.ReserveModePriceHelpAdapter;
import com.dinsafer.module.powerstation.bean.ReserveModePriceHelpBean;
import com.dinsafer.permission.BaseBottomSheetDialog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/27 15:18
 * @description :
 */
public class ReserveModePriceHelpDialog extends BaseBottomSheetDialog<DialogReserveModePriceHelpBinding> {

    public static final String TAG = ReserveModePriceHelpDialog.class.getSimpleName();
    private ReserveModePriceHelpAdapter mReserveModePriceHelpAdapter;

    public static ReserveModePriceHelpDialog newInstance() {
        return new ReserveModePriceHelpDialog();
    }

    @Override
    protected int provideResId() {
        return R.layout.dialog_reserve_mode_price_help;
    }

    @Override
    protected void initView() {
        super.initView();
        mBinding.getRoot().setBackgroundResource(R.drawable.shape_bg_cascading_reserve_mode);
        setEnableDrag(false);
        mBinding.ivClose.setOnClickListener(v -> dismiss());
        mBinding.tvGotIt.setOnClickListener(v -> {
            dismiss();
        });
        initRv();
    }

    private void initRv() {
        mBinding.rvNote.setLayoutManager(new LinearLayoutManager(getContext()));
        mReserveModePriceHelpAdapter = new ReserveModePriceHelpAdapter();
        mBinding.rvNote.setAdapter(mReserveModePriceHelpAdapter);
        List<String> helpTitles =  Arrays.asList(getContext().getResources().getStringArray(R.array.reserve_price_help_titles));
        List<String> helpDescs =  Arrays.asList(getContext().getResources().getStringArray(R.array.reserve_price_help_descs));
        List<ReserveModePriceHelpBean> data = new ArrayList<>();
        for (int i=0; i<helpTitles.size(); i++) {
            data.add(new ReserveModePriceHelpBean(helpTitles.get(i), helpDescs.get(i)));
        }
        mReserveModePriceHelpAdapter.setNewData(data);
    }
}
