package com.dinsafer.module.powerstation.settings.network;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsNetworkSettingsBinding;
import com.dinsafer.dinsdk.BmtBinderWrapper;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.PSProductNameFragment;
import com.dinsafer.module.powerstation.adapter.PSNetworkSettingsWifiAdapter;
import com.dinsafer.module.powerstation.adapter.PSWifiInfoBean;
import com.dinsafer.module.powerstation.device.PSBleBaseBindFragment;
import com.dinsafer.module.powerstation.event.FinishAddBmtEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_bmt.add.BaseBmtBinder;
import com.dinsafer.module_bmt.add.BaseBmtNetworkManager;
import com.dinsafer.module_bmt.bean.BmtBleWifiInfo;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.annotations.NonNull;
import rx.Observable;
import rx.Observer;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * <AUTHOR> WZH
 * @date : 2022/11/30 16:01
 * @description :
 */
public class PSNetworkSettingsFragment extends PSBleBaseBindFragment<FragmentPsNetworkSettingsBinding>
        implements BaseBmtBinder.IWifiListCallBack, BaseBmtNetworkManager.IWifiListCallBack {

    private static final String KEY_IS_CAN_CLOSE = "isCanClose";

    private PSNetworkSettingsWifiAdapter mPSNetworkSettingsWifiAdapter;
    private PSNetworkSettingsWifiAdapter mPSNetworkSettingsWiredAdapter;
    private final ArrayList<PSWifiInfoBean> mData = new ArrayList<>();
    boolean isCanClose = false;
    private AlertDialogV2 offlineDialog;
    private Subscription mGetWifiSubscription;
    private Animation operatingAnim;

    private boolean supportPwdFreeNetwork;
    private boolean isIgnoreDisconnect;

    public static PSNetworkSettingsFragment newInstanceForScanAdd(String deviceID, String model, boolean isCanClose, boolean isIgnoreDisconnect) {
        return newInstance(KEY_FROM_SCAN_ADD, deviceID, model, isCanClose, isIgnoreDisconnect);
    }

    public static PSNetworkSettingsFragment newInstanceForChangeNetwork(String deviceID, String model, boolean isCanClose) {
        return newInstance(KEY_FROM_CHANGE_NETWORK, deviceID, model, isCanClose, false);
    }

    private static PSNetworkSettingsFragment newInstance(int from, String deviceID, String model
            , boolean isCanClose, boolean isIgnoreDisconnect) {
        PSNetworkSettingsFragment fragment = new PSNetworkSettingsFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_FROM, from);
        bundle.putBoolean(KEY_IS_CAN_CLOSE, isCanClose);
        bundle.putString(KEY_DEVICE_ID, deviceID);
        bundle.putBoolean(KEY_IGNORE_DISCONNECT, isIgnoreDisconnect);
        if (model != null) {
            bundle.putString(PSKeyConstant.KEY_MODEL, model);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_network_settings;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.ps_advanced_settings_network_network_settings));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
        initParams();
        operatingAnim = AnimationUtils.loadAnimation(getContext(), R.anim.rotation);
        if (isCanClose) {
            mBinding.commonBar.commonBarBack.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBar.commonBarBack.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onBinderReady(@NonNull BmtBinderWrapper pluginBinder) {
        mBinder.setWifiListCallBackAdd(this);
        mBinder.setWifiListCallBackChange(this);
        mBinder.addBindCallBack(this);
        mBinder.addEthernetCallback(this);

        mBinder.setModelCallbackAdd(new BaseBmtBinder.IModelCallback() {
            @Override
            public void onModelCallback(String cmd, int status, String result) {
                if (cmd != null && cmd.equals("set_model")) {
                    if (status != 1) {
                        closeLoadingFragment();
                        AlertDialog.createBuilder(activity)
                                .setOk(getResources().getString(R.string.got_it))
                                .setContent(getResources().getString(R.string.model_do_not_match_tips))
                                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                                    @Override
                                    public void onOkClick() {
                                        isIgnoreDisconnect = true;
                                        getDelegateActivity().addCommonFragment(PSProductNameFragment.newInstance(deviceID));
                                        removeSelf();
                                    }
                                })
                                .preBuilder().show();
                    }
                }
            }
        });
        initRv();
        toStartRefresh(1000);
        initWiredRv();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null != mBinder) {
            mBinding.ivLoading.setVisibility(View.VISIBLE);
            mBinding.ivLoading.startAnimation(operatingAnim);
        }
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        toStartRefresh(0);
    }

    @Override
    public void onPauseFragment() {
        super.onPauseFragment();
        toStopRefresh();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        toStopRefresh();
        EventBus.getDefault().unregister(this);
        if (null != mBinder) {
            mBinder.setWifiListCallBackAdd(null);
            mBinder.setWifiListCallBackChange(null);
            mBinder.removeBindCallBack(this);
            mBinder.setModelCallbackAdd(null);
            mBinder.removeEthernetCallback(this);
            if (!isIgnoreDisconnect) {
                mBinder.stop();
            }
        }
    }

    private void initParams() {
        Bundle bundle = getArguments();
        if (null == bundle) {
            return;
        }
        mFrom = bundle.getInt(KEY_FROM);
        deviceID = bundle.getString(KEY_DEVICE_ID);
        isIgnoreDisconnect = bundle.getBoolean(KEY_IGNORE_DISCONNECT);
        mModel = bundle.getString(PSKeyConstant.KEY_MODEL);
        isCanClose = bundle.getBoolean(KEY_IS_CAN_CLOSE);
    }

    private void initRv() {
        mBinding.rvWifi.setLayoutManager(new LinearLayoutManager(getContext()));
        mPSNetworkSettingsWifiAdapter = new PSNetworkSettingsWifiAdapter();
        mBinding.rvWifi.setAdapter(mPSNetworkSettingsWifiAdapter);
        mPSNetworkSettingsWifiAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                toStopRefresh();
                final String otherStr = Local.s(getResources().getString(R.string.wifi_other));
                final PSWifiInfoBean clickItem = mData.get(position);
                final boolean isOther = otherStr.equals(clickItem.getSsid());

                if (!isOther && supportPwdFreeNetwork
                        && !mData.get(position).isAuth()) {
                    connectOpenWifi(mData.get(position));
                } else {
                    getDelegateActivity().addCommonFragment(PSConfigureNetworkSettingsFragment
                            .newInstance(mFrom, deviceID, mModel, clickItem.getSsid(), clickItem.getRssi(), isOther));
                }
            }
        });
        mPSNetworkSettingsWifiAdapter.setNewData(mData);
    }

    private void initWiredRv() {
        if (BmtUtil.isBmtDeviceV2(mModel)) {
            mBinding.tvChooseWiredNetwork.setVisibility(View.VISIBLE);
            mBinding.vMaxHeightViewWired.setVisibility(View.VISIBLE);
            mBinding.rvWired.setLayoutManager(new LinearLayoutManager(getContext()));
            mPSNetworkSettingsWiredAdapter = new PSNetworkSettingsWifiAdapter();
            mBinding.rvWired.setAdapter(mPSNetworkSettingsWiredAdapter);
            mPSNetworkSettingsWiredAdapter.setOnItemClickListener((adapter, view, position) -> {
                showTimeOutLoadinFramgmentWithCallBack(mConfigTimeout, this);
                isEthernetConnect = true;
                mBinder.getEthernetState();
            });

            List<PSWifiInfoBean> wired = new ArrayList<>();
            wired.add(new PSWifiInfoBean(Local.s(getResources().getString(R.string.ethernet)), 999, false));
            mPSNetworkSettingsWiredAdapter.setNewData(wired);

        } else {
            mBinding.tvChooseWiredNetwork.setVisibility(View.GONE);
            mBinding.vMaxHeightViewWired.setVisibility(View.GONE);
        }

    }

    public void connectOpenWifi(@NonNull PSWifiInfoBean info) {
        DDLog.d(TAG, "connectOpenWifi: " + info.getSsid());
        if (info.getSsid().length() > 0) {
            showTimeOutLoadinFramgmentWithCallBack(mConfigTimeout, this);
            wifiSSid = info.getSsid();
            wifiPassword = "";
            mBinder.setSsid(wifiSSid);
            mBinder.setSsidPassword(wifiPassword);
            mBinder.setSsidAuth(info.isAuth());
            mBinder.bindDevice(null);
        }
    }


    public void toRefresh() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                mBinder.getWifiList();
            }
        }, 1000);
    }

    public void toStartRefresh(final long delay) {
        DDLog.d(TAG, "toStartRefresh, delay: " + delay);
        if (mGetWifiSubscription != null && !mGetWifiSubscription.isUnsubscribed()) {
            mGetWifiSubscription.unsubscribe();
        }
        mGetWifiSubscription = Observable.interval(delay, APIKey.BLE_GET_WIFI_LIST_INTERVAL,
                TimeUnit.MILLISECONDS).observeOn(AndroidSchedulers.mainThread()).subscribe(new Observer<Long>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {
                i(e.getMessage());
            }

            @Override
            public void onNext(Long aLong) {
                DDLog.d(TAG, "onNext, delay: " + aLong);
                mBinding.ivLoading.setVisibility(View.VISIBLE);
                mBinding.ivLoading.startAnimation(operatingAnim);
                if (null != mBinder) {
                    mBinder.getWifiList();
                }
            }
        });
    }

    public void toStopRefresh() {
        if (mGetWifiSubscription != null && !mGetWifiSubscription.isUnsubscribed()) {
            mGetWifiSubscription.unsubscribe();
            mGetWifiSubscription = null;
        }
    }

    @Override
    public boolean onBackPressed() {
        return !isCanClose;
    }

    public void close() {
        removeSelf();
    }

    @Override
    public void onWifiListCallBack(List<BmtBleWifiInfo> list, boolean spfn) {
        mData.clear();
        if (null != list && list.size() > 0) {
            PSWifiInfoBean bean;
            for (BmtBleWifiInfo wifiInfo : list) {
                bean = new PSWifiInfoBean(wifiInfo);
                mData.add(bean);
            }
        }
        supportPwdFreeNetwork = spfn;
        mData.add(new PSWifiInfoBean(Local.s(getResources().getString(R.string.wifi_other)), 999, false));
        mPSNetworkSettingsWifiAdapter.setNewData(mData);
        mPSNetworkSettingsWifiAdapter.notifyDataSetChanged();

        closeTimeOutLoadinFramgmentWithErrorAlert();
        mBinding.ivLoading.clearAnimation();
        mBinding.ivLoading.setVisibility(View.GONE);

        final String rememberWifiSsid = DBUtil.SGet(DBKey.REMEMBER_WIFI);
        if (!TextUtils.isEmpty(rememberWifiSsid)) {
            final PSWifiInfoBean rememberBean = new PSWifiInfoBean(rememberWifiSsid, 0, false);
            if (mData.contains(rememberBean)) {
                if (offlineDialog != null && offlineDialog.isShowing()) {
                    return;
                }
                offlineDialog = AlertDialogV2.createBuilder(getActivity())
                        .setContent(Local.s(this.getResources().getString(R.string.ipc_remember_ssid_hint))
                                + rememberWifiSsid)
                        .setOk(this.getResources().getString(R.string.Confirm))
                        .setCancel(this.getResources().getString(R.string.select_other_network))
                        .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                toStopRefresh();
                                showTimeOutLoadinFramgmentWithCallBack(mConfigTimeout, PSNetworkSettingsFragment.this);
                                wifiSSid = rememberWifiSsid;
                                wifiPassword = DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD);
                                mBinder.setSsid(wifiSSid);
                                mBinder.setSsidPassword(wifiPassword);
                                mBinder.bindDevice(null);
                            }
                        })
                        .preBuilder();
                offlineDialog.setCancel(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        offlineDialog.dismiss();
                    }
                });
                offlineDialog.show();
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(FinishAddBmtEvent event) {
        isIgnoreDisconnect = false;
        removeSelf();
    }
}
