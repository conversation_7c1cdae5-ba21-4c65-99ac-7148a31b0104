package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemPsDirectionBinding;

public class PSDirectionItemModel extends BasePowerStationItemModel<ItemPsDirectionBinding> {

    public PSDirectionItemModel(Context context, String deviceId, String subcategory) {
        super(context, deviceId, subcategory);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_ps_direction;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemPsDirectionBinding itemPsDirectionBinding) {

    }
}
