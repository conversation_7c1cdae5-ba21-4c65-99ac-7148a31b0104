package com.dinsafer.module.powerstation.electricity.bean;

import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineDataSet;

import java.util.List;

public class SectionLineDataSet extends LineDataSet {

    private int[] gradientColors;
    private float[] gradientPosition;
    private int textureColor;
    private boolean isSection;
    private boolean drawTexture;
    private List<Integer> intervalList;

    public SectionLineDataSet(List<Entry> yVals, String label) {
        super(yVals, label);
    }


    public int[] getGradientColors() {
        return gradientColors;
    }

    public void setGradientColors(int[] gradientColors) {
        this.gradientColors = gradientColors;
    }

    public float[] getGradientPosition() {
        return gradientPosition;
    }

    public void setGradientPosition(float[] gradientPosition) {
        this.gradientPosition = gradientPosition;
    }

    public int getTextureColor() {
        return textureColor;
    }

    public void setTextureColor(int textureColor) {
        this.textureColor = textureColor;
    }

    public boolean isSection() {
        return isSection;
    }

    public void setSection(boolean section) {
        isSection = section;
    }

    public boolean isDrawTexture() {
        return drawTexture;
    }

    public void setDrawTexture(boolean drawTexture) {
        this.drawTexture = drawTexture;
    }

    public List<Integer> getIntervalList() {
        return intervalList;
    }

    public void setIntervalList(List<Integer> intervalList) {
        this.intervalList = intervalList;
    }
}
