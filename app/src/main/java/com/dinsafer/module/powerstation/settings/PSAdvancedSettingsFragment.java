package com.dinsafer.module.powerstation.settings;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.BmtManager;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.HomeManager;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAdvancedSettingsBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.model.event.NeedGetAllDeviceEvent;
import com.dinsafer.module.iap.powercare.TrafficPackageProductSchedulesResponse;
import com.dinsafer.module.iap.powercare.TrafficPackageServiceCardItemModel;
import com.dinsafer.module.powerstation.PSConnectLoadingFragment;
import com.dinsafer.module.powerstation.PSFirmWareVersionFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.StatusConstant;
import com.dinsafer.module.powerstation.adapter.PSAdvancedSettingsAdapter;
import com.dinsafer.module.powerstation.bean.PSAdvanceInfoBean;
import com.dinsafer.module.powerstation.bean.PSAdvancedSettingsItemBean;
import com.dinsafer.module.powerstation.event.BmtChipsStatusReloadEvent;
import com.dinsafer.module.powerstation.event.PSUpdateSuccessEvent;
import com.dinsafer.module.powerstation.gridrewards.PSTerminateFragment;
import com.dinsafer.module.powerstation.settings.network.PSNetworkBleScanFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.BmtListFragment;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.MultiClickCounter;
import com.dinsafer.util.StringUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Func4;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;


/**
 * <AUTHOR> WZH
 * @date : 2022/11/29 22:56
 * @description :
 */
public class PSAdvancedSettingsFragment extends PSConnectLoadingFragment<FragmentAdvancedSettingsBinding> implements IDeviceCallBack {

    private PSAdvancedSettingsAdapter mNetworkInfoAdapter;
    private PSAdvancedSettingsAdapter mDeviceInfoAdapter;
    private PSAdvancedSettingsAdapter mInverterAdapter;
    private AlertDialogV2 mDeleteDialog;
    private Map<String, Object> params = new HashMap<>();
    private String subCategory;
    private String mVersion = "";
    private boolean hasDelete;
    private boolean isConnectWifi = false;
    private BehaviorSubject<Map<String, Object>> mAdvanceSubject = BehaviorSubject.create();
    private BehaviorSubject<Map<String, Object>> mSignalSubject = BehaviorSubject.create();
    private BehaviorSubject<Map<String, Object>> mMcuInfoSubject = BehaviorSubject.create();
    private BehaviorSubject<Map<String, Object>> mChipStatusSubject = BehaviorSubject.create();
    private Subscriber<PSAdvanceInfoBean> mSubscriber;
    private boolean isRequested;
    private String mMcuId;

    public static PSAdvancedSettingsFragment newInstance(String deviceId, String subcategory) {
        PSAdvancedSettingsFragment fragment = new PSAdvancedSettingsFragment();
        Bundle bundle = new Bundle();
        bundle.putString(PSKeyConstant.KEY_DEVICE_ID, deviceId);
        bundle.putString(PSKeyConstant.KEY_SUB_CATEGORY, subcategory);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_advanced_settings;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        initParams();
        mBinding.commonBar.commonBarTitle.setText(Local.s(getResources().getString(R.string.ps_advanced_settings)));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> {
            removeSelf();
        });
        mBinding.commonBar.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);
        mBinding.commonBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarRightIcon.setOnClickListener(v -> {
            showMenuDialog(Local.s(getString(R.string.ps_advanced_settings_delete)));
        });

        initRvNetworkInfo();
        initRvDeviceInfo();
        initRvInverter();
        initSubscriber();
        setLoadingVisible(true);
        getData();
        updateByDeviceOnlineStatus();
    }

    private void updateByDeviceOnlineStatus() {
        final boolean online = BmtUtil.isDeviceConnected(mPSDevice);
        DDLog.i(TAG, "updateByDeviceOnlineStatus: online: " + online);
        if (null != mDeviceInfoAdapter) {
            mDeviceInfoAdapter.getItem(2).setOnline(online);
        }
        if (null != mInverterAdapter) {
            mInverterAdapter.getItem(0).setOnline(online);
        }
    }


    private void getData() {
        submitCmd(DsCamCmd.GET_ADVANCE_INFO, 0);
        submitCmd(DsCamCmd.GET_COMMUNICATE_SIGNAL, 0);
        submitCmd(DsCamCmd.GET_MCU_INFO, 0);
        submitCmd(BmtCmd.GET_CHIPS_STATUS, 0);
    }

    private void initSubscriber() {
        mSubscriber = new Subscriber<PSAdvanceInfoBean>() {
            @Override
            public void onCompleted() {

            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(PSAdvanceInfoBean psAdvanceInfoBean) {
                if (isRequested) return;
                setLoadingVisible(false);
                if (psAdvanceInfoBean != null) {
                    isRequested = true;
                    if (mNetworkInfoAdapter != null) {
                        mNetworkInfoAdapter.getItem(0).setValue(psAdvanceInfoBean.getName());
                        mNetworkInfoAdapter.getItem(1).setValue(psAdvanceInfoBean.getSignal());
                        mNetworkInfoAdapter.getItem(2).setValue(psAdvanceInfoBean.getIp());
                        mNetworkInfoAdapter.getItem(3).setValue(psAdvanceInfoBean.getMac());
                        mNetworkInfoAdapter.getItem(4).setValue(psAdvanceInfoBean.getEthernetIp());
                        mNetworkInfoAdapter.getItem(5).setValue(psAdvanceInfoBean.getEthernetMac());
                        boolean isConnectWifi = psAdvanceInfoBean.isConnectWifi();
                        mNetworkInfoAdapter.getItem(1).setVisible(isConnectWifi);
                        mNetworkInfoAdapter.getItem(2).setVisible(isConnectWifi);
                        mNetworkInfoAdapter.getItem(3).setVisible(isConnectWifi);
                        boolean isConnectEthernet = psAdvanceInfoBean.isConnectEthernet();
                        mNetworkInfoAdapter.getItem(4).setVisible(isConnectEthernet);
                        mNetworkInfoAdapter.getItem(5).setVisible(isConnectEthernet);
                        mNetworkInfoAdapter.notifyDataSetChanged();
                    }
                    if (null != mDeviceInfoAdapter) {
                        mMcuId = psAdvanceInfoBean.getMcuId();
                        String deviceID = BmtUtil.isBmtDeviceHP5000(mPSDevice) ? psAdvanceInfoBean.getMcuId() : mDeviceId;
                        mDeviceInfoAdapter.getItem(0).setValue(deviceID);
//                        mDeviceInfoAdapter.getItem(0).setValue(mDeviceId);
//                        mDeviceInfoAdapter.getItem(1).setValue(psAdvanceInfoBean.getMcuId());
                        String iotVersion = psAdvanceInfoBean.getVersion();
                        mDeviceInfoAdapter.getItem(2).setValue(iotVersion);
                        mDeviceInfoAdapter.getItem(2).setShowDot(psAdvanceInfoBean.isShowDot());
                        mDeviceInfoAdapter.notifyDataSetChanged();
                    }
                    mBinding.llParent.setVisibility(View.VISIBLE);
                    mAdvanceSubject.onNext(null);
                    mSignalSubject.onNext(null);
                    mMcuInfoSubject.onNext(null);
                    mChipStatusSubject.onNext(null);
                } else {
//                    showErrorToast();
                }
            }
        };
        Observable.combineLatest(mAdvanceSubject, mSignalSubject, mMcuInfoSubject, mChipStatusSubject,
                        new Func4<Map<String, Object>, Map<String, Object>, Map<String, Object>, Map<String, Object>, PSAdvanceInfoBean>() {
                            @Override
                            public PSAdvanceInfoBean call(Map<String, Object> advanceSubjectMap,
                                                          Map<String, Object> signalSubjectMap,
                                                          Map<String, Object> mcuInfoSubjectMap,
                                                          Map<String, Object> mChipStatusSubjectMap) {
                                if (advanceSubjectMap == null || advanceSubjectMap.size() < 1 ||
                                        signalSubjectMap == null || signalSubjectMap.size() < 1 ||
                                        mcuInfoSubjectMap == null || mcuInfoSubjectMap.size() < 1 ||
                                        mcuInfoSubjectMap == null || mcuInfoSubjectMap.size() < 1)
                                    return null;
                                String currentNetwork = DeviceHelper.getString(advanceSubjectMap, PSKeyConstant.WIFI_NAME, "");
                                String ip = DeviceHelper.getString(advanceSubjectMap, PSKeyConstant.IP, "");
                                String mac = DeviceHelper.getString(advanceSubjectMap, PSKeyConstant.MAC, "");
                                final String iotVersion = DeviceHelper.getString(advanceSubjectMap, BmtDataKey.VERSION, "");
                                int wifi = DeviceHelper.getInt(signalSubjectMap, PSKeyConstant.WIFI, -1);
                                int ethernet = DeviceHelper.getInt(signalSubjectMap, PSKeyConstant.ETHERNET, 0);
                                String ethernetIp = DeviceHelper.getString(advanceSubjectMap, BmtDataKey.ETHERNET_IP, "");
                                String ethernetMac = DeviceHelper.getString(advanceSubjectMap, BmtDataKey.ETHERNET_MAC, "");

                                isConnectWifi = wifi > 1;
                                int cellular = DeviceHelper.getInt(signalSubjectMap, PSKeyConstant.CELLULAR, -1);
                                String idInfo = (String) MapUtils.get(mcuInfoSubjectMap, PSKeyConstant.ID_INFO, null);
                                String barcode = (String) MapUtils.get(mcuInfoSubjectMap, PSKeyConstant.BAR_CODE, null);
                                final int chipsStatus = DeviceHelper.getInt(mChipStatusSubjectMap, BmtDataKey.STATUS, -1);
                                final boolean showDot = BmtUtil.isNeedShowUpgradeDot(chipsStatus);
                                if (!isConnectWifi) {
                                    currentNetwork = cellular > 1 ? getResources().getString(R.string.ap_step_wifi_connect_result_4g) : "";
                                }
                                if (ethernet > 1) {
                                    currentNetwork = isConnectWifi ? (getResources().getString(R.string.ap_step_wifi_connect_result_ethernet)
                                            + "/" + currentNetwork) : getResources().getString(R.string.ap_step_wifi_connect_result_ethernet);
                                }
                                int rssi = DeviceHelper.getInt(signalSubjectMap, BmtDataKey.WIF_RSSI, -1);
                                String signal = getWifiSignalStr(wifi) + "(" + -rssi + ")";
                                PSAdvanceInfoBean advanceInfoBean = new PSAdvanceInfoBean(currentNetwork,
                                        signal, ip, mac, mDeviceId, barcode, iotVersion,
                                        isConnectWifi, showDot, ethernetIp, ethernetMac);
                                return advanceInfoBean;
                            }
                        }).subscribeOn(Schedulers.io())
                .compose(bindToLifecycle())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mSubscriber);
    }

    @Override
    public boolean onBackPressed() {
        return super.onBackPressed();
    }

    @Override
    public void onDestroyView() {
        if (mPSDevice != null) {
            mPSDevice.unregisterDeviceCallBack(this);
        }
        if (mSubscriber != null) {
            mSubscriber.unsubscribe();
        }
        super.onDestroyView();
    }

    @Override
    protected void initParams() {
        super.initParams();
        if (null != mPSDevice) {
            mPSDevice.registerDeviceCallBack(this);
            subCategory = mPSDevice.getSubCategory();
        }
    }

    private void setLoadingVisible(boolean visible) {
        if (visible) {
            showLoadingFragment(0);
        } else {
            closeLoadingFragment();
        }
    }

    private void submitCmd(String cmd, int index) {
        if (mPSDevice != null) {
            params.clear();
            params.put(PSKeyConstant.CMD, cmd);
            switch (cmd) {
                case DsCamCmd.GET_INVERTER_INFO:
                    params.put(PSKeyConstant.INDEX, index);
                    break;

            }
            mPSDevice.submit(params);
        }
    }

    private void showMenuDialog(String otherBtnTxt) {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.ps_advanced_settings_cancel)))
                .setOtherButtonTitles(otherBtnTxt)
                .setLastButtonTextColor(getResources().getColor(R.color.ps_advanced_settings_delete_color))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);
                        if (otherBtnTxt.equals(Local.s(getString(R.string.ps_advanced_settings_delete)))) {
                            checkExpired();
                        } else if (otherBtnTxt.equals(Local.s(getString(R.string.power_restart_inverter)))) {
                            showTimeOutLoadinFramgment();
                            BmtManager.getInstance().resetInverter(mPSDevice, false);
                        }
                    }
                }).show();
    }

    private void checkExpired() {
        showTimeOutLoadinFramgment();
        DinsafeAPI.getApi().bmtListProductSchedules(mDeviceId).enqueue(new Callback<TrafficPackageProductSchedulesResponse>() {
            @Override
            public void onResponse(Call<TrafficPackageProductSchedulesResponse> call, Response<TrafficPackageProductSchedulesResponse> response) {
                closeLoadingFragment();
                if (response != null && response.body() != null) {
                    List<TrafficPackageServiceCardItemModel> productSchedules = response.body().getResult().getList();
                    if (CollectionUtil.isListNotEmpty(productSchedules)) {
                        TrafficPackageServiceCardItemModel itemModel = productSchedules.get(0);
                        int status = itemModel.getStatus();
                        if (status == 0 || status == 1) {
                            showAbandonmentTrafficPackageDialog();
                        } else {
                            checkContract();
                        }
                    } else {
                        checkContract();
                    }
                } else {
                    showErrorToast();
                }
            }

            @Override
            public void onFailure(Call<TrafficPackageProductSchedulesResponse> call, Throwable t) {
                closeLoadingFragment();
                showErrorToast();
            }
        });
    }

    /**
     * 检查设备是否签约状态
     */
    private void checkContract() {
        if (BmtManager.getInstance().getNotDeletedBmtDeviceList().size() > 1) {
            showDeleteDialog();
            return;
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        DinHome.getInstance().getFamilyBalanceContractInfo(HomeManager.getInstance().getCurrentHome().getHomeID(), new IDefaultCallBack2<FamilyBalanceContractInfoResponse.ResultBean>() {
            @Override
            public void onSuccess(FamilyBalanceContractInfoResponse.ResultBean resultBean) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (null != resultBean) {
                    if (resultBean.isSigning()) {
                        showTerminateContractDialog();
                    } else {
                        showDeleteDialog();
                    }
                } else {
                    showErrorToast();
                }
            }

            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
            }
        });
    }

    /**
     * 提示解约
     */
    private void showTerminateContractDialog() {
        AlertDialogV2.createBuilder(getActivity())
                .setContent(Local.s(getString(R.string.terminate_del_tip)))
                .setOk(Local.s(getString(R.string.got_it)))
                .setCancel(Local.s(getString(R.string.terminate_contract)))
                .setCancelListener(() -> getDelegateActivity().addCommonFragment(PSTerminateFragment.newInstanceFromSetting(
                        HomeManager.getInstance().getCurrentHome().getHomeName()
                        , HomeManager.getInstance().getCurrentHome().getHomeID()
                )))
                .preBuilder()
                .show();
    }

    /**
     * 没过期弹窗
     */
    private void showAbandonmentTrafficPackageDialog() {
        String content = Local.s(this.getResources().getString(R.string.delete_bmt_tips))
                .replace(this.getResources().getString(R.string.hashtag_service),
                        Local.s(getString(R.string.iap_4g_traffic_package)));
        AlertDialogV2 alertDialogV2 = AlertDialogV2.createBuilder(getActivity())
                .setContent(content)
                .setOk(this.getResources().getString(R.string.cancel))
                .setCancel(this.getResources().getString(R.string.delete))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(Dialog dialog) {
                        dialog.dismiss();
                    }
                })

                .preBuilder();
        alertDialogV2.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialogV2.dismiss();
                if (null == mPSDevice) {
                    showErrorToast();
                    return;
                }
                checkContract();

            }
        });
        alertDialogV2.show();
    }

    /**
     * 删除警告弹窗
     */
    private void showDeleteDialog() {
        if (mDeleteDialog == null) {
            mDeleteDialog = AlertDialogV2.createBuilder(getContext())
                    .setContent(Local.s(this.getResources().getString(R.string.delete_the_panel_confirm)))
                    .setOk(Local.s(this.getResources().getString(R.string.Yes)))
                    .setCancel(Local.s(this.getResources().getString(R.string.ps_advanced_settings_cancel)))
                    .setOKListener(new AlertDialogV2.AlertOkClickCallback() {  // cancel
                        @Override
                        public void onOkClick() {
                            if (null == mPSDevice) {
                                showErrorToast();
                                return;
                            }
                            showTimeOutLoadinFramgmentWithErrorAlert();
                            deleteOrResetBmt();
                        }
                    })
                    .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {  // transfer
                        @Override
                        public void onOkClick() {

                        }
                    })
                    .setCancelListener(new AlertDialogV2.AlertCancelClickCallback() {  // Delete

                        @Override
                        public void onClick() {

                        }
                    })
                    .preBuilder();
        }
        if (mDeleteDialog != null && !mDeleteDialog.isShowing()) {
            mDeleteDialog.show();
        }
    }

    private void deleteOrResetBmt() {
        if (BmtUtil.isDeviceConnected(mPSDevice)) {
            resetDevice();
        } else {
            deleteDevice();
        }
    }

    private void resetDevice() {
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DinConst.CMD_RESET_DEVICE);
        mPSDevice.submit(data);
    }

    private synchronized void deleteDevice() {
        //  为什么要这样子处理，因为调用reset的时候，BMT有可能返回多次reset成功给我们，那么就会调用多次删除设备
        if (hasDelete) {
            MsctLog.i(TAG, "已经删除过BMT了，不需要在删除了");
            return;
        }
        hasDelete = true;
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DinConst.CMD_DELETE);
        mPSDevice.submit(data);
    }

    /**
     * 网络信息
     */
    private void initRvNetworkInfo() {
        mBinding.rvNetwork.setLayoutManager(new LinearLayoutManager(getContext()));
        mNetworkInfoAdapter = new PSAdvancedSettingsAdapter();
        mBinding.rvNetwork.setAdapter(mNetworkInfoAdapter);
        List<PSAdvancedSettingsItemBean> data = new ArrayList<>();

        data.add(new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_current_network), "", true));
        PSAdvancedSettingsItemBean wifiSignalItem = new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_wifi_signal), "");
        wifiSignalItem.setVisible(false);
        data.add(wifiSignalItem);
        PSAdvancedSettingsItemBean wifiIpAddressItem = new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_wifi_ip_address), "", true);
        wifiIpAddressItem.setVisible(false);
        data.add(wifiIpAddressItem);
        PSAdvancedSettingsItemBean wifiMacItem = new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_wifi_mac_address), "", true);
        wifiMacItem.setVisible(false);
        data.add(wifiMacItem);
        PSAdvancedSettingsItemBean ethernetIpItem = new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_ethernet_ip_address), "", true);
        ethernetIpItem.setVisible(false);
        data.add(ethernetIpItem);
        PSAdvancedSettingsItemBean ethernetMacItem = new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_ethernet_mac_address), "", true);
        ethernetMacItem.setVisible(false);
        data.add(ethernetMacItem);
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_change_network), R.drawable.btn_device_setting_arrow));
        mNetworkInfoAdapter.setNewData(data);
        mNetworkInfoAdapter.setOnItemClickListener((adapter, view, position) -> {
            PSAdvancedSettingsItemBean itemBean = mNetworkInfoAdapter.getItem(position);
            if (itemBean.getKey().equals(getString(R.string.ps_advanced_settings_change_network))) {
                getDelegateActivity().addCommonFragment(PSNetworkBleScanFragment.newInstance(mDeviceId, mSubcategory));
            }
        });
    }

    /**
     * 设备信息
     */
    private void initRvDeviceInfo() {
        MultiClickCounter multiClickCounter = new MultiClickCounter(5, 2000);
        multiClickCounter.setListener(() -> {
            BindInverterActivity.startScan(getMainActivity(), mDeviceId, mSubcategory, mDeviceInfoAdapter.getItem(0).getValue());
        });

        mBinding.rvDevice.setLayoutManager(new LinearLayoutManager(getContext()));
        mDeviceInfoAdapter = new PSAdvancedSettingsAdapter();
        mBinding.rvDevice.setAdapter(mDeviceInfoAdapter);
        List<PSAdvancedSettingsItemBean> data = new ArrayList<>();
        String defaultId = !BmtUtil.isBmtDeviceHP5000(mPSDevice) && BmtUtil.isDeviceOffline(mPSDevice) ? mDeviceId : "";
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_device_id), defaultId, true));
//        data.add(new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_mcu_id), ""));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.product_name), BmtUtil.getDeviceDefaultName(mPSDevice)));
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.ps_advanced_settings_firmware_version), R.drawable.btn_device_setting_arrow));
        mDeviceInfoAdapter.setNewData(data);

        mDeviceInfoAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            PSAdvancedSettingsItemBean itemBean = mDeviceInfoAdapter.getItem(position);
            if (itemBean.getKey().equals(getString(R.string.ps_advanced_settings_device_id))) {
                multiClickCounter.click();
            }
        });
        mDeviceInfoAdapter.setOnItemClickListener((adapter, view, position) -> {
            PSAdvancedSettingsItemBean itemBean = mDeviceInfoAdapter.getItem(position);
            final boolean online = BmtUtil.isDeviceConnected(mPSDevice);
            if (itemBean.getKey().equals(getString(R.string.ps_advanced_settings_device_id))) {
                multiClickCounter.click();
            } else if (itemBean.getKey().equals(getString(R.string.ps_advanced_settings_firmware_version)) && online) {
                getDelegateActivity().addCommonFragment(PSFirmWareVersionFragment.newInstance(mDeviceId, mSubcategory, mMcuId));
            }
        });
    }

    private void initRvInverter() {
        mBinding.rvInverter.setLayoutManager(new LinearLayoutManager(getContext()));
        mInverterAdapter = new PSAdvancedSettingsAdapter();
        mBinding.rvInverter.setAdapter(mInverterAdapter);
        List<PSAdvancedSettingsItemBean> data = new ArrayList<>();
        data.add(new PSAdvancedSettingsItemBean(getString(R.string.power_restart_inverter), R.drawable.btn_device_setting_arrow));
        mInverterAdapter.setNewData(data);
        mInverterAdapter.setOnItemClickListener((adapter, view, position) -> {
            PSAdvancedSettingsItemBean itemBean = mInverterAdapter.getItem(position);
            final boolean online = BmtUtil.isDeviceConnected(mPSDevice);
            if (itemBean.getKey().equals(getString(R.string.power_restart_inverter)) && online) {
                showMenuDialog(Local.s(getString(R.string.power_restart_inverter)));
            }
        });
    }

    private String getWifiSignalStr(int wifi) {
        String wifiSignal = "";
        switch (wifi) {
            case 1:
                wifiSignal = getString(R.string.ps_wifi_signal_none);
                break;

            case 2:
                wifiSignal = getString(R.string.ps_wifi_signal_weak);
                break;

            case 3:
                wifiSignal = getString(R.string.ps_wifi_signal_medium);
                break;

            case 4:
                wifiSignal = getString(R.string.ps_wifi_signal_strong);
                break;

            default:
                wifiSignal = "";
                break;
        }
        return Local.s(wifiSignal);
    }

    @Subscribe
    public void onEvent(BmtChipsStatusReloadEvent ev) {
        final String deviceId = ev.getDeviceId();
        if (TextUtils.isEmpty(deviceId) || !deviceId.equals(mDeviceId)) {
            return;
        }

        //
        // submitCmd(BmtCmd.GET_CHIPS_STATUS, 0);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMainThreadEvent(PSUpdateSuccessEvent event) {
        if (null != mDeviceInfoAdapter && mDeviceInfoAdapter.getData() != null &&
                mDeviceInfoAdapter.getData().size() > 2) {
            mDeviceInfoAdapter.getItem(2).setShowDot(false);
            mDeviceInfoAdapter.notifyItemChanged(2);
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {

        Log.v(TAG, "onCmdCallBack, bmtDevice===setting: " + deviceId +
                " /cmd:" + cmd
                + " /result:" + map.toString()
                + " /" + Thread.currentThread().getName());

        if (StringUtil.isNotEmpty(deviceId)
                && StringUtil.isNotEmpty(mDeviceId)
                && mPSDevice != null
                && subCategory.equals(mPSDevice.getSubCategory())
                && deviceId.equals(mDeviceId)) {
            runOnMainThread(() -> {
                if (!getDelegateActivity().isCommonFragmentExist(PSAdvancedSettingsFragment.class.getName())) {
                    return;
                }

                Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
                int status = (int) MapUtils.get(map, PSKeyConstant.STATUS, -1);
                if (cmd.equals(DinConst.CMD_DELETE)) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    runOnMainThread(new Runnable() {
                        @Override
                        public void run() {
                            if (status == StatusConstant.STATUS_SUCCESS) {
                                if (ActivityController.getInstance().getFragment(BmtListFragment.class) != null) {
                                    getDelegateActivity().removeToFragment(BmtListFragment.class.getName());
                                } else {
                                    getDelegateActivity().removeAllCommonFragment();
                                }
                                BmtManager.getInstance().checkResetNotLoadCountries();
                            } else {
                                int originStatus = DeviceHelper.getInt(result, "originStatus", -1);
                                if (originStatus == -77) {
                                    AlertDialog.createBuilder(getActivity())
                                            .setOk(Local.s(getResources().getString(R.string.ok)))
                                            .setContent(Local.s(getResources().getString(R.string.use_the_fol)))
                                            .setOKListener(() -> {
                                                getDelegateActivity().removeAllCommonFragment();
                                                BmtManager.getInstance().removeDeletedDevice(mDeviceId, mSubcategory);
                                                EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
                                            }).preBuilder().show();
                                } else {
                                    showErrorToast();
                                }
                            }
                        }
                    });
                } else if (DinConst.CMD_RESET_DEVICE.equals(cmd)) {
                    if (status != StatusConstant.STATUS_SUCCESS) {
                        hasDelete = false;
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                    }
                } else {
                    runOnMainThread(new Runnable() {
                        @Override
                        public void run() {
                            if (status == StatusConstant.STATUS_SUCCESS) {
                                if (result != null && result.size() > 0) {
                                    switch (cmd) {
                                        case DsCamCmd.GET_ADVANCE_INFO:
//                                            String wifiName = DeviceHelper.getString(result, PSKeyConstant.WIFI_NAME, "");
//                                            String ip = DeviceHelper.getString(result, PSKeyConstant.IP, "");
//                                            String mac = DeviceHelper.getString(result, PSKeyConstant.MAC, "");
//                                            final String iotVersion = DeviceHelper.getString(result, BmtDataKey.VERSION, "");
//                                            isConnectWifi = !TextUtils.isEmpty(ip);
//                                            if (mNetworkInfoAdapter != null) {
//                                                if (isConnectWifi) {
//                                                    mNetworkInfoAdapter.getItem(0).setValue(wifiName);
//                                                    mNetworkInfoAdapter.getItem(2).setValue(ip);
//                                                    mNetworkInfoAdapter.getItem(3).setValue(mac);
//                                                }
//                                                mNetworkInfoAdapter.getItem(2).setVisible(isConnectWifi);
//                                                mNetworkInfoAdapter.getItem(3).setVisible(isConnectWifi);
//                                                mNetworkInfoAdapter.notifyDataSetChanged();
//                                            }
//                                            if (null != mDeviceInfoAdapter) {
//                                                mDeviceInfoAdapter.getItem(2).setValue(iotVersion);
//                                                mDeviceInfoAdapter.notifyItemChanged(2);
//                                            }
                                            mAdvanceSubject.onNext(result);
                                            break;

                                        case DsCamCmd.GET_COMMUNICATE_SIGNAL:
//                                            if (isConnectWifi) {
//                                                int wifi = DeviceHelper.getInt(result, PSKeyConstant.WIFI, -1);
//                                                if (mNetworkInfoAdapter != null) {
//                                                    mNetworkInfoAdapter.getItem(1).setValue(getWifiSignalStr(wifi));
//                                                    mNetworkInfoAdapter.getItem(1).setVisible(isConnectWifi);
//                                                    mNetworkInfoAdapter.notifyItemChanged(1);
//                                                }
//                                            } else {
//                                                int cellular = DeviceHelper.getInt(result, PSKeyConstant.CELLULAR, -1);
//                                                if (mNetworkInfoAdapter != null) {
//                                                    mNetworkInfoAdapter.getItem(0).setValue(cellular > 1 ?
//                                                            getResources().getString(R.string.ap_step_wifi_connect_result_4g) : "");
//                                                    mNetworkInfoAdapter.notifyItemChanged(0);
//                                                }
//                                            }
                                            mSignalSubject.onNext(result);
                                            break;

                                        case DsCamCmd.GET_MCU_INFO:
//                                            String idInfo = (String) MapUtils.get(result, PSKeyConstant.ID_INFO, null);
//                                            String barcode = (String) MapUtils.get(result, PSKeyConstant.BAR_CODE, null);
//                                            if (mDeviceInfoAdapter != null) {
//                                                mDeviceInfoAdapter.getItem(1).setValue(barcode);
//                                                mDeviceInfoAdapter.notifyItemChanged(1);
//                                            }
                                            mMcuInfoSubject.onNext(result);
                                            break;
                                        case BmtCmd.GET_CHIPS_STATUS:
//                                            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
//                                            final boolean showDot = BmtUtil.isNeedShowUpgradeMarker(chipsStatus);
//                                            if (mDeviceInfoAdapter != null) {
//                                                mDeviceInfoAdapter.getItem(2).setShowDot(showDot);
//                                                mDeviceInfoAdapter.notifyItemChanged(2);
//                                            }
                                            final int chipsStatus = DeviceHelper.getInt(result, BmtDataKey.STATUS, -1);
                                            DDLog.i("XXX-4", String.valueOf(chipsStatus));
                                            mChipStatusSubject.onNext(result);
                                            break;

                                        case BmtCmd.RESET_INVERTER:
                                            closeTimeOutLoadinFramgmentWithErrorAlert();
                                            getMainActivity().showTopToast(R.drawable.icon_toast_succeed, getString(R.string.success));
                                            break;
                                    }
                                }
                            } else {
                                switch (cmd) {
                                    case DsCamCmd.GET_ADVANCE_INFO:
                                        mAdvanceSubject.onNext(result);
                                        break;
                                    case DsCamCmd.GET_COMMUNICATE_SIGNAL:
                                        mSignalSubject.onNext(result);
                                        break;
                                    case DsCamCmd.GET_MCU_INFO:
                                        mMcuInfoSubject.onNext(result);
                                        break;
                                    case BmtCmd.GET_CHIPS_STATUS:
                                        mChipStatusSubject.onNext(result);
                                        break;

                                    case BmtCmd.RESET_INVERTER:
                                        closeTimeOutLoadinFramgmentWithErrorAlert();
                                        showErrorToast();
                                        break;
                                }
                            }
                        }
                    });

                }
            });
        }
    }
}
