package com.dinsafer.module.powerstation.electricity.manager;

import android.content.Context;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.bean.SectionLineDataSet;
import com.dinsafer.module.powerstation.electricity.chart.SectionLineChart;
import com.dinsafer.module.powerstation.electricity.chart.render.SectionLineChartRender;
import com.dinsafer.util.CollectionUtil;
import com.github.mikephil.charting.components.MarkerView;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.FillFormatter;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.utils.ViewPortHandler;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


public class LineChartManager {
    private Context mContext;
    private SectionLineChart mLineChart;
    private int mInterval = 1;
    private FillFormatter fillFormatter = (dataSet, dataProvider) -> 0;
    public LineChartManager(Context context, SectionLineChart lineChart) {
        this.mContext = context;
        this.mLineChart = lineChart;
    }

    public void initChart(int interval, List<List<Float>> data, boolean isBSensorInstall, int hourCount) {
        mInterval= interval;
        XAxisValueFormatter formatter = new XAxisValueFormatter() {
            @Override
            public String getXValue(String original, int index, ViewPortHandler viewPortHandler) {
                if ((index * interval) % 720 == 0) {
                    int hour = (index * interval) / 60;
                    String text = hour < 10 ? ("0" + hour) + ":00" : hour + ":00";
                    return text;
                }
                return "";
            }
        };
        initChart(true, 0.5f, mContext.getResources().getColor(R.color.color_white_04),
                false, false, false,
                0f, 0f, 0f, 15f, 0f,
                false, "", 1200,
                true, false, mContext.getResources().getColor(R.color.color_white_04),
                10, true, mContext.getResources().getColor(R.color.color_white_03),
                0.5f, XAxis.XAxisPosition.BOTTOM, 359, formatter,
                false, 0f, mContext.getResources().getColor(R.color.color_white_04), 0.5f, false, true,
                mContext.getResources().getColor(R.color.color_white_04), 5000f,
                0f, false, null, null, data, isBSensorInstall, hourCount);
    }

    public void initChart(boolean isDrawBorders, float borderWidth, int borderColor,
                          boolean isHighlightPerDragEnabled, boolean isHighlightFullBarEnabled,
                          boolean isScaleEnabled, float leftOffsets, float topOffsets,
                          float rightOffsets, float bottomOffsets, float minOffset,
                          boolean isLegendEnabled, String description, int animateXDurationMillis,
                          boolean isDrawXGridLines, boolean isDrawXAxisLine, int xGridColor,
                          float labelTextSize, boolean isAvoidFirstLastClipping, int labelTextColor,
                          float xAxisLineWidth, XAxis.XAxisPosition xAxisPosition, int labelsToSkip,
                          XAxisValueFormatter xFormatter, boolean isDrawYAxisLabel, float yLabelTextSize,
                          int yLabelTextColor, float axisLineWidth, boolean isDrawAxisLine,
                          boolean isDrawYGridLines, int yGridColor, float yAxisMaxValue, float yAxisMinValue,
                          boolean isAxisRightEnabled, YAxisValueFormatter yFormatter, MarkerView markerView,
                          List<List<Float>> data, boolean isBSensorInstall, int hourCount) {
        mLineChart.setDrawBorders(isDrawBorders);
        mLineChart.setBorderWidth(borderWidth);
        mLineChart.setBorderColor(borderColor);
        mLineChart.setHighlightPerDragEnabled(isHighlightPerDragEnabled);
        mLineChart.setHighlightFullBarEnabled(isHighlightFullBarEnabled);
        mLineChart.setScaleEnabled(isScaleEnabled);
        //设置图表距离上下左右的距离
        mLineChart.setExtraOffsets(leftOffsets, topOffsets, rightOffsets, bottomOffsets);
        mLineChart.setMinOffset(minOffset);
        //图例
        mLineChart.getLegend().setEnabled(isLegendEnabled);
        mLineChart.setDescription(description);

        mLineChart.animateX(animateXDurationMillis);
        mLineChart.setNeedNoDataGrid(false);
        //获取X轴
        XAxis xAxis = mLineChart.getXAxis();
        //将垂直于X轴的网格线隐藏，将X轴显示
        xAxis.setDrawGridLines(isDrawXGridLines);
        xAxis.setDrawAxisLine(isDrawXAxisLine);
        xAxis.setGridColor(xGridColor);
        //设置X轴上lable颜色和大小
        xAxis.setTextSize(labelTextSize);
        xAxis.setAvoidFirstLastClipping(isAvoidFirstLastClipping);
        xAxis.setTextColor(labelTextColor);
        //设置X轴高度
        xAxis.setAxisLineWidth(xAxisLineWidth);
        //x轴刻度值的位置
        xAxis.setPosition(xAxisPosition);
//        设置在”绘制下一个标签”时，要忽略的标签数。
        xAxis.setLabelsToSkip(hourCount / mInterval / 4 - 1);
        xAxis.setValueFormatter(xFormatter);
        //获取左侧侧坐标轴
        YAxis yAxis = mLineChart.getAxisLeft();
        //设置是否显示Y轴的值
        yAxis.setDrawLabels(isDrawYAxisLabel);
        yAxis.setTextSize(yLabelTextSize);
        yAxis.setTextColor(yLabelTextColor);
        yAxis.setAxisLineWidth(axisLineWidth);
        //是否绘制坐标轴
        yAxis.setDrawAxisLine(isDrawAxisLine);
        //设置所有垂直Y轴的的网格线是否显示
        yAxis.setDrawGridLines(isDrawYGridLines);
        yAxis.setLabelCount(6, true);
        yAxis.setGridColor(yGridColor);
        float yMax = getYMax(data, isBSensorInstall);
        yAxis.setAxisMaxValue(ChartDataUtil.findUpperLimit(yMax, true));
        yAxis.setAxisMinValue(0);
        yAxis.setValueFormatter(yFormatter);

        if (mLineChart.getRenderer() instanceof SectionLineChartRender) {
            SectionLineChartRender renderer = (SectionLineChartRender) mLineChart.getRenderer();
            renderer.setOutCircleColor(mContext.getResources().getColor(R.color.electricity_hig_light_circle_out_color));
        }
        if (markerView != null) {
            mLineChart.setMarkerView(markerView);
        }
        mLineChart.getAxisRight().setEnabled(isAxisRightEnabled);
    }

    public void setData(List<List<Float>> data, boolean isHighlightEnabled, boolean isBSensorInstall, int hourCount, boolean isSuccess) {

        int count = hourCount /mInterval  + 1;
        ArrayList<Entry> entries = new ArrayList<Entry>();
        for (int i = 0; i < count; i++) {
            entries.add(new Entry(0, i));
        }
        for (List<Float> sonData : data) {
            int offset = Math.round(Math.abs(sonData.get(0))) / mInterval;
            float val = 0f;
            if (isBSensorInstall) {
                val = sonData.get(1) + sonData.get(2) + sonData.get(3);
            } else {
                val = sonData.get(1) + sonData.get(3);
            }

            if (offset>=0) {
//                entries.add(new Entry(val, offset));
                entries.get(offset).setVal(val);
            }
        }

        SectionLineDataSet dayDataSet = new SectionLineDataSet(entries, null);
        dayDataSet.setLineWidth(0.5f);
        dayDataSet.setValueTextSize(15f);
        dayDataSet.setMode(LineDataSet.Mode.LINEAR);
        dayDataSet.setFillFormatter(fillFormatter);
        dayDataSet.setHighlightEnabled(isHighlightEnabled);
        dayDataSet.setDrawCircles(false);
        dayDataSet.setDrawValues(false);
        dayDataSet.setDrawFilled(true);
        dayDataSet.setDrawHorizontalHighlightIndicator(false);
        dayDataSet.setHighLightColor(mContext.getResources().getColor(R.color.color_white_03));
        dayDataSet.setHighlightLineWidth(1f);
        dayDataSet.setDrawCircleHole(false);
        dayDataSet.setFillAlpha(60);
        float yMax = getYMax(data, isBSensorInstall);
        float[] pos = ChartDataUtil.getPositions(ChartDataUtil.findUpperLimit(yMax, true));
        int[] colors;
        if (isSuccess) {
            colors = ChartDataUtil.getLineGradientColor(mContext, ChartDataUtil.findUpperLimit(yMax, true), false);
        } else {
            colors = new int[]{mContext.getResources().getColor(R.color.transparent), mContext.getResources().getColor(R.color.transparent)
                    , mContext.getResources().getColor(R.color.transparent)};
        }
        dayDataSet.setGradientColors(colors);
        dayDataSet.setGradientPosition(pos);
        dayDataSet.setSection(true);

        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            String text = i < 10 ? ("0" + i) + ":00" : i + ":00";
            xVals.add(text);
        }
        List<ILineDataSet> lineDataSets = new ArrayList<>();
        lineDataSets.add(dayDataSet);
        LineData lineData = new LineData(xVals, lineDataSets);
        mLineChart.setData(lineData);

        mLineChart.invalidate();
    }

    private List<Entry> generateData() {
        List<Entry> dataList = new ArrayList<>(1441);
        for (int i = 0; i < 50; i++) {
            dataList.add(new Entry(200, i));
        }
        for (int i = 50; i < 100; i++) {
            dataList.add(new Entry(100, i));
        }

        for (int i = 100; i < 110; i++) {
            dataList.add(new Entry(200 + i, i));
        }

        for (int i = 110; i < 150; i++) {
            dataList.add(new Entry(1200, i));
        }
        for (int i = 150; i < 160; i++) {
            dataList.add(new Entry(1200 + i, i));
        }
        for (int i = 160; i < 600; i++) {
            dataList.add(new Entry(1200 + i, i));
        }

        for (int i = 600; i < 800; i++) {
            float val = (float) (Math.random() * 500);
            dataList.add(new Entry(val, i));
        }

        for (int i = 800; i < 1441; i++) {
            dataList.add(new Entry(2000 + i, i));
        }
        return dataList;
    }

    public float getYMax(List<List<Float>> data, boolean isBSensorInstall) {
        float maxVal = 0f;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            List<Float> sonData = data.get(i);
            float sum = 0;
            if (CollectionUtil.isListNotEmpty(sonData)) {
                if (isBSensorInstall) {
                    sum = sonData.get(1) + sonData.get(2) + sonData.get(3);
                } else {
                    sum = sonData.get(1) + sonData.get(3);
                }
            }
            sumData.add(sum);
        }
        if (CollectionUtil.isListNotEmpty(data)) {
            Collections.sort(sumData);
            maxVal = sumData.get(sumData.size() - 1);
        }
        return maxVal;
    }
}

