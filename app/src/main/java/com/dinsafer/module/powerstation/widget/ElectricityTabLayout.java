package com.dinsafer.module.powerstation.widget;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutElectricityTabBinding;
import com.dinsafer.module.powerstation.electricity.BaseChartFragment;
import com.dinsafer.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

public class ElectricityTabLayout extends ConstraintLayout {

    private Context mContext;
    private List<ImageView> imgList;
    private LayoutElectricityTabBinding mBinding;
    private int[] bgRes = {R.drawable.shape_electricity_usage_bg_circle,
            R.drawable.shape_electricity_battery_bg_circle, R.drawable.shape_electricity_sun_bg_circle,
            R.drawable.shape_electricity_city_bg_circle};

    public ElectricityTabLayout(Context context) {
        this(context, null);
    }

    public ElectricityTabLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ElectricityTabLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        init(context);
    }

    private void init(Context context) {
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_electricity_tab, this, true);
        setIndicatorBackgroundColor(mContext.getResources().getColor(R.color.electricity_bg_color));
        imgList = new ArrayList<>();
        imgList.add(mBinding.ivUsage);
        imgList.add(mBinding.ivBattery);
        imgList.add(mBinding.ivSolar);
        imgList.add(mBinding.ivGrid);

        for (int i = 0; i < imgList.size(); i++) {
            ImageView imageView = imgList.get(i);
            int finalI = i;
            imageView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    setTabSelected(finalI, true);
                }
            });
        }
    }

    public void setFirstPosition(int index) {
        if (CollectionUtil.isListEmpty(imgList) || index < 0 || index > imgList.size()) return;
        mBinding.viewIndicator.setBackgroundResource(bgRes[index]);
        mBinding.flIndicator.setVisibility(VISIBLE);
        setTabSelected(index, true);
    }

    private void clearSelectedImageView() {
        for (int i = 0; i < imgList.size(); i++) {
            imgList.get(i).setSelected(false);
        }
    }

    private float getLastSelectedX() {
        for (ImageView imageView : imgList) {
            if (imageView.isSelected()) {
                return imageView.getX();
            }
        }
        return 0;
    }

    public void setTabSelected(int index, boolean isSelf) {
        if (CollectionUtil.isListEmpty(imgList) || index < 0 || index > imgList.size()) return;
        ImageView imageView = imgList.get(index);
        if (imageView.isSelected()) return;
        float lastSelectedPosition = getLastSelectedX();
        float nowSelectedPosition = imageView.getX();
        float[] x = {lastSelectedPosition, nowSelectedPosition};
        ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(
                mBinding.flIndicator,
                "translationX",
                x
        );
        objectAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
                if (isSelf && tabSelectedListener != null) {
                    tabSelectedListener.onTabSelected(index);
                }
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                imageView.setSelected(true);
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        objectAnimator.setDuration(200);
        clearSelectedImageView();
        mBinding.viewIndicator.setBackgroundResource(bgRes[index]);
        objectAnimator.start();
    }

    public void setIndicatorBackgroundColor(int color) {
        Drawable drawable = mContext.getResources()
                .getDrawable(R.drawable.bg_tab_sel);
        drawable.setTint(color);
        mBinding.flIndicator.setBackground(drawable);
    }

    private OnTabSelectedListener tabSelectedListener;

    public void setTabSelectedListener(OnTabSelectedListener tabSelectedListener) {
        this.tabSelectedListener = tabSelectedListener;
    }

    public interface OnTabSelectedListener {
        void onTabSelected(int index);
    }
}
