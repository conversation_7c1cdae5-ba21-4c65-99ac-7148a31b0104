package com.dinsafer.module.powerstation.utils;

import android.content.Context;
import android.text.TextUtils;

import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.powerstation.ChartDataUtil;
import com.dinsafer.module.powerstation.electricity.bean.CustomBarDataSet;
import com.dinsafer.module.powerstation.electricity.chart.CustomCombinedChart;
import com.dinsafer.module.powerstation.electricity.manager.CustomCombinedChartManager;
import com.dinsafer.util.CollectionUtil;
import com.dinsafer.util.DDDateUtil;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.formatter.XAxisValueFormatter;
import com.github.mikephil.charting.formatter.YAxisValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class RelativePriceChartHelper {

    private Context mContext;
    private CustomCombinedChart mPriceChart;
    private CustomCombinedChartManager mPriceChartManager;
    private int mHourCount = 1440;
    private int mInterval = 1;
    private final static int DST_TIME_MINUTE = 1500;
    private final static int SUMMER_TIME_MINUTE = 1380;

    private XAxisValueFormatter formatter = (original, index, viewPortHandler) -> {
        String text = "";
        if (mHourCount == DST_TIME_MINUTE) {
            if (index == 0) {
                text = "00:00";
            }  else if (index == 24) {
                text = "24:00";
            }
        } else if (mHourCount == SUMMER_TIME_MINUTE) {
            if (index == 0) {
                text = "00:00";
            }  else if (index == 22) {
                text = "24:00";
            }
        } else {
            if (index == 0) {
                text = "00:00";
            } else if (index == 12) {
                text = "12:00";
            } else if (index == 23) {
                text = "23:00";
            }
        }
        return text;
    };

    private YAxisValueFormatter yFormatter = (value, yAxis) -> {
        return "";
    };

    public RelativePriceChartHelper(Context context, CustomCombinedChart priceChart) {
        this.mContext = context;
        this.mPriceChart = priceChart;
        mPriceChartManager = new CustomCombinedChartManager(mContext, mPriceChart);
        mPriceChartManager.initChart(true, 0.5f, mContext.getResources().getColor(R.color.color_white_04),
                false, false, false, 0f,
                0f, 0f, 15f, 0f, 0, false,
                "", 1200, true);
        initXAxis(0);
        initYAxis();
    }

    public void initXAxis(int labelsToSkip) {
        if (mPriceChartManager == null) return;
        mPriceChartManager.initXAxis(false, false, mContext.getResources().getColor(R.color.color_white_04), 10f,
                true, mContext.getResources().getColor(R.color.color_white_03), 0.5f, XAxis.XAxisPosition.BOTTOM,
                labelsToSkip, formatter);
    }

    private void initYAxis() {
        if (mPriceChartManager == null) return;
        mPriceChartManager.initYAxis(false, 10f, mContext.getResources().getColor(R.color.color_white_03),
                0.5f, false, true, mContext.getResources().getColor(R.color.color_white_04),
                yFormatter, false, 6);
        YAxis yAxis = mPriceChart.getAxisLeft();
        yAxis.setAxisMinValue(0);
    }

    public void setPriceData(List<List<Float>> priceChartData, long startTime, String timezone) {

        if (DDDateUtil.isDSTTransitionDay(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1500;
        } else if (DDDateUtil.isWT2ST(startTime * 1000, TimeZone.getTimeZone(timezone))) {
            mHourCount = 1380;
        } else {
            mHourCount = 1440;
        }

        int count = priceChartData.size();
        ArrayList<String> xVals = new ArrayList<String>();
        for (int i = 0; i < count; i++) {
            xVals.add(String.valueOf(i));
        }
        ArrayList<BarEntry> yVals = new ArrayList<BarEntry>();
        for (int i = 0; i < count; i++) {
            List<Float> sonData = priceChartData.get(i);
            yVals.add(new BarEntry(new float[]{sonData.get(1)}, i));
        }

        CombinedData data = new CombinedData(xVals);
        CustomBarDataSet barDataSet = new CustomBarDataSet(yVals, "");
        int colorsBar[] = new int[]{getColor(R.color.color_tip_warning)};
        int negativeColorsBar[] = new int[]{getColor(R.color.color_brand_primary)};
        barDataSet.setColors(colorsBar);
        barDataSet.setNegativeColors(negativeColorsBar);
        barDataSet.setDrawValues(false);
        barDataSet.setHighlightEnabled(false);
        barDataSet.setBarSpacePercent(mPriceChart.getWidth() * 1f / count * 1.2f);
        barDataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        ArrayList<IBarDataSet> dataSets = new ArrayList<IBarDataSet>();
        dataSets.add(barDataSet);
        BarData barData = new BarData(xVals, dataSets);
        data.setData(barData);
        float max = ChartDataUtil.findUpperPriceLimit(getYMax(priceChartData));
        mPriceChartManager.setYAxisMaxMin(max,-max, 5);
        mPriceChart.setData(data);
        mPriceChart.invalidate();
        mPriceChartManager.initBarChartRender(DensityUtil.dp2px(mContext, 5), getColor(R.color.color_white_03), 1.0f, 2.0f);

    }

    public float getYMax(List<List<Float>> data) {
        float maxVal = 0f;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            List<Float> sonData = data.get(i);
            float sum = 0;
            if (CollectionUtil.isListNotEmpty(sonData) && sonData.size() > 1) {
                sum = Math.abs(sonData.get(1));
            }
            sumData.add(sum);
        }
        if (CollectionUtil.isListNotEmpty(sumData)) {
            Collections.sort(sumData);
            maxVal = sumData.get(sumData.size() - 1);
        }
        return maxVal;
    }

    public float getRelativePriceMin(List<List<Float>> data) {
        if (CollectionUtil.isListEmpty(data)) return 0;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            List<Float> sonData = data.get(i);
            if (CollectionUtil.isListNotEmpty(sonData) && sonData.size() > 1) {
                sumData.add(sonData.get(1));
            }
        }
        float minVal = 0;
        if (CollectionUtil.isListNotEmpty(sumData)) {
            Collections.sort(sumData);
            minVal = sumData.get(0);
        }
        return minVal;
    }

    public float getRelativePriceMax(List<List<Float>> data) {
        if (CollectionUtil.isListEmpty(data)) return 0;
        List<Float> sumData = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            List<Float> sonData = data.get(i);
            if (CollectionUtil.isListNotEmpty(sonData) && sonData.size() > 1) {
                sumData.add(sonData.get(1));
            }
        }
        float maxVal = 0;
        if (CollectionUtil.isListNotEmpty(sumData)) {
            Collections.sort(sumData);
            maxVal = sumData.get(sumData.size() - 1);
        }
        return maxVal;
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

}
