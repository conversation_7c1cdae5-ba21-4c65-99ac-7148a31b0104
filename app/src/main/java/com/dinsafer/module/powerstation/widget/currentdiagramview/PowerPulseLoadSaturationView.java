package com.dinsafer.module.powerstation.widget.currentdiagramview;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LayoutPowerPulseLoadSaturationBinding;
import com.dinsafer.module.powerstation.LottieManager;
import com.dinsafer.module.powerstation.widget.BatteryChargeView;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.UnitUtil;

import java.util.Map;

public class PowerPulseLoadSaturationView extends ConstraintLayout {

    private final Context mContext;
    private LayoutPowerPulseLoadSaturationBinding mBinding;
    private final LottieManager mLottieManager;
    private boolean mIsLoading;
    private boolean isBalancing;
    private int balancingState = 0;

    public PowerPulseLoadSaturationView(@NonNull Context context) {
        this(context, null);
    }

    public PowerPulseLoadSaturationView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PowerPulseLoadSaturationView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        mLottieManager = new LottieManager();
        mBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.layout_power_pulse_load_saturation, this, true);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.LoadSaturationView);
        int currentWidth = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_width, 0);
        int currentHeight = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_height, 0);
        int backgroundColor = typedArray.getColor(R.styleable.LoadSaturationView_background_color, 0);
        int valTextColor = typedArray.getColor(R.styleable.LoadSaturationView_current_val_text_color, 0);
        int valTextSize = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_val_text_size, 0);
        int unitTextColor = typedArray.getColor(R.styleable.LoadSaturationView_current_unit_text_color, 0);
        int unitTextSize = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_unit_text_size, 0);
        int marginLeft = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_current_margin_left, 0);
        int centerMarginBottom = typedArray.getDimensionPixelSize(R.styleable.LoadSaturationView_center_margin_bottom, 0);
        boolean ivArrowVisible = typedArray.getBoolean(R.styleable.LoadSaturationView_vehicle_arrow_visible, true);
        mBinding.ivBatteryArrow.setVisibility(ivArrowVisible ? VISIBLE : GONE);
        typedArray.recycle();

        if (currentWidth > 0 && currentHeight > 0) {
            LayoutParams cdvLayoutParam = (LayoutParams) mBinding.cdvInverter.getLayoutParams();
            cdvLayoutParam.width = currentWidth;
            cdvLayoutParam.height = currentHeight;
            if (marginLeft > 0) {
                cdvLayoutParam.leftMargin = marginLeft;
            }
            mBinding.cdvInverter.setLayoutParams(cdvLayoutParam);
        }

        if (centerMarginBottom > 0) {
            LayoutParams ivModeLayoutParams = (LayoutParams) mBinding.ivMode.getLayoutParams();
            ivModeLayoutParams.bottomMargin = centerMarginBottom;
            mBinding.ivMode.setLayoutParams(ivModeLayoutParams);

            LayoutParams clSwitchLayoutParams = (LayoutParams) mBinding.clSwitch.getLayoutParams();
            clSwitchLayoutParams.bottomMargin = centerMarginBottom;
            mBinding.clSwitch.setLayoutParams(clSwitchLayoutParams);
        }
        if (backgroundColor != 0) {
            mBinding.clParent.setBackgroundColor(backgroundColor);
        }
        if (valTextColor != 0) {
            mBinding.tvBatteryVal.setTextColor(valTextColor);
            mBinding.tvBalancingPowerVal.setTextColor(valTextColor);
        }
        if (valTextSize > 0) {
            mBinding.tvBatteryVal.setTextSize(valTextSize);
            mBinding.tvBalancingPowerVal.setTextSize(valTextSize);
        }

        if (unitTextColor != 0) {
            mBinding.tvBatteryUnit.setTextColor(unitTextColor);
            mBinding.tvBalancingPowerUnit.setTextColor(unitTextColor);
        }
        if (unitTextSize > 0) {
            mBinding.tvBatteryUnit.setTextSize(unitTextSize);
            mBinding.tvBalancingPowerUnit.setTextSize(unitTextSize);
        }
        initListener();
    }

    public void setKeyText() {
        mBinding.tvBalancingPowerKey.setLocalText(mContext.getString(R.string.balancing_power));
        mBinding.tvBatteryKey.setLocalText(mContext.getString(R.string.power_station_battery));
    }

    private void initListener() {
        OnClickListener onBatteryListener = view -> {
            if (mOperateListener != null) {
                mOperateListener.onClickBattery(view);
            }
        };
        mBinding.llBattery.setOnClickListener(onBatteryListener);

        mBinding.viewDisable.setOnClickListener(view -> {
            if (mOperateListener != null) {
                mOperateListener.onClickUpdate(view);
                changeViewStateByUpdateState(false);
            }
        });
        mBinding.ivUpdating.setOnClickListener(view -> {
            if (mOperateListener != null) {
                mOperateListener.onClickUpdate(view);
                changeViewStateByUpdateState(false);
            }
        });
    }

    /**
     * 这个方法提供 AddWidget 时设置默认值调用
     *
     * @param batteryVal
     * @param balancingPower
     */
    public void setDefaultVal(String batteryVal, String balancingPower) {
        mBinding.tvBatteryVal.setLocalText(batteryVal);
        mBinding.tvBalancingPowerVal.setLocalText(balancingPower);
        mBinding.lavBalancingPower.setFrame(100);
    }

    public void setDefaultBattery(int percent, boolean online, int chargeStatus,
                                  float smartReserve) {
        mBinding.viewBattery.setOnline(online);
        mBinding.viewBattery.setChargeStatus(chargeStatus, false);
        mBinding.viewBattery.setSmartReserve(smartReserve);
        mBinding.viewBattery.setProgress(percent / 100f, true);
    }

    /**
     * 显示中间开关
     */
    public void showSwitchStatus(boolean show, boolean loading) {
        if (show) {
            mBinding.rippleBackground.startRippleAnimation();
        } else {
            mBinding.rippleBackground.stopRippleAnimation();
        }
        mBinding.clSwitch.setOnClickListener(v -> {
            if (!mIsLoading) {
                setLoading(true);
//                    submitCmd(DsCamCmd.SET_INVERTER_OPEN, 0);
                if (mOperateListener != null) {
                    mOperateListener.onClickSwitch(v);
                }
            }
        });
        if (show) {
            setLoading(loading);
        } else {
            setLoading(false);
        }
        mBinding.clSwitch.setVisibility(show ? VISIBLE : View.INVISIBLE);
    }

    /**
     * 是否loading状态
     *
     * @param isLoading
     */
    public void setLoading(boolean isLoading) {
        mIsLoading = isLoading;
        mBinding.rippleBackground.setVisibility(isLoading ? View.INVISIBLE : VISIBLE);
        if (isLoading) {
            mBinding.lavLoading.playAnimation();
        } else {
            mBinding.lavLoading.pauseAnimation();
        }
        mBinding.ivSwitch.setVisibility(isLoading ? GONE : VISIBLE);
        mBinding.lavLoading.setVisibility(isLoading ? VISIBLE : GONE);
    }

    private void setNotVal() {
        mBinding.cdvInverter.stopAnim();
        mBinding.tvBalancingPowerVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.tvBatteryVal.setLocalText(mContext.getString(R.string.power_station_cdv_offline_val));
        mBinding.viewBattery.setOnline(false);
        mBinding.viewBattery.setProgress(0f, true);
        mBinding.viewBattery.setChargeStatus(0, true);
        mLottieManager.controlBalancingPower(mBinding.lavBalancingPower, LottieManager.BALANCING_POWER_OFF);
        if (isBalancing) {
            mLottieManager.controlBalancingMode(mBinding.lavBalancingMode, LottieManager.AnimBalanceState.END);
        }
    }

    public void setOffline(boolean offline) {
        if (offline) {
            setNotVal();
            changeViewStateByUpdateState(false);
        } else {
            mBinding.viewBattery.setOnline(true);
        }
    }

    public void restartInverter() {
        setNotVal();
        showSwitchStatus(true, true);
    }

    public void resetViewBattery(float emergencyReserve, float smartReserve) {
        mBinding.viewBattery.setEmergencyReserve(emergencyReserve);
        mBinding.viewBattery.setSmartReserve(smartReserve);
        mBinding.viewBattery.resetColor();
        mBinding.viewBattery.invalidate();
    }

    public void setMode(int mode) {
        if (mode < 0 || mode > 4) return;
        int[] modeIcons = {R.drawable.icon_power_lightning, R.drawable.icon_power_lightning_mode1,
                R.drawable.icon_power_lightning_mode2, R.drawable.icon_power_lightning_mode3,
                R.drawable.icon_power_lightning_mode4};
        mBinding.ivMode.setImageResource(modeIcons[mode]);
    }

    public void setBalancingMode(LottieManager.AnimBalanceState state) {
        isBalancing = state == LottieManager.AnimBalanceState.ING;
        mLottieManager.controlBalancingMode(mBinding.lavBalancingMode, state);
    }

    public void changeViewStateByUpdateState(final boolean showUpdate) {
        if (showUpdate) {
            mBinding.viewDisable.setVisibility(VISIBLE);
            mBinding.ivUpdating.setVisibility(VISIBLE);
        } else {
            mBinding.viewDisable.setVisibility(GONE);
            mBinding.ivUpdating.setVisibility(GONE);
        }
    }

    private int getColor(int colorId) {
        return mContext.getResources().getColor(colorId);
    }

    public void setGlobalCurrent(boolean isThreePhase, Map<String, Object> globalInfoMap) {
        int batteryWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.BATTERY_WAT, 0);
        int solarWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.SOLAR_WAT, 0);
        int gridWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.GRID_WAT, 0);
        int additionWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.ADDITIONAL_LOAD_WAT, 0);
        int otherWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.OTHER_LOAD_WAT, 0);
        int vehicleWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.VECHI_WAT, 0);
        int ip2Wat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.IP2_WAT, 0);
        int op2Wat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.OP2_WAT, 0);
        int solarEfficiency = DeviceHelper.getInt(globalInfoMap, BmtDataKey.SOLAR_EFFICIENCY, 0);
        boolean thirdPartyPVOn = DeviceHelper.getBoolean(globalInfoMap, BmtDataKey.THIRD_PARTY_PV_ON, false);
        int dualPowerWat = DeviceHelper.getInt(globalInfoMap, BmtDataKey.DUAL_POWER_WAT, 0);

        DDLog.i("PowerPulseLoadSaturationView","ip2WatAbs: " + Math.abs(ip2Wat) + " op2WatAbs: " + Math.abs(op2Wat));
        int ip2WatAbs = Math.abs(ip2Wat);
        int op2WatAbs = Math.abs(op2Wat);

        boolean isBalancingPowerOpen = false;
        if (ip2WatAbs > op2WatAbs) {
            mBinding.tvBalancingPowerVal.setText(UnitUtil.savePointStr(Math.abs(ip2WatAbs) / 1000d, 1));
            isBalancingPowerOpen = true;
            dealBalancingPowerCurrent(-1);
        } else if (ip2WatAbs < op2WatAbs) {
            mBinding.tvBalancingPowerVal.setText(UnitUtil.savePointStr(Math.abs(op2WatAbs) / 1000d, 1));
            isBalancingPowerOpen = true;
            dealBalancingPowerCurrent(1);
        } else {
            mBinding.tvBalancingPowerVal.setText("0");
            dealBalancingPowerCurrent(0);
        }
        mLottieManager.controlBalancingPower(mBinding.lavBalancingPower, isBalancingPowerOpen ? LottieManager.BALANCING_POWER_ON : LottieManager.BALANCING_POWER_OFF);

        dealBatteryCurrent(batteryWat);


        /**
         *                              EV-out(OP4)+家庭保障供应(OP1)
         *  负载饱和度        =   ------------------------------------------- *100%
         * Load Saturation      逆变设计负载上限（单相电3.6kW   /  三相电10.8kW）
         */
        float limited = isThreePhase ? 10800f : 3600;
        float load = ((vehicleWat < 0 ? Math.abs(vehicleWat) * 100 : 0) +
                (otherWat < 0 ? Math.abs(otherWat) * 100 : 0)) / limited * 100;
        int startColor = getColor(R.color.ps_current_path_start_color_1);
        int endColor = getColor(R.color.ps_current_path_end_color_1);
        if (load < 30) {
            startColor = getColor(R.color.ps_current_path_start_color_1);
            endColor = getColor(R.color.ps_current_path_end_color_1);
        } else if (load >= 30 && load < 65) {
            startColor = getColor(R.color.ps_current_path_start_color_2);
            endColor = getColor(R.color.ps_current_path_end_color_2);
        } else if (load >= 65 && load < 90) {
            startColor = getColor(R.color.ps_current_path_start_color_3);
            endColor = getColor(R.color.ps_current_path_end_color_3);
        } else if (load >= 90) {
            startColor = getColor(R.color.ps_current_path_start_color_4);
            endColor = getColor(R.color.ps_current_path_end_color_4);
        }

        if (isBalancing) {
            startColor = getColor(R.color.ps_current_path_start_color_balancing_power);
            endColor = getColor(R.color.ps_current_path_end_color_balancing_power);
        }

        mBinding.clParent.setAlpha(1f);
        mBinding.cdvInverter.setSameColor(startColor, endColor);
        int[] colors = {endColor, startColor};
        float[] positions = {0f, 1f};
        mBinding.cdvInverter.setCircleColors(colors);
        mBinding.cdvInverter.setCirclePositions(positions);
        mBinding.cdvInverter.startAnim();

        int batteryStatus = BatteryChargeView.NORMAL;
        if (batteryWat > 0) {
            batteryStatus = BatteryChargeView.DISCHARGING;
        }
        if (batteryWat < 0) {
            batteryStatus = BatteryChargeView.CHARGING;
        }
        mBinding.viewBattery.setChargeStatus(batteryStatus, false);
        mBinding.viewBattery.setOnline(true);
    }

    /**
     * 电池电流动画控制
     *
     * @param batteryWat
     */
    private void dealBatteryCurrent(int batteryWat) {
        if (balancingState == 1) {
            CurrentPathBean battery2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_BATTERY_INVERTER_KEY);
            CurrentPathBean inverter2Battery = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_BATTERY_KEY);
            if (battery2Inverter != null) {
                battery2Inverter.setDrawPath(false);
            }
            if (inverter2Battery != null) {
                inverter2Battery.setDrawPath(false);
            }
        } else {
            if (batteryWat > 0) {
                CurrentPathBean batteryInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_BATTERY_INVERTER_KEY);
                if (batteryInverter == null) {
                    batteryInverter = new CurrentPathBean(CurrentPathBean.BALANCING_POWER_BATTERY_INVERTER_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.BALANCING_POWER_BATTERY_INVERTER_KEY, batteryInverter);
                }
                batteryInverter.setDrawPath(true);
                CurrentPathBean inverter2Battery = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_BATTERY_KEY);
                if (inverter2Battery != null) {
                    inverter2Battery.setDrawPath(false);
                }
            } else if (batteryWat < 0) {
                CurrentPathBean inverterBattery = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_BATTERY_KEY);
                if (inverterBattery == null) {
                    inverterBattery = new CurrentPathBean(CurrentPathBean.BALANCING_POWER_INVERTER_BATTERY_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.BALANCING_POWER_INVERTER_BATTERY_KEY, inverterBattery);
                }
                inverterBattery.setDrawPath(true);
                CurrentPathBean battery2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_BATTERY_INVERTER_KEY);
                if (battery2Inverter != null) {
                    battery2Inverter.setDrawPath(false);
                }
            } else {
                CurrentPathBean battery2Inverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_BATTERY_INVERTER_KEY);
                CurrentPathBean inverter2Battery = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_BATTERY_KEY);
                if (battery2Inverter != null) {
                    battery2Inverter.setDrawPath(false);
                }
                if (inverter2Battery != null) {
                    inverter2Battery.setDrawPath(false);
                }
            }
        }
    }

    public void dealBalancingPowerCurrent(int type) {
        DDLog.i("PowerPulseLoadSaturationView","balancingState: " + balancingState);
        if (balancingState == 1) {
            CurrentPathBean invert2BalancingPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_BALANCING_POWER_KEY);
            if (invert2BalancingPower != null) {
                invert2BalancingPower.setDrawPath(false);
            }
            CurrentPathBean balancingPower2Invert = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_KEY);
            if (balancingPower2Invert != null) {
                balancingPower2Invert.setDrawPath(false);
            }
        } else {
            if (type < 0) { // 流出
                CurrentPathBean balancingPowerInverter = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_KEY);
                if (balancingPowerInverter == null) {
                    balancingPowerInverter = new CurrentPathBean(CurrentPathBean.BALANCING_POWER_INVERTER_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.BALANCING_POWER_INVERTER_KEY, balancingPowerInverter);
                }
                balancingPowerInverter.setDrawPath(true);
                CurrentPathBean invert2BalancingPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_BALANCING_POWER_KEY);
                if (invert2BalancingPower != null) {
                    invert2BalancingPower.setDrawPath(false);
                }
            } else if (type > 0) {  // 流入
                CurrentPathBean inverterBalancingPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_BALANCING_POWER_KEY);
                if (inverterBalancingPower == null) {
                    inverterBalancingPower = new CurrentPathBean(CurrentPathBean.INVERTER_BALANCING_POWER_KEY);
                    mBinding.cdvInverter.setPathMap(CurrentPathBean.INVERTER_BALANCING_POWER_KEY, inverterBalancingPower);
                }
                inverterBalancingPower.setDrawPath(true);
                CurrentPathBean balancingPower2Invert = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_KEY);
                if (balancingPower2Invert != null) {
                    balancingPower2Invert.setDrawPath(false);
                }
            } else {
                CurrentPathBean invert2BalancingPower = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.INVERTER_BALANCING_POWER_KEY);
                if (invert2BalancingPower != null) {
                    invert2BalancingPower.setDrawPath(false);
                }
                CurrentPathBean balancingPower2Invert = mBinding.cdvInverter.getPathBeanByKey(CurrentPathBean.BALANCING_POWER_INVERTER_KEY);
                if (balancingPower2Invert != null) {
                    balancingPower2Invert.setDrawPath(false);
                }
            }
        }
    }

    public void stopAnim() {
        stopAnim(false);
    }

    public void setBalancingState(int balancingState) {
        this.balancingState = balancingState;
    }

    public void stopAnim(boolean drawPath) {
        mBinding.cdvInverter.stopAnim(drawPath);
    }

    public void setBatteryViewAnim(boolean isAnim) {
        mBinding.viewBattery.setAnim(isAnim);
    }

    public void setBatteryInfo(BatteryInfoBean batteryInfoBean) {
        int smartReserve = batteryInfoBean.getSmartReserve();
        int emergencyReserve = batteryInfoBean.getEmergencyReserve();
        float er = emergencyReserve / 100f;
        float sr = smartReserve / 100f;
        mBinding.tvBatteryVal.setLocalText(String.valueOf(batteryInfoBean.getPercentage()));
        mBinding.viewBattery.setEmergencyReserve(er);
        mBinding.viewBattery.setSmartReserve(sr);
        mBinding.viewBattery.setProgress(batteryInfoBean.getPercentage() / 100f, true);
    }

    private OnOperateListener mOperateListener;

    public OnOperateListener getOperateListener() {
        return mOperateListener;
    }

    public void setOperateListener(OnOperateListener operateListener) {
        this.mOperateListener = operateListener;
    }

    public interface OnOperateListener {
        void onClickBattery(View view);

        void onClickUpdate(View view);

        void onClickSwitch(View view);
    }
}