package com.dinsafer.module.powerstation.gridrewards;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentPsAddressInformationBinding;
import com.dinsafer.dinsdk.BmtUtil;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.powerstation.bean.FamilyBalanceContractInfo;
import com.dinsafer.module.powerstation.event.PSCountryEvent;
import com.dinsafer.module.powerstation.event.PSElectricitySupplierEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.module_home.bean.ElectricitySupplierBean;
import com.dinsafer.util.ClearAllEditText;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DensityUtils;
import com.google.gson.Gson;
import com.jakewharton.rxbinding.view.RxView;
import com.jakewharton.rxbinding.widget.RxTextView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import rx.Observable;

public class PSAddressInformationFragment extends MyBaseFragment<FragmentPsAddressInformationBinding> {

    private List<CountryBean> mCountryList;
    private CountryBean mSelectedCountryBean;
    private String mSelectSupplierId;
    private FamilyBalanceContractInfo mFamilyBalanceContractInfo;

    public static PSAddressInformationFragment newInstance(List<CountryBean> countries, FamilyBalanceContractInfo familyBalanceContractInfo) {
        PSAddressInformationFragment fragment = new PSAddressInformationFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PSKeyConstant.KEY_COUNTRIES, (Serializable) countries);
        bundle.putParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO, familyBalanceContractInfo);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ps_address_information;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.commonBar.commonBarTitle.setLocalText(getString(R.string.address_information));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.tvCountryValue.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PSCountryFragment.newInstance(mCountryList)));
        mBinding.ivCountry.setOnClickListener(v -> getDelegateActivity().addCommonFragment(PSCountryFragment.newInstance(mCountryList)));

        mBinding.tvSupplierValue.setOnClickListener(v -> goSuppliers());
        mBinding.ivSupplier.setOnClickListener(v -> goSuppliers());

        mBinding.llParent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCloseInput();
            }
        });
        mBinding.clContent.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toCloseInput();
            }
        });
        mBinding.btnNext.setOnClickListener(v -> {
            if (BmtUtil.isCountryInDevices(mSelectedCountryBean.getCountry_code())) {
                saveCache();
                ClearAllEditText.clearAllEditTextFocus(this);
                getDelegateActivity().addCommonFragment(PSBankAccountFragment.newInstance(mCountryList, mFamilyBalanceContractInfo));
            } else {
                showNotCountryInDeviceDialog();
            }
        });
    }

    private void saveCache() {
        int type = mFamilyBalanceContractInfo.getType();
        setCountryToModel();
        mFamilyBalanceContractInfo.setElectricitySupplier(mBinding.tvSupplierValue.getText().toString());
        if (!TextUtils.isEmpty(mSelectSupplierId)) {
            mFamilyBalanceContractInfo.setElectricitySupplierId(mSelectSupplierId);
        }
        mFamilyBalanceContractInfo.setCity(mBinding.etCityValue.getText().toString());
        mFamilyBalanceContractInfo.setStreetNameAndNumber(mBinding.etAddressValue.getText().toString());
        mFamilyBalanceContractInfo.setZipCode(mBinding.etZipCodeValue.getText().toString());
        String json = new Gson().toJson(mFamilyBalanceContractInfo);
        String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        DBUtil.Put(DBKey.KEY_FAMILY_BALANCE_CONTRACT_INFO + "_" + type + "_" + homeId, json);
    }

    @Override
    public void initData() {
        super.initData();
        Observable<CharSequence> ObservableCountry = RxTextView.textChanges(mBinding.tvCountryValue);
        Observable<CharSequence> ObservableCity = RxTextView.textChanges(mBinding.etCityValue);
        Observable<CharSequence> ObservableAddress = RxTextView.textChanges(mBinding.etAddressValue);
        Observable<CharSequence> ObservableZipCode = RxTextView.textChanges(mBinding.etZipCodeValue);
        Observable<CharSequence> ObservableSupplier = RxTextView.textChanges(mBinding.tvSupplierValue);

        Observable.combineLatest(ObservableCountry, ObservableCity, ObservableAddress, ObservableZipCode, ObservableSupplier,
                (country, city, address, zipCode, supplier) ->
                        !TextUtils.isEmpty(country.toString())
                                && !TextUtils.isEmpty(city.toString())
                                && !TextUtils.isEmpty(address.toString())
                                && !TextUtils.isEmpty(zipCode.toString())
                                && !TextUtils.isEmpty(supplier.toString())).subscribe(aBoolean -> {
            mBinding.btnNext.setAlpha(aBoolean ? 1f : 0.5f);
            RxView.enabled(mBinding.btnNext).call(aBoolean);
        });

        Bundle bundle = getArguments();
        if (bundle != null) {
            mCountryList = (List<CountryBean>) bundle.getSerializable(PSKeyConstant.KEY_COUNTRIES);
            mFamilyBalanceContractInfo = bundle.getParcelable(PSKeyConstant.KEY_FAMILY_BALANCE_CONTRACT_INFO);
            if (mFamilyBalanceContractInfo != null) {
                if (mCountryList != null && mCountryList.size() > 0) {
                    List<CountryBean> supportCountries = new ArrayList<>();
                    for (CountryBean countryBean : mCountryList) {
                        if (countryBean.isBalance_contract_support()) {
                            supportCountries.add(countryBean);
                        }
                    }
                    mSelectedCountryBean = null;
                    String countryCodeCache = mFamilyBalanceContractInfo.getCountry_code();
                    for (CountryBean countryBean : supportCountries) {
                        String countryCode = countryBean.getCountry_code();
                        if (!TextUtils.isEmpty(countryCode)) {
                            if (!TextUtils.isEmpty(countryCodeCache) && countryCode.equals(countryCodeCache)) {
                                mSelectedCountryBean = countryBean;
                                break;
                            }
                        }
                    }
                    if (mSelectedCountryBean == null) {
                        for (CountryBean countryBean : supportCountries) {
                            String countryCode = countryBean.getCountry_code();
                            if (!TextUtils.isEmpty(countryCode) && countryCode.equals("DK")) {
                                mSelectedCountryBean = countryBean;
                                mFamilyBalanceContractInfo.setElectricitySupplier(null);
                                mFamilyBalanceContractInfo.setElectricitySupplierId(null);
                                break;
                            }
                        }
                    }
                    if (mSelectedCountryBean == null) {
                        if (supportCountries.size() > 0) {
                            mSelectedCountryBean = supportCountries.get(0);
                        }
                        mFamilyBalanceContractInfo.setElectricitySupplier(null);
                        mFamilyBalanceContractInfo.setElectricitySupplierId(null);
                    }
                    setCountryToModel();
                } else {
                    mFamilyBalanceContractInfo.setCountry_code(null);
                    mFamilyBalanceContractInfo.setCountryNameDisplay(null);
                    mFamilyBalanceContractInfo.setElectricitySupplier(null);
                    mFamilyBalanceContractInfo.setElectricitySupplierId(null);
                }
                String countryNameDisplay = mFamilyBalanceContractInfo.getCountryNameDisplay();
                if (!TextUtils.isEmpty(countryNameDisplay)) {
                    mBinding.tvCountryValue.setText(countryNameDisplay);
                }
                String city = mFamilyBalanceContractInfo.getCity();
                if (!TextUtils.isEmpty(city)) {
                    mBinding.etCityValue.setText(city);
                }
                String address = mFamilyBalanceContractInfo.getStreetNameAndNumber();
                if (!TextUtils.isEmpty(address)) {
                    mBinding.etAddressValue.setText(address);
                }
                String zipCode = mFamilyBalanceContractInfo.getZipCode();
                if (!TextUtils.isEmpty(zipCode)) {
                    mBinding.etZipCodeValue.setText(zipCode);
                }
                String supplier = mFamilyBalanceContractInfo.getElectricitySupplier();
                if (!TextUtils.isEmpty(supplier)) {
                    mBinding.tvSupplierValue.setText(supplier);
                }
            }
        }
    }

    private void goSuppliers() {
        if (mSelectedCountryBean == null) {
            return;
        }
        ClearAllEditText.clearAllEditTextFocus(this);
        getDelegateActivity().addCommonFragment(PSElectricitySupplierFragment.newInstance(mSelectedCountryBean.getCountry_code()));
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PSCountryEvent countryEvent) {
        mSelectedCountryBean = countryEvent.getCountryBean();
        mBinding.tvCountryValue.setText(mSelectedCountryBean.getCountry_name_display());
        mBinding.tvSupplierValue.setText("");
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PSElectricitySupplierEvent electricitySupplierEvent) {
        ElectricitySupplierBean supplier = electricitySupplierEvent.getSupplier();
        mSelectSupplierId = electricitySupplierEvent.getSupplier().getId();
        mBinding.tvSupplierValue.setText(supplier.getName());
    }

    private void setCountryToModel() {
        if (mFamilyBalanceContractInfo != null) {
            if (mSelectedCountryBean != null) {
                mFamilyBalanceContractInfo.setCountry_code(mSelectedCountryBean.getCountry_code());
                mFamilyBalanceContractInfo.setCountryNameDisplay(mSelectedCountryBean.getCountry_name_display());
            } else {
                mFamilyBalanceContractInfo.setCountry_code(null);
                mFamilyBalanceContractInfo.setCountryNameDisplay(null);
            }
        }
    }

    private void showNotCountryInDeviceDialog() {
        AlertDialog.createBuilder(getContext())
                .setOk(getResources().getString(R.string.ok))
                .setContent(getResources().getString(R.string.Family_has_no_devices_from_this_country_Please_choose_another_country))
                .setContentGravity(Gravity.CENTER_HORIZONTAL)
                .setIsSuccess(false)
                .setIsShowContentImageView(true)
                .setCanCancel(false)
                .setAutoDissmiss(true)
                .setContentLayoutMinHeight(0)
                .setContentLayoutMarginTop(DensityUtils.dp2px(getContext(), 30))
                .setContentLayoutMarginBottom(20)
                .preBuilder()
                .show();
    }
}
