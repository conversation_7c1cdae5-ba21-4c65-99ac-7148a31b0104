package com.dinsafer.module.powerstation.bean;

import androidx.annotation.DrawableRes;


public class PSReserveModeBeanV2 {

    private @DrawableRes int logo;
    private String title;
    private String subtitle;
    private int type; // 0. Price Tracking Mode 1. Scheduled Mode
    private int status = -1; // -1. 未应用  0. 加载中  1. 已应用
    private boolean isSelected;
    private boolean isSameSelected = true;
    private boolean enabled = true;
    private boolean showTag;

    public PSReserveModeBeanV2(int logo, String title, String subtitle, int type) {
        this.logo = logo;
        this.title = title;
        this.subtitle = subtitle;
        this.type = type;
    }

    public PSReserveModeBeanV2(int logo, String title, String subtitle, int type, boolean enabled) {
        this.logo = logo;
        this.title = title;
        this.subtitle = subtitle;
        this.type = type;
        this.enabled = enabled;
    }

    public PSReserveModeBeanV2(int logo, String title, String subtitle, int type, boolean enabled, boolean showTag) {
        this.logo = logo;
        this.title = title;
        this.subtitle = subtitle;
        this.type = type;
        this.enabled = enabled;
        this.showTag = showTag;
    }

    public int getLogo() {
        return logo;
    }

    public void setLogo(int logo) {
        this.logo = logo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        setSameSelected(this.isSelected == selected);
        isSelected = selected;
    }

    public boolean isSameSelected() {
        return isSameSelected;
    }

    public void setSameSelected(boolean sameSelected) {
        isSameSelected = sameSelected;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isShowTag() {
        return showTag;
    }

    public void setShowTag(boolean showTag) {
        this.showTag = showTag;
    }
}
