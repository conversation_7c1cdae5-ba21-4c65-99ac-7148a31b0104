package com.dinsafer.module.powerstation.adapter;

import android.content.Context;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemOnGridConfigurationBottomBinding;
import com.dinsafer.ui.rv.BindModel;

public class OGCBottomModel extends BindModel<ItemOnGridConfigurationBottomBinding> {

    public OGCBottomModel(Context context) {
        super(context);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_on_grid_configuration_bottom;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemOnGridConfigurationBottomBinding itemOnGridConfigurationBottomBinding) {

    }
}
