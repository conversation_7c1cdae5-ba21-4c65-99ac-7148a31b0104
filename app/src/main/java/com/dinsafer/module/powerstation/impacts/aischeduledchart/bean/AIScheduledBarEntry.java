package com.dinsafer.module.powerstation.impacts.aischeduledchart.bean;

import androidx.annotation.DrawableRes;

import com.github.mikephil.charting.data.BarEntry;

public class AIScheduledBarEntry extends BarEntry {

    private int customColor;
    @DrawableRes
    private int iconRes;
    private float iconTopOffset;
    private String topNote;
    private int noteColor;
    private float noteTextSize;
    private boolean isDrawBorder;
    private int borderColor;
    private float borderWidth;
    private boolean isDrawIcon;
    private boolean isDrawTopNote;
    private float textBackgroundRadius;
    private float textPadding = 2;
    private float textPaddingLeft = 2;
    private float textPaddingTop = 2;
    private float textPaddingRight = 2;
    private float textPaddingBottom = 2;

    public AIScheduledBarEntry(float[] vals, int xIndex) {
        super(vals, xIndex);
    }

    public AIScheduledBarEntry(float[] vals, int xIndex, int customColor) {
        super(vals, xIndex);
        this.customColor = customColor;
    }

    public AIScheduledBarEntry(float val, int xIndex) {
        super(val, xIndex);
    }

    public AIScheduledBarEntry(float val, int xIndex, int customColor) {
        super(val, xIndex);
        this.customColor = customColor;
    }


    public AIScheduledBarEntry(float[] vals, int xIndex, String label) {
        super(vals, xIndex, label);
    }

    public AIScheduledBarEntry(float[] vals, int xIndex, String label, int customColor) {
        super(vals, xIndex, label);
        this.customColor = customColor;
    }

    public AIScheduledBarEntry(float val, int xIndex, Object data) {
        super(val, xIndex, data);
    }

    public AIScheduledBarEntry(float val, int xIndex, Object data, int customColor) {
        super(val, xIndex, data);
        this.customColor = customColor;
    }

    public int getCustomColor() {
        return customColor;
    }

    public void setCustomColor(int customColor) {
        this.customColor = customColor;
    }

    public int getIconRes() {
        return iconRes;
    }

    public void setIconRes(int iconRes) {
        this.iconRes = iconRes;
    }

    public float getIconTopOffset() {
        return iconTopOffset;
    }

    public void setIconTopOffset(float iconTopOffset) {
        this.iconTopOffset = iconTopOffset;
    }

    public String getTopNote() {
        return topNote;
    }

    public void setTopNote(String topNote) {
        this.topNote = topNote;
    }

    public int getNoteColor() {
        return noteColor;
    }

    public void setNoteColor(int noteColor) {
        this.noteColor = noteColor;
    }

    public float getNoteTextSize() {
        return noteTextSize;
    }

    public void setNoteTextSize(float noteTextSize) {
        this.noteTextSize = noteTextSize;
    }

    public boolean isDrawBorder() {
        return isDrawBorder;
    }

    public void setDrawBorder(boolean drawBorder) {
        isDrawBorder = drawBorder;
    }

    public int getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(int borderColor) {
        this.borderColor = borderColor;
    }

    public float getBorderWidth() {
        return borderWidth;
    }

    public void setBorderWidth(float borderWidth) {
        this.borderWidth = borderWidth;
    }

    public boolean isDrawIcon() {
        return isDrawIcon;
    }

    public void setDrawIcon(boolean drawIcon) {
        isDrawIcon = drawIcon;
    }

    public boolean isDrawTopNote() {
        return isDrawTopNote;
    }

    public void setDrawTopNote(boolean drawTopNote) {
        isDrawTopNote = drawTopNote;
    }

    public float getTextBackgroundRadius() {
        return textBackgroundRadius;
    }

    public void setTextBackgroundRadius(float textBackgroundRadius) {
        this.textBackgroundRadius = textBackgroundRadius;
    }

    public float getTextPadding() {
        return textPadding;
    }

    public void setTextPadding(float textPadding) {
        this.textPadding = textPadding;
    }

    public float getTextPaddingLeft() {
        return textPaddingLeft;
    }

    public void setTextPaddingLeft(float textPaddingLeft) {
        this.textPaddingLeft = textPaddingLeft;
    }

    public float getTextPaddingTop() {
        return textPaddingTop;
    }

    public void setTextPaddingTop(float textPaddingTop) {
        this.textPaddingTop = textPaddingTop;
    }

    public float getTextPaddingRight() {
        return textPaddingRight;
    }

    public void setTextPaddingRight(float textPaddingRight) {
        this.textPaddingRight = textPaddingRight;
    }

    public float getTextPaddingBottom() {
        return textPaddingBottom;
    }

    public void setTextPaddingBottom(float textPaddingBottom) {
        this.textPaddingBottom = textPaddingBottom;
    }
}
