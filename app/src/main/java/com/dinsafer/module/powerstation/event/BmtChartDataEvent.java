package com.dinsafer.module.powerstation.event;

import java.util.Map;

public class BmtChartDataEvent {

    private final String deviceId;
    private final String subcategory;
    private boolean isTop;
    private Map<String, Object> result;

    public BmtChartDataEvent(String deviceId, String subcategory, Map<String, Object> result) {
        this.deviceId = deviceId;
        this.subcategory = subcategory;
        this.result = result;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public boolean isTop() {
        return isTop;
    }

    public void setTop(boolean top) {
        isTop = top;
    }

    public Map<String, Object> getResult() {
        return result;
    }

    public void setResult(Map<String, Object> result) {
        this.result = result;
    }
}
