package com.dinsafer.module.addmore.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAddMoreContentMoreBinding;
import com.dinsafer.module.addmore.view.AddMoreHelper;

/**
 * AddMore 底部更多Item
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/30 3:09 PM
 */
public class AddMoreMoreModel extends BaseAddMoreModel<ItemAddMoreContentMoreBinding> {

    private OnAddMoreMoreItemClickListener mListener;

    public AddMoreMoreModel() {
        super(AddMoreHelper.ADD_MORE_CONTENT_VIEW_TYPE_MORE);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_add_more_content_more;
    }

    @Override
    public boolean onDo(View v) {
        int targetId = v.getId();
        if (null != mListener) {
            if (R.id.btn_more == targetId) {
                mListener.onMoreClick(v);
            }
        }
        return false;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAddMoreContentMoreBinding viewBinding) {
        viewBinding.btnMore.setLocalText(DinSaferApplication.getAppContext().getResources().getString(R.string.more));
    }

    public void setOnMoreClickListener(OnAddMoreMoreItemClickListener listener) {
        this.mListener = listener;
    }
}
