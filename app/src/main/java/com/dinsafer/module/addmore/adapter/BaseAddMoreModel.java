package com.dinsafer.module.addmore.adapter;

import androidx.databinding.ViewDataBinding;

import com.dinsafer.common.widget.rv.BaseBindModel;
import com.dinsafer.module.addmore.view.AddMoreHelper;

/**
 * AddMore列表视图基类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/30 3:02 PM
 */
public abstract class BaseAddMoreModel<V extends ViewDataBinding> implements BaseBindModel<V> {
    protected @AddMoreHelper.AddMoreContentViewType
    int mContentViewType;

    public BaseAddMoreModel(int contentViewType) {
        this.mContentViewType = contentViewType;
    }

    public int getContentViewType() {
        return mContentViewType;
    }
}
