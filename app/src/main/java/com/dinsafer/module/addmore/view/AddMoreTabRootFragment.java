package com.dinsafer.module.addmore.view;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.config.AppConfig;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAddMoreTabRootBinding;
import com.dinsafer.model.addmore.TabInfo;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.ArrayList;

/**
 * AddMore大分类页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/29 7:13 PM
 */
public class AddMoreTabRootFragment extends LazyBaseFragment {
    private static final String KEY_TAB_INFO = "tab_info";
    private FragmentAddMoreTabRootBinding mBinding;
    private TabInfo mCurrentTabInfo;
    private CommonPagerAdapter mAdapter;
    private ArrayList<BaseFragment> mFragments;

    public static AddMoreTabRootFragment newInstance(TabInfo tabInfo) {
        AddMoreTabRootFragment fragment = new AddMoreTabRootFragment();
        Bundle args = new Bundle();
        args.putParcelable(KEY_TAB_INFO, tabInfo);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_add_more_tab_root, container, false);
        mCurrentTabInfo = getArguments().getParcelable(KEY_TAB_INFO);
        initView(mBinding.getRoot(), savedInstanceState);
        initData();
        super.onCreateView(inflater, container, savedInstanceState);
        return mBinding.getRoot();
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        if (mCurrentTabInfo == null) {
            DDLog.e(TAG, "onFragmentFirstVisible:mCurrentTabInfo is empty");
            return;
        }
        DDLog.i(TAG, mCurrentTabInfo.getTabTittle());

        ArrayList<String> childTabTittle = AddMoreHelper.getInstance().getTabTittlesByParentTab(mCurrentTabInfo.getTabTittle());
        if (childTabTittle == null || childTabTittle.size() == 0) {
            DDLog.e(TAG, "onFragmentFirstVisible:childTabTittle is empty");
            return;
        }
        String[] localTittles = new String[childTabTittle.size()];
        for (int i = 0; i < childTabTittle.size(); i++) {
            localTittles[i] = Local.s(childTabTittle.get(i));
        }
        mFragments = new ArrayList<>();
        for (int i = 0; i < childTabTittle.size(); i++) {
            mFragments.add(AddMoreTabContentFragment.newInstance(new TabInfo(i, childTabTittle.get(i))));
        }

        mAdapter = new CommonPagerAdapter(getChildFragmentManager(), mFragments);
        mBinding.vpAddMoreRoot.setAdapter(mAdapter);
        if (1 == localTittles.length && AppConfig.Functions.SUPPORT_ADD_MORE_HIDE_TAB_FOR_ONE_ITEM) {
            mBinding.tabAddMoreRoot.setVisibility(View.GONE);
        } else {
            mBinding.tabAddMoreRoot.setVisibility(View.VISIBLE);
            mBinding.tabAddMoreRoot.setTextAppearanceResId(R.style.TextFamilyTittleS);
            mBinding.tabAddMoreRoot.setShowBackground(true);
            mBinding.tabAddMoreRoot.setViewPager(mBinding.vpAddMoreRoot, localTittles);
        }
        mBinding.vpAddMoreRoot.setOffscreenPageLimit(childTabTittle.size());
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        AddMoreHelper.getInstance().setCurrentRootTabInfo(mCurrentTabInfo);
    }
}
