package com.dinsafer.module.addmore.view;

import android.text.TextUtils;

import androidx.annotation.ArrayRes;
import androidx.annotation.IntDef;
import androidx.annotation.StringRes;
import androidx.databinding.ViewDataBinding;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AddMoreConfig;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.addmore.AddMorePluginItem;
import com.dinsafer.model.addmore.AddMoreSceneItem;
import com.dinsafer.model.addmore.AddMoreTittleItem;
import com.dinsafer.model.addmore.TabInfo;
import com.dinsafer.module.addmore.adapter.AddMoreBottomModel;
import com.dinsafer.module.addmore.adapter.AddMoreMoreModel;
import com.dinsafer.module.addmore.adapter.AddMorePluginModel;
import com.dinsafer.module.addmore.adapter.AddMoreSceneModel;
import com.dinsafer.module.addmore.adapter.AddMoreTittleModel;
import com.dinsafer.module.addmore.adapter.BaseAddMoreModel;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Set;

/**
 * 添加更多配件Helper
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/30 11:52 AM
 */
public class AddMoreHelper {
    private static final String TAG = AddMoreHelper.class.getSimpleName();

    /**
     * # 2C Alphahom
     * <p>
     * - 按原设计的结构完整显示
     * - 没发布的产品，按原设计方案，显示敬请期待
     * <p>
     * # 2B
     * <p>
     * ### Helio Pro 公版
     * <p>
     * - Add More页面的两级菜单
     * - 一级菜单只保留Home
     * - 二级菜单中只保留Alarm System、Video Monitoring、Customized Scenes
     * - 是否只显示二级菜单，把一级菜单的Home也隐藏？看开发怎么方便
     * <p>
     * ### OEM
     * <p>
     * - 首页保留Add More卡片，点击之后直接跳转扫码页
     * <p>
     * ### CUSTOMIZE
     * - 高度自定义AddMore功能
     * <p>
     */
    public static final int FUNCTION_MODE_ALPHAHOM = 0;
    public static final int FUNCTION_MODE_HELIO_PRO = 1;
    public static final int FUNCTION_MODE_OEM = 2;
    public static final int FUNCTION_MODE_CUSTOMIZE = 3;
    public static final int FUNCTION_MODE_BMT = 4;

    @IntDef({FUNCTION_MODE_ALPHAHOM, FUNCTION_MODE_HELIO_PRO, FUNCTION_MODE_OEM, FUNCTION_MODE_CUSTOMIZE, FUNCTION_MODE_BMT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface AddMoreFunctionMode {
    }

    /**
     * AddMoreContent视图类型定义
     */
    public static final int ADD_MORE_CONTENT_VIEW_TYPE_SCENE = 0;
    public static final int ADD_MORE_CONTENT_VIEW_TYPE_TITTLE = 1;
    public static final int ADD_MORE_CONTENT_VIEW_TYPE_PLUGIN = 2;
    public static final int ADD_MORE_CONTENT_VIEW_TYPE_PADDING = 3;
    public static final int ADD_MORE_CONTENT_VIEW_TYPE_MORE = 4;

    @IntDef({ADD_MORE_CONTENT_VIEW_TYPE_SCENE, ADD_MORE_CONTENT_VIEW_TYPE_TITTLE, ADD_MORE_CONTENT_VIEW_TYPE_PLUGIN,
            ADD_MORE_CONTENT_VIEW_TYPE_PADDING, ADD_MORE_CONTENT_VIEW_TYPE_MORE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface AddMoreContentViewType {
    }

    /**
     * 配件类型定义
     */
    public static final int PLUGIN_SMART_ALARM_SYSTEM = 0;
    public static final int PLUGIN_WIRELESS_DOOR_WINDOW_SENSOR = 1;
    public static final int PLUGIN_WIRELESS_MOTION_SENSOR = 2;
    public static final int PLUGIN_WIRELESS_OUTDOOR_SIREN = 3;
    public static final int PLUGIN_WIRELESS_WATER_INTRUSION_SENSOR = 4;
    public static final int PLUGIN_WIRELESS_SMOKE_SENSOR = 5;
    public static final int PLUGIN_WIRELESS_OUTDOOR_CAMERA_BULLET = 7;
    public static final int PLUGIN_WIRELESS_INDOOR_CAMERA_FIXED = 9;
    public static final int PLUGIN_WIRELESS_SMART_BULB = 10;
    public static final int PLUGIN_WIRELESS_SIGNAL_REPEATER_PLUG = 160;
    public static final int PLUGIN_WIRELESS_SMART_PLUG_DSPH1 = 12;
    public static final int PLUGIN_WIRELESS_SMART_BUTTON = 14;
    public static final int PLUGIN_CARE_GO_4G = 16;
    public static final int PLUGIN_CARE_GO = 17;
    public static final int PLUGIN_SMART_CAT_LITTER_BOX = 18;
    public static final int PLUGIN_LIFE_RADAR = 19;
    public static final int PLUGIN_WIRELESS_VIBRATION_SENSOR = 20;
    public static final int PLUGIN_WIRELESS_GARAGE_DOOR_SENSOR = 21;
    public static final int PLUGIN_WIRELESS_PET_IMMUNE_MOTION_SENSOR = 22;
    public static final int PLUGIN_WIRELESS_INDOOR_SIREN = 23;
    public static final int PLUGIN_RFID_TAG = 24;
    public static final int PLUGIN_WIRELESS_KEYPAD = 25;
    public static final int PLUGIN_PTZ_CAMERA = 26;
    public static final int PLUGIN_WIFI_SMART_PLUG_DSP01A = 27;
    public static final int PLUGIN_CUSTOM_REMOTE_CONTROLLER = 28;
    public static final int PLUGIN_WIRELESS_BATTERY_CAMERA = 29;
    public static final int PLUGIN_WIRED_BRIDGE = 30;
    public static final int PLUGIN_SOLAR_SECURITY_CAMERA = 31;
    public static final int PLUGIN_BMT = 32;
    public static final int PLUGIN_WIRED_SECURITY_CAMERA = 33;
    public static final int PLUGIN_ADJUSTABLE_MOTION_SENSOR = 34;
    public static final int PLUGIN_BMT_POWERCORE20 = 35;
    public static final int PLUGIN_BMT_POWERSTORE = 36;
    public static final int PLUGIN_BMT_POWERSPULSE = 38;
    public static final int PLUGIN_BMT_POWERCORE30 = 39;


    @IntDef({PLUGIN_SMART_ALARM_SYSTEM, PLUGIN_WIRELESS_DOOR_WINDOW_SENSOR, PLUGIN_WIRELESS_MOTION_SENSOR,
            PLUGIN_WIRELESS_OUTDOOR_SIREN, PLUGIN_WIRELESS_WATER_INTRUSION_SENSOR, PLUGIN_WIRELESS_SMOKE_SENSOR,
            PLUGIN_WIRELESS_OUTDOOR_CAMERA_BULLET, PLUGIN_WIRELESS_INDOOR_CAMERA_FIXED, PLUGIN_WIRELESS_SMART_BULB,
            PLUGIN_WIRELESS_SIGNAL_REPEATER_PLUG, PLUGIN_WIRELESS_SMART_PLUG_DSPH1, PLUGIN_WIRELESS_SMART_BUTTON,
            PLUGIN_CARE_GO_4G, PLUGIN_CARE_GO, PLUGIN_SMART_CAT_LITTER_BOX, PLUGIN_LIFE_RADAR, PLUGIN_WIRELESS_VIBRATION_SENSOR,
            PLUGIN_WIRELESS_GARAGE_DOOR_SENSOR, PLUGIN_WIRELESS_PET_IMMUNE_MOTION_SENSOR, PLUGIN_WIRELESS_INDOOR_SIREN,
            PLUGIN_RFID_TAG, PLUGIN_WIRELESS_KEYPAD, PLUGIN_PTZ_CAMERA, PLUGIN_WIFI_SMART_PLUG_DSP01A,
            PLUGIN_CUSTOM_REMOTE_CONTROLLER, PLUGIN_WIRELESS_BATTERY_CAMERA, PLUGIN_WIRED_BRIDGE, PLUGIN_SOLAR_SECURITY_CAMERA,
            PLUGIN_BMT, PLUGIN_WIRED_SECURITY_CAMERA, PLUGIN_ADJUSTABLE_MOTION_SENSOR, PLUGIN_BMT_POWERCORE20,
            PLUGIN_BMT_POWERSTORE, PLUGIN_BMT_POWERSPULSE, PLUGIN_BMT_POWERCORE30
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface AddMorePluginType {
    }

    /**
     * 配件场景定义
     */
    public static final int PLUGIN_SCENE_RC_PLUGIN = 0; // RC配件
    public static final int PLUGIN_SCENE_PANEL = 1; // 主机
    public static final int PLUGIN_SCENE_IPC = 2; // IPC
    public static final int PLUGIN_SCENE_CARE_GO = 3; // care go
    public static final int PLUGIN_SCENE_IPC_V005 = 4;
    public static final int PLUGIN_SCENE_IPC_V006 = 5;
    public static final int PLUGIN_SCENE_BMT = 6;
    public static final int PLUGIN_SCENE_IPC_V015 = 7;
    public static final int PLUGIN_SCENE_BMT_POWERCORE20 = 8;
    public static final int PLUGIN_SCENE_BMT_POWERSTORE = 9;
    public static final int PLUGIN_SCENE_BMT_POWERPULSE = 10;
    public static final int PLUGIN_SCENE_BMT_POWERCORE30 = 11;


    @IntDef({PLUGIN_SCENE_RC_PLUGIN, PLUGIN_SCENE_PANEL, PLUGIN_SCENE_IPC, PLUGIN_SCENE_CARE_GO
            , PLUGIN_SCENE_IPC_V005, PLUGIN_SCENE_IPC_V006, PLUGIN_SCENE_IPC_V015, PLUGIN_SCENE_BMT,
            PLUGIN_SCENE_BMT_POWERCORE20, PLUGIN_SCENE_BMT_POWERSTORE, PLUGIN_SCENE_BMT_POWERPULSE,
            PLUGIN_SCENE_BMT_POWERCORE30})
    @Retention(RetentionPolicy.SOURCE)
    public @interface AddMoreSceneType {
    }

    private LinkedHashMap<String, ArrayList<String>> mTabTittle;
    private TabInfo mCurrentRootTabInfo, mCurrentContentTabInfo;
    private final Set<Integer> mUnSupportPlugin; // 暂未支持的配件类型
    /**
     * AddMore相关功能的显示模式
     */
    private final static @AddMoreFunctionMode
    int mFunctionMode = FUNCTION_MODE_BMT;

    private AddMoreHelper() {
        mUnSupportPlugin = new HashSet<>();
        mUnSupportPlugin.add(PLUGIN_LIFE_RADAR);
        mUnSupportPlugin.add(PLUGIN_CARE_GO);
        mUnSupportPlugin.add(PLUGIN_CARE_GO_4G);
        mUnSupportPlugin.add(PLUGIN_SMART_CAT_LITTER_BOX);
    }

    private static class Holder {
        private final static AddMoreHelper instance = new AddMoreHelper();
    }

    public static AddMoreHelper getInstance() {
        return Holder.instance;
    }

    public void initTabTittles() {
        mTabTittle = new LinkedHashMap<>();
        String[] rootTittles;
        if (FUNCTION_MODE_HELIO_PRO == mFunctionMode) {
            rootTittles = getTabByResId(R.array.add_more_tab_root_tittle_helio_pro);
        } else if (FUNCTION_MODE_CUSTOMIZE == mFunctionMode) {
            rootTittles = getTabByResId(R.array.add_more_tab_root_tittle_customize);
        } else if (FUNCTION_MODE_BMT == mFunctionMode) {
            rootTittles = getTabByResId(R.array.add_more_tab_root_tittle_bmt);
        } else {
            rootTittles = getTabByResId(R.array.add_more_tab_root_tittle);
        }
        ArrayList<String> childTabs;
        for (String rootTittle : rootTittles) {
            childTabs = initContentTabTittle(rootTittle);
            if (childTabs.size() > 0) {
                mTabTittle.put(rootTittle, childTabs);
            }
        }
    }

    public void cleanTabTittles() {
        mTabTittle.clear();
    }

    public ArrayList<String> getRootTabTittles() {
        return new ArrayList<>(mTabTittle.keySet());
    }

    public ArrayList<String> getTabTittlesByParentTab(String parentTabName) {
        if (mTabTittle != null && mTabTittle.containsKey(parentTabName)) {
            return mTabTittle.get(parentTabName);
        }
        return new ArrayList<>();
    }

    /**
     * 初始化子Tab
     *
     * @param rootTabTittle 父Tab名称
     * @return 子Tab
     */
    private ArrayList<String> initContentTabTittle(String rootTabTittle) {
        ArrayList<String> tittles = new ArrayList<>();
        if (TextUtils.isEmpty(rootTabTittle)) {
            return tittles;
        }
        String[] tittleStr = null;
        switch (rootTabTittle) {
            case "Home":
                if (FUNCTION_MODE_HELIO_PRO == mFunctionMode) {
                    tittleStr = getTabByResId(R.array.add_more_tab_home_tittle_helio_pro);
                } else if (FUNCTION_MODE_CUSTOMIZE == mFunctionMode) {
                    tittleStr = getTabByResId(R.array.add_more_tab_home_tittle_customize);
                } else if (FUNCTION_MODE_BMT == mFunctionMode) {
                    tittleStr = getTabByResId(R.array.add_more_tab_home_tittle_bmt);
                } else {
                    tittleStr = getTabByResId(R.array.add_more_tab_home_tittle);
                }
                break;
            case "Personal":
                tittleStr = getTabByResId(R.array.add_more_tab_personal_tittle);
                break;
            case "Pets":
                tittleStr = getTabByResId(R.array.add_more_tab_pets_tittle);
                break;
        }
        if (null != tittleStr && tittleStr.length > 0) {
            tittles.addAll(Arrays.asList(tittleStr));
        }

        return tittles;
    }

    private String[] getTabByResId(@ArrayRes int resId) {
        return DinSaferApplication.getAppContext().getResources().getStringArray(resId);
    }

    public String getString(@StringRes int resId) {
        return DinSaferApplication.getAppContext().getResources().getString(resId);
    }

    /**
     * 根据标题获取添加设备的列表
     *
     * @param needAll  是否加载全部数据 true: 全部数据； false: 部分数据
     * @param onlyMore neeaAll为false时使用；true: 点击More后返回的数据; false: 超过6个显示为More的数据
     */
    public ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getContentListByTabTittle(String tabTittle, boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        if (TextUtils.isEmpty(tabTittle)) {
            DDLog.e(TAG, "Empty tab tittle.");
            return models;
        }

        switch (tabTittle) {
            case "Alarm System":
                models = getAlarmSystemModels(needAll, onlyMore);
                break;
            case "Elderly Care":
                models = getElderlyCareModels(needAll, onlyMore);
                break;
            case "Video Monitoring":
                models = getVideoMonitoringModels(needAll, onlyMore);
                break;
            case "Customized Scenes":
                models = getCustomizedScenesModels(needAll, onlyMore);
                break;
            case "Protection On-the-go":
                models = getProtectOnGoModels(needAll, onlyMore);
                break;
            case "Easy Cat Care":
                models = getEasyCatCareModels(needAll, onlyMore);
                break;
            case "Energy":
                models = getEnergyModels(needAll, onlyMore);
                break;
            default:
                DDLog.e(TAG, "Undefine tab content for tad: " + tabTittle);
                break;
        }

        return models;
    }

    /**
     * 获取Home-Alarm System 的页面内容
     */
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getAlarmSystemModels(boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        boolean hasPanel = CommonDataUtil.getInstance().isHadPanelNotDeleted();
        if (needAll || !onlyMore) {
            models.add(new AddMoreSceneModel(
                    new AddMoreSceneItem.AddMoreSceneItemBuilder()
                            .setSceneTittle(getString(R.string.scene_tittle_full_arm))
                            .setSceneHint(getString(R.string.scene_content_full_arm))
                            .setTittleLeftIconResId(R.drawable.icon_product_arm)
                            .setImagePath("json/animation_addmore_home_arm.json")
                            .createAddMoreSceneItem()));
            models.add(new AddMoreSceneModel(
                    new AddMoreSceneItem.AddMoreSceneItemBuilder()
                            .setSceneTittle(getString(R.string.scene_tittle_security_monitoring))
                            .setSceneHint(getString(R.string.scene_content_security_monitoring))
                            .setTittleLeftIconResId(R.drawable.icon_product_sos)
                            .setImagePath("json/animation_addmore_home_life.json")
                            .createAddMoreSceneItem()));
            models.add(new AddMoreTittleModel(new AddMoreTittleItem(getString(R.string.add_devices))));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_SMART_ALARM_SYSTEM)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_DOOR_WINDOW_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_VIBRATION_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_GARAGE_DOOR_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_MOTION_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_PET_IMMUNE_MOTION_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_ADJUSTABLE_MOTION_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
        }
        // 超过6个，显示More
        if (!needAll && !onlyMore) {
            models.add(new AddMoreMoreModel());
        }

        if (needAll || onlyMore) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_OUTDOOR_SIREN)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_INDOOR_SIREN)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_WATER_INTRUSION_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_SMOKE_SENSOR)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_RFID_TAG)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_KEYPAD)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
            if (AppConfig.Plugins.SUPPORT_WIRED_BRIDGE && AddMoreConfig.Plugins.SHOW_WIRED_BRIDGE) {
                models.add(new AddMorePluginModel(
                        new AddMorePluginItem.AddMorePluginItemBuilder()
                                .setPluginType(PLUGIN_WIRED_BRIDGE)
                                .setCanAdd(hasPanel)
                                .createAddMorePluginItem()));
            }
//        models.add(new AddMoreMoreModel());
            models.add(new AddMoreBottomModel());
        }
        return models;
    }

    /**
     * 获取Home-Elderly Care 的页面内容
     */
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getElderlyCareModels(boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        models.add(new AddMoreSceneModel(
                new AddMoreSceneItem.AddMoreSceneItemBuilder()
                        .setSceneTittle(getString(R.string.scene_tittle_life_radar))
                        .setSceneHint(getString(R.string.scene_content_life_radar))
                        .setTittleLeftIconResId(R.drawable.icon_product_radar)
                        .setImagePath("json/animation_addmore_home_radar.json")
                        .createAddMoreSceneItem()));
        models.add(new AddMoreTittleModel(new AddMoreTittleItem(getString(R.string.add_devices))));
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_LIFE_RADAR)
                        .createAddMorePluginItem()));
        models.add(new AddMoreBottomModel());
        return models;
    }

    /**
     * 获取Home-Video Monitoring 的页面内容
     */
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getVideoMonitoringModels(boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        models.add(new AddMoreSceneModel(
                new AddMoreSceneItem.AddMoreSceneItemBuilder()
                        .setSceneTittle(getString(R.string.scene_tittle_real_time_monitoring))
                        .setSceneHint(getString(R.string.scene_content_real_time_monitoring))
                        .setTittleLeftIconResId(R.drawable.icon_product_ipc)
                        .setImagePath("json/animation_addmore_home_video1.json")
                        .createAddMoreSceneItem()));
        models.add(new AddMoreSceneModel(
                new AddMoreSceneItem.AddMoreSceneItemBuilder()
                        .setSceneTittle(getString(R.string.scene_tittle_recording))
                        .setSceneHint(getString(R.string.scene_content_recording))
                        .setTittleLeftIconResId(R.drawable.icon_product_motion)
                        .setImagePath("json/animation_addmore_home_video2.json")
                        .createAddMoreSceneItem()));
        models.add(new AddMoreTittleModel(new AddMoreTittleItem(getString(R.string.add_devices))));
        if (AppConfig.Plugins.SUPPORT_DSCAM_V005) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_BATTERY_CAMERA)
                            .createAddMorePluginItem()));
        }
        if (AppConfig.Plugins.SUPPORT_DSCAM_V006) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_SOLAR_SECURITY_CAMERA)
                            .createAddMorePluginItem()));
        }
        if (AppConfig.Plugins.SUPPORT_DSCAM_V015) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRED_SECURITY_CAMERA)
                            .createAddMorePluginItem()));
        }
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_WIRELESS_OUTDOOR_CAMERA_BULLET)
                        .createAddMorePluginItem()));
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_PTZ_CAMERA)
                        .createAddMorePluginItem()));
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_WIRELESS_INDOOR_CAMERA_FIXED)
                        .createAddMorePluginItem()));
        models.add(new AddMoreBottomModel());
        return models;
    }

    /**
     * 获取Home-Customized Scenes 的页面内容
     */
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getCustomizedScenesModels(boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        models.add(new AddMoreSceneModel(
                new AddMoreSceneItem.AddMoreSceneItemBuilder()
                        .setSceneTittle(getString(R.string.scene_tittle_smart_following))
                        .setSceneHint(getString(R.string.scene_content_smart_following))
                        .setTittleLeftIconResId(R.drawable.icon_product_following)
                        .setImagePath("json/animation_addmore_home_following.json")
                        .createAddMoreSceneItem()));
        models.add(new AddMoreTittleModel(new AddMoreTittleItem(getString(R.string.add_devices))));
        boolean hasPanel = CommonDataUtil.getInstance().isHadPanelNotDeleted();
        if (AppConfig.Plugins.SUPPORT_SIGNAl_REPEATER_PLUG && AddMoreConfig.Plugins.SHOW_SIGNAL_REPEATER_PLUG) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_SIGNAL_REPEATER_PLUG)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
        }
        if (AppConfig.Plugins.SUPPORT_SMART_PLUG && AddMoreConfig.Plugins.SHOW_SMART_PLUG) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_SMART_PLUG_DSPH1)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
        }
        if (APIKey.IS_SHOW_SMART_BUTTON && AddMoreConfig.Plugins.SHOW_SMART_BUTTON) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_WIRELESS_SMART_BUTTON)
                            .setCanAdd(hasPanel)
                            .createAddMorePluginItem()));
        }
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_CUSTOM_REMOTE_CONTROLLER)
                        .setCanAdd(hasPanel)
                        .createAddMorePluginItem()));
        models.add(new AddMoreBottomModel());
        return models;
    }

    /**
     * 获取Home-Protection On-the-go 的页面内容
     */
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getProtectOnGoModels(boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        models.add(new AddMoreSceneModel(
                new AddMoreSceneItem.AddMoreSceneItemBuilder()
                        .setSceneTittle(getString(R.string.scene_tittle_protect_loved))
                        .setSceneHint(getString(R.string.scene_content_protect_loved))
                        .setTittleLeftIconResId(R.drawable.icon_product_carego)
                        .setImagePath("json/animation_addmore_personal_carego.json")
                        .createAddMoreSceneItem()));
        models.add(new AddMoreTittleModel(new AddMoreTittleItem(getString(R.string.add_devices))));
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_CARE_GO_4G)
                        .createAddMorePluginItem()));
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_CARE_GO)
                        .createAddMorePluginItem()));
        models.add(new AddMoreBottomModel());
        return models;
    }

    /**
     * 获取Home-Easy Cat Care 的页面内容
     */
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getEasyCatCareModels(boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        models.add(new AddMoreSceneModel(
                new AddMoreSceneItem.AddMoreSceneItemBuilder()
                        .setSceneTittle(getString(R.string.scene_tittle_clean_experience))
                        .setSceneHint(getString(R.string.scene_content_clean_experience))
                        .setTittleLeftIconResId(R.drawable.icon_product_catbox)
                        .setImagePath("json/animation_addmore_pet_catbox.json")
                        .createAddMoreSceneItem()));
        models.add(new AddMoreTittleModel(new AddMoreTittleItem(getString(R.string.add_devices))));
        models.add(new AddMorePluginModel(
                new AddMorePluginItem.AddMorePluginItemBuilder()
                        .setPluginType(PLUGIN_SMART_CAT_LITTER_BOX)
                        .createAddMorePluginItem()));
        models.add(new AddMoreBottomModel());
        return models;
    }

    /**
     * 获取Home-Energy 的页面内容
     */
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> getEnergyModels(boolean needAll, boolean onlyMore) {
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> models = new ArrayList<>();
        models.add(new AddMoreSceneModel(
                new AddMoreSceneItem.AddMoreSceneItemBuilder()
                        .setSceneTittle(getString(R.string.emaldo_power_station))
                        .setSceneHint(getString(R.string.scene_content_power_station))
                        .setTittleLeftIconResId(R.drawable.icon_product_bmt)
                        .setImagePath("json/animation_addmore_power_station.json")
                        .createAddMoreSceneItem()));
        models.add(new AddMoreTittleModel(new AddMoreTittleItem(getString(R.string.add_devices))));
        if (AppConfig.Plugins.SUPPORT_BMT_POWERSTORE) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_BMT_POWERSTORE)
                            .createAddMorePluginItem()));
        }

        if (AppConfig.Plugins.SUPPORT_BMT_POWER_PULSE) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_BMT_POWERSPULSE)
                            .createAddMorePluginItem()));
        }
//        if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE30) {
//            models.add(new AddMorePluginModel(
//                    new AddMorePluginItem.AddMorePluginItemBuilder()
//                            .setPluginType(PLUGIN_BMT_POWERCORE30)
//                            .createAddMorePluginItem()));
//        }
        if (AppConfig.Plugins.SUPPORT_BMT_POWERCORE20) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_BMT_POWERCORE20)
                            .createAddMorePluginItem()));
        }
        if (AppConfig.Plugins.SUPPORT_BMT_HP5000) {
            models.add(new AddMorePluginModel(
                    new AddMorePluginItem.AddMorePluginItemBuilder()
                            .setPluginType(PLUGIN_BMT)
                            .createAddMorePluginItem()));
        }
        models.add(new AddMoreBottomModel());
        return models;
    }

    /**
     * 获取配件图片ID
     *
     * @param pluginType 配件类型
     * @return 配件图片资源ID
     */
    public int getPluginBigIconResId(@AddMorePluginType int pluginType) {
        int iconResId;
        switch (pluginType) {
            case PLUGIN_SMART_ALARM_SYSTEM:
                iconResId = R.drawable.img_product_panel_s;
                break;
            case PLUGIN_CARE_GO:
                iconResId = R.drawable.img_product_carego;
                break;
            case PLUGIN_CARE_GO_4G:
                iconResId = R.drawable.img_product_carego4g;
                break;
            case PLUGIN_SMART_CAT_LITTER_BOX:
                iconResId = R.drawable.img_product_catbox;
                break;
            case PLUGIN_WIRELESS_DOOR_WINDOW_SENSOR:
                iconResId = R.drawable.img_product_door_sensor;
                break;
            case PLUGIN_WIRELESS_INDOOR_CAMERA_FIXED:
                iconResId = R.drawable.img_product_ipc_fixed;
                break;
            case PLUGIN_WIRELESS_MOTION_SENSOR:
            case PLUGIN_WIRELESS_PET_IMMUNE_MOTION_SENSOR:
            case PLUGIN_ADJUSTABLE_MOTION_SENSOR:
                iconResId = R.drawable.img_product_pir;
                break;
            case PLUGIN_WIRELESS_OUTDOOR_CAMERA_BULLET:
                iconResId = R.drawable.img_product_ipc_bullet;
                break;
            case PLUGIN_WIRELESS_OUTDOOR_SIREN:
                iconResId = R.drawable.img_product_outdoor_siren;
                break;
            case PLUGIN_WIRELESS_SMART_BULB:
                iconResId = R.drawable.img_product_smartbulb;
                break;
            case PLUGIN_WIRELESS_SMART_BUTTON:
                iconResId = R.drawable.img_product_smart_btn;
                break;
            case PLUGIN_WIRELESS_SIGNAL_REPEATER_PLUG:
            case PLUGIN_WIRELESS_SMART_PLUG_DSPH1:
                iconResId = R.drawable.img_product_smartplug_dsph1;
                break;
            case PLUGIN_WIRELESS_SMOKE_SENSOR:
                iconResId = R.drawable.img_product_smock_sensor;
                break;
            case PLUGIN_WIRELESS_WATER_INTRUSION_SENSOR:
                iconResId = R.drawable.img_product_water_sensor;
                break;
            case PLUGIN_LIFE_RADAR:
                iconResId = R.drawable.img_product_radar;
                break;
            case PLUGIN_WIRELESS_VIBRATION_SENSOR:
                iconResId = R.drawable.img_product_door_sensor_vibration;
                break;
            case PLUGIN_WIRELESS_GARAGE_DOOR_SENSOR:
                iconResId = R.drawable.img_product_door_sensor_rolling;
                break;
            case PLUGIN_WIRELESS_INDOOR_SIREN:
                iconResId = R.drawable.img_product_indoor_siren;
                break;
            case PLUGIN_RFID_TAG:
                iconResId = R.drawable.img_product_rfid;
                break;
            case PLUGIN_WIRELESS_KEYPAD:
                iconResId = R.drawable.img_product_keypad;
                break;
            case PLUGIN_WIRED_BRIDGE:
                iconResId = R.drawable.img_product_wired_bridge;
                break;
            case PLUGIN_PTZ_CAMERA:
                iconResId = R.drawable.img_product_ipc_ptz;
                break;
            case PLUGIN_WIFI_SMART_PLUG_DSP01A:
                iconResId = R.drawable.img_product_plug_dsp01a;
                break;
            case PLUGIN_CUSTOM_REMOTE_CONTROLLER:
                iconResId = R.drawable.img_product_remote;
                break;
            case PLUGIN_WIRELESS_BATTERY_CAMERA:
                iconResId = R.drawable.img_product_ipc_wireless;
                break;
            case PLUGIN_SOLAR_SECURITY_CAMERA:
                iconResId = R.drawable.img_product_ipc_solar;
                break;
            case PLUGIN_WIRED_SECURITY_CAMERA:
                iconResId = R.drawable.img_product_ipc_wired;
                break;
            case PLUGIN_BMT:
            case PLUGIN_BMT_POWERCORE20:
            case PLUGIN_BMT_POWERCORE30:
                iconResId = R.drawable.img_product_power_station;
                break;
            case PLUGIN_BMT_POWERSTORE:
                iconResId = R.drawable.img_product_ps1;
                break;
            case PLUGIN_BMT_POWERSPULSE:
                iconResId = R.drawable.img_product_vb1;
                break;
            default:
                iconResId = 0;
                DDLog.e(TAG, "Undefine plugin icon.");
                break;
        }

        return iconResId;
    }

    /**
     * 获取配件标题ID
     *
     * @param pluginType 配件类型
     * @return 配件标题资源ID
     */
    public int getPluginTittleResId(@AddMorePluginType int pluginType) {
        int tittleResId;
        switch (pluginType) {
            case PLUGIN_SMART_ALARM_SYSTEM:
                tittleResId = R.string.plugin_smart_alarm_system;
                break;
            case PLUGIN_CARE_GO:
                tittleResId = R.string.plugin_care_go;
                break;
            case PLUGIN_CARE_GO_4G:
                tittleResId = R.string.plugin_care_go_4g;
                break;
            case PLUGIN_SMART_CAT_LITTER_BOX:
                tittleResId = R.string.plugin_smart_cat_litter_box;
                break;
            case PLUGIN_WIRELESS_DOOR_WINDOW_SENSOR:
                tittleResId = R.string.plugin_window_contact_sensor;
                break;
            case PLUGIN_WIRELESS_INDOOR_CAMERA_FIXED:
                tittleResId = R.string.plugin_wireless_outdoor_camera_fixed;
                break;
            case PLUGIN_WIRELESS_MOTION_SENSOR:
                tittleResId = R.string.plugin_wireless_motion_sensor;
                break;
            case PLUGIN_WIRELESS_OUTDOOR_CAMERA_BULLET:
                tittleResId = R.string.plugin_wireless_outdoor_camera_bullet;
                break;
            case PLUGIN_WIRELESS_OUTDOOR_SIREN:
                tittleResId = R.string.plugin_wireless_outdoor_siren;
                break;
            case PLUGIN_WIRELESS_SMART_BULB:
                tittleResId = R.string.plugin_wireless_smart_bulb;
                break;
            case PLUGIN_WIRELESS_SMART_BUTTON:
                tittleResId = R.string.plugin_wireless_smart_button;
                break;
            case PLUGIN_WIRELESS_SIGNAL_REPEATER_PLUG:
                tittleResId = R.string.plugin_wireless_signal_repeater_plug;
                break;
            case PLUGIN_WIRELESS_SMART_PLUG_DSPH1:
                tittleResId = R.string.plugin_wireless_smart_plug_dsph1;
                break;
            case PLUGIN_WIRELESS_SMOKE_SENSOR:
                tittleResId = R.string.plugin_wireless_smoke_sensor;
                break;
            case PLUGIN_WIRELESS_WATER_INTRUSION_SENSOR:
                tittleResId = R.string.plugin_wireless_water_sensor;
                break;
            case PLUGIN_LIFE_RADAR:
                tittleResId = R.string.plugin_life_radar;
                break;
            case PLUGIN_WIRELESS_VIBRATION_SENSOR:
                tittleResId = R.string.plugin_wireless_vibration_sensor;
                break;
            case PLUGIN_WIRELESS_GARAGE_DOOR_SENSOR:
                tittleResId = R.string.plugin_wireless_garage_door_sensor;
                break;
            case PLUGIN_WIRELESS_PET_IMMUNE_MOTION_SENSOR:
                tittleResId = R.string.plugin_wireless_pet_immune_motion_sensor;
                break;
            case PLUGIN_WIRELESS_INDOOR_SIREN:
                tittleResId = R.string.plugin_wireless_indoor_siren;
                break;
            case PLUGIN_RFID_TAG:
                tittleResId = R.string.plugin_rfid_tag;
                break;
            case PLUGIN_WIRELESS_KEYPAD:
                tittleResId = R.string.plugin_wireless_keypad;
                break;
            case PLUGIN_WIRED_BRIDGE:
                tittleResId = R.string.wired_bridge;
                break;
            case PLUGIN_PTZ_CAMERA:
                tittleResId = R.string.plugin_ptz_camera;
                break;
            case PLUGIN_WIFI_SMART_PLUG_DSP01A:
                tittleResId = R.string.plugin_wifi_smart_plug_dsp01a;
                break;
            case PLUGIN_CUSTOM_REMOTE_CONTROLLER:
                tittleResId = R.string.plugin_custom_remote_controller;
                break;
            case PLUGIN_WIRELESS_BATTERY_CAMERA:
                tittleResId = R.string.plugin_wireless_battery_camera;
                break;
            case PLUGIN_SOLAR_SECURITY_CAMERA:
                tittleResId = R.string.plugin_solar_security_camera;
                break;
            case PLUGIN_WIRED_SECURITY_CAMERA:
                tittleResId = R.string.plugin_wired_security_camera;
                break;
            case PLUGIN_BMT:
                tittleResId = R.string.power_core_1_point_0;
                break;
            case PLUGIN_ADJUSTABLE_MOTION_SENSOR:
                tittleResId = R.string.plugin_adjustable_motion_sensor;
                break;
            case PLUGIN_BMT_POWERCORE20:
                tittleResId = R.string.power_core_2_point_0;
                break;
            case PLUGIN_BMT_POWERCORE30:
                tittleResId = R.string.power_core_3_point_0;
                break;
            case PLUGIN_BMT_POWERSTORE:
                tittleResId = R.string.power_store;
                break;

            case PLUGIN_BMT_POWERSPULSE:
                tittleResId = R.string.power_pulse;
                break;
            default:
                tittleResId = 0;
                DDLog.e(TAG, "Undefine plugin type.");
                break;
        }

        return tittleResId;
    }

    public @AddMoreSceneType
    int getSceneType(@AddMorePluginType int pluginType) {
        int sceneType;
        switch (pluginType) {
            case PLUGIN_SMART_ALARM_SYSTEM:
                sceneType = PLUGIN_SCENE_PANEL;
                break;
            case PLUGIN_PTZ_CAMERA:
            case PLUGIN_WIRELESS_OUTDOOR_CAMERA_BULLET:
            case PLUGIN_WIRELESS_INDOOR_CAMERA_FIXED:
                sceneType = PLUGIN_SCENE_IPC;
                break;
            case PLUGIN_WIRELESS_BATTERY_CAMERA:
                sceneType = PLUGIN_SCENE_IPC_V005;
                break;
            case PLUGIN_SOLAR_SECURITY_CAMERA:
                sceneType = PLUGIN_SCENE_IPC_V006;
                break;
            case PLUGIN_WIRED_SECURITY_CAMERA:
                sceneType = PLUGIN_SCENE_IPC_V015;
                break;
            case PLUGIN_CARE_GO:
            case PLUGIN_CARE_GO_4G:
                sceneType = PLUGIN_SCENE_CARE_GO;
                break;
            case PLUGIN_BMT:
                sceneType = PLUGIN_SCENE_BMT;
                break;
            case PLUGIN_BMT_POWERCORE20:
                sceneType = PLUGIN_SCENE_BMT_POWERCORE20;
                break;
            case PLUGIN_BMT_POWERCORE30:
                sceneType = PLUGIN_SCENE_BMT_POWERCORE30;
                break;
            case PLUGIN_BMT_POWERSTORE:
                sceneType = PLUGIN_SCENE_BMT_POWERSTORE;
                break;
            case PLUGIN_BMT_POWERSPULSE:
                sceneType = PLUGIN_SCENE_BMT_POWERPULSE;
                break;
            default:
                sceneType = PLUGIN_SCENE_RC_PLUGIN;
                break;
        }
        return sceneType;
    }

    /**
     * TODO 获取触发配对的配件类型
     */
    public String getTriggerRFPluginType(@AddMorePluginType int pluginType) {
        String triggerPluginType = "";
        switch (pluginType) {
            case PLUGIN_WIRELESS_DOOR_WINDOW_SENSOR:
                triggerPluginType = "Door Window Sensor";
                break;
            case PLUGIN_WIRELESS_MOTION_SENSOR:
            case PLUGIN_ADJUSTABLE_MOTION_SENSOR:
            case PLUGIN_WIRELESS_PET_IMMUNE_MOTION_SENSOR:
                triggerPluginType = "PIR Sensor";
                break;
            case PLUGIN_WIRELESS_VIBRATION_SENSOR:
                triggerPluginType = "Vibration Sensor";
                break;
            case PLUGIN_WIRELESS_GARAGE_DOOR_SENSOR:
                triggerPluginType = "Rolling Door Window Sensor";
                break;
            case PLUGIN_WIRELESS_INDOOR_SIREN:
            case PLUGIN_WIRELESS_OUTDOOR_SIREN:
                triggerPluginType = "Wireless Siren";
                break;
            case PLUGIN_WIRELESS_WATER_INTRUSION_SENSOR:
                triggerPluginType = "Liquid Sensor";
                break;
            case PLUGIN_WIRELESS_SMOKE_SENSOR:
                triggerPluginType = "Smoke Sensor";
                break;
            case PLUGIN_RFID_TAG:
                triggerPluginType = "RFID Tag";
                break;
            case PLUGIN_WIRELESS_KEYPAD:
                triggerPluginType = "Wireless Keypad";
                break;
            case PLUGIN_WIRED_BRIDGE:
                triggerPluginType = PluginConstants.NAME_WIRED_BRIDGE;
                break;
            case PLUGIN_WIRELESS_SIGNAL_REPEATER_PLUG:
                triggerPluginType = PluginConstants.NAME_SIGNAL_REPEATER_PLUG;
                break;
            case PLUGIN_WIRELESS_SMART_PLUG_DSPH1:
                triggerPluginType = "Smart Plug";
                break;
            case PLUGIN_WIRELESS_SMART_BUTTON:
                triggerPluginType = "Smart Button";
                break;
            case PLUGIN_WIRELESS_SMART_BULB:
                triggerPluginType = "WIFI Bulb";
                break;
            case PLUGIN_WIFI_SMART_PLUG_DSP01A:
                triggerPluginType = "WIFI Plug";
                break;
            case PLUGIN_CUSTOM_REMOTE_CONTROLLER:
                triggerPluginType = "Remote Controller";
                break;
        }

        return triggerPluginType;
    }

    /**
     * 获取配件标题
     *
     * @param pluginType 配件类型
     * @return 配件标题
     */
    public String getPluginTittleStringByType(@AddMorePluginType int pluginType) {
        if (isUnSupportPlugin(pluginType)) {
            return DinSaferApplication.getAppContext().getResources().getString(R.string.coming_soon);
        }

        int resId = getPluginTittleResId(pluginType);
        return getString(resId);
    }

    public TabInfo getCurrentRootTabInfo() {
        return mCurrentRootTabInfo;
    }

    public void setCurrentRootTabInfo(TabInfo currentRootTabInfo) {
        this.mCurrentRootTabInfo = currentRootTabInfo;
    }

    public TabInfo getCurrentContentTabInfo() {
        return mCurrentContentTabInfo;
    }

    public void setCurrentContentTabInfo(TabInfo currentContentTabInfo) {
        this.mCurrentContentTabInfo = currentContentTabInfo;
    }

    /**
     * 是否为暂未支持的配件类型
     *
     * @param pluginType 配件类型
     */
    public boolean isUnSupportPlugin(@AddMorePluginType int pluginType) {
        return mUnSupportPlugin.contains(pluginType);
    }

    /**
     * 获取AddMore功能的展示模式
     *
     * @return {@link #FUNCTION_MODE_ALPHAHOM}/{@link #FUNCTION_MODE_HELIO_PRO}/{@link #FUNCTION_MODE_OEM}/{@link #FUNCTION_MODE_CUSTOMIZE}
     */
    public int getFunctionMode() {
        return mFunctionMode;
    }
}
