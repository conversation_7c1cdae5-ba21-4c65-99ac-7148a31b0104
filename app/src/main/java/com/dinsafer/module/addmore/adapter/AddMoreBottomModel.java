package com.dinsafer.module.addmore.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAddMoreContentBottomBinding;
import com.dinsafer.module.addmore.view.AddMoreHelper;

/**
 * AddMore 底部边距Item
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/30 3:09 PM
 */
public class AddMoreBottomModel extends BaseAddMoreModel<ItemAddMoreContentBottomBinding> {
    public AddMoreBottomModel() {
        super(AddMoreHelper.ADD_MORE_CONTENT_VIEW_TYPE_PADDING);
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_add_more_content_bottom;
    }

    @Override
    public boolean onDo(View v) {
        return false;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAddMoreContentBottomBinding viewBinding) {

    }
}
