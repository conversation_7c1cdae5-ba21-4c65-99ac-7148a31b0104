package com.dinsafer.module.addmore.view;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.common.HomeManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAddMoreBinding;
import com.dinsafer.model.addmore.TabInfo;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.module.settting.ui.ScannerActivity;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.flyco.tablayout.listener.OnTabSelectListener;

import java.util.ArrayList;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

/**
 * 添加更多配件页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/29 6:39 PM
 */
public class AddMoreFragment extends BaseFragment {

    private FragmentAddMoreBinding mBinding;
    private CommonPagerAdapter mAdapter;
    private ArrayList<BaseFragment> mFragments;

    public static AddMoreFragment newInstance() {
        return new AddMoreFragment();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_add_more, container, false);
        AddMoreHelper.getInstance().initTabTittles();
        initView(mBinding.getRoot(), savedInstanceState);
        initData();
        return mBinding.getRoot();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.tittleBar.commonBarTitle.setLocalText(null);
        mBinding.tittleBar.vDivider.setVisibility(View.GONE);
        mBinding.tittleBar.rlParent.setBackgroundColor(getResources().getColor(R.color.add_more_page_bg_color));
        mBinding.tittleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.tittleBar.commonBarRightIcon.setImageResource(R.drawable.icon_scan);
        mBinding.tittleBar.commonBarRightIcon.setVisibility(LocalKey.GUEST == HomeManager.getInstance().getCurrentHome().getLevel() ? View.GONE : View.VISIBLE);
        mBinding.tittleBar.commonBarRightIcon.setOnClickListener(
                v -> ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE, true));

        ArrayList<String> rootTabTittles = AddMoreHelper.getInstance().getRootTabTittles();
        if (rootTabTittles == null || rootTabTittles.size() == 0) {
            DDLog.e(TAG, "initView:rootTabTittles is empty");
            return;
        }
        String[] localTittles = new String[rootTabTittles.size()];
        for (int i = 0; i < rootTabTittles.size(); i++) {
            localTittles[i] = Local.s(rootTabTittles.get(i));
        }
        mFragments = new ArrayList<>();
        for (int i = 0; i < rootTabTittles.size(); i++) {
            mFragments.add(AddMoreTabRootFragment.newInstance(new TabInfo(i, rootTabTittles.get(i))));
        }


        mAdapter = new CommonPagerAdapter(getFragmentManager(), mFragments);
        mBinding.vpAddMore.setCanSlide(true);
        mBinding.vpAddMore.setAdapter(mAdapter);
        if (1 == localTittles.length && AppConfig.Functions.SUPPORT_ADD_MORE_HIDE_TAB_FOR_ONE_ITEM) {
            mBinding.tabAddMore.setVisibility(View.GONE);
        } else {
            mBinding.tabAddMore.setVisibility(View.VISIBLE);
            mBinding.tabAddMore.setTextAppearanceResId(R.style.TextFamilyTittleL);
            mBinding.tabAddMore.setViewPager(mBinding.vpAddMore, localTittles);
            mBinding.tabAddMore.setOnTabSelectListener(new OnTabSelectListener() {
                @Override
                public void onTabSelect(int position) {
                    mBinding.vpAddMore.setCurrentItem(position);
                }

                @Override
                public void onTabReselect(int position) {
                }
            });
        }
        mBinding.vpAddMore.setOffscreenPageLimit(rootTabTittles.size());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        AddMoreHelper.getInstance().cleanTabTittles();
    }
}
