package com.dinsafer.module.addmore.view;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.module.BaseFragment;
import com.dinsafer.util.DDLog;

import java.util.List;

/**
 * 懒加载Fragment
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/6 4:30 PM
 */
public class LazyBaseFragment extends BaseFragment {
    private boolean mViewCreated; // View是否已被创建
    private boolean mViewVisible; // 当前Fragment是否可见
    private boolean mFirstVisible = true; // 当前Fragment是否第一次可见

    /**
     * Fragment首次可见
     * 在onDestroyView不被
     */
    protected void onFragmentFirstVisible() {
        DDLog.d(TAG, "onFragmentFirstVisible");
    }

    /**
     * Fragment进入可见状态
     * 可能会多次回调
     */
    protected void onFragmentResume() {
        DDLog.d(TAG, "onFragmentResume");
    }

    /**
     * Fragment进入不可见状态
     * 可能会多次回调
     */
    protected void onFragmentPause() {
        DDLog.d(TAG, "onFragmentPause");
    }

    /**
     * 分发当前Fragment的可见状态
     *
     * @param isVisible 当前Fragment是否可见
     */
    private void dispatchViewVisibleState(boolean isVisible) {
        if (isVisible && isParentInvisible()) {
            // 当前父容器不可见
            return;
        }

        if (mViewVisible == isVisible) {
            return;
        }

        mViewVisible = isVisible;
        if (isVisible) {
            if (mFirstVisible) {
                mFirstVisible = false;
                onFragmentFirstVisible();
            }

            onFragmentResume();
            dispatchChildViewVisibleState(true);
        } else {
            onFragmentPause();
            dispatchChildViewVisibleState(false);
        }
    }

    /**
     * 分发子Fragment的可见状态
     *
     * @param isVisible
     */
    private void dispatchChildViewVisibleState(boolean isVisible) {
        FragmentManager fragmentManager = getChildFragmentManager();
        List<Fragment> list = fragmentManager.getFragments();

        for (Fragment fg : list) {
            if (fg instanceof LazyBaseFragment
                    && !fg.isHidden() && fg.getUserVisibleHint()) {
                ((LazyBaseFragment) fg).dispatchViewVisibleState(isVisible);
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View page = super.onCreateView(inflater, container, savedInstanceState);
        mViewCreated = true;
        if (!isHidden() && getUserVisibleHint()) {
            dispatchViewVisibleState(true);
        }
        return page;
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mViewCreated = false;
        mViewVisible = false;
        mFirstVisible = true;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!mFirstVisible) {
            if (!isHidden() && !mViewVisible && getUserVisibleHint()) {
                dispatchViewVisibleState(true);
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mViewVisible && getUserVisibleHint()) {
            dispatchViewVisibleState(false);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (mViewCreated) {
            if (mViewVisible && !isVisibleToUser) {
                dispatchViewVisibleState(false);
            } else if (!mViewVisible && isVisibleToUser) {
                dispatchViewVisibleState(true);
            }
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        dispatchViewVisibleState(!hidden);
    }

    /**
     * 父Fragment是否可见
     *
     * @return
     */
    private boolean isParentInvisible() {
        Fragment parent = getParentFragment();
        DDLog.d(TAG, "getParentFragment:" + parent + "");
        if (parent instanceof LazyBaseFragment) {
            LazyBaseFragment lz = (LazyBaseFragment) parent;
            return !lz.mViewVisible;
        }

        return false;// 默认可见
    }
}
