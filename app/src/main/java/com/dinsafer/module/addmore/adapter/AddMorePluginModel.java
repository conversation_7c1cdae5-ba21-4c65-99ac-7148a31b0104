package com.dinsafer.module.addmore.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAddMoreContentPluginBinding;
import com.dinsafer.model.addmore.AddMorePluginItem;
import com.dinsafer.module.addmore.view.AddMoreHelper;
import com.dinsafer.util.Local;

/**
 * AddMore 底部配件类型Item
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/30 3:09 PM
 */
public class AddMorePluginModel extends BaseAddMoreModel<ItemAddMoreContentPluginBinding> {
    private AddMorePluginItem mData;
    private OnAddMorePluginItemClickListener mListener;

    public AddMorePluginModel(AddMorePluginItem data) {
        super(AddMoreHelper.ADD_MORE_CONTENT_VIEW_TYPE_PLUGIN);
        this.mData = data;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_add_more_content_plugin;
    }

    @Override
    public boolean onDo(View v) {
        if (!mData.isCanAdd()
                || AddMoreHelper.getInstance().isUnSupportPlugin(mData.getPluginType())) {
            return false;
        }

        int targetId = v.getId();
        if (null != mListener) {
            if (R.id.iv_plugin_add == targetId) {
                mListener.onAddIconClick(v, mData.getPluginType());
            } else if (R.id.ll_add_more_plugin_item_root == targetId) {
                mListener.onPluginItemClick(v, mData.getPluginType());
            }
        }

        return false;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAddMoreContentPluginBinding viewBinding) {
        int pluginType = mData.getPluginType();
        String name = Local.s(AddMoreHelper.getInstance().getPluginTittleStringByType(pluginType));
        viewBinding.tvPluginName.setLocalText(name);
        viewBinding.ivPluginIcon.setImageResource(AddMoreHelper.getInstance().getPluginBigIconResId(pluginType));
        if (AddMoreHelper.getInstance().isUnSupportPlugin(pluginType)) {
            viewBinding.ivPluginAdd.setVisibility(View.INVISIBLE);
        } else {
            viewBinding.ivPluginAdd.setVisibility(View.VISIBLE);
            viewBinding.ivPluginAdd.setAlpha(mData.isCanAdd() ? 1.0f : 0.5f);
        }
    }

    public void setOnPluginItemClickListener(OnAddMorePluginItemClickListener listener) {
        this.mListener = listener;
    }

}
