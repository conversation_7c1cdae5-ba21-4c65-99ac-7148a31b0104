package com.dinsafer.module.addmore.adapter;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAddMoreContentTittleBinding;
import com.dinsafer.model.addmore.AddMoreTittleItem;
import com.dinsafer.module.addmore.view.AddMoreHelper;

/**
 * AddMore 中间标题类型Item
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/30 3:09 PM
 */
public class AddMoreTittleModel extends BaseAddMoreModel<ItemAddMoreContentTittleBinding> {
    private final AddMoreTittleItem mData;

    public AddMoreTittleModel(AddMoreTittleItem data) {
        super(AddMoreHelper.ADD_MORE_CONTENT_VIEW_TYPE_TITTLE);
        this.mData = data;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_add_more_content_tittle;
    }

    @Override
    public boolean onDo(View v) {
        return false;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAddMoreContentTittleBinding viewBinding) {
        viewBinding.tvTittle.setLocalText(mData.getTittle());
    }
}
