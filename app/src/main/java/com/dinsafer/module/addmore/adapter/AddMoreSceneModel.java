package com.dinsafer.module.addmore.adapter;

import android.text.TextUtils;
import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemAddMoreContentSceneBinding;
import com.dinsafer.model.addmore.AddMoreSceneItem;
import com.dinsafer.module.addmore.view.AddMoreHelper;

/**
 * AddMore 顶部带图类型Item
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/30 3:09 PM
 */
public class AddMoreSceneModel extends BaseAddMoreModel<ItemAddMoreContentSceneBinding> {
    private AddMoreSceneItem mData;

    public AddMoreSceneModel(AddMoreSceneItem data) {
        super(AddMoreHelper.ADD_MORE_CONTENT_VIEW_TYPE_SCENE);
        this.mData = data;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_add_more_content_scene;
    }

    @Override
    public boolean onDo(View v) {
        return false;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemAddMoreContentSceneBinding viewBinding) {
        viewBinding.tvSceneTittle.setLocalText(mData.getSceneTittle());
        viewBinding.tvSceneHint.setLocalText(mData.getSceneHint());
        viewBinding.ivSceneTittle.setImageResource(mData.getTittleLeftIconResId());

        if (holder.itemView.getTag(R.id.add_more_scene_item_listener_tag_id) != null) {
            //移除旧的监听器
            holder.itemView.removeOnAttachStateChangeListener((View.OnAttachStateChangeListener)
                    holder.itemView.getTag(R.id.add_more_scene_item_listener_tag_id));
        }
        View.OnAttachStateChangeListener listener = new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                final String uri = mData.getImagePath();
                viewBinding.ivSceneImage.setAnimation(uri);
                viewBinding.ivSceneImage.playAnimation();
            }

            @Override
            public void onViewDetachedFromWindow(View v) {
                viewBinding.ivSceneImage.cancelAnimation();
            }
        };
        holder.itemView.addOnAttachStateChangeListener(listener);
        holder.itemView.setTag(R.id.add_more_scene_item_listener_tag_id, listener);

    }
}
