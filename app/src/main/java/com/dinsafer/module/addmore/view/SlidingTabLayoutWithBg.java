package com.dinsafer.module.addmore.view;

import android.content.Context;

import androidx.annotation.StyleRes;

import android.util.AttributeSet;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.dinnet.R;
import com.dinsafer.util.DensityUtils;
import com.flyco.tablayout.SlidingTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;

/**
 * 添加Tab背景
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/9/26 11:36 上午
 */
public class SlidingTabLayoutWithBg extends SlidingTabLayout {

    private @StyleRes
    int mTextAppearanceResId = -1;
    private boolean showBackground;
    private OnTabSelectListener mOriginalListener;
    private final OnTabSelectListener mWrapper = new OnTabSelectListener() {
        @Override
        public void onTabSelect(int position) {
            updateTabBgStyle(position);
            if (null != mOriginalListener) {
                mOriginalListener.onTabSelect(position);
            }
        }

        @Override
        public void onTabReselect(int position) {
            if (null != mOriginalListener) {
                mOriginalListener.onTabReselect(position);
            }
        }
    };

    public SlidingTabLayoutWithBg(Context context) {
        this(context, null);
    }

    public SlidingTabLayoutWithBg(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SlidingTabLayoutWithBg(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        super.setOnTabSelectListener(mWrapper);
    }

    @Override
    public void notifyDataSetChanged() {
        super.notifyDataSetChanged();
        updateTabBgStyle(getCurrentTab());
    }

    @Override
    public void setOnTabSelectListener(OnTabSelectListener listener) {
        mOriginalListener = listener;
    }

    @Override
    public void addNewTab(String title) {
        super.addNewTab(title);
        updateTabBgStyle(getCurrentTab());
    }

    private void updateTabBgStyle(final int current) {
        if (getChildCount() > 0 && getChildAt(0) instanceof LinearLayout) {
            LinearLayout container = (LinearLayout) getChildAt(0);
            int childCount = container.getChildCount();
            final int currentTab = (0 > current || current >= childCount) ? getCurrentTab() : current;
            if (childCount > 0) {
                for (int i = 0; i < childCount; i++) {
                    if (container.getChildAt(i) instanceof RelativeLayout) {
                        if (showBackground) {
                            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) container.getChildAt(i).getLayoutParams();
                            layoutParams.leftMargin = DensityUtils.dp2px(getContext(), 5);
                            layoutParams.rightMargin = DensityUtils.dp2px(getContext(), 5);
                            container.getChildAt(i).setBackgroundResource(currentTab == i
                                    ? R.drawable.shape_add_more_second_tittle_bg
                                    : R.drawable.shape_add_more_second_tittle_bg_def);
                        }

                        if (-1 != mTextAppearanceResId) {
                            ((TextView) container.getChildAt(i).findViewById(R.id.tv_tab_title)).setTextAppearance(getContext(), mTextAppearanceResId);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void onPageSelected(int position) {
        super.onPageSelected(position);
        updateTabBgStyle(position);
    }

    public void setTextAppearanceResId(int textAppearanceResId) {
        this.mTextAppearanceResId = textAppearanceResId;
    }

    public void setShowBackground(boolean showBackground) {
        this.showBackground = showBackground;
    }
}
