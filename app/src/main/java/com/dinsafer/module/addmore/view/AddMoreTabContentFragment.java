package com.dinsafer.module.addmore.view;

import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.HomeManager;
import com.dinsafer.common.widget.rv.BindMultiAdapter;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAddMoreTabContentBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamBleScanFragment;
import com.dinsafer.model.addmore.TabInfo;
import com.dinsafer.module.add.ui.BLeStepScanDeviceFragment;
import com.dinsafer.module.addmore.adapter.AddMoreMoreModel;
import com.dinsafer.module.addmore.adapter.AddMorePluginModel;
import com.dinsafer.module.addmore.adapter.BaseAddMoreModel;
import com.dinsafer.module.addmore.adapter.OnAddMoreMoreItemClickListener;
import com.dinsafer.module.addmore.adapter.OnAddMorePluginItemClickListener;
import com.dinsafer.module.main.view.DeviceStatusDetailFragment;
import com.dinsafer.module.powerstation.device.PSBleScanFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.ScannerActivity;
import com.dinsafer.module.settting.ui.TiggleDeviceFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.common.utils.DensityUtil;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.ArrayList;

/**
 * AddMore小分类内容页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/29 7:13 PM
 */
public class AddMoreTabContentFragment extends LazyBaseFragment
        implements OnAddMorePluginItemClickListener, OnAddMoreMoreItemClickListener, ActionSheet.ActionSheetListener {
    private static final String KEY_TAB_INFO = "tab_info";

    private FragmentAddMoreTabContentBinding mBinding;
    private TabInfo mCurrentTabInfo;
    private BindMultiAdapter<BaseAddMoreModel<? extends ViewDataBinding>> mAdapter;
    private ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> mDatas;
    private int mCurrentAddPluginType;

    public static AddMoreTabContentFragment newInstance(TabInfo tabInfo) {
        AddMoreTabContentFragment fragment = new AddMoreTabContentFragment();
        Bundle args = new Bundle();
        args.putParcelable(KEY_TAB_INFO, tabInfo);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_add_more_tab_content, container, false);
        mCurrentTabInfo = getArguments().getParcelable(KEY_TAB_INFO);
        initView(mBinding.getRoot(), savedInstanceState);
        initData();
        super.onCreateView(inflater, container, savedInstanceState);
        return mBinding.getRoot();
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        if (null == mCurrentTabInfo) {
            DDLog.e(TAG, "onFragmentFirstVisible:mCurrentTabInfo is empty");
            return;
        }
        DDLog.i(TAG, mCurrentTabInfo.getTabTittle());
        mAdapter = new BindMultiAdapter<>();
        mBinding.rcvContent.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rcvContent.setAdapter(mAdapter);
        mDatas = new ArrayList<>();
        mDatas.addAll(AddMoreHelper.getInstance().getContentListByTabTittle(mCurrentTabInfo.getTabTittle(), false, false));
        mAdapter.setNewData(mDatas);
        for (BaseAddMoreModel<? extends ViewDataBinding> item : mDatas) {
            if (item instanceof AddMorePluginModel) {
                ((AddMorePluginModel) item).setOnPluginItemClickListener(this);
            } else if (item instanceof AddMoreMoreModel) {
                ((AddMoreMoreModel) item).setOnMoreClickListener(this);
            }
        }
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        AddMoreHelper.getInstance().setCurrentContentTabInfo(mCurrentTabInfo);
    }

    @Override
    public void onAddIconClick(View view, @AddMoreHelper.AddMorePluginType int pluginType) {
        DDLog.i(TAG, "onAddIconClick, pluginType: " + pluginType);
        printCurrentTabInfo();
        if (shouldShowDetailDialog(pluginType)) {
            showDetailDialog(pluginType);
        } else {
            onAddPluginClick(pluginType);
        }
    }

    @Override
    public void onPluginItemClick(View view, @AddMoreHelper.AddMorePluginType int pluginType) {
        DDLog.i(TAG, "onPluginItemClick, pluginType: " + pluginType);
        printCurrentTabInfo();
        if (shouldShowDetailDialog(pluginType)) {
            showDetailDialog(pluginType);
        } else {
            onAddPluginClick(pluginType);
        }
    }

    private boolean shouldShowDetailDialog(@AddMoreHelper.AddMorePluginType int pluginType) {
        int sceneType = AddMoreHelper.getInstance().getSceneType(pluginType);
        if (AddMoreHelper.PLUGIN_SCENE_BMT == sceneType
                || AddMoreHelper.PLUGIN_SCENE_BMT_POWERCORE20 == sceneType
                || AddMoreHelper.PLUGIN_SCENE_BMT_POWERCORE30 == sceneType
                || AddMoreHelper.PLUGIN_SCENE_BMT_POWERSTORE == sceneType
                || AddMoreHelper.PLUGIN_SCENE_BMT_POWERPULSE == sceneType) {
            return false;
        }

        if (AddMoreHelper.PLUGIN_SCENE_PANEL == sceneType) {
            return true;
        }

        return true;
    }

    private void showDetailDialog(@AddMoreHelper.AddMorePluginType final int pluginType) {
        final Dialog bottomDialog = new Dialog(getContext(), R.style.BottomDialog);
        View contentView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_more_plugin_detail, null);
        ((ImageView) contentView.findViewById(R.id.iv_plugin_icon)).setImageResource(
                AddMoreHelper.getInstance().getPluginBigIconResId(pluginType));
        ((LocalTextView) contentView.findViewById(R.id.tv_no_accessories)).setLocalText(getContext().getString(R.string.add_more_no_accessories));
        ((LocalTextView) contentView.findViewById(R.id.tv_no_accessories)).setVisibility(
                TextUtils.isEmpty(APIKey.ADD_MORE_BUY_URL)
                        ? View.INVISIBLE
                        : View.VISIBLE);
        contentView.findViewById(R.id.tv_no_accessories).setOnClickListener(v -> {
            onNoAccessionClick(pluginType);
            if (bottomDialog.isShowing())
                bottomDialog.dismiss();
        });
        ((LocalCustomButton) contentView.findViewById(R.id.btn_add)).setLocalText(getContext().getString(R.string.add));
        contentView.findViewById(R.id.btn_add).setOnClickListener(v -> {
            onAddPluginClick(pluginType);
            if (bottomDialog.isShowing())
                bottomDialog.dismiss();
        });
        contentView.findViewById(R.id.btn_add).setVisibility(LocalKey.GUEST == HomeManager.getInstance().getCurrentHome().getLevel() ? View.GONE : View.VISIBLE);
        contentView.findViewById(R.id.iv_close).setOnClickListener(v -> {
            if (bottomDialog.isShowing())
                bottomDialog.dismiss();
        });
        bottomDialog.setContentView(contentView);
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) contentView.getLayoutParams();
        params.width = getResources().getDisplayMetrics().widthPixels - DensityUtil.dp2px(getContext(), 16f);
        contentView.setLayoutParams(params);
        bottomDialog.getWindow().setGravity(Gravity.BOTTOM);
        bottomDialog.getWindow().setWindowAnimations(R.style.BottomDialog_Animation);
        bottomDialog.show();
    }

    private void onAddPluginClick(@AddMoreHelper.AddMorePluginType final int pluginType) {
        DDLog.i(TAG, "onAddPluginClick, pluginType: " + pluginType);
        mCurrentAddPluginType = pluginType;
        int sceneType = AddMoreHelper.getInstance().getSceneType(pluginType);
        switch (sceneType) {
            case AddMoreHelper.PLUGIN_SCENE_RC_PLUGIN:
                if (AppConfig.Functions.SUPPORT_TRIGGER_ADD_PLUGIN) {
                    // 支持触发配对添加配件-弹窗让用户选择添加方式
                    ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                                    getDelegateActivity().getSupportFragmentManager())
                            .setTitle(false)
                            .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                            .setOtherButtonTitles(Local.s(getResources().getString(R.string.device_management_add_scanQR)),
                                    Local.s(getResources().getString(R.string.devie_management_add_tiggle)))
                            .setCancelableOnTouchOutside(true)
                            .setListener(this).show();
                } else {
                    // 不支持触发配对添加，直接跳转扫描页
                    getMainActivity().setNotNeedToLogin(true);
                    ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
                }
                break;
            case AddMoreHelper.PLUGIN_SCENE_PANEL:
                if (CommonDataUtil.getInstance().isHadPanelNotDeleted()) {
                    final String panelId = CommonDataUtil.getInstance().getCurrentDeviceId();
                    boolean online = CommonDataUtil.getInstance().isPanelOnline();
                    if (online) {
                        getDelegateActivity().addCommonFragment(DeviceStatusDetailFragment.newInstance());
                    } else {
                        getDelegateActivity().addCommonFragment(BLeStepScanDeviceFragment.newInstance(panelId));
                    }
                } else {
                    getMainActivity().addCommonFragment(BLeStepScanDeviceFragment.newInstance());
                }
                break;

            case AddMoreHelper.PLUGIN_SCENE_IPC:
                getMainActivity().setNotNeedToLogin(true);
                ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
                break;
            case AddMoreHelper.PLUGIN_SCENE_IPC_V005:
                getDelegateActivity().addCommonFragment(DsCamBleScanFragment.newInstance(DinConst.TYPE_DSCAM));
                break;
            case AddMoreHelper.PLUGIN_SCENE_IPC_V006:
                getDelegateActivity().addCommonFragment(DsCamBleScanFragment.newInstance(DinConst.TYPE_DSCAM_VOO6));
                break;
            case AddMoreHelper.PLUGIN_SCENE_IPC_V015:
                getDelegateActivity().addCommonFragment(DsCamBleScanFragment.newInstance(DinConst.TYPE_DSCAM_VO15));
                break;
            case AddMoreHelper.PLUGIN_SCENE_BMT:
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_HP5000));
                break;
            case AddMoreHelper.PLUGIN_SCENE_BMT_POWERCORE20:
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERCORE20));
                break;
            case AddMoreHelper.PLUGIN_SCENE_BMT_POWERCORE30:
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERCORE30));
                break;
            case AddMoreHelper.PLUGIN_SCENE_BMT_POWERSTORE:
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERSTORE));
                break;
            case AddMoreHelper.PLUGIN_SCENE_BMT_POWERPULSE:
                getDelegateActivity().addCommonFragment(PSBleScanFragment.newInstance(DinConst.TYPE_BMT_POWERPULSE));
                break;
            default:
                // TODO 其他类型的AddMore处理
                break;
        }
    }

    private void onNoAccessionClick(@AddMoreHelper.AddMorePluginType final int pluginType) {
        DDLog.i(TAG, "onNoAccessionClick, pluginType: " + pluginType);

        Uri uri = Uri.parse(APIKey.ADD_MORE_BUY_URL);
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.setData(uri);
        startActivity(intent);
    }

    @Override
    public void onMoreClick(View view) {
        DDLog.i(TAG, "onMoreClick.");
        printCurrentTabInfo();
        if (null == mDatas || mDatas.size() < 1) {
            return;
        }

        int lastIndex = mDatas.size() - 1;
        BaseAddMoreModel<? extends ViewDataBinding> moreItem = mDatas.get(lastIndex);
        if (moreItem instanceof AddMoreMoreModel) {
            mDatas.remove(lastIndex);
            mAdapter.notifyItemRemoved(lastIndex);
        }

        if (null == mCurrentTabInfo) {
            DDLog.e(TAG, "mCurrentTabInfo is empty");
            return;
        }
        ArrayList<BaseAddMoreModel<? extends ViewDataBinding>> newItems = AddMoreHelper.getInstance().getContentListByTabTittle(mCurrentTabInfo.getTabTittle(), false, true);
        if (null != newItems && 0 < newItems.size()) {
            for (BaseAddMoreModel<? extends ViewDataBinding> item : newItems) {
                if (item instanceof AddMorePluginModel) {
                    ((AddMorePluginModel) item).setOnPluginItemClickListener(this);
                }
            }
            lastIndex = mDatas.size();
            mDatas.addAll(newItems);
            mAdapter.notifyItemRangeInserted(lastIndex, newItems.size());
        }
    }

    private void printCurrentTabInfo() {
        final TabInfo currentRootTabInfo = AddMoreHelper.getInstance().getCurrentRootTabInfo();
        final TabInfo currentContentTabInfo = AddMoreHelper.getInstance().getCurrentContentTabInfo();
        if (null == currentRootTabInfo || null == currentContentTabInfo) {
            DDLog.e(TAG, "currentRootTabInfo is empty or currentContentTabInfo is empty");
            return;
        }
        DDLog.i(TAG, "Current root tab name: "
                + currentRootTabInfo.getTabTittle() +
                ", current content tab name: "
                + currentContentTabInfo.getTabTittle());
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        if (index == 0) {
            getMainActivity().setNotNeedToLogin(true);
            ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
        } else {
            getDelegateActivity().addCommonFragment(TiggleDeviceFragment.newInstance(
                    AddMoreHelper.getInstance().getTriggerRFPluginType(mCurrentAddPluginType)
            ));
        }
    }

    /**
     * 家庭已经有主机提示
     */
    private void showFamilyHadPanelDialog() {
        AlertDialog.createBuilder(getContext())
                .setOk("OK")
                .setContent(getString(R.string.ble_step_had_panel_hint))
                .preBuilder()
                .show();
    }
}
