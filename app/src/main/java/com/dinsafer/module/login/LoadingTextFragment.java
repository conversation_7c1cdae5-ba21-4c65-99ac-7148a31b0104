package com.dinsafer.module.login;

import android.animation.ValueAnimator;
import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FramgentLoadingTextBinding;
import com.dinsafer.module.MyBaseFragment;

public class LoadingTextFragment extends MyBaseFragment<FramgentLoadingTextBinding> {

    public static final int BLACK = 0;

    public static final int BLUE = 1;

    public boolean isCanBack = false;

    public boolean showText = true;

    public static LoadingTextFragment newInstance() {
        return newInstance(BLACK, "Connecting..", true, false);
    }

    public static LoadingTextFragment newInstance(int blackOrBlue) {
        return newInstance(blackOrBlue, "Connecting..", true, false);
    }

    public static LoadingTextFragment newInstance(int blackOrBlue, String description) {
        return newInstance(blackOrBlue, description, true, false);
    }

    public static LoadingTextFragment newInstance(int blackOrBlue, String description, boolean showText) {
        return newInstance(blackOrBlue, description, showText, false);
    }

    public static LoadingTextFragment newInstance(int blackOrBlue, String description, boolean showText, boolean canBack) {
        LoadingTextFragment loadingFragment = new LoadingTextFragment();
        Bundle args = new Bundle();
        args.putInt("blackOrBlue", blackOrBlue);
        args.putString("description", description);
        args.putBoolean("canBack", canBack);
        args.putBoolean("showText", showText);
        loadingFragment.setArguments(args);
        return loadingFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.framgent_loading_text;
    }

    @Override
    public void initData() {
        super.initData();
        Bundle arguments = getArguments();
        if (arguments != null) {
            if (arguments.getInt("blackOrBlue") == BLACK) {
                mBinding.clParent.setBackgroundColor(getResources().getColor(R.color.colorLoadingBlack));
            }
            isCanBack = arguments.getBoolean("canBack");
            mBinding.tvLoading.setLocalText(arguments.getString("description"));
            showText = arguments.getBoolean("showText");
            mBinding.tvLoading.setVisibility(showText ? View.VISIBLE : View.GONE);
        }

        String uri = "json/animation_loading.json";

        mBinding.lvLoading.setRepeatCount(ValueAnimator.INFINITE);
        mBinding.lvLoading.setAnimation(uri);
        mBinding.lvLoading.playAnimation();
    }

    @Override
    public boolean onBackPressed() {
        return !isCanBack;
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinding.lvLoading.pauseAnimation();
    }

    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        return null;
    }
}
