package com.dinsafer.module.login;

import android.animation.ValueAnimator;
import android.os.Bundle;
import android.view.ViewGroup;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.LodingLayoutBinding;
import com.dinsafer.module.MyBaseFragment;

/**
 * Created by Rinfon on 16/7/8.
 */
public class LoadingFragment extends MyBaseFragment<LodingLayoutBinding> {

    public static final int BLACK = 0;

    public static final int BLUE = 1;

    public boolean isCanBack = false;


    public static LoadingFragment newInstance(int blackOrBlue, String description, boolean canBack) {
        LoadingFragment loadingFragment = new LoadingFragment();
        Bundle args = new Bundle();
        args.putInt("blackOrBlue", blackOrBlue);
        args.putBoolean("canBack", canBack);
        args.putString("description", "");
        loadingFragment.setArguments(args);
        return loadingFragment;
    }

    public static LoadingFragment newInstance(int blackOrBlue, String description) {
        LoadingFragment loadingFragment = new LoadingFragment();
        Bundle args = new Bundle();
        args.putInt("blackOrBlue", blackOrBlue);
        args.putString("description", "");
        loadingFragment.setArguments(args);
        return loadingFragment;
    }

    public static LoadingFragment newInstance(int blackOrBlue, String description, int marginTop) {
        LoadingFragment loadingFragment = new LoadingFragment();
        Bundle args = new Bundle();
        args.putInt("blackOrBlue", blackOrBlue);
        args.putString("description", description);
        args.putInt("marginTop", marginTop);
        loadingFragment.setArguments(args);
        return loadingFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.loding_layout;
    }

    @Override
    public void initData() {
        super.initData();
        if (getArguments().getInt("blackOrBlue") == BLACK) {
            mBinding.lodingBackground.setBackgroundColor(getResources().getColor(R.color.colorLoadingBlack));
        }
        int marginTop = getArguments().getInt("marginTop");
        if (marginTop > 0) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mBinding.lodingBackground.getLayoutParams();
            layoutParams.topMargin = marginTop;
            mBinding.lodingBackground.setLayoutParams(layoutParams);
        }
        isCanBack = getArguments().getBoolean("canBack");
        mBinding.lodingText.setLocalText(getArguments().getString("description"));
        String uri = "json/animation_loading.json";

        mBinding.lodingImage.setRepeatCount(ValueAnimator.INFINITE);
        mBinding.lodingImage.setAnimation(uri);
        mBinding.lodingImage.playAnimation();

    }

    @Override
    public boolean onBackPressed() {
        return !isCanBack;
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinding.lodingImage.pauseAnimation();
    }

}

