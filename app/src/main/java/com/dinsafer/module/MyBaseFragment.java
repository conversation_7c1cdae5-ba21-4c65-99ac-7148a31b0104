package com.dinsafer.module;

import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.dinsafer.util.DDLog;

public abstract class MyBaseFragment<V extends ViewDataBinding> extends BaseFragment {

    protected V mBinding;

    protected boolean isInit = false;

    protected abstract int provideContentViewLayoutId();

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        DDLog.d(TAG, "onCreateView()");
        if (provideContentViewLayoutId() != 0) {

            mBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), provideContentViewLayoutId(), container, false);
            initView(mBinding.getRoot(), savedInstanceState);
            initData();
            isInit = true;
            return mBinding.getRoot();
        } else {
            throw new IllegalArgumentException("provideContentViewLayoutId(): you should provide a layout id!");
        }



    }

    @Override
    public void onResume() {
        super.onResume();
        DDLog.d(TAG, "onResume()");
    }

    /**
     * 该方法在onResume()之后调用
     */
    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
//        isInit = true;
        DDLog.d(TAG, "onFinishAnim()");
//        initData();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        DDLog.d(TAG, "setUserVisibleHint():" + isVisibleToUser);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        DDLog.d(TAG, "onDestroyView()");
    }

}