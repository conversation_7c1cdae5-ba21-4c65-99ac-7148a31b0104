package com.dinsafer.module.daily;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;

import com.blankj.utilcode.util.FileUtils;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityDailyMemoriesVideoPlayBinding;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.module.ipc.common.video.HttpVideoDownLoadTask;
import com.dinsafer.module.ipc.common.video.VideoDownloadQueue;
import com.dinsafer.module.ipc.heartlai.record.DownloadListener;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.ui.CustomButton;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.TimeUtil;
import com.dinsafer.util.VideoCopyUtil;
import com.githang.statusbar.StatusBarCompat;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import tv.danmaku.ijk.media.player.IjkMediaPlayer;


public class DailyMemoriesVideoPlayActivity extends BaseMainActivity {
    private String TAG = getClass().getSimpleName();

    private ActivityDailyMemoriesVideoPlayBinding mBinding;

    private String url;
    private String ipcId;
    private long date;
    private String rid;
    private boolean isFullScreen = false;
    private SimpleDateFormat sdf = new SimpleDateFormat("mm:ss");
    private boolean isAnimation = true;
    private VideoDownloadQueue videoDownloadQueue;

    public static void start(Context context, String url, String ipcId, long date, String rid) {
        Intent starter = new Intent(context, DailyMemoriesVideoPlayActivity.class);
        starter.putExtra("url", url);
        starter.putExtra("ipcId", ipcId);
        starter.putExtra("date", date);
        starter.putExtra("rid", rid);
        context.startActivity(starter);
    }

    @Override
    protected boolean initVariables() {
        url = getIntent().getStringExtra("url");
        ipcId = getIntent().getStringExtra("ipcId");
        date = getIntent().getLongExtra("date", 0) * 1000;
        rid = getIntent().getStringExtra("rid");
        return true;
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        super.initViews(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_daily_memories_video_play);
        StatusBarCompat.setStatusBarColor(this, getResources().getColor(R.color.black), false);

        mBinding.commonBar.commonBarBack.setOnClickListener(v -> finish());
        mBinding.btnBackLand.setOnClickListener(v -> finish());
        mBinding.btnBackLand2.setOnClickListener(v -> finish());
        mBinding.resumePause.setOnClickListener(v -> clickPlay());
        mBinding.btnFunctionPlayLand.setOnClickListener(v -> {
            mBinding.llFunctionBtnLand.setVisibility(View.GONE);
            clickPlay();
        });
        mBinding.glviewFullscreen.setOnClickListener(v -> toFullScreen());

        mBinding.videoView.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_UP)
                clickPlay();
            return true;
        });

        mBinding.btnGoLivePort.setLocalText(getString(R.string.download));
        mBinding.btnGoLivePort.setOnClickListener(v -> {
            if (PermissionUtil.isStoragePermissionDeny(this)) {
                requestReadVideoPermission(this);
            } else {
                download();
            }
        });

        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mBinding.rlPlayer.getLayoutParams();
        layoutParams.height = (int) (ScreenUtils.getScreenWidth(this) * 0.53625f);
        mBinding.rlPlayer.setLayoutParams(layoutParams);
    }

    @Override
    protected void loadData() {
        EventBus.getDefault().register(this);
        // init player
        IjkMediaPlayer.loadLibrariesOnce(null);
        IjkMediaPlayer.native_profileBegin("libijkplayer.so");

        mBinding.commonBar.commonBarTitle.setText(getPageTitle());

        mBinding.videoView.setOnPreparedListener(mp -> {
            if (isAnimation) {
                isAnimation = false;
                mBinding.imgLoading.setVisibility(View.GONE);
                mBinding.glviewFullscreen.setVisibility(View.VISIBLE);
            }
            handler.post(run);
            mp.setOnCompletionListener(mp1 -> {
                mBinding.videoView.pause();
                mBinding.seekBar.setProgress(mBinding.seekBar.getMax());
                mBinding.seekBarFullscreen.setProgress(mBinding.seekBarFullscreen.getMax());

                final int duration = mBinding.videoView.getDuration();
                final String durationStr = sdf.format(duration);
                final String displayStr = String.format("%s/%s", durationStr, durationStr);
                mBinding.seekbarTime.setText(displayStr);
                mBinding.seekBarFullscreenTime.setText(displayStr);

                if (isFullScreen) {
                    mBinding.llFunctionBtnLand.setVisibility(View.VISIBLE);
                } else {
                    mBinding.resumePause.setVisibility(View.VISIBLE);
                }
            });
        });

        mBinding.seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    if (mBinding.videoView.isPlaying()) {
                        mBinding.videoView.pause();
                    }
                    onUserSeek(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                DDLog.d(TAG, "onStartTrackingTouch: ");
                mBinding.videoView.pause();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                DDLog.d(TAG, "onStopTrackingTouch: " + seekBar.getProgress() + "/" + seekBar.getSecondaryProgress());
                seekTo(seekBar.getProgress());
            }
        });
        mBinding.seekBarFullscreen.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    if (mBinding.videoView.isPlaying()) {
                        mBinding.videoView.pause();
                    }
                    onUserSeek(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                DDLog.d(TAG, "onStartTrackingTouch: ");
                mBinding.videoView.pause();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                DDLog.d(TAG, "onStopTrackingTouch: " + seekBar.getProgress() + "/" + seekBar.getSecondaryProgress());
                seekTo(seekBar.getProgress());
            }
        });

        mBinding.imgLoading.setVisibility(View.VISIBLE);
        mBinding.glviewFullscreen.setVisibility(View.GONE);

        mBinding.videoView.setVideoPath(url);
        mBinding.videoView.start();
    }

    public String getPageTitle() {
        return Local.s(getString(R.string.memory_of_date))
                .replace("#date", TimeUtil.long2Time(date, "yyyy-MM-dd"));
    }

    public void clickPlay() {
        if (mBinding.videoView.isPlaying()) {
            mBinding.resumePause.setVisibility(View.VISIBLE);
            mBinding.videoView.pause();
        } else {
            mBinding.resumePause.setVisibility(View.GONE);
            mBinding.videoView.start();
        }
    }

    public void toFullScreen() {
        if (!isFullScreen) {
            makeVideoFullScreen(false);
        } else {
            exitVideoFullScreen(false);
        }
        isFullScreen = !isFullScreen;
    }

    private LinearLayout.LayoutParams defaultVideoViewParams;
    private int defaultScreenOrientationMode;

    private void makeVideoFullScreen(boolean autoRotate) {
        defaultScreenOrientationMode = getResources().getConfiguration().orientation;
        defaultVideoViewParams = (LinearLayout.LayoutParams) mBinding.rlPlayer.getLayoutParams();
        if (!autoRotate) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
        mBinding.controlView.setVisibility(View.GONE);
        mBinding.btnBackLand.setVisibility(View.VISIBLE);
        mBinding.fullscreenControl.setVisibility(View.VISIBLE);
        mBinding.commonBar.getRoot().setVisibility(View.GONE);
        mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_small_screen);
        mBinding.rlPlayer.postDelayed(new Runnable() {

            @Override
            public void run() {
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) new LinearLayout.LayoutParams(
                        RelativeLayout.LayoutParams.MATCH_PARENT,
                        RelativeLayout.LayoutParams.MATCH_PARENT);

                mBinding.rlPlayer.setLayoutParams(params);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);
    }

    private void exitVideoFullScreen(boolean autoRotate) {
        if (autoRotate) {
            setRequestedOrientation(defaultScreenOrientationMode);
        } else {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
        mBinding.controlView.setVisibility(View.VISIBLE);
        mBinding.btnBackLand.setVisibility(View.GONE);
        mBinding.fullscreenControl.setVisibility(View.GONE);
        mBinding.llFunctionBtnLand.setVisibility(View.GONE);
        mBinding.commonBar.getRoot().setVisibility(View.VISIBLE);
        mBinding.commonBar.commonBarTitle.setText(getPageTitle());
        mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_full_screen);
        mBinding.rlPlayer.postDelayed(new Runnable() {

            @Override
            public void run() {
                mBinding.rlPlayer.setLayoutParams(defaultVideoViewParams);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);
    }

    private void download() {
        if (PermissionUtil.isStoragePermissionDeny(this)) {
            return;
        }
        if (videoDownloadQueue == null) {
            videoDownloadQueue = new VideoDownloadQueue();
            videoDownloadQueue.start();
        }
        StringBuilder sb = new StringBuilder(getExternalCacheDir().getAbsolutePath())
                .append(File.separator)
                .append("daily")
                .append(File.separator)
                .append(getPageTitle().replace(" ", "_").replace("-", "_").toLowerCase())
                .append("_")
                .append(rid)
                .append("_")
                .append(".mp4");
        String path = sb.toString();
        String finishPath = sb.insert(sb.lastIndexOf(".mp4"), System.currentTimeMillis()).toString();

        HttpVideoDownLoadTask task = new HttpVideoDownLoadTask(url, path, false);
        task.setDownLoadListener(new DownloadListener() {
            @Override
            public void onStart(String url) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setDownloadBtnEnable(false);
                    }
                });
            }

            @Override
            public void onProgress(String url, int progress) {
            }

            @Override
            public void onFinish(String url, String path) {
                try {
                    FileUtils.copy(path, finishPath);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                VideoCopyUtil.videoSaveToNotifyGalleryToRefreshWhenVersionGreaterQ(getApplicationContext(), new File(finishPath), "");
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setDownloadBtnEnable(true);
                        showTopToast(R.drawable.icon_toast_succeed, Local.s(getString(R.string.saved_to_album)));
                    }
                });
            }

            @Override
            public void onFail(String url, String errorInfo) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setDownloadBtnEnable(true);
                        showErrorToast();
                    }
                });
            }
        });
        videoDownloadQueue.add(task);
    }

    private void setDownloadBtnEnable(boolean enable) {
        //mBinding.btnGoLivePort.setAlpha(enable ? 1.0f : 0.5f);
        mBinding.btnGoLivePort.setEnabled(enable);
        mBinding.btnGoLivePort.setState(enable ? CustomButton.State.NORMAL : CustomButton.State.LOADING);

    }

    private void onUserSeek(int progress) {
        int duration = mBinding.seekBar.getMax();
        if (progress > duration) {
            progress = duration;
        }
        DDLog.d(TAG, "updateTime-->currentPosition:" + progress + " /duration:" + duration);
        mBinding.seekbarTime.setText(sdf.format(progress) + "/" + sdf.format(duration));
        mBinding.seekBarFullscreenTime.setText(sdf.format(progress) + "/" + sdf.format(duration));
    }

    private void seekTo(int progress) {
        mBinding.videoView.seekTo(progress);
        if (!mBinding.videoView.isPlaying()) {
            mBinding.videoView.start();
            mBinding.resumePause.setVisibility(View.GONE);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.d(TAG, "onConfigurationChanged: ");
        // Checks the orientation of the screen
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            isFullScreen = true;
            makeVideoFullScreen(true);
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            isFullScreen = false;
            exitVideoFullScreen(true);
        }
    }

    private Handler handler = new Handler();
    private Runnable run = new Runnable() {
        @Override
        public void run() {
            if (!mBinding.videoView.isPlaying()) {
                handler.postDelayed(run, 1000);
                return;
            }
            // 获得当前播放时间和当前视频的长度
            // 获得当前播放时间和当前视频的长度
            int currentPosition = mBinding.videoView.getCurrentPosition();
            int duration = mBinding.videoView.getDuration();
            // 设置进度条的主要进度，表示当前的播放时间
            mBinding.seekBar.setProgress(currentPosition);
            mBinding.seekBar.setMax(duration);
            mBinding.seekBarFullscreen.setProgress(currentPosition);
            mBinding.seekBarFullscreen.setMax(duration);
            mBinding.seekbarTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));
            mBinding.seekBarFullscreenTime.setText(sdf.format(currentPosition) + "/" + sdf.format(duration));

            handler.postDelayed(run, 1000);
        }
    };

    @Override
    public void onStop() {
        super.onStop();
        mBinding.videoView.release(true);
        mBinding.videoView.stopBackgroundPlay();
        IjkMediaPlayer.native_profileEnd();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        mBinding.videoView.setOnPreparedListener(null);
        mBinding.videoView.release(true);
        mBinding.videoView.stopBackgroundPlay();
        IjkMediaPlayer.native_profileEnd();
        handler.removeCallbacks(run);
        if (videoDownloadQueue != null) {
            videoDownloadQueue.stop();
            videoDownloadQueue.release();
        }
    }

    public void requestReadVideoPermission(@NonNull final Activity activity) {
        final String[] permission = PermissionUtil.getStoragePermissions();
        boolean denied = AndPermission.hasAlwaysDeniedPermission(activity, permission);
        AndPermission.with(activity)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {
                    download();
                })
                .onDenied(permissions -> {
                    DDLog.e(TAG, "Storage permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(activity, permissions)) {
                        showPermissionNotGrantTip(getString(R.string.ipc_save_snapshot_without_permission));
                    }
                })
                .start();
    }

    protected void showPermissionNotGrantTip(String tip) {
        AlertDialog.createBuilder(this)
                .setOk(getString(R.string.go_setting))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        goIntentSetting();
                    }
                })
                .setCancel(getString(R.string.cancel))
                .setContent(tip)
                .preBuilder()
                .show();
    }

    private void goIntentSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        try {
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayMotionDetectedRecordEvent(PlayMotionDetectedRecordEvent playMotionDetectedRecordEvent) {
        finish();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSOSevent(SOSevent soSevent) {
        finish();
    }
}

