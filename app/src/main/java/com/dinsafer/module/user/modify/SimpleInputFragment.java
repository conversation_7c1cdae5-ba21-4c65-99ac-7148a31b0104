package com.dinsafer.module.user.modify;

import android.os.Bundle;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentSimpleInputBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/11 18:50
 */
public abstract class SimpleInputFragment extends MyBaseFragment<FragmentSimpleInputBinding> {
    protected final static int INPUT_TYPE_CONTENT = 0;
    protected final static int INPUT_TYPE_PASSWORD = 1;

    private int inputType = INPUT_TYPE_CONTENT;

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    protected void changeContentViewType(int type, boolean force) {
        if (INPUT_TYPE_CONTENT != type && INPUT_TYPE_PASSWORD != type) {
            DDLog.e(TAG, "不支持的类型: " + type);
            return;
        }

        if (inputType == type && !force) {
            return;
        }

        if (INPUT_TYPE_PASSWORD == type) {
            mBinding.etContent.setVisibility(View.GONE);
            mBinding.loginPassLayout.setVisibility(View.VISIBLE);
        } else {
            mBinding.etContent.setVisibility(View.VISIBLE);
            mBinding.loginPassLayout.setVisibility(View.GONE);
        }

        inputType = type;
        updateBtnStateEnable();
    }

    private void updateBtnStateEnable() {
        boolean enable = isNextEnableForInput(getInputContent());
        setNextBtnEnable(enable);
    }

    protected void setInputMaxLength(int length) {
        if (INPUT_TYPE_CONTENT == inputType) {
            mBinding.etContent.setFilters(new InputFilter[]{new InputFilter.LengthFilter(length)});
        } else if (INPUT_TYPE_PASSWORD == inputType) {
            mBinding.etPwd.setFilters(new InputFilter[]{new InputFilter.LengthFilter(length)});
        }
    }

    @Nullable
    protected String getInputContent() {
        final String input;
        if (INPUT_TYPE_CONTENT == inputType) {
            input = mBinding.etContent.getText().toString().trim();
        } else if (INPUT_TYPE_PASSWORD == inputType) {
            input = mBinding.etPwd.getText().toString().trim();
        } else {
            input = null;
        }
        return input;
    }

    protected void setNextBtnEnable(final boolean enable) {
        mBinding.btnNext.setAlpha(enable ? 1.0f : 0.5f);
        mBinding.btnNext.setEnabled(enable);
    }

    protected void setContentInputHint(@NonNull final String hint, @NonNull final String btnNext) {
        setContentInputHint(hint, btnNext, Integer.MAX_VALUE);
    }

    protected void setContentInputHint(@NonNull final String hint, @NonNull final String btnNext, int maxLength) {
        setContentInputHint(hint, btnNext, maxLength, true);
    }

    protected void setContentInputHint(@NonNull final String hint, @NonNull final String btnNext, int maxLength, boolean needLocaling) {
        changeContentViewType(INPUT_TYPE_CONTENT, true);
        mBinding.etContent.setHint(needLocaling ? Local.s(hint) : hint);
        mBinding.btnNext.setText(needLocaling ? Local.s(btnNext) : btnNext);
        setInputMaxLength(maxLength);
    }

    protected void setPasswordInputHint(@NonNull final String hint, @NonNull final String btnNext) {
        setPasswordInputHint(hint, btnNext, Integer.MAX_VALUE);
    }

    protected void setPasswordInputHint(@NonNull final String hint, @NonNull final String btnNext, int maxLength) {
        changeContentViewType(INPUT_TYPE_PASSWORD, true);
        mBinding.etPwd.setHint(Local.s(hint));
        mBinding.btnNext.setText(Local.s(btnNext));
        setInputMaxLength(maxLength);
    }

    protected void setTopHint(@Nullable final String hint) {
        if (TextUtils.isEmpty(hint)) {
            mBinding.tvHint.setVisibility(View.GONE);
            return;
        }

        mBinding.tvHint.setVisibility(View.VISIBLE);
        mBinding.tvHint.setLocalText(hint);
    }

    @CallSuper
    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.commonTitleBar.commonBarTitle.setLocalText(provideTittle());
        mBinding.btnNext.setOnClickListener(v -> {
            setNextBtnEnable(false);
            onNextClick(getInputContent());
        });
        mBinding.etContent.addTextChangedListener(mWatcher);
        mBinding.etPwd.addTextChangedListener(mWatcher);
        mBinding.ivPsdIcon.setOnClickListener(v -> changePassShow());
        changePassShow(false);
    }

    protected void changePassShow(boolean show) {
        if (show) {
            mBinding.ivPsdIcon.setImageResource(R.drawable.icon_form_show);
            mBinding.etPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
        } else {
            mBinding.ivPsdIcon.setImageResource(R.drawable.icon_form_hide);
            mBinding.etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
        }
        mBinding.etPwd.setSelection(mBinding.etPwd.getText().length());
    }

    protected void changePassShow() {
        if (PasswordTransformationMethod.getInstance() == mBinding.etPwd.getTransformationMethod()) {
            mBinding.ivPsdIcon.setImageResource(R.drawable.icon_form_show);
            mBinding.etPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
        } else {
            mBinding.ivPsdIcon.setImageResource(R.drawable.icon_form_hide);
            mBinding.etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
        }
        mBinding.etPwd.setSelection(mBinding.etPwd.getText().length());
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_simple_input;
    }

    protected abstract String provideTittle();

    protected abstract void onNextClick(@Nullable final String inputContent);

    protected boolean isNextEnableForInput(@Nullable final String inputContent) {
        return !TextUtils.isEmpty(inputContent);
    }
}
