package com.dinsafer.module.user.modify;

import static com.dinsafer.util.RegxUtil.USER_PWD_MAX_LEN;
import static com.dinsafer.util.RegxUtil.USER_PWD_MIN_LEN;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dinnet.R;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDLog;

/**
 * 重置密码-校验旧密码
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/12 10:46
 */
public class ChangePwdCheckOldPwdFragment extends SimpleInputFragment {

    public static ChangePwdCheckOldPwdFragment newInstance() {
        return new ChangePwdCheckOldPwdFragment();
    }

    @Override
    public void initData() {
        super.initData();
        setPasswordInputHint(getResources().getString(R.string.change_password_old_hint),
                getResources().getString(R.string.Confirm),
                USER_PWD_MAX_LEN);
    }

    @Override
    protected boolean isNextEnableForInput(@Nullable String inputContent) {
        return !TextUtils.isEmpty(inputContent);
    }

    @Override
    protected String provideTittle() {
        return getResources().getString(R.string.change_password_title);
    }

    @Override
    protected void onNextClick(@Nullable String inputContent) {
        if (TextUtils.isEmpty(inputContent)
                || USER_PWD_MIN_LEN > inputContent.length()
                || USER_PWD_MAX_LEN < inputContent.length()) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.user_pwd_condition));
            setNextBtnEnable(true);
            return;
        }

        requestCheckPassword(inputContent);
    }

    private void requestCheckPassword(@NonNull final String oldPassword) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setNextBtnEnable(true);
        });

        DinSDK.getUserInstance().checkPasswordOnly(oldPassword, new IResultCallback2<Boolean>() {
            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error, i: " + i + ", s: " + s);
                closeLoadingFragmentWithCallBack();
                if (!ChangePwdCheckOldPwdFragment.this.isAdded()) {
                    return;
                }
                showErrorToast();
                setNextBtnEnable(true);
            }

            @Override
            public void onSuccess(Boolean success) {
                closeLoadingFragmentWithCallBack();
                if (!ChangePwdCheckOldPwdFragment.this.isAdded()) {
                    return;
                }

                if (null != success && success) {
                    // 校验成功
                    getDelegateActivity().addCommonFragment(SetPwdFragment.newInstanceForChange());
                } else {
                    // 校验失败
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.change_uid_unbind_wrong_password));
                }
                setNextBtnEnable(true);
            }
        });
    }
}
