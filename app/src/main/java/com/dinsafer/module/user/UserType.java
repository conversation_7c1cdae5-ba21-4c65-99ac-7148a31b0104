package com.dinsafer.module.user;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用户登录或注册类型
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/7 12:59
 */
@IntDef({UserType.EMAIL, UserType.PHONE, UserType.UID})
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
public @interface UserType {
    int EMAIL = 0;
    int PHONE = 1;
    int UID = 2;
}
