package com.dinsafer.module.user.modify;

import android.os.Bundle;
import androidx.annotation.NonNull;
import android.view.View;

import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.BindEmailSuccessEvent;
import com.dinsafer.module.user.BaseVerifyCodeFragment;
import com.dinsafer.module.user.UserType;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

/**
 * 修改邮箱-校验验证码
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/11 15:30
 */
public class ChangeEmailVerifyCodeFragment extends BaseVerifyCodeFragment {

    private static final String KEY_ACCOUNT = "account";

    private String account;

    public static ChangeEmailVerifyCodeFragment newInstance(@NonNull final String email) {
        final ChangeEmailVerifyCodeFragment fragment = new ChangeEmailVerifyCodeFragment();
        final Bundle args = new Bundle();
        args.putString(KEY_ACCOUNT, email);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);

        account = getArguments().getString(KEY_ACCOUNT, "");
        userType = UserType.EMAIL;
        final String topHint = Local.s(getResources().getString(R.string.send_verification_key)) + " " + account;
        mBinding.tvVerifyHint.setText(topHint);
    }

    @Override
    protected void onInputFished(@NonNull String inputCode) {
        requestCheckBindCode(account, inputCode);
    }

    @Override
    protected void onRefreshClick(String verifyCode, String verifyId) {
        toGetMessage(account);
    }

    private void toGetMessage(@NonNull final String account) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setRefreshEnable(true);
        });
        DinSDK.getUserInstance().bindEmail(account, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeLoadingFragmentWithCallBack();
                if (ChangeEmailVerifyCodeFragment.this.isAdded()) {
                    if (i == ErrorCode.ERROR_EMAIL_EXIST)
                        getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getResources().getString(R.string.error_email_exist));
                    else {
                        showErrorToast();
                    }
                } else {
                    showErrorToast();
                }
                setRefreshEnable(true);
            }

            @Override
            public void onSuccess() {
                closeLoadingFragmentWithCallBack();
                if (!ChangeEmailVerifyCodeFragment.this.isAdded()) {
                    return;
                }
                changeCountDownViewType(TYPE_COUNTING_DOWN, true);
                clearPassword();
                requestFocusAndOpenInput();
            }
        });
    }

    private void requestCheckBindCode(@NonNull final String account,
                                      @NonNull final String inputCode) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getUserInstance().verifyBindEmailCode(account, inputCode, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ChangeEmailVerifyCodeFragment.this.isAdded()) {
                    return;
                }
                if (i == -1) {
                    showErrorToast();
                } else {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.forget_password_error_code));
                }
                clearPassword();
                requestFocusAndOpenInput();
            }

            @Override
            public void onSuccess() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ChangeEmailVerifyCodeFragment.this.isAdded()) {
                    return;
                }
                showSuccess();
                EventBus.getDefault().post(new BindEmailSuccessEvent());
                getDelegateActivity().removeAllCommonFragment();
            }
        });
    }
}
