package com.dinsafer.module.user.forgetpwd;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.user.BaseVerifyCodeFragment;
import com.dinsafer.module.user.UserType;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;

/**
 * 忘记密码校验验证码页面
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/9 16:50
 */
public class ForgetPwdVerifyCodeFragment extends BaseVerifyCodeFragment {

    private static final String KEY_USER_TYPE = "user_type";
    private static final String KEY_ACCOUNT = "account";
    private static final String KEY_PHONE_ZONE_CODE = "phone_zone_code";

    private String phoneZoneCode;
    private String account;

    public static ForgetPwdVerifyCodeFragment newInstanceForPhone(@NonNull final String phoneZoneCode, @NonNull final String phone) {
        final ForgetPwdVerifyCodeFragment fragment = new ForgetPwdVerifyCodeFragment();
        final Bundle args = new Bundle();
        args.putInt(KEY_USER_TYPE, UserType.PHONE);
        args.putString(KEY_PHONE_ZONE_CODE, phoneZoneCode);
        args.putString(KEY_ACCOUNT, phone);
        fragment.setArguments(args);
        return fragment;
    }

    public static ForgetPwdVerifyCodeFragment newInstanceForEmail(@NonNull final String email) {
        final ForgetPwdVerifyCodeFragment fragment = new ForgetPwdVerifyCodeFragment();
        final Bundle args = new Bundle();
        args.putInt(KEY_USER_TYPE, UserType.EMAIL);
        args.putString(KEY_ACCOUNT, email);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        final int type = getArguments().getInt(KEY_USER_TYPE, -1);

        if (UserType.EMAIL != type && UserType.PHONE != type) {
            showErrorToast();
            removeSelf();
            return;
        }

        userType = type;
        phoneZoneCode = getArguments().getString(KEY_PHONE_ZONE_CODE, "");
        account = getArguments().getString(KEY_ACCOUNT, "");

        final String topHint = Local.s(getResources().getString(R.string.send_verification_key)) + " " + getAccountHint();
        mBinding.tvVerifyHint.setText(topHint);
        mBinding.etCode.setHint(Local.s(getString(R.string.change_phone_code)));
    }

    private String getAccountHint() {
        if (UserType.PHONE == userType) {
            return phoneZoneCode + " " + account;

        }
        return account;
    }

    @Override
    protected void onInputFished(@NonNull String inputCode) {
        if (UserType.PHONE == userType) {
            // 手机号注册验证码校验
            requestCheckVerifyCode(phoneZoneCode + " " + account, inputCode);
            return;
        }

        if (UserType.EMAIL == userType) {
            // 邮箱注册验证码校验
            requestCheckVerifyCode(account, inputCode);
            return;
        }

        // 容错
        showErrorToast();
    }

    @Override
    protected void onRefreshClick(String verifyCode, String verifyId) {
        if (UserType.PHONE == userType) {
            // 手机号注册
            showTimeOutLoadinFramgmentWithCallBack(() -> {
                showErrorToast();
                mBinding.ivRefresh.setEnabled(true);
            });
            requestForgetPwdVerifyCodeForPhone(phoneZoneCode, account, verifyCode, verifyId);
            return;
        }

        if (UserType.EMAIL == userType) {
            // 邮箱注册
            showTimeOutLoadinFramgmentWithCallBack(() -> {
                showErrorToast();
                mBinding.ivRefresh.setEnabled(true);
            });
            requestForgetPwdVerifyCodeForEmail(account);
            return;
        }

        // 容错
        showErrorToast();
        mBinding.ivRefresh.setEnabled(true);
    }

    private void requestCheckVerifyCode(@NonNull final String account,
                                        @NonNull final String inputCode) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getUserInstance().verifyCodeOnly(account, inputCode, new IResultCallback2<Boolean>() {
            @Override
            public void onError(int i, String s) {
                i("requestCheckVerifyCode error:" + i + " s:" + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ForgetPwdVerifyCodeFragment.this.isAdded()) {
                    return;
                }

                showErrorToast();
                clearPassword();
                requestFocusAndOpenInput();
            }

            @Override
            public void onSuccess(Boolean success) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ForgetPwdVerifyCodeFragment.this.isAdded()) {
                    return;
                }

                if (null != success && success) {
                    if (UserType.PHONE == userType) {
                        getDelegateActivity().addCommonFragment(ForgetSetPwdFragment.newInstanceForPhone(phoneZoneCode,
                                ForgetPwdVerifyCodeFragment.this.account, inputCode));
                    } else {
                        getDelegateActivity().addCommonFragment(ForgetSetPwdFragment.newInstanceForEmail(account, inputCode));
                    }
                    getDelegateActivity().removeCommonFragmentAndData(ForgetPwdVerifyCodeFragment.this, false);
                } else {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.forget_password_error_code));
                    clearPassword();
                    requestFocusAndOpenInput();
                }
            }
        });
    }

    private void requestForgetPwdVerifyCodeForPhone(@NonNull final String phoneZone,
                                                    @NonNull final String phone, final String verifyCode, final String verifyId) {
        final String phoneZoneCode = phoneZone.split(" ")[0];
        DinSDK.getUserInstance().getForgetPWDbyPhone(phoneZoneCode + " " + phone, DDSystemUtil.getWidevineId()
                , verifyCode, verifyId, new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragmentWithCallBack();
                        showErrorToast();
                    }

                    @Override
                    public void onSuccess() {
                        closeLoadingFragmentWithCallBack();
                        changeCountDownViewType(TYPE_COUNTING_DOWN, false);
                    }
                });
    }

    private void requestForgetPwdVerifyCodeForEmail(@NonNull final String email) {
        DinSDK.getUserInstance().getForgetPWDbyEmail(email, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeLoadingFragmentWithCallBack();
                showErrorToast();
            }

            @Override
            public void onSuccess() {
                closeLoadingFragmentWithCallBack();
                changeCountDownViewType(TYPE_COUNTING_DOWN, false);
            }
        });
    }
}
