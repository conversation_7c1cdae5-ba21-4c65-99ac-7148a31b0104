package com.dinsafer.module.user;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.common.utils.BitmapUtil;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentSignUpNewBinding;
import com.dinsafer.model.event.LoginFromSignUpEvent;
import com.dinsafer.module.BaseScrollTittleFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.ChoosePhoneZoneFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * 注册页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/6 17:07
 */
public class SignUpFragment extends BaseScrollTittleFragment<FragmentSignUpNewBinding>
        implements View.OnFocusChangeListener, ChoosePhoneZoneFragment.IChoosePhoneZoneCallBack {
    private static final long TOGGLE_TITTLE_DELAY_MILLIS = 200;

    @UserType
    private int signUpType = UserType.EMAIL;

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    private String phoneZone;
    private String lastInputPhone, lastInputEmail;
    private String verifyId;
    private final Handler mH = new Handler(Looper.getMainLooper());
    private final Runnable toggleTittleRunnable = () -> setTittleExpanded(!mContentBinding.etAccount.isFocused());

    private static final long TOW_SECONDS = 2 * 1000;

    public static SignUpFragment newInstance() {
        return new SignUpFragment();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        setLocalTittle(Local.s(getString(R.string.sign_up)));

        mContentBinding.tvViaEmail.setText(Local.s(getString(R.string.via_email)));
        mContentBinding.tvViaPhone.setText(Local.s(getString(R.string.via_phone)));
        mContentBinding.btnNext.setText(Local.s(getString(R.string.create_an_account)));

        mContentBinding.etAccount.setOnFocusChangeListener(this);
        mContentBinding.etAccount.addTextChangedListener(mWatcher);
        mContentBinding.etCode.addTextChangedListener(mWatcher);

        mContentBinding.tvViaEmail.setOnClickListener(v -> {
            changeSignUpTypeView(UserType.EMAIL, false);
            mContentBinding.etAccount.requestFocus();
            toOpenInput();
        });
        if (isSupportPhoneSignup()) {
            mContentBinding.tvViaPhone.setOnClickListener(v -> {
                changeSignUpTypeView(UserType.PHONE, false);
                mContentBinding.etAccount.requestFocus();
                toOpenInput();
            });
            mContentBinding.tvPhoneZone.setOnClickListener(v -> toChoosePhoneZone());
        }
        mContentBinding.btnNext.setOnClickListener(v -> {
            setSignUpEnable(false);
            signUp();
        });

        mContentBinding.ivCode.setOnClickListener(v -> {
            setIvCodeEnable(false);
            getVerificationCode();
        });

        changeSignUpTypeView(signUpType, true);
        initDefaultPhoneZone();
        updateBtnStateEnable();

        if (isSupportPhoneSignup()) {
            mContentBinding.vTypeDivider.setVisibility(View.VISIBLE);
            mContentBinding.tvViaPhone.setVisibility(View.VISIBLE);
        } else {
            mContentBinding.vTypeDivider.setVisibility(View.GONE);
            mContentBinding.tvViaPhone.setVisibility(View.GONE);
        }
    }

    /**
     * 初始化手机号注册的默认区号
     */
    private void initDefaultPhoneZone() {
        String defaultPhoneZone = ChoosePhoneZoneFragment.getCachePhoneZone();
        if (TextUtils.isEmpty(defaultPhoneZone)) {
            phoneZone = APIKey.DEFAULT_PHONE_TEXT;
        } else {
            phoneZone = defaultPhoneZone;
        }
        onPhoneZoneUpdate();
    }

    private void signUp() {
        final String account = mContentBinding.etAccount.getText().toString().trim();
        final String verifyCode = mContentBinding.etCode.getText().toString().trim();
        if (TextUtils.isEmpty(account)) {
            showErrorToast();
            setSignUpEnable(true);
            return;
        }

        if (UserType.PHONE == signUpType) {
            // 手机号注册
            if (TextUtils.isEmpty(phoneZone) || phoneZone.split(" ").length < 1 || TextUtils.isEmpty(verifyCode)) {
                showErrorToast();
                setSignUpEnable(true);
                return;
            }
            final String phoneZoneCode = phoneZone.split(" ")[0];
            if (!RegxUtil.isPhoneNumber(phoneZoneCode, account)) {
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.phone_format_illegal));
                setSignUpEnable(true);
                return;
            }

            toCloseInput();
            final String phoneZone = this.phoneZone;
            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
            requestSignUpVerifyCodeForPhone(phoneZone, account, verifyCode);
            return;
        }

        // 邮箱注册
        if (!RegxUtil.isEmail(account)) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.email_format_illegal));
            setSignUpEnable(true);
            return;
        }

        toCloseInput();
        showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
        requestSignUpVerifyCodeForEmail(account);
    }

    private void requestSignUpVerifyCodeForEmail(@NonNull final String email) {
        DinSDK.getUserInstance().getEmailValidateCode(email, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                i("get phone register code error:" + i + " s:" + s);
                if (i == ErrorCode.ERROR_EMAIL_EXIST) {
                    showAccountAlreadyUsedDialog(email, getResources().getString(R.string.error_email_exist), null);
                } else {
                    showErrorToast();
                }
                closeTimeOutLoadinFramgmentWithErrorAlert();
                setSignUpEnable(true);
            }

            @Override
            public void onSuccess() {
                i("onSuccess");
                closeTimeOutLoadinFramgmentWithErrorAlert();
                getDelegateActivity().addCommonFragment(SignUpVerifyCodeFragment.newInstanceForEmail(email));
                setSignUpEnable(true);
            }
        });
    }

    private void requestSignUpVerifyCodeForPhone(@NonNull final String phoneZone,
                                                 @NonNull final String phone, @NonNull final String verifyCode) {
        final String phoneZoneCode = phoneZone.split(" ")[0];
        DinSDK.getUserInstance().getPhoneValidateCode(phoneZoneCode + " " + phone, DDSystemUtil.getWidevineId()
                , verifyCode, verifyId, new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        i("get phone register code error:" + i + " s:" + s);
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (i == ErrorCode.ERROR_PHONE_EXIST) {
                            showAccountAlreadyUsedDialog(phone, getResources().getString(R.string.error_phone_exist), phoneZone);
                        } else if (i == ErrorCode.ERROR_WRONG_VERIFICATION_CODE_ENTERED) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_wrong_verifyCode)));
                            getVerificationCode();
                        } else if (i == ErrorCode.ERROR_VERIFICATION_CODE_ENTERED_TIMEOUT) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_verifyCode_timeout)));
                            getVerificationCode();
                        } else {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));
                        }
                        setSignUpEnable(true);
                    }

                    @Override
                    public void onSuccess() {
                        i("onSuccess");
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        getDelegateActivity().addCommonFragment(SignUpVerifyCodeFragment.newInstanceForPhone(phoneZoneCode, phone));
                        setSignUpEnable(true);
                    }
                });
    }

    private void showAccountAlreadyUsedDialog(@NonNull final String account,
                                              @NonNull final String hint,
                                              @Nullable final String phoneZone) {
        AlertDialog dialog = new AlertDialog.Builder(getDelegateActivity())
                .setContent(hint)
                .setOk(getString(R.string.login))
                .setCancel(getString(R.string.got_it))
                .setOKListener(() -> {
                    if (TextUtils.isEmpty(phoneZone)) {
                        EventBus.getDefault().post(new LoginFromSignUpEvent(account));
                    } else {
                        EventBus.getDefault().post(new LoginFromSignUpEvent(account, phoneZone));
                    }
                })
                .preBuilder();
        dialog.show();
    }

    private void updateBtnStateEnable() {
        final String account = mContentBinding.etAccount.getText().toString().trim();
        final String verificationCode = mContentBinding.etCode.getText().toString().trim();

        boolean enable;
        if (UserType.PHONE == signUpType) {
            enable = !TextUtils.isEmpty(account)
                    && !TextUtils.isEmpty(verificationCode)
                    && !TextUtils.isEmpty(phoneZone);
        } else {
            enable = !TextUtils.isEmpty(account);
        }
        setSignUpEnable(enable);
    }

    private void setSignUpEnable(final boolean enable) {
        mContentBinding.btnNext.setAlpha(enable ? 1.0f : 0.5f);
        mContentBinding.btnNext.setEnabled(enable);
    }

    private void changeSignUpTypeView(@UserType final int newType, final boolean force) {
        if ((signUpType == newType && !force)
                || (UserType.EMAIL != newType && UserType.PHONE != newType)) {
            return;
        }

        if (UserType.PHONE == newType) {
            lastInputEmail = mContentBinding.etAccount.getText().toString().trim();
            mContentBinding.etAccount.setText("");

            mContentBinding.tvViaPhone.setTextColor(getResources().getColor(R.color.color_white_01));
            mContentBinding.tvViaEmail.setTextColor(getResources().getColor(R.color.color_white_02));

            mContentBinding.clInputVerificationCode.setVisibility(View.VISIBLE);
            mContentBinding.tvPhoneZone.setVisibility(View.VISIBLE);
            mContentBinding.vPhoneLine.setVisibility(View.VISIBLE);
            mContentBinding.etAccount.setHint(Local.s(getString(R.string.enter_phone_number)));
            mContentBinding.etAccount.setInputType(InputType.TYPE_CLASS_PHONE);
            mContentBinding.etCode.setHint(Local.s(getString(R.string.change_phone_code)));

            if (!TextUtils.isEmpty(lastInputPhone)) {
                mContentBinding.etAccount.setText(lastInputPhone);
            }

            getVerificationCode();
        } else {
            lastInputPhone = mContentBinding.etAccount.getText().toString().trim();
            mContentBinding.etAccount.setText("");

            mContentBinding.tvViaPhone.setTextColor(getResources().getColor(R.color.color_white_02));
            mContentBinding.tvViaEmail.setTextColor(getResources().getColor(R.color.color_white_01));

            mContentBinding.clInputVerificationCode.setVisibility(View.GONE);
            mContentBinding.tvPhoneZone.setVisibility(View.GONE);
            mContentBinding.vPhoneLine.setVisibility(View.GONE);
            mContentBinding.etAccount.setHint(Local.s(getString(R.string.email_address)));
            mContentBinding.etAccount.setInputType(InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);

            if (!TextUtils.isEmpty(lastInputEmail)) {
                mContentBinding.etAccount.setText(lastInputEmail);
            }
        }

        mContentBinding.etAccount.setSelection(mContentBinding.etAccount.getText().length());
        signUpType = newType;
        updateBtnStateEnable();
    }

    @Override
    protected int provideLayoutId() {
        return R.layout.fragment_sign_up_new;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        mH.removeCallbacks(toggleTittleRunnable);
        mH.postDelayed(toggleTittleRunnable, TOGGLE_TITTLE_DELAY_MILLIS);
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        if (signUpType == UserType.PHONE) {
            getVerificationCode();
        }
    }

    @Override
    public void onDestroyView() {
        mH.removeCallbacksAndMessages(null);
        super.onDestroyView();
    }

    public void toChoosePhoneZone() {
        String currentPhoneZone = "";
        if (!TextUtils.isEmpty(phoneZone)) {
            currentPhoneZone = phoneZone;
        }
        ChoosePhoneZoneFragment fragment = ChoosePhoneZoneFragment.newInstance(currentPhoneZone);
        fragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(fragment);
    }

    public void refreshUiI () {
        changeSignUpTypeView(UserType.EMAIL, false);
    }

    // 选择时区回调
    @Override
    public void onResult(String code, String name) {
        phoneZone = code + " " + name;
        onPhoneZoneUpdate();
    }

    private void onPhoneZoneUpdate() {
        String phoneCode = "";
        if (!TextUtils.isEmpty(phoneZone) && phoneZone.split(" ").length > 1) {
            phoneCode = phoneZone.split(" ")[0];
        }
        mContentBinding.tvPhoneZone.setText(phoneCode);
    }

    private boolean isSupportPhoneSignup() {
        return AppConfig.Functions.SUPPORT_PHONE_FUNCTION;
    }

    private void getVerificationCode() {
        mContentBinding.viewLoading.setVisibility(View.VISIBLE);
        DinSDK.getUserInstance().refreshVerifyCode(DDSystemUtil.getWidevineId(), new IResultCallback2<RefreshVerifyCodeResponse.ResultBean>() {
            @Override
            public void onSuccess(RefreshVerifyCodeResponse.ResultBean resultBean) {
                DDLog.d(TAG, "refreshVerifyCode. onSuccess");
                mContentBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);

                if (resultBean != null) {
                    verifyId = resultBean.getVerify_id();
                    String base64Image = resultBean.getBase64();
                    Bitmap bitmap = BitmapUtil.convertStringToBitmap(base64Image);
                    Bitmap roundedBitmap = BitmapUtil.getRoundedCornerBitmap(getContext(), bitmap, 6);
                    mContentBinding.ivCode.setImageBitmap(roundedBitmap);
                }
            }

            @Override
            public void onError(int code, String msg) {
                DDLog.e(TAG, "refreshVerifyCode. onError: " + code + " " + msg);
                mContentBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);

                if (ErrorCode.ERROR_TOO_MANY_VERIFICATION == code) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_too_many_verifyCode)));
                    return;
                }
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));


            }
        });
    }

    private void setIvCodeEnable(boolean enable) {
        if (enable) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mContentBinding.ivCode.setEnabled(true);
                    mContentBinding.ivCode.setAlpha(1.0f);
                }
            }, TOW_SECONDS);
            return;
        }
        mContentBinding.ivCode.setEnabled(false);
        mContentBinding.ivCode.setAlpha(0.5f);
    }

}
