package com.dinsafer.module.user;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.util.Patterns;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;

import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentLoginNewBinding;
import com.dinsafer.model.event.HadLoginEvent;
import com.dinsafer.model.event.LoginFromSignUpEvent;
import com.dinsafer.module.BaseScrollTittleFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.settting.ui.ChoosePhoneZoneFragment;
import com.dinsafer.module.user.forgetpwd.ForgetPwdFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * 登录页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/6 17:07
 */
public class LoginFragment extends BaseScrollTittleFragment<FragmentLoginNewBinding>
        implements View.OnFocusChangeListener, ChoosePhoneZoneFragment.IChoosePhoneZoneCallBack {
    private static final long TOGGLE_TITTLE_DELAY_MILLIS = 200;

    @UserType
    private int loginType = UserType.EMAIL;

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    private String phoneZone;
    private String lastInputPhone, lastInputEmail, lastPhonePwd, lastEmailPwd;
    private final Handler mH = new Handler(Looper.getMainLooper());
    private final Runnable toggleTittleRunnable = () -> {
        setTittleExpanded(!mContentBinding.etAccount.isFocused()
                && !mContentBinding.etPwd.isFocused());
    };

    public static LoginFragment newInstance() {
        return new LoginFragment();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        setLocalTittle(Local.s(getString(R.string.login)));

        mContentBinding.tvViaEmail.setText(Local.s(getString(R.string.via_email_username)));
        mContentBinding.tvViaPhone.setText(Local.s(getString(R.string.via_phone)));
        mContentBinding.etPwd.setHint(Local.s(getString(R.string.login_pass_hint)));
        mContentBinding.cbRemember.setText(Local.s(getString(R.string.remember)));
        mContentBinding.tvForgetPwd.setText(Local.s(getString(R.string.change_password_forgot)));
        mContentBinding.btnLogin.setText(Local.s(getString(R.string.login)));

        mContentBinding.etAccount.addTextChangedListener(mWatcher);
        mContentBinding.etPwd.addTextChangedListener(mWatcher);

        mContentBinding.etPwd.setOnKeyListener((v, keyCode, event) -> {
            if (event.getKeyCode() == KeyEvent.KEYCODE_ENTER && event.getAction() != KeyEvent.ACTION_UP) {
                InputMethodManager inputMethodManager = (InputMethodManager) v.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                if (null != inputMethodManager && inputMethodManager.isActive()) {
                    mContentBinding.etPwd.clearFocus();
                    IBinder binder = activity.getWindow().getDecorView().getWindowToken();
                    inputMethodManager.hideSoftInputFromWindow(binder, 0);
                    return true;
                }
            }
            return false;
        });

        mContentBinding.etAccount.setOnFocusChangeListener(this);
        mContentBinding.etPwd.setOnFocusChangeListener(this);

        mContentBinding.tvViaEmail.setOnClickListener(v -> {
            changeLoginTypeView(UserType.EMAIL, false);
            mContentBinding.etAccount.requestFocus();
            toOpenInput();
        });
        if (isSupportPhoneLogin()) {
            mContentBinding.tvViaPhone.setOnClickListener(v -> {
                changeLoginTypeView(UserType.PHONE, false);
                mContentBinding.etAccount.requestFocus();
                toOpenInput();
            });
            mContentBinding.tvPhoneZone.setOnClickListener(v -> toChoosePhoneZone());
        }
        mContentBinding.ivPsdIcon.setOnClickListener(v -> toChangePassShow());
        mContentBinding.tvForgetPwd.setOnClickListener(v -> getDelegateActivity().addCommonFragment(ForgetPwdFragment.newInstance()));
        mContentBinding.btnLogin.setOnClickListener(v -> {
            setLoginEnable(false);
            login();
        });

        mContentBinding.etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
        initFromCache();
        updateBtnStateEnable();

        if (isSupportPhoneLogin()) {
            mContentBinding.vTypeDivider.setVisibility(View.VISIBLE);
            mContentBinding.tvViaPhone.setVisibility(View.VISIBLE);
        } else {
            mContentBinding.vTypeDivider.setVisibility(View.GONE);
            mContentBinding.tvViaPhone.setVisibility(View.GONE);
        }
    }

    private void login() {
        final String account = mContentBinding.etAccount.getText().toString().trim();
        final String pwd = mContentBinding.etPwd.getText().toString().trim();
        if (TextUtils.isEmpty(account)) {
            showAccountErrorBg(true);
            showErrorToast();
            setLoginEnable(true);
            return;
        }

        if (TextUtils.isEmpty(pwd)) {
            showPwdErrorBg(true);
            showErrorToast();
            setLoginEnable(true);
            return;
        }
        showPwdErrorBg(false);

        if (UserType.PHONE == loginType) {
            if (TextUtils.isEmpty(phoneZone) || phoneZone.split(" ").length < 1) {
                showAccountErrorBg(true);
                showErrorToast();
                setLoginEnable(true);
                return;
            }

            final String phoneZoneCode = phoneZone.split(" ")[0];
            if (TextUtils.isEmpty(account)
                    || !RegxUtil.isPhoneNumber(phoneZoneCode, account)) {
                showAccountErrorBg(true);
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.phone_format_illegal));
                setLoginEnable(true);
                return;
            }

            showAccountErrorBg(false);
            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
            loginWithPhonePwd(phoneZoneCode, account, pwd);
            return;
        }

        if (Patterns.EMAIL_ADDRESS.matcher(account).matches()) {
            showAccountErrorBg(false);
            showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
            loginEmailPwd(account, pwd);
            return;
        }

        showAccountErrorBg(false);
        showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.loging_hint));
        loginUidPwd(account, pwd);
    }

    private void loginUidPwd(@NonNull final String uid, @NonNull final String pwd) {
        DinSDK.getUserInstance().loginWithUUid(uid, pwd, new ILoginCallback() {
            @Override
            public void onSuccess(DinUser dinUser) {
                onLoginSuccess(dinUser, UserType.UID, uid, pwd);
            }

            @Override
            public void onError(int i, String s) {
                onLoginError(i, s);
            }
        });
    }

    private void loginEmailPwd(@NonNull final String email, @NonNull final String pwd) {
        DinSDK.getUserInstance().loginWithEmailPassword(email, pwd, new ILoginCallback() {
            @Override
            public void onSuccess(DinUser dinUser) {
                onLoginSuccess(dinUser, UserType.EMAIL, email, pwd);
            }

            @Override
            public void onError(int i, String s) {
                onLoginError(i, s);
            }
        });
    }

    private void loginWithPhonePwd(@NonNull final String phoneZoneCode,
                                   @NonNull final String phone,
                                   @NonNull final String pwd) {
        DinSDK.getUserInstance().loginWithPhonePassword(phoneZoneCode, phone, pwd, new ILoginCallback() {
            @Override
            public void onSuccess(DinUser dinUser) {
                onLoginSuccess(dinUser, UserType.PHONE, phone, pwd);
            }

            @Override
            public void onError(int i, String s) {
                onLoginError(i, s);
            }
        });
    }

    private void onLoginSuccess(@NonNull final DinUser dinUser, @UserType int type,
                                @NonNull final String account, @NonNull final String pwd) {
        DBUtil.SPut(DBKey.USER_KEY, dinUser);
        DBUtil.Put(DBKey.TOKEN, dinUser.getToken());
        // 不管是否记住密码，都需要保存，因为注销的时候需要本地校验密码
        DBUtil.SPut(DBKey.USER_PASSWORD, pwd);
        DBUtil.SPut(DBKey.CHECK_USER_PASSWORD, pwd);
        CommonDataUtil.getInstance().setAlias(dinUser.getUser_id());

        if (mContentBinding.cbRemember.isChecked()) {
            if (UserType.PHONE == type) {
                DBUtil.Put(DBKey.REMEMBER_PHONE, account);
                DBUtil.Put(DBKey.REMEMBER_PHONE_ZONE, phoneZone);
                DBUtil.Delete(DBKey.REMEMBER_UID);
            } else {
                DBUtil.Put(DBKey.REMEMBER_UID, account);
                DBUtil.Delete(DBKey.REMEMBER_PHONE);
            }
        } else {
            DBUtil.Delete(DBKey.REMEMBER_UID);
            DBUtil.Delete(DBKey.REMEMBER_PHONE);
            DBUtil.Delete(DBKey.REMEMBER_PHONE_ZONE);
        }
        MainActivity.start(getActivity());
        setLoginEnable(true);
        PermissionDialogUtil.checkNotificationPermission();
        EventBus.getDefault().post(new HadLoginEvent());
        getActivity().finish();
    }

    private void onLoginError(int code, String msg) {
        DDLog.e(TAG, "onError, i: " + code + ", s: " + msg);
        closeLoadingFragment();
        setLoginEnable(true);
        if (ErrorCode.ERROR_USERNAME_OR_PWD == code) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.uid_or_pwd_error));
        } else {
            showErrorToast();
        }
    }

    private void initFromCache() {
        String defaultPhoneZone = ChoosePhoneZoneFragment.getCachePhoneZone();
        if (DBUtil.Exists(DBKey.REMEMBER_UID)) {
            lastInputEmail = DBUtil.Str(DBKey.REMEMBER_UID);
            lastEmailPwd = DBUtil.SGet(DBKey.USER_PASSWORD);
            changeLoginTypeView(UserType.EMAIL, true);
        } else if (isSupportPhoneLogin() && DBUtil.Exists(DBKey.REMEMBER_PHONE)) {
            defaultPhoneZone = DBUtil.Str(DBKey.REMEMBER_PHONE_ZONE);
            lastInputPhone = DBUtil.Str(DBKey.REMEMBER_PHONE);
            lastPhonePwd = DBUtil.SGet(DBKey.USER_PASSWORD);
            changeLoginTypeView(UserType.PHONE, true);
        } else {
            changeLoginTypeView(UserType.EMAIL, true);
        }

        if (TextUtils.isEmpty(defaultPhoneZone)) {
            phoneZone = APIKey.DEFAULT_PHONE_TEXT;
        } else {
            phoneZone = defaultPhoneZone;
        }

        onPhoneZoneUpdate();
    }

    public void toChoosePhoneZone() {
        String currentPhoneZone = "";
        if (!TextUtils.isEmpty(phoneZone)) {
            currentPhoneZone = phoneZone;
        }
        ChoosePhoneZoneFragment fragment = ChoosePhoneZoneFragment.newInstance(currentPhoneZone);
        fragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(fragment);
    }

    private void updateBtnStateEnable() {
        final String account = mContentBinding.etAccount.getText().toString().trim();
        final String pwd = mContentBinding.etPwd.getText().toString().trim();

        boolean enable;
        if (UserType.PHONE == loginType) {
            enable = !TextUtils.isEmpty(account)
                    && !TextUtils.isEmpty(pwd)
                    && !TextUtils.isEmpty(phoneZone);
        } else {
            enable = !TextUtils.isEmpty(account)
                    && !TextUtils.isEmpty(pwd);
        }
        setLoginEnable(enable);
    }

    private void setLoginEnable(final boolean enable) {
        mContentBinding.btnLogin.setAlpha(enable ? 1.0f : 0.5f);
        mContentBinding.btnLogin.setEnabled(enable);
    }

    private void changeLoginTypeView(@UserType final int newType, final boolean force) {
        if ((loginType == newType && !force)
                || (UserType.UID != newType && UserType.EMAIL != newType && UserType.PHONE != newType)) {
            return;
        }

        if (UserType.PHONE == newType) {
            lastInputEmail = mContentBinding.etAccount.getText().toString().trim();
            lastEmailPwd = mContentBinding.etPwd.getText().toString().trim();
            mContentBinding.etAccount.setText("");
            mContentBinding.etPwd.setText("");

            mContentBinding.tvViaPhone.setTextColor(getResources().getColor(R.color.color_white_01));
            mContentBinding.tvViaEmail.setTextColor(getResources().getColor(R.color.color_white_02));

            mContentBinding.tvPhoneZone.setVisibility(View.VISIBLE);
            mContentBinding.vPhoneLine.setVisibility(View.VISIBLE);
            mContentBinding.etAccount.setHint(Local.s(getString(R.string.enter_phone_number)));
            mContentBinding.etAccount.setInputType(InputType.TYPE_CLASS_PHONE);

            if (!TextUtils.isEmpty(lastInputPhone)) {
                mContentBinding.etAccount.setText(lastInputPhone);
            }
            if (!TextUtils.isEmpty(lastPhonePwd)) {
                mContentBinding.etPwd.setText(lastPhonePwd);
            }
        } else {
            lastInputPhone = mContentBinding.etAccount.getText().toString().trim();
            lastPhonePwd = mContentBinding.etPwd.getText().toString().trim();
            mContentBinding.etAccount.setText("");
            mContentBinding.etPwd.setText("");

            mContentBinding.tvViaPhone.setTextColor(getResources().getColor(R.color.color_white_02));
            mContentBinding.tvViaEmail.setTextColor(getResources().getColor(R.color.color_white_01));

            mContentBinding.tvPhoneZone.setVisibility(View.GONE);
            mContentBinding.vPhoneLine.setVisibility(View.GONE);
            mContentBinding.etAccount.setHint(Local.s(getString(R.string.login_name_hint)));
            mContentBinding.etAccount.setInputType(InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);

            if (!TextUtils.isEmpty(lastInputEmail)) {
                mContentBinding.etAccount.setText(lastInputEmail);
            }
            if (!TextUtils.isEmpty(lastEmailPwd)) {
                mContentBinding.etPwd.setText(lastEmailPwd);
            }
        }

        mContentBinding.etAccount.setSelection(mContentBinding.etAccount.getText().length());
        loginType = newType;
        updateBtnStateEnable();
        showAccountErrorBg(false);
        showPwdErrorBg(false);
    }

    public void toChangePassShow() {
        if (PasswordTransformationMethod.getInstance() == mContentBinding.etPwd.getTransformationMethod()) {
            mContentBinding.ivPsdIcon.setImageResource(R.drawable.icon_form_show);
            mContentBinding.etPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
        } else {
            mContentBinding.ivPsdIcon.setImageResource(R.drawable.icon_form_hide);
            mContentBinding.etPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
        }
        mContentBinding.etPwd.setSelection(mContentBinding.etPwd.getText().length());
    }

    private void showAccountErrorBg(final boolean error) {
        mContentBinding.llInputAccount.setBackgroundResource(error ? R.drawable.alpha_input_rectangle_error : R.drawable.alpha_input_rectangle);
    }

    private void showPwdErrorBg(final boolean error) {
        mContentBinding.loginPassLayout.setBackgroundResource(error ? R.drawable.alpha_input_rectangle_error : R.drawable.alpha_input_rectangle);
    }

    @Override
    protected int provideLayoutId() {
        return R.layout.fragment_login_new;
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        mH.removeCallbacks(toggleTittleRunnable);
        mH.postDelayed(toggleTittleRunnable, TOGGLE_TITTLE_DELAY_MILLIS);
    }

    // 选择时区回调
    @Override
    public void onResult(String code, String name) {
        phoneZone = code + " " + name;
        onPhoneZoneUpdate();
    }

    private void onPhoneZoneUpdate() {
        String phoneCode = "";
        if (!TextUtils.isEmpty(phoneZone) && phoneZone.split(" ").length > 1) {
            phoneCode = phoneZone.split(" ")[0];
        }
        mContentBinding.tvPhoneZone.setText(phoneCode);
    }

    @Override
    public void onDestroyView() {
        mH.removeCallbacksAndMessages(null);
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    private LoginFromSignUpEvent lastEvent;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(LoginFromSignUpEvent event) {
        if (null != lastEvent && lastEvent.equals(event)) {
            return;
        }
        lastEvent = event;
        if (event.isPhoneLogin()) {
            phoneZone = event.getPhoneZone();
            lastInputPhone = event.getAccount();
            lastPhonePwd = event.getPwd();
            onPhoneZoneUpdate();
            changeLoginTypeView(UserType.PHONE, true);
            if (event.isAutoLogin()) {
                login();
            } else {
                mContentBinding.etPwd.requestFocus();
                toOpenInput();
            }
            return;
        }

        if (event.isEmailLogin()) {
            lastInputEmail = event.getAccount();
            lastEmailPwd = event.getPwd();
            changeLoginTypeView(UserType.EMAIL, true);
            if (event.isAutoLogin()) {
                login();
            } else {
                mContentBinding.etPwd.requestFocus();
                toOpenInput();
            }
        }
    }

    private boolean isSupportPhoneLogin() {
        return AppConfig.Functions.SUPPORT_PHONE_FUNCTION;
    }
}
