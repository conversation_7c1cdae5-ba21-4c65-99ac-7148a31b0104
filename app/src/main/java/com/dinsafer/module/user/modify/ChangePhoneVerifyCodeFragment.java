package com.dinsafer.module.user.modify;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.BindPhoneSuccessEvent;
import com.dinsafer.module.user.BaseVerifyCodeFragment;
import com.dinsafer.module.user.UserType;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;

/**
 * 修改邮箱-校验验证码
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/11 15:30
 */
public class ChangePhoneVerifyCodeFragment extends BaseVerifyCodeFragment {
    private static final String KEY_PHONE_ZONE_CODE = "phone_zone_code";
    private static final String KEY_ACCOUNT = "account";

    private String phoneZoneCode;
    private String account;

    public static ChangePhoneVerifyCodeFragment newInstance(@NonNull final String phoneZoneCode,
                                                            @NonNull final String phone) {
        final ChangePhoneVerifyCodeFragment fragment = new ChangePhoneVerifyCodeFragment();
        final Bundle args = new Bundle();
        args.putString(KEY_PHONE_ZONE_CODE, phoneZoneCode);
        args.putString(KEY_ACCOUNT, phone);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);

        account = getArguments().getString(KEY_ACCOUNT, "");
        phoneZoneCode = getArguments().getString(KEY_PHONE_ZONE_CODE, "");
        userType = UserType.PHONE;
        final String topHint = Local.s(getResources().getString(R.string.send_verification_key)) + " " + getAccountHint();
        mBinding.tvVerifyHint.setText(topHint);
    }

    private String getAccountHint() {
        return phoneZoneCode + " " + account;
    }

    @Override
    protected void onInputFished(@NonNull String inputCode) {
        requestCheckBindCode(phoneZoneCode, account, inputCode);
    }

    @Override
    protected void onRefreshClick(String verifyCode, String verifyId) {
        toGetMessage(phoneZoneCode, account, verifyCode, verifyId);
    }

    private void toGetMessage(@NonNull final String phoneZoneCode, @NonNull final String phone
            , final String verifyCode, final String verifyId) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setRefreshEnable(true);
        });
        DinSDK.getUserInstance().bindPhone(phoneZoneCode, phone, DDSystemUtil.getWidevineId()
                , verifyCode, verifyId, new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        closeLoadingFragmentWithCallBack();
                        if (!ChangePhoneVerifyCodeFragment.this.isAdded()) {
                            return;
                        }

                        if (i == ErrorCode.ERROR_PHONE_EXIST) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getResources().getString(R.string.error_phone_exist));
                        } else if (i == ErrorCode.ERROR_WRONG_VERIFICATION_CODE_ENTERED) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_wrong_verifyCode)));
                            getVerificationCode();
                        } else if (i == ErrorCode.ERROR_VERIFICATION_CODE_ENTERED_TIMEOUT) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_verifyCode_timeout)));
                            getVerificationCode();
                        } else {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));
                        }
                        setRefreshEnable(true);
                    }

                    @Override
                    public void onSuccess() {
                        closeLoadingFragmentWithCallBack();
                        if (!ChangePhoneVerifyCodeFragment.this.isAdded()) {
                            return;
                        }
                        changeCountDownViewType(TYPE_COUNTING_DOWN, true);
                        clearPassword();
                        requestFocusAndOpenInput();
                    }
                });
    }

    private void requestCheckBindCode(@NonNull final String phoneZoneCode,
                                      @NonNull final String phone,
                                      @NonNull final String inputCode) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getUserInstance().verifyBindPhone(phoneZoneCode, phone, inputCode,
                new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (!ChangePhoneVerifyCodeFragment.this.isAdded()) {
                            return;
                        }
                        if (i == -1) {
                            showErrorToast();
                        } else {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.forget_password_error_code));
                        }
                        clearPassword();
                        requestFocusAndOpenInput();
                    }

                    @Override
                    public void onSuccess() {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (!ChangePhoneVerifyCodeFragment.this.isAdded()) {
                            return;
                        }

                        showSuccess();
                        EventBus.getDefault().post(new BindPhoneSuccessEvent());
                        getDelegateActivity().removeAllCommonFragment();
                    }
                });
    }
}
