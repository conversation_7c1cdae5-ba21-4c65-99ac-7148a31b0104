package com.dinsafer.module.user.forgetpwd;

import static com.dinsafer.util.RegxUtil.USER_PWD_MAX_LEN;
import static com.dinsafer.util.RegxUtil.USER_PWD_MIN_LEN;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;

import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.event.LoginFromSignUpEvent;
import com.dinsafer.module.settting.ui.ChoosePhoneZoneFragment;
import com.dinsafer.module.user.AccountFragment;
import com.dinsafer.module.user.UserType;
import com.dinsafer.module.user.modify.SimpleInputFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * 忘记密码-设置密码
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/12 12:28
 */
public class ForgetSetPwdFragment extends SimpleInputFragment {
    private static final String KEY_ACCOUNT = "account";
    private static final String KEY_VERIFY_CODE = "verify_code";
    private static final String KEY_USER_TYPE = "user_type";
    private static final String KEY_PHONE_ZONE_CODE = "phone_zone_code";

    private int userType = -1;
    private String phoneZoneCode;
    private String account;
    private String verifyCode;

    public static ForgetSetPwdFragment newInstanceForPhone(@NonNull final String phoneZoneCode,
                                                           @NonNull final String phone,
                                                           @NonNull final String verifyCode) {
        final ForgetSetPwdFragment fragment = new ForgetSetPwdFragment();
        final Bundle args = new Bundle();
        args.putInt(KEY_USER_TYPE, UserType.PHONE);
        args.putString(KEY_PHONE_ZONE_CODE, phoneZoneCode);
        args.putString(KEY_VERIFY_CODE, verifyCode);
        args.putString(KEY_ACCOUNT, phone);
        fragment.setArguments(args);
        return fragment;
    }

    public static ForgetSetPwdFragment newInstanceForEmail(@NonNull final String email,
                                                           @NonNull final String verifyCode) {
        final ForgetSetPwdFragment fragment = new ForgetSetPwdFragment();
        final Bundle args = new Bundle();
        args.putInt(KEY_USER_TYPE, UserType.EMAIL);
        args.putString(KEY_VERIFY_CODE, verifyCode);
        args.putString(KEY_ACCOUNT, email);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        initParams();
        super.initView(inflateView, savedInstanceState);
    }

    private void initParams() {
        userType = getArguments().getInt(KEY_USER_TYPE, -1);
        account = getArguments().getString(KEY_ACCOUNT, "");
        phoneZoneCode = getArguments().getString(KEY_PHONE_ZONE_CODE, "");
        verifyCode = getArguments().getString(KEY_VERIFY_CODE, "");
    }

    @Override
    public void initData() {
        super.initData();
        setTopHint(getString(R.string.user_pwd_condition));
        setPasswordInputHint(getString(R.string.change_password_new_hint),
                getResources().getString(R.string.Confirm),
                USER_PWD_MAX_LEN);
    }

    @Override
    protected String provideTittle() {
        return getString(R.string.change_password_title);
    }

    @Override
    protected void onNextClick(@Nullable String inputContent) {
        if (TextUtils.isEmpty(inputContent)
                || USER_PWD_MIN_LEN > inputContent.length()
                || USER_PWD_MAX_LEN < inputContent.length()
                || !RegxUtil.isLetterAndNum(inputContent)) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.user_pwd_condition));
            setNextBtnEnable(true);
            return;
        }

        if (null != DinSDK.getUserInstance().getUser()
                && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUid())
                && inputContent.contains(DinSDK.getUserInstance().getUser().getUid())) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.username_contains_password));
            setNextBtnEnable(true);
            return;
        }

        if (UserType.PHONE == userType) {
            // 手机号重置密码
            requestSetPasswordForPhone(phoneZoneCode + " " + account, verifyCode, inputContent);
            return;
        }

        if (UserType.EMAIL == userType) {
            // 邮箱重置密码
            requestSetPasswordForEmail(account, verifyCode, inputContent);
            return;
        }

        // 容错
        showErrorToast();
    }

    private void requestSetPasswordForEmail(@NonNull final String account,
                                            @NonNull final String verifyCode,
                                            @NonNull final String pwd) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getUserInstance().comfirmForgetPWDByEmailCode(account, verifyCode, pwd, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ForgetSetPwdFragment.this.isAdded()) {
                    return;
                }
                showErrorToast();
                setNextBtnEnable(true);
            }

            @Override
            public void onSuccess() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (!ForgetSetPwdFragment.this.isAdded()) {
                    return;
                }
                final LoginFromSignUpEvent event = new LoginFromSignUpEvent(account);
                event.setPwd(pwd);
                event.setAutoLogin(true);
                ForgetSetPwdFragment.this.onSuccess(event);
            }
        });
    }

    private void requestSetPasswordForPhone(@NonNull final String account,
                                            @NonNull final String verifyCode,
                                            @NonNull final String pwd) {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinSDK.getUserInstance().comfirmForgetPWDByPhoneCode(account, verifyCode, pwd,
                new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (!ForgetSetPwdFragment.this.isAdded()) {
                            return;
                        }
                        showErrorToast();
                        setNextBtnEnable(true);
                    }

                    @Override
                    public void onSuccess() {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (!ForgetSetPwdFragment.this.isAdded()) {
                            return;
                        }
                        String phoneZone = "";
                        for (int i = 0; i < ChoosePhoneZoneFragment.countryCodes.length; i++) {
                            if (phoneZoneCode.equals(ChoosePhoneZoneFragment.countryCodes[i])) {
                                phoneZone = phoneZoneCode + " " + ChoosePhoneZoneFragment.countryNames[i];
                                break;
                            }
                        }
                        final LoginFromSignUpEvent event = new LoginFromSignUpEvent(ForgetSetPwdFragment.this.account, phoneZone);
                        event.setPwd(pwd);
                        event.setAutoLogin(true);
                        ForgetSetPwdFragment.this.onSuccess(event);
                    }
                }
        );
    }

    private void onSuccess(@NonNull final LoginFromSignUpEvent event) {
        if (getDelegateActivity().isCommonFragmentExist(AccountFragment.class.getName())) {
            getDelegateActivity().removeToFragment(AccountFragment.class.getName());
        } else {
            getDelegateActivity().removeAllCommonFragment();
        }
        EventBus.getDefault().post(event);
    }
}
