package com.dinsafer.module.user.forgetpwd;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.dinsafer.common.utils.BitmapUtil;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentForgetPwdBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.ui.ChoosePhoneZoneFragment;
import com.dinsafer.module.user.UserType;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/9 16:26
 */
public class ForgetPwdFragment extends MyBaseFragment<FragmentForgetPwdBinding>
        implements ChoosePhoneZoneFragment.IChoosePhoneZoneCallBack {

    @UserType
    private int forgetPwdType = UserType.EMAIL;

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    private String phoneZone;
    private String lastInputPhone, lastInputEmail;
    private String verifyId;
    private static final long TOW_SECONDS = 2 * 1000;

    public static ForgetPwdFragment newInstance() {
        return new ForgetPwdFragment();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonTitleBar.commonBarTitle.setText(Local.s(getString(R.string.find_password)));
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());

        mBinding.tvViaEmail.setText(Local.s(getString(R.string.via_email)));
        mBinding.tvViaPhone.setText(Local.s(getString(R.string.via_phone)));
        mBinding.btnNext.setText(Local.s(getString(R.string.verify)));

        mBinding.etAccount.addTextChangedListener(mWatcher);
        mBinding.etCode.addTextChangedListener(mWatcher);

        mBinding.tvViaEmail.setOnClickListener(v -> changeSignUpTypeView(UserType.EMAIL, false));
        if (isSupportPhoneForgetPwd()) {
            mBinding.tvViaPhone.setOnClickListener(v -> changeSignUpTypeView(UserType.PHONE, false));
            mBinding.tvPhoneZone.setOnClickListener(v -> toChoosePhoneZone());
        }
        mBinding.btnNext.setOnClickListener(v -> {
            setNextBtnEnable(false);
            onNextClick();
        });

        mBinding.ivCode.setOnClickListener(v -> {
            setIvCodeEnable(false);
            getVerificationCode();
        });


        changeSignUpTypeView(forgetPwdType, true);
        initDefaultPhoneZone();
        updateBtnStateEnable();

        if (isSupportPhoneForgetPwd()) {
            mBinding.vTypeDivider.setVisibility(View.VISIBLE);
            mBinding.tvViaPhone.setVisibility(View.VISIBLE);
        } else {
            mBinding.vTypeDivider.setVisibility(View.GONE);
            mBinding.tvViaPhone.setVisibility(View.GONE);
        }
    }

    private void onNextClick() {
        final String account = mBinding.etAccount.getText().toString().trim();
        if (TextUtils.isEmpty(account)) {
            showErrorToast();
            setNextBtnEnable(true);
            return;
        }

        if (UserType.PHONE == forgetPwdType) {
            final String verifyCode = mBinding.etCode.getText().toString().trim();

            // 手机号
            if (TextUtils.isEmpty(phoneZone) || phoneZone.split(" ").length < 1 || TextUtils.isEmpty(verifyCode)) {
                showErrorToast();
                setNextBtnEnable(true);
                return;
            }
            final String phoneZoneCode = phoneZone.split(" ")[0];
            if (!RegxUtil.isPhoneNumber(phoneZoneCode, account)) {
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.phone_format_illegal));
                setNextBtnEnable(true);
                return;
            }

            toCloseInput();
            final String phoneZone = this.phoneZone;
            showTimeOutLoadinFramgmentWithErrorAlert();
            requestForgetPwdVerifyCodeForPhone(phoneZone, account, verifyCode);
            return;
        }

        // 邮箱
        if (!RegxUtil.isEmail(account)) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.email_format_illegal));
            setNextBtnEnable(true);
            return;
        }

        toCloseInput();
        showTimeOutLoadinFramgmentWithErrorAlert();
        requestForgetPwdVerifyCodeForEmail(account);
    }

    private void requestForgetPwdVerifyCodeForPhone(@NonNull final String phoneZone, @NonNull final String phone, final String verifyCode) {
        final String phoneZoneCode = phoneZone.split(" ")[0];
        DinSDK.getUserInstance().getForgetPWDbyPhone(phoneZoneCode + " " + phone, DDSystemUtil.getWidevineId()
                , verifyCode, verifyId, new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (i == ErrorCode.ERROR_WRONG_VERIFICATION_CODE_ENTERED) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_wrong_verifyCode)));
                            getVerificationCode();
                        } else if (i == ErrorCode.ERROR_VERIFICATION_CODE_ENTERED_TIMEOUT) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_verifyCode_timeout)));
                            getVerificationCode();
                        } else {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));
                        }
                        setNextBtnEnable(true);
                    }

                    @Override
                    public void onSuccess() {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        getDelegateActivity().addCommonFragment(ForgetPwdVerifyCodeFragment.newInstanceForPhone(phoneZoneCode, phone));
                        setNextBtnEnable(true);
                    }
                });
    }

    private void requestForgetPwdVerifyCodeForEmail(@NonNull final String email) {
        DinSDK.getUserInstance().getForgetPWDbyEmail(email, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showErrorToast();
                setNextBtnEnable(true);
            }

            @Override
            public void onSuccess() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                getDelegateActivity().addCommonFragment(ForgetPwdVerifyCodeFragment.newInstanceForEmail(email));
                setNextBtnEnable(true);
            }
        });
    }

    /**
     * 初始化手机号注册的默认区号
     */
    private void initDefaultPhoneZone() {
        String defaultPhoneZone = ChoosePhoneZoneFragment.getCachePhoneZone();
        if (TextUtils.isEmpty(defaultPhoneZone)) {
            phoneZone = APIKey.DEFAULT_PHONE_TEXT;
        } else {
            phoneZone = defaultPhoneZone;
        }
        onPhoneZoneUpdate();
    }

    private void updateBtnStateEnable() {
        final String account = mBinding.etAccount.getText().toString().trim();
        final String verificationCode = mBinding.etCode.getText().toString().trim();

        boolean enable;
        if (UserType.PHONE == forgetPwdType) {
            enable = !TextUtils.isEmpty(account)
                    && !TextUtils.isEmpty(verificationCode)
                    && !TextUtils.isEmpty(phoneZone);
        } else {
            enable = !TextUtils.isEmpty(account);
        }
        setNextBtnEnable(enable);
    }

    private void setNextBtnEnable(final boolean enable) {
        mBinding.btnNext.setAlpha(enable ? 1.0f : 0.5f);
        mBinding.btnNext.setEnabled(enable);
    }

    private void changeSignUpTypeView(@UserType final int newType, final boolean force) {
        if ((forgetPwdType == newType && !force)
                || (UserType.EMAIL != newType && UserType.PHONE != newType)) {
            return;
        }

        if (UserType.PHONE == newType) {
            lastInputEmail = mBinding.etAccount.getText().toString().trim();
            mBinding.etAccount.setText("");

            mBinding.tvViaPhone.setTextColor(getResources().getColor(R.color.color_white_01));
            mBinding.tvViaEmail.setTextColor(getResources().getColor(R.color.color_white_02));

            mBinding.clInputVerificationCode.setVisibility(View.VISIBLE);
            mBinding.tvPhoneZone.setVisibility(View.VISIBLE);
            mBinding.vPhoneLine.setVisibility(View.VISIBLE);
            mBinding.etAccount.setHint(Local.s(getString(R.string.enter_phone_number)));
            mBinding.etCode.setHint(Local.s(getString(R.string.change_phone_code)));
            mBinding.etAccount.setInputType(InputType.TYPE_CLASS_PHONE);

            if (!TextUtils.isEmpty(lastInputPhone)) {
                mBinding.etAccount.setText(lastInputPhone);
            }
            getVerificationCode();
        } else {
            lastInputPhone = mBinding.etAccount.getText().toString().trim();
            mBinding.etAccount.setText("");

            mBinding.tvViaPhone.setTextColor(getResources().getColor(R.color.color_white_02));
            mBinding.tvViaEmail.setTextColor(getResources().getColor(R.color.color_white_01));

            mBinding.clInputVerificationCode.setVisibility(View.GONE);
            mBinding.tvPhoneZone.setVisibility(View.GONE);
            mBinding.vPhoneLine.setVisibility(View.GONE);
            mBinding.etAccount.setHint(Local.s(getString(R.string.email_address)));
            mBinding.etAccount.setInputType(InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);

            if (!TextUtils.isEmpty(lastInputEmail)) {
                mBinding.etAccount.setText(lastInputEmail);
            }
        }

        mBinding.etAccount.setSelection(mBinding.etAccount.getText().length());
        forgetPwdType = newType;
        updateBtnStateEnable();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_forget_pwd;
    }

    public void toChoosePhoneZone() {
        String currentPhoneZone = "";
        if (!TextUtils.isEmpty(phoneZone)) {
            currentPhoneZone = phoneZone;
        }
        ChoosePhoneZoneFragment fragment = ChoosePhoneZoneFragment.newInstance(currentPhoneZone);
        fragment.setCallBack(this);
        getDelegateActivity().addCommonFragment(fragment);
    }

    // 选择时区回调
    @Override
    public void onResult(String code, String name) {
        phoneZone = code + " " + name;
        onPhoneZoneUpdate();
    }

    private void onPhoneZoneUpdate() {
        String phoneCode = "";
        if (!TextUtils.isEmpty(phoneZone) && phoneZone.split(" ").length > 1) {
            phoneCode = phoneZone.split(" ")[0];
        }
        mBinding.tvPhoneZone.setText(phoneCode);
    }

    private boolean isSupportPhoneForgetPwd() {
        return AppConfig.Functions.SUPPORT_PHONE_FUNCTION;
    }

    private void getVerificationCode() {
        mBinding.viewLoading.setVisibility(View.VISIBLE);
        DinSDK.getUserInstance().refreshVerifyCode(DDSystemUtil.getWidevineId(), new IResultCallback2<RefreshVerifyCodeResponse.ResultBean>() {
            @Override
            public void onSuccess(RefreshVerifyCodeResponse.ResultBean resultBean) {
                DDLog.d(TAG, "refreshVerifyCode. onSuccess");
                mBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);
                if (resultBean != null) {
                    verifyId = resultBean.getVerify_id();
                    String base64Image = resultBean.getBase64();
                    Bitmap bitmap = BitmapUtil.convertStringToBitmap(base64Image);
                    Bitmap roundedBitmap = BitmapUtil.getRoundedCornerBitmap(getContext(), bitmap, 6);
                    mBinding.ivCode.setImageBitmap(roundedBitmap);
                }
            }

            @Override
            public void onError(int code, String msg) {
                DDLog.e(TAG, "refreshVerifyCode. onError: " + code + " " + msg);
                mBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);
                if (ErrorCode.ERROR_TOO_MANY_VERIFICATION == code) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_too_many_verifyCode)));
                    return;
                }
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));

            }
        });
    }

    private void setIvCodeEnable(boolean enable) {
        if (enable) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mBinding.ivCode.setEnabled(true);
                    mBinding.ivCode.setAlpha(1.0f);
                }
            }, TOW_SECONDS);
            return;
        }
        mBinding.ivCode.setEnabled(false);
        mBinding.ivCode.setAlpha(0.5f);
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        if (forgetPwdType == UserType.PHONE) {
            getVerificationCode();
        }
    }

}
