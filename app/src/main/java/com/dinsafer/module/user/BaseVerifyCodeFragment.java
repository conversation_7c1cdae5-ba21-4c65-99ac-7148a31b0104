package com.dinsafer.module.user;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.CallSuper;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import com.dinsafer.common.utils.BitmapUtil;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBaseVerifyCodeBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.verify.OnInputListener;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 验证码校验页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/7 19:43
 */
public abstract class BaseVerifyCodeFragment extends MyBaseFragment<FragmentBaseVerifyCodeBinding>
        implements OnInputListener {
    private static final long DEFAULT_TIME_OUT_SECONDS = 120L;

    protected static final int TYPE_DEFAULT = 0;
    protected static final int TYPE_COUNTING_DOWN = 1;
    protected static final int TYPE_TIME_OUT = 2;

    private static final long TOW_SECONDS = 2 * 1000;


    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.SOURCE)
    @IntDef({TYPE_DEFAULT, TYPE_COUNTING_DOWN, TYPE_TIME_OUT})
    protected @interface CountDownViewType {
    }

    @CountDownViewType
    private int countDownViewType = TYPE_DEFAULT;

    private final StringBuilder mCountDownSb = new StringBuilder();
    private long maxCount;
    private Disposable countDownDisposable;

    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private final TextWatcher mWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            updateBtnStateEnable();
        }
    };

    private String verifyId;

    protected int userType = -1;


    protected void clearPassword() {
        mBinding.gpvCode.setText("");
    }

    protected void requestFocusAndOpenInput() {
        mainHandler.post(() -> {
            mBinding.gpvCode.requestFocus();
            toOpenInput();
        });
    }

    protected void setRefreshEnable(final boolean enable) {
        mBinding.ivRefresh.setEnabled(enable);
    }

    protected void changeCountDownViewType(@CountDownViewType int type, boolean force) {
        if (TYPE_DEFAULT != type && TYPE_COUNTING_DOWN != type && TYPE_TIME_OUT != type) {
            DDLog.w(TAG, "不支持的视图类型： " + type);
            return;
        }

        if (countDownViewType == type && !force) {
            return;
        }

        if (TYPE_COUNTING_DOWN == type) {
            mBinding.tvCountDown.setVisibility(View.VISIBLE);
            mBinding.tvCountDown.setText("");
            mBinding.ivRefresh.setVisibility(View.GONE);
            mBinding.clInputVerificationCode.setVisibility(View.GONE);
            mBinding.btnNext.setVisibility(View.GONE);
            startCountDown();
        } else if (TYPE_TIME_OUT == type) {
            cancelCountDown();
            mBinding.tvCountDown.setVisibility(View.GONE);
            mBinding.tvCountDown.setText("");
            mBinding.ivRefresh.setVisibility(View.VISIBLE);
            mBinding.ivRefresh.setEnabled(true);
            mBinding.clInputVerificationCode.setVisibility(View.GONE);
            mBinding.btnNext.setVisibility(View.GONE);
        } else {
            cancelCountDown();
            mBinding.tvCountDown.setVisibility(View.GONE);
            mBinding.ivRefresh.setVisibility(View.GONE);
            mBinding.clInputVerificationCode.setVisibility(View.GONE);
            mBinding.btnNext.setVisibility(View.GONE);
        }
        countDownViewType = type;
    }

    private synchronized void startCountDown() {
        cancelCountDown();

        maxCount = getTimeOutInSeconds();
        if (maxCount <= 0) {
            maxCount = DEFAULT_TIME_OUT_SECONDS;
        }

        mCountDownSb.delete(0, mCountDownSb.length());
        mCountDownSb.append(maxCount)
                .append(" ")
                .append(Local.s("s"));
        mBinding.tvCountDown.setText(mCountDownSb.toString());
        countDownDisposable = Observable.intervalRange(1, maxCount, 1, 1, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe((value) -> {
                    mCountDownSb.delete(0, mCountDownSb.length());
                    mCountDownSb.append(maxCount - value)
                            .append(" ")
                            .append(Local.s("s"));
                    mBinding.tvCountDown.setText(mCountDownSb.toString());
                }, (e) -> {
                    e.printStackTrace();
                    changeCountDownViewType(TYPE_TIME_OUT, true);
                    onTimeOut();
                }, () -> {
                    changeCountDownViewType(TYPE_TIME_OUT, true);
                    onTimeOut();
                });
    }

    private synchronized void cancelCountDown() {
        if (null != countDownDisposable
                && !countDownDisposable.isDisposed()) {
            countDownDisposable.dispose();
            countDownDisposable = null;
        }
    }

    @CallSuper
    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.gpvCode.setOnInputListener(this);

        mBinding.commonTitleBar.commonBarTitle.setLocalText(R.string.verification);
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.ivRefresh.setOnClickListener(v -> {
            mBinding.ivRefresh.setEnabled(false);
            if (userType == UserType.PHONE) {
                mBinding.ivRefresh.setVisibility(View.GONE);
                mBinding.clInputVerificationCode.setVisibility(View.VISIBLE);
                mBinding.btnNext.setVisibility(View.VISIBLE);
                setNextBtnEnable(false);
                getVerificationCode();
            } else if (userType == UserType.EMAIL) {
                onRefreshClick(null, null);
            }
        });

        mBinding.etCode.setHint(Local.s(getString(R.string.change_phone_code)));
        mBinding.etCode.addTextChangedListener(mWatcher);
        mBinding.btnNext.setOnClickListener(v -> {
            final String verifyCode = mBinding.etCode.getText().toString().trim();
            if (TextUtils.isEmpty(verifyCode)) {
                showErrorToast();
                return;
            }
            onRefreshClick(verifyCode, verifyId);
        });
        mBinding.ivCode.setOnClickListener(v -> {
            if (userType == UserType.PHONE) {
                setIvCodeEnable(false);
                getVerificationCode();
            }
        });


        mBinding.etCode.setOnFocusChangeListener((v, hasFocus) -> mBinding.gpvCode.setHideCursor(hasFocus));

        changeCountDownViewType(TYPE_COUNTING_DOWN, true);
    }

    protected void getVerificationCode() {
        mBinding.viewLoading.setVisibility(View.VISIBLE);
        DinSDK.getUserInstance().refreshVerifyCode(DDSystemUtil.getWidevineId(), new IResultCallback2<RefreshVerifyCodeResponse.ResultBean>() {
            @Override
            public void onSuccess(RefreshVerifyCodeResponse.ResultBean resultBean) {
                DDLog.d(TAG, "refreshVerifyCode. onSuccess");
                mBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);
                if (resultBean != null) {
                    verifyId = resultBean.getVerify_id();
                    String base64Image = resultBean.getBase64();
                    Bitmap bitmap = BitmapUtil.convertStringToBitmap(base64Image);
                    Bitmap roundedBitmap = BitmapUtil.getRoundedCornerBitmap(getContext(), bitmap, 6);
                    mBinding.ivCode.setImageBitmap(roundedBitmap);
                }
            }

            @Override
            public void onError(int code, String msg) {
                DDLog.e(TAG, "refreshVerifyCode. onError: " + code + " " + msg);
                mBinding.viewLoading.setVisibility(View.GONE);
                setIvCodeEnable(true);
                if (ErrorCode.ERROR_TOO_MANY_VERIFICATION == code) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_too_many_verifyCode)));
                    return;
                }
                getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));

            }
        });
    }

    private void setIvCodeEnable(boolean enable) {
        if (enable) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mBinding.ivCode.setEnabled(true);
                    mBinding.ivCode.setAlpha(1.0f);
                }
            }, TOW_SECONDS);
            return;
        }
        mBinding.ivCode.setEnabled(false);
        mBinding.ivCode.setAlpha(0.5f);
    }


    private void setNextBtnEnable(final boolean enable) {
        mBinding.btnNext.setAlpha(enable ? 1.0f : 0.5f);
        mBinding.btnNext.setEnabled(enable);
    }

    private void updateBtnStateEnable() {
        final String verificationCode = mBinding.etCode.getText().toString().trim();

        boolean enable = !TextUtils.isEmpty(verificationCode);
        setNextBtnEnable(enable);
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        mBinding.gpvCode.requestFocus();
        toOpenInput();
    }

    @CallSuper
    @Override
    public void onDestroyView() {
        mainHandler.removeCallbacksAndMessages(null);
        changeCountDownViewType(TYPE_DEFAULT, true);
        mBinding.gpvCode.clearFocus();
        super.onDestroyView();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_base_verify_code;
    }

    @Override
    public void onInputFinished(String content) {
        onInputFished(content);
    }

    @Override
    public void onInputChanged(String text) {

    }

    // 倒计时超时时间-单位：s
    protected long getTimeOutInSeconds() {
        return DEFAULT_TIME_OUT_SECONDS;
    }

    protected void onTimeOut() {
    }

    // 验证码输入完成
    protected abstract void onInputFished(@NonNull final String inputCode);

    protected abstract void onRefreshClick(final String verifyCode, final String verifyId);
}
