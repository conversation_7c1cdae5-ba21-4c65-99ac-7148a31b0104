package com.dinsafer.module.user;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.dinsafer.config.DBKey;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.user.api.IRegisterCallback;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dinnet.R;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.module.user.modify.SetUidFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;

/**
 * 用户注册-验证码校验页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/7 20:20
 */
public class SignUpVerifyCodeFragment extends BaseVerifyCodeFragment {
    private static final String KEY_USER_TYPE = "user_type";
    private static final String KEY_ACCOUNT = "account";
    private static final String KEY_PHONE_ZONE_CODE = "phone_zone_code";

    private String phoneZoneCode;
    private String account;

    public static SignUpVerifyCodeFragment newInstanceForPhone(@NonNull final String phoneZoneCode, @NonNull final String phone) {
        final SignUpVerifyCodeFragment fragment = new SignUpVerifyCodeFragment();
        final Bundle args = new Bundle();
        args.putInt(KEY_USER_TYPE, UserType.PHONE);
        args.putString(KEY_PHONE_ZONE_CODE, phoneZoneCode);
        args.putString(KEY_ACCOUNT, phone);
        fragment.setArguments(args);
        return fragment;
    }

    public static SignUpVerifyCodeFragment newInstanceForEmail(@NonNull final String email) {
        final SignUpVerifyCodeFragment fragment = new SignUpVerifyCodeFragment();
        final Bundle args = new Bundle();
        args.putInt(KEY_USER_TYPE, UserType.EMAIL);
        args.putString(KEY_ACCOUNT, email);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        final int type = getArguments().getInt(KEY_USER_TYPE, -1);

        if (UserType.EMAIL != type && UserType.PHONE != type) {
            showErrorToast();
            removeSelf();
            return;
        }

        userType = type;
        phoneZoneCode = getArguments().getString(KEY_PHONE_ZONE_CODE, "");
        account = getArguments().getString(KEY_ACCOUNT, "");

        final String topHint = Local.s(getResources().getString(R.string.send_verification_key)) + " " + getAccountHint();
        mBinding.tvVerifyHint.setText(topHint);
    }

    private String getAccountHint() {
        if (UserType.PHONE == userType) {
            return phoneZoneCode + " " + account;

        }
        return account;
    }

    @Override
    protected void onInputFished(@NonNull String inputCode) {
        if (UserType.PHONE == userType) {
            // 手机号注册验证码校验
            showTimeOutLoadinFramgmentWithErrorAlert();
            requestCheckPhoneVerifyCode(phoneZoneCode + " " + account, inputCode);
            return;
        }

        if (UserType.EMAIL == userType) {
            // 邮箱注册验证码校验
            showTimeOutLoadinFramgmentWithErrorAlert();
            requestCheckEmailVerifyCode(account, inputCode);
            return;
        }

        // 容错
        showErrorToast();
    }

    @Override
    protected void onRefreshClick(String verifyCode, String verifyId) {
        if (UserType.PHONE == userType) {
            // 手机号注册
            showTimeOutLoadinFramgmentWithCallBack(() -> {
                showErrorToast();
                setRefreshEnable(true);
            });
            requestSignUpVerifyCodeForPhone(phoneZoneCode, account, verifyCode, verifyId);
            return;
        }

        if (UserType.EMAIL == userType) {
            // 邮箱注册
            showTimeOutLoadinFramgmentWithCallBack(() -> {
                showErrorToast();
                setRefreshEnable(true);
            });
            requestSignUpVerifyCodeForEmail(account);
            return;
        }

        // 容错
        showErrorToast();
        setRefreshEnable(true);
    }

    /**
     * 校验验证码
     */
    protected void requestCheckEmailVerifyCode(final String target, final String verifyCode) {
        DDLog.d(TAG, "requestCheckEmailVerifyCode");
        DinSDK.getUserInstance().registerAccountWithEmail(target, verifyCode, new IRegisterCallback() {
            @Override
            public void onSuccess(DinUser dinUser) {
                i("onSuccess:" + dinUser.getUser_id());
                onVerifySuccess(target);
            }

            @Override
            public void onError(int i, String s) {
                i("get phone register code error:" + i + " s:" + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (i == -1 && !TextUtils.isEmpty(s)) {
                    showErrorToast();
                } else {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.forget_password_error_code));
                }
                clearPassword();
                requestFocusAndOpenInput();
            }
        });
    }

    /**
     * 校验验证码
     */
    protected void requestCheckPhoneVerifyCode(final String target, final String verifyCode) {
        DDLog.d(TAG, "requestCheckPhoneVerifyCode");
        DinSDK.getUserInstance().registerAccountWithPhone(target, verifyCode, new IRegisterCallback() {
            @Override
            public void onSuccess(DinUser dinUser) {
                i("onSuccess:" + dinUser.getUser_id());
                onVerifySuccess(target);
            }

            @Override
            public void onError(int i, String s) {
                i("get phone register code error:" + i + " s:" + s);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (i == -1 && !TextUtils.isEmpty(s)) {
                    showErrorToast();
                } else {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.forget_password_error_code));
                }
                clearPassword();
                requestFocusAndOpenInput();
            }
        });
    }

    private void onVerifySuccess(final String target) {
        DinSDK.getHomeInstance().createHome(Local.s(getString(R.string.default_family_name)),
                LocalManager.getInstance().getCurrentLanguage(), new IDefaultCallBack2<Home>() {
                    @Override
                    public void onSuccess(Home home) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        //getDelegateActivity().removeAllCommonFragment();
                        removeSelf();
                        DBUtil.Delete(DBKey.REMEMBER_UID);
                        DBUtil.Delete(DBKey.REMEMBER_PHONE);
                        DBUtil.Delete(DBKey.REMEMBER_PHONE_ZONE);
                        getDelegateActivity().addCommonFragment(SetUidFragment.newInstanceForSet());
                    }

                    @Override
                    public void onError(int i, String s) {
                        i("create default home fail:" + i + " s:" + s);
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                    }
                });
    }

    private void requestSignUpVerifyCodeForEmail(@NonNull final String email) {
        DinSDK.getUserInstance().getEmailValidateCode(email, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                i("get phone register code error:" + i + " s:" + s);
                if (i == ErrorCode.ERROR_EMAIL_EXIST) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, (getResources().getString(R.string.error_email_exist)));
                } else {
                    showErrorToast();
                }
                closeLoadingFragmentWithCallBack();
                setRefreshEnable(true);
            }

            @Override
            public void onSuccess() {
                i("onSuccess");
                closeLoadingFragmentWithCallBack();
                changeCountDownViewType(TYPE_COUNTING_DOWN, true);
                getDelegateActivity().showTopToast(R.drawable.icon_toast_succeed, getString(R.string.send_email_verify_code_success_hint));
            }
        });
    }

    private void requestSignUpVerifyCodeForPhone(@NonNull final String phoneZoneCode,
                                                 @NonNull final String phone, final String verifyCode, final String verifyId) {
        DinSDK.getUserInstance().getPhoneValidateCode(phoneZoneCode + " " + phone, DDSystemUtil.getWidevineId()
                , verifyCode, verifyId, new IResultCallback() {
                    @Override
                    public void onError(int i, String s) {
                        i("get phone register code error:" + i + " s:" + s);
                        closeLoadingFragmentWithCallBack();
                        if (i == ErrorCode.ERROR_PHONE_EXIST) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getResources().getString(R.string.error_phone_exist));
                        } else if (i == ErrorCode.ERROR_WRONG_VERIFICATION_CODE_ENTERED) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_wrong_verifyCode)));
                            getVerificationCode();
                        } else if (i == ErrorCode.ERROR_VERIFICATION_CODE_ENTERED_TIMEOUT) {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.error_verifyCode_timeout)));
                            getVerificationCode();
                        } else {
                            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, Local.s(getString(R.string.failed_try_again)));
                        }
                        setRefreshEnable(true);
                    }

                    @Override
                    public void onSuccess() {
                        i("onSuccess");
                        closeLoadingFragmentWithCallBack();
                        changeCountDownViewType(TYPE_COUNTING_DOWN, true);
                    }
                });
    }
}
