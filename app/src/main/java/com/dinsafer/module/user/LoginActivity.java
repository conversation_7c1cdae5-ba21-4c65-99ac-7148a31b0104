package com.dinsafer.module.user;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Process;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;

import com.dinsafer.common.HomeManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityLoginBinding;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.util.TestLog;

import androidx.databinding.DataBindingUtil;

/**
 * <AUTHOR>
 * @date 2023/7/17
 */
public class LoginActivity extends BaseMainActivity {
    private ActivityLoginBinding mBinding;

    public static void start(Context context, boolean showAbnormalLogoutDialog) {
        Intent starter = new Intent(context, LoginActivity.class);
        starter.putExtra("showAbnormalLogoutDialog", showAbnormalLogoutDialog);
        context.startActivity(starter);
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        super.initViews(savedInstanceState);
        HomeManager.getInstance().setCurrentHomeIdEmpty();
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_login);
        FrameLayout.LayoutParams flParams = (FrameLayout.LayoutParams) mBinding.commonTopToastLy.getLayoutParams();
        flParams.topMargin = getStatusBarHeight();
        mBinding.commonTopToastLy.setLayoutParams(flParams);
        addCommonFragment(AccountFragment.newInstance());
        if (getIntent().getBooleanExtra("showAbnormalLogoutDialog", false)) {
            showAbnormalLogoutDialog();
        }
    }

    @Override
    public void onBackPressed() {
        try {
            if (isFragmentInTop(AccountFragment.class.getSimpleName())) {
                exitApp();
                return;
            }
            if (getFragmentList().get(getFragmentList().size() - 1).onBackPressed()) {
                return;
            }
            removeCommonFragmentAndData(getFragmentList().get(getFragmentList().size() - 1), true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void exitApp() {
        AlertDialog builder = AlertDialog.createBuilder(this)
                .setContent(getResources().getString(R.string.exit_app))
                .setOk(getResources().getString(R.string.Confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        Process.killProcess(Process.myPid());    //获取PID
                        System.exit(0);   //常规java、c#的标准退出法，返回值为0代表正常退出
                    }
                })
                .setCancel(getResources().getString(R.string.Cancel))
                .preBuilder();
        builder.show();
    }


    @Override
    public void showTopToast(int iconResId, String message) {
        if (!TextUtils.isEmpty(message)) {
            final Drawable leftIcon;
            if (iconResId > 0) {
                leftIcon = getResources().getDrawable(iconResId);
                leftIcon.setBounds(0, 0, leftIcon.getMinimumWidth(), leftIcon.getMinimumHeight());
            } else {
                leftIcon = null;
            }
            mBinding.commonTopToast.setCompoundDrawables(leftIcon, null, null, null);
            mBinding.commonTopToast.setLocalText(message);
            //隐藏配件屏蔽信息
            mBinding.commonTopToastBlock.setVisibility(View.GONE);
            mBinding.commonTopToastBlockPlugin.setVisibility(View.GONE);
            mBinding.ivLine2.setVisibility(View.GONE);
            mBinding.ivLine1.setVisibility(View.VISIBLE);
            mBinding.commonTopToastLy.showToast();
        }
    }


    public void showTopToast(String message, String blockPlugin) {
        showTopToast(0, message, blockPlugin);
    }

    public void showTopToast(int icon, String message, String blockPlugin) {
        if (!TextUtils.isEmpty(message)) {
            if (TextUtils.isEmpty(blockPlugin)) {
                mBinding.commonTopToastBlock.setVisibility(View.GONE);
                mBinding.commonTopToastBlockPlugin.setVisibility(View.GONE);
                mBinding.ivLine2.setVisibility(View.GONE);
                mBinding.ivLine1.setVisibility(View.VISIBLE);
            } else {
                mBinding.commonTopToastBlock.setLocalText(getString(R.string.Bypassed));
                mBinding.commonTopToastBlockPlugin.setText(blockPlugin);
                mBinding.commonTopToastBlock.setVisibility(View.VISIBLE);
                mBinding.commonTopToastBlockPlugin.setVisibility(View.VISIBLE);
                mBinding.ivLine2.setVisibility(View.VISIBLE);
                mBinding.ivLine1.setVisibility(View.GONE);

            }
            if (icon != 0) {
                Drawable leftIcon = getResources().getDrawable(icon);
                leftIcon.setBounds(0, 0, leftIcon.getMinimumWidth(), leftIcon.getMinimumHeight());
                mBinding.commonTopToast.setCompoundDrawables(leftIcon, null, null, null);
            } else {
                mBinding.commonTopToast.setCompoundDrawables(null, null, null, null);
            }
            mBinding.commonTopToast.setLocalText(message);

            mBinding.commonTopToastLy.showToast();
        }
    }

    public void showTopToast(String message) {
        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                showTopToast(R.drawable.icon_toast_succeed, message, "");
            }
        });
    }


    private AlertDialog mAbnormalLogoutDialog;

    /**
     * 异常退出登录提示弹窗
     */
    public boolean showAbnormalLogoutDialog() {
        TestLog.d(null, "showAbnormalLogoutDialog: " + TestLog.getStackTrace());
        if (null != mAbnormalLogoutDialog
                && mAbnormalLogoutDialog.isShowing()) {
            return false;
        }
        mAbnormalLogoutDialog = AlertDialog.createBuilder(this)
                .setOk("OK")
                .setContent(getResources().getString(R.string.user_logouted))
                .preBuilder();
        mAbnormalLogoutDialog.show();
        return true;
    }

}
