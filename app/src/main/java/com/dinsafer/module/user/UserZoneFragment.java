package com.dinsafer.module.user;

import android.Manifest;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.ContentValues;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.dinsafer.common.BmtManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.api.ILogoutCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.UserZoneLayoutBinding;
import com.dinsafer.model.BindEmailSuccessEvent;
import com.dinsafer.model.BindPhoneSuccessEvent;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.model.UnBindEmail;
import com.dinsafer.model.UnBindPhone;
import com.dinsafer.model.UserUidChangeEvent;
import com.dinsafer.model.event.GetDeviceInfoEvent;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.model.event.HadLogoutPreEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.ui.ChangeEmailFragment;
import com.dinsafer.module.settting.ui.ChangePhoneFragment;
import com.dinsafer.module.settting.ui.ChangeUidFragment;
import com.dinsafer.module.settting.ui.MyFamilyFragment;
import com.dinsafer.module.settting.ui.UserEmailFragment;
import com.dinsafer.module.settting.ui.UserPhoneFragment;
import com.dinsafer.module.user.modify.ChangePwdCheckOldPwdFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.CircularView;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDFileUtil;
import com.dinsafer.util.DDImageUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.viewanimator.AnimationBuilder;
import com.dinsafer.util.viewanimator.AnimationListener;
import com.dinsafer.util.viewanimator.ViewAnimator;
import com.edmodo.cropper.CropImageView;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.dinsafer.aop.annotations.Safer;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.util.Random;

import retrofit2.Call;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;


/**
 * Created by Rinfon on 16/7/8.
 */
public class UserZoneFragment extends MyBaseFragment<UserZoneLayoutBinding> implements ActionSheet.ActionSheetListener {


    private static final long MIDDLE_CIRCLE_TIME = 43700;

    private static final long OUT_CIRCLE_TIME = 70500;

    private static final int LINE_MARGIN_TOP = 5;

    private static float EMAIL_ANGLE_END = 34;

    private static float EMAIL_ANGLE_START = -45;

    private static long EMAIL_DELAY = 450;

    private static long EMAIL_TIME = 550;

    private static long AVATAR_TIME = 850;

    private static long INNER_CIRCLE_TIME = 620;

    private static float INNER_CIRCLE_ALPHA_END = 0.7f;

    private static long INNER_CIRCLE_DELAY_TIME = 170;

    private static float MIDDLE_CIRCLE_ALPHA_END = 0.3f;

    private static long MIDDLE_CIRCLE_DELAY_TIME = 270;

    private static long MIDDLE_CIRCLE_ALPHA_TIME = 400;

    private static long OUT_CIRCLE_DELAY_TIME = 430;

    private static long OUT_CIRCLE_ALPHA_TIME = 480;

    private static float OUT_CIRCLE_ALPHA_END = 0.15f;

    private static final int MIN_ICON_SIZE = 28;

    private static final int DEFAULT_ICON_SIZE = 32;

    private static final int MAX_ICON_SIZE = 41;

    private static final int MAX_WARN_ICON_SIZE = 19;

    private static final long PHONE_DELAY = 350;

    private static final long PHONE_TIME = 470;

    private static final float PHONE_ANGLE_START = 40;

    private static final float PHONE_ANGLE_END = 130;

    private static final float FAQ_ANGLE_END = 265;

    private static final float FAQ_ANGLE_START = 205;

    private static final long FAQ_TIME = 370;

    private static final long FAQ_DELAY = 500;

    private static float DEVICE_ANGLE_END = 337;

    private static float DEVICE_ANGLE_START = 290;

    private static long DEVICE_DELAY = 1070;

    private static long DEVICE_TIME = 520;

    private static float PASSWORD_ANGLE_END = 205;

    private static float PASSWORD_ANGLE_START = 90;

    private static long PASSWORD_DELAY = 800;

    private static long PASSWORD_TIME = 520;

    private static final int ICON_SIZE_RANGE = MAX_ICON_SIZE - MIN_ICON_SIZE;

    private static long BACK_TIME = 1000;

//============================相片相关==========================

    public static final int REQUEST_CODE_CAPTURE_CAMEIA_FROM_AVATAR = 500;

    public static final int REQUEST_CODE_PICK_IMAGE_FROM_AVATAR = 501;

    public static final int REQUEST_CODE_PICK_IMAGE_FROM_BANNER = 503;

    public static final int REQUEST_CODE_CAPTURE_CAMEIA_FROM_BANNER = 504;

    public static final int REQUEST_CROP_IMAGE = 502;

    private String capturePath;

//============================相片相关==========================

    ImageView userZoneEmailWarn, userZonePhoneWarn, userZoneDeviceWarn, userZonePasswordWarn;

    ImageView userZoneEmail;

    ImageView userZonePhone;

    ImageView userZoneFAQ;

    ImageView userZoneDevice;

    ImageView userZonePassword;

    RelativeLayout userZoneEmailLayout, userZoneEmailInnerLayout;

    RelativeLayout userZonePhoneLayout, userZonePhoneInnerLayout;

    RelativeLayout userZoneFAQLayout;

    RelativeLayout userZoneDeviceLayout, userZoneDeviceInnerLayout;

    RelativeLayout userZonePasswordLayout, userZonePasswordInnerLayout;

    private int phoneWidth;

    private int emailWidth;

    private int faqWidth;

    private int deviceWidth;

    private int passwordWidth;

    private Bitmap mUploadImage;

    /**
     * 拍照保存时图片的Uri
     */
    private Uri mPhotoUri;

    private int mStatusBarHeight;

    private Observable<String> uploadObservable = Observable.create(new Observable.OnSubscribe<String>() {
        @Override
        public void call(Subscriber<? super String> subscriber) {
            subscriber.onNext(uploadImage(mUploadImage));
            subscriber.onCompleted();
        }
    }).subscribeOn(AndroidSchedulers.mainThread()).observeOn(AndroidSchedulers.mainThread());

    public static UserZoneFragment newInstance() {
        return new UserZoneFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.user_zone_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        uploadObservable.compose(this.<String>bindToLifecycle());
        getDelegateActivity().setTheme(R.style.ActionSheetStyleiOS7);
        mStatusBarHeight = DensityUtils.getStatusBarHeight(getContext());
        initView();
        initAnim();
        EventBus.getDefault().register(this);
    }

    int emailMargin;

    int phoneMargin;

    int faqMargin;

    int deviceMargin;

    int passwordMargin;

    int padding = 10;

    private void initView() {


        emailWidth = DisplayUtil.dip2px(getDelegateActivity(), DEFAULT_ICON_SIZE);


//      初始化email layout
//        int emailWidth = getResources().getDimensionPixelOffset(R.dimen.user_zone_max_icon_size);
        emailMargin = (getResources().getDimensionPixelOffset(R.dimen.user_zone_inner_circle_size)
                - getResources().getDimensionPixelOffset(R.dimen.user_zone_avatar_size) - emailWidth - padding) / 2;
        userZoneEmailLayout = new RelativeLayout(getDelegateActivity());
        RelativeLayout.LayoutParams emailLayoutParams = new RelativeLayout.LayoutParams(emailWidth + padding,
                emailWidth + padding);
        emailLayoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        emailLayoutParams.addRule(RelativeLayout.ABOVE, R.id.user_zone_avatar);
        emailLayoutParams.setMargins(0, 0, 0, emailMargin);

        userZoneEmailLayout.setLayoutParams(emailLayoutParams);


        userZoneEmailInnerLayout = new RelativeLayout(getDelegateActivity());
        userZoneEmailInnerLayout.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));

        userZoneEmailLayout.addView(userZoneEmailInnerLayout);

// ====================================================================================================================

//      初始化email
        userZoneEmail = new ImageView(getDelegateActivity());
//        userZoneEmail.setId(View.generateViewId());
        RelativeLayout.LayoutParams emailParams = new RelativeLayout.LayoutParams(emailWidth, emailWidth);
        emailParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        userZoneEmail.setLayoutParams(emailParams);
        userZoneEmail.setImageResource(R.drawable.btn_userpage_email_sel);
        userZoneEmailInnerLayout.addView(userZoneEmail);
// ====================================================================================================================

//        初始化email warn
        userZoneEmailWarn = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams emailWarnParams = new RelativeLayout.LayoutParams((MAX_WARN_ICON_SIZE * emailWidth / MAX_ICON_SIZE) / 1
                , (MAX_WARN_ICON_SIZE * emailWidth / MAX_ICON_SIZE) / 1);
        emailWarnParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        emailWarnParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        userZoneEmailWarn.setLayoutParams(emailWarnParams);
        userZoneEmailWarn.setScaleType(ImageView.ScaleType.FIT_CENTER);
        userZoneEmailWarn.setImageResource(R.drawable.icon_userpage_warning);

        userZoneEmailInnerLayout.addView(userZoneEmailWarn);

        mBinding.userZone.addView(userZoneEmailLayout);
// ====================================================================================================================

//        初始化phonelayout

        phoneWidth = DisplayUtil.dip2px(getDelegateActivity(), DEFAULT_ICON_SIZE);

        phoneMargin = (getResources().getDimensionPixelOffset(R.dimen.user_zone_inner_circle_size)
                - getResources().getDimensionPixelOffset(R.dimen.user_zone_avatar_size) - phoneWidth - padding) / 2;
        userZonePhoneLayout = new RelativeLayout(getDelegateActivity());
        RelativeLayout.LayoutParams phoneLayoutParams = new RelativeLayout.LayoutParams(phoneWidth + padding,
                phoneWidth + padding);
        phoneLayoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        phoneLayoutParams.addRule(RelativeLayout.ABOVE, R.id.user_zone_avatar);
        phoneLayoutParams.setMargins(0, 0, 0, phoneMargin);
        userZonePhoneLayout.setLayoutParams(phoneLayoutParams);


        userZonePhoneInnerLayout = new RelativeLayout(getDelegateActivity());
        userZonePhoneInnerLayout.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));

        userZonePhoneLayout.addView(userZonePhoneInnerLayout);
// ====================================================================================================================

//        phone icon
        userZonePhone = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams phoneParams = new RelativeLayout.LayoutParams(phoneWidth, phoneWidth);
        phoneParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        userZonePhone.setLayoutParams(phoneParams);
        userZonePhone.setImageResource(R.drawable.btn_userpage_phone_sel);

        userZonePhoneInnerLayout.addView(userZonePhone);
// ====================================================================================================================

//        phone warn
        userZonePhoneWarn = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams phoneWarnParams = new RelativeLayout.LayoutParams((MAX_WARN_ICON_SIZE * phoneWidth / MAX_ICON_SIZE) / 1
                , (MAX_WARN_ICON_SIZE * phoneWidth / MAX_ICON_SIZE) / 1);
        phoneWarnParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        phoneWarnParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        userZonePhoneWarn.setLayoutParams(phoneWarnParams);
        userZonePhoneWarn.setScaleType(ImageView.ScaleType.FIT_CENTER);
        userZonePhoneWarn.setImageResource(R.drawable.icon_userpage_warning);

        userZonePhoneInnerLayout.addView(userZonePhoneWarn);
// ====================================================================================================================


        mBinding.userZone.addView(userZonePhoneLayout);

//      初始化FAQ
        faqWidth = DisplayUtil.dip2px(getDelegateActivity(), DEFAULT_ICON_SIZE);

        faqMargin = (getResources().getDimensionPixelOffset(R.dimen.user_zone_inner_circle_size)
                - getResources().getDimensionPixelOffset(R.dimen.user_zone_avatar_size) - faqWidth - padding) / 2;
        userZoneFAQLayout = new RelativeLayout(getDelegateActivity());
        RelativeLayout.LayoutParams faqLayoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        faqLayoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        faqLayoutParams.addRule(RelativeLayout.ABOVE, R.id.user_zone_avatar);
        faqLayoutParams.setMargins(0, 0, 0, faqMargin);
        userZoneFAQLayout.setLayoutParams(faqLayoutParams);

        userZoneFAQ = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams faqParams = new RelativeLayout.LayoutParams(faqWidth, faqWidth);
        faqParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        userZoneFAQ.setLayoutParams(faqParams);
        userZoneFAQ.setScaleType(ImageView.ScaleType.CENTER_CROP);
        userZoneFAQ.setImageResource(R.drawable.btn_userpage_key_sel);

        userZoneFAQLayout.addView(userZoneFAQ);
        mBinding.userZone.addView(userZoneFAQLayout);

//      初始化Device
        deviceWidth = DisplayUtil.dip2px(getDelegateActivity(), DEFAULT_ICON_SIZE);

        deviceMargin = (getResources().getDimensionPixelOffset(R.dimen.user_zone_middle_circle_size)
                - getResources().getDimensionPixelOffset(R.dimen.user_zone_avatar_size) - deviceWidth - padding) / 2;
        userZoneDeviceLayout = new RelativeLayout(getDelegateActivity());
        RelativeLayout.LayoutParams deviceLayoutParams = new RelativeLayout.LayoutParams(deviceWidth + padding,
                deviceWidth + padding);
        deviceLayoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        deviceLayoutParams.addRule(RelativeLayout.ABOVE, R.id.user_zone_avatar);
        deviceLayoutParams.setMargins(0, 0, 0, deviceMargin);
        userZoneDeviceLayout.setLayoutParams(deviceLayoutParams);

        userZoneDeviceInnerLayout = new RelativeLayout(getDelegateActivity());
        userZoneDeviceInnerLayout.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));

        userZoneDeviceLayout.addView(userZoneDeviceInnerLayout);
// ====================================================================================================================

//        初始化device icon
        userZoneDevice = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams deviceParams = new RelativeLayout.LayoutParams(deviceWidth, deviceWidth);
        deviceParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        userZoneDevice.setLayoutParams(deviceParams);
        userZoneDevice.setScaleType(ImageView.ScaleType.CENTER_CROP);
        userZoneDevice.setImageResource(R.drawable.btn_userpage_family_sel);

        userZoneDeviceInnerLayout.addView(userZoneDevice);
// ====================================================================================================================

//        初始化device warn
        userZoneDeviceWarn = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams deviceWarnParams = new RelativeLayout.LayoutParams((MAX_WARN_ICON_SIZE * deviceWidth / MAX_ICON_SIZE) / 1
                , (MAX_WARN_ICON_SIZE * deviceWidth / MAX_ICON_SIZE) / 1);
        deviceWarnParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        deviceWarnParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        userZoneDeviceWarn.setLayoutParams(deviceWarnParams);
        userZoneDeviceWarn.setScaleType(ImageView.ScaleType.FIT_CENTER);
        userZoneDeviceWarn.setImageResource(R.drawable.icon_userpage_warning);

        userZoneDeviceInnerLayout.addView(userZoneDeviceWarn);
// ====================================================================================================================

        mBinding.userZone.addView(userZoneDeviceLayout);

// ====================================================================================================================

//        初始化password
        passwordWidth = DisplayUtil.dip2px(getDelegateActivity(), DEFAULT_ICON_SIZE);

        passwordMargin = (getResources().getDimensionPixelOffset(R.dimen.user_zone_middle_circle_size)
                - getResources().getDimensionPixelOffset(R.dimen.user_zone_avatar_size) - passwordWidth - padding) / 2;
        userZonePasswordLayout = new RelativeLayout(getDelegateActivity());
        RelativeLayout.LayoutParams passwordLayoutParams = new RelativeLayout.LayoutParams(passwordWidth + padding, passwordWidth + padding);
        passwordLayoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
        passwordLayoutParams.addRule(RelativeLayout.ABOVE, R.id.user_zone_avatar);
        passwordLayoutParams.setMargins(0, 0, 0, passwordMargin);
        userZonePasswordLayout.setLayoutParams(passwordLayoutParams);


        userZonePasswordInnerLayout = new RelativeLayout(getDelegateActivity());
        userZonePasswordInnerLayout.setLayoutParams(new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));

        userZonePasswordLayout.addView(userZonePasswordInnerLayout);
// ====================================================================================================================

//        初始化password icon
        userZonePassword = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams passwordParams = new RelativeLayout.LayoutParams(passwordWidth, passwordWidth);
        passwordParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        userZonePassword.setLayoutParams(passwordParams);
        userZonePassword.setScaleType(ImageView.ScaleType.CENTER_CROP);
        userZonePassword.setImageResource(R.drawable.btn_userpage_key_sel);

        userZonePasswordInnerLayout.addView(userZonePassword);
// ====================================================================================================================

//        初始化password warn
        userZonePasswordWarn = new ImageView(getDelegateActivity());
        RelativeLayout.LayoutParams passwordWarnParams = new RelativeLayout.LayoutParams((MAX_WARN_ICON_SIZE * deviceWidth / MAX_ICON_SIZE) / 1
                , (MAX_WARN_ICON_SIZE * deviceWidth / MAX_ICON_SIZE) / 1);
        passwordWarnParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        passwordWarnParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        userZonePasswordWarn.setLayoutParams(passwordWarnParams);
        userZonePasswordWarn.setScaleType(ImageView.ScaleType.FIT_CENTER);
        userZonePasswordWarn.setImageResource(R.drawable.icon_userpage_warning);

        userZonePasswordInnerLayout.addView(userZonePasswordWarn);
// ====================================================================================================================

        mBinding.userZone.addView(userZonePasswordLayout);

// ====================================================================================================================

//        初始化click事件
        initClick();
    }

    private void initClick() {
        mBinding.commonBarBack.setOnClickListener(v -> close());
        mBinding.userZoneName.setOnClickListener(v -> toChangeUserName());
        mBinding.userZoneAvatar.setOnClickListener(v -> toSetAvatar());
        mBinding.userZoneLogoutText.setOnClickListener(v -> toLogout());

        userZonePasswordLayout.setOnClickListener(view -> {
//                toPassword();
//                不要问我为什么password的点击事件会是faq？因为人在江湖，身不由己！
//                getDelegateActivity().addCommonFragment(FAQFragment.newInstance());
            toPassword();
        });

        userZoneEmailLayout.setOnClickListener(view -> {
            if (TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail())) {
                getDelegateActivity().addCommonFragment(ChangeEmailFragment.newInstance());
            } else {
                getDelegateActivity().addCommonFragment(UserEmailFragment.newInstance());
            }
        });

        if (AppConfig.Functions.SUPPORT_PHONE_FUNCTION) {
            userZonePhoneLayout.setOnClickListener(view -> {
                if (TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone())) {
                    getDelegateActivity().addCommonFragment(ChangePhoneFragment.newInstance());
                } else {
                    getDelegateActivity().addCommonFragment(UserPhoneFragment.newInstance());
                }

            });
        }

        userZoneDeviceLayout.setOnClickListener(view -> getDelegateActivity().addCommonFragment(MyFamilyFragment.newInstance()));

        userZoneFAQLayout.setOnClickListener(view -> {
//                getDelegateActivity().addCommonFragment(FAQFragment.newInstance());
//                自己看上面！！
//                toPassword();
        });

        toUpdataBadge();
    }

    private void toPassword() {
        getDelegateActivity().addCommonFragment(ChangePwdCheckOldPwdFragment.newInstance());
    }


    public void toUpdataBadge() {
        mBinding.userZoneBadgeview.setVisibility(View.GONE);
//        if (DinSDK.getUserInstance().isLogin() &&
//                (TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail()) ||
//                        TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone()))) {
//            userZoneBadgeview.setVisibility(View.VISIBLE);
//        } else {
//            userZoneBadgeview.setVisibility(View.GONE);
//        }
    }


    @Override
    public void initData() {
        super.initData();
        updateUI();
        updateUserName();
        updateUserAvatar();
    }

    private void updateUI() {
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.user_zone_title));
        mBinding.userZonePhoneHint.setLocalText(getResources().getString(R.string.user_zone_phone_hint));
        mBinding.userZoneEmailHint.setLocalText(getResources().getString(R.string.user_zone_email_hint));
        mBinding.userZoneDeviceHint.setLocalText(getResources().getString(R.string.user_zone_family_hint));
//        userZoneFaqHint.setLocalText(getResources().getString(R.string.user_zone_password_hint));
        mBinding.userZonePasswordHint.setLocalText(getResources().getString(R.string.user_zone_password_hint));
        mBinding.userZoneLogoutText.setLocalText(getResources().getString(R.string.user_zone_logout));
        mBinding.userZoneMoreSetting.setLocalText(getResources().getString(R.string.user_zone_more_setting_text));
        mBinding.userZoneCropRota.setLocalText(getResources().getString(R.string.Rota));
        mBinding.userZoneCropSave.setLocalText(getResources().getString(R.string.Confirm));
        mBinding.userZoneCropCancel.setLocalText(getResources().getString(R.string.Cancel));
    }

    @Safer
    private void updateUserName() {
        if (DinSDK.getUserInstance().isLogin()) {
            mBinding.userZoneName.setText(DinSDK.getUserInstance().getUser().getUid());
            mBinding.userZoneMoreSetting.setVisibility(View.GONE);

            if (TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail())) {
                userZoneEmail.setImageResource(R.drawable.btn_userpage_email_nor);
                userZoneEmailWarn.setVisibility(View.VISIBLE);
            } else {
                userZoneEmail.setImageResource(R.drawable.btn_userpage_email_sel);
                userZoneEmailWarn.setVisibility(View.INVISIBLE);
            }
            if (TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone())) {
                userZonePhone.setImageResource(R.drawable.btn_userpage_phone_nor);
                userZonePhoneWarn.setVisibility(View.VISIBLE);
            } else {
                userZonePhone.setImageResource(R.drawable.btn_userpage_phone_sel);
                userZonePhoneWarn.setVisibility(View.INVISIBLE);
            }

            userZoneDeviceWarn.setVisibility(View.INVISIBLE);

            userZonePasswordWarn.setVisibility(View.INVISIBLE);
        }
    }

    public void toSetAvatar() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.user_zone_get_from_media)),
                        Local.s(getResources().getString(R.string.user_zone_get_from_camera)))
                .setCancelableOnTouchOutside(true)
                .setListener(this).show();
    }

    public void close() {
        getMainActivity().smoothToSetting();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (logoutCall != null) {
            logoutCall.cancel();
        }
        EventBus.getDefault().unregister(this);
    }

    AnimationBuilder emailAnim;
    AnimationBuilder phoneAnim;
    AnimationBuilder faqAnim;
    AnimationBuilder avatarAnim;
    AnimationBuilder innerCircleAnim;
    AnimationBuilder middleCircleAnim, middleCircleRotateAnim;
    AnimationBuilder outCircleAnim, outCircleRotateAnim;
    AnimationBuilder deviceAnim;
    AnimationBuilder passwordAnim;
    AnimationBuilder backAnim;
    AnimationBuilder phoneHintAnim, emailHintAnim, deviceHintAnim, passwordHintAnim, faqHintAnim;
    AnimationBuilder phoneWarnAnim, emailWarnAnim, deviceWarnAnim, passwordWarnAnim;


    Handler mPhoneHandler, mEmailHandler, mDeviceHandler, mPasswordHandler;

    private void initAnim() {
//        email anim
        final float emailLeft = emailWidth / 2;
        float phoneLeft = phoneWidth / 2;
        float faqLeft = faqWidth / 2;
        float deviceLeft = deviceWidth / 2;
        float passwordLeft = passwordWidth / 2;
        float emailR = getResources().getDimensionPixelOffset(R.dimen.user_zone_inner_circle_size) / 2 + emailLeft;
        float phoneR = getResources().getDimensionPixelOffset(R.dimen.user_zone_inner_circle_size) / 2 + phoneLeft;
        float faqR = getResources().getDimensionPixelOffset(R.dimen.user_zone_inner_circle_size) / 2 + faqLeft;
        float deviceR = getResources().getDimensionPixelOffset(R.dimen.user_zone_middle_circle_size) / 2 + deviceLeft;
        float passwordR = getResources().getDimensionPixelOffset(R.dimen.user_zone_middle_circle_size) / 2 + passwordLeft;


        mPhoneHandler = new Handler();

        mEmailHandler = new Handler();

        mDeviceHandler = new Handler();

        mPasswordHandler = new Handler();

        emailHintAnim = ViewAnimator.animate(mBinding.userZoneEmailHintLayout)
                .duration(130)
                .flash();

        emailWarnAnim = ViewAnimator.animate(userZoneEmailWarn)
                .duration(100)
                .flash()
//                .scale(0.5f, 1.5f, 1f)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!UserZoneFragment.this.isVisible())
                                    return;
                                emailWarnAnim.start();
                            }
                        }, 9000);
                    }
                });

        phoneWarnAnim = ViewAnimator.animate(userZonePhoneWarn)
                .duration(100)
                .flash()
//                .scale(0.5f, 1.5f, 1f)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!UserZoneFragment.this.isVisible())
                                    return;
                                phoneWarnAnim.start();
                            }
                        }, 9000);
                    }
                });

        deviceWarnAnim = ViewAnimator.animate(userZoneDeviceWarn)
                .duration(100)
                .flash()
//                .scale(0.5f, 1.5f, 1f)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!UserZoneFragment.this.isVisible())
                                    return;
                                deviceWarnAnim.start();
                            }
                        }, 9000);
                    }
                });

        passwordWarnAnim = ViewAnimator.animate(userZonePasswordWarn)
                .duration(100)
                .flash()
//                .scale(0.5f, 1.5f, 1f)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (!UserZoneFragment.this.isVisible())
                                    return;
                                passwordWarnAnim.start();
                            }
                        }, 9000);
                    }
                });

        deviceHintAnim = ViewAnimator.animate(mBinding.userZoneDeviceHintLayout)
                .duration(130)
                .flash();

        passwordHintAnim = ViewAnimator.animate(mBinding.userZonePasswordHintLayout)
                .duration(130)
                .flash();

        faqHintAnim = ViewAnimator.animate(mBinding.userZoneFaqHintLayout)
                .duration(130)
                .flash();

        emailAnim = ViewAnimator.animate(userZoneEmailLayout)
                .rotation(EMAIL_ANGLE_START, EMAIL_ANGLE_END)
                .pivotY(emailR)
                .pivotX(emailLeft)
                .duration(EMAIL_TIME)
                .andAnimate(userZoneEmailInnerLayout)
                .alpha(0, 1)
                .rotation(EMAIL_ANGLE_START * -1, EMAIL_ANGLE_END * -1)
                .duration(EMAIL_TIME)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        //        初始化email hint
                        if (!UserZoneFragment.this.isVisible())
                            return;
                        int[] location = new int[2];
                        userZoneEmailLayout.getLocationOnScreen(location);
                        int emailx = location[0];//获取当前位置的横坐标
                        int emaily = location[1];//获取当前位置的纵坐标
                        emailx = emailx + emailWidth * 2 / 5;
//                        emaily = emaily - emailWidth * 3 / 4 - LINE_MARGIN_TOP - padding;
                        emaily = emaily - mBinding.userZoneEmailHintLayout.getHeight() * 4 / 5 - mStatusBarHeight;

//                         if (emailx + userZoneEmailHintLayout.getWidth() >= DDSystemUtil.getDeviceWidth(getActivity())) {
// //                            超出屏幕多少
//                             int tempWidth = emailx + userZoneEmailHintLayout.getWidth() - DDSystemUtil.getDeviceWidth(getActivity());
//                             userZoneEmailHint.setWidth(userZoneEmailHint.getWidth() - tempWidth - 10);
//                             userZoneEmailHintLayout.getLayoutParams().width = userZoneEmailHintLayout.getWidth() - tempWidth - 10;
//                         }


                        mBinding.userZoneEmailHintLayout.setX(emailx);
                        mBinding.userZoneEmailHintLayout.setY(emaily);
                        mBinding.userZoneEmailHintLayout.setAlpha(0f);
                        emailHintAnim.start();

                        mEmailHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                emailWarnAnim.start();

                            }
                        }, 170);
                    }
                });

        avatarAnim = ViewAnimator.animate(mBinding.userZoneAvatar)
                .alpha(0, 1)
                .duration(AVATAR_TIME)
                .andAnimate(mBinding.userZoneName)
                .alpha(0, 1)
                .duration(AVATAR_TIME);

        innerCircleAnim = ViewAnimator.animate(mBinding.userZoneInnerCircle)
                .alpha(0, INNER_CIRCLE_ALPHA_END)
                .duration(INNER_CIRCLE_TIME);

        middleCircleAnim = ViewAnimator.animate(mBinding.userZoneMiddleCircle)
                .alpha(0, MIDDLE_CIRCLE_ALPHA_END)
                .duration(MIDDLE_CIRCLE_ALPHA_TIME);

        middleCircleRotateAnim = ViewAnimator.animate(mBinding.userZoneMiddleCircleLayout)
                .rotation(0, 360f)
                .duration(MIDDLE_CIRCLE_TIME)
                .interpolator(new LinearInterpolator())
                .repeatCount(ValueAnimator.INFINITE);

        outCircleAnim = ViewAnimator.animate(mBinding.userZoneOutCircle)
                .alpha(0, OUT_CIRCLE_ALPHA_END)
                .duration(OUT_CIRCLE_ALPHA_TIME);

        outCircleRotateAnim = ViewAnimator.animate(mBinding.userZoneOutCircleLayout)
                .rotation(0, -360)
                .duration(OUT_CIRCLE_TIME)
                .interpolator(new LinearInterpolator())
                .repeatCount(ValueAnimator.INFINITE);

        phoneHintAnim = ViewAnimator.animate(mBinding.userZonePhoneHintLayout)
                .duration(130)
                .flash();

        phoneAnim = ViewAnimator.animate(userZonePhoneLayout)
                .rotation(PHONE_ANGLE_START, PHONE_ANGLE_END)
                .pivotY(phoneR)
                .pivotX(phoneLeft)
                .duration(PHONE_TIME)
                .andAnimate(userZonePhoneInnerLayout)
                .alpha(0, 1)
                .rotation(PHONE_ANGLE_START * -1, PHONE_ANGLE_END * -1)
                .duration(PHONE_TIME)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
//        初始化phone hint
                        if (!UserZoneFragment.this.isVisible())
                            return;
                        int[] location = new int[2];
                        DDLog.i("width", DDSystemUtil.getDeviceWidth(getActivity()) + "");
                        userZonePhoneLayout.getLocationOnScreen(location);
                        int phonex = location[0];//获取当前位置的横坐标
                        int phoney = location[1];//获取当前位置的纵坐标
                        phonex = phonex - phoneWidth * 3 / 5;
                        phoney = phoney + phoneWidth * 3 / 5 - mStatusBarHeight;

//                         if (phonex + userZonePhoneHintLayout.getWidth() >= DDSystemUtil.getDeviceWidth(getActivity())) {
// //                            超出屏幕多少
//                             int tempWidth = phonex + userZonePhoneHintLayout.getWidth() - DDSystemUtil.getDeviceWidth(getActivity());
//                             userZonePhoneHint.setWidth(userZonePhoneHint.getWidth() - tempWidth - 10);
//                             userZonePhoneHintLayout.getLayoutParams().width = userZonePhoneHintLayout.getWidth() - tempWidth - 10;
//                         }

                        mBinding.userZonePhoneHintLayout.setX(phonex);
                        mBinding.userZonePhoneHintLayout.setY(phoney);
                        mBinding.userZonePhoneHintLayout.setAlpha(0f);
                        phoneHintAnim.start();

                        mPhoneHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                phoneWarnAnim.start();

                            }
                        }, 170);
                    }
                });


        faqAnim = ViewAnimator.animate(userZoneFAQLayout)
                .rotation(FAQ_ANGLE_START, FAQ_ANGLE_END)
                .pivotY(faqR)
                .pivotX(faqLeft)
                .duration(FAQ_TIME)
                .andAnimate(userZoneFAQ)
                .alpha(0, 1)
                .rotation(FAQ_ANGLE_START * -1, FAQ_ANGLE_END * -1)
                .duration(FAQ_TIME)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        //        初始化faq hint
                        if (!UserZoneFragment.this.isVisible())
                            return;
                        int[] location = new int[2];
                        userZoneFAQLayout.getLocationOnScreen(location);
                        int faqx = location[0];//获取当前位置的横坐标
                        int faqY = location[1];//获取当前位置的纵坐标
//                        faqx = faqx - faqWidth * 4 / 5 - userZoneFaqHintLayout.getWidth();
                        faqx = faqx - mBinding.userZoneFaqHintLayout.getWidth() * 4 / 5;
                        faqY = faqY - mStatusBarHeight;
                        mBinding.userZoneFaqHintLayout.setX(faqx);
                        mBinding.userZoneFaqHintLayout.setY(faqY);
                        mBinding.userZoneFaqHintLayout.setAlpha(0f);

                        faqHintAnim.start();
                    }
                });

        deviceAnim = ViewAnimator.animate(userZoneDeviceLayout)
                .rotation(DEVICE_ANGLE_START, DEVICE_ANGLE_END)
                .pivotY(deviceR)
                .pivotX(deviceLeft)
                .duration(DEVICE_TIME)
                .andAnimate(userZoneDeviceInnerLayout)
                .alpha(0, 1)
                .rotation(DEVICE_ANGLE_START * -1, DEVICE_ANGLE_END * -1)
                .duration(DEVICE_TIME)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        if (!UserZoneFragment.this.isVisible())
                            return;
                        //        初始化device hint
                        int[] location = new int[2];
                        userZoneDeviceLayout.getLocationOnScreen(location);
                        int devicex = location[0];//获取当前位置的横坐标
                        int devicey = location[1];//获取当前位置的纵坐标
                        devicex = devicex - mBinding.userZoneDeviceHintLayout.getWidth() + deviceWidth / 4;
                        devicey = devicey - mBinding.userZoneDeviceHintLayout.getHeight() * 6 / 5 - mStatusBarHeight;
//                        if (devicex < 10) {
//                            userZoneDeviceHint.setWidth(userZoneDeviceHint.getWidth() + devicex - 10);
//                            userZoneDeviceHintLayout.getLayoutParams().width = userZoneDeviceHintLayout.getWidth() + devicex - 10;
//                            devicex = 10;
//                        }
                        mBinding.userZoneDeviceHintLayout.setX(devicex);
                        mBinding.userZoneDeviceHintLayout.setY(devicey);
                        mBinding.userZoneDeviceHintLayout.setAlpha(0f);
                        deviceHintAnim.start();
                        mDeviceHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                deviceWarnAnim.start();
                            }
                        }, 170);
                    }
                });
        passwordAnim = ViewAnimator.animate(userZonePasswordLayout)
                .rotation(PASSWORD_ANGLE_START, PASSWORD_ANGLE_END)
                .pivotY(passwordR)
                .pivotX(passwordLeft)
                .duration(PASSWORD_TIME)
                .andAnimate(userZonePasswordInnerLayout)
                .alpha(0, 1)
                .rotation(PASSWORD_ANGLE_START * -1, PASSWORD_ANGLE_END * -1)
                .duration(PASSWORD_TIME)
                .onStop(new AnimationListener.Stop() {
                    @Override
                    public void onStop() {
                        //        初始化password hint
                        if (!UserZoneFragment.this.isVisible())
                            return;
                        int[] location = new int[2];
                        userZonePasswordInnerLayout.getLocationOnScreen(location);
                        int passwordx = location[0];//获取当前位置的横坐标
                        int passwordy = location[1];//获取当前位置的纵坐标

                        passwordx = passwordx - mBinding.userZonePasswordHintLayout.getWidth() * 4 / 5;
                        passwordy = passwordy + passwordWidth - mStatusBarHeight;

                        mBinding.userZonePasswordHintLayout.setX(passwordx);
                        mBinding.userZonePasswordHintLayout.setY(passwordy);
                        mBinding.userZonePasswordHintLayout.setAlpha(0f);
                        passwordHintAnim.start();

//                        mPasswordHandler.postDelayed(new Runnable() {
//                            @Override
//                            public void run() {
//                                passwordWarnAnim.start();
//
//                            }
//                        }, 170);
                    }
                });

        backAnim = ViewAnimator.animate(mBinding.commonBarBack)
                .alpha(0, 1f)
                .duration(BACK_TIME);

        hideAnim();
    }

    private void hideAnim() {
        if (emailHandler != null) {
            emailHandler.removeCallbacksAndMessages(null);
            phoneHandler.removeCallbacksAndMessages(null);
            deviceHandler.removeCallbacksAndMessages(null);
            pwdHandler.removeCallbacksAndMessages(null);
        }
        if (mBinding.userZoneAvatar != null) {
//            userZoneEmail.setAlpha(0f);
            userZoneFAQ.setAlpha(0f);
//            userZonePhone.setAlpha(0f);
            mBinding.userZoneAvatar.setAlpha(0f);
            mBinding.userZoneInnerCircle.setAlpha(0f);
            mBinding.userZoneMiddleCircle.setAlpha(0f);
            mBinding.userZoneOutCircle.setAlpha(0f);
//            userZoneDevice.setAlpha(0f);
//            userZonePassword.setAlpha(0f);
            mBinding.userZoneName.setAlpha(0f);
            mBinding.userZonePhoneHintLayout.setAlpha(0f);
            mBinding.userZoneDeviceHintLayout.setAlpha(0f);
            mBinding.userZoneEmailHintLayout.setAlpha(0f);
            mBinding.userZonePasswordHintLayout.setAlpha(0f);
            mBinding.userZoneFaqHintLayout.setAlpha(0f);
//            userZoneEmailWarn.setAlpha(0f);
            userZoneEmailInnerLayout.setAlpha(0f);
            userZoneDeviceInnerLayout.setAlpha(0f);
            userZonePasswordInnerLayout.setAlpha(0f);
            userZonePhoneInnerLayout.setAlpha(0f);
            mBinding.userZoneFaqHintLayout.setAlpha(0f);

//            userZoneDeviceLayout.setAlpha(0f);
//            userZoneFAQLayout.setAlpha(0f);
//            userZonePhoneLayout.setAlpha(0f);
        }
    }

    private Handler emailHandler, phoneHandler, deviceHandler, faqHandler, pwdHandler;

    private void startAnim() {
        hideAnim();

        startCircleAnim();

        emailHandler = new Handler();
        emailHandler.postDelayed(new Runnable() {
            public void run() {
                emailAnim.start();
            }
        }, EMAIL_DELAY);

        phoneHandler = new Handler();
        if (AppConfig.Functions.SUPPORT_PHONE_FUNCTION) {
            phoneHandler.postDelayed(new Runnable() {
                public void run() {
                    phoneAnim.start();
                }
            }, PHONE_DELAY);
        }


        deviceHandler = new Handler();
        deviceHandler.postDelayed(new Runnable() {
            public void run() {
                i("start deviceanim");
                deviceAnim.start();
            }
        }, DEVICE_DELAY);

//        TODO faq 9月份版本才会有
        pwdHandler = new Handler();
        pwdHandler.postDelayed(new Runnable() {
            public void run() {
                passwordAnim.start();
            }
        }, PASSWORD_DELAY);

//        faqHandler = new Handler();
//        faqHandler.postDelayed(new Runnable() {
//            public void run() {
//                faqAnim.start();
//            }
//        }, FAQ_DELAY);
    }

    private void startCircleAnim() {
        avatarAnim.start();

        new Handler().postDelayed(new Runnable() {
            public void run() {
                innerCircleAnim.start();
            }
        }, INNER_CIRCLE_DELAY_TIME);

        new Handler().postDelayed(new Runnable() {
            public void run() {
                middleCircleAnim.start();
                middleCircleRotateAnim.start();
            }
        }, MIDDLE_CIRCLE_DELAY_TIME);

        new Handler().postDelayed(new Runnable() {
            public void run() {
                outCircleAnim.start();
                outCircleRotateAnim.start();
            }
        }, OUT_CIRCLE_DELAY_TIME);
    }

    /**
     * Set a hint to the system about whether this fragment's UI is currently visible
     * to the user. This hint defaults to true and is persistent across fragment instance
     * state save and restore.
     * <p/>
     * <p>An app may set this to false to indicate that the fragment's UI is
     * scrolled out of visibility or is otherwise not directly visible to the user.
     * This may be used by the system to prioritize operations such as fragment lifecycle updates
     * or loader ordering behavior.</p>
     *
     * @param isVisibleToUser true if this fragment's UI is currently visible to the user (default),
     *                        false if it is not.
     */
    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            updateUserName();
            updateUserAvatar();
            startAnim();
            if (mBinding.commonBarBack != null) {
                backAnim.start();
            }

//            不需要请求了
//            if (DinSDK.getUserInstance().isLogin() &&
//                    (TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getEmail())
//                            || TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getPhone()))) {
//                DinsafeAPI.getApi().getUserDataCall()
//                        .enqueue(new Callback<LoginResponse>() {
//                            @Override
//                            public void onResponse(Call<LoginResponse> call, Response<LoginResponse> response) {
//                                LoginResponse loginResponse = response.body();
//                                if (loginResponse.getStatus() == 1) {
//                                    if (!TextUtils.isEmpty(loginResponse.getResult().getMail())) {
//                                        CommonDataUtil.getInstance().getUser().getResult().setMail(loginResponse.getResult().getMail());
//                                    } else {
//                                        CommonDataUtil.getInstance().getUser().getResult().setMail("");
//                                    }
//
//                                    if (!TextUtils.isEmpty(loginResponse.getResult().getPhone())) {
//                                        CommonDataUtil.getInstance().getUser().getResult().setPhone(loginResponse.getResult().getPhone());
//                                    } else {
//                                        CommonDataUtil.getInstance().getUser().getResult().setPhone("");
//                                    }
//                                    updateUserName();
//                                }
//                            }
//
//                            @Override
//                            public void onFailure(Call<LoginResponse> call, Throwable t) {
//
//                            }
//                        });
//            }
//        } else {
//            hideAnim();
        }
    }


    /**
     * 这里必须返回null，否则会影响到正常的viewpageer滑动动画
     *
     * @param transit
     * @param enter
     * @param nextAnim
     * @return
     */
    @Override
    public Animation onCreateAnimation(int transit, boolean enter, int nextAnim) {
        return null;
    }

    @Override
    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

    }

    public void toChangeUserName() {
        getDelegateActivity().addCommonFragment(ChangeUidFragment.newInstance());
    }

    @Override
    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
        if (DDSystemUtil.isMarshmallow() && PermissionUtil.isStoragePermissionDeny(getActivity())) {
            requestReadImagePermission();
            return;
        }
        if (index == 0) {
            getPhotoClick(REQUEST_CODE_PICK_IMAGE_FROM_AVATAR);
        } else if (index == 1) {
            if (DDSystemUtil.isMarshmallow() && ContextCompat.checkSelfPermission(getActivity(),
                    Manifest.permission.CAMERA)
                    != PackageManager.PERMISSION_GRANTED) {
                requestCameraPermission();
                return;
            }

            getImageFromCamera(REQUEST_CODE_CAPTURE_CAMEIA_FROM_AVATAR);
        }
    }

    /**
     * Receive the result from a previous call to
     * {@link #startActivityForResult(Intent, int)}.  This follows the
     * related Activity API as described there in
     *
     * @param requestCode The integer request code originally supplied to
     *                    startActivityForResult(), allowing you to identify who this
     *                    result came from.
     * @param resultCode  The integer result code returned by the child activity
     *                    through its setStatus().
     * @param data        An Intent, which can return result data to the caller
     */
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        getMainActivity().setNotNeedToLogin(true);
        if (requestCode == REQUEST_CODE_CAPTURE_CAMEIA_FROM_AVATAR) {
            try {
//                if (Build.VERSION_CODES.Q <= Build.VERSION.SDK_INT) {
//                    // Androidx,需要通过URI的方式读取图片
//                    Bitmap image = DDImageUtil.decodeUri(getContext(), mPhotoUri);
//                    if (image == null) {
//                        return;
//                    }
//                    startPhotoZoom(image);
//                } else {
                // Androidx之前，使用File的方式读取图片
                File file = new File(capturePath);
                if (!file.exists()) {
                    return;
                }
                startPhotoZoom(file);
//                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        } else if (requestCode == REQUEST_CROP_IMAGE) {
            if (resultCode != Activity.RESULT_OK) {
                return;
            }
//            uploadImage();
        } else if (requestCode == REQUEST_CODE_PICK_IMAGE_FROM_AVATAR && data != null) {
            Uri uri = data.getData();
            Bitmap image;
            if (Build.VERSION_CODES.Q <= Build.VERSION.SDK_INT) {
                image = DDImageUtil.decodeUri(getContext(), uri);
            } else {
                image = DDImageUtil.decodeFile(new File(DDImageUtil.getRealPathFromURI(uri, getContext())));
            }

            if (image == null) {
                showToast("您选择的图片已经被删除或者是图片不存在");
                return;
            }
            startPhotoZoom(image);
        }
    }

    private String uploadImage(Bitmap image) {
//        Bitmap image = null;
        showTimeOutLoadinFramgmentWithErrorAlert();
        try {
//                image = MediaStore.Images.Media.getBitmap(getActivity().getContentResolver(), fileUri);
//                压缩图片
            image = DDImageUtil.compressImage(image);
        } catch (Exception e) {
            e.printStackTrace();
            closeTimeOutLoadinFramgmentWithErrorAlert();
        }
        if (image != null) {
            DDFileUtil.deleteFile(capturePath);
            final String fileName = System.currentTimeMillis() + "";
            DDImageUtil.saveBitmap(image, DDGlobalEnv.getInstance().getImageFolder() + fileName, Bitmap.CompressFormat.PNG, true);
//              上传头像
            DinSDK.getUserInstance().uploadUserAvatar(DDGlobalEnv.getInstance().getImageFolder() + fileName, new IResultCallback2<String>() {
                @Override
                public void onError(int i, String s) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    if (UserZoneFragment.this.isAdded())
                        showErrorToast();
                }

                @Override
                public void onSuccess(String s) {
                    mBinding.userZoneAvatar.setImageURI(Uri.parse(DDGlobalEnv.getInstance().getImageFolder() + fileName));
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                }
            });
        }
        return "";
    }

    public void startPhotoZoom(Bitmap image) {
        mBinding.userZoneCropLayout.setVisibility(View.VISIBLE);
        mBinding.userZoneCropimage.setGuidelines(2);
        if (image != null) {
            mBinding.userZoneCropimage.setImageBitmap(image);
        }

        mBinding.userZoneCropSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.uploading_hint));
                mBinding.userZoneCropSave.setEnabled(false);
                mBinding.userZoneCropLayout.setVisibility(View.GONE);
                Bitmap bitmap = mBinding.userZoneCropimage.getCroppedImage();
                if (bitmap != null) {
                    mUploadImage = bitmap;
                    uploadObservable.subscribe(new Subscriber<String>() {
                        @Override
                        public void onCompleted() {
                            mBinding.userZoneCropSave.setEnabled(true);
                        }

                        @Override
                        public void onError(Throwable e) {
                            mBinding.userZoneCropSave.setEnabled(true);
                        }

                        @Override
                        public void onNext(String v) {

                        }
                    });
//                    uploadImage(bitmap);

                }
            }
        });

        mBinding.userZoneCropCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mBinding.userZoneCropLayout.setVisibility(View.GONE);
            }
        });

        mBinding.userZoneCropRota.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mBinding.userZoneCropimage.rotateImage(90);
            }
        });
    }

    public void startPhotoZoom(File file) {
        mBinding.userZoneCropLayout.setVisibility(View.VISIBLE);
        mBinding.userZoneCropimage.setGuidelines(2);
//        Bitmap image = DDImageUtil.decodeFile(getActivity(), file.getAbsolutePath());
        Bitmap image = DDImageUtil.decodeFile(file);
        if (image != null) {
            mBinding.userZoneCropimage.setImageBitmap(image);
        }

        mBinding.userZoneCropSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showLoadingFragment(LoadingFragment.BLACK, getResources().getString(R.string.uploading_hint));
                mBinding.userZoneCropSave.setEnabled(false);
                mBinding.userZoneCropLayout.setVisibility(View.GONE);
                Bitmap bitmap = mBinding.userZoneCropimage.getCroppedImage();
                if (bitmap != null) {
                    mUploadImage = bitmap;
                    uploadObservable.subscribe(new Subscriber<String>() {
                        @Override
                        public void onCompleted() {
                            mBinding.userZoneCropSave.setEnabled(true);
                        }

                        @Override
                        public void onError(Throwable e) {
                            mBinding.userZoneCropSave.setEnabled(true);
                        }

                        @Override
                        public void onNext(String v) {

                        }

                    });

                }
            }
        });

        mBinding.userZoneCropCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mBinding.userZoneCropLayout.setVisibility(View.GONE);
            }
        });

        mBinding.userZoneCropRota.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mBinding.userZoneCropimage.rotateImage(90);
            }
        });
    }

    protected void getImageFromCamera(int requestCode) {
        getMainActivity().setNotNeedToLogin(true);
        String state = Environment.getExternalStorageState();
        if (state.equals(Environment.MEDIA_MOUNTED)) {
            Intent getImageByCamera = new Intent("android.media.action.IMAGE_CAPTURE");
            String out_file_path = DDGlobalEnv.getInstance().getImageFolder();
            File dir = new File(out_file_path);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            capturePath = out_file_path + System.currentTimeMillis() + ".jpg";
            File cameraPhoto = new File(capturePath);
//            if (Build.VERSION_CODES.Q <= Build.VERSION.SDK_INT) {
//                // Androidx，拍照图片需要保存在公共库中
//                mPhotoUri = createImageUri();
//            } else
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                //使用FileProvider解决file:// URI引起的FileUriExposedException
                //6.0以上就需要这么做。
                mPhotoUri = FileProvider.getUriForFile(
                        getContext(),
                        getContext().getPackageName() + ".fileProvider",
                        cameraPhoto);
            } else {
                mPhotoUri = Uri.fromFile(cameraPhoto);
            }
            getImageByCamera.putExtra(MediaStore.EXTRA_OUTPUT, mPhotoUri);
            getImageByCamera.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 1);
            startActivityForResult(getImageByCamera, requestCode);
        } else {
        }
    }

    /**
     * 创建图片地址uri,用于保存拍照后的照片 Android 10以后使用这种方法
     */
    private Uri createImageUri() {
        String status = Environment.getExternalStorageState();
        ContentValues contentValues = new ContentValues();
        String fileName = System.currentTimeMillis() + ".jpg";
        contentValues.put(MediaStore.Images.Media.DISPLAY_NAME, fileName);
        // 判断是否有SD卡,优先使用SD卡存储,当没有SD卡时使用手机存储
        if (status.equals(Environment.MEDIA_MOUNTED)) {
            return getContext().getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
        } else {
            return getContext().getContentResolver().insert(MediaStore.Images.Media.INTERNAL_CONTENT_URI, contentValues);
        }
    }

    public void getPhotoClick(int requestCode) {
        getMainActivity().setNotNeedToLogin(true);
        Intent mAddIntent = new Intent(Intent.ACTION_PICK,
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(mAddIntent, requestCode);
    }

    public void updateUserAvatar() {
        if (DinSDK.getUserInstance().isLogin()
                && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getAvatar())
                && mBinding.userZoneAvatar != null) {
            ImageLoader.getInstance().displayImage(APIKey.UPLOAD_SERVER_IP +
                    DinSDK.getUserInstance().getUser().getAvatar(), mBinding.userZoneAvatar);
        } else {
            if (mBinding.userZoneAvatar != null) {
                mBinding.userZoneAvatar.setImageResource(R.drawable.btn_userpage_add);
            }
        }
    }


    @Subscribe
    public void onEventMainThread(UserUidChangeEvent event) {
        updateUserName();
        toUpdataBadge();
        if (CommonDataUtil.getInstance().isBind()) {
            startAnim();
        }
    }

    @Subscribe
    public void onEventMainThread(GetDeviceInfoEvent event) {
        if (!event.isSuccess()) {
            return;
        }
        toUpdataBadge();
    }

    @Subscribe
    public void onEventMainThread(BindPhoneSuccessEvent event) {
        userZonePhone.setImageResource(R.drawable.btn_userpage_phone_sel);
        userZonePhoneWarn.setVisibility(View.INVISIBLE);
    }

    @Subscribe
    public void onEventMainThread(BindEmailSuccessEvent event) {
        userZoneEmail.setImageResource(R.drawable.btn_userpage_email_sel);
        userZoneEmailWarn.setVisibility(View.INVISIBLE);
    }

    @Subscribe
    public void onEventMainThread(UnBindPhone event) {
        userZonePhone.setImageResource(R.drawable.btn_userpage_phone_nor);
        userZonePhoneWarn.setVisibility(View.VISIBLE);
    }

    @Subscribe
    public void onEventMainThread(UnBindEmail event) {
        userZoneEmail.setImageResource(R.drawable.btn_userpage_email_nor);
        userZoneEmailWarn.setVisibility(View.VISIBLE);
    }

    private Call<StringResponseEntry> logoutCall;

    public void toLogout() {
        BmtManager.getInstance().stopPolling();
        EventBus.getDefault().post(new HadLogoutPreEvent());
        CommonDataUtil.getInstance().unSetAlias();
        showLoadingFragment(LoadingFragment.BLACK);
        clearBannerIgnoreCache();

        if (null != UserManager.getInstance().getUser()) {
            mBinding.userZoneLogoutText.setEnabled(false);
            DinSDK.getUserInstance().logout(new ILogoutCallback() {
                @Override
                public void onSuccess() {
                    getMainActivity().toCloseWs(true);
                    CommonDataUtil.getInstance().clearDB();
                    LoginActivity.start(getActivity(), false);
                    getActivity().finish();
                    EventBus.getDefault().post(new HadLogoutEvent());
                }
            });
        } else {
            getMainActivity().toCloseWs(true);
            CommonDataUtil.getInstance().clearDB();
            LoginActivity.start(getActivity(), false);
            getActivity().finish();
            EventBus.getDefault().post(new HadLogoutEvent());
        }
    }

    private void clearBannerIgnoreCache() {
        String userId = null;
        if (null != UserManager.getInstance().getUser())
            userId = UserManager.getInstance().getUser().getUser_id();
        String key = null;
        if (!TextUtils.isEmpty(userId)) {
            key = DBKey.KEY_IGNORE_TASK_ID + userId;
        }
        if (!TextUtils.isEmpty(userId) && DBUtil.Exists(key)) {
            DBUtil.Delete(key);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        updateUI();
    }

}
