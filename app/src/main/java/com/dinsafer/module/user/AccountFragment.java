package com.dinsafer.module.user;

import android.os.Bundle;
import androidx.viewpager.widget.ViewPager;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAccountBinding;
import com.dinsafer.model.event.LoginFromSignUpEvent;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.BaseScrollTittleFragment;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.adapter.CommonNonSaveStatePagerAdapter;
import com.dinsafer.module.main.adapter.CommonPagerAdapter;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.StringStyle;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

/**
 * 登录和注册入口主页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/6 21:17
 */
public class AccountFragment extends MyBaseFragment<FragmentAccountBinding> {
    private static final int OPERATION_TYPE_LOGIN = 0;
    private static final int OPERATION_TYPE_SIGN_UP = 1;

    private CommonPagerAdapter mAdapter;
    private ArrayList<BaseFragment> mFragments;

    private int operationType = OPERATION_TYPE_LOGIN;

    public static AccountFragment newInstance() {
        return new AccountFragment();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        //getMainActivity().setHideKeyboardOnTouchBlank(true);
        mFragments = new ArrayList<>();
        mFragments.add(LoginFragment.newInstance());
        mFragments.add(SignUpFragment.newInstance());

        mAdapter = new CommonNonSaveStatePagerAdapter(getChildFragmentManager(), mFragments);
        mBinding.vpAccount.setCanSlide(true);
        mBinding.vpAccount.setAdapter(mAdapter);
        mBinding.vpAccount.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                final int currentOperateType = i;
                operationType = currentOperateType;
                updateBottomHint();
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });

        //设置点击后的颜色为透明，否则会一直出现高亮
        mBinding.tvBottomHint.setHighlightColor(getResources().getColor(R.color.text_blue_1));
        mBinding.tvBottomHint.setMovementMethod(LinkMovementMethod.getInstance());
        updateBottomHint();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        //getMainActivity().checkAppUpgrade();
    }

    private void updateBottomHint() {
        CharSequence hint;
        if (OPERATION_TYPE_LOGIN == operationType) {
            hint = getSignUpSpannable();
        } else {
            hint = getGoLoginSpannable();
        }
        mBinding.tvBottomHint.setText(hint);
    }

    private void changeOperateType(int operationType) {
        if (OPERATION_TYPE_LOGIN != operationType && OPERATION_TYPE_SIGN_UP != operationType) {
            DDLog.e(TAG, "不支持的操作类型: " + operationType);
            return;
        }

        if (this.operationType == operationType) {
            return;
        }

        final int index = operationType;
        mBinding.vpAccount.setCurrentItem(index, true);
        if (index == 1) {
            ((SignUpFragment) mAdapter.getItem(index)).refreshUiI();
        }
        this.operationType = operationType;
    }

    private CharSequence getGoLoginSpannable() {
        return createSpannable(
                DinSaferApplication.getAppContext().getResources().getString(R.string.already_have_account),
                DinSaferApplication.getAppContext().getString(R.string.login),
                new ClickableSpan() {
                    @Override
                    public void updateDrawState(TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(DinSaferApplication.getAppContext().getResources().getColor(R.color.color_brand_text));       //设置文字颜色
                        ds.setUnderlineText(false);      //设置下划线
                    }

                    @Override
                    public void onClick(View widget) {
                        changeOperateType(OPERATION_TYPE_LOGIN);
                    }
                }
        );
    }

    private CharSequence getSignUpSpannable() {
        return createSpannable(
                DinSaferApplication.getAppContext().getResources().getString(R.string.first_time_user),
                DinSaferApplication.getAppContext().getString(R.string.sign_up),
                new ClickableSpan() {
                    @Override
                    public void updateDrawState(TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(DinSaferApplication.getAppContext().getResources().getColor(R.color.color_brand_text));       //设置文字颜色
                        ds.setUnderlineText(false);      //设置下划线
                    }

                    @Override
                    public void onClick(View widget) {
                        changeOperateType(OPERATION_TYPE_SIGN_UP);
                    }
                }
        );
    }

    private CharSequence createSpannable(final String text, final String clickableText, final ClickableSpan clickableSpan) {
        final CharSequence hint2 = StringStyle.format(DinSaferApplication.getAppContext(),
                Local.s(clickableText), R.style.accountClickableTextStyle);
        SpannableString spStr = new SpannableString(hint2);
        spStr.setSpan(clickableSpan, 0, hint2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return new SpannableStringBuilder()
                .append(StringStyle.format(DinSaferApplication.getAppContext(), Local.s(text), R.style.accountNormalTextStyle))
                .append(" ")
                .append(spStr);
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_account;
    }

    @Override
    public boolean onBackPressed() {
        return true;
    }

    @Override
    public void onDestroyView() {
        //getMainActivity().setHideKeyboardOnTouchBlank(false);
        EventBus.getDefault().unregister(this);
        super.onDestroyView();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(LoginFromSignUpEvent event) {
        changeOperateType(OPERATION_TYPE_LOGIN);
    }

    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        if (operationType == OPERATION_TYPE_SIGN_UP) {
            ((SignUpFragment) mAdapter.getItem(1)).onEnterFragment();
        }
    }
}
