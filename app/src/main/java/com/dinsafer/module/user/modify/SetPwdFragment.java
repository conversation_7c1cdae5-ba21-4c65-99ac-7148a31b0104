package com.dinsafer.module.user.modify;

import static com.dinsafer.util.RegxUtil.USER_PWD_MAX_LEN;
import static com.dinsafer.util.RegxUtil.USER_PWD_MIN_LEN;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.View;

import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.event.HadLoginEvent;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * 修改或设置用户密码
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/12 12:28
 */
public class SetPwdFragment extends SimpleInputFragment {
    private static final String KEY_MODE = "mode";
    private static final boolean DEFAULT_MODE_CHANGE = false; // 默认为设置密码模式

    // true修改密码，false 设置密码
    private boolean modeChange = DEFAULT_MODE_CHANGE;

    public static SetPwdFragment newInstanceForChange() {
        return newInstance(true);
    }

    public static SetPwdFragment newInstanceForSet() {
        return newInstance(false);
    }

    private static SetPwdFragment newInstance(boolean modeChange) {
        final SetPwdFragment fragment = new SetPwdFragment();
        final Bundle args = new Bundle();
        args.putBoolean(KEY_MODE, modeChange);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        initParams();
        super.initView(inflateView, savedInstanceState);
    }

    @Override
    public void initData() {
        super.initData();
        setTopHint(getString(R.string.user_pwd_condition));
        final String inputHint = modeChange ? getString(R.string.change_password_new_hint)
                : getString(R.string.login_pass_hint);
        setPasswordInputHint(inputHint,
                getResources().getString(R.string.Confirm),
                USER_PWD_MAX_LEN);
    }

    private void initParams() {
        modeChange = getArguments().getBoolean(KEY_MODE, DEFAULT_MODE_CHANGE);
    }

    @Override
    protected String provideTittle() {
        return modeChange ? getString(R.string.change_password_title)
                : getString(R.string.set_password_tittle);
    }

    @Override
    protected void onNextClick(@Nullable String inputContent) {
        if (TextUtils.isEmpty(inputContent)
                || USER_PWD_MIN_LEN > inputContent.length()
                || USER_PWD_MAX_LEN < inputContent.length()
                || !RegxUtil.isLetterAndNum(inputContent)) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.user_pwd_condition));
            setNextBtnEnable(true);
            return;
        }

        if (null == DinSDK.getUserInstance().getUser()
                || TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUser_id())) {
            showErrorToast();
            setNextBtnEnable(true);
            return;
        }

        if (null != DinSDK.getUserInstance().getUser()
                && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUid())
                && inputContent.equals(DinSDK.getUserInstance().getUser().getUid())) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.username_contains_password));
            setNextBtnEnable(true);
            return;
        }

        requestSetPassword(DinSDK.getUserInstance().getUser().getUser_id(), inputContent);
    }

    private void requestSetPassword(@NonNull final String uid, @NonNull final String pwd) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setNextBtnEnable(true);
        });

        DinSDK.getUserInstance().changePasswordOnly(uid, pwd, new IResultCallback2<Boolean>() {
            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "Error, i: " + i + ", s: " + s);
                closeLoadingFragmentWithCallBack();
                if (!SetPwdFragment.this.isAdded()) {
                    return;
                }
                showErrorToast();
                setNextBtnEnable(true);
            }

            @Override
            public void onSuccess(Boolean success) {
                closeLoadingFragmentWithCallBack();
                if (!SetPwdFragment.this.isAdded()) {
                    return;
                }

                if (null != success && success) {
                    DBUtil.Put(DBKey.TOKEN, DinSDK.getUserInstance().getUser().getToken());

                    if (modeChange) {
                        if (DBUtil.contain(DBKey.USER_PASSWORD)) {
                            DBUtil.SPut(DBKey.USER_PASSWORD, pwd);
                        }
                        showSuccess();
                        getDelegateActivity().removeAllCommonFragment();
                    } else {
                        DBUtil.SPut(DBKey.USER_PASSWORD, pwd);
                        login(uid,pwd);
                    }
                } else {
                    showErrorToast();
                    setNextBtnEnable(true);
                }
            }
        });
    }

    private void login(String uid, String pwd){
        UserManager.getInstance().loginWithUUid(uid, pwd, new ILoginCallback() {
            @Override
            public void onSuccess(DinUser dinUser) {
                toMain();
            }

            @Override
            public void onError(int i, String s) {
                toMain();
            }
        });
    }

    private void toMain() {
        CommonDataUtil.getInstance().setAlias(DinSDK.getUserInstance().getUser().getUser_id());
        // 登录成功后直接跳转首页
        MainActivity.start(getContext());
        PermissionDialogUtil.checkNotificationPermission();
        EventBus.getDefault().post(new HadLoginEvent());
        getActivity().finish();

    }
}
