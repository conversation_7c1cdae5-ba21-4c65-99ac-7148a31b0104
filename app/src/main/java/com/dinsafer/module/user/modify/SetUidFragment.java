package com.dinsafer.module.user.modify;

import static com.dinsafer.util.RegxUtil.USER_UID_MAX_LEN;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.text.method.TextKeyListener;
import android.view.View;

import com.dinsafer.config.DBKey;
import com.dinsafer.config.ErrorCode;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.UserUidChangeEvent;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/12 15:45
 */
public class SetUidFragment extends SimpleInputFragment {
    private static final String KEY_MODE = "mode";
    private static final boolean DEFAULT_MODE_CHANGE = false; // 默认为设置UID模式

    // true修改UID，false 设置UID
    private boolean modeChange = DEFAULT_MODE_CHANGE;
    private String defaultUid = "";

    public static SetUidFragment newInstanceForChange() {
        return newInstance(true);
    }

    public static SetUidFragment newInstanceForSet() {
        return newInstance(false);
    }

    private static SetUidFragment newInstance(boolean modeChange) {
        final SetUidFragment fragment = new SetUidFragment();
        final Bundle args = new Bundle();
        args.putBoolean(KEY_MODE, modeChange);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        initParams();
        if (!modeChange) {
            mBinding.commonTitleBar.commonBarBack.setVisibility(View.INVISIBLE);

        }
        super.initView(inflateView, savedInstanceState);
    }

    @Override
    protected boolean isNextEnableForInput(@Nullable String inputContent) {
        if (modeChange) {
            return super.isNextEnableForInput(inputContent);
        }
        // 注册用户设置UID,会使用默认ID，所以空的时候也是可以点击的
        return true;
    }

    @Override
    public void initData() {
        super.initData();
        if (!modeChange) {
            if (null != DinSDK.getUserInstance().getUser()
                    && !TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUser_id())) {
                defaultUid = DinSDK.getUserInstance().getUser().getUser_id();
                mBinding.etContent.setHint(defaultUid);
            }
        }
        setTopHint(getString(R.string.username_condition));
        final String inputHint = !TextUtils.isEmpty(defaultUid)
                ? defaultUid
                : Local.s(getString(R.string.username));
        setContentInputHint(inputHint,
                Local.s(getResources().getString(R.string.Next)),
                USER_UID_MAX_LEN, false);

        mBinding.etContent.setKeyListener(TextKeyListener.getInstance());
    }

    private void initParams() {
        modeChange = getArguments().getBoolean(KEY_MODE, DEFAULT_MODE_CHANGE);
    }

    @Override
    protected String provideTittle() {
        return modeChange ? getString(R.string.username)
                : getString(R.string.username);
    }

    @Override
    protected void onNextClick(@Nullable String inputContent) {
        final String currentUid;
        if (!modeChange && TextUtils.isEmpty(inputContent)) {
            currentUid = defaultUid;
        } else {
            currentUid = inputContent == null ? null : inputContent.trim();
        }

        if (TextUtils.isEmpty(currentUid) || !RegxUtil.isLegalName(currentUid)) {
            getDelegateActivity().showTopToast(R.drawable.icon_toast_fail,  Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
            setNextBtnEnable(true);
            return;
        }

        if (null == DinSDK.getUserInstance().getUser()
                || TextUtils.isEmpty(DinSDK.getUserInstance().getUser().getUser_id())) {
            showErrorToast();
            setNextBtnEnable(true);
            return;
        }

        requestSetUid(currentUid);
    }

    private void requestSetUid(@NonNull final String uid) {
        showTimeOutLoadinFramgmentWithCallBack(() -> {
            showErrorToast();
            setNextBtnEnable(true);
        });

        DinSDK.getUserInstance().changeUid(uid, new IResultCallback() {
            @Override
            public void onError(int i, String s) {
                closeLoadingFragmentWithCallBack();
                if (!SetUidFragment.this.isAdded()) {
                    return;
                }

                if (i == ErrorCode.SAME_USER_NAME) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.this_username_is_already_occupied));
                } else if (ErrorCode.SAME_PWD_WITH_USERNAME == i) {
                    getDelegateActivity().showTopToast(R.drawable.icon_toast_fail, getString(R.string.username_contains_password));
                } else {
                    showErrorToast();
                }
                setNextBtnEnable(true);
            }

            @Override
            public void onSuccess() {
                closeLoadingFragmentWithCallBack();
                if (!SetUidFragment.this.isAdded()) {
                    return;
                }

                if (modeChange) {
                    if (DBUtil.Exists(DBKey.REMEMBER_UID)) {
                        DBUtil.Put(DBKey.REMEMBER_UID, uid);
                    }
                    onChangeUidSuccess();
                } else {
                    DBUtil.Put(DBKey.REMEMBER_UID, uid);
                    onSetUidSuccess();
                }

                setNextBtnEnable(true);
            }
        });
    }

    private void onChangeUidSuccess() {
        EventBus.getDefault().post(new UserUidChangeEvent());
        showSuccess();
        getDelegateActivity().removeAllCommonFragment();
    }

    private void onSetUidSuccess() {
        showSuccess();
        getDelegateActivity().addCommonFragment(SetPwdFragment.newInstanceForSet());
    }

    @Override
    public boolean onBackPressed() {
        return !modeChange;
    }
}
