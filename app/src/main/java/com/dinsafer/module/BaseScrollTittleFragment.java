package com.dinsafer.module;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;

import androidx.annotation.CallSuper;
import androidx.annotation.LayoutRes;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBaseScrollTittleBinding;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/5/6 20:16
 */
public abstract class BaseScrollTittleFragment<V extends ViewDataBinding>
        extends MyBaseFragment<FragmentBaseScrollTittleBinding>
        implements ViewTreeObserver.OnGlobalLayoutListener {

    protected V mContentBinding;
    private boolean tittleExpanded = false;
    private int mTotalHeight = 0;
    private int mAppBarHeight = 0;

    @CallSuper
    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mContentBinding = DataBindingUtil.inflate(LayoutInflater.from(getContext()), provideLayoutId(),
                mBinding.flContainer, false);

        mBinding.flContainer.removeAllViews();
        mBinding.flContainer.addView(mContentBinding.getRoot());
        mBinding.getRoot().getViewTreeObserver().addOnGlobalLayoutListener(this);
        mBinding.appbarLayout.addOnOffsetChangedListener((appBarLayout, i) -> {
            if (mTotalHeight <= 0 || mAppBarHeight <= 0) {
                mTotalHeight = mBinding.getRoot().getHeight();
                mAppBarHeight = mBinding.appbarLayout.getHeight();
            }

            int height = mTotalHeight;
            int appbarHeight = mAppBarHeight;
            ViewGroup.LayoutParams layoutParams = mBinding.flContainer.getLayoutParams();
            layoutParams.height = height - appbarHeight - i;
            mBinding.flContainer.setLayoutParams(layoutParams);
        });
    }

    protected void setLocalTittle(final String localTittle) {
        mBinding.collapsingToolbarLayout.setTitle(null == localTittle ? "" : localTittle);
    }

    protected void setTittleExpanded(final boolean expanded) {
        mBinding.appbarLayout.setExpanded(expanded, true);
        tittleExpanded = expanded;
    }

    protected void toggle() {
        tittleExpanded = !tittleExpanded;
        mBinding.appbarLayout.setExpanded(tittleExpanded, true);
    }

    @Override
    protected final int provideContentViewLayoutId() {
        return R.layout.fragment_base_scroll_tittle;
    }

    @Override
    public void onGlobalLayout() {
        int height = mBinding.getRoot().getHeight();
        int appHeight = mBinding.appbarLayout.getHeight();
        mTotalHeight = height;
        mAppBarHeight = appHeight;

        ViewGroup.LayoutParams layoutParams = mBinding.flContainer.getLayoutParams();
        layoutParams.height = height - appHeight;
        mBinding.flContainer.setLayoutParams(layoutParams);
        if (mTotalHeight > 0) {
            mBinding.getRoot().getViewTreeObserver().removeOnGlobalLayoutListener(this);
        }
    }

    @LayoutRes
    protected abstract int provideLayoutId();
}
