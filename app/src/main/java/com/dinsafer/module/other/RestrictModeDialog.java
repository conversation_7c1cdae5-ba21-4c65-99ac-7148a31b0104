package com.dinsafer.module.other;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.gridlayout.widget.GridLayout;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;

import com.alibaba.fastjson.JSONObject;
import com.dinsafer.dinnet.R;
import com.dinsafer.dssupport.utils.HexUtil;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.viewanimator.AnimationListener;
import com.dinsafer.util.viewanimator.ViewAnimator;


/**
 * Description: 限制模式dialog
 * Author: MiraclesHed
 * Date: 2020-03-12
 */
public class RestrictModeDialog extends Dialog {

    private String TAG = "RestrictMode";

    private Context context;
    private LocalCustomButton btnOk;

    private GridLayout gridLayout;
    private LocalTextView tvTip;
    private View btnShowTip;

    private LocalTextView tvArm;
    private LocalTextView tvDisarm;
    private LocalTextView tvHomeArm;
    private LocalTextView tvSOS;

    private final int TAG_EXIT = 0;
    private final int TAG_OK = 1;

    private final int width = 720;

    private String uid;
    private String devicePhone;


    public RestrictModeDialog(@NonNull Context context, String uid, String devicePhone) {
        super(context, R.style.RestrictModeDialogStyle);
        this.uid = uid;
        this.devicePhone = devicePhone;
        initView(context);
    }

    private void initView(Context context) {
        this.context = context;
        View inflate = LayoutInflater.from(context).inflate(R.layout.dialog_restrict_mode, null);
        setContentView(inflate);

        btnOk = inflate.findViewById(R.id.btn_ok);
        gridLayout = inflate.findViewById(R.id.tbl);
        tvTip = inflate.findViewById(R.id.tv_tip);
        btnShowTip = inflate.findViewById(R.id.iv_show_tip);
        tvArm = inflate.findViewById(R.id.iv_arm);
        tvDisarm = inflate.findViewById(R.id.iv_disarm);
        tvHomeArm = inflate.findViewById(R.id.iv_homearm);
        tvSOS = inflate.findViewById(R.id.iv_sos);

        tvArm.setLocalText(context.getResources().getString(R.string.toolbar_arm_text));
        tvDisarm.setLocalText(context.getResources().getString(R.string.toolbar_disarm_text));
        tvHomeArm.setLocalText(context.getResources().getString(R.string.toolbar_homearm_text));
        tvSOS.setLocalText(context.getResources().getString(R.string.main_section_sos));
        tvTip.setLocalText(context.getResources().getString(R.string.restrict_model_tip));
        ((LocalTextView) inflate.findViewById(R.id.tv_title)).setLocalText(context.getResources().getString(R.string.restrict_model_title));

        btnShowTip.setOnClickListener(v -> {
            btnOk.setTag(TAG_OK);
            btnOk.setLocalText(context.getResources().getString(R.string.ok));
            ViewAnimator.animate(gridLayout)
                    .slideLeftOut(width)
                    .andAnimate(tvTip)
                    .slideRightIn(width)
                    .duration(400)
                    .onStart(new AnimationListener.Start() {
                        @Override
                        public void onStart() {
                            btnOk.setEnabled(false);
                            btnShowTip.setVisibility(View.GONE);
                            tvTip.setVisibility(View.VISIBLE);
                        }
                    })
                    .onStop(new AnimationListener.Stop() {
                        @Override
                        public void onStop() {
                            btnOk.setEnabled(true);
                            gridLayout.setVisibility(View.INVISIBLE);
                        }
                    })
                    .start();
        });

        btnOk.setTag(TAG_EXIT);
        btnOk.setLocalText(context.getResources().getString(R.string.restrict_model_exit));
        btnOk.setOnClickListener(v -> {
            if (((int) btnOk.getTag()) == TAG_EXIT) {
                dismiss();
            } else if (((int) btnOk.getTag()) == TAG_OK) {
                btnOk.setTag(TAG_EXIT);
                ViewAnimator.animate(tvTip)
                        .slideRightOut(width)
                        .andAnimate(gridLayout)
                        .slideLeftIn(width)
                        .duration(400)
                        .onStart(new AnimationListener.Start() {
                            @Override
                            public void onStart() {
                                btnOk.setEnabled(false);
                                gridLayout.setVisibility(View.VISIBLE);
                            }
                        })
                        .onStop(new AnimationListener.Stop() {
                            @Override
                            public void onStop() {
                                btnOk.setEnabled(true);
                                btnShowTip.setVisibility(View.VISIBLE);
                                tvTip.setVisibility(View.INVISIBLE);
                                btnOk.setLocalText(context.getResources().getString(R.string.restrict_model_exit));
                            }
                        })
                        .start();
            }
        });

        tvArm.setOnTouchListener(new MyOnTouchListener(tvArm));
        tvDisarm.setOnTouchListener(new MyOnTouchListener(tvDisarm));
        tvHomeArm.setOnTouchListener(new MyOnTouchListener(tvHomeArm));
        tvSOS.setOnTouchListener(new MyOnTouchListener(tvSOS));

        tvArm.setOnClickListener(v -> {
            goSystemSendSmsPage("ARM");
        });

        tvDisarm.setOnClickListener(v -> {
            goSystemSendSmsPage("DISARM");
        });

        tvHomeArm.setOnClickListener(v -> {
            goSystemSendSmsPage("HOMEARM");
        });

        tvSOS.setOnClickListener(v -> {
            goSystemSendSmsPage("SOS");
        });

    }

    //短信内容：{"userid": "jin001", "gmtime": 1584341556244140032, "cmd": "TASK_ARM"}
    //rc4后，base64
    private void goSystemSendSmsPage(String cmd) {
        long time = System.currentTimeMillis() * 1000000;
//        Log.d(TAG, "goSystemSendSmsPage: " + time);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userid", uid);
        jsonObject.put("cmd", "TASK_" + cmd);
        jsonObject.put("gmtime", time);
        String format = jsonObject.toJSONString();
//        Log.d(TAG, "goSystemSendSmsPage-->info: " + format);
        String sc = DDSecretUtil.getSC(format);
//        Log.d(TAG, "goSystemSendSmsPage-->sc: " + sc);
        String base = Base64.encodeToString(HexUtil.hexStringToBytes(sc), Base64.NO_WRAP);
//        Log.d(TAG, "goSystemSendSmsPage-->base:" + base);
        DDSystemUtil.goSystemSendSmsPage(context, devicePhone, base);
    }

    private class MyOnTouchListener implements View.OnTouchListener {
        private View view;

        public MyOnTouchListener(View view) {
            this.view = view;
        }

        @Override
        public boolean onTouch(View v, MotionEvent event) {

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    view.setAlpha(0.7f);
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    view.setAlpha(1.0f);
                    break;
            }
            return false;
        }
    }

}
