package com.dinsafer.module.other;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.spash.SpashActivity;
import com.dinsafer.util.DDJSONUtil;

import org.json.JSONException;
import org.json.JSONObject;


import static com.dinsafer.DNMessageReceiver.ALERT;
import static com.dinsafer.DNMessageReceiver.CATEGORY;
import static com.dinsafer.DNMessageReceiver.CMDTYPE;
import static com.dinsafer.DNMessageReceiver.CODE;
import static com.dinsafer.DNMessageReceiver.DEVICEID;
import static com.dinsafer.DNMessageReceiver.DEVICENAME;
import static com.dinsafer.DNMessageReceiver.PLUGINID;
import static com.dinsafer.DNMessageReceiver.PLUGIN_NAME;
import static com.dinsafer.DNMessageReceiver.SOUND;
import static com.dinsafer.DNMessageReceiver.SUBCATEGORY;


/**
 * 应用在后台收到FCM推送时，点击通知会打开该Activity。
 */
public class PushActivity extends Activity {

    private String TAG = getClass().getSimpleName();

    /**
     * Id
     **/
    private static final String KEY_MSGID = "msg_id";
    /** **/
    private static final String KEY_WHICH_PUSH_SDK = "rom_type";
    /** **/
    private static final String KEY_TITLE = "n_title";
    /** **/
    private static final String KEY_CONTENT = "n_content";
    /**          **/
    private static final String KEY_EXTRAS = "n_extras";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: ");
        setContentView(R.layout.splashscreen);
        handleOpenClick();
    }

    private void handleOpenClick() {
        String data = null;
        Intent intent = getIntent();
        Intent messageIntent = new Intent(this, SpashActivity.class);
        String msgId = "";
        byte whichPushSDK = 0;
        if (intent.getData() != null) {
            data = intent.getData().toString();
        }
        if (TextUtils.isEmpty(data) && intent.getExtras() != null) {
            data = intent.getExtras().getString("JMessageExtra");
        }
//        Log.w(TAG, "msg content is " + String.valueOf(data));

        if (!TextUtils.isEmpty(data)) {
            try {

                JSONObject jsonObject = DDJSONUtil.getJSONObject(new JSONObject(data), KEY_EXTRAS);
//                messageIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
                messageIntent.putExtra(DEVICEID, DDJSONUtil.getString(jsonObject, DEVICEID));
                messageIntent.putExtra(CMDTYPE, DDJSONUtil.getString(jsonObject, CMDTYPE));
                messageIntent.putExtra(SOUND, DDJSONUtil.getString(jsonObject, SOUND));
                messageIntent.putExtra(DEVICENAME, DDJSONUtil.getString(jsonObject, DEVICENAME));
                messageIntent.putExtra(CODE, DDJSONUtil.getInt(jsonObject, CODE));
                messageIntent.putExtra(ALERT, DDJSONUtil.getString(jsonObject, KEY_CONTENT));
                messageIntent.putExtra(PLUGIN_NAME, DDJSONUtil.getString(jsonObject, PLUGIN_NAME));
                messageIntent.putExtra(PLUGINID, DDJSONUtil.getString(jsonObject, PLUGINID));
                messageIntent.putExtra(CATEGORY, DDJSONUtil.getString(jsonObject, CATEGORY));
                messageIntent.putExtra(SUBCATEGORY, DDJSONUtil.getString(jsonObject, SUBCATEGORY));

                JSONObject object = new JSONObject(data);
                msgId = object.optString(KEY_MSGID);
                whichPushSDK = (byte) object.optInt(KEY_WHICH_PUSH_SDK);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        //上报点击事件
//        JPushInterface.reportNotificationOpened(this, msgId,
//                whichPushSDK);
        startActivity(messageIntent);
        finish();
    }

}