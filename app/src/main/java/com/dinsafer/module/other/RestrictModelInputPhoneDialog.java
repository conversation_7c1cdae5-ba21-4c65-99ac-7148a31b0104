package com.dinsafer.module.other;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.Local;


/**
 * 限制模式输入手机号对话框
 *
 * <AUTHOR>
 * @date 2020-04-10 12:45
 */
public class RestrictModelInputPhoneDialog extends Dialog {

    int layoutRes;
    Context mContext;
    LocalCustomButton mOk, mCancel;
    EditText mInput;
    LocalTextView mTitle;

    public RestrictModelInputPhoneDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.dialog_restrict_mode_input_phone;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);

        mOk = (LocalCustomButton) view.findViewById(R.id.dialog_ok);
        mCancel = (LocalCustomButton) view.findViewById(R.id.dialog_cancel);
        mTitle = (LocalTextView) view.findViewById(R.id.dialog_title);
        mInput = (EditText) view.findViewById(R.id.dialog_input);

        mOk.setLocalText(mContext.getResources().getString(R.string.Confirm));
        mCancel.setLocalText(mContext.getResources().getString(R.string.Cancel));
        mTitle.setLocalText(mContext.getResources()
                .getString(R.string.restrict_model_dialog_input_phone_title));

        if (!TextUtils.isEmpty(builder.currentPhone)) {
            mInput.setText(builder.currentPhone);
        }

        mCancel.setOnClickListener(v -> {
            dismiss();
            if (builder.clickCallback != null) {
                builder.clickCallback.onCancelClick(RestrictModelInputPhoneDialog.this);
            }
        });

        mOk.setOnClickListener(v -> {
            if (builder.isAutoDismiss) {
                dismiss();
            }

            if (builder.clickCallback != null) {
                builder.clickCallback.onOkClick(RestrictModelInputPhoneDialog.this,
                        mInput.getText().toString());
            }
        });
    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
//        super.cancel();
    }

    public interface AlertClickCallback {

        void onOkClick(RestrictModelInputPhoneDialog dialog, String phone);

        void onCancelClick(RestrictModelInputPhoneDialog dialog);
    }

    public static class Builder {
        private Context mContext;
        private String currentPhone;
        private boolean isAutoDismiss = true;
        private AlertClickCallback clickCallback;

        public Builder(Context context) {
            mContext = context;
        }

        public Builder setOKListener(AlertClickCallback listener) {
            this.clickCallback = listener;
            return this;
        }

        public Builder setCurrentPhone(String phone) {
            currentPhone = phone;
            return this;
        }

        public Builder setAutoDismiss(boolean dismiss) {
            isAutoDismiss = dismiss;
            return this;
        }

        public RestrictModelInputPhoneDialog preBuilder() {
            RestrictModelInputPhoneDialog alertDialog = new RestrictModelInputPhoneDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                    | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }
    }

}
