package com.dinsafer.module.iap;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentIpcCloudStorageServiceBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemChildClickListener;
import com.dinsafer.ui.rv.OnBindItemClickListener;
import com.dinsafer.util.DBUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CloudStorageServiceFragment extends MyBaseFragment<FragmentIpcCloudStorageServiceBinding> {

    private String deviceId;
    private BindMultiAdapter<ServiceCardItemModel> serviceCardAdapter = new BindMultiAdapter<>();
    private BindMultiAdapter<SmallBannerModel> bannerAdapter = new BindMultiAdapter<>();

    private boolean isShowMore = false;

    public static CloudStorageServiceFragment newInstance() {
        Bundle args = new Bundle();
        CloudStorageServiceFragment fragment = new CloudStorageServiceFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static CloudStorageServiceFragment newInstance(String deviceId) {
        Bundle args = new Bundle();
        args.putString("deviceId", deviceId);
        CloudStorageServiceFragment fragment = new CloudStorageServiceFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ipc_cloud_storage_service;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);

        mBinding.title.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.title.commonBarTitle.setLocalText(getString(R.string.iap_video_cloud_service));
        mBinding.title.commonBarRightIcon.setImageResource(R.drawable.icon_nav_list);
        mBinding.title.commonBarRightIcon.setOnClickListener(v -> getDelegateActivity().addCommonFragment(CloudStorageOrderListFragment.newInstance()));
        mBinding.title.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.rvServiceCard.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvServiceCard.setAdapter(serviceCardAdapter);
        serviceCardAdapter.openLoadAnimation();
        serviceCardAdapter.setOnBindItemChildClickListener(new OnBindItemChildClickListener<ServiceCardItemModel>() {
            @Override
            public void onItemChildClick(View view, int position, ServiceCardItemModel model) {
                if (view.getId() == R.id.btn_renew) {
                    deviceId = serviceCardAdapter.getData().get(position).getPid();
                    getDelegateActivity().addCommonFragment(CloudStorageServiceRenewFragment.newInstance(deviceId
                            , ServiceCardItemModel.SERVICE_TYPE_FAMILY == model.getService_type() ? model.getPid() : ""
                            , model.getService_type()
                            , false));
                } else if (view.getId() == R.id.ll_more) {
                    showMore();
                }
            }
        });
        mBinding.rvBanner.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvBanner.setAdapter(bannerAdapter);
        bannerAdapter.setOnBindItemClickListener((OnBindItemClickListener<SmallBannerModel>) (v, position, model) -> getDelegateActivity().addCommonFragment(BetaUserClubInvitationFragment.newInstance(model.getTaskId())));

        mBinding.tvFeatures.setLocalText(getString(R.string.features));
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        deviceId = getArguments().getString("deviceId");
        CloudStorageServiceHelper.getInstance().registerProductSchedulesListener(listener);

        initFunctionList();
        initServiceCardList();
        initTaskBannerList();
        if (!TextUtils.isEmpty(deviceId)) {
            //有设备id,重新获取排序的数据
            showBlueTimeOutLoadinFramgment();
            CloudStorageServiceHelper.getInstance().fetchProductSchedules(deviceId);
        } else {
            if (CloudStorageServiceHelper.getInstance().getProductSchedules() == null || CloudStorageServiceHelper.getInstance().getProductSchedules().size() == 0) {
                showBlueTimeOutLoadinFramgment();
                CloudStorageServiceHelper.getInstance().fetchProductSchedules("");
            }
        }
    }

    private void showMore() {
        if (isShowMore) {
            return;
        }
        isShowMore = true;
        serviceCardAdapter.notifyItemChanged(0);
        ArrayList<ServiceCardItemModel> data = new ArrayList<>();
        final List<ServiceCardItemModel> productSchedules = CloudStorageServiceHelper.getInstance().getProductSchedules(deviceId);
        if (null != productSchedules && productSchedules.size() > 1) {
            data.addAll(productSchedules.subList(1, productSchedules.size()));
        }
        serviceCardAdapter.addData(data);

        final List<ServiceCardItemModel> allProducts = serviceCardAdapter.getData();
        if (allProducts.size() > 0) {
            final boolean usingFamily = CloudStorageServiceHelper.isUsingFamilyServices(allProducts);
            for (ServiceCardItemModel productSchedule : allProducts) {
                productSchedule.setShowMore(false);
                productSchedule.setUsingFamilyService(usingFamily);
            }
            serviceCardAdapter.notifyDataSetChanged();
        }
    }

    private void initFunctionList() {
        BindMultiAdapter<BaseBindModel> funtionItemAdapter = new BindMultiAdapter<>();
        mBinding.rvFunction.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rvFunction.setAdapter(funtionItemAdapter);
        funtionItemAdapter.addData(new CloudStorageFunctionItem(R.drawable.icon_rights_cloud, R.string.iap_cloud_storage, R.string.iap_function_sub_title1));
        funtionItemAdapter.addData(new CloudStorageFunctionItem(R.drawable.icon_rights_picture, R.string.iap_function_title3, R.string.iap_function_sub_title3));
        funtionItemAdapter.addData(new CloudStorageFunctionItem(R.drawable.icon_rights_smoothplay, R.string.iap_function_title4, R.string.iap_function_sub_title4));
        funtionItemAdapter.addData(new CloudStorageFunctionItem(R.drawable.icon_rights_download, R.string.iap_function_title5, R.string.iap_function_sub_title5));
//        funtionItemAdapter.addData(new CloudStorageFunctionItem(R.drawable.icon_rights_sd, R.string.iap_function_title6, R.string.iap_function_sub_title6));
        funtionItemAdapter.addData(new CloudStorageFunctionItem(R.drawable.icon_rights_story, R.string.iap_function_title7, R.string.iap_function_sub_title7));
    }

    private void initServiceCardList() {
        final List<ServiceCardItemModel> productList = CloudStorageServiceHelper.getInstance().getProductSchedules();
        if (productList != null && productList.size() > 0) {
            if (isShowMore) {
                final List<ServiceCardItemModel> productSchedules = CloudStorageServiceHelper.getInstance().getProductSchedules(deviceId);
                if (productSchedules != null && productSchedules.size() > 0) {
                    final boolean usingFamily = CloudStorageServiceHelper.isUsingFamilyServices(productSchedules);
                    for (ServiceCardItemModel productSchedule : productSchedules) {
                        productSchedule.setShowMore(false);
                        productSchedule.setUsingFamilyService(usingFamily);
                    }
                }
                serviceCardAdapter.setNewData(productSchedules);
            } else {
                serviceCardAdapter.setNewData(null);
                final List<ServiceCardItemModel> productSchedules = CloudStorageServiceHelper.getInstance().getProductSchedules(deviceId);
                if (null != productSchedules && productSchedules.size() > 0) {
                    final ServiceCardItemModel serviceCardItemModel = productSchedules.get(0);
                    serviceCardItemModel.setUsingFamilyService(false);
                    serviceCardItemModel.setShowMore(productSchedules.size() > 1);
                    serviceCardAdapter.addData(serviceCardItemModel);
                }
            }
        }
    }

    private void initTaskBannerList() {
        DinsafeAPI.getApi().listSmallBanner().enqueue(new Callback<ListSmallBannerResponse>() {
            @Override
            public void onResponse(Call<ListSmallBannerResponse> call, Response<ListSmallBannerResponse> response) {

                if (response != null && response.body() != null && response.body().getStatus() == 1 && response.body().getResult() != null && response.body().getResult().size() > 0) {
                    List<SmallBannerModel> data = new ArrayList<>();
                    final Set<String> ignoreSet = new HashSet<>();
                    try {
                        final JSONArray ignoreList;
                        if (DBUtil.Exists(DBKey.KEY_IGNORE_TASK_ID + UserManager.getInstance().getUser().getUser_id())) {
                            ignoreList = new JSONArray(DBUtil.Str(DBKey.KEY_IGNORE_TASK_ID + UserManager.getInstance().getUser().getUser_id()));
                        } else {
                            ignoreList = new JSONArray();
                        }
                        if (ignoreList.length() > 0) {
                            for (int i = 0; i < ignoreList.length(); i++) {
                                String id = ignoreList.getString(i);
                                ignoreSet.add(id);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    for (SmallBannerModel smallBannerModel : response.body().getResult()) {
                        boolean ignored = false;
                        try {
                            ignored = ignoreSet.contains(smallBannerModel.getTask_id());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (!ignored) {
                            data.add(smallBannerModel);
                        }
                    }
                    bannerAdapter.setNewData(data);
                    mBinding.rvBanner.setVisibility(data.size() > 0 ? View.VISIBLE : View.GONE);
                } else {
                    bannerAdapter.setNewData(null);
                    mBinding.rvBanner.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFailure(Call<ListSmallBannerResponse> call, Throwable t) {
                bannerAdapter.setNewData(null);
                mBinding.rvBanner.setVisibility(View.GONE);
            }
        });
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        CloudStorageServiceHelper.getInstance().unregisterProductSchedulesListener(listener);
        listener = null;
        if (!TextUtils.isEmpty(deviceId)) {
            //退出时清除有设备id时获取的数据
            CloudStorageServiceHelper.getInstance().cleanProductSchedules();
        }
    }

    private CloudStorageServiceHelper.OnListProductSchedulesListener listener = new CloudStorageServiceHelper.OnListProductSchedulesListener() {
        @Override
        public void onUpdate(List<ServiceCardItemModel> data) {
            initServiceCardList();
            closeLoadingFragment();
        }
    };

    @Subscribe
    public void onBetaUserInviteSuccessEvent(BetaUserInviteSuccessEvent event) {
        initTaskBannerList();
    }


    @Subscribe
    public void onUpdateServiceCardEvent(UpdateServiceCardEvent event) {
        CloudStorageServiceHelper.getInstance().fetchProductSchedules(deviceId);
    }

}
