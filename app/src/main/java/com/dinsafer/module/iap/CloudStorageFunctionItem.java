package com.dinsafer.module.iap;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemCloudStorageFunctionBinding;
import com.dinsafer.ui.rv.BaseBindModel;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/8/18
 */
public class CloudStorageFunctionItem implements BaseBindModel<ItemCloudStorageFunctionBinding> {

    private int icon;
    private int title;
    private int subTitle;

    public CloudStorageFunctionItem(int icon, int title, int subTitle) {
        this.icon = icon;
        this.title = title;
        this.subTitle = subTitle;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_cloud_storage_function;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemCloudStorageFunctionBinding binding) {
        binding.ivIcon.setImageResource(icon);
        binding.tvTitle.setLocalText(binding.getRoot().getContext().getString(title));
        binding.tvSubTitle.setLocalText(binding.getRoot().getContext().getString(subTitle));
    }
}
