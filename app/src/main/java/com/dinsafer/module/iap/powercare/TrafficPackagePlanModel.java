package com.dinsafer.module.iap.powercare;

import android.content.Context;
import android.view.View;

import androidx.annotation.Keep;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemCloudStoragePlanBinding;
import com.dinsafer.dinnet.databinding.ItemTrafficPackagePlanBinding;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.util.Local;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/11/16
 */
@Keep
public class TrafficPackagePlanModel implements BaseBindModel<ItemTrafficPackagePlanBinding> {

    public static final int PRODUCT_TYPE_FAMILY = 0;
    public static final int PRODUCT_TYPE_SINGLE_CAMMERA = 1;
    public static final int PRODUCT_TYPE_SINGLE_VIDEO_DOORBELL = 2;
    public static final int PRODUCT_TYPE_TRAFFIC_PACKAGE = 4;

    private Integer days;
    private Double price;
    private String product_id;
    private Integer save;
    private String trans_currency;
    private int product_type;

    private boolean selected = false;

    @Override
    public int getLayoutID() {
        return R.layout.item_traffic_package_plan;
    }

    @Override
    public void onDo(View v) {

    }

    @Override
    public void convert(BaseViewHolder holder, ItemTrafficPackagePlanBinding binding) {
        Context context = binding.getRoot().getContext();
        //标题
        binding.tvTitle.setText(Local.s(context.getResources().getString(R.string.iap_plan_name)).replace("#plan", String.valueOf(getDays())));

        //当前价格
        binding.tvDiscountPrice.setText(String.format("%s %.2f", getTrans_currency(), getPrice()));

        //折扣
        if (null != getSave() && getSave() >= 1) {
            binding.tvDiscount.setVisibility(View.VISIBLE);
            binding.tvDiscount.setText(Local.s(context.getString(R.string.iap_save)).replace("#discount", String.format("%d%%", getSave())));
        } else {
            binding.tvDiscount.setVisibility(View.GONE);
        }

        if (selected) {
            binding.clRoot.setBackgroundResource(R.drawable.shape_item_cloud_storage_plan_selected);
        } else {
            binding.clRoot.setBackgroundResource(R.drawable.shape_item_monthly_plan_unselect);
        }

    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public Integer getSave() {
        return save;
    }

    public void setSave(Integer save) {
        this.save = save;
    }

    public String getTrans_currency() {
        return trans_currency;
    }

    public void setTrans_currency(String trans_currency) {
        this.trans_currency = trans_currency;
    }

    public int getProduct_type() {
        return product_type;
    }

    public void setProduct_type(int product_type) {
        this.product_type = product_type;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
}
