package com.dinsafer.module.spash;

import android.Manifest;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Window;
import android.widget.TextView;

import com.dinsafer.DNMessageReceiver;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.DinCore;
import com.dinsafer.dinnet.R;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.module.main.view.MainActivity;
import com.dinsafer.module.user.LoginActivity;
import com.dinsafer.push.PushUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.yanzhenjie.permission.Action;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import androidx.core.splashscreen.SplashScreen;
import androidx.fragment.app.FragmentActivity;

/**
 * Created by Rinfon on 16/9/14.
 */
public class SpashActivity extends FragmentActivity {
    private String TAG = getClass().getSimpleName();

    TextView appVersion;

    private final int SPLASH_DISPLAY_LENGTH = 1000;
    private boolean isNeedToLoadLanguage = false;

    Intent mainIntent;

    @Override
    public void onCreate(Bundle icicle) {
        super.onCreate(icicle);
        EventBus.getDefault().register(this);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            SplashScreen splashScreen = SplashScreen.installSplashScreen(this);
            splashScreen.setKeepOnScreenCondition(new SplashScreen.KeepOnScreenCondition() {
                @Override
                public boolean shouldKeepOnScreen() {
                    return true;
                }
            });
        }
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.splashscreen);
        __bindViews();
        appVersion.setText("v " + DDSystemUtil.getVersion(this));
        if (DinCore.getUserInstance().getUser() != null
                && !TextUtils.isEmpty(DinCore.getUserInstance().getUser().getUid())) {
            mainIntent = new Intent(SpashActivity.this, MainActivity.class);
        } else {
            mainIntent = new Intent(SpashActivity.this, LoginActivity.class);
        }
        if (getIntent() != null) {
            Log.d("Main", "spash: not null ");
            if (getIntent().getExtras() != null
                    && getIntent().getExtras().containsKey(DNMessageReceiver.CMDTYPE)) {
                mainIntent.putExtras(getIntent().getExtras());
            }
            if (getIntent().getData()!=null) {
                mainIntent.setData(getIntent().getData());
            }
        }
        //待解决：这里本来应该是申请完权限的那里的。。
        initDB();
//        initTuya();
    }

    private void __bindViews() {
        appVersion = findViewById(R.id.app_version);
    }

    private void initDB() {
        try {
            DBUtil db = new DBUtil(DinSaferApplication.getAppContext());
        } catch (Exception e) {
            DDLog.log("DB", "数据库异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        requestPermissions();
    }

    private void requestPermissions() {
        if (DDSystemUtil.isAndroid13()) {
            AndPermission.with(this)
                    .runtime()
                    .permission(Manifest.permission.POST_NOTIFICATIONS)
                    .onGranted(new Action<List<String>>() {
                        @Override
                        public void onAction(List<String> permissions) {
                            initData();
                        }
                    })
                    .onDenied(new Action<List<String>>() {
                        @Override
                        public void onAction(List<String> permissions) {
                            initData();
                        }
                    })
                    .start();
        } else {
            initData();
        }
    }


    private void initData() {
        initLanguageList();
        startCountDown();
        initPushChannel();
    }

    private void initPushChannel() {
        Log.d(TAG, "initPushChannel.");
        PushUtil.createNotificationChannel(this);
//        PushUtil.setAlias(PushChannel.XIAOMI,"jj001","asldfjalsdfj");
    }

    private static final long LANGUAGE_UPDATE_TIME = 7 * 24 * 60 * 60;

    private void initLanguageList() {
        try {
            long time = DBUtil.Lum(DBKey.LANGUAGE_TIME);
            if (time <= 0 || (System.currentTimeMillis() - time) / 1000 >= LANGUAGE_UPDATE_TIME) {
                LocalManager.getInstance().download(null);
                DBUtil.Put(DBKey.LANGUAGE_TIME, System.currentTimeMillis());
            }

//            当没有初始化过语言数据库，则重新加载，加载到数据库里面去，成功后才进去主页
            isNeedToLoadLanguage = true;
            LocalManager.getInstance().readLocalCache();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LanguageUpdataEvent ev) {
        DDLog.i("language", "finish");
        if (isNeedToLoadLanguage) {
            jumpToMainActivity();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    private void startCountDown() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isNeedToLoadLanguage) {
                    jumpToMainActivity();
                }
            }
        }, SPLASH_DISPLAY_LENGTH);
    }

    private void jumpToMainActivity() {
        startActivity(mainIntent);
        finish();
    }

}
