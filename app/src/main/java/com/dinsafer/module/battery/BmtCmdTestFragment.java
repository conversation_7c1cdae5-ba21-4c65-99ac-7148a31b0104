package com.dinsafer.module.battery;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.common.BmtManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBmtCmdTestBinding;
import com.dinsafer.dinnet.databinding.ItemBmtCmdBinding;
import com.dinsafer.dinsdk.DinSDKHelper;
import com.dinsafer.dinsdk.ExecutorAction;
import com.dinsafer.dinsdk.IExecutorCallBack;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.util.MapUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class BmtCmdTestFragment extends MyBaseFragment<FragmentBmtCmdTestBinding> {
    private BindMultiAdapter adapter = new BindMultiAdapter();
    private Device device;
    Gson gson = new Gson();
    Type gsonType = new TypeToken<HashMap>() {
    }.getType();

    public static BmtCmdTestFragment newInstance() {

        Bundle args = new Bundle();

        BmtCmdTestFragment fragment = new BmtCmdTestFragment();
        fragment.setArguments(args);
        return fragment;
    }


    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_bmt_cmd_test;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.rv.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rv.setAdapter(adapter);

    }

    @Override
    public void initData() {
        super.initData();
        // device = BmtManager.getInstance().getDeviceById("J5jDEANNK11NrcRj");
        List<Device> allBmtDevice = BmtManager.getInstance().getAllBmtDevice();
        if (allBmtDevice.size() == 0) {
            Log.e(TAG, "initData: device list's size is zero");
            removeSelf();
            return;
        }
        device = allBmtDevice.get(0);
        if (device == null) {
            Log.e(TAG, "initData: device null");
            removeSelf();
            return;
        }
        device.registerDeviceCallBack(new IDeviceCallBack() {
            @Override
            public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
                String gsonString = gson.toJson(map, gsonType);
                Log.v(TAG, "onCmdCallBack: " + id
                        + " /subCategory: " + subCategory
                        + " /cmd:" + cmd
                        + " /result:" + gsonString
                        + " /" + Thread.currentThread().getName());
            }
        });
        Type type = new TypeToken<List>() {
        }.getType();

        DinSDKHelper.getInstance().excute(new ExecutorAction() {
            @Override
            public Object runAction() {
                String json = loadJSONFromAsset(getDelegateActivity());
                List<CmdItemModle> result = new ArrayList<>();
                try {
                    JSONArray list = new JSONArray(json);
                    for (int i = 0; i < list.length(); i++) {
                        JSONObject jsonObject = list.getJSONObject(i);
                        Iterator<String> keys = jsonObject.keys();
                        Map map = new HashMap();
                        while (keys.hasNext()) {
                            String key = keys.next();
                            Object value = jsonObject.get(key);
                            if (value instanceof JSONArray) {
                                if (((JSONArray) value).opt(0) instanceof Integer) {
                                    List newValue = new ArrayList();
                                    for (int i1 = 0; i1 < ((JSONArray) value).length(); i1++) {
                                        newValue.add(((JSONArray) value).optInt(i1));
                                    }
                                    value = newValue;
                                }
                            }
                            map.put(key, value);
                        }
                        result.add(new CmdItemModle(map));
                    }


                } catch (JSONException e) {
                    e.printStackTrace();
                }

                return result;
            }
        }).thenUI(new IExecutorCallBack<List>() {
            @Override
            public void onResult(List result) {
                adapter.setNewData(result);
            }
        });
    }

    public String loadJSONFromAsset(Context context) {
        String json = null;
        try {
            InputStream is = context.getAssets().open("bmt_cmd.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            json = new String(buffer, "UTF-8");
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
        return json;
    }

    class CmdItemModle implements BaseBindModel<ItemBmtCmdBinding> {
        private Map params;

        public CmdItemModle(Map params) {
            this.params = params;
        }


        @Override
        public int getLayoutID() {
            return R.layout.item_bmt_cmd;
        }

        @Override
        public void onDo(View v) {
            if (device != null) {
                try {
                    device.submit(params);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void convert(BaseViewHolder holder, ItemBmtCmdBinding binding) {
            binding.title.setText((CharSequence) MapUtils.get(params, "cmd", "cmd null"));
            binding.tvContent.setText(gson.toJson(params));
        }
    }
}
