package com.dinsafer.module.battery;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentBatteryRootBinding;
import com.dinsafer.module.MyBaseFragment;

/**
 * BMT电源主页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/10/11 3:57 下午
 */
public class BatteryRootFragment extends MyBaseFragment<FragmentBatteryRootBinding> {

    public static BatteryRootFragment newInstance() {
        return new BatteryRootFragment();
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_battery_root;
    }
}
