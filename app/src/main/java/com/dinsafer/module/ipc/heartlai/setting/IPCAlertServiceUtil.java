package com.dinsafer.module.ipc.heartlai.setting;

import com.dinsafer.dinnet.R;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/11/30
 */
public class IPCAlertServiceUtil {
    private static final int VIP_0 = 0;
    private static final int VIP_1 = 1;
    private static final int VIP_2 = 2;
    private static final int VIP_3 = 3;
    private static final int VIP_4 = 4;

    public static int getVIPLevelIcon(int vipLevel) {
        if (VIP_1 == vipLevel) {
            return R.drawable.icon_ipc_vip1_s;
        } else if (VIP_2 == vipLevel) {
            return R.drawable.icon_ipc_vip2_s;
        } else if (VIP_3 == vipLevel) {
            return R.drawable.icon_ipc_vip3_s;
        } else if (VIP_4 == vipLevel) {
            return R.drawable.icon_ipc_vip4_s;
        } else {
            return R.drawable.icon_ipc_vip0_s;
        }
    }

    public static int getVIPLevelIconDashboard(int vipLevel) {
        if (VIP_1 == vipLevel) {
            return R.drawable.icon_ipc_vip1;
        } else if (VIP_2 == vipLevel) {
            return R.drawable.icon_ipc_vip2;
        } else if (VIP_3 == vipLevel) {
            return R.drawable.icon_ipc_vip3;
        } else if (VIP_4 == vipLevel) {
            return R.drawable.icon_ipc_vip4;
        } else {
            return R.drawable.icon_ipc_vip0;
        }
    }


    public static int getVIPLevelDes(int vipLevel) {
        if (VIP_1 == vipLevel) {
            return R.string.ipc_subscription_current_plan_detail;
        } else if (VIP_2 == vipLevel) {
            return R.string.ipc_subscription_current_plan_detail;
        } else if (VIP_3 == vipLevel) {
            return R.string.ipc_subscription_current_plan_detail;
        } else if (VIP_4 == vipLevel) {
            return R.string.ipc_subscription_current_plan_detail;
        } else {
            return R.string.ipc_subscription_no_subscription_detail;
        }
    }
}
