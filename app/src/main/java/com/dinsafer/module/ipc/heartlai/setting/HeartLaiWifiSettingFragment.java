package com.dinsafer.module.ipc.heartlai.setting;

import android.app.AlertDialog;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.InputType;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.dinsafer.common.IPCManager;
import com.dinsafer.common.PluginConstants;
import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.login.LoadingFragment;
import com.dinsafer.module.settting.ui.Builder;
import com.dinsafer.module.settting.ui.ModifyASKPlugsFragment;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.add.NetworkConfigurer;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.NumberEditText;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.Local;

/**
 * Created by rinfon on 17-6-14.
 */

public class HeartLaiWifiSettingFragment extends BaseFragment {


    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    LocalCustomButton commonBarLeft;
    RelativeLayout commonTitleBar;
    EditText ipcWifiName;
    NumberEditText ipcWifiPassword;
    NumberEditText ipcWifiRePassword;
    CheckBox wifiRemember;
    ImageView ipcWifiPasswordIcon;
    ImageView ipcWifiRepasswordIcon;

    String wifiSSid;

    int en;

    boolean isAdd;

    Device model;

    static Handler handler = new Handler();

    boolean isAutoDisconnectAp = false;

    private NetworkConfigurer networkConfigurer;

    public static HeartLaiWifiSettingFragment newInstance(String deviceId, String ssid, int en, boolean isAdd, boolean isAutoDisconnectAp, NetworkConfigurer networkConfigurer) {
        HeartLaiWifiSettingFragment ipcSettingFragment = new HeartLaiWifiSettingFragment();
        Bundle bundle = new Bundle();
        bundle.putString("deviceId", deviceId);
        bundle.putString("ssid", ssid);
        bundle.putInt("en", en);
        bundle.putBoolean("isAdd", isAdd);
        bundle.putBoolean("isAutoDisconnectAp", isAutoDisconnectAp);
        ipcSettingFragment.networkConfigurer = networkConfigurer;
        ipcSettingFragment.setArguments(bundle);
        return ipcSettingFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.ipc_wifi_setting, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        commonBarTitle.setText(Local.s("Wifi Setting"));
        commonBarLeft.setLocalText(getString(R.string.save));
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.btn_save).setOnClickListener( v -> toSave());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.ipc_wifi_password_icon).setOnClickListener( v -> toShowNextPassword());
        rootView.findViewById(R.id.ipc_wifi_repassword_icon).setOnClickListener( v -> toShowConfirmPassword());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarLeft = rootView.findViewById(R.id.btn_save);
        commonTitleBar = rootView.findViewById(R.id.common_title_bar);
        ipcWifiName = rootView.findViewById(R.id.ipc_wifi_name);
        ipcWifiPassword = rootView.findViewById(R.id.ipc_wifi_password);
        ipcWifiRePassword = rootView.findViewById(R.id.ipc_wifi_re_password);
        wifiRemember = rootView.findViewById(R.id.wifi_remember);
        ipcWifiPasswordIcon = rootView.findViewById(R.id.ipc_wifi_password_icon);
        ipcWifiRepasswordIcon = rootView.findViewById(R.id.ipc_wifi_repassword_icon);
    }

    public void initData() {
        super.initData();

        isAdd = getArguments().getBoolean("isAdd");
        isAutoDisconnectAp = getArguments().getBoolean("isAutoDisconnectAp");
        en = getArguments().getInt("en");
        wifiSSid = getArguments().getString("ssid");
        model = networkConfigurer.getCurrentConfigDevice();
        if (model == null) {
            toClose();
            return;
        }

        ipcWifiName.setText(wifiSSid);
        ipcWifiName.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ipc_wifi_set_name)));
        ipcWifiName.setEnabled(false);
        ipcWifiPassword.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ap_step_wifi_pass_hint)));
        ipcWifiRePassword.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ap_step_wifi_pass_hint_confirm)));
        ipcWifiPassword.setInputType(129);
        ipcWifiRePassword.setInputType(129);

        wifiRemember.setChecked(true);
        wifiRemember.setText(Local.s(getResources().getString(R.string.remember_password)));

        ipcWifiName.setOnTouchListener((v, event) -> {
            final int DRAWABLE_LEFT = 0;
            final int DRAWABLE_TOP = 1;
            final int DRAWABLE_RIGHT = 2;
            final int DRAWABLE_BOTTOM = 3;

            if (event.getAction() == MotionEvent.ACTION_UP) {
                final Drawable[] compoundDrawables = ipcWifiName.getCompoundDrawables();
                final boolean hadIcon = (null != compoundDrawables && compoundDrawables.length >= DRAWABLE_RIGHT + 1);
                if (hadIcon && event.getRawX() >= (ipcWifiName.getRight() - compoundDrawables[DRAWABLE_RIGHT].getBounds().width())) {
                    // your action here
                    removeSelf();
                    return true;
                }
            }
            return true;
        });

        if (wifiSSid.equals(DBUtil.SGet(DBKey.REMEMBER_WIFI))) {
            ipcWifiPassword.setText(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
            ipcWifiRePassword.setText(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
        }

        networkConfigurer.setOnConfigNetworkCallback(configNetworkCallback);
    }

    public void toSave() {
        if ("".equals(ipcWifiName.getText().toString())) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getDelegateActivity());
            builder.setMessage(Local.s("empty ssid is forbidden"));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        final String pwd1 = ipcWifiPassword.getText().toString();
        String pwd2 = ipcWifiRePassword.getText().toString();

        if (!pwd1.equals(pwd2)) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getMainActivity());
            builder.setMessage(Local.s(getResources().getString(R.string.password_not_match)));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        if (("".equals(pwd1)) || ("".equals(pwd2))) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getMainActivity());
            builder.setMessage(Local.s("empty ssid is forbidden"));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        if (ipcWifiName.getText().toString().length() > 0) {
            showLoadingFragment(LoadingFragment.BLACK, "");
            networkConfigurer.setWifiSSID(ipcWifiName.getText().toString());
            networkConfigurer.setWifiPWD(pwd1);
            networkConfigurer.setWifiExtra(en);
            networkConfigurer.startConfig();


            if (wifiRemember.isChecked()) {
                DBUtil.SPut(DBKey.REMEMBER_WIFI, ipcWifiName.getText().toString());
                DBUtil.SPut(DBKey.REMEMBER_WIFI_PASSWORD, ipcWifiPassword.getText().toString());
            } else {
                DBUtil.Delete(DBKey.REMEMBER_WIFI);
                DBUtil.Delete(DBKey.REMEMBER_WIFI_PASSWORD);
            }

        }

    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        handler.removeCallbacksAndMessages(null);
        networkConfigurer.destory();
        super.onDestroyView();
    }

    public void toShowNextPassword() {
        if (ipcWifiPassword.getInputType() == 129) {
            ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_show);
            ipcWifiPassword.setInputType(InputType.TYPE_CLASS_TEXT);
            ipcWifiRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_hide);
            ipcWifiPassword.setInputType(129);
            ipcWifiRePassword.setInputType(129);
        }
    }

    public void toShowConfirmPassword() {
        if (ipcWifiRePassword.getInputType() == 129) {
            ipcWifiRepasswordIcon.setImageResource(R.drawable.icon_form_show);
            ipcWifiRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
            ipcWifiRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            ipcWifiRepasswordIcon.setImageResource(R.drawable.icon_form_hide);
            ipcWifiRePassword.setInputType(129);
            ipcWifiRePassword.setInputType(129);
        }
    }

    private void finishSetting() {
        if (!isAdd) {
            // 先断开全部的心赖，减少出现不断重连或之前添加的心赖很难连上的概率
            // 不是添加的，直接开始重连
            DinSDK.getHomeInstance().releaseDeviceByType(DinConst.TYPE_HEARTLAI);
            IPCManager.getInstance().connectAllHeartLaiIPC();
            showSuccess();
            getDelegateActivity().removeAllCommonFragment();
        } else {
            // 添加的，需要在改名字后重新请求IPC列表后再连接，在这里请求返回的结果可能数据还没有上传导致获取不到新添加的IPC
            Device model1 = networkConfigurer.getCurrentConfigDevice();
            if (model1 == null) {
                showErrorToast();
                getDelegateActivity().removeAllCommonFragment();
                return;
            }
            Builder builder = new Builder();
            builder.setId(model1.getId())
                    .setAdd(true)
                    .setOffical(true)
                    .setShowDelete(false)
                    .setShowwave(false)
                    .setData(DeviceHelper.getString(model1, HeartLaiConstants.ATTR_SOURCE_DATA, "{}"));
            Plugin plugin = new Plugin(model1.getId());
            plugin.setPluginTypeName(PluginConstants.TYPE_1F);
            plugin.setSourceData(builder.getData());
            getDelegateActivity().addCommonFragment(ModifyASKPlugsFragment.newInstance(builder, plugin));
        }
    }

    private NetworkConfigurer.OnConfigNetworkCallback configNetworkCallback = new NetworkConfigurer.OnConfigNetworkCallback() {
        @Override
        public void onConfigNetworkSuccess() {
            Log.d(TAG, "onConfigNetworkSuccess: ");
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (wifiRemember.isChecked()) {
                        DBUtil.SPut(DBKey.REMEMBER_WIFI, ipcWifiName.getText().toString());
                        DBUtil.SPut(DBKey.REMEMBER_WIFI_PASSWORD, ipcWifiPassword.getText().toString());
                    }

                    closeLoadingFragment();
                    finishSetting();
                }
            });

        }

        @Override
        public void onConfigNetworkFail() {
            Log.d(TAG, "onConfigNetworkFail: ");
            closeLoadingFragment();
            showErrorToast();
        }

        @Override
        public void receiveData(Object... args) {

        }
    };
}
