package com.dinsafer.module.ipc.heartlai.setting;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.adapter.TimePhoneZoneAdapter;
import com.dinsafer.module_heartlai.add.NetworkConfigurer;
import com.dinsafer.module_heartlai.model.WifiModel;
import com.dinsafer.ui.LocalTextView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Rinfon on 16/7/8.
 */
public class HeartLaiWifiListFragment extends BaseFragment {

    LocalTextView commonBarTitle;

    ListView choosePhoneZoneListview;
    ImageView commonBarBack;
    ImageView commonBarRight;

    private TimePhoneZoneAdapter mAdapter;

    private ArrayList<String> mData = new ArrayList<String>();

    boolean isCanClose = false;

    List<WifiModel> wifilist = new ArrayList<>();

    boolean isAdd = false;

    boolean isAutoDisconnectAp = false;

    private NetworkConfigurer networkConfigurer;

    public static HeartLaiWifiListFragment newInstance(String ipcPid, boolean isCanClose, boolean isAdd, boolean isAutoDisconnectAp, NetworkConfigurer networkConfigurer) {
        HeartLaiWifiListFragment fragment = new HeartLaiWifiListFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean("isCanClose", isCanClose);
        bundle.putBoolean("isAdd", isAdd);
        bundle.putBoolean("isAutoDisconnectAp", isAutoDisconnectAp);
        bundle.putString("deviceId", ipcPid);
        fragment.networkConfigurer = networkConfigurer;
        fragment.setArguments(bundle);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.time_zone_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> close());
        rootView.findViewById(R.id.common_bar_right).setOnClickListener( v -> toRefresh());
    }

    private void __bindViews(View rootView) {
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        choosePhoneZoneListview = rootView.findViewById(R.id.choose_phone_zone_listview);
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarRight = rootView.findViewById(R.id.common_bar_right);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getResources().getString(R.string.wifi_list));
        mData = new ArrayList<String>();
        mAdapter = new TimePhoneZoneAdapter(getActivity(), mData);
        choosePhoneZoneListview.setAdapter(mAdapter);
        isCanClose = true;
        isAdd = getArguments().getBoolean("isAdd");
        isAutoDisconnectAp = getArguments().getBoolean("isAutoDisconnectAp");
        if (isCanClose) {
            commonBarBack.setVisibility(View.VISIBLE);
        } else {
            commonBarBack.setVisibility(View.GONE);
        }

        commonBarRight.setImageResource(R.drawable.btn_userpage_refresh);
        commonBarRight.setVisibility(View.VISIBLE);
        choosePhoneZoneListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                getDelegateActivity().addCommonFragment(HeartLaiWifiSettingFragment.newInstance(getArguments().getString("deviceId"),
                        wifilist.get(position)
                                .getSsid(), wifilist.get(position).getEncryption(), isAdd, isAutoDisconnectAp, networkConfigurer));

            }
        });

        toRefresh();
    }

    @Override
    public boolean onBackPressed() {
        if (isCanClose) {
            return false;
        } else {
            return true;
        }
    }

    public void close() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public void toRefresh() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        networkConfigurer.getWifiList(new NetworkConfigurer.GetWifiListCallback() {
            @Override
            public void onGetWifiListSuccess(List wifiNameList, List data) {
                mData.clear();
                mData.addAll(wifiNameList);

                wifilist.clear();
                wifilist.addAll(data);

                mAdapter.setData(mData);
                mAdapter.notifyDataSetChanged();
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }

            @Override
            public void onGetWifiListFail() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
            }
        });

    }

}

