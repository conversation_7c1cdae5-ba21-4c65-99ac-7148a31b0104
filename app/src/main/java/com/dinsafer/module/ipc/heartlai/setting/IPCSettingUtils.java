package com.dinsafer.module.ipc.heartlai.setting;

import android.content.Context;
import androidx.core.content.ContextCompat;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.CircleSeekBar;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/11/23
 */
public class IPCSettingUtils {
    public static String getFormatPeriod(Context context, int startIndex, int endIndex) {
        int startHour = (int) (startIndex / 6F);
        int startMin = (startIndex % 6) * 10;
        StringBuilder startBuilder = new StringBuilder();

        if (startHour < 10) {
            startBuilder.append("0");
        }
        startBuilder
                .append(startHour)
                .append(":");
        if (startMin < 10) {
            startBuilder.append("0");
        }
        startBuilder.append(startMin);

        int endHour = (int) (endIndex / 6F);
        int endMin = (endIndex % 6) * 10;
        if (endHour == 0 && endMin == 0) {
            endHour = 24;
        }

        startBuilder.append("-");
        if (endHour < 10) {
            startBuilder.append("0");
        }
        startBuilder.append(endHour)
                .append(":");
        if (endMin < 10) {
            startBuilder.append("0");
        }
        startBuilder.append(endMin);
        startBuilder.append(" ")
                .append(Local.s(context.getString(R.string.ipc_setting_each_day)));

        return startBuilder.toString();
    }

    public static String getFormatPeriodByIndex(int index) {
        int startHour = (int) (index / 6F);
        int startMin = (index % 6) * 10;
        StringBuilder startBuilder = new StringBuilder();

        if (startHour < 10) {
            startBuilder.append("0");
        }
        startBuilder
                .append(startHour)
                .append(":");
        if (startMin < 10) {
            startBuilder.append("0");
        }
        startBuilder.append(startMin);

        return startBuilder.toString();
    }

    public static void initCommonStyleCircleSeekBar(Context context, CircleSeekBar csb) {
        csb.setArcColor(ContextCompat.getColor(context, R.color.ipc_setting_circle_seekbar_arc_color));
        csb.setArcBgColor(ContextCompat.getColor(context, R.color.ipc_setting_circle_seekbar_arc_bg_color));
        csb.setInnerBgColor(ContextCompat.getColor(context, R.color.ipc_setting_circle_seekbar_inner_bg_color));
        csb.setStartThumbColor(ContextCompat.getColor(context, R.color.ipc_setting_circle_seekbar_start_thumb_color));
        csb.setStartThumbSelectedColor(ContextCompat.getColor(context, R.color.ipc_setting_circle_seekbar_start_thumb_selected_color));
        csb.setEndThumbColor(ContextCompat.getColor(context, R.color.ipc_setting_circle_seekbar_start_thumb_color));
        csb.setEndThumbSelectedColor(ContextCompat.getColor(context, R.color.ipc_setting_circle_seekbar_start_thumb_selected_color));
        csb.setArcWidth(DensityUtils.dp2px(context, 26));
        csb.setMaxProgress(144);
        csb.setStartIndex(0);
        csb.setEndIndex(144);
        csb.invalidate();
    }

    public static int getHourFormCircleSeekBarIndex(int index) {
        return (int) (index / 6F);
    }

    public static int getMinFormCircleSeekBarIndex(int index) {
        return (index % 6) * 10;
    }
}
