package com.dinsafer.module.ipc.heartlai;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.aop.annotations.Safer;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dinnet.R;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.model.AppStatePreEvent;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.IPCEvent;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.model.event.HadLogoutPreEvent;
import com.dinsafer.module.BaseLiveVideoActivity;
import com.dinsafer.module.ipc.heartlai.event.IPCInfoChangeEvent;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.settting.adapter.ipc.FullPlayBackEvent;
import com.dinsafer.module.settting.camera.HeartLaiPlayerManager;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.play.HeartLaiPlayer;
import com.dinsafer.module_heartlai.play.base.IPlayerListener;
import com.dinsafer.module_heartlai.play.player.DinsaferPlayer;
import com.dinsafer.module_heartlai.play.player.IPlayerSnapshotCallback;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.MyViewFlipper;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DeviceParmsBuilder;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;
import com.githang.statusbar.StatusBarCompat;
import com.heartlai.ipc.BridgeService;
import com.heartlai.ipc.widget.MyLiveViewGLMonitor;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;


/**
 * Created by Rinfon on 16/7/12.
 */
public class HeartLaiFullPlayActivity extends BaseLiveVideoActivity
        implements BridgeService.VideoExitInterface, IDeviceCallBack, IDeviceListChangeListener {

    private static final String TAG = HeartLaiFullPlayActivity.class.getSimpleName();
    TextView commonBarTitle;

    CameraVideoView mVideoView;

    MyLiveViewGLMonitor mMonitor;

    LinearLayout ipcControlMain;

    MyViewFlipper ipcControlFlipper;

    ImageView ipcControlSpeakBtn;

    RelativeLayout mTitleBar;
    Button ipcControlMove;
    Button ipcControlListener;
    Button ipcControlSpeak;
    Button ipcControlSnapshot;

    private boolean isInitTakePicture = true;
    private boolean isPlaySound;
    private boolean isFromUserClick = false;

    private DinsaferPlayer currentPlayer;
    private Device device;

    SurfaceHolder playHolder;
    private Timer timer;
    public String mCameraPid;

    private SurfaceHolder.Callback surfaceHolderCallback;

    public static void startActivity(Context context, String pid) {
        Intent intent = new Intent(context, HeartLaiFullPlayActivity.class);
        intent.putExtra("pid", pid);
        context.startActivity(intent);
    }

    /**
     * 初始化变量
     */
    @Override
    protected boolean initVariables() {
        device = IPCManager.getInstance().getHeartLaiDeviceByPID(getIntent().getStringExtra("pid"));
        if (device == null) {
            return false;
        }
        mCameraPid = DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, "");
        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
        return true;
    }

    /**
     * 初始化控件
     *
     * @param savedInstanceState
     */
    @Override
    protected void initViews(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);//隐藏标题
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);//设置全屏
        setContentView(R.layout.heart_lai_ipc_layout_new);
        __bindViews();
        __bindClicks();
        StatusBarCompat.setStatusBarColor(this, Color.BLACK, false);
        EventBus.getDefault().register(this);
        mTitleBar = (RelativeLayout) findViewById(R.id.common_bar);
        commonBarTitle.setText(Local.s(getResources().getString(R.string.device_managent_ip_camera)));
        initVideoView();
    }

    private void __bindClicks() {
        findViewById(R.id.ipc_control_snapshot).setOnClickListener(v -> toSnapshot());
        findViewById(R.id.common_bar_back).setOnClickListener(v -> close());
        findViewById(R.id.ipc_full_screen).setOnClickListener(v -> close());
        findViewById(R.id.ipc_control_listener).setOnClickListener(v -> toListener());
        findViewById(R.id.ipc_control_move).setOnClickListener(v -> toMove());
        findViewById(R.id.ipc_control_move_back).setOnClickListener(v -> toMainControl());
        findViewById(R.id.ipc_control_speak_back).setOnClickListener(v -> toMainControl());
        findViewById(R.id.ipc_control_speak).setOnClickListener(v -> toSpeakLayout());
        findViewById(R.id.ipc_control_speak_btn).setOnTouchListener((v, event) -> toTalk(v, event));
        findViewById(R.id.ipc_control_move_left).setOnTouchListener((v, event) -> toMoveLeft(v, event));
        findViewById(R.id.ipc_control_move_right).setOnTouchListener((v, event) -> toMoveRight(v, event));
        findViewById(R.id.ipc_control_move_down).setOnTouchListener((v, event) -> toMoveDown(v, event));
        findViewById(R.id.ipc_control_move_up).setOnTouchListener((v, event) -> toMoveUp(v, event));
        findViewById(R.id.ipc_control_move_zoom_in).setOnTouchListener((v, event) -> toZoomIn(v, event));
        findViewById(R.id.ipc_control_move_zoom_out).setOnTouchListener((v, event) -> toZoomOut(v, event));
    }

    private void __bindViews() {
        commonBarTitle = findViewById(R.id.common_bar_title);
        mVideoView = findViewById(R.id.video_view);
        ipcControlMain = findViewById(R.id.ipc_control_main);
        ipcControlFlipper = findViewById(R.id.ipc_control_flipper);
        ipcControlSpeakBtn = findViewById(R.id.ipc_control_speak_btn);
        ipcControlMove = findViewById(R.id.ipc_control_move);
        ipcControlListener = findViewById(R.id.ipc_control_listener);
        ipcControlSpeak = findViewById(R.id.ipc_control_speak);
        ipcControlSnapshot = findViewById(R.id.ipc_control_snapshot);
    }

    /**
     * 初始化数据
     */
    @Override
    protected void loadData() {
        refreshVideoViewState(device);
        connectIPC();

        if (!HeartLaiUtils.isDeviceConnected(device) && !HeartLaiUtils.isDeviceConnecting(device)) {
            showDeviceOfflineDialog(device);
        }
    }

    private void initVideoView() {
        mVideoView.getVideoContainer().removeAllViews();
        mMonitor = new MyLiveViewGLMonitor(this, null);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
        mVideoView.getVideoContainer().addView(mMonitor, lp);

        ipcControlListener.setCompoundDrawablesWithIntrinsicBounds(null, getResources().getDrawable(R.drawable.icon_ipc_monitor_inactive_fullscreen)
                , null, null);
        ipcControlSnapshot.setCompoundDrawablesWithIntrinsicBounds(null, getResources().getDrawable(R.drawable.icon_ipc_screenshot_fullscreen)
                , null, null);

        // 预览图
        final String snapshotPath = DeviceHelper.getString(device, HeartLaiConstants.ATTR_SNAPSHOT, "");
        if (!TextUtils.isEmpty(snapshotPath)) {
            File file = new File(snapshotPath);
            if (file.exists()) {
                mVideoView.setCoverImageUri(Uri.fromFile(file));
            } else {
                mVideoView.setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
            }
        } else {
            mVideoView.setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
        }
    }

    public void toSnapshot() {
        DDLog.i(TAG, "截图 点击");
        if (PermissionUtil.isStoragePermissionDeny(this)) {
            requestReadImagesPermission();
            return;
        }

        if (isInitTakePicture) {
            DDLog.i(TAG, "自动化截图未完成");
//            在还没有进行默认的第一次自动截图时，不能让用户点击截图
            return;
        }
        isFromUserClick = true;
        DDLog.i(TAG, "开始点击截图");
        this.currentPlayer.getSnapshot(new IPlayerSnapshotCallback() {
            @Override
            public void onSnapshot(Bitmap bitmap) {
                DDLog.i(TAG, "截图回调：" + bitmap.getByteCount());
                if (device != null) {
                    savePicture(bitmap, device.getId());
                }
            }
        });
    }

    /**
     * 配置IPC信息
     */
    private void initIPCInfo() {
        commonBarTitle.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, ""));

        try {
            JSONObject jsonObject = new JSONObject(DeviceHelper.getString(device, HeartLaiConstants.ATTR_SOURCE_DATA, "{}"));
            if (DDJSONUtil.getBoolean(jsonObject, "shake")) {
                ipcControlMove.setVisibility(View.VISIBLE);
            } else {
                ipcControlMove.setVisibility(View.GONE);
            }

            if (DDJSONUtil.getBoolean(jsonObject, "talk")) {
                ipcControlSpeak.setVisibility(View.VISIBLE);
            } else {
                ipcControlSpeak.setVisibility(View.GONE);
            }

            if (DDJSONUtil.getBoolean(jsonObject, "listen")) {
                ipcControlListener.setVisibility(View.VISIBLE);
            } else {
                ipcControlListener.setVisibility(View.GONE);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    private void refreshVideoViewState(Device device) {
        DDLog.d(TAG, "refreshVideoViewState1: " + DeviceHelper.getInt(this.device, HeartLaiConstants.ATTR_CAMERA_STATUS, 0));
        DDLog.d(TAG, "refreshVideoViewState2: " + DeviceHelper.getInt(device, HeartLaiConstants.ATTR_CAMERA_STATUS, 0));
        initIPCInfo();
        if (HeartLaiUtils.isDeviceConnected(device)) {
            mVideoView.showLoading();
        } else if (HeartLaiUtils.isDeviceConnecting(device)) {
            mVideoView.showLoading();
        } else {
            mVideoView.showError();
            ipcControlFlipper.setVisibility(View.GONE);
            showDeviceOfflineDialog(this.device);
        }

    }

    //    获取两个触控点之间的距离；
    private float spacing(MotionEvent event) {
        float x = 0.0f, y = 0.0f;
        try {
            x = event.getX(0) - event.getY(1);
            y = event.getY(0) - event.getY(1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (float) Math.sqrt(x * x + y * y);
    }


    private int glviewWidth = 0, glviewHeight = 0;

    private void glViewZoomOut() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {


                /**
                 * 宽高一次缩小为原来图片的0.9倍。
                 */

                int width = mMonitor.width <= 0 ? mMonitor.getWidth() : mMonitor.width;
//                int height = mMonitor.height <= 0 ? mMonitor.getHeight() : mMonitor.height;


                if (glviewWidth == 0) {
                    glviewWidth = mMonitor.getWidth();
                    glviewHeight = mMonitor.getHeight();
                }

                if (width * 0.9 <= glviewWidth) {
                    mMonitor.width = glviewWidth;
                    mMonitor.height = glviewHeight;
                } else {
                    mMonitor.width = (int) (width * 0.9);
//                                lp.height = (int) (mMonitor.getHeight() * 0.9);
                    mMonitor.height = (int) (width * 3 / 4 * 0.9);
                }
                mMonitor.setMatrix(mMonitor.left, mMonitor.bottom, mMonitor.width,
                        mMonitor.height);
            }
        });

    }

    private void glViewZoomIn() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {

                /**
                 * 宽高一次放大为原来的1.1倍（当然，也可以为其他数值）。
                 */

                int width = mMonitor.width <= 0 ? mMonitor.getWidth() : mMonitor.width;
//                int height = mMonitor.height <= 0 ? mMonitor.getHeight() : mMonitor.height;

                if (glviewWidth == 0) {
                    glviewWidth = mMonitor.getWidth();
                    glviewHeight = mMonitor.getHeight();
                }
                /**
                 * 只能放大3次
                 */
                if (width >= glviewWidth * 1.1 * 1.1 * 1.1) {
                    return;
                }

                mMonitor.width = (int) (width * 1.1);
//                                lp.height = (int) (glView.getHeight() * 1.1);
                mMonitor.height = (int) (width * 3 / 4 * 1.1);

                mMonitor.setMatrix(mMonitor.left, mMonitor.bottom, mMonitor.width,
                        mMonitor.height);
            }
        });

    }

    private void initMatrix(int screen_width, int screen_height) {
        mMonitor.left = 0;
        mMonitor.bottom = 0;

        mMonitor.width = screen_width;
        mMonitor.height = screen_height;
    }

    public void connectIPC() {
        BridgeService.addVideoExitInterface(this);

        // 初始化SDK并登录
        HeartLaiPlayer heartLaiPlayer = HeartLaiPlayer.newBuilder()
                .activity(this)
                .pid(mCameraPid)
                .user(DeviceHelper.getString(device, HeartLaiConstants.ATTR_UID, ""))
                .name(DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, ""))
                .pwd(DeviceHelper.getString(device, HeartLaiConstants.ATTR_PASSWORD, ""))
                .monitor(mMonitor)
                .device(device)
                .build();

        currentPlayer = new DinsaferPlayer();
        currentPlayer.setPlayer(heartLaiPlayer);
        playHolder = mMonitor.getHolder();
        HeartLaiPlayerManager.getInstance().addPlayer(mCameraPid, currentPlayer);
        heartLaiPlayer.setMonitor(mMonitor);
        heartLaiPlayer.addPlayListener(mVideoView);

        heartLaiPlayer.addPlayListener(new IPlayerListener() {
            @Override
            public void onLoadData() {
                DDLog.d(TAG, "onLoadData: ");
            }

            @Override
            public void onPlay() {
                DDLog.d(TAG, "onPlay: ");
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ipcControlFlipper.setVisibility(View.VISIBLE);
                        mVideoView.hideAllIcon();
                    }
                });
                currentPlayer.getSnapshot(new IPlayerSnapshotCallback() {
                    @Override
                    public void onSnapshot(Bitmap bitmap) {
                        DDLog.i(TAG, "截图回调：" + bitmap.getByteCount());
                        if (device != null) {
                            savePicture(bitmap, device.getId());
                        }
                    }
                });
            }

            @Override
            public void onPlaying() {
                DDLog.d(TAG, "onPlaying: ");
            }

            @Override
            public void onPausePlay() {
                DDLog.d(TAG, "onPausePlay: ");
            }

            @Override
            public void onResumePlay() {
                DDLog.d(TAG, "onResumePlay: ");
            }

            @Override
            public void onStopPlay() {
                DDLog.d(TAG, "onStopPlay: ");
            }

            @Override
            public void onDestroy() {
                DDLog.d(TAG, "onDestroy: ");
            }

            @Override
            public void onStartListen() {
                DDLog.d(TAG, "onStartListen: ");
            }

            @Override
            public void onStartTalk() {
                DDLog.d(TAG, "onStartTalk: ");
            }

            @Override
            public void onStopListen() {

            }

            @Override
            public void onStopTalk() {

            }

            @Override
            public void onGetSnapshot() {
                DDLog.d(TAG, "onGetSnapshot: ");
            }

            @Override
            public boolean onStartListening() {
                return false;
            }

            @Override
            public boolean onStartTalking() {
                return false;
            }
        });

        playHolder = mMonitor.getHolder();
        if (surfaceHolderCallback != null) {
            playHolder.removeCallback(surfaceHolderCallback);
        }
        surfaceHolderCallback = new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) {
                try {
                    if (holder == playHolder) {
                        if (currentPlayer != null) {
                            currentPlayer.startPlay();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {

            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
//                if (currentPlayer != null) {
//                    currentPlayer.releasePlay();
//                }

            }
        };
        playHolder.addCallback(surfaceHolderCallback);

        device.registerDeviceCallBack(this);
    }

    @Override
    public void onExit() {
        DDLog.i(TAG, "video onExit");
    }

    public void close() {
        toClose();
    }

    @Override
    protected void toClose() {
        closeSelf(false);
    }

    /**
     * @param needMainLoading 是否需要触发MainActivity的loading
     */
    private void closeSelf(final boolean needMainLoading) {
        releasePlayer();
        if (!needMainLoading) {
            EventBus.getDefault().post(new CloseActivityEvent());
        }
        this.finish();
    }

    @Subscribe
    public void onEvent(IPCEvent event) {
        toClose();
    }

    private void releasePlayer() {
        BridgeService.removeVideoExitInterface(this);
        if (this.currentPlayer != null) {
            this.currentPlayer.releasePlay();
            this.currentPlayer = null;
        }
        if (playHolder != null) {
            playHolder.removeCallback(surfaceHolderCallback);
        }
        HeartLaiPlayerManager.getInstance().removePlayer(mCameraPid);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        releasePlayer();
        EventBus.getDefault().post(new FullPlayBackEvent(mCameraPid));
        EventBus.getDefault().unregister(this);
        DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(this);
        if (device != null) {
            device.unregisterDeviceCallBack(this);
        }

        IPCManager.getInstance().disconnectNotOnlineCamera();
    }

    private void savePicture(Bitmap bitmap, String ipcId) {
        if (bitmap == null || ipcId == null) {
            return;
        }
        if (!isFromUserClick) {
            try {

                DDLog.i(TAG, "自动化截图");
                // 将图片截小；
//            Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bmp, 132, 100,
//                    ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
                Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, APIKey.IMAGE_WIDTH, APIKey.IMAGE_HIGH,
                        ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

                String genPath = DDGlobalEnv.getInstance().getAppImageFolder();
                File genFile = new File(genPath);
                if (!genFile.exists()) {
                    genFile.mkdirs();
                }

                String path = genPath + ".ipc";
                File file = new File(path);
                if (!file.exists()) {
                    file.mkdir();
                }
                //fileName为文件绝对路径+文件名
                String fileName = path + "/" + ipcId + String.valueOf("_" + System.currentTimeMillis()) + ".jpg";
                File snapshotFile = new File(fileName);

                if (snapshotFile.exists()) {
                    snapshotFile.delete();
                }

                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                ipcImage.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();

                JSONObject jsonObject;
                if (DBUtil.Exists(ipcId)) {
                    jsonObject = new JSONObject(DBUtil.Str(ipcId));
                } else {
                    jsonObject = new JSONObject();
                }

                DDLog.i(TAG, "自动化截图 完毕");

                jsonObject.put(LocalKey.SNAPSHOT, fileName);
                jsonObject.put(LocalKey.LAST_OPEN_TIME, (System.currentTimeMillis()));
                KV.putString(DBKey.KEY_SNAPSHOT + ipcId, jsonObject.toString());
                if (IPCManager.getInstance().getHeartLaiDeviceByID(device.getId()) != null) {
                    IPCManager.getInstance().getHeartLaiDeviceByID(device.getId()).getInfo().put(HeartLaiConstants.ATTR_SNAPSHOT, fileName);
                }
//                这里如果没有用到这个事件的话,就会出现白屏
                EventBus.getDefault().post(new IPCInfoChangeEvent(ipcId));

            } catch (Exception e) {
                e.printStackTrace();
            }

            isInitTakePicture = false;

        } else {
//        用户主动点击时候的截图
            DDLog.i(TAG, "用户主动点击时候的截图");
            Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, APIKey.IMAGE_WIDTH, APIKey.IMAGE_HIGH,
                    ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

            File file = new File(DDGlobalEnv.getInstance().getImageFolder());
            if (!file.exists()) {
                file.mkdirs();
            }

            String strDate = new SimpleDateFormat("yyyy.MM.dd_HH.mm.ss").format(new Date());
            //fileName为文件绝对路径+文件名
            String fileName = DDGlobalEnv.getInstance().getImageFolder() + ipcId + "_" + strDate + ".jpg";
            File snapshotFile = new File(fileName);
            if (snapshotFile.exists()) {
                snapshotFile.delete();
            }
            try {
                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                ipcImage.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();
                DDSystemUtil.updatePhoto(this, snapshotFile);
                showToast("Save the photo successfully");

            } catch (Exception e) {
                showToast("Fail to save the photo");
            }

            isFromUserClick = true;
        }
    }

    private void showToast(final String s) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.createBuilder(HeartLaiFullPlayActivity.this)
                        .setOk("OK")
                        .setContent(s)
                        .preBuilder()
                        .show();

            }
        });
    }

    /**
     * 输出日志
     *
     * @param msg
     */
    public final void i(@NonNull String msg) {
        DDLog.i(this.getClass().getName(), msg);
    }


    public void toListener() {

        if (currentPlayer == null || !currentPlayer.isConnected()) {
            return;
        }
        if (isPlaySound) {
            currentPlayer.stopListen();
            ipcControlListener.setCompoundDrawablesWithIntrinsicBounds(null, getResources().getDrawable(R.drawable.icon_ipc_monitor_inactive_fullscreen)
                    , null, null);
            ipcControlListener.setTextColor(getResources().getColor(R.color.colorIPCControlIconText));
        } else {
            currentPlayer.startListen();
            ipcControlListener.setCompoundDrawablesWithIntrinsicBounds(null, getResources().getDrawable(R.drawable.icon_ipc_monitor_active_fullscreen)
                    , null, null);
            ipcControlListener.setTextColor(getResources().getColor(R.color.colorIPCControlIconTextsel));
        }

        isPlaySound = !isPlaySound;
    }

    public boolean toTalk(View v, MotionEvent event) {
        final int action = event.getAction();
        if (DDSystemUtil.isMarshmallow()
                && ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
            if (MotionEvent.ACTION_DOWN == action) {
                requestAudioPermission();
            }
            return true;
        }

        switch (action) {
            case MotionEvent.ACTION_DOWN:
                if (currentPlayer == null || !currentPlayer.isConnected()) {
                    return true;
                }
                currentPlayer.startTalk();
                ipcControlSpeakBtn.setImageResource(R.drawable.icon_ipc_talk_active_fullscreen);
                break;
            case MotionEvent.ACTION_UP:
                if (currentPlayer == null || !currentPlayer.isConnected()) {
                    return true;
                }
                currentPlayer.stopTalk();
                ipcControlSpeakBtn.setImageResource(R.drawable.icon_ipc_talk_inactive_fullscreen);
                break;
        }
        return true;
    }


    public void toMove() {

        Animation in = AnimationUtils.loadAnimation(this, R.anim.slide_out_left);
        in.setFillAfter(true);
        ipcControlFlipper.setInAnimation(AnimationUtils.loadAnimation(this,
                R.anim.slide_in_left));
        ipcControlFlipper.setOutAnimation(AnimationUtils.loadAnimation(this,
                R.anim.slide_out_left));
//        跳转到指定的页面
        ipcControlFlipper.setDisplayedChild(1);
    }

    public void toMainControl() {
        Animation in = AnimationUtils.loadAnimation(this, R.anim.slide_out_left);
        in.setFillAfter(true);
        ipcControlFlipper.setInAnimation(AnimationUtils.loadAnimation(this,
                R.anim.slide_in_right));
        ipcControlFlipper.setOutAnimation(AnimationUtils.loadAnimation(this,
                R.anim.slide_out_right));
        ipcControlFlipper.setDisplayedChild(0);
    }

    @Safer
    public boolean toMoveLeft(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                v.setAlpha(0.5f);
                device.submit(DeviceParmsBuilder
                        .newBuilder()
                        .cmd(HeartLaiCmd.CMD_CONTROL_DIRECTION)
                        .parm("direction", 2)
                        .build());
                break;
            case MotionEvent.ACTION_UP:
                v.setAlpha(1f);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_STOP_DIRECTION)
                        .build());
                break;
        }
        return true;
    }

    @Safer
    public boolean toMoveRight(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                v.setAlpha(0.5f);
                device.submit(DeviceParmsBuilder
                        .newBuilder()
                        .cmd(HeartLaiCmd.CMD_CONTROL_DIRECTION)
                        .parm("direction", 3)
                        .build());
                break;
            case MotionEvent.ACTION_UP:
                v.setAlpha(1f);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_STOP_DIRECTION)
                        .build());
                break;
        }
        return true;
    }

    @Safer
    public boolean toMoveUp(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                v.setAlpha(0.5f);
                device.submit(DeviceParmsBuilder
                        .newBuilder()
                        .cmd(HeartLaiCmd.CMD_CONTROL_DIRECTION)
                        .parm("direction", 0)
                        .build());
                break;
            case MotionEvent.ACTION_UP:
                v.setAlpha(1f);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_STOP_DIRECTION)
                        .build());
                break;
        }
        return true;
    }

    @Safer
    public boolean toMoveDown(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                v.setAlpha(0.5f);
                device.submit(DeviceParmsBuilder
                        .newBuilder()
                        .cmd(HeartLaiCmd.CMD_CONTROL_DIRECTION)
                        .parm("direction", 1)
                        .build());
                break;
            case MotionEvent.ACTION_UP:
                v.setAlpha(1f);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_STOP_DIRECTION)
                        .build());
                break;
        }
        return true;
    }

    @Safer
    public boolean toZoomIn(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                v.setAlpha(0.5f);
                timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        glViewZoomIn();
                    }
                }, 0, 200);
                break;
            case MotionEvent.ACTION_UP:
                v.setAlpha(1f);
                timer.cancel();
                timer = null;
                break;
        }
        return true;
    }

    @Safer
    public boolean toZoomOut(View v, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                v.setAlpha(0.5f);
                timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        glViewZoomOut();
                    }
                }, 0, 200);
                break;
            case MotionEvent.ACTION_UP:
                v.setAlpha(1f);
                timer.cancel();
                timer = null;
                break;
        }
        return true;
    }

    public void toSpeakLayout() {
        Animation in = AnimationUtils.loadAnimation(this, R.anim.slide_out_left);
        in.setFillAfter(true);
        ipcControlFlipper.setInAnimation(AnimationUtils.loadAnimation(this,
                R.anim.slide_in_left));
        ipcControlFlipper.setOutAnimation(AnimationUtils.loadAnimation(this,
                R.anim.slide_out_left));

//        跳转到指定的页面
        ipcControlFlipper.setDisplayedChild(2);
    }

    /**
     * 处理手机重力感应及屏幕旋转
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        final int orientation = newConfig.orientation;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    // 启动全屏
                    final DisplayMetrics dm = new DisplayMetrics();
                    getWindowManager().getDefaultDisplay().getMetrics(dm);
                    int dmWidth = dm.widthPixels;
                    int dmHeight = dm.heightPixels;
                    FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams) mMonitor
                            .getLayoutParams();
                    lp.width = dmWidth;
                    lp.height = dmHeight;
                    mMonitor.setLayoutParams(lp);
                    mMonitor.setX(0);
                    mMonitor.setY(0);
                    mTitleBar.setVisibility(View.GONE);
                    ipcControlFlipper.setVisibility(View.VISIBLE);
                } else if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                    // 退出全屏
                    final DisplayMetrics dm = new DisplayMetrics();
                    getWindowManager().getDefaultDisplay().getMetrics(dm);
                    int dmWidth = dm.widthPixels;
                    int dmHeight = dm.heightPixels;
                    FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams) mMonitor
                            .getLayoutParams();
                    lp.width = dmWidth;
                    lp.height = dmWidth * 3 / 4;
                    mMonitor.setLayoutParams(lp);
                    mMonitor.setX(0);
                    mMonitor.setY(0);
                    mTitleBar.setVisibility(View.VISIBLE);
                    if (currentPlayer.isConnected()) {
                        ipcControlFlipper.setVisibility(View.VISIBLE);
                    } else {
                        ipcControlFlipper.setVisibility(View.GONE);
                    }
                }
            }
        });

    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        toClose();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return true;
        }

        return super.onKeyDown(keyCode, event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayMotionDetectedRecordEvent(PlayMotionDetectedRecordEvent playMotionDetectedRecordEvent) {
        toClose();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(AppStatePreEvent ev) {
        if (ev.isBackground()) {
            closeSelf(true);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(HadLogoutPreEvent ev) {
        closeSelf(true);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSOSevent(SOSevent soSevent) {
        toClose();
    }

    @Override
    public void onCmdCallBack(String deviceID, String subCategory, String cmd, Map map) {
        DDLog.d(TAG, "onCmdCallBack: " + deviceID + " /cmd:" + cmd);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!deviceID.equals(device.getId())) {
                    return;
                }

                switch (cmd) {
                    case HeartLaiCmd.CMD_STATUS_CHANGE:
                        refreshVideoViewState(device);
                        break;
                    case HeartLaiCmd.CMD_DEL:
                        toClose();
                        break;
                    default:
                        break;
                }


            }
        });

    }

    private AlertDialogV2 offlineDialog;

    public void showDeviceOfflineDialog(Device device) {
        if (offlineDialog != null && offlineDialog.isShowing()) {
            return;
        }
        AlertDialogV2.Builder builder = AlertDialogV2.createBuilder(this)
                .setContent(this.getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setCancel(this.getResources().getString(R.string.cancel))
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        mVideoView.showLoading();

                        Map<String, Object> args = new HashMap<>();
                        args.put("cmd", HeartLaiCmd.CMD_DISCONNECT);
                        device.submit(args);

                        Map<String, Object> args1 = new HashMap<>();
                        args1.put("cmd", HeartLaiCmd.CMD_CONNECT);
                        args1.put("discardCache", true);
                        device.submit(args1);
                    }
                });

        if (LocalKey.ADMIN == HomeManager.getInstance().getCurrentHome().getLevel()) {
            builder.setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                    .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            EventBus.getDefault().post(new GoNetworkConfigFragmentEvent(device.getId()));
                            toClose();
                        }
                    });
        }

        offlineDialog = builder
                .preBuilder();
        offlineDialog.show();
    }

    private void hideDeviceOfflineDialog(Device device) {
        if (offlineDialog == null || !offlineDialog.isShowing()) {
            return;
        }
        offlineDialog.dismiss();
    }

    @Override
    public void onDeviceAdd(Device device) {

    }

    @Override
    public void onDeviceRemove(Device s) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (s.getId().equals(device.getId())) {
                    toClose();
                }
            }
        });
    }
}


