package com.dinsafer.module.ipc.heartlai;

import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.common.IPCManager;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.module.BaseFragmentActivity;
import com.dinsafer.module.ipc.heartlai.setting.DateVo;
import com.dinsafer.module.ipc.heartlai.setting.VideoVo;
import com.dinsafer.module.ipc.heartlai.widget.CalendarAdapter;
import com.dinsafer.module.ipc.heartlai.widget.DialogForCalendar;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.HeartLaiServiceManager;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DeviceParmsBuilder;
import com.githang.statusbar.StatusBarCompat;
import com.heartlai.ipc.BridgeService;
import com.heartlai.ipc.entity.CustomBuffer;
import com.heartlai.ipc.entity.CustomBufferData;
import com.heartlai.ipc.entity.CustomBufferHead;
import com.heartlai.ipc.interfaces.OnValueChangeListener;
import com.heartlai.ipc.utils.AudioPlayer;
import com.heartlai.ipc.utils.RecordInfo;
import com.heartlai.ipc.utils.ScreenUtil;
import com.heartlai.ipc.widget.MyLiveViewGLMonitor;
import com.heartlai.ipc.widget.VideoRulerView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 心赖录像全屏回播页
 *
 * <AUTHOR>
 * @date 2020/9/2 11:39 AM
 */
public class HeartLaiRecordPlayActivity extends BaseFragmentActivity
        implements BridgeService.VideoExitInterface, BridgeService.BackVideoTimeInterface,
        BridgeService.WindowRendering, BridgeService.PlayBackInterface,
        CalendarAdapter.OnItemClickListenr, View.OnTouchListener,
        MyLiveViewGLMonitor.OnfigCangeInterface, BridgeService.SHIXCOMMONInterface {

    private static final String TAG = HeartLaiRecordPlayActivity.class.getSimpleName();

    LinearLayout linView;
    TextView tvCurrentTime;
    VideoRulerView vrvRule;
    RelativeLayout linRule;
    SurfaceHolder playHolder;
    MyLiveViewGLMonitor mMonitor;
    private String id;
    private Device device;


    long startTime, endTime;
    String dateStringStart, dateStringEnd;
    DateVo vo;
    List<VideoVo> vos;
    List<RecordInfo> data = new ArrayList<>();
    Calendar calendar;

    int year;
    int month;
    int currentYear;
    int currentMonth;
    int selectYear;
    int selectMonth;
    int selectDay;
    List<DateVo> dates;
    int weekDay; // 获取day对应的星期几
    Calendar mCalendar;

    boolean isShowRule = true;
    boolean isShowTime = false;
    boolean isBarMoving = false;
    long BarMoveFinishCurrentTime = 0;  // 停止拖动时间轴的时间
    private String strFilePath;
    SimpleDateFormat sdf;
    boolean isOneStart = false;
    volatile boolean isCallBack; // 命令是否有返回
    volatile boolean isVideoTrue; // 视频时间戳是否正常
    volatile boolean isStartVideo = false;
    DialogForCalendar forCalendar;

    private byte[] videodata = null;
    private int videoDataLen = 0;
    private int nVideoWidth = 0;
    private int nVideoHeight = 0;
    boolean isOneShow = true;
    boolean bDisplayFinished = false;
    boolean isStartAudio = false;
    private MyHander myHander;

    private boolean mInitVideoRecordSuccess;
    ProgressDialog progressDialog;

    private CustomBuffer audioBuffer = null;
    private AudioPlayer audioPlayer = null;  // 音频 播放
    private static final int AUDIO_BUFFER_START_CODE = 0xff00ff;

    private AlertDialog mErrorDialog, mMessageDialog;

    private String uid;
    private String password;

    public static Intent getStartIntent(Context context, String cameraId) {
        Intent startIntent = new Intent(context, HeartLaiRecordPlayActivity.class);
        startIntent.putExtra("id", cameraId);
        return startIntent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: ");
        EventBus.getDefault().register(this);
        requestWindowFeature(Window.FEATURE_NO_TITLE);//隐藏标题
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);//设置全屏
        setContentView(R.layout.ipc_heart_lai_play_back_layout);
        StatusBarCompat.setStatusBarColor(this, Color.BLACK, false);
        __bindViews();
        __bindClicks();
        if (initPageArgsWithError()) {
            showToast(getString(R.string.empty_record_file));
            DDLog.e(TAG, "Init Activity start params ERROR.");
            return;
        }

        initView();
        initData();
    }

    private void __bindClicks() {
        findViewById(R.id.btn_calendar).setOnClickListener( v -> onClick(v));
        findViewById(R.id.btn_close).setOnClickListener( v -> onClick(v));
    }

    private void __bindViews() {
        linView = findViewById(R.id.lin_view);
        tvCurrentTime = findViewById(R.id.tv_current_time);
        vrvRule = findViewById(R.id.vrv_rule);
        linRule = findViewById(R.id.lin_rule);
        mMonitor = findViewById(R.id.gl_monitor);
    }

    /**
     * 初始化变量
     */
    @Override
    protected boolean initVariables() {
        return true;
    }

    /**
     * 初始化控件
     *
     * @param savedInstanceState
     */
    @Override
    protected void initViews(Bundle savedInstanceState) {

    }

    /**
     * 初始化数据
     */
    @Override
    protected void loadData() {

    }

    @Override
    protected void onDestroy() {
        DDLog.i(TAG, "onDestroy");
        EventBus.getDefault().unregister(this);
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
        HeartLaiServiceManager.getInstance().removeSHIXCOMMONInterface(this);
        BridgeService.removeVideoExitInterface(this);
//        BridgeService.addWindowRendering(this);
//        BridgeService.addBackVideoTimeInterface(this);
        mHandler.removeCallbacksAndMessages(null);
        if (null != mErrorDialog && mErrorDialog.isShowing()) {
            mErrorDialog.dismiss();
        }
        if (null != mMessageDialog && mMessageDialog.isShowing()) {
            mMessageDialog.dismiss();
        }
        super.onDestroy();
    }

    /**
     * 初始化页面参数
     */
    private boolean initPageArgsWithError() {
        DDLog.i(TAG, "initPageArgs");

        id = getIntent().getStringExtra("id");
        if (TextUtils.isEmpty(id)) {
            DDLog.e(TAG, "Camera id and CameraParamsVo can't be null either.");
            return true;
        }
        device = IPCManager.getInstance().getHeartLaiDeviceByID(id);
        return null == device;
    }

    public void initView() {
        playHolder = mMonitor.getHolder();
        playHolder.addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) {
            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
                DDLog.i(TAG, "surfaceDestroyed");
                stopListenAndVideo();
            }
        });

        DDLog.i(TAG, "initView");
//        mMonitor.setOnTouchListener(this);
//        mMonitor.setOnFigListener(this);
        mMonitor.setEnabled(false);
        mMonitor.setScreenSize(ScreenUtil.getScreenWidth(this), ScreenUtil.getScreenHeight(this));
    }

    public void initData() {
        DDLog.i(TAG, "initData");
        uid = DeviceHelper.getString(device, HeartLaiConstants.ATTR_UID, "");
        password = DeviceHelper.getString(device, HeartLaiConstants.ATTR_PASSWORD, "");

        BridgeService.addVideoExitInterface(this);
        HeartLaiServiceManager.getInstance().addSHIXCOMMONInterface(this);
        BridgeService.addWindowRendering(this);
        BridgeService.addBackVideoTimeInterface(this);

        initAudioPlayer();

        dates = new ArrayList<>();
        myHander = new MyHander(this);
        calendar = Calendar.getInstance();
        vo = new DateVo();
        vos = new ArrayList<>();
        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        checkSDCard();
        initVideoRule();

        forCalendar = DialogForCalendar.getInstance(this);
        forCalendar.addOnItemClickListener(this);
    }

    private void checkSDCard() {
        DDLog.i(TAG, "checkSDCard");
        if (device != null) {
            showTimeOutLoadinFramgmentWithErrorAlert();
            Map<String, Object> parms = new HashMap<>();
            parms.put("cmd", HeartLaiCmd.CMD_GET_SD_INFO);
            device.submit(parms);
        }
    }

    private void getVedioFile() {
        DDLog.i(TAG, "getVedioFile");
        device.submit(DeviceParmsBuilder.newBuilder()
                .cmd(HeartLaiCmd.CMD_GET_FILE_PARMS)
                .build());
        Date dt = new Date();
        calendar.setTime(dt);
        vo.setYear(calendar.get(Calendar.YEAR));
        vo.setMonth(calendar.get(Calendar.MONTH) + 1);
        vo.setDay(calendar.get(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        startTime = calendar.getTimeInMillis();
        endTime = dt.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateStringStart = sdf.format(startTime);
        dateStringEnd = sdf.format(endTime);
//        HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_GetVideoFiles(uid, password, dateStringStart, dateStringEnd), 0);
        device.submit(DeviceParmsBuilder.newBuilder()
                .cmd(HeartLaiCmd.CMD_GET_VIDEO)
                .parm("dateStringStart", dateStringStart)
                .parm("dateStringEnd", dateStringEnd)
                .build()
        );
    }

    private void initVideoRule() {
        DDLog.i(TAG, "initVideoRule");
        tvCurrentTime.setVisibility(View.VISIBLE);
        vrvRule.setSelectTime(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
        vrvRule.setmListener(new OnValueChangeListener() {
            @Override
            public void onValueChange(float value) {
                if (!mInitVideoRecordSuccess) {
                    DDLog.i(TAG, "onValueChange, mInitVideoRecordSuccess = FALSE.");
                    return;
                }

                BarMoveFinishCurrentTime = getVideoStartTime(data, vrvRule.getTimeInMillis(value));
                startTime = BarMoveFinishCurrentTime;
//                if (isShowTime) {
//                    tvCurrentTime.setVisibility(View.GONE);
//                    isShowTime = false;
//                }
                if (BarMoveFinishCurrentTime > 0
                        && !TextUtils.isEmpty(id)
                        && !TextUtils.isEmpty(strFilePath)) {
                    isCallBack = false;
                    isVideoTrue = false;
//                    HeartLaiJniClientProxy.startPlayBack(strDID, strFilePath, 100);
                    device.submit(DeviceParmsBuilder.newBuilder()
                            .cmd(HeartLaiCmd.CMD_START_PLAY_BACK)
                            .parm("strFilePath", strFilePath)
                            .parm("var2", 100)
                            .build());
//                    HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StartVideoOffset(uid, password, sdf.format(BarMoveFinishCurrentTime)), 0);
                    device.submit(DeviceParmsBuilder.newBuilder()
                            .cmd(HeartLaiCmd.CMD_SET_VIDEO_OFFSET)
                            .parm("BarMoveFinishCurrentTime", sdf.format(BarMoveFinishCurrentTime))
                            .build());

                }
                isBarMoving = false;
//                else {
//                    DialogLoading.getInstance().onDismiss();
//                    isBarMoving = false;
//                    isNewTime = false;
//                    Toast.makeText(PlayBackNewActivity.this, getResources().getString(R.string.current_has_no_video), Toast.LENGTH_SHORT).show();
//                }
            }

            @Override
            public void onMoving(String time) {
                if (!mInitVideoRecordSuccess) {
                    DDLog.i(TAG, "onMoving, mInitVideoRecordSuccess = FALSE.");
                    return;
                }
//                if (!isShowTime) {
//                    tvCurrentTime.setVisibility(View.VISIBLE);
//                    isShowTime = true;
//                }
                tvCurrentTime.setText(time);
                isBarMoving = true;
                if (!TextUtils.isEmpty(id)
                        && !TextUtils.isEmpty(strFilePath)) {
//                    HeartLaiJniClientProxy.startPlayBack(strDID, strFilePath, 100);
                    device.submit(DeviceParmsBuilder.newBuilder()
                            .cmd(HeartLaiCmd.CMD_START_PLAY_BACK)
                            .parm("strFilePath", strFilePath)
                            .parm("var2", 100)
                            .build());
                }

            }
        });
    }

    private long getVideoStartTime(List<RecordInfo> list, long currentTime) {
        DDLog.i(TAG, "getVideoStartTime");
        for (RecordInfo vo : list) {
            if (currentTime >= vo.getStartTime()
                    && currentTime < (vo.getEndTime())) {
                strFilePath = vo.getFileName();
                return currentTime;
            }
        }
        for (RecordInfo vo : list) {
            if (vo.getStartTime() > currentTime) {
                strFilePath = vo.getFileName();
                return vo.getStartTime() + 10000;
            }
        }
        return -1;
    }

    @Override
    public void onExit() {
        DDLog.i(TAG, "onExit");
        pop();
    }

    int newTime = 0;

    @Override
    public void callBackVideoTime(String did, int time) {
        long time1 = ((long) time) * ((long) 1000);
        String dateStringStart = sdf.format(time1);
        newTime = time;

        Message message = new Message();
        message.what = 3;
        message.obj = time1;
        mHandler.sendMessage(message);
    }

    @Override
    public void onRender() {
    }


//    private class surfaceCallback implements SurfaceHolder.Callback {
//        @Override
//        public void surfaceCreated(SurfaceHolder holder) {
//            new Thread() {
//                @Override
//                public void run() {
//                    try {
//                        Thread.sleep(500);
//                        HeartLaiJniClientProxy.setSurface(playHolder.getSurface());
////                            player.AudioPlayStart();
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }.start();
//        }
//
//        @Override
//        public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
//
//        }
//
//        @Override
//        public void surfaceDestroyed(SurfaceHolder holder) {
//            DDLog.i(TAG, "surfaceDestroyed");
//            stopListenAndVideo();
//        }
//    }

    public void stopListenAndVideo() {
        DDLog.i(TAG, "stopListenAndVideo");
        stopListen();
        stopVideo();
    }

    private void stopVideo() {
        DDLog.i(TAG, "stopVideo");
//        HeartLaiJniClientProxy.stopPlayBack(strDID);
        device.submit(DeviceParmsBuilder.newBuilder()
                .cmd(HeartLaiCmd.CMD_STOP_PLAY_BACK)
                .build());
//        HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StopVideoFiles(DeviceHelper.getString(device, HeartLaiConstants.ATTR_UID, ""),
//                DeviceHelper.getString(device, HeartLaiConstants.ATTR_PASSWORD, "")), 0);
        device.submit(DeviceParmsBuilder.newBuilder()
                .cmd(HeartLaiCmd.CMD_STOP_VIDEO)
                .build());
    }

    @SuppressLint("HandlerLeak")
    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        public void handleMessage(Message msg) {
            switch (msg.what) {
//                case 4545:
//                    layoutConnPrompt.setVisibility(View.GONE);
//                    break;
                case 3:
                    if (!isBarMoving && isShowRule) {
                        if ((long) msg.obj < endTime) {
                            vrvRule.setCurrentIime((Long) msg.obj);
                            startTime = (long) msg.obj;
                        }
                        String dateStringStart = sdf.format((long) msg.obj);
                        tvCurrentTime.setText(dateStringStart);
                    }
                    break;
                case 1024:
                    mInitVideoRecordSuccess = true;
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    forCalendar.setDate();
                    setDate();
                    break;
                case 5321:
                    if (vrvRule != null) {
                        vrvRule.setSelectTime(vo.getYear(), vo.getMonth(), vo.getDay());
                    }
                    startVideoFile();
                    setVideoTimeSlot();
                    break;
                case 4242:
//                    HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_Record_day(uid, password, 0), 0);
                    device.submit(DeviceParmsBuilder.newBuilder()
                            .cmd(HeartLaiCmd.CMD_GET_FILE_PARMS)
                            .build());
                    if (null == VideoVo.keys || 0 >= VideoVo.keys.size()) {
                        showToast(getString(R.string.empty_record_file));
                    } else {
                        DDLog.i(TAG, "有其他日期包含视频");
                    }
//                    Toast.makeText(getContext(), getResources().getString(R.string.device_has_no_record), Toast.LENGTH_SHORT).show();
//                    pop();
                    break;
                case 3020:
                    if (!TextUtils.isEmpty(id)) {
//                        HeartLaiJniClientProxy.stopPlayBack(strDID);
                        isStartVideo = false;
//                        HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StopVideoFiles(uid, password), 0);
                        device.submit(DeviceParmsBuilder.newBuilder()
                                .cmd(HeartLaiCmd.CMD_STOP_VIDEO)
                                .build());
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        if (!isStartVideo) {
                            isStartVideo = true;
                            if (TextUtils.isEmpty(strFilePath)) {
//                                Toast.makeText(getContext(), getResources().getString(R.string.device_has_no_record), Toast.LENGTH_SHORT).show();
                                showToast(getString(R.string.empty_record_file));
//                                pop();
                                return;
                            }
//                            HeartLaiJniClientProxy.startPlayBack(strDID, strFilePath, 0);
                            device.submit(DeviceParmsBuilder.newBuilder()
                                    .cmd(HeartLaiCmd.CMD_START_PLAY_BACK)
                                    .parm("strFilePath", strFilePath)
                                    .parm("var2", 0)
                                    .build());
//                            HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StartVideoFiles(uid, password, sdf.format(startTime)), 0);
                            device.submit(DeviceParmsBuilder.newBuilder()
                                    .cmd(HeartLaiCmd.CMD_START_VIDEO)
                                    .parm("startTime", sdf.format(startTime))
                                    .build());
                        }
                    }
                    break;
                case 3030:
//                    Toast.makeText(getContext(), getResources().getString(R.string.face_check_sd_status), Toast.LENGTH_SHORT).show();
                    showToast(getString(R.string.empty_record_file));
//                    pop();
                    break;
                case 3040:
                    if (!isStartVideo) {
                        isStartVideo = true;
                        if (TextUtils.isEmpty(strFilePath)) {
//                            Toast.makeText(getContext(), getResources().getString(R.string.device_has_no_record), Toast.LENGTH_SHORT).show();
                            showToast(getString(R.string.empty_record_file));
//                            pop();
                            return;
                        }
//                        HeartLaiJniClientProxy.startPlayBack(strDID, strFilePath, 0);
                        device.submit(DeviceParmsBuilder.newBuilder()
                                .cmd(HeartLaiCmd.CMD_START_PLAY_BACK)
                                .parm("strFilePath", strFilePath)
                                .parm("var2", 0)
                                .build());
//                        HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StartVideoFiles(uid, password, sdf.format(startTime)), 0);
                        device.submit(DeviceParmsBuilder.newBuilder()
                                .cmd(HeartLaiCmd.CMD_START_VIDEO)
                                .parm("startTime", sdf.format(startTime))
                                .build());
                    }
                    break;

                default:
                    break;
            }
        }
    };

    private void startVideoFile() {
        startListen();
        if (vos != null && vos.size() > 0) {
            if (!TextUtils.isEmpty(strFilePath)) {
//                HeartLaiJniClientProxy.startPlayBack(strDID, strFilePath, 100);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_START_PLAY_BACK)
                        .parm("strFilePath", strFilePath)
                        .parm("var2", 100)
                        .build());
            }
            strFilePath = vos.get(0).getFileName();
            isBarMoving = false;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            startTime = getStartTime(vos.get(0));
            if (TextUtils.isEmpty(strFilePath)) {
                showToast(getString(R.string.empty_record_file));
//                Toast.makeText(getContext(), getResources().getString(R.string.device_has_no_record), Toast.LENGTH_SHORT).show();
//                pop();
                return;
            }
            vrvRule.setCurrentIime(startTime);
            if (!isOneStart) {
//                HeartLaiJniClientProxy.stopPlayBack(strDID);
                isStartVideo = false;
//                HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StopVideoFiles(uid, password), 0);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_STOP_VIDEO)
                        .build());
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
//                HeartLaiJniClientProxy.startPlayBack(strDID, strFilePath, 0);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_START_PLAY_BACK)
                        .parm("strFilePath", strFilePath)
                        .parm("var2", 0)
                        .build());
//                HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StartVideoFiles(uid, password, sdf.format(startTime)), 0);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_START_VIDEO)
                        .parm("startTime", sdf.format(startTime))
                        .build());
                isStartVideo = true;
                isOneStart = true;
            } else {
                isCallBack = false;
                isVideoTrue = false;
//                HeartLaiJniClientProxy.startPlayBack(strDID, strFilePath, 100);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_START_PLAY_BACK)
                        .parm("strFilePath", strFilePath)
                        .parm("var2", 100)
                        .build());
//                HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_StartVideoOffset(uid, password, sdf.format(startTime)), 0);
                device.submit(DeviceParmsBuilder.newBuilder()
                        .cmd(HeartLaiCmd.CMD_SET_VIDEO_OFFSET)
                        .parm("BarMoveFinishCurrentTime", sdf.format(startTime))
                        .build());
            }
        } else {
            showToast(getString(R.string.empty_record_file));
//            Toast.makeText(getContext(), getResources().getString(R.string.current_has_no_video), Toast.LENGTH_SHORT).show();
        }
    }

    long getStartTime(VideoVo videoVo) {
        Calendar calendars = Calendar.getInstance();
        if (vo != null) {
            calendars.set(Calendar.YEAR, vo.getYear());
            calendars.set(Calendar.MONTH, vo.getMonth() - 1);
            calendars.set(Calendar.DAY_OF_MONTH, vo.getDay());
        }
        calendars.set(Calendar.HOUR_OF_DAY, 0);
        calendars.set(Calendar.MINUTE, 0);
        calendars.set(Calendar.SECOND, 2);
        startTime = calendars.getTimeInMillis();
        if (videoVo.getStartTime() > startTime) {

            startTime = videoVo.getStartTime();
        }

        return startTime;
    }

    /**
     * 设置视频区域
     */
    private void setVideoTimeSlot() {
        data.clear();
        for (VideoVo vo : vos) {
            RecordInfo info = new RecordInfo();
            info.setStartTime(vo.getStartTime());
            info.setEndTime(vo.getStartTime() + vo.getDuration());
            info.setFileName(vo.getFileName());
            if (vo.isAlarm()) {
                info.setType(2);
            } else {
                info.setType(1);
            }
            data.add(info);
        }
        vos.clear();
        vrvRule.setVedioArea(data);
    }

    private void pop() {
        DDLog.i(TAG, "pop");
        finish();
    }

    private void showToast(String msg) {
        DDLog.i(TAG, "showToast, msg: " + msg);
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                if (HeartLaiRecordPlayActivity.this.isFinishing()
                        || HeartLaiRecordPlayActivity.this.isDestroyed()) {
                    DDLog.e(TAG, "Activity is destroyed.");
                    return;
                }
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (null != mMessageDialog && mMessageDialog.isShowing()) {
                    mMessageDialog.dismiss();
                    mMessageDialog = null;
                }

                mMessageDialog = AlertDialog.createBuilder(HeartLaiRecordPlayActivity.this)
                        .setOk("OK")
                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                pop();
                            }
                        })
                        .setCanCancel(false)
                        .setAutoDissmiss(true)
                        .setContent(msg)
                        .preBuilder();
                mMessageDialog.show();
            }
        });
    }


    Handler timeoutHandler;

    public final void closeTimeOutLoadinFramgmentWithErrorAlert() {
        DDLog.i(TAG, "closeTimeOutLoadinFramgmentWithErrorAlert");
        hideLoadingProgress();
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
    }

    public final void showTimeOutLoadinFramgmentWithErrorAlert() {
        DDLog.i(TAG, "showTimeOutLoadinFramgmentWithErrorAlert");
        showLoadingProgress();
        if (timeoutHandler != null) {
            timeoutHandler.removeCallbacksAndMessages(null);
        }
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                hideLoadingProgress();
                showErrorToast();
            }
        }, LocalKey.TIMEOUT);
    }

    private void showLoadingProgress() {
        if (null == progressDialog) {
            progressDialog = ProgressDialog.show(this, null, null, true, false);
        } else {
            progressDialog.show();
        }
    }

    private void hideLoadingProgress() {
        if (progressDialog != null) {
            progressDialog.dismiss();
            progressDialog = null;
        }
    }

    public final void showErrorToast() {
        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (null == mErrorDialog) {
                    mErrorDialog = AlertDialog.createBuilder(HeartLaiRecordPlayActivity.this)
                            .setOk("OK")
                            .setContent(getResources().getString(R.string.failed_try_again))
                            .setOKListener(new AlertDialog.AlertOkClickCallback() {
                                @Override
                                public void onOkClick() {
                                    pop();
                                }
                            })
                            .preBuilder();
                }
                mErrorDialog.show();
            }
        });
    }

    /**
     * 初始化声音播放相关内容
     */
    private void initAudioPlayer() {
        DDLog.i(TAG, "initAudioPlayer");
        audioBuffer = new CustomBuffer();
        audioPlayer = new AudioPlayer(audioBuffer);
        BridgeService.setPlayBackInterface(this);
    }


    /**
     * 停止播放声音
     */
    public void stopListen() {
        DDLog.i(TAG, "stopListen");
        synchronized (this) {
            audioPlayer.AudioPlayStop();
            audioBuffer.ClearAll();
        }
    }

    /**
     * 开始播放声音
     */
    public void startListen() {
        DDLog.i(TAG, "startListen, Thread: " + Thread.currentThread().getName());
        synchronized (this) {
            audioBuffer.ClearAll();
            audioPlayer.AudioPlayStart();
        }
    }

    String tempStr = null;
    String tempStrs[];
    JSONObject object;

    @Override
    public void CallBackSHIXJasonCommon(String did, String jason) {
        DDLog.i(TAG, "CallBackSHIXJasonCommon, DID: " + did + ", jason: " + jason);
        try {
            object = new JSONObject(jason);
            if (object.has("cmd")) {
                if (object.getString("cmd").equals("116")) { // 获取播放文件
                    JSONObject dataJson;
                    dataJson = new JSONObject(jason);
                    int result = dataJson.getInt("result");
                    if (result >= 0) {
                        int ap_number = dataJson.getInt("count");
                        if (ap_number == 0) {
                            mHandler.sendEmptyMessage(4242);
                        } else {
                            vos.clear();
                            for (int i = 0; i < ap_number; i++) {
                                String key1 = "name[" + i + "]";
                                String fileName = dataJson.getString(key1);
                                tempStr = dataJson.getString(key1);
                                tempStr = tempStr.substring(0, tempStr.indexOf("."));
                                tempStrs = tempStr.split("_");
                                VideoVo vo = new VideoVo();
                                String year = tempStrs[0].substring(0, 4);
                                String month = tempStrs[0].substring(4, 6);
                                String day = tempStrs[0].substring(6, 8);
                                vo.setFileName(fileName);
                                vo.setStartTime(VideoVo.str2TimeStamp(tempStrs[0], tempStrs[1]));
                                vo.setDuration(Long.parseLong(tempStrs[2]) * 1000);
                                if (tempStrs[4].equals("m")) {
                                    vo.setAlarm(true);
                                } else {
                                    vo.setAlarm(false);
                                }
                                vos.add(vo);
                            }
                            mHandler.sendEmptyMessage(5321);
                        }
                    }
                } else if (object.getInt("cmd") == 115) {  // 获取 回放视频天数
                    int ap_number = object.getInt("count");
                    DDLog.i(TAG, "视频天数: " + ap_number);
                    VideoVo.clearDate();
                    for (int i = 0; i < ap_number; i++) {
                        String key1 = "date[" + i + "]";
                        tempStr = object.getString(key1);
                        VideoVo.addDate(tempStr);
                    }
                    mHandler.sendEmptyMessage(1024);
                } else if (object.getInt("cmd") == 118) {

                    if (object.getInt("result") == -1) {
                        isCallBack = true;
                        mHandler.sendEmptyMessage(3020);
                    }
                } else if (object.getInt("cmd") == 109) {  //  获取sd信息
                    if (object.has("status")) {
                        if (object.getInt("status") == 1) {
                            getVedioFile();
                        } else {
                            mHandler.sendEmptyMessage(3030);
                        }
                    }
                }
//                else if (object.getInt("cmd") == 120) {
//                    if (object.getInt("result") >= 0 && isRunning && !isStartVideo) {
//                        isStartVideo = true;
//                        mHandler.sendEmptyMessage(3040);
//                    }
//                }

            }
        } catch (JSONException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


    public void onClick(View v) {
        DDLog.i(TAG, "onClick, ID: " + v.getId());
        switch (v.getId()) {
            case R.id.lin_next_day:
                setData(true);
                break;
            case R.id.lin_previous_day:
                setData(false);
                break;
            case R.id.btn_calendar:
                if (forCalendar != null) {
                    forCalendar.showCalender();
                }
                break;
            case R.id.btn_close:
                pop();
                break;
        }
    }

    private void getVedioFile(DateVo vo) {
        DDLog.i(TAG, "getVedioFile, DateVo");
        calendar.set(Calendar.YEAR, vo.getYear());
        calendar.set(Calendar.MONTH, vo.getMonth() - 1);
        calendar.set(Calendar.DAY_OF_MONTH, vo.getDay());

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        startTime = calendar.getTimeInMillis();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        endTime = calendar.getTimeInMillis();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //System.out.println(sdf.format(date));
        String dateStringStart = sdf.format(startTime);
        String dateStringEnd = sdf.format(endTime);
//        mTvDay.setText(dateStringEnd.substring(0, 10));
//        HeartLaiJniClientProxy.transferMessage(strDID, CommonUtil.SHIX_GetVideoFiles(uid, password, dateStringStart, dateStringEnd), 0);
        device.submit(DeviceParmsBuilder.newBuilder()
                .cmd(HeartLaiCmd.CMD_GET_VIDEO)
                .parm("dateStringStart", dateStringStart)
                .parm("dateStringEnd", dateStringEnd)
                .build());
    }


    @Override
    public void onItemClick(DateVo vo) {
        DDLog.i(TAG, "onItemClick");
        if (vo.getYear() == this.vo.getYear() &&
                vo.getMonth() == this.vo.getMonth() &&
                vo.getDay() == this.vo.getDay()) {
            forCalendar.dismiss();
            return;
        }
        if (!vo.isHasVedio()) {
            showToast(getString(R.string.empty_record_file));
        } else {
            forCalendar.dismiss();
            selectYear = vo.getYear();
            selectMonth = vo.getMonth();
            selectDay = vo.getDay();
            forCalendar.setDate(selectYear, selectMonth, selectDay);
            setDate();
            this.vo = vo;
            getVedioFile(this.vo);
        }
    }


    public void setDate() {
        DDLog.i(TAG, "setDate");
        dates.clear();
        mCalendar = (Calendar) calendar.clone();
        mCalendar.set(Calendar.YEAR, selectYear);
        mCalendar.set(Calendar.MONTH, selectMonth - 1);
        int pyear = mCalendar.get(Calendar.YEAR);
        int pmonth = mCalendar.get(Calendar.MONTH) + 1;
        year = pyear;
        month = pmonth;

        // 将日期改为 1 号 以来确定1号为星期几
        mCalendar.set(Calendar.DAY_OF_MONTH, 1);
        weekDay = mCalendar.get(Calendar.DAY_OF_WEEK) - 1;
        mCalendar.add(Calendar.DAY_OF_MONTH, -weekDay);
        int maxNumber = 6 * 7;
        while (dates.size() < maxNumber) {
            int year = mCalendar.get(Calendar.YEAR);
            int month = mCalendar.get(Calendar.MONTH) + 1;
            int day = mCalendar.get(Calendar.DAY_OF_MONTH);
            DateVo vo = new DateVo();
            vo.setYear(year);
            vo.setMonth(month);
            vo.setDay(day);
            if (year == selectYear &&
                    month == selectMonth &&
                    day == selectDay) {
                vo.setSelect(true);
            } else {
                vo.setSelect(false);
            }
            vo.setHasVedio(getd(year, month, day));
            dates.add(vo);
            mCalendar.add(Calendar.DAY_OF_MONTH, 1);
        }

    }

    private void setData(boolean isNext) {
        DDLog.i(TAG, "setData, isNext: " + isNext);
        if (mCalendar == null) return;
        mCalendar.set(Calendar.YEAR, year);
        mCalendar.set(Calendar.MONTH, month - 1);
        if (isNext) {
            if (currentYear == year) {
                if (currentMonth > month) {
                    mCalendar.add(Calendar.MONTH, +1);
                } else {
                    return;
                }
            } else {
                mCalendar.add(Calendar.MONTH, +1);
            }
        } else {
            mCalendar.add(Calendar.MONTH, -1);
        }
        dates.clear();
        int pyear = mCalendar.get(Calendar.YEAR);
        int pmonth = mCalendar.get(Calendar.MONTH) + 1;
        year = pyear;
        month = pmonth;
        // 将日期改为 1 号 以来确定1号为星期几
        mCalendar.set(Calendar.DAY_OF_MONTH, 1);
        weekDay = mCalendar.get(Calendar.DAY_OF_WEEK) - 1;
        mCalendar.add(Calendar.DAY_OF_MONTH, -weekDay);
        int maxNumber = 6 * 7;
        while (dates.size() < maxNumber) {
            int year = mCalendar.get(Calendar.YEAR);
            int month = mCalendar.get(Calendar.MONTH) + 1;
            int day = mCalendar.get(Calendar.DAY_OF_MONTH);
            DateVo vo = new DateVo();
            vo.setYear(year);
            vo.setMonth(month);
            vo.setDay(day);
            if (year == selectYear &&
                    month == selectMonth &&
                    day == selectDay) {
                vo.setSelect(true);
            } else {
                vo.setSelect(false);
            }
            vo.setHasVedio(getd(year, month, day));
            dates.add(vo);
            mCalendar.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    private boolean getd(int year, int month, int day) {
        DDLog.i(TAG, "getd, year: " + year + ", month: " + month + ", day: " + day);
        for (String key : VideoVo.keys) {
            if (year == Integer.parseInt(key.substring(0, 4)) &&
                    month == Integer.parseInt(key.substring(5, 7)) &&
                    day == Integer.parseInt(key.substring(8, 10))) {
                return true;
            }
        }
//        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayMotionDetectedRecordEvent(PlayMotionDetectedRecordEvent playMotionDetectedRecordEvent) {
        finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayLiveEvent(CloseActivityEvent closeActivityEvent) {
        finish();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSOSevent(SOSevent soSevent) {
        finish();
    }

    /**
     * 声频数据回调
     */
    @Override
    public void callBackPlaybackAudioData(byte[] pcm, int len) {
        CustomBufferHead head = new CustomBufferHead();
        CustomBufferData data = new CustomBufferData();
        head.length = len;
        head.startcode = AUDIO_BUFFER_START_CODE;
        data.head = head;
        data.data = pcm;
        audioBuffer.addData(data);
    }

    /**
     * 视频数据回调
     */
    @Override
    public void callBackPlaybackVideoData(String did, byte[] videobuf, int h264Data, int len, int width, int height, int time) {
        if (!did.equals(DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, "")))
            return;
        callBackVideoTime(did, time);
        bDisplayFinished = false;

        videodata = videobuf;
        videoDataLen = len;
        Message msg = new Message();

        // mBmp = Bitmap.createBitmap(videobuf, width, height,
        // Bitmap.Config.RGB_565);
        // h264Data = 0;

        nVideoWidth = width;
        nVideoHeight = height;


        msg.what = 1;
        myHander.sendMessage(msg);
    }


    class MyHander extends Handler {
        WeakReference<FragmentActivity> reference;

        public MyHander(FragmentActivity activity) {
            reference = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (isOneShow) {
                isOneShow = false;

            }
            int width = getWindowManager().getDefaultDisplay().getWidth();
            int height = getWindowManager().getDefaultDisplay().getHeight();
            if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
//					 setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                        width, width * 2 / 3);
//                lp.gravity = Gravity.CENTER;
                mMonitor.setLayoutParams(lp);
            } else if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
//					 setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                        width, height);
                lp.gravity = Gravity.CENTER;
                mMonitor.setLayoutParams(lp);
            }

            mMonitor.setYuvFrameData(videodata, nVideoWidth, nVideoHeight);

            bDisplayFinished = true;
        }
    }


    private boolean isDown = false;
    private boolean isSecondDown = false;
    private float x1 = 0;
    private float x2 = 0;
    private float y1 = 0;
    private float y2 = 0;
    boolean isHD = false;
    int xlenOld;
    int ylenOld;
    float lastX;
    float lastY;
    double nLenStart = 0;
    private float action_down_x;
    private float action_down_y;
    float move_x;
    float move_y;
    boolean isPort = false;


    public boolean onTouch(View view, MotionEvent event) {

        if (view.getId() == R.id.gl_monitor) {

            int nCnt = event.getPointerCount();

            // int n = event.getAction();
            if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN
                    && 2 == nCnt)// <span
            // style="color:#ff0000;">2表示两个手指</span>
            {

                mMonitor.setTouchMove(2);
                // mMonitor.setState(3);
                for (int i = 0; i < nCnt; i++) {
                    float x = event.getX(i);
                    float y = event.getY(i);
                    Point pt = new Point((int) x, (int) y);
                }

                xlenOld = Math.abs((int) event.getX(0) - (int) event.getX(1));
                ylenOld = Math.abs((int) event.getY(0) - (int) event.getY(1));

                // HiLog.e("event.getX(0):"+(int)event.getX(0)+"(int)event.getX(1):"+(int)event.getX(1));

                nLenStart = Math.sqrt((double) xlenOld * xlenOld
                        + (double) ylenOld * ylenOld);

            } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE
                    && 2 == nCnt) {


                mMonitor.setTouchMove(2);
                // mMonitor.setState(3);
                for (int i = 0; i < nCnt; i++) {
                    float x = event.getX(i);
                    float y = event.getY(i);

                    Point pt = new Point((int) x, (int) y);

                }

                int xlen = Math.abs((int) event.getX(0) - (int) event.getX(1));
                int ylen = Math.abs((int) event.getY(0) - (int) event.getY(1));

                int moveX = Math.abs(xlen - xlenOld);
                int moveY = Math.abs(ylen - ylenOld);

                double nLenEnd = Math.sqrt((double) xlen * xlen + (double) ylen
                        * ylen);
                if (moveX < 20 && moveY < 20) {
                    return false;
                }

                if (!isPort) {
                    if (nLenEnd > nLenStart) {
                        resetMonitorSize(true, nLenEnd);
                    } else {
                        resetMonitorSize(false, nLenEnd);
                    }
                }

                xlenOld = xlen;
                ylenOld = ylen;
                nLenStart = nLenEnd;

                return true;
            } else if (nCnt == 1) {

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        action_down_x = event.getRawX();
                        action_down_y = event.getRawY();

                        lastX = action_down_x;
                        lastY = action_down_y;

                        x1 = event.getX();
                        y1 = event.getY();

                        // HiLog.e("ACTION_DOWN");
                        mMonitor.setTouchMove(0);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (!isDown) {
                            x1 = event.getX();
                            y1 = event.getY();
                            isDown = true;
                        }
                        x2 = event.getX();
                        y2 = event.getY();
                        if (mMonitor.getTouchMove() != 0)
                            break;
                        move_x = event.getRawX();
                        move_y = event.getRawY();


                        if (Math.abs(move_x - action_down_x) > 40
                                || Math.abs(move_y - action_down_y) > 40) {
                            mMonitor.setTouchMove(1);
                            // HiLog.e("ACTION_MOVE");
                        }

                        break;
                    case MotionEvent.ACTION_UP: {
                        x2 = event.getX();
                        y2 = event.getY();

                        if (Math.abs((x1 - x2)) < 50 && Math.abs((y1 - y2)) < 50) {

                            isSecondDown = false;
                        } else {
                        }
                        x1 = 0;
                        x2 = 0;
                        y1 = 0;
                        y2 = 0;
                        isDown = false;

                        if (mMonitor.getTouchMove() != 0) {
                            break;
                        }


                        break;
                    }
                    default:
                        break;
                }
            }

            return false;
        }
//        if (event.getAction() == MotionEvent.ACTION_DOWN){
//            StartTalk();
//
//        }else if (event.getAction() == MotionEvent.ACTION_UP){
//            StopTalk();
//        }
        return false;
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    @Override
    public void CallBackOnfigStatu(int var1) {

    }


    int moveX;
    int moveY;

    private void resetMonitorSize(boolean large, double move) {

        if (mMonitor.height == 0 && mMonitor.width == 0) {
            initMatrix((int) mMonitor.screen_width,
                    (int) mMonitor.screen_height);

        }


        if (move == 0) {
            mMonitor.setState(0);
            mMonitor.setMatrix(0, 0, (int) mMonitor.screen_width,
                    (int) mMonitor.screen_height);
            return;
        }


        moveX = (int) (move / 2);
        moveY = (int) ((move * mMonitor.screen_height / mMonitor.screen_width) / 2);

        if (large) {

            if (mMonitor.width <= 2 * mMonitor.screen_width
                    && mMonitor.height <= 2 * mMonitor.screen_height) {

                mMonitor.left -= (moveX / 2);
                mMonitor.bottom -= (moveY / 2);
                mMonitor.width += (moveX);
                mMonitor.height += (moveY);
            }
        } else {

            mMonitor.left += (moveX / 2);
            mMonitor.bottom += (moveY / 2);
            mMonitor.width -= (moveX);
            mMonitor.height -= (moveY);
        }

        if (mMonitor.left > 0 || mMonitor.width < (int) mMonitor.screen_width
                || mMonitor.height < (int) mMonitor.screen_height
                || mMonitor.bottom > 0) {
            initMatrix((int) mMonitor.screen_width,
                    (int) mMonitor.screen_height);
        }


        if (mMonitor.width > (int) mMonitor.screen_width) {
            mMonitor.setState(1);
        } else {
            mMonitor.setState(0);
        }

        mMonitor.setMatrix(mMonitor.left, mMonitor.bottom, mMonitor.width,
                mMonitor.height);

    }

    private void initMatrix(int screen_width, int screen_height) {
        mMonitor.left = 0;
        mMonitor.bottom = 0;

        mMonitor.width = screen_width;
        mMonitor.height = screen_height;
    }


}
