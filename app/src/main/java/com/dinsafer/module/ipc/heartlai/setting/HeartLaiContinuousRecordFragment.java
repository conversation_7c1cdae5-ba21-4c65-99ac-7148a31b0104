package com.dinsafer.module.ipc.heartlai.setting;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentHeartlaiContinuousRecordBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.ui.CircleSeekBar;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.Local;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/11/18
 */
public class HeartLaiContinuousRecordFragment extends MyBaseFragment<FragmentHeartlaiContinuousRecordBinding> implements IDeviceCallBack {

    private Device device;
    private int isEnableRecord = 0;
    private int record_start_min = 0;
    private int record_stop_min = 0;
    private int record_start_hour = 0;
    private int record_stop_hour = 0;
    private AtomicInteger mCount = new AtomicInteger(2);
    private boolean isChangeSetting = false;

    public static HeartLaiContinuousRecordFragment newInstance(String id) {
        Bundle args = new Bundle();
        args.putString("id", id);
        HeartLaiContinuousRecordFragment fragment = new HeartLaiContinuousRecordFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_heartlai_continuous_record;
    }

    @Override
    public void initData() {
        super.initData();

        device = IPCManager.getInstance().getHeartLaiDeviceByID(getArguments().getString("id"));
        if (device == null) {
            removeSelf();
            return;
        }

        device.registerDeviceCallBack(this);

        mBinding.tvRecordingTitle.setVisibility(View.GONE);
        mBinding.llFullDay.setVisibility(View.GONE);

//        get record detail
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", HeartLaiCmd.CMD_GET_RECORD_CONFIG);
        device.submit(parms);

        mBinding.title.commonBarBack.setOnClickListener(v -> {
            if (isChangeSetting) {
                showNotSaveTip();
            } else {
                removeSelf();
            }
        });
        mBinding.title.commonBarTitle.setLocalText(getString(R.string.ipc_setting_continuous_recording));
        mBinding.title.commonBarRightIcon.setVisibility(View.GONE);
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.btnSave.setOnClickListener(v -> save());

        mBinding.tvContinuousRecording.setLocalText(getString(R.string.ipc_setting_continuous_recording));
        mBinding.tvRecordingTitle.setLocalText(getString(R.string.ipc_setting_recording_period));
        mBinding.tvMotionDetectFullDay.setLocalText(getString(R.string.full_day));

        mBinding.switchMotionDetectFullDay.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                if (isOn) {
                    mBinding.llTimeSetting.setVisibility(View.GONE);
                    record_start_hour = 0;
                    record_start_min = 0;
                    record_stop_hour = 24;
                    record_stop_min = 0;
                } else {
                    mBinding.llTimeSetting.setVisibility(View.VISIBLE);
                    record_start_hour = IPCSettingUtils.getHourFormCircleSeekBarIndex(mBinding.csb.getStartIndex());
                    record_start_min = IPCSettingUtils.getMinFormCircleSeekBarIndex(mBinding.csb.getStartIndex());
                    int endHour = IPCSettingUtils.getHourFormCircleSeekBarIndex(mBinding.csb.getEndIndex());
                    int endMin = IPCSettingUtils.getMinFormCircleSeekBarIndex(mBinding.csb.getEndIndex());
                    if (endHour == 0 && endMin == 0) {
                        endHour = 24;
                    }
                    record_stop_hour = endHour;
                    record_stop_min = endMin;

                }
                Log.d(TAG, "onStateSwitched-->" + record_start_hour + ":" + record_start_min + "~~" + record_stop_hour + ":" + record_stop_min);
            }
        });

        IPCSettingUtils.initCommonStyleCircleSeekBar(getContext(), mBinding.csb);
        mBinding.csb.setOnCircleSeekBarChangeListener(new CircleSeekBar.OnCircleSeekBarChangeListener() {
            @Override
            public void onSeekProgressChange(int startIndex, int endIndex) {
                Log.d("CircleSeekBar", "onSeekProgressChange--> startIndex:" + startIndex + " /endIndex:" + endIndex);
                isChangeSetting = true;
                mBinding.tvRecordingPeriodTime.setText(IPCSettingUtils.getFormatPeriod(getContext(), startIndex, endIndex));
                record_start_hour = IPCSettingUtils.getHourFormCircleSeekBarIndex(startIndex);
                record_start_min = IPCSettingUtils.getMinFormCircleSeekBarIndex(startIndex);
                int endHour = IPCSettingUtils.getHourFormCircleSeekBarIndex(endIndex);
                int endMin = IPCSettingUtils.getMinFormCircleSeekBarIndex(endIndex);
                if (endHour == 0 && endMin == 0) {
                    endHour = 24;
                }
                record_stop_hour = endHour;
                record_stop_min = endMin;
            }
        });

        mBinding.switchContinuousRecording.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                isChangeSetting = true;
                if (isOn) {
                    isEnableRecord = 1;
                    mBinding.tvRecordingTitle.setVisibility(View.VISIBLE);
                    mBinding.llFullDay.setVisibility(View.VISIBLE);
                    if (record_start_hour == 0 && record_start_min == 0
                            && record_stop_hour == 24 && record_stop_min == 0) {
                        mBinding.switchMotionDetectFullDay.setOn(true);
                        mBinding.llTimeSetting.setVisibility(View.GONE);
                    } else {
                        mBinding.switchMotionDetectFullDay.setOn(false);
                        mBinding.llTimeSetting.setVisibility(View.VISIBLE);
                    }
                } else {
                    isEnableRecord = 0;
                    mBinding.llTimeSetting.setVisibility(View.GONE);
                    mBinding.tvRecordingTitle.setVisibility(View.GONE);
                    mBinding.llFullDay.setVisibility(View.GONE);
                }
            }
        });
        mBinding.sv.setInteracpt(true, false);

    }

    private void save() {
        mCount.set(2);
        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", HeartLaiCmd.CMD_CONFIG_RECORD);
        parms.put("pro", "set_videorecord");
        parms.put("start_min", record_start_min);
        parms.put("stop_min", record_stop_min);
        parms.put("start_hour", record_start_hour);
        parms.put("stop_hour", record_stop_hour);
        parms.put("user", DeviceHelper.getString(device, HeartLaiConstants.ATTR_UID, ""));
        parms.put("pwd", DeviceHelper.getString(device, HeartLaiConstants.ATTR_PASSWORD, ""));
        parms.put("chno", 0);
        parms.put("enable", isEnableRecord);
        device.submit(parms);
        showTimeOutLoadinFramgmentWithErrorAlert();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (device != null) {
            device.unregisterDeviceCallBack(this);
        }
    }

    private void updataRecord() {
        if (isEnableRecord == 1) {
            mBinding.switchContinuousRecording.setOn(true);
            mBinding.tvRecordingTitle.setVisibility(View.VISIBLE);
            mBinding.llFullDay.setVisibility(View.VISIBLE);
            if (record_start_hour == 0 && record_start_min == 0
                    && record_stop_hour == 24 && record_stop_min == 0) {
                mBinding.switchMotionDetectFullDay.setOn(true);
                mBinding.llTimeSetting.setVisibility(View.GONE);
            } else {
                mBinding.switchMotionDetectFullDay.setOn(false);
                mBinding.llTimeSetting.setVisibility(View.VISIBLE);
            }
        } else {
            mBinding.switchContinuousRecording.setOn(false);
            mBinding.llTimeSetting.setVisibility(View.GONE);
            mBinding.tvRecordingTitle.setVisibility(View.GONE);
            mBinding.llFullDay.setVisibility(View.GONE);
        }

        int startIndex = record_start_hour * 6 + record_start_min / 10;
        int endIndex = record_stop_hour * 6 + record_stop_min / 10;

        mBinding.csb.setStartIndex(startIndex);
        mBinding.csb.setEndIndex(endIndex);

        mBinding.tvRecordingPeriodTime.setText(IPCSettingUtils.getFormatPeriod(getContext(), startIndex, endIndex));
    }

    private void showNotSaveTip() {
        AlertDialog dialog = null;
        dialog = AlertDialog.createBuilder(getContext())
                .setContent(Local.s(getString(R.string.ipc_setting_not_save_tip)))
                .setOk(getString(R.string.ok))
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    save();
                })
                .setCancelListener(() -> {
                    removeSelf();
                }).preBuilder();
        dialog.show();
    }

    @Override
    public boolean onBackPressed() {
        if (isChangeSetting) {
            showNotSaveTip();
            return true;
        }
        return super.onBackPressed();
    }

    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (device == null || TextUtils.isEmpty(id) || !id.equals(device.getId())) {
            return;
        }

        switch (cmd) {
            case HeartLaiCmd.CMD_GET_RECORD_CONFIG:
                if (((int) map.get("status")) == 1) {
                    try {
                        HashMap<String, Object> result = (HashMap<String, Object>) map.get("result");
                        isEnableRecord = (int) result.get("enable");
                        record_start_hour = (int) result.get("start_hour");
                        record_stop_hour = (int) result.get("stop_hour");
                        record_start_min = (int) result.get("start_min");
                        record_stop_min = (int) result.get("stop_min");
//                        if (record_stop_hour == 23 && record_stop_min == 59) {
//                            record_stop_hour = 24;
//                            record_stop_min = 0;
//                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            updataRecord();
                        }
                    });
                } else {

                }
                break;
            case HeartLaiCmd.CMD_CONFIG_RECORD:
                if (((int) map.get("status")) == 1) {
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
//                        defLoading();
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            getMainActivity().showTopToast(getString(R.string.success));
                            removeSelf();
                        }
                    });
                } else {

                }
                break;
            default:
                break;
        }


    }
}
