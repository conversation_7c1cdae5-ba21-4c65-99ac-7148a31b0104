package com.dinsafer.module.ipc.heartlai.setting;

import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentIpcAlertServiceSettingBinding;
import com.dinsafer.model.AlertServicePlanUpdateEvent;
import com.dinsafer.model.DeviceAlertServicePlanResponse;
import com.dinsafer.model.IPCAlertServiceSettingResponse;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.iap.IPCSubscriptionFragment;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.CircleSeekBar;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DeviceInfoHelper;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/11/20
 */
public class IPCAlertServiceSettingFragment extends MyBaseFragment<FragmentIpcAlertServiceSettingBinding> {
    private String ipc_id;
    private IPCAlertServiceSettingResponse.ResultBean ipcAlertServiceSetting;
    private String alertCondition;
    private String alertMode;

    private boolean isChangeSetting = false;
    private String[] alertModeOptions;

    public static IPCAlertServiceSettingFragment newInstance(String ipc_id, IPCAlertServiceSettingResponse.ResultBean ipcAlertServiceSetting) {
        Bundle args = new Bundle();
        args.putString("ipc_id", ipc_id);
        args.putSerializable("ipcAlertServiceSetting", ipcAlertServiceSetting);
        IPCAlertServiceSettingFragment fragment = new IPCAlertServiceSettingFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ipc_alert_service_setting;
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);

        ipcAlertServiceSetting = (IPCAlertServiceSettingResponse.ResultBean) getArguments().getSerializable("ipcAlertServiceSetting");
        if (ipcAlertServiceSetting == null) {
            removeSelf();
            return;
        }
        ipc_id = getArguments().getString("ipc_id");

        mBinding.title.commonBarBack.setOnClickListener(v -> {
            if (isChangeSetting) {
                showNotSaveTip();
            } else {
                removeSelf();
            }
        });
        mBinding.title.commonBarTitle.setLocalText(getString(R.string.ipc_settting_service_setting));
        mBinding.title.commonBarRightIcon.setVisibility(View.GONE);
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.btnSave.setOnClickListener(v -> save());

        mBinding.tvAlertCondition.setLocalText(getString(R.string.ipc_setting_alert_conditions));
        mBinding.tvAlertCondition.setOnClickListener(v -> showConditionOption());
        mBinding.tvAlertConditionNumber.setOnClickListener(v -> showConditionOption());
        mBinding.tvAlertMode.setLocalText(getString(R.string.ipc_setting_alert_mode));
        mBinding.tvAlertMode.setOnClickListener(v -> showAlertModeOption());
        mBinding.tvAlertModeNumber.setOnClickListener(v -> showAlertModeOption());

        alertModeOptions = new String[]{Local.s(getResources().getString(R.string.ipc_setting_alert_mode_normal)),
                Local.s(getResources().getString(R.string.ipc_setting_alert_mode_no_alert))};

        IPCSettingUtils.initCommonStyleCircleSeekBar(getContext(), mBinding.csb);
        mBinding.csb.setOnCircleSeekBarChangeListener(new CircleSeekBar.OnCircleSeekBarChangeListener() {
            @Override
            public void onSeekProgressChange(int startIndex, int endIndex) {
//                Log.d("CircleSeekBar", "onSeekProgressChange--> startIndex:" + startIndex + " /endIndex:" + endIndex);
                isChangeSetting = true;
                mBinding.tvRecordingPeriodTime.setText(IPCSettingUtils.getFormatPeriod(getContext(), startIndex, endIndex));
            }
        });

        update(ipcAlertServiceSetting);
    }

    private void update(IPCAlertServiceSettingResponse.ResultBean ipcAlertServiceSetting) {
        IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean ipc_service = ipcAlertServiceSetting.getIpc_service();

        if (IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean.ALERT_MODE_ONLY_BACKUP.equals(ipc_service.getAlert_mode())) {
            alertMode = IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean.ALERT_MODE_ONLY_BACKUP;
            mBinding.tvAlertModeNumber.setText(alertModeOptions[1]);
        } else {
            alertMode = ipc_service.getAlert_mode();
            mBinding.tvAlertModeNumber.setText(alertModeOptions[0]);
        }

        if (IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean.ALERT_CONDITION_TIME.equals(ipc_service.getAlert_conditions())) {
            changeToConditionByTime();
            int startIndex = ipc_service.getStart_hour() * 6 + ipc_service.getStart_min() / 10;
            int endIndex = ipc_service.getEnd_hour() * 6 + ipc_service.getEnd_min() / 10;
            mBinding.tvRecordingPeriodTime.setText(IPCSettingUtils.getFormatPeriod(getContext(), startIndex, endIndex));
            mBinding.csb.setStartIndex(startIndex);
            mBinding.csb.setEndIndex(endIndex);
        } else {
            changeToConditionByDeviceStatus();
            mBinding.cbIpcSettingArm.setChecked(ipc_service.getArm());
            mBinding.cbIpcSettingDisarm.setChecked(ipc_service.getDisarm());
            mBinding.cbIpcSettingHomearm.setChecked(ipc_service.getHomearm());
            mBinding.cbIpcSettingSos.setChecked(ipc_service.getSos());
        }

        updatSubscriptionInfo();
    }

    private void updatSubscriptionInfo() {
        DeviceAlertServicePlanResponse.AlertServicePlanInfo alertServicePlanInfo = CommonDataUtil.getInstance().getAlertServicePlanInfo();
        if (alertServicePlanInfo == null || alertServicePlanInfo.getSubscription() == null) {
            return;
        }
        mBinding.layoutAlertService.tvCurrentSubsPlan.setCompoundDrawablesWithIntrinsicBounds(IPCAlertServiceUtil.getVIPLevelIcon(alertServicePlanInfo.getCurrentVIP()), 0, 0, 0);
        mBinding.layoutAlertService.tvCurrentSubsPlanDes.setText(alertServicePlanInfo.getSubscription().getPlanDes(getContext()));
        mBinding.layoutAlertService.tvCurrentSubsRemainCount.setText(String.valueOf(alertServicePlanInfo.getTotalRemain()));
        mBinding.layoutAlertService.tvCurrentSubsRemain.setLocalText(getString(R.string.ipc_subscription_remainder));
        if (alertServicePlanInfo.getSubscription().isHasSubs()) {
            mBinding.layoutAlertService.btnChangeSubsPlan.setLocalText(getString(R.string.ipc_subscription_more));
        } else {
            mBinding.layoutAlertService.btnChangeSubsPlan.setLocalText(getString(R.string.ipc_subscription_upgrade_service));
        }
        mBinding.layoutAlertService.btnChangeSubsPlan.setOnClickListener(v -> getDelegateActivity().addCommonFragment(IPCSubscriptionFragment.newInstance()));
    }

    private void changeToConditionByDeviceStatus() {
        alertCondition = IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean.ALERT_CONDITION_DEVICE_STATUS;
        mBinding.tvConditionTip.setLocalText(getString(R.string.ipc_setting_alert_conditions_follow_device_tip));
        mBinding.tvIpcSetttingArm.setLocalText(getString(R.string.toolbar_arm_text));
        mBinding.tvIpcSetttingDisarm.setLocalText(getString(R.string.toolbar_disarm_text));
        mBinding.tvIpcSetttingHomearm.setLocalText(getString(R.string.toolbar_homearm_text));
        mBinding.tvIpcSetttingSos.setLocalText(getString(R.string.main_section_sos));
        mBinding.tvAlertConditionNumber.setLocalText(getString(R.string.ipc_setting_alert_conditions_follow_device));

        mBinding.llConditionByDeviceStatus.setVisibility(View.VISIBLE);
        mBinding.llConditionByTime.setVisibility(View.GONE);

//        mBinding.tvIpcSetttingArm.setOnClickListener(v -> {
//            isChangeSetting = true;
//            mBinding.cbIpcSettingArm.setChecked(!mBinding.cbIpcSettingArm.isChecked());
//        });
        mBinding.tvIpcSetttingDisarm.setOnClickListener(v -> {
            isChangeSetting = true;
            mBinding.cbIpcSettingDisarm.setChecked(!mBinding.cbIpcSettingDisarm.isChecked());
        });
        mBinding.tvIpcSetttingHomearm.setOnClickListener(v -> {
            isChangeSetting = true;
            mBinding.cbIpcSettingHomearm.setChecked(!mBinding.cbIpcSettingHomearm.isChecked());
        });
//        mBinding.tvIpcSetttingSos.setOnClickListener(v -> mBinding.cbIpcSettingSos.setChecked(!mBinding.cbIpcSettingSos.isChecked()));

//        mBinding.cbIpcSettingArm.setOnClickListener(v -> isChangeSetting = true);
        mBinding.cbIpcSettingDisarm.setOnClickListener(v -> isChangeSetting = true);
        mBinding.cbIpcSettingHomearm.setOnClickListener(v -> isChangeSetting = true);
//        mBinding.cbIpcSettingSos.setOnClickListener(v -> isChangeSetting = true);

        mBinding.tvIpcSetttingArm.setClickable(false);
        mBinding.tvIpcSetttingArm.setFocusable(false);
        mBinding.cbIpcSettingArm.setClickable(false);
        mBinding.cbIpcSettingArm.setFocusable(false);

        mBinding.tvIpcSetttingSos.setClickable(false);
        mBinding.tvIpcSetttingSos.setFocusable(false);
        mBinding.cbIpcSettingSos.setClickable(false);
        mBinding.cbIpcSettingSos.setFocusable(false);

        mBinding.sv.setInteracpt(false, false);
    }

    private void changeToConditionByTime() {
        alertCondition = IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean.ALERT_CONDITION_TIME;
        mBinding.tvConditionTip.setLocalText(getString(R.string.ipc_setting_alert_conditions_by_time_tip));
        mBinding.tvRecordingPeriod.setLocalText(getString(R.string.ipc_settting_alert_period));
        mBinding.tvAlertConditionNumber.setLocalText(getString(R.string.ipc_setting_alert_conditions_by_time));
        mBinding.llConditionByDeviceStatus.setVisibility(View.GONE);
        mBinding.llConditionByTime.setVisibility(View.VISIBLE);
        mBinding.sv.setInteracpt(true, false);
    }


    private void showConditionOption() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getResources().getString(R.string.ipc_setting_alert_conditions_follow_device)),
                        Local.s(getResources().getString(R.string.ipc_setting_alert_conditions_by_time)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);
                        isChangeSetting = true;
                        if (index == 0) {
                            changeToConditionByDeviceStatus();
                        } else if (index == 1) {
                            changeToConditionByTime();
                        }
                    }
                }).show();
    }

    private void save() {
        IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean ipc_service = ipcAlertServiceSetting.getIpc_service();
        ipc_service.setStart_hour(IPCSettingUtils.getHourFormCircleSeekBarIndex(mBinding.csb.getStartIndex()));
        ipc_service.setStart_min(IPCSettingUtils.getMinFormCircleSeekBarIndex(mBinding.csb.getStartIndex()));
        ipc_service.setEnd_hour(IPCSettingUtils.getHourFormCircleSeekBarIndex(mBinding.csb.getEndIndex()));
        ipc_service.setEnd_min(IPCSettingUtils.getMinFormCircleSeekBarIndex(mBinding.csb.getEndIndex()));
        if (ipc_service.getEnd_hour() == 0 && ipc_service.getEnd_min() == 0) {
            ipc_service.setEnd_hour(24);
        }

        ipc_service.setArm(mBinding.cbIpcSettingArm.isChecked());
        ipc_service.setDisarm(mBinding.cbIpcSettingDisarm.isChecked());
        ipc_service.setHomearm(mBinding.cbIpcSettingHomearm.isChecked());
        ipc_service.setSos(mBinding.cbIpcSettingSos.isChecked());

        showTimeOutLoadinFramgmentWithErrorAlert();
        DinsafeAPI.getApi().modifyIPCAlertServiceSettingDetail(DeviceInfoHelper.getInstance().getCurrentDeviceInfo().getToken(),
                ipc_id, alertCondition, alertMode, ipc_service.getArm(), ipc_service.getDisarm(),
                ipc_service.getHomearm(), ipc_service.getSos(), ipc_service.getStart_hour(),
                ipc_service.getStart_min(), ipc_service.getEnd_hour(), ipc_service.getEnd_min())
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        getMainActivity().showTopToast(getString(R.string.success));
                        if (response.isSuccessful()) {
                            if (ipcAlertServiceSetting != null) {
                                ipcAlertServiceSetting.getIpc_service().setAlert_conditions(alertCondition);
                                ipcAlertServiceSetting.getIpc_service().setAlert_mode(alertMode);
                            }
                            removeSelf();
                        } else {
                            showErrorToast();
                        }
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        showErrorToast();
                    }
                });

    }

    private void showNotSaveTip() {
        AlertDialog dialog = null;
        dialog = AlertDialog.createBuilder(getContext())
                .setContent(Local.s(getString(R.string.ipc_setting_not_save_tip)))
                .setOk(getString(R.string.ok))
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    save();
                })
                .setCancelListener(() -> {
                    removeSelf();
                }).preBuilder();
        dialog.show();
    }

    private void showAlertModeOption() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(alertModeOptions)
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);
                        isChangeSetting = true;
                        mBinding.tvAlertModeNumber.setText(alertModeOptions[index]);
                        if (index == 1) {
                            alertMode = IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean.ALERT_MODE_ONLY_BACKUP;
                        } else {
                            alertMode = IPCAlertServiceSettingResponse.ResultBean.IpcServiceBean.ALERT_MODE_NORMAL;

                        }
                    }
                }).show();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AlertServicePlanUpdateEvent alertServicePlanUpdateEvent) {
        updatSubscriptionInfo();
    }

    @Override
    public boolean onBackPressed() {
        if (isChangeSetting) {
            showNotSaveTip();
            return true;
        }
        return super.onBackPressed();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
    }
}
