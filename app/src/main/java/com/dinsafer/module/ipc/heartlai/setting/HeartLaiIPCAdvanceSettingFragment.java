package com.dinsafer.module.ipc.heartlai.setting;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentHeartLaiIpcAdvanceSetttingBinding;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.model.event.NeedGetAllDeviceEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.iap.CloudStorageServiceHelper;
import com.dinsafer.module.iap.GetLastTriggeredTimeResponse;
import com.dinsafer.module.iap.GetServiceExpirationResponse;
import com.dinsafer.module.powerstation.PSKeyConstant;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.ApStepHeartLaiIpcFragment;
import com.dinsafer.module.settting.ui.EdittextDialog;
import com.dinsafer.module.settting.ui.FeedBackFragment;
import com.dinsafer.module.settting.ui.IPCListNewFragment;
import com.dinsafer.module.settting.ui.event.PluginDeleteEvent;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.add.NetworkConfigurer;
import com.dinsafer.module_heartlai.add.impl.HeartLaiNetworkConfigurer;
import com.dinsafer.module_heartlai.util.HeartLaiUtils;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


/**
 * 心赖IPC高级设置页
 */
public class HeartLaiIPCAdvanceSettingFragment extends MyBaseFragment<FragmentHeartLaiIpcAdvanceSetttingBinding> implements IDeviceCallBack {

    private static final String TAG = HeartLaiIPCAdvanceSettingFragment.class.getSimpleName();

    private Device device;

    public static HeartLaiIPCAdvanceSettingFragment newInstance(String id) {
        HeartLaiIPCAdvanceSettingFragment ipcSettingFragment = new HeartLaiIPCAdvanceSettingFragment();
        Bundle bundle = new Bundle();
        bundle.putString("id", id);
        ipcSettingFragment.setArguments(bundle);
        return ipcSettingFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_heart_lai_ipc_advance_settting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonTitleBar.commonBarTitle.setText(Local.s(getResources().getString(R.string.advanced_settings)));
        mBinding.commonTitleBar.commonBarBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeSelf();
            }
        });

        mBinding.commonTitleBar.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);
        mBinding.commonTitleBar.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.commonTitleBar.commonBarRightIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showMoreSettingDialog();
            }
        });

        mBinding.llPwd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toChangePassword();
            }
        });

        mBinding.ipcSettingIpcStatus.setLocalText(getString(R.string.ipc_status));
        mBinding.ipcSettingIpcPassword.setLocalText(getString(R.string.ipc_password));
        mBinding.ipcSettingIpcPid.setLocalText(getString(R.string.ipc_setting_ipc_pid));
        mBinding.tvDeviceCurrentNetworkTitle.setLocalText(getString(R.string.dscam_advance_current_network));
        mBinding.ipcSettingIpcIp.setLocalText(getString(R.string.ipc_setting_ipc_ip));

        //网络配置
        mBinding.ipcNetworkConfig.setLocalText(getString(R.string.modify_plugs_network_2));
        mBinding.ipcNetworkConfig.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getMainActivity().addCommonFragment(ApStepHeartLaiIpcFragment.newInstance(DeviceHelper.getString(device, HeartLaiConstants.ATTR_SOURCE_DATA, ""), false, HeartLaiUtils.getSourceDataIpcType(device)));
            }
        });

        //设置wifi
        mBinding.ipcWifiSetting.setLocalText("Wifi Setting");
        mBinding.ipcWifiSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                NetworkConfigurer networkConfigurer = new HeartLaiNetworkConfigurer();
                Bundle bundle = new Bundle();
                bundle.putString("id", device.getId());
                bundle.putString("home_id", HomeManager.getInstance().getCurrentHome().getHomeID());
                bundle.putString("data", DeviceHelper.getString(device, HeartLaiConstants.ATTR_SOURCE_DATA, "{}"));
                bundle.putBoolean("isAdd", false);
                bundle.putBoolean("isAutoDisconnectAp", true);
                networkConfigurer.setConfigParms(getContext(), bundle);

                getMainActivity().addCommonFragment(HeartLaiWifiListFragment.newInstance(DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""), true, false, false, networkConfigurer));
            }
        });

        //同步时区
        mBinding.syncTimezoneText.setLocalText("SYNC with phone's timezone");
        mBinding.syncTimezoneText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                syncTimezone();
            }
        });


    }

    private void getCurrentWifi() {
        mBinding.pbLoadingIpcWifi.setVisibility(View.VISIBLE);
        mBinding.tvDeviceCurrentNetwork.setVisibility(View.GONE);

        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", HeartLaiCmd.CMD_CURRENT_WIFI);
        device.submit(parms);
    }

    @Override
    public void initData() {
        super.initData();

        String id = getArguments().getString("id");
        device = IPCManager.getInstance().getHeartLaiDeviceByID(id);
        if (device == null) {
            removeSelf();
            return;
        }
        device.registerDeviceCallBack(this);

        if (HeartLaiUtils.isDeviceConnected(device)) {
            Map<String, Object> parms = new HashMap<>();
            parms.put("cmd", HeartLaiCmd.CMD_GET_SETTING);
            device.submit(parms);
            mBinding.pbLoadingIpcIp.setVisibility(View.VISIBLE);
            mBinding.ipcSettingIpcIpText.setVisibility(View.GONE);

            getCurrentWifi();
        } else {
            mBinding.llPwd.setAlpha(0.5f);
            mBinding.llPwd.setEnabled(false);
            mBinding.tvDeviceCurrentNetworkTitle.setCanTouch(false);
            mBinding.ipcSettingIpcIp.setCanTouch(false);
            mBinding.syncTimezoneText.setCanTouch(false);
            mBinding.ipcWifiSetting.setCanTouch(false);
        }

        updata();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (device != null) {
            device.unregisterDeviceCallBack(this);
        }
    }

    public void syncTimezone() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", HeartLaiCmd.CMD_SYNC_TIMEZONE);
        device.submit(parms);
    }

    private void showMsg(String msg) {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                showToast(msg);
            }
        });
    }

    public void updata() {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.ipcSettingIpcStatusText.setLocalText(HeartLaiUtils.isDeviceConnected(device) ? getString(R.string.ipc_online) : getString(R.string.ipc_off_online));
                mBinding.ipcPasswordText.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_PASSWORD, ""));
                mBinding.ipcSettingIpcPidText.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""));
                mBinding.ipcSettingIpcIpText.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_IP_ADDRESS, ""));
            }
        });
    }

    public void toChangePassword() {
        if (!HeartLaiUtils.isDeviceConnected(device)) {
            showToast(getResources().getString(R.string.ipc_off_online_toast));
            return;
        }
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultName(DeviceHelper.getString(device, HeartLaiConstants.ATTR_PASSWORD, ""))
                .setContent(getResources().getString(R.string.ipc_input_password))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
//                                            change ipc password
                        if (TextUtils.isEmpty(string)) {
                            return;
                        }
                        dialog.dismiss();
                        showBlueTimeOutLoadinFramgmentWithBack();
                        Map<String, Object> parms = new HashMap<>();
                        parms.put("cmd", HeartLaiCmd.CMD_CHANGE_PASSWORD);
                        parms.put("password", string);
                        device.submit(parms);
                    }
                })
                .preBuilder()
                .show();

    }


    private void showMoreSettingDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(Local.s(getString(R.string.delete)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            if (!CloudStorageServiceHelper.getInstance().isHeartLaiServiceOpen()) {
                                showConfirmDelDialog();
                            } else {
                                showTimeOutLoadinFramgmentWithErrorAlert();
                                checkCloudStorageServiceExpired();
                            }
                        }
                    }
                }).show();

    }

    private void checkCloudStorageServiceExpired() {
        final String id = DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, device.getId());
        DinsafeAPI.getApi().checkDeviceServiceRemaining(id)
                .enqueue(new Callback<GetServiceExpirationResponse>() {
                    @Override
                    public void onResponse(Call<GetServiceExpirationResponse> call, Response<GetServiceExpirationResponse> response) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (response.body() != null && response.body().getStatus() == 1) {
                            if (!response.body().isService_expiration()) {
                                showConfirmDelDialog2();
                            } else {
                                checkLastTriggeredTimeByIpcId();
                            }
                        } else {
                            checkLastTriggeredTimeByIpcId();
                        }
                    }

                    @Override
                    public void onFailure(Call<GetServiceExpirationResponse> call, Throwable t) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                    }
                });
    }


    private void checkLastTriggeredTimeByIpcId() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        DinsafeAPI.getApi().getLastTriggeredTimeByIpcId(device.getId())
                .enqueue(new Callback<GetLastTriggeredTimeResponse>() {
                    @Override
                    public void onResponse(Call<GetLastTriggeredTimeResponse> call, Response<GetLastTriggeredTimeResponse> response) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                        if (response.body() != null && response.body().getStatus() == 1 && null != response.body().getResult()) {
                            if (response.body().getResult().getTime() > 0) {
                                showHasMotionRecordDialog();
                            } else {
                                showConfirmDelDialog();
                            }
                        } else {
                            showConfirmDelDialog();
                        }
                    }

                    @Override
                    public void onFailure(Call<GetLastTriggeredTimeResponse> call, Throwable t) {
                        closeTimeOutLoadinFramgmentWithErrorAlert();
                    }
                });
    }

    private void showHasMotionRecordDialog() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.cancel))
                .setCancel(getResources().getString(R.string.delete))
                .setAutoDissmiss(true)
                .setContent(getResources().getString(R.string.delete_ipc_cloud_storage_has_motion_record))
                .setCancelListener(new AlertDialog.AlertCancelClickCallback() {
                    @Override
                    public void onClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        Map<String, Object> data = new HashMap<>();
                        data.put("cmd", HeartLaiCmd.CMD_DEL);
                        data.put("home_id", HomeManager.getInstance().getCurrentHome().getHomeID());
                        device.submit(data);
                    }
                })
                .preBuilder()
                .show();
    }


    private void showConfirmDelDialog() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        Map<String, Object> data = new HashMap<>();
                        data.put("cmd", HeartLaiCmd.CMD_DEL);
                        data.put("home_id", HomeManager.getInstance().getCurrentHome().getHomeID());
                        device.submit(data);
                    }
                })
                .preBuilder()
                .show();
    }

    private void showConfirmDelDialog2() {
        AlertDialogV2 alertDialogV2 = AlertDialogV2.createBuilder(getActivity())
                .setContent(this.getResources().getString(R.string.delete_ipc_cloud_storage_not_expire))
                .setOk(this.getResources().getString(R.string.cancel))
                .setOkV2(this.getResources().getString(R.string.transfer))
                .setCancel(this.getResources().getString(R.string.delete))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(Dialog dialog) {
                        dialog.dismiss();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        getDelegateActivity().addCommonFragment(FeedBackFragment.newInstance());
                    }
                })
                .preBuilder();
        alertDialogV2.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                alertDialogV2.dismiss();
                checkLastTriggeredTimeByIpcId();
            }
        });
        alertDialogV2.show();
    }

    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (device == null || !device.getId().equals(id) || TextUtils.isEmpty(cmd)) {
            return;
        }
        Log.d(TAG, "onCmdCallBack: " + cmd);
        Map<String, Object> result = (Map<String, Object>) MapUtils.get(map, PSKeyConstant.RESULT, null);
        switch (cmd) {
            case HeartLaiCmd.CMD_GET_SETTING:
                updata();
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mBinding.pbLoadingIpcIp.setVisibility(View.GONE);
                        mBinding.ipcSettingIpcIpText.setVisibility(View.VISIBLE);
                    }
                });
                break;
            case HeartLaiCmd.CMD_CURRENT_WIFI:
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mBinding.pbLoadingIpcWifi.setVisibility(View.GONE);
                        mBinding.tvDeviceCurrentNetwork.setVisibility(View.VISIBLE);
                        mBinding.tvDeviceCurrentNetwork.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_WIFI_SSID, ""));
                    }
                });
                break;

            case HeartLaiCmd.CMD_CHANGE_PASSWORD:
                if (((int) map.get("status")) == 1) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    mBinding.ipcPasswordText.setText(((String) map.get("pwd[0]")));
                } else {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                }
                break;

            case HeartLaiCmd.CMD_SYNC_TIMEZONE:
                if (((int) map.get("status")) == 1) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showMsg("Smart camera's timezone is synchronized with your smartphone");
                    removeSelf();
                } else {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                }
                break;

            case HeartLaiCmd.CMD_DEL:
                if (((int) map.get("status")) == 1) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    removeSelf();
                    EventBus.getDefault().post(new PluginDeleteEvent(device.getId()));
                } else {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    int originStatus = DeviceHelper.getInt(result, "originStatus", -1);
                    if (originStatus == -77) {
                        AlertDialog.createBuilder(getActivity())
                                .setOk(Local.s(getResources().getString(R.string.ok)))
                                .setContent(Local.s(getResources().getString(R.string.use_the_fol)))
                                .setOKListener(() -> {
                                    getDelegateActivity().removeAllCommonFragment();
                                    IPCManager.getInstance().removeDeletedDevice(device.getId());
                                    EventBus.getDefault().post(new NeedGetAllDeviceEvent(true, true, true, true));
                                }).preBuilder().show();
                    } else {
                        showErrorToast();
                    }
                }
                break;

            default:
                break;
        }
    }


}


