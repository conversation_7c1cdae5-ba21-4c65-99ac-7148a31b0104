package com.dinsafer.module.ipc.heartlai.setting;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 回放视频信息对象
 */
public class VideoVo implements Serializable {

    public static List<VideoVo> datas = new ArrayList<>();
    public static Set<String> keys = new HashSet<>();


    boolean isAlarm;
    long startTime;
    long duration;
    String fileName;


    public boolean isAlarm() {
        return isAlarm;
    }

    public void setAlarm(boolean alarm) {
        isAlarm = alarm;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public static void clearAll(){
        if (keys != null){
            keys.clear();
        }
        if (datas != null){
            datas.clear();
        }

    }
    public static void clearData(){
        if (datas != null){
            datas.clear();
        }
    }
    public static void clearDate(){
        if (keys != null){
            keys.clear();
        }
    }



    public static void addDate(String date){
        if (keys != null){
            if (!keys.contains(date)){
                keys.add(date);
            }
        }
    }

    public static void addData(VideoVo vo){
        if (datas != null){
            datas.add(vo);
        }
    }


    public static long str2TimeStamp(String day, String time){
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            Date date = format.parse(day+time);
            return date.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }
}
