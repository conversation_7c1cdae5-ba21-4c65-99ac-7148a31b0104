package com.dinsafer.module.ipc.heartlai.widget;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.graphics.Color;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.dinsafer.dinnet.R;
import com.dinsafer.module.ipc.heartlai.setting.DateVo;
import com.dinsafer.module.ipc.heartlai.setting.VideoVo;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;


public class DialogForCalendar implements View.OnClickListener {
    AlertDialog dialog;
    WindowManager.LayoutParams params;
    Calendar calendar;
    RecyclerView rcy_calendar;
    LinearLayout mLinNext;
    LinearLayout mLinPre;
    GridLayoutManager manager;
    TextView mTvYear;
    static Activity activity;
    static DialogForCalendar instance;
    CalendarAdapter adapter;
    View viewRoot;
    TextView mTvPreviousMonth;
    TextView mTvNextMonth;
    TextView mTvSunday;
    TextView mTvMonday;
    TextView mTvTuesday;
    TextView mTvWednesday;
    TextView mTvThursday;
    TextView mTvFriday;
    TextView mTvSaturday;

    int year;
    int month;
    int currentYear;
    int currentMonth;
    int selectYear;
    int selectMonth;
    int selectDay;
    List<DateVo> dates;
    int weekDay; // 获取day对应的星期几
    Calendar mCalendar;

    String strDID;
    private DialogForCalendar(Activity activity){
        this.activity = activity;
        initData();
    }

    public static DialogForCalendar getInstance(Activity activity){
        instance = new DialogForCalendar(activity);
        return instance;
    }

    void initData(){
        manager = new GridLayoutManager(activity,7);
        calendar = Calendar.getInstance();

        selectYear = calendar.get(Calendar.YEAR);
        selectMonth = calendar.get(Calendar.MONTH)+1;
        selectDay = calendar.get(Calendar.DAY_OF_MONTH);
        currentYear = selectYear;
        currentMonth = selectMonth;
        viewRoot = LayoutInflater.from(activity).inflate(R.layout.layout_dialog_calendar,null);
        adapter = new CalendarAdapter(activity);
        rcy_calendar = (RecyclerView) viewRoot.findViewById(R.id.rcv_calendar);
        mLinNext = (LinearLayout) viewRoot.findViewById(R.id.lin_next_day);
        mLinPre = (LinearLayout) viewRoot.findViewById(R.id.lin_previous_day);

        rcy_calendar.setLayoutManager(manager);
        rcy_calendar.setAdapter(adapter);
        mTvYear = (TextView) viewRoot.findViewById(R.id.tv_day_year);

        // 视图文本内容本地化
        mTvPreviousMonth = (TextView) viewRoot.findViewById(R.id.tv_previous_month);
        mTvNextMonth = (TextView) viewRoot.findViewById(R.id.tv_next_month);
        mTvSunday = (TextView) viewRoot.findViewById(R.id.tv_sunday);
        mTvMonday = (TextView) viewRoot.findViewById(R.id.tv_monday);
        mTvTuesday = (TextView) viewRoot.findViewById(R.id.tv_tuesday);
        mTvWednesday = (TextView) viewRoot.findViewById(R.id.tv_wednesday);
        mTvThursday = (TextView) viewRoot.findViewById(R.id.tv_thursday);
        mTvFriday = (TextView) viewRoot.findViewById(R.id.tv_firday);
        mTvSaturday = (TextView) viewRoot.findViewById(R.id.tv_saturday);

        mTvPreviousMonth.setText(Local.s(activity.getResources().getString(R.string.alarm_previous_day)));
        mTvNextMonth.setText(Local.s(activity.getResources().getString(R.string.alarm_next_day)));
        mTvSunday.setText(Local.s(activity.getResources().getString(R.string.sunday)));
        mTvMonday.setText(Local.s(activity.getResources().getString(R.string.monday)));
        mTvTuesday.setText(Local.s(activity.getResources().getString(R.string.tuesday)));
        mTvWednesday.setText(Local.s(activity.getResources().getString(R.string.wednesday)));
        mTvThursday.setText(Local.s(activity.getResources().getString(R.string.thursday)));
        mTvFriday.setText(Local.s(activity.getResources().getString(R.string.friday)));
        mTvSaturday.setText(Local.s(activity.getResources().getString(R.string.saturday)));

        dates = new ArrayList<>();
        dialog = new AlertDialog.Builder(activity).create();
        params = dialog.getWindow().getAttributes();
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;

        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                dialog = null;
                manager = null;

            }
        });
    }

    public void setStrDID(String strDID) {
        this.strDID = strDID;
    }

    public void showCalender(){

        if (mLinPre != null){
            mLinPre.setOnClickListener(this);
        }
        if (mLinNext != null){
            mLinNext.setOnClickListener(this);
        }

        if (dialog != null){
            dialog.show();
            dialog.getWindow().setAttributes(params);
            dialog.getWindow().getDecorView().setBackgroundColor(Color.parseColor("#00ffffff"));
        }
        if (viewRoot != null && !dialog.getWindow().hasChildren()){
            dialog.setContentView(viewRoot);
        }
    }

    public void dismiss(){
        if (dialog != null){
            if (dialog.isShowing()){
                dialog.dismiss();
            }
        }
    }
    public void addOnItemClickListener(final CalendarAdapter.OnItemClickListenr listenr){
        adapter.setListenr(new CalendarAdapter.OnItemClickListenr() {
            @Override
            public void onItemClick(DateVo vo) {
                if (vo.isHasVedio()) {
                    selectYear = vo.getYear();
                    selectMonth = vo.getMonth();
                    selectDay = vo.getDay();
                    setDate();
                    dismiss();
                    if (listenr != null) {
                        listenr.onItemClick(vo);
                    }
                }else {
                    Toast.makeText(activity,
                            Local.s(activity.getResources().getString(R.string.empty_record_file))
                            , Toast.LENGTH_SHORT).show();
                }
            }
        });
    }



    public void setDate(){
        dates.clear();
        mCalendar = (Calendar) calendar.clone();
        mCalendar.set(Calendar.YEAR,selectYear);
        mCalendar.set(Calendar.MONTH,selectMonth-1);
        int pyear = mCalendar.get(Calendar.YEAR);
        int pmonth = mCalendar.get(Calendar.MONTH)+1;
        year = pyear;
        month = pmonth;

        if (mTvYear != null){
            mTvYear.setText(pyear+"-"+ String.format("%02d",pmonth));
        }
        // 将日期改为 1 号 以来确定1号为星期几
        mCalendar.set(Calendar.DAY_OF_MONTH,1);
        weekDay =  mCalendar.get(Calendar.DAY_OF_WEEK)-1;
        mCalendar.add(Calendar.DAY_OF_MONTH,-weekDay);
        int maxNumber = 6*7;
        while(dates.size()<maxNumber){
            int year =  mCalendar.get(Calendar.YEAR);
            int month =  mCalendar.get(Calendar.MONTH)+1;
            int day =  mCalendar.get(Calendar.DAY_OF_MONTH);
            DateVo vo = new DateVo();
            vo.setYear(year);
            vo.setMonth(month);
            vo.setDay(day);
            if (year == selectYear &&
                    month == selectMonth&&
                    day == selectDay){
                vo.setSelect(true);
            }else {
                vo.setSelect(false);
            }
            vo.setHasVedio(getd(year,month,day));
            dates.add(vo);
            mCalendar.add(Calendar.DAY_OF_MONTH,1);
        }
        adapter.setmMonth(pmonth);
        adapter.setDatas(dates);
    }

    public void setDate(int mSelectYear,int mSelectMonth,int mSelectDay){
        selectYear = mSelectYear;
        selectMonth = mSelectMonth;
        selectDay = mSelectDay;
        dates.clear();
        mCalendar = (Calendar) calendar.clone();
        mCalendar.set(Calendar.YEAR,selectYear);
        mCalendar.set(Calendar.MONTH,selectMonth-1);
        int pyear = mCalendar.get(Calendar.YEAR);
        int pmonth = mCalendar.get(Calendar.MONTH)+1;
        year = pyear;
        month = pmonth;

        if (mTvYear != null){
            mTvYear.setText(pyear+"-"+ String.format("%02d",pmonth));
        }
        // 将日期改为 1 号 以来确定1号为星期几
        mCalendar.set(Calendar.DAY_OF_MONTH,1);
        weekDay =  mCalendar.get(Calendar.DAY_OF_WEEK)-1;
        mCalendar.add(Calendar.DAY_OF_MONTH,-weekDay);
        int maxNumber = 6*7;
        while(dates.size()<maxNumber){
            int year =  mCalendar.get(Calendar.YEAR);
            int month =  mCalendar.get(Calendar.MONTH)+1;
            int day =  mCalendar.get(Calendar.DAY_OF_MONTH);
            DateVo vo = new DateVo();
            vo.setYear(year);
            vo.setMonth(month);
            vo.setDay(day);
            if (year == selectYear &&
                    month == selectMonth&&
                    day == selectDay){
                vo.setSelect(true);
            }else {
                vo.setSelect(false);
            }
            vo.setHasVedio(getd(year,month,day));
            dates.add(vo);
            mCalendar.add(Calendar.DAY_OF_MONTH,1);
        }
        adapter.setmMonth(pmonth);
        adapter.setDatas(dates);
    }

    private boolean getd(int year,int month,int day){
//        if (!TextUtils.isEmpty(str)) {
//            RecordMapModel model = Constant.modelMap.get(str);
//            if (model != null) {
//                for (String key :model.getKeys()){
//                    if (month == Integer.parseInt(key.substring(5, 7)) &&
//                            day == Integer.parseInt(key.substring(8, 10))) {
//                        return true;
//                    }
//                }
//            }
//        }else {
        for (String key : VideoVo.keys) {
            if (   year == Integer.parseInt(key.substring(0,4)) &&
                    month == Integer.parseInt(key.substring(5, 7)) &&
                    day == Integer.parseInt(key.substring(8, 10))) {
                return true;
            }
        }
//        }
        return false;
    }

    private void setData(boolean isNext){
        if (mCalendar == null)return;
        mCalendar.set(Calendar.YEAR,year);
        mCalendar.set(Calendar.MONTH,month-1);
        if (isNext){
            if (currentYear == year ){
                if (currentMonth > month){
                    mCalendar.add(Calendar.MONTH,+1);
                }else {
                    return;
                }
            }else {
                mCalendar.add(Calendar.MONTH,+1);
            }
        }else {
            mCalendar.add(Calendar.MONTH,-1);
        }
        dates.clear();
        int pyear = mCalendar.get(Calendar.YEAR);
        int pmonth = mCalendar.get(Calendar.MONTH)+1;
        year = pyear;
        month = pmonth;
        if (mTvYear != null){
            mTvYear.setText(pyear+"-"+ String.format("%02d",pmonth));
        }
        // 将日期改为 1 号 以来确定1号为星期几
        mCalendar.set(Calendar.DAY_OF_MONTH,1);
        weekDay =  mCalendar.get(Calendar.DAY_OF_WEEK)-1;
        mCalendar.add(Calendar.DAY_OF_MONTH,-weekDay);
        int maxNumber = 6*7;
        while(dates.size()<maxNumber){
            int year =  mCalendar.get(Calendar.YEAR);
            int month =  mCalendar.get(Calendar.MONTH)+1;
            int day =  mCalendar.get(Calendar.DAY_OF_MONTH);
            DateVo vo = new DateVo();
            vo.setYear(year);
            vo.setMonth(month);
            vo.setDay(day);
            if (year == selectYear &&
                    month == selectMonth&&
                    day == selectDay){
                vo.setSelect(true);
            }else {
                vo.setSelect(false);
            }
            vo.setHasVedio(getd(year,month,day));
            dates.add(vo);
            mCalendar.add(Calendar.DAY_OF_MONTH,1);
        }
        adapter.setmMonth(pmonth);
        adapter.setDatas(dates);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.lin_next_day:
                setData(true);
                break;
            case R.id.lin_previous_day:
                setData(false);
                break;
        }
    }
}
