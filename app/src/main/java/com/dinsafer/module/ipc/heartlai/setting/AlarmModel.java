package com.dinsafer.module.ipc.heartlai.setting;


import android.util.Log;

import com.heartlai.ipc.utils.CommonUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;


public class AlarmModel implements Serializable {

    private int cmd;
    private int result;
    private int enable;
    private int level;
    private int audio_out;
    private int record;
    private int msg_push;
    private int start_min;
    private int stop_min;
    private int start_hour;
    private int stop_hour;
    private int play_times = -1;
    private String alarm_type;
    private String alarm_audio_type = null;
    private int audio_file_type = 1;

    public int getCmd() {
        return cmd;
    }

    public void setCmd(int cmd) {
        this.cmd = cmd;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public int getEnable() {
        return enable;
    }

    public void setEnable(int enable) {
        this.enable = enable;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getAudio_out() {
        return audio_out;
    }

    public void setAudio_out(int audio_out) {
        this.audio_out = audio_out;
    }

    public int getRecord() {
        return record;
    }

    public void setRecord(int record) {
        this.record = record;
    }

    public int getMsg_push() {
        return msg_push;
    }

    public void setMsg_push(int msg_push) {
        this.msg_push = msg_push;
    }

    public int getStart_min() {
        return start_min;
    }

    public void setStart_min(int start_min) {
        this.start_min = start_min;
    }

    public int getStop_min() {
        return stop_min;
    }

    public void setStop_min(int stop_min) {
        this.stop_min = stop_min;
    }

    public int getStart_hour() {
        return start_hour;
    }

    public void setStart_hour(int start_hour) {
        this.start_hour = start_hour;
    }

    public int getStop_hour() {
        return stop_hour;
    }

    public void setStop_hour(int stop_hour) {
        this.stop_hour = stop_hour;
    }


    public int getPlay_times() {
        return play_times;
    }

    public void setPlay_times(int play_times) {
        this.play_times = play_times;
    }

    public String getAlarm_type() {
        return alarm_type;
    }

    public void setAlarm_type(String alarm_type) {
        this.alarm_type = alarm_type;
    }

    public String getAlarm_audio_type() {
        return alarm_audio_type;
    }

    public void setAlarm_audio_type(String alarm_audio_type) {
        this.alarm_audio_type = alarm_audio_type;
    }

    public int getAudio_file_type() {
        return audio_file_type;
    }

    public void setAudio_file_type(int audio_file_type) {
        this.audio_file_type = audio_file_type;
    }

    public static AlarmModel jsonToModel(String json) throws JSONException {
//        private int cmd;
//        private int result;
//        private int enable;
//        private int level;
//        private int audio_out;
//        private int record;
//        private int msg_push;
//        private int start_min;
//        private int stop_min;
//        private int start_hour;
//        private int stop_hour;

        AlarmModel parmsModel = new AlarmModel();
        JSONObject obj = new JSONObject(json);
        parmsModel.setCmd(CommonUtil.jasonPaseInt(obj,"cmd", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setResult(CommonUtil.jasonPaseInt(obj,"result", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setEnable(CommonUtil.jasonPaseInt(obj,"enable", CommonUtil.SHIXFINAL_ERRORINT));

        parmsModel.setLevel(CommonUtil.jasonPaseInt(obj,"level", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setAudio_out(CommonUtil.jasonPaseInt(obj,"audio_out", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setRecord(CommonUtil.jasonPaseInt(obj,"record", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setMsg_push(CommonUtil.jasonPaseInt(obj,"msg_push", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setStart_min(CommonUtil.jasonPaseInt(obj,"start_min", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setStop_min(CommonUtil.jasonPaseInt(obj,"stop_min", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setStart_hour(CommonUtil.jasonPaseInt(obj,"start_hour", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setStop_hour(CommonUtil.jasonPaseInt(obj,"stop_hour", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setAlarm_type(CommonUtil.jasonPaseString(obj,"alarm_type"));
        parmsModel.setPlay_times(CommonUtil.jasonPaseInt(obj,"audio_out_times", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setAlarm_audio_type(CommonUtil.jasonPaseString(obj,"audio_out_file"));
        parmsModel.setAudio_file_type(CommonUtil.jasonPaseInt(obj,"audio_file_type", CommonUtil.SHIXFINAL_ERRORINT));

        return parmsModel;
    }



    public static String toJson(AlarmModel alrmModel, String user, String pwd) throws JSONException {
        //        private int cmd;
//        private int result;
//        private int enable;
//        private int level;
//        private int audio_out;
//        private int record;
//        private int msg_push;
//        private int start_min;
//        private int stop_min;
//        private int start_hour;
//        private int stop_hour;
        JSONObject obj = new JSONObject();
        obj.put("pro", "set_alarm");
        obj.put("cmd", 108);
        obj.put("user", user);
        obj.put("pwd", pwd);

        obj.put("enable", alrmModel.getEnable());
        obj.put("level", alrmModel.getLevel());
        obj.put("audio_out", alrmModel.getAudio_out());

        obj.put("record", alrmModel.getRecord());
        obj.put("msg_push", alrmModel.getMsg_push());
        obj.put("start_min", alrmModel.getStart_min());
        obj.put("stop_min", alrmModel.getStop_min());
        obj.put("start_hour", alrmModel.getStart_hour());
        obj.put("stop_hour", alrmModel.getStop_hour());

        obj.put("alarm_type",alrmModel.getAlarm_type());
        obj.put("audio_out_times",alrmModel.getPlay_times());
        obj.put("audio_out_file",alrmModel.getAlarm_audio_type());
        obj.put("audio_file_type",alrmModel.getAudio_file_type());
        Log.e("alarm",obj.toString());
        return obj.toString();
    }

}
