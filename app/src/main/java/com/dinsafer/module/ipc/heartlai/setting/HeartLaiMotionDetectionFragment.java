package com.dinsafer.module.ipc.heartlai.setting;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentHeartlaiMotionDetectionBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.CircleSeekBar;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.Local;
import com.heartlai.ipc.utils.CommonUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/11/18
 */
public class HeartLaiMotionDetectionFragment extends MyBaseFragment<FragmentHeartlaiMotionDetectionBinding> implements IDeviceCallBack {

    private String[] mSensitivityData;
    private AlarmModel alarmModel = new AlarmModel();
    private AtomicInteger mCount = new AtomicInteger(2);
    private boolean isChangeSetting = false;
    private Device device;

    public static HeartLaiMotionDetectionFragment newInstance(String id) {
        Bundle args = new Bundle();
        args.putString("id", id);
        HeartLaiMotionDetectionFragment fragment = new HeartLaiMotionDetectionFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_heartlai_motion_detection;
    }

    @Override
    public void initData() {
        super.initData();
        device = IPCManager.getInstance().getHeartLaiDeviceByID(getArguments().getString("id"));
        if (device == null) {
            removeSelf();
            return;
        }

        device.registerDeviceCallBack(this);

//        get alarm detail
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", HeartLaiCmd.CMD_GET_ALERT_INFO);
        device.submit(parms);

        parms = new HashMap<>();
        parms.put("cmd", HeartLaiCmd.CMD_GET_ALERT_MODE);
        parms.put("home_id", HomeManager.getInstance().getCurrentHome().getHomeID());
        device.submit(parms);

        mBinding.title.commonBarBack.setOnClickListener(v -> {
            if (isChangeSetting) {
                showNotSaveTip();
            } else {
                removeSelf();
            }
        });
        mBinding.title.commonBarTitle.setLocalText(getString(R.string.device_ipc_motion));
        mBinding.title.commonBarRightIcon.setVisibility(View.GONE);
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.btnSave.setOnClickListener(v -> save());

        mBinding.tvMotionDetect.setLocalText(getString(R.string.device_ipc_motion));
        mBinding.tvAlertSound.setLocalText(getString(R.string.ipc_setting_alarm_sound));
        mBinding.ipcMotionSensitivityText.setLocalText(getString(R.string.ipc_motion_sensitivity_text));
        mBinding.tvRecordingTitle.setLocalText(getString(R.string.ipc_setting_detection_period));


        mSensitivityData = new String[]{Local.s(getResources().getString(R.string.motion_sensitivity_low)),
                Local.s(getResources().getString(R.string.motion_sensitivity_middle)),
                Local.s(getResources().getString(R.string.motion_sensitivity_high))};

        mBinding.ipcMotionSensitivityNumber.setOnClickListener(v -> toChangeSensitivity());
        mBinding.ipcMotionSensitivityText.setOnClickListener(v -> toChangeSensitivity());

        mBinding.tvMotionDetectFullDay.setLocalText(getString(R.string.full_day));

        mBinding.switchMotionDetectFullDay.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                if (isOn) {
                    mBinding.llTimeSetting.setVisibility(View.GONE);
                    alarmModel.setStart_hour(0);
                    alarmModel.setStart_min(0);
                    alarmModel.setStop_hour(24);
                    alarmModel.setStop_min(0);
                } else {
                    mBinding.llTimeSetting.setVisibility(View.VISIBLE);
                    alarmModel.setStart_hour(IPCSettingUtils.getHourFormCircleSeekBarIndex(mBinding.csb.getStartIndex()));
                    alarmModel.setStart_min(IPCSettingUtils.getMinFormCircleSeekBarIndex(mBinding.csb.getStartIndex()));
                    int endHour = IPCSettingUtils.getHourFormCircleSeekBarIndex(mBinding.csb.getEndIndex());
                    int endMin = IPCSettingUtils.getMinFormCircleSeekBarIndex(mBinding.csb.getEndIndex());
                    if (endHour == 0 && endMin == 0) {
                        endHour = 24;
                    }
                    alarmModel.setStop_hour(endHour);
                    alarmModel.setStop_min(endMin);

                }
            }
        });

        IPCSettingUtils.initCommonStyleCircleSeekBar(getContext(), mBinding.csb);
        mBinding.csb.setOnCircleSeekBarChangeListener(new CircleSeekBar.OnCircleSeekBarChangeListener() {
            @Override
            public void onSeekProgressChange(int startIndex, int endIndex) {
                Log.d("CircleSeekBar", "onSeekProgressChange--> startIndex:" + startIndex + " /endIndex:" + endIndex);
                isChangeSetting = true;
                mBinding.tvRecordingPeriodTime.setText(IPCSettingUtils.getFormatPeriod(getContext(), startIndex, endIndex));
                alarmModel.setStart_hour(IPCSettingUtils.getHourFormCircleSeekBarIndex(startIndex));
                alarmModel.setStart_min(IPCSettingUtils.getMinFormCircleSeekBarIndex(startIndex));
                int endHour = IPCSettingUtils.getHourFormCircleSeekBarIndex(endIndex);
                int endMin = IPCSettingUtils.getMinFormCircleSeekBarIndex(endIndex);
                if (endHour == 0 && endMin == 0) {
                    endHour = 24;
                }
                alarmModel.setStop_hour(endHour);
                alarmModel.setStop_min(endMin);
            }
        });

        mBinding.switchMotionDetect.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                isChangeSetting = true;
                if (isOn) {
                    alarmModel.setEnable(1);
                    alarmModel.setRecord(1);
                    setSubOptionVisibility(View.VISIBLE);
                    if (alarmModel.getStart_hour() == 0 && alarmModel.getStart_min() == 0
                            && alarmModel.getStop_hour() == 24 && alarmModel.getStop_min() == 0) {
                        mBinding.switchMotionDetectFullDay.setOn(true);
                        mBinding.llTimeSetting.setVisibility(View.GONE);
                    } else {
                        mBinding.switchMotionDetectFullDay.setOn(false);
                        mBinding.llTimeSetting.setVisibility(View.VISIBLE);
                    }
                } else {
                    alarmModel.setEnable(0);
                    alarmModel.setRecord(0);
                    setSubOptionVisibility(View.GONE);
                    mBinding.llTimeSetting.setVisibility(View.GONE);
                }
            }
        });

        mBinding.switchAlertSound.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                isChangeSetting = true;
                if (isOn) {
                    alarmModel.setAudio_out(1);
                } else {
                    alarmModel.setAudio_out(0);
                }
            }
        });
        mBinding.sv.setInteracpt(true, false);

        mBinding.tvDetectionAlertMode.setLocalText(getString(R.string.ipc_setting_alert_mode));
        mBinding.llDetectionAlertMode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(),
                        getDelegateActivity().getSupportFragmentManager())
                        .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                        .setOtherButtonTitles(
                                Local.s(getString(R.string.critical_alert)),
                                Local.s(getString(R.string.normal_alert)),
                                Local.s(getString(R.string.no_alert)))
                        .setCancelableOnTouchOutside(true)
                        .setListener(new ActionSheet.ActionSheetListener() {

                            @Override
                            public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                            }

                            @Override
                            public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                                showTimeOutLoadinFramgmentWithErrorAlert();
                                Map par = new HashMap();
                                par.put("cmd", HeartLaiCmd.CMD_SET_ALERT_MODE);
                                par.put("home_id", HomeManager.getInstance().getCurrentHome().getHomeID());
                                if (index == 0) {
                                    mBinding.tvDetectionAlertModeType.setLocalText(getString(R.string.critical_alert));
                                    par.put("alert_mode", "critical");
                                } else if (index == 1) {
                                    mBinding.tvDetectionAlertModeType.setLocalText(getString(R.string.normal_alert));
                                    par.put("alert_mode", "normal");
                                } else {
                                    mBinding.tvDetectionAlertModeType.setLocalText(getString(R.string.no_alert));
                                    par.put("alert_mode", "backup");
                                }
                                device.submit(par);
                            }
                        }).show();
            }
        });
    }


    public void toChangeSensitivity() {

        AlertDialog.Builder listDialog =
                new AlertDialog.Builder(getDelegateActivity());
        listDialog.setItems(mSensitivityData, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                isChangeSetting = true;
                if (which == 0) {
                    alarmModel.setLevel(which);
                } else if (which == 1) {
                    alarmModel.setLevel(2);
                } else {
                    alarmModel.setLevel(4);
                }
                updata();
            }
        });
        listDialog.show();
    }

    private void save() {
        try {
            mCount.set(2);
            alarmModel.setAlarm_type(CommonUtil.MOTION_DETECT);
            String send = AlarmModel.toJson(alarmModel,
                    DeviceHelper.getString(device, HeartLaiConstants.ATTR_UID, ""),
                    DeviceHelper.getString(device, HeartLaiConstants.ATTR_PASSWORD, ""));
            Map<String, Object> parms = new HashMap<>();
            parms.put("cmd", HeartLaiCmd.CMD_SET_ALERT_INFO);
            parms.put("savedInfo", send);
            device.submit(parms);
            showTimeOutLoadinFramgmentWithErrorAlert();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void updata() {
        if (alarmModel.getEnable() == 1) {
            mBinding.switchMotionDetect.setOn(true);
            setSubOptionVisibility(View.VISIBLE);
            if (alarmModel.getStart_hour() == 0 && alarmModel.getStart_min() == 0
                    && alarmModel.getStop_hour() == 24 && alarmModel.getStop_min() == 0) {
                mBinding.switchMotionDetectFullDay.setOn(true);
                mBinding.llTimeSetting.setVisibility(View.GONE);
            } else {
                mBinding.switchMotionDetectFullDay.setOn(false);
                mBinding.llTimeSetting.setVisibility(View.VISIBLE);
            }
        } else {
            mBinding.switchMotionDetect.setOn(false);
            setSubOptionVisibility(View.GONE);
            mBinding.llTimeSetting.setVisibility(View.GONE);
        }

        if (alarmModel.getAudio_out() == 1) {
            mBinding.switchAlertSound.setOn(true);
        } else {
            mBinding.switchAlertSound.setOn(false);
        }

        if (alarmModel.getLevel() < 2) {
            mBinding.ipcMotionSensitivityNumber.setText(Local.s(getResources().getString(R.string.motion_sensitivity_low)));
        } else if (alarmModel.getLevel() == 2) {
            mBinding.ipcMotionSensitivityNumber.setText(Local.s(getResources().getString(R.string.motion_sensitivity_middle)));
        } else {
            mBinding.ipcMotionSensitivityNumber.setText(Local.s(getResources().getString(R.string.motion_sensitivity_high)));
        }

        int startIndex = alarmModel.getStart_hour() * 6 + alarmModel.getStart_min() / 10;
        int endIndex = alarmModel.getStop_hour() * 6 + alarmModel.getStop_min() / 10;

        mBinding.csb.setStartIndex(startIndex);
        mBinding.csb.setEndIndex(endIndex);

        mBinding.tvRecordingPeriodTime.setText(IPCSettingUtils.getFormatPeriod(getContext(), startIndex, endIndex));
    }

    private void showNotSaveTip() {
        com.dinsafer.module.settting.ui.AlertDialog dialog = null;
        dialog = com.dinsafer.module.settting.ui.AlertDialog.createBuilder(getContext())
                .setContent(Local.s(getString(R.string.ipc_setting_not_save_tip)))
                .setOk(getString(R.string.ok))
                .setCancel(getString(R.string.cancel))
                .setOKListener(() -> {
                    save();
                })
                .setCancelListener(() -> {
                    removeSelf();
                }).preBuilder();
        dialog.show();
    }

    private void setSubOptionVisibility(int visibility) {
        Log.d(TAG, "setSubOptionVisibility: " + (View.VISIBLE == visibility));
        mBinding.llAlarmSound.setVisibility(visibility);
        mBinding.rlMotionSensitivity.setVisibility(visibility);
//        mBinding.llTimeSetting.setVisibility(visibility);
        mBinding.tvRecordingTitle.setVisibility(visibility);
        mBinding.llFullDay.setVisibility(visibility);
        mBinding.llDetectionAlertMode.setVisibility(visibility);
    }

    @Override
    public boolean onBackPressed() {
        if (isChangeSetting) {
            showNotSaveTip();
            return true;
        }
        return super.onBackPressed();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (device != null) {
            device.unregisterDeviceCallBack(this);
        }
    }

    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (device == null || TextUtils.isEmpty(id) || !id.equals(device.getId())) {
            return;
        }
        switch (cmd) {
            case HeartLaiCmd.CMD_GET_ALERT_INFO:
                if (((int) map.get("status")) == 1) {
                    try {
                        HashMap<String, Object> result = (HashMap<String, Object>) map.get("result");
                        JSONObject resultJson = new JSONObject(result);
                        alarmModel = AlarmModel.jsonToModel(resultJson.toString());
//                        if (alarmModel.getStop_hour() == 23 && alarmModel.getStop_min() == 59) {
//                            alarmModel.setStop_hour(24);
//                            alarmModel.setStop_min(0);
//                        }
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                updata();
                                closeTimeOutLoadinFramgmentWithErrorAlert();
                            }
                        });
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                } else {

                }
                break;

            case HeartLaiCmd.CMD_SET_ALERT_INFO:
                if (((int) map.get("status")) == 1) {
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            getMainActivity().showTopToast(getString(R.string.success));
                            removeSelf();
                        }
                    });
                } else {
                }
                break;

            case HeartLaiCmd.CMD_GET_ALERT_MODE:
                if (((int) map.get("status")) == 1) {
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            String alertMode = (String) map.get("alert_mode");
                            Log.d(TAG, "run-->alertMode: " + alertMode);
                            if (TextUtils.isEmpty(alertMode)) {
//                                mBinding.llDetectionAlertMode.setAlpha(0.5f);
//                                mBinding.llDetectionAlertMode.setClickable(false);
                            } else {
                                mBinding.llDetectionAlertMode.setAlpha(1f);
                                mBinding.llDetectionAlertMode.setClickable(true);
                                if ("backup".equals(alertMode)) {
                                    mBinding.tvDetectionAlertModeType.setLocalText(getString(R.string.no_alert));
                                } else if ("critical".equals(alertMode)) {
                                    mBinding.tvDetectionAlertModeType.setLocalText(getString(R.string.critical_alert));
                                } else {
                                    mBinding.tvDetectionAlertModeType.setLocalText(getString(R.string.normal_alert));
                                }
                            }
                        }
                    });
                } else {
                }
                break;

            case HeartLaiCmd.CMD_SET_ALERT_MODE:
                if (((int) map.get("status")) == 1) {
                    getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                        }
                    });
                } else {
                    getMainActivity().showErrorToast();
                }
                break;
        }
    }
}
