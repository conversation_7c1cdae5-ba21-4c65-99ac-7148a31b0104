package com.dinsafer.module.popup;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.PopupLayoutBinding;
import com.dinsafer.model.PopupEntry;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.util.CollectionUtil;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.util.ArrayList;

/**
 * @describe：
 * @date：2025/5/22
 * @author: create by Sydnee
 */
public class PopupFragment extends MyBaseFragment<PopupLayoutBinding> {

    private ArrayList<PopupEntry.ImageItem> imageList;
    private int curIndex = 0;

    public static PopupFragment getInstance(ArrayList<PopupEntry.ImageItem> entry) {
        PopupFragment fragment = new PopupFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable("POPUP_ENTRY", entry);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.popup_layout;
    }

    @Override
    public void initData() {
        super.initData();
        setBaseEnterAnim(AnimationUtils.loadAnimation(getActivity(), R.anim.popup_enter));
        setBaseOuterAnim(AnimationUtils.loadAnimation(getActivity(), R.anim.popup_exit));
        Bundle bundle = getArguments();
        if (bundle != null) {
            imageList = (ArrayList<PopupEntry.ImageItem>) bundle.getSerializable("POPUP_ENTRY");
            if (CollectionUtil.isListEmpty(imageList)) {
                removeSelf();
                return;
            }
            ImageLoader.getInstance().displayImage(imageList.get(curIndex).getImage(), mBinding.imgPopup);
        }
        mBinding.imgPopup.setOnClickListener(v -> {
            if (curIndex >= imageList.size()) {
                return;
            }

            if (TextUtils.isEmpty(imageList.get(curIndex).getUrl())) {
                return;
            }

            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(imageList.get(curIndex).getUrl()));
            getMainActivity().startActivity(intent);
        });

        mBinding.ivClose.setOnClickListener(v -> {
            curIndex++;
            if (curIndex >= imageList.size()) {
                mBinding.clParent.setBackgroundResource(R.color.transparent);
                removeSelf();
                return;
            }

            ImageLoader.getInstance().displayImage(imageList.get(curIndex).getImage(), mBinding.imgPopup);
        });
    }
}
