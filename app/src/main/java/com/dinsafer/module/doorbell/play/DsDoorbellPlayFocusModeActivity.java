package com.dinsafer.module.doorbell.play;

import android.Manifest;
import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import androidx.databinding.DataBindingUtil;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.SurfaceTexture;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.TextureView;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.Toast;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityDsdoorbellPlayFocusModeBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dscam.DsCamNetWorkSetting;
import com.dinsafer.dscam.DsCamPlayerManager;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.IPCEvent;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.module.BaseLiveVideoActivity;
import com.dinsafer.module.ipc.heartlai.event.IPCInfoChangeEvent;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.ipc.player.IVideoViewListener;
import com.dinsafer.module.settting.adapter.ipc.FullPlayBackEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_dscam.player.DsCamPlayer;
import com.dinsafer.module_dscam.player.IPlayerStatusListener;
import com.dinsafer.module_dscam.player.KRealTimePlayView;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.viewanimator.AnimationListener;
import com.dinsafer.util.viewanimator.ViewAnimator;
import com.githang.statusbar.StatusBarCompat;
import com.dinsafer.aop.annotations.SingleClick;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;


public class DsDoorbellPlayFocusModeActivity extends BaseLiveVideoActivity
        implements IDeviceCallBack, IPlayerStatusListener, IVideoViewListener {

    private static final String TAG = DsDoorbellPlayFocusModeActivity.class.getSimpleName();
    private ActivityDsdoorbellPlayFocusModeBinding mBinding;

    private boolean isInitTakePicture = true;
    private boolean isPlaySound;
    private boolean isFromUserClick = false;
    private static final String KEY_QR_CODE_ID = "device_id";
    private Device device;
    public String mCameraPid;
    private KRealTimePlayView kRealTimePlayView;
    private DsCamPlayer player;

    private static Handler handler = new Handler();

    private IDefaultCallBack2<Bitmap> snapshotCallBack = new IDefaultCallBack2<Bitmap>() {
        @Override
        public void onSuccess(Bitmap bitmap) {
            savePicture(bitmap, device.getId());
        }

        @Override
        public void onError(int i, String s) {

        }
    };

    private boolean isRequestPlay;
    private boolean isFromCall = false;
    //暂时先弃用
    private boolean isAutoTalking = false;
    private boolean isAutoListenning = false;
    private String pushTip;

    private Animation animationEnter;
    private Animation animationBreath;
    private Animation animationExit;

    public static void startActivity(Context context, String id, boolean isFromCall, boolean isAutoTalking, boolean isAutoListenning, String pushTip) {
        Intent intent = new Intent(context, DsDoorbellPlayFocusModeActivity.class);
        intent.putExtra(KEY_QR_CODE_ID, id);
        intent.putExtra("isFromCall", isFromCall);
        intent.putExtra("isAutoTalking", isAutoTalking);
        intent.putExtra("isAutoListenning", isAutoListenning);
        intent.putExtra("pushTip", pushTip);
        context.startActivity(intent);
    }

    /**
     * 初始化变量
     */
    @Override
    protected boolean initVariables() {
        device = IPCManager.getInstance().getDsDoorbellDeviceByID(getIntent().getStringExtra(KEY_QR_CODE_ID));
        if (device == null) {
            return false;
        }
        mCameraPid = device.getId();
        device.registerDeviceCallBack(this);
        isFromCall = getIntent().getBooleanExtra("isFromCall", true);
        isAutoTalking = getIntent().getBooleanExtra("isAutoTalking", isAutoTalking);
        isAutoListenning = getIntent().getBooleanExtra("isAutoListenning", isAutoListenning);
        pushTip = getIntent().getStringExtra("pushTip");
        return true;
    }

    /**
     * 初始化控件
     *
     * @param savedInstanceState
     */
    @Override
    protected void initViews(Bundle savedInstanceState) {

        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_dsdoorbell_play_focus_mode);
        StatusBarCompat.setStatusBarColor(this, Color.BLACK, false);
        EventBus.getDefault().register(this);

        if (isFromCall) {
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            lp.width = WindowManager.LayoutParams.MATCH_PARENT;
            lp.height = (int) ((ScreenUtils.getScreenHeight(getApplicationContext()) - ScreenUtils.getStatusHeight(getApplicationContext())) * 0.96f);
            lp.gravity = Gravity.BOTTOM;
            getWindow().setAttributes(lp);
            overridePendingTransition(R.anim.dialog_enter_anim, R.anim.activity_main_modal_exit_anim);
            setFinishOnTouchOutside(false);

            if (!TextUtils.isEmpty(pushTip)) {
                mBinding.tvTip.setVisibility(VISIBLE);
                mBinding.tvTip.setLocalText(pushTip);
            }

            mBinding.commonBar.setVisibility(GONE);
            mBinding.tvFocusModeTitle.setVisibility(VISIBLE);

            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mBinding.flVideoView.getLayoutParams();
            layoutParams.topMargin = DensityUtils.dp2px(getApplicationContext(), 19);
            mBinding.flVideoView.setLayoutParams(layoutParams);

            mBinding.rlRoot.setBackgroundResource(R.drawable.shape_modal_bg);

            getWindow().getDecorView().setBackgroundColor(Color.BLACK);
        }

        mBinding.ipcIr.setOnClickListener(v -> setIR());
        mBinding.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.ipcSound.setOnClickListener(v -> toListener());
        mBinding.ipcClose.setOnClickListener(v -> {
            toClose();
            overridePendingTransition(R.anim.activity_main_modal_enter_anim, R.anim.dialog_exit_anim);
        });
        mBinding.ipcTalk.setOnTouchListener((v, event) -> {
            final int action = event.getAction();
            if (DDSystemUtil.isMarshmallow()
                    && ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                    != PackageManager.PERMISSION_GRANTED) {
                if (MotionEvent.ACTION_DOWN == action) {
                    requestAudioPermission();
                }
                return true;
            }

            return toTalk(v, action);
        });
        mBinding.ipcQuality.setOnClickListener(v -> toSwitchQuality());
        mBinding.ipcSnap.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick(duration = 500)
            @Override
            public void onClick(View v) {
                DsDoorbellPlayFocusModeActivity.this.toSnapshot();
            }
        });
        mBinding.ivFocus.setOnClickListener(v -> {
            boolean isTalking = player.isTalking();
            boolean isListening = player.isListening();
            stopPlay();
            DsDoorbellPlayFullscreenActivity.startActivity(DsDoorbellPlayFocusModeActivity.this, mCameraPid, isTalking, isListening);
            finish();
        });

        mBinding.commonBarTitle.setText(Local.s(getResources().getString(R.string.video_doorbell)));
        mBinding.tvFocusModeTitle.setText(Local.s(getResources().getString(R.string.video_doorbell)));
        if (isFromCall) {
            mBinding.commonBarBack.setVisibility(View.INVISIBLE);
            mBinding.ipcClose.setVisibility(VISIBLE);
            mBinding.ipcIr.setVisibility(GONE);
            mBinding.ipcQuality.setVisibility(GONE);
            mBinding.space3.setVisibility(GONE);
            mBinding.space4.setVisibility(GONE);
        } else {
            mBinding.ipcClose.setVisibility(GONE);
        }
        disableButton();
        initVideoView();

        mBinding.ipcMainControl.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                //适配小屏幕
                if (mBinding.ipcMainControl.getMeasuredHeight() < 400) {
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mBinding.ipcMoreControl.getLayoutParams();
                    layoutParams.bottomMargin = 0;
                    mBinding.ipcMoreControl.setLayoutParams(layoutParams);


                }
                mBinding.ipcMainControl.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });

        animationEnter = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.anim_talk_btn_in);
        animationBreath = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.anim_talk_btn_breath);
        animationExit = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.anim_talk_btn_out);

        if (isFromCall) {
            startControlButtonEnterAnim();
        }

    }

    private void startControlButtonEnterAnim() {
        mBinding.ipcMoreControl.setVisibility(View.INVISIBLE);
        mBinding.ipcClose.setTranslationY(500);
        mBinding.flIpcTalk.setTranslationY(500);
        ObjectAnimator talkEnterAnim = ObjectAnimator.ofFloat(mBinding.flIpcTalk, "translationY", 500, -20, 0);
        talkEnterAnim.setInterpolator(new LinearInterpolator());
        talkEnterAnim.setDuration(800);
        talkEnterAnim.setStartDelay(300);
        talkEnterAnim.start();

        ObjectAnimator closeEnterAnim = ObjectAnimator.ofFloat(mBinding.ipcClose, "translationY", 500, -20, 0);
        closeEnterAnim.setInterpolator(new LinearInterpolator());
        closeEnterAnim.setDuration(800);
        closeEnterAnim.setStartDelay(400);
        closeEnterAnim.start();

        mBinding.ipcSound.setTranslationY(300);
        mBinding.ipcSnap.setTranslationY(300);
        ObjectAnimator soundEnterAnim = ObjectAnimator.ofFloat(mBinding.ipcSound, "translationY", 300, -20, 0);
        soundEnterAnim.setInterpolator(new LinearInterpolator());
        soundEnterAnim.setDuration(700);
        soundEnterAnim.setStartDelay(1100);
        soundEnterAnim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                mBinding.ipcMoreControl.setVisibility(VISIBLE);
            }

            @Override
            public void onAnimationEnd(Animator animation) {

            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        soundEnterAnim.start();

        ObjectAnimator snapEnterAnim = ObjectAnimator.ofFloat(mBinding.ipcSnap, "translationY", 300, -20, 0);
        snapEnterAnim.setInterpolator(new LinearInterpolator());
        snapEnterAnim.setDuration(700);
        snapEnterAnim.setStartDelay(1200);
        snapEnterAnim.start();
    }

    /**
     * 初始化数据
     */
    @Override
    protected void loadData() {
        if (device == null) {
            return;
        }
        initIPCInfo();
        initPlayer();
        connectIPC();
        refreshVideoViewState(device);
    }

    private void initPlayer() {
        player = DsCamPlayerManager.getInstance().getPlayer(device.getId());
        if (player == null) {
            try {
                player = new DsCamPlayer.Builder()
                        .kRealTimePlayView(kRealTimePlayView)
                        .withDevice(device)
                        .withKeyFrame(true)
                        .withContext(DinSaferApplication.getAppContext())
                        .build();
                player.loadData();
            } catch (Exception e) {
                e.printStackTrace();
            }
            DsCamPlayerManager.getInstance().addPlayer(device.getId(), player);

        } else {
            player.changeBindView(kRealTimePlayView);
        }
        player.setStatusListener(this);
        player.registerSnapShotCallBack(snapshotCallBack);
    }

    private void initVideoView() {
        mBinding.videoView.getVideoContainer().removeAllViews();
        kRealTimePlayView = new KRealTimePlayView(this);
        FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
        kRealTimePlayView.setOutListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surface, int width, int height) {
                MsctLog.i(TAG, "onSurfaceTextureAvailable:" + surface.toString() + "width:" + width
                        + " height:" + height);
            }

            @Override
            public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surface, int width, int height) {
                MsctLog.i(TAG, "onSurfaceTextureSizeChanged:" + surface.toString() + "width:" + width
                        + " height:" + height);

            }

            @Override
            public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surface) {
                MsctLog.i(TAG, "onSurfaceTextureDestroyed:" + surface.toString());
                return false;
            }

            @Override
            public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surface) {

            }
        });
        mBinding.videoView.getVideoContainer().addView(kRealTimePlayView, lp);
        mBinding.videoView.setVideoViewListener(this);
        kRealTimePlayView.setOnClickListener(new View.OnClickListener() {

            @Keep
            @SingleClick(duration = 500)
            @Override
            public void onClick(View v) {
                DDLog.i(TAG, "点击了播放view");
                if (player.isPlaying()) {
//                    tiggleView();
                }
            }
        });
        // 预览图
        final String snapshotPath = DeviceHelper.getString(device, HeartLaiConstants.ATTR_SNAPSHOT, "");
        if (!TextUtils.isEmpty(snapshotPath)) {
            File file = new File(snapshotPath);
            if (file.exists()) {
                mBinding.videoView.setCoverImageUri(Uri.fromFile(file));
            } else {
                mBinding.videoView.setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
            }
        } else {
            mBinding.videoView.setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
        }

        mBinding.flVideoView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                //视频窗口大小1:1
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mBinding.flVideoView.getLayoutParams();
                layoutParams.height = (int) ((ScreenUtils.getScreenWidth(getApplicationContext()) - DensityUtils.dp2px(getApplicationContext(), 30)) * 0.95);
                if (mBinding.ipcMainControl.getMeasuredHeight() < 400) {
                    layoutParams.height = (int) (layoutParams.height * 0.9f);
                }

//                ViewGroup.LayoutParams layoutParams1 = kRealTimePlayView.getLayoutParams();
//                layoutParams1.height = layoutParams.height;
//                layoutParams1.width = (int) (layoutParams.height * (16 / 9f));
//                kRealTimePlayView.setLayoutParams(layoutParams1);

                mBinding.flVideoView.setLayoutParams(layoutParams);
                mBinding.flVideoView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });

        kRealTimePlayView.setZoomable(false);
    }

    private static Handler timeoutHandler = new Handler(Looper.getMainLooper());
    private boolean isSnapping = false;

    public void toSnapshot() {
        DDLog.i(TAG, "截图 点击");
        if (isInitTakePicture) {
            DDLog.i(TAG, "自动化截图未完成");
//            在还没有进行默认的第一次自动截图时，不能让用户点击截图
//           2021年08月10日11:30:58，修改了需求，为了解决第一次自动截图失败了，以后再也无法截图问题
//            return;
        }

        if (PermissionUtil.isStoragePermissionDeny(this)) {
            requestReadImagesPermission();
            return;
        }

        if (isSnapping) {
            return;
        }
        isSnapping = true;
        isFromUserClick = true;
        DDLog.i(TAG, "开始点击截图");
        if (this.player != null) {
            this.player.getSnapshot();
        }

        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                showToast(getResources().getString(R.string.failed_try_again));
                isSnapping = false;
            }
        }, 20 * 1000);
    }

    /**
     * 配置IPC信息
     */
    private void initIPCInfo() {
        mBinding.commonBarTitle.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, ""));
        mBinding.tvFocusModeTitle.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, ""));
        mBinding.ipcMainControl.setVisibility(VISIBLE);
        mBinding.ivFocus.setVisibility(VISIBLE);
        boolean isIR = (boolean) MapUtils.get(device.getInfo(), "gray", false);
        if (isIR) {
            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_off);
        } else {
            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_on);
        }

    }

    private void refreshVideoViewState(Device device) {
        DDLog.d(TAG, "refreshVideoViewState1: " + DeviceHelper.getInt(this.device, DinConst.NETWORK_STATE, -2));
        DDLog.d(TAG, "refreshVideoViewState2: " + DeviceHelper.getInt(device, DinConst.NETWORK_STATE, -2));
        if (DsCamUtils.isDeviceConnected(device)) {
            if (!player.isPlaying()) {
                play();
                DDLog.i(TAG, "开始截图");
                player.getSnapshot();
                if (isAutoListenning) {
                    toListener();
                    isAutoListenning = false;
                }
            }
        } else if (DsCamUtils.isDeviceConnecting(device)) {
            disableButton();
            mBinding.videoView.showLoading();
        } else {
            disableButton();
            mBinding.videoView.showError();
            showDeviceOfflineDialog(this.device);
        }

    }

    private void enableButton() {
        mBinding.ipcTalk.setCanTouch(true);
        mBinding.ipcSnap.setCanTouch(true);
        mBinding.ipcSound.setCanTouch(true);
        mBinding.ipcIr.setCanTouch(true);
        mBinding.ipcQuality.setCanTouch(true);
    }

    private void disableButton() {
        mBinding.ipcTalk.setCanTouch(false);
        mBinding.ipcSnap.setCanTouch(false);
        mBinding.ipcSound.setCanTouch(false);
        mBinding.ipcIr.setCanTouch(false);
        mBinding.ipcQuality.setCanTouch(false);
    }

    public void play() {
        if (isRequestPlay == true) {
            return;
        }
        isRequestPlay = true;


        if (DBUtil.contain(DBKey.KEY_DSCAM_VIDEO_TYPE + device.getId())) {
            int type = DBUtil.Num(DBKey.KEY_DSCAM_VIDEO_TYPE + device.getId());
            qualityType = type;
            player.play(type, new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    isRequestPlay = false;
                }

                @Override
                public void onError(int i, String s) {
                    isRequestPlay = false;
                }
            });
        } else {
            player.play(new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    isRequestPlay = false;
                }

                @Override
                public void onError(int i, String s) {
                    isRequestPlay = false;
                }
            });
        }
        if (qualityType == 0) {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_sd);
        } else {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_hd);
        }

    }

    public void connectIPC() {
        IPCManager.getInstance().connectDevice(device);
    }

    public void setIR() {
        boolean isIR = (boolean) MapUtils.get(device.getInfo(), "gray", false);
        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", DsCamCmd.SET_GRAY);
        parms.put("gray", !isIR);
        device.submit(parms);
    }


    public void toClose() {
        if (this.player != null) {
            this.player.pausePlay();
        }
        EventBus.getDefault().post(new CloseActivityEvent());
        this.finish();
    }

    @Subscribe
    public void onEvent(IPCEvent event) {
        toClose();
    }

    @Override
    public void onDestroy() {
        handler.removeCallbacksAndMessages(null);
        timeoutHandler.removeCallbacksAndMessages(null);
        super.onDestroy();
        if (device != null) {
            device.unregisterDeviceCallBack(this);
        }
        EventBus.getDefault().post(new FullPlayBackEvent(mCameraPid));
        EventBus.getDefault().unregister(this);
        stopPlay();
    }

    private void stopPlay() {
        if (this.player != null) {
            DDLog.i(TAG, "停止播放");
            this.player.destory();
            this.player.unregisterSnapShotCallBack(snapshotCallBack);
            DsCamPlayerManager.getInstance().removePlayer(device.getId());
            this.player = null;
        }
    }

    private void savePicture(Bitmap bitmap, String ipcId) {
        if (bitmap == null || ipcId == null) {
            return;
        }
        if (isInitTakePicture) {
            try {

                DDLog.i(TAG, "自动化截图");
                // 将图片截小；
//            Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bmp, 132, 100,
//                    ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
//                Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, APIKey.IMAGE_WIDTH, APIKey.IMAGE_HIGH,
//                        ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

                String genPath = DDGlobalEnv.getInstance().getAppImageFolder();
                File genFile = new File(genPath);
                if (!genFile.exists()) {
                    genFile.mkdirs();
                }

                String path = genPath + ".ipc";
                File file = new File(path);
                if (!file.exists()) {
                    file.mkdir();
                }
                //fileName为文件绝对路径+文件名
                String fileName = path + "/" + ipcId + String.valueOf("_" + System.currentTimeMillis()) + ".jpg";
                File snapshotFile = new File(fileName);

                if (snapshotFile.exists()) {
                    snapshotFile.delete();
                }

                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();

                JSONObject jsonObject;
                if (DBUtil.Exists(ipcId)) {
                    jsonObject = new JSONObject(DBUtil.Str(ipcId));
                } else {
                    jsonObject = new JSONObject();
                }

                DDLog.i(TAG, "自动化截图 完毕");

                jsonObject.put(LocalKey.SNAPSHOT, fileName);
                jsonObject.put(LocalKey.LAST_OPEN_TIME, (System.currentTimeMillis()));
                KV.putString(DBKey.KEY_SNAPSHOT + ipcId, jsonObject.toString());
                if (IPCManager.getInstance().getDsDoorbellDeviceByID(device.getId()) != null) {
                    IPCManager.getInstance().getDsDoorbellDeviceByID(device.getId()).getInfo().put(HeartLaiConstants.ATTR_SNAPSHOT, fileName);
                }
//                这里如果没有用到这个事件的话,就会出现白屏
                EventBus.getDefault().post(new IPCInfoChangeEvent(ipcId));

            } catch (Exception e) {
                e.printStackTrace();
            }

            isInitTakePicture = false;

        }
        if (isFromUserClick) {
//        用户主动点击时候的截图
            if (!isSnapping) {
                return;
            }
            timeoutHandler.removeCallbacksAndMessages(null);
            isSnapping = false;
            DDLog.i(TAG, "用户主动点击时候的截图");
//            Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, APIKey.IMAGE_WIDTH, APIKey.IMAGE_HIGH,
//                    ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

            File file = new File(DDGlobalEnv.getInstance().getImageFolder());
            if (!file.exists()) {
                file.mkdirs();
            }

            String strDate = new SimpleDateFormat("yyyy.MM.dd_HH.mm.ss").format(new Date());
            //fileName为文件绝对路径+文件名
            String fileName = DDGlobalEnv.getInstance().getImageFolder() + ipcId + "_" + strDate + ".jpg";
            File snapshotFile = new File(fileName);
            if (snapshotFile.exists()) {
                snapshotFile.delete();
            }
            try {
                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();
                DDSystemUtil.updatePhoto(this, snapshotFile);
                animSnapshot(bitmap);
//                showToast("Save the photo successfully");

            } catch (Exception e) {
                showToast("Fail to save the photo");
            }

            isFromUserClick = false;
        }
    }

    private void animSnapshot(Bitmap bitmap) {
        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.animSnapshot.setImageBitmap(bitmap);
                ViewAnimator.animate(mBinding.animSnapshot)
                        .duration(300)
                        .scale(1.0f, 0.8f)
                        .onStart(new AnimationListener.Start() {
                            @Override
                            public void onStart() {
                                mBinding.animSnapshot.setVisibility(VISIBLE);
                            }
                        })
                        .onStop(new AnimationListener.Stop() {
                            @Override
                            public void onStop() {
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (DsDoorbellPlayFocusModeActivity.this.isDestroyed()) {
                                            return;
                                        }
                                        ViewAnimator.animate(mBinding.animSnapshot)
                                                .duration(300)
                                                .scale(0.8f, 1f)
                                                .onStart(new AnimationListener.Start() {
                                                    @Override
                                                    public void onStart() {
                                                        mBinding.animSnapshot.setVisibility(VISIBLE);
                                                    }
                                                })
                                                .onStop(new AnimationListener.Stop() {
                                                    @Override
                                                    public void onStop() {
                                                        if (DsDoorbellPlayFocusModeActivity.this.isDestroyed()) {
                                                            return;
                                                        }
                                                        mBinding.animSnapshot.setVisibility(GONE);
                                                        showTopToast(getString(R.string.dscam_save_to_album));
                                                    }
                                                })
                                                .start();
                                    }
                                }, 600);
                            }
                        })
                        .start();
            }
        });
    }

    private void showTopToast(final String message) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.commonTopToast.setLocalText(message);
                Drawable leftIcon = getResources().getDrawable(R.drawable.icon_toast_succeed);
                leftIcon.setBounds(0, 0, leftIcon.getMinimumWidth(), leftIcon.getMinimumHeight());
                mBinding.commonTopToast.setCompoundDrawables(leftIcon, null, null, null);
                mBinding.commonTopToastLy.showToast();
            }
        });
    }

    private void showToast(final String s) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.createBuilder(DsDoorbellPlayFocusModeActivity.this)
                        .setOk("OK")
                        .setContent(s)
                        .preBuilder()
                        .show();

            }
        });
    }

    /**
     * 输出日志
     *
     * @param msg
     */
    public final void i(@NonNull String msg) {
        DDLog.i(this.getClass().getName(), msg);
    }

    public void toListener() {
        if (player == null || !DsCamUtils.isDeviceConnected(device)) {
            return;
        }
        if (player.isTalking()) {
            player.stopTalk();
            mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_nor);
            mBinding.ipcTalk.setBackgroundResource(R.drawable.shape_bg_ipc_talk_nor);
        }
        if (isPlaySound) {
            player.stopListen();
            mBinding.ipcSound.setImageResource(R.drawable.icon_ipc_sounds_off);
        } else {
            player.startListen(new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "开始监听");
                }

                @Override
                public void onError(int i, String s) {
                    DDLog.i(TAG, "开始监听失败：" + s);
                }
            });
            mBinding.ipcSound.setImageResource(R.drawable.icon_ipc_sounds_on);
        }

        isPlaySound = !isPlaySound;
    }


    private boolean isTalkButtonDown = false;
    private Handler talkHandler = new Handler();

    public boolean toTalk(View v, final int action) {
        if (player == null || !DsCamUtils.isDeviceConnected(device)) {
            return false;
        }

        switch (action) {
            case MotionEvent.ACTION_DOWN:
                hideTip();
                isTalkButtonDown = true;
                mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_sel);
                mBinding.ipcTalk.setBackgroundResource(R.drawable.shape_bg_ipc_talk_sel);
                startTalkBreathAnim();
                talkHandler.removeCallbacks(startTalkRunnable);
                talkHandler.postDelayed(startTalkRunnable, 1000);
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (isTalkButtonDown) {
                    isTalkButtonDown = false;
                    mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_nor);
                    mBinding.ipcTalk.setBackgroundResource(R.drawable.shape_bg_ipc_talk_nor);
                    talkHandler.removeCallbacks(startTalkRunnable);
                    stopTalkBreathAnim();
                    stopTalk();
                }
                break;
        }
        return true;
    }

    private void startTalkBreathAnim() {
        animationEnter.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mBinding.viewTalkBg.startAnimation(animationBreath);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        mBinding.viewTalkBg.startAnimation(animationEnter);
    }

    private void stopTalkBreathAnim() {
        animationExit.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mBinding.viewTalkBg.clearAnimation();
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mBinding.viewTalkBg.startAnimation(animationExit);

    }

    private void hideTip() {
        mBinding.tvTip.setVisibility(GONE);
        mBinding.tvTip.setText(null);
    }

    private Runnable startTalkRunnable = new Runnable() {
        @Override
        public void run() {
            startTalk();
        }
    };

    private void startTalk() {
        if (isPlaySound) {
            player.stopListen();
        }
        player.startTalk(new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                DDLog.i(TAG, "开始讲话");
//                DsDoorbellPlayFocusModeActivity.this.runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        Toast.makeText(DsDoorbellPlayFocusModeActivity.this,
//                                Local.s(getString(R.string.start_talk)), Toast.LENGTH_SHORT).show();
//                    }
//                });
            }

            @Override
            public void onError(int i, String s) {
                DDLog.i(TAG, "开始讲话失败：" + s);
                DsDoorbellPlayFocusModeActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_nor);
                        mBinding.ipcTalk.setBackgroundResource(R.drawable.shape_bg_ipc_talk_nor);
                    }
                });
            }
        });
    }

    private void stopTalk() {
        if (player.isTalking()) {
            player.stopTalk();
            mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_nor);
            mBinding.ipcTalk.setBackgroundResource(R.drawable.shape_bg_ipc_talk_nor);
        }
        if (isPlaySound) {
            player.startListen(new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "开始监听");
                }

                @Override
                public void onError(int i, String s) {
                    DDLog.i(TAG, "开始监听失败：" + s);
                }
            });
        }
    }

    //    0:std 1:hd
    int qualityType = 0;

    public void toSwitchQuality() {
//        refreshTimer();
        if (player == null || !DsCamUtils.isDeviceConnected(device)) {
            return;
        }
        if (qualityType == 0) {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_hd);
            qualityType = 1;
        } else {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_sd);
            qualityType = 0;
        }
        player.switchQuality(qualityType, new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                DBUtil.Put(DBKey.KEY_DSCAM_VIDEO_TYPE + device.getId(), qualityType);
                DsDoorbellPlayFocusModeActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (qualityType == 0) {
                            Toast.makeText(DsDoorbellPlayFocusModeActivity.this,
                                    Local.s(getString(R.string.switched_to_STD)), Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(DsDoorbellPlayFocusModeActivity.this,
                                    Local.s(getString(R.string.switched_to_HD)), Toast.LENGTH_SHORT).show();
                        }
                    }
                });
                DDLog.i(TAG, "切换成功");
            }

            @Override
            public void onError(int i, String s) {
                if (qualityType == 0) {
                    qualityType = 1;
                } else {
                    qualityType = 0;
                }
                DDLog.i(TAG, "切换失败：" + s);
            }
        });
    }

    @Override
    public void onBackPressed() {
        if (!isFromCall) {
            toClose();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayMotionDetectedRecordEvent(PlayMotionDetectedRecordEvent playMotionDetectedRecordEvent) {
        finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(HadLogoutEvent ev) {
        finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSOSevent(SOSevent soSevent) {
        finish();
    }

    @Override
    public void onCmdCallBack(String deviceID, String subCategory, String cmd, Map map) {
        DDLog.d(TAG, "onCmdCallBack: " + deviceID + " /cmd:" + cmd);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (DsDoorbellPlayFocusModeActivity.this.isDestroyed()) {
                    return;
                }
                if (!deviceID.equals(device.getId())) {
                    return;
                }

                switch (cmd) {
                    case DsCamCmd.CMD_CONNECT:
                        refreshVideoViewState(device);
                        break;
                    case DsCamCmd.SET_GRAY:
                        boolean isIR = (boolean) MapUtils.get(device.getInfo(), "gray", false);
                        if (isIR) {
                            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_off);
                            Toast.makeText(DsDoorbellPlayFocusModeActivity.this,
                                    Local.s(getString(R.string.switched_to_ir_cut)), Toast.LENGTH_SHORT).show();
                        } else {
                            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_on);
                            Toast.makeText(DsDoorbellPlayFocusModeActivity.this,
                                    Local.s(getString(R.string.switched_to_color_night)), Toast.LENGTH_SHORT).show();
                        }
                        break;
                    case DsCamCmd.CONNECT_STATUS_CHANGED:
                        refreshVideoViewState(device);
                        break;
                    default:
                        break;
                }


            }
        });

    }

    private AlertDialogV2 offlineDialog;

    public void showDeviceOfflineDialog(Device device) {
        if (offlineDialog != null && offlineDialog.isShowing()) {
            return;
        }
        offlineDialog = AlertDialogV2.createBuilder(this)
                .setContent(this.getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(this.getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        IPCManager.getInstance().connectDevice(device);
                        mBinding.videoView.showLoading();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        EventBus.getDefault().post(new DsCamNetWorkSetting(device.getId()));
                        finish();
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    private void hideDeviceOfflineDialog(Device device) {
        if (offlineDialog == null || !offlineDialog.isShowing()) {
            return;
        }
        offlineDialog.dismiss();
    }

    @Override
    public void onPrepared() {

    }

    @Override
    public void onStarted() {

    }

    @Override
    public void onPlaying() {
        Log.d(TAG, "onPlaying: ");
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                enableButton();
                mBinding.videoView.hideAllIcon();
                kRealTimePlayView.setScale(1.5f);
            }
        });
    }

    @Override
    public void onPaused() {

    }

    @Override
    public void onRelease() {

    }

    @Override
    public void onWaiting() {

    }

    @Override
    public void onError(int i, String s) {

    }

    @Override
    public void onCompletion() {

    }

    @Override
    public void onPlayIconClick(int position, CameraVideoView videoViewRoot, View parent) {

    }

    @Override
    public void onErrorIconClick(int position, CameraVideoView videoViewRoot, View parent) {
        IPCManager.getInstance().connectDevice(device);
        mBinding.videoView.showLoading();
    }

    @Override
    public void onFullscreenIconClick(int position, CameraVideoView videoView, View parent) {

    }
}


