package com.dinsafer.module.doorbell.setting.network;

import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.AdapterView;

import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.TimeZoneLayoutBinding;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.doorbell.setting.DsDoorbellAdvanceSettingFragment;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.settting.adapter.TimePhoneZoneAdapter;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_dscam.bean.CamBleWifiInfo;
import com.dinsafer.module_dscam.doorbell.DsDoorbellNetworkManager;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Rinfon on 16/7/8.
 */
public class DsDoorbellNetworkChangeWifiListFragment extends MyBaseFragment<TimeZoneLayoutBinding> implements DsDoorbellNetworkManager.IWifiListCallBack, BaseMainActivity.ILoadingCallBack, IPluginBindCallBack {

    private TimePhoneZoneAdapter mAdapter;

    private ArrayList<String> mData = new ArrayList<String>();

    boolean isCanClose = false;
    private DsDoorbellNetworkManager mBinder;

    private String deviceID;
    private AlertDialogV2 offlineDialog;

    public static DsDoorbellNetworkChangeWifiListFragment newInstance(boolean isCanClose, String deviceID) {
        DsDoorbellNetworkChangeWifiListFragment fragment = new DsDoorbellNetworkChangeWifiListFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean("isCanClose", isCanClose);
        bundle.putString("deviceID", deviceID);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.time_zone_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> close());
        mBinding.commonBarRight.setOnClickListener(v -> toRefresh());
    }

    @Override
    public void initData() {
        super.initData();
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof DsDoorbellNetworkManager)) {
            DDLog.e(TAG, "Error DsDoorbellNetworkManager binder.");
            showErrorToast();
            return;
        }
        mBinder = (DsDoorbellNetworkManager) pluginBinder;
        mBinder.setWifiListCallBack(this);
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.wifi_list));
        mData = new ArrayList<String>();
        mAdapter = new TimePhoneZoneAdapter(getActivity(), mData);
        mBinding.choosePhoneZoneListview.setAdapter(mAdapter);
        isCanClose = getArguments().getBoolean("isCanClose");
        deviceID = getArguments().getString("deviceID");
        if (isCanClose) {
            mBinding.commonBarBack.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBarBack.setVisibility(View.GONE);
        }

        mBinding.commonBarRight.setImageResource(R.drawable.btn_userpage_refresh);
        mBinding.commonBarRight.setVisibility(View.VISIBLE);
        mBinding.choosePhoneZoneListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                getDelegateActivity().addCommonFragment(
                        DsDoorbellNetworkChangeWifiSettingFragment.newInstance(mData.get(position), deviceID));
            }
        });
        mBinder.addBindCallBack(this);
        toRefresh();
    }

    @Override
    public boolean onBackPressed() {
        if (isCanClose) {
            return false;
        } else {
            return true;
        }
    }

    public void close() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinder.setWifiListCallBack(null);
        mBinder.removeBindCallBack(this);
        mBinder.stop();
    }

    public void toRefresh() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                mBinder.getWifiList();
            }
        }, 1000);
    }

    @Override
    public void onBindResult(int i, String s) {
        if (!this.isVisible()) {
            return;
        }
        if (i == 1) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
//                    虽然返回了1，但是立刻去连ipc，会离线，所以还是取消吧
//                    IPCManager.getInstance().connectIPC(IPCManager.getInstance().getDsCamDeviceByID(id));
                    closeLoadingFragmentWithCallBack();
                    close();
                    if (ActivityController.getInstance().getFragment(DsDoorbellAdvanceSettingFragment.class) != null) {
                        getDelegateActivity().removeToFragment(DsDoorbellAdvanceSettingFragment.class.getName());
                    } else {
                        getDelegateActivity().removeAllCommonFragment();
                    }
                }
            });
        } else if (i == -70) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.tiggle_has_plug));
            getDelegateActivity().removeToFragment(DsDoorbellNetworkBleScanFragment.class.getName());
        } else if (i == -73) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.add_ipc_limit_error));
            getDelegateActivity().removeToFragment(DsDoorbellNetworkBleScanFragment.class.getName());
        } else {
            closeLoadingFragmentWithCallBack();
            showErrorConnectDialog(i);
        }

    }


    @Override
    public void onWifiListCallBack(List<CamBleWifiInfo> list, boolean spfn) {
        mData.clear();
        for (CamBleWifiInfo info : list) {
            mData.add(info.getSsid());
        }
        mAdapter.setData(mData);
        mAdapter.notifyDataSetChanged();
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (mData.contains(DBUtil.SGet(DBKey.REMEMBER_WIFI))) {
            if (offlineDialog != null && offlineDialog.isShowing()) {
                return;
            }
            offlineDialog = AlertDialogV2.createBuilder(getActivity())
                    .setContent(Local.s(this.getResources().getString(R.string.ipc_remember_ssid_hint))
                            + DBUtil.SGet(DBKey.REMEMBER_WIFI))
                    .setOk(this.getResources().getString(R.string.Confirm))
                    .setCancel(this.getResources().getString(R.string.select_other_network))
                    .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            showTimeOutLoadinFramgmentWithCallBack(DsDoorbellNetworkChangeWifiListFragment.this);
                            mBinder.setSsid(DBUtil.SGet(DBKey.REMEMBER_WIFI));
                            mBinder.setSsidPassword(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
                            mBinder.bindDevice(null);
                        }
                    })
                    .preBuilder();
            offlineDialog.setCancel(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mBinder.removeBindCallBack(DsDoorbellNetworkChangeWifiListFragment.this);
                    offlineDialog.dismiss();
                }
            });
            offlineDialog.show();
        }
    }

    private AlertDialogV2 errorDialog;

    private void showErrorConnectDialog(int code) {
        if (errorDialog != null && errorDialog.isShowing()) {
            return;
        }
        final String msg = Local.s(DsCamUtils.getConfigErrorMsgByCode(code));
        errorDialog = AlertDialogV2.createBuilder(getActivity())
                .setContent(msg)
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithCallBack(DsDoorbellNetworkChangeWifiListFragment.this);
                        mBinder.setSsid(DBUtil.SGet(DBKey.REMEMBER_WIFI));
                        mBinder.setSsidPassword(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
                        mBinder.bindDevice(null);
                    }
                })
                .setCanCancel(true)
                .setCancel(getString(R.string.Cancel))
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        errorDialog.dismiss();
                    }
                })
                .preBuilder();
        errorDialog.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                errorDialog.dismiss();
                close();
                getDelegateActivity().removeToFragment(DsDoorbellAdvanceSettingFragment.class.getName());
            }
        });
        errorDialog.show();
    }

    @Override
    public void onTimeout() {
        showErrorConnectDialog(-999);
    }
}

