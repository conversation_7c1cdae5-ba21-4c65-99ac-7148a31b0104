package com.dinsafer.module.doorbell.setting;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.IPCManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDsdoorbellSettingBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dscam.DsCamIPCSDCardFragment;
import com.dinsafer.dscam.DsCamNetWorkSetting;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.doorbell.chime.ChimeAddEvent;
import com.dinsafer.module.doorbell.chime.ChimeListFragment;
import com.dinsafer.module.doorbell.chime.ChimeListRefreshEvent;
import com.dinsafer.module.iap.CloudStorageFragment;
import com.dinsafer.module.iap.CloudStorageServiceFragment;
import com.dinsafer.module.iap.CloudStorageServiceHelper;
import com.dinsafer.module.iap.IapRootActivity;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module.settting.ui.EdittextDialog;
import com.dinsafer.module.settting.ui.IPCListNewFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.RegxUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.Map;

/**
 * Doorbell设置页
 */
public class DsDoorbellSettingFragment extends MyBaseFragment<FragmentDsdoorbellSettingBinding> implements IDeviceCallBack, IDeviceListChangeListener {
    private Device device;
    private static Handler handler = new Handler();

    public static DsDoorbellSettingFragment newInstance(String id) {
        Bundle args = new Bundle();
        args.putSerializable("id", id);
        DsDoorbellSettingFragment fragment = new DsDoorbellSettingFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_dsdoorbell_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        String id = getArguments().getString("id");
        device = IPCManager.getInstance().getDsDoorbellDeviceByID(id);
        if (device == null) {
            showErrorToast();
            removeSelf();
            return;
        }
        mBinding.title.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.title.commonBarTitle.setLocalText("");
        mBinding.title.vDivider.setVisibility(View.GONE);

        mBinding.tvPluginName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showChangeNameDialog();
            }
        });
        mBinding.tvRecordSettingTitle.setLocalText(getString(R.string.ipc_setting_record_setting));
//        //移动侦测
//        mBinding.tvMotionDetection.setLocalText(Local.s(getString(R.string.ipc_motion_detection_dscam)));
//        mBinding.tvMotionDetection.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                getDelegateActivity().addCommonFragment(DsCamMotionDetectionFragment.newInstance(device.getId()));
//            }
//        });

        mBinding.tvDeviceSetting.setLocalText(getString(R.string.device_settings));
        mBinding.tvAdvanceSetting.setLocalText(getString(R.string.advanced_settings));
        mBinding.tvAdvanceSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDelegateActivity().addCommonFragment(DsDoorbellAdvanceSettingFragment.newInstance(device.getId()));
            }
        });

        mBinding.tvNoSdCard.setLocalText(getString(R.string.ipc_no_sd_card));
        mBinding.tvSdCard.setLocalText(getString(R.string.ipc_sd_card));
        mBinding.tvSdCard.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                long total = (long) MapUtils.get(device.getInfo(), "tfCapacity", 0);
                if (total == 0) {
                    return;
                }
                getDelegateActivity().addCommonFragment(DsCamIPCSDCardFragment.newInstance(device.getId()));
            }
        });

        mBinding.tvTitleAlertService.setLocalText(getString(R.string.iap_free_trial));
        mBinding.tvTurnOnService.setLocalText(getString(R.string.ipc_settting_turn_on_service));
        mBinding.switchCloudService.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
//                modifyIPCAlertServiceSettingEnable(isOn);
            }
        });

        mBinding.tvServiceSetting.setLocalText(getString(R.string.iap_cloud_storage));
        mBinding.tvServiceSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                toServiceSetting();
                if (!CloudStorageServiceHelper.getInstance().isDsDoorbellServiceOpen()) {
//                    getMainActivity().addCommonFragment(CloudStorageServiceRenewFragment.newInstance(device.getId(), -1, true));
//                     getMainActivity().addCommonFragment(CloudStorageFragment.newInstance());
                    IapRootActivity.start(getDelegateActivity(), IapRootActivity.START_FRAG_CLOUD_STORAGE);
                } else {
                    final Bundle args = new Bundle();
                    args.putString("deviceId", device.getId());
                    IapRootActivity.start(getDelegateActivity(), IapRootActivity.START_FRAG_CLOUD_STORAGE_SERVICE, args);
                    // getMainActivity().addCommonFragment(CloudStorageServiceFragment.newInstance(device.getId()));
                }
            }
        });

        mBinding.tvChimesAdded.setLocalText(getResources().getString(R.string.chimes_added));
        mBinding.llChime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDelegateActivity().addCommonFragment(ChimeListFragment.newInstance(device.getId()));
            }
        });

        if (AppConfig.Functions.SUPPORT_CLOUD_SERVICE_DS_DOORBELL) {
            mBinding.llAlertService.setVisibility(View.VISIBLE);
        }

        updata();
        loadData();
        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
    }

    private void loadData() {
        if (DsCamUtils.isDeviceConnected(device)) {
            getIPCInfo();
        } else if (DsCamUtils.isDeviceConnecting(device)) {
            showTimeOutLoadinFramgment();
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showDeviceOfflineDialog(device);
        }
    }

    private AlertDialogV2 offlineDialog;

    public void showDeviceOfflineDialog(Device device) {
        if (offlineDialog != null && offlineDialog.isShowing()) {
            return;
        }
        offlineDialog = AlertDialogV2.createBuilder(getActivity())
                .setContent(this.getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(this.getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        IPCManager.getInstance().connectDevice(device);
                        showTimeOutLoadinFramgmentWithErrorAlert();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        EventBus.getDefault().post(new DsCamNetWorkSetting(device.getId()));
                        removeSelf();
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    private void getIPCInfo() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", DsCamCmd.GET_PARAMS);
        device.submit(parms);
        DDLog.i(TAG, "发送获取dscam信息指令");
    }

    @Override
    public void initData() {
        super.initData();
        if (null == device) {
            return;
        }
        EventBus.getDefault().register(this);
        mBinding.tvPluginName.setText((String) MapUtils.get(device.getInfo(), "name", ""));
        mBinding.tvPlugsType.setLocalText(getResources().getString(R.string.video_doorbell));
        mBinding.tvPlugsId.setText(device.getId());
        device.registerDeviceCallBack(this);
        if (DsCamUtils.isDeviceConnected(device)) {
            mBinding.tvSdCard.setAlpha(1f);
            mBinding.tvSdCard.setCanTouch(true);
            mBinding.llChime.setAlpha(1f);
            mBinding.llChime.setEnabled(true);
        } else {
            mBinding.tvSdCard.setAlpha(0.8f);
            mBinding.tvSdCard.setCanTouch(false);
            mBinding.llTurnOnService.setAlpha(0.8f);
            mBinding.switchCloudService.setEnabled(false);
            mBinding.llChime.setAlpha(0.8f);
            mBinding.llChime.setEnabled(false);
        }

//        startAlertServiceLoading();
//        getIPCAlertServiceSetting();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        handler.removeCallbacksAndMessages(null);
        DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(this);
        if (device != null)
            device.unregisterDeviceCallBack(this);
    }

    public void showChangeNameDialog() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultName(mBinding.tvPluginName.getText().toString())
                .setContent(getResources().getString(R.string.rename_accessory))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
//                                            change ipc password
                        if (TextUtils.isEmpty(string) || !RegxUtil.isLegalName(string)) {
                            getMainActivity().showTopToast(R.drawable.icon_toast_fail,  Local.s(getString(R.string.name_format_error_prefix)) + getString(R.string.name_format_error_char));
                            dialog.dismiss();
                            return;
                        }
                        dialog.dismiss();
                        mBinding.tvPluginName.setText(string);
                        saveName();
                    }
                })
                .preBuilder()
                .show();
    }

    private void saveName() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DinConst.CMD_SET_NAME);
        data.put("name", mBinding.tvPluginName.getText().toString());
        device.submit(data);
    }

    public void updata() {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.tvPluginName.setText((String) MapUtils.get(device.getInfo(), "name", ""));
                long total = (long) MapUtils.get(device.getInfo(), "tfCapacity", 0);
                if (total == 0) {
                    mBinding.tvNoSdCard.setVisibility(View.VISIBLE);
                    mBinding.imSdCardNor.setVisibility(View.GONE);
                } else {
                    mBinding.tvNoSdCard.setVisibility(View.GONE);
                    mBinding.imSdCardNor.setVisibility(View.VISIBLE);
                }

                int chime_count = (int) MapUtils.get(device.getInfo(), "chime_count", 0);
                mBinding.tvChimesCount.setText(String.valueOf(chime_count));
            }
        });
    }


    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (device == null || !device.getId().equals(id) || TextUtils.isEmpty(cmd)) {
            return;
        }
        Log.d(TAG, "onCmdCallBack: " + cmd);

        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                switch (cmd) {
                    case DsCamCmd.CMD_CONNECT:
                    case DsCamCmd.CONNECT_STATUS_CHANGED:
                        int state = DeviceHelper.getInt(device, "networkState", -2);
                        if (state == -1) {
                            loadData();
                            return;
                        }
                        if (state == 1) {
                            loadData();
                            return;
                        }
                        break;
                    case DinConst.CMD_SET_NAME:
                        if (((int) map.get("status")) == 1) {
                            updata();
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                        } else {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showErrorToast();
                        }
                        break;
                    case DinConst.CMD_DELETE_DEVICE:
                        if (((int) map.get("status")) == 1) {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            removeSelf();
                            getDelegateActivity().removeToFragment(IPCListNewFragment.class.getName());
                        } else {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showErrorToast();
                        }
                        break;
                    case DsCamCmd.GET_PARAMS:
                        DDLog.i(TAG, "收到获取dscam信息指令：" + ((int) map.get("status")));
                        if (((int) map.get("status")) == 1) {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            DDLog.i(TAG, "get ipc info success:" + map.get("version"));
                        } else {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                            showErrorToast();
                        }
                        updata();
                        break;
                    default:
                        break;
                }
            }
        });

    }

    @Override
    public boolean onBackPressed() {
        removeSelf();
        return true;
    }

    @Override
    public void onDeviceAdd(Device device) {

    }

    @Override
    public void onDeviceRemove(Device s) {
        if (device == null || s == null || !s.getId().equals(device.getId())) {

            return;
        }
        getDelegateActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                removeSelf();
                getDelegateActivity().removeToFragment(IPCListNewFragment.class.getName());
            }
        });

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChimeAddEvent(ChimeAddEvent event) {
        updata();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChimeAddEvent(ChimeListRefreshEvent event) {
        updata();
    }


}
