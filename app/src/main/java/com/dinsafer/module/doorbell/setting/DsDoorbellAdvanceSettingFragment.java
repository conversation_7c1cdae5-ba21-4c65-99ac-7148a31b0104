package com.dinsafer.module.doorbell.setting;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentDsdoorbellAdvanceSettingBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.doorbell.setting.network.DsDoorbellNetworkBleScanFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.IPCListNewFragment;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.Local;

import java.util.HashMap;
import java.util.Map;

/**
 * DsDoorbell高级设置页
 */
public class DsDoorbellAdvanceSettingFragment extends MyBaseFragment<FragmentDsdoorbellAdvanceSettingBinding> implements IDeviceCallBack, IDeviceListChangeListener {
    private Device device;
    private boolean hasDelete;

    public static DsDoorbellAdvanceSettingFragment newInstance(String id) {
        Bundle args = new Bundle();
        args.putSerializable("id", id);
        DsDoorbellAdvanceSettingFragment fragment = new DsDoorbellAdvanceSettingFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_dsdoorbell_advance_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        String id = getArguments().getString("id");
        device = IPCManager.getInstance().getDsDoorbellDeviceByID(id);
        if (device == null) {
            showErrorToast();
            removeSelf();
            return;
        }
        mBinding.title.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.title.commonBarTitle.setLocalText("");
        mBinding.tvDeviceIdTitle.setLocalText(getString(R.string.ipc_device_id));
        mBinding.tvDeviceCurrentNetworkTitle.setLocalText(getString(R.string.dscam_advance_current_network));
        mBinding.tvDeviceCurrentBatteryTitle.setLocalText(getString(R.string.battery_quantity));
        mBinding.tvDeviceIpAddressTitle.setLocalText(getString(R.string.ip_address));
        mBinding.tvChangeNetwork.setLocalText(getString(R.string.ipc_change_network));
        mBinding.ipcTimeZone.setLocalText(getString(R.string.sync_time_zone));
        mBinding.ipcReset.setLocalText(getString(R.string.ipc_reset_device));
        mBinding.tvIpcVersionTitle.setLocalText(getString(R.string.ipc_version));
        mBinding.tvDeviceWifiRssiTitle.setLocalText(getString(R.string.dscam_advance_wifi_rssi));
        mBinding.tvDeviceMacAddressTitle.setLocalText(getString(R.string.mac_address));
        mBinding.tvDeviceId.setText(device.getId());
        int battery = DsCamUtils.getDeviceBattery(device);
        mBinding.tvDeviceCurrentBattery.setText(battery > 0 ? battery + "%" : "");
        mBinding.tvDeviceCurrentNetwork.setText(DeviceHelper.getString(device, "ssid", ""));
        mBinding.tvDeviceIpAddress.setText(DeviceHelper.getString(device, "ip", ""));
        mBinding.tvIpcVersion.setText(DeviceHelper.getString(device, "version", ""));
        mBinding.tvDeviceWifiRssi.setText(DeviceHelper.getInt(device, "rssi", 0) + "");
        mBinding.tvDeviceMacAddress.setText(DeviceHelper.getString(device, "mac", ""));


        mBinding.title.commonBarTitle.setLocalText(getString(R.string.advanced_setting));
        mBinding.title.commonBarRightIcon.setImageResource(R.drawable.icon_nav_more);
        mBinding.title.commonBarRightIcon.setVisibility(View.VISIBLE);
        mBinding.title.commonBarRightIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showMoreSettingDialog();
            }
        });

        mBinding.ipcReset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AlertDialog.createBuilder(getDelegateActivity())
                        .setOk(getResources().getString(R.string.smart_plugs_list_delete_yes))
                        .setCancel(getResources().getString(R.string.smart_plugs_list_delete_no))
                        .setContent(getResources().getString(R.string.ipc_restore_device))
                        .setOKListener(new AlertDialog.AlertOkClickCallback() {
                            @Override
                            public void onOkClick() {
                                showTimeOutLoadinFramgmentWithErrorAlert();
                                restore();
                            }
                        })
                        .preBuilder()
                        .show();

            }
        });

        mBinding.ipcTimeZone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTimeOutLoadinFramgmentWithErrorAlert();
                Map<String, Object> data = new HashMap<>();
                data.put("cmd", DsCamCmd.SET_TZ);
                device.submit(data);
            }
        });

        mBinding.tvChangeNetwork.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDelegateActivity().addCommonFragment(DsDoorbellNetworkBleScanFragment.newInstance(device.getId()));
            }
        });

        device.registerDeviceCallBack(this);
        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
    }

    @Override
    public void initData() {
        super.initData();
    }

    private void restore() {
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DinConst.CMD_RESTORE_DEFAULT);
        device.submit(data);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
//        EventBus.getDefault().unregister(this);
        DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(this);
        if (device != null)
            device.unregisterDeviceCallBack(this);
    }

    public void updata() {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
            }
        });
    }


    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (device == null || !device.getId().equals(id) || TextUtils.isEmpty(cmd)) {
            return;
        }
        Log.d(TAG, "onCmdCallBack: " + cmd);

        switch (cmd) {
            case DinConst.CMD_DELETE_DEVICE:
                if (((int) map.get("status")) == 1) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    removeSelf();
                    getDelegateActivity().removeToFragment(IPCListNewFragment.class.getName());
                } else {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showErrorToast();
                }
                break;
            case DinConst.CMD_RESET_DEVICE:
                if (((int) map.get("status")) == 1) {
                    // deleteDevice(); 在dincore调用了
                } else {
                    hasDelete = false;
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showErrorToast();
                }
                break;
            case DsCamCmd.SET_TZ:
                if (((int) map.get("status")) == 1) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showSuccess();
                } else {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showErrorToast();
                }
                break;
            case DinConst.CMD_RESTORE_DEFAULT:
                if (((int) map.get("status")) == 1) {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                } else {
                    closeTimeOutLoadinFramgmentWithErrorAlert();
                    showErrorToast();
                }
                break;
            default:
                break;
        }
    }

    private void showMoreSettingDialog() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setOtherButtonTitles(DsCamUtils.isDeviceConnected(device) ?
                        Local.s(getString(R.string.reset))
                        : Local.s(getString(R.string.delete)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            showConfirmDelDialog();
                        }
                    }
                }).show();

    }

    private void showConfirmDelDialog() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.ok))
                .setCancel(getResources().getString(R.string.cancel))
                .setContent(DsCamUtils.isDeviceConnected(device) ?
                        getResources().getString(R.string.reset_device_content_hint)
                        : getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        if (DsCamUtils.isDeviceConnected(device)) {
//                            TODO 发送恢复出厂指令和删除ipc指令，最后已删除ipc指令为准
                            resetDevice();
                        } else {
                            deleteDevice();
                        }
                    }
                })
                .preBuilder()
                .show();
    }

    private void resetDevice() {
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DinConst.CMD_RESET_DEVICE);
        device.submit(data);
    }

    private synchronized void deleteDevice() {

//        为什么要这样子处理，因为调用reset的时候，ipc有可能返回多次reset成功给我们，那么就会调用多次删除设备
        if (hasDelete) {
            MsctLog.i(TAG, "已经删除过ipc了，不需要在删除了");
            return;
        }
        hasDelete = true;
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DinConst.CMD_DELETE_DEVICE);
        data.put("id", device.getId());
        device.submit(data);
    }

    @Override
    public boolean onBackPressed() {
        removeSelf();
        return true;
    }

    @Override
    public void onDeviceAdd(Device device) {

    }

    @Override
    public void onDeviceRemove(Device s) {
        if (device == null || s == null || !s.getId().equals(device.getId())) {

            return;
        }
        getDelegateActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                closeTimeOutLoadinFramgmentWithErrorAlert();
                removeSelf();
                getDelegateActivity().removeToFragment(IPCListNewFragment.class.getName());
            }
        });

    }
}
