package com.dinsafer.module.doorbell.setting.network;

import android.animation.ObjectAnimator;
import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.dinsafer.DinSaferApplication;
import com.dinsafer.config.APIKey;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.utils.BleHelper;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.BleStepScanDeviceLayoutBinding;
import com.dinsafer.model.BleCloseTimerEvent;
import com.dinsafer.model.BleDeviceSimpleEntry;
import com.dinsafer.model.BleStartScanEvent;
import com.dinsafer.model.WindowFocusChangedEvent;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.BleCheckBluetoothDialog;
import com.dinsafer.module.add.ui.adapter.BleScanDeviceAdapter;
import com.dinsafer.module_dscam.doorbell.DsDoorbellNetworkManager;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DisplayUtil;
import com.dinsafer.util.ScreenUtils;
import com.github.sahasbhop.apngview.ApngDrawable;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * dsdoorbell 网络修改扫描页面
 */
public class DsDoorbellNetworkBleScanFragment extends MyBaseFragment<BleStepScanDeviceLayoutBinding> implements BleHelper.ConnectCallback {

    private Subscription connectTimer;

    private BleScanDeviceAdapter bleScanDeviceAdapter;
    private int listMarginTopPx;
    private boolean isUp = true;
    private ArrayList<BleDevice> bleDeviceArrayList;
    private ArrayList<BleDeviceSimpleEntry> simpleBleList;
    private boolean isScan = false;

    private String TAG = "DsDoorbellNetworkBleScanFragment bletest";
    public final static int ANIM_UP_TIME = 300;
    private DsDoorbellNetworkManager mBinder;
    private String id;

    public static DsDoorbellNetworkBleScanFragment newInstance(String deviceID) {
        DsDoorbellNetworkBleScanFragment dsCamBleScanFragment = new DsDoorbellNetworkBleScanFragment();
        Bundle bundle = new Bundle();
        bundle.putString("deviceID", deviceID);
        dsCamBleScanFragment.setArguments(bundle);
        return dsCamBleScanFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.ble_step_scan_device_layout;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        clean();
        if (null != mBinder) {
            mBinder.destroyBinder();
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> toBack());
        mBinding.title.setOnClickListener(v -> toUp());
        mBinding.imgBtnUp.setOnClickListener(v -> toUp());
        mBinding.commonBarRight.setOnClickListener(v -> toHelp());
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        bleDeviceArrayList = new ArrayList<BleDevice>();
        simpleBleList = new ArrayList<BleDeviceSimpleEntry>();
        id = getArguments().getString("deviceID");
        tmpBleDeviceArrayList = new ArrayList<BleDevice>();
        tmpSimpleBleList = new ArrayList<BleDeviceSimpleEntry>();
        mBinding.hintToCloseToPhone.setLocalText(getResources().getString(R.string.ble_doorbell_scan_close_to_phone));
        mBinding.commonBarRight.setLocalText(getResources().getString(R.string.ble_add_no_ipc_found));
        mBinding.title.setLocalText(getResources().getString(R.string.ble_scan_list_title));

        /**
         * listMarginTopPx 为layout离顶部的距离
         */
        listMarginTopPx = DisplayUtil.dip2px(getContext(), 360.0f);

        mBinding.rvDeviceList.setLayoutManager(new LinearLayoutManager(getDelegateActivity(), LinearLayoutManager.VERTICAL, false));
        bleScanDeviceAdapter = new BleScanDeviceAdapter(simpleBleList);
        mBinding.rvDeviceList.setAdapter(bleScanDeviceAdapter);

        bleScanDeviceAdapter.setOnItemClick(new BleScanDeviceAdapter.OnItemClick() {
            @Override
            public void connect(BleDeviceSimpleEntry bleDeviceSimpleEntry, int position) {
                connectDevice(position);
            }
        });


        mBinding.lyDeviceList.setVisibility(View.GONE);

        mBinding.centerIcon.setImageResource(R.drawable.img_vdoorbell_wireless_power_on);

        DinSDK.getPluginActivtor().setup(DinSaferApplication.getAppContext());
        DinSDK.getPluginActivtor().createDsDoorbellNetworkManager();
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof DsDoorbellNetworkManager)) {
            DDLog.e(TAG, "Error DsDoorbellNetworkManager binder.");
            showErrorToast();
            return;
        }
        mBinder = (DsDoorbellNetworkManager) pluginBinder;
    }

    private void connectDevice(int position) {
        closeScanTimer();

//        showLoadingFragment(LoadingFragment.BLUE);

        mBinder.connectDevice(bleDeviceArrayList.get(position),
                DsDoorbellNetworkBleScanFragment.this);

        connectTimer = Observable.interval(APIKey.BLE_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                .take(1)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "subscribe里");
                        fail();
                    }
                });
    }


    public void toHelp() {
        showOpenDeviceBle();
    }

    private void toStartAnim() {
        if (mBinding.centerIcon == null) {
            return;
        }

        ApngDrawable apngDrawable = ApngDrawable.getFromView(mBinding.centerIcon);
        if (apngDrawable == null) return;

        if (!apngDrawable.isRunning()) {
            apngDrawable.start(); // Stop animation
        }
    }


    private void toStopAnim() {
        DDLog.d(TAG, "toStopAnim");
        if (mBinding.centerIcon == null) {
            return;
        }

        ApngDrawable apngDrawable = ApngDrawable.getFromView(mBinding.centerIcon);
        if (apngDrawable == null) return;

        if (apngDrawable.isRunning()) {
            apngDrawable.stop(); // Stop animation
        }
    }


    @Override
    public void onEnterFragment() {
        super.onEnterFragment();
        bleDeviceArrayList.clear();
        simpleBleList.clear();
        bleScanDeviceAdapter.notifyDataSetChanged();
        startScanTimer();
        toStartAnim();
    }

    @Override
    public void onPauseFragment() {
        DDLog.d(TAG, "onPauseFragment");
        super.onPauseFragment();
        toStopAnim();

    }

    @Override
    public void onExitFragment() {
        DDLog.d(TAG, "onExitFragment");
        super.onExitFragment();
        toStopAnim();
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        toStartAnim();
        startScanTimer();
        if (null != mBinder && !BleManager.getInstance().isBlueEnable()) {
            showOpenPhoneBle();
        }
    }

    public void toBack() {
        getDelegateActivity().removeCommonFragmentAndData(this, true);
//        removeSelf();
    }

    private ArrayList<BleDevice> mdata;

    public void toUp() {
        ObjectAnimator animator;
        if (isUp) {

            animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", listMarginTopPx, 0);
            mBinding.imgBtnUp.setImageDrawable(DinSaferApplication.getAppContext().getResources().getDrawable(R.drawable.btn_cell_device_down));
        } else {
            animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", 0, listMarginTopPx);
            mBinding.imgBtnUp.setImageDrawable(DinSaferApplication.getAppContext().getResources().getDrawable(R.drawable.btn_cell_device_up));
        }

        animator.setDuration(ANIM_UP_TIME);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.start();
        isUp = !isUp;
    }

    private boolean isNowAppear = false;

    public void toAppear(boolean isAppear) {
        DDLog.d(TAG, "toAppear");
        if (this.isNowAppear == isAppear) {
            return;
        }
        if (isAppear) {

            ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", ScreenUtils.getScreenHeight(getDelegateActivity()), listMarginTopPx);
            animator.setDuration(300);
            animator.setInterpolator(new DecelerateInterpolator());
            animator.start();

        } else {

            ObjectAnimator animator = ObjectAnimator.ofFloat(mBinding.lyDeviceList, "translationY", listMarginTopPx, ScreenUtils.getScreenHeight(getDelegateActivity()));
            animator.setDuration(300);
            animator.setInterpolator(new DecelerateInterpolator());
            animator.start();
        }

        this.isNowAppear = isAppear;
    }

    private ArrayList<BleDevice> tmpBleDeviceArrayList;
    private ArrayList<BleDeviceSimpleEntry> tmpSimpleBleList;

    private final BleScanCallback bleScanCallback = new BleScanCallback() {
        @Override
        public void onScanStarted(boolean success) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    DDLog.d(TAG, "开始扫描");
                    tmpBleDeviceArrayList.clear();
                    tmpSimpleBleList.clear();
                    if (bleDeviceArrayList.size() <= 0) {
                        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
                            return;
                        }
                        startShowTipTimer();
                    }
                }
            });
        }

        @Override
        public void onScanning(BleDevice bleDevice) {
            // 扫描到一个符合扫描规则的BLE设备（主线程）
            if (bleDevice.getName() == null) {
                return;
            }
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    DDLog.d(TAG, "扫描中");

                    if (!getDelegateActivity().isFragmentInTop(DsDoorbellNetworkBleScanFragment.class.getSimpleName())) {
                        return;
                    }

                    /**
                     * 名字：ydEqumoVNyhPiKfg
                     * 显示：device id 的后四位
                     */
                    String name = bleDevice.getName();
                    if (id.equalsIgnoreCase(name)) {
                        bleDeviceArrayList.add(bleDevice);
                        connectDevice(0);
                    }

                    /**
                     * 如果扫描到，就取消扫描不到出现打开主机蓝牙提示的定时器
                     */
                    closeShowTipTimer();

                }
            });
        }

        @Override
        public void onScanFinished(List<BleDevice> scanResultList) {
            if (!getDelegateActivity().isFragmentInTop(DsDoorbellNetworkBleScanFragment.class.getSimpleName())) {
                return;
            }

            getDelegateActivity().runOnUiThread(
                    new Runnable() {
                        @Override
                        public void run() {
// 扫描结束，列出所有扫描到的符合扫描规则的BLE设备（主线程）
                            DDLog.d(TAG, "扫描结束");
                            isScan = false;
//
//                            DDLog.d(TAG, "tmpBleDeviceArrayList size is " + tmpBleDeviceArrayList.size());
//                            DDLog.d(TAG, "simpleBleList size is " + simpleBleList.size());
//                            bleDeviceArrayList.clear();
//                            bleDeviceArrayList.addAll(tmpBleDeviceArrayList);
//                            simpleBleList.clear();
//                            simpleBleList.addAll(tmpSimpleBleList);
//                            bleScanDeviceAdapter.notifyDataSetChanged();

                        }
                    });
        }
    };


    BleCheckBluetoothDialog dialog = null;

    /**
     * 如果弹窗选择了退出当前页，那么久不再在onWindowFacous做判断。是否弹出弹窗
     */
    private boolean isQuit = false;

    public void showOpenPhoneBle() {
        if (dialog != null && dialog.isShowing()) {
            return;
        } else {
            isQuit = false;
            dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.BLE_DIALOG_CHECK_PHONE);
            dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    dialog.dismiss();
                    if (null != mBinder && !BleManager.getInstance().isBlueEnable()) {
                        showOpenPhoneBle();
                    } else {
                        startScanTimer();
                    }
                }

                @Override
                public void clickCanal() {
                    isQuit = true;
                    dialog.dismiss();
                }
            });
            dialog.show();
        }
    }

    private void stopScan() {
        try {
            if (isScan) {
                BleManager.getInstance().cancelScan();
                isScan = false;
            }

        } catch (Exception e) {

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(WindowFocusChangedEvent ev) {
        if (!BleManager.getInstance().isBlueEnable() && !isQuit) {
            showOpenPhoneBle();
        }
    }

    public void showOpenDeviceBle() {
        /**
         * 打开弹窗，提示检查主机蓝牙
         */
        if (dialog != null && dialog.isShowing()) {
            DDLog.d(TAG, "dialog != null && dialog.isShowing()");
            return;
        } else {
            DDLog.d(TAG, "dialog == null ||  dialog.isNotShowing()");
            dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.DOORBELL_CONFIG_NETWORK);
            dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
                @Override
                public void clickOk() {
                    dialog.dismiss();
                    if (null != mBinder) {
                        if (BleManager.getInstance().isBlueEnable()) {
                            startScanTimer();
                        }
                    }
                }

                @Override
                public void clickCanal() {
                    dialog.dismiss();
                    removeSelf();
                }
            });
            dialog.show();
        }

    }

    private Subscription scanTimer;

    /**
     * 启动定时器
     */
    public void startScanTimer() {
//        //扫描超时时间+1

        closeScanTimer();
        scanTimer = Observable.interval(0, APIKey.BLE_SCAN_TIMEOUT + 500, TimeUnit.MILLISECONDS)
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "onNext");
                        mBinder.discoveryDevice(APIKey.BLE_SCAN_TIMEOUT, bleScanCallback);
                    }
                });

    }

    public void closeScanTimer() {
        if (scanTimer != null && !scanTimer.isUnsubscribed()) {
            scanTimer.unsubscribe();
        }
        mBinder.stopDiscoveryDevice();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleStartScanEvent ev) {
//        startTimer();
//        toStartAnim();
        DDLog.d(TAG, "BleStartScanEvent");
    }

    /**
     * 失败处理
     */
    private void fail() {
        DDLog.d(TAG, "onFail");
        simpleBleList.clear();
        bleDeviceArrayList.clear();
        bleScanDeviceAdapter.notifyDataSetChanged();
        showErrorToast();
        mBinder.stop();
//        closeLoadingFragment();
        if (connectTimer != null && !connectTimer.isUnsubscribed()) {
            connectTimer.unsubscribe();
        }
        startScanTimer();
        toStartAnim();
    }

    /**
     * 清空处理：定时器、loading
     */
    private void clean() {
        DDLog.d(TAG, "clean");
        closeScanTimer();
        if (connectTimer != null && !connectTimer.isUnsubscribed()) {
            connectTimer.unsubscribe();
        }
        closeShowTipTimer();
//        closeLoadingFragment();
    }


    /**
     * 关闭定时器，用于其他弹窗出现的时候————ble断开的时候
     *
     * @param ev
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(BleCloseTimerEvent ev) {
        clean();
    }

    private Subscription showTipTimer;

    /**
     * 在没有数据的情况下，每隔三分钟弹出一次help框
     */
    private void startShowTipTimer() {
        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
            showTipTimer.unsubscribe();
        }

        showTipTimer = Observable.interval(APIKey.BLE_SCAN_NO_DEVICE_TIP_TIMEOUT, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .compose(bindToLifecycle()) // 管理生命周期, 防止内存泄露
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onNext(Object o) {
                        toHelp();
                    }
                });

    }

    private void closeShowTipTimer() {
        if (showTipTimer != null && !showTipTimer.isUnsubscribed()) {
            showTipTimer.unsubscribe();
        }
    }

    @Override
    public boolean onBackPressed() {
        toBack();
        return true;

    }

    @Override
    public void onStartConnect() {

    }

    @Override
    public void onConnectFail(String s) {
        fail();
    }

    @Override
    public void onConnectSuccess() {
        DDLog.i(TAG, "onConnectSuccess");
        clean();
//        TODO wifi页面
        getDelegateActivity().addCommonFragment(DsDoorbellNetworkChangeWifiListFragment.newInstance(true, id));
    }

    @Override
    public void onDisConnected() {
        DDLog.i(TAG, "onDisConnected");
        showOpenDeviceBle();
    }
}
