package com.dinsafer.module.doorbell.play;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentIpcListNewBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dscam.DsCamCmd;
import com.dinsafer.dscam.DsCamPlayerManager;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.ipc.heartlai.event.IPCInfoChangeEvent;
import com.dinsafer.module.ipc.heartlai.event.IPCListUpdateEvent;
import com.dinsafer.module.settting.adapter.ipc.IpcItemSection;
import com.dinsafer.module.settting.adapter.ipc.IpcSectionAdapter;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/26 11:44 AM
 */
public class DoorbellListFragment extends MyBaseFragment<FragmentIpcListNewBinding> implements IDeviceCallBack {

    private List<Device> doorbellList = new ArrayList<>();
    private IpcSectionAdapter mAdapter;
    private LinearLayoutManager mLayoutManager;
    private String TAG = "dsdoorbell";

    public static DoorbellListFragment newInstance() {
        return new DoorbellListFragment();
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_ipc_list_new;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBar.commonBarTitle.setLocalText(getResources().getString(R.string.video_doorbell));
        mBinding.tvEmptyHint.setLocalText(getResources().getString(R.string.listview_empty));
        mBinding.commonBar.commonBarBack.setOnClickListener(v -> removeSelf());
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        showBlueTimeOutLoadinFramgment();
        mLayoutManager = new LinearLayoutManager(getContext());
        mAdapter = new IpcSectionAdapter();
        mBinding.rvIpc.setLayoutManager(mLayoutManager);
        mBinding.rvIpc.setAdapter(mAdapter);
        mBinding.rvIpc.addOnChildAttachStateChangeListener(new RecyclerView.OnChildAttachStateChangeListener() {
            @Override
            public void onChildViewAttachedToWindow(@NonNull View view) {
                DDLog.i(TAG, "onChildViewAttachedToWindow");
            }

            @Override
            public void onChildViewDetachedFromWindow(@NonNull View view) {

            }
        });
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        createPlugsList();
    }

    private void createPlugsList() {
        showTimeOutLoadinFramgment();
        doorbellList = IPCManager.getInstance().getAllDoorbellDevice();
        try {
            mAdapter.removeAllSections();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (doorbellList.size() <= 0) {
            mBinding.tvEmptyHint.setVisibility(View.VISIBLE);

        } else {
            mBinding.tvEmptyHint.setVisibility(View.GONE);
            for (Device device : doorbellList) {
                device.registerDeviceCallBack(DoorbellListFragment.this);
            }

            if (doorbellList.size() > 0)
                mAdapter.addSection(TAG, new IpcItemSection(getMainActivity(), TAG, (ArrayList<Device>) doorbellList, false, false));

        }
        closeLoadingFragment();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        for (Device device : doorbellList) {
            device.unregisterDeviceCallBack(this);
        }
        DsCamPlayerManager.getInstance().stopAllCam();
        IPCManager.getInstance().disconnectNotOnlineCamera();
    }

    @Override
    public void onCmdCallBack(String deviceID, String subCategory, String cmd, Map map) {

        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (Device device : doorbellList) {
                    if (deviceID.equals(device.getId())) {
                        if (DinConst.CMD_SET_NAME.equals(cmd)) {
                            mAdapter.notifyDataSetChanged();
                        } else if (DsCamCmd.CMD_CONNECT.equals(cmd)) {
                            mAdapter.notifyDataSetChanged();
                        }
                        return;
                    }
                }
            }
        });

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCListUpdateEvent ev) {
        createPlugsList();
        mAdapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(IPCInfoChangeEvent ev) {
        mAdapter.notifyDataSetChanged();
    }
}
