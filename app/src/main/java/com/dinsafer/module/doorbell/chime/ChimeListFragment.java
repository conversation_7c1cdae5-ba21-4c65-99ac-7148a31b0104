package com.dinsafer.module.doorbell.chime;

import android.os.Bundle;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.IPCManager;
import com.dinsafer.common.widget.rv.decoration.ItemDividerDecoration;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentChimesListBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module_dscam.doorbell.DsDoorbellCmd;
import com.dinsafer.ui.rv.BindMultiAdapter;
import com.dinsafer.ui.rv.OnBindItemClickListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/11/25
 */
public class ChimeListFragment extends MyBaseFragment<FragmentChimesListBinding> implements IDeviceCallBack {
    private String doorbellId;
    private Device doorbellDevice;
    private ArrayList<Device> chimeList;

    private BindMultiAdapter<ChimeListItemModel> adapter = new BindMultiAdapter<>();

    public static ChimeListFragment newInstance(String doorbellId) {
        Bundle args = new Bundle();
        args.putString("doorbellId", doorbellId);
        ChimeListFragment fragment = new ChimeListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_chimes_list;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        EventBus.getDefault().register(this);
        mBinding.title.commonBarTitle.setLocalText(getString(R.string.chimes_added));
        mBinding.title.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.btnAddChime.setLocalText(getString(R.string.add_a_chime));
        mBinding.btnAddChime.setOnClickListener(v -> getDelegateActivity().addCommonFragment(AddChimeFragment.newInstance(doorbellId)));
        mBinding.rv.setLayoutManager(new LinearLayoutManager(getContext()));
        mBinding.rv.setAdapter(adapter);
        mBinding.rv.addItemDecoration(new ItemDividerDecoration(getContext(), LinearLayoutManager.HORIZONTAL, false));
        adapter.setOnBindItemClickListener(new OnBindItemClickListener<ChimeListItemModel>() {
            @Override
            public void onItemClick(View v, int position, ChimeListItemModel model) {
                getDelegateActivity().addCommonFragment(ChimeSettingFragment.newInstance(doorbellId, model.getDevice().getId(), model.getDevice(),ChimeSettingFragment.FROM_TYPE_SETTING));
            }
        });
        mBinding.tvEmptyHint.setLocalText(getResources().getString(R.string.listview_empty));
    }

    @Override
    public void initData() {
        super.initData();
        doorbellId = getArguments().getString("doorbellId");
        doorbellDevice = IPCManager.getInstance().getDsDoorbellDeviceByID(doorbellId);
        if (doorbellDevice == null) {
            removeSelf();
            return;
        }

        doorbellDevice.registerDeviceCallBack(this);
        requestChimeList();
    }

    private void requestChimeList() {
        showTimeOutLoadinFramgment();
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DsDoorbellCmd.GET_CHIME_LIST);
        doorbellDevice.submit(data);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        doorbellDevice.unregisterDeviceCallBack(this);
    }

    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (!id.equals(doorbellId)) {
            return;
        }
        Log.d(TAG, "onCmdCallBack: " + cmd);
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                int status = (int) map.get("status");
                switch (cmd) {
                    case DsDoorbellCmd.GET_CHIME_LIST:
                        if (status == 1) {
                            chimeList = (ArrayList<Device>) map.get("chimes");
                            refreshChimeList();
                            closeLoadingFragment();
                        } else {
                            closeLoadingFragment();
                        }
                        break;
                }
            }
        });
    }

    private void refreshChimeList() {
        adapter.setNewData(null);
        if (chimeList == null || chimeList.size() == 0) {
            Log.d(TAG, "refreshChimeList: climeList empty");
            mBinding.rv.setVisibility(View.GONE);
            mBinding.tvEmptyHint.setVisibility(View.VISIBLE);
        } else {
            Log.w(TAG, "refreshChimeList: " + chimeList.size());
            mBinding.rv.setVisibility(View.VISIBLE);
            mBinding.tvEmptyHint.setVisibility(View.GONE);
            for (Device device : chimeList) {
                adapter.addData(new ChimeListItemModel(device));
            }
        }
        adapter.notifyDataSetChanged();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChimeAddEvent(ChimeAddEvent event) {
        requestChimeList();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChimeAddEvent(ChimeListRefreshEvent event) {
        requestChimeList();
    }
}
