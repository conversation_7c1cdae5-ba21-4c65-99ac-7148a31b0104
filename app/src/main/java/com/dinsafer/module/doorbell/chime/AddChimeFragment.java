package com.dinsafer.module.doorbell.chime;

import android.os.Bundle;
import android.view.View;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAddChimeBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.settting.ui.ScannerActivity;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/11/25
 */
public class AddChimeFragment extends MyBaseFragment<FragmentAddChimeBinding> {

    private String targetDoorbellId;

    public static AddChimeFragment newInstance(String targetDoorbellId) {
        Bundle args = new Bundle();
        args.putString("targetDoorbellId", targetDoorbellId);
        AddChimeFragment fragment = new AddChimeFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_add_chime;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        targetDoorbellId = getArguments().getString("targetDoorbellId");

        mBinding.title.commonBarTitle.setLocalText(getString(R.string.add_a_chime));
        mBinding.title.commonBarBack.setOnClickListener(v -> removeSelf());
        mBinding.tvTip.setLocalText(getString(R.string.add_a_chime_tip));
        mBinding.ivImg.setImageResource(R.drawable.img_chime_power_on);
        mBinding.tvScan.setLocalText(getString(R.string.start_adding_a_device));
        mBinding.tvScan.setOnClickListener(v -> {
            ScannerActivity.startScan(getMainActivity(), false, ScannerActivity.FROM_ADD_DEVICE);
        });

    }

    public String getTargetDoorbellId() {
        return targetDoorbellId;
    }


}
