package com.dinsafer.module.doorbell.setting.network;

import android.app.AlertDialog;
import android.os.Bundle;
import android.text.InputType;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.IpcWifiSettingBinding;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.doorbell.setting.DsDoorbellAdvanceSettingFragment;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_dscam.doorbell.DsDoorbellNetworkManager;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

/**
 * Created by rinfon on 17-6-14.
 */

public class DsDoorbellNetworkChangeWifiSettingFragment extends MyBaseFragment<IpcWifiSettingBinding> implements IPluginBindCallBack, BaseMainActivity.ILoadingCallBack {

    String wifiSSid;
    String id;
    private DsDoorbellNetworkManager mBinder;

    public static DsDoorbellNetworkChangeWifiSettingFragment newInstance(String ssid, String deviceID) {
        DsDoorbellNetworkChangeWifiSettingFragment ipcSettingFragment = new DsDoorbellNetworkChangeWifiSettingFragment();
        Bundle bundle = new Bundle();
        bundle.putString("ssid", ssid);
        bundle.putString("deviceID", deviceID);
        ipcSettingFragment.setArguments(bundle);
        return ipcSettingFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.ipc_wifi_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarTitle.setText(Local.s("Wifi Setting"));
        mBinding.btnSave.setLocalText(getString(R.string.Confirm));
        mBinding.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.btnSave.setOnClickListener(v -> toSave());
        mBinding.ipcWifiPasswordIcon.setOnClickListener(v -> toShowNextPassword());
        mBinding.ipcWifiRepasswordIcon.setOnClickListener(v -> toShowConfirmPassword());
    }

    public void initData() {
        super.initData();
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof DsDoorbellNetworkManager)) {
            DDLog.e(TAG, "Error DsDoorbellNetworkManager binder.");
            showErrorToast();
            return;
        }
        mBinder = (DsDoorbellNetworkManager) pluginBinder;
        mBinder.addBindCallBack(this);
        wifiSSid = getArguments().getString("ssid");
        id = getArguments().getString("deviceID");
        mBinding.ipcWifiName.setText(wifiSSid);
        mBinding.ipcWifiName.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ipc_wifi_set_name)));
        mBinding.ipcWifiPassword.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ap_step_wifi_pass_hint)));
        mBinding.ipcWifiRePassword.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ap_step_wifi_pass_hint_confirm)));
        mBinding.ipcWifiPassword.setInputType(129);
        mBinding.ipcWifiRePassword.setInputType(129);

        mBinding.wifiRemember.setChecked(true);
        mBinding.wifiRemember.setText(Local.s(getResources().getString(R.string.remember_password)));

        if (wifiSSid.equals(DBUtil.SGet(DBKey.REMEMBER_WIFI))) {
            mBinding.ipcWifiPassword.setText(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
            mBinding.ipcWifiRePassword.setText(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
        }

        mBinding.ipcWifiName.setOnTouchListener((v, event) -> {
            final int DRAWABLE_LEFT = 0;
            final int DRAWABLE_TOP = 1;
            final int DRAWABLE_RIGHT = 2;
            final int DRAWABLE_BOTTOM = 3;

            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (event.getRawX() >= (mBinding.ipcWifiName.getRight() -
                        mBinding.ipcWifiName.getCompoundDrawables()[DRAWABLE_RIGHT].getBounds().width())) {
                    // your action here
                    removeSelf();
                    return true;
                }
            }
            return false;
        });
    }

    public void toSave() {
        if ("".equals(mBinding.ipcWifiName.getText().toString())) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getDelegateActivity());
            builder.setMessage(Local.s("empty ssid is forbidden"));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        final String pwd1 = mBinding.ipcWifiPassword.getText().toString();
        String pwd2 = mBinding.ipcWifiRePassword.getText().toString();

        if (!pwd1.equals(pwd2)) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getMainActivity());
            builder.setMessage(Local.s(getResources().getString(R.string.password_not_match)));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        if (("".equals(pwd1)) || ("".equals(pwd2))) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getMainActivity());
            builder.setMessage(Local.s("empty ssid is forbidden"));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        if (mBinding.ipcWifiName.getText().toString().length() > 0) {
            showTimeOutLoadinFramgmentWithCallBack(DsDoorbellNetworkChangeWifiSettingFragment.this);
            mBinder.setSsid(mBinding.ipcWifiName.getText().toString());
            mBinder.setSsidPassword(pwd1);
            mBinder.bindDevice(null);
            if (mBinding.wifiRemember.isChecked()) {
                DBUtil.SPut(DBKey.REMEMBER_WIFI, mBinding.ipcWifiName.getText().toString());
                DBUtil.SPut(DBKey.REMEMBER_WIFI_PASSWORD, mBinding.ipcWifiPassword.getText().toString());
            } else {
                DBUtil.Delete(DBKey.REMEMBER_WIFI);
                DBUtil.Delete(DBKey.REMEMBER_WIFI_PASSWORD);
            }

        }


    }


    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinder.removeBindCallBack(this);
    }

    public void toShowNextPassword() {
        if (mBinding.ipcWifiPassword.getInputType() == 129) {
            mBinding.ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_show);
            mBinding.ipcWifiPassword.setInputType(InputType.TYPE_CLASS_TEXT);
            mBinding.ipcWifiRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            mBinding.ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_hide);
            mBinding.ipcWifiPassword.setInputType(129);
            mBinding.ipcWifiRePassword.setInputType(129);
        }
    }

    public void toShowConfirmPassword() {
        if (mBinding.ipcWifiRePassword.getInputType() == 129) {
            mBinding.ipcWifiRepasswordIcon.setImageResource(R.drawable.icon_form_show);
            mBinding.ipcWifiRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
            mBinding.ipcWifiRePassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            mBinding.ipcWifiRepasswordIcon.setImageResource(R.drawable.icon_form_hide);
            mBinding.ipcWifiRePassword.setInputType(129);
            mBinding.ipcWifiRePassword.setInputType(129);
        }
    }


    @Override
    public void onBindResult(int i, String s) {
        if (i == 1) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (mBinding.wifiRemember.isChecked()) {
                        DBUtil.SPut(DBKey.REMEMBER_WIFI, mBinding.ipcWifiName.getText().toString());
                        DBUtil.SPut(DBKey.REMEMBER_WIFI_PASSWORD, mBinding.ipcWifiPassword.getText().toString());
                    }
//                    虽然返回了1，但是立刻去连ipc，会离线，所以还是取消吧
//                    IPCManager.getInstance().connectIPC(IPCManager.getInstance().getDsCamDeviceByID(id));
                    closeLoadingFragmentWithCallBack();
                    removeSelf();
                    getDelegateActivity().removeToFragment(DsDoorbellAdvanceSettingFragment.class.getName());
                }
            });
        } else if (i == -70) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.tiggle_has_plug));
            getDelegateActivity().removeToFragment(DsDoorbellNetworkBleScanFragment.class.getName());
        } else if (i == -73) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.add_ipc_limit_error));
            getDelegateActivity().removeToFragment(DsDoorbellNetworkBleScanFragment.class.getName());
        } else {
            closeLoadingFragmentWithCallBack();
            showErrorConnectDialog(i);
        }

    }

    private AlertDialogV2 errorDialog;

    private void showErrorConnectDialog(int code) {
        if (errorDialog != null && errorDialog.isShowing()) {
            return;
        }
        final String msg = Local.s(DsCamUtils.getConfigErrorMsgByCode(code));
        errorDialog = AlertDialogV2.createBuilder(getActivity())
                .setContent(msg)
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithCallBack(DsDoorbellNetworkChangeWifiSettingFragment.this);
                        mBinder.setSsid(DBUtil.SGet(DBKey.REMEMBER_WIFI));
                        mBinder.setSsidPassword(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
                        mBinder.bindDevice(null);
                    }
                })
                .setCanCancel(true)
                .setCancel(getString(R.string.Cancel))
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        errorDialog.dismiss();
                        getDelegateActivity().removeToFragment(DsDoorbellNetworkChangeWifiListFragment.class.getName());
                    }
                })
                .preBuilder();
        errorDialog.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                errorDialog.dismiss();
                removeSelf();
                getDelegateActivity().removeToFragment(DsDoorbellAdvanceSettingFragment.class.getName());
            }
        });
        errorDialog.show();
    }

    @Override
    public void onTimeout() {
        showErrorConnectDialog(-999);
    }

}
