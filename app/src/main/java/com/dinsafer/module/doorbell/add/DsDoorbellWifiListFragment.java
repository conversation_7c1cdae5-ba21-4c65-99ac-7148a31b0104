package com.dinsafer.module.doorbell.add;

import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.AdapterView;

import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.TimeZoneLayoutBinding;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.settting.adapter.TimePhoneZoneAdapter;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_dscam.bean.CamBleWifiInfo;
import com.dinsafer.module_dscam.doorbell.DsDoorbellBinder;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.ArrayList;
import java.util.List;

public class DsDoorbellWifiListFragment extends MyBaseFragment<TimeZoneLayoutBinding> implements DsDoorbellBinder.IWifiListCallBack, IPluginBindCallBack, IDeviceListChangeListener, BaseMainActivity.ILoadingCallBack {

    private TimePhoneZoneAdapter mAdapter;
    private ArrayList<String> mData = new ArrayList<String>();

    boolean isCanClose = false;
    private DsDoorbellBinder mBinder;
    private AlertDialogV2 offlineDialog;
    private String deviceID;

    public static DsDoorbellWifiListFragment newInstance(String deviceID, boolean isCanClose) {
        DsDoorbellWifiListFragment fragment = new DsDoorbellWifiListFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean("isCanClose", isCanClose);
        bundle.putString("deviceId", deviceID);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.time_zone_layout;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);

        mBinding.commonBarBack.setOnClickListener(v -> close());
        mBinding.commonBarRight.setOnClickListener(v -> toRefresh());
    }

    @Override
    public void initData() {
        super.initData();
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof DsDoorbellBinder)) {
            DDLog.e(TAG, "Error DsDoorbellBinder binder.");
            showErrorToast();
            return;
        }
        deviceID = getArguments().getString("deviceId");
        mBinder = (DsDoorbellBinder) pluginBinder;
        mBinder.setWifiListCallBack(this);
        mBinder.addBindCallBack(this);
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.wifi_list));
        mData = new ArrayList<String>();
        mAdapter = new TimePhoneZoneAdapter(getActivity(), mData);
        mBinding.choosePhoneZoneListview.setAdapter(mAdapter);
        isCanClose = true;
        if (isCanClose) {
            mBinding.commonBarBack.setVisibility(View.VISIBLE);
        } else {
            mBinding.commonBarBack.setVisibility(View.GONE);
        }

        mBinding.commonBarRight.setImageResource(R.drawable.btn_userpage_refresh);
        mBinding.commonBarRight.setVisibility(View.VISIBLE);
        mBinding.choosePhoneZoneListview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                toSetting(parent,view,position,id);
            }
        });
        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
        toRefresh();
    }

    public void toSetting(AdapterView<?> parent, View view, int position, long id) {
        mBinder.removeBindCallBack(DsDoorbellWifiListFragment.this);
        DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(DsDoorbellWifiListFragment.this);
        getDelegateActivity().addCommonFragment(DsDoorbellWifiSettingFragment.newInstance(deviceID, mData.get(position)));
    }

    @Override
    public boolean onBackPressed() {
        if (isCanClose) {
            return false;
        } else {
            return true;
        }
    }

    public void close() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinder.setWifiListCallBack(null);
        mBinder.removeBindCallBack(this);
        DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(this);
        mBinder.stop();
    }

    public void toRefresh() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                mBinder.getWifiList();
            }
        }, 1000);


    }

    @Override
    public void onWifiListCallBack(List<CamBleWifiInfo> list, boolean spfn) {
        mData.clear();
        for (CamBleWifiInfo info : list) {
            mData.add(info.getSsid());
        }
        mAdapter.setData(mData);
        mAdapter.notifyDataSetChanged();
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (mData.contains(DBUtil.SGet(DBKey.REMEMBER_WIFI))) {
            if (offlineDialog != null && offlineDialog.isShowing()) {
                return;
            }
            offlineDialog = AlertDialogV2.createBuilder(getActivity())
                    .setContent(Local.s(this.getResources().getString(R.string.ipc_remember_ssid_hint))
                            + DBUtil.SGet(DBKey.REMEMBER_WIFI))
                    .setOk(this.getResources().getString(R.string.Confirm))
                    .setCancel(this.getResources().getString(R.string.select_other_network))
                    .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                        @Override
                        public void onOkClick() {
                            showTimeOutLoadinFramgmentWithCallBack(DsDoorbellWifiListFragment.this);
                            mBinder.setSsid(DBUtil.SGet(DBKey.REMEMBER_WIFI));
                            mBinder.setSsidPassword(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
                            mBinder.bindDevice(null);
                        }
                    })
                    .preBuilder();
            offlineDialog.setCancel(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mBinder.removeBindCallBack(DsDoorbellWifiListFragment.this);
                    DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(DsDoorbellWifiListFragment.this);
                    offlineDialog.dismiss();
                }
            });
            offlineDialog.show();
        }
    }

    @Override
    public void onBindResult(int i, String s) {
        if (!this.isVisible()) {
            return;
        }
        if (i == 1) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (!DsDoorbellWifiListFragment.this.isAdded()) {
                        return;
                    }
                    closeLoadingFragmentWithCallBack();
                    DDLog.i(TAG, "添加ipc成功：" + deviceID);
                    getDelegateActivity().removeAllCommonFragment();
                    getDelegateActivity().addCommonFragment(AddDsDoorbellSetNameFragment.newInstance(deviceID));
                }
            });
        } else if (i == -70) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.tiggle_has_plug));
            getDelegateActivity().removeToFragment(DsDoorbellBleScanFragment.class.getName());
        } else if (i == -73) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.add_ipc_limit_error));
            getDelegateActivity().removeToFragment(DsDoorbellBleScanFragment.class.getName());
        } else {
            closeLoadingFragmentWithCallBack();
            showErrorConnectDialog(i);
        }

    }

    @Override
    public void onDeviceAdd(Device device) {
//        getDelegateActivity().runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                if (!DsCamWifiListFragment.this.isAdded()) {
//                    return;
//                }
//                if (device.getCategory() == 2 && "dscam".equals(device.getSubCategory())) {
//                    closeLoadingFragmentWithCallBack();
//                    DDLog.i(TAG, "添加ipc成功：" + device.getInfo().toString());
////                    这里不能这么快移除fragment，主要是不能调用mBinder.stop();
////                    因为回调到这里，只是说明，ipc和服务器已经建立沟通了，但是不代表register成功，如果这个时候断开蓝牙
////                    那么就会导致ipc还没添加完就结束了
////                    getDelegateActivity().removeAllCommonFragment();
//                    getDelegateActivity().addCommonFragment(AddDsCamSetNameFragment.newInstance(device.getId()));
//                }
//            }
//        });
    }

    @Override
    public void onDeviceRemove(Device s) {

    }

    private AlertDialogV2 errorDialog;

    private void showErrorConnectDialog(int code) {
        if (errorDialog != null && errorDialog.isShowing()) {
            return;
        }
        final String msg = Local.s(DsCamUtils.getConfigErrorMsgByCode(code));
        errorDialog = AlertDialogV2.createBuilder(getActivity())
                .setContent(msg)
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithCallBack(DsDoorbellWifiListFragment.this);
                        mBinder.setSsid(DBUtil.SGet(DBKey.REMEMBER_WIFI));
                        mBinder.setSsidPassword(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
                        mBinder.bindDevice(null);
                    }
                })
                .setCanCancel(true)
                .setCancel(getString(R.string.Cancel))
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        errorDialog.dismiss();
                    }
                })
                .preBuilder();
        errorDialog.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                errorDialog.dismiss();
                getDelegateActivity().removeAllCommonFragment();
            }
        });
        errorDialog.show();
    }

    @Override
    public void onTimeout() {
        showErrorConnectDialog(-999);
    }
}

