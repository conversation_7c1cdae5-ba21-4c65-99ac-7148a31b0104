package com.dinsafer.module.doorbell.add;

import android.app.AlertDialog;
import android.os.Bundle;
import android.text.InputType;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import com.dinsafer.config.DBKey;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.IpcWifiSettingBinding;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.main.view.BaseMainActivity;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_dscam.doorbell.DsDoorbellBinder;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

public class DsDoorbellWifiSettingFragment extends MyBaseFragment<IpcWifiSettingBinding> implements IPluginBindCallBack, IDeviceListChangeListener, BaseMainActivity.ILoadingCallBack {

    String wifiSSid;
    String deviceID;
    private DsDoorbellBinder mBinder;

    public static DsDoorbellWifiSettingFragment newInstance(String deviceId, String ssid) {
        DsDoorbellWifiSettingFragment ipcSettingFragment = new DsDoorbellWifiSettingFragment();
        Bundle bundle = new Bundle();
        bundle.putString("ssid", ssid);
        bundle.putString("deviceId", deviceId);
        ipcSettingFragment.setArguments(bundle);
        return ipcSettingFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.ipc_wifi_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarTitle.setText(Local.s("Wifi Setting"));
        mBinding.btnSave.setLocalText(getString(R.string.Confirm));
        mBinding.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.btnSave.setOnClickListener(v -> toSave());
        mBinding.ipcWifiPasswordIcon.setOnClickListener(v -> toShowNextPassword());
    }

    public void initData() {
        super.initData();
        BasePluginBinder pluginBinder = DinSDK.getPluginActivtor().getPluginBinder();
        if (!(pluginBinder instanceof DsDoorbellBinder)) {
            DDLog.e(TAG, "Error DsDoorbellBinder binder.");
            showErrorToast();
            return;
        }
        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
        mBinder = (DsDoorbellBinder) pluginBinder;
        mBinder.addBindCallBack(this);
        wifiSSid = getArguments().getString("ssid");
        deviceID = getArguments().getString("deviceId");
        mBinding.ipcWifiName.setText(wifiSSid);
        mBinding.ipcWifiName.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ipc_wifi_set_name)));
        mBinding.ipcWifiPassword.setHint(Local.s(getDelegateActivity().getResources().getString(R.string.ap_step_wifi_pass_hint)));
        mBinding.ipcWifiPassword.setInputType(129);

        mBinding.wifiRemember.setChecked(true);
        mBinding.wifiRemember.setText(Local.s(getResources().getString(R.string.remember_password)));

        mBinding.ipcWifiName.setOnTouchListener((v, event) -> {
            final int DRAWABLE_LEFT = 0;
            final int DRAWABLE_TOP = 1;
            final int DRAWABLE_RIGHT = 2;
            final int DRAWABLE_BOTTOM = 3;

            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (event.getRawX() >= (mBinding.ipcWifiName.getRight() -
                        mBinding.ipcWifiName.getCompoundDrawables()[DRAWABLE_RIGHT].getBounds().width())) {
                    // your action here
                    removeSelf();
                    return true;
                }
            }
            return true;
        });

        if (wifiSSid.equals(DBUtil.SGet(DBKey.REMEMBER_WIFI))) {
            mBinding.ipcWifiPassword.setText(DBUtil.SGet(DBKey.REMEMBER_WIFI_PASSWORD));
        }
    }

    public void toSave() {
        if ("".equals(mBinding.ipcWifiName.getText().toString())) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getDelegateActivity());
            builder.setMessage(Local.s("empty ssid is forbidden"));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        final String pwd1 = mBinding.ipcWifiPassword.getText().toString();

        if (("".equals(pwd1))) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getMainActivity());
            builder.setMessage(Local.s("empty ssid is forbidden"));
            builder.setNegativeButton(Local.s("OK"), null);
            AlertDialog alertDialog = builder.show();
            TextView messageText = (TextView) alertDialog.findViewById(android.R.id.message);
            messageText.setGravity(Gravity.CENTER);
            return;
        }

        if (mBinding.ipcWifiName.getText().toString().length() > 0) {
            showTimeOutLoadinFramgmentWithCallBack(this);
            mBinder.setSsid(mBinding.ipcWifiName.getText().toString());
            mBinder.setSsidPassword(pwd1);
            mBinder.bindDevice(null);
            if (mBinding.wifiRemember.isChecked()) {
                DBUtil.SPut(DBKey.REMEMBER_WIFI, mBinding.ipcWifiName.getText().toString());
                DBUtil.SPut(DBKey.REMEMBER_WIFI_PASSWORD, mBinding.ipcWifiPassword.getText().toString());
            } else {
                DBUtil.Delete(DBKey.REMEMBER_WIFI);
                DBUtil.Delete(DBKey.REMEMBER_WIFI_PASSWORD);
            }

        }

    }


    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mBinder.removeBindCallBack(this);
        DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(this);
    }

    public void toShowNextPassword() {
        if (mBinding.ipcWifiPassword.getInputType() == 129) {
            mBinding.ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_show);
            mBinding.ipcWifiPassword.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            mBinding.ipcWifiPasswordIcon.setImageResource(R.drawable.icon_form_hide);
            mBinding.ipcWifiPassword.setInputType(129);
        }
    }


    @Override
    public void onBindResult(int i, String s) {
        if (i == 1) {
            getDelegateActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (!DsDoorbellWifiSettingFragment.this.isAdded()) {
                        return;
                    }
                    if (mBinding.wifiRemember.isChecked()) {
                        DBUtil.SPut(DBKey.REMEMBER_WIFI, mBinding.ipcWifiName.getText().toString());
                        DBUtil.SPut(DBKey.REMEMBER_WIFI_PASSWORD, mBinding.ipcWifiPassword.getText().toString());
                    }
                    closeLoadingFragmentWithCallBack();
                    DDLog.i(TAG, "添加ipc成功：" + deviceID);
                    getDelegateActivity().removeAllCommonFragment();
                    getDelegateActivity().addCommonFragment(AddDsDoorbellSetNameFragment.newInstance(deviceID));
                }
            });
        } else if (i == -70) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.tiggle_has_plug));
            getDelegateActivity().removeToFragment(DsDoorbellBleScanFragment.class.getName());
        } else if (i == -73) {
            closeLoadingFragmentWithCallBack();
            showToast(getResources().getString(R.string.add_ipc_limit_error));
            getDelegateActivity().removeToFragment(DsDoorbellBleScanFragment.class.getName());
        } else {
            closeLoadingFragmentWithCallBack();
            showErrorConnectDialog(i);
        }

    }

    private AlertDialogV2 errorDialog;

    private void showErrorConnectDialog(int code) {
        if (errorDialog != null && errorDialog.isShowing()) {
            return;
        }
        final String msg = Local.s(DsCamUtils.getConfigErrorMsgByCode(code));
        errorDialog = AlertDialogV2.createBuilder(getActivity())
                .setContent(msg)
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithCallBack(DsDoorbellWifiSettingFragment.this);
                        mBinder.setSsid(mBinding.ipcWifiName.getText().toString());
                        mBinder.setSsidPassword(mBinding.ipcWifiPassword.getText().toString());
                        mBinder.bindDevice(null);
                    }
                })
                .setCanCancel(true)
                .setCancel(getString(R.string.Cancel))
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        errorDialog.dismiss();
                        getDelegateActivity().removeToFragment(DsDoorbellWifiListFragment.class.getName());
                    }
                })
                .preBuilder();
        errorDialog.setCancel(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                errorDialog.dismiss();
                getDelegateActivity().removeAllCommonFragment();
            }
        });
        errorDialog.show();
    }


    @Override
    public void onDeviceAdd(Device device) {

    }

    @Override
    public void onDeviceRemove(Device s) {

    }

    @Override
    public void onTimeout() {
        showErrorConnectDialog(-999);
    }
}
