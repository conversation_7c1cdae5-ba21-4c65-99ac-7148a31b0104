package com.dinsafer.module.doorbell.chime;

import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentChimeSettingBinding;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.add.ui.BleCheckBluetoothDialog;
import com.dinsafer.module.doorbell.add.AddDsDoorbellSetNameFragment;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.EdittextDialog;
import com.dinsafer.module_dscam.doorbell.DsDoorbellCmd;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.util.ActivityController;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 叮咚添加改名页,设置页
 * Author: MiraclesHed
 * Date: 2021/11/25
 */
public class ChimeSettingFragment extends MyBaseFragment<FragmentChimeSettingBinding> implements IDeviceCallBack {

    //从添加门铃跳转进来
    public static final int FROM_TYPE_ADD_DOORBELL = 0;
    //直接扫叮咚二维码添加
    public static final int FROM_TYPE_SCAN = 1;
    //设置进来
    public static final int FROM_TYPE_SETTING = 2;

    private String doorbellId;
    private Device doorbellDevice;
    private String chimeId;
    private int fromType = FROM_TYPE_SETTING;
    private Device chimeDevice;

    public static ChimeSettingFragment newInstance(String doorbellId, String chimeId, Device chimeDevice, int fromType) {
        Bundle args = new Bundle();
        args.putString("doorbellId", doorbellId);
        args.putString("chimeId", chimeId);
        args.putInt("fromType", fromType);
        ChimeSettingFragment fragment = new ChimeSettingFragment();
        fragment.setArguments(args);
        fragment.chimeDevice = chimeDevice;
        return fragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_chime_setting;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);

        doorbellId = getArguments().getString("doorbellId");
        chimeId = getArguments().getString("chimeId");
        fromType = getArguments().getInt("fromType");
        doorbellDevice = IPCManager.getInstance().getDsDoorbellDeviceByID(doorbellId);

        mBinding.commonBarTitle.setLocalText(getString(R.string.accessory_settings));
        mBinding.modifyPlugsType.setLocalText(getResources().getString(R.string.Chime));
        mBinding.btnSave.setLocalText(getString(R.string.save));
        mBinding.btnTestChime.setLocalText(getString(R.string.test_the_chime));
        mBinding.tvHelp.setLocalText(getString(R.string.siren_help));
        mBinding.modifyPlugsId.setText("ID:" + chimeId);

        if (doorbellDevice != null) {
            doorbellDevice.registerDeviceCallBack(this);
            mBinding.tvDoorbell.setText((String) MapUtils.get(doorbellDevice.getInfo(), "name", ""));
        }
        if (chimeDevice != null) {
            mBinding.etName.setText((String) MapUtils.get(chimeDevice.getInfo(), "name", ""));
        }

        if (fromType == FROM_TYPE_ADD_DOORBELL) {
            mBinding.commonBarBack.setVisibility(View.VISIBLE);
            mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
            mBinding.llSaveBtn.setVisibility(View.VISIBLE);
            mBinding.btnSave.setVisibility(View.VISIBLE);
            mBinding.btnSave.setOnClickListener(v -> clickSave());
            mBinding.tvDoorbell.setVisibility(View.GONE);
            mBinding.tvDoorbell.setCompoundDrawables(null, null, null, null);
            mBinding.etName.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    refreshSaveBtn();
                }
            });

        } else if (fromType == FROM_TYPE_SCAN) {
            mBinding.commonBarBack.setVisibility(View.VISIBLE);
            mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
            mBinding.llSaveBtn.setVisibility(View.VISIBLE);
            mBinding.btnSave.setVisibility(View.VISIBLE);
            mBinding.btnSave.setOnClickListener(v -> clickSave());
            mBinding.btnSave.setEnabled(false);
            mBinding.btnSave.setAlpha(0.7f);
            mBinding.tvDoorbell.setTextColor(ContextCompat.getColor(getContext(), R.color.color_white_03));
            mBinding.tvDoorbell.setOnClickListener(v -> clickDoorbell());
            mBinding.etName.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    refreshSaveBtn();
                }
            });

            //只有一个门铃默认选中
            if (IPCManager.getInstance().getAllDoorbellDevice() != null && IPCManager.getInstance().getAllDoorbellDevice().size() == 1) {
                doorbellDevice = IPCManager.getInstance().getAllDoorbellDevice().get(0);
                doorbellId = doorbellDevice.getId();
                if (doorbellDevice != null) {
                    doorbellDevice.registerDeviceCallBack(this);
                }

                mBinding.tvDoorbell.setTextColor(ContextCompat.getColor(getContext(), R.color.white));
                mBinding.tvDoorbell.setText((String) MapUtils.get(doorbellDevice.getInfo(), "name", ""));
            }
        } else if (fromType == FROM_TYPE_SETTING) {
            mBinding.commonBarBack.setVisibility(View.VISIBLE);
            mBinding.commonBarBack.setOnClickListener(v -> removeSelf());
            mBinding.commonBarRightIcon.setVisibility(View.VISIBLE);
            mBinding.commonBarRightIcon.setOnClickListener(v -> clickMoreSetting());
            mBinding.tvDoorbell.setOnClickListener(v -> clickDoorbell());
            mBinding.llTestChime.setVisibility(View.VISIBLE);
            mBinding.etName.setFocusable(false);
            mBinding.etName.setOnClickListener(v -> showChangeNameDialog());
            mBinding.tvHelp.setOnClickListener(v -> showHelpDialog());
            mBinding.btnTestChime.setOnClickListener(v -> testChime());
        }

        refreshSaveBtn();
    }

    @Override
    public void initData() {
        super.initData();


    }

    public void showChangeNameDialog() {
        EdittextDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Confirm))
                .setCancel(getResources().getString(R.string.Cancel))
                .setDefaultName(mBinding.etName.getText().toString())
                .setContent(getResources().getString(R.string.rename_accessory))
                .setAutoDismiss(false)
                .setOKListener(new EdittextDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick(EdittextDialog dialog, String string) {
                        if (TextUtils.isEmpty(string)) {
                            return;
                        }
                        dialog.dismiss();
                        mBinding.etName.setText(string);
                        saveName();
                    }
                })
                .preBuilder()
                .show();
    }

    private void saveName() {
        showTimeOutLoadinFramgmentWithErrorAlert();
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DsDoorbellCmd.RENAME_CHIME);
        data.put("chime_id", chimeId);
        data.put("chime_name", mBinding.etName.getText().toString());
        doorbellDevice.submit(data);
    }

    private void showHelpDialog() {
        AlertDialog dialog = AlertDialog.createBuilder(getContext())
                .setContent(Local.s(getString(R.string.chime_help)))
                .setOk(Local.s(getString(R.string.know_it)))
                .preBuilder();
        dialog.show();
    }

    private void testChime() {
        showTimeOutLoadinFramgment();
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DsDoorbellCmd.TEST_CHIME);
        data.put("chime_id", chimeId);
        doorbellDevice.submit(data);
    }

    private void clickSave() {
        if (doorbellDevice == null) {
            showTip(R.string.failed_try_again);
            return;
        }
        showTimeOutLoadinFramgment();
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DsDoorbellCmd.ADD_CHIME);
        data.put("chime_id", chimeId);
        data.put("chime_rfid", "0014");
        data.put("chime_name", mBinding.etName.getText().toString());
        doorbellDevice.submit(data);
    }

    private void clickDoorbell() {
        ArrayList<String> doorbellNameList = new ArrayList<>();
        for (Device device1 : IPCManager.getInstance().getAllDoorbellDevice()) {
            doorbellNameList.add((String) MapUtils.get(device1.getInfo(), "name", ""));
        }

        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setOtherButtonTitles(doorbellNameList.toArray(new String[]{}))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        Log.d(TAG, "onOtherButtonClick: " + index);

                        try {
                            List<Device> allDoorbellDevice = IPCManager.getInstance().getAllDoorbellDevice();
                            if (allDoorbellDevice != null && allDoorbellDevice.size() > 0 && allDoorbellDevice.size() > index) {
                                Device device = allDoorbellDevice.get(index);

                                //设置页修改门铃提示先解绑
                                if (fromType == FROM_TYPE_SETTING && !TextUtils.isEmpty(doorbellId) && !device.getId().equals(doorbellId)) {
                                    showChangeChimeDoorbellTip();
                                    return;
                                }

                                if (doorbellNameList.get(index).equals(MapUtils.get(device.getInfo(), "name", ""))) {
                                    if (!DsCamUtils.isDeviceConnected(device)) {
                                        showTip(R.string.add_chime_doorbell_offline_tip);
                                        return;
                                    }
                                    if (doorbellDevice != null) {
                                        doorbellDevice.unregisterDeviceCallBack(ChimeSettingFragment.this);
                                    }

                                    doorbellId = device.getId();
                                    doorbellDevice = IPCManager.getInstance().getDsDoorbellDeviceByID(doorbellId);
                                    if (doorbellDevice != null) {
                                        doorbellDevice.registerDeviceCallBack(ChimeSettingFragment.this);
                                    }
                                    mBinding.tvDoorbell.setText(doorbellNameList.get(index));
                                    mBinding.tvDoorbell.setTextColor(ContextCompat.getColor(getContext(), R.color.white));

                                    refreshSaveBtn();
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }
                }).show();
    }

    private void refreshSaveBtn() {
        if (!TextUtils.isEmpty(mBinding.etName.getText().toString()) && !TextUtils.isEmpty(doorbellId)) {
            mBinding.btnSave.setAlpha(1.0f);
            mBinding.btnSave.setEnabled(true);
        } else {
            mBinding.btnSave.setAlpha(0.7f);
            mBinding.btnSave.setEnabled(false);
        }
    }

    private void clickMoreSetting() {
        ActionSheet.createBuilder(getDelegateActivity().getApplicationContext(), getDelegateActivity().getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.device_management_add_cancel)))
                .setLastButtonTextColor(getContext().getResources().getColor(R.color.color_del_button_text))
                .setOtherButtonTitles(Local.s(getString(R.string.delete)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {

                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            showConfirmDelDialog();
                        }
                    }
                }).show();
    }

    private void showConfirmDelDialog() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.ok))
                .setCancel(getResources().getString(R.string.cancel))
                .setContent(getResources().getString(R.string.smart_plugs_list_delete_confirm))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        deleteDevice();
                    }
                })
                .preBuilder()
                .show();
    }

    private void deleteDevice() {
        showTimeOutLoadinFramgment();
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DsDoorbellCmd.DEL_CHIME);
        data.put("chime_id", chimeId);
        doorbellDevice.submit(data);
    }

    private void showTip(int tip) {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.ok))
                .setContent(getResources().getString(tip))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                    }
                })
                .preBuilder()
                .show();
    }

    private void showChangeChimeDoorbellTip() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.know_it))
                .setContent(getResources().getString(R.string.change_chime_doorbell_tip))
                .setOKListener(new AlertDialog.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                    }
                })
                .preBuilder()
                .show();
    }

    public void showChimeHaveBindedTip() {
        BleCheckBluetoothDialog dialog = new BleCheckBluetoothDialog(getDelegateActivity(), BleCheckBluetoothDialog.CHIME_HAVE_BINDED);
        dialog.setOnBtnClickListener(new BleCheckBluetoothDialog.OnBtnClickListener() {
            @Override
            public void clickOk() {
                dialog.dismiss();
                removeSelf();
            }

            @Override
            public void clickCanal() {
                dialog.dismiss();
                removeSelf();
            }
        });
        dialog.show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (doorbellDevice != null) {
            doorbellDevice.unregisterDeviceCallBack(this);
        }
    }

    @Override
    public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
        if (!id.equals(doorbellId)) {
            return;
        }
        Log.d(TAG, "onCmdCallBack: " + cmd);
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                int status = (int) map.get("status");
                switch (cmd) {
                    case DsDoorbellCmd.ADD_CHIME:
                        if (status == 1) {
                            EventBus.getDefault().post(new ChimeAddEvent(chimeId, mBinding.etName.getText().toString()));
                            if (fromType == FROM_TYPE_SCAN) {
                                closeLoadingFragment();
                                removeSelf();
                            } else if (fromType == FROM_TYPE_ADD_DOORBELL) {
                                removeSelf();
                                closeLoadingFragment();
                                if (ActivityController.getInstance().getFragment(AddDsDoorbellSetNameFragment.class) != null) {
                                    getDelegateActivity().removeToFragment(AddDsDoorbellSetNameFragment.class.getName());
                                } else if (ActivityController.getInstance().getFragment(ChimeListFragment.class) != null) {
                                    getDelegateActivity().removeToFragment(ChimeListFragment.class.getName());
                                }
                            }
                        } else if (status == -1) {
                            showChimeHaveBindedTip();
                        } else {
                            closeLoadingFragment();
                            showTip(R.string.failed_try_again);
                        }
                        break;
                    case DsDoorbellCmd.RENAME_CHIME:
                    case DsDoorbellCmd.DEL_CHIME:
                        if (status == 1) {
                            EventBus.getDefault().post(new ChimeListRefreshEvent());
                            closeLoadingFragment();
                            removeSelf();
                        } else {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                        }
                        break;
                    case DsDoorbellCmd.TEST_CHIME:
                        if (status == 1) {
                            closeLoadingFragment();
                        } else {
                            closeTimeOutLoadinFramgmentWithErrorAlert();
                        }
                        break;

                }
            }
        });

    }
}
