package com.dinsafer.module.doorbell.chime;

import android.view.View;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ItemChimeItemBinding;
import com.dinsafer.ui.rv.BaseBindModel;
import com.dinsafer.util.MapUtils;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/11/25
 */
public class ChimeListItemModel implements BaseBindModel<ItemChimeItemBinding> {
    private Device device;

    public ChimeListItemModel(Device device) {
        this.device = device;
    }

    @Override
    public int getLayoutID() {
        return R.layout.item_chime_item;
    }

    @Override
    public void onDo(View v) {

    }

    public Device getDevice() {
        return device;
    }

    @Override
    public void convert(BaseViewHolder holder, ItemChimeItemBinding binding) {
        binding.tvPluginName.setText((String) MapUtils.get(device.getInfo(),"name",""));
    }
}
