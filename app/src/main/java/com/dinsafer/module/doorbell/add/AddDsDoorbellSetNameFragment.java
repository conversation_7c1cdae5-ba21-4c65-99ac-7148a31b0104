package com.dinsafer.module.doorbell.add;

import android.content.res.ColorStateList;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dinsafer.common.IPCManager;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.FragmentAddDsdoorbellSetnameBinding;
import com.dinsafer.module.MyBaseFragment;
import com.dinsafer.module.doorbell.chime.AddChimeFragment;
import com.dinsafer.module.doorbell.chime.ChimeAddEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class AddDsDoorbellSetNameFragment extends MyBaseFragment<FragmentAddDsdoorbellSetnameBinding> implements IDeviceCallBack {

    public static final String ID = "id";
    private String id;
    private Device device;
    private ArrayList<String> chimeList = new ArrayList<>();

    public static AddDsDoorbellSetNameFragment newInstance(String id) {
        AddDsDoorbellSetNameFragment modifyPlugsFragment = new AddDsDoorbellSetNameFragment();
        Bundle args = new Bundle();
        args.putString(ID, id);
        modifyPlugsFragment.setArguments(args);
        return modifyPlugsFragment;
    }

    @Override
    protected int provideContentViewLayoutId() {
        return R.layout.fragment_add_dsdoorbell_setname;
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
        mBinding.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.btnSave.setOnClickListener(v -> toSave());
        mBinding.llAddChime.setOnClickListener(v -> getDelegateActivity().addCommonFragment(AddChimeFragment.newInstance(id)));
        mBinding.tvAddChime.setLocalText(getString(R.string.add_a_chime));
    }

    @Override
    public void initData() {
        super.initData();
        EventBus.getDefault().register(this);
        mBinding.commonBarTitle.setLocalText(getResources().getString(R.string.modify_plugs_title));
        mBinding.modifyPlugsInput.setHint(Local.s(getResources().getString(R.string.modifyaccessoryhint)));
        mBinding.modifyPlugsInput.setText(Local.s(getResources().getString(R.string.video_doorbell)));
        mBinding.btnSave.setLocalText(getResources().getString(R.string.save));
        String text = getArguments().getString(ID);
        id = text;
        device = DinSDK.getHomeInstance().getDevice(id);
        if (device == null) {
            showErrorToast();
            return;
        }
        mBinding.commonBarBack.setVisibility(View.INVISIBLE);

        device.registerDeviceCallBack(this);

        mBinding.modifyPlugsType.setLocalText(getResources().getString(R.string.device_managent_ip_camera));
        mBinding.modifyPlugsId.setText("ID:" + id);

        refreshChimeList();

    }

    public void toClose() {
        removeSelf();

    }

    public void toSave() {
        if (TextUtils.isEmpty(mBinding.modifyPlugsInput.getText())) {
            return;
        }

        toChangeName();
    }

    private void toChangeName() {
        if (TextUtils.isEmpty(mBinding.modifyPlugsInput.getText().toString().trim())) {
            return;
        }
        showTimeOutLoadinFramgmentWithErrorAlert();

        DDLog.i(TAG, "toChangePluginName");
        device = DinHome.getInstance().getDevice(id);
        if (null != device) {
            DDLog.i(TAG, "修改名字");
            Map<String, Object> result = new HashMap();
            result.put("cmd", "set_name");
            result.put("name", mBinding.modifyPlugsInput.getText().toString());
            device.submit(result);
        } else {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        device.unregisterDeviceCallBack(this);
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onCmdCallBack(String deviceID, String subCategory, String cmd, Map map) {
        if (deviceID.equals(device.getId())) {
            if (cmd.equals("set_name")) {
                int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
                closeTimeOutLoadinFramgmentWithErrorAlert();
                if (status == 1) {
                    IPCManager.getInstance().connectDevice(device);
                    getDelegateActivity().removeAllCommonFragment();
                } else {
                    showSaveNameErrorTip();
                }
            }
        }

    }

    private void showSaveNameErrorTip() {
        AlertDialog.createBuilder(getDelegateActivity())
                .setOk(getResources().getString(R.string.Retry))
                .setCancel(getResources().getString(R.string.rename_later))
                .setContent(getResources().getString(R.string.doorbell_save_name_error_tip))
                .setOKListener(() -> toSave())
                .setCancelListener(() -> getDelegateActivity().removeAllCommonFragment())
                .preBuilder()
                .show();
    }

    private void refreshChimeList() {

        if (chimeList == null || chimeList.size() == 0) {
            LocalTextView tv = new LocalTextView(getContext());
            tv.setLocalText(getString(R.string.no_chime));
            tv.setTextColor(ContextCompat.getColor(getContext(), R.color.color_white_03));
            tv.setTextSize(16);
            tv.setGravity(Gravity.CENTER_VERTICAL);

            mBinding.llChimes.addView(tv);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) tv.getLayoutParams();
            layoutParams.width = LinearLayout.LayoutParams.MATCH_PARENT;
            layoutParams.height = DensityUtils.dp2px(getContext(), 52);
            layoutParams.gravity = Gravity.CENTER_VERTICAL;

            mBinding.llAddChimeRoot.setBackgroundTintList(ColorStateList.valueOf(ContextCompat.getColor(getContext(), R.color.color_brand_dark_01)));
            return;
        }

        mBinding.llChimes.removeAllViews();
        if (chimeList.size() >= 4) {
            ViewGroup.LayoutParams lp = mBinding.svChimes.getLayoutParams();
            lp.width = ViewGroup.LayoutParams.MATCH_PARENT;
            lp.height = DensityUtils.dp2px(getContext(), 180);
            mBinding.svChimes.setLayoutParams(lp);
        }

        for (int i = 0; i < chimeList.size(); i++) {
            TextView tv = new TextView(getContext());
            tv.setText(chimeList.get(i));
            tv.setTextColor(ContextCompat.getColor(getContext(), R.color.color_white_01));
            tv.setTextSize(16);
            tv.setSingleLine(true);
            tv.setGravity(Gravity.CENTER_VERTICAL);

            mBinding.llChimes.addView(tv);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) tv.getLayoutParams();
            layoutParams.width = LinearLayout.LayoutParams.MATCH_PARENT;
            layoutParams.height = DensityUtils.dp2px(getContext(), 52);
            layoutParams.gravity = Gravity.CENTER_VERTICAL;
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onChimeAddEvent(ChimeAddEvent event){
        chimeList.add(0,event.getChimeName());
        refreshChimeList();
    }

}
