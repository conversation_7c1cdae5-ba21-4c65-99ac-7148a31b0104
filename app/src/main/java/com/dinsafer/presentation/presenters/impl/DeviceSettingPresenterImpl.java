package com.dinsafer.presentation.presenters.impl;


import com.dinsafer.action.ShareQRRepository;
import com.dinsafer.action.SirenSettingRepository;
import com.dinsafer.domain.interactors.BaseInteractor;
import com.dinsafer.domain.interactors.impl.BaseInteractorImpl;
import com.dinsafer.presentation.presenters.base.BasePresenterImpl;


/**
 * Created by dmi<PERSON><PERSON> on 12/13/15.
 */
public class DeviceSettingPresenterImpl extends BasePresenterImpl {

    public DeviceSettingPresenterImpl(IBaseView view) {
        super(view);
    }

    BaseInteractor mainInteractor;

    public void toShareQR(String deviceid, int newuserpermission) {
        mView.showProgress();
        mainInteractor = new BaseInteractorImpl(ShareQRRepository.Builder(baseCallback)
                .setDeviceid(deviceid).setNewuserpermission(newuserpermission));
        mainInteractor.execute();
    }

    public void toSetASKSirenSetting(String deviceid, String sirenSetting, String messageid, String sendid, String stype) {
        mView.showProgress();
        mainInteractor = new BaseInteractorImpl(SirenSettingRepository.Builder(baseCallback)
                .setDeviceid(deviceid).setDeviceid(deviceid).setMessageid(messageid)
                .setPluginid(sendid).setSirenSetting(sirenSetting)
                .setAsk(true).setStype(stype));
        mainInteractor.execute();
    }

    public void toSetSirenSetting(String deviceid, String sirenSetting, String messageid, String pluginid) {
        mView.showProgress();
        mainInteractor = new BaseInteractorImpl(SirenSettingRepository.Builder(baseCallback)
                .setDeviceid(deviceid).setDeviceid(deviceid).setMessageid(messageid)
                .setPluginid(pluginid).setSirenSetting(sirenSetting));
        mainInteractor.execute();
    }

    public void toCacelAllRequset() {
        if (mainInteractor != null)
            mainInteractor.cancel();
    }

}
