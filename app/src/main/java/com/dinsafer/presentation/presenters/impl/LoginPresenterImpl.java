package com.dinsafer.presentation.presenters.impl;


import com.dinsafer.action.ForgetPasswordRepository;
import com.dinsafer.action.LoginRepository;
import com.dinsafer.domain.interactors.BaseInteractor;
import com.dinsafer.domain.interactors.impl.BaseInteractorImpl;
import com.dinsafer.presentation.presenters.base.BasePresenterImpl;


/**
 * Created by d<PERSON><PERSON><PERSON> on 12/13/15.
 */
public class LoginPresenterImpl extends BasePresenterImpl {

    public LoginPresenterImpl(IBaseView view) {
        super(view);
    }

    BaseInteractor mainInteractor;

    public void toLogin(String uid, String password) {
        mView.showProgress();
        mainInteractor = new BaseInteractorImpl(LoginRepository.Builder(baseCallback)
                .setUid(uid).setPassword(password));
        mainInteractor.execute();
    }

    public void toForgetPassword(String uid) {
        mView.showProgress();
        mainInteractor = new BaseInteractorImpl(ForgetPasswordRepository.Builder(baseCallback)
                .setUid(uid));
        mainInteractor.execute();

    }

    public void toCacelAllRequset() {
        if (mainInteractor != null)
            mainInteractor.cancel();
    }


}
