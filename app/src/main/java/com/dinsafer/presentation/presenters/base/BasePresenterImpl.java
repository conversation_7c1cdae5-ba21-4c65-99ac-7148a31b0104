package com.dinsafer.presentation.presenters.base;

import com.dinsafer.domain.interactors.BaseInteractor;
import com.dinsafer.domain.model.ErrorMode;
import com.dinsafer.model.LoginResponse;
import com.dinsafer.presentation.presenters.MainPresenter;

/**
 * Created by Rinfon on 17/3/13.
 */
public class BasePresenterImpl<T> implements MainPresenter {

    public IBaseView mView;

    public BaseInteractor.Callback baseCallback = new BaseInteractor.Callback<T>() {
        @Override
        public void onRequsetSuccess(T result) {
            mView.hideProgress();
            mView.onRequestSuccess(result);
        }

        @Override
        public void onRequestFailed(ErrorMode ErrorMode) {
            mView.hideProgress();
            mView.showError(ErrorMode);
        }

        @Override
        public void onCancel() {
            mView.showError(ErrorMode.BuilderCancel());
        }
    };

    public BasePresenterImpl(IBaseView mView) {
        this.mView = mView;
    }


    public void unBind() {
        mView = null;
    }
}
