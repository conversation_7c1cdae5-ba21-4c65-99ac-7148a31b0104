package com.dinsafer;

import android.app.Application;
import android.content.Context;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import com.alibaba.sdk.android.httpdns.HttpDns;
import com.alibaba.sdk.android.httpdns.HttpDnsService;
import com.clj.fastble.BleManager;
import com.dinsafer.aop.uitls.RLog;
import com.dinsafer.common.BuildConfig;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinsaferpush.core.DinsaferPushManager;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.easylocal.LocalConfig;
import com.dinsafer.easylocal.LocalManager;
import com.dinsafer.easylocal.LocalSDK;
import com.dinsafer.model.LanguageUpdataEvent;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.util.ActivityManager;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.CrashHandler;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.github.sahasbhop.apngview.ApngImageLoader;
import com.gu.toolargetool.TooLargeTool;
import com.nostra13.universalimageloader.cache.disc.naming.Md5FileNameGenerator;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.ImageLoaderConfiguration;
import com.nostra13.universalimageloader.core.assist.ImageScaleType;
import com.nostra13.universalimageloader.core.assist.QueueProcessingType;
import com.qiniu.android.common.Zone;
import com.qiniu.android.storage.Configuration;
import com.qiniu.android.storage.UploadManager;

import org.greenrobot.eventbus.EventBus;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;

import androidx.multidex.MultiDexApplication;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;


/**
 * Created by Rinfon on 16/7/21.
 */
public class DinSaferApplication extends MultiDexApplication {
    private String TAG = getClass().getSimpleName();

    private static UploadManager uploadManager;

    public static UploadManager getUploadManager() {
        return uploadManager;
    }

    private static Context context;

    private static HttpDnsService httpdns;

    private static Application mInstance;

    @Override
    public void onCreate() {
        super.onCreate();

        TooLargeTool.startLogging(this);
        mInstance = this;

        DDGlobalEnv.getInstance().init(context);

        ActivityManager.get().init(this);

//        initBugly();

        checkSignature();
//        initDB();
        //        初始化Rlog
        RLog.init(context);

        CrashHandler.getInstance().init(this);

        ApngImageLoader i = ApngImageLoader.getInstance();
        i.setEnableDebugLog(false);
        i.setEnableVerboseLog(false);
        i.init(getApplicationContext());

        CommonDataUtil.init();

        DisplayImageOptions options = new DisplayImageOptions.Builder()
                .cacheInMemory(true)
                .cacheOnDisk(true)
                .imageScaleType(ImageScaleType.IN_SAMPLE_POWER_OF_2)
                .build();
        ImageLoaderConfiguration config = new ImageLoaderConfiguration.Builder(this)
                .threadPriority(Thread.NORM_PRIORITY - 2)//加载图片的线程数
                .denyCacheImageMultipleSizesInMemory() //解码图像的大尺寸将在内存中缓存先前解码图像的小尺寸。
                .discCacheFileNameGenerator(new Md5FileNameGenerator())//设置磁盘缓存文件名称
                .diskCacheSize(50 * 1024 * 1024)
                .tasksProcessingOrder(QueueProcessingType.LIFO)//设置加载显示图片队列进程
                .defaultDisplayImageOptions(options)
//                .writeDebugLogs() // Remove for release app
                .build();
//
//        Initialize ImageLoader with configuration.
        ImageLoader.getInstance().init(config);
        initUploadManager();
//        initJPushSDK(true);


//        StrictMode.setVmPolicy(new StrictMode.VmPolicy.Builder().setClassInstanceLimit(LoginFragment.class, 1).penaltyLog().build());

//        StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder()
//                .detectAll()//开启所有的detectXX系列方法
//                .penaltyDialog()//弹出违规提示框
//                .penaltyLog()//在Logcat中打印违规日志
//                .build());


//        uploadLogPoint();

//        initIPCSDK();

//        initTuya();

        initBle();

        String processName = getProcessName(android.os.Process.myPid());
        if (processName != null && processName.equals(context.getPackageName())) {
//            initTuya();
            initDinSdk();
            initHttp();
//            if (BuildConfig.DEBUG) {
//                BlockDetectByChoreographer.start();
//            }
        }
        initDinsaferPush();
        initLocal();
    }

    private void initLocal() {
        LocalSDK.init(new LocalConfig()
                .setContext(this)
                .setAppID(APIKey.LANGUAGEKEY)
                .setLogEnable(true)
        );
        LocalManager.getInstance().setLocalListener(new LocalManager.LocalListener() {
            @Override
            public void onCurrentLanguageChange(String newLanguage) {
                DDLog.w(TAG, "onCurrentLanguageChange>" + newLanguage);
                DBUtil.Put(DBKey.WIDGET_REQUEST_FAIL, Local.s(context.getResources().getString(R.string.failed_try_again)));
                DBUtil.Put(DBKey.WIDGET_OFFLINE_DEVICE, Local.s(context.getResources().getString(R.string.offline_hint)));
                DBUtil.Put(DBKey.WIDGET_NO_PANNEL, Local.s(context.getResources().getString(R.string.no_alarm_panel)));
                DBUtil.Put(DBKey.WIDGET_STRING_ARM, Local.s(context.getResources().getString(R.string.toolbar_arm_text)));
                DBUtil.Put(DBKey.WIDGET_STRING_HOMEARM, Local.s(context.getResources().getString(R.string.toolbar_homearm_text)));
                DBUtil.Put(DBKey.WIDGET_STRING_DISARM, Local.s(context.getResources().getString(R.string.toolbar_disarm_text)));
                DBUtil.Put(DBKey.WIDGET_STRING_SOS, Local.s(context.getResources().getString(R.string.main_section_sos)));

                LanguageUpdataEvent event = new LanguageUpdataEvent(LanguageUpdataEvent.EVENT_FINISHED);
                EventBus.getDefault().post(event);
            }
        });
    }

    private void initDinSdk() {
        if (DBUtil.contain(DBKey.APIKEY)) {
            APIKey.DOMIAN = DBUtil.Str(DBKey.APIKEY);
        }
        if (DBUtil.contain(DBKey.API_STAT_KEY)) {
            APIKey.STATISTICS_HTTP_BASE_URL = DBUtil.Str(DBKey.API_STAT_KEY);
        }
        DinSDK.DinSDKBuilder.create()
                .withApplication(this)
                .withAppID(APIKey.APP_ID)
                .withAppSecret("FpF4Uqiio9k8p9VUSX36UZxy9wLs7ybT")
                .withDebugMode(BuildConfig.DEBUG)
                .withDomain(APIKey.DOMIAN)
                .withE2EDomain(APIKey.E2E_DOMAIN)
                .withE2EHelpDomain("")
                .withE2EPort(APIKey.E2E_PORT)
                .withE2EHelpPort(APIKey.E2E_HELP_PORT)
                .withTuyaAppKey(getAppMetaDataString(this, "TUYA_SMART_APPKEY", ""))
                .withTuyaAppSecret(getAppMetaDataString(this, "TUYA_SMART_SECRET", ""))
                .withHelioCamSecret("ReiXeequoo1aushoh7Eecha0ohKac7sh")
                .withBmtDomain(APIKey.STATISTICS_HTTP_BASE_URL)
                .build();

        DDLog.setIsDebug(BuildConfig.DEBUG);
        MsctLog.initLog(BuildConfig.DEBUG);


    }

    private void initDinsaferPush() {
        //TODO 开启调试模式，默认关闭，需在DinsaferPushManager.init(Context)前调用。
        DinsaferPushManager.enableDebug(DDLog.isDebug());
        //TODO 开启第三方推送通道,默认开启,需在DinsaferPushManager.init(Context)前调用。
        DinsaferPushManager.setIsThirdPartyEnable(true);
        //TODO 修改服务器地址
        DinsaferPushManager.setServiceUrl(APIKey.SERVER_IP, APIKey.SERVER_IP);
        //TODO 设置服务器域名是否为api
        DinsaferPushManager.setIsApiService(true);
        //TODO 初始化
        DinsaferPushManager.init(this);

    }

//    private void initBugly() {
//        // 获取当前包名
//        String packageName = context.getPackageName();
//        // 获取当前进程名
//        String processName = getProcessName(android.os.Process.myPid());
//        // 设置是否为上报进程
//        CrashReport.UserStrategy strategy = new CrashReport.UserStrategy(context);
//        strategy.setUploadProcess(processName == null || processName.equals(packageName));
//        // 初始化Bugly
//        CrashReport.initCrashReport(context, APIKey.BUGLY_APPID, DDLog.isDebug(), strategy);
//    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        context = this;
    }

    /**
     * 获取进程号对应的进程名
     *
     * @param pid 进程号
     * @return 进程名
     */
    private static String getProcessName(int pid) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader("/proc/" + pid + "/cmdline"));
            String processName = reader.readLine();
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim();
            }
            return processName;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException exception) {
                exception.printStackTrace();
            }
        }
        return null;
    }

    private Observable<String> readFileObservable = Observable.create(new Observable.OnSubscribe<String>() {
        @Override
        public void call(Subscriber<? super String> subscriber) {
            subscriber.onNext(RLog.getLog().getLogPointFile(10));
            subscriber.onCompleted();
        }
    }).subscribeOn(Schedulers.computation()).observeOn(AndroidSchedulers.mainThread());


    public void uploadLogPoint() {
        readFileObservable.subscribe(new Subscriber<String>() {
            @Override
            public void onCompleted() {
                RLog.getLog().detLogPoint(10);
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onNext(String msg) {
                if (!TextUtils.isEmpty(msg)) {
                    DinsafeAPI.getApi().getLogUploadCall(msg)
                            .enqueue(new Callback<StringResponseEntry>() {
                                @Override
                                public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                                }

                                @Override
                                public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                                }
                            });
                }
            }
        });
    }

    public static Context getAppContext() {
        return context;
    }

    private void initUploadManager() {
        Configuration config = new Configuration.Builder()
                .chunkSize(256 * 1024)  //分片上传时，每片的大小。 默认256K
                .putThreshhold(512 * 1024)  // 启用分片上传阀值。默认512K
                .connectTimeout(60) // 链接超时。默认10秒
                .responseTimeout(60) // 服务器响应超时。默认60秒
                .zone(Zone.zone0) // 设置区域，指定不同区域的上传域名、备用域名、备用IP。默认 Zone.zone0
                .build();
// 重用uploadManager。一般地，只需要创建一个uploadManager对象
        uploadManager = new UploadManager(config);
    }

    public static HttpDnsService getHttpdns() {
        return httpdns;
    }

    private void initHttp() {
        DinsafeAPI.init();
        httpdns = HttpDns.getService(context, "160011", "d180fdf58e9c03ca8894f700441d8b44");
//        httpdns = HttpDns.getService(context, "accoutid", "sec");
        httpdns.setHTTPSRequestEnabled(true);
        httpdns.setPreResolveHosts(new ArrayList<>(Arrays.asList(APIKey.DOMIAN, APIKey.DOMIAN_DOU)));
        // 允许过期IP以实现懒加载策略
        httpdns.setExpiredIPEnabled(false);
        httpdns.setCachedIPEnabled(false);
    }

    private void initDB() {
        try {
            DBUtil db = new DBUtil(context);
        } catch (Exception e) {
            DDLog.log("DB", "数据库异常: " + e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 手动获取keystore的md5的方法：keytool -list -v -keystore /xx/xx/xxx.keystore
     * get到A3:25:C2:.....改成A325C2就可以啦
     */
    private static final String SIGNATURE_MD5 = "56996D7FF22F5255CFB29202BDB1E958";

    /**
     * 检测签名是否准确，不准确退出系统
     */
    private void checkSignature() {
//        String getMd5 = DDSystemUtil.getSignatureMd5();
//        DDLog.d("DinSaferApplication", "原签名：" + SIGNATURE_MD5 + "。 现签名：" + getMd5);
//        if (!getMd5.equals(SIGNATURE_MD5)) {
//            CrashReport.putUserData(context, "CheckSignature", "false");
//            DDSystemUtil.exit(0);
//        }
    }

    public static Application getInstance() {
        return mInstance;
    }

    public void initBle() {
//        setReConnectCount,设置重连，否则在reset主机后，app第一次蓝牙连接失败而导致添加主机失败。
        BleManager.getInstance().init(this);
        BleManager.getInstance()
                .enableLog(false)
                .setReConnectCount(2, 5000)
                .setConnectOverTime(APIKey.BLE_CONNECT_TIMEOUT)
                .setOperateTimeout(APIKey.BLE_OPERATE_TIMEOUT);
    }


    /**
     * 不同的类型要区别获取，以下是String类型的
     *
     * @param context      上下午
     * @param metaName     meta-data定义的名字
     * @param defaultValue 默认值
     * @return
     */
    public static String getAppMetaDataString(Context context, String metaName, String defaultValue) {
        try {
            //application标签下用getApplicationinfo，如果是activity下的用getActivityInfo
            //Sting类型的用getString，Boolean类型的getBoolean，其他具体看api
            String value = context.getPackageManager()
                    .getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA)
                    .metaData.getString(metaName, defaultValue);
            return value;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return defaultValue;
        }
    }
}
