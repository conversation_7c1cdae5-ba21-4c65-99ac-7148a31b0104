package com.dinsafer.widget;

import android.annotation.TargetApi;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.dinsafer.common.Constants;
import com.dinsafer.common.DinsafeAPI;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.StringResponseEntry;
import com.dinsafer.nova.Widget;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;
import com.dinsafer.util.RandomStringUtils;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by LT on 2018/7/26.
 */

public class WidgetService extends Service {
    private final String TAG = "WidgetService";
    public static final int SERVICE_ID = 10001;
    private int serviceStatus;

    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate: ");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForeground(SERVICE_ID, new Notification());
            startMyOwnForeground(SERVICE_ID);
        }
        super.onCreate();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        DDLog.d(TAG, "LT Test : onStartCommand");
        if (intent == null) {
            return super.onStartCommand(intent, flags, startId);
        }
        serviceStatus = intent.getIntExtra(Constants.KEY_SERVICE_STATUS, 0);
        DDLog.d(TAG, "LT Test : KEY_SERVICE_STATUS: " + serviceStatus);
        String cmd;

        switch (serviceStatus) {
            case Widget.ArmStatus.STATUS_ARM:
                cmd = LocalKey.ARM_KEY;
                break;
            case Widget.ArmStatus.STATUS_DISARM:
                cmd = LocalKey.DISARM_KEY;
                break;
            case Widget.ArmStatus.STATUS_HOMEARM:
                cmd = LocalKey.HOMEARM_KEY;
                break;
            case Widget.ArmStatus.STATUS_SOS:
                cmd = LocalKey.TASK_SOS;
                break;
            default:
                cmd = LocalKey.ARM_KEY;
                break;
        }

        new DeviceCmdTask(cmd).start();
        /*
         * 返回此参数，代表如果service异常死掉，在重启时，将intent重新传入
         */

        return Service.START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        stopSelf();
        super.onDestroy();
        Intent localIntent = new Intent();
        localIntent.setAction("com.dinsafer.widget.restart");
        sendBroadcast(localIntent);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private class DeviceCmdTask extends Thread {
        private String cmd;

        public DeviceCmdTask(String _cmd) {
            this.cmd = _cmd;
        }

        @Override
        public void run() {
            DDLog.d(TAG, "run()");
            String messageid = RandomStringUtils.getMessageId();
            String deviceToken = DBUtil.Str(DBKey.WIDGET_CURRENT_DEVICE_TOKEN);
            String uid = DBUtil.Str(DBKey.WIDGET_UID);

            Call<StringResponseEntry> call = DinsafeAPI.getApi()
                    .getDeviceCmdCall(uid
                            , deviceToken
                            , messageid,
                            cmd,
                            true);
            call.enqueue(new Callback<StringResponseEntry>() {
                @Override
                public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                    DDLog.d(TAG, "LT Test----- call onSuccess");
                    if (Widget.ArmStatus.STATUS_SOS != serviceStatus) {
                        DBUtil.Put(Constants.KEY_SERVICE_STATUS, serviceStatus);
                    }
                    Widget.update(getApplicationContext());
                    stopSelf();
                }

                @Override
                public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                    DDLog.d(TAG, "LT Test----- call failure :message = " + t.getMessage());
                    if (DBUtil.Exists(DBKey.WIDGET_REQUEST_FAIL)) {
                        showToast(DBUtil.Str(DBKey.WIDGET_REQUEST_FAIL));
                    } else {
                        showToast("Request failed");
                    }
                    stopSelf();
                }
            });
        }

    }

    public String getLocalText(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return "";
        } else {
            try {
                return Local.s(msg);
            } catch (Exception ex) {
                return msg;
            }
        }

    }

    private void showToast(String msg) {
        Toast.makeText(WidgetService.this, msg, Toast.LENGTH_SHORT).show();
    }

    @TargetApi(Build.VERSION_CODES.O)
    private void startMyOwnForeground(int id) {
        String NOTIFICATION_CHANNEL_ID = getResources().getString(R.string.app_name);
        String channelName = "My Background Service";
        NotificationChannel chan = new NotificationChannel(NOTIFICATION_CHANNEL_ID, channelName, NotificationManager.IMPORTANCE_NONE);
        chan.setLightColor(Color.BLUE);
        chan.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        assert manager != null;
        manager.createNotificationChannel(chan);

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID);
        Notification notification = notificationBuilder.setOngoing(true)
                .setSmallIcon(R.mipmap.icon_notification_tran_bg)
                .setContentTitle("")
                .setPriority(NotificationManager.IMPORTANCE_MIN)
                .setCategory(Notification.CATEGORY_SERVICE)
                .build();
        // if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        //     startForeground(id, notification, FOREGROUND_SERVICE_TYPE_DATA_SYNC);
        // } else {
        //     startForeground(id, notification);
        // }
    }
}
