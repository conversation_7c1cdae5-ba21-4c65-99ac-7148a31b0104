package com.dinsafer;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.dinsafer.config.APIKey;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dinnet.R;
import com.dinsafer.http.DDSecretUtil;
import com.dinsafer.model.DeviceOnlineEvent;
import com.dinsafer.model.LoginResponse;
import com.dinsafer.model.SelfTestEvent;
import com.dinsafer.model.UserDeviceListChangeEvent;
import com.dinsafer.module.spash.SpashActivity;
import com.dinsafer.push.PushUtil;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.nostra13.universalimageloader.core.ImageLoader;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.UUID;

import androidx.core.content.ContextCompat;


/**
 * Created by rinfon on 15/12/9.
 */
@Deprecated
public class DNMessageReceiver extends BroadcastReceiver {
    private String TAG = getClass().getSimpleName();

    private static Notification messageNotification = null;

    private static NotificationManager messageNotificationManager = null;

    private static PendingIntent messagePendingIntent = null;

    private static final String EXTRA = "cn.jpush.android.EXTRA";

    public static final String ALERT = "cn.jpush.android.ALERT";

    private static Intent messageIntent = null;

    public static final String DEVICEID = "deviceid";

    public static final String CMDTYPE = "cmdtype";

    public static final String SOUND = "sound";

    public static final String DEVICENAME = "devicename";

    public static final String CODE = "code";

    public static final String IMG = "img";

    public static final String PLUGIN_NAME = "pluginname";

    public static final String PLUGINID = "pluginid";

    public static final String CATEGORY = "category";

    public static final String SUBCATEGORY = "subcategory";

    private static int messageNotificationID = 1000;


    @Override
    public void onReceive(Context context, Intent intent) {
        DDLog.i("onReceive", "DNMessageReceiver:onReceive");
        if (messageNotification == null) {
            messageNotificationManager = (NotificationManager) context.getSystemService(context.NOTIFICATION_SERVICE);
        }

        try {
            JSONObject jsonObject = new JSONObject(intent.getExtras().get(EXTRA).toString());
            messageIntent = new Intent(context, SpashActivity.class);
            messageIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
            messageIntent.putExtra(DEVICEID, DDJSONUtil.getString(jsonObject, DEVICEID));
            messageIntent.putExtra(CMDTYPE, DDJSONUtil.getString(jsonObject, CMDTYPE));
            messageIntent.putExtra(SOUND, DDJSONUtil.getString(jsonObject, SOUND));
            messageIntent.putExtra(DEVICENAME, DDJSONUtil.getString(jsonObject, DEVICENAME));
            messageIntent.putExtra(CODE, DDJSONUtil.getInt(jsonObject, CODE));
            messageIntent.putExtra(IMG, DDJSONUtil.getString(jsonObject, IMG));
            messageIntent.putExtra(ALERT, intent.getExtras().get(ALERT).toString());
            messageIntent.putExtra(PLUGIN_NAME, DDJSONUtil.getString(jsonObject, PLUGIN_NAME));
            messageIntent.putExtra(PLUGINID, DDJSONUtil.getString(jsonObject, PLUGINID));
            messageIntent.putExtra(CATEGORY, DDJSONUtil.getString(jsonObject, CATEGORY));
            messageIntent.putExtra(SUBCATEGORY, DDJSONUtil.getString(jsonObject, SUBCATEGORY));
            messagePendingIntent = PendingIntent.getActivity(context, UUID.randomUUID().hashCode(), messageIntent, PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT);
            showNotification(context, intent.getExtras().get(ALERT).toString(), jsonObject);

            String user = DBUtil.SGet(DBKey.USER_KEY);
//             if (!TextUtils.isEmpty(user)) {
//                 LoginResponse loginResponse = JSON.parseObject(user, LoginResponse.class);
//                 String cmdType = jsonObject.getString(CMDTYPE);
//                 if (LocalKey.ARM_KEY.equals(cmdType)) {
//                     EventBus.getDefault().post(new SelfTestEvent(SelfTestEvent.PUSH_TASK));
//                 }
//                 int code = jsonObject.getInt(CODE);
//                 if (!TextUtils.isEmpty(cmdType) && (
//                         (cmdType.equals(LocalKey.RESET_DEVICE) ||
//                                 (cmdType.equals(LocalKey.UPDATE_CONTACT) && code == 1)))) {
//                     DDLog.i(TAG, "设备重置，清除涂鸦登录标记");
// //                账号被移除了,或者主机重置,需要移除对应的主机
//                     for (int i = 0; i < loginResponse.getResult().getDevice().size(); i++) {
//                         if (loginResponse.getResult().getDevice().get(i).getDeviceid().equals(
//                                 jsonObject.getString(DEVICEID))) {
//                             if (i == DBUtil.Num(DBKey.CURRENT_DEVICE)) {
// //                                需要修复下标(在没有退出app的时候收到push,点击则需要删除对应的主机)
//                                 DBUtil.Put(DBKey.CURRENT_DEVICE, 0);
//                             }
//                             loginResponse.getResult().getDevice().remove(i);
//                             CommonDataUtil.getInstance().setUser(loginResponse);
//                             DBUtil.SPut(DBKey.USER_KEY, loginResponse);
//                             EventBus.getDefault().post(new UserDeviceListChangeEvent());
//                             break;
//                         }
//                     }
//
//                 } else if (!TextUtils.isEmpty(cmdType) && cmdType.equals(LocalKey.ONLINE_STATE)) {
//                     EventBus.getDefault().post(new DeviceOnlineEvent());
//
//                 }
//             }
        } catch (Exception e) {
//            e.printStackTrace();
        }

    }

    private void showNotification(Context context, String content, JSONObject jsonObject) throws JSONException {
        String title = DDJSONUtil.getString(jsonObject, DEVICENAME);
        String soundFile = DDJSONUtil.getString(jsonObject, SOUND);
        String imageurl = DDJSONUtil.getString(jsonObject, IMG);
        String CHANNEL_ID = TextUtils.isEmpty(soundFile) ? context.getResources().getString(R.string.app_name) : soundFile.toUpperCase();
        int soundId = context.getResources().getIdentifier(soundFile, "raw", context.getPackageName());
        Uri path = Uri.parse("android.resource://" + context.getPackageName() + "/" + soundId);
//        JPushInterface.clearAllNotifications(context);

        try {
            if (TextUtils.isEmpty(imageurl)) {

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    @SuppressLint("WrongConstant")
                    NotificationChannel channel = new NotificationChannel(PushUtil.getNotificationChannelId(CHANNEL_ID),
                            CHANNEL_ID,
                            NotificationManager.IMPORTANCE_HIGH);
                    channel.enableVibration(true);
                    channel.enableLights(true);
                    channel.setSound(path,
                            Notification.AUDIO_ATTRIBUTES_DEFAULT);

                    messageNotificationManager.createNotificationChannel(channel);
                    messageNotification = new Notification.Builder(context, CHANNEL_ID)
                            .setAutoCancel(true)
                            .setContentTitle(title)
                            .setContentText(content)
                            .setDefaults(Notification.DEFAULT_VIBRATE)
                            .setContentIntent(messagePendingIntent)
                            .setSmallIcon(R.mipmap.icon_notification_tran_bg)
                            .setColorized(!DDSystemUtil.BRAND_SAMSUN.equals(DDSystemUtil.getDeviceBrand()))
                            .setColor(ContextCompat.getColor(context,R.color.notification_icon_bg_color))
                            .setWhen(System.currentTimeMillis())
                            .setChannelId(PushUtil.getNotificationChannelId(CHANNEL_ID))
                            .build();
                } else {
                    Notification.Builder builder = new Notification.Builder(context)
                            .setAutoCancel(true)
                            .setContentTitle(title)
                            .setContentText(content)
                            .setDefaults(Notification.DEFAULT_VIBRATE)
                            .setContentIntent(messagePendingIntent)
                            .setWhen(System.currentTimeMillis())
                            .setSound(path);

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        builder.setSmallIcon(R.mipmap.icon_notification_tran_bg);
                    } else {
                        builder.setSmallIcon(R.mipmap.ic_launcher);
                    }
                    messageNotification = builder.build();
                }

                messageNotification.flags = Notification.FLAG_AUTO_CANCEL;
                //发布消息
                messageNotificationManager.notify(messageNotificationID, messageNotification);
            } else {
                String url = DDSecretUtil.privateDownloadUrlWithDeadline(APIKey.DOOR_BELL_SERVER_IP + imageurl);
                Notification.BigPictureStyle style = new Notification.BigPictureStyle();
                style.setBigContentTitle(title);
                style.setSummaryText(content);

                Notification.Builder builder = new Notification.Builder(context)
                        .setAutoCancel(true)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setDefaults(Notification.DEFAULT_VIBRATE)
                        .setContentIntent(messagePendingIntent)
                        .setWhen(System.currentTimeMillis())
                        .setSound(path);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    builder.setSmallIcon(R.mipmap.icon_notification_tran_bg);
                } else {
                    builder.setSmallIcon(R.mipmap.ic_launcher);
                }
                new sendNotification(DinSaferApplication.getAppContext(),
                        builder, url,
                        style).execute();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}

class sendNotification extends AsyncTask<String, Void, Bitmap> {

    Context ctx;
    Notification.Builder mBuild;
    String imageUrl;
    Notification.BigPictureStyle bigPictureStyle;

    public sendNotification(Context context, Notification.Builder build, String imageurl, Notification.BigPictureStyle style) {
        super();
        this.ctx = context;
        this.mBuild = build;
        this.imageUrl = imageurl;
        this.bigPictureStyle = style;
    }

    @Override
    protected Bitmap doInBackground(String... params) {
        return ImageLoader.getInstance().loadImageSync(imageUrl);
    }

    @Override
    protected void onPostExecute(Bitmap result) {

        super.onPostExecute(result);
        try {
            NotificationManager notificationManager = (NotificationManager) ctx
                    .getSystemService(Context.NOTIFICATION_SERVICE);
            this.bigPictureStyle.bigPicture(result);
            this.mBuild.setStyle(bigPictureStyle);
            Notification messageNotification = this.mBuild
                    .build();
            messageNotification.flags = Notification.FLAG_AUTO_CANCEL;
            //发布消息
            notificationManager.notify(1000, messageNotification);


        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

