package com.dinsafer.dialog;

import android.content.Context;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.DialogCommonAlertBinding;
import com.dinsafer.util.ScreenUtils;

public class CommonAlertDialog extends BaseDialog<DialogCommonAlertBinding> {

    private Builder mBuilder;

    public CommonAlertDialog(@NonNull Context context, final Builder builder) {
        super(context, R.style.CommonDialogStyle);
        mBuilder = builder;
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = ScreenUtils.getScreenWidth(mContext) / 5 * 4;
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        layoutParams.gravity = Gravity.CENTER;
        getWindow().setAttributes(layoutParams);
    }

    @Override
    protected int layoutRes() {
        return R.layout.dialog_common_alert;
    }

    @Override
    protected void initView() {
        if (mBuilder == null) return;
        setCancelable(mBuilder.isCanCancel);
        setCanceledOnTouchOutside(mBuilder.isCanCancel);
        if (mBuilder.coverRes != -1) {
            mBinding.ivCover.setImageResource(mBuilder.coverRes);
            mBinding.ivCover.setVisibility(View.VISIBLE);
        } else {
            mBinding.ivCover.setVisibility(View.GONE);
        }
        if (mBuilder.titleTypeface != null) {
            mBinding.tvTitle.setTypeface(mBuilder.titleTypeface);
        }
        if (mBuilder.contentTypeface != null) {
            mBinding.tvContent.setTypeface(mBuilder.contentTypeface);
        }
        mBinding.tvTitle.setText(mBuilder.titleTxt);
        mBinding.tvContent.setText(mBuilder.contentTxt);
        mBinding.tvCancel.setLocalText(mBuilder.cancelTxt);
        mBinding.tvCancel.setVisibility(mBuilder.isShowCancel ? View.VISIBLE : View.GONE);
        mBinding.tvContent.post(new Runnable() {
            @Override
            public void run() {
                if (mBinding.tvContent.getLineCount() <= 1) {
                    mBinding.tvContent.setGravity(Gravity.CENTER_HORIZONTAL);
                }
            }
        });

        mBinding.lcbConfirm.setLocalText(mBuilder.confirmTxt);

        mBinding.tvTitle.setVisibility(mBuilder.showTitle ? View.VISIBLE : View.GONE);
        mBinding.tvContent.setVisibility(mBuilder.showContent ? View.VISIBLE : View.GONE);

        mBinding.lcbConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mBuilder.confirmCallback != null) {
                    mBuilder.confirmCallback.onConfirm(CommonAlertDialog.this);
                }
                if (mBuilder.isAutoDismiss) {
                    dismiss();
                }
            }
        });

        mBinding.tvCancel.setOnClickListener(view -> {
            if (mBuilder.confirmCallback != null) {
                mBuilder.confirmCallback.onCancel(CommonAlertDialog.this);
            }

            if (mBuilder.isAutoDismiss) {
                dismiss();
            }
        });
    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    public static class Builder {
        private Context mContext;
        private String titleTxt;
        private String contentTxt;
        private String confirmTxt;
        private String cancelTxt;
        private boolean showTitle = true;
        private boolean showContent = true;
        private boolean isCanCancel;
        private boolean isAutoDismiss;
        private boolean isShowCancel;
        private Typeface titleTypeface;
        private Typeface contentTypeface;
        private int coverRes = -1;
        private OnConfirmCallback confirmCallback;

        public Builder(Context context) {
            mContext = context;
        }

        public Builder setCoverRes(int coverRes) {
            this.coverRes = coverRes;
            return this;
        }

        public Builder setTitleTxt(String titleTxt) {
            this.titleTxt = titleTxt;
            return this;
        }

        public Builder setContentTxt(String contentTxt) {
            this.contentTxt = contentTxt;
            return this;
        }

        public Builder setConfirmTxt(String confirmTxt) {
            this.confirmTxt = confirmTxt;
            return this;
        }

        public Builder setCancelTxt(String cancelTxt) {
            this.cancelTxt = cancelTxt;
            return this;
        }

        public Builder showTitle(boolean showTitle) {
            this.showTitle = showTitle;
            return this;
        }

        public Builder showContent(boolean showContent) {
            this.showContent = showContent;
            return this;
        }

        public Builder setCancel(boolean isCanCancel) {
            this.isCanCancel = isCanCancel;
            return this;
        }

        public Builder setAutoDismiss(boolean isAutoDismiss) {
            this.isAutoDismiss = isAutoDismiss;
            return this;
        }

        public Builder setShowCancel(boolean isShowCancel) {
            this.isShowCancel = isShowCancel;
            return this;
        }

        public Builder setTitleTypeface(Typeface titleTypeface) {
            this.titleTypeface = titleTypeface;
            return this;
        }

        public Builder setContentTypeface(Typeface contentTypeface) {
            this.contentTypeface = contentTypeface;
            return this;
        }

        public Builder setConfirmCallback(OnConfirmCallback confirmCallback) {
            this.confirmCallback = confirmCallback;
            return this;
        }

        public CommonAlertDialog builder() {
            CommonAlertDialog commonAlertDialog = new CommonAlertDialog(mContext, this);
            commonAlertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);
            return commonAlertDialog;
        }
    }

    public interface OnConfirmCallback {
        void onConfirm(CommonAlertDialog dialog);

        void onCancel(CommonAlertDialog dialog);
    }
}
