package com.dinsafer.caremode;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.RandomStringUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

public class CareModeFragment extends BaseFragment
        implements IDeviceCallBack {

    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    ImageView commonBarLeftIcon;
    RelativeLayout titleLayout;
    ImageView ltView;
    View careModeSwitchLine1;
    LocalTextView careModeSwitchText;
    IOSSwitch careModeSwitch;
    View careModeSwitchLine2;
    RelativeLayout careModeSwitchLayout;
    LocalTextView careModeHint;
    View careModeLifeMotionLine1;
    LocalTextView careModeLifeMotionText;
    ImageView careModeLifeMotionArrow;
    LocalTextView careModeLifeMotionStatus;
    ImageView careModeLifeMotionHintBar;
    View careModeLifeMotionLine2;
    RelativeLayout careModeLifeMotionLayout;

    private JSONObject data;

    private boolean isErrorStatus = false;
    private boolean isHasPlugin = false;
    private boolean isHasChoosePlugin = false;
    private boolean isOpenCareMode;

    private String messageID;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static CareModeFragment newInstance() {
        CareModeFragment fragment = new CareModeFragment();
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.care_mode_layout, container, false);
        showTimeOutLoadinFramgmentWithErrorAlert();
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initView(rootView, savedInstanceState);
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener(v -> toClose());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarLeftIcon = rootView.findViewById(R.id.common_bar_left_icon);
        titleLayout = rootView.findViewById(R.id.title_layout);
        ltView = rootView.findViewById(R.id.lt_view);
        careModeSwitchLine1 = rootView.findViewById(R.id.care_mode_switch_line_1);
        careModeSwitchText = rootView.findViewById(R.id.care_mode_switch_text);
        careModeSwitch = rootView.findViewById(R.id.care_mode_switch);
        careModeSwitchLine2 = rootView.findViewById(R.id.care_mode_switch_line_2);
        careModeSwitchLayout = rootView.findViewById(R.id.care_mode_switch_layout);
        careModeHint = rootView.findViewById(R.id.care_mode_hint);
        careModeLifeMotionLine1 = rootView.findViewById(R.id.care_mode_life_motion_line_1);
        careModeLifeMotionText = rootView.findViewById(R.id.care_mode_life_motion_text);
        careModeLifeMotionArrow = rootView.findViewById(R.id.care_mode_life_motion_arrow);
        careModeLifeMotionStatus = rootView.findViewById(R.id.care_mode_life_motion_status);
        careModeLifeMotionHintBar = rootView.findViewById(R.id.care_mode_life_motion_hint_bar);
        careModeLifeMotionLine2 = rootView.findViewById(R.id.care_mode_life_motion_line_2);
        careModeLifeMotionLayout = rootView.findViewById(R.id.care_mode_life_motion_layout);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            removeSelf();
            return;
        }

        mPanelDevice.submit(PanelParamsHelper.getCareMode());
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getString(R.string.care_mode_life_title));
        careModeLifeMotionText.setLocalText(getString(R.string.care_mode_life_motion_text));
        careModeHint.setLocalText(getString(R.string.care_mode_hint));
        careModeSwitchText.setLocalText(getString(R.string.care_mode_switch_text));
        careModeSwitch.setOnSwitchStateChangeListener(new IOSSwitch.OnSwitchStateChangeListener() {
            @Override
            public void onStateSwitched(boolean isOn) {
                showTimeOutLoadinFramgmentWithErrorAlert();
                isSelfOperate = true;
                mPanelDevice.submit(PanelParamsHelper.setCareModeEnable(isOn));
                messageID = RandomStringUtils.getMessageId();
            }
        });

        careModeLifeMotionLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDelegateActivity().addCommonFragment(LifeStateMonitoringFragment.newInstance(data.toString()));
            }
        });
    }

    private void updata() {
        if (isOpenCareMode && isHasChoosePlugin) {
            careModeLifeMotionStatus.setLocalText(getString(R.string.done));
            careModeLifeMotionStatus.setVisibility(View.VISIBLE);
        } else {
            careModeLifeMotionStatus.setVisibility(View.GONE);
        }

        if (isOpenCareMode && (!isHasPlugin || !isHasChoosePlugin)) {
            careModeLifeMotionHintBar.setVisibility(View.VISIBLE);
        } else {
            careModeLifeMotionHintBar.setVisibility(View.GONE);
        }
    }

    private void checkIsHasPlugin() throws JSONException {
        if (data == null) {
            return;
        }
        isHasPlugin = false;
        isHasChoosePlugin = false;
        JSONObject careMode = DDJSONUtil.getJSONObject(data, "care_mode");

        isOpenCareMode = DDJSONUtil.getBoolean(careMode, "care_mode");
        careModeSwitch.setOn(isOpenCareMode);

        JSONObject pluginList = DDJSONUtil.getJSONObject(data, "plugin_info");

        JSONArray doors = DDJSONUtil.getJSONarray(pluginList, "door_window");
        if (doors != null && doors.length() > 0) {
            isHasPlugin = true;
            for (int i = 0; i < doors.length(); i++) {
                if (DDJSONUtil.getBoolean(doors.getJSONObject(i), "care_mode")) {
                    isHasChoosePlugin = true;
                    return;
                }
            }
        }

        JSONArray pir = DDJSONUtil.getJSONarray(pluginList, "pir");
        if (pir != null && pir.length() > 0) {
            isHasPlugin = true;
            for (int i = 0; i < pir.length(); i++) {
                if (DDJSONUtil.getBoolean(pir.getJSONObject(i), "care_mode")) {
                    isHasChoosePlugin = true;
                    return;
                }
            }
        }

        JSONArray smartPlug = DDJSONUtil.getJSONarray(pluginList, "smart_plug");
        if (smartPlug != null && smartPlug.length() > 0) {
            isHasPlugin = true;
            for (int i = 0; i < smartPlug.length(); i++) {
                if (DDJSONUtil.getBoolean(smartPlug.getJSONObject(i), "care_mode")) {
                    isHasChoosePlugin = true;
                    return;
                }
            }
        }

        JSONArray repeaterPlug = DDJSONUtil.getJSONarray(pluginList, "signal_repeater_plug");
        if (repeaterPlug != null && repeaterPlug.length() > 0) {
            isHasPlugin = true;
            for (int i = 0; i < repeaterPlug.length(); i++) {
                if (DDJSONUtil.getBoolean(repeaterPlug.getJSONObject(i), "care_mode")) {
                    isHasChoosePlugin = true;
                    return;
                }
            }
        }

//        JSONArray oldDoorWindow = DDJSONUtil.getJSONarray(pluginList, "old_door_window");
//        if (oldDoorWindow.length() > 0) {
//            isHasPlugin = true;
//            for (int i = 0; i < oldDoorWindow.length(); i++) {
//                if (DDJSONUtil.getBoolean(oldDoorWindow.getJSONObject(i), "care_mode")) {
//                    isHasChoosePlugin = true;
//                    return;
//                }
//            }
//        }
//
//        JSONArray oldPir = DDJSONUtil.getJSONarray(pluginList, "old_pir");
//        if (oldPir.length() > 0) {
//            isHasPlugin = true;
//            for (int i = 0; i < oldPir.length(); i++) {
//                if (DDJSONUtil.getBoolean(oldPir.getJSONObject(i), "care_mode")) {
//                    isHasChoosePlugin = true;
//                    return;
//                }
//            }
//        }
//
//        JSONArray oldSmartPlug = DDJSONUtil.getJSONarray(pluginList, "old_smart_plug");
//        if (oldSmartPlug.length() > 0) {
//            isHasPlugin = true;
//            for (int i = 0; i < oldSmartPlug.length(); i++) {
//                if (DDJSONUtil.getBoolean(oldSmartPlug.getJSONObject(i), "care_mode")) {
//                    isHasChoosePlugin = true;
//                    return;
//                }
//            }
//        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPluginChange(CareModePluginChange ev) {
        try {
            data.put("plugin_info", new JSONObject(ev.getData()));
//            更新isHasChoosePlugin等字段
            checkIsHasPlugin();
            updata();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (PanelCmd.GET_CAREMODE.equals(cmd)) {
            // 获取CareMode的设置
            onGetCareModeData(status, map);
        } else if (resultType == 1) {
            if (PanelCmd.SET_CAREMODE.equals(cmd)) {
                // 设置CareMode
                onSetCareModeState(status, map);
                isSelfOperate = false;
            }
        }
    }

    /**
     * 获取到CareMode数据
     */
    private void onGetCareModeData(int status, Map map) {
        DDLog.i(TAG, "onGetCareModeData, status: " + status + ", result: " + map);
        closeLoadingFragment();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            showErrorToast();
            return;
        }

        try {
            data = new JSONObject(DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, ""));
            checkIsHasPlugin();
            updata();
            closeTimeOutLoadinFramgmentWithErrorAlert();
        } catch (Exception e) {
            DDLog.e(TAG, "Error");
            e.printStackTrace();
        }
    }

    /**
     * 设置CareMode的开启状态
     */
    private void onSetCareModeState(int status, Map map) {
        DDLog.i(TAG, "onSetCareModeState, status: " + status + ", result: " + map);
        JSONObject careMode = DDJSONUtil.getJSONObject(data, "care_mode");
        if (PanelDataKey.CmdResult.SUCCESS == status && null != careMode) {
            try {
                JSONObject result = new JSONObject(DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, ""));
                if (DDJSONUtil.has(result, "care_mode")) {
                    DDLog.i(TAG, "Update local data.");
                    boolean open = DDJSONUtil.getBoolean(result, "care_mode");
                    careMode.put("care_mode", open);
                    if (isSelfOperate) {
                        checkIsHasPlugin();
                        updata();

                        if (open && !isHasChoosePlugin) {
                            getDelegateActivity().addCommonFragment(LifeStateMonitoringFragment.newInstance(data.toString()));
                            getDelegateActivity().addCommonFragment(LifeMonitorPluginFragment.newInstance(data.toString()));
                        }
                    }
                }
                if (DDJSONUtil.has(result, "alarm_delay_time")) {
                    careMode.put("alarm_delay_time", DDJSONUtil.getInt(result, "alarm_delay_time"));
                }
                if (DDJSONUtil.has(result, "no_action_time")) {
                    careMode.put("no_action_time", DDJSONUtil.getInt(result, "no_action_time"));
                }
            } catch (JSONException e) {
                DDLog.e(TAG, "Error");
                e.printStackTrace();
            }
        }

        if (isSelfOperate) {
            closeTimeOutLoadinFramgmentWithErrorAlert();
            if (PanelDataKey.CmdResult.SUCCESS == status) {
                showSuccess();
            } else {
                showErrorToast();
            }
        }
    }
}
