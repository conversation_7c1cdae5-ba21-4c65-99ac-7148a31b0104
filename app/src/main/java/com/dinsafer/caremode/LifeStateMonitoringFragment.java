package com.dinsafer.caremode;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module.settting.ui.TimePicker;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Map;

public class LifeStateMonitoringFragment extends BaseFragment
        implements IDeviceCallBack {
    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    ImageView commonBarLeftIcon;
    RelativeLayout titleLayout;
    View lifeMotionAccLine1;
    LocalTextView lifeMotionAccText;
    ImageView careModeLifeMotionArrow;
    View lifeMotionAccLine2;
    RelativeLayout lifeMotionAccLayout;
    LocalTextView lifeMonitorAccHint;
    View careModeMaxTimeLine1;
    LocalTextView careModeMaxTimeText;
    ImageView careModeMaxTimeArrow;
    View careModeMaxTimeLine2;
    RelativeLayout lifeStateTimepickerLayout;
    LocalTextView lifeStateSystemAlarmDelayText;
    ImageView lifeStateSystemAlarmDelayArrow;
    LocalTextView lifeStateSystemAlarmDelayStatus;
    View lifeStateSystemAlarmDelayLine2;
    RelativeLayout lifeStateSystemAlarmDelayLayout;
    LocalTextView careModeMaxTimeStatus;

    private JSONObject data;

    private int alarmDelayTime;
    private int noActionTime;

    int noActionTimeIndex;
    int delayTimeIndex;

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static LifeStateMonitoringFragment newInstance(String data) {
        LifeStateMonitoringFragment fragment = new LifeStateMonitoringFragment();
        Bundle bundle = new Bundle();
        bundle.putString("data", data);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.life_state_mon_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initView(rootView, savedInstanceState);
        initData();
        EventBus.getDefault().register(this);
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
        rootView.findViewById(R.id.life_motion_acc_layout).setOnClickListener( v -> toPlugins());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarLeftIcon = rootView.findViewById(R.id.common_bar_left_icon);
        titleLayout = rootView.findViewById(R.id.title_layout);
        lifeMotionAccLine1 = rootView.findViewById(R.id.life_motion_acc_line_1);
        lifeMotionAccText = rootView.findViewById(R.id.life_motion_acc_text);
        careModeLifeMotionArrow = rootView.findViewById(R.id.care_mode_life_motion_arrow);
        lifeMotionAccLine2 = rootView.findViewById(R.id.life_motion_acc_line_2);
        lifeMotionAccLayout = rootView.findViewById(R.id.life_motion_acc_layout);
        lifeMonitorAccHint = rootView.findViewById(R.id.life_monitor_acc_hint);
        careModeMaxTimeLine1 = rootView.findViewById(R.id.care_mode_max_time_line_1);
        careModeMaxTimeText = rootView.findViewById(R.id.care_mode_max_time_text);
        careModeMaxTimeArrow = rootView.findViewById(R.id.care_mode_max_time_arrow);
        careModeMaxTimeLine2 = rootView.findViewById(R.id.care_mode_max_time_line_2);
        lifeStateTimepickerLayout = rootView.findViewById(R.id.life_state_timepicker_layout);
        lifeStateSystemAlarmDelayText = rootView.findViewById(R.id.life_state_system_alarm_delay_text);
        lifeStateSystemAlarmDelayArrow = rootView.findViewById(R.id.life_state_system_alarm_delay_arrow);
        lifeStateSystemAlarmDelayStatus = rootView.findViewById(R.id.life_state_system_alarm_delay_status);
        lifeStateSystemAlarmDelayLine2 = rootView.findViewById(R.id.life_state_system_alarm_delay_line_2);
        lifeStateSystemAlarmDelayLayout = rootView.findViewById(R.id.life_state_system_alarm_delay_layout);
        careModeMaxTimeStatus = rootView.findViewById(R.id.care_mode_max_time_status);
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice || null == data) {
            showErrorToast();
            removeSelf();
        }
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getString(R.string.care_mode_life_motion_text));
        lifeMotionAccText.setLocalText(getString(R.string.life_motion_acc_text));
        lifeMonitorAccHint.setLocalText(getString(R.string.life_monitor_acc_hint));
        careModeMaxTimeText.setLocalText(getString(R.string.care_mode_max_time_text));
        lifeStateSystemAlarmDelayText.setLocalText(getString(R.string.life_state_system_alarm_delay_text));
        try {
            getData();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        noActionTimeIndex = 6;
        ArrayList maxNoActiontime = new ArrayList();
        for (int i = 2; i <= 24; i++) {
            maxNoActiontime.add(i + "h");
            if (noActionTime == (i) * 60 * 60) {
                noActionTimeIndex = i - 2;
            }
        }


        lifeStateTimepickerLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TimePicker picker = TimePicker.newInstance(Local.s(getResources().getString(R.string.care_mode_max_time_text)),
                        maxNoActiontime, noActionTimeIndex);
                picker.setCallBack(new TimePicker.ITimePickerCallBack() {
                    @Override
                    public void getSelect(String select, int selectIndex) {
                        noActionTimeIndex = selectIndex;
                        int time = (selectIndex + 2) * 60 * 60;
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        isSelfOperate = true;
                        mPanelDevice.submit(PanelParamsHelper.setCareModeNoAction(time));
                    }
                });

                getDelegateActivity().addCommonFragment(picker);
            }
        });

        delayTimeIndex = 6;
        ArrayList delayTime = new ArrayList();
        for (int i = 30; i <= 90; i = i + 5) {
            delayTime.add(i + "s");
            if (alarmDelayTime == i) {
                delayTimeIndex = delayTime.size() - 1;
            }
        }
        lifeStateSystemAlarmDelayLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TimePicker picker = TimePicker.newInstance(Local.s(getResources().getString(R.string.life_state_system_alarm_delay_text)),
                        delayTime, delayTimeIndex);
                picker.setCallBack(new TimePicker.ITimePickerCallBack() {
                    @Override
                    public void getSelect(String select, int selectIndex) {
                        delayTimeIndex = selectIndex;
                        showTimeOutLoadinFramgmentWithErrorAlert();
                        isSelfOperate = true;
                        mPanelDevice.submit(PanelParamsHelper.setCareModeAlarmDelayTime(30 + 5 * selectIndex));
                    }
                });

                getDelegateActivity().addCommonFragment(picker);
            }
        });
    }

    private void getData() throws JSONException {
        String d = getArguments().getString("data");
        data = new JSONObject(d);
        JSONObject careMode = DDJSONUtil.getJSONObject(data, "care_mode");
        alarmDelayTime = DDJSONUtil.getInt(careMode, "alarm_delay_time");
        noActionTime = DDJSONUtil.getInt(careMode, "no_action_time");

        careModeMaxTimeStatus.setText(noActionTime / 60 / 60 + "h");
        careModeMaxTimeStatus.setVisibility(View.VISIBLE);
        lifeStateSystemAlarmDelayStatus.setText(alarmDelayTime + "s");
        lifeStateSystemAlarmDelayStatus.setVisibility(View.VISIBLE);
    }

    private void updataTime() {
        careModeMaxTimeStatus.setText(noActionTime / 60 / 60 + "h");
        lifeStateSystemAlarmDelayStatus.setText(alarmDelayTime + "s");
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPluginChange(CareModePluginChange ev) {
        try {
            data.put("plugin_info", new JSONObject(ev.getData()));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void toClose() {
        removeSelf();
    }

    public void toPlugins() {
        getDelegateActivity().addCommonFragment(LifeMonitorPluginFragment.newInstance(data.toString()));
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_CAREMODE.equals(cmd)) {
                onSetCareModeState(status, map);
                isSelfOperate = false;
            }
        }
    }

    /**
     * 设置CareMode的开启状态
     */
    private void onSetCareModeState(int status, Map map) {
        DDLog.i(TAG, "onSetCareModeState, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS == status) {
            JSONObject careMode = DDJSONUtil.getJSONObject(data, "care_mode");
            if (null != careMode) {
                try {
                    JSONObject result = new JSONObject(DeviceHelper.getString(map, PanelDataKey.CmdResult.RESULT, ""));
                    if (DDJSONUtil.has(result, "alarm_delay_time")) {
                        alarmDelayTime = DDJSONUtil.getInt(result, "alarm_delay_time");
                        careMode.put("alarm_delay_time", alarmDelayTime);
                        updataTime();
                    }
                    if (DDJSONUtil.has(result, "no_action_time")) {
                        noActionTime = DDJSONUtil.getInt(result, "no_action_time");
                        careMode.put("no_action_time", noActionTime);
                        updataTime();
                    }
                } catch (JSONException e) {
                    DDLog.e(TAG, "Error");
                    e.printStackTrace();
                }
            }
        } else {
            showErrorToast();
        }
    }

}
