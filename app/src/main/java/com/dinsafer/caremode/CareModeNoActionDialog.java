package com.dinsafer.caremode;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dinnet.R;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.Local;

import java.util.concurrent.atomic.AtomicInteger;


/**
 * Created by rinfon on 15/6/26.
 */
public class CareModeNoActionDialog extends Dialog {
    private final String TAG = CareModeNoActionDialog.class.getSimpleName();
    private final int MAX_ERROR_COUNT = 5; // 最大重试次数
    private final int ACTION_TIMEOUT_SECOND = 5; // 操作超时时长

    int layoutRes;//布局文件

    Context mContext;

    LocalCustomButton mCancel;

    private LocalTextView mContent, mOk;

    private boolean isCanCancel = true;

    private CountDownTimer countDownTimer, mTimeoutTimer;
    private AtomicInteger mActionErrorCount = new AtomicInteger(0); // 操作错误次数

    private TextView mCountTextView;
    RelativeLayout mOkLayout;

    public CareModeNoActionDialog(Context context, final Builder builder) {
        super(context, R.style.CustomDialogStyle);
        mContext = context;
        this.layoutRes = R.layout.care_mode_no_action_alert_dialog;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(layoutRes, null);
        setContentView(view);
        mContent = (LocalTextView) view.findViewById(R.id.round_alert_dialog_content);
        mContent.setMovementMethod(ScrollingMovementMethod.getInstance());
        mOk = (LocalTextView) view.findViewById(R.id.care_mode_no_action_dia_alarm);
        mCancel = (LocalCustomButton) view.findViewById(R.id.care_mode_no_action_dia_cancel);
        mCountTextView = (TextView) view.findViewById(R.id.care_mode_no_action_dia_alarm_count);
        mOkLayout = (RelativeLayout) view.findViewById(R.id.care_mode_no_action_dia_alarm_ly);
        mOkLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                setButtonEnable(false, true);
                startTimeoutCounter();
                mOk.setText(Local.s(mContext.getString(R.string.loading)));

                toCareModeNoActionSOS();
            }
        });

        mCancel.setLocalText(mContext.getString(R.string.care_mode_no_action_cancel));
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setButtonEnable(false, false);
                startTimeoutCounter();
                mCancel.setText(Local.s(mContext.getString(R.string.loading)));

                toCancelCareModeNoActionSOS();
            }
        });

        String okText = Local.s(mContext.getString(R.string.care_mode_no_action_alarm));
        mOk.setText(okText);
        mContent.setLocalText(builder.mContent);
        isCanCancel = builder.isCanCancel;

        countDownTimer = new CountDownTimer(builder.startCountTime * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                mCountTextView.setText(millisUntilFinished / 1000 + "s");
            }

            @Override
            public void onFinish() {
                dismiss();
            }
        };
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                countDownTimer.cancel();
                stopTimeoutCounter();
            }
        });
        countDownTimer.start();
    }

    /**
     * 开始操作倒计时
     */
    private void startTimeoutCounter() {
        DDLog.d(TAG, "开始操作超时计时");
        mTimeoutTimer = new CountDownTimer(ACTION_TIMEOUT_SECOND * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                DDLog.e(TAG, "操作超时");
                setButtonEnable(true, false);
                if (mActionErrorCount.incrementAndGet() > MAX_ERROR_COUNT) {
                    DDLog.e(TAG, "超过最大的操作错误次数，最大次数: " + MAX_ERROR_COUNT);
                    dismiss();
                }
            }
        };
        mTimeoutTimer.start();
    }

    /**
     * 取消操作倒计时
     */
    private void stopTimeoutCounter() {
        if (null != mTimeoutTimer) {
            mTimeoutTimer.cancel();
        }
    }

    /**
     * 修改按钮的可用状态
     *
     * @param enable
     * @param hindCounterText 是否隐藏倒计时
     */
    private void setButtonEnable(boolean enable, boolean hindCounterText) {
        if (enable) {
            mOk.setEnabled(true);
            mCancel.setEnabled(true);
            mOk.setAlpha(1.0f);
            mCancel.setAlpha(1.0f);
            mCountTextView.setVisibility(View.VISIBLE);

            String okText = Local.s(mContext.getString(R.string.care_mode_no_action_alarm));
            String cancelText = Local.s(mContext.getString(R.string.care_mode_no_action_cancel));
            mOk.setText(okText);
            mCancel.setText(cancelText);
        } else {
            if (hindCounterText) {
                mCountTextView.setVisibility(View.GONE);
            }

            mOk.setEnabled(false);
            mCancel.setEnabled(false);
            mOk.setAlpha(0.5f);
            mCancel.setAlpha(0.5f);
        }
    }

    public static Builder createBuilder(Context context) {
        return new Builder(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setCanceledOnTouchOutside(false);
    }

    /**
     * 发送报警指令
     */
    private void toCareModeNoActionSOS() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final Device panelDevice = DinHome.getInstance().getDevice(panelId);
        if (null != panelDevice) {
            panelDevice.submit(PanelParamsHelper.noActionCareModeSos());
        }
    }

    /**
     * 发送取消倒计时指令
     */
    private void toCancelCareModeNoActionSOS() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        final Device panelDevice = DinHome.getInstance().getDevice(panelId);
        if (null != panelDevice) {
            panelDevice.submit(PanelParamsHelper.cancelCareModeSos());
        }
    }


    @Override
    public void cancel() {
/**       TODO 区分出点击取消按钮获取是点击空白地方
 *        1.如果是空白地方或者返回键,并且输入法为弹出状态,则关闭输入法,但不关闭dialog
 *        2.如果点击取消,则关闭dialog
 */
        if (isCanCancel)
            super.cancel();
    }

    public void setOKText(String ok) {
        mOk.setLocalText(ok);
    }

    public void setOKClick(View.OnClickListener onclick) {
        mOk.setOnClickListener(onclick);
    }

    public void setContent(String content) {

        mContent.setLocalText(content);
    }

    public interface AlertOkClickCallback {

        void onOkClick();
    }

    public static class Builder {

        private Context mContext;

        private String mContent;

        private boolean isCanCancel = true;

        private boolean isAutoDismiss = true;

        private boolean isRound1 = true;

        private AlertOkClickCallback okClick;

        /**
         * 单位：秒
         */
        private int startCountTime;

        public Builder(Context context) {
            mContext = context;
        }


        public Builder setOKListener(AlertOkClickCallback listener) {
            this.okClick = listener;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }


        public Builder setAutoDissmiss(boolean isAutoDismiss) {
            this.isAutoDismiss = isAutoDismiss;
            return this;
        }

        public Builder setCanCancel(boolean isCanCancel) {
            this.isCanCancel = isCanCancel;
            return this;
        }

        public Builder setRound1(boolean isRound1) {
            this.isRound1 = isRound1;
            return this;
        }

        public Builder setStartTime(int time) {
            this.startCountTime = time;
            return this;
        }

        public CareModeNoActionDialog preBuilder() {
            CareModeNoActionDialog alertDialog = new CareModeNoActionDialog(mContext, this);
            alertDialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM);

            return alertDialog;
        }

    }

}
