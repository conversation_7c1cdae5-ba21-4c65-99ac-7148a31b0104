package com.dinsafer.caremode;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dinsafer.dinnet.R;
import com.dinsafer.ui.IOSSwitch;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.SectionedBaseAdapter;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.Local;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

/**
 * Created by Rinfon on 16/7/1.
 */
public class CareModePluginAdapter extends SectionedBaseAdapter {

    //    @BindView(R.id.care_mode_plugin_header)
//    LocalTextView header;
    private Activity mActivity;

    private JSONObject mData;

    private ArrayList<String> header;

    private boolean isShowSectionTitle = false;

    public CareModePluginAdapter(Activity mActivity, JSONObject mData, ArrayList<String> header, boolean isShowSectionTitle) {
        this.mActivity = mActivity;
        this.mData = mData;
        this.header = header;
        this.isShowSectionTitle = isShowSectionTitle;
    }

    @Override
    public Object getItem(int section, int position) {
        return null;
    }

    @Override
    public long getItemId(int section, int position) {
        return 0;
    }

    @Override
    public int getSectionCount() {
        return mData.length();
    }

    @Override
    public int getCountForSection(int section) {
        JSONArray array = DDJSONUtil.getJSONarray(mData, section + "");
        if (array == null) {
            return 0;
        }
        return array.length();
    }

    @Override
    public View getItemView(final int section, final int position, View convertView, ViewGroup parent) {
        ViewHolder holder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(mActivity).inflate(R.layout.care_mode_plugin_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            try {
                holder = (ViewHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        JSONArray plugin = DDJSONUtil.getJSONarray(mData, section + "");
        if (position == getCountForSection(section) - 1) {
            holder.pluginLineMargin.setVisibility(View.GONE);
            holder.pluginLineEnd.setVisibility(View.VISIBLE);
        } else {
            holder.pluginLineMargin.setVisibility(View.VISIBLE);
            holder.pluginLineEnd.setVisibility(View.GONE);
        }

        ViewHolder finalHolder = holder;
        holder.pluginChoose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JSONObject data = null;
                try {
                    data = plugin.getJSONObject(position);
                    data.put("care_mode", !DDJSONUtil.getBoolean(data, "care_mode"));
                    if (DDJSONUtil.getBoolean(data, "care_mode")) {
                        finalHolder.pluginChoose.setImageResource(R.drawable.choose_sel);
                    } else {
                        finalHolder.pluginChoose.setImageResource(R.drawable.choose_nor);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });

        holder.pluginName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JSONObject data = null;
                try {
                    data = plugin.getJSONObject(position);
                    data.put("care_mode", !DDJSONUtil.getBoolean(data, "care_mode"));
                    if (DDJSONUtil.getBoolean(data, "care_mode")) {
                        finalHolder.pluginChoose.setImageResource(R.drawable.choose_sel);
                    } else {
                        finalHolder.pluginChoose.setImageResource(R.drawable.choose_nor);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });

        try {
            JSONObject data = plugin.getJSONObject(position);
            String name = DDJSONUtil.getString(data, "name");
            String decodeid = DDJSONUtil.getString(data, "decodeid");
            String id = DDJSONUtil.getString(data, "id");
            String stype = DDJSONUtil.getString(data, "stype");
            if (TextUtils.isEmpty(name)) {
                if (!TextUtils.isEmpty(decodeid)) {
                    //   如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                    name = CommonDataUtil.getInstance().getSTypeByDecodeid(decodeid);
                } else if (id.startsWith("!")) {
                    name = CommonDataUtil.getInstance().getASKNameByBSType(stype);
                } else {
                    name = CommonDataUtil.getInstance().getSTypeByID(id);
                }
                name = Local.s(name) + "_" + id;
            }
            holder.pluginName.setLocalText(name);
            if (DDJSONUtil.getBoolean(data, "care_mode")) {
                holder.pluginChoose.setImageResource(R.drawable.choose_sel);
            } else {
                holder.pluginChoose.setImageResource(R.drawable.choose_nor);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }


        return convertView;
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        sectionHeaderHolder mHolder = null;
        if (convertView == null) {
            mHolder = new sectionHeaderHolder();
            LayoutInflater inflator = (LayoutInflater) parent.getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflator.inflate(R.layout.care_mode_plugin_section_header, null);
            mHolder.titleName = (LocalTextView) convertView.findViewById(R.id.care_mode_plugin_header);
            convertView.setTag(mHolder);
        } else {
            try {
                mHolder = (sectionHeaderHolder) convertView.getTag();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (isShowSectionTitle) {
            mHolder.titleName.setLocalText(header.get(section));
            mHolder.titleName.setVisibility(View.VISIBLE);
        } else {
            mHolder.titleName.setVisibility(View.GONE);
        }
        return convertView;
    }


    static class sectionHeaderHolder {
        LocalTextView titleName;
    }

    static class ViewHolder {
        LocalTextView pluginName;
        ImageView pluginChoose;
        View pluginLineMargin;
        View pluginLineEnd;

        ViewHolder(View view) {
            __bindViews(view);
        }

        private void __bindViews(View view) {
            pluginName = view.findViewById(R.id.plugin_name);
            pluginChoose = view.findViewById(R.id.plugin_choose);
            pluginLineMargin = view.findViewById(R.id.plugin_line_margin);
            pluginLineEnd = view.findViewById(R.id.plugin_line_end);
        }
    }
}
