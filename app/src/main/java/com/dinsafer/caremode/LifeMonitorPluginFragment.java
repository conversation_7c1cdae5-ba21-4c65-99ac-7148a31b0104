package com.dinsafer.caremode;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.dinsafer.common.PluginConstants;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.model.panel.MainPanelHelper;
import com.dinsafer.module.BaseFragment;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.PinnedHeaderListView;
import com.dinsafer.util.CommonDataUtil;
import com.dinsafer.util.DDJSONUtil;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Map;

public class LifeMonitorPluginFragment extends BaseFragment
        implements IDeviceCallBack {

    ImageView commonBarBack;
    LocalTextView commonBarTitle;
    ImageView commonBarLeftIcon;
    RelativeLayout titleLayout;
    LocalTextView pluginStatusHint;
    PinnedHeaderListView careModePluginList;
    LocalTextView careModePluginSave;

    private JSONObject data;

    private CareModePluginAdapter adapter;
    private JSONObject mAllData = new JSONObject();

    private final ArrayList<String> headers = new ArrayList<>();

    private Device mPanelDevice;
    private boolean isSelfOperate;

    public static LifeMonitorPluginFragment newInstance(String data) {
        LifeMonitorPluginFragment fragment = new LifeMonitorPluginFragment();
        Bundle bundle = new Bundle();
        bundle.putString("data", data);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        View rootView = inflater.inflate(R.layout.life_state_mon_plugin_layout, container, false);
        __bindViews(rootView);
        __bindClicks(rootView);
        findPanel();
        initView(rootView, savedInstanceState);
        initData();
        return rootView;
    }

    private void __bindClicks(View rootView) {
        rootView.findViewById(R.id.care_mode_plugin_save).setOnClickListener( v -> toSave());
        rootView.findViewById(R.id.common_bar_back).setOnClickListener( v -> toClose());
    }

    private void __bindViews(View rootView) {
        commonBarBack = rootView.findViewById(R.id.common_bar_back);
        commonBarTitle = rootView.findViewById(R.id.common_bar_title);
        commonBarLeftIcon = rootView.findViewById(R.id.common_bar_left_icon);
        titleLayout = rootView.findViewById(R.id.title_layout);
        pluginStatusHint = rootView.findViewById(R.id.plugin_status_hint);
        careModePluginList = rootView.findViewById(R.id.care_mode_plugin_list);
        careModePluginSave = rootView.findViewById(R.id.care_mode_plugin_save);
    }

    private void findPanel() {
        final String panelId = CommonDataUtil.getInstance().getCurrentPanelID();
        if (!TextUtils.isEmpty(panelId)
                && null != DinHome.getInstance().getDevice(panelId)) {
            mPanelDevice = DinHome.getInstance().getDevice(panelId);
            mPanelDevice.registerDeviceCallBack(this);
        }
    }

    @Override
    public void onFinishAnim() {
        super.onFinishAnim();
        if (null == mPanelDevice || null == data) {
            showErrorToast();
            removeSelf();
        }
    }

    @Override
    public void initView(View inflateView, Bundle savedInstanceState) {
        super.initView(inflateView, savedInstanceState);
    }

    @Override
    public void initData() {
        super.initData();
        commonBarTitle.setLocalText(getString(R.string.life_motion_acc_text));
        pluginStatusHint.setLocalText(getString(R.string.care_mode_plugin_hint));
        careModePluginSave.setLocalText(getString(R.string.save));
        careModePluginList.setPinHeaders(false);
        try {
            getData();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        adapter = new CareModePluginAdapter(getMainActivity(), mAllData, headers, true);
        careModePluginList.setAdapter(adapter);
    }

    private void getData() throws JSONException {
        String d = getArguments().getString("data");
        data = new JSONObject(d);
        JSONObject pluginList = DDJSONUtil.getJSONObject(data, "plugin_info");


        JSONArray pirList = DDJSONUtil.getJSONarray(pluginList, "pir");

        if (pirList != null && pirList.length() > 0) {
            mAllData.put(mAllData.length() + "", pirList);
            headers.add(CommonDataUtil.getInstance().getNameByStype("09"));
        }

        JSONArray doorList = DDJSONUtil.getJSONarray(pluginList, "door_window");
        if (doorList != null && doorList.length() > 0) {
            mAllData.put(mAllData.length() + "", doorList);
            headers.add(CommonDataUtil.getInstance().getNameByStype("0B"));
        }

        JSONArray switchList = DDJSONUtil.getJSONarray(pluginList, "smart_plug");

        if (switchList != null && switchList.length() > 0) {
            mAllData.put(mAllData.length() + "", switchList);
            headers.add(CommonDataUtil.getInstance().getNameByStype("3E"));
        }

        JSONArray repeaterList = DDJSONUtil.getJSONarray(pluginList, "signal_repeater_plug");

        if (repeaterList != null && repeaterList.length() > 0) {
            mAllData.put(mAllData.length() + "", repeaterList);
            headers.add(CommonDataUtil.getInstance().getNameByStype(PluginConstants.TYPE_4E));
        }


        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (mAllData.length() <= 0) {
                    careModePluginSave.setEnabled(false);
                    careModePluginSave.setAlpha(MainPanelHelper.VIEW_DISABLE_ALPHA);
                    pluginStatusHint.setLocalText(getString(R.string.care_mode_no_plugin_hint));
                } else {
                    pluginStatusHint.setLocalText(getString(R.string.care_mode_plugin_hint));
                }
            }
        });
    }

    public void toSave() {

        JSONArray result = new JSONArray();

        for (int i = 0; i < mAllData.length(); i++) {
            try {
                JSONArray temp = mAllData.getJSONArray(i + "");
                for (int j = 0; j < temp.length(); j++) {
                    JSONObject tempJ = temp.getJSONObject(j);
                    result.put(tempJ);
                }

            } catch (JSONException e) {
            }
        }

        showTimeOutLoadinFramgmentWithErrorAlert();
        isSelfOperate = true;
        mPanelDevice.submit(PanelParamsHelper.setCareModePlugins(result.toString()));
    }

    public void toClose() {
        removeSelf();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (null != mPanelDevice) {
            mPanelDevice.unregisterDeviceCallBack(this);
            mPanelDevice = null;
        }
    }

    @Override
    public void onCmdCallBack(String deviceId, String subCategory, String cmd, Map map) {
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(cmd)
                || null == mPanelDevice) {
            return;
        }

        int status = DeviceHelper.getInt(map, PanelDataKey.CmdResult.STATUS, 0);
        int resultType = DeviceHelper.getInt(map, PanelDataKey.CmdResult.RESULT_TYPE, -1);

        if (resultType == 1 && isSelfOperate) {
            if (PanelCmd.SET_CAREMODE_PLUGINS.equals(cmd)) {
                onSetCareModePlugins(status, map);
                isSelfOperate = false;
            }
        }
    }

    /**
     * 设置CareMode的开启状态
     */
    private void onSetCareModePlugins(int status, Map map) {
        DDLog.i(TAG, "onSetCareModePlugins, status: " + status + ", result: " + map);
        closeTimeOutLoadinFramgmentWithErrorAlert();
        if (PanelDataKey.CmdResult.SUCCESS != status) {
            showErrorToast();
            return;
        }

        JSONObject result = new JSONObject();
        for (int i = 0; i < mAllData.length(); i++) {
            try {
                if (headers.get(i).equals(CommonDataUtil.DOOR_WINDOW)) {
                    result.put("door_window", mAllData.get(i + ""));
                } else if (headers.get(i).equals(CommonDataUtil.PIR)) {
                    result.put("pir", mAllData.get(i + ""));
                } else if (headers.get(i).equals(CommonDataUtil.SMART_PLUGIN)) {
                    result.put("smart_plug", mAllData.get(i + ""));
                } else if (headers.get(i).equals(CommonDataUtil.SIGNAL_REPEATER_PLUGIN)) {
                    result.put("signal_repeater_plug", mAllData.get(i + ""));
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        EventBus.getDefault().post(new CareModePluginChange(result.toString()));
        removeSelf();
    }

}
