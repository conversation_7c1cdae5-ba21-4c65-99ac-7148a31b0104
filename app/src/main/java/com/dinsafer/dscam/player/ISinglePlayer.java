package com.dinsafer.dscam.player;

import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module_dscam.player.record.IRecordCallBack;

import java.util.Map;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/6/23 5:04 下午
 */
public interface ISinglePlayer {
    String CMD_TALK = "talk";
    String CMD_LISTEN = "listen";
    String CMD_SNAPSHOT = "snapshot";
    String CMD_HD_MODE = "hdMode";
    String CMD_HD_MODE_ENABLE = "hdModeEnable";
    String CMD_FULLSCREEN = "fullscreen";
    String CMD_FULLSCREEN_ENABLE = "fullscreenEnable";
    String CMD_UPGRADE = "upgrade";
    String CMD_ERROR_CLICK = "errorClick";
    String CMD_RECORD_COMPLETED = "record_completed";

    CameraVideoView getVideoView();

    // 是否支持全屏
    boolean isEnableFullscreen();

    // 是否支持设置Hd
    boolean isEnableHdMode();

    // 获取关联的deviceId
    String getId();

    // 释放资源
    void release();

    // 当前player进入交互模式
    void activePlayer();

    // 当前player退出交互模式，取消当前的操作，如说话或播放声音等
    void deactivatePlayer();

    boolean setIrCut(final boolean irCut);

    boolean setLightState(final int lightState);

    boolean startListen();

    boolean stopListen();

    boolean isListening();

    boolean startTalk();

    boolean stopTalk();

    boolean isTalk();

    boolean setHdMode(final boolean hd);

    boolean isHdMode();

    boolean takeSnapshot();

    boolean isSnapshotting();

    void updateConfig(Map<String, Object> config);

    boolean isRecording();

    boolean isRecordWriting();

    boolean isRecordEnable();

    boolean startRecord(IRecordCallBack callBack);

    void stopRecord(boolean discardResult);
}
