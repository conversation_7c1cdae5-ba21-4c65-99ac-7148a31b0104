package com.dinsafer.dscam.timeline;

import static com.dinsafer.ui.timeruler.TimePartUtil.IPC_PAGE_COUNT;
import static com.dinsafer.ui.timeruler.menu.RecordTimeLineBaseMenuView.ActionMenuConfig.PLAY_SPEED_0_5;
import static com.dinsafer.ui.timeruler.menu.RecordTimeLineBaseMenuView.ActionMenuConfig.PLAY_SPEED_1;
import static com.dinsafer.ui.timeruler.menu.RecordTimeLineBaseMenuView.ActionMenuConfig.PLAY_SPEED_2;
import static com.dinsafer.util.DisplayUtil.VIEW_ALPHA_100;
import static com.dinsafer.util.DisplayUtil.VIEW_ALPHA_40;
import static com.dinsafer.util.DisplayUtil.dip2px;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.ColorStateList;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.HomeManager;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.AppConfig;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityMotionRecordTimelinePlayerBinding;
import com.dinsafer.dscam.DsCamMultiFullPlayActivity;
import com.dinsafer.dscam.DsCamNetWorkSetting;
import com.dinsafer.dscam.upgrade.DsCamUpgradeEvent;
import com.dinsafer.dscam.upgrade.DsCamUpgradeManager;
import com.dinsafer.dscam.DsCamUtils;
import com.dinsafer.dscam.timeline.dialog.RecordSelectDateDialog;
import com.dinsafer.dscam.timeline.dialog.RecordSelectIpcBean;
import com.dinsafer.dscam.timeline.dialog.RecordSelectIpcDialog;
import com.dinsafer.dscam.timeline.dialog.RecordSelectTimeDialog;
import com.dinsafer.model.AlertServicePlanUpdateEvent;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.module.BaseFragmentActivity;
import com.dinsafer.module.iap.CloudStorageServiceHelper;
import com.dinsafer.module.iap.IapRootActivity;
import com.dinsafer.module.iap.ServiceCardItemModel;
import com.dinsafer.module.ipc.common.video.HttpVideoDownLoadTask;
import com.dinsafer.module.ipc.common.video.IDownLoadTask;
import com.dinsafer.module.ipc.common.video.VideoRecordPlayManager;
import com.dinsafer.module.ipc.common.video.global.motion.MotionDownloadAmountUpdateListener;
import com.dinsafer.module.ipc.common.video.global.motion.MotionDownloadEvent;
import com.dinsafer.module.ipc.common.video.global.motion.MotionDownloadListener;
import com.dinsafer.module.ipc.common.video.global.motion.MotionDownloadManager;
import com.dinsafer.module.ipc.common.video.global.motion.MotionVideoDownloadTask;
import com.dinsafer.module.ipc.heartlai.record.DownloadListener;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_home.DinHome;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.bean.LastMotionIpcListResponse;
import com.dinsafer.module_home.bean.MotionEventDatesResponse;
import com.dinsafer.module_home.bean.MotionEventResponse;
import com.dinsafer.module_home.bean.MotionEventVideoResponse;
import com.dinsafer.permission.PermissionDialogUtil;
import com.dinsafer.ui.ActionSheet;
import com.dinsafer.ui.ActionsheetDismiss;
import com.dinsafer.ui.LocalCustomButton;
import com.dinsafer.ui.LocalTextView;
import com.dinsafer.ui.timeruler.IpcTimePartInfo;
import com.dinsafer.ui.timeruler.TimePartEvent;
import com.dinsafer.ui.timeruler.TimePartInfo;
import com.dinsafer.ui.timeruler.TimePartLoadEvent;
import com.dinsafer.ui.timeruler.TimePartUtil;
import com.dinsafer.ui.timeruler.TimeRulerView;
import com.dinsafer.ui.timeruler.menu.MenuSelectDateView;
import com.dinsafer.ui.timeruler.menu.MenuSelectTimeView;
import com.dinsafer.ui.timeruler.menu.OnRecordMenuClickListener;
import com.dinsafer.ui.timeruler.menu.RecordMenuType;
import com.dinsafer.ui.timeruler.menu.RecordTimeLineBaseMenuView;
import com.dinsafer.ui.timeruler.menu.RecordTimeLineVideoState;
import com.dinsafer.ui.timeruler.menu.listener.OnMultiIpcSelectedListener;
import com.dinsafer.util.CalendarUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDDateUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.DensityUtils;
import com.dinsafer.util.Local;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.ScreenUtils;
import com.dinsafer.util.StringStyle;
import com.dinsafer.aop.annotations.SingleClick;
import com.yanzhenjie.permission.AndPermission;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.FileOutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import retrofit2.Call;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * 时间轴录像播放页面
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/8/12 6:02 下午
 */
public class MotionRecordTimelinePlayerActivity extends BaseFragmentActivity implements MotionDownloadAmountUpdateListener {

    private String TAG = "MotionRecordTimelinePlayerActivity";

    private static final int REQ_DOWNLOAD_LIST = 2401;

    private static final long DURATION_LOADING_TIMEOUT = 15 * 1000;

    private static final int DOWNLOAD_MAX_PROGRESS = 100;
    private static final int DOWNLOAD_TARGET_PROGRESS = 80;
    private static final Long DOWNLOAD_TARGET_TIME_MILLIS = 5000L;
    private static final int DOWNLOAD_TARGET_UPDATE_NUM = 100;

    private ActivityMotionRecordTimelinePlayerBinding mBinding;

    private TimePartEvent mPlayingTimePartEvent; // 当前正在播放的移动帧测录像事件
    private String mInitPlayIpcId; // 初始化需要播放的IPC的id
    private String mInitPlayEventId; // 初始化需要播放的事件id
    private long mInitEventTimeMillis; // 初始化需要播放事件所在时间

    private boolean isFullScreen = false;
    private VideoRecordPlayManager videoRecordPlayManager;
    private final List<MotionEventVideoResponse.VideosBean> mVideoList = new ArrayList<>();
    private volatile boolean mNeedSeedToCurrent = false;

    // 对于一个视频，重新播放后需要让播放器开始播放的偏移量，单位ms
    private int seekTime = 0;
    // 当前选择的时间在事件开始时间的偏移量-单位ms
    // 由于视频的时长在视频列表中，如果一次偏移的时长大于目前加载的总时长，需要重新加载视频地址列表才能实现定位需要播放的视频
    // 如总共有30个视频，指针一下子定位到25个视频的位置，则需要重新加载视频列表，否则无法定位到要播放的视频
    private long seekFromEventStartMillis = 0;
    private ThreadPoolExecutor mLoadPool = new ThreadPoolExecutor(0, 4, 1, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10));
    private VideoListDownloadTask mVideoListDownloadTask;

    private boolean isCompleted = false;
    private boolean failed = false;
    private int playCount = 0; // 用于判断是否在开始播放时需要自动暂停-首次播放需要自动暂停
    private final Map<Long, Call<?>> unFinishedRequests = new HashMap<>();

    private final RecordTimeLineBaseMenuView.ActionMenuConfig mMenuConfig = new RecordTimeLineBaseMenuView.ActionMenuConfigBuilder()
            .setMenuEnable(false)
            .setSoundOpened(false)
            .setSpeed(PLAY_SPEED_1)
            .build();

    private final Handler handler = new Handler();
    private final Runnable updateProgressRunnable = new Runnable() {
        @Override
        public void run() {
            if (mBinding.videoView.isPlaying()) {
                // mBinding.resumePause.setVisibility(View.GONE);
                updatePlayProgress();
            }
            handler.postDelayed(updateProgressRunnable, 1000);
        }
    };

    private final List<IpcTimePartInfo> mTimePartList = new ArrayList<>(); // 移动侦测时间列表
    private final List<LastMotionIpcListResponse.IpcsBean> mIpcListSrc = new ArrayList<>(); // ipc列表
    private final List<String> mIpcListSelected = new ArrayList<>(); // ipc列表当前选中的
    private final List<String> mMotionDateList = new ArrayList<>(); // 有移动侦测的日期

    @RecordTimeLineVideoState
    private int videoState = RecordTimeLineVideoState.STATE_EMPTY;
    private int mCurrentHour, mCurrentMin, mCurrentSecond;
    private Calendar mTodayCalendar, mYesterdayCalendar, mSelectCalendar, mMinCalendar;
    private final Date mDate = new Date();
    private final DateFormat mDateFormat = DateFormat.getDateTimeInstance(DateFormat.MEDIUM, DateFormat.MEDIUM, Locale.CHINA);
    private final SimpleDateFormat mRulerDateFormat = new SimpleDateFormat("HH:mm:ss");
    // 是否需要显示上滑的提示内容
    private boolean needShowScrollUpHint = true, showScrollUpHint = false;
    // 处于激活状态的时间轴-横屏时间轴或竖屏时间轴
    private TimeRulerView mActivatedTimeRulerView;

    private final TimeRulerView.OnTimeEventListener mTimeChangedListener = new TimeRulerView.OnTimeEventListener() {
        @Override
        public void onTimeChanged(long newTimeValue) {
            onTimeUpdate(newTimeValue);
        }

        @Override
        public boolean onNeedLoadEvents(final TimePartLoadEvent loadEvent) {
            requestMotionEvent(loadEvent);
            return true;
        }

        @Override
        public void onTimeRangeUpdate(long startTimeSec, long endTimeSec) {
            DDLog.i(TAG, "onTimeRangeUpdate, " + formatDateAndTime(startTimeSec * 1000)
                    + "-" + formatDateAndTime(endTimeSec * 1000));
        }

        @Override
        public void onGoLive(int pageIndex, @NonNull List<String> focusIpcIds, String activatedIpcId) {
            DDLog.i(TAG, "onGoLive, pageIndex: " + pageIndex);
            if (TextUtils.isEmpty(activatedIpcId)) {
                showErrorToast();
                return;
            }

            Device device = IPCManager.getInstance().getDsCamDeviceByID(activatedIpcId);
            if (null == device) {
                showIpcDeletedDialog(activatedIpcId);
                return;
            }

            if (DsCamUtils.isDeviceConnected(device)) {
                if (checkNeedUpgrade(device)) {
                    showUpgradeDialog(device);
                } else {
                    final ArrayList<String> params = new ArrayList<>();
                    params.add(activatedIpcId);
                    DsCamMultiFullPlayActivity.start(MotionRecordTimelinePlayerActivity.this, params);
                }
            } else {
                showDeviceOfflineDialog(activatedIpcId);
            }
        }

        @Override
        public void onInteractAction(int action) {
            if (TimeRulerView.OnTimeEventListener.ACTION_DOWN == action) {
                pausePlay();
            } else if (TimeRulerView.OnTimeEventListener.ACTION_PULL_UP == action) {
                if (needShowScrollUpHint && showScrollUpHint) {
                    needShowScrollUpHint = false;
                    showScrollUpHint = false;
                    DBUtil.Put(DBKey.KEY_TIME_LINE_MOTION_SCROLL_UP, true);
                    mBinding.tvScrollUpHint.setVisibility(View.GONE);
                }
            }
        }

        @Override
        public String onTimeSelected(int pageIndex, long currentSec, List<TimePartEvent> timePartEvents, String activatedDeviceId) {
            DDLog.i(TAG, "onTimeSelected, " + formatDateAndTime(currentSec * 1000) + ", info: " + timePartEvents + ", id: " + activatedDeviceId);
            if (isFinishing() || isDestroyed()) {
                return null;
            }

            String activatedEventId = null;
            TimePartEvent playEvent = null;
            if (null != timePartEvents && timePartEvents.size() > 0 && !TextUtils.isEmpty(activatedDeviceId)) {
                TimePartEvent event;
                for (int i = 0; i < timePartEvents.size(); i++) {
                    event = timePartEvents.get(i);
                    if (activatedDeviceId.equals(event.getDeviceId())) {
                        playEvent = event;
                        activatedEventId = event.getEventId();
                        break;
                    }
                }
            }

            // 1. 当前指针没有指示事件，切换到没有视图的预览界面
            if (null == playEvent) {
                onSelectedEmpty();
                return null;
            }

            // 2. 指针指示到了某个事件
            // 此处是手动拖拽时间轴后的定位，有可能需要从视频中的某个位置开始播放，所以需要偏移
            final long currentSelectTime = mActivatedTimeRulerView.getCurrentTime();
            final long currentOffsetStart = (currentSelectTime - playEvent.getStartTime()) * 1000; // 当前指针相对于事件开始偏移事件，单位ms

            mNeedSeedToCurrent = false;
            seekFromEventStartMillis = 0;
            if (null != mPlayingTimePartEvent && mPlayingTimePartEvent.equals(playEvent)) {
                // 选中的是正在播放的事件，要判断是否能从指针指示的地方开始播放
                boolean loaded = false; // 需要偏移的位置是否已经加载了，如果已经加载，可以直接偏移，如果没有加载，需要重新从头加载
                synchronized (mVideoList) {
                    long videoTotalDurationMillis = 0;
                    if (mVideoList.size() > 0) {
                        for (int i = 0; i < mVideoList.size(); i++) {
                            videoTotalDurationMillis += mVideoList.get(i).getLen();
                            // 视频总时长大于
                            if (videoTotalDurationMillis > currentOffsetStart) {
                                loaded = true;
                                break;
                            }
                        }
                    }
                }
                if (loaded) {
                    // 从头指针指示的地方开始播放
                    seekToCurrent();
                } else {
                    // 指针指示的位置还没有加载，需要加载后再从指定的位置开始播放
                    stopPlay();
                    mPlayingTimePartEvent = playEvent;
                    mBinding.videoMenuView.setPreviewImage(null);
                    seekFromEventStartMillis = currentOffsetStart;
                    startPlay(true);
                }
            } else {
                // 选中的是新的事件，从选中事件指定位置开始播放
                stopPlay();
                mPlayingTimePartEvent = playEvent;
                mBinding.videoMenuView.setPreviewImage(null);
                seekFromEventStartMillis = currentOffsetStart;
                startPlay(true);

                initLastDownloadStatus();
            }

            // 更新下载菜单可用状态
            mMenuConfig.setDownloadEnable(true);
            onActionMenuUpdate();

            return activatedEventId;
        }

        @Override
        public void onPreviousNextEventState(boolean havePrevious, boolean haveNext) {
            if (isFullScreen) {
                mBinding.videoMenuView.updateFullscreenNextPreEventEnable(havePrevious, haveNext);
            } else {
                updateNextPreEventEnable(havePrevious, haveNext);
            }
        }

        @Override
        public void onActivatedIpcChanged(@Nullable IpcTimePartInfo lastFocusedIpc, @Nullable IpcTimePartInfo currentFocusedIpc) {
            String selectedIpcName = "";
            if (null != currentFocusedIpc) {
                if (!TextUtils.isEmpty(currentFocusedIpc.getDeviceName())) {
                    selectedIpcName = currentFocusedIpc.getDeviceName();
                } else if (!TextUtils.isEmpty(currentFocusedIpc.getDeviceId())) {
                    selectedIpcName = currentFocusedIpc.getDeviceId();
                }
            }
            mBinding.videoMenuView.setFocusedIpcName(selectedIpcName);
        }
    };

    private final TimeRulerView.OnTimePartClickListener mTimePartClickListener = new TimeRulerView.OnTimePartClickListener() {
        @Override
        public void onTimePartClick(TimePartEvent timePartEvent) {
            mMenuConfig.setMenuEnable(true);
            mMenuConfig.setDownloadEnable(true);
            mMenuConfig.setSoundOpened(false);
            setPlayerMenuEnable(false);

            // 此处是点击事件后定位，指针必定指在事件的开始，所以只需要从事件的第一个视频起始位置开始播放即可
            mNeedSeedToCurrent = false;
            seekFromEventStartMillis = 0;
            if (null != mPlayingTimePartEvent && mPlayingTimePartEvent.equals(timePartEvent)) {
                // 从头开始播放
                setVideoState(RecordTimeLineVideoState.STATE_LOADING);
                isCompleted = false;
                videoRecordPlayManager.seekTo(0);
            } else {
                // 重新开始播放
                stopPlay();
                mPlayingTimePartEvent = timePartEvent;
                mBinding.videoMenuView.setPreviewImage(null);
                startPlay(false);

                initLastDownloadStatus();
            }
        }

        @Override
        public void onTimePartLongPress(TimePartEvent timePartEvent) {
            pausePlay();
            showDeleteTimePartMenu(timePartEvent);
        }
    };

    private final TimeRulerView.OnPageChangedListener mPageChangedListener = (currentIndex, totalIndex) -> {
        if (needShowScrollUpHint && totalIndex >= 1) {
            mBinding.tvScrollUpHint.setVisibility(View.VISIBLE);
            showScrollUpHint = true;
        }
        mBinding.pageIndicator.updateIndicatorSelected(currentIndex, totalIndex + 1);
        mBinding.videoMenuView.updateIndicatorSelected(currentIndex, totalIndex + 1);
    };

    private final MenuSelectTimeView.OnTimePickListener mTimePickListener = (hour, min, second) -> {
        pausePlay();
        setPlayerMenuEnable(false);

        mSelectCalendar.set(Calendar.HOUR_OF_DAY, hour);
        mSelectCalendar.set(Calendar.MINUTE, min);
        mSelectCalendar.set(Calendar.SECOND, second);
        mSelectCalendar.set(Calendar.MILLISECOND, 0);
        final long selectedSec = mSelectCalendar.getTimeInMillis() / 1000;
        mActivatedTimeRulerView.setInitTime(selectedSec, true, false, false, true);
        onTimeUpdate(selectedSec);
    };

    private final OnMultiIpcSelectedListener mOnIpcSelectedListener = (selectedIpcList, unselectedIpcList) -> {
        DDLog.i(TAG, "SelectIpc: " + selectedIpcList);
        if (mIpcListSelected.equals(selectedIpcList)) {
            DDLog.d(TAG, "IPC列表没有更新");
            return;
        }

        onSelectedEmpty();

        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        DsCamUtils.saveTimeLineMotionUnselectedIds(homeId, unselectedIpcList);

        String ipcId;
        final List<IpcTimePartInfo> newTimePartList = new ArrayList<>();
        long initTimeSec = -1;
        for (LastMotionIpcListResponse.IpcsBean ipcBean : mIpcListSrc) {
            ipcId = ipcBean.getIpc_id();
            if (selectedIpcList.size() > 0 && selectedIpcList.contains(ipcId)) {
                if (-1 == initTimeSec) {
                    initTimeSec = ipcBean.getTime();
                }

                final IpcTimePartInfo timePartInfo = new IpcTimePartInfo(ipcId, ipcBean.getName());
                newTimePartList.add(timePartInfo);
            }
        }
        mTimePartList.clear();
        mTimePartList.addAll(newTimePartList);
        mIpcListSelected.clear();
        mIpcListSelected.addAll(selectedIpcList);

        if (-1 == initTimeSec) {
            initTimeSec = System.currentTimeMillis() / 1000;
        }
        mActivatedTimeRulerView.setTimePartList(mTimePartList);
        if (mTimePartList.size() == 0) {
            // 没有事件，定位到最新时间
            mActivatedTimeRulerView.setInitTime(initTimeSec, true, false, true);
        } else {
            // 自动定位到最新的一个事件
            mActivatedTimeRulerView.setInitTime(initTimeSec, true, false, true, true);
        }
        onTimeUpdate(initTimeSec);
    };

    private final MenuSelectDateView.OnDateSelectedListener mOnDateSelectedListener = (year, month, day) -> {
        DDLog.i(TAG, "OnDateClick: " + year + "." + month + "." + day);
        final int selectYear = mSelectCalendar.get(Calendar.YEAR);
        final int selectMonth = mSelectCalendar.get(Calendar.MONTH) + 1;
        final int selectDay = mSelectCalendar.get(Calendar.DAY_OF_MONTH);
        // 没有切换选中的日期，不处理
        if (selectYear == year && selectMonth == month && selectDay == day) {
            return;
        }

        mActivatedTimeRulerView.setScaleTimeMin();

        onSelectedEmpty();

        mSelectCalendar.set(year, month - 1, day);
        // mSelectCalendar.set(Calendar.HOUR_OF_DAY, 0);
        // mSelectCalendar.set(Calendar.MINUTE, 0);
        // mSelectCalendar.set(Calendar.SECOND, 0);
        mSelectCalendar.set(Calendar.MILLISECOND, 0);
        if (mSelectCalendar.after(mTodayCalendar)) {
            mSelectCalendar.setTimeInMillis(mTodayCalendar.getTimeInMillis());
        }
        final long selectedSec = mSelectCalendar.getTimeInMillis() / 1000;
        mActivatedTimeRulerView.setInitTime(selectedSec, true, false, false, true);
        onTimeUpdate(selectedSec);
    };

    private final OnRecordMenuClickListener mActionMenuListener = (menuType, param) -> {
        boolean operated = true;
        switch (menuType) {
            case RecordMenuType.TYPE_ACTION_FULLSCREEN:
                makeVideoFullScreen(false);
                break;
            case RecordMenuType.TYPE_FULLSCREEN_EXIT_FULLSCREEN:
                exitVideoFullScreen(false);
                break;
            case RecordMenuType.TYPE_FULLSCREEN_DOWNLOAD:
            case RecordMenuType.TYPE_ACTION_DOWNLOAD:
                if (PermissionUtil.isStoragePermissionDeny(this)) {
                    requestReadVideoPermission();
                    break;
                }
                download();
                break;
            case RecordMenuType.TYPE_ACTION_SOUND:
            case RecordMenuType.TYPE_FULLSCREEN_SOUND:
                final boolean soundOpened = !mMenuConfig.isSoundOpened();
                mMenuConfig.setSoundOpened(soundOpened);
                onActionMenuUpdate();

                mBinding.videoView.setMute(!soundOpened);
                mBinding.actionMenuView.setMute(!soundOpened);
                break;
            case RecordMenuType.TYPE_ACTION_CHANGE_SPEED:
            case RecordMenuType.TYPE_FULLSCREEN_SPEED:
                final float currentPlaySpeed = mMenuConfig.getSpeed();
                final float nextPlaySpeed;
                if (PLAY_SPEED_2 == currentPlaySpeed) {
                    nextPlaySpeed = PLAY_SPEED_0_5;
                } else if (PLAY_SPEED_0_5 == currentPlaySpeed) {
                    nextPlaySpeed = PLAY_SPEED_1;
                } else {
                    nextPlaySpeed = PLAY_SPEED_2;
                }
                mMenuConfig.setSpeed(nextPlaySpeed);
                onActionMenuUpdate();
                mBinding.videoView.setPlaySpeed(nextPlaySpeed);
                mBinding.actionMenuView.setSpeed(nextPlaySpeed);
                break;
            case RecordMenuType.TYPE_ACTION_SNAPSHOT:
            case RecordMenuType.TYPE_FULLSCREEN_SNAPSHOT:
                // 检查权限
                if (PermissionUtil.isStoragePermissionDeny(this)) {
                    requestReadImagesPermission();
                    return false;
                }
                // 截图
                final Bitmap snapshot = mBinding.videoView.getSnapshot();
                mBinding.videoMenuView.animaSnapshot(snapshot);
                final boolean success = saveSnapshot(snapshot);
                if (success) {
                    showTopToast(getString(R.string.dscam_save_to_album));
                } else {
                    showTopToast(getString(R.string.failed_to_save_photo));
                }
                break;
            case RecordMenuType.TYPE_FULLSCREEN_SELECT_DATE:
                showFullscreenSelectDateMenu();
                break;
            case RecordMenuType.TYPE_FULLSCREEN_SELECT_TIME:
                showFullscreenSelectTimeMenu();
                break;
            case RecordMenuType.TYPE_FULLSCREEN_SELECT_IPC:
                showFullscreenSelectIpcMenu();
                break;
            case RecordMenuType.TYPE_FULLSCREEN_FOCUS_NEXT_EVENT:
                TimePartEvent activatedNextEvent = navigateNextOrPrevious(true, true);
                break;
            case RecordMenuType.TYPE_FULLSCREEN_FOCUS_PREVIOUS_EVENT:
                TimePartEvent activatedPreEvent = navigateNextOrPrevious(false, true);
                break;
            case RecordMenuType.TYPE_COMMON_PLAY:
            case RecordMenuType.TYPE_COMMON_REFRESH:
                if (RecordTimeLineVideoState.STATE_EMPTY == videoState) {
                    break;
                }
                clickPlay();
                break;
            default:
                break;
        }
        return operated;
    };

    private final CloudStorageServiceHelper.OnListProductSchedulesListener listProductSchedulesListener = new CloudStorageServiceHelper.OnListProductSchedulesListener() {
        @Override
        public void onUpdate(List<ServiceCardItemModel> data) {
            initAlertServiceInfo();
        }
    };

    private void setPlayerMenuEnable(final boolean enable) {
        if (enable) {
            mMenuConfig.setSoundEnable(true);
            mMenuConfig.setSnapshotEnable(true);
            mMenuConfig.setChangeSpeedEnable(true);
        } else {
            mMenuConfig.setSoundEnable(false);
            mMenuConfig.setSnapshotEnable(false);
            mMenuConfig.setChangeSpeedEnable(false);
        }
        onActionMenuUpdate();
    }

    /**
     * 更新操作菜单
     */
    private void onActionMenuUpdate() {
        mBinding.actionMenuView.updateConfig(mMenuConfig);
        mBinding.videoMenuView.updateConfig(mMenuConfig);
    }

    /**
     * 保存截图到手机
     *
     * @param snapshot 截图
     */
    private boolean saveSnapshot(final Bitmap snapshot) {
        if (null == snapshot) {
            return false;
        }

        File file = new File(DDGlobalEnv.getInstance().getImageFolder());
        if (!file.exists()) {
            file.mkdirs();
        }

        String strDate = new SimpleDateFormat("yyyy.MM.dd_HH.mm.ss").format(new Date());
        //fileName为文件绝对路径+文件名
        String fileName = DDGlobalEnv.getInstance().getImageFolder() + mPlayingTimePartEvent.getEventId() + "_" + strDate + ".jpg";
        File snapshotFile = new File(fileName);
        if (snapshotFile.exists()) {
            snapshotFile.delete();
        }
        try {
            snapshotFile.createNewFile();
            FileOutputStream fos = new FileOutputStream(snapshotFile);
            snapshot.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            fos.flush();
            fos.close();
            DDSystemUtil.updatePhoto(this, snapshotFile);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public static void start(Context context) {
        start(context, null, null, 0);
    }

    /**
     * @param ipcId   加载完成后需要播放哪个个IPC的移动帧测的事件
     * @param eventId 加载完成后需要播放哪个移动帧测事件
     */
    public static void start(Context context, @Nullable String ipcId, @Nullable String eventId, final long eventStartTimeMillis) {
        Intent starter = new Intent(context, MotionRecordTimelinePlayerActivity.class);
        starter.putExtra("eventId", eventId);
        starter.putExtra("ipcId", ipcId);
        starter.putExtra("eventTime", eventStartTimeMillis);
        context.startActivity(starter);
    }

    @Override
    protected boolean initVariables() {
        mInitPlayEventId = getIntent().getStringExtra("eventId");
        mInitPlayIpcId = getIntent().getStringExtra("ipcId");
        mInitEventTimeMillis = getIntent().getLongExtra("eventTime", 0);
        return true;
    }

    @Override
    public void onBackPressed() {
        try {
            if (!ActionSheet.mDismissed) {
                EventBus.getDefault().post(new ActionsheetDismiss());
            } else if (mBinding.loadingView.isShowLoading()) {
                // 正在loading，不能退出
            } else {
                toClose();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void toClose() {
        finish();
    }

    private void initTimeline() {
        final Date today = new Date();
        mTodayCalendar = Calendar.getInstance();
        mTodayCalendar.setTime(today);
        mMinCalendar = (Calendar) mTodayCalendar.clone();
        mMinCalendar.add(Calendar.DAY_OF_MONTH, -TimePartUtil.TIME_OLDEST_DAY);
        mMinCalendar.set(Calendar.HOUR_OF_DAY, 0);
        mMinCalendar.set(Calendar.MINUTE, 0);
        mMinCalendar.set(Calendar.SECOND, 0);
        mMinCalendar.set(Calendar.MILLISECOND, 0);
        mYesterdayCalendar = Calendar.getInstance();
        mYesterdayCalendar.setTime(today);
        mYesterdayCalendar.add(Calendar.DAY_OF_MONTH, -1);
        mSelectCalendar = Calendar.getInstance();
        mSelectCalendar.setTime(today);
        onTimeUpdate(today.getTime() / 1000);

        mBinding.ivNextDay.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                mActivatedTimeRulerView.setScaleTimeMin();

                onSelectedEmpty();

                mSelectCalendar.add(Calendar.DAY_OF_MONTH, 1);
                // mSelectCalendar.set(Calendar.HOUR_OF_DAY, 0);
                // mSelectCalendar.set(Calendar.MINUTE, 0);
                // mSelectCalendar.set(Calendar.SECOND, 0);
                mSelectCalendar.set(Calendar.MILLISECOND, 0);
                if (mSelectCalendar.after(mTodayCalendar)) {
                    mSelectCalendar.setTimeInMillis(mTodayCalendar.getTimeInMillis());
                }
                final long selectedSec = mSelectCalendar.getTimeInMillis() / 1000;
                mActivatedTimeRulerView.setInitTime(selectedSec, true, false, false, true);
                onTimeUpdate(selectedSec);
            }
        });
        mBinding.ivPreviousDay.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                mActivatedTimeRulerView.setScaleTimeMin();

                onSelectedEmpty();

                mSelectCalendar.add(Calendar.DAY_OF_MONTH, -1);
                // mSelectCalendar.set(Calendar.HOUR_OF_DAY, 0);
                // mSelectCalendar.set(Calendar.MINUTE, 0);
                // mSelectCalendar.set(Calendar.SECOND, 0);
                mSelectCalendar.set(Calendar.MILLISECOND, 0);
                if (mSelectCalendar.after(mTodayCalendar)) {
                    mSelectCalendar.setTimeInMillis(mTodayCalendar.getTimeInMillis());
                }
                final long selectedSec = mSelectCalendar.getTimeInMillis() / 1000;
                mActivatedTimeRulerView.setInitTime(selectedSec, true, false, false, true);
                onTimeUpdate(selectedSec);
            }
        });
        mBinding.tvDate.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                new RecordSelectDateDialog.Builder()
                        .minYear(mMinCalendar.get(Calendar.YEAR))
                        .minMonth(mMinCalendar.get(Calendar.MONTH) + 1)
                        .minDay(mMinCalendar.get(Calendar.DAY_OF_MONTH))
                        .defaultYear(mSelectCalendar.get(Calendar.YEAR))
                        .defaultMonth(mSelectCalendar.get(Calendar.MONTH) + 1)
                        .defaultDay(mSelectCalendar.get(Calendar.DAY_OF_MONTH))
                        .events(mMotionDateList)
                        .onDateSelectedListener(mOnDateSelectedListener)
                        .build()
                        .show(getSupportFragmentManager(), RecordSelectDateDialog.TAG);
            }
        });
        mBinding.videoMenuView.setFullscreenOnDateSelectedListener(mOnDateSelectedListener);
        mBinding.tvTime.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                new RecordSelectTimeDialog.Builder()
                        .hourIndex(mCurrentHour)
                        .minIndex(mCurrentMin)
                        .secondIndex(mCurrentSecond)
                        .timePickListener(mTimePickListener)
                        .build()
                        .show(getSupportFragmentManager(), RecordSelectTimeDialog.TAG);
            }
        });
        mBinding.videoMenuView.setFullscreenTimePickListener(mTimePickListener);
        mBinding.ivFilter.setImageResource(R.drawable.icon_nav_filter);
        mBinding.ivFilter.setImageTintList(ColorStateList.valueOf(getResources().getColor(R.color.color_white_01)));
        mBinding.ivFilter.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                new RecordSelectIpcDialog.Builder()
                        .onIpcSelectedListener(mOnIpcSelectedListener)
                        .ipcList(mIpcListSrc, mIpcListSelected)
                        .build()
                        .show(getSupportFragmentManager(), RecordSelectIpcDialog.TAG);
            }
        });
        mBinding.videoMenuView.setOnIpcSelectedListener(mOnIpcSelectedListener);
        mBinding.ivPreviousTime.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                TimePartEvent activatedEvent = navigateNextOrPrevious(false, true);
            }
        });
        mBinding.ivNextTime.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                TimePartEvent activatedEvent = navigateNextOrPrevious(true, true);
            }
        });

        // 视频时间轴
        mBinding.trv.setOnTimePartClickListener(mTimePartClickListener);
        mBinding.trv.setOnTimeChangedListener(mTimeChangedListener);
        mBinding.trv.setOnPageChangedListener(mPageChangedListener);
        mBinding.trv.setTimePartList(mTimePartList);
        // 全屏播放时间轴菜单
        mBinding.videoMenuView.getTimeRulerView().setOnTimePartClickListener(mTimePartClickListener);
        mBinding.videoMenuView.getTimeRulerView().setOnTimeChangedListener(mTimeChangedListener);
        mBinding.videoMenuView.getTimeRulerView().setOnPageChangedListener(mPageChangedListener);
        mBinding.videoMenuView.getTimeRulerView().setTimePartList(mTimePartList);

        final long endTime = System.currentTimeMillis() / 1000;
        final long startTime = endTime - 30 * 24 * 3600;

        requestLastMotionIpcList(startTime, endTime);
        requestRecordDate();
    }

    /**
     * 定位到上一个或下一个事件
     *
     * @param navigateNext true: 定位到下一个事件； false: 定位到上一个事件
     */
    private TimePartEvent navigateNextOrPrevious(final boolean navigateNext, final boolean shouldNotify) {
        final String eventId;
        final long startTime;
        if (null != mPlayingTimePartEvent) {
            eventId = mPlayingTimePartEvent.getEventId();
            startTime = mPlayingTimePartEvent.getStartTime();
        } else {
            eventId = null;
            startTime = mBinding.trv.getCurrentTime();
        }

        if (navigateNext) {
            return mActivatedTimeRulerView.setFocusNextEvent(eventId, startTime, shouldNotify);
        } else {
            return mActivatedTimeRulerView.setFocusPreviousEvent(eventId, startTime, shouldNotify);
        }
    }

    /**
     * 获取最近有移动侦测的IPC列表
     *
     * @param startTimeSec 开始时间，单位s
     * @param endTimeSec   结束时间，单位s
     */
    private void requestLastMotionIpcList(final long startTimeSec, final long endTimeSec) {
        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        DinHome.getInstance().getLastMotionIpcList(homeId, startTimeSec, endTimeSec, new IDefaultCallBack2<LastMotionIpcListResponse>() {
            @Override
            public void onSuccess(LastMotionIpcListResponse lastMotionIpcListResponse) {
                mBinding.loadingView.hideLoading();
                List<LastMotionIpcListResponse.IpcsBean> result = lastMotionIpcListResponse.getResult().getIpcs();
                if (null == result || 0 == result.size()) {
                    return;
                }
                final MotionIpcBeanComparator comparator = new MotionIpcBeanComparator();
                Collections.sort(result, comparator);

                mIpcListSrc.clear();
                mIpcListSelected.clear();

                String ipcId;
                mTimePartList.clear();
                final ArrayList<String> unSelectedIds = DsCamUtils.getTimeLineMotionUnselectedIds(homeId);
                long initTimeSec = -1;
                for (LastMotionIpcListResponse.IpcsBean ipcBean : result) {
                    ipcId = ipcBean.getIpc_id();
                    if (unSelectedIds.size() > 0 && unSelectedIds.contains(ipcId)) {
                        mIpcListSrc.add(ipcBean);
                        continue;
                    }

                    final IpcTimePartInfo timePartInfo = new IpcTimePartInfo(ipcId, ipcBean.getName());
                    if (isNeedInitPlay() && mInitPlayIpcId.equals(ipcId)) {
                        mTimePartList.add(0, timePartInfo);
                        mIpcListSelected.add(0, ipcId);
                        mIpcListSrc.add(0, ipcBean);
                        initTimeSec = mInitEventTimeMillis / 1000;
                    } else {
                        if (-1 == initTimeSec) {
                            initTimeSec = ipcBean.getTime();
                        }
                        mTimePartList.add(timePartInfo);
                        mIpcListSelected.add(ipcId);
                        mIpcListSrc.add(ipcBean);
                    }
                }
                if (-1 == initTimeSec) {
                    initTimeSec = System.currentTimeMillis() / 1000;
                }
                mActivatedTimeRulerView.setTimePartList(mTimePartList);
                if (isNeedInitPlay()) {
                    // 等加载完成后播放指定的事件
                    mActivatedTimeRulerView.setInitTime(initTimeSec, true, false, true);
                } else {
                    // 自动定位到最新的一个事件
                    mActivatedTimeRulerView.setInitTime(initTimeSec, true, false, true, true);
                }
                onTimeUpdate(initTimeSec);
            }

            @Override
            public void onError(int i, String s) {
                DDLog.e(TAG, "i: " + i + ", s: " + s);
            }
        });
    }

    /**
     * 是否需要初始化完成后就自动播放录像
     *
     * @return true:加载完成后自动播放
     */
    private boolean isNeedInitPlay() {
        final long minTimeMillis = mMinCalendar.getTimeInMillis();
        final long maxTimeMillis = mTodayCalendar.getTimeInMillis();
        DDLog.i(TAG, "isNeedInitPlay, " + "ipcId: " + mInitPlayIpcId + ", eventId: " + mInitPlayIpcId + ", time: " + formatDateAndTime(mInitEventTimeMillis));
        return !TextUtils.isEmpty(mInitPlayIpcId)
                && !TextUtils.isEmpty(mInitPlayEventId)
                && mInitEventTimeMillis > minTimeMillis
                && mInitEventTimeMillis < maxTimeMillis;
    }

    /**
     * 清除初始化的播放信息
     */
    private void cleanInitPlay() {
        mInitPlayIpcId = null;
        mInitPlayEventId = null;
        mInitEventTimeMillis = 0;
    }

    /**
     * 获取正在播放的事件ID
     *
     * @return 正在播放的事件ID
     */
    @Nullable
    private String getPlayingEventId() {
        return null == mPlayingTimePartEvent ? null : mPlayingTimePartEvent.getEventId();
    }

    /**
     * 获取正在播放的IPC ID
     *
     * @return 正在播放的ipc ID
     */
    @Nullable
    private String getPlayingIpcId() {
        return null == mPlayingTimePartEvent ? null : mPlayingTimePartEvent.getDeviceId();
    }

    /**
     * 获取有移动侦测日期
     */
    private void requestRecordDate() {
        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        final String zoneCode = DDDateUtil.getDefaultTimeZoneCode();
        DinSDK.getHomeInstance().getMotionEventDates(homeId, null == zoneCode ? "" : zoneCode,
                new IDefaultCallBack2<MotionEventDatesResponse>() {
                    @Override
                    public void onSuccess(MotionEventDatesResponse motionEventDatesResponse) {
                        if (null == motionEventDatesResponse) {
                            return;
                        }
                        final List<String> result = motionEventDatesResponse.getResult().getDates();
                        mMotionDateList.clear();
                        if (null != result) {
                            mMotionDateList.addAll(result);
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        DDLog.e(TAG, "i: " + i + ", s: " + s);
                    }
                });
    }

    // 刷新显示的日期
    private void updateDisplayDate() {
        updateNextPreDateEnable();

        final String dateStr;
        if (CalendarUtil.isSameDate(mTodayCalendar, mSelectCalendar)) {
            dateStr = Local.s(getString(R.string.today));
        } else if (CalendarUtil.isSameDate(mYesterdayCalendar, mSelectCalendar)) {
            dateStr = Local.s(getString(R.string.yesterday));
        } else {
            final int selectYear = mSelectCalendar.get(Calendar.YEAR);
            final int selectMonthIndex = mSelectCalendar.get(Calendar.MONTH);
            final int selectDay = mSelectCalendar.get(Calendar.DAY_OF_MONTH);
            final int displayMonth = selectMonthIndex + 1;
            dateStr = CalendarUtil.formatYYYYMMDD(selectYear, displayMonth, selectDay);
        }

        if (isFullScreen) {
            setFullScreenDisplayDate(dateStr);
        } else {
            mBinding.tvDate.setText(dateStr);
        }
    }

    /**
     * 更新上一个下一个事件按钮的可用状态
     *
     * @param previousEnable 是否可以定位到上一个事件
     * @param nextEnable     是否可以定位到下一个事件
     */
    private void updateNextPreEventEnable(final boolean previousEnable, final boolean nextEnable) {
        if (previousEnable) {
            mBinding.ivPreviousTime.setEnabled(true);
            mBinding.ivPreviousTime.setAlpha(VIEW_ALPHA_100);
        } else {
            mBinding.ivPreviousTime.setEnabled(false);
            mBinding.ivPreviousTime.setAlpha(VIEW_ALPHA_40);
        }

        if (nextEnable) {
            mBinding.ivNextTime.setEnabled(true);
            mBinding.ivNextTime.setAlpha(VIEW_ALPHA_100);
        } else {
            mBinding.ivNextTime.setEnabled(false);
            mBinding.ivNextTime.setAlpha(VIEW_ALPHA_40);
        }
    }

    // 更新上一天和下一天的可用状态
    private void updateNextPreDateEnable() {
        final boolean nextDayEnable = CalendarUtil.isDateBefore(mSelectCalendar, mTodayCalendar, false);
        if (nextDayEnable) {
            mBinding.ivNextDay.setEnabled(true);
            mBinding.ivNextDay.setAlpha(VIEW_ALPHA_100);
        } else {
            mBinding.ivNextDay.setEnabled(false);
            mBinding.ivNextDay.setAlpha(VIEW_ALPHA_40);
        }

        final boolean previousDayEnable = CalendarUtil.isDateAfter(mSelectCalendar, mMinCalendar, false);
        if (previousDayEnable) {
            mBinding.ivPreviousDay.setEnabled(true);
            mBinding.ivPreviousDay.setAlpha(VIEW_ALPHA_100);
        } else {
            mBinding.ivPreviousDay.setEnabled(false);
            mBinding.ivPreviousDay.setAlpha(VIEW_ALPHA_40);
        }
    }

    /**
     * 刷新时间轴顶部的时间
     *
     * @param timeInSecond 0~23*3600+59*60+59
     */
    private void onTimeUpdate(final long timeInSecond) {
        final long timeMillis = timeInSecond * 1000;
        mSelectCalendar.setTimeInMillis(timeMillis);
        mCurrentHour = mSelectCalendar.get(Calendar.HOUR_OF_DAY);
        mCurrentMin = mSelectCalendar.get(Calendar.MINUTE);
        mCurrentSecond = mSelectCalendar.get(Calendar.SECOND);

        final String timeStr = formatTimeHHmm(timeMillis);
        if (isFullScreen) {
            setFullScreenDisplayTime(timeStr);
        } else {
            mBinding.tvTime.setText(timeStr);
        }

        updateDisplayDate();
    }

    private void setFullScreenDisplayTime(String timeStr) {
        mBinding.videoMenuView.setFullScreenDisplayTime(timeStr);
    }

    private void setFullScreenDisplayDate(String dateStr) {
        mBinding.videoMenuView.setFullScreenDisplayDate(dateStr);
    }

    private String formatDateAndTime(final long timeMillis) {
        mDate.setTime(timeMillis);
        return mDateFormat.format(mDate);
    }

    private String formatTimeHHmm(long timeMillis) {
        mDate.setTime(timeMillis);
        return mRulerDateFormat.format(mDate);
    }


    /**
     * 获取某个时间段内的移动帧测事件
     */
    private void requestMotionEvent(final TimePartLoadEvent loadEvent) {
        DDLog.i(TAG, "requestMotionEvent, " + loadEvent.toString() + ", TimeRange: "
                + formatDateAndTime(loadEvent.getStartTime() * 1000)
                + "-" + formatDateAndTime(loadEvent.getEndTime() * 1000));
        final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
        synchronized (unFinishedRequests) {
            final Long timeStamp = System.currentTimeMillis();
            final Call<?> call = DinHome.getInstance().getMotionEvents(homeId, loadEvent.getDeviceIds(), loadEvent.getStartTime(), loadEvent.getEndTime(),
                    new IDefaultCallBack2<MotionEventResponse>() {
                        @Override
                        public void onSuccess(MotionEventResponse motionEventResponse) {
                            synchronized (unFinishedRequests) {
                                unFinishedRequests.remove(timeStamp);
                            }
                            if (isFinishing() || isDestroyed()) {
                                return;
                            }
                            if (1 != motionEventResponse.getStatus() || null == motionEventResponse.getResult()) {
                                mActivatedTimeRulerView.onLoadFinished(loadEvent, false);
                                return;
                            }
                            onLoadedMotionEvent(loadEvent, motionEventResponse.getResult().getEvents());
                        }

                        @Override
                        public void onError(int i, String s) {
                            synchronized (unFinishedRequests) {
                                unFinishedRequests.remove(timeStamp);
                            }
                            if (isFinishing() || isDestroyed()) {
                                return;
                            }
                            DDLog.e(TAG, "requestMotionEvent error, i: " + i + ", s: " + s + loadEvent);
                            mActivatedTimeRulerView.onLoadFinished(loadEvent, false);
                        }
                    });
            if (null != call) {
                unFinishedRequests.put(timeStamp, call);
            }
        }
    }

    /**
     * 处理加载到的移动帧测数据
     *
     * @param loadEvent 请求参数
     * @param result    获取到的结果
     */
    private void onLoadedMotionEvent(final TimePartLoadEvent loadEvent, final @NonNull List<MotionEventResponse.EventsBean> result) {
        if (result.size() == 0) {
            mActivatedTimeRulerView.onLoadFinished(loadEvent, true);
            return;
        }

        final List<IpcTimePartInfo> localList = new ArrayList<>();
        final int startIndex = loadEvent.getPageIndex() * IPC_PAGE_COUNT;
        final int remainCount = mTimePartList.size() - startIndex;
        final int maxCount = Math.min(remainCount, IPC_PAGE_COUNT);
        for (int i = 0; i < maxCount; i++) {
            localList.add(mTimePartList.get(startIndex + i));
        }

        boolean hadInitPlayEventId = false; // 加载的数据有没有需要初始化完成就需要播放的事件
        if (localList.size() > 0) {
            for (MotionEventResponse.EventsBean bean : result) {
                final String ipcId = bean.getIpc_id();
                if (TextUtils.isEmpty(ipcId) || TextUtils.isEmpty(bean.getEvent_id())) {
                    continue;
                }

                if (!hadInitPlayEventId && isNeedInitPlay() && bean.getEvent_id().equals(mInitPlayEventId)) {
                    hadInitPlayEventId = true;
                }

                for (int i = 0; i < localList.size(); i++) {
                    final IpcTimePartInfo ipcTimePartInfo = localList.get(i);
                    final String localId = ipcTimePartInfo.getDeviceId();
                    if (ipcId.equals(localId)) {
                        TimePartInfo timePartInfo = new TimePartInfo(bean.getEvent_id(), bean.getEvent_start_time(),
                                bean.getEvent_start_time() + bean.getLen() / 1000, "");
                        ipcTimePartInfo.addTimePart(timePartInfo.getEventKey(), timePartInfo);
                        break;
                    }
                }
            }
        }

        mActivatedTimeRulerView.onLoadFinished(loadEvent, true);

        if (isNeedInitPlay() && hadInitPlayEventId) {
            final boolean focused = mActivatedTimeRulerView.setFocusEvent(mInitPlayIpcId, mInitPlayEventId, true);
            if (focused) {
                cleanInitPlay();
            }
        }
    }

    private void showDeleteTimePartMenu(final TimePartEvent timePartEvent) {
        ActionSheet.createBuilder(getApplicationContext(), getSupportFragmentManager())
                .setTitle(false)
                .setCancelButtonTitle(Local.s(getResources().getString(R.string.cancel)))
                .setLastButtonTextColor(getResources().getColor(R.color.color_minor_1))
                .setOtherButtonTitles(Local.s(getString(R.string.delete)))
                .setCancelableOnTouchOutside(true)
                .setListener(new ActionSheet.ActionSheetListener() {
                    @Override
                    public void onDismiss(ActionSheet actionSheet, boolean isCancel) {
                        mActivatedTimeRulerView.cancelLongPressTimePart(timePartEvent.getEventId());
                    }

                    @Override
                    public void onOtherButtonClick(ActionSheet actionSheet, int index) {
                        if (index == 0) {
                            requestDeleteEvent(timePartEvent);
                        }
                    }
                }).show();
    }

    /**
     * 请求删除移动帧测事件
     *
     * @param timePartEvent 需要删除的事件
     */
    private void requestDeleteEvent(final TimePartEvent timePartEvent) {
        if (null == timePartEvent || TextUtils.isEmpty(timePartEvent.getEventId())) {
            showErrorToast();
            return;
        }

        mBinding.loadingView.showLoading(DURATION_LOADING_TIMEOUT, this::showErrorToast);
        DinSDK.getHomeInstance().deleteMotionDetectionRecord(HomeManager.getInstance().getCurrentHome().getHomeID(),
                Collections.singletonList(timePartEvent.getEventId()),
                new IDefaultCallBack() {
                    @Override
                    public void onSuccess() {
                        mBinding.loadingView.hideLoading();
                        if (null != mPlayingTimePartEvent && timePartEvent.getEventId().equals(mPlayingTimePartEvent.getEventId())) {
                            onSelectedEmpty();
                        }

                        mActivatedTimeRulerView.setDeleteTimePartResult(timePartEvent, true);
                    }

                    @Override
                    public void onError(int i, String s) {
                        DDLog.w(TAG, "deleteMotionDetectionRecord onError, i: " + i + ", s: " + s);
                        mBinding.loadingView.hideLoading();
                        mActivatedTimeRulerView.setDeleteTimePartResult(timePartEvent, false);
                    }
                });
    }

    @Override
    protected void initViews(Bundle savedInstanceState) {
        setTheme(R.style.ActionSheetStyleiOS7);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_motion_record_timeline_player);
        changeActivatedTimeRulerView(false);
        mBinding.videoMenuView.getTimeRulerView().syncWith(mBinding.trv);
        mBinding.loadingView.showLoading(DURATION_LOADING_TIMEOUT, this::showErrorToast);

        if (DBUtil.Exists(DBKey.KEY_TIME_LINE_MOTION_SCROLL_UP)) {
            needShowScrollUpHint = false;
        }

        onActionMenuUpdate();
        initTimeline();

        mBinding.ivBack.setOnClickListener(new View.OnClickListener() {
            @Keep
            @SingleClick
            @Override
            public void onClick(View v) {
                toClose();
            }
        });

        // mBinding.videoView.setOnTouchListener((v, event) -> {
        //     DDLog.v(TAG, "VideoView was touched!");
        //     final int action = event.getAction();
        //     if (MotionEvent.ACTION_UP == action) {
        //         mBinding.videoMenuView.toggleMenuView();
        //
        //         if (RecordTimeLineVideoState.STATE_EMPTY == videoState) {
        //             return true;
        //         }
        //
        //         clickPlay();
        //     }
        //     return true;
        // });

        mBinding.videoView.setOnDoubleTapListener(new GestureDetector.OnDoubleTapListener() {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                mBinding.videoMenuView.toggleMenuView();
                return true;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (RecordTimeLineVideoState.STATE_EMPTY == videoState) {
                    return true;
                }
                clickPlay();
                return true;
            }

            @Override
            public boolean onDoubleTapEvent(MotionEvent e) {
                return false;
            }
        });

        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mBinding.cvPlayer.getLayoutParams();
        layoutParams.height = (int) (ScreenUtils.getScreenWidth(this) * 0.53625f);
        mBinding.cvPlayer.setLayoutParams(layoutParams);

        mBinding.videoView.setOnPreparedListener(mp -> {
            DDLog.d(TAG, "OnPreparedListener: ");
            mMenuConfig.setMenuEnable(true);
            setPlayerMenuEnable(true);

            mp.setOnInfoListener(new MediaPlayer.OnInfoListener() {
                @Override
                public boolean onInfo(MediaPlayer mp, int what, int extra) {
                    if (MediaPlayer.MEDIA_INFO_VIDEO_RENDERING_START == what) {
                        setVideoState(RecordTimeLineVideoState.STATE_PLAYING);
                        if (1 == playCount) {
                            playCount++;
                            clickPlay();
                        }
                    }
                    return false;
                }
            });

            if (videoRecordPlayManager.getCurrentPlayIndex() == 0) {
                // //心籁移动侦测视频长度不固定,需要获取视频的播放时长.
                // if (HeartLaiConstants.PROVIDER_HEARTLAI.equals(provider)) {
                //     VIDEO_LENGHT = Math.round(mBinding.videoView.getDuration() / 1000f);
                //     Log.w(TAG, "OnPreparedListener: " + VIDEO_LENGHT);
                //     mBinding.seekBarFullscreen.setMax(videoRecordPlayManager.getTotalVideoCount() * VIDEO_LENGHT);
                // }
                handler.removeCallbacks(updateProgressRunnable);
                handler.post(updateProgressRunnable);
            }
            //视频准备播放,跳到目标时间
            DDLog.d(TAG, "loadData-->seekTime:" + seekTime);
            mBinding.videoView.seekTo(seekTime * 1000);
            seekTime = 0;
            mp.setOnCompletionListener(mp1 -> {
                DDLog.d(TAG, "OnCompletionListener: ");
                mBinding.videoMenuView.setPreviewImage(mBinding.videoView.getSnapshot());
                mBinding.videoView.pause();
                boolean hasNext = videoRecordPlayManager.next();
                if (!hasNext) {
                    videoRecordPlayManager.reset();
                }

            });
        });

        initDownloadStatus();

        mBinding.actionMenuView.setOnMenuClickListener(mActionMenuListener);
        mBinding.videoMenuView.setOnMenuClickListener(mActionMenuListener);
        mBinding.videoMenuView.setIsFullscreenMode(isFullScreen);

        if (CloudStorageServiceHelper.getInstance().getProductSchedules().size() == 0) {
            CloudStorageServiceHelper.getInstance().registerProductSchedulesListener(listProductSchedulesListener);
            CloudStorageServiceHelper.getInstance().fetchProductSchedules("");
        } else {
            initAlertServiceInfo();
        }
    }

    @Override
    protected void loadData() {
        EventBus.getDefault().register(this);
        // IjkMediaPlayer.loadLibrariesOnce(null);
        // IjkMediaPlayer.native_profileBegin("libijkplayer.so");
    }

    /**
     * 切换到没有录像的状态
     */
    private void onSelectedEmpty() {
        mActivatedTimeRulerView.deactivateSelectedTimePart();

        stopPlay();
        setVideoState(RecordTimeLineVideoState.STATE_EMPTY);

        mPlayingTimePartEvent = null;

        mMenuConfig.setMenuEnable(false);
        setPlayerMenuEnable(false);

        initLastDownloadStatus();
    }

    /**
     * 暂停播放并显示预览图禁用视频相关菜单
     */
    private void pausePlay() {
        if (mBinding.videoView.isPlaying()) {
            setPlayerMenuEnable(false);
            if (RecordTimeLineVideoState.STATE_PLAYING == videoState) {
                mBinding.videoMenuView.setPreviewImage(mBinding.videoView.getSnapshot());
            }
            setVideoState(RecordTimeLineVideoState.STATE_PAUSE);
            mBinding.videoView.pause();
            DDLog.i(TAG, "pausePlay, state11: " + videoState);
        } else if (RecordTimeLineVideoState.STATE_PLAYING == videoState
                || RecordTimeLineVideoState.STATE_LOADING == videoState) {
            setPlayerMenuEnable(false);
            setVideoState(RecordTimeLineVideoState.STATE_PAUSE);
            mBinding.videoMenuView.setPreviewImage(null);
            DDLog.i(TAG, "pausePlay, state21: " + videoState);
        }
    }

    /**
     * 开始播放
     *
     * @param needSeek 是否需要移动到指定的位置开始 false：从视频开始的位置开始播放
     */
    private synchronized void startPlay(final boolean needSeek) {
        playCount++;
        videoRecordPlayManager = new VideoRecordPlayManager(motionDetectionPlayListener);
        setVideoState(RecordTimeLineVideoState.STATE_LOADING);

        if (needSeek) {
            // 先加载视频列表，然后从当前指针所指的位置开始播放
            mNeedSeedToCurrent = true;
            videoRecordPlayManager.startNotAutoDownload();
        } else {
            // 先加载视频列表，然后直接从视频开头开始播放
            mNeedSeedToCurrent = false;
            seekFromEventStartMillis = 0;
            videoRecordPlayManager.start();
        }
    }

    private synchronized void stopPlay() {
        mNeedSeedToCurrent = false;
        seekFromEventStartMillis = 0;
        cancelGetVideoList();
        if (mBinding.videoView.isPlaying()) {
            mBinding.videoMenuView.setPreviewImage(null);
            mBinding.videoView.pause();
            mBinding.videoView.release(true);
        }
        if (videoRecordPlayManager != null) {
            videoRecordPlayManager.stop();
        }
        synchronized (mVideoList) {
            mVideoList.clear();
        }
        handler.removeCallbacks(updateProgressRunnable);
    }

    private void setVideoState(@RecordTimeLineVideoState final int newState) {
        this.videoState = newState;
        mBinding.videoMenuView.setVideoState(newState);
    }

    public void clickPlay() {
        if (mBinding.videoView.isPlaying()) {
            pausePlay();
        } else {
            setPlayerMenuEnable(false);
            if (!isCompleted) {
                // setVideoState(RecordTimeLineVideoState.STATE_PLAYING);
                seekToCurrent();
            } else {
                setVideoState(RecordTimeLineVideoState.STATE_LOADING);
                isCompleted = false;
                videoRecordPlayManager.seekTo(0);
            }
        }
    }

    private RelativeLayout.LayoutParams defaultVideoViewParams;
    private int defaultScreenOrientationMode;

    private void makeVideoFullScreen(boolean autoRotate) {
        defaultScreenOrientationMode = getResources().getConfiguration().orientation;
        defaultVideoViewParams = (RelativeLayout.LayoutParams) mBinding.cvPlayer.getLayoutParams();
        if (!autoRotate) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
        mBinding.controlView.setVisibility(View.GONE);
        // mBinding.btnBackLand.setVisibility(View.VISIBLE);
        // mBinding.fullscreenControl.setVisibility(View.VISIBLE);
        mBinding.commonBar.setVisibility(View.GONE);
        // mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_small_screen);
        setDownloadIconVisible(false, true);
        mBinding.cvPlayer.postDelayed(new Runnable() {

            @Override
            public void run() {
                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) new RelativeLayout.LayoutParams(
                        getResources().getDisplayMetrics().widthPixels,
                        getResources().getDisplayMetrics().heightPixels);

                mBinding.cvPlayer.setLayoutParams(params);
                mBinding.cvPlayer.setRadius(0);
                CardView.LayoutParams p = new CardView.LayoutParams(
                        RelativeLayout.LayoutParams.MATCH_PARENT,
                        RelativeLayout.LayoutParams.MATCH_PARENT);
                mBinding.rlPlayer.setLayoutParams(p);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);

        mBinding.videoMenuView.setIsFullscreenMode(true);

        // 进入全屏隐藏状态栏
        //获得 WindowManager.LayoutParams 属性对象
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        //直接对它flags变量操作   LayoutParams.FLAG_FULLSCREEN 表示设置全屏
        lp.flags |= WindowManager.LayoutParams.FLAG_FULLSCREEN;
        //设置属性
        getWindow().setAttributes(lp);
        //意思大致就是  允许窗口扩展到屏幕之外
//        getWindow().addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
    }

    private void exitVideoFullScreen(boolean autoRotate) {
        if (autoRotate) {
            setRequestedOrientation(defaultScreenOrientationMode);
        } else {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
        mBinding.controlView.setVisibility(View.VISIBLE);
        // mBinding.btnBackLand.setVisibility(View.GONE);
        // mBinding.fullscreenControl.setVisibility(View.GONE);
        // mBinding.llFunctionBtnLand.setVisibility(View.GONE);
        mBinding.commonBar.setVisibility(View.VISIBLE);
        setDownloadIconVisible(true, false);
        // mBinding.glviewFullscreen.setImageResource(R.drawable.icon_ipc_full_screen);
        mBinding.cvPlayer.postDelayed(new Runnable() {

            @Override
            public void run() {
                mBinding.cvPlayer.setLayoutParams(defaultVideoViewParams);
                mBinding.cvPlayer.setRadius(dip2px(getApplicationContext(), 16));
                CardView.LayoutParams p = new CardView.LayoutParams(
                        RelativeLayout.LayoutParams.MATCH_PARENT,
                        RelativeLayout.LayoutParams.MATCH_PARENT);
                mBinding.rlPlayer.setLayoutParams(p);
                mBinding.videoView.layout(10, 10, 10, 10);
            }
        }, 100);

        mBinding.videoMenuView.setIsFullscreenMode(false);
        if (null != mPlayingTimePartEvent && mBinding.videoView.isPlaying()) {
            updatePlayProgress();
        }

        // 退出全屏显示状态栏
        //获得 WindowManager.LayoutParams 属性对象
        WindowManager.LayoutParams lp2 = getWindow().getAttributes();
        //LayoutParams.FLAG_FULLSCREEN 强制屏幕状态条栏弹出
        lp2.flags &= (~WindowManager.LayoutParams.FLAG_FULLSCREEN);
        //设置属性
        getWindow().setAttributes(lp2);
        //不允许窗口扩展到屏幕之外  clear掉了
//        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        DDLog.d(TAG, "onConfigurationChanged: ");
        // Checks the orientation of the screen
        mBinding.scrollView.scrollTo(0, 0);
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mBinding.scrollView.setEnableScroll(false);
            mBinding.llServiceRoot.setVisibility(View.INVISIBLE);
            isFullScreen = true;
            onTimeUpdate(mActivatedTimeRulerView.getCurrentTime());
            changeActivatedTimeRulerView(true);
            makeVideoFullScreen(true);
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            mBinding.scrollView.setEnableScroll(true);
            mBinding.llServiceRoot.setVisibility(View.VISIBLE);
            isFullScreen = false;
            onTimeUpdate(mActivatedTimeRulerView.getCurrentTime());
            changeActivatedTimeRulerView(false);
            exitVideoFullScreen(true);
        }
    }

    /**
     * 切换全屏时间轴和竖屏时间轴的激活状态
     *
     * @param activateFullScreenTimeRuler 是否激活全屏时间轴
     */
    private void changeActivatedTimeRulerView(boolean activateFullScreenTimeRuler) {
        final TimeRulerView lastActivatedView = mActivatedTimeRulerView;
        if (null != lastActivatedView) {
            lastActivatedView.deactivate();
        }
        mActivatedTimeRulerView = activateFullScreenTimeRuler ? mBinding.videoMenuView.getTimeRulerView() : mBinding.trv;
        mActivatedTimeRulerView.activate();
    }

    private void updatePlayProgress() {
        // 设置进度条的主要进度，表示当前的播放时间
        if (null != mPlayingTimePartEvent && null != videoRecordPlayManager) {
            final int playIndex = videoRecordPlayManager.getCurrentPlayIndex();
            long videoStartTime = 0;
            synchronized (mVideoList) {
                if (playIndex >= 0 && playIndex < mVideoList.size()) {
                    for (int i = 0; i < playIndex; i++) {
                        videoStartTime += mVideoList.get(i).getLen();
                    }
                }
            }
            final long duration = (mPlayingTimePartEvent.getEndTime() - mPlayingTimePartEvent.getStartTime()) * 1000;
            long currentPosition = videoStartTime + mBinding.videoView.getCurrentPosition();
            DDLog.d(TAG, "updatePlayProgress-->currentPosition:" + currentPosition + " /duration:" + duration);
            if (currentPosition > duration) {
                currentPosition = duration;
            } else if (currentPosition < 0) {
                currentPosition = 0;
            }
            final long currentTime = mPlayingTimePartEvent.getStartTime() + (currentPosition + 500) / 1000;
            DDLog.i("updatePlayProgress, start: ", formatDateAndTime(mPlayingTimePartEvent.getStartTime() * 1000));
            DDLog.i("updatePlayProgress, current: ", formatDateAndTime(currentTime * 1000));
            mActivatedTimeRulerView.setCurrentTime(currentTime);
        }
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        DDLog.d(TAG, "onRestart");
        if (!isCompleted && !failed) {
            seekToCurrent();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        Log.d(TAG, "onStop: ");
        pausePlay();
        if (failed) {
            showErrorToast();
        }
    }

    @Override
    protected void onDestroy() {
        cancelVirtualProgressAnim();
        CloudStorageServiceHelper.getInstance().unregisterProductSchedulesListener(listProductSchedulesListener);
        MotionDownloadManager.get().removeAllTaskDownloadListener();
        MotionDownloadManager.get().removeMotionDownloadAmountListener(this);
        try {
            mLoadPool.shutdownNow();
            mLoadPool = null;
        } catch (Exception e) {

        }
        if (errorDialog != null && errorDialog.isShowing()) {
            errorDialog.dismiss();
        }

        // 取消未完成的请求
        synchronized (unFinishedRequests) {
            if (unFinishedRequests.size() > 0) {
                for (Call<?> unFinishedRequest : unFinishedRequests.values()) {
                    if (null != unFinishedRequest) {
                        unFinishedRequest.cancel();
                    }
                }
            }
        }

        super.onDestroy();
        Log.d(TAG, "onDestroy: ");
        EventBus.getDefault().unregister(this);
        mBinding.videoView.setOnPreparedListener(null);
        mBinding.videoView.release(true);
        // IjkMediaPlayer.native_profileEnd();
        handler.removeCallbacksAndMessages(null);
        stopPlay();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayMotionDetectedRecordEvent(PlayMotionDetectedRecordEvent playMotionDetectedRecordEvent) {
        toClose();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(HadLogoutEvent ev) {
        finish();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSOSevent(SOSevent soSevent) {
        toClose();
    }

    private void seekToCurrent() {
        if (null == mPlayingTimePartEvent || null == videoRecordPlayManager) {
            return;
        }

        int index = 0;
        seekTime = 0;
        synchronized (mVideoList) {
            final long startTime = mPlayingTimePartEvent.getStartTime();
            final long endTime = mPlayingTimePartEvent.getEndTime();
            final long current = mActivatedTimeRulerView.getCurrentTime();
            if (mVideoList.size() > 0 && startTime <= current && current <= endTime) {
                MotionEventVideoResponse.VideosBean videosBean;
                final long timeDiff = current - startTime;
                long totalTime = 0, lastTotalTime;
                for (int i = 0; i < mVideoList.size(); i++) {
                    videosBean = mVideoList.get(i);
                    lastTotalTime = totalTime;
                    totalTime += (videosBean.getLen() / 1000);
                    if (lastTotalTime < timeDiff && timeDiff <= totalTime) {
                        index = i;
                        seekTime = (int) (timeDiff - lastTotalTime);
                        break;
                    }
                }
            }
        }

        DDLog.i(TAG, "seekTo: " + index + ", seekTime: " + seekTime);
        setVideoState(RecordTimeLineVideoState.STATE_LOADING);
        isCompleted = false;
        videoRecordPlayManager.seekTo(index);
    }

    private final VideoRecordPlayManager.VideoPlayListener motionDetectionPlayListener = new VideoRecordPlayManager.VideoPlayListener() {

        private String currentPlayUrl = "";

        @Override
        public IDownLoadTask createDownLoadTask(int index, String url, DownloadListener downloadListener) {

            String saveFileName = null;
            synchronized (mVideoList) {
                if (0 <= index && index < mVideoList.size()) {
                    MotionEventVideoResponse.VideosBean bean = mVideoList.get(index);
                    final String videoUrl = bean.getUrl();
                    final long startTime = bean.getStart_time();
                    if (!TextUtils.isEmpty(videoUrl) && videoUrl.equals(url) && startTime > 0) {
                        saveFileName = String.valueOf(startTime);
                    }
                }
            }

            final String playingEventId = getPlayingEventId();
            if (TextUtils.isEmpty(playingEventId) || TextUtils.isEmpty(saveFileName)) {
                return null;
            }
            String path = new StringBuilder(getExternalCacheDir().getAbsolutePath())
                    .append(File.separator)
                    .append(playingEventId)
                    .append(File.separator)
                    .append(saveFileName)
                    .append("_transform")
                    .append(".mp4")
                    .toString();
            HttpVideoDownLoadTask task = new HttpVideoDownLoadTask(url, path, true);
            return task;
        }

        @Override
        public void onLoadMoreVideo(int startIndex, int c, VideoRecordPlayManager.OnLoadMoreVideoCallback callback) {
            DDLog.i(TAG, "onLoadMoreVideo, startIndex: " + startIndex + ", total: " + c);
            setVideoState(RecordTimeLineVideoState.STATE_LOADING);

            final long startTime;
            synchronized (mVideoList) {
                if (0 < mVideoList.size()) {
                    startTime = mVideoList.get(mVideoList.size() - 1).getStart_time() + 1;
                } else if (null != mPlayingTimePartEvent) {
                    startTime = mPlayingTimePartEvent.getStartTime();
                } else {
                    startTime = -1;
                }
            }

            final String homeId = HomeManager.getInstance().getCurrentHome().getHomeID();
            final String eventId = getPlayingEventId();
            final String ipcId = getPlayingIpcId();

            if (TextUtils.isEmpty(homeId)
                    || TextUtils.isEmpty(ipcId)
                    || TextUtils.isEmpty(eventId)
                    || 0 >= startTime) {
                callback.onFinish(null);
                return;
            }

            long realSeekTimeMillis = 0;
            if (mNeedSeedToCurrent && seekFromEventStartMillis > 0 && null != mPlayingTimePartEvent) {
                final long videoDuration = (mPlayingTimePartEvent.getEndTime() - mPlayingTimePartEvent.getStartTime()) * 1000;
                if (seekFromEventStartMillis < videoDuration) {
                    realSeekTimeMillis = seekFromEventStartMillis;
                }
            }

            cancelGetVideoList();

            mVideoListDownloadTask = new VideoListDownloadTask(homeId, ipcId, eventId, startTime, realSeekTimeMillis, new VideoListDownloadTask.Callback() {
                @Override
                public void onResult(String eventId, List<MotionEventVideoResponse.VideosBean> dataList) {
                    runOnUiThread(() -> {
                        if (isFinishing() || isDestroyed()) {
                            return;
                        }
                        DDLog.i(TAG, "VideoListDownloadTask, onResult: " + dataList);
                        final String currentEventId = getPlayingEventId();

                        // 忽略请求结果
                        if (TextUtils.isEmpty(currentEventId) || TextUtils.isEmpty(eventId) || !currentEventId.equals(eventId)) {
                            DDLog.w(TAG, "eventId问题导致忽略请求结果!");
                            return;
                        }

                        // 加载出错
                        if (null == dataList || 0 == dataList.size()) {
                            if (null != callback) {
                                callback.onFinish(null);
                            }
                            return;
                        }

                        // 加载成功
                        synchronized (mVideoList) {
                            final int newCount = dataList.size();
                            final int cacheCount = mVideoList.size();
                            final int newTotalCount = cacheCount + newCount;
                            videoRecordPlayManager.setTotalVideoCount(newTotalCount);
                            ArrayList<String> list = videoRecordPlayManager.createNullElementArrayList(new ArrayList<>(), newTotalCount);
                            MotionEventVideoResponse.VideosBean record;
                            for (int i = 0; i < dataList.size(); i++) {
                                record = dataList.get(i);
                                list.add(cacheCount + i, record.getUrl());
                            }
                            mVideoList.addAll(dataList);
                            callback.onFinish(list);

                            if (mNeedSeedToCurrent) {
                                mNeedSeedToCurrent = false;
                                seekFromEventStartMillis = 0;
                                seekToCurrent();
                            }
                        }
                    });
                }
            });
            if (null != mLoadPool) {
                mLoadPool.submit(mVideoListDownloadTask);
            }
        }

        @Override
        public void onDownLoad() {
            setVideoState(RecordTimeLineVideoState.STATE_LOADING);
            failed = false;
        }

        @Override
        public void onPlay(int index, String url, String filePath) {
            //这里控制播放哪一个视频
            // setVideoState(RecordTimeLineVideoState.STATE_PLAYING);
            if (mBinding.videoView.isPlaying() && url.equals(currentPlayUrl)) {
                DDLog.d(TAG, "当前url已经在播放中 ");
                return;
            }
            handler.removeCallbacks(updateProgressRunnable);
            handler.post(updateProgressRunnable);
            currentPlayUrl = url;
            mBinding.videoView.setVideoPath(filePath);
            mBinding.videoView.setMute(!mMenuConfig.isSoundOpened());
            mBinding.videoView.setPlaySpeed(mMenuConfig.getSpeed());
            mBinding.videoView.start();
        }

        @Override
        public boolean onDownloadFail(String url, String errorInfo) {
            return true;
        }

        @Override
        public void onCompletion() {
            // 播放结束,没有更多数据
            DDLog.d(TAG, "onCompletion: " + videoRecordPlayManager.getCurrentPlayIndex());
            onPlayComplete(false);
        }
    };

    private void onPlayComplete(boolean failed) {
        this.failed = failed;
        isCompleted = true;
        setPlayerMenuEnable(false);
        setVideoState(RecordTimeLineVideoState.STATE_COMPLETED);
        mBinding.videoMenuView.setPreviewImage(mBinding.videoView.getSnapshot());
        mBinding.videoView.pause();
        videoRecordPlayManager.reset();

        if (failed) {
            showErrorToast();
        }
    }

    /**
     * 取消加载视频列表
     */
    private void cancelGetVideoList() {
        if (null != mVideoListDownloadTask) {
            mVideoListDownloadTask.cancel();
            mVideoListDownloadTask = null;
        }
    }

    private AlertDialog errorDialog;

    public final void showErrorToast() {
        runOnUiThread(() -> {
            if (errorDialog != null && errorDialog.isShowing()) {
                errorDialog.dismiss();
            }
            errorDialog = AlertDialog.createBuilder(MotionRecordTimelinePlayerActivity.this)
                    .setOk(getResources().getString(R.string.ok))
                    .setContent(getResources().getString(R.string.failed_try_again))
                    .setOKListener(() -> {
                        errorDialog.dismiss();
                    })
                    .preBuilder();
            errorDialog.show();
        });
    }

    private final MotionDownloadListener downloadListener = new MotionDownloadListener() {
        @Override
        public void onSuccess(@Nullable Boolean result) {
            DDLog.i(TAG, "onSuccess, result: " + result);
        }

        @Override
        public void onException(@NonNull Throwable error) {
            DDLog.e(TAG, "Error");
            error.printStackTrace();
            changeDownloadViewStatus(false);
        }

        @Override
        public void onDownloadSuccess(int current, int total, String filePath) {
            DDLog.v(TAG, "onDownloadSuccess, current: " + current + ", total: " + total + ", filePath: " + filePath);
        }

        @Override
        public void onDownloadError(int current, int total, Throwable throwable) {
            DDLog.w(TAG, "onDownloadError,  current: " + current + ", total: " + total + ", error: " + throwable.getMessage());
        }

        @Override
        public void onCurrent(int current, int total) {
            DDLog.v(TAG, "onCurrent,  current: " + current + ", total: " + total);
            changeDownloadViewStatus(true);
            if (!isDownloadProgressAnim()) {
                setDownloadProgressCurrent(DOWNLOAD_TARGET_PROGRESS);
            }
        }
    };

    private Subscription downloadProgressSubscription;
    private volatile boolean downloadFinished = false;

    private void startVirtualProgressAnim() {
        cancelVirtualProgressAnim();
        setDownloadProgressMax(DOWNLOAD_MAX_PROGRESS);
        setDownloadProgressCurrent(0);
        downloadProgressSubscription = Observable.interval((1000 + DOWNLOAD_TARGET_TIME_MILLIS) / DOWNLOAD_TARGET_UPDATE_NUM, TimeUnit.MILLISECONDS)
                .take(DOWNLOAD_TARGET_PROGRESS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(value -> {
                    DDLog.i(TAG, "value: " + value);
                    setDownloadProgressCurrent((int) (value + 1));
                }, (e) -> {
                }, () -> {
                    if (downloadFinished) {
                        onDownloadFinished();
                    }
                });
    }

    private void onDownloadFinished() {
        setDownloadProgressCurrent(DOWNLOAD_MAX_PROGRESS);
        handler.postDelayed(() -> {
            changeDownloadViewStatus(false);
            showTopToast(getString(R.string.saved_to_album));

        }, 300);
    }

    private void cancelVirtualProgressAnim() {
        if (null != downloadProgressSubscription && !downloadProgressSubscription.isUnsubscribed()) {
            downloadProgressSubscription.unsubscribe();
            downloadProgressSubscription = null;
        }
    }

    private boolean isDownloadProgressAnim() {
        return null != downloadProgressSubscription && !downloadProgressSubscription.isUnsubscribed();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (REQ_DOWNLOAD_LIST == requestCode && RESULT_OK == resultCode) {
            initLastDownloadStatus();
        }
    }

    public void setDownloadProgressMax(int maxProgress) {
        mBinding.videoMenuView.setDownloadProgressMax(maxProgress);
        mBinding.actionMenuView.setDownloadProgressMax(maxProgress);
    }

    public void setDownloadProgressCurrent(int progress) {
        mBinding.videoMenuView.setDownloadProgressCurrent(progress);
        mBinding.actionMenuView.setDownloadProgressCurrent(progress);
    }

    /**
     * 初始化任务的下载状态
     */
    private void initDownloadStatus() {
        if (isSupportVideoDownload()) {
            MotionDownloadManager.get().addMotionDownloadAmountListener(this);
            setDownloadIconVisible(true, false);
            mBinding.flDownloadList.setOnClickListener(new View.OnClickListener() {
                @Keep
                @SingleClick
                @Override
                public void onClick(View v) {
                    MotionRecordDownloadListActivity.start(MotionRecordTimelinePlayerActivity.this, REQ_DOWNLOAD_LIST);
                }
            });

            setDownloadProgressMax(DOWNLOAD_MAX_PROGRESS);
            setDownloadProgressCurrent(0);

            initLastDownloadStatus();
            return;
        }

        setDownloadIconVisible(false, false);
    }

    private void initLastDownloadStatus() {
        changeDownloadViewStatus(false);

        final String playingEventId = getPlayingEventId();
        if (TextUtils.isEmpty(playingEventId)) {
            return;
        }

        MotionVideoDownloadTask lastTask = MotionDownloadManager.get().monitorLastTask(playingEventId, downloadListener);
        changeDownloadViewStatus(null != lastTask);
        if (null != lastTask) {
            setDownloadProgressCurrent(DOWNLOAD_TARGET_PROGRESS);
        }
    }

    private void changeDownloadViewStatus(final boolean downloading) {
        runOnUiThread(() -> {
            if (!downloading) {
                cancelVirtualProgressAnim();
                downloadFinished = false;
            }

            if (isFinishing() || isDestroyed()) {
                return;
            }
            DDLog.v(TAG, "changeDownloadViewStatus, downloading: " + downloading);
            mBinding.videoMenuView.changeDownloadViewStatus(downloading);
            mBinding.actionMenuView.changeDownloadViewStatus(downloading);
        });
    }

    private void download() {
        final String playingEventId = getPlayingEventId();
        if (TextUtils.isEmpty(playingEventId)) {
            changeDownloadViewStatus(false);
            return;
        }
        changeDownloadViewStatus(true);
        downloadFinished = false;
        startVirtualProgressAnim();
        final String homeId = DinHome.getInstance().getCurrentHome().getHomeID();
        MotionDownloadManager.get().addDownloadTask(DinSaferApplication.getAppContext(), homeId,
                new MotionDownloadEvent(mPlayingTimePartEvent.getDeviceId(), mPlayingTimePartEvent.getDeviceName(), playingEventId,
                        mPlayingTimePartEvent.getStartTime(), mPlayingTimePartEvent.getCoverUrl()),
                downloadListener);
    }

    /**
     * 设置下载图标的可见性
     *
     * @param tittleDownloadVisible 标题下载图标是否可见
     * @param videoDownloadVisible  视频播放界面下载图标是否可见
     */
    private void setDownloadIconVisible(final boolean tittleDownloadVisible, final boolean videoDownloadVisible) {
        // if (!isSupportVideoDownload()) {
        //     mBinding.flDownload.setVisibility(View.INVISIBLE);
        //     return;
        // }
        // mBinding.flDownload.setVisibility(videoDownloadVisible ? View.VISIBLE : View.INVISIBLE);
    }

    /**
     * 是否支持下载视频
     *
     * @return true 支持下载
     */
    private boolean isSupportVideoDownload() {
        // return (DinConst.TYPE_DSCAM.equalsIgnoreCase(provider) || DinConst.TYPE_DSCAM_VOO6.equalsIgnoreCase(provider))
        //         && !mKcpMode;
        return true;
    }

    private void showTopToast(final String message) {
        runOnUiThread(() -> {
            mBinding.commonTopToast.setLocalText(message);
            Drawable leftIcon = getResources().getDrawable(R.drawable.icon_toast_succeed);
            leftIcon.setBounds(0, 0, leftIcon.getMinimumWidth(), leftIcon.getMinimumHeight());
            mBinding.commonTopToast.setCompoundDrawables(leftIcon, null, null, null);
            mBinding.commonTopToastLy.showToast();
        });
    }

    protected void showPermissionNotGrantTip(final String tip, final String[] permission) {
        AlertDialog.createBuilder(this)
                .setOk(getString(R.string.go_setting))
                .setOKListener(() -> {
                    PermissionDialogUtil.goIntentSetting(this);
                })
                .setCancel(getString(R.string.ignore))
                .setContent(tip)
                .preBuilder()
                .show();
    }

    protected void requestReadImagesPermission() {
        final String[] permission = PermissionUtil.getStoragePermissions();
        requestStoragePermission(permission);
    }

    protected void requestReadVideoPermission() {
        final String[] permission = PermissionUtil.getStoragePermissions();
        requestStoragePermission(permission);
    }


    protected void requestStoragePermission(String[] permission) {
        if (null == permission) {
            return;
        }
        boolean denied = AndPermission.hasAlwaysDeniedPermission(this, permission);
        AndPermission.with(this)
                .runtime()
                .permission(permission)
                .onGranted(permissions -> {

                })
                .onDenied(permissions -> {
                    DDLog.e("", "Storage permission deny!!!");
                    if (denied && AndPermission.hasAlwaysDeniedPermission(this, permissions)) {
                        showPermissionNotGrantTip(getString(R.string.ipc_save_snapshot_without_permission), permission);
                    }
                })
                .start();
    }

    @Override
    public void onTaskAmountUpdate(int taskTotalCount) {
        runOnUiThread(() -> {
            if (taskTotalCount > 0) {
                mBinding.tvDownloadAmount.setVisibility(View.VISIBLE);
                mBinding.tvDownloadAmount.setText(String.valueOf(taskTotalCount));
                mBinding.flDownloadList.setVisibility(View.VISIBLE);
            } else {
                mBinding.tvDownloadAmount.setVisibility(View.GONE);
                mBinding.tvDownloadAmount.setText("");
                mBinding.flDownloadList.setVisibility(View.GONE);
            }
        });
    }

    @Override
    public void onTaskAdd(MotionDownloadEvent event) {
    }

    @Override
    public void onTaskCompleted(MotionDownloadEvent event, boolean success) {
        runOnUiThread(() -> {
            final String completedEventId = event.getEventId();
            if (!TextUtils.isEmpty(completedEventId) && completedEventId.equals(getPlayingEventId())) {
                downloadFinished = true;
                cancelVirtualProgressAnim();
                onDownloadFinished();
            }
        });
    }

    @Override
    public void onTaskProgressUpdate(String eventId, int current, int total, boolean success) {
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(AlertServicePlanUpdateEvent alertServicePlanUpdateEvent) {
        initAlertServiceInfo();
    }

    private void initAlertServiceInfo() {
        if (!AppConfig.Functions.SUPPORT_CLOUD_SERVICE || !(HomeManager.getInstance().getCurrentHome().getLevel() == LocalKey.ADMIN)) {
            return;
        }

        if (IPCManager.getInstance().getNotDeletedDsCamList().size() == 0) {
            return;
        }

        if (!CloudStorageServiceHelper.getInstance().isDsCamV005V006ServiceOpen()) {
            mBinding.layoutAlertServiceBeta.clCurrentPlanCard.setVisibility(View.VISIBLE);
            SpannableStringBuilder stringBuilder = new SpannableStringBuilder()
                    .append(StringStyle.format(this, Local.s(getResources().getString(R.string.iap_cloud_storage_service_is_on)), R.style.iAPCurrentPlanDes1))
                    .append(" ")
                    .append(StringStyle.format(this, Local.s(getResources().getString(R.string.iap_free_trial)), R.style.iAPCurrentPlanDes2));
            ((LocalTextView) mBinding.layoutAlertServiceBeta.clCurrentPlanCard.findViewById(R.id.tv_current_subs_plan)).setText(stringBuilder);
            ((LocalCustomButton) mBinding.layoutAlertServiceBeta.clCurrentPlanCard.findViewById(R.id.btn_change_subs_plan)).setLocalText(getString(R.string.iap_what_is_cloud_storage));
            mBinding.layoutAlertServiceBeta.clCurrentPlanCard.findViewById(R.id.btn_change_subs_plan).setOnClickListener(new View.OnClickListener() {
                @Keep
                @SingleClick
                @Override
                public void onClick(View v) {
                    clickCurrentPlanCard();
                }
            });
        } else {
            int cardCount = CloudStorageServiceHelper.getInstance().getProductSchedules().size();
            if (cardCount == 0) {
                mBinding.layoutAlertService.setVisibility(View.GONE);
            } else {
                mBinding.layoutAlertService.setVisibility(View.VISIBLE);
                mBinding.layoutAlertService.setOnClickListener(new View.OnClickListener() {
                    @Keep
                    @SingleClick
                    @Override
                    public void onClick(View v) {
                        clickCurrentPlanCard();
                    }
                });
                mBinding.btnManage.setOnClickListener(new View.OnClickListener() {
                    @Keep
                    @SingleClick
                    @Override
                    public void onClick(View v) {
                        clickCurrentPlanCard();
                    }
                });
                ServiceCardItemModel listBean = CloudStorageServiceHelper.getInstance().getProductSchedules("").get(0);
                //2.1 有家庭服务卡
                //2.2 无家庭服务卡，显示有效期最近的设备服务卡状态
                mBinding.layoutAlertService.findViewById(R.id.tv_name).setVisibility(View.VISIBLE);
                ((LocalTextView) mBinding.layoutAlertService.findViewById(R.id.tv_name)).setLocalText(listBean.isFamilyService() ? this.getString(R.string.family_service) : listBean.getName());
                ((TextView) mBinding.layoutAlertService.findViewById(R.id.tv_des)).setText(listBean.getServiceDateText(this));
                ((TextView) mBinding.layoutAlertService.findViewById(R.id.tv_des)).setTextColor(listBean.getServiceDateTextColor(this));
                ((ImageView) mBinding.layoutAlertService.findViewById(R.id.iv_expired)).setVisibility(listBean.isExpired() ? View.VISIBLE : View.GONE);

                //若有多个服务卡，则显示折叠样式
                if (cardCount > 1) {
                    mBinding.layoutAlertService.setPadding(0, DensityUtils.dp2px(this, 10), 0, 0);
                }
            }
        }
    }

    public void clickCurrentPlanCard() {
        if (IPCManager.getInstance().getNotDeletedDsCamList().size() == 0) {
            IapRootActivity.start(this, IapRootActivity.START_FRAG_BUY_IPC);
            return;
        }

        if (!CloudStorageServiceHelper.getInstance().isDsCamV005V006ServiceOpen()) {
            IapRootActivity.start(this, IapRootActivity.START_FRAG_CLOUD_STORAGE);
            return;
        }

        IapRootActivity.start(this, IapRootActivity.START_FRAG_CLOUD_STORAGE_SERVICE);
    }

    private boolean checkNeedUpgrade(Device device) {
        if (DsCamUtils.isDeviceConnected(device)) {
            int firmwareUpgradeState = DsCamUpgradeManager.getInstance().getFirmwareUpgradeState(device.getId());
            return firmwareUpgradeState >= DsCamUpgradeManager.IPC_FIRMWARE_UPGRADE;
        }
        return false;
    }

    public void showDeviceOfflineDialog(final String deviceId) {
        final AlertDialogV2 offlineDialog = AlertDialogV2.createBuilder(this)
                .setContent(this.getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(this.getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        final Device device = DinSDK.getHomeInstance().getDevice(deviceId);
                        IPCManager.getInstance().connectDevice(device, true);
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        EventBus.getDefault().post(new DsCamNetWorkSetting(deviceId));
                        toClose();
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    private void showUpgradeDialog(Device device) {
        final int firmwareUpgradeState = DsCamUpgradeManager.getInstance().getFirmwareUpgradeState(device.getId());
        final boolean isForce = firmwareUpgradeState == DsCamUpgradeManager.IPC_FIRMWARE_FORCE_UPGRADE;
        String content = Local.s(this.getResources().getString(isForce ? R.string.ipc_upgrade_dialog_force : R.string.ipc_upgrade_dialog_nor))
                .replace("#plugin", DeviceHelper.getString(device, "name", " "));
        AlertDialog.createBuilder(this)
                .setContent(content)
                .setOk(this.getString(R.string.update))
                .setOKListener(() -> {
                    EventBus.getDefault().post(new DsCamUpgradeEvent(device.getId()));
                    toClose();
                })
                .setCancel(this.getString(R.string.ignore))
                .setCancelListener(() -> {
                    if (!isForce) {
                        DsCamUpgradeManager.getInstance().ignoreFirmwarVersion(device);
                    }
                }).preBuilder().show();
    }

    private void showIpcDeletedDialog(final String deviceId) {
        String name = "";
        if (!TextUtils.isEmpty(deviceId) && mIpcListSrc.size() > 0) {
            for (int i = 0; i < mIpcListSrc.size(); i++) {
                if (deviceId.equals(mIpcListSrc.get(i).getIpc_id())) {
                    name = mIpcListSrc.get(i).getName();
                    break;
                }
            }
        }
        final String content = Local.s(getResources().getString(R.string.video_time_line_ipc_deleted))
                .replace("#Camera_Name", name == null ? "" : name);
        AlertDialog.createBuilder(this)
                .setContent(content)
                .setOk(this.getResources().getString(R.string.ok))
                .setAutoDissmiss(true)
                .preBuilder().show();
    }

    /**
     * 显示全屏播放状态下选择IPC的菜单
     */
    private void showFullscreenSelectIpcMenu() {
        final ArrayList<RecordSelectIpcBean> ipcList = new ArrayList<>();
        if (mIpcListSrc.size() > 0) {
            final boolean hadSelected = mIpcListSelected.size() > 0;
            for (LastMotionIpcListResponse.IpcsBean ipcBean : mIpcListSrc) {
                final boolean selected = hadSelected && mIpcListSelected.contains(ipcBean.getIpc_id());
                ipcList.add(new RecordSelectIpcBean(ipcBean.getIpc_id(), ipcBean.getName(), selected));
            }
        }
        mBinding.videoMenuView.showFullscreenSelectIpcMenu(ipcList);
    }

    /**
     * 显示全屏播放状态下选择时间菜单
     */
    private void showFullscreenSelectTimeMenu() {
        mBinding.videoMenuView.showFullscreenSelectTimeMenu(mCurrentHour, mCurrentMin, mCurrentSecond);
    }

    /**
     * 显示全屏播放状态下选择日期菜单
     */
    private void showFullscreenSelectDateMenu() {
        final MenuSelectDateView.DateConfig config = new MenuSelectDateView.DateConfig()
                .minYear(mMinCalendar.get(Calendar.YEAR))
                .minMonth(mMinCalendar.get(Calendar.MONTH) + 1)
                .minDay(mMinCalendar.get(Calendar.DAY_OF_MONTH))
                .defaultYear(mSelectCalendar.get(Calendar.YEAR))
                .defaultMonth(mSelectCalendar.get(Calendar.MONTH) + 1)
                .defaultDay(mSelectCalendar.get(Calendar.DAY_OF_MONTH))
                .events(mMotionDateList);
        mBinding.videoMenuView.showFullscreenSelectDateMenu(config);
    }

    /**
     * 下载视频列表
     * <p>
     * 因为视频的时长在事件中，如果移动到一次加载不玩的视频上，就需要循环下载多次
     */
    private static class VideoListDownloadTask implements Runnable {
        private static final String TAG = "VideoListDownloadTask";
        private static final long TIME_OUT_MILLIS = 15 * 1000;//超时时间

        private volatile boolean intercept = false;
        private boolean failed = false;
        private final Object lock = new Object();
        private final List<MotionEventVideoResponse.VideosBean> resultList = new ArrayList<>();
        private final List<MotionEventVideoResponse.VideosBean> newList = new ArrayList<>();

        private final String homeId, ipcId, eventId;
        private final long startTime, offsetTimeMillis;
        private final Callback callback;

        public VideoListDownloadTask(String homeId, String ipcId, String eventId, long startTime, long offsetTimeMillis, Callback callback) {
            this.homeId = homeId;
            this.ipcId = ipcId;
            this.eventId = eventId;
            this.startTime = startTime;
            this.offsetTimeMillis = offsetTimeMillis;
            this.callback = callback;
        }

        public void cancel() {
            intercept = true;
        }

        @Override
        public void run() {
            if (null == callback) {
                DDLog.e(TAG, "没有callback，不进行下载！");
                return;
            }

            if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(eventId) || startTime <= 0) {
                DDLog.w(TAG, "下载条件出错");
                return;
            }

            if (intercept) {
                DDLog.w(TAG, "任务已被取消");
                return;
            }

            final long downloadStartTimeMillis = System.currentTimeMillis();
            while (true) {
                newList.clear();
                long loadStartTime = startTime;
                if (resultList.size() > 0) {
                    loadStartTime = resultList.get(resultList.size() - 1).getStart_time() + 1;
                }
                DDLog.i(TAG, "准备获取视频列表, " + eventId + " - " + loadStartTime);

                synchronized (lock) {
                    DinHome.getInstance().getMotionEventVideos(homeId, ipcId, eventId, loadStartTime,
                            new IDefaultCallBack2<MotionEventVideoResponse>() {
                                @Override
                                public void onSuccess(MotionEventVideoResponse motionEventVideoResponse) {
                                    List<MotionEventVideoResponse.VideosBean> result = motionEventVideoResponse.getResult().getVideos();
                                    if (null == result || result.size() == 0) {
                                        synchronized (lock) {
                                            lock.notify();
                                        }
                                        return;
                                    }

                                    synchronized (lock) {
                                        newList.addAll(result);
                                        lock.notify();
                                    }
                                }

                                @Override
                                public void onError(int i, String s) {
                                    DDLog.w(TAG, "getMotionEventVideos.onError, " + i + " : " + s);
                                    failed = true;
                                    synchronized (lock) {
                                        lock.notify();
                                    }
                                }
                            });

                    try {
                        lock.wait();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                if (intercept) {
                    break;
                }

                if (failed) {
                    callback.onResult(eventId, null);
                    break;
                }

                if (newList.size() == 0) {
                    callback.onResult(eventId, resultList);
                    break;
                }

                resultList.addAll(newList);
                if (resultList.size() == 0) {
                    callback.onResult(eventId, resultList);
                    break;
                }

                boolean success = false;
                long videoDuration = 0;
                for (int i = 0; i < resultList.size(); i++) {
                    videoDuration += resultList.get(i).getLen();
                    if (videoDuration > offsetTimeMillis) {
                        success = true;
                        break;
                    }
                }

                if (success) {
                    callback.onResult(eventId, resultList);
                    break;
                }

                if ((System.currentTimeMillis() - downloadStartTimeMillis > TIME_OUT_MILLIS)) {
                    DDLog.i(TAG, "获取视频列表超时了！！！");
                    callback.onResult(eventId, resultList);
                    break;
                }

                DDLog.i(TAG, "开始下一次下载事件视频列表");
            }
        }

        interface Callback {
            /**
             * @param dataList null for error
             */
            void onResult(final String eventId, List<MotionEventVideoResponse.VideosBean> dataList);
        }
    }
}
