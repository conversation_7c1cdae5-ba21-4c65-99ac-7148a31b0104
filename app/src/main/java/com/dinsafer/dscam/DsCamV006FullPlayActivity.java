package com.dinsafer.dscam;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.SurfaceTexture;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.TextureView;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.Toast;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;

import com.dinsafer.DinSaferApplication;
import com.dinsafer.common.IPCManager;
import com.dinsafer.config.DBKey;
import com.dinsafer.config.DDGlobalEnv;
import com.dinsafer.config.LocalKey;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dinnet.R;
import com.dinsafer.dinnet.databinding.ActivityDscamV006FullplayBinding;
import com.dinsafer.dinsdk.DinConst;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.model.CloseActivityEvent;
import com.dinsafer.model.IPCEvent;
import com.dinsafer.model.PlayMotionDetectedRecordEvent;
import com.dinsafer.model.SOSevent;
import com.dinsafer.model.event.HadLogoutEvent;
import com.dinsafer.module.BaseLiveVideoActivity;
import com.dinsafer.module.ipc.heartlai.event.IPCInfoChangeEvent;
import com.dinsafer.module.ipc.player.CameraVideoView;
import com.dinsafer.module.ipc.player.IVideoViewListener;
import com.dinsafer.module.settting.adapter.ipc.FullPlayBackEvent;
import com.dinsafer.module.settting.ui.AlertDialog;
import com.dinsafer.module.settting.ui.AlertDialogV2;
import com.dinsafer.module_dscam.player.DsCamPlayer;
import com.dinsafer.module_dscam.player.IPlayer;
import com.dinsafer.module_dscam.player.IPlayerStatusListener;
import com.dinsafer.module_dscam.player.KRealTimePlayView;
import com.dinsafer.module_dscam.player.webrtc.CustomSurfaceViewRender;
import com.dinsafer.module_dscam.player.webrtc.DsCamWebRTCPlayer;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.util.AudioControllerUtil;
import com.dinsafer.util.DBUtil;
import com.dinsafer.util.DDLog;
import com.dinsafer.util.DDSystemUtil;
import com.dinsafer.util.Local;
import com.dinsafer.util.MapUtils;
import com.dinsafer.util.PermissionUtil;
import com.dinsafer.util.viewanimator.AnimationListener;
import com.dinsafer.util.viewanimator.ViewAnimator;
import com.githang.statusbar.StatusBarCompat;
import com.dinsafer.aop.annotations.SingleClick;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;
import org.webrtc.SurfaceViewRenderer;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;

@Deprecated
public class DsCamV006FullPlayActivity extends BaseLiveVideoActivity implements IDeviceCallBack {

    private static final String TAG = DsCamV006FullPlayActivity.class.getSimpleName();
    private static final long AUTO_FLASH_OFF_TIME_MILLIS = 15 * 1000L; // 强光告警、声光告警15s后自动切换回关灯图标

    private static final String VIDEO_MODE_KCP = "kcp";
    private static final String VIDEO_MODE_WEBRTC = "webrtc";

    private ActivityDscamV006FullplayBinding mBinding;
    private int duration = 300;

    private boolean isInitTakePicture = true;
    private boolean isPlaySound;
    private boolean isFromUserClick = false;
    private static final String KEY_QR_CODE_ID = "device_id";
    private Device device;
    private Timer timer;
    public String mCameraPid;
    private IPlayer player;
    private View playView;

    private static Handler handler = new Handler();
    private final Handler autoFlashOffH = new Handler(Looper.getMainLooper());
    // 强光告警、声光告警15s后自动切换回关灯图标
    private final Runnable autoFlashOffTask = () -> {
        mBinding.ipcLight.setImageResource(R.drawable.icon_ipc_light_off);
    };

    private IDefaultCallBack2<Bitmap> snapshotCallBack = new IDefaultCallBack2<Bitmap>() {
        @Override
        public void onSuccess(Bitmap bitmap) {
            savePicture(bitmap, device.getId());
        }

        @Override
        public void onError(int i, String s) {

        }
    };

    private boolean isRequestPlay;


    public static void startActivity(Context context, String id) {
        Intent intent = new Intent(context, DsCamV006FullPlayActivity.class);
        intent.putExtra(KEY_QR_CODE_ID, id);
        context.startActivity(intent);
    }

    /**
     * 初始化变量
     */
    @Override
    protected boolean initVariables() {
        device = IPCManager.getInstance().getDsCamDeviceByID(getIntent().getStringExtra(KEY_QR_CODE_ID));
        if (device == null) {
            return false;
        }
        mCameraPid = device.getId();
        device.registerDeviceCallBack(this);
        return true;
    }

    /**
     * 初始化控件
     *
     * @param savedInstanceState
     */
    @Override
    protected void initViews(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);//隐藏标题
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);//设置全屏
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_dscam_v006_fullplay);
        StatusBarCompat.setStatusBarColor(this, Color.BLACK, false);
        EventBus.getDefault().register(this);

//        mBinding.ipcIr.setOnClickListener(v -> setIR());
        mBinding.commonBarBack.setOnClickListener(v -> toClose());
        mBinding.ipcClose.setOnClickListener(v -> toClose());
        mBinding.ipcSound.setOnClickListener(v -> toListener());
        mBinding.ipcControlNor.setOnClickListener(v -> tigglePanel());
        mBinding.ipcTalk.setOnTouchListener((v, event) -> {
            final int action = event.getAction();
            if (DDSystemUtil.isMarshmallow()
                    && ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                    != PackageManager.PERMISSION_GRANTED) {
                if (MotionEvent.ACTION_DOWN == action) {
                    requestAudioPermission();
                }
                return true;
            }

            return toTalk(v, action);
        });
        mBinding.ipcQuality.setOnClickListener(v -> toSwitchQuality());
        mBinding.ipcSnap.setOnTouchListener((v, event) -> {
            final int action = event.getAction();
            if (PermissionUtil.isStoragePermissionDeny(this)) {
                if (MotionEvent.ACTION_DOWN == action) {
                    requestReadImagesPermission();
                }
                return true;
            }

            toSnapshot(v, event);
            return true;
        });

        mBinding.ipcSnap.setImageResource(R.drawable.icon_ipc_screenshots);
        mBinding.ipcSound.setImageResource(R.drawable.icon_ipc_sounds_off);

        mBinding.commonBarTitle.setText(Local.s(getResources().getString(R.string.device_managent_ip_camera)));
        mBinding.ipcMainControl.setVisibility(GONE);
//        boolean isIR = (boolean) MapUtils.get(device.getInfo(), "gray", false);
//        if (isIR) {
//            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_on);
//        } else {
//            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_off);
//        }

        mBinding.ipcLightOff.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                autoFlashOffH.removeCallbacks(autoFlashOffTask);
                refreshTimer();
                mBinding.ipcLight.setImageResource(R.drawable.icon_ipc_light_off);
                setFloodLight(0);
                tiggleLightView();
            }
        });

        mBinding.ipcLightOn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                autoFlashOffH.removeCallbacks(autoFlashOffTask);
                refreshTimer();
                mBinding.ipcLight.setImageResource(R.drawable.icon_ipc_light_on);
                setFloodLight(1);
                tiggleLightView();
            }
        });

        mBinding.ipcLightFlash.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                autoFlashOffH.removeCallbacks(autoFlashOffTask);
                autoFlashOffH.postDelayed(autoFlashOffTask, AUTO_FLASH_OFF_TIME_MILLIS);
                refreshTimer();
                mBinding.ipcLight.setImageResource(R.drawable.icon_ipc_light_flash);
                setFloodLight(2);
                tiggleLightView();
                Toast.makeText(DsCamV006FullPlayActivity.this,
                        Local.s(getString(R.string.flash_alarm_tip)), Toast.LENGTH_SHORT).show();
            }
        });

        mBinding.ipcLightAlarmFlash.setOnClickListener(v -> {
            autoFlashOffH.removeCallbacks(autoFlashOffTask);
            autoFlashOffH.postDelayed(autoFlashOffTask, AUTO_FLASH_OFF_TIME_MILLIS);
            refreshTimer();
            mBinding.ipcLight.setImageResource(R.drawable.icon_ipc_light_flashsound);
            setFloodLight(3);
            tiggleLightView();
            Toast.makeText(DsCamV006FullPlayActivity.this,
                    Local.s(getString(R.string.flash_and_sound_alarm_tip)), Toast.LENGTH_SHORT).show();
        });

        mBinding.ipcLight.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                refreshTimer();
                tiggleLightView();
            }
        });

        mBinding.ipcLightAlarmFlash.setVisibility(isSupportLightAlarmFlash() ? VISIBLE : GONE);
    }

    /**
     * 是否有声光告警功能-1.2.3新增
     *
     * @return true 有声光告警功能
     */
    private boolean isSupportLightAlarmFlash() {
        return DsCamUtils.isSupportAlarmSound(device);
    }

    private float getLightViewWidth() {
        return isSupportLightAlarmFlash() ? 228 : 168;
    }

    private void setFloodLight(int floodLight) {
        Map<String, Object> data = new HashMap<>();
        data.put("cmd", DsCamCmd.SET_FLOODLIGHT);
        data.put("floodlight", floodLight);
        device.submit(data);
    }

    private void tiggleLightView() {
        if (mBinding.llLight.getVisibility() == GONE) {
            ViewAnimator.animate(mBinding.llLight)
                    .alpha(0.3f, 1)
                    .dp()
                    .width(48, getLightViewWidth())
                    .duration(200)
                    .onStop(new AnimationListener.Stop() {
                        @Override
                        public void onStop() {
                        }
                    })
                    .onStart(new AnimationListener.Start() {
                        @Override
                        public void onStart() {
                            mBinding.llLight.setVisibility(VISIBLE);
                        }
                    })
                    .start();
        } else {
            ViewAnimator.animate(mBinding.llLight)
                    .alpha(1, 0.3f)
                    .dp()
                    .width(getLightViewWidth(), 48)
                    .duration(200)
                    .onStop(new AnimationListener.Stop() {
                        @Override
                        public void onStop() {
                            mBinding.llLight.setVisibility(GONE);
                        }
                    })
                    .onStart(new AnimationListener.Start() {
                        @Override
                        public void onStart() {

                        }
                    })
                    .start();
        }
    }


    /**
     * 初始化数据
     */
    @Override
    protected void loadData() {
        if (device == null) {
            return;
        }
        initIPCInfo();
        initPlayer();
        initVideoView();
        connectIPC();
        mBinding.videoView.showLoading();
        refreshVideoViewState(device);
    }

    private void initPlayer() {
        String videoMode = DeviceHelper.getString(device, "video_mode", "kcp");
        switch (videoMode) {
            case VIDEO_MODE_WEBRTC:
                player = DsCamWebRTCPlayer.Builder.newPlayer()
                        .withContext(getApplicationContext())
                        .withDevice(device)
                        .withChannelName("test-ipc-signaling-channel")
                        .build();
                playView = new CustomSurfaceViewRender(this);
                ((DsCamWebRTCPlayer) player).bindView((SurfaceViewRenderer) playView);
                break;
            default:
                playView = new KRealTimePlayView(this);
                ((KRealTimePlayView) playView).setOutListener(new TextureView.SurfaceTextureListener() {
                    @Override
                    public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surface, int width, int height) {
                        MsctLog.i(TAG, "onSurfaceTextureAvailable:" + surface.toString() + "width:" + width
                                + " height:" + height);
                    }

                    @Override
                    public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surface, int width, int height) {
                        MsctLog.i(TAG, "onSurfaceTextureSizeChanged:" + surface.toString() + "width:" + width
                                + " height:" + height);
                    }

                    @Override
                    public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surface) {
                        MsctLog.i(TAG, "onSurfaceTextureDestroyed:" + surface.toString());
                        return false;
                    }

                    @Override
                    public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surface) {

                    }
                });
                try {
                    player = new DsCamPlayer.Builder()
                            .kRealTimePlayView(((KRealTimePlayView) playView))
                            .withDevice(device)
                            .withKeyFrame(true)
                            .withContext(DinSaferApplication.getAppContext())
                            .build();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                player.changeBindView(((KRealTimePlayView) playView));
                break;
        }
        player.setStatusListener(playerStatusListener);
        player.registerSnapShotCallBack(snapshotCallBack);
        //初始化player数据
        player.loadData();
    }

    private boolean isShowIcon = true;

    private void initVideoView() {
        mBinding.videoView.getVideoContainer().removeAllViews();
        mBinding.videoView.getVideoContainer().addView(playView, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
        mBinding.videoView.setVideoViewListener(videoViewListener);
        playView.setOnClickListener(new View.OnClickListener() {

            @Keep
            @SingleClick(duration = 500)
            @Override
            public void onClick(View v) {
                DDLog.i(TAG, "点击了播放view");
                if (player.isPlaying()) {
                    tiggleView();
                }
            }
        });
        // 预览图
        final String snapshotPath = DeviceHelper.getString(device, HeartLaiConstants.ATTR_SNAPSHOT, "");
        if (!TextUtils.isEmpty(snapshotPath)) {
            File file = new File(snapshotPath);
            if (file.exists()) {
                mBinding.videoView.setCoverImageUri(Uri.fromFile(file));
            } else {
                mBinding.videoView.setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
            }
        } else {
            mBinding.videoView.setDefaultCoverImage(R.drawable.img_multiscreen_default_3);
        }

        refreshTimer();
    }

    private void refreshTimer() {
        handler.removeCallbacksAndMessages(null);
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (DsCamV006FullPlayActivity.this.isDestroyed() || !isShowIcon) {
                    return;
                }
                if (isTalkButtonDown) {
                    DDLog.i(TAG, "正在说话,不隐藏按钮");
                    return;
                }
                isShowIcon = false;
                if (mBinding.ipcMainControl.getVisibility() == VISIBLE) {
                    ViewAnimator.animate(mBinding.ipcMainControl)
                            .slideRightOut(mBinding.ipcMainControl.getWidth())
                            .duration(duration)
                            .onStop(() -> mBinding.ipcMainControl.setVisibility(GONE))
                            .start();
                }
                mBinding.ipcMoreLine.setVisibility(GONE);

                if (mBinding.ipcMoreControl.getVisibility() == VISIBLE) {
                    ViewAnimator.animate(mBinding.ipcMoreControl)
                            .slideRightOut(mBinding.ipcMoreControl.getWidth())
                            .duration(duration)
                            .start();
                }

                if (mBinding.ipcMoreControl.getVisibility() == VISIBLE) {
                    ViewAnimator.animate(mBinding.ipcClose)
                            .slideLeftOut(mBinding.ipcClose.getWidth())
                            .duration(duration)
                            .start();
                }
            }
        }, 5000);
    }

    public void tiggleView() {
        refreshTimer();
        if (isShowIcon) {
            isShowIcon = false;
            ViewAnimator.animate(mBinding.ipcMainControl)
                    .slideRightOut(mBinding.ipcMainControl.getWidth())
                    .duration(duration)
                    .onStop(() -> mBinding.ipcMainControl.setVisibility(GONE))
                    .start();
            ViewAnimator.animate(mBinding.ipcMoreControl)
                    .slideRightOut(mBinding.ipcMoreControl.getWidth())
                    .duration(duration)
                    .onStart(() -> mBinding.ipcMoreLine.setVisibility(GONE))
                    .start();
            ViewAnimator.animate(mBinding.ipcClose)
                    .slideLeftOut(mBinding.ipcClose.getWidth())
                    .duration(duration)
                    .start();
        } else {
            isShowIcon = true;
            ViewAnimator.animate(mBinding.ipcMainControl)
                    .slideRightIn(mBinding.ipcMainControl.getWidth())
                    .duration(duration)
                    .onStart(() -> mBinding.ipcMainControl.setVisibility(VISIBLE))
                    .start();
            ViewAnimator.animate(mBinding.ipcMoreControl)
                    .slideRightIn(mBinding.ipcMoreControl.getWidth())
                    .duration(duration)
                    .onStart(() -> {
                        if (mBinding.ipcMoreControl.getWidth() <= 0) {
                            mBinding.ipcMoreLine.setVisibility(GONE);
                        } else {
                            mBinding.ipcMoreLine.setVisibility(VISIBLE);
                        }
                    })
                    .start();
            ViewAnimator.animate(mBinding.ipcClose)
                    .slideLeftIn(mBinding.ipcClose.getWidth())
                    .duration(duration)
                    .start();
        }
    }

    private static Handler timeoutHandler = new Handler(Looper.getMainLooper());
    private boolean isSnapping = false;


    public boolean toSnapshot(View v, MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            mBinding.ipcSnap.setImageResource(R.drawable.icon_ipc_screenshots);
            mBinding.ipcSnap.setAlpha(0.4f);
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            mBinding.ipcSnap.setImageResource(R.drawable.icon_ipc_screenshots);
            mBinding.ipcSnap.setAlpha(1.0f);
            refreshTimer();
            DDLog.i(TAG, "截图 点击");
            if (isInitTakePicture) {
                DDLog.i(TAG, "自动化截图未完成");
//            在还没有进行默认的第一次自动截图时，不能让用户点击截图
//           2021年08月10日11:30:58，修改了需求，为了解决第一次自动截图失败了，以后再也无法截图问题
//            return;
            }

            if (isSnapping) {
                return true;
            }
            isSnapping = true;
            isFromUserClick = true;
            DDLog.i(TAG, "开始点击截图");
            if (this.player != null) {
                timeoutHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        showToast(getResources().getString(R.string.failed_try_again));
                        isSnapping = false;
                    }
                }, 20 * 1000);

                this.player.getSnapshot();
            }
        }
        return true;
    }

    /**
     * 配置IPC信息
     */
    private void initIPCInfo() {
        mBinding.commonBarTitle.setText(DeviceHelper.getString(device, HeartLaiConstants.ATTR_NAME, ""));
        mBinding.ipcMainControl.setVisibility(VISIBLE);
        mBinding.ipcClose.setVisibility(VISIBLE);

    }

    private void refreshVideoViewState(Device device) {
//        DDLog.e(TAG, Log.getStackTraceString(new Throwable()));
        DDLog.d(TAG, "refreshVideoViewState1: " + DeviceHelper.getInt(this.device, DinConst.NETWORK_STATE, -2));
        DDLog.d(TAG, "refreshVideoViewState2: " + DeviceHelper.getInt(device, DinConst.NETWORK_STATE, -2));
        if (DsCamUtils.isDeviceConnected(device)) {
//            mBinding.videoView.showLoading();
            mBinding.ipcMainControl.setVisibility(GONE);
            mBinding.ipcClose.setVisibility(VISIBLE);
            if (!player.isPlaying()) {
                play();
                //重新连接后恢复之前的监听状态
                if (isPlaySound) {
                    player.startListen(new IDefaultCallBack() {
                        @Override
                        public void onSuccess() {
                            DDLog.i(TAG, "开始监听");
                        }

                        @Override
                        public void onError(int i, String s) {
                            DDLog.i(TAG, "开始监听失败：" + s);
                        }
                    });
                }
                DDLog.i(TAG, "开始截图");
                player.getSnapshot();
            }
        } else if (DsCamUtils.isDeviceConnecting(device)) {
            mBinding.videoView.showLoading();
            mBinding.ipcMainControl.setVisibility(GONE);
            mBinding.ipcClose.setVisibility(VISIBLE);
        } else {
            if (player != null) {
                player.pausePlay();
            }
            mBinding.videoView.showError();
            isShowIcon = false;
            mBinding.ipcMainControl.setVisibility(GONE);
            mBinding.ipcClose.setVisibility(VISIBLE);
            showDeviceOfflineDialog(this.device);
        }

    }

    public void play() {
        if (isRequestPlay == true) {
            return;
        }
        isRequestPlay = true;


        if (DBUtil.contain(DBKey.KEY_DSCAM_VIDEO_TYPE + device.getId())) {
            int type = DBUtil.Num(DBKey.KEY_DSCAM_VIDEO_TYPE + device.getId());
            qualityType = type;
            player.play(type, new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    isRequestPlay = false;
                }

                @Override
                public void onError(int i, String s) {
                    isRequestPlay = false;
                }
            });
        } else {
            player.play(new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    isRequestPlay = false;
                }

                @Override
                public void onError(int i, String s) {
                    isRequestPlay = false;
                }
            });
        }
        if (qualityType == 0) {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_sd);
        } else {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_hd);
        }

    }

    public void connectIPC() {
        IPCManager.getInstance().connectDevice(device);
    }

    public void setIR() {
        boolean isIR = (boolean) MapUtils.get(device.getInfo(), "gray", false);
        Map<String, Object> parms = new HashMap<>();
        parms.put("cmd", DsCamCmd.SET_GRAY);
        parms.put("gray", !isIR);
        device.submit(parms);
    }


    @Override
    public void toClose() {
        if (this.player != null) {
            this.player.pausePlay();
        }
        EventBus.getDefault().post(new CloseActivityEvent());
        this.finish();
    }

    @Subscribe
    public void onEvent(IPCEvent event) {
        toClose();
    }

    @Override
    public void onStop() {
        super.onStop();
        if (this.player != null) {
            this.player.pausePlay();
        }
    }

    @Override
    public void onDestroy() {
        autoFlashOffH.removeCallbacksAndMessages(null);
        handler.removeCallbacksAndMessages(null);
        timeoutHandler.removeCallbacksAndMessages(null);
        talkHandler.removeCallbacksAndMessages(null);
        super.onDestroy();
        //退出关闭泛光灯
        setFloodLight(0);
        if (device != null) {
            device.unregisterDeviceCallBack(this);
        }
        EventBus.getDefault().post(new FullPlayBackEvent(mCameraPid));
        EventBus.getDefault().unregister(this);
        if (this.player != null) {
            DDLog.i(TAG, "停止播放");
            this.player.destory();
            this.player.unregisterSnapShotCallBack(snapshotCallBack);
//            DsCamPlayerManager.getInstance().removePlayer(device.getId());
        }
    }

    private void savePicture(Bitmap bitmap, String ipcId) {
        if (bitmap == null || ipcId == null) {
            return;
        }
        if (isInitTakePicture) {
            try {

                DDLog.i(TAG, "自动化截图");
                // 将图片截小；
//            Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bmp, 132, 100,
//                    ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
//                Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, APIKey.IMAGE_WIDTH, APIKey.IMAGE_HIGH,
//                        ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

                String genPath = DDGlobalEnv.getInstance().getAppImageFolder();
                File genFile = new File(genPath);
                if (!genFile.exists()) {
                    genFile.mkdirs();
                }

                String path = genPath + ".ipc";
                File file = new File(path);
                if (!file.exists()) {
                    file.mkdir();
                }
                //fileName为文件绝对路径+文件名
                String fileName = path + "/" + ipcId + String.valueOf("_" + System.currentTimeMillis()) + ".jpg";
                File snapshotFile = new File(fileName);

                if (snapshotFile.exists()) {
                    snapshotFile.delete();
                }

                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();

                JSONObject jsonObject;
                if (DBUtil.Exists(ipcId)) {
                    jsonObject = new JSONObject(DBUtil.Str(ipcId));
                } else {
                    jsonObject = new JSONObject();
                }

                DDLog.i(TAG, "自动化截图 完毕");

                jsonObject.put(LocalKey.SNAPSHOT, fileName);
                jsonObject.put(LocalKey.LAST_OPEN_TIME, (System.currentTimeMillis()));
                KV.putString(DBKey.KEY_SNAPSHOT + ipcId, jsonObject.toString());
                if (IPCManager.getInstance().getDsCamDeviceByID(device.getId()) != null) {
                    IPCManager.getInstance().getDsCamDeviceByID(device.getId()).getInfo().put(HeartLaiConstants.ATTR_SNAPSHOT, fileName);
                }
//                这里如果没有用到这个事件的话,就会出现白屏
                EventBus.getDefault().post(new IPCInfoChangeEvent(ipcId));

            } catch (Exception e) {
                e.printStackTrace();
            }

            isInitTakePicture = false;

        }
        if (isFromUserClick) {
//        用户主动点击时候的截图
            if (!isSnapping) {
                return;
            }
            timeoutHandler.removeCallbacksAndMessages(null);
            isSnapping = false;
            DDLog.i(TAG, "用户主动点击时候的截图");
//            Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, APIKey.IMAGE_WIDTH, APIKey.IMAGE_HIGH,
//                    ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

            File file = new File(DDGlobalEnv.getInstance().getImageFolder());
            if (!file.exists()) {
                file.mkdirs();
            }

            String strDate = new SimpleDateFormat("yyyy.MM.dd_HH.mm.ss").format(new Date());
            //fileName为文件绝对路径+文件名
            String fileName = DDGlobalEnv.getInstance().getImageFolder() + ipcId + "_" + strDate + ".jpg";
            File snapshotFile = new File(fileName);
            if (snapshotFile.exists()) {
                snapshotFile.delete();
            }
            try {
                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();
                DDSystemUtil.updatePhoto(this, snapshotFile);
                animSnapshot(bitmap);
//                showToast("Save the photo successfully");

            } catch (Exception e) {
                showToast("Fail to save the photo");
            }

            isFromUserClick = false;
        }
    }

    private void animSnapshot(Bitmap bitmap) {
        this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.animSnapshot.setImageBitmap(bitmap);
                ViewAnimator.animate(mBinding.animSnapshot)
                        .duration(300)
                        .scale(1.0f, 0.8f)
                        .onStart(new AnimationListener.Start() {
                            @Override
                            public void onStart() {
                                mBinding.animSnapshot.setVisibility(VISIBLE);
                            }
                        })
                        .onStop(new AnimationListener.Stop() {
                            @Override
                            public void onStop() {
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (DsCamV006FullPlayActivity.this.isDestroyed()) {
                                            return;
                                        }
                                        ViewAnimator.animate(mBinding.animSnapshot)
                                                .duration(300)
                                                .scale(0.8f, 1f)
                                                .onStart(new AnimationListener.Start() {
                                                    @Override
                                                    public void onStart() {
                                                        mBinding.animSnapshot.setVisibility(VISIBLE);
                                                    }
                                                })
                                                .onStop(new AnimationListener.Stop() {
                                                    @Override
                                                    public void onStop() {
                                                        if (DsCamV006FullPlayActivity.this.isDestroyed()) {
                                                            return;
                                                        }
                                                        mBinding.animSnapshot.setVisibility(GONE);
                                                        showTopToast(getString(R.string.dscam_save_to_album));
                                                    }
                                                })
                                                .start();
                                    }
                                }, 600);
                            }
                        })
                        .start();
            }
        });
    }

    private void showTopToast(final String message) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.commonTopToast.setLocalText(message);
                Drawable leftIcon = getResources().getDrawable(R.drawable.icon_toast_succeed);
                leftIcon.setBounds(0, 0, leftIcon.getMinimumWidth(), leftIcon.getMinimumHeight());
                mBinding.commonTopToast.setCompoundDrawables(leftIcon, null, null, null);
                mBinding.commonTopToastLy.showToast();
            }
        });
    }

    private void showToast(final String s) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.createBuilder(DsCamV006FullPlayActivity.this)
                        .setOk("OK")
                        .setContent(s)
                        .preBuilder()
                        .show();

            }
        });
    }

    /**
     * 输出日志
     *
     * @param msg
     */
    public final void i(@NonNull String msg) {
        DDLog.i(this.getClass().getName(), msg);
    }


    public void toListener() {
        refreshTimer();
        if (player == null || !DsCamUtils.isDeviceConnected(device)) {
            return;
        }
        if (player.isTalking()) {
            player.stopTalk();
            mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_inactive);
        }
        if (isPlaySound) {
            player.stopListen();
            mBinding.ipcSound.setImageResource(R.drawable.icon_ipc_sounds_off);
        } else {
            player.startListen(new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "开始监听");
                }

                @Override
                public void onError(int i, String s) {
                    DDLog.i(TAG, "开始监听失败：" + s);
                }
            });
            mBinding.ipcSound.setImageResource(R.drawable.icon_ipc_sounds_on);
        }

        isPlaySound = !isPlaySound;
    }


    private boolean isShowMoreControl = false;

    public void tigglePanel() {
        DDLog.i(TAG, "tigglePanel:" + isShowMoreControl);
        refreshTimer();
        if (isShowMoreControl) {
            isShowMoreControl = false;
            ViewAnimator.animate(mBinding.ipcControlNor)
                    .rotation(0)
                    .duration(duration)
                    .start();
            ViewAnimator.animate(mBinding.ipcSnap, mBinding.ipcQuality)
                    .dp()
                    .width(100, 0)
                    .duration(duration)
                    .onStart(new AnimationListener.Start() {
                        @Override
                        public void onStart() {
                            mBinding.ipcMoreControl.setVisibility(VISIBLE);
                        }
                    })
                    .onStop(() -> {
                        if (DsCamV006FullPlayActivity.this.isDestroyed()) {
                            return;
                        }
                        mBinding.ipcMoreControl.setVisibility(GONE);
                        mBinding.ipcMoreLine.setVisibility(GONE);
                    })
                    .start();

        } else {
            isShowMoreControl = true;
            ViewAnimator.animate(mBinding.ipcControlNor)
                    .rotation(-180)
                    .duration(duration)
                    .start();
            ViewAnimator.animate(mBinding.ipcSnap, mBinding.ipcQuality)
                    .dp()
                    .width(0, 100)
                    .duration(duration)
                    .onStop(new AnimationListener.Stop() {
                        @Override
                        public void onStop() {
                            mBinding.ipcMoreLine.setVisibility(VISIBLE);
                        }
                    })
                    .onStart(new AnimationListener.Start() {
                        @Override
                        public void onStart() {
                            mBinding.ipcMoreControl.setVisibility(VISIBLE);
                        }
                    })
                    .start();
        }
    }

    private boolean isTalkButtonDown = false;
    private Handler talkHandler = new Handler();

    public boolean toTalk(View v, final int action) {
        refreshTimer();
        if (player == null || !DsCamUtils.isDeviceConnected(device)) {
            return false;
        }

        switch (action) {
            case MotionEvent.ACTION_DOWN:
                isTalkButtonDown = true;
                mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_active);
                talkHandler.removeCallbacks(startTalkRunnable);
                talkHandler.postDelayed(startTalkRunnable, 1000);
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (isTalkButtonDown) {
                    isTalkButtonDown = false;
                    mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_inactive);
                    talkHandler.removeCallbacks(startTalkRunnable);
                    stopTalk();
                }
                break;
        }
        return true;
    }

    private Runnable startTalkRunnable = new Runnable() {
        @Override
        public void run() {
            startTalk();
        }
    };

    private void startTalk() {
        if (isPlaySound) {
            player.stopListen();
//            ipcSound.setImageResource(R.drawable.icon_ipc_sound_off);
        }
        player.startTalk(new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                DDLog.i(TAG, "开始讲话");
                DsCamV006FullPlayActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Toast.makeText(DsCamV006FullPlayActivity.this,
                                Local.s(getString(R.string.start_talk)), Toast.LENGTH_SHORT).show();
                    }
                });
            }

            @Override
            public void onError(int i, String s) {
                DDLog.i(TAG, "开始讲话失败：" + s);
                DsCamV006FullPlayActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_inactive);
                    }
                });
            }
        });
    }

    private void stopTalk() {
        if (player.isTalking()) {
            player.stopTalk();
            mBinding.ipcTalk.setImageResource(R.drawable.icon_ipc_talk_inactive);
        }
        if (isPlaySound) {
            player.startListen(new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "开始监听");
                }

                @Override
                public void onError(int i, String s) {
                    DDLog.i(TAG, "开始监听失败：" + s);
                }
            });
        }
    }

    //    0:std 1:hd
    int qualityType = 0;

    public void toSwitchQuality() {
        refreshTimer();
        if (player == null || !DsCamUtils.isDeviceConnected(device)) {
            return;
        }
        if (qualityType == 0) {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_hd);
            qualityType = 1;
        } else {
            mBinding.ipcQuality.setImageResource(R.drawable.icon_ipc_resolution_sd);
            qualityType = 0;
        }
        player.switchQuality(qualityType, new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                DBUtil.Put(DBKey.KEY_DSCAM_VIDEO_TYPE + device.getId(), qualityType);
                DsCamV006FullPlayActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (qualityType == 0) {
                            Toast.makeText(DsCamV006FullPlayActivity.this,
                                    Local.s(getString(R.string.switched_to_STD)), Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(DsCamV006FullPlayActivity.this,
                                    Local.s(getString(R.string.switched_to_HD)), Toast.LENGTH_SHORT).show();
                        }
                    }
                });
                DDLog.i(TAG, "切换成功");
            }

            @Override
            public void onError(int i, String s) {
                if (qualityType == 0) {
                    qualityType = 1;
                } else {
                    qualityType = 0;
                }
                DDLog.i(TAG, "切换失败：" + s);
            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        toClose();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
            AudioControllerUtil.get().adjustDown();
            return true;
        } else if (keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            AudioControllerUtil.get().adjustUp();
            return true;
        }

        return super.onKeyDown(keyCode, event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayMotionDetectedRecordEvent(PlayMotionDetectedRecordEvent playMotionDetectedRecordEvent) {
        finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(HadLogoutEvent ev) {
        finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSOSevent(SOSevent soSevent) {
        finish();
    }

    @Override
    public void onCmdCallBack(String deviceID, String subCategory, String cmd, Map map) {
        DDLog.d(TAG, "onCmdCallBack: " + deviceID + " /cmd:" + cmd);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (DsCamV006FullPlayActivity.this.isDestroyed()) {
                    return;
                }
                if (!deviceID.equals(device.getId())) {
                    return;
                }

                final int status = DeviceHelper.getInt(map, "status", 0);
                switch (cmd) {
                    case DsCamCmd.CMD_CONNECT:
                        refreshVideoViewState(device);
                        break;
//                    case DsCamCmd.SET_GRAY:
//                        boolean isIR = (boolean) MapUtils.get(device.getInfo(), "gray", false);
//                        if (isIR) {
//                            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_on);
//                            Toast.makeText(DsCamV006FullPlayActivity.this,
//                                    Local.s(getString(R.string.switched_to_ir_cut)), Toast.LENGTH_SHORT).show();
//                        } else {
//                            mBinding.ipcIr.setImageResource(R.drawable.icon_ipc_ir_cut_off);
//                            Toast.makeText(DsCamV006FullPlayActivity.this,
//                                    Local.s(getString(R.string.switched_to_color_night)), Toast.LENGTH_SHORT).show();
//                        }
//                        break;
                    case DsCamCmd.CONNECT_STATUS_CHANGED:
                        refreshVideoViewState(device);
                        break;
//                    case HeartLaiCmd.CMD_DEL:
//                        finish();
//                        break;
                    case DsCamCmd.SET_FLOODLIGHT:
                        final int originStatus = DeviceHelper.getInt(map, "origin_status", 0);
                        if (0 == status) {
                            // 设置失败
                            autoFlashOffH.removeCallbacks(autoFlashOffTask);
                            final int iconResId;
                            if (3 == originStatus) {
                                iconResId = R.drawable.icon_ipc_light_flashsound;
                            } else if (2 == originStatus) {
                                iconResId = R.drawable.icon_ipc_light_flash;
                            } else if (1 == originStatus) {
                                iconResId = R.drawable.icon_ipc_light_on;
                            } else {
                                iconResId = R.drawable.icon_ipc_light_off;
                            }
                            mBinding.ipcLight.setImageResource(iconResId);
                            Toast.makeText(DsCamV006FullPlayActivity.this,
                                    Local.s(getString(R.string.failed_try_again)), Toast.LENGTH_SHORT).show();
                        }
                        break;
                    default:
                        break;
                }


            }
        });

    }

    private AlertDialogV2 offlineDialog;

    public void showDeviceOfflineDialog(Device device) {
        if (offlineDialog != null && offlineDialog.isShowing()) {
            return;
        }
        offlineDialog = AlertDialogV2.createBuilder(this)
                .setContent(this.getResources().getString(R.string.ipc_failed_to_connect_the_network))
                .setOk(this.getResources().getString(R.string.ipc_reconnect_the_network))
                .setOkV2(this.getResources().getString(R.string.ipc_reconfigure_the_network))
                .setCancel(this.getResources().getString(R.string.cancel))
                .setOKListener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        IPCManager.getInstance().disconnectDevice(device);
                        IPCManager.getInstance().connectDevice(device);
                        mBinding.videoView.showLoading();
                    }
                })
                .setOKV2Listener(new AlertDialogV2.AlertOkClickCallback() {
                    @Override
                    public void onOkClick() {
                        EventBus.getDefault().post(new DsCamNetWorkSetting(device.getId()));
                        finish();
                    }
                })
                .preBuilder();
        offlineDialog.show();
    }

    private void hideDeviceOfflineDialog(Device device) {
        if (offlineDialog == null || !offlineDialog.isShowing()) {
            return;
        }
        offlineDialog.dismiss();
    }

    private IPlayerStatusListener playerStatusListener = new IPlayerStatusListener() {
        @Override
        public void onPrepared() {

        }

        @Override
        public void onStarted() {

        }

        @Override
        public void onPlaying() {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    mBinding.ipcMainControl.setVisibility(VISIBLE);
                    mBinding.ipcClose.setVisibility(VISIBLE);
                    mBinding.videoView.hideAllIcon();
                }
            });
        }

        @Override
        public void onPaused() {

        }

        @Override
        public void onRelease() {

        }

        @Override
        public void onWaiting() {

        }

        @Override
        public void onError(int i, String s) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    mBinding.videoView.showError();
                    isShowIcon = false;
                    mBinding.ipcMainControl.setVisibility(GONE);
                    mBinding.ipcClose.setVisibility(VISIBLE);
                }
            });
        }

        @Override
        public void onCompletion() {

        }
    };

    private IVideoViewListener videoViewListener = new IVideoViewListener() {

        @Override
        public void onPlayIconClick(int position, CameraVideoView videoViewRoot, View parent) {

        }

        @Override
        public void onErrorIconClick(int position, CameraVideoView videoViewRoot, View parent) {
            IPCManager.getInstance().connectDevice(device);
            mBinding.videoView.showLoading();
        }

        @Override
        public void onFullscreenIconClick(int position, CameraVideoView videoView, View parent) {

        }
    };
}


