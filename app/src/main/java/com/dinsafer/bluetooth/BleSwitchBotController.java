package com.dinsafer.bluetooth;

import android.view.View;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.dinsafer.model.BleDeviceSimpleEntry;
import com.dinsafer.util.DDLog;

import java.util.List;

/**
 * Created by LT on 2019/4/17.
 */
public class BleSwitchBotController {
    public BleSwitchBotController(BleDataCallback bleDataCallback) {
        this.bleDataCallback = bleDataCallback;
    }

    public void scan(BleScanCallback bleScanCallback) {
        BleManager.getInstance().scan(bleScanCallback);
    }

//    private BleScanCallback bleScanCallback = new BleScanCallback() {
//        @Override
//        public void onScanStarted(boolean success) {
//        }
//
//        @Override
//        public void onScanning(BleDevice bleDevice) {
//        }
//
//        @Override
//        public void onScanFinished(List<BleDevice> scanResultList) {
//            if (bleDataCallback != null) {
//                bleDataCallback.onScanFinished(scanResultList);
//            }
//        }
//    };


    public interface BleDataCallback{
        void onScanFinished(List<BleDevice> scanResultList);
    }

    private BleDataCallback bleDataCallback;

    public BleDataCallback getBleDataCallback() {
        return bleDataCallback;
    }

    public void setBleDataCallback(BleDataCallback bleDataCallback) {
        this.bleDataCallback = bleDataCallback;
    }
}
