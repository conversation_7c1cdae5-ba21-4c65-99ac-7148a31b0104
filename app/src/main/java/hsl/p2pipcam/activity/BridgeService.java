/**
 *
 */
package hsl.p2pipcam.activity;


import android.app.Service;
import android.bluetooth.BluetoothClass.Device;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;

import com.dinsafer.model.IPCPlayPos;
import com.dinsafer.util.DDLog;

import org.greenrobot.eventbus.EventBus;

import java.util.Collections;
import java.util.List;
import java.util.Vector;

import hsl.p2pipcam.nativecaller.DeviceSDK;

/**
 * <AUTHOR>
 */
public class BridgeService extends Service {
    private String TAg = getClass().getSimpleName();
    private static final int SERVICE_ID = 8888;
    //监听回调接口
    private static List<DeviceStatusListener> deviceStatusListener;
//    private static DeviceStatusListener deviceStatusListener;
    private static PlayListener playListener;
    private static PlayListener mAnchorPlayListener;
    private static RecorderListener recorderListener;
//    private static SettingsListener settingsListener;
    private static List<SettingsListener> settingsListener;
    private static SettingsListener mAnchorListener;

    @Override
    public IBinder onBind(Intent arg0) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        DDLog.d(TAg, "onCreate: ");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForeground(SERVICE_ID, new Notification());
        }
        deviceStatusListener = Collections.synchronizedList(new Vector());
        settingsListener =  Collections.synchronizedList(new Vector());
        DeviceSDK.initialize("");
        DeviceSDK.setCallback(this);
        DeviceSDK.networkDetect();
    }

    @Override
    public void onDestroy() {
        DeviceSDK.unInitSearchDevice();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
         super.onStartCommand(intent, flags, startId);
        return START_NOT_STICKY;
    }

    public static void setDeviceStatusListener(DeviceStatusListener deviceStatusListener) {
//        BridgeService.deviceStatusListener = deviceStatusListener;
    }

    public static void setPlayListener(PlayListener playListener) {
        BridgeService.playListener = playListener;
    }


    public static void setRecorderListener(RecorderListener recorderListener) {
        BridgeService.recorderListener = recorderListener;
    }

    public static SettingsListener getAnchorListener() {
        return mAnchorListener;
    }

    public static void setAnchorListener(SettingsListener mAnchorListener) {
        BridgeService.mAnchorListener = mAnchorListener;
    }

    public static void resetSettingListener() {
//        settingsListener = mAnchorListener;
    }

    //-------------------------------------------------------------------------
    //---------------------------以下是JNI层回调的接口-------------------------------
    //-------------------------------------------------------------------------

    public static void setSettingsListener(SettingsListener settingsListener) {
//        BridgeService.settingsListener = settingsListener;
    }

    public static PlayListener getAnchorPlayListener() {
        return mAnchorPlayListener;
    }

    public static void setAnchorPlayListener(PlayListener mAnchorPlayListener) {
        BridgeService.mAnchorPlayListener = mAnchorPlayListener;
    }

    public void CallBack_SnapShot(long UserID, byte[] buff, int len) {
    }


    public void CallBack_GetParam(long UserID, long nType, String param) {
        System.out.println("|-------------------param==" + param + "--------------|");
//        if (settingsListener != null)
//            settingsListener.callBack_getParam(UserID, nType, param);
    }


    public void CallBack_SetParam(long UserID, long nType, int nResult) {
//        if (settingsListener != null)
//            settingsListener.callBack_setParam(UserID, nType, nResult);
    }

    public void CallBack_Event(long UserID, long nType) {
        int status = new Long(nType).intValue();
//        if (deviceStatusListener != null)
//            deviceStatusListener.receiveDeviceStatus(UserID, status);
    }

    public void VideoData(long UserID, byte[] VideoBuf, int h264Data, int nLen, int Width, int Height, int time) {

    }

    public void callBackAudioData(long nUserID, byte[] pcm, int size) {
        if (playListener != null)
            playListener.callBackAudioData(nUserID, pcm, size);
        if (recorderListener != null)
            recorderListener.callBackAudioData(nUserID, pcm, size);
    }

    public void CallBack_RecordFileList(long UserID, int filecount, String fname, String strDate, int size) {
        System.out.println("|-----------------filecount==" + filecount + "---fname==" + fname + "------|");
//        if (settingsListener != null)
//            settingsListener.recordFileList(UserID, filecount, fname, strDate, size);
    }

    public void CallBack_P2PMode(long UserID, int nType) {
    }

    public void CallBack_RecordPlayPos(long userid, int pos) {
        EventBus.getDefault().post(new IPCPlayPos(pos));
    }

    public void CallBack_VideoData(long nUserID, byte[] data, int type, int size) {
    }

    public void CallBack_AlarmMessage(long UserID, int nType) {
        System.out.println("................");
    }

    public void showNotification(String message, Device device, int nType) {
    }

    public void CallBack_RecordPicture(long UserID, byte[] buff, int len) {
    }

    public void CallBack_RecordFileListV2(long UserID, String param) {

    }

    /**
     * @param userid
     * @param rgb
     * @param w
     * @param h
     * @param type=0为RGB32 数据长度为 w*h*bitcount/8  或者 w*h*4
     */
    public void CallBack_RGB(long userid, byte[] rgb, int w, int h, int bitcount) {
        if (playListener != null) {
            //返回给视频界面
            playListener.callBack_RGB(userid, rgb, w, h, bitcount);
        }

    }

}
