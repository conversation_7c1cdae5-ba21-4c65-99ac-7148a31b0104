package com.dinsafer.aop.annotations;

import androidx.annotation.IntRange;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 防止View被连续点击
 * 间隔：1秒
 *
 * <AUTHOR> MiraclesHed
 * @date : 2018/4/20/020
 * @email : <EMAIL>
 */

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface SingleClick {
    @IntRange(from = 0) long duration() default 1000;
}
