
package com.dinsafer.aop.runtime;

import android.os.Looper;

import com.dinsafer.aop.uitls.RLog;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;

/**
 * 跟踪被DebugTrace注解标记的方法和构造函数
 */
@Aspect
public class SaferAspect {

    private static volatile boolean enabled = true;

    private static final String POINTCUT_METHOD =
            "execution(@com.dinsafer.aop.annotations.Safer * *(..))";

    private static final String POINTCUT_CONSTRUCTOR =
            "execution(@com.dinsafer.aop.annotations.Safer *.new(..))";

    @Pointcut(POINTCUT_METHOD)
    public void methodAnnotated() {
    }

    @Pointcut(POINTCUT_CONSTRUCTOR)
    public void constructorAnnotated() {
    }

    @Around("methodAnnotated()||constructorAnnotated()")
    public Object weaveJoinPoint(ProceedingJoinPoint joinPoint) {
        Object result = null;
        try {
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            saveErrorString(joinPoint, throwable.getMessage());
            throwable.printStackTrace();
        }
        return result;
    }


    private void saveErrorString(ProceedingJoinPoint joinPoint, String message) {

        CodeSignature codeSignature = (CodeSignature) joinPoint.getSignature();

        String methodName = codeSignature.getName();

        StringBuilder builder = new StringBuilder();
        builder.append("3").append(',')
                .append(System.currentTimeMillis() + ",")
                .append("Android").append(",")
                .append(methodName).append(",")
                .append("0").append(",")
                .append(message);

        if (Looper.myLooper() != Looper.getMainLooper()) {
            builder.append("&").append(" [Thread:\"").append(Thread.currentThread().getName()).append("\"]");
        }

        RLog.getLog().writeLog(builder.toString());
    }
}
